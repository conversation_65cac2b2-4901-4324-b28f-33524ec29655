package com.zenith.front.common.context;

import com.alibaba.ttl.TransmittableThreadLocal;

/**
 * get方法参数应用上下文
 *
 * <AUTHOR>
 * @version V1.0
 * @date 2023/3/1 16:13
 */
public class GetParameterContextHolder {

    private static final ThreadLocal<String> THREAD_LOCAL = new TransmittableThreadLocal<>();

    public static String get() {
        return THREAD_LOCAL.get();
    }

    public static void set(String value) {
        THREAD_LOCAL.set(value);
    }

    public static void clear() {
        THREAD_LOCAL.remove();
    }

}
