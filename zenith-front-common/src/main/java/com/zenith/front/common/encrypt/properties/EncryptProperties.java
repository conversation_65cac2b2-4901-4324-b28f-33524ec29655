package com.zenith.front.common.encrypt.properties;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

/**
 * 加密属性
 *
 * <AUTHOR>
 */
@Component
public class EncryptProperties {

    public static String nginxKey;

    public static boolean enable;

    @Value("${exchange_nginx_key}")
    public void setNginxKey(String nginxKey) {
        EncryptProperties.nginxKey = nginxKey;
    }

    @Value("${encrypt.enable}")
    public void setEnable(boolean enable) {
        EncryptProperties.enable = enable;
    }
}
