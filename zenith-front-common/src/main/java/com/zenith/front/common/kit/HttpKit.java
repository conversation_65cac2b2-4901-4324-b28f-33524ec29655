package com.zenith.front.common.kit;

import com.alibaba.fastjson.JSONObject;

import javax.servlet.http.HttpServletRequest;
import java.io.BufferedReader;
import java.io.IOException;
import java.util.ArrayList;
import java.util.Map;
import org.apache.http.HttpEntity;
import org.apache.http.HttpResponse;
import org.apache.http.NameValuePair;
import org.apache.http.client.HttpClient;
import org.apache.http.client.entity.UrlEncodedFormEntity;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpGet;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.message.BasicNameValuePair;
import org.apache.http.params.CoreConnectionPNames;
import org.apache.http.util.EntityUtils;
import java.util.List;

/**
 * <AUTHOR>
 */
public class HttpKit {

    private static final String UA_HEADER = "user-agent";

    public static String readData(HttpServletRequest request) {
        BufferedReader br;

        try {
            br = request.getReader();
            String line = br.readLine();
            if (line == null) {
                return "";
            } else {
                StringBuilder ret = new StringBuilder();
                ret.append(line);

                while ((line = br.readLine()) != null) {
                    ret.append('\n').append(line);
                }

                return ret.toString();
            }
        } catch (IOException var4) {
            throw new RuntimeException(var4);
        }
    }

    /***
     * 获取浏览器UA头
     * */
    public static String getUA(HttpServletRequest request) {
        return request.getHeader(UA_HEADER);
    }


    public static String doPost(String url, Map<String, String> map, String charset) {
        HttpClient httpClient = null;
        HttpPost httpPost = null;
        String result = null;
        try {
            httpClient = new SSLClient();
            httpPost = new HttpPost(url);
            //设置参数
            List<NameValuePair> list = new ArrayList<>();
            for (Map.Entry<String, String> elem : map.entrySet()) {
                list.add(new BasicNameValuePair(elem.getKey(), elem.getValue()));
            }
            if (list.size() > 0) {
                UrlEncodedFormEntity entity = new UrlEncodedFormEntity(list, charset);
                httpPost.setEntity(entity);
            }
            //连接时间
            httpClient.getParams().setParameter(CoreConnectionPNames.CONNECTION_TIMEOUT, 1000 * 3);
            //数据传输时间
            httpClient.getParams().setParameter(CoreConnectionPNames.SO_TIMEOUT, 1000 * 10);

            HttpResponse response = httpClient.execute(httpPost);
            if (response != null) {

                result = response.getStatusLine().getStatusCode() + "";
                HttpEntity resEntity = response.getEntity();
                if (resEntity != null) {
                    result = response.getStatusLine().getStatusCode() + "|" + EntityUtils.toString(resEntity, charset);

                }
            }
        } catch (Exception ex) {
            ex.printStackTrace();
            result = "-1";
        }
        return result;
    }

    /**
     * JSon格式传送数据
     *
     * @param url
     * @param jsonObj
     * @param charset
     * @return
     */
    public static String doPost(String url, JSONObject jsonObj, String charset) {
        HttpClient httpClient = null;
        HttpPost httpPost = null;
        String result = null;
        try {
            httpClient = new SSLClient();
            httpPost = new HttpPost(url);
            //设置参数
            StringEntity se = new StringEntity(jsonObj.toString(), charset);
//			se.setContentEncoding(charset);
            httpPost.setEntity(se);
            httpPost.setHeader("Content-Type", "application/json; charset=UTF-8");
            HttpResponse response = httpClient.execute(httpPost);
            if (response != null) {
                result = response.getStatusLine().getStatusCode() + "";
                HttpEntity resEntity = response.getEntity();
                if (resEntity != null) {
                    //result = response.getStatusLine().getStatusCode()+"|"+EntityUtils.toString(resEntity,charset);
                    result = EntityUtils.toString(resEntity, charset);
                }
            }
        } catch (Exception ex) {
            ex.printStackTrace();
            result = "-1";
        }
        return result;
    }

    public static String doPost(String url, String json, String charset) {
        HttpClient httpClient = null;
        HttpPost httpPost = null;
        String result = null;
        try {
            httpClient = new SSLClient();
            httpPost = new HttpPost(url);
            //设置参数
            StringEntity se = new StringEntity(json, charset);
//			se.setContentEncoding(charset);
            httpPost.setEntity(se);
            httpPost.setHeader("Content-Type", "application/json; charset=UTF-8");
            HttpResponse response = httpClient.execute(httpPost);
            if (response != null) {
                result = response.getStatusLine().getStatusCode() + "";
                HttpEntity resEntity = response.getEntity();
                if (resEntity != null) {
                    //result = response.getStatusLine().getStatusCode()+"|"+EntityUtils.toString(resEntity,charset);
                    result = EntityUtils.toString(resEntity, charset);
                }
            }
        } catch (Exception ex) {
            ex.printStackTrace();
            result = "-1";
        }
        return result;
    }

    public static String doGet(String url) {
        // 创建Httpclient对象
        CloseableHttpClient httpclient = HttpClients.createDefault();
        // 创建http GET请求
        HttpGet httpGet = new HttpGet(url);
        String content = null;
        CloseableHttpResponse response = null;
        try {
            // 执行请求
            response = httpclient.execute(httpGet);
            // 判断返回状态是否为200
            if (response.getStatusLine().getStatusCode() == 200) {
                //请求体内容
                content = EntityUtils.toString(response.getEntity(), "UTF-8");
            }
        } catch (IOException e) {
            e.printStackTrace();
        } finally {
            if (response != null) {
                try {
                    response.close();
                } catch (IOException e) {
                    e.printStackTrace();
                }
            }
            //相当于关闭浏览器
            try {
                httpclient.close();
            } catch (IOException e) {
                e.printStackTrace();
            }
        }
        return content;
    }


}
