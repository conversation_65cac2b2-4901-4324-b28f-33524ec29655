package com.zenith.front.common.kit;//package com.zenith.front.common.kit;
//
//import cn.hutool.core.date.SystemClock;
//import javax.servlet.http.HttpServletRequest;
//import javax.servlet.http.HttpServletResponse;
//import java.io.*;
//import java.math.BigDecimal;
//import java.math.RoundingMode;
//import java.nio.charset.StandardCharsets;
//import java.util.HashMap;
//import java.util.Map;
//import java.util.SortedMap;
//import java.util.TreeMap;
//import java.util.function.Function;
//
///**
// * <AUTHOR>
// * @version 1.0
// * @Description:
// * @date 2019/5/16 15:29
// */
//public class WxPayUtil {
//
//    /**
//     * 公众账号ID
//     */
//    private static String appid = "wxd3029c600830d86f";
//    /**
//     * 商户号
//     */
//    public static String mch_id = "1499941172";
//    /**
//     * 支付密钥
//     */
//    public static String paternerKey = "6bd957f342aa52f4cfbac6b390473679";
//    /**
//     * 异步接收微信支付结果通知的回调地址，通知url必须为外网可访问的url，不能携带参数
//     */
//    private static String notify_url = "http://shenjr.shinesec.com.cn/api/fee/order/wxPayNotifyUrl";
//
//    /**
//     * 下载资金账单
//     */
//    private static String downloadFundflowUrl = "https://api.mch.weixin.qq.com/pay/downloadfundflow";
//
//    /**
//     * 公众号支付js-sdk
//     *
//     * @param openId
//     * @param body
//     * @param totalFee
//     * @param ip
//     * @param outTradeNo
//     * @return
//     */
//    public static Map<String, String> publicAccountsPay(String openId, String body, int totalFee, String ip, String outTradeNo) {
//        Map<String, String> params = new HashMap<>(30);
//        params.put("openid", openId);
//        setParams(params, body, totalFee, ip, outTradeNo, PaymentApi.TradeType.JSAPI.name());
//        String xmlResult = PaymentApi.pushOrder(params);
//
//        Map<String, String> resultMap = new HashMap<>(2);
//
//        System.out.println(xmlResult);
//        Map<String, String> result = PaymentKit.xmlToMap(xmlResult);
//
//        String returnCode = result.get("return_code");
//        String returnMsg = result.get("return_msg");
//        if (StrKit.isBlank(returnCode) || !CommonConstant.SUCCESS.equals(returnCode)) {
//            resultMap.put("isError", "1");
//            resultMap.put("message", returnMsg);
//            return resultMap;
//        }
//        String resultCode = result.get("result_code");
//        if (StrKit.isBlank(resultCode) || !CommonConstant.SUCCESS.equals(resultCode)) {
//            resultMap.put("isError", "1");
//            resultMap.put("message", returnMsg);
//            return resultMap;
//        }
//        // 以下字段在return_code 和result_code都为SUCCESS的时候有返回
//        String prepayId = result.get("prepay_id");
//
//        Map<String, String> packageParams = new HashMap<String, String>();
//        packageParams.put("appId", appid);
//        packageParams.put("timeStamp", SystemClock.now() / 1000 + "");
//        packageParams.put("nonceStr", SystemClock.now() + "");
//        packageParams.put("package", "prepay_id=" + prepayId);
//        packageParams.put("signType", "MD5");
//        String packageSign = PaymentKit.createSign(packageParams, paternerKey);
//        packageParams.put("paySign", packageSign);
//
//        // 此参数不是微信需要的
//        packageParams.put("outTradeNo", outTradeNo);
//        String jsonStr = JsonUtils.toJson(packageParams);
//        resultMap.put("isError", "0");
//        resultMap.put("message", jsonStr);
//        return resultMap;
//    }
//
//    /**
//     * PC支付模式二，PC支付不需要openid
//     */
//    public static Map<String, String> pcModeTwo(String body, String productId, int totalFee, String ip, String outTradeNo) {
//        Map<String, String> params = new HashMap<>(30);
//        // 商品ID trade_type=NATIVE，此参数必传。此id为二维码中包含的商品ID，商户自行定义。
//        params.put("product_id", productId);
//        setParams(params, body, totalFee, ip, outTradeNo, PaymentApi.TradeType.NATIVE.name());
//
//        String xmlResult = PaymentApi.pushOrder(params);
//        return PaymentKit.xmlToMap(xmlResult);
//    }
//
//    /**
//     * 设置请求参数
//     *
//     * @param params
//     * @param body
//     * @param totalFee
//     * @param ip
//     * @param outTradeNo
//     */
//    private static void setParams(Map<String, String> params, String body, int totalFee, String ip, String outTradeNo, String tradeType) {
//        params.put("appid", appid);
//        params.put("mch_id", mch_id);
//        params.put("body", body);
//        // 商户订单号 商户系统内部的订单号,32个字符内、可包含字母, 其他说明见商户订单号
//        params.put("out_trade_no", outTradeNo);
//        params.put("total_fee", totalFee + "");
//        params.put("spbill_create_ip", ip);
//        params.put("trade_type", tradeType);
//        params.put("nonce_str", SystemClock.now() + "");
//        params.put("notify_url", notify_url);
//        String sign = PaymentKit.createSign(params, paternerKey);
//        params.put("sign", sign);
//    }
//
//    /**
//     * 自定义商户订单号
//     *
//     * @return
//     */
//    public static String getOutTradeNo() {
//        return StrKit.getRandomUUID().substring(0, 19) + SystemClock.now();
//    }
//
//    /**
//     * 获取商品订单号
//     *
//     * @return
//     */
//    public static String getProductId() {
//        return StrKit.getRandomUUID();
//    }
//
//    /**
//     * 微信平pc扫码支付订单状态查询
//     *
//     * @param outTradeNo 商户订单号
//     * @return
//     */
//    public static Map<String, String> orderQueryByOutTradeNo(String outTradeNo) {
//        return PaymentApi.queryByOutTradeNo(appid, mch_id, paternerKey, outTradeNo);
//    }
//
//    /**
//     * 检查微信支付相关结果
//     *
//     * @param resultMap 微信返回结果
//     * @return
//     */
//    public static OutMessage checkResultMap(Map<String, String> resultMap) {
//        // 返回状态码
//        String returnCode = resultMap.get("return_code");
//        // 返回信息
//        String returnMsg = resultMap.get("return_msg");
//        if (StrKit.isBlank(returnCode) || !CommonConstant.SUCCESS.equals(returnCode)) {
//            return new OutMessage<>(Status.FAIL, returnMsg);
//        }
//        // 业务结果
//        String resultCode = resultMap.get("result_code");
//        if (StrKit.isBlank(resultCode) || !CommonConstant.SUCCESS.equals(resultCode)) {
//            return new OutMessage<>(Status.FAIL, returnMsg);
//        }
//        return null;
//    }
//
//    /**
//     * 转换为分为单位的金额
//     *
//     * @param bigDecimal 两位小数的金额,四舍五入
//     * @return
//     */
//    public static int payMoneyToInt(BigDecimal bigDecimal) {
//        bigDecimal = bigDecimal.setScale(2, RoundingMode.HALF_UP);
//        BigDecimal multiplicand = new BigDecimal(100);
//        return bigDecimal.multiply(multiplicand).intValue();
//    }
//
//    /**
//     * 支付回调
//     *
//     * @param request
//     * @param function
//     * @return
//     * @throws IOException
//     */
//    public static boolean wxPayNotify(HttpServletRequest request, Function<Map<String, String>, Boolean> function) throws IOException {
//        StringBuffer sb = new StringBuffer();
//        //读取参数
//        InputStream inputStream = request.getInputStream();
//        String s;
//        BufferedReader in = new BufferedReader(new InputStreamReader(inputStream, StandardCharsets.UTF_8));
//        while ((s = in.readLine()) != null) {
//            sb.append(s);
//        }
//        in.close();
//        inputStream.close();
//
//        //解析xml成map
//        Map<String, String> receiveMap = PaymentKit.xmlToMap(sb.toString());
//
//        //过滤空 设置 TreeMap
//        SortedMap<String, String> resultMap = new TreeMap<>();
//        for (String parameter : receiveMap.keySet()) {
//            String parameterValue = receiveMap.get(parameter);
//            String v = "";
//            if (null != parameterValue) {
//                v = parameterValue.trim();
//            }
//            resultMap.put(parameter, v);
//        }
//
//        // 结果map
//        return function.apply(resultMap);
//    }
//
//    /**
//     * 返回回调状态
//     *
//     * @param flag 回调状态
//     */
//    public static void wxPayNotifyState(HttpServletResponse response, boolean flag) throws IOException {
//        if (flag) {
//            String resXml = "<xml>" + "<return_code><![CDATA[SUCCESS]]></return_code>"
//                    + "<return_msg><![CDATA[OK]]></return_msg>" + "</xml> ";
//            BufferedOutputStream out = new BufferedOutputStream(
//                    response.getOutputStream());
//            out.write(resXml.getBytes());
//            out.flush();
//            out.close();
//            System.out.println("通知微信.异步确认成功");
//        } else {
//            String resXml = "<xml>" + "<return_code><![CDATA[FAIL]]></return_code>"
//                    + "<return_msg><![CDATA[报文为空]]></return_msg>" + "</xml> ";
//
//            BufferedOutputStream out = new BufferedOutputStream(
//                    response.getOutputStream());
//            out.write(resXml.getBytes());
//            out.flush();
//            out.close();
//            System.out.println("执行回调函数失败");
//        }
//    }
//
//    /**
//     * 下载对账单
//     */
//    public static String downloadBill(String billDate) {
//        return PaymentApi.downloadBill(appid, mch_id, paternerKey, billDate, PaymentApi.BillType.SUCCESS);
//    }
//
//    /**
//     * 下载资金账单
//     * 需要安装API证书
//     *
//     * @param billDate
//     */
//    public static String downloadFundflow(String billDate) {
//        Map<String, String> params = new HashMap<>(10);
//        params.put("appid", appid);
//        params.put("mch_id", mch_id);
//        params.put("nonce_str", System.currentTimeMillis() + "");
//        params.put("bill_date", billDate);
//        /**
//         * 账单的资金来源账户：
//         * Basic  基本账户
//         * Operation 运营账户
//         * Fees 手续费账户
//         */
//        params.put("account_type", "Fees");
//        String sign = PaymentKit.createSign(params, paternerKey);
//        params.put("sign", sign);
//        return HttpUtils.post(downloadFundflowUrl, PaymentKit.toXml(params));
//    }
//
//}
