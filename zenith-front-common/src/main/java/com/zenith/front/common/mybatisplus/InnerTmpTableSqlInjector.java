package com.zenith.front.common.mybatisplus;

import com.baomidou.mybatisplus.core.injector.AbstractMethod;
import com.baomidou.mybatisplus.core.injector.DefaultSqlInjector;
import com.baomidou.mybatisplus.extension.injector.methods.InsertBatchSomeColumn;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * 批量新增到临时表
 *
 * <AUTHOR>
 * @creed Talk is cheap,show me the code
 * @date 2021年12月26日 09时07分
 */
@Component
public class InnerTmpTableSqlInjector extends DefaultSqlInjector {

    @Override
    public List<AbstractMethod> getMethodList(Class<?> mapperClass) {
        List<AbstractMethod> methodList = super.getMethodList(mapperClass);
        methodList.add(new InnerTmpTableInsertBatchMethod());
        methodList.add(new InsertBatchPgMethod());

        return methodList;
    }
}
