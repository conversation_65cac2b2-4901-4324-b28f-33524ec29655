package com.zenith.front.common.password;

import cdf.jce.provider.CDFJCE;
import cdf.jce.spec.InternalKeyParameterSpec;
import com.zenith.front.common.safe.Base64;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.ResourceUtils;

import javax.crypto.Cipher;
import javax.crypto.KeyGenerator;
import javax.crypto.Mac;
import javax.crypto.SecretKey;
import javax.crypto.spec.IvParameterSpec;
import java.net.URLDecoder;
import java.nio.charset.StandardCharsets;
import java.security.SecureRandom;
import java.security.Security;
import java.util.Arrays;
import java.util.concurrent.ConcurrentHashMap;


import com.sm4alg.SM4Context;
import com.sm4alg.SM4Util;

@Slf4j
public class FMSymEx {
    static ConcurrentHashMap<Integer,byte[]> KEY_CACHE=new ConcurrentHashMap<>();


    public static byte[] getKey(int keyIndex){
        byte[] key=KEY_CACHE.get(keyIndex);
        if(key==null){
            try{
                KeyGenerator keyGenerator=KeyGenerator.getInstance("SM4","CDFJCE");
                keyGenerator.init(128,SecureRandom.getInstance("RandomSM4InnerKey"+keyIndex));
                SecretKey secretKey=keyGenerator.generateKey();
                key=secretKey.getEncoded();
                KEY_CACHE.put(keyIndex,key);
            }catch (Exception e){
            }
        }
        return key;
    }


    public static byte[] sm4CBC(boolean mode, byte[] key, byte[] inData, byte[] iv) {
        SM4Context ctx = new SM4Context();
        ctx.isPadding = false;
        if (mode) {
            ctx.mode = SM4Util.SM4_ENCRYPT;
        } else {
            ctx.mode = SM4Util.SM4_DECRYPT;
        }
        SM4Util sm4 = new SM4Util();
        byte[] data;
        try {
            if (mode) {
                sm4.sm4_setkey_enc(ctx, key);
            } else {
                sm4.sm4_setkey_dec(ctx, key);
            }
            data = sm4.sm4_crypt_cbc(ctx, iv, inData);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
        return data;
    }

    public static byte[] pkcs5Padding(byte[] data,int blockSize){
        int paddingLen=blockSize-data.length%blockSize;
        byte[] res=new byte[data.length+paddingLen];
        System.arraycopy(data,0,res,0,data.length);
        for(int i=data.length;i<res.length;i++){
            res[i]=(byte)(paddingLen&0xff);
        }
        return res;
    }

    public static byte[] pkcs5UnPadding(byte[] data,int blockSize){
        int padding=data[data.length-1];
        if(padding>blockSize||padding<=0){
            return null;
        }
        for(int i=1;i<padding;i++){
            if(data[data.length-i-1]!=padding){
                return null;
            }
        }
        byte[] res=new byte[data.length-padding];
        System.arraycopy(data,0,res,0,res.length);
        return res;
    }


    /**
     * 内部对称密钥加密运算
     * @param keyAlg 算法类型只支持SM1/SM4，默认SM4
     * @param keyId 密钥号，各个厂家不要使用同一个密钥
     * @param mode "CBC"或者"ECB"，密评默认CBC模式
     * @param isPad true为内部打补丁，即输入数据可为任意长度;false为上层打补丁，即输入数据必须为密钥模长的整数倍，默认true
     * @param inData 待加密数据
     * @param iv iv值，CBC模式必须传一个16字节的随机数组，加密解密保证这个值一样
     * @return 加密后的数据，加密之后的数据直接转为String类型是乱码，需要先加Base64编码再转成String类型进行存储
     */
    public static byte[] internalSymEncrypt(String keyAlg, int keyId, String mode, boolean isPad, byte[] inData, byte[] iv) {
        /*alg:参数格式"算法名称/模式/打补丁方式"；*/
        /* 如"SM1/ECB/NOPADDING"为SM1算法，ECB模式，不打补丁*/
        /* "SM1/CBC/PKCS5PADDING"为SM1算法，CBC模式，打补丁*/
        byte[] dataPad=null;
        if(isPad){
            dataPad=pkcs5Padding(inData,16);
        }else{
            dataPad=inData;
        }
        try{
            byte[] keyBuf=getKey(keyId);
            if(keyBuf==null){
                throw new Exception("密钥不存在");
            }
            byte[] enc=sm4CBC(true,keyBuf,dataPad,iv);
            if(enc==null){
                throw new Exception("SM4加密失败");
            }
            return enc;
        }catch(Exception e){
            log.error("加密失败:"+e.toString());
        }
        return null;
    }

    /**
     * 内部对称密钥解密运算
     * @param keyAlg 算法类型只支持SM1/SM4，默认SM4
     * @param keyId 密钥号，和加密时使用的密钥号相同
     * @param mode "CBC"或者"ECB"，密评默认CBC模式
     * @param isPad true为内部打补丁，即输入数据可为任意长度;false为上层打补丁，即输入数据必须为密钥模长的整数倍，默认true
     * @param inData 待解密数据，如果之前做了Base64编码需要先解Base64后再解密
     * @param iv iv值，CBC模式必须传一个16字节的随机数组，加密解密保证这个值一样
     * @return 解密后的数据
     */
    public static byte[] internalSymDecrypt(String keyAlg, int keyId, String mode, boolean isPad, byte[] inData, byte[] iv) {
        /*alg:参数格式"算法名称/模式/打补丁方式"；*/
        /* 如"SM1/ECB/NOPADDING"为SM1算法，ECB模式，不打补丁*/
        /* "SM1/CBC/PKCS5PADDING"为SM1算法，CBC模式，打补丁*/
        try {
            byte[] keyBuf=getKey(keyId);
            if(keyBuf==null){
                throw new Exception("密钥不存在");
            }
            byte[] dec=sm4CBC(false,keyBuf,inData,iv);
            if(dec==null){
                throw new Exception("SM4解密失败");
            }
            if(isPad){
                dec=pkcs5UnPadding(dec,16);
                if(dec==null){
                    throw new Exception("补丁解密失败"+keyId+":"+cdf.util.encoders.Base64.encode(keyBuf)+" "+cdf.util.encoders.Base64.encode(inData)+
                            " "+cdf.util.encoders.Base64.encode(dec));
                }
            }
            return dec;
        } catch(Exception e) {
            log.error("解密失败:"+e.toString());
        }
        return null;
    }





    /**
     * HMAC算法
     * @param keyNum 对称密钥号，和加解密用一个密钥号
     * @param keyLen 对称密钥长度，16
     * @param macAlg "HMac/SM3"
     * @param inData  输入数据，固定32字节
     * @return HMAC结果，使用Base64编码后再存储
     */
    public static byte[] hMacInnerKey(int keyNum, int keyLen, String macAlg, byte[] inData) throws Exception {
        byte[] result = null;
//        try {
        Mac mac = Mac.getInstance(macAlg, "CDFJCE");
        mac.init(null, new InternalKeyParameterSpec(keyNum, keyLen));
        mac.reset();
        mac.update(inData, 0, inData.length);
        result = mac.doFinal();
//        } catch (Exception e) {
//            log.error("hMac error");
//            e.printStackTrace();
//        }
        return result;
    }

    public static void main(String[] args) throws Exception {
        //必须执行读取配置文件
        // 先尝试从classpath读取
        String confPath = "D:\\work\\code\\work\\zenith-front-dj\\zenith-front-web\\src\\main\\resources\\";

//        for (int i = 0; i < 1000; i++) {
//            String data = "测试测试";
//            FMSym.init(confPath);
////        String s1 = JmjUtil.InternalSM4Enc(data);
////        System.out.println(s1);
//            String s1= "LhVmZrAWWECJY00TEWM2Hg==";
//            String s2 = JmjUtil.InternalSM4Dec(s1);
//            System.out.println(s2);
//            System.out.println(JmjUtil.sm3Hac(data));
//        }yGaafqat6COfFJmRn2ve5Q==
        String data = "李江华";
        FMSym.init(confPath);
//        String s1 = JmjUtil.InternalSM4Enc(data);
//        System.out.println(s1);
//         s1 = "5L2Z5rW354eVBwcHBwcHBw==";
//         s1 = "xPCIkOZtJCfzYjgMpNfIow==";
        String s2 = JmjUtil.InternalSM4Dec("/e7so9rrIUbmfWKHGI6cqQ==");
        System.out.println(s2);
        System.out.println(Base64.encode(s2.getBytes(StandardCharsets.UTF_8)));
//        System.out.println(JmjUtil.sm3Hac(data));
    }
}


