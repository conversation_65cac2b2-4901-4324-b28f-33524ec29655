package com.zenith.front.common.annotation;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * 用于对村社区相关接口的响应结果进行加密
 *
 * <AUTHOR>
 * @version V1.0
 * @date 2022/8/26 16:07
 */
@Retention(RetentionPolicy.RUNTIME)
@Target({ElementType.METHOD, ElementType.PARAMETER})
public @interface VcEncrypt {

}
