package com.zenith.front.common.constant;

import java.util.Arrays;

/**
 * <AUTHOR>
 */
public enum LogicCompareType {

    NOT_EQUAL("notEqual"),
    EQ("eq"),
    LESS_THAN("LessThan"),
    <PERSON>OR<PERSON>("more"),
    LESS_THAN_EQ("LessThanEq"),
    MORE_EQ("moreEq"),
    CONTAIN("contain"),
    NOT_CONTAIN("notContain"),

    ;

    public final String keyword;

    LogicCompareType(String keyword) {
        this.keyword = keyword;
    }

    public static LogicCompareType matching(String keyword) {
        return Arrays.stream(LogicCompareType.values()).filter(logicCompareTypeConstant -> logicCompareTypeConstant.keyword.equals(keyword)).findFirst().orElse(null);
    }
}