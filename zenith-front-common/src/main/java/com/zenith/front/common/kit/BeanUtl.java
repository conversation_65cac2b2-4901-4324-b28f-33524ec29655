package com.zenith.front.common.kit;

import cn.hutool.core.util.StrUtil;
import lombok.extern.slf4j.Slf4j;

import java.lang.reflect.Field;
import java.util.List;

/**
 * <AUTHOR>
 * @create_date 2023-11-30 21:16
 * @description
 */
@Slf4j
public class BeanUtl {


    /**
     *  复制指定的字段属性
     * @param source
     * @param target
     * @param appointFieldList
     */
    public static void copyProperties(Object source, Object target, List<String> appointFieldList){
        Class<?> sClazz = source.getClass();
        Class<?> tClazz = target.getClass();
        Field[] sFields = sClazz.getDeclaredFields();
        Field[] tFields = tClazz.getDeclaredFields();
        for (String fieldName : appointFieldList) {
            Object sV = null;
            for (Field sField : sFields) {
                // 来源存在这个字段就获取值
                if(StrUtil.equalsIgnoreCase(sField.getName(), fieldName)){
                    try {
                        sField.setAccessible(true);
                        sV = sField.get(source);
                        break;
                    } catch (IllegalAccessException e) {
                        log.error(e.getMessage());
                    }
                }
            }
            for (Field tField : tFields) {
                // 目标存在这个字段就更新值
                if(StrUtil.equalsIgnoreCase(tField.getName(), fieldName)){
                    try {
                        tField.setAccessible(true);
                        tField.set(target, sV);
                        break;
                    } catch (IllegalAccessException e) {
                        log.error(e.getMessage());
                    }
                }
            }
        }
    }
}
