package com.zenith.front.common.kit;

import cn.hutool.core.date.DateUnit;
import cn.hutool.core.util.StrUtil;

import java.time.*;
import java.time.format.DateTimeFormatter;
import java.util.Calendar;
import java.util.Date;
import java.util.Objects;

/**
 * <AUTHOR>
 * @version 1.0
 * @Description: 时间工具类
 * @date 2019/3/26 14:27
 */
public class DateUtil {

    /**
     * 实现Date和LocalDate的相互转换
     */
    public static LocalDate dateToLocaleDate(Date date) {
        Date d = new Date(date.getTime());
        Instant instant = d.toInstant();
        ZoneId zoneId = ZoneId.systemDefault();
        return instant.atZone(zoneId).toLocalDate();
    }

    /**
     * 实现Date和LocalDate的相互转换
     */
    public static Date localeDateToDate(LocalDate localDate) {
        ZoneId zoneId = ZoneId.systemDefault();
        ZonedDateTime zonedDateTime = localDate.atStartOfDay(zoneId);

        return Date.from(zonedDateTime.toInstant());

    }

    /**
     * 比较时间是否被包含
     *
     * @param startDate 开始时间
     * @param endDate   结束时间
     * @param date      比较时间
     * @return
     */
    public static boolean isIncludeDate(Date startDate, Date endDate, Date date) {
        if (Objects.isNull(startDate) || Objects.isNull(endDate) || Objects.isNull(date)) {
            return false;
        }
        // 开始时间比较
        long between1 = cn.hutool.core.date.DateUtil.between(startDate, date, DateUnit.DAY, false);
        long between2 = cn.hutool.core.date.DateUtil.between(endDate, date, DateUnit.DAY, false);
        return between1 > 0 && between2 < 0;
    }

    /**
     * 根据月份获取季度
     * 月份从1开始
     *
     * @param month
     * @return
     */
    public static String quarter(String month) {
        int integer = Integer.parseInt(month);
        return String.valueOf((integer - 1) / 3 + 1);
    }

    /**
     * 根据月份获取季度
     * 月份从1开始
     *
     * @param month
     * @return
     */
    public static Integer quarter(Integer month) {
        return (month - 1) / 3 + 1;
    }

    /**
     * 判断时间差
     */
    public static boolean dateCompare(Date time1, Date time2, int numYear) {
        Date time3 = add(time1, Calendar.YEAR, numYear);
        return time3.getTime() < time2.getTime();
    }

    public static Date add(final Date date, final int calendarFiled, final int amount) {
        if (date == null) {
            return null;
        }
        final Calendar c = Calendar.getInstance();
        c.setTime(date);
        c.add(calendarFiled, amount);
        return c.getTime();
    }

    /**
     * 是否本年度
     *
     * @param startDate
     * @param endDate
     * @param year
     * @return
     */
    public static boolean isIncludeYear(Date startDate, Date endDate, String year) {
        if (Objects.isNull(startDate) || Objects.isNull(year) || StrUtil.isBlank(year)) {
            return false;
        }
        final int currYear = Integer.parseInt(year);
        final int startYear = cn.hutool.core.date.DateUtil.year(startDate);
        final int endYear = cn.hutool.core.date.DateUtil.year(endDate);
        return !(startYear > currYear || endYear < currYear);
    }


    /**
     * 处理入党时间
     *
     * @param joinOrgDate 入党时间
     * @return 入党时间年度分类
     */
    public static int processJoinOrgDate(Date joinOrgDate) {
        if (Objects.isNull(joinOrgDate)) {
            return 0;
        }
        // 1937  年  7  月  6  日  及  以  前
        if (cn.hutool.core.date.DateUtil.between(joinOrgDate, cn.hutool.core.date.DateUtil.endOfDay(cn.hutool.core.date.DateUtil.parseDate("1937.07.06")), DateUnit.MS, false) >= 0) {
            return 1;
        } else if (cn.hutool.core.date.DateUtil.between(joinOrgDate, cn.hutool.core.date.DateUtil.beginOfDay(cn.hutool.core.date.DateUtil.parseDate("1937.07.07")), DateUnit.MS, false) <= 0 &&
                cn.hutool.core.date.DateUtil.between(joinOrgDate, cn.hutool.core.date.DateUtil.endOfDay(cn.hutool.core.date.DateUtil.parseDate("1945.09.02")), DateUnit.MS, false) >= 0) {
            // 1937年7月7日至1945年9月2日
            return 2;
        } else if (cn.hutool.core.date.DateUtil.between(joinOrgDate, cn.hutool.core.date.DateUtil.beginOfDay(cn.hutool.core.date.DateUtil.parseDate("1945.09.03")), DateUnit.MS, false) <= 0 &&
                cn.hutool.core.date.DateUtil.between(joinOrgDate, cn.hutool.core.date.DateUtil.endOfDay(cn.hutool.core.date.DateUtil.parseDate("1949.09.30")), DateUnit.MS, false) >= 0) {
            // 1945年9月3日至1949年9月
            return 3;
        } else if (cn.hutool.core.date.DateUtil.between(joinOrgDate, cn.hutool.core.date.DateUtil.beginOfDay(cn.hutool.core.date.DateUtil.parseDate("1949.10.01")), DateUnit.MS, false) <= 0 &&
                cn.hutool.core.date.DateUtil.between(joinOrgDate, cn.hutool.core.date.DateUtil.endOfDay(cn.hutool.core.date.DateUtil.parseDate("1966.04.30")), DateUnit.MS, false) >= 0) {
            // 1949 年10月至1966年4月
            return 4;
        } else if (cn.hutool.core.date.DateUtil.between(joinOrgDate, cn.hutool.core.date.DateUtil.beginOfDay(cn.hutool.core.date.DateUtil.parseDate("1966.05.01")), DateUnit.MS, false) <= 0 &&
                cn.hutool.core.date.DateUtil.between(joinOrgDate, cn.hutool.core.date.DateUtil.endOfDay(cn.hutool.core.date.DateUtil.parseDate("1976.10.31")), DateUnit.MS, false) >= 0) {
            // 1966 年5月至1976年10月
            return 5;
        } else if (cn.hutool.core.date.DateUtil.between(joinOrgDate, cn.hutool.core.date.DateUtil.beginOfDay(cn.hutool.core.date.DateUtil.parseDate("1976.11.01")), DateUnit.MS, false) <= 0 &&
                cn.hutool.core.date.DateUtil.between(joinOrgDate, cn.hutool.core.date.DateUtil.endOfDay(cn.hutool.core.date.DateUtil.parseDate("1978.12.31")), DateUnit.MS, false) >= 0) {
            // 1976 年11月至1978年12月
            return 6;
        } else if (cn.hutool.core.date.DateUtil.between(joinOrgDate, cn.hutool.core.date.DateUtil.beginOfDay(cn.hutool.core.date.DateUtil.parseDate("1979.01.01")), DateUnit.MS, false) <= 0 &&
                cn.hutool.core.date.DateUtil.between(joinOrgDate, cn.hutool.core.date.DateUtil.endOfDay(cn.hutool.core.date.DateUtil.parseDate("2002.10.31")), DateUnit.MS, false) >= 0) {
            // 1979年1月至2002年10月
            return 7;
        } else if (cn.hutool.core.date.DateUtil.between(joinOrgDate, cn.hutool.core.date.DateUtil.beginOfDay(cn.hutool.core.date.DateUtil.parseDate("2002.11.01")), DateUnit.MS, false) <= 0 &&
                cn.hutool.core.date.DateUtil.between(joinOrgDate, cn.hutool.core.date.DateUtil.endOfDay(cn.hutool.core.date.DateUtil.parseDate("2012.10.31")), DateUnit.MS, false) >= 0) {
            // 2002年11月至2012年10月
            return 8;
        } else if (cn.hutool.core.date.DateUtil.between(joinOrgDate, cn.hutool.core.date.DateUtil.beginOfDay(cn.hutool.core.date.DateUtil.parseDate("2012.11.01")), DateUnit.MS, false) <= 0) {
            // 2012年11月及以后
            return 9;
        }
        return 0;
    }

    /**
     * 获取某年最后一天
     *
     * @param year 年份
     * @return 年份最后一天
     */
    public static String lastYearDay(String year) {
        Calendar calendar = Calendar.getInstance();

        calendar.clear();

        calendar.set(Calendar.YEAR, Integer.parseInt(year));

        calendar.roll(Calendar.DAY_OF_YEAR, -1);

        Date currYearLast = calendar.getTime();
        Instant instant = currYearLast.toInstant();
        ZoneId zoneId = ZoneId.systemDefault();
        LocalDateTime localDateTime = instant.atZone(zoneId).toLocalDateTime();
        DateTimeFormatter dtf = DateTimeFormatter.ofPattern("yyyy-MM-dd");

        return dtf.format(localDateTime);
    }
}
