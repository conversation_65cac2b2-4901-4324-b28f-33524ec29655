package com.zenith.front.common.annualstatisticsconf;

import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Configuration;

import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.List;

/**
 * 自定义常量
 *
 * <AUTHOR>
 * @since 2021/9/28 10:54
 */
@Configuration
public class AnnualStatisticsConstant {
    private static final String STATISTICS_REGEX_A = "^\\d{4}";
    private static final String STATISTICS_REGEX_B = "^\\d{4}\\d{1,2}\\d{1,2}$";

    /**
     * 统计年度
     */
    public static String STATISTICAL_YEAR;
    /**
     * 是否启用 数据库同步到Es
     */
    public static boolean SYNC_ES_ENABLE;
    /**
     * 是否启用 业务表同步到宽表
     */
    public static boolean SYNC_DB_ENABLE;

    @Value("${statistical.year}")
    public void setStatisticalYear(String statisticalYear) {
        STATISTICAL_YEAR = statisticalYear;
    }

    @Value("${sync.es.enable}")
    public void setSyncEsEnable(boolean esEnable) {
        SYNC_ES_ENABLE = esEnable;
    }

    @Value("${sync.db.enable}")
    public void setSyncDbEnable(boolean dbEnable) {
        SYNC_DB_ENABLE = dbEnable;
    }

    public static final List<String> FORMAT = Arrays.asList("yyyy", "yyyyMMdd");

    public static List<Date> annualStatisticsPeriod() throws Exception {
        List<Date> dateList = new ArrayList<>();
        if (!StrUtil.contains(STATISTICAL_YEAR, ',')) {
            if (STATISTICAL_YEAR.matches(STATISTICS_REGEX_A)) {
                Date date = parseDate(STATISTICAL_YEAR, FORMAT.get(0));
                DateTime beginOfYear = DateUtil.beginOfYear(date);
                DateTime endOfYear = DateUtil.endOfYear(date);
                dateList.add(beginOfYear);
                dateList.add(endOfYear);
                return dateList;
            }
            if (STATISTICAL_YEAR.matches(STATISTICS_REGEX_B)) {
                Date currentDate = parseDate(STATISTICAL_YEAR, FORMAT.get(1));
                DateTime beginOfYear = DateUtil.beginOfYear(currentDate);
                dateList.add(beginOfYear);
                dateList.add(currentDate);
                return dateList;
            }
            throw new IllegalArgumentException("Invalid statistical year value '" + STATISTICAL_YEAR + "'");
        }
        String[] split = STATISTICAL_YEAR.split(",");
        if (split.length > 2) {
            throw new IllegalArgumentException("Invalid statistical year value '" + STATISTICAL_YEAR + "'");
        }
        String date1 = split[0];
        String date2 = split[1];
        if (date1.matches(STATISTICS_REGEX_B) && date2.matches(STATISTICS_REGEX_B)) {
            Date beginOfYear = parseDate(date1, FORMAT.get(1));
            Date endOfYear = parseDate(date2, FORMAT.get(1));
            dateList.add(beginOfYear);
            dateList.add(endOfYear);
            return dateList;
        }
        throw new IllegalArgumentException("Invalid statistical year value '" + STATISTICAL_YEAR + "'");
    }


    /**
     * 格式化日期
     *
     * @param dateStr String 字符型日期
     * @param format  String 格式
     * @return Date 日期
     */
    public static Date parseDate(String dateStr, String format) throws Exception {
        DateFormat dateFormat = new SimpleDateFormat(format);
        return dateFormat.parse(dateStr);
    }
}
