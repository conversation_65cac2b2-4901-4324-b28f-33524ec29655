package com.zenith.front.common.untils;

import com.alibaba.druid.util.StringUtils;
import org.apache.commons.codec.binary.Base64;
import sun.misc.BASE64Decoder;
import sun.misc.BASE64Encoder;

import javax.crypto.Cipher;
import javax.crypto.KeyGenerator;
import javax.crypto.SecretKey;
import javax.crypto.spec.SecretKeySpec;
import java.math.BigInteger;
import java.nio.charset.StandardCharsets;
import java.security.SecureRandom;

/**
 * @author: D.watermelon
 * @date: 2019/9/17 17:24
 * @description: 入则恳恳以尽忠 出则谦谦以自悔
 */
public class AESUntil {
    public static String AESEncode(String encodeRuless, String content) throws Exception {
        //1.构造密钥生成器，指定为AES算法,不区分大小写
        //KeyGenerator keygen=KeyGenerator.getInstance("AES");
        //2.根据ecnodeRules规则初始化密钥生成器
        //生成一个128位的随机源,根据传入的字节数组
        //keygen.init(256, new SecureRandom(encodeRuless.getBytes()));
        String charset = "utf-8";
        KeyGenerator keygen = KeyGenerator.getInstance("AES");// 创建AES的Key生产者
        SecureRandom secureRandom = SecureRandom.getInstance("SHA1PRNG");
        secureRandom.setSeed(encodeRuless.getBytes(charset));
        keygen.init(256, secureRandom);

        //3.产生原始对称密钥
        SecretKey original_key = keygen.generateKey();
        //4.获得原始对称密钥的字节数组
        byte[] raw = original_key.getEncoded();
        //5.根据字节数组生成AES密钥
        SecretKey key = new SecretKeySpec(raw, "AES");
        //6.根据指定算法AES自成密码器
        Cipher cipher = Cipher.getInstance("AES");
        //7.初始化密码器，第一个参数为加密(Encrypt_mode)或者解密解密(Decrypt_mode)操作，第二个参数为使用的KEY
        cipher.init(Cipher.ENCRYPT_MODE, key);
        //8.获取加密内容的字节数组(这里要设置为utf-8)不然内容中如果有中文和英文混合中文就会解密为乱码
        byte[] byte_encode = content.getBytes(StandardCharsets.UTF_8);
        //9.根据密码器的初始化方式--加密：将数据加密
        byte[] byte_AES = cipher.doFinal(byte_encode);
        //10.将加密后的数据转换为字符串
        //这里用Base64Encoder中会找不到包
        //解决办法：
        //在项目的Build path中先移除JRE System Library，再添加库JRE System Library，重新编译后就一切正常了。
        String AES_encode = new BASE64Encoder().encode(byte_AES);
        //11.将字符串返回
        String encode = AES_encode.replaceAll("\r\n", "");
        AES_encode.replaceAll("\\r", "");
        return encode;
    }

    /*
     * 解密
     * 解密过程：
     * 1.同加密1-4步
     * 2.将加密后的字符串反纺成byte[]数组
     * 3.将加密内容解密
     */
    public static String AESDncode(String encodeRules, String content) throws Exception {
        //1.构造密钥生成器，指定为AES算法,不区分大小写
//            KeyGenerator keygen=KeyGenerator.getInstance("AES");
//            //2.根据ecnodeRules规则初始化密钥生成器
//            //生成一个128位的随机源,根据传入的字节数组
//            keygen.init(256, new SecureRandom(encodeRules.getBytes()));
        String charset = "utf-8";
        KeyGenerator keygen = KeyGenerator.getInstance("AES");// 创建AES的Key生产者
        SecureRandom secureRandom = SecureRandom.getInstance("SHA1PRNG");
        secureRandom.setSeed(encodeRules.getBytes(charset));
        keygen.init(256, secureRandom);
        //3.产生原始对称密钥
        SecretKey original_key = keygen.generateKey();
        //4.获得原始对称密钥的字节数组
        byte[] raw = original_key.getEncoded();
        //5.根据字节数组生成AES密钥
        SecretKey key = new SecretKeySpec(raw, "AES");
        //6.根据指定算法AES自成密码器
        Cipher cipher = Cipher.getInstance("AES");
        //7.初始化密码器，第一个参数为加密(Encrypt_mode)或者解密(Decrypt_mode)操作，第二个参数为使用的KEY
        cipher.init(Cipher.DECRYPT_MODE, key);
        //8.将加密并编码后的内容解码成字节数组
        byte[] byte_content = new BASE64Decoder().decodeBuffer(content);
        /*
         * 解密
         */
        byte[] byte_decode = cipher.doFinal(byte_content);
        return new String(byte_decode, StandardCharsets.UTF_8);
    }

//    public static void main(String[] args) throws Exception {
//        String data="123456";
//        String pass2="d41d8cd98f00b204e9800998ecf8427e";
//        String s2 = AESEncode(pass2, data);
//        System.out.println("加密后字符串为:"+s2);
//
//        String s3 = AESDncode(pass2, s2);
//        System.out.println("解密后字符串为:"+s3);
//    }


    //算法 AES/ECB/PKCS5Padding
    private static final String ALGORITHMSTR = "AES";
    private static final String AES_KEY = "AESNBHB3ZA==HKXt";

    /**
     * aes解密
     * @param encrypt   内容
     * @return
     * @throws Exception
     */
    public static String aesDecrypt(String encrypt) {
        try {
            return aesDecrypt(encrypt, AES_KEY);
        } catch (Exception e) {
            e.printStackTrace();
            return "";
        }
    }

    /**
     * aes加密
     * @param content
     * @return
     * @throws Exception
     */
    public static String aesEncrypt(String content) {
        try {
            return aesEncrypt(content, AES_KEY);
        } catch (Exception e) {
            e.printStackTrace();
            return "";
        }
    }

    /**
     * 将byte[]转为各种进制的字符串
     * @param bytes byte[]
     * @param radix 可以转换进制的范围，从Character.MIN_RADIX到Character.MAX_RADIX，超出范围后变为10进制
     * @return 转换后的字符串
     */
    public static String binary(byte[] bytes, int radix){
        return new BigInteger(1, bytes).toString(radix);// 这里的1代表正数
    }

    /**
     * base 64 encode
     * @param bytes 待编码的byte[]
     * @return 编码后的base 64 code
     */
    public static String base64Encode(byte[] bytes){
        return Base64.encodeBase64String(bytes);
    }

    /**
     * base 64 decode
     * @param base64Code 待解码的base 64 code
     * @return 解码后的byte[]
     * @throws Exception
     */
    public static byte[] base64Decode(String base64Code) throws Exception{
        return StringUtils.isEmpty(base64Code) ? null : new BASE64Decoder().decodeBuffer(base64Code);
    }


    /**
     * AES加密
     * @param content 待加密的内容
     * @param encryptKey 加密密钥
     * @return 加密后的byte[]
     * @throws Exception
     */
    public static byte[] aesEncryptToBytes(String content, String encryptKey) throws Exception {
        KeyGenerator kgen = KeyGenerator.getInstance("AES");
        kgen.init(128);
        Cipher cipher = Cipher.getInstance(ALGORITHMSTR);
        cipher.init(Cipher.ENCRYPT_MODE, new SecretKeySpec(encryptKey.getBytes(), "AES"));

        return cipher.doFinal(content.getBytes(StandardCharsets.UTF_8));
    }


    /**
     * AES加密为base 64 code
     * @param content 待加密的内容
     * @param encryptKey 加密密钥
     * @return 加密后的base 64 code
     * @throws Exception
     */
    public static String aesEncrypt(String content, String encryptKey) throws Exception {
        return base64Encode(aesEncryptToBytes(content, encryptKey));
    }

    /**
     * AES解密
     * @param encryptBytes 待解密的byte[]
     * @param decryptKey 解密密钥
     * @return 解密后的String
     * @throws Exception
     */
    public static String aesDecryptByBytes(byte[] encryptBytes, String decryptKey) throws Exception {
        KeyGenerator kgen = KeyGenerator.getInstance("AES");
        kgen.init(128);
        Cipher cipher = Cipher.getInstance(ALGORITHMSTR);
        cipher.init(Cipher.DECRYPT_MODE, new SecretKeySpec(decryptKey.getBytes(), "AES"));
        byte[] decryptBytes = cipher.doFinal(encryptBytes);
        return new String(decryptBytes);
    }


    /**
     * 将base 64 code AES解密
     * @param encryptStr 待解密的base 64 code
     * @param decryptKey 解密密钥
     * @return 解密后的string
     * @throws Exception
     */
    public static String aesDecrypt(String encryptStr, String decryptKey) throws Exception {
        return StringUtils.isEmpty(encryptStr) ? null : aesDecryptByBytes(base64Decode(encryptStr), decryptKey);
    }

    public static String checkData(String passWord) throws Exception {
        return aesDecrypt(passWord, AES_KEY);
    }

    /**
     * 测试
     * 前端js将参数加密提交到后台如何解密
     * 首先获取服务端的私钥:将客户端的公钥加密后获得的结果
     * 通过服务端的私钥和客户端传递的加密字符串即可实现解密
     */
//    public static void main(String[] args) throws Exception {
//        String content = "123456";
//        System.out.println("加密前：" + content);
//        System.out.println("加密密钥和解密密钥：" + AES_KEY);
//        String encrypt = aesEncrypt(content, AES_KEY);
//        System.out.println("加密后：" + encrypt);
//   /*     String decrypt = aesDecrypt(encrypt, AES_KEY);
//        System.out.println("解密后：" + decrypt);*/
//        //js加密后的字符串: tLWpQV+OIFZOWO7yp00Ruw==
//        String jsData = aesDecrypt("J0XNyesfm4txH6RakKVBQQ==", AES_KEY);
//        System.out.println("前端数据解密后的值:" + jsData);
//
//    }



}
