package com.zenith.front.common.constant;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @version 1.0
 * @Description:
 * @date 2019/4/24 15:39
 */
public class RepresentativeConstant {

    public static final List<String> ORG_TYPE_LIST = new ArrayList<String>() {
        private static final long serialVersionUID = 8546601250678072235L;

        {
        /**
         * 11 | 中共中央委员会
         */
        add("11");
        /**
         * 123 | 中共中央直辖市委员会
         */
        add("123");
        /**
         * 134 | 中共中央直辖市属区委员会
         */
        add("134");
        /**
         * 145 | 中共中央直辖市属县委员会
         */
        add("145");
    }};

    public static final List<String> UNIT_TYPE_LIST = new ArrayList<String>() {
        private static final long serialVersionUID = -3856119864830672123L;

        {
        /**
         * 9121 | 乡
         */
        add("9121");
        /**
         * 9122 | 镇
         */
        add("9122");
    }};

    public static final Map<String, String> ELECT_TYPE_MAP = new HashMap<String, String>() {
        private static final long serialVersionUID = -2281623770923756216L;

        {
        /**
         * 单位类型 -- 乡
         */
        put("9121", "41");
        /**
         * 单位类型 -- 镇
         */
        put("9122", "42");
        /**
         * 组织类型 -- 全国
         */
        put("111", "1");
        /**
         * 组织类型 -- 省市
         */
        put("123", "2");
        /**
         * 组织类型 -- 区
         */
        put("134", "31");
        /**
         * 组织类型 -- 县
         */
        put("145", "32");

    }};

    /**
     * 领导职务
     */
    public static final String D46_LDZW = "011";
}
