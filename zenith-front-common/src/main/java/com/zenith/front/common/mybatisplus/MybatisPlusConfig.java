package com.zenith.front.common.mybatisplus;

import com.baomidou.mybatisplus.annotation.DbType;
import com.baomidou.mybatisplus.extension.plugins.MybatisPlusInterceptor;
import com.baomidou.mybatisplus.extension.plugins.handler.TableNameHandler;
import com.baomidou.mybatisplus.extension.plugins.inner.DynamicTableNameInnerInterceptor;
import com.baomidou.mybatisplus.extension.plugins.inner.PaginationInnerInterceptor;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.util.*;

/**
 * <AUTHOR>
 */
@Configuration
public class MybatisPlusConfig {

    /**
     * 使用ThreadLocal将表名传进来
     */
    public static final ThreadLocal<String> MY_TABLE_NAME_LOCAL = new ThreadLocal<>();

    private static final String TABLE_NAME = "dict";

    @Bean
    public MybatisPlusInterceptor mybatisPlusInterceptor() {
        MybatisPlusInterceptor interceptor = new MybatisPlusInterceptor();
        DynamicTableNameInnerInterceptor dynamicTableNameInnerInterceptor = new DynamicTableNameInnerInterceptor();
        HashMap<String, TableNameHandler> map = new HashMap<String, TableNameHandler>(20) {
            private static final long serialVersionUID = -6945499966162365225L;

            {
                //字典表
                put(TABLE_NAME, (sql, tableName) -> MY_TABLE_NAME_LOCAL.get());
            }
        };
        MY_TABLE_NAME_LOCAL.remove();
        dynamicTableNameInnerInterceptor.setTableNameHandlerMap(map);
        interceptor.addInnerInterceptor(dynamicTableNameInnerInterceptor);
        //向Mybatis过滤器链中添加分页拦截器
        //新的分页插件,一缓和二缓遵循mybatis的规则,需要设置 MybatisConfiguration#useDeprecatedExecutor = false 避免缓存出现问题(该属性会在旧插件移除后一同移除)
        PaginationInnerInterceptor paginationInnerInterceptor = new PaginationInnerInterceptor(DbType.POSTGRE_SQL);
        //分页单页pageSize最大设置
        paginationInnerInterceptor.setMaxLimit(10000L);
        interceptor.addInnerInterceptor(paginationInnerInterceptor);
        return interceptor;
    }
}
