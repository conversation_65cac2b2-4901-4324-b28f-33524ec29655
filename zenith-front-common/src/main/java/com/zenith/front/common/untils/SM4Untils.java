package com.zenith.front.common.untils;

import cn.hutool.core.util.CharsetUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.crypto.SmUtil;
import cn.hutool.crypto.symmetric.SymmetricCrypto;
import com.zenith.front.common.constant.CommonConstant;
import com.zenith.front.common.encrypt.properties.EncryptProperties;
import com.zenith.front.common.password.JmjUtil;
import lombok.extern.slf4j.Slf4j;

/**
 * @author: D.watermelon
 * @date: 2022/7/5 16:31
 * @description: 入则恳恳以尽忠 出则谦谦以自悔
 */
@Slf4j
public class SM4Untils {

    private static final String defultKey="zenith02368601166";

    /**
     * SM4加密
     * @param key 加密key
     * @param content 内容
     * @return String 加密后内容
     * @remark 加密内容带盐值
     */
    public static String encryptContent(String key, String content) {
        if (!EncryptProperties.enable || StrUtil.isBlank(content)) {
            return content;
        }

        long startTime = System.currentTimeMillis();
        String data = JmjUtil.InternalSM4Enc(content);
        long endTime = System.currentTimeMillis();

        // 获取调用堆栈信息
        String callerInfo = getCallerInfo();

        log.info("JMJ加密耗时: {}ms, 调用链: {}", endTime - startTime, callerInfo);

        if(StrUtil.isBlank(data)){
            return content;
        }
        return data;
//        log.error("JMJ 数据加密失败，进行原始逻辑加密！");
//        SymmetricCrypto sm4Object = SmUtil.sm4(checkKey(key).getBytes());
//        return sm4Object.encryptHex(content);
    }

    public static String encryptContentOld(String key, String content) {
        if (!EncryptProperties.enable || StrUtil.isBlank(content)) {
            return content;
        }
        SymmetricCrypto sm4Object = SmUtil.sm4(checkKey(key).getBytes());
        return sm4Object.encryptHex(content);
    }

    /***
     * SM4解密
     * @param key 加密key
     * @param content 内容
     * @return String 解密后内容
     * @remark 加密内容带盐值
     */
    public static String decryptContent(String key, String content){
        if (!EncryptProperties.enable || StrUtil.isBlank(content)) {
            return content;
        }
        long startTime = System.currentTimeMillis();
        String data = JmjUtil.InternalSM4Dec(content);
        long endTime = System.currentTimeMillis();

        // 获取调用堆栈信息
        String callerInfo = getCallerInfo();

        log.info("JMJ解密耗时: {}ms, 调用链: {}", endTime - startTime, callerInfo);

        if(StrUtil.isBlank(data)){
            return content;
        }
        return data;
//        log.error("JMJ 数据解密失败，进行原始逻辑解密！");
//        SymmetricCrypto sm4Object = SmUtil.sm4(checkKey(key).getBytes());
//        return sm4Object.decryptStr(content,CharsetUtil.CHARSET_UTF_8);
    }

    public static String decryptContentOld(String key, String content){
        SymmetricCrypto sm4Object = SmUtil.sm4(checkKey(key).getBytes());
        return sm4Object.decryptStr(content,CharsetUtil.CHARSET_UTF_8);
    }


    /**
     * SM4加密
     * @param content 内容
     * @return String 加密后内容
     * @remark 加密内容不带盐值
     */
    public static String  encryptContent(String content){
        return encryptContent(defultKey,content);
    }

    /***
     * SM4解密
     * @param content 内容
     * @return String 解密后内容
     * @remark 加密内容不带盐值
     */
    public static String decryptContent(String content){
        return decryptContent(defultKey,content);
    }

    /**
     * key不足16位时自动补足问题
     * **/
    private static String checkKey(String key){
        if (StrUtil.isBlank(key)){
            key=CommonConstant.BASE_STR;
        }

        if (key.length()< CommonConstant.SIXTEEN_INT){
            key=key+CommonConstant.BASE_STR;
        }

        if (key.length()>CommonConstant.SIXTEEN_INT){
            key=key.substring(CommonConstant.ZERO_INT,CommonConstant.SIXTEEN_INT);
        }
        return key;
    }

    /**
     * 获取调用方信息
     */
    private static String getCallerInfo() {
        return null;
//        StackTraceElement[] stackTrace = Thread.currentThread().getStackTrace();
//        StringBuilder callerInfo = new StringBuilder();
//
//        for (int i = 4; i < maxDepth; i++) {
//            StackTraceElement element = stackTrace[i];
//            String className = element.getClassName();
//
//            // 简化类名，只显示最后一部分
//            String simpleClassName = className.substring(className.lastIndexOf('.') + 1);
//
//            if (callerInfo.length() > 0) {
//                callerInfo.append(" -> ");
//            }
//            callerInfo.append(simpleClassName)
//                     .append(".")
//                     .append(element.getMethodName())
//                     .append(":")
//                     .append(element.getLineNumber());
//        }
//
//        return callerInfo.toString();
    }

    public static void main(String[] args) {
        System.out.println(decryptContentOld("38029070b0194a35", "d992c09e759894720b7860600408c211"));
    }

}
