package com.zenith.front.common.kit;

/**
 * <AUTHOR>
 * @version 1.0
 * @Description:
 * @date 2019/3/18 15:32
 */
public class PageUtil {
    /**
     * 验证分页参数
     *
     * @param num
     * @return
     */
    public static int verifyInt(int num, int para) {
        return (num <= 0 || num > 100) ? para : num;
    }

    /**
     * 验证分页参数
     *
     * @param num
     * @return
     */
    public static int verifyInt(Integer num, int para) {
        return (num == null || num <= 0 || num > 100) ? para : num;
    }
}
