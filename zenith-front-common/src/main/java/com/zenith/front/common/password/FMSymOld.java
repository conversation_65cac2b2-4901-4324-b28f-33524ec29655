package com.zenith.front.common.password;

import cdf.jce.provider.CDFJCE;
import cdf.jce.spec.InternalKeyParameterSpec;
import com.zenith.front.common.safe.Base64;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.ResourceUtils;

import javax.crypto.Cipher;
import javax.crypto.Mac;
import javax.crypto.SecretKey;
import javax.crypto.spec.IvParameterSpec;
import java.net.URLDecoder;
import java.nio.charset.StandardCharsets;
import java.security.SecureRandom;
import java.security.Security;

@Slf4j
public class FMSymOld {

    public static void init(String confPath) {
        Security.addProvider(new CDFJCE(confPath));
    }


    public static void initFmSym() {
        try {
            String confPath = URLDecoder.decode(ResourceUtils.getURL("classpath:").getPath(), "UTF-8");
            FMSymOld.init(confPath);
            log.info("初始化 FmSym 成功！");
        } catch (Exception e) {
            log.error("初始化 FmSym 失败！", e);
        }
    }

    /**
     * 内部对称密钥加密运算
     * @param keyAlg 算法类型只支持SM1/SM4，默认SM4
     * @param keyId 密钥号，各个厂家不要使用同一个密钥
     * @param mode "CBC"或者"ECB"，密评默认CBC模式
     * @param isPad true为内部打补丁，即输入数据可为任意长度;false为上层打补丁，即输入数据必须为密钥模长的整数倍，默认true
     * @param inData 待加密数据
     * @param iv iv值，CBC模式必须传一个16字节的随机数组，加密解密保证这个值一样
     * @return 加密后的数据，加密之后的数据直接转为String类型是乱码，需要先加Base64编码再转成String类型进行存储
     */
    public static byte[] internalSymEncrypt(String keyAlg, int keyId, String mode, boolean isPad, byte[] inData, byte[] iv) {
        /*alg:参数格式"算法名称/模式/打补丁方式"；*/
        /* 如"SM1/ECB/NOPADDING"为SM1算法，ECB模式，不打补丁*/
        /* "SM1/CBC/PKCS5PADDING"为SM1算法，CBC模式，打补丁*/
        String alg = keyAlg + "/" + mode + "/";
        byte[] cipherData = null;
        byte[] tail = null;
        SecretKey key = null;
        byte[] ret = null;
        IvParameterSpec ivspe = null;
        if (isPad) {
            alg += "PKCS5PADDING";
        } else {
            alg += "NOPADDING";
        }
        String sysAlg = "Random" + keyAlg + keyId;
        try {
            SecureRandom ran = SecureRandom.getInstance(sysAlg, "CDFJCE");
            Cipher cp = Cipher.getInstance(alg, "CDFJCE");
            if (mode.equalsIgnoreCase("CBC")) {
                ivspe = new IvParameterSpec(iv, 0, 16);
                cp.init(Cipher.ENCRYPT_MODE, key, ivspe, ran); //key不起作用，ran中密钥号起作用
            } else {
                cp.init(Cipher.ENCRYPT_MODE, key, ran);
            }
            ret = cp.doFinal(inData);
        } catch (Exception e) {
            log.error(keyAlg + " internal encrypt error");
            log.error("加密失败",e);
        }
        return ret;
    }

    /**
     * 内部对称密钥解密运算
     * @param keyAlg 算法类型只支持SM1/SM4，默认SM4
     * @param keyId 密钥号，和加密时使用的密钥号相同
     * @param mode "CBC"或者"ECB"，密评默认CBC模式
     * @param isPad true为内部打补丁，即输入数据可为任意长度;false为上层打补丁，即输入数据必须为密钥模长的整数倍，默认true
     * @param inData 待解密数据，如果之前做了Base64编码需要先解Base64后再解密
     * @param iv iv值，CBC模式必须传一个16字节的随机数组，加密解密保证这个值一样
     * @return 解密后的数据
     */
    public static byte[] internalSymDecrypt(String keyAlg, int keyId, String mode, boolean isPad, byte[] inData, byte[] iv) {
        /*alg:参数格式"算法名称/模式/打补丁方式"；*/
        /* 如"SM1/ECB/NOPADDING"为SM1算法，ECB模式，不打补丁*/
        /* "SM1/CBC/PKCS5PADDING"为SM1算法，CBC模式，打补丁*/
        String alg = keyAlg + "/" + mode + "/";
        byte[] data = null;
        byte[] tail = null;
        SecretKey key = null;
        byte[] ret = null;
        IvParameterSpec ivspe = null;
        if (isPad) {
            alg += "PKCS5PADDING";
        } else {
            alg += "NOPADDING";
        }
        String sysAlg = "Random" + keyAlg + keyId;
        try {
            SecureRandom ran = SecureRandom.getInstance(sysAlg, "CDFJCE");
            Cipher cp = Cipher.getInstance(alg, "CDFJCE");
            if (mode.equalsIgnoreCase("CBC")) {
                ivspe = new IvParameterSpec(iv, 0, 16);
                cp.init(Cipher.DECRYPT_MODE, key, ivspe, ran);
            } else {
                cp.init(Cipher.DECRYPT_MODE, key, ran);
            }
            ret = cp.doFinal(inData);
        } catch (Exception e) {
//            log.error("internal decrypt error："+cdf.util.encoders.Base64.decode(inData)+" "+e.getStackTrace()[0].getMethodName()+" "+e.toString());
            log.error(alg + " internal decrypt error");
            log.error("解密失败",e);
        }
        return ret;
    }





    /**
     * HMAC算法
     * @param keyNum 对称密钥号，和加解密用一个密钥号
     * @param keyLen 对称密钥长度，16
     * @param macAlg "HMac/SM3"
     * @param inData  输入数据，固定32字节
     * @return HMAC结果，使用Base64编码后再存储
     */
    public static byte[] hMacInnerKey(int keyNum, int keyLen, String macAlg, byte[] inData) throws Exception {
        byte[] result = null;
//        try {
        Mac mac = Mac.getInstance(macAlg, "CDFJCE");
        mac.init(null, new InternalKeyParameterSpec(keyNum, keyLen));
        mac.reset();
        mac.update(inData, 0, inData.length);
        result = mac.doFinal();
//        } catch (Exception e) {
//            log.error("hMac error");
//            e.printStackTrace();
//        }
        return result;
    }

    public static void main(String[] args) throws Exception {
        //必须执行读取配置文件
        // 先尝试从classpath读取
        String confPath = "D:\\work\\code\\work\\zenith-front-dj\\zenith-front-web\\src\\main\\resources\\";

//        for (int i = 0; i < 1000; i++) {
//            String data = "测试测试";
//            FMSym.init(confPath);
////        String s1 = JmjUtil.InternalSM4Enc(data);
////        System.out.println(s1);
//            String s1= "LhVmZrAWWECJY00TEWM2Hg==";
//            String s2 = JmjUtil.InternalSM4Dec(s1);
//            System.out.println(s2);
//            System.out.println(JmjUtil.sm3Hac(data));
//        }yGaafqat6COfFJmRn2ve5Q==
        String data = "李江华";
        FMSymOld.init(confPath);
//        String s1 = JmjUtil.InternalSM4Enc(data);
//        System.out.println(s1);
//         s1 = "5L2Z5rW354eVBwcHBwcHBw==";
//         s1 = "xPCIkOZtJCfzYjgMpNfIow==";
        String s2 = JmjUtil.InternalSM4Dec("/e7so9rrIUbmfWKHGI6cqQ==");
        System.out.println(s2);
        System.out.println(Base64.encode(s2.getBytes(StandardCharsets.UTF_8)));
//        System.out.println(JmjUtil.sm3Hac(data));
    }
}


