package com.zenith.front.common.mybatisplus;

import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.zenith.front.common.constant.CommonConstant;
import com.zenith.front.common.encrypt.typehandler.EncryptTypeHandler;
import com.zenith.front.common.encrypt.utli.ReflectionUtil;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @create_date 2025-07-21 10:43
 * @description
 */
public class WrapperUtil {

    /**
     * 1、保留指定加密字段
     * 2、如果没指定需要返回加密的字段，则所有加密字段都剔除
     * 3、如果前面select了非加密字段将保留返回
     * @param queryWrapper
     * @param clazz
     * @param encipherFieldName 查询返回指定加密字段
     * @param <T>
     * @return
     */
    public static <T> LambdaQueryWrapper<T> existsEncrypt(LambdaQueryWrapper<T> queryWrapper, Class <T> clazz, String... encipherFieldName) {

        // 前面添加了查询语句的
        String sqlSelect = queryWrapper.getSqlSelect();
        List<String> columnList = new ArrayList<>();
        if(StrUtil.isNotBlank(sqlSelect)) {
            columnList = Arrays.asList(sqlSelect.split(CommonConstant.DOU_HAO_STRING));
        }
        final List<String> finalColumnList = columnList;
        queryWrapper.select(clazz, et -> {
            TableField field = et.getField().getAnnotation(TableField.class);
            if(field.typeHandler().equals(EncryptTypeHandler.class)) {
                // 如果没指定需要返回加密的字段，则所有加密字段都不返回查询
                if(Objects.isNull(encipherFieldName) || encipherFieldName.length == 0) {
                    return false;
                }
                for (String s : encipherFieldName) {
                    if(et.getProperty().equals(s)) {
                        return true;
                    }
                }
                return false;
            }
            // 保留指定查询了的非加密字段
            if (finalColumnList.size() > 0) {
                return finalColumnList.contains(et.getColumn());
            } else {
                return true;
            }
        });
        return queryWrapper;
    }

    /**
     * 排除所有加密字段
     * @param queryWrapper
     * @param clazz
     * @return
     * @param <T>
     */
    public static <T> LambdaQueryWrapper<T> excludeEncrypt(LambdaQueryWrapper<T> queryWrapper,Class <T> clazz) {
        queryWrapper.select(clazz, et -> !ReflectionUtil.getEntityEncryptFields(clazz).contains(et.getProperty()));
        return queryWrapper;
    }
}
