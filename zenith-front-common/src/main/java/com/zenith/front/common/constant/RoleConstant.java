package com.zenith.front.common.constant;

/**
 * <AUTHOR>
 * @version 1.0
 * @Description: 角色相关常量
 * @date 2019/3/12 11:35
 */
public class RoleConstant {
    /**
     * 每个层级最大的code
     */
    public static final String MAXCODE = "99";
    /**
     * 每个层级有几位
     */
    public static final int CODETIER = 2;
    /**
     * 每个层级第一个层级码
     */
    public static final String FRISTCODE = "10";
    /**
     * 是系统内建角色
     */
    public static final int IS_BUILT_IN = 1;
    /**
     * 不是系统内建角色
     */
    public static final int NOT_BUILT_IN = 0;
    /**
     * 生成角色层级码前缀
     */
    public static final String ROLE_CODE_PRE = "R";
    /***
     * 角色被删除
     * */
    public static final int IS_DELETE = 1;
    /***
     * 角色未被删除
     * */
    public static final int NOT_IS_DELETE = 0;
    /**
     * 角色类型 超级管理员
     * */
    public static final int ROLE_TYPE_SUPER_ADMIN = 1;

}
