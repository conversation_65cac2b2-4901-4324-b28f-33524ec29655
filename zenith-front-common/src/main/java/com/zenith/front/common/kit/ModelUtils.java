package com.zenith.front.common.kit;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.bean.copier.CopyOptions;
import cn.hutool.core.util.StrUtil;
import org.springframework.cglib.core.ReflectUtils;

import java.beans.PropertyDescriptor;
import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;
import java.util.*;

/**
 * <AUTHOR>
 * @date 2019/3/134:30 PM
 * 后续废弃
 */
@Deprecated
public class ModelUtils {

    /**
     * DTO 转 Mmodel
     */
    public static <T, B> B dtoTOModel(T source, B targetObj) {
        return copyAndParse(source, targetObj);
    }

    /**
     * 转换为下划线
     *
     * @param camelCaseName
     * @return
     */
    public static String underscoreName(String camelCaseName) {
        StringBuilder result = new StringBuilder();
        if (camelCaseName != null && camelCaseName.length() > 0) {
            result.append(camelCaseName.substring(0, 1).toLowerCase());
            for (int i = 1; i < camelCaseName.length(); i++) {
                char ch = camelCaseName.charAt(i);
                if (Character.isUpperCase(ch)) {
                    result.append("_");
                    result.append(Character.toLowerCase(ch));
                } else {
                    result.append(ch);
                }
            }
        }
        return result.toString();
    }

    /**
     * 转换为驼峰
     *
     * @param underscoreName
     * @return
     */
    public static String camelCaseName(String underscoreName) {
        StringBuilder result = new StringBuilder();
        if (underscoreName != null && underscoreName.length() > 0) {
            boolean flag = false;
            for (int i = 0; i < underscoreName.length(); i++) {
                char ch = underscoreName.charAt(i);
                if ('_' == ch) {
                    flag = true;
                } else {
                    if (flag) {
                        result.append(Character.toUpperCase(ch));
                        flag = false;
                    } else {
                        result.append(ch);
                    }
                }
            }
        }
        return result.toString();
    }

    /**
     * 利用反射实现Model对象之间属性复制
     *
     * @param from
     * @param to
     */
    public static void copyProperties(Object from, Object to) throws Exception {
        copyPropertiesExclude(from, to, null);
    }

    /**
     * 利用反射实现Model对象之间属性复制
     *
     * @param from
     * @param to
     */
    public static void copyPropertiesToVO(Object from, Object to) throws Exception {
        copyPropertiesToVOExclude(from, to, null);
    }

    /**
     * 利用反射实现Model对象之间属性复制
     *
     * @param from
     * @param to
     */
    public static void copyPropertiesModel(Object from, Object to) throws Exception {
        copyPropertiesModelExclude(from, to, (String) null);
    }

    /**
     * 利用反射实现Model转成model,会复制空值
     *
     * @param from
     * @param to
     */
    public static void copyPropertiesModelExclude(Object from, Object to, String... strings) throws Exception {
        List<String> excludesList = null;
        if (strings != null && strings.length > 0) {
            //构造列表对象
            excludesList = Arrays.asList(strings);
        }
        // 针对有BaseModel的情况
        Method[] fromMethods = from.getClass().getDeclaredMethods();
        Method[] toMethods = to.getClass().getDeclaredMethods();

        Method fromMethod;
        String fromMethodName;
        for (Method method : fromMethods) {
            fromMethod = method;
            fromMethodName = fromMethod.getName();
            if (!fromMethodName.contains("get")) {
                continue;
            }
            //排除列表检测
            String str = fromMethodName.substring(3);
            if (excludesList != null && excludesList.contains(str.substring(0, 1).toLowerCase() + str.substring(1))) {
                continue;
            }
            String toMethodName;
            Method toMethod;
            toMethodName = "set" + fromMethodName.substring(3);
            toMethod = findMethodByName(toMethods, toMethodName);
            if (toMethod == null) {
                continue;
            }
            Object value = fromMethod.invoke(from);

            //集合类判空处理
            if (value instanceof Collection) {
                Collection newValue = (Collection) value;
                if (newValue.size() <= 0) {
                    continue;
                }
            }
            toMethod.invoke(to, value);
        }

    }

    /**
     * 利用反射实现Model转成bean
     *
     * @param from
     * @param to
     */
    private static void copyPropertiesToVOExclude(Object from, Object to, String[] excludsArray) throws InvocationTargetException, IllegalAccessException {
        List<String> excludesList = null;
        if (excludsArray != null && excludsArray.length > 0) {
            //构造列表对象
            excludesList = Arrays.asList(excludsArray);
        }
        // 针对有BaseModel的情况
        Method[] fromMethods = from.getClass().getDeclaredMethods();
        Method[] toMethods = to.getClass().getDeclaredMethods();
        findGetMethodName(from, to, excludesList, fromMethods, toMethods);
    }

    /**
     * 复制属性,空值也会复制
     *
     * @param from
     * @param to
     * @param excludsArray 排除空值复制集合
     */
    public static void copyPropertiesWithNull(Object from, Object to, String... excludsArray) throws Exception {
        List<String> excludesList = null;
        if (excludsArray != null && excludsArray.length > 0) {
            //构造列表对象
            excludesList = Arrays.asList(excludsArray);
        }

        // 针对有BaseModel的情况
        Method[] fromMethods = from.getClass().getDeclaredMethods();
        Method[] toMethods = to.getClass().getDeclaredMethods();
        Method fromMethod;
        String fromMethodName;
        for (Method method : fromMethods) {
            fromMethod = method;
            fromMethodName = fromMethod.getName();
            if (!fromMethodName.contains("get")) {
                continue;
            }

            //排除列表检测
            String str = fromMethodName.substring(3);
            if (excludesList != null && excludesList.contains(str.substring(0, 1).toLowerCase() + str.substring(1))) {
                continue;
            }

            String toMethodName;
            Method toMethod;
            toMethodName = "set" + fromMethodName.substring(3);
            toMethod = findMethodByName(toMethods, toMethodName);
            if (toMethod == null) {
                continue;
            }
            Object value = fromMethod.invoke(from);
            toMethod.invoke(to, value);
        }
    }

    /**
     * 获取get方法的方法名称
     *
     * @param from
     * @param to
     * @param excludesList
     * @param fromMethods
     * @param toMethods
     * @throws IllegalAccessException
     * @throws InvocationTargetException
     */
    private static void findGetMethodName(Object from, Object to, List<String> excludesList, Method[] fromMethods, Method[] toMethods) throws IllegalAccessException, InvocationTargetException {
        Method fromMethod;
        String fromMethodName;
        for (Method method : fromMethods) {
            fromMethod = method;
            fromMethodName = fromMethod.getName();
            if (!fromMethodName.contains("get")) {
                continue;
            }
            //排除列表检测
            String str = fromMethodName.substring(3);
            if (excludesList != null && excludesList.contains(str.substring(0, 1).toLowerCase() + str.substring(1))) {
                continue;
            }
            setMethodMutator(from, to, toMethods, fromMethod, fromMethodName);
        }
    }

    /**
     * 复制对象属性
     *
     * @param from
     * @param to
     * @param excludsArray 排除属性列表
     * @throws Exception
     */
    public static void copyPropertiesExclude(Object from, Object to, String[] excludsArray) throws Exception {
        List<String> excludesList = null;
        if (excludsArray != null && excludsArray.length > 0) {
            //构造列表对象
            excludesList = Arrays.asList(excludsArray);
        }
        // 针对有BaseModel的情况
        Method[] fromMethods = from.getClass().getDeclaredMethods();
        Method[] toMethods = to.getClass().getDeclaredMethods();
        findGetMethodName(from, to, excludesList, fromMethods, toMethods);
    }

    /**
     * 复制对象属性
     *
     * @param from
     * @param to
     * @param excludsArray 排除属性列表
     * @throws Exception
     */
    @SuppressWarnings("rawtypes")
    public static void copyBeanPropertiesExclude(Object from, Object to, String[] excludsArray) throws Exception {
        List<String> excludesList = null;
        if (excludsArray != null && excludsArray.length > 0) {
            //构造列表对象
            excludesList = Arrays.asList(excludsArray);
        }
        // 针对有BaseModel的情况
        Method[] fromMethods = from.getClass().getDeclaredMethods();
        Method[] toMethods = to.getClass().getDeclaredMethods();
        findGetMethodName(from, to, excludesList, fromMethods, toMethods);
    }

    /**
     * 通过set方法赋值
     *
     * @param from
     * @param to
     * @param toMethods
     * @param fromMethod
     * @param fromMethodName
     * @throws IllegalAccessException
     * @throws InvocationTargetException
     */
    private static void setMethodMutator(Object from, Object to, Method[] toMethods, Method fromMethod, String fromMethodName) throws IllegalAccessException, InvocationTargetException {
        String toMethodName;
        Method toMethod;
        toMethodName = "set" + fromMethodName.substring(3);
        toMethod = findMethodByName(toMethods, toMethodName);
        if (toMethod == null) {
            return;
        }
        Object value = fromMethod.invoke(from);
        if (value == null) {
            return;
        }
        //集合类判空处理
        if (value instanceof Collection) {
            Collection newValue = (Collection) value;
            if (newValue.size() <= 0) {
                return;
            }
        }
        toMethod.invoke(to, value);
    }

    /**
     * 对象属性值复制，仅复制指定名称的属性值
     *
     * @param from
     * @param to
     * @param includsArray
     * @throws Exception
     */
    @SuppressWarnings("rawtypes")
    public static void copyPropertiesInclude(Object from, Object to, String[] includsArray) throws Exception {
        List<String> includesList;
        if (includsArray != null && includsArray.length > 0) {

            includesList = Arrays.asList(includsArray);
        } else {
            return;
        }
        Method[] fromMethods = from.getClass().getDeclaredMethods();
        Method[] toMethods = to.getClass().getDeclaredMethods();
        Method fromMethod;
        String fromMethodName;
        for (Method method : fromMethods) {
            fromMethod = method;
            fromMethodName = fromMethod.getName();
            if (!fromMethodName.contains("get")) {
                continue;
            }

            //排除列表检测
            String str = fromMethodName.substring(3);
            if (!includesList.contains(str.substring(0, 1).toLowerCase() + str.substring(1))) {
                continue;
            }
            setMethodMutator(from, to, toMethods, fromMethod, fromMethodName);
        }
    }

    /**
     * 从方法数组中获取指定名称的方法
     *
     * @param methods
     * @param name
     * @return
     */
    public static Method findMethodByName(Method[] methods, String name) {
        for (Method method : methods) {
            if (method.getName().equals(name)) {
                return method;
            }
        }
        return null;
    }

    /**
     * 缓存CopyOptions（注意这个是HuTool的类，不是Cglib的）
     */
    private static final Map<Class, CopyOptions> cacheMap = new HashMap<>();

    /**
     * 驼峰转换
     *
     * @param source
     * @param target
     * @param <K>
     * @param <T>
     * @return
     */
    public static <K, T> T copyAndParse(K source, T target) {
        // 下划线转驼峰
        BeanUtil.copyProperties(source, target, getCopyOptions(source.getClass()));
        return target;
    }

    private static CopyOptions getCopyOptions(Class source) {
        CopyOptions options = cacheMap.get(source);
        if (options == null) {
            // 不加锁，我们认为重复执行不会比并发加锁带来的开销大
            options = CopyOptions.create().setFieldMapping(buildFieldMapper(source));
            cacheMap.put(source, options);
        }
        return options;
    }

    /**
     * @param source
     * @return
     */
    private static Map<String, String> buildFieldMapper(Class source) {
        PropertyDescriptor[] properties = ReflectUtils.getBeanProperties(source);
        Map<String, String> map = new HashMap<>(20);
        for (PropertyDescriptor target : properties) {
            String name = target.getName();
            String camel = StrUtil.toCamelCase(name);
            if (!name.equalsIgnoreCase(camel)) {
                map.put(name, camel);
            }
            String under = StrUtil.toUnderlineCase(name);
            if (!name.equalsIgnoreCase(under)) {
                map.put(name, under);
            }
        }
        return map;
    }

}
