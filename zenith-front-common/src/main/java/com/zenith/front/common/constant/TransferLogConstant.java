package com.zenith.front.common.constant;

/**
 * <AUTHOR>
 * @date 2019/4/193:08 PM
 */
public class TransferLogConstant {

    /***
     * 发起转接
     * */
    public static final int PUSH_TRANSFER_STATUS = 0;
    /**
     * 通过审核
     * */
    public static final int SUCCESS_STATUS = 1;
    /***
     * 驳回
     * */
    public static final int BACK_STATUS = 2;
    /***
     * 撤销状态
     * */
    public static final int UNDO_STATUS = 3;

    /***
     * 超期自动退回
     * */
    public static final int OVERDUE_BACK_STATUS = 4;
    /***
     * 更改目标组织
     * */
    public static final int RESET_TARGET_ORG_STATUS = 4;
    /***
     * 通过审核默认理由
     * */
    public static final String SUCCESS_REASON = "审核通过";
    /**
     * 发起组织关系转接默认理由
     * */
    public static final String PUSH_ORG_TRANSFER_REASON = "发起组织关系转接";
    /***
     * 发起个人关系转接默认理由
     * */
    public static final String PUSH_MEM_TRANSFER_REASON = "发起个人关系转接";
    /***
     * 支部间人员调整默认通过
     * */
    public static final String PUSH_ADJUST_MEM_TRANSFER_REASON = "支部间人员调整默认通过";
}
