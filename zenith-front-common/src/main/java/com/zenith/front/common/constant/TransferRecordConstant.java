package com.zenith.front.common.constant;

import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

/**
 * 该常量可以通过配置文件进行配置
 * <AUTHOR>
 * @date 2019/4/176:31 PM
 */
public class TransferRecordConstant {

    /***
     * 关系转出字典表
     * */
    public static final String DICT_OUT = "dict_d58";
    /***
     * 关系转入字典表
     * */
    public static final String DICT_IN = "dict_d59";
    /**
     * 转出类型前缀
     * */
    public static final char OUT_TYPE_KEY_PREFIX_REX = '2';
    /***
     * 转入类型前缀
     * */
    public static final char IN_TYPE_KEY_PREFIX_REX = '1';
    /***
     * 系统外转入
     * */
    public static final String SYSTEM_OUT_IN_REX = "^12.*";
    /***
     * 转出到系统外
     * */
    public static final String SYSTEM_OUT_OUT_REX = "^22.*";
    /**
     * 整建制转入
     * */
    public static final Set<String> ORG_ALL_IN = new HashSet<String>(){
        private static final long serialVersionUID = -8449932572961221849L;

        {
        add("126");
        add("127");
        add("128");
        add("116");
        add("117");
        add("115");
    }};
    /**
     * 整建制转出
     * */
    public static final Set<String> ORG_ALL_OUT = new HashSet<String>(){
        private static final long serialVersionUID = 6402049938986676203L;

        {
       add("226");
       add("212");
       add("211");
       add("216");
       add("227");
       add("228");
    }};

    /***
     * 人员转入
     * */
    public static final List<String> MEM_IN = new ArrayList<String>(){
        private static final long serialVersionUID = -8603350964694082872L;

        {
        add("123");
        add("124");
        add("125");
        add("112");
        add("113");
        add("114");

    }};

    /***
     * 人员转出
     * */
    public static final List<String> MEM_OUT = new ArrayList<String>(){
        private static final long serialVersionUID = 3960134503233994784L;

        {
        add("223");
        add("224");
        add("225");
        add("21");
        add("22");
        add("213");
        add("214");
        add("215");
    }};
    /***
     * 支部间人员调整 转出
     * */
    public static final String MEM_ADJUST_OUT = "29";
    /***
     * 支部间人员调整 转出
     * */
    public static final String MEM_ADJUST_IN = "19";

    /**
     * 组织关系转接
     * 目的组织只能是党委
     * */
    public static final List<String> ORG_TRANSFER_TARGET_ORG_TYPE =  new ArrayList<String>(1){
        private static final long serialVersionUID = -9172296422708703397L;

        {
        //组织类型党委
        add("1");
    }};

    /***
     * 党支部
     * 人员关系转接只能是党支部
     * */
    public static final List<String> MEM_TRANSFER_TARGET_ORG_TYPE = new ArrayList<String>(2){
        private static final long serialVersionUID = -255046725904614103L;

        {
        //组织类型党支部
       add("3");
       //组织类型联合党支部
       add("4");
    }};


    /***
     * 关系转接中
     * */
    public static final Integer TRANSFERING = 0;
    /***
     * 关系转接完成
     * */
    public static final Integer TRANSFER_SUCCESS = 1;
    /***
     * 转接已撤销
     * */
    public static final Integer TRANSFER_UNDO = 2;

    /***
     * 超期自动退回
     * */
    public static final Integer TRANSFER_OVERDUE_BACK = 4;

    /**
     * 跨省转入（未接入全国交换区）
     */
    public static final String TRANSFER_OFFLINEf_ONE = "125";
    /**
     * 跨省转出（未接入全国交换区）
     */
    public static final String TRANSFER_OFFLINEf_TWO = "223";

}
