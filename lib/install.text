--安装jar到maven库命令  自己改路径
mvn install:install-file -Dmaven.repo.local=E:\LocalRepository -DgroupId=com.fisherman -DartifactId=dsvs_api -Dversion=1.0.0 -Dpackaging=jar -Dfile=./dsvs_api.jar
mvn install:install-file -Dmaven.repo.local=E:\LocalRepository -DgroupId=com.fisherman -DartifactId=jce -Dversion=1.0.0 -Dpackaging=jar -Dfile=./JCE-2.3.0.jar

mvn install:install-file   -DgroupId=com.cdf -DartifactId=CDF_JMJ_API -Dversion=1.10 -Dpackaging=jar   -Dfile=./CDF_JMJ_API-1.10.jar
mvn install:install-file   -DgroupId=com.cdf -DartifactId=CDF_JMJ_JNI -Dversion=1.10 -Dpackaging=jar   -Dfile=./CDF_JMJ_JNI-1.10.jar
mvn install:install-file   -DgroupId=com.cdf -DartifactId=CDFJCE -Dversion=2.3.0 -Dpackaging=jar   -Dfile=./CDFJCE-2.3.0.jar
mvn install:install-file   -DgroupId=com.cdf -DartifactId=CDFSoft -Dversion=2.2 -Dpackaging=jar   -Dfile=./CDFSoft-2.2.jar


-- jce_policy-8.zip 里面的jar包需要复制到JAVA_HOME/jre/lib/security/替换这两个jar包
-- 服务器上需要在Java中加入lib依赖