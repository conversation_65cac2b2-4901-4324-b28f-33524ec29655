# 原始值存储功能说明

## 🎯 功能概述

为了满足数据审计、问题排查和可能的回滚需求，我们在解密任务记录表中添加了原始值和处理后值的存储功能。

## 📊 数据库变更

### 新增字段

在 `decrypting_task_record` 表中新增了两个字段：

```sql
-- 原始字段值（JSON格式存储）
original_values text NULL,

-- 处理后字段值（JSON格式存储）  
processed_values text NULL,
```

### 数据格式

字段值以JSON格式存储，例如：

```json
{
  "contacter": "原始加密值1",
  "contact_phone": "原始加密值2", 
  "secretary": null
}
```

## 🔧 功能特性

### 1. 原始值记录

- **记录时机**：创建任务记录时
- **存储内容**：所有需要处理的字段的原始值
- **格式**：JSON格式，字段名作为key，原始值作为value

### 2. 处理后值记录

- **记录时机**：任务处理完成时
- **存储内容**：所有字段处理后的值
- **格式**：JSON格式，包含更新的值和未更新的原值

### 3. 值比较分析

提供详细的值变化分析：
- 哪些字段发生了变化
- 哪些字段从null变为有值
- 哪些字段从有值变为null
- 哪些字段保持不变

### 4. 回滚支持

- 检查任务是否支持回滚
- 生成回滚SQL语句
- 基于原始值恢复数据

## 📋 API接口

### 基础查询接口

```http
# 获取批次统计信息
GET /exchangeLog/task/batch/{batchNo}/statistics

# 获取批次失败任务列表  
GET /exchangeLog/task/batch/{batchNo}/failed-tasks

# 重试批次失败任务
POST /exchangeLog/task/batch/{batchNo}/retry
```

### 值分析接口（待完善）

```http
# 比较任务值变化
GET /exchangeLog/task/value/compare?taskId={taskId}

# 获取值变化摘要
GET /exchangeLog/task/value/summary?taskId={taskId}

# 生成回滚SQL
GET /exchangeLog/task/rollback/sql?taskId={taskId}
```

## 💡 使用场景

### 1. 数据审计

```java
// 获取值变化摘要
DecryptingTaskValueService.ValueChangeSummary summary = 
    decryptingTaskValueService.getChangeSummary(taskRecord);

System.out.println("总字段数：" + summary.getTotalFields());
System.out.println("变化字段数：" + summary.getChangedFields());
System.out.println("null转有值：" + summary.getNullToValueFields());
System.out.println("有值转null：" + summary.getValueToNullFields());
```

### 2. 问题排查

```java
// 比较原始值和处理后值
Map<String, DecryptingTaskValueService.ValueComparison> comparisons = 
    decryptingTaskValueService.compareValues(taskRecord);

for (DecryptingTaskValueService.ValueComparison comparison : comparisons.values()) {
    if (comparison.isChanged()) {
        System.out.println("字段 " + comparison.getField() + " 发生变化：");
        System.out.println("  原始值：" + comparison.getOriginalValue());
        System.out.println("  处理后：" + comparison.getProcessedValue());
        System.out.println("  变化类型：" + comparison.getChangeType());
    }
}
```

### 3. 数据回滚

```java
// 检查是否可以回滚
if (decryptingTaskValueService.canRollback(taskRecord)) {
    // 生成回滚SQL
    String rollbackSql = decryptingTaskValueService.generateRollbackSql(taskRecord);
    System.out.println("回滚SQL：" + rollbackSql);
    
    // 执行回滚（需要谨慎操作）
    // mapper.updateDecryptingInformation1(rollbackSql);
}
```

## 🔍 数据示例

### 任务记录示例

```json
{
  "id": "abc123",
  "tableName": "ccp_org",
  "dataCode": "org001", 
  "fields": "contacter,contact_phone,secretary",
  "originalValues": "{\"contacter\":\"encrypted_value_1\",\"contact_phone\":\"encrypted_value_2\",\"secretary\":null}",
  "processedValues": "{\"contacter\":\"new_encrypted_value_1\",\"contact_phone\":null,\"secretary\":\"new_encrypted_value_3\"}",
  "status": 2,
  "successFieldCount": 3,
  "totalFieldCount": 3
}
```

### 值比较结果示例

```json
{
  "contacter": {
    "field": "contacter",
    "originalValue": "encrypted_value_1", 
    "processedValue": "new_encrypted_value_1",
    "changed": true,
    "changeType": "VALUE_CHANGED"
  },
  "contact_phone": {
    "field": "contact_phone",
    "originalValue": "encrypted_value_2",
    "processedValue": null,
    "changed": true, 
    "changeType": "VALUE_TO_NULL"
  },
  "secretary": {
    "field": "secretary",
    "originalValue": null,
    "processedValue": "new_encrypted_value_3", 
    "changed": true,
    "changeType": "NULL_TO_VALUE"
  }
}
```

## 🛠️ 部署步骤

### 1. 执行数据库升级脚本

```sql
-- 执行升级脚本
\i doc/decrypting_task_record_upgrade.sql
```

### 2. 验证字段添加

```sql
-- 验证新字段
SELECT column_name, data_type, is_nullable 
FROM information_schema.columns 
WHERE table_name = 'decrypting_task_record' 
AND column_name IN ('original_values', 'processed_values');
```

### 3. 重启应用

重启应用以加载新的代码和配置。

## ⚠️ 注意事项

### 1. 存储空间

- JSON格式存储会占用额外空间
- 建议定期清理历史任务记录
- 可以考虑压缩存储

### 2. 性能影响

- JSON序列化/反序列化有性能开销
- 大量数据处理时注意内存使用
- 可以考虑异步处理

### 3. 数据安全

- 原始值包含敏感的加密数据
- 需要严格控制访问权限
- 考虑数据脱敏需求

### 4. 回滚操作

- 回滚操作需要谨慎执行
- 建议先在测试环境验证
- 执行前做好数据备份

## 📈 后续扩展

### 1. 数据压缩

可以考虑对JSON数据进行压缩存储：

```java
// 压缩存储
String compressedJson = CompressUtil.gzip(jsonStr);
record.setOriginalValues(compressedJson);

// 解压读取  
String jsonStr = CompressUtil.ungzip(record.getOriginalValues());
```

### 2. 批量回滚

支持批量回滚操作：

```java
// 批量生成回滚SQL
List<String> rollbackSqls = decryptingTaskValueService.generateBatchRollbackSql(taskRecords);
```

### 3. 变化统计

提供更详细的变化统计分析：

```java
// 批次级别的变化统计
BatchChangeStatistics stats = decryptingTaskValueService.getBatchChangeStatistics(batchNo);
```

## 📝 总结

原始值存储功能为解密任务提供了完整的数据审计和回滚能力，提高了系统的可靠性和可维护性。通过详细的值比较和分析功能，可以更好地了解数据处理的效果和影响。
