CREATE SEQUENCE IF NOT EXISTS data_equals_record_id_seq
    INCREMENT 1
    MINVALUE 1
    NO MAXVALUE
    START 1
    CACHE 1;

DROP TABLE IF EXISTS "public"."data_equals_record";
CREATE TABLE "public"."data_equals_record" (
                                         "id" int8 NOT NULL DEFAULT nextval('data_equals_record_id_seq'::regclass),
                                         "biz_id" varchar(64) COLLATE "pg_catalog"."default" NOT NULL DEFAULT NULL::character varying,
                                         "biz_table_name" varchar(64) COLLATE "pg_catalog"."default" NOT NULL DEFAULT NULL::character varying,
                                         "biz_data" text,
                                         "biz_data_encrypt" text,
                                         "create_time" timestamp(6) ,
                                         "create_user"  varchar(64),
                                         "update_time" timestamp(6) ,
                                         "update_user"  varchar(64),
                                         "delete_time" timestamp(6) ,
                                         "remark" text,
                                         "timestamp" timestamp(6) DEFAULT CURRENT_TIMESTAMP,

                                         CONSTRAINT "data_equals_record_pkey" PRIMARY KEY ("id")
)
;

COMMENT ON COLUMN "public"."data_equals_record"."id" IS '本表中唯一标识符';
COMMENT ON COLUMN "public"."data_equals_record"."biz_id" IS '业务主键';
COMMENT ON COLUMN "public"."data_equals_record"."biz_table_name" IS '业务表名';
COMMENT ON COLUMN "public"."data_equals_record"."biz_data" IS '加密前的原始数据';
COMMENT ON COLUMN "public"."data_equals_record"."biz_data_encrypt" IS '加密后的数据';
