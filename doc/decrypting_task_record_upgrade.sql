-- 解密任务记录表升级脚本
-- 为现有表添加原始值和处理后值字段

-- 添加原始值字段
ALTER TABLE public.decrypting_task_record 
ADD COLUMN IF NOT EXISTS original_values text NULL;

-- 添加处理后值字段
ALTER TABLE public.decrypting_task_record 
ADD COLUMN IF NOT EXISTS processed_values text NULL;

-- 添加字段注释
COMMENT ON COLUMN public.decrypting_task_record.original_values IS '原始字段值（JSON格式存储）';
COMMENT ON COLUMN public.decrypting_task_record.processed_values IS '处理后字段值（JSON格式存储）';

-- 验证字段是否添加成功
SELECT column_name, data_type, is_nullable, column_default
FROM information_schema.columns 
WHERE table_name = 'decrypting_task_record' 
AND column_name IN ('original_values', 'processed_values')
ORDER BY ordinal_position;
