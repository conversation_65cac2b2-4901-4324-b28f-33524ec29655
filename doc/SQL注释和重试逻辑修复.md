# SQL注释和重试逻辑修复

## 🚨 问题分析

### 1. Druid SQL注入检测问题

**错误信息**：
```
sql injection violation, dbType postgresql, druid-version 1.2.1, comment not allow
```

**原因**：
- Druid 连接池默认不允许 SQL 中包含注释
- 我们的 SQL 中包含了 `-- 待处理或失败` 注释
- 被 Druid 误判为 SQL 注入攻击

**问题SQL**：
```sql
SELECT * FROM decrypting_task_record 
WHERE (status = 0 OR status = 3)  -- 待处理或失败
AND retry_count < max_retry_count
```

### 2. 重试逻辑问题

**数据库现状**：
- 有17条状态为0（待处理）的记录
- 但查询没有返回这些记录

**根本原因**：
`canRetry()` 方法只检查失败状态，不检查待处理状态：

```java
public boolean canRetry() {
    return this.retryCount < this.maxRetryCount && isFailed(); // ❌ 只检查失败
}
```

## ✅ 修复方案

### 1. 移除SQL注释

**修复前**：
```sql
WHERE (status = 0 OR status = 3)  -- 待处理或失败
```

**修复后**：
```sql
WHERE (status = 0 OR status = 3)
```

### 2. 修复重试逻辑

**修复前**：
```java
public boolean canRetry() {
    return this.retryCount < this.maxRetryCount && isFailed();
}
```

**修复后**：
```java
public boolean canRetry() {
    return this.retryCount < this.maxRetryCount && (isFailed() || this.status == STATUS_PENDING);
}
```

### 3. 增加调试日志

添加详细的调试信息：
```java
log.info("批次{}任务状态分析：待处理={}，失败={}，可重试={}，总数={}", 
        batchNo, pendingCount, failedCount, canRetryCount, allTasks.size());

log.warn("待处理任务详情：id={}，表名={}，code={}，重试次数={}，最大重试次数={}，可重试={}", 
        task.getId(), task.getTableName(), task.getDataCode(), 
        task.getRetryCount(), task.getMaxRetryCount(), task.canRetry());
```

## 🔍 问题排查过程

### 1. SQL执行失败
- Druid 检测到注释，抛出 SQL 注入异常
- 导致查询失败，无法获取待处理任务

### 2. 重试条件不满足
- 即使SQL执行成功，`canRetry()` 方法也不会返回待处理任务
- 因为它只检查失败状态，不检查待处理状态

### 3. 数据不一致
- 统计显示有17条待处理任务
- 但重试查询返回0条记录

## 📊 修复效果

### 修复前
- SQL执行失败，抛出异常
- 待处理任务无法被重试
- 数据处理停滞

### 修复后
- SQL正常执行
- 待处理和失败任务都能被重试
- 数据处理继续进行

## 🎯 预期结果

修复后应该看到类似的日志：
```
批次DECRYPT_20250630102641_723eb180任务状态分析：待处理=17，失败=0，可重试=17，总数=117
开始处理失败和待处理的任务，批次：DECRYPT_20250630102641_723eb180，总任务数：17，待处理：17，失败：0
开始重试任务：表名=ccp_mem_log，code=xxx，当前状态=0，重试次数=0
重试任务成功：表名=ccp_mem_log，code=xxx
...
批次DECRYPT_20250630102641_723eb180统计信息：总数=117，待处理=0，处理中=0，成功=117，失败=0
```

## 🛡️ 预防措施

### 1. SQL编写规范
- 避免在 MyBatis XML 中使用 SQL 注释
- 使用 XML 注释替代 SQL 注释
- 如需注释，在 XML 注释中说明

### 2. 状态检查完整性
- 确保状态检查逻辑覆盖所有需要的状态
- 添加单元测试验证状态转换逻辑
- 定期检查状态定义的一致性

### 3. 调试日志
- 在关键逻辑点添加详细日志
- 包含足够的上下文信息
- 便于问题排查和监控

### 4. Druid配置
如果确实需要SQL注释，可以配置Druid允许注释：
```yaml
spring:
  datasource:
    druid:
      wall:
        comment-allow: true
```

## 📝 总结

这次问题涉及两个层面：
1. **技术层面**：Druid SQL注入检测机制
2. **逻辑层面**：重试条件不完整

通过移除SQL注释和修复重试逻辑，解决了待处理任务无法重试的问题。这个修复确保了数据处理的连续性和完整性。
