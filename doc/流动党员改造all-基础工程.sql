------------- 基础库 ---------------------
CREATE SEQUENCE IF NOT EXISTS ccp_org_flow_id_seq
    INCREMENT 1
    MINVALUE 1
    NO MAXVALUE
    START 1
    CACHE 1;

CREATE SEQUENCE IF NOT EXISTS ccp_org_flow_audit_id_seq
    INCREMENT 1
    MINVALUE 1
    NO MAXVALUE
    START 1
    CACHE 1;


DROP TABLE IF EXISTS "public"."ccp_org_flow";
CREATE TABLE "public"."ccp_org_flow" (
                                         "id" int8 NOT NULL DEFAULT nextval('ccp_org_flow_id_seq'::regclass),
                                         "code" varchar(64) COLLATE "pg_catalog"."default" NOT NULL DEFAULT NULL::character varying,
                                         "es_id" varchar(50) COLLATE "pg_catalog"."default" NOT NULL DEFAULT NULL::character varying,
                                         "zb_code" varchar(50) COLLATE "pg_catalog"."default" NOT NULL DEFAULT NULL::character varying,
                                         "org_code" varchar(50) COLLATE "pg_catalog"."default" DEFAULT NULL::character varying,
                                         "name" varchar(200) COLLATE "pg_catalog"."default" DEFAULT NULL::character varying,
                                         "short_name" varchar(200) COLLATE "pg_catalog"."default" DEFAULT NULL::character varying,
                                         "pinyin" varchar(1200) COLLATE "pg_catalog"."default" DEFAULT NULL::character varying,
                                         "is_enable" int2 NOT NULL,
                                         "parent_code" varchar(50) COLLATE "pg_catalog"."default" DEFAULT NULL::character varying,
                                         "parent_flow_type" int2 NULL,
                                         "d01_code" varchar(50) COLLATE "pg_catalog"."default" DEFAULT NULL::character varying,
                                         "create_date" date,
                                         "contacter" varchar(200) COLLATE "pg_catalog"."default" DEFAULT NULL::character varying,
                                         "contact_phone" varchar(200) COLLATE "pg_catalog"."default" DEFAULT NULL::character varying,
                                         "d200_code" varchar(50) COLLATE "pg_catalog"."default" DEFAULT NULL::character varying,
                                         "approve_code" varchar(50) COLLATE "pg_catalog"."default" DEFAULT NULL::character varying,
                                         "approve_name" varchar(200) COLLATE "pg_catalog"."default" DEFAULT NULL::character varying,
                                         "administrative_division" varchar(255) COLLATE "pg_catalog"."default",
                                         "flow_administrative_division" varchar(255) COLLATE "pg_catalog"."default",
                                         "d201_code" varchar(50) COLLATE "pg_catalog"."default" DEFAULT NULL::character varying,
                                         "support_unit_details" varchar(255) COLLATE "pg_catalog"."default",
                                         "cancel_details" varchar(255) COLLATE "pg_catalog"."default",
                                         "source_type" int2 DEFAULT 1 NULL,
                                         "sort" int4,
                                         "create_time" date,
                                         "update_time" date,
                                         "delete_time" date,
                                         "remark" varchar(255) COLLATE "pg_catalog"."default" DEFAULT NULL::character varying,
                                         "timestamp" timestamp(6) DEFAULT CURRENT_TIMESTAMP,

                                         CONSTRAINT "ccp_org_flow_pkey" PRIMARY KEY ("id"),
                                         CONSTRAINT "uq_ccp_org_flow_code" UNIQUE ("code")
)
;

COMMENT ON COLUMN "public"."ccp_org_flow"."id" IS '本表中唯一标识符';
COMMENT ON COLUMN "public"."ccp_org_flow"."code" IS '本表中唯一标识符';
COMMENT ON COLUMN "public"."ccp_org_flow"."es_id" IS 'es中唯一标识符';
COMMENT ON COLUMN "public"."ccp_org_flow"."zb_code" IS '党组织代码';
COMMENT ON COLUMN "public"."ccp_org_flow"."org_code" IS '层级码';
COMMENT ON COLUMN "public"."ccp_org_flow"."name" IS '名称';
COMMENT ON COLUMN "public"."ccp_org_flow"."short_name" IS '简称';
COMMENT ON COLUMN "public"."ccp_org_flow"."pinyin" IS '拼音';
COMMENT ON COLUMN "public"."ccp_org_flow"."is_enable" IS '是否有效，审批通过才是有效的组织';
COMMENT ON COLUMN "public"."ccp_org_flow"."parent_code" IS '父级的code';
COMMENT ON COLUMN "public"."ccp_org_flow"."parent_flow_type" IS '父级的党组织类型  1-普通党组织  2-流动党组织';
COMMENT ON COLUMN "public"."ccp_org_flow"."d01_code" IS '党组织类型的code';
COMMENT ON COLUMN "public"."ccp_org_flow"."create_date" IS '成立日期';
COMMENT ON COLUMN "public"."ccp_org_flow"."contacter" IS '联系人';
COMMENT ON COLUMN "public"."ccp_org_flow"."contact_phone" IS '联系方式';
COMMENT ON COLUMN "public"."ccp_org_flow"."d200_code" IS '党组织成立类型';
COMMENT ON COLUMN "public"."ccp_org_flow"."approve_code" IS '批准成立的党组织（县级以上党委）';
COMMENT ON COLUMN "public"."ccp_org_flow"."approve_name" IS '批准成立的党组织（县级以上党委）名称';
COMMENT ON COLUMN "public"."ccp_org_flow"."administrative_division" IS '行政区划代码';
COMMENT ON COLUMN "public"."ccp_org_flow"."flow_administrative_division" IS '流动地行政区划代码';
COMMENT ON COLUMN "public"."ccp_org_flow"."d201_code" IS '依托单位code';
COMMENT ON COLUMN "public"."ccp_org_flow"."support_unit_details" IS '其他依托单位详情';
COMMENT ON COLUMN "public"."ccp_org_flow"."cancel_details" IS '撤销原因';
COMMENT ON COLUMN "public"."ccp_org_flow"."source_type" IS '1-本地创建  2-交换区数据';


------------党组织成立类型字典表---------
DROP TABLE IF EXISTS "public"."dict_d200";
CREATE TABLE IF NOT EXISTS public.dict_d200 (
                                                id bigserial NOT NULL,
                                                "key" varchar(10) NULL,
    name varchar(10) NULL,
    pinyin varchar(255) NULL,
    parent varchar(3) NULL,
    is_leaf bool NULL,
    sort int8 NULL,
    remark varchar(255) NULL,
    disabled bool NULL,
    enabled bool NULL,
    CONSTRAINT dict_d200_pkey PRIMARY KEY (id)
    );
-------------党组织成立类型字典表插入数据----------
DELETE FROM dict_d200 WHERE ID = 1;
INSERT INTO dict_d200
(id, "key", "name", pinyin, parent, is_leaf, sort, remark, disabled, enabled)
VALUES(1, '1', '流入地成立', 'lrdcl', '-1', true, 1, NULL, NULL, NULL);
DELETE FROM dict_d200 WHERE ID = 2;
INSERT INTO dict_d200
(id, "key", "name", pinyin, parent, is_leaf, sort, remark, disabled, enabled)
VALUES(2, '2', '流出地成立', 'lcdcl', '-1', true, 2, NULL, NULL, NULL);

------------审批类型字典表---------
DROP TABLE IF EXISTS "public"."dict_d202";
CREATE TABLE IF NOT EXISTS public.dict_d202 (
                                                id bigserial NOT NULL,
                                                "key" varchar(10) NULL,
    name varchar(10) NULL,
    pinyin varchar(255) NULL,
    parent varchar(3) NULL,
    is_leaf bool NULL,
    sort int8 NULL,
    remark varchar(255) NULL,
    disabled bool NULL,
    enabled bool NULL,
    CONSTRAINT dict_d202_pkey PRIMARY KEY (id)
    );
-------------审批类型字典表插入数据----------
INSERT INTO dict_d202 (id, "key", "name", pinyin, parent, is_leaf, sort, remark, disabled, enabled)
VALUES(0, '0', '待审批', 'dsp', '-1', true, 0, NULL, NULL, NULL);
INSERT INTO dict_d202(id, "key", "name", pinyin, parent, is_leaf, sort, remark, disabled, enabled)
VALUES(1, '1', '审批通过', 'sptg', '-1', true, 1, NULL, NULL, NULL);
INSERT INTO dict_d202(id, "key", "name", pinyin, parent, is_leaf, sort, remark, disabled, enabled)
VALUES(2, '2', '审批未通过', 'spwtg', '-1', true, 2, NULL, NULL, NULL);
INSERT INTO dict_d202(id, "key", "name", pinyin, parent, is_leaf, sort, remark, disabled, enabled)
VALUES(3, '3', '撤销审批', 'spwtg', '-1', true, 3, NULL, NULL, NULL);

------------审批拒绝原因字典表---------
DROP TABLE IF EXISTS "public"."dict_d203";
CREATE TABLE IF NOT EXISTS public.dict_d203 (
                                                id bigserial NOT NULL,
                                                "key" varchar(10) NULL,
    name varchar(10) NULL,
    pinyin varchar(255) NULL,
    parent varchar(3) NULL,
    is_leaf bool NULL,
    sort int8 NULL,
    remark varchar(255) NULL,
    disabled bool NULL,
    enabled bool NULL,
    CONSTRAINT dict_d203_pkey PRIMARY KEY (id)
    );
-------------审批拒绝原因字典表插入数据----------
INSERT INTO dict_d203 (id, "key", "name", pinyin, parent, is_leaf, sort, remark, disabled, enabled)
VALUES(1, '1', '党员不存在', 'dybcz', '-1', true, 1, NULL, NULL, NULL);
INSERT INTO dict_d203(id, "key", "name", pinyin, parent, is_leaf, sort, remark, disabled, enabled)
VALUES(2, '2', '党员组织关系转接中', 'dyzzgxzjz', '-1', true, 2, NULL, NULL, NULL);
INSERT INTO dict_d203(id, "key", "name", pinyin, parent, is_leaf, sort, remark, disabled, enabled)
VALUES(3, '3', '党员信息流转中', 'dyxxlzz', '-1', true, 3, NULL, NULL, NULL);


------------ dict_d01字典表加数据 ---------------
delete from dict_d01 where id = 89;
INSERT INTO dict_d01
(id, "key", "name", pinyin, parent, "type", is_leaf, sort, map_new_key, map_type, map_new_key_name, old_key, old_name, remark, disabled, enabled)
VALUES(89, '8', '功能型党组织', 'gnxdzz', '-1', NULL, 'f', 70, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL);
delete from dict_d01 where id = 90;
INSERT INTO dict_d01
(id, "key", "name", pinyin, parent, "type", is_leaf, sort, map_new_key, map_type, map_new_key_name, old_key, old_name, remark, disabled, enabled)
VALUES(90, '81', '流动党员党组织', 'lddydzz', '8', NULL, 'f', 71, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL);
delete from dict_d01 where id = 91;
INSERT INTO dict_d01
(id, "key", "name", pinyin, parent, "type", is_leaf, sort, map_new_key, map_type, map_new_key_name, old_key, old_name, remark, disabled, enabled)
VALUES(91, '811', '流动党员党委', 'lddydw', '81', '1', 'f', 72, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL);
delete from dict_d01 where id = 92;
INSERT INTO dict_d01
(id, "key", "name", pinyin, parent, "type", is_leaf, sort, map_new_key, map_type, map_new_key_name, old_key, old_name, remark, disabled, enabled)
VALUES(92, '812', '流动党员党总支', 'lddydzz', '81', '2', 't', 73, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL);
delete from dict_d01 where id = 93;
INSERT INTO dict_d01
(id, "key", "name", pinyin, parent, "type", is_leaf, sort, map_new_key, map_type, map_new_key_name, old_key, old_name, remark, disabled, enabled)
VALUES(93, '813', '流动党员党支部', 'lddydzb', '81', '3', 't', 74, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL);

DROP TABLE IF EXISTS "public"."ccp_org_flow_audit";
CREATE TABLE "public"."ccp_org_flow_audit" (
                                               "id" int8 NOT NULL DEFAULT nextval('ccp_org_flow_audit_id_seq'::regclass),
                                               "code" varchar(64) COLLATE "pg_catalog"."default" NOT NULL DEFAULT NULL::character varying,
                                               "flow_org_code" varchar(50) COLLATE "pg_catalog"."default" DEFAULT NULL::character varying,
                                               "flow_org_level_code" varchar(50) COLLATE "pg_catalog"."default" DEFAULT NULL::character varying,
                                               "name" varchar(200) COLLATE "pg_catalog"."default" DEFAULT NULL::character varying,
                                               "is_exchange" int2 NOT NULL,
                                               "status" varchar(10) NOT NULL,
                                               "audit_time" date,
                                               "audit_user_id" varchar(64),
                                               "audit_user_name" varchar(50),
                                               "approve_code" varchar(50) COLLATE "pg_catalog"."default" DEFAULT NULL::character varying,
                                               "reason" varchar(255),
                                               "create_time"  timestamp(6),
                                               "update_time"  timestamp(6),
                                               "delete_time"  timestamp(6),
                                               "remark" text,
                                               "timestamp" timestamp(6) DEFAULT CURRENT_TIMESTAMP,

                                               CONSTRAINT "ccp_org_flow_audit_pkey" PRIMARY KEY ("id"),
                                               CONSTRAINT "uq_ccp_org_flow_audit_code" UNIQUE ("code")
)
;

COMMENT ON COLUMN "public"."ccp_org_flow_audit"."id" IS '本表中唯一标识符';
COMMENT ON COLUMN "public"."ccp_org_flow_audit"."code" IS '本表中唯一标识符';
COMMENT ON COLUMN "public"."ccp_org_flow_audit"."flow_org_code" IS '流动组织层级码';
COMMENT ON COLUMN "public"."ccp_org_flow_audit"."flow_org_level_code" IS '流动组织Code';
COMMENT ON COLUMN "public"."ccp_org_flow_audit"."name" IS '流动组织名称';
COMMENT ON COLUMN "public"."ccp_org_flow_audit"."is_exchange" IS '是否需要中央交换区流转，1-是，0-否';
COMMENT ON COLUMN "public"."ccp_org_flow_audit"."status" IS '审批状态，0-待审批，1-审批通过，2-审批未通过';
COMMENT ON COLUMN "public"."ccp_org_flow_audit"."audit_time" IS '审批时间';
COMMENT ON COLUMN "public"."ccp_org_flow_audit"."audit_user_id" IS '审批人唯一标识';
COMMENT ON COLUMN "public"."ccp_org_flow_audit"."audit_user_name" IS '审批人名称';
COMMENT ON COLUMN "public"."ccp_org_flow_audit"."approve_code" IS '审批单位的组织code';
COMMENT ON COLUMN "public"."ccp_org_flow_audit"."reason" IS '审批理由';
COMMENT ON COLUMN "public"."ccp_org_flow_audit"."remark" IS '备注';


CREATE SEQUENCE IF NOT EXISTS ccp_mem_flow_sign_audit_id_seq
    INCREMENT 1
    MINVALUE 1
    NO MAXVALUE
    START 1
    CACHE 1;
drop table if exists "public"."ccp_mem_flow_sign_audit";
create table "public"."ccp_mem_flow_sign_audit" (
                                                    "id" int8 not null DEFAULT nextval('ccp_mem_flow_sign_audit_id_seq'::regclass),
                                                    "code" varchar(64) not null,
                                                    "sign_code" varchar(50) default null,
                                                    "flow_sign_code" varchar(50) default null,
                                                    "flow_sign_records_code" varchar(50) default null,
                                                    "flow_org_code" varchar(50) default null,
                                                    "flow_org_name" varchar(50) default null,
                                                    "audit_time" timestamp(6) default null,
                                                    "audit_user_id" varchar(64) default null,
                                                    "audit_user_name" varchar(50) default null,
                                                    "audit_org_id" varchar(64) default null,
                                                    "audit_org_code" varchar(64) default null,
                                                    "audit_org_name" varchar(50) default null,
                                                    "status" varchar(10) default null,
                                                    "refuse" varchar(10) default null,
                                                    "reason" varchar(255) default null,
                                                    "create_time"  timestamp(6) default null,
                                                    "update_time"  timestamp(6) default null,
                                                    "delete_time"  timestamp(6) default null,
                                                    "remark" text default null,
                                                    "timestamp" timestamp(6) default current_timestamp,
                                                    constraint "ccp_mem_flow_sign_audit_pkey" primary key ("id"),
                                                    constraint "uq_ccp_mem_flow_sign_audit_code" unique ("code")
)
;

comment on column "public"."ccp_mem_flow_sign_audit"."id" is '本表中唯一标识符';
comment on column "public"."ccp_mem_flow_sign_audit"."code" is '本表中唯一标识符';
comment on column "public"."ccp_mem_flow_sign_audit"."sign_code" is '流动党员code';
comment on column "public"."ccp_mem_flow_sign_audit"."flow_sign_code" is '入党申请人code';
comment on column "public"."ccp_mem_flow_sign_audit"."flow_sign_records_code" is '党组织记录code';
comment on column "public"."ccp_mem_flow_sign_audit"."flow_org_code" is '流动组织code';
comment on column "public"."ccp_mem_flow_sign_audit"."flow_org_name" is '流动组织名称';
comment on column "public"."ccp_mem_flow_sign_audit"."audit_time" is '审批时间';
comment on column "public"."ccp_mem_flow_sign_audit"."audit_user_id" is '审批人ID';
comment on column "public"."ccp_mem_flow_sign_audit"."audit_user_name" is '审批人名称';
comment on column "public"."ccp_mem_flow_sign_audit"."audit_org_id" is '审批人组织ID';
comment on column "public"."ccp_mem_flow_sign_audit"."audit_org_code" is '审批人组织层级码';
comment on column "public"."ccp_mem_flow_sign_audit"."audit_org_name" is '审批人组织名称';
comment on column "public"."ccp_mem_flow_sign_audit"."status" is '审批状态，0-待审批，1-审批通过，2-审批未通过';
comment on column "public"."ccp_mem_flow_sign_audit"."refuse" is '拒绝原因，1-党员不存在，2-党员组织关系转接中，3-党员信息流转中';
comment on column "public"."ccp_mem_flow_sign_audit"."reason" is '说明理由';
comment on column "public"."ccp_mem_flow_sign_audit"."remark" is '备注';


DROP TABLE IF EXISTS "public"."exchange_log";
CREATE TABLE "public"."exchange_log" (
                                         "id"  varchar(64)  NOT NULL,
                                         "type_name" varchar(64) COLLATE "pg_catalog"."default" NOT NULL DEFAULT NULL::character varying,
                                         "operation_name" varchar(64) COLLATE "pg_catalog"."default" NOT NULL DEFAULT NULL::character varying,
                                         "biz_id" varchar(64) COLLATE "pg_catalog"."default" NOT NULL DEFAULT NULL::character varying,
                                         "operation_timestamp" timestamp(6) DEFAULT CURRENT_TIMESTAMP,
                                         "is_success"int2 DEFAULT 0,
                                         "request_data" text,
                                         "request_url" text,
                                         "request_method" varchar(10),
                                         "result_data" text,
                                         "parse_data" text,
                                         "create_time" timestamp(6),
                                         "update_time" timestamp(6),
                                         "delete_time" timestamp(6),
                                         "remark" varchar(255) COLLATE "pg_catalog"."default" DEFAULT NULL::character varying,
                                         "timestamp" timestamp(6) DEFAULT CURRENT_TIMESTAMP,
                                         CONSTRAINT "exchange_log_pkey" PRIMARY KEY ("id")
)
;

COMMENT ON COLUMN "public"."exchange_log"."id" IS '本表中唯一标识符';
COMMENT ON COLUMN "public"."exchange_log"."type_name" IS '交换区业务类型，直接写表名或者其他';
COMMENT ON COLUMN "public"."exchange_log"."operation_name" IS '操作名称，增删改查之类的';
COMMENT ON COLUMN "public"."exchange_log"."biz_id" IS '业务主键';
COMMENT ON COLUMN "public"."exchange_log"."operation_timestamp" IS '操作时间戳';
COMMENT ON COLUMN "public"."exchange_log"."is_success" IS '是否成功';
COMMENT ON COLUMN "public"."exchange_log"."request_data" IS '请求数据';
COMMENT ON COLUMN "public"."exchange_log"."request_url" IS '请求地址';
COMMENT ON COLUMN "public"."exchange_log"."request_method" IS '请求方法，1-get,2-post';
COMMENT ON COLUMN "public"."exchange_log"."result_data" IS '结果数据';
COMMENT ON COLUMN "public"."exchange_log"."parse_data" IS '如果数据加密，存放解析后的数据';
COMMENT ON COLUMN "public"."exchange_log"."create_time" IS '创建时间';
COMMENT ON COLUMN "public"."exchange_log"."update_time" IS '修改时间';
COMMENT ON COLUMN "public"."exchange_log"."delete_time" IS '删除时间';
COMMENT ON COLUMN "public"."exchange_log"."remark" IS '备注';
COMMENT ON COLUMN "public"."exchange_log"."timestamp" IS '时间戳';



delete from sys_permission where des = '流动党员党组织';
INSERT INTO "public"."sys_permission" ("id", "des", "create_time", "type", "parent_id") VALUES ((select (max(id) + 1) from "public"."sys_permission"), '流动党员党组织', now(), 1, 13);
update sys_role set permission = concat(permission, '1') where length(permission) != (SELECT count(*) FROM "sys_permission");
update sys_user_role_permission set permission = concat(permission, '1') where length(permission) != (SELECT count(*) FROM "sys_permission");

DROP TABLE IF EXISTS "public"."dict_d201";
CREATE TABLE "public"."dict_d201" (
                                      "id" int4 NOT NULL,
                                      "key" varchar(255),
                                      "name" varchar(255),
                                      "pinyin" varchar(255),
                                      "parent" varchar(255),
                                      "is_leaf" bool,
                                      "sort" int8,
                                      "disabled" bool,
                                      "remark" varchar(255),
                                      "enabled" bool,
                                      PRIMARY KEY ("id")
)
;

INSERT INTO "public"."dict_d201" ("id", "key", "name", "pinyin", "parent", "is_leaf", "sort", "disabled", "remark", "enabled") VALUES (11, '11', '驻外地办事机构(流出地)', NULL, '-1', NULL, 11, NULL, NULL, NULL);
INSERT INTO "public"."dict_d201" ("id", "key", "name", "pinyin", "parent", "is_leaf", "sort", "disabled", "remark", "enabled") VALUES (12, '12', '商会(流出地)', NULL, '-1', NULL, 12, NULL, NULL, NULL);
INSERT INTO "public"."dict_d201" ("id", "key", "name", "pinyin", "parent", "is_leaf", "sort", "disabled", "remark", "enabled") VALUES (13, '13', '流入地的园区(流出地)', NULL, '-1', NULL, 13, NULL, NULL, NULL);
INSERT INTO "public"."dict_d201" ("id", "key", "name", "pinyin", "parent", "is_leaf", "sort", "disabled", "remark", "enabled") VALUES (14, '14', '行业协会(流出地)', NULL, '-1', NULL, 14, NULL, NULL, NULL);
INSERT INTO "public"."dict_d201" ("id", "key", "name", "pinyin", "parent", "is_leaf", "sort", "disabled", "remark", "enabled") VALUES (15, '15', '其他(流出地)', NULL, '-1', NULL, 15, NULL, NULL, NULL);
INSERT INTO "public"."dict_d201" ("id", "key", "name", "pinyin", "parent", "is_leaf", "sort", "disabled", "remark", "enabled") VALUES (21, '21', '街道(流入地)', NULL, '-1', NULL, 21, NULL, NULL, NULL);
INSERT INTO "public"."dict_d201" ("id", "key", "name", "pinyin", "parent", "is_leaf", "sort", "disabled", "remark", "enabled") VALUES (22, '22', '社区(流入地)', NULL, '-1', NULL, 22, NULL, NULL, NULL);
INSERT INTO "public"."dict_d201" ("id", "key", "name", "pinyin", "parent", "is_leaf", "sort", "disabled", "remark", "enabled") VALUES (23, '23', '园区(流入地)', NULL, '-1', NULL, 23, NULL, NULL, NULL);
INSERT INTO "public"."dict_d201" ("id", "key", "name", "pinyin", "parent", "is_leaf", "sort", "disabled", "remark", "enabled") VALUES (24, '24', '楼宇(流入地)', NULL, '-1', NULL, 24, NULL, NULL, NULL);
INSERT INTO "public"."dict_d201" ("id", "key", "name", "pinyin", "parent", "is_leaf", "sort", "disabled", "remark", "enabled") VALUES (25, '25', '商圈(流入地)', NULL, '-1', NULL, 25, NULL, NULL, NULL);
INSERT INTO "public"."dict_d201" ("id", "key", "name", "pinyin", "parent", "is_leaf", "sort", "disabled", "remark", "enabled") VALUES (26, '26', '新经济组织(流入地)', NULL, '-1', NULL, 26, NULL, NULL, NULL);
INSERT INTO "public"."dict_d201" ("id", "key", "name", "pinyin", "parent", "is_leaf", "sort", "disabled", "remark", "enabled") VALUES (27, '27', '新社会组织(流入地)', NULL, '-1', NULL, 27, NULL, NULL, NULL);
INSERT INTO "public"."dict_d201" ("id", "key", "name", "pinyin", "parent", "is_leaf", "sort", "disabled", "remark", "enabled") VALUES (28, '28', '其他(流入地)', NULL, '-1', NULL, 28, NULL, NULL, NULL);





-----------创建流动党员登记表----------
CREATE SEQUENCE IF NOT EXISTS ccp_mem_flow_sign_id_seq
    INCREMENT 1
    MINVALUE 1
    NO MAXVALUE
    START 1
    CACHE 1;

DROP TABLE IF EXISTS "public"."ccp_mem_flow_sign";
CREATE TABLE "public"."ccp_mem_flow_sign" (
                                              "id"  varchar(64)  NOT NULL,
                                              "code" varchar(64) NOT NULL,
                                              "mem_code" varchar(64) NULL,
                                              "name" varchar(64) NULL,
                                              "sex_code" varchar(64) NULL,
                                              "sex_name" varchar(64) NULL,
                                              "idcard" varchar(255) NULL,
                                              "birthday" date NULL,
                                              "d06_code" varchar(64) NULL,
                                              "d06_name" varchar(64) NULL,
                                              "d07_code" varchar(64) NULL,
                                              "d07_name" varchar(64) NULL,
                                              "d145_code" varchar(64) NULL,
                                              "d145_name" varchar(64) NULL,
                                              "join_date" date NULL,
                                              "formal_date" date NULL,
                                              "d09_code" varchar(64) NULL,
                                              "d09_name" varchar(64) NULL,
                                              "d20_code" varchar(64) NULL,
                                              "d20_name" varchar(64) NULL,
                                              "work_post_code" varchar(64) NULL,
                                              "work_post_name" varchar(64) NULL,
                                              "d08_code" varchar(64) NULL,
                                              "d08_name" varchar(64) NULL,
                                              "is_farmer" varchar(64) NULL,
                                              "phone" varchar(64) NULL,
                                              "house_hold_register" varchar(255) NULL,
                                              "home_address" varchar(255) NULL,
                                              "age_correction" varchar(64) NULL,
                                              "org_flow_code" varchar(64) NULL,
                                              "org_flow_level_code" varchar(64) NULL,
                                              "org_flow_name" varchar(64) NULL,
                                              "apply_org_flow_code" varchar(64) NULL,
                                              "apply_org_flow_level_code" varchar(64) NULL,
                                              "apply_org_flow_name" varchar(64) NULL,
                                              "connection" varchar(64) NULL,
                                              "flow_connection_name" varchar(64) NULL,
                                              "flow_connection" varchar(64) NULL,
                                              "frequently_phone" varchar(64) NULL,
                                              "d146_code" varchar(64) NULL,
                                              "d146_name" varchar(64) NULL,
                                              "flow_reason_detail" varchar(255) NULL,
                                              "out_date" date NULL,
                                              "out_instructions" varchar(255) NULL,
                                              "ghana_date" date NULL,
                                              "receive_code" varchar(64) NULL,
                                              "receive_name" varchar(64) NULL,
                                              "data_type" varchar(10) NULL,
                                              "create_time" date NULL,
                                              "update_time" date NULL,
                                              "delete_time" date NULL,
                                              "remark" varchar(255) NULL,
                                              "timestamp" timestamp(6) DEFAULT CURRENT_TIMESTAMP NULL,
                                              "org_connection" varchar NULL,
                                              "org_connection_name" varchar NULL,
                                              "flow_up_code" varchar NULL,
                                              "receive_node_code" varchar NULL,
                                              "send_node_code" varchar NULL,
                                              "flow_warn_status" varchar NULL,
                                              "flow_view_warn" varchar NULL,
                                              CONSTRAINT ccp_mem_flow_sign_pkey PRIMARY KEY ("id"),
                                              CONSTRAINT ccp_mem_flow_sign_code UNIQUE ("code")
);
COMMENT ON COLUMN "public"."ccp_mem_flow_sign"."id" IS '唯一标识';
COMMENT ON COLUMN "public"."ccp_mem_flow_sign"."code" IS '唯一标识';
COMMENT ON COLUMN "public"."ccp_mem_flow_sign"."mem_code" IS '党员code';
COMMENT ON COLUMN "public"."ccp_mem_flow_sign"."name" IS '姓名';
COMMENT ON COLUMN "public"."ccp_mem_flow_sign"."sex_code" IS '性别code';
COMMENT ON COLUMN "public"."ccp_mem_flow_sign"."sex_name" IS '性别名称';
COMMENT ON COLUMN "public"."ccp_mem_flow_sign"."idcard" IS '身份证';
COMMENT ON COLUMN "public"."ccp_mem_flow_sign"."birthday" IS '出生日期';
COMMENT ON COLUMN "public"."ccp_mem_flow_sign"."d06_code" IS '民族code';
COMMENT ON COLUMN "public"."ccp_mem_flow_sign"."d06_name" IS '民族名称';
COMMENT ON COLUMN "public"."ccp_mem_flow_sign"."d07_code" IS '学历code';
COMMENT ON COLUMN "public"."ccp_mem_flow_sign"."d07_name" IS '学历名称';
COMMENT ON COLUMN "public"."ccp_mem_flow_sign"."d145_code" IS '学位code';
COMMENT ON COLUMN "public"."ccp_mem_flow_sign"."d145_name" IS '学位名称';
COMMENT ON COLUMN "public"."ccp_mem_flow_sign"."join_date" IS '入党日期';
COMMENT ON COLUMN "public"."ccp_mem_flow_sign"."formal_date" IS '转正日期';
COMMENT ON COLUMN "public"."ccp_mem_flow_sign"."d09_code" IS '工作岗位';
COMMENT ON COLUMN "public"."ccp_mem_flow_sign"."d09_name" IS '工作岗位名称';
COMMENT ON COLUMN "public"."ccp_mem_flow_sign"."d20_code" IS '新社会阶层类型code';
COMMENT ON COLUMN "public"."ccp_mem_flow_sign"."d20_name" IS '新社会阶层类型名称';
COMMENT ON COLUMN "public"."ccp_mem_flow_sign"."work_post_code" IS '从事专业技术职务code';
COMMENT ON COLUMN "public"."ccp_mem_flow_sign"."work_post_name" IS '从事专业技术职务名称';
COMMENT ON COLUMN "public"."ccp_mem_flow_sign"."d08_code" IS '人员类别';
COMMENT ON COLUMN "public"."ccp_mem_flow_sign"."d08_name" IS '人员类别名称';
COMMENT ON COLUMN "public"."ccp_mem_flow_sign"."is_farmer" IS '是否为农民';
COMMENT ON COLUMN "public"."ccp_mem_flow_sign"."phone" IS '手机号码';
COMMENT ON COLUMN "public"."ccp_mem_flow_sign"."house_hold_register" IS '户籍所在地';
COMMENT ON COLUMN "public"."ccp_mem_flow_sign"."home_address" IS '家庭住址';
COMMENT ON COLUMN "public"."ccp_mem_flow_sign"."age_correction" IS '党龄校正值';
COMMENT ON COLUMN "public"."ccp_mem_flow_sign"."org_flow_code" IS '流动党组织代码';
COMMENT ON COLUMN "public"."ccp_mem_flow_sign"."org_flow_level_code" IS '流动党组织层级码';
COMMENT ON COLUMN "public"."ccp_mem_flow_sign"."org_flow_name" IS '流动党组织名称';
COMMENT ON COLUMN "public"."ccp_mem_flow_sign"."apply_org_flow_code" IS '申请党组织代码';
COMMENT ON COLUMN "public"."ccp_mem_flow_sign"."apply_org_flow_level_code" IS '申请党组织层级码';
COMMENT ON COLUMN "public"."ccp_mem_flow_sign"."apply_org_flow_name" IS '申请党组织名称';
COMMENT ON COLUMN "public"."ccp_mem_flow_sign"."connection" IS '联系电话';
COMMENT ON COLUMN "public"."ccp_mem_flow_sign"."flow_connection_name" IS '流入地联系人';
COMMENT ON COLUMN "public"."ccp_mem_flow_sign"."flow_connection" IS '流入地联系方式';
COMMENT ON COLUMN "public"."ccp_mem_flow_sign"."frequently_phone" IS '提醒常用手机号码';
COMMENT ON COLUMN "public"."ccp_mem_flow_sign"."d146_code" IS '流动原因code';
COMMENT ON COLUMN "public"."ccp_mem_flow_sign"."d146_name" IS '流动原因';
COMMENT ON COLUMN "public"."ccp_mem_flow_sign"."flow_reason_detail" IS '流动原因详情';
COMMENT ON COLUMN "public"."ccp_mem_flow_sign"."out_date" IS '外出日期';
COMMENT ON COLUMN "public"."ccp_mem_flow_sign"."out_instructions" IS '外出日期补充说明';
COMMENT ON COLUMN "public"."ccp_mem_flow_sign"."ghana_date" IS '党费缴至日期流出地';
COMMENT ON COLUMN "public"."ccp_mem_flow_sign"."receive_code" IS '接收支部code';
COMMENT ON COLUMN "public"."ccp_mem_flow_sign"."receive_name" IS '接收支部';
COMMENT ON COLUMN "public"."ccp_mem_flow_sign"."data_type" IS '0-本节点  1-省内非本节点 2-中组部获取的省外数据';
COMMENT ON COLUMN "public"."ccp_mem_flow_sign"."org_connection" IS '党组织联系方式';
COMMENT ON COLUMN "public"."ccp_mem_flow_sign"."org_connection_name" IS '党组织联系人';
COMMENT ON COLUMN "public"."ccp_mem_flow_sign"."receive_node_code" IS '接收提醒根节点代码';
COMMENT ON COLUMN "public"."ccp_mem_flow_sign"."send_node_code" IS '发送提醒根节代码';
COMMENT ON COLUMN "public"."ccp_mem_flow_sign"."flow_warn_status" IS '流动提醒处理状态 1.办理中 2.已登记  3.已退回';
COMMENT ON COLUMN "public"."ccp_mem_flow_sign"."flow_view_warn" IS '流出地是否查看提醒 0-否 1-是';



---------d146字典表添加数据-----------
delete from public.dict_d146 where id = 1;
INSERT INTO public.dict_d146 (id, "key", "name", pinyin, parent, is_leaf, sort, disabled, remark, enabled) VALUES(1, '1', '工作调动', NULL, '-1', NULL, 1, NULL, NULL, NULL);
delete from public.dict_d146 where id = 2;
INSERT INTO public.dict_d146 (id, "key", "name", pinyin, parent, is_leaf, sort, disabled, remark, enabled) VALUES(2, '2', '入学或毕业', NULL, '-1', NULL, 2, NULL, NULL, NULL);
delete from public.dict_d146 where id = 3;
INSERT INTO public.dict_d146 (id, "key", "name", pinyin, parent, is_leaf, sort, disabled, remark, enabled) VALUES(3, '3', '入伍或退伍', NULL, '-1', NULL, 3, NULL, NULL, NULL);
delete from public.dict_d146 where id = 4;
INSERT INTO public.dict_d146 (id, "key", "name", pinyin, parent, is_leaf, sort, disabled, remark, enabled) VALUES(4, '4', '入职或离职', NULL, '-1', NULL, 4, NULL, NULL, NULL);
delete from public.dict_d146 where id = 5;
INSERT INTO public.dict_d146 (id, "key", "name", pinyin, parent, is_leaf, sort, disabled, remark, enabled) VALUES(5, '5', '离开户籍地定居', NULL, '-1', NULL, 5, NULL, NULL, NULL);
delete from public.dict_d146 where id = 6;
INSERT INTO public.dict_d146 (id, "key", "name", pinyin, parent, is_leaf, sort, disabled, remark, enabled) VALUES(6, '6', '外出务工经商', NULL, '-1', NULL, 6, NULL, NULL, NULL);
delete from public.dict_d146 where id = 7;
INSERT INTO public.dict_d146 (id, "key", "name", pinyin, parent, is_leaf, sort, disabled, remark, enabled) VALUES(7, '7', '外出居住 ', NULL, '-1', NULL, 7, NULL, NULL, NULL);
delete from public.dict_d146 where id = 8;
INSERT INTO public.dict_d146 (id, "key", "name", pinyin, parent, is_leaf, sort, disabled, remark, enabled) VALUES(8, '8', '待业或未落实工作单位', NULL, '-1', NULL, 8, NULL, NULL, NULL);
delete from public.dict_d146 where id = 10;
INSERT INTO public.dict_d146 (id, "key", "name", pinyin, parent, is_leaf, sort, disabled, remark, enabled) VALUES(10, '10', '未就业的毕业学生', NULL, '-1', NULL, 10, NULL, NULL, NULL);
delete from public.dict_d146 where id = 11;
INSERT INTO public.dict_d146 (id, "key", "name", pinyin, parent, is_leaf, sort, disabled, remark, enabled) VALUES(11, '11', '自主择业的退伍军人', NULL, '-1', NULL, 11, NULL, NULL, NULL);
delete from public.dict_d146 where id = 12;
INSERT INTO public.dict_d146 (id, "key", "name", pinyin, parent, is_leaf, sort, disabled, remark, enabled) VALUES(12, '12', '离退休人员党员异地居住', NULL, '-1', NULL, 12, NULL, NULL, NULL);
delete from public.dict_d146 where id = 9;
INSERT INTO public.dict_d146 (id, "key", "name", pinyin, parent, is_leaf, sort, disabled, remark, enabled) VALUES(9, '9', '其他', NULL, '-1', NULL, 13, NULL, NULL, NULL);

---------流入登记表改变注释-----------
comment on column ccp_mem_flow_sign.org_flow_code is '党员组织关系所在党组织code-改';
comment on column ccp_mem_flow_sign.org_flow_level_code is '党员组织关系所在党组织层级码-改';
comment on column ccp_mem_flow_sign.org_flow_name is '党员组织关系所在党组织名称-改';
comment on column ccp_mem_flow_sign.apply_org_flow_code is '流入地党支部code-改';
comment on column ccp_mem_flow_sign.apply_org_flow_level_code is '流入地党支部层级码-改';
comment on column ccp_mem_flow_sign.apply_org_flow_name is '流入地党支部名称-改';
comment on column ccp_mem_flow_sign.frequently_phone is '党员在流入地常用联系方式-改';
comment on column ccp_mem_flow_sign.out_instructions is '外出地点补充说明-改';

----------流入登记表添加字段--------
alter table public.ccp_mem_flow_sign add column IF NOT EXISTS org_flow_division varchar(255) default null;
comment on column public.ccp_mem_flow_sign.org_flow_division is '党员组织关系所在党组织区划码';



-----------外出原因-------------
DROP TABLE IF EXISTS "public"."dict_d204";
CREATE TABLE IF NOT EXISTS public.dict_d204 (
                                                id bigserial NOT NULL,
                                                "key" varchar(255) NULL,
    name varchar(255) NULL,
    pinyin varchar(255) NULL,
    parent varchar(255) NULL,
    is_leaf bool NULL,
    sort int8 NULL,
    remark varchar(255) NULL,
    disabled bool NULL,
    enabled bool NULL,
    CONSTRAINT dict_d204_pkey PRIMARY KEY (id)
    );
INSERT INTO dict_d204 (id, "key", "name", pinyin, parent, is_leaf, sort, remark, disabled, enabled)
VALUES(1, '01', '外出务工经商', 'wcwgjs', '-1', true, 1, NULL, NULL, NULL);
INSERT INTO dict_d204 (id, "key", "name", pinyin, parent, is_leaf, sort, remark, disabled, enabled)
VALUES(2, '02', '外出居住', 'wcjz', '-1', true, 2, NULL, NULL, NULL);
INSERT INTO dict_d204 (id, "key", "name", pinyin, parent, is_leaf, sort, remark, disabled, enabled)
VALUES(3, '03', '待业或未落实工作单位', 'dyhwlsgzdw', '-1', true, 3, NULL, NULL, NULL);
INSERT INTO dict_d204 (id, "key", "name", pinyin, parent, is_leaf, sort, remark, disabled, enabled)
VALUES(4, '04', '未就业的毕业学生', 'wjydbyxs', '-1', true, 4, NULL, NULL, NULL);
INSERT INTO dict_d204 (id, "key", "name", pinyin, parent, is_leaf, sort, remark, disabled, enabled)
VALUES(5, '05', '自主择业的退伍军人', 'zzzydtwjr', '-1', true, 5, NULL, NULL, NULL);
INSERT INTO dict_d204 (id, "key", "name", pinyin, parent, is_leaf, sort, remark, disabled, enabled)
VALUES(6, '06', '离退休人员党员异地居住', 'ltxrydyydjz', '-1', true, 6, NULL, NULL, NULL);
INSERT INTO dict_d204 (id, "key", "name", pinyin, parent, is_leaf, sort, remark, disabled, enabled)
VALUES(9, '09', '其他', 'qt', '-1', true, 9, NULL, NULL, NULL);

--------------流入地单位类型------------
DROP TABLE IF EXISTS "public"."dict_d205";
CREATE TABLE IF NOT EXISTS public.dict_d205 (
                                                id bigserial NOT NULL,
                                                "key" varchar(20) DEFAULT NULL::character varying NOT NULL,
    "name" varchar(100) DEFAULT NULL::character varying NULL,
    pinyin varchar(255) DEFAULT NULL::character varying NULL,
    parent varchar(5) DEFAULT NULL::character varying NULL,
    is_leaf bool NULL,
    sort int8 NULL,
    map_new_key varchar(20) DEFAULT NULL::character varying NULL,
    map_new_key_name varchar(100) DEFAULT NULL::character varying NULL,
    old_key varchar(10) DEFAULT NULL::character varying NULL,
    old_name varchar(100) DEFAULT NULL::character varying NULL,
    remark varchar(255) DEFAULT NULL::character varying NULL,
    disabled bool NULL,
    enabled bool NULL,
    CONSTRAINT dict_d205_pkey PRIMARY KEY (id)
    );
INSERT INTO public.dict_d205 (id, key, name, pinyin, parent, is_leaf, sort, map_new_key, map_new_key_name, old_key, old_name, remark, disabled, enabled)
VALUES (3, '12', '事业单位', null, '1', true, 3, null, null, null, null, null, false, false);
INSERT INTO public.dict_d205 (id, key, name, pinyin, parent, is_leaf, sort, map_new_key, map_new_key_name, old_key, old_name, remark, disabled, enabled)
VALUES (1, '1', '公有经济控制单位', null, '-1', false, 1, null, null, null, null, null, false, false);
INSERT INTO public.dict_d205 (id, key, name, pinyin, parent, is_leaf, sort, map_new_key, map_new_key_name, old_key, old_name, remark, disabled, enabled)
VALUES (7, '21', '民办非企业单位 ', null, '2', true, 7, null, null, null, null, null, false, false);
INSERT INTO public.dict_d205 (id, key, name, pinyin, parent, is_leaf, sort, map_new_key, map_new_key_name, old_key, old_name, remark, disabled, enabled)
VALUES (4, '13', '港澳台商投资企业', null, '1', true, 4, null, null, null, null, null, false, false);
INSERT INTO public.dict_d205 (id, key, name, pinyin, parent, is_leaf, sort, map_new_key, map_new_key_name, old_key, old_name, remark, disabled, enabled)
VALUES (5, '14', '外商投资企业', null, '1', true, 5, null, null, null, null, null, false, false);
INSERT INTO public.dict_d205 (id, key, name, pinyin, parent, is_leaf, sort, map_new_key, map_new_key_name, old_key, old_name, remark, disabled, enabled)
VALUES (2, '11', '机关', null, '1', true, 2, null, null, null, null, null, false, false);
INSERT INTO public.dict_d205 (id, key, name, pinyin, parent, is_leaf, sort, map_new_key, map_new_key_name, old_key, old_name, remark, disabled, enabled)
VALUES (11, '3', '个体工商户 ', null, '-1', true, 11, null, null, null, null, null, false, false);
INSERT INTO public.dict_d205 (id, key, name, pinyin, parent, is_leaf, sort, map_new_key, map_new_key_name, old_key, old_name, remark, disabled, enabled)
VALUES (8, '22', '私营企业 ', null, '2', true, 8, null, null, null, null, null, false, false);
INSERT INTO public.dict_d205 (id, key, name, pinyin, parent, is_leaf, sort, map_new_key, map_new_key_name, old_key, old_name, remark, disabled, enabled)
VALUES (9, '23', '港澳台商投资企业 ', null, '2', true, 9, null, null, null, null, null, false, false);
INSERT INTO public.dict_d205 (id, key, name, pinyin, parent, is_leaf, sort, map_new_key, map_new_key_name, old_key, old_name, remark, disabled, enabled)
VALUES (6, '2', '非公有经济控制单位 ', null, '-1', false, 6, null, null, null, null, null, false, false);
INSERT INTO public.dict_d205 (id, key, name, pinyin, parent, is_leaf, sort, map_new_key, map_new_key_name, old_key, old_name, remark, disabled, enabled)
VALUES (15, '7', '其他 ', null, '-1', true, 15, null, null, null, null, null, false, false);
INSERT INTO public.dict_d205 (id, key, name, pinyin, parent, is_leaf, sort, map_new_key, map_new_key_name, old_key, old_name, remark, disabled, enabled)
VALUES (12, '4', '城市社区 ', null, '-1', true, 12, null, null, null, null, null, false, false);
INSERT INTO public.dict_d205 (id, key, name, pinyin, parent, is_leaf, sort, map_new_key, map_new_key_name, old_key, old_name, remark, disabled, enabled)
VALUES (13, '5', '乡镇社区 ', null, '-1', true, 13, null, null, null, null, null, false, false);
INSERT INTO public.dict_d205 (id, key, name, pinyin, parent, is_leaf, sort, map_new_key, map_new_key_name, old_key, old_name, remark, disabled, enabled)
VALUES (10, '24', '外商投资企业 ', null, '2', true, 10, null, null, null, null, null, false, false);
INSERT INTO public.dict_d205 (id, key, name, pinyin, parent, is_leaf, sort, map_new_key, map_new_key_name, old_key, old_name, remark, disabled, enabled)
VALUES (14, '6', '行政村 ', null, '-1', true, 14, null, null, null, null, null, false, false);

--------------失去联系情形---------------
DROP TABLE IF EXISTS "public"."dict_d206";
CREATE TABLE IF NOT EXISTS public.dict_d206 (
                                                id bigserial NOT NULL,
                                                "key" varchar(255) NULL,
    name varchar(255) NULL,
    pinyin varchar(255) NULL,
    parent varchar(255) NULL,
    is_leaf bool NULL,
    sort int8 NULL,
    remark varchar(255) NULL,
    disabled bool NULL,
    enabled bool NULL,
    CONSTRAINT dict_d206_pkey PRIMARY KEY (id)
    );

INSERT INTO dict_d206 (id, "key", "name", pinyin, parent, is_leaf, sort, remark, disabled, enabled)
VALUES(1, '1', '外出务工经商', NULL, '-1', true, 1, NULL, NULL, NULL);
INSERT INTO dict_d206 (id, "key", "name", pinyin, parent, is_leaf, sort, remark, disabled, enabled)
VALUES(2, '2', '单位改制或破产', NULL, '-1', true, 2, NULL, NULL, NULL);
INSERT INTO dict_d206 (id, "key", "name", pinyin, parent, is_leaf, sort, remark, disabled, enabled)
VALUES(3, '3', '毕业后去向不明', NULL, '-1', true, 3, NULL, NULL, NULL);
INSERT INTO dict_d206 (id, "key", "name", pinyin, parent, is_leaf, sort, remark, disabled, enabled)
VALUES(4, '4', '工作单位改变', NULL, '-1', true, 4, NULL, NULL, NULL);
INSERT INTO dict_d206 (id, "key", "name", pinyin, parent, is_leaf, sort, remark, disabled, enabled)
VALUES(5, '5', '出国（境）', NULL, '-1', true, 5, NULL, NULL, NULL);
INSERT INTO dict_d206 (id, "key", "name", pinyin, parent, is_leaf, sort, remark, disabled, enabled)
VALUES(6, '6', '居住地改变', NULL, '-1', true, 6, NULL, NULL, NULL);
INSERT INTO dict_d206 (id, "key", "name", pinyin, parent, is_leaf, sort, remark, disabled, enabled)
VALUES(7, '9', '其他', NULL, '-1', true, 9, NULL, NULL, NULL);


----------------外出流向字典表新增字典------------
delete from public.dict_d148 where id = 5;
INSERT INTO public.dict_d148 (id, "key", "name", pinyin, parent, is_leaf, sort, disabled, remark, enabled)
VALUES(5, '5', '流向流动党员党组织', NULL, NULL, NULL, NULL, NULL, NULL, NULL);


-------------撤销原因---------------
DROP TABLE IF EXISTS "public"."dict_d207";
CREATE TABLE IF NOT EXISTS public.dict_d207 (
                                                id bigserial NOT NULL,
                                                "key" varchar(255) NULL,
    name varchar(255) NULL,
    pinyin varchar(255) NULL,
    parent varchar(255) NULL,
    is_leaf bool NULL,
    sort int8 NULL,
    remark varchar(255) NULL,
    disabled bool NULL,
    enabled bool NULL,
    CONSTRAINT dict_d207_pkey PRIMARY KEY (id)
    );
INSERT INTO dict_d207 (id, "key", "name", pinyin, parent, is_leaf, sort, remark, disabled, enabled)
VALUES(1, '1', '已返回', NULL, '-1', true, 1, NULL, NULL, NULL);
INSERT INTO dict_d207 (id, "key", "name", pinyin, parent, is_leaf, sort, remark, disabled, enabled)
VALUES(2, '2', '信息填写错误', NULL, '-1', true, 2, NULL, NULL, NULL);
INSERT INTO dict_d207 (id, "key", "name", pinyin, parent, is_leaf, sort, remark, disabled, enabled)
VALUES(3, '9', '其他', NULL, '-1', true, 9, NULL, NULL, NULL);


-------------退回原因---------------
delete from public.dict_d153 where id = 6;
INSERT INTO dict_d153 (id, "key", "name", pinyin, parent, is_leaf, sort, remark, disabled, enabled)
VALUES(6, '6', '由流出地建立的流动党员党组织管理', NULL, NULL, NULL, NULL, NULL, NULL, NULL);
delete from public.dict_d153 where id = 7;
INSERT INTO dict_d153 (id, "key", "name", pinyin, parent, is_leaf, sort, remark, disabled, enabled)
VALUES(7, '7', '有转移组织关系意愿且符合转移条件', NULL, NULL, NULL, NULL, NULL, NULL, NULL);


--------------流出前原工作岗位字典表------------
DROP TABLE IF EXISTS "public"."dict_d208";
CREATE TABLE IF NOT EXISTS public.dict_d208 (
                                                id bigserial NOT NULL,
                                                "key" varchar(20) DEFAULT NULL::character varying NOT NULL,
    "name" varchar(100) DEFAULT NULL::character varying NULL,
    pinyin varchar(255) DEFAULT NULL::character varying NULL,
    parent varchar(5) DEFAULT NULL::character varying NULL,
    is_leaf bool NULL,
    sort int8 NULL,
    map_new_key varchar(20) DEFAULT NULL::character varying NULL,
    map_new_key_name varchar(100) DEFAULT NULL::character varying NULL,
    old_key varchar(10) DEFAULT NULL::character varying NULL,
    old_name varchar(100) DEFAULT NULL::character varying NULL,
    remark varchar(255) DEFAULT NULL::character varying NULL,
    disabled bool NULL,
    enabled bool NULL,
    CONSTRAINT dict_d208_pkey PRIMARY KEY (id)
    );
INSERT INTO "public"."dict_d208"("id", "key", "name", "pinyin", "parent", "is_leaf", "sort", "map_new_key", "map_new_key_name", "old_key", "old_name", "remark", "disabled", "enabled")
VALUES (1, '01', '公有经济企事业单位人员', NULL, '-1', 'f', 1, NULL, NULL, NULL, NULL, NULL, 'f', 'f');
INSERT INTO "public"."dict_d208"("id", "key", "name", "pinyin", "parent", "is_leaf", "sort", "map_new_key", "map_new_key_name", "old_key", "old_name", "remark", "disabled", "enabled")
VALUES (2, '011', '党政机关工作人员', NULL, '01', 't', 2, NULL, NULL, NULL, NULL, NULL, 'f', 'f');
INSERT INTO "public"."dict_d208"("id", "key", "name", "pinyin", "parent", "is_leaf", "sort", "map_new_key", "map_new_key_name", "old_key", "old_name", "remark", "disabled", "enabled")
VALUES (3, '012', '事业单位管理岗位（含参公）', NULL, '01', 't', 3, NULL, NULL, NULL, NULL, NULL, 'f', 'f');
INSERT INTO "public"."dict_d208"("id", "key", "name", "pinyin", "parent", "is_leaf", "sort", "map_new_key", "map_new_key_name", "old_key", "old_name", "remark", "disabled", "enabled")
VALUES (4, '013', '事业单位专业技术岗位（含参公）', NULL, '01', 't', 4, NULL, NULL, NULL, NULL, NULL, 'f', 'f');
INSERT INTO "public"."dict_d208"("id", "key", "name", "pinyin", "parent", "is_leaf", "sort", "map_new_key", "map_new_key_name", "old_key", "old_name", "remark", "disabled", "enabled")
VALUES (5, '014', '公有经济控制企业管理岗位', NULL, '01', 't', 5, NULL, NULL, NULL, NULL, NULL, 'f', 'f');
INSERT INTO "public"."dict_d208"("id", "key", "name", "pinyin", "parent", "is_leaf", "sort", "map_new_key", "map_new_key_name", "old_key", "old_name", "remark", "disabled", "enabled")
VALUES (6, '015', '公有经济控制企业专业技术岗位', NULL, '01', 't', 6, NULL, NULL, NULL, NULL, NULL, 'f', 'f');
INSERT INTO "public"."dict_d208"("id", "key", "name", "pinyin", "parent", "is_leaf", "sort", "map_new_key", "map_new_key_name", "old_key", "old_name", "remark", "disabled", "enabled")
VALUES (7, '016', '公有制单位工勤技能人员', NULL, '01', 't', 7, NULL, NULL, NULL, NULL, NULL, 'f', 'f');
INSERT INTO "public"."dict_d208"("id", "key", "name", "pinyin", "parent", "is_leaf", "sort", "map_new_key", "map_new_key_name", "old_key", "old_name", "remark", "disabled", "enabled")
VALUES (8, '02', '非公有制经济组织从业人员', '', '-1', 'f', 8, NULL, NULL, NULL, NULL, NULL, 'f', 'f');
INSERT INTO "public"."dict_d208"("id", "key", "name", "pinyin", "parent", "is_leaf", "sort", "map_new_key", "map_new_key_name", "old_key", "old_name", "remark", "disabled", "enabled")
VALUES (9, '021', '非公有经济控制企业管理岗位', NULL, '02', 't', 9, NULL, NULL, NULL, NULL, NULL, 'f', 'f');
INSERT INTO "public"."dict_d208"("id", "key", "name", "pinyin", "parent", "is_leaf", "sort", "map_new_key", "map_new_key_name", "old_key", "old_name", "remark", "disabled", "enabled")
VALUES (10, '022', '非公有经济控制企业专业技术岗位', NULL, '02', 't', 10, NULL, NULL, NULL, NULL, NULL, 'f', 'f');
INSERT INTO "public"."dict_d208"("id", "key", "name", "pinyin", "parent", "is_leaf", "sort", "map_new_key", "map_new_key_name", "old_key", "old_name", "remark", "disabled", "enabled")
VALUES (11, '023', '非公有制单位工勤技能人员', NULL, '02', 't', 11, NULL, NULL, NULL, NULL, NULL, 'f', 'f');
INSERT INTO "public"."dict_d208"("id", "key", "name", "pinyin", "parent", "is_leaf", "sort", "map_new_key", "map_new_key_name", "old_key", "old_name", "remark", "disabled", "enabled")
VALUES (12, '03', '社会组织从业人员', NULL, '-1', 'f', 12, NULL, NULL, NULL, NULL, NULL, 'f', 'f');
INSERT INTO "public"."dict_d208"("id", "key", "name", "pinyin", "parent", "is_leaf", "sort", "map_new_key", "map_new_key_name", "old_key", "old_name", "remark", "disabled", "enabled")
VALUES (13, '031', '社会团体', NULL, '03', 'f', 13, NULL, NULL, NULL, NULL, NULL, 'f', 'f');
INSERT INTO "public"."dict_d208"("id", "key", "name", "pinyin", "parent", "is_leaf", "sort", "map_new_key", "map_new_key_name", "old_key", "old_name", "remark", "disabled", "enabled")
VALUES (14, '0311', '社会团体管理岗位', NULL, '031', 't', 14, NULL, NULL, NULL, NULL, NULL, 'f', 'f');
INSERT INTO "public"."dict_d208"("id", "key", "name", "pinyin", "parent", "is_leaf", "sort", "map_new_key", "map_new_key_name", "old_key", "old_name", "remark", "disabled", "enabled")
VALUES (15, '0312', '社会团体专业技术岗位', NULL, '031', 't', 15, NULL, NULL, NULL, NULL, NULL, 'f', 'f');
INSERT INTO "public"."dict_d208"("id", "key", "name", "pinyin", "parent", "is_leaf", "sort", "map_new_key", "map_new_key_name", "old_key", "old_name", "remark", "disabled", "enabled")
VALUES (16, '0313', '社会团体工勤技能人员', NULL, '031', 't', 16, NULL, NULL, NULL, NULL, NULL, 'f', 'f');
INSERT INTO "public"."dict_d208"("id", "key", "name", "pinyin", "parent", "is_leaf", "sort", "map_new_key", "map_new_key_name", "old_key", "old_name", "remark", "disabled", "enabled")
VALUES (17, '032', '民办非企业', NULL, '03', 'f', 17, NULL, NULL, NULL, NULL, NULL, 'f', 'f');
INSERT INTO "public"."dict_d208"("id", "key", "name", "pinyin", "parent", "is_leaf", "sort", "map_new_key", "map_new_key_name", "old_key", "old_name", "remark", "disabled", "enabled")
VALUES (18, '0321', '民办非企业管理岗位', NULL, '032', 't', 18, NULL, NULL, NULL, NULL, NULL, 'f', 'f');
INSERT INTO "public"."dict_d208"("id", "key", "name", "pinyin", "parent", "is_leaf", "sort", "map_new_key", "map_new_key_name", "old_key", "old_name", "remark", "disabled", "enabled")
VALUES (19, '0322', '民办非企业专业技术岗位', NULL, '032', 't', 19, NULL, NULL, NULL, NULL, NULL, 'f', 'f');
INSERT INTO "public"."dict_d208"("id", "key", "name", "pinyin", "parent", "is_leaf", "sort", "map_new_key", "map_new_key_name", "old_key", "old_name", "remark", "disabled", "enabled")
VALUES (20, '0323', '民办非企业工勤技能人员', NULL, '032', 't', 20, NULL, NULL, NULL, NULL, NULL, 'f', 'f');
INSERT INTO "public"."dict_d208"("id", "key", "name", "pinyin", "parent", "is_leaf", "sort", "map_new_key", "map_new_key_name", "old_key", "old_name", "remark", "disabled", "enabled")
VALUES (21, '033', '中介组织从业人员', NULL, '03', 't', 21, NULL, NULL, NULL, NULL, NULL, 'f', 'f');
INSERT INTO "public"."dict_d208"("id", "key", "name", "pinyin", "parent", "is_leaf", "sort", "map_new_key", "map_new_key_name", "old_key", "old_name", "remark", "disabled", "enabled")
VALUES (22, '034', '其他社会组织从业人员', NULL, '03', 't', 22, NULL, NULL, NULL, NULL, NULL, 'f', 'f');
INSERT INTO "public"."dict_d208"("id", "key", "name", "pinyin", "parent", "is_leaf", "sort", "map_new_key", "map_new_key_name", "old_key", "old_name", "remark", "disabled", "enabled")
VALUES (23, '04', '新就业群体', NULL, '-1', 't', 23, NULL, NULL, NULL, NULL, NULL, 'f', 'f');
INSERT INTO "public"."dict_d208"("id", "key", "name", "pinyin", "parent", "is_leaf", "sort", "map_new_key", "map_new_key_name", "old_key", "old_name", "remark", "disabled", "enabled")
VALUES (24, '1', '农牧渔民', NULL, '-1', 't', 24, NULL, NULL, NULL, NULL, NULL, 'f', 'f');
INSERT INTO "public"."dict_d208"("id", "key", "name", "pinyin", "parent", "is_leaf", "sort", "map_new_key", "map_new_key_name", "old_key", "old_name", "remark", "disabled", "enabled")
VALUES (25, '2', '现役解放军、武警', NULL, '-1', 't', 25, NULL, NULL, NULL, NULL, NULL, 'f', 'f');
INSERT INTO "public"."dict_d208"("id", "key", "name", "pinyin", "parent", "is_leaf", "sort", "map_new_key", "map_new_key_name", "old_key", "old_name", "remark", "disabled", "enabled")
VALUES (26, '3', '学生', NULL, '-1', 't', 26, NULL, NULL, NULL, NULL, NULL, 'f', 'f');
INSERT INTO "public"."dict_d208"("id", "key", "name", "pinyin", "parent", "is_leaf", "sort", "map_new_key", "map_new_key_name", "old_key", "old_name", "remark", "disabled", "enabled")
VALUES (27, '4', '离退休人员', NULL, '-1', 't', 27, NULL, NULL, NULL, NULL, NULL, 'f', 'f');
INSERT INTO "public"."dict_d208"("id", "key", "name", "pinyin", "parent", "is_leaf", "sort", "map_new_key", "map_new_key_name", "old_key", "old_name", "remark", "disabled", "enabled")
VALUES (28, '5', '未就业的高校毕业生', NULL, '-1', 't', 28, NULL, NULL, NULL, NULL, NULL, 'f', 'f');
INSERT INTO "public"."dict_d208"("id", "key", "name", "pinyin", "parent", "is_leaf", "sort", "map_new_key", "map_new_key_name", "old_key", "old_name", "remark", "disabled", "enabled")
VALUES (29, '6', '其他', NULL, '-1', 't', 29, NULL, NULL, NULL, NULL, NULL, 'f', 'f');



-------------重要事反馈---------------
DROP TABLE IF EXISTS "public"."dict_d209";
CREATE TABLE IF NOT EXISTS public.dict_d209 (
                                                id bigserial NOT NULL,
                                                "key" varchar(255) NULL,
    name varchar(255) NULL,
    pinyin varchar(255) NULL,
    parent varchar(255) NULL,
    is_leaf bool NULL,
    sort int8 NULL,
    remark varchar(255) NULL,
    disabled bool NULL,
    enabled bool NULL,
    CONSTRAINT dict_d209_pkey PRIMARY KEY (id)
    );

INSERT INTO dict_d209 (id, "key", "name", pinyin, parent, is_leaf, sort, remark, disabled, enabled)
VALUES(1, '1', '正常参加组织生活', NULL, '-1', true, 1, NULL, NULL, NULL);
INSERT INTO dict_d209 (id, "key", "name", pinyin, parent, is_leaf, sort, remark, disabled, enabled)
VALUES(2, '2', '偶尔参加组织生活情况', NULL, '-1', true, 2, NULL, NULL, NULL);
INSERT INTO dict_d209 (id, "key", "name", pinyin, parent, is_leaf, sort, remark, disabled, enabled)
VALUES(3, '3', '从未参加组织生活情况', NULL, '-1', true, 3, NULL, NULL, NULL);



-------------活动类型---------------
DROP TABLE IF EXISTS "public"."dict_d210";
CREATE TABLE IF NOT EXISTS public.dict_d210 (
                                                id bigserial NOT NULL,
                                                "key" varchar(255) NULL,
    name varchar(255) NULL,
    pinyin varchar(255) NULL,
    parent varchar(255) NULL,
    is_leaf bool NULL,
    sort int8 NULL,
    remark varchar(255) NULL,
    disabled bool NULL,
    enabled bool NULL,
    CONSTRAINT dict_d210_pkey PRIMARY KEY (id)
    );

INSERT INTO dict_d210 (id, "key", "name", pinyin, parent, is_leaf, sort, remark, disabled, enabled)
VALUES(0, '1', '组织生活（三会一课、主题党日）', NULL, '-1', true, 0, NULL, NULL, NULL);
INSERT INTO dict_d210 (id, "key", "name", pinyin, parent, is_leaf, sort, remark, disabled, enabled)
VALUES(1, '2', '组织生活会', NULL, '-1', true, 1, NULL, NULL, NULL);
INSERT INTO dict_d210 (id, "key", "name", pinyin, parent, is_leaf, sort, remark, disabled, enabled)
VALUES(3, '3', '民主评议党员', NULL, '-1', true, 1, NULL, NULL, NULL);
INSERT INTO dict_d210 (id, "key", "name", pinyin, parent, is_leaf, sort, remark, disabled, enabled)
VALUES(4, '4', '志愿服务', NULL, '-1', true, 1, NULL, NULL, NULL);
INSERT INTO dict_d210 (id, "key", "name", pinyin, parent, is_leaf, sort, remark, disabled, enabled)
VALUES(5, '5', '其他', NULL, '-1', true, 1, NULL, NULL, NULL);

-------------流出走向---------------
DROP TABLE IF EXISTS "public"."dict_d211";
CREATE TABLE IF NOT EXISTS public.dict_d211 (
                                                id bigserial NOT NULL,
                                                "key" varchar(255) NULL,
    name varchar(255) NULL,
    pinyin varchar(255) NULL,
    parent varchar(255) NULL,
    is_leaf bool NULL,
    sort int8 NULL,
    remark varchar(255) NULL,
    disabled bool NULL,
    enabled bool NULL,
    CONSTRAINT dict_d211_pkey PRIMARY KEY (id)
    );

INSERT INTO dict_d211 (id, "key", "name", pinyin, parent, is_leaf, sort, remark, disabled, enabled)
VALUES(1, '1', '流出方发送', NULL, '-1', true, 1, NULL, NULL, NULL);
INSERT INTO dict_d211 (id, "key", "name", pinyin, parent, is_leaf, sort, remark, disabled, enabled)
VALUES(2, '2', '流入方发送', NULL, '-1', true, 2, NULL, NULL, NULL);



alter table public.mem_flow add column IF NOT EXISTS paired_contact_code varchar(255) default null;
comment on column public.mem_flow.paired_contact_code is '结对联系人Code';

alter table public.mem_flow add column IF NOT EXISTS lost_contact_time date default null;
comment on column public.mem_flow.lost_contact_time is '失去联系日期';

alter table public.mem_flow add column IF NOT EXISTS out_org_contact varchar(255) default null;
comment on column public.mem_flow.out_org_contact is '流入地党组织联系人';

alter table public.mem_flow add column IF NOT EXISTS out_org_contact_phone varchar(255) default null;
comment on column public.mem_flow.out_org_contact_phone is '流入地党组织联系方式';

alter table public.mem_flow add column IF NOT EXISTS in_org_user varchar(255) default null;
comment on column public.mem_flow.in_org_user is '流入地联系人';

alter table public.mem_flow add column IF NOT EXISTS flow_out_status varchar(10) default null;
comment on column public.mem_flow.flow_out_status is '是否为本节点流出地成立状态  0/null-否 1-是';

alter table public.mem_flow add column IF NOT EXISTS flow_sign_type varchar(10) default null;
comment on column public.mem_flow.flow_sign_type is '流出登记类型  1-省内主动流出登记 2-省外主动流出登记 3-省外提醒流出登记 4-省内提醒流出登记  5-流出地组织流出登记';




-------------已纳入流入地管理活动信息列表---------------
DROP TABLE IF EXISTS "public"."mem_flow_events";
CREATE TABLE IF NOT EXISTS public.mem_flow_events (
                                                      id varchar(32) not null primary key,
    mem_flow_code varchar(100) not null,
    mem_code varchar(100) not null,
    event_type varchar(1) default null,
    event_name varchar(300) default null,
    event_explain text default null,
    event_details text default null,
    event_date timestamp(6) default null,
    maintain_date timestamp(6) default null,
    filler_user_id varchar(100) default null,
    filler_user_name varchar(100) default null,
    filler_org_id varchar(100) default null,
    filler_org_code varchar(100) default null,
    filler_org_name varchar(100) default null,
    filler_date timestamp(6) default null,
    flow_direction varchar(1) default null,
    outflow_node_code varchar(100) default null,
    inflow_node_code varchar(100) default null,
    source_type int4 default null,
    create_time timestamp(6) default null,
    update_time timestamp(6) default null,
    delete_time timestamp(6) default null
    );
comment on column public.mem_flow_events.id is '主键ID';
comment on column public.mem_flow_events.mem_flow_code is '流动党员登记code';
comment on column public.mem_flow_events.mem_code is '流动党员code';
comment on column public.mem_flow_events.event_type is '活动类型（0组织活动 1志愿服务）';
comment on column public.mem_flow_events.event_name is '活动名称或者志愿服务名称';
comment on column public.mem_flow_events.event_explain is '活动情况说明';
comment on column public.mem_flow_events.event_details is '活动类型详情';
comment on column public.mem_flow_events.event_date is '活动日期';
comment on column public.mem_flow_events.maintain_date is '维护日期';
comment on column public.mem_flow_events.filler_user_id is '填写人ID';
comment on column public.mem_flow_events.filler_user_name is '填写人名称';
comment on column public.mem_flow_events.filler_org_id is '填写党组织ID';
comment on column public.mem_flow_events.filler_org_code is '填写党组织code';
comment on column public.mem_flow_events.filler_org_name is '填写党组织名称';
comment on column public.mem_flow_events.filler_date is '填写日期';
comment on column public.mem_flow_events.flow_direction is '流出走向 （消息走向：1流出方发送 2流入方发送）';
comment on column public.mem_flow_events.outflow_node_code is '流出方根节点代码';
comment on column public.mem_flow_events.inflow_node_code is '流入方根节点代码';
comment on column public.mem_flow_events.source_type is '1-本地创建  2-交换区数据';
comment on column public.mem_flow_events.create_time is '创建时间';
comment on column public.mem_flow_events.update_time is '修改时间';
comment on column public.mem_flow_events.delete_time is '删除时间';

alter table public.mem_flow add column IF NOT EXISTS d207_code varchar(255) default null;
comment on column public.mem_flow.d207_code is '撤销原因 dict_d207';

---------------流动党员消息表--------------
DROP TABLE IF EXISTS "public"."ccp_mem_flow_message";
CREATE TABLE IF NOT EXISTS "public"."ccp_mem_flow_message" (
                                                               "id"  varchar(64)  NOT NULL,
    "mem_flow_code" varchar(64) NULL,
    "direction" varchar(10) NULL,
    "out_flow_code" varchar(64) NULL,
    "out_flow_name" varchar(64) NULL,
    "into_flow_code" varchar(64) NULL,
    "into_flow_name" varchar(64) NULL,
    "message" text NULL,
    "out_flow_node" varchar(64) NULL,
    "into_flow_node" varchar(64) NULL,
    "source_type" int2 DEFAULT 1 NULL,
    "send_time" varchar(64) NULL,
    "create_time" date NULL,
    "update_time" date NULL,
    "delete_time" date NULL,
    "remark" varchar(255) NULL,
    "timestamp" timestamp(6) DEFAULT CURRENT_TIMESTAMP NULL,
    "send_name" varchar(255) NULL,
    "send_code" varchar(255) NULL,
    CONSTRAINT ccp_mem_flow_message_pkey PRIMARY KEY ("id")
    );
COMMENT ON COLUMN "public"."ccp_mem_flow_message"."id" IS '唯一标识';
COMMENT ON COLUMN "public"."ccp_mem_flow_message"."mem_flow_code" IS '流动记录code';
COMMENT ON COLUMN "public"."ccp_mem_flow_message"."direction" IS '消息走向 1-流出方发送 2-流入方发送';
COMMENT ON COLUMN "public"."ccp_mem_flow_message"."out_flow_code" IS '流出方组织code';
COMMENT ON COLUMN "public"."ccp_mem_flow_message"."out_flow_name" IS '流出方组织名称';
COMMENT ON COLUMN "public"."ccp_mem_flow_message"."into_flow_code" IS '流入方组织code';
COMMENT ON COLUMN "public"."ccp_mem_flow_message"."into_flow_name" IS '流入方组织名称';
COMMENT ON COLUMN "public"."ccp_mem_flow_message"."message" IS '消息';
COMMENT ON COLUMN "public"."ccp_mem_flow_message"."out_flow_node" IS '流出方根节点代码';
COMMENT ON COLUMN "public"."ccp_mem_flow_message"."into_flow_node" IS '流入方根节点代码';
COMMENT ON COLUMN "public"."ccp_mem_flow_message"."source_type" IS '1-本地创建消息  2-交换区拉取消息';
COMMENT ON COLUMN "public"."ccp_mem_flow_message"."send_time" IS '消息发送时间';
COMMENT ON COLUMN "public"."ccp_mem_flow_message"."send_name" IS '消息发送组织名称';
COMMENT ON COLUMN "public"."ccp_mem_flow_message"."send_code" IS '消息发送组织code';

-------删除d202数据--------
delete from dict_d202 where key = '3';

---------mem_flow表添加lddy_id字段--------------
alter table public.mem_flow add column IF NOT EXISTS lddy_id varchar(255) default null;
comment on column public.mem_flow.lddy_id is '流动提醒ID，中组部字段';
ALTER TABLE ccp_mem_flow_sign  ALTER COLUMN  remark TYPE TEXT;






