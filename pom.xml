<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <parent>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-starter-parent</artifactId>
        <version>2.3.1.RELEASE</version>
        <relativePath/>
    </parent>

    <groupId>com.zenith</groupId>
    <artifactId>zenith-front-dj</artifactId>
    <version>1.1.10.301</version>
    <packaging>pom</packaging>
    <name>zenith-front-dj</name>
    <description>贵州党建政务系统</description>

    <modules>
        <module>zenith-front-api</module>
        <module>zenith-front-service</module>
        <module>zenith-front-web</module>
        <module>zenith-front-dao</module>
        <module>zenith-front-model</module>
        <module>zenith-front-log</module>
        <module>zenith-front-starter-job</module>
        <module>zenith-front-starter-file</module>
        <module>zenith-front-common</module>
        <module>zenith-front-zunyi</module>
    </modules>

    <properties>
        <java.version>1.8</java.version>
        <maven.compiler.source>8</maven.compiler.source>
        <maven.compiler.target>8</maven.compiler.target>
        <maven.compiler.compilerVersion>1.8</maven.compiler.compilerVersion>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <project.reporting.outputEncoding>UTF-8</project.reporting.outputEncoding>
    </properties>

    <dependencyManagement>
        <dependencies>
            <dependency>
                <groupId>com.zenith</groupId>
                <artifactId>zenith-front-api</artifactId>
                <version>1.1.10.301</version>
            </dependency>
            <dependency>
                <groupId>com.zenith</groupId>
                <artifactId>zenith-front-service</artifactId>
                <version>1.1.10.301</version>
            </dependency>
            <dependency>
                <groupId>com.zenith</groupId>
                <artifactId>zenith-front-web</artifactId>
                <version>1.1.10.301</version>
            </dependency>
            <dependency>
                <groupId>com.zenith</groupId>
                <artifactId>zenith-front-dao</artifactId>
                <version>1.1.10.301</version>
            </dependency>
            <dependency>
                <groupId>com.zenith</groupId>
                <artifactId>zenith-front-model</artifactId>
                <version>1.1.10.301</version>
            </dependency>
            <dependency>
                <groupId>com.zenith</groupId>
                <artifactId>zenith-front-log</artifactId>
                <version>1.1.10.301</version>
            </dependency>
            <dependency>
                <groupId>com.zenith</groupId>
                <artifactId>zenith-front-starter-job</artifactId>
                <version>1.1.10.301</version>
            </dependency>
            <dependency>
                <groupId>com.zenith</groupId>
                <artifactId>zenith-front-starter-file</artifactId>
                <version>1.1.10.301</version>
            </dependency>
            <dependency>
                <groupId>com.zenith</groupId>
                <artifactId>zenith-front-zunyi</artifactId>
                <version>1.1.10.301</version>
            </dependency>
        </dependencies>
    </dependencyManagement>

    <dependencies>
        <dependency>
            <groupId>com.zenith</groupId>
            <artifactId>zenith-front-common</artifactId>
            <version>1.0.0-SNAPSHOT</version>
        </dependency>
    </dependencies>

    <profiles>
        <profile>
            <!-- 开发环境 -->
            <id>dev</id>
            <properties>
                <port>8089</port>
                <env>dev</env>
                <dev>true</dev>
                <datasource.url>
                    <![CDATA[**********************************************************************************************************************************]]></datasource.url>
                <datasource.station.url>
                    <![CDATA[********************************************************************************************************************************]]></datasource.station.url>
                <datasource.driver>org.postgresql.Driver</datasource.driver>
                <datasource.username>postgres</datasource.username>
                <datasource.password>20191809</datasource.password>
                <redis.url>redis://************:6388</redis.url>
                <es.ip>*************</es.ip>
                <es.port>9200</es.port>
                <es.username>elastic</es.username>
                <es.password>20191809</es.password>
            </properties>
            <!-- 是否默认 true表示默认-->
            <activation>
                <activeByDefault>true</activeByDefault>
            </activation>
        </profile>

        <profile>
            <!-- 测试环境 -->
            <id>test</id>
            <properties>
                <port>8080</port>
                <env>test</env>
                <dev>false</dev>
                <datasource.url>
                    <![CDATA[*********************************************************************************************************************]]></datasource.url>
                <datasource.station.url>
                    <![CDATA[********************************************************************************************************************************]]></datasource.station.url>
                <datasource.driver>org.postgresql.Driver</datasource.driver>
                <datasource.username>postgres</datasource.username>
                <datasource.password>20191809</datasource.password>
                <redis.url>redis://*************:6388</redis.url>
                <es.ip>*************</es.ip>
                <es.port>9200</es.port>
                <es.username>elastic</es.username>
                <es.password>20191809</es.password>
            </properties>
        </profile>

        <profile>
            <!-- 正式环境 -->
            <id>prod</id>
            <properties>
                <port>80</port>
                <env>prod</env>
                <dev>false</dev>
                <datasource.url>
                    <![CDATA[*********************************************************************************************************************]]></datasource.url>
                <datasource.station.url>
                    <![CDATA[********************************************************************************************************************************]]></datasource.station.url>
                <datasource.driver>org.postgresql.Driver</datasource.driver>
                <datasource.username>postgres</datasource.username>
                <datasource.password>20191809</datasource.password>
                <redis.url>redis://*************:6388</redis.url>
                <es.ip>*************</es.ip>
                <es.port>9200</es.port>
                <es.username>elastic</es.username>
                <es.password>20191809</es.password>
            </properties>
        </profile>
    </profiles>

    <build>
        <plugins>
            <!--动态统一修改pom版本号-->
            <plugin>
                <groupId>org.codehaus.mojo</groupId>
                <artifactId>versions-maven-plugin</artifactId>
                <configuration>
                    <!--不生成pom备份文件-->
                    <generateBackupPoms>false</generateBackupPoms>
                </configuration>
            </plugin>
        </plugins>
    </build>

</project>
