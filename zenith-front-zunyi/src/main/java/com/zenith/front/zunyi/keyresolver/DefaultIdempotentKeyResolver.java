//package com.zenith.front.zunyi.keyresolver;
//
//import cn.hutool.core.util.StrUtil;
//import cn.hutool.crypto.SecureUtil;
//import com.zenith.front.model.message.InMessage;
//import org.aspectj.lang.JoinPoint;
//
///**
// * 默认幂等 Key 解析器，使用方法名 + 方法参数，组装成一个 Key
// * <p>
// * 为了避免 Key 过长，使用 MD5 进行“压缩”
// *
// * <AUTHOR>
// */
//public class DefaultIdempotentKeyResolver implements IdempotentKeyResolver {
//
//    @Override
//    public String resolver(JoinPoint joinPoint, Idempotent idempotent) {
//        String methodName = joinPoint.getSignature().toString();
//        Object[] args = joinPoint.getArgs();
//        String argsStr = StrUtil.join(",", ((InMessage<?>) args[0]).getData());
//        return SecureUtil.md5(methodName + argsStr);
//    }
//
//}
