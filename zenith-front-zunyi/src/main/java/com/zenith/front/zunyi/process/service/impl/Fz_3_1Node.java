package com.zenith.front.zunyi.process.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateField;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.zenith.front.api.mem.IMemDevelopService;
import com.zenith.front.common.constant.ProcessNodeEnum;
import com.zenith.front.model.bean.MemDevelop;
import com.zenith.front.model.bean.MemDevelopProcess;
import com.zenith.front.model.bean.User;
import com.zenith.front.model.message.OutMessage;
import com.zenith.front.model.message.Status;
import com.zenith.front.zunyi.model.dto.FileDigitalDto;
import com.zenith.front.zunyi.process.service.ProcessEngineService;
import com.zenith.front.zunyi.service.IMemDevelopProcessService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 一月内进行预审
 *
 * <AUTHOR>
 * @since 2025/2/27
 */
@Service
@Slf4j
public class Fz_3_1Node implements ProcessEngineService {
    @Resource
    private Fz_1Node fz1Node;
    @Resource
    private IMemDevelopService iMemDevelopService;
    @Resource
    private IMemDevelopProcessService iMemDevelopProcessService;


    @Override
    public String getNode() {
        return ProcessNodeEnum.FZ_3_1.getNode();
    }

    @Override
    public OutMessage<ProcessNodeEnum> next(String currentNode, String businessId, Map<String, Object> paramMap) {
        Object filesList = paramMap.get("filesList");
        if (Objects.nonNull(filesList)) {
            List<FileDigitalDto> list = (List<FileDigitalDto>) filesList;
            Set<String> collect = list.stream().map(FileDigitalDto::getD222Code).collect(Collectors.toSet());
            if (!collect.contains("203")) {
                return new OutMessage<>(5001, "入党积极分子、发展对象培养教育考察登记表未上传", null);
            }
            if (!collect.contains("402")) {
                return new OutMessage<>(5001, "基层党委预审会议记录及预审批复（复印件）未上传", null);
            }
        }
        // 1、查询业务是否存在
        LambdaQueryWrapper<MemDevelop> wrapper = new LambdaQueryWrapper<MemDevelop>()
                .eq(MemDevelop::getCode, businessId)
                .eq(MemDevelop::getProcessNode, currentNode)
                .isNull(MemDevelop::getDeleteTime).last("limit 1");
        MemDevelop entity = iMemDevelopService.getOne(wrapper);
        if (Objects.isNull(entity) || StrUtil.isBlank(entity.getDigitalLotNo())) {
            log.error("业务信息不存在或档案批次唯一码为空，业务ID：{}", businessId);
            return new OutMessage<>(5001, "业务信息不存在或档案批次唯一码为空", null);
        }

        // 2、查询当前流程是否存在
        String lotNo = entity.getDigitalLotNo();
        LambdaQueryWrapper<MemDevelopProcess> processWrapper = new LambdaQueryWrapper<MemDevelopProcess>()
                .eq(MemDevelopProcess::getProcessNode, currentNode)
                .eq(MemDevelopProcess::getDigitalLotNo, lotNo)
                .isNull(MemDevelopProcess::getApproveTime).last("limit 1");
        MemDevelopProcess currentProcess = iMemDevelopProcessService.getOne(processWrapper);
        if (Objects.isNull(currentProcess)) {
            log.error("当前流程信息不存在，批次唯一码：{}", lotNo);
            return new OutMessage<>(5001, "当前流程信息不存在", null);
        }

        User user = currentUser();
        // 3、当前流程节点审核完成信息填写并保存
        currentProcess.setApproveUser(user.getAccount());
        currentProcess.setApproveTime(new Date());
        currentProcess.setUpdateUser(user.getAccount());
        currentProcess.setUpdateTime(new Date());
        iMemDevelopProcessService.updateById(currentProcess);
        // 4、生成下一步流程信息
        ProcessNodeEnum nextNode = ProcessNodeEnum.FZ_4;

        MemDevelopProcess nextProcess = fz1Node.saveProcessInfo(currentProcess, user, nextNode.getNode());
        if (Objects.isNull(nextProcess)) {
            log.error("流程流转失败，业务ID：{}", businessId);
            return new OutMessage<>(5003, "流程流转失败", null);
        }
        //  5、业务更新流程节点信息
//        entity.setProcessNode(nextProcess.getProcessNode());
//        iMemDevelopService.updateById(entity);
        LambdaUpdateWrapper<MemDevelop> updateWrapper = Wrappers.lambdaUpdate();
        updateWrapper.set(MemDevelop::getProcessNode, nextProcess.getProcessNode())
                .eq(MemDevelop::getCode, entity.getCode());
        iMemDevelopService.update(updateWrapper);
        return new OutMessage<>(Status.SUCCESS, nextNode);
    }


    /**
     * "FZ_3_2", "预审时间少于十天"
     * 定时计算判断流程节点
     */
    @Override
    public OutMessage<?> task() {
        // 1、查找时间到了的信息
        LambdaQueryWrapper<MemDevelopProcess> wrapper = new LambdaQueryWrapper<MemDevelopProcess>()
                .isNull(MemDevelopProcess::getApproveTime)
                .eq(MemDevelopProcess::getProcessNode, getNode())
                .isNotNull(MemDevelopProcess::getDigitalLotNo)
                .apply("extend_end_time <= current_date");
        List<MemDevelopProcess> list = iMemDevelopProcessService.list(wrapper);
        if (CollUtil.isEmpty(list)) {
            return null;
        }
        // 预审时间少于十天
        final ProcessNodeEnum nextNode = ProcessNodeEnum.FZ_3_2;

        // 统一生成下一步流程信息
        List<MemDevelopProcess> nextList = new ArrayList<>(list.size());
        list.forEach(process -> {
            process.setApproveUser("定时任务生成");
            process.setApproveTime(new Date());
            process.setUpdateUser("定时任务生成");
            process.setUpdateTime(new Date());
            nextList.add(getNextProcessInfo(process, null, nextNode.getNode()));
        });

        iMemDevelopProcessService.updateBatchById(list);
        iMemDevelopProcessService.saveBatch(nextList);
        // 业务更新流程节点信息
        updateMemDevelopProcessNode(getNode(), nextNode.getNode(), list.stream().map(MemDevelopProcess::getDigitalLotNo).collect(Collectors.toSet()));

        return new OutMessage<>(Status.SUCCESS);
    }

    /**
     * 获取下一个流程信息
     */
    public MemDevelopProcess getNextProcessInfo(MemDevelopProcess fProcess, User user, String processNode) {
        MemDevelopProcess process = new MemDevelopProcess();
        process.setCode(IdUtil.simpleUUID());
        process.setProcessNode(processNode);
        process.setPreviousCode(fProcess.getCode());
        process.setDigitalLotNo(fProcess.getDigitalLotNo());
        process.setd08Code(fProcess.getd08Code());
        process.setCreateTime(new Date());
        process.setCreateUser(Objects.nonNull(user) ? user.getAccount() : "定时任务生成");
        // 大步骤审批超时时间从进入该步骤第一个节点开始定时计算
        if (StrUtil.equalsAny(processNode,
                ProcessNodeEnum.FZ_3_2.getNode(),
                ProcessNodeEnum.FZ_5_2.getNode())) {
            // "FZ_3_2", "预审时间少于十天" -> "FZ_3_3", "一月内未进行预审"
            process.setExtendStarTime(fProcess.getExtendStarTime());
            process.setExtendEndTime(DateUtil.offset(process.getExtendStarTime(), DateField.MONTH, 1));
        }
        // "FZ_6_2", "审批时间少于十天" -> "FZ_6_3", "审批超时"
        if (StrUtil.equalsAny(processNode, ProcessNodeEnum.FZ_6_2.getNode())) {
            process.setExtendStarTime(fProcess.getExtendStarTime());
            process.setExtendEndTime(DateUtil.offset(process.getExtendStarTime(), DateField.MONTH, 3));
        }
        // "FZ_6_3", "审批超时"
        if (StrUtil.equalsAny(processNode, ProcessNodeEnum.FZ_6_3.getNode())) {
            process.setExtendStarTime(fProcess.getExtendStarTime());
            process.setExtendEndTime(fProcess.getExtendEndTime());
        }
        return process;
    }

    /**
     * 业务更新流程节点信息
     */
    public void updateMemDevelopProcessNode(String fProcess, String tProcess, Set<String> lotNoSet) {
        LambdaUpdateWrapper<MemDevelop> updateWrapper = new LambdaUpdateWrapper<MemDevelop>()
                .set(MemDevelop::getProcessNode, tProcess)
                .eq(MemDevelop::getD08Code, "3")
                .eq(MemDevelop::getProcessNode, fProcess)
                .in(MemDevelop::getDigitalLotNo, lotNoSet)
                .isNull(MemDevelop::getDeleteTime);
        iMemDevelopService.update(updateWrapper);
    }

}
