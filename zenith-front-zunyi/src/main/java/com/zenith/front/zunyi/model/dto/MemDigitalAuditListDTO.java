package com.zenith.front.zunyi.model.dto;

import lombok.Data;

import javax.validation.constraints.Max;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @create_date 2025-07-11 15:51
 * @description
 */
@Data
public class MemDigitalAuditListDTO {
    /**
     * 分页页数
     */
    @Min(value = 1,  message = "页码最小为1")
    private Integer pageNum;
    /**
     * 分页每页数
     */
    @Max(value = 100,  message = "每页条数大小范围在1-100")
    private Integer pageSize;
    /**
     * 是否显示下级 1 显示 0 不显示
     */
    private String subordinate;


    /**
     * 层级码
     */
    @NotBlank(message = "orgLevelCode 不能为空")
    private String orgLevelCode;

    /**
     * 名称
     */
    private String memName;

    /**
     * 类型：1-导出审核； 2-补充档案审核
     */
    @NotNull(message = "type 不能为空")
    private Integer type;
}
