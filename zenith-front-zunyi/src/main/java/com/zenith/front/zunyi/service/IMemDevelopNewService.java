package com.zenith.front.zunyi.service;

import com.zenith.front.model.bean.User;
import com.zenith.front.model.dto.ImportExcelDevelopDTO;
import com.zenith.front.model.message.OutMessage;
import com.zenith.front.zunyi.model.dto.DevelopStepLogNewDTO;
import com.zenith.front.zunyi.model.dto.MemDevelopNewDTO;
import com.zenith.front.zunyi.model.dto.MemListNewDTO;
import com.zenith.front.zunyi.model.dto.UploadFileDigitalDto;
import com.zenith.front.zunyi.model.vo.MemDevelopNewVo;

/**
 * <AUTHOR>
 * @since 2025/2/16
 */
public interface IMemDevelopNewService {

    /**
     * 获取发展党员列表
     */
    OutMessage<?> getList(MemListNewDTO memListDTO);

    /**
     * 发展党员每个流程节点人数
     */
    OutMessage<?> count(MemListNewDTO memListDTO);

    /**
     * 新增发展党员
     */
    OutMessage<?> addDevelop(MemDevelopNewDTO memDevelopNewDTO) throws Exception;

    /**
     * 上传数字档案
     */
    OutMessage<?> uploadFileDigital(UploadFileDigitalDto digitalDto, User user);

    /**
     * 入党申请人确定为积极分子
     *
     * @param stepLogDTO 请求参数
     * @return 返回结果
     */
    OutMessage<?> becomeActivist(DevelopStepLogNewDTO stepLogDTO, User user);

    /**
     * 积极分子确定为发展对象
     *
     * @param stepLogDTO 请求参数
     * @return 返回结果
     */
    OutMessage<?> becomeDevelopObject(DevelopStepLogNewDTO stepLogDTO, User user);

    /**
     * 发展对象确定为预备党员
     *
     * @param stepLogDTO
     * @return
     */
    OutMessage becomePreliminary(DevelopStepLogNewDTO stepLogDTO, boolean hasSpecialDevelopment) throws Exception;

    /**
     * 撤销资格
     *
     * @param stepLogDTO
     * @return
     */
    OutMessage backOutStatus(DevelopStepLogNewDTO stepLogDTO) throws Exception;

    /**
     * 通过code获取发展党员
     *
     * @param code
     * @return
     */
    OutMessage<MemDevelopNewVo> findByCodeOut(String code);

    boolean processFileDigital(DevelopStepLogNewDTO stepLogDTO, User user);

    OutMessage ImportExcelDevelop(ImportExcelDevelopDTO data, String currManOrgCode) throws Exception;

    /**
     * 修改发展党员
     *
     * @param developDTO
     * @return
     */
    OutMessage updateDevelop(MemDevelopNewDTO developDTO) throws Exception;
}
