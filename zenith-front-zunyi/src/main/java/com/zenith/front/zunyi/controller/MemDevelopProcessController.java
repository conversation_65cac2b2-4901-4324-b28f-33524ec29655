package com.zenith.front.zunyi.controller;

import com.zenith.front.common.annotation.RequiresPermissions;
import com.zenith.front.common.constant.OperateConstant;
import com.zenith.front.common.constant.ProcessNodeEnum;
import com.zenith.front.model.bean.User;
import com.zenith.front.model.constant.UserConstant;
import com.zenith.front.model.message.InMessage;
import com.zenith.front.model.message.OutMessage;
import com.zenith.front.model.message.Status;
import com.zenith.front.model.validate.annotation.Validate;
import com.zenith.front.model.validate.group.Common1Group;
import com.zenith.front.model.validate.group.Common2Group;
import com.zenith.front.zunyi.model.dto.AuditMemDto;
import com.zenith.front.zunyi.model.dto.DevelopExtendApprovalDto;
import com.zenith.front.zunyi.model.dto.MemListNewDTO;
import com.zenith.front.zunyi.scheduled.impl.FzTask;
import com.zenith.front.zunyi.scheduled.impl.JjTask;
import com.zenith.front.zunyi.scheduled.impl.RdTask;
import com.zenith.front.zunyi.scheduled.impl.YbqTask;
import com.zenith.front.zunyi.service.IMemDevelopProcessService;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

/**
 * 流程管理
 *
 * <AUTHOR>
 * @since 2025/3/5
 */
@RestController
@RequestMapping("/zunyi/process")
public class MemDevelopProcessController {

    @Resource
    private IMemDevelopProcessService iMemDevelopProcessService;
    @Resource
    FzTask fzTask;
    @Resource
    JjTask jjTask;
    @Resource
    RdTask rdTask;
    @Resource
    YbqTask ybqTask;

    /**
     * 获取预备党员转正审核列表
     */
    @PostMapping("/getMemAuditList")
    @Validate
    @RequiresPermissions
    public OutMessage<?> getMemAuditList(@RequestBody InMessage<MemListNewDTO> inMessage) {
        User user = UserConstant.USER_CONTEXT.get().getUser();
        return iMemDevelopProcessService.getMemAuditList(inMessage.getData(), user);
    }

    /**
     * 获取预备党员审核列表
     */
    @PostMapping("/getMemDevelopAuditList")
    @Validate
    @RequiresPermissions
    public OutMessage<?> getMemDevelopAuditList(@RequestBody InMessage<MemListNewDTO> inMessage) {
        User user = UserConstant.USER_CONTEXT.get().getUser();
        return iMemDevelopProcessService.getMemDevelopAuditList(inMessage.getData(), user);
    }

    /**
     * 审核预备党员
     */
    @PostMapping("/auditMemDevelop")
    @Validate(group = Common1Group.class)
    @RequiresPermissions
    public OutMessage<?> auditMemDevelop(@RequestBody InMessage<AuditMemDto> inMessage) {
        User user = UserConstant.USER_CONTEXT.get().getUser();
        return iMemDevelopProcessService.auditMemDevelop(inMessage.getData(), user);
    }

    /**
     * 预备党员转正审核
     */
    @PostMapping("/auditMem")
    @Validate(group = Common1Group.class)
    @RequiresPermissions
    public OutMessage<?> auditMem(@RequestBody InMessage<AuditMemDto> inMessage) throws Exception {
        User user = UserConstant.USER_CONTEXT.get().getUser();
        return iMemDevelopProcessService.auditMem(inMessage.getData(), user);
    }


    /**
     * 获取发展对象流程信息
     */
    @PostMapping("/findDevelopProcess")
    @RequiresPermissions
    public OutMessage<?> findDevelopProcess(@RequestBody InMessage<DevelopExtendApprovalDto> inMessage) {
        User user = UserConstant.USER_CONTEXT.get().getUser();
        return iMemDevelopProcessService.findDevelopProcess(inMessage.getData(), user);
    }

    /**
     * 获取县委审核流程记录
     */
    @PostMapping("/findAuditProcess")
    @RequiresPermissions
    public OutMessage<?> findAuditProcess(@RequestBody InMessage<DevelopExtendApprovalDto> inMessage) {
        User user = UserConstant.USER_CONTEXT.get().getUser();
        return iMemDevelopProcessService.findAuditProcess(inMessage.getData(), user);
    }

    /**
     * 发展对象延长审批
     */
    @PostMapping("/developExtendApproval")
    @Validate(group = Common1Group.class)
    @RequiresPermissions(opt = OperateConstant.UPDATE)
    public OutMessage<?> developExtendApproval(@RequestBody InMessage<DevelopExtendApprovalDto> inMessage) {
        DevelopExtendApprovalDto approvalDto = inMessage.getData();
        User user = UserConstant.USER_CONTEXT.get().getUser();
        return iMemDevelopProcessService.developExtendApproval(approvalDto, user);
    }


    /**
     * 发展对象审批超时解除锁定
     */
    @PostMapping("/unlockExtendApproval")
    @Validate(group = Common2Group.class)
    @RequiresPermissions(opt = OperateConstant.UPDATE)
    public OutMessage<?> unlockExtendApproval(@RequestBody InMessage<DevelopExtendApprovalDto> inMessage) {
        DevelopExtendApprovalDto approvalDto = inMessage.getData();
        User user = UserConstant.USER_CONTEXT.get().getUser();
        return iMemDevelopProcessService.unlockExtendApproval(approvalDto, user);
    }

    /**
     * 获取退回跳转指定流程集合
     *
     * @param cancelCode 1-入党申请人，2-积极分子
     * @return
     */
    @GetMapping("/backProcessNodeList")
    @RequiresPermissions
    public OutMessage<?> backProcessNodeList(String cancelCode) {
        return new OutMessage<>(Status.SUCCESS, ProcessNodeEnum.backProcessNodeList(cancelCode));
    }

    /**
     * 后台调用流程任务，初上线调试使用
     */
    @GetMapping("/startTask")
    public void startTask() {
        System.out.println("后台调用流程任务......");
        fzTask.task();
        jjTask.task();
        rdTask.task();
        ybqTask.task();
        System.out.println("调用流程任务结束......");
    }
}
