package com.zenith.front.zunyi.process.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.zenith.front.api.mem.IMemDevelopService;
import com.zenith.front.common.constant.ProcessNodeEnum;
import com.zenith.front.common.kit.StrKit;
import com.zenith.front.model.bean.MemDevelop;
import com.zenith.front.model.message.OutMessage;
import com.zenith.front.model.message.Status;
import com.zenith.front.model.bean.MemDevelopProcess;
import com.zenith.front.zunyi.process.service.ProcessEngineService;
import com.zenith.front.zunyi.service.IMemDevelopProcessService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2025-03-13 10:52
 */
@Slf4j
@Service
public class Rd_3Node implements ProcessEngineService {

    @Resource
    private IMemDevelopService memDevelopService;

    @Resource
    private IMemDevelopProcessService memDevelopProcessService;

    @Override
    public String getNode() {
        return ProcessNodeEnum.RD_3.getNode();
    }

    @Override
    public OutMessage<ProcessNodeEnum> next(String currentNode, String businessId, Map<String, Object> paramMap) {
        return null;
    }

    @Override
    public OutMessage<?> task() {

        // 1、查找时间到了的信息
        LambdaQueryWrapper<MemDevelopProcess> wrapper = Wrappers.lambdaQuery();
        wrapper.isNull(MemDevelopProcess::getApproveTime)
                .eq(MemDevelopProcess::getProcessNode, getNode())
                // 当前时间大于等于需要执行时间，则计算
                .apply(" to_char(extend_end_time, 'yyyy-MM-dd') <= to_char(now(), 'yyyy-MM-dd') ");
        List<MemDevelopProcess> list = memDevelopProcessService.list(wrapper);
        if (CollUtil.isEmpty(list)) {
            return null;
        }

        // 获取关联党员信息
        final Set<String> lotNoSet = list.stream().map(MemDevelopProcess::getDigitalLotNo).collect(Collectors.toSet());
        LambdaQueryWrapper<MemDevelop> memLambdaQueryWrapper = Wrappers.lambdaQuery();
        memLambdaQueryWrapper.select(MemDevelop::getId, MemDevelop::getCode, MemDevelop::getDigitalLotNo, MemDevelop::getD08Code);
        memLambdaQueryWrapper.in(MemDevelop::getDigitalLotNo, lotNoSet)
                .eq(MemDevelop::getProcessNode, getNode())
                .isNull(MemDevelop::getDeleteTime);
        List<MemDevelop> memList = memDevelopService.list(memLambdaQueryWrapper);
        if (CollUtil.isEmpty(memList)) {
            return null;
        }
        Map<String, MemDevelop> memMap = memList.stream().collect(Collectors.toMap(MemDevelop::getDigitalLotNo, e -> e, (e1, e2) -> e1));

        for (MemDevelopProcess memDevelopProcess : list) {
            memDevelopProcess.setApproveUser("定时任务");
            memDevelopProcess.setApproveTime(new Date());
        }

        ProcessNodeEnum nextNode = ProcessNodeEnum.RD_4;
        // 生成下一步流程信息
        List<MemDevelopProcess> saveList = list.stream().filter(e -> memMap.containsKey(e.getDigitalLotNo()))
                .map(processCurrent -> {
                    // 记录下一节点流程信息
                    MemDevelopProcess processNext = new MemDevelopProcess();
                    processNext.setCode(StrKit.getRandomUUID());
                    processNext.setProcessNode(nextNode.getNode());
                    processNext.setDigitalLotNo(processCurrent.getDigitalLotNo());
                    processNext.setd08Code(processCurrent.getd08Code());
                    processNext.setExtendStarTime(processCurrent.getExtendStarTime());
                    processNext.setExtendEndTime(processCurrent.getExtendEndTime());
                    processNext.setPreviousCode(processCurrent.getCode());
                    processNext.setCreateUser("定时任务");
                    processNext.setCreateTime(DateUtil.date());
                    return processNext;
                }).collect(Collectors.toList());
        boolean flag1 = memDevelopProcessService.saveBatch(saveList);
        boolean flag2 = memDevelopProcessService.updateBatchById(list);

        if (flag1 && flag2) {
            // 更新MemDevelop表当前节点
            LambdaUpdateWrapper<MemDevelop> updateWrapper = Wrappers.lambdaUpdate();
            updateWrapper.set(MemDevelop::getProcessNode, nextNode.getNode())
                    .in(MemDevelop::getDigitalLotNo, lotNoSet)
                    .eq(MemDevelop::getProcessNode, getNode())
                    .isNull(MemDevelop::getDeleteTime);
            memDevelopService.update(updateWrapper);
        }

        log.info("完成【RD_3(已谈话) ---> RD_4(满足积极份子)】定时任务.");
        return new OutMessage<>(Status.SUCCESS);
    }
}
