package com.zenith.front.zunyi.scheduled.impl;

import com.zenith.front.common.constant.UserConstant;
import com.zenith.front.zunyi.process.ProcessTool;
import com.zenith.front.zunyi.process.service.impl.Rd_2_1Node;
import com.zenith.front.zunyi.process.service.impl.Rd_2_2Node;
import com.zenith.front.zunyi.process.service.impl.Rd_3Node;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Async;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @since 2025-03-13 10:50
 */
@Slf4j
@Service
public class RdTask {
    @Resource
    private Rd_2_1Node rd_2_1Node;
    @Resource
    private Rd_2_2Node rd_2_2Node;
    @Resource
    private Rd_3Node rd_3Node;

    @Async
    @Scheduled(cron = ProcessTool.cron)
    public void task() {
        // 非遵义的节点不用执行流程任务
        if (!UserConstant.HAS_ZUN_YI) {
            return;
        }
        log.info("入党申请人定时任务执行.........");

        // RD_2_1(一月内需要谈话) ---> RD_2_2(谈话时间少于10天)
        this.rd_2_1Node.task();
        log.info("【谈话时间少于10天】执行结束.........");

        // RD_2_2(谈话时间少于10天) ---> RD_2_3(一月内未进行谈话)
        this.rd_2_2Node.task();
        log.info("【一月内未进行谈话】执行结束.........");

        // RD_3(已谈话) ---> RD_4(满足积极份子)
        this.rd_3Node.task();
        log.info("【满足积极份子】执行结束.........");

        log.info("入党申请人定时任务执行结束.........");
    }
}
