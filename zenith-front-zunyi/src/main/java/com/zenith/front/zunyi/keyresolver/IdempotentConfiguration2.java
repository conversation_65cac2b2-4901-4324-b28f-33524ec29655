//package com.zenith.front.zunyi.keyresolver;
//
//import cn.hutool.core.util.StrUtil;
//import org.redisson.config.Config;
//import org.redisson.config.SingleServerConfig;
//import org.springframework.context.annotation.Bean;
//import org.springframework.context.annotation.Configuration;
//import org.springframework.core.io.ClassPathResource;
//import org.springframework.core.io.Resource;
//import org.springframework.data.redis.connection.RedisConnectionFactory;
//import org.springframework.data.redis.connection.RedisStandaloneConfiguration;
//import org.springframework.data.redis.connection.lettuce.LettuceConnectionFactory;
//import org.springframework.data.redis.core.StringRedisTemplate;
//
//import java.io.IOException;
//import java.io.InputStream;
//import java.lang.reflect.Field;
//import java.util.List;
//
///**
// * <AUTHOR>
// */
//@Configuration(proxyBeanMethods = false)
//public class IdempotentConfiguration2 {
//
//    @Bean
//    public IdempotentAspect idempotentAspect(List<IdempotentKeyResolver> keyResolvers, IdempotentRedisDAO idempotentRedisDAO) {
//        return new IdempotentAspect(keyResolvers, idempotentRedisDAO);
//    }
//
//    @Bean
//    public IdempotentRedisDAO idempotentRedisDAO(StringRedisTemplate stringRedisTemplate) {
//        return new IdempotentRedisDAO(stringRedisTemplate);
//    }
//
//    // ========== 各种 IdempotentKeyResolver Bean ==========
//
//    @Bean
//    public DefaultIdempotentKeyResolver defaultIdempotentKeyResolver() {
//        return new DefaultIdempotentKeyResolver();
//    }
//
//
//    @Bean
//    public StringRedisTemplate stringRedisTemplate(RedisConnectionFactory redisConnectionFactory) {
//        return new StringRedisTemplate(redisConnectionFactory);
//    }
//
//
//    @Bean
//    public RedisConnectionFactory redisConnectionFactory() {
//        RedisStandaloneConfiguration config = new RedisStandaloneConfiguration();
//        Resource resource = new ClassPathResource("redisson.yml");
//        if (!resource.exists()) {
//            return null;
//        }
//        try {
//            InputStream inputStream = resource.getInputStream();
//            Object object = getFieldValue(Config.fromYAML(inputStream), "singleServerConfig");
//            if (object != null) {
//                SingleServerConfig serverConfig = (SingleServerConfig) object;
//                config.setPassword(serverConfig.getPassword());
//                String address = serverConfig.getAddress();
//                if (StrUtil.isNotBlank(address)) {
//                    String[] split = StrUtil.trim(address).split(":");
//                    config.setHostName(split[1].replace("//", ""));
//                    config.setPort(Integer.parseInt(split[2]));
//                }
//            }
//        } catch (IOException e) {
//            return null;
//        }
//        return new LettuceConnectionFactory(config);
//    }
//
//    private static Object getFieldValue(Object obj, String fieldName) {
//        try {
//            // 获取字段并访问其值
//            Field field = obj.getClass().getDeclaredField(fieldName);
//            field.setAccessible(true);
//            return field.get(obj);
//        } catch (Exception e) {
//            return null;
//        }
//    }
//}
