package com.zenith.front.zunyi.process.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.zenith.front.api.mem.IMemDevelopService;
import com.zenith.front.common.constant.ProcessNodeEnum;
import com.zenith.front.common.kit.StrKit;
import com.zenith.front.model.bean.MemDevelop;
import com.zenith.front.model.message.OutMessage;
import com.zenith.front.model.message.Status;
import com.zenith.front.model.bean.MemDevelopProcess;
import com.zenith.front.zunyi.process.service.ProcessEngineService;
import com.zenith.front.zunyi.service.IMemDevelopProcessService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2025-02-27 10:12
 */
@Slf4j
@Service
public class Rd_2_2Node implements ProcessEngineService {

    @Resource
    private IMemDevelopService memDevelopService;

    @Resource
    private IMemDevelopProcessService memDevelopProcessService;

    @Override
    public String getNode() {
        return ProcessNodeEnum.RD_2_2.getNode();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public OutMessage<ProcessNodeEnum> next(String currentNode, String businessId, Map<String, Object> paramMap) {
        // 获取考察党员基本信息
        LambdaQueryWrapper<MemDevelop> memDevelopLambdaQueryWrapper = Wrappers.lambdaQuery();
        memDevelopLambdaQueryWrapper.eq(MemDevelop::getCode, businessId);
        memDevelopLambdaQueryWrapper.eq(MemDevelop::getProcessNode, getNode());
        memDevelopLambdaQueryWrapper.isNull(MemDevelop::getDeleteTime);
        memDevelopLambdaQueryWrapper.last("limit 1");
        MemDevelop memDevelop = this.memDevelopService.getOne(memDevelopLambdaQueryWrapper);
        if (Objects.isNull(memDevelop) || StrUtil.isBlank(memDevelop.getDigitalLotNo())) {
            log.error("业务信息不存在或档案批次唯一码不存在，业务ID：{}", businessId);
            return new OutMessage<>(5001, "业务信息不存在或档案批次唯一码不存在", null);
        }
        String digitalLotNo = memDevelop.getDigitalLotNo();
        // 当前节点流程记录
        LambdaQueryWrapper<MemDevelopProcess> processCurrentWrapper = Wrappers.lambdaQuery();
        processCurrentWrapper.eq(MemDevelopProcess::getProcessNode, getNode());
        processCurrentWrapper.eq(MemDevelopProcess::getDigitalLotNo, digitalLotNo);
        processCurrentWrapper.isNull(MemDevelopProcess::getApproveTime);
        processCurrentWrapper.isNull(MemDevelopProcess::getApproveUser);
        processCurrentWrapper.last("limit 1");
        MemDevelopProcess processCurrent = this.memDevelopProcessService.getOne(processCurrentWrapper);
        if (ObjectUtil.isNull(processCurrent)) {
            log.error("当前节点流程不存在，业务ID：{}", businessId);
            return new OutMessage<>(5001, "当前节点流程不存在", null);
        }
        // 下一步流程信息
        ProcessNodeEnum nextNode = ProcessNodeEnum.RD_3;
        MemDevelopProcess processNext = new MemDevelopProcess();
        processNext.setCode(StrKit.getRandomUUID());
        processNext.setProcessNode(nextNode.getNode());
        processNext.setDigitalLotNo(processCurrent.getDigitalLotNo());
        processNext.setd08Code(processCurrent.getd08Code());
        // 取值入党申请时间满足3个月
        processNext.setExtendStarTime(memDevelop.getApplyDate());
        processNext.setExtendEndTime(DateUtil.offsetMonth(processNext.getExtendStarTime(), 3));
        processNext.setPreviousCode(processCurrent.getCode());
        processNext.setCreateUser(currentUser().getAccount());
        processNext.setCreateTime(DateUtil.date());
        // 修改当前节点流程信息
        processCurrent.setApproveUser(currentUser().getAccount());
        processCurrent.setApproveTime(DateUtil.date());
        processCurrent.setUpdateUser(currentUser().getAccount());
        processCurrent.setUpdateTime(DateUtil.date());
        boolean updateProcessCurrent = this.memDevelopProcessService.updateById(processCurrent);
        if (!updateProcessCurrent) {
            log.error("当前节点流程记录失败，业务ID：{}", businessId);
            return new OutMessage<>(5001, "当前节点流程记录失败", null);
        }
        // 记录下一节点流程信息
        boolean insertProcessNext = this.memDevelopProcessService.save(processNext);
        if (!insertProcessNext) {
            log.error("下一节点流程开启失败，业务ID：{}", businessId);
            return new OutMessage<>(5001, "下一节点流程开启失败", null);
        }
        // 修改下一流程节点
//        memDevelop.setProcessNode(processNext.getProcessNode());
//        boolean updateDevelop = this.memDevelopService.updateById(memDevelop);
        LambdaUpdateWrapper<MemDevelop> updateWrapper = Wrappers.lambdaUpdate();
        updateWrapper.set(MemDevelop::getProcessNode, processNext.getProcessNode())
                .eq(MemDevelop::getCode, memDevelop.getCode());
        boolean updateDevelop = memDevelopService.update(updateWrapper);
        if (!updateDevelop) {
            log.error("记录下一流程节点失败，业务ID：{}", businessId);
            return new OutMessage<>(5001, "记录下一流程节点失败", null);
        }
        return new OutMessage<>(Status.SUCCESS, nextNode);
    }

    @Override
    public OutMessage<?> task() {
        // 1、查找时间到了的信息
        LambdaQueryWrapper<MemDevelopProcess> wrapper = Wrappers.lambdaQuery();
        wrapper.isNull(MemDevelopProcess::getApproveTime)
                .eq(MemDevelopProcess::getProcessNode, getNode())
                // 当前时间大于等于需要执行时间，则计算
                .apply(" to_char(extend_end_time, 'yyyy-MM-dd') <= to_char(now(), 'yyyy-MM-dd') ");
        List<MemDevelopProcess> list = memDevelopProcessService.list(wrapper);
        if (CollUtil.isEmpty(list)) {
            return null;
        }

        // 获取关联党员信息
        final Set<String> lotNoSet = list.stream().map(MemDevelopProcess::getDigitalLotNo).collect(Collectors.toSet());
        LambdaQueryWrapper<MemDevelop> memLambdaQueryWrapper = Wrappers.lambdaQuery();
        memLambdaQueryWrapper.select(MemDevelop::getId, MemDevelop::getCode, MemDevelop::getDigitalLotNo, MemDevelop::getD08Code);
        memLambdaQueryWrapper.in(MemDevelop::getDigitalLotNo, lotNoSet)
                .eq(MemDevelop::getProcessNode, getNode())
                .isNull(MemDevelop::getDeleteTime);
        List<MemDevelop> memList = memDevelopService.list(memLambdaQueryWrapper);
        if (CollUtil.isEmpty(memList)) {
            return null;
        }
        Map<String, MemDevelop> memMap = memList.stream().collect(Collectors.toMap(MemDevelop::getDigitalLotNo, e -> e, (e1, e2) -> e1));

        for (MemDevelopProcess memDevelopProcess : list) {
            memDevelopProcess.setApproveUser("定时任务");
            memDevelopProcess.setApproveTime(new Date());
        }

        ProcessNodeEnum nextNode = ProcessNodeEnum.RD_2_3;

        // 生成下一步流程信息
        List<MemDevelopProcess> saveList = list.stream().filter(e -> memMap.containsKey(e.getDigitalLotNo()))
                .map(processCurrent -> {
                    // 记录下一节点流程信息
                    MemDevelopProcess processNext = new MemDevelopProcess();
                    processNext.setCode(StrKit.getRandomUUID());
                    processNext.setProcessNode(nextNode.getNode());
                    processNext.setDigitalLotNo(processCurrent.getDigitalLotNo());
                    processNext.setd08Code(processCurrent.getd08Code());
                    processNext.setExtendStarTime(processCurrent.getExtendStarTime());
                    processNext.setExtendEndTime(processCurrent.getExtendEndTime());
                    processNext.setPreviousCode(processCurrent.getCode());
                    processNext.setCreateUser("定时任务");
                    processNext.setCreateTime(DateUtil.date());
                    return processNext;
                }).collect(Collectors.toList());

        boolean flag1 = memDevelopProcessService.saveBatch(saveList);
        boolean flag2 = memDevelopProcessService.updateBatchById(list);

        if (flag1 && flag2) {
            // 更新MemDevelop表当前节点
            LambdaUpdateWrapper<MemDevelop> updateWrapper = Wrappers.lambdaUpdate();
            updateWrapper.set(MemDevelop::getProcessNode, nextNode.getNode())
                    .in(MemDevelop::getDigitalLotNo, lotNoSet)
                    .eq(MemDevelop::getProcessNode, getNode())
                    .isNull(MemDevelop::getDeleteTime);
            memDevelopService.update(updateWrapper);
        }
        log.info("完成【RD_2_2(谈话时间少于10天) ---> RD_2_3(一月内未进行谈话)】定时任务.");
        return new OutMessage<>(Status.SUCCESS);
    }
}
