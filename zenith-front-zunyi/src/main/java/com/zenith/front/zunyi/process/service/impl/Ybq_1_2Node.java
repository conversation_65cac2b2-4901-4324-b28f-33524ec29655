package com.zenith.front.zunyi.process.service.impl;

import cn.hutool.core.date.DateField;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.zenith.front.common.constant.ProcessNodeEnum;
import com.zenith.front.dao.mapper.mem.MemMapper;
import com.zenith.front.model.bean.Mem;
import com.zenith.front.model.message.OutMessage;
import com.zenith.front.model.message.Status;
import com.zenith.front.dao.mapper.mem.MemDevelopProcessMapper;
import com.zenith.front.model.bean.MemDevelopProcess;
import com.zenith.front.zunyi.process.ProcessTool;
import com.zenith.front.zunyi.process.service.ProcessEngineService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.Map;
import java.util.Objects;

/**
 * 待第一次考察
 *
 * <AUTHOR>
 * @create_date 2025-02-24 14:38
 * @description
 */
@Service
@Slf4j
public class Ybq_1_2Node implements ProcessEngineService {

    @Resource
    private MemDevelopProcessMapper mapper;
    @Resource
    private MemMapper memMapper;

    /**
     * 当前流程节点名
     *
     * @return
     */
    @Override
    public String getNode() {
        return ProcessNodeEnum.YBQ_1_2.getNode();
    }

    /**
     * 下一步流程
     *
     * @param currentNode 当前节点
     * @param businessId  业务ID
     * @param paramMap    参数
     * @return
     */
    @Override
    public OutMessage<ProcessNodeEnum> next(String currentNode, String businessId, Map<String, Object> paramMap) {
        // 1、查询业务是否存在
        LambdaQueryWrapper<Mem> wrapper = Wrappers.lambdaQuery();
        wrapper.select(Mem::getId, Mem::getCode, Mem::getDigitalLotNo, Mem::getExtendPreparDate);
        wrapper.eq(Mem::getCode, businessId)
                .eq(Mem::getProcessNode, currentNode)
                // 预备党员
                .eq(Mem::getD08Code, "2")
                .isNull(Mem::getDeleteTime);
        Mem entity = memMapper.selectOne(wrapper);
        if (Objects.isNull(entity) || StrUtil.isBlank(entity.getDigitalLotNo())) {
            log.error("业务信息不存在或档案批次唯一码不存在，业务ID：{}", businessId);
            return new OutMessage<>(5001, "业务信息不存在或档案批次唯一码不存在", null);
        }
        // 2、查询当前流程并且关键信息是否存在
        final String lotNo = entity.getDigitalLotNo();
        LambdaQueryWrapper<MemDevelopProcess> processLambdaQueryWrapper = Wrappers.lambdaQuery();
        processLambdaQueryWrapper.eq(MemDevelopProcess::getDigitalLotNo, lotNo)
                .eq(MemDevelopProcess::getProcessNode, currentNode)
                .isNull(MemDevelopProcess::getApproveTime);
        MemDevelopProcess currentProcess = mapper.selectOne(processLambdaQueryWrapper);
        if (Objects.isNull(currentProcess) || StrUtil.isBlank(currentProcess.getCode()) || Objects.isNull(currentProcess.getExtendStarTime())) {
            log.error("当前流程信息不存在或扩展时间为空，批次唯一码：{}", lotNo);
            return new OutMessage<>(5001, "当前流程信息不存在或扩展时间为空", null);
        }
        // 3、当前流程节点审核完成信息填写并保存
        currentProcess.setApproveUser(currentUser().getAccount());
        currentProcess.setApproveTime(new Date());
        mapper.updateById(currentProcess);

        // 4、计算下一步节点并处理关键信息、生成下一步流程信息
        ProcessNodeEnum nextNode;
        // 预备期到的时间(或延长到的时间)
        final Date extendPreparDate = entity.getExtendPreparDate();
        // 预备转正结束时间前一周（7天）
        Date extendEndTime = DateUtil.offsetDay(extendPreparDate, -7);
        // 通过成为预备党员时间计算半年
        Date moth6Dat = ProcessTool.computeDate(Objects.nonNull(entity.getJoinOrgDate()) ? entity.getJoinOrgDate() : currentProcess.getExtendEndTime(), DateField.MONTH, 6);
        // 扩展结束时间比当前时间小，表示预备期已超过1年,直接进入“满足转正申请”，否则进入“待考察人员”
        // 延长预备期对满足转正也没有影响，满足转正就是的条件是预备期满前一周（7天）的时间节点（预备期都是一年，除非延长，如果延长也是最终的时间减7天）
        if (DateUtil.compare(extendEndTime, new Date(), "yyyy-MM-dd") <= 0) {
            nextNode = ProcessNodeEnum.YBQ_1_3;
        } else {
            nextNode = ProcessNodeEnum.YBQ_1_1;
            // 预备党员转正日期前一周时间早于上次结束时间上在加半年时间，表示这是触发到满足转正时间，例如：现在是转正时间前2周，在过一周就触发到“满足转正”，而不是加上半年触发
            extendEndTime = DateUtil.compare(extendEndTime, moth6Dat, "yyyy-MM-dd") <= 0 ? extendEndTime : moth6Dat;
        }

        final MemDevelopProcess nextProcess = new MemDevelopProcess();
        nextProcess.setCode(IdUtil.simpleUUID());
        nextProcess.setDigitalLotNo(lotNo);
        nextProcess.setd08Code(currentProcess.getd08Code());
        nextProcess.setCreateTime(new Date());
        nextProcess.setCreateUser(currentUser().getAccount());
        nextProcess.setPreviousCode(currentProcess.getCode());
        nextProcess.setProcessNode(nextNode.getNode());
        nextProcess.setExtendStarTime(currentProcess.getExtendStarTime());
        nextProcess.setExtendEndTime(extendEndTime);
        if (mapper.insert(nextProcess) == 0) {
            log.error("流程流转失败，业务ID：{}", businessId);
            return new OutMessage<>(5001, "流程流转失败", null);
        }

        //  5、业务保存关键信息
        LambdaUpdateWrapper<Mem> updateWrapper = Wrappers.lambdaUpdate();
        updateWrapper.set(Mem::getProcessNode, nextProcess.getProcessNode())
                .eq(Mem::getCode, entity.getCode());
        memMapper.update(null, updateWrapper);
        return new OutMessage<>(Status.SUCCESS, nextNode);
    }
}
