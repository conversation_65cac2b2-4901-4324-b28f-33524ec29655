package com.zenith.front.zunyi.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateField;
import cn.hutool.core.date.DateUnit;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.zenith.front.api.mem.IMemDevelopService;
import com.zenith.front.api.mem.IMemService;
import com.zenith.front.common.constant.CommonConstant;
import com.zenith.front.common.constant.ProcessNodeEnum;
import com.zenith.front.common.constant.UserConstant;
import com.zenith.front.common.encrypt.properties.EncryptProperties;
import com.zenith.front.common.untils.SM4Untils;
import com.zenith.front.core.cache.CacheUtils;
import com.zenith.front.core.service.transfer.TransferRecordServiceImpl;
import com.zenith.front.dao.mapper.mem.MemDevelopProcessMapper;
import com.zenith.front.dao.mapper.mem.MemDigitalCountMapper;
import com.zenith.front.dao.mapper.org.OrgMapper;
import com.zenith.front.model.bean.Mem;
import com.zenith.front.model.bean.MemDevelop;
import com.zenith.front.model.bean.MemDevelopProcess;
import com.zenith.front.model.bean.User;
import com.zenith.front.model.dto.DevelopStepLogDTO;
import com.zenith.front.model.message.OutMessage;
import com.zenith.front.model.message.Status;
import com.zenith.front.model.vo.MemAuditListVo;
import com.zenith.front.zunyi.model.dto.AuditMemDto;
import com.zenith.front.zunyi.model.dto.DevelopExtendApprovalDto;
import com.zenith.front.zunyi.model.dto.MemListNewDTO;
import com.zenith.front.zunyi.model.vo.DevelopExtendApprovalVo;
import com.zenith.front.zunyi.process.service.impl.Fz_1Node;
import com.zenith.front.zunyi.process.service.impl.Fz_3_1Node;
import com.zenith.front.zunyi.scheduled.impl.MemDigitalCountTask;
import com.zenith.front.zunyi.service.IMemDevelopProcessService;
import com.zenith.front.zunyi.service.IMemDigitalService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <p>
 * 党员（发展党员）流程信息表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-02-14
 */
@Service
@Slf4j
public class MemDevelopProcessServiceImpl extends ServiceImpl<MemDevelopProcessMapper, MemDevelopProcess> implements IMemDevelopProcessService {
    @Resource
    private Fz_1Node fz1Node;
    @Resource
    private IMemDevelopService iMemDevelopService;
    @Resource
    private IMemService imemService;
    @Resource
    private OrgMapper orgMapper;
    @Resource
    private MemDevelopProcessMapper processMapper;
    @Resource
    private IMemDigitalService iMemDigitalService;
    @Resource
    private MemDigitalCountMapper memDigitalCountMapper;
    @Resource
    private MemDigitalCountTask memDigitalCountTask;


    /**
     * 获取预备党员转正审核列表
     */
    @Override
    public OutMessage<?> getMemAuditList(MemListNewDTO memListDTO, User user) {
        Integer pageNum = memListDTO.getPageNum();
        Integer pageSize = memListDTO.getPageSize();
        String memName = memListDTO.getMemName();
        if (StrUtil.isNotBlank(memName)) {
            memListDTO.setMemName(SM4Untils.encryptContent(EncryptProperties.nginxKey, memName));
        }
        boolean xjdw = iMemDigitalService.isXJDW();
        Page<MemAuditListVo> page = processMapper.getMemAuditList(new Page<>(pageNum, pageSize), memListDTO);
        page.getRecords().forEach(e -> {
            e.setCreateTime(e.getUpdateTime());
            e.setOrgName(CacheUtils.getOrgName(e.getOrgCode()));
            if (Objects.isNull(e.getApproveTime())) {
                e.setApprove(xjdw);
            }
            e.setD08Name("预备党员");
        });
        return new OutMessage<>(Status.SUCCESS, page);
    }

    /**
     * 获取预备党员审核列表
     */
    @Override
    public OutMessage<?> getMemDevelopAuditList(MemListNewDTO memListDTO, User user) {
        Integer pageNum = memListDTO.getPageNum();
        Integer pageSize = memListDTO.getPageSize();
        String memName = memListDTO.getMemName();
        if (StrUtil.isNotBlank(memName)) {
            memListDTO.setMemName(SM4Untils.encryptContent(EncryptProperties.nginxKey, memName));
        }
        boolean xjdw = iMemDigitalService.isXJDW();
        Page<MemAuditListVo> page = processMapper.getMemDevelopAuditList(new Page<>(pageNum, pageSize), memListDTO);
        page.getRecords().forEach(e -> {
            e.setCreateTime(e.getUpdateTime());
            e.setOrgName(CacheUtils.getOrgName(e.getOrgCode()));
            if (Objects.isNull(e.getApproveTime())) {
                e.setApprove(xjdw);
            }
            e.setD08Name("发展对象");
        });
        return new OutMessage<>(Status.SUCCESS, page);
    }

    /**
     * 审核预备党员
     */
    @Override
    public OutMessage<?> auditMemDevelop(AuditMemDto data, User user) {
        String lotNo = data.getDigitalLotNo();
        if (StrUtil.isBlank(lotNo)) {
            return new OutMessage<>(Status.NOT_NULL_ERROR);
        }
        MemDevelopProcess process = findByLotNo(lotNo);
        if (Objects.isNull(process) || !StrUtil.equals(process.getProcessNode(), ProcessNodeEnum.FZ_8.getNode())) {
            return new OutMessage<>(5001, "审核参数有误，请核查当前流程信息是否存在！", null);
        }
        LambdaQueryWrapper<MemDevelop> wrapper = new LambdaQueryWrapper<MemDevelop>()
                .eq(MemDevelop::getDigitalLotNo, lotNo)
                .eq(MemDevelop::getProcessNode, process.getProcessNode())
                .eq(MemDevelop::getAuditStatus, 1)
                .last("limit 1");
        MemDevelop memDevelop = iMemDevelopService.getOne(wrapper);
        if (Objects.isNull(memDevelop)) {
            return new OutMessage<>(Status.OBJEC_NOT_EXIST);
        }
        process.setApproveUser(user.getAccount());
        process.setApproveTime(new Date());
        process.setUpdateUser(user.getAccount());
        process.setAuditStatus(StrUtil.equals(data.getStatus(), "1") ? 2 : 3);
        // 审核通过
        if (StrUtil.equals(data.getStatus(), CommonConstant.ONE)) {
            // 1、流程流转到接收预备党员
            ProcessNodeEnum nextNode = ProcessNodeEnum.FZ_7;
            MemDevelopProcess nextProcess = fz1Node.saveProcessInfo(process, user, nextNode.getNode());
            if (Objects.isNull(nextProcess)) {
                return new OutMessage<>(5005, "审核数据异常，流程流转失败", null);
            }
            // 2、业务更新流程节点，县委审核状态
            LambdaUpdateWrapper<MemDevelop> updateWrapper = Wrappers.lambdaUpdate();
            updateWrapper.set(MemDevelop::getProcessNode, nextProcess.getProcessNode())
                    .set(MemDevelop::getAuditStatus, process.getAuditStatus())
                    .eq(MemDevelop::getCode, memDevelop.getCode());
            iMemDevelopService.update(updateWrapper);

            // 审核不通过
        } else if (StrUtil.equals(data.getStatus(), CommonConstant.ZERO)) {
            process.setReason(data.getReason());
            // 恢复原来的流程及基础信息表示
            this.saveCurrentProcess(process);
            iMemDevelopService.update(new LambdaUpdateWrapper<MemDevelop>().set(MemDevelop::getAuditStatus, process.getAuditStatus())
                    .eq(MemDevelop::getCode, memDevelop.getCode()));
        } else {
            return new OutMessage<>(Status.NOT_NULL_ERROR);
        }
        boolean update = updateById(process);
        return new OutMessage<>(update ? Status.SUCCESS : Status.FAIL);
    }

    /**
     * 预备党员转正审核
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public OutMessage<?> auditMem(AuditMemDto data, User user) throws Exception {
        String lotNo = data.getDigitalLotNo();
        if (StrUtil.isBlank(lotNo)) {
            return new OutMessage<>(Status.NOT_NULL_ERROR);
        }
        MemDevelopProcess process = findByLotNo(lotNo);
        if (Objects.isNull(process) || !StrUtil.equals(process.getProcessNode(), ProcessNodeEnum.YBQ_6.getNode())) {
            return new OutMessage<>(5001, "审核参数有误，请核查当前流程信息是否存在！", null);
        }
        LambdaQueryWrapper<Mem> wrapper = new LambdaQueryWrapper<Mem>()
                .eq(Mem::getDigitalLotNo, lotNo)
                .eq(Mem::getProcessNode, process.getProcessNode())
                .eq(Mem::getAuditStatus, 1)
                .last("limit 1");
        Mem mem = imemService.getOne(wrapper);
        if (Objects.isNull(mem)) {
            return new OutMessage<>(Status.OBJEC_NOT_EXIST);
        }
        process.setApproveTime(new Date());
        process.setApproveUser(user.getAccount());
        process.setUpdateUser(user.getAccount());
        process.setAuditStatus(StrUtil.equals(data.getStatus(), "1") ? 2 : 3);
        // 审核通过
        if (StrUtil.equals(data.getStatus(), CommonConstant.ONE)) {

            // 预备党员县委审核通过后调用原有转正方法
            if (StrUtil.isNotBlank(mem.getProcessNode()) && mem.getProcessNode().equals("YBQ_6")) {
                // 预备党员转正的相关参数
                String memText = mem.getMemberText();
                if (StrUtil.isBlank(memText) || Objects.isNull(JSONUtil.toBean(memText, DevelopStepLogDTO.class))) {
                    return new OutMessage<>(4002, "预备党员转正失败，参数缺失！", null);
                }
                OutMessage out = imemService.becomeFullMem(JSONUtil.toBean(memText, DevelopStepLogDTO.class));
                // 转正失败
                if (!Objects.equals(out.getCode(), Status.SUCCESS.getCode())) {
                    return out;
                }
            }

            // 流程流转到已归档
            ProcessNodeEnum nextNode = ProcessNodeEnum.YBQ_5;
            MemDevelopProcess nextProcess = fz1Node.saveProcessInfo(process, user, nextNode.getNode());
            if (Objects.isNull(nextProcess)) {
                return new OutMessage<>(5001, "审核数据异常，流程流转失败", null);
            }
            // 业务更新流程节点，县委审核状态
            LambdaUpdateWrapper<Mem> updateWrapper = Wrappers.lambdaUpdate();
            updateWrapper.set(Mem::getProcessNode, nextProcess.getProcessNode())
                    .set(Mem::getAuditStatus, process.getAuditStatus())
                    .set(Mem::getIsArchived, 1)
                    .eq(Mem::getCode, mem.getCode());
            imemService.update(updateWrapper);


            // 审核不通过
        } else if (StrUtil.equals(data.getStatus(), CommonConstant.ZERO)) {
            process.setReason(data.getReason());
            // 恢复原来的流程及基础信息标识
            this.saveCurrentProcess(process);
            imemService.update(new LambdaUpdateWrapper<Mem>().set(Mem::getAuditStatus, process.getAuditStatus())
                    .eq(Mem::getCode, mem.getCode()));
        } else {
            return new OutMessage<>(Status.NOT_NULL_ERROR);
        }
        boolean update = updateById(process);
        return new OutMessage<>(update ? Status.SUCCESS : Status.FAIL);
    }

    /**
     * 重新生成当前流程
     *
     * @param currentProcess 当前流程
     * @return
     */
    public boolean saveCurrentProcess(MemDevelopProcess currentProcess) {
        MemDevelopProcess process = new MemDevelopProcess();
        process.setCode(IdUtil.simpleUUID());
        process.setProcessNode(currentProcess.getProcessNode());
        process.setPreviousCode(currentProcess.getPreviousCode());
        process.setDigitalLotNo(currentProcess.getDigitalLotNo());
        process.setd08Code(currentProcess.getd08Code());
        process.setCreateTime(new Date());
        process.setCreateUser("未通过县委审核生成");
        return save(process);
    }

    /**
     * 获取最新流程信息（包含已审核）
     *
     * @param processNode  当前节点
     * @param digitalLotNo 档案批次唯一码
     * @return 流程信息
     */
    public MemDevelopProcess findByProcessNodeLotNo(String processNode, String digitalLotNo) {
        LambdaQueryWrapper<MemDevelopProcess> wrapper = new LambdaQueryWrapper<MemDevelopProcess>()
                .eq(MemDevelopProcess::getProcessNode, processNode)
                .eq(MemDevelopProcess::getDigitalLotNo, digitalLotNo)
                .isNull(MemDevelopProcess::getDeleteTime)
                .orderByDesc(MemDevelopProcess::getCreateTime)
                .last("limit 1");
        return getOne(wrapper);
    }

    /**
     * 获取最新流程信息（未审核的）
     *
     * @param digitalLotNo 档案批次唯一码
     * @return 最新流程
     */
    public MemDevelopProcess findByLotNo(String digitalLotNo) {
        LambdaQueryWrapper<MemDevelopProcess> wrapper = new LambdaQueryWrapper<MemDevelopProcess>()
                .isNull(MemDevelopProcess::getApproveTime)
                .eq(MemDevelopProcess::getDigitalLotNo, digitalLotNo)
                .isNull(MemDevelopProcess::getDeleteTime)
                .orderByDesc(MemDevelopProcess::getCreateTime)
                .last("limit 1");
        return getOne(wrapper);
    }


    /**
     * 获取发展对象流程信息
     */
    @Override
    public OutMessage<?> findDevelopProcess(DevelopExtendApprovalDto approvalDto, User user) {
        String processNode = approvalDto.getProcessNode();
        String digitalLotNo = approvalDto.getDigitalLotNo();
        if (StrUtil.isBlank(processNode) || StrUtil.isBlank(digitalLotNo)) {
            return new OutMessage<>(Status.NOT_NULL_ERROR);
        }
        MemDevelopProcess memDevelopProcess = findByProcessNodeLotNo(processNode, digitalLotNo);

        DevelopExtendApprovalVo approvalVo = new DevelopExtendApprovalVo();
        BeanUtil.copyProperties(memDevelopProcess, approvalVo);
        Date starTime = memDevelopProcess.getExtendStarTime();
        Date endTime = memDevelopProcess.getExtendEndTime();
        StringBuilder sb = new StringBuilder();
        if (Objects.nonNull(starTime) && Objects.nonNull(endTime)) {
            long betweenMonth = DateUtil.betweenMonth(starTime, endTime, false);
            long betweenDay = DateUtil.betweenDay(starTime, endTime, false);
            if (betweenMonth > 0) {
                long day = DateUtil.betweenDay(DateUtil.offsetMonth(starTime, (int) betweenMonth), endTime, false);
                sb.append(betweenMonth).append("个月").append(day > 0 ? day + "天" : "");
            } else {
                sb.append(betweenDay).append("天");
            }
            // 基层党委审批延长时间
            approvalVo.setExtendEndTimeStr(sb.toString());
        }
        return new OutMessage<>(Status.SUCCESS, approvalVo);
    }

    /**
     * 获取县委审核流程记录
     */
    @Override
    public OutMessage<?> findAuditProcess(DevelopExtendApprovalDto approvalDto, User user) {
        String processNode = approvalDto.getProcessNode();
        String digitalLotNo = approvalDto.getDigitalLotNo();
        if (StrUtil.isBlank(processNode) || StrUtil.isBlank(digitalLotNo)) {
            return new OutMessage<>(Status.NOT_NULL_ERROR);
        }
        // 获取最新审批记录
        LambdaQueryWrapper<MemDevelopProcess> wrapper = new LambdaQueryWrapper<MemDevelopProcess>()
                .eq(MemDevelopProcess::getProcessNode, processNode)
                .eq(MemDevelopProcess::getDigitalLotNo, digitalLotNo)
                .isNotNull(MemDevelopProcess::getApproveTime)
                .isNull(MemDevelopProcess::getDeleteTime)
                .orderByDesc(MemDevelopProcess::getCreateTime)
                .last("limit 1");
        MemDevelopProcess process = getOne(wrapper);
        MemAuditListVo vo = new MemAuditListVo();
        BeanUtil.copyProperties(process, vo);
        vo.setStatus(StrUtil.isBlank(process.getReason()) ? "1" : "0");
        return new OutMessage<>(Status.SUCCESS, vo);
    }

    /**
     * 发展对象延长审批
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public OutMessage<?> developExtendApproval(DevelopExtendApprovalDto approvalDto, User user) {
        String processNode = approvalDto.getProcessNode();
        String digitalLotNo = approvalDto.getDigitalLotNo();
        if (StrUtil.isBlank(processNode) || StrUtil.isBlank(digitalLotNo)) {
            return new OutMessage<>(Status.NOT_NULL_ERROR);
        }
        // 目前只有发展对象基层党委审批阶段可以延长审批
        if (!StrUtil.equalsAny(processNode, ProcessNodeEnum.FZ_6_1.getNode(), ProcessNodeEnum.FZ_6_2.getNode(), ProcessNodeEnum.FZ_6_3.getNode())) {
            return new OutMessage<>(Status.NOT_NULL_ERROR);
        }
        MemDevelop memDevelop = iMemDevelopService.findByCode(approvalDto.getMemCode());
        if (ObjectUtil.isNull(memDevelop) || !StrUtil.equals(memDevelop.getProcessNode(), processNode)) {
            return new OutMessage<>(Status.OBJEC_NOT_EXIST);
        }
        MemDevelopProcess memDevelopProcess = findByProcessNodeLotNo(processNode, digitalLotNo);
        if (ObjectUtil.isNull(memDevelopProcess)) {
            log.error("当前流程信息不存在，批次唯一码：" + digitalLotNo + "流程节点：" + processNode);
            return new OutMessage<>(5001, "当前流程信息不存在", null);
        }
        if (StrUtil.equals(memDevelopProcess.getIsAutoLock(), CommonConstant.ONE)) {
            return new OutMessage<>(5001, "该数据三个月未进行审批，系统已自动锁定！", null);
        }
        Date extendEndTime = null;
        Date endTime = memDevelopProcess.getExtendEndTime();
        Integer isMark = approvalDto.getIsMark();
        // 一个月
        if (Objects.equals(CommonConstant.ONE_INT, isMark)) {
            extendEndTime = DateUtil.offset(endTime, DateField.MONTH, 1);
            // 两个月
        } else if (Objects.equals(CommonConstant.TWO_INT, isMark)) {
            extendEndTime = DateUtil.offset(endTime, DateField.MONTH, 2);
            // 三个月
        } else if (Objects.equals(CommonConstant.THREE_INT, isMark)) {
            extendEndTime = DateUtil.offset(endTime, DateField.MONTH, 3);
            // 半个月
        } else if (Objects.equals(CommonConstant.NINE_INT, isMark)) {
            extendEndTime = DateUtil.offset(endTime, DateField.DAY_OF_YEAR, 15);
            // 自定义
        } else if (Objects.equals(CommonConstant.ZERO_INT, isMark)) {
            extendEndTime = approvalDto.getExtendEndTime();
            if (Objects.isNull(extendEndTime)) {
                return new OutMessage<>(Status.NOT_NULL_ERROR);
            }
            if (extendEndTime.before(endTime)) {
                return new OutMessage<>(5001, "延长时间不能早于当前流程待审批时间！", null);
            }
            // 延长审批逻辑：如果是自定义时长，不得超过90天，超过90天弹窗进行提示。
            if (DateUtil.between(endTime, extendEndTime, DateUnit.DAY, false) > 90) {
                return new OutMessage<>(5001, "自定义延长时长不得超过90天！", null);
            }
        } else {
            return new OutMessage<>(Status.NOT_NULL_ERROR);
        }
        memDevelopProcess.setApproveUser(user.getAccount());
        memDevelopProcess.setApproveTime(new Date());
        memDevelopProcess.setUpdateUser(user.getAccount());
        memDevelopProcess.setUpdateTime(new Date());
        boolean update = updateById(memDevelopProcess);
        // 特殊原因延长审批
        ProcessNodeEnum nextNode = ProcessNodeEnum.FZ_6_4;
        MemDevelopProcess nextProcess = new Fz_3_1Node().getNextProcessInfo(memDevelopProcess, user, nextNode.getNode());
        nextProcess.setExtendStarTime(memDevelopProcess.getExtendEndTime());
        nextProcess.setExtendEndTime(extendEndTime);
        nextProcess.setExtendApproveExplain(approvalDto.getExtendApproveExplain());
        boolean save = save(nextProcess);
        if (!save || !update) {
            return new OutMessage<>(Status.FAIL);
        }
        // 业务更新流程节点信息
        LambdaUpdateWrapper<MemDevelop> updateWrapper = Wrappers.lambdaUpdate();
        updateWrapper.set(MemDevelop::getProcessNode, nextProcess.getProcessNode())
                .set(MemDevelop::getAuditStatus, 0)
                .eq(MemDevelop::getCode, memDevelop.getCode());
        iMemDevelopService.update(updateWrapper);
        return new OutMessage<>(Status.SUCCESS, nextNode);
    }

    /**
     * 发展对象审批超时解除锁定
     */
    @Override
    public OutMessage<?> unlockExtendApproval(DevelopExtendApprovalDto approvalDto, User user) {
        String processNode = approvalDto.getProcessNode();
        String digitalLotNo = approvalDto.getDigitalLotNo();
        if (StrUtil.isBlank(processNode) || StrUtil.isBlank(digitalLotNo)) {
            return new OutMessage<>(Status.NOT_NULL_ERROR);
        }
        MemDevelopProcess memDevelopProcess = findByProcessNodeLotNo(processNode, digitalLotNo);
        if (ObjectUtil.isNull(memDevelopProcess)) {
            log.error("当前流程信息不存在，批次唯一码：" + digitalLotNo + "流程节点：" + processNode);
            return new OutMessage<>(5001, "当前流程信息不存在", null);
        }
        memDevelopProcess.setIsAutoLock("0");
        memDevelopProcess.setUpdateUser(user.getAccount());
        memDevelopProcess.setUpdateTime(new Date());
        updateById(memDevelopProcess);
        return new OutMessage<>(Status.SUCCESS);
    }


    /**
     * 遵义初始化生成流程
     */
    public void autoGenerateProcess() {

        // 是否遵义节点
        if (UserConstant.HAS_ZUN_YI) {
            log.info("初始化遵义入党申请人、积极份子、发展党员流程数据！！！");
            LambdaQueryWrapper<MemDevelop> wrapper = Wrappers.lambdaQuery();
            wrapper.select(MemDevelop::getId, MemDevelop::getDigitalLotNo, MemDevelop::getActiveDate, MemDevelop::getD08Code,
                    MemDevelop::getCreateTime, MemDevelop::getApplyDate);
            wrapper.isNull(MemDevelop::getDeleteTime)
                    .isNull(MemDevelop::getProcessNode)
                    .in(MemDevelop::getD08Code, "3", "4", "5");
            List<MemDevelop> memDevelops = iMemDevelopService.list(wrapper);
            List<MemDevelopProcess> processList = Collections.synchronizedList(new ArrayList<>());
            List<MemDevelop> saveList = memDevelops.stream().peek(e -> {
                String digLotNo = StrUtil.isNotBlank(e.getDigitalLotNo()) ? e.getDigitalLotNo() : IdUtil.simpleUUID();
                Date cDate = Objects.equals(e.getD08Code(), CommonConstant.FIVE) ? e.getApplyDate() : e.getActiveDate();
                List<MemDevelopProcess> processS = TransferRecordServiceImpl.generate(e.getD08Code(), digLotNo, cDate);
                processS.stream().filter(p -> Objects.nonNull(p.getApproveTime())).forEach(e1 -> {
                    e1.setApproveUser("初始化生成");
                });
                MemDevelopProcess process = processS.stream().filter(p -> Objects.isNull(p.getApproveTime())).findFirst().orElse(null);
                if (Objects.nonNull(process)) {
                    process.setCreateUser("初始化生成");
                    e.setDigitalLotNo(digLotNo);
                    e.setProcessNode(process.getProcessNode());
                    processList.addAll(processS);
                }
            }).filter(e -> StrUtil.isNotBlank(e.getProcessNode())).collect(Collectors.toList());

            if (CollUtil.isNotEmpty(saveList)) {
                iMemDevelopService.updateBatchById(saveList);
                log.info("MemDevelop基础表流程初始化完成！：" + saveList.size());
            }
            if (CollUtil.isNotEmpty(processList)) {
                this.saveBatch(processList);
                log.info("入党申请人流程初始化完成！：" + processList.size());
            }

            processList.clear();
            log.info("初始化遵义预备党员流程数据！！！");
            LambdaQueryWrapper<Mem> memLambdaQueryWrapper = Wrappers.lambdaQuery();
            memLambdaQueryWrapper.select(Mem::getId, Mem::getDigitalLotNo, Mem::getJoinOrgDate, Mem::getD08Code);
            memLambdaQueryWrapper.isNull(Mem::getDeleteTime)
                    .isNull(Mem::getProcessNode)
                    .in(Mem::getD08Code, "2");
            List<Mem> memList = imemService.list(memLambdaQueryWrapper);
            List<Mem> memSaveList = memList.stream().peek(e -> {
                String digLotNo = StrUtil.isNotBlank(e.getDigitalLotNo()) ? e.getDigitalLotNo() : IdUtil.simpleUUID();
                List<MemDevelopProcess> processS = TransferRecordServiceImpl.generate(e.getD08Code(), digLotNo, e.getJoinOrgDate());
                processS.stream().filter(p -> Objects.nonNull(p.getApproveTime())).forEach(e1 -> {
                    e1.setApproveUser("初始化生成");
                });
                MemDevelopProcess process = processS.stream().filter(p -> Objects.isNull(p.getApproveTime())).findFirst().orElse(null);
                if (Objects.nonNull(process)) {
                    process.setCreateUser("初始化生成");
                    e.setDigitalLotNo(digLotNo);
                    e.setProcessNode(process.getProcessNode());
                    e.setIsCatalogue(CommonConstant.ONE_INT);
                    processList.addAll(processS);
                }

            }).filter(e -> StrUtil.isNotBlank(e.getProcessNode())).collect(Collectors.toList());

            if (CollUtil.isNotEmpty(memSaveList)) {
                imemService.updateBatchById(memSaveList);
                log.info("Mem基础表流程初始化完成！：" + memSaveList.size());
            }
            if (CollUtil.isNotEmpty(processList)) {
                this.saveBatch(processList);
                log.info("预备党员流程初始化完成！：" + processList.size());
            }

            // 当统计表没有数据时计算档案上传信息（完整度）
            log.info("开始计算档案完整度详细信息..." );
            memDigitalCountTask.task();
        }
    }

}
