package com.zenith.front.zunyi.process.service.impl;

import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.zenith.front.api.mem.IMemDevelopService;
import com.zenith.front.common.constant.CommonConstant;
import com.zenith.front.common.constant.ProcessNodeEnum;
import com.zenith.front.model.bean.MemDevelop;
import com.zenith.front.model.bean.MemDevelopProcess;
import com.zenith.front.model.message.OutMessage;
import com.zenith.front.model.message.Status;
import com.zenith.front.zunyi.process.service.ProcessEngineService;
import com.zenith.front.zunyi.service.IMemDevelopProcessService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.Map;
import java.util.Objects;

/**
 * 县委审核
 *
 * <AUTHOR>
 * @since 2025/3/27
 */
@Service
@Slf4j
public class Fz_8Node implements ProcessEngineService {
    @Resource
    private IMemDevelopService iMemDevelopService;
    @Resource
    private IMemDevelopProcessService iMemDevelopProcessService;


    @Override
    public String getNode() {
        return ProcessNodeEnum.FZ_8.getNode();
    }

    @Override
    public OutMessage<ProcessNodeEnum> next(String currentNode, String businessId, Map<String, Object> paramMap) {
        // 1、查询业务是否存在
        LambdaQueryWrapper<MemDevelop> wrapper = new LambdaQueryWrapper<MemDevelop>()
                .eq(MemDevelop::getCode, businessId)
                .eq(MemDevelop::getProcessNode, currentNode)
                .isNull(MemDevelop::getDeleteTime).last("limit 1");
        MemDevelop entity = iMemDevelopService.getOne(wrapper);
        if (Objects.isNull(entity) || StrUtil.isBlank(entity.getDigitalLotNo())) {
            log.error("业务信息不存在或档案批次唯一码为空，业务ID：{}", businessId);
            return new OutMessage<>(5001, "业务信息不存在或档案批次唯一码为空", null);
        }

        // 2、查询当前流程是否存在
        String lotNo = entity.getDigitalLotNo();
        LambdaQueryWrapper<MemDevelopProcess> processWrapper = new LambdaQueryWrapper<MemDevelopProcess>()
                .eq(MemDevelopProcess::getProcessNode, currentNode)
                .eq(MemDevelopProcess::getDigitalLotNo, lotNo)
                .isNull(MemDevelopProcess::getApproveTime).last("limit 1");
        MemDevelopProcess currentProcess = iMemDevelopProcessService.getOne(processWrapper);
        if (Objects.isNull(currentProcess)) {
            log.error("当前流程信息不存在，批次唯一码：{}", lotNo);
            return new OutMessage<>(5001, "当前流程信息不存在", null);
        }

        // 3、业务更新县委审核状态：1未审核，2审核通过，3审核未通过，默认为0
        final Integer auditStatus = CommonConstant.ONE_INT;
        LambdaUpdateWrapper<MemDevelop> updateWrapper = Wrappers.lambdaUpdate();
        updateWrapper.set(MemDevelop::getAuditStatus, auditStatus)
                .eq(MemDevelop::getCode, entity.getCode());
        iMemDevelopService.update(updateWrapper);
        boolean update = iMemDevelopProcessService.update(new LambdaUpdateWrapper<MemDevelopProcess>().set(MemDevelopProcess::getUpdateTime, new Date())
                .set(MemDevelopProcess::getAuditStatus, auditStatus)
                .eq(MemDevelopProcess::getCode, currentProcess.getCode()));
        return update ? new OutMessage<>(Status.SUCCESS, ProcessNodeEnum.find(currentNode)) : new OutMessage<>(Status.FAIL);
    }


}
