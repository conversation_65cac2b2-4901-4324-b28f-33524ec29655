package com.zenith.front.zunyi.model.dto;

import com.zenith.front.common.annotation.FiledValue;
import com.zenith.front.common.annotation.PrimaryKey;
import com.zenith.front.model.dto.MemDevelopDTO;
import com.zenith.front.model.validate.group.Common1Group;
import com.zenith.front.model.validate.group.Common2Group;
import lombok.Data;
import lombok.ToString;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @since 2025/2/24
 */
@ToString
@Data
public class MemDevelopNewDTO {

    /**
     * 上传文件集合
     */
    @NotBlank(groups = Common1Group.class, message = "档案文件 不能为空")
    private List<FileDigitalDto> filesList;
    /**
     * 前端录入,操作人（上传人、删除人）
     */
    @NotNull(groups = Common1Group.class, message = "上传人不能为空")
    private String oprationUser;

    /**
     * 组织类别
     */
    private String d01Code;

    private Long id;
    @PrimaryKey
    @NotBlank(groups = Common2Group.class, message = "code 不能为空")
    private String code;
    /**
     * 人事关系是否在党组织关联单位内(1是，0否)
     */
    @FiledValue
    private Integer hasUnitStatistics;

    private String esId;
    /**
     * 发展党员姓名
     */
    @NotBlank(groups = Common1Group.class, message = "name 不能为空")
    private String name;
    private String pinyin;
    /**
     * 发展党员身份证
     */
    @NotBlank(groups = Common1Group.class, message = "idcard 不能为空")
    private String idcard;
    /**
     * 民族code
     */
    @NotBlank(groups = Common1Group.class, message = "d06Code 不能为空")
    private String d06Code;
    /**
     * 民族名称
     */
    @NotBlank(groups = Common1Group.class, message = "d06Name 不能为空")
    private String d06Name;
    /**
     * 籍贯
     */
    @NotBlank(groups = Common1Group.class, message = "d48Code 不能为空")
    private String d48Code;
    /**
     * 籍贯名称
     */
    private String d48Name;
    /**
     * 性别
     */
    @NotBlank(groups = Common1Group.class, message = "sexCode 不能为空")
    private String sexCode;
    /**
     * 性别name
     */
    @NotBlank(groups = Common1Group.class, message = "sexName 不能为空")
    private String sexName;
    /**
     * 出生日期
     */
    @NotNull(groups = Common1Group.class, message = "birthday 不能为空")
    private java.util.Date birthday;
    @NotBlank(groups = Common1Group.class, message = "phone 不能为空")
    private String phone;
    /**
     * 学历code
     */
    @NotBlank(groups = Common1Group.class, message = "d07Code 不能为空")
    private String d07Code;
    /**
     * 学历名称
     */
    @NotBlank(groups = Common1Group.class, message = "d07Name 不能为空")
    private String d07Name;
    /**
     * 毕业院校（专科及以上填写）
     */
    private String byyx;
    /**
     * 毕业专业（专科及以上填写）
     */
    private String d88Code;
    private String d88Name;
    /**
     * 工作岗位
     */
    @NotBlank(groups = Common1Group.class, message = "d09Code 不能为空")
    private String d09Code;
    @NotBlank(groups = Common1Group.class, message = "d09Name 不能为空")
    private String d09Name;
    /**
     * 婚姻状况code
     */
    private String d60Code;
    private String d60Name;
    /**
     * 参加工作日期
     */
    private java.util.Date joinWorkDate;
    /**
     * 申请入党日期
     */
    @NotNull(groups = Common1Group.class, message = "applyDate 不能为空")
    private java.util.Date applyDate;
    /**
     * 档案管理单位名称
     */
    private String archiveUnit;
    /**
     * 家庭住址
     */
    @NotBlank(groups = Common1Group.class, message = "homeAddress 不能为空")
    private String homeAddress;
    /**
     * 聘任专业技术职务名称
     */
    @NotBlank(groups = Common1Group.class, message = "d19Name 不能为空")
    private String d19Name;
    /**
     * 聘任专业技术职务code
     */
    @NotBlank(groups = Common1Group.class, message = "d19Name 不能为空")
    private String d19Code;
    /**
     * 新社会阶层类型名称
     */
    @NotBlank(groups = Common1Group.class, message = "d20Name 不能为空")
    private String d20Name;
    /**
     * 新社会阶层类型code
     */
    @NotBlank(groups = Common1Group.class, message = "d20Code 不能为空")
    private String d20Code;
    /**
     * 一线情况code
     */
    @NotBlank(groups = Common1Group.class, message = "d21Code 不能为空")
    private String d21Code;
    /**
     * 一线情况名称
     */
    @NotBlank(groups = Common1Group.class, message = "d21Name 不能为空")
    private String d21Name;
    /**
     * 发展党员类型code
     */
    @NotBlank(groups = Common1Group.class, message = "d08Code 不能为空")
    private String d08Code;
    /**
     * 发展党员类型名称
     */
    @NotBlank(groups = Common1Group.class, message = "d08Name 不能为空")
    private String d08Name;
    /**
     * 是否农民工
     */
    @NotNull(groups = Common1Group.class, message = "isFarmer 不能为空")
    private Integer isFarmer;
    /**
     * 入党申请书扫描件
     */
    private String fileUrl;
    /**
     * 聘任日期
     */
    private java.util.Date appointmentDate;
    /**
     * 入党递交党组织code
     */
    private String appliedOrgCode;
    private String appliedOrgZbCode;
    private String appliedOrgName;
    /**
     * 入党递交党组织层级码
     */
    private String developAppliedOrgCode;
    /**
     * 失联日期
     */
    private java.util.Date lossDate;
    /**
     * 终止日期
     */
    private java.util.Date appointmentEndDate;
    /**
     * 是否高知识群体
     */
    private Integer isHighKnowledge;
    /**
     * 失联情况
     */
    @NotBlank(groups = Common1Group.class, message = "d18Name 不能为空")
    private String d18Name;
    /**
     * 失联情况code
     */
    @NotBlank(groups = Common1Group.class, message = "d18Code 不能为空")
    private String d18Code;
    /**
     * 管理党组织code
     */
    @NotBlank(groups = Common1Group.class, message = "orgCode 不能为空")
    private String orgCode;
    /**
     * 管理党组织层级码
     */
    @NotBlank(groups = Common1Group.class, message = "developOrgCode不能为空")
    private String developOrgCode;
    @NotBlank(groups = Common1Group.class, message = "orgName 不能为空")
    private String orgName;
    @NotBlank(groups = Common1Group.class, message = "orgZbCode 不能为空")
    private String orgZbCode;
    /**
     * 是否是先进模范人物
     */
    private String advancedModelCode;
    private java.util.Date createTime;
    private java.util.Date updateTime;
    /**
     * 召开支委会日期(成为积极分子时间
     */
    private java.util.Date activeDate;
    private java.util.Date objectDate;
    /**
     * 加入中共组织的类别code
     */
    private String joinOrgType;
    /**
     * 进入支部类型
     */
    private String d11Name;
    /**
     * 进入支部类型code
     */
    private String d11Code;
    /**
     * 加入共产党类型code（发展对象=》预备党员）
     */
    private String joinOrgCode;
    /**
     * 加入中共组织的类别名称
     */
    private String joinOrgName;
    /**
     * 是否劳务派遣
     */
    private Integer isDispatch;
    private java.util.Date deleteTime;
    private java.util.Date timestamp;
    private String zbCode;
    private Integer isHistory;
    private Integer isOutSystem;
    private String updateAccount;
    private String outBranchOrgName;
    /**
     * 说明
     */
    private String instructions;

    private Integer hasYoungFarmers;

    private Integer hasWorker;

    private String politicsCode;

    private String politicsName;

    /**
     * 在读院校
     */
    private String readingCollege;

    /**
     * 在读专业
     */
    private String readingProfessionalCode;

    /**
     * 在读专业名称
     */
    private String readingProfessionalName;

    /**
     * 学制
     */
    private String educationalSystem;

    /**
     * 是否死亡
     */
    private Integer hasDead;


    /**
     * 死亡时间
     */
    private Date deadTime;


    /**
     * 死亡时间
     */
    private Date ratificationTime;

    /**
     * 工作性质
     */
    private String jobNatureCode;

    /**
     * 专业技术职称
     */
    private String d126Code;

    /**
     * 专业技术职称名称
     */
    private String d126Name;

    /**
     * 人事关系所在单位名称(1是，0否)
     */
    private String unitInformation;
    /**
     * 人事关系所在单位类别
     */
    private String d04Code;
    /**
     * 人事关系所在单位是否省内单位(1是，0否)
     */
    private Integer hasUnitProvince;
    /**
     * 统计单位 人事关系所在单位code
     */
    private String statisticalUnit;
    /**
     * 中间交换区单独传的单位标识
     */
    private String middleUnitCode;
    /**
     * 中间交换区单独传的单位名称
     */
    private String middleUnitName;
    /**
     * 自定义单位名称
     */
    private String selfUnitName;

    /**
     * 入学时间
     */
    private Date enterSchoolDate;

    /**
     * 支部党员大会讨论通过时间
     */
    private Date topreJoinOrgDate;
    /**
     * 知识分子情况
     */
    private String d154Code;

    private String d154Name;

    /**
     * 国民经济行业CODE
     */
    private String d194Code;

    /**
     * 与国民经济行业name
     */
    private String d194Name;

    /**
     * 生产性服务行业CODE
     */
    private String d195Code;

    /**
     * 生产性服务行业name
     */
    private String d195Name;

    /**
     * 档案批次唯一码
     */
    private String digitalLotNo;

    /**
     * 流程节点:RD_1(新增入党申请)；RD_2_1(一月内需要谈话)；RD_2_2(谈话时间少于10天)；RD_2_3(一月内未进行谈话)；RD_3(已谈话)；RD_4(满足积极份子)；	 JJ_1(待考察人员)；JJ_2(待第一次考察)；JJ_3(待第二次考察)；JJ_4(超半月未按时考察)；JJ_5(持续考察人员)； JJ_6（上级党委备案）；JJ_7(发展对象阶段)；	 FZ_1(支委会审查)；FZ_2(党总支审查)；FZ_3_1(一月内进行预审)；FZ_3_2（预审时间少于十天）；FZ_3_3(一月内未进行预审)；FZ_4(县级党委预审)；FZ_5_1(一月内进行讨论人员)；FZ_5_2(讨论时间少于五天)；FZ_5_3(一月内未进行讨论)；FZ_6_1(三个月内审批人员)；FZ_6_2(审批时间少于十天)；FZ_6_3(审批超时)；FZ_6_4(特殊原因延长审批)；FZ_7(接收预备党员)；
     */
    private String processNode;

    /**
     * 档案类别：1-绿色档案； 2-蓝色档案； 3-红色档案； 99-其他
     */
    private Integer digitalType;

    /**
     * 党员身份认定：1-已认定；0-未认定
     */
    private Integer isSure;

    /**
     * 发展阶段：1-第一阶段；2-第二阶段；3-第三阶段
     */
    private Integer developPahase;

    private Map<String, Object> paramMap;


}
