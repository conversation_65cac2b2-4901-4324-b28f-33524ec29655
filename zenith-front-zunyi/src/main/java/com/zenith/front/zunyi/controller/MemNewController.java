package com.zenith.front.zunyi.controller;

import com.zenith.front.api.report.IStatisticsYearService;
import com.zenith.front.common.annotation.LogicValidator;
import com.zenith.front.common.annotation.RequiresPermissions;
import com.zenith.front.common.constant.OperateConstant;
import com.zenith.front.model.bean.Mem;
import com.zenith.front.model.bean.User;
import com.zenith.front.model.constant.UserConstant;
import com.zenith.front.model.dto.DevelopStepLogDTO;
import com.zenith.front.model.dto.MemDTO;
import com.zenith.front.model.dto.MemListDTO;
import com.zenith.front.model.message.InMessage;
import com.zenith.front.model.message.OutMessage;
import com.zenith.front.model.message.Status;
import com.zenith.front.model.validate.annotation.Validate;
import com.zenith.front.model.validate.group.Common1Group;
import com.zenith.front.model.validate.group.Common4Group;
import com.zenith.front.model.validate.group.MemGroup;
import com.zenith.front.model.vo.BetweenReportDateVo;
import com.zenith.front.zunyi.model.dto.UploadFileDigitalDto;
import com.zenith.front.zunyi.service.IMemNewService;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import javax.validation.constraints.NotBlank;

/**
 * 党员管理
 * <AUTHOR>
 * @create_date 2025-02-28 10:48
 * @description
 */
@RestController
@RequestMapping("/zunyi/mem/new")
public class MemNewController {

    @Resource
    private IMemNewService memService;
    @Resource
    private IStatisticsYearService iStatisticsYearService;


    /**
     * 党员管理列表
     */
    @PostMapping("/list")
    @RequiresPermissions
    @Validate(group = Common4Group.class)
    public OutMessage<?> memList(@RequestBody InMessage<MemListDTO> inMessage) {
        MemListDTO dto = inMessage.getData();
        String currManOrgCode = UserConstant.USER_CONTEXT.get().getUserRolePermission().getOrgCode();
        if (!dto.getMemOrgCode().startsWith(currManOrgCode)) {
            return new OutMessage<>(Status.PERMISSION_DENIED);
        }
        return memService.memList(dto, false);
    }

    /**
     * 延长预备期
     *
     * @return
     */
    @PostMapping("/prolongationMem")
    @Validate(group = Common1Group.class)
    @RequiresPermissions(opt = OperateConstant.UPDATE)
    public OutMessage prolongationMem(@RequestBody InMessage<DevelopStepLogDTO> inMessage) {
        DevelopStepLogDTO developStepLogDTO = inMessage.getData();
        return memService.prolongationMem(developStepLogDTO);
    }

    /**
     * 上传数字档案
     */
    @PostMapping("/uploadFileDigital")
    @Validate(group = {Common1Group.class})
    @RequiresPermissions
    public OutMessage<?> uploadFileDigital(@RequestBody InMessage<UploadFileDigitalDto> inMessage) throws Exception {
        UploadFileDigitalDto digitalDto = inMessage.getData();
        User user = UserConstant.USER_CONTEXT.get().getUser();
        return memService.uploadFileDigital(digitalDto, user);
    }

    /**
     * 预备党员转正反显 支部大会讨论通过时间
     */
    @GetMapping("/memInfo")
    @RequiresPermissions
    public OutMessage<?> memInfo(String memCode) {
        return memService.memInfo(memCode);
    }

    /**
     * 转正前公示校核是否上传入党宣誓材料
     */
    @GetMapping("/checkOath")
    @RequiresPermissions
    public OutMessage<?> checkOath(String memCode) {
        return memService.checkOath(memCode);
    }

    /**
     * 党员流程节点总数
     * @return
     */
    @PostMapping("/count")
    @RequiresPermissions
    public OutMessage<?> count(@RequestBody InMessage<MemListDTO> inMessage){
        MemListDTO dto = inMessage.getData();
        String currManOrgCode = UserConstant.USER_CONTEXT.get().getUserRolePermission().getOrgCode();
        if (!dto.getMemOrgCode().startsWith(currManOrgCode)) {
            return new OutMessage<>(Status.PERMISSION_DENIED);
        }
        return memService.count(dto);
    }

    /**
     * 关系转接数字档案目录
     * @param memCode
     * @return
     */
    @GetMapping("/transferMemDigital")
    @RequiresPermissions
    public OutMessage<?> transferMemDigital(String memCode) {
        return memService.transferMemDigital(memCode);
    }

    /**
     * 党员退回到预备党员
     */
    @GetMapping("/backToProbationary")
    @RequiresPermissions
    @Valid
    public OutMessage backToProbationary(@RequestParam("code") @NotBlank(message = "code 不能为空") String code,
                                         @RequestParam("processNode") @NotBlank(message = "processNode 不能为空") String processNode) {
        return memService.backToProbationary(code, processNode);
    }

    /**
     * 补录党员
     * @param inMessage
     * @return
     */
    @PostMapping("/addMem")
    @Validate(group = MemGroup.class)
    @LogicValidator(opt = OperateConstant.INSERT, clazz = Mem.class)
    @RequiresPermissions(opt = OperateConstant.INSERT)
    public OutMessage addMem(@Valid @RequestBody InMessage<MemDTO> inMessage) {
        MemDTO memDTO = inMessage.getData();
        if (!memDTO.getMemOrgCode().startsWith(UserConstant.USER_CONTEXT.get().getUserRolePermission().getOrgCode())) {
            return new OutMessage<>(Status.PERMISSION_DENIED);
        }
        BetweenReportDateVo betweenReportDate = iStatisticsYearService.isBeforeReportDate(memDTO.getReplenishInputDate(), "2");
        if (!betweenReportDate.getIsBetween()) {
            return new OutMessage<String>(Status.NOT_IN_BETWEEN_DEFAULT_DATE).format("补充录入时间", betweenReportDate.getMessage());
        }
        return memService.addMem(memDTO);
    }


    /**
     * 档案归档
     *
     * @param memCode    党员code
     * @param isArchived 档案是否已归档：1已归档，0未归档
     * @return
     */
    @GetMapping("/isArchived")
    @RequiresPermissions
    public OutMessage<?> isArchived(String memCode, Integer isArchived) {
        return memService.isArchived(memCode, isArchived);
    }



}
