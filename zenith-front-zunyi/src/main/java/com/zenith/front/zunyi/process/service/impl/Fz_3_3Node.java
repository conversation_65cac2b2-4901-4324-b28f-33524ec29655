package com.zenith.front.zunyi.process.service.impl;

import com.zenith.front.common.constant.ProcessNodeEnum;
import com.zenith.front.model.message.OutMessage;
import com.zenith.front.zunyi.process.service.ProcessEngineService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Map;

/**
 * 一月内未进行预审
 *
 * <AUTHOR>
 * @since 2025/2/27
 */
@Service
@Slf4j
public class Fz_3_3Node implements ProcessEngineService {
    @Resource
    private Fz_3_1Node fz31Node;


    @Override
    public String getNode() {
        return ProcessNodeEnum.FZ_3_3.getNode();
    }

    @Override
    public OutMessage<ProcessNodeEnum> next(String currentNode, String businessId, Map<String, Object> paramMap) {
        return fz31Node.next(currentNode, businessId, paramMap);
    }
}
