package com.zenith.front.zunyi.process.service;

import com.zenith.front.common.constant.ProcessNodeEnum;
import com.zenith.front.model.bean.User;
import com.zenith.front.model.constant.UserConstant;
import com.zenith.front.model.message.OutMessage;

import java.util.Map;

/**
 * <AUTHOR>
 * @create_date 2025-02-17 09:17
 * @description
 */
public interface ProcessEngineService {

    default User currentUser() { return UserConstant.USER_CONTEXT.get().getUser();}

    /**
     * 当前流程节点名
     *
     * @return
     */
    String getNode();

    /**
     * 下一步流程
     *
     * @param currentNode 当前节点
     * @param businessId  业务ID
     * @param paramMap    参数
     * @return
     */
    OutMessage<ProcessNodeEnum> next(String currentNode, String businessId, Map<String, Object> paramMap);

    /**
     * 定时计算判断流程节点
     * @return
     */
    default OutMessage<?> task(){return null;}
}
