package com.zenith.front.zunyi.controller;

import com.zenith.front.common.annotation.RequiresPermissions;
import com.zenith.front.common.constant.OperateConstant;
import com.zenith.front.model.bean.User;
import com.zenith.front.model.constant.UserConstant;
import com.zenith.front.model.dto.MemListDTO;
import com.zenith.front.model.message.InMessage;
import com.zenith.front.model.message.OutMessage;
import com.zenith.front.model.message.Status;
import com.zenith.front.model.validate.annotation.Validate;
import com.zenith.front.model.validate.group.Common1Group;
import com.zenith.front.model.validate.group.Common4Group;
import com.zenith.front.zunyi.model.dto.CommonDto;
import com.zenith.front.zunyi.model.dto.DigitalCountDetailDTO;
import com.zenith.front.zunyi.model.dto.MemListNewDTO;
import com.zenith.front.zunyi.model.dto.UploadFileDigitalDto;
import com.zenith.front.zunyi.service.IMemDigitalCountService;
import com.zenith.front.zunyi.service.IMemDigitalService;
import com.zenith.front.zunyi.service.IMemNewService;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;

/**
 * 档案管理控制层
 *
 * <AUTHOR>
 * @create_date 2025-02-27 16:42
 * @description
 */
@RestController
@RequestMapping("/zunyi/digital")
public class MemDigitalController {

    @Resource
    private IMemNewService iMemNewService;
    @Resource
    private IMemDigitalService iMemDigitalService;
    @Resource
    private IMemDigitalCountService memDigitalCountService;

    /**
     * 获取党员档案管理
     */
    @PostMapping("/mem/list")
    @RequiresPermissions
    @Validate(group = Common4Group.class)
    public OutMessage<?> memList(@RequestBody InMessage<MemListDTO> inMessage) {
        MemListDTO dto = inMessage.getData();
        String currManOrgCode = UserConstant.USER_CONTEXT.get().getUserRolePermission().getOrgCode();
        if (!dto.getMemOrgCode().startsWith(currManOrgCode)) {
            return new OutMessage<>(Status.PERMISSION_DENIED);
        }
        return iMemNewService.memList(dto, true);
    }

    /**
     * 获取发展档案管理
     */
    @PostMapping("/developMem/list")
    @RequiresPermissions
    @Validate(group = Common1Group.class)
    public OutMessage<?> developMemList(@RequestBody InMessage<MemListNewDTO> inMessage) {
        MemListNewDTO dto = inMessage.getData();
        String currManOrgCode = UserConstant.USER_CONTEXT.get().getUserRolePermission().getOrgCode();
        if (!dto.getMemOrgCode().startsWith(currManOrgCode)) {
            return new OutMessage<>(Status.PERMISSION_DENIED);
        }
        return iMemNewService.developMemList(dto, true);
    }

    /**
     * 获取档案目录树结构
     *
     * @param isCatalogue 档案目录区分
     */
    @GetMapping("/developMem/memDigitalContents")
    @RequiresPermissions
    public OutMessage<?> memDigitalContents(String isCatalogue, String d08Code, String processNode, String memCode) {
        return iMemDigitalService.memDigitalContents(isCatalogue, d08Code, processNode, memCode);
    }

    /**
     * 档案材料
     *
     * @param digitalLotNo 档案唯一码
     * @param d222Code     目录code
     * @return
     */
    @GetMapping("/developMem/listMemDigital")
    @RequiresPermissions
    public OutMessage<?> listMemDigital(String digitalLotNo, String d222Code, String memCode) {
        return iMemDigitalService.listMemDigital(digitalLotNo, d222Code, memCode);
    }

    /**
     * 是否认定
     *
     * @param memCode
     * @param sure    党员身份认定：1-已认定；0-未认定
     * @return
     */
    @GetMapping("/sure")
    @RequiresPermissions
    public OutMessage<?> isSure(String memCode, Integer sure) {
        return iMemDigitalService.isSure(memCode, sure);
    }

    /**
     * 档案清除
     */
    @PostMapping("/cleanDigital")
    @Validate
    @RequiresPermissions(opt = OperateConstant.UPDATE)
    public OutMessage<?> cleanDigital(@RequestBody InMessage<CommonDto> inMessage) {
        User user = UserConstant.USER_CONTEXT.get().getUser();
        return iMemDigitalService.cleanDigital(inMessage.getData(), user);
    }

    /**
     * 档案排序
     */
    @PostMapping("/sortDigital")
    @Validate
    @RequiresPermissions(opt = OperateConstant.UPDATE)
    public OutMessage<?> sortDigital(@RequestBody InMessage<CommonDto> inMessage) {
        User user = UserConstant.USER_CONTEXT.get().getUser();
        return iMemDigitalService.sortDigital(inMessage.getData(), user);
    }

    /**
     * 获取档案操作日志列表
     */
    @PostMapping("/getDigitalLogList")
    @Validate
    @RequiresPermissions
    public OutMessage getDigitalLogList(@RequestBody InMessage<MemListNewDTO> inMessage) {
        User user = UserConstant.USER_CONTEXT.get().getUser();
        return iMemDigitalService.getDigitalLogList(inMessage.getData(), user);
    }

    /**
     * 档案管理里面的上传档案
     */
    @PostMapping("/uploadDigital")
    @RequiresPermissions
    public OutMessage uploadDigital(@RequestBody InMessage<UploadFileDigitalDto> inMessage) {
        UploadFileDigitalDto digitalDto = inMessage.getData();
        User user = UserConstant.USER_CONTEXT.get().getUser();
        return iMemDigitalService.uploadDigital(digitalDto, user);
    }

    /**
     * 获取关系转接档案材料及日志
     *
     * @param digitalLotNo 档案唯一码
     * @param d222Code     目录code
     * @return 查询结果
     */
    @GetMapping("/transferMemDigitalList")
    @RequiresPermissions
    public OutMessage<?> transferMemDigitalList(String digitalLotNo, String d222Code) {
        return iMemDigitalService.transferMemDigitalList(digitalLotNo, d222Code);
    }

    /**
     * 电子档案信息
     * @param memCode 党员code
     * @param transferId 转接ID
     * @return
     */
    @GetMapping("/electronic")
    @RequiresPermissions
    public OutMessage<?> electronic(@RequestParam("memCode") String memCode, String transferId) {
        return iMemDigitalService.electronic(memCode, transferId);
    }


    /**
     * 档案统计汇总列表
     * @param orgLevelCode
     * @param orgName 搜索组织名称
     * @return
     */
    @GetMapping("/digitalCountList")
    @RequiresPermissions
    public OutMessage digitalCountList(@RequestParam("orgLevelCode") String orgLevelCode, String orgName) {
        return memDigitalCountService.list(orgLevelCode, orgName);
    }

    /**
     * 档案统计汇总明细
     * @return
     */
    @PostMapping("/digitalCountListDetail")
    @RequiresPermissions
    public OutMessage digitalCountListDetail(@Valid @RequestBody InMessage<DigitalCountDetailDTO> inMessage) {
        return memDigitalCountService.detail(inMessage.getData());
    }

    /**
     * 档案统计汇总列表导出
     * @return
     */
    @PostMapping("/export/digitalCountList")
    @RequiresPermissions
    public void exportDigitalCountList(HttpServletResponse response, @RequestParam("orgLevelCode") String orgLevelCode, String orgName) {
        memDigitalCountService.exportDigitalCountList(response, orgLevelCode, orgName);
    }

    /**
     * 档案统计汇总明细导出
     * @return
     */
    @PostMapping("/export/digitalCountListDetail")
    @RequiresPermissions
    public void exportDigitalCountListDetail(HttpServletResponse response, @RequestBody InMessage<DigitalCountDetailDTO> inMessage) {
        memDigitalCountService.exportDigitalCountListDetail(response, inMessage.getData());
    }

    /**
     * 档案综合统计 列表
     * @return
     */
    @GetMapping("/comprehensiveList")
    @RequiresPermissions
    public OutMessage comprehensiveList(@RequestParam("orgLevelCode") String orgLevelCode, String orgName) {
        return memDigitalCountService.comprehensiveList(orgLevelCode, orgName);
    }
}
