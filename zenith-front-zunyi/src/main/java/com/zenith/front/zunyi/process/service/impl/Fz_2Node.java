package com.zenith.front.zunyi.process.service.impl;

import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.zenith.front.api.mem.IMemDevelopService;
import com.zenith.front.common.constant.ProcessNodeEnum;
import com.zenith.front.model.bean.MemDevelop;
import com.zenith.front.model.bean.User;
import com.zenith.front.model.message.OutMessage;
import com.zenith.front.model.message.Status;
import com.zenith.front.model.bean.MemDevelopProcess;
import com.zenith.front.zunyi.process.service.ProcessEngineService;
import com.zenith.front.zunyi.service.IMemDevelopProcessService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.Map;
import java.util.Objects;

/**
 * 党总支审查
 *
 * <AUTHOR>
 * @since 2025/2/27
 */
@Service
@Slf4j
public class Fz_2Node implements ProcessEngineService {
    @Resource
    private Fz_1Node fz1Node;
    @Resource
    private IMemDevelopService iMemDevelopService;
    @Resource
    private IMemDevelopProcessService iMemDevelopProcessService;

    /**
     * 当前流程节点名
     */
    @Override
    public String getNode() {
        return ProcessNodeEnum.FZ_2.getNode();
    }

    /**
     * 下一步流程
     *
     * @param currentNode 当前节点
     * @param businessId  业务ID
     * @param paramMap    参数
     * @return 操作结果信息
     */
    @Override
    public OutMessage<ProcessNodeEnum> next(String currentNode, String businessId, Map<String, Object> paramMap) {
        // 1、查询业务是否存在
        LambdaQueryWrapper<MemDevelop> wrapper = new LambdaQueryWrapper<MemDevelop>()
                .eq(MemDevelop::getCode, businessId)
                .isNull(MemDevelop::getDeleteTime).last("limit 1");
        MemDevelop entity = iMemDevelopService.getOne(wrapper);
        if (Objects.isNull(entity) || StrUtil.isBlank(entity.getDigitalLotNo())) {
            log.error("业务信息不存在或档案批次唯一码为空，业务ID：{}", businessId);
            return new OutMessage<>(5001, "业务信息不存在或档案批次唯一码为空", null);
        }

        // 2、查询当前流程是否存在
        // "党总支审查"："FZ_2",节点根据组织上下级关系做动态显示，上一节点的支委会有上级党总支时显示当前节点，没有上级党总支时不显示当前节点。
        boolean hasSuperOrgDzz = fz1Node.hasSuperOrgDzz(entity.getOrgCode());
//        if (!hasSuperOrgDzz) {
//            return new OutMessage<>(5001, "当前流程信息不存在", null);
//        }
        String lotNo = entity.getDigitalLotNo();
        LambdaQueryWrapper<MemDevelopProcess> processWrapper = new LambdaQueryWrapper<MemDevelopProcess>()
                .eq(MemDevelopProcess::getProcessNode, currentNode)
                .eq(MemDevelopProcess::getDigitalLotNo, lotNo)
                .isNull(MemDevelopProcess::getApproveTime).last("limit 1");
        MemDevelopProcess currentProcess = iMemDevelopProcessService.getOne(processWrapper);
        if (Objects.isNull(currentProcess)) {
            log.error("当前流程信息不存在，批次唯一码：{}", lotNo);
            return new OutMessage<>(5001, "当前流程信息不存在", null);
        }

        // 3、当前流程节点审核完成信息填写并保存
        User user = currentUser();
        currentProcess.setApproveUser(user.getAccount());
        currentProcess.setApproveTime(new Date());
        currentProcess.setUpdateUser(user.getAccount());
        currentProcess.setUpdateTime(new Date());
        iMemDevelopProcessService.updateById(currentProcess);

        // 4、生成下一步流程信息
        ProcessNodeEnum nextNode = ProcessNodeEnum.FZ_3_1;

        MemDevelopProcess nextProcess = fz1Node.saveProcessInfo(currentProcess, user, nextNode.getNode());
        if (Objects.isNull(nextProcess)) {
            log.error("流程流转失败，业务ID：{}", businessId);
            return new OutMessage<>(5001, "流程流转失败", null);
        }
        //  5、业务更新流程节点信息
//        entity.setProcessNode(nextProcess.getProcessNode());
//        iMemDevelopService.updateById(entity);
        LambdaUpdateWrapper<MemDevelop> updateWrapper = Wrappers.lambdaUpdate();
        updateWrapper.set(MemDevelop::getProcessNode, nextProcess.getProcessNode())
                .eq(MemDevelop::getCode, entity.getCode());
        iMemDevelopService.update(updateWrapper);
        return new OutMessage<>(Status.SUCCESS);
    }


}
