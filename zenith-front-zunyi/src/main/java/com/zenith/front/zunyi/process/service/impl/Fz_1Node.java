package com.zenith.front.zunyi.process.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateField;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.zenith.front.api.mem.IMemDevelopService;
import com.zenith.front.api.org.IOrgService;
import com.zenith.front.common.constant.ProcessNodeEnum;
import com.zenith.front.model.bean.MemDevelop;
import com.zenith.front.model.bean.Org;
import com.zenith.front.model.bean.User;
import com.zenith.front.model.message.OutMessage;
import com.zenith.front.model.message.Status;
import com.zenith.front.model.bean.MemDevelopProcess;
import com.zenith.front.zunyi.model.dto.FileDigitalDto;
import com.zenith.front.zunyi.process.service.ProcessEngineService;
import com.zenith.front.zunyi.service.IMemDevelopProcessService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 支委会审查
 *
 * <AUTHOR>
 * @since 2025/2/27
 */
@Service
@Slf4j
public class Fz_1Node implements ProcessEngineService {
    @Resource
    private IMemDevelopService iMemDevelopService;
    @Resource
    private IMemDevelopProcessService iMemDevelopProcessService;
    @Resource
    private IOrgService iOrgService;

    /**
     * 当前流程节点名
     */
    @Override
    public String getNode() {
        return ProcessNodeEnum.FZ_1.getNode();
    }

    /**
     * 下一步流程
     *
     * @param currentNode 当前节点
     * @param businessId  业务ID
     * @param paramMap    参数
     * @return 操作结果信息
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public OutMessage<ProcessNodeEnum> next(String currentNode, String businessId, Map<String, Object> paramMap) {
        // 1、查询业务是否存在
        LambdaQueryWrapper<MemDevelop> wrapper = new LambdaQueryWrapper<MemDevelop>()
                .eq(MemDevelop::getCode, businessId)
                .eq(MemDevelop::getProcessNode, currentNode)
                .isNull(MemDevelop::getDeleteTime).last("limit 1");
        MemDevelop entity = iMemDevelopService.getOne(wrapper);
        if (Objects.isNull(entity) || StrUtil.isBlank(entity.getDigitalLotNo())) {
            log.error("业务信息不存在或档案批次唯一码为空，业务ID：{}", businessId);
            return new OutMessage<>(5001, "业务信息不存在或档案批次唯一码为空", null);
        }

        // 2、查询当前流程是否存在
        String lotNo = entity.getDigitalLotNo();
        LambdaQueryWrapper<MemDevelopProcess> processWrapper = new LambdaQueryWrapper<MemDevelopProcess>()
                .eq(MemDevelopProcess::getProcessNode, currentNode)
                .eq(MemDevelopProcess::getDigitalLotNo, lotNo)
                .isNull(MemDevelopProcess::getApproveTime).last("limit 1");
        MemDevelopProcess currentProcess = iMemDevelopProcessService.getOne(processWrapper);
        if (Objects.isNull(currentProcess)) {
            log.error("当前流程信息不存在，批次唯一码：{}", lotNo);
            return new OutMessage<>(5001, "当前流程信息不存在", null);
        }

        User user = currentUser();
        // 3、当前流程节点审核完成信息填写并保存
        currentProcess.setApproveUser(user.getAccount());
        currentProcess.setApproveTime(new Date());
        currentProcess.setUpdateUser(user.getAccount());
        currentProcess.setUpdateTime(new Date());
        iMemDevelopProcessService.updateById(currentProcess);
        // 4、生成下一步流程信息
        ProcessNodeEnum nextNode = ProcessNodeEnum.FZ_2;
        // "党总支审查"："FZ_2",节点根据组织上下级关系做动态显示，上一节点的支委会有上级党总支时显示当前节点，没有上级党总支时不显示当前节点。
        boolean hasSuperOrgDzz = hasSuperOrgDzz(entity.getOrgCode());
        if (!hasSuperOrgDzz) {
            nextNode = ProcessNodeEnum.FZ_3_1;
        }

        MemDevelopProcess nextProcess = saveProcessInfo(currentProcess, user, nextNode.getNode());
        if (Objects.isNull(nextProcess)) {
            log.error("流程流转失败，业务ID：{}", businessId);
            return new OutMessage<>(5001, "流程流转失败", null);
        }

        //  5、业务更新流程节点信息
//        entity.setProcessNode(nextProcess.getProcessNode());
        LambdaUpdateWrapper<MemDevelop> updateWrapper = Wrappers.lambdaUpdate();
        updateWrapper.set(MemDevelop::getProcessNode, nextProcess.getProcessNode())
                .eq(MemDevelop::getCode, entity.getCode());
        iMemDevelopService.update(updateWrapper);
        return new OutMessage<>(Status.SUCCESS, nextNode);
    }

    /**
     * 判断是否有上级党总支
     *
     * @param orgCode 党组织code
     */
    public boolean hasSuperOrgDzz(String orgCode) {
        List<String> orgCodeList = iOrgService.findAllParentOrg(orgCode).stream().map(Org::getCode).collect(Collectors.toList());
        if (CollUtil.isEmpty(orgCodeList)) {
            return false;
        }
        List<Org> orgList = iOrgService.findByOrgCodeList(orgCodeList);
        if (CollUtil.isNotEmpty(orgList)) {
            Optional<Org> first = orgList.stream().filter(e -> StrUtil.equalsAny(e.getD01Code(), "62", "921")).findFirst();
            return first.isPresent();
        }
        return false;
    }


    /**
     * 存储流程信息表
     */
    public MemDevelopProcess saveProcessInfo(MemDevelopProcess fProcess, User user, String processNode) {
        MemDevelopProcess process = new MemDevelopProcess();
        process.setCode(IdUtil.simpleUUID());
        process.setProcessNode(processNode);
        process.setDigitalLotNo(fProcess.getDigitalLotNo());
        process.setd08Code(fProcess.getd08Code());
        process.setPreviousCode(fProcess.getCode());
        process.setCreateTime(new Date());
        process.setCreateUser(Objects.nonNull(user) ? user.getAccount() : "定时任务生成");
        //FZ_3_1 一月内进行预审 -> FZ_3_2 预审时间少于十天
        if (StrUtil.equalsAny(processNode, ProcessNodeEnum.FZ_3_1.getNode())) {
            process.setExtendStarTime(process.getCreateTime());
            process.setExtendEndTime(DateUtil.offsetDay(DateUtil.offset(process.getExtendStarTime(), DateField.MONTH, 1), -10));
        }
        //FZ_5_1 一月内进行讨论人员 -> FZ_5_2 讨论时间少于五天
        if (StrUtil.equalsAny(processNode, ProcessNodeEnum.FZ_5_1.getNode())) {
            process.setExtendStarTime(process.getCreateTime());
            process.setExtendEndTime(DateUtil.offsetDay(DateUtil.offset(process.getExtendStarTime(), DateField.MONTH, 1), -5));
        }
        //FZ_6_1 三个月内审批人员 -> FZ_6_2 审批时间少于十天
        if (StrUtil.equalsAny(processNode, ProcessNodeEnum.FZ_6_1.getNode())) {
            process.setExtendStarTime(process.getCreateTime());
            process.setExtendEndTime(DateUtil.offsetDay(DateUtil.offset(process.getExtendStarTime(), DateField.MONTH, 3), -10));
        }
        boolean save = iMemDevelopProcessService.save(process);
        return save ? process : null;
    }

}
