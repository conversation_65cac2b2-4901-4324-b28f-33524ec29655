package com.zenith.front.zunyi.controller;

import com.zenith.front.api.develop.IDevelopStepLogService;
import com.zenith.front.api.mem.IMemDevelopService;
import com.zenith.front.common.annotation.RequiresPermissions;
import com.zenith.front.core.kit.ExcelUtil;
import com.zenith.front.model.constant.UserConstant;
import com.zenith.front.model.message.InMessage;
import com.zenith.front.model.message.OutMessage;
import com.zenith.front.model.message.Status;
import com.zenith.front.model.validate.annotation.Validate;
import com.zenith.front.model.validate.group.Common1Group;
import com.zenith.front.model.validate.group.Common2Group;
import com.zenith.front.zunyi.model.dto.MemListNewDTO;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;

/**
 * <AUTHOR>
 * @version 1.0
 * @Description: 发展党员
 * @date 2019/5/7 16:44
 */
@RestController
@RequestMapping("/zunyi/data/develop")
public class DevelopNewDataController {
    @Resource
    private IMemDevelopService memDevelopService;
    @Resource
    private IDevelopStepLogService iDevelopStepLogService;


    /**
     * 导出发展党员数据
     */
    @Validate(group = {Common1Group.class, Common2Group.class})
    @RequiresPermissions
    @PostMapping("/exportData")
    public void exportData(@RequestBody InMessage<MemListNewDTO> inMessage, HttpServletResponse response) {
        MemListNewDTO memListDTO = inMessage.getData();
        String memOrgCode = memListDTO.getMemOrgCode();
        String currManOrgCode = UserConstant.USER_CONTEXT.get().getUserRolePermission().getOrgCode();
        if (!memOrgCode.startsWith(currManOrgCode)) {
            ExcelUtil.exportErrorExcel(response, new OutMessage<>(Status.PERMISSION_DENIED));
            return;
        }
        memDevelopService.exportDevelopData(memListDTO);
    }


}
