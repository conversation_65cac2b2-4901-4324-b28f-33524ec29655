package com.zenith.front.zunyi.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateField;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUnit;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.thread.ThreadUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.IdcardUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.extra.pinyin.PinyinUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Constants;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.zenith.front.api.develop.IDevelopStepLogService;
import com.zenith.front.api.mem.IMemDevelopOperationService;
import com.zenith.front.api.mem.IMemDevelopService;
import com.zenith.front.api.org.IOrgService;
import com.zenith.front.api.report.IStatisticsYearService;
import com.zenith.front.api.sync.ISyncMemService;
import com.zenith.front.common.constant.CommonConstant;
import com.zenith.front.common.constant.ProcessNodeEnum;
import com.zenith.front.common.kit.CodeUtil;
import com.zenith.front.common.kit.StrKit;
import com.zenith.front.common.mybatisplus.WrapperUtil;
import com.zenith.front.core.cache.CacheUtils;
import com.zenith.front.core.kit.ExcelImportUtil;
import com.zenith.front.core.service.mem.MemDevelopServiceImpl;
import com.zenith.front.core.service.mem.MemServiceImpl;
import com.zenith.front.dao.mapper.mem.MemDevelopMapper;
import com.zenith.front.dao.mapper.mem.MemDigitalMapper;
import com.zenith.front.dao.mapper.mem.MemMapper;
import com.zenith.front.dao.mapper.mem.MemRewardMapper;
import com.zenith.front.framework.file.core.MinioTemplate2;
import com.zenith.front.model.bean.*;
import com.zenith.front.model.constant.UserConstant;
import com.zenith.front.model.custom.Record;
import com.zenith.front.model.dto.ImportDevelopDTO;
import com.zenith.front.model.dto.ImportExcelDevelopDTO;
import com.zenith.front.model.dto.MemDevelopDTO;
import com.zenith.front.model.message.OutMessage;
import com.zenith.front.model.message.Status;
import com.zenith.front.model.vo.BetweenReportDateVo;
import com.zenith.front.model.vo.CheckIdCardDuplicateVO;
import com.zenith.front.model.vo.MemDevelopVo;
import com.zenith.front.zunyi.model.dto.*;
import com.zenith.front.zunyi.model.vo.FileDigitalVo;
import com.zenith.front.zunyi.model.vo.MemDevelopNewVo;
import com.zenith.front.zunyi.process.ProcessTool;
import com.zenith.front.zunyi.service.IMemDevelopNewService;
import com.zenith.front.zunyi.service.IMemDevelopProcessService;
import com.zenith.front.zunyi.service.IMemDigitalOperationLogService;
import com.zenith.front.zunyi.service.IMemDigitalService;
import org.apache.poi.xssf.usermodel.XSSFCell;
import org.apache.poi.xssf.usermodel.XSSFRow;
import org.apache.poi.xssf.usermodel.XSSFSheet;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.ResourceUtils;

import javax.annotation.Resource;
import java.io.FileInputStream;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.util.*;
import java.util.stream.Collectors;

import static com.zenith.front.model.constant.UserConstant.USER_CONTEXT;

/**
 * 入党申请人
 *
 * <AUTHOR>
 * @since 2025/2/16
 */
@Service
public class MemDevelopNewServiceImpl extends ServiceImpl<MemDevelopMapper, MemDevelop> implements IMemDevelopNewService {

    @Resource
    private IMemDevelopService iMemDevelopService;
    @Resource
    private MemDevelopServiceImpl memDevelopService;
    @Resource
    private MemServiceImpl memService;
    @Resource
    private IStatisticsYearService iStatisticsYearService;
    @Resource
    private ISyncMemService iSyncMemService;
    @Resource
    IDevelopStepLogService developStepLogService;
    @Resource
    IMemDevelopOperationService operationService;
    @Resource
    IOrgService orgService;
    @Resource
    private IMemDigitalService iMemDigitalService;
    @Resource
    private MemDigitalMapper memDigitalMapper;
    @Resource
    private IMemDevelopProcessService iMemDevelopProcessService;
    @Resource
    private IMemDigitalOperationLogService iMemDigitalOperationLogService;
    @Resource
    private MemRewardMapper rewardMapper;
    @Resource
    private MemMapper memMapper;
    @Resource
    private MinioTemplate2 minioTemplate2;

    /**
     * 获取发展党员列表
     */
    @Override
    public OutMessage<?> getList(MemListNewDTO memListDTO) {
        Integer pageNum = memListDTO.getPageNum();
        Integer pageSize = memListDTO.getPageSize();
        LambdaQueryWrapper<MemDevelop> wrapper = memDevelopService.getListCondition(memListDTO);
        // 流程节点
        List<String> processNodeList = memListDTO.getProcessNode();
        wrapper.in(CollUtil.isNotEmpty(processNodeList), MemDevelop::getProcessNode, processNodeList);
        // 指定解密字段，减少解密频率
        WrapperUtil.existsEncrypt(wrapper, MemDevelop.class, "name", "idcard", "phone");
        Page<MemDevelop> page = iMemDevelopService.page(new Page<>(pageNum, pageSize), wrapper);
        // 用户是否为基层党委人员
        boolean jcdw = iMemDigitalService.isJCDW();
        // 用户是否为党总支人员
        boolean dzz = iMemDigitalService.isDZZ();
        // 用户是否为县级党委
        boolean xjdw = iMemDigitalService.isXJDW();
        page.getRecords().forEach(memDevelop -> {
            memDevelop.setOrgName(CacheUtils.getOrgName(memDevelop.getOrgCode()));
            memDevelop.setMemOrgCode(memDevelop.getDevelopOrgCode());
            String d08Code = memDevelop.getD08Code();
            //在入党申请人查询界面加一个图标，申请入党时间与当前时间达到3个月的显示出来，鼠标悬停图标上显示：该入党申请人递交入党申请书已达3个月，请及时研究
            Date applyDate = memDevelop.getApplyDate();
            if (StrUtil.equals(d08Code, "5") && Objects.nonNull(applyDate) && com.zenith.front.common.kit.DateUtil.dateToLocaleDate(applyDate).plusMonths(3).isBefore(LocalDate.now())) {
                memDevelop.setJoinMessage("该入党申请人递交入党申请书已达3个月，请及时研究。");
            }
            //在积极分子查询界面加一个图标，成为积极分子时间与当前时间达到1年的显示出来，鼠标悬停图标上显示：该入党申请人培养期已满1年，请及时研究。
            Date activeDate = memDevelop.getActiveDate();
            if (StrUtil.equals(d08Code, "4") && Objects.nonNull(activeDate) && com.zenith.front.common.kit.DateUtil.dateToLocaleDate(activeDate).plusYears(1).isBefore(LocalDate.now())) {
                memDevelop.setActivistMessage("该入党申请人培养期已满1年，请及时研究。");
            }
            //当前流程节点为超半月未按时考察时，需要返回lastCode参数
            String processNodeCurrent = memDevelop.getProcessNode();
            String digitalLotNo = memDevelop.getDigitalLotNo();
            if (StrUtil.isNotEmpty(processNodeCurrent) && StrUtil.isNotEmpty(digitalLotNo)) {
                MemDevelopProcess memDevelopProcess = iMemDevelopProcessService.findByProcessNodeLotNo(processNodeCurrent, digitalLotNo);
                if (Objects.nonNull(memDevelopProcess)) {
                    memDevelop.setIsAutoLock(memDevelopProcess.getIsAutoLock());
                }
                if (StrUtil.equals(processNodeCurrent, ProcessNodeEnum.JJ_4.getNode())) {
                    MemDevelopProcess processLast = this.getLastMemDevelopProcess(processNodeCurrent, digitalLotNo);
                    if (ObjectUtil.isNotNull(processLast)) {
                        memDevelop.setLastCode(processLast.getProcessNode());
                        memDevelop.setLastName(ProcessNodeEnum.find(processLast.getProcessNode()).getNodeName());
                    }
                }
            }

            // 根据流程节点来确定前端是否显示审批按钮
            if (StrUtil.isNotBlank(memDevelop.getProcessNode())) {
                // 基层党委
                if (StrUtil.equalsAny(memDevelop.getProcessNode(),
//                        ProcessNodeEnum.JJ_6.getNode(),
                        ProcessNodeEnum.FZ_3_1.getNode(),
                        ProcessNodeEnum.FZ_3_2.getNode(),
                        ProcessNodeEnum.FZ_3_3.getNode(),
                        ProcessNodeEnum.FZ_6_1.getNode(),
                        ProcessNodeEnum.FZ_6_2.getNode(),
                        ProcessNodeEnum.FZ_6_3.getNode(),
                        ProcessNodeEnum.FZ_6_4.getNode()
                )) {
                    memDevelop.setApprove(jcdw);
                }
                // 党总支
                if (StrUtil.equalsAny(memDevelop.getProcessNode(), ProcessNodeEnum.FZ_2.getNode())) {
                    memDevelop.setApprove(dzz);
                }
                // 县级党委
                if (StrUtil.equalsAny(memDevelop.getProcessNode(), ProcessNodeEnum.FZ_4.getNode())) {
                    memDevelop.setApprove(xjdw);
                }
            }
        });
        return new OutMessage<>(Status.SUCCESS, page);
    }

    /**
     * 发展党员每个流程节点人数
     */
    @Override
    public OutMessage<?> count(MemListNewDTO memListDTO) {
        LambdaQueryWrapper<MemDevelop> wrapper = memDevelopService.getListCondition(memListDTO);
        Map<String, Object> paramValue = wrapper.getParamNameValuePairs();
        String sqlStr = StrUtil.sub(wrapper.getTargetSql(), 0, wrapper.getTargetSql().indexOf("ORDER BY"));
        String[] parts = sqlStr.split("\\?");

        // 处理查询语句
        StringBuilder builder = new StringBuilder();
        for (int i = 1; i <= parts.length; i++) {
            Object o = paramValue.get(Constants.WRAPPER_PARAM + i);
            if (Objects.isNull(o)) {
                builder.append(parts[i - 1]);
            } else {
                String value = o.toString();
                if (o instanceof String) {
                    value = "'" + o + "' ";
                }
                builder.append(parts[i - 1]).append(value);
            }
        }
        QueryWrapper<MemDevelop> query = new QueryWrapper<MemDevelop>()
                .select("process_node", "count(1) as total")
                .apply(builder.toString())
                .isNotNull("process_node")
                .groupBy("process_node");
        Map<String, Object> result = new HashMap<>(100);
        List<Map<String, Object>> selectMaps = iMemDevelopService.getBaseMapper().selectMaps(query);
        if (CollUtil.isNotEmpty(selectMaps)) {
            result = selectMaps.stream().collect(Collectors.toMap(e -> e.get("process_node").toString(), e -> e.get("total"), (a, b) -> b));
        }
        return new OutMessage<>(Status.SUCCESS, result);
    }

    /**
     * 新增发展党员
     */
    @Override
    @Transactional
    public OutMessage<?> addDevelop(MemDevelopNewDTO memDevelopNewDTO) throws Exception {
        memDevelopNewDTO.setCode(StrKit.getRandomUUID());
        memDevelopNewDTO.setDigitalLotNo(StrKit.getRandomUUID());
        memDevelopNewDTO.setProcessNode(ProcessNodeEnum.RD_1.getNode());
        MemDevelopDTO developDTO = new MemDevelopDTO();
        BeanUtils.copyProperties(memDevelopNewDTO, developDTO);
        OutMessage<?> outMessage = iMemDevelopService.addDevelop(developDTO);
        if (!ObjectUtil.equals(outMessage.getCode(), Status.SUCCESS.getCode())) {
            return outMessage;
        }
        // 保存档案材料信息
        DevelopStepLogNewDTO logNewDTO = new DevelopStepLogNewDTO();
        logNewDTO.setFilesList(memDevelopNewDTO.getFilesList());
        logNewDTO.setProcessNode(memDevelopNewDTO.getProcessNode());
        logNewDTO.setOprationUser(memDevelopNewDTO.getOprationUser());
        logNewDTO.setDigitalLotNo(memDevelopNewDTO.getDigitalLotNo());
        logNewDTO.setD08Code(memDevelopNewDTO.getD08Code());
        boolean save = processFileDigital(logNewDTO, UserConstant.USER_CONTEXT.get().getUser());
        if (!save) {
            return new OutMessage<>(Status.FAIL);
        }
        return ProcessTool.next(ProcessNodeEnum.RD_1.getNode(), memDevelopNewDTO.getCode(), null);
    }

    /**
     * 上传数字档案
     */
    @Override
    public OutMessage<?> uploadFileDigital(UploadFileDigitalDto digitalDto, User user) {
        String processNode = digitalDto.getProcessNode();
        String memDevelopCode = digitalDto.getCode();
        MemDevelop memDevelop = memDevelopService.findByCode(memDevelopCode);
        if (ObjectUtil.isNull(memDevelop)) {
            return new OutMessage<>(Status.OBJEC_NOT_EXIST);
        }
        // 如果档案唯一码为空则生成个，系统上线前正式党员没有这个字段数据，那么在上传档案时主动生成
        if (StrUtil.isBlank(memDevelop.getDigitalLotNo())) {
            memDevelop.setDigitalLotNo(IdUtil.simpleUUID());
            // 更新档案唯一码
            LambdaUpdateWrapper<MemDevelop> updateWrapper = Wrappers.lambdaUpdate();
            updateWrapper.set(MemDevelop::getDigitalLotNo, memDevelop.getDigitalLotNo())
                    .isNull(MemDevelop::getDigitalLotNo)
                    .eq(MemDevelop::getCode, memDevelop.getCode());
            boolean a = memDevelopService.update(updateWrapper);
            if (!a) {
                return new OutMessage<>(5001, "档案唯一码更新失败，请重新上传！", null);
            }
            digitalDto.setDigitalLotNo(memDevelop.getDigitalLotNo());
        }

        // 进入下一步流程节点
        Map<String, Object> paramMap = MapUtil.newHashMap();
        paramMap.put("lastCode", digitalDto.getLastCode());
        paramMap.put("nextCode", digitalDto.getNextCode());
        paramMap.put("filesList", digitalDto.getFilesList());
        paramMap.put("nextProcessNode", digitalDto.getNextProcessNode());
        OutMessage<ProcessNodeEnum> next = ProcessTool.next(processNode, memDevelopCode, paramMap);
        if (Objects.isNull(next) || !Objects.equals(next.getCode(), Status.SUCCESS.getCode())) {
            return next;
        }
        DevelopStepLogNewDTO stepLogNewDTO = new DevelopStepLogNewDTO();
        BeanUtils.copyProperties(digitalDto, stepLogNewDTO);
        stepLogNewDTO.setD08Code(memDevelop.getD08Code());
        stepLogNewDTO.setDigitalLotNo(memDevelop.getDigitalLotNo());
        //发展党员数字档案、数字档案操作日志
        boolean b = processFileDigital(stepLogNewDTO, user);

        if (b) {
            // 可以异步执行
            ThreadUtil.execAsync(() -> {
                // 档案完整度执行
                Integer integrality = iMemDigitalService.getIntegrality(stepLogNewDTO.getDigitalLotNo());
                LambdaUpdateWrapper<MemDevelop> updateWrapper = Wrappers.lambdaUpdate();
                updateWrapper.set(MemDevelop::getIsIntegrality, integrality)
                        .eq(MemDevelop::getCode, memDevelop.getCode());
                memDevelopService.update(updateWrapper);
            });
        }

        return next;
    }

    /**
     * 入党申请人确定为积极分子
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public OutMessage<?> becomeActivist(DevelopStepLogNewDTO stepLogDTO, User user) {
        // 流程节点 "RD_4", "满足积极份子"
        String processNode = ProcessNodeEnum.RD_4.getNode();
        BetweenReportDateVo betweenReportDate = iStatisticsYearService.isBeforeReportDate(stepLogDTO.getActiveDate(), "3");
        if (!betweenReportDate.getIsBetween()) {
            return new OutMessage<String>(Status.NOT_IN_BETWEEN_DEFAULT_DATE).format("确定积极分子时间", betweenReportDate.getMessage());
        }
        String memCode = stepLogDTO.getMemCode();
        MemDevelop memDevelop = memDevelopService.findByCode(memCode);
        OutMessage<?> message = checkDevelopData(memDevelop, stepLogDTO);
        if (message != null) {
            return message;
        }
        String d08Code = stepLogDTO.getD08Code();
        if (!CommonConstant.FIVE.equals(d08Code)) {
            // 人员类型不对
            return new OutMessage<>(Status.D08_CODE_ERROR);
        }
        stepLogDTO.setProcessNode(processNode);
        memDevelop.setDigitalLotNo(stepLogDTO.getDigitalLotNo());
        memDevelop.setD08Code(CommonConstant.FOUR);
        memDevelop.setD08Name("积极分子");
        memDevelop.setActiveDate(stepLogDTO.getActiveDate());

        // 进入下一流程
        Map<String, Object> paramMap = MapUtil.newHashMap();
        paramMap.put("active_date", memDevelop.getActiveDate());
        OutMessage<ProcessNodeEnum> next = ProcessTool.next(ProcessNodeEnum.RD_4.getNode(), memCode, paramMap);
        if (Objects.nonNull(next) && Objects.equals(next.getCode(), Status.SUCCESS.getCode())) {
            // 更新基础表信息
            memDevelop.setProcessNode(null);
            getOutMessage(stepLogDTO, memDevelop);
            // 发展党员数字档案
            boolean b = processFileDigital(stepLogDTO, user);
            if (b) {
                // 可以异步执行
                ThreadUtil.execAsync(() -> {
                    // 档案完整度执行
                    Integer integrality = iMemDigitalService.getIntegrality(stepLogDTO.getDigitalLotNo());
                    LambdaUpdateWrapper<MemDevelop> updateWrapper = Wrappers.lambdaUpdate();
                    updateWrapper.set(MemDevelop::getIsIntegrality, integrality)
                            .eq(MemDevelop::getCode, memDevelop.getCode());
                    memDevelopService.update(updateWrapper);
                });
            }
        }
        return next;
    }


    /**
     * 积极分子确定为发展对象
     */
    @Override
    @Transactional
    public OutMessage<?> becomeDevelopObject(DevelopStepLogNewDTO stepLogDTO, User user) {
        // 流程节点 "JJ_7", "发展对象阶段"
        String processNode = ProcessNodeEnum.JJ_7.getNode();
        BetweenReportDateVo betweenReportDate = iStatisticsYearService.isBeforeReportDate(stepLogDTO.getObjectDate(), "3");
        if (!betweenReportDate.getIsBetween()) {
            return new OutMessage<String>(Status.NOT_IN_BETWEEN_DEFAULT_DATE).format("确定发展对象时间", betweenReportDate.getMessage());
        }
        String memCode = stepLogDTO.getMemCode();
        MemDevelop memDevelop = memDevelopService.findByCode(memCode);
        OutMessage<?> message = checkDevelopData(memDevelop, stepLogDTO);
        if (message != null) {
            return message;
        }
        String d08Code = stepLogDTO.getD08Code();
        if (!CommonConstant.FOUR.equals(d08Code)) {
            // 人员类型不对
            return new OutMessage<>(Status.D08_CODE_ERROR);
        }
        stepLogDTO.setProcessNode(processNode);
        memDevelop.setDigitalLotNo(stepLogDTO.getDigitalLotNo());
        memDevelop.setD08Code(CommonConstant.THREE);
        memDevelop.setD08Name("发展对象");
        memDevelop.setObjectDate(stepLogDTO.getObjectDate());
        memDevelop.setInstructions(stepLogDTO.getInstructions());

        // 进入下一流程
        OutMessage<ProcessNodeEnum> next = ProcessTool.next(ProcessNodeEnum.JJ_7.getNode(), memCode, null);
        if (Objects.nonNull(next) && Objects.equals(next.getCode(), Status.SUCCESS.getCode())) {
            // 更新基础表信息
            memDevelop.setProcessNode(null);
            getOutMessage(stepLogDTO, memDevelop);
            // 发展党员数字档案
            boolean b = processFileDigital(stepLogDTO, user);
            if (b) {
                // 可以异步执行
                ThreadUtil.execAsync(() -> {
                    // 档案完整度执行
                    Integer integrality = iMemDigitalService.getIntegrality(stepLogDTO.getDigitalLotNo());
                    LambdaUpdateWrapper<MemDevelop> updateWrapper = Wrappers.lambdaUpdate();
                    updateWrapper.set(MemDevelop::getIsIntegrality, integrality)
                            .eq(MemDevelop::getCode, memDevelop.getCode());
                    memDevelopService.update(updateWrapper);
                });
            }
        }
        return next;
    }

    @Override
    public OutMessage becomePreliminary(DevelopStepLogNewDTO stepLogDTO, boolean hasSpecialDevelopment) throws Exception {
        String d08Code = stepLogDTO.getD08Code();
        if (!hasSpecialDevelopment) {
            if (!CommonConstant.THREE.equals(d08Code)) {
                // 人员类型不对
                return new OutMessage(Status.D08_CODE_ERROR);
            }
        }
        //时间判断
        if (ObjectUtil.isNull(stepLogDTO.getShortTrainingEndTime()) && ObjectUtil.isNull(stepLogDTO.getShortTrainingBeginTime()) && ObjectUtil.isNull(stepLogDTO.getReviewConclusionTime()) && ObjectUtil.isNull(stepLogDTO.getObjectDate())) {
            return new OutMessage(Status.TIME_NULL_YBDY);
        } else if (ObjectUtil.isNotNull(stepLogDTO.getShortTrainingBeginTime()) && ObjectUtil.isNotNull(stepLogDTO.getShortTrainingEndTime()) && ObjectUtil.isNotNull(stepLogDTO.getReviewConclusionTime())) {
            Date shortTrainingBeginTime = stepLogDTO.getShortTrainingBeginTime();
            Date shortTrainingEndTime = stepLogDTO.getShortTrainingEndTime();
            SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            Date reviewConclusionTime = stepLogDTO.getReviewConclusionTime();
            Date objectDate = stepLogDTO.getObjectDate();
            Date topreJoinOrgDate = stepLogDTO.getTopreJoinOrgDate();
            Calendar c = Calendar.getInstance();
            c.setTime(shortTrainingEndTime);
            c.add(Calendar.DATE, 1);
            Date time = c.getTime();
            if (shortTrainingEndTime.before(shortTrainingBeginTime)) {
                return new OutMessage(Status.TIME_NULL_YBDY);
            }
            //相差天数
            long between = DateUtil.between(time, shortTrainingBeginTime, DateUnit.DAY);
            if (between < 3) {
                return new OutMessage(Status.TIME_NULL_YBDY);
            }

            if (!shortTrainingBeginTime.after(objectDate) || !shortTrainingEndTime.after(objectDate) || !reviewConclusionTime.after(objectDate)) {
                return new OutMessage(Status.TIME_ERROR_YBDY);
            }
            if (!topreJoinOrgDate.after(shortTrainingBeginTime) && !topreJoinOrgDate.after(shortTrainingEndTime) && !topreJoinOrgDate.after(reviewConclusionTime)) {
                return new OutMessage(Status.TIME_ERROR__TWO_YBDY);
            }


        }
        return this.becomePreliminDta(stepLogDTO, hasSpecialDevelopment);
    }

    @Override
    public OutMessage<MemDevelopNewVo> findByCodeOut(String code) {
        OutMessage byCodeOut = iMemDevelopService.findByCodeOut(code);
        if (Objects.nonNull(byCodeOut) && Objects.equals(byCodeOut.getCode(), Status.SUCCESS.getCode())) {
            MemDevelopVo vo = (MemDevelopVo) byCodeOut.getData();
            //获取支部党员大会讨论通过时间 ----查询 "支部大会讨论"流程中  最新的审批时间
            List<String> strings = Arrays.asList(ProcessNodeEnum.FZ_5_1.getNode(), ProcessNodeEnum.FZ_5_2.getNode(), ProcessNodeEnum.FZ_5_3.getNode());
            LambdaQueryWrapper<MemDevelopProcess> processWrapper = new LambdaQueryWrapper<MemDevelopProcess>()
                    .in(MemDevelopProcess::getProcessNode, strings)
                    .eq(MemDevelopProcess::getDigitalLotNo, vo.getDigitalLotNo())
                    .isNotNull(MemDevelopProcess::getApproveTime)
                    .orderByDesc(MemDevelopProcess::getApproveTime)
                    .last("limit 1");
            MemDevelopProcess currentProcess = iMemDevelopProcessService.getOne(processWrapper);
            if (ObjectUtil.isNotNull(currentProcess)) {
                vo.setTopreJoinOrgDate(currentProcess.getApproveTime());
            }

            MemDevelopNewVo newVo = new MemDevelopNewVo();
            BeanUtil.copyProperties(vo, newVo);

            Map<String, String> d89Map = com.zenith.front.core.kit.CollectionUtil.listRecordToMap(CacheUtils.getDic("dict_d89"), "key", "name");
            Map<String, String> d107Map = com.zenith.front.core.kit.CollectionUtil.listRecordToMap(CacheUtils.getDic("dict_d107"), "key", "name");
            String politicsCode = vo.getPoliticsCode();
            if (StrUtil.isNotBlank(politicsCode)) {
                List<String> stringList = Arrays.stream(politicsCode.split(CommonConstant.DOU_HAO_STRING)).map(d89Map::get).collect(Collectors.toList());
                newVo.setPoliticsName(CollUtil.join(stringList, CommonConstant.DOU_HAO_STRING));
            }
            newVo.setJobNatureCodeName(d107Map.get(vo.getJobNatureCode()));
            // 获取历史档案
            String digitalLotNo = vo.getDigitalLotNo();
            List<MemDigital> memDigitalList = this.iMemDigitalService.getUploadDigitalList(digitalLotNo);
            // 综合性政审报告和相关材料、参加短期集中培训的结业证书 判断是否有相关档案附件, 支委会审查阶段才返回未上传的
            Set<String> notExistD222Code = Objects.equals(newVo.getProcessNode(), ProcessNodeEnum.FZ_1.getNode())
                    && Objects.equals(newVo.getD08Code(), CommonConstant.THREE) ? CollUtil.set(false, "304", "305") : CollUtil.newHashSet();
            if (CollUtil.isNotEmpty(memDigitalList)) {
                List<FileDigitalVo> filesList = CollUtil.newArrayList();
                Map<String, List<MemDigital>> groupedByD222Code = memDigitalList.stream().collect(Collectors.groupingBy(MemDigital::getd222Code));
                for (String d222Code : groupedByD222Code.keySet()) {
                    // 过滤存在的指定档案
                    notExistD222Code.remove(d222Code);

                    List<MemDigital> digitalList = groupedByD222Code.get(d222Code);
                    if (CollUtil.isNotEmpty(digitalList)) {
                        MemDigital memDigital = digitalList.get(0);
                        FileDigitalVo fileDigitalVo = new FileDigitalVo();
                        fileDigitalVo.setD222Code(memDigital.getd222Code());
                        fileDigitalVo.setD222Name(memDigital.getd222Name());
                        List<FileDigitalVo> childFilesList = CollUtil.newArrayList();
                        digitalList.forEach(digital -> {
                            FileDigitalVo sub = new FileDigitalVo();
                            sub.setName(digital.getName());
                            // 设置分享地址
                            sub.setPath(minioTemplate2.shareLink(digital.getPath()));
                            childFilesList.add(sub);
                        });
                        fileDigitalVo.setFilesList(childFilesList);
                        filesList.add(fileDigitalVo);
                    }
                }
                newVo.setFilesList(filesList);
            }
            // 需要 再次上传的档案
            newVo.setNotExistD222Code(notExistD222Code);
            return new OutMessage<>(Status.SUCCESS, newVo);
        }
        return byCodeOut;
    }

    /**
     * @param stepLogDTO
     * @param hasSpecialDevelopment false:发展对象确定为预备党员  true:火线入党
     * @return
     * @throws Exception
     */
    public OutMessage becomePreliminDta(DevelopStepLogNewDTO stepLogDTO, boolean hasSpecialDevelopment) throws Exception {
        OutMessage s = this.hasStaffOrganization(stepLogDTO, stepLogDTO.getTopreIntroductionMem());
        if (s != null) {
            return s;
        }
        BetweenReportDateVo betweenReportDate = iStatisticsYearService.isBeforeReportDate(stepLogDTO.getTopreJoinOrgDate(), "4");
        if (!betweenReportDate.getIsBetween()) {
            return new OutMessage<String>(Status.NOT_IN_BETWEEN_DEFAULT_DATE).format("支部党员大会讨论通过时间", betweenReportDate.getMessage());
        }
        String memCode = stepLogDTO.getMemCode();
        MemDevelop memDevelop = memDevelopService.findByCode(memCode);
        OutMessage<?> message = checkDevelopData(memDevelop, stepLogDTO, hasSpecialDevelopment);
        if (message != null) {
            return message;
        }
        if ("121".equals(stepLogDTO.getJoinOrgCode())) {
            boolean b = com.zenith.front.common.kit.DateUtil.dateCompare(memDevelop.getBirthday(), stepLogDTO.getTopreCommitteeDate(), 27);
            if (b) {
                return new OutMessage<>(Status.LEAGUE_MEMBERS_IN_DIRECTING);
            }
        }

        // 当前流程节点
        final String processNode = memDevelop.getProcessNode();
        final String digitalLotNo = memDevelop.getDigitalLotNo();


        if (developStepLogService.hasZsfzQkD154Code(memDevelop.getD09Code()) && StrUtil.isEmpty(memDevelop.getD154Code())) {
            return new OutMessage<>(500, "知识分子情况不能为空,请先完善该信息！", null);
        }
        String d11Code = stepLogDTO.getD11Code();
        if (!CommonConstant.ONE.equals(d11Code)) {
            // 进入支部类型错误
            return new OutMessage(Status.D11_CODE_ERROR);
        }

        String joinOrgCode = stepLogDTO.getJoinOrgCode();
        if (CommonConstant.ONE.startsWith(joinOrgCode) || CommonConstant.NINE.startsWith(joinOrgCode)) {
            // 进入支部类型错误
            return new OutMessage(Status.JOIN_ORG_CODE_ERROR);
        }
        // 加入中共组织的类别，如果是共青团员必须选择团员推优
        if (StrUtil.contains(memDevelop.getPoliticsCode(), "03") && !StrUtil.equals(joinOrgCode, "1111")) {
            return new OutMessage<>(500, "加入中共组织的类别错误，团员必须推优！", null);
        }
        BeanUtils.copyProperties(memDevelop, stepLogDTO, "code", "esId", "orgCode", "orgName", "d11Code", "d11Name", "joinOrgName");
        DevelopStepLog developStepLog = new DevelopStepLog();
        BeanUtils.copyProperties(stepLogDTO, developStepLog);
        developStepLog.setD89Code(memDevelop.getPoliticsCode());
        developStepLog.setD89Name(memDevelop.getPoliticsName());
        developStepLog.setD194Code(memDevelop.getD194Code());
        developStepLog.setD194Name(memDevelop.getD194Name());
        developStepLog.setD195Code(memDevelop.getD195Code());
        developStepLog.setD195Name(memDevelop.getD195Name());
        // TODO: 2022/7/15  学历为研究生以上（博士研究生 111，硕士研究生 112） 或 专业技术职务 为副高以上（正高级 11）
        developStepLog.setHasHighKnowledge(StrUtil.equalsAny(memDevelop.getD07Code(), "11", "111", "112") || StrUtil.equalsAny(memDevelop.getD19Code(), "11", "12") ? 1 : 0);
        // TODO: 2022/7/19 高层次人才是博士研究生学历或正高职称
        developStepLog.setHasHighLevelTalents(StrUtil.equalsAny(memDevelop.getD07Code(), "111") || StrUtil.equalsAny(memDevelop.getD19Code(), "11") ? 1 : 0);
        //是否产业工厂
        developStepLog.setHasWorker(developStepLogService.hasWorker(stepLogDTO.getD09Code(), stepLogDTO.getHasWorker()));

        String code = memDevelop.getCode();
        MemDevelopOperation operation = new MemDevelopOperation();
        BeanUtils.copyProperties(stepLogDTO, operation, "code");
        operation.setDevelopCode(stepLogDTO.getMemCode());
        operation.setCreateTime(new Date());
        BeanUtils.copyProperties(operation, memDevelop);
        operationService.save(operation);
        memDevelop.setCode(code);

        memDevelop.setD08Code(CommonConstant.TWO);
        memDevelop.setD08Name("预备党员");
        memDevelop.setD09Code(stepLogDTO.getD09Code());
        memDevelop.setD09Name(stepLogDTO.getD09Name());

        // 设置人员数据
        Mem mem = new Mem();
        BeanUtils.copyProperties(memDevelop, mem);
        mem.setOrgName("");
        mem.setCode(memDevelop.getCode());
        mem.setEsId(CodeUtil.getEsId());
        mem.setName(memDevelop.getName());
        mem.setPinyin(PinyinUtil.getPinyin(memDevelop.getName()));
        mem.setIdcard(memDevelop.getIdcard());
        mem.setMemOrgCode(stepLogDTO.getLogOrgCode());
        mem.setOrgZbCode(stepLogDTO.getOrgZbCode());
        mem.setOrgCode(stepLogDTO.getOrgCode());
        mem.setPhone(memDevelop.getPhone());
        Date topreJoinOrgDate = stepLogDTO.getTopreJoinOrgDate();
        mem.setJoinOrgDate(topreJoinOrgDate);
        mem.setCreateTime(new Date());
        mem.setTimestamp(new Date());
        mem.setUpdateTime(new Date());
        mem.setMarryCode(memDevelop.getD60Code());
        mem.setJoinWorkDate(memDevelop.getJoinWorkDate());
        mem.setArchiveUnit(memDevelop.getArchiveUnit());
        mem.setHomeAddress(memDevelop.getHomeAddress());
        mem.setLostContactDate(memDevelop.getLossDate());
        mem.setD18Code(memDevelop.getD18Code());
        mem.setD18Name(memDevelop.getD18Name());
        mem.setD19Name(memDevelop.getD19Name());
        mem.setD19Code(memDevelop.getD19Code());
        mem.setD20Name(memDevelop.getD20Name());
        mem.setD20Code(memDevelop.getD20Code());
        mem.setD21Code(memDevelop.getD21Code());
        mem.setD21Name(memDevelop.getD21Name());
        mem.setBirthday(memDevelop.getBirthday());
        mem.setSexCode(memDevelop.getSexCode());
        mem.setSexName(memDevelop.getSexName());
        mem.setD06Code(memDevelop.getD06Code());
        mem.setD06Name(memDevelop.getD06Name());
        mem.setD48Code(memDevelop.getD48Code());
        mem.setD48Name(memDevelop.getD48Name());
        mem.setD07Code(memDevelop.getD07Code());
        mem.setD07Name(memDevelop.getD07Name());
        mem.setD08Code(memDevelop.getD08Code());
        mem.setD08Name(memDevelop.getD08Name());
        mem.setD09Code(stepLogDTO.getD09Code());
        mem.setD09Name(stepLogDTO.getD09Name());
        mem.setIsFarmer(memDevelop.getIsFarmer());
        mem.setApplyDate(memDevelop.getApplyDate());
        mem.setActiveDate(memDevelop.getActiveDate());
        //进入支部日期
        mem.setJoinOrgPartyDate(topreJoinOrgDate);
        mem.setObjectDate(memDevelop.getObjectDate());
        mem.setD11Name(stepLogDTO.getD11Name());
        mem.setD11Code(stepLogDTO.getD11Code());
        mem.setD27Name(stepLogDTO.getJoinOrgName());
        mem.setD27Code(stepLogDTO.getJoinOrgCode());
        mem.setBranchOrgZbCode(memDevelop.getOrgZbCode());
        mem.setBranchOrgName(memDevelop.getOrgName());
        mem.setBranchOrgKey(memDevelop.getOrgCode());
        mem.setBranchOrgCode(memDevelop.getDevelopOrgCode());
        mem.setIsDispatch(memDevelop.getIsDispatch());
        mem.setExtendPreparDate(DateUtil.offset(topreJoinOrgDate, DateField.YEAR, 1));
        mem.setIsHistory(CommonConstant.ZERO_INT);
        mem.setUpdateAccount(USER_CONTEXT.get().getUser().getAccount());
        mem.setOutBranchOrgName(memDevelop.getOutBranchOrgName());
        mem.setD89Code(memDevelop.getPoliticsCode());
        mem.setD89Name(memDevelop.getPoliticsName());
        mem.setHasUnitStatistics(memDevelop.getHasUnitStatistics());
        mem.setHasUnitProvince(memDevelop.getHasUnitProvince());
        mem.setUnitInformation(memDevelop.getUnitInformation());
        mem.setD04Code(memDevelop.getD04Code());
        mem.setStatisticalUnit(memDevelop.getStatisticalUnit());
        mem.setId(null);
        //设置预备期待考察人员
        mem.setProcessNode(ProcessNodeEnum.YBQ_1_1.getNode());
        // 定时半年
        // 比较当前时间是否已经触发到进入满足考察
        if (DateUtil.compare(DateUtil.offset(topreJoinOrgDate, DateField.MONTH, 6), new Date(), "yyyy-MM-dd") <= 0) {
            mem.setProcessNode(ProcessNodeEnum.YBQ_1_2.getNode());
        }
        //设置档案目录区分  现有入党申请人到预备党员的所有档案目录
        mem.setIsCatalogue(CommonConstant.ONE_INT);
        // 是否入党宣誓 1-是；0否
        mem.setIsOath(CommonConstant.ZERO_INT);
        mem.setDigitalLotNo(digitalLotNo);
        boolean b1 = this.updateById(memDevelop);
        //党员表中存在此党员的信息，直接更新
        Mem existMem = memService.getOne(new LambdaQueryWrapper<Mem>().eq(Mem::getCode, code).isNull(Mem::getDeleteTime));
        if (Objects.nonNull(existMem)) {
            mem.setId(existMem.getId());
        }
        boolean b2 = memService.saveOrUpdate(mem);
        boolean b = developStepLogService.saveDevelopStepLog(developStepLog);
        User user = UserConstant.USER_CONTEXT.get().getUser();

        if (b1 && b2) {
            ThreadUtil.execAsync(() -> {
                iSyncMemService.syncMemDevelop(memDevelop.getCode());
                iSyncMemService.syncMem(memDevelop.getCode(), "1");
            });
            // 发展党员数字档案
            processFileDigital(stepLogDTO, user);
        }
        // 进入下一流程
        return firing(digitalLotNo, processNode, memDevelop.getCode(), topreJoinOrgDate);
    }

    /**
     * 火线入党或者发展对象转预备党员
     *
     * @param digitalLotNo     档案唯一码
     * @param processNode      流程节点
     * @param memDevelopCode   发展党员code
     * @param topreJoinOrgDate 成为预备党员时间
     * @return
     */
    private OutMessage<?> firing(String digitalLotNo, String processNode, String memDevelopCode, Date topreJoinOrgDate) {
        final String finalDigitalLotNo = digitalLotNo;

        // 预备党员
        if (Objects.equals(processNode, ProcessNodeEnum.FZ_7.getNode())) {
            Map<String, Object> map = new HashMap<>();
            //  成为预备党员时间
            map.put("topreJoinOrgDate", topreJoinOrgDate);

            OutMessage<?> out = ProcessTool.next(processNode, memDevelopCode, map);
            ThreadUtil.execAsync(() -> {
                // 是否上传入党志愿书
                Integer volunteer = iMemDigitalService.getIsVolunteer(finalDigitalLotNo);
                // 档案完整度执行
                Integer integrality = iMemDigitalService.getIntegrality(finalDigitalLotNo);
                // 档案类别
                Integer digitalType = iMemDigitalService.getDigitalType(finalDigitalLotNo);
                LambdaUpdateWrapper<Mem> updateWrapper = Wrappers.lambdaUpdate();
                updateWrapper.set(Mem::getIsIntegrality, integrality)
                        .set(Mem::getDigitalType, digitalType)
                        .set(Mem::getIsVolunteer, volunteer)
                        .eq(Mem::getDigitalLotNo, finalDigitalLotNo);
                memService.update(updateWrapper);
            });
            return out;
        }
        User user = UserConstant.USER_CONTEXT.get().getUser();
        String previousCode = null;
        if (StrUtil.isBlank(digitalLotNo)) {
            // 假如该申请人为历史数据可能不存在档案唯一码，则生成个
            digitalLotNo = IdUtil.simpleUUID();
        } else {
            LambdaQueryWrapper<MemDevelopProcess> wrapper = Wrappers.lambdaQuery();
            wrapper.eq(MemDevelopProcess::getProcessNode, processNode)
                    .eq(MemDevelopProcess::getDigitalLotNo, digitalLotNo)
                    .isNull(MemDevelopProcess::getApproveTime);
            MemDevelopProcess currentProcess = iMemDevelopProcessService.getOne(wrapper, false);
            if (Objects.nonNull(currentProcess) && StrUtil.isNotBlank(currentProcess.getCode())) {
                // 处理当前流程
                currentProcess.setApproveUser(user.getAccount());
                currentProcess.setApproveTime(new Date());
                iMemDevelopProcessService.updateById(currentProcess);
                previousCode = currentProcess.getCode();
            }
        }

        // 生成新的预备党员流程
        ProcessNodeEnum nextNode = ProcessNodeEnum.YBQ_1_1;
        // 定时半年
        // 比较当前时间是否已经触发到进入满足考察
        if (DateUtil.compare(DateUtil.offset(topreJoinOrgDate, DateField.MONTH, 6), new Date(), "yyyy-MM-dd") <= 0) {
            nextNode = ProcessNodeEnum.YBQ_1_2;
        }
        final MemDevelopProcess nextProcess = new MemDevelopProcess();
        nextProcess.setCode(IdUtil.simpleUUID());
        nextProcess.setDigitalLotNo(digitalLotNo);
        nextProcess.setd08Code(CommonConstant.TWO);
        nextProcess.setCreateTime(new Date());
        nextProcess.setCreateUser(user.getAccount());
        nextProcess.setPreviousCode(previousCode);
        nextProcess.setProcessNode(nextNode.getNode());
        nextProcess.setExtendStarTime(topreJoinOrgDate);
        // 半年期
        nextProcess.setExtendEndTime(DateUtil.offset(topreJoinOrgDate, DateField.MONTH, 6));
        iMemDevelopProcessService.save(nextProcess);

        // 入党、积极分子、发展党员更新流程
        LambdaUpdateWrapper<MemDevelop> memDevelopLambdaQueryWrapper = Wrappers.lambdaUpdate();
        memDevelopLambdaQueryWrapper.set(MemDevelop::getProcessNode, nextNode)
                // 当档案唯一码为空时候则新增
                .set(Objects.isNull(previousCode), MemDevelop::getDigitalLotNo, digitalLotNo)
                .eq(MemDevelop::getCode, memDevelopCode);
        memDevelopService.update(memDevelopLambdaQueryWrapper);

        // 预备党员更新流程
        LambdaUpdateWrapper<Mem> memLambdaUpdateWrapper = Wrappers.lambdaUpdate();
        memLambdaUpdateWrapper.set(Mem::getProcessNode, nextNode)
                // 当档案唯一码为空时候则新增
                .set(Objects.isNull(previousCode), Mem::getDigitalLotNo, digitalLotNo)
                .eq(Mem::getCode, memDevelopCode);
        boolean b = memService.update(memLambdaUpdateWrapper);

        if(b) {
            ThreadUtil.execAsync(() -> {
                // 是否上传入党志愿书
                Integer volunteer = iMemDigitalService.getIsVolunteer(finalDigitalLotNo);
                // 档案完整度执行
                Integer integrality = iMemDigitalService.getIntegrality(finalDigitalLotNo);
                // 档案类别
                Integer digitalType = iMemDigitalService.getDigitalType(finalDigitalLotNo);
                LambdaUpdateWrapper<Mem> updateWrapper = Wrappers.lambdaUpdate();
                updateWrapper.set(Mem::getIsIntegrality, integrality)
                        .set(Mem::getDigitalType, digitalType)
                        .set(Mem::getIsVolunteer, volunteer)
                        .eq(Mem::getDigitalLotNo, finalDigitalLotNo);
                memService.update(updateWrapper);
            });
        }
        return new OutMessage<>(Status.SUCCESS);
    }

    /**
     * 查看培养联系人是否是留党查看人员
     *
     * @param stepLogDTO
     * @return
     */
    private OutMessage hasStaffOrganization(DevelopStepLogNewDTO stepLogDTO, String person) {
        if (true) {
            return null;
        }
        Integer hasStaffOrganization = stepLogDTO.getHasStaffOrganization();
        if (CommonConstant.ONE_INT == hasStaffOrganization) {
            String[] split = person.split(",");
            List<MemReward> memRewards = rewardMapper.selectList(new QueryWrapper<MemReward>().lambda().in(MemReward::getMemCode, Arrays.asList(split))
                    .eq(MemReward::getD029Code, "214").isNull(MemReward::getDeleteTime));
            if (CollectionUtil.isNotEmpty(memRewards)) {
                List<String> strings = memRewards.stream().map(MemReward::getMemCode).distinct().collect(Collectors.toList());
                List<Mem> memList = memService.findMemByCodes(strings);
                String s = memList.stream().map(Mem::getName).collect(Collectors.joining(","));
                return new OutMessage<>(Status.HAS_STAFF_ORGANIZATION).format(s);
            }
        }
        return null;
    }

    private OutMessage<?> checkDevelopData(MemDevelop memDevelop, DevelopStepLogNewDTO stepLogDTO) {
        return checkDevelopData(memDevelop, stepLogDTO, false);
    }

    /**
     * 效验发展参数
     * hasSpecialDevelopment - false:发展对象确定为预备党员  true:火线入党
     */
    private OutMessage<?> checkDevelopData(MemDevelop memDevelop, DevelopStepLogNewDTO stepLogDTO, boolean hasSpecialDevelopment) {
        if (ObjectUtil.isNull(memDevelop)) {
            return new OutMessage<>(Status.OBJEC_NOT_EXIST);
        }
        String message = memService.findByIdCard(memDevelop.getCode(), memDevelop.getIdcard());
        if (StrUtil.isNotBlank(message)) {
            return new OutMessage<>(99999, message, null);
        }
        // 火线入党直接返回
        if (hasSpecialDevelopment) {
            return null;
        }
        List<FileDigitalDto> filesList = stepLogDTO.getFilesList();
        if (CollUtil.isEmpty(filesList)) {
            return new OutMessage<>(99999, "上传文件档案不能为空", null);
        }
        Set<String> collect = filesList.stream().map(FileDigitalDto::getD222Code).collect(Collectors.toSet());
        String d08Code = stepLogDTO.getD08Code();
        // 入党申请人
        if (StrUtil.equals(d08Code, CommonConstant.FIVE)) {
            if (!collect.contains("201")) {
                return new OutMessage<>(5001, "党员（群团组织）推荐入党积极分子登记表未上传", null);
            }
            if (!collect.contains("202")) {
                return new OutMessage<>(5001, "确定为入党积极分子的支委会会议记录、备案未上传", null);
            }
            if (!collect.contains("203")) {
                return new OutMessage<>(5001, "入党积极分子、发展对象培养教育考察登记表未上传", null);
            }
        }
        // 积极分子
        if (StrUtil.equals(d08Code, CommonConstant.FOUR)) {
            if (!collect.contains("403")) {
                return new OutMessage<>(5001, "发展党员工作有关部门征求意见情况（复印件）未上传", null);
            }
        }
        // 发展对象
        if (StrUtil.equals(d08Code, CommonConstant.THREE)) {
            if (!collect.contains("408")) {
                return new OutMessage<>(5001, "向上级党委组织部门的备案报告和批复（复印件）未上传", null);
            }
        }
        return null;
    }

    /**
     * 发展党员数字档案
     */
    @Override
    public boolean processFileDigital(DevelopStepLogNewDTO stepLogDTO, User user) {
        List<FileDigitalDto> filesList = stepLogDTO.getFilesList();
        if (CollUtil.isEmpty(filesList)) {
            return false;
        }
        // 将前端传的档案分类排序
        Map<String, List<FileDigitalDto>> d0222DtoMap = filesList.stream().sorted(Comparator.comparingInt(e -> Objects.nonNull(e.getSort()) ? e.getSort() : 0))
                .filter(e -> StrUtil.isNotEmpty(e.getD222Code())).collect(Collectors.groupingBy(FileDigitalDto::getD222Code));

        // 获取数据库中各类别最高排序号
        Map<String, Object> dbD222AllCountMap = memDigitalMapper.selectD222AllCount(stepLogDTO.getDigitalLotNo()).stream().collect(Collectors.toMap(s -> s.get("d222_code").toString(), s -> s.get("sort")));

        Date date = new Date();
        List<MemDigital> list = new ArrayList<>();
        List<MemDigitalOperationLog> logList = new ArrayList<>();
        String oprationOrgName = CacheUtils.getOrgName(user.getOrgId());
        d0222DtoMap.forEach((key, value) -> {
            int index = 1;
            // 获取数据库中最高排序号
            int dbSort = Integer.parseInt(dbD222AllCountMap.getOrDefault(key, 0).toString());
            for (FileDigitalDto e : value) {
                // 数字档案表
                MemDigital memDigital = new MemDigital();
                memDigital.setCode(StrKit.getRandomUUID());
                memDigital.setProcessNode(stepLogDTO.getProcessNode());
                memDigital.setDigitalLotNo(stepLogDTO.getDigitalLotNo());
                memDigital.setName(e.getName());
                // 如果是临时桶则拷贝过去
                memDigital.setPath(minioTemplate2.copyTmpToTarget(e.getPath()));
                memDigital.setd222Code(e.getD222Code());
                memDigital.setd222Name(e.getD222Name());
                memDigital.setd08Code(stepLogDTO.getD08Code());
                memDigital.setSort(dbSort + index);
                memDigital.setOprationUser(stepLogDTO.getOprationUser());
                memDigital.setOprationCode(stepLogDTO.getOprationCode());
                memDigital.setCreateTime(date);
                memDigital.setCreateUser(user.getAccount());
                memDigital.setFileSize(e.getFileSize());
                list.add(memDigital);
                index++;
            }
        });

        // 数字档案操作日志：分类存储操作日志
        Map<String, List<MemDigital>> digitalMap = list.stream()
                .filter(e -> StrUtil.isNotEmpty(e.getd222Code())).collect(Collectors.groupingBy(MemDigital::getd222Code));
        digitalMap.forEach((key, value) -> {
            MemDigitalOperationLog operationLog = new MemDigitalOperationLog();
            operationLog.setCode(StrKit.getRandomUUID());
            operationLog.setProcessNode(stepLogDTO.getProcessNode());
            operationLog.setDigitalLotNo(stepLogDTO.getDigitalLotNo());
            operationLog.setD222Code(key);
            operationLog.setD222Name(value.get(0).getd222Name());
            operationLog.setDigitalNames(CollUtil.join(value.stream().map(MemDigital::getName).collect(Collectors.toList()), CommonConstant.DOU_HAO_STRING));
            operationLog.setDigitalCodes(CollUtil.join(value.stream().map(MemDigital::getCode).collect(Collectors.toList()), CommonConstant.DOU_HAO_STRING));
            operationLog.setOprationTime(date);
            operationLog.setOprationUser(stepLogDTO.getOprationUser());
            operationLog.setOprationOrgName(oprationOrgName);
            operationLog.setOprationType(CommonConstant.ONE_INT);
            operationLog.setCreateTime(date);
            operationLog.setCreateUser(user.getAccount());
            operationLog.setOprationCode(stepLogDTO.getOprationCode());
            logList.add(operationLog);
        });
        boolean b = iMemDigitalService.saveBatch(list, list.size());
        boolean b1 = iMemDigitalOperationLogService.saveBatch(logList, logList.size());
        return b && b1;
    }

    private MemDevelopProcess getLastMemDevelopProcess(String processNode, String digitalLotNo) {
        LambdaQueryWrapper<MemDevelopProcess> processCurrentWrapper = Wrappers.lambdaQuery();
        processCurrentWrapper.eq(MemDevelopProcess::getProcessNode, processNode);
        processCurrentWrapper.eq(MemDevelopProcess::getDigitalLotNo, digitalLotNo);
        processCurrentWrapper.isNull(MemDevelopProcess::getApproveTime);
        processCurrentWrapper.isNull(MemDevelopProcess::getApproveUser);
        processCurrentWrapper.last("limit 1");
        MemDevelopProcess processCurrent = this.iMemDevelopProcessService.getOne(processCurrentWrapper);
        if (ObjectUtil.isNotNull(processCurrent)) {
            String previousCode = processCurrent.getPreviousCode();
            return this.iMemDevelopProcessService.getById(previousCode);
        }
        return null;
    }

    /**
     * 事务操作
     *
     * @param stepLogDTO 请求参数
     * @param memDevelop 发展基础数据
     * @return 操作结果
     */
    public OutMessage<?> getOutMessage(DevelopStepLogNewDTO stepLogDTO, MemDevelop memDevelop) {
        String d08 = memDevelop.getD08Code();
        String d08Name = memDevelop.getD08Name();
        DevelopStepLog developStepLog = new DevelopStepLog();
        if (CommonConstant.FIVE.equals(stepLogDTO.getD08Code()) || CommonConstant.FOUR.equals(stepLogDTO.getD08Code())) {
            MemDevelopOperation operation = new MemDevelopOperation();
            BeanUtils.copyProperties(stepLogDTO, operation, "code");
            operation.setDevelopCode(stepLogDTO.getMemCode());
            operation.setCreateTime(new Date());
            operationService.save(operation);
        }
        memDevelop.setD08Code(d08);
        memDevelop.setD08Name(d08Name);
        BeanUtils.copyProperties(stepLogDTO, developStepLog);
        // 入党积极分子培养联系人(申请人=》积极分子）
        developStepLog.setToactiveContextPerson(stepLogDTO.getToactiveContextPerson());
        // 培养联系人(积极分子=》发展对象）
        developStepLog.setToobjContextMem(stepLogDTO.getToobjContextMem());
        // 考察材料扫描件(积极分子=》发展对象）
        List<FileDigitalDto> filesList = stepLogDTO.getFilesList();
        if (CollUtil.isNotEmpty(filesList)) {
            Set<String> collect = filesList.stream().filter(e -> StrUtil.equals(e.getD222Code(), "203") && StrUtil.isNotBlank(e.getPath()))
                    .map(FileDigitalDto::getPath).collect(Collectors.toSet());
            developStepLog.setToobjCheckFileUrl(CollUtil.join(collect, CommonConstant.DOU_HAO_STRING));
        }
        if (StrUtil.isEmpty(memDevelop.getOrgZbCode())) {
            Org org = orgService.findOrgByCode(memDevelop.getOrgCode());
            if (Objects.nonNull(org) && StrUtil.isNotEmpty(org.getZbCode())) {
                memDevelop.setOrgZbCode(org.getZbCode());
                developStepLog.setOrgZbCode(org.getZbCode());
            }
        }
        boolean b = updateById(memDevelop);
        //更新成为积极分子时间和成为发展对象时间
        update(new LambdaUpdateWrapper<MemDevelop>()
                .set(MemDevelop::getObjectDate, memDevelop.getObjectDate())
                .set(MemDevelop::getActiveDate, memDevelop.getActiveDate())
                .eq(MemDevelop::getId, memDevelop.getId()));
        boolean b1 = developStepLogService.saveDevelopStepLog(developStepLog);
        if (b) {
            iSyncMemService.syncMemDevelop(stepLogDTO.getMemCode());
        }
        return new OutMessage<>(b && b1 ? Status.SUCCESS : Status.FAIL);
    }

    /**
     * 撤销资格
     *
     * @param stepLogDTO
     * @return
     */
    @Override
    @Transactional
    public OutMessage backOutStatus(DevelopStepLogNewDTO stepLogDTO) throws Exception {
        String memCode = stepLogDTO.getMemCode();
        MemDevelop memDevelop = this.memDevelopService.findByCode(memCode);
        if (ObjectUtil.isNull(memDevelop)) {
            return new OutMessage<>(Status.OBJEC_NOT_EXIST);
        }

        String d08Code = stepLogDTO.getD08Code();
        String canncelCode = stepLogDTO.getCanncelCode();
        if (!StrUtil.equals(canncelCode, "3")) {
            if (!(CommonConstant.FOUR.equals(d08Code) || CommonConstant.THREE.equals(d08Code))) {
                // 人员类型不对
                return new OutMessage<>(Status.D08_CODE_ERROR);
            }
        }
        // 回退到的节点
        final String processNode = stepLogDTO.getProcessNode();
        if (CommonConstant.ONE.equals(canncelCode)) {
            // 退回到入党申请人第一个流程节点
            memDevelop.setD08Code(CommonConstant.FIVE);
            memDevelop.setD08Name("入党申请人");
            memDevelop.setActiveDate(null);
            memDevelop.setObjectDate(null);
            String currentNode = memDevelop.getProcessNode();
            String digitalLotNo = memDevelop.getDigitalLotNo();
            memDevelop.setProcessNode(processNode);
            // 执行下一步流程节点
            OutMessage<?> nextProcessNode = nextProcessNode(currentNode, processNode, digitalLotNo, memDevelop.getApplyDate());
            if (!ObjectUtil.equals(nextProcessNode.getCode(), Status.SUCCESS.getCode())) {
                return nextProcessNode;
            }
        } else if (CommonConstant.TWO.equals(canncelCode)) {
            // 退回到积极分子
            memDevelop.setD08Code(CommonConstant.FOUR);
            memDevelop.setD08Name("积极分子");
            memDevelop.setObjectDate(null);
            String currentNode = memDevelop.getProcessNode();
            String digitalLotNo = memDevelop.getDigitalLotNo();
            memDevelop.setProcessNode(processNode);
            // 执行下一步流程节点
            OutMessage<?> nextProcessNode = nextProcessNode(currentNode, processNode, digitalLotNo, memDevelop.getActiveDate());
            if (!ObjectUtil.equals(nextProcessNode.getCode(), Status.SUCCESS.getCode())) {
                return nextProcessNode;
            }
        } else if ("3".equals(canncelCode)) {
            // 取消发展
            memDevelop.setCancelDevelopReason(stepLogDTO.getCancelDevelopReason());
            memDevelop.setDeleteTime(new Date());
            memDevelop.setIsHistory(1);
        } else {
            return new OutMessage<>(Status.PARA_ERROR);
        }
        OutMessage out = getOutMessage(stepLogDTO, memDevelop);
        if(Objects.equals(out.getCode(), Status.SUCCESS.getCode())) {
            // 异步执行
            ThreadUtil.execAsync(() -> {
                // 档案完整度执行
                Integer integrality = iMemDigitalService.getIntegrality(memDevelop.getDigitalLotNo());
                LambdaUpdateWrapper<MemDevelop> updateWrapper = Wrappers.lambdaUpdate();
                updateWrapper.set(MemDevelop::getIsIntegrality, integrality)
                        .eq(MemDevelop::getCode, memDevelop.getCode());
                memDevelopService.update(updateWrapper);
            });
        }
        return out;
    }

    /**
     * 流程流转处理
     *
     * @param currentNode  当前节点
     * @param nextNode     下一节点
     * @param digitalLotNo 档案唯一码
     * @param startDate    入党申请人创建时间、成为积极分子时间
     * @return
     */
    private OutMessage<?> nextProcessNode(String currentNode, String nextNode, String digitalLotNo, Date startDate) {
        User user = USER_CONTEXT.get().getUser();
        // 获取当前流程节点信息
        LambdaQueryWrapper<MemDevelopProcess> processCurrentWrapper = Wrappers.lambdaQuery();
        processCurrentWrapper.eq(MemDevelopProcess::getProcessNode, currentNode);
        processCurrentWrapper.eq(MemDevelopProcess::getDigitalLotNo, digitalLotNo);
        processCurrentWrapper.isNull(MemDevelopProcess::getApproveTime);
        processCurrentWrapper.isNull(MemDevelopProcess::getApproveUser);
        processCurrentWrapper.last("limit 1");
        MemDevelopProcess processCurrent = this.iMemDevelopProcessService.getOne(processCurrentWrapper);
        if (ObjectUtil.isNull(processCurrent)) {
            return new OutMessage<>(Status.OBJEC_NOT_EXIST);
        }
        // 修改当前节点流程信息
        processCurrent.setApproveUser(user.getAccount());
        processCurrent.setApproveTime(DateUtil.date());
        processCurrent.setUpdateUser(user.getAccount());
        processCurrent.setUpdateTime(DateUtil.date());
        // 记录下一节点流程信息
        MemDevelopProcess processNext = new MemDevelopProcess();
        processNext.setCode(StrKit.getRandomUUID());
        processNext.setProcessNode(nextNode);
        processNext.setDigitalLotNo(processCurrent.getDigitalLotNo());
        processNext.setd08Code(processCurrent.getd08Code());
        processNext.setExtendStarTime(Objects.nonNull(startDate) ? startDate : new Date());
        // 一月内需要谈话
        if (StrUtil.equals(nextNode, ProcessNodeEnum.RD_2_1.getNode())) {
            // 设置定时谈话时间少于10天时间
            processNext.setExtendEndTime(DateUtil.offsetDay(processNext.getExtendStarTime(), 20));
        } else if (StrUtil.equals(nextNode, ProcessNodeEnum.RD_3.getNode())) {
            // 设置入党申请书提交3个月
            processNext.setExtendEndTime(DateUtil.offsetMonth(processNext.getExtendStarTime(), 3));
        } else if (StrUtil.equals(nextNode, ProcessNodeEnum.JJ_1.getNode())) {
            // 设置定时待第一次考察触发时间-成为积极份子时间加上半年
            processNext.setExtendEndTime(DateUtil.offsetMonth(processNext.getExtendStarTime(), 6));
        } else if (StrUtil.equals(nextNode, ProcessNodeEnum.JJ_5.getNode())) {
            // 设置持续考察触发时间为成为积极份子时间加上一年半
            processNext.setExtendEndTime(DateUtil.offsetMonth(processCurrent.getExtendStarTime(), 18));
        }
        processNext.setPreviousCode(processCurrent.getCode());
        processNext.setCreateUser(user.getAccount());
        processNext.setCreateTime(DateUtil.date());
        // 修改当前节点流程信息
        boolean updateProcessCurrent = this.iMemDevelopProcessService.updateById(processCurrent);
        if (!updateProcessCurrent) {
            new OutMessage<>(Status.FAIL);
        }
        // 记录下一节点流程信息
        boolean saveProcessNext = this.iMemDevelopProcessService.save(processNext);
        if (!saveProcessNext) {
            new OutMessage<>(Status.FAIL);
        }
        return new OutMessage<>(Status.SUCCESS);
    }

    @Override
    public OutMessage ImportExcelDevelop(ImportExcelDevelopDTO data, String currManOrgCode) throws Exception {
        String excelFile = data.getExcelFile();
        if (StrUtil.containsAny(excelFile, "\\")) {
            excelFile = excelFile.replaceAll("\\\\", "/");
        }
        String excelFilePath = ResourceUtils.getURL(ResourceUtils.CLASSPATH_URL_PREFIX).getPath() + excelFile;
        OutMessage errorMessage = this.proccessIsExcelFtl(excelFilePath);
        if (ObjectUtil.isNotNull(errorMessage)) {
            return errorMessage;
        }

        FileInputStream fileInputStream = new FileInputStream(excelFilePath);
        ExcelImportUtil<ImportDevelopDTO> excelImportUtil = new ExcelImportUtil<>(ImportDevelopDTO.class);
        List<ImportDevelopDTO> importDevelopDTOS = excelImportUtil.readExcel(fileInputStream, 1, 0);
        List<String> orgNameList = importDevelopDTOS.stream()
                .map(ImportDevelopDTO::getOrgName)
                .filter(StrUtil::isNotEmpty)
                .collect(Collectors.toList());

        List<MemDevelop> saveMemDevelopList = new ArrayList<>();
        List<MemDevelopProcess> saveMemDevelopProcessList = new ArrayList<>();
        Map<String, String> d48Map = this.dicNameKey(CacheUtils.getDic("dict_d48"));
        Map<String, String> d06Map = this.dicNameKey(CacheUtils.getDic("dict_d06"));
        Map<String, String> d09Map = this.dicNameKey(CacheUtils.getDic("dict_d09"));
        Map<String, String> d07Map = this.dicNameKey(CacheUtils.getDic("dict_d07"));
        Map<String, String> d88Map = this.dicNameKey(CacheUtils.getDic("dict_d88"));
        Map<String, String> d89Map = this.dicNameKey(CacheUtils.getDic("dict_d89"));
        Map<String, String> d107Map = this.dicNameKey(CacheUtils.getDic("dict_d107"));
        Map<String, String> d21Map = this.dicNameKey(CacheUtils.getDic("dict_d21"));
        Map<String, String> d20Map = this.dicNameKey(CacheUtils.getDic("dict_d20"));
        Map<String, String> d19Map = this.dicNameKey(CacheUtils.getDic("dict_d19"));
        Map<String, String> d104Map = this.dicNameKey(CacheUtils.getDic("dict_d104"));

        OutMessage out = this.verifyImportData(importDevelopDTOS);
        if (ObjectUtil.isNotNull(out)) {
            return out;
        }
        List<Org> orgList = orgService.findByOrgNames(orgNameList);
        OutMessage message = this.verifyOrgInfo(orgList, currManOrgCode);
        if (Objects.nonNull(message)) {
            return message;
        }
        Map<String, Org> orgMap = orgList.stream().collect(Collectors.toMap(Org::getName, value -> value, (key1, key2) -> key1));
        int rowNum = 2;
        for (ImportDevelopDTO importDevelopDTO : importDevelopDTOS) {
            if (orgMap.containsKey(importDevelopDTO.getOrgName())) {
                // TODO: 2022/6/22 校验身份证
                if (StrUtil.isNotBlank(importDevelopDTO.getIdcard()) && !IdcardUtil.isValidCard(importDevelopDTO.getIdcard())) {
                    return new OutMessage(500, "导入失败第" + rowNum + "行身份证号格式错误", null);
                }
                MemDevelop memDevelop = new MemDevelop();
                memDevelop.setCode(StrKit.getRandomUUID());
                memDevelop.setEsId(StrKit.getRandomUUID());
                memDevelop.setIsOutSystem(CommonConstant.ZERO_INT);
                memDevelop.setCreateTime(new Date());
                memDevelop.setD08Code("5");
                memDevelop.setD08Name("入党申请人");
                memDevelop.setOrgCode(orgMap.get(importDevelopDTO.getOrgName()).getCode());
                memDevelop.setOrgName(importDevelopDTO.getOrgName());
                memDevelop.setOrgZbCode(orgMap.get(importDevelopDTO.getOrgName()).getZbCode());
                memDevelop.setDevelopOrgCode(orgMap.get(importDevelopDTO.getOrgName()).getOrgCode());
                memDevelop.setName(StrUtil.isNotEmpty(importDevelopDTO.getName()) ? importDevelopDTO.getName() : null);
                memDevelop.setPinyin(StrUtil.isNotEmpty(importDevelopDTO.getName()) ? PinyinUtil.getPinyin(importDevelopDTO.getName()) : null);
                if (StrUtil.equals(importDevelopDTO.getSexName(), "男")) {
                    memDevelop.setSexName("男");
                    memDevelop.setSexCode("1");
                } else {
                    memDevelop.setSexName("女");
                    memDevelop.setSexCode("0");
                }
                memDevelop.setIdcard(StrUtil.isNotEmpty(importDevelopDTO.getIdcard()) ? importDevelopDTO.getIdcard() : null);
                if (StrUtil.isNotEmpty(importDevelopDTO.getBirthday())) {
                    try {
                        DateTime dateTime = DateUtil.parse(importDevelopDTO.getBirthday(), "yyyyMMdd");
                        memDevelop.setBirthday(dateTime);
                    } catch (Exception e) {
                        return new OutMessage(500, "导入失败第" + rowNum + "行出生日期格式不对请以年月日的形式填写例如 20190101", null);
                    }
                    // TODO 20241225 入党申请人，新增和导入的时候， 要根据出生年月计算， 是否大于和等于18岁。
                    if (DateUtil.ageOfNow(importDevelopDTO.getBirthday()) < 18) {
                        return new OutMessage(500, "导入失败第" + rowNum + "行" + Status.AGE_NEED_OVER_18.getMessage(), null);
                    }
                } else {
                    memDevelop.setBirthday(null);
                }

                memDevelop.setD48Code(d48Map.getOrDefault(importDevelopDTO.getD48Name(), null));
                memDevelop.setD48Name(importDevelopDTO.getD48Name());

                if (d06Map.containsKey(importDevelopDTO.getD06Name())) {
                    memDevelop.setD06Code(d06Map.get(importDevelopDTO.getD06Name()));
                    memDevelop.setD06Name(importDevelopDTO.getD06Name());
                } else {
                    return new OutMessage(500, "导入失败第" + rowNum + "行民族信息不存在于字典表中，请对照系统的选项修改", null);
                }

                if (d09Map.containsKey(importDevelopDTO.getD09Name())) {
                    memDevelop.setD09Code(d09Map.get(importDevelopDTO.getD09Name()));
                    memDevelop.setD09Name(importDevelopDTO.getD09Name());
                } else {
                    return new OutMessage(500, "导入失败第" + rowNum + "行申请时工作岗位信息不存在于字典表中，请对照系统的选项修改", null);
                }

                if (d07Map.containsKey(importDevelopDTO.getD07Name())) {
                    memDevelop.setD07Code(d07Map.get(importDevelopDTO.getD07Name()));
                    memDevelop.setD07Name(importDevelopDTO.getD07Name());
                } else {
                    return new OutMessage(500, "导入失败第" + rowNum + "行申请时学历情况信息不存在于字典表中，请对照系统的选项修改", null);
                }

                memDevelop.setByyx(StrUtil.isNotEmpty(importDevelopDTO.getByyx()) ? importDevelopDTO.getByyx() : null);
                if (d88Map.containsKey(importDevelopDTO.getD88Name())) {
                    memDevelop.setD88Code(d88Map.get(importDevelopDTO.getD88Name()));
                    memDevelop.setD88Name(importDevelopDTO.getD88Name());
                } else {
                    memDevelop.setD88Code(null);
                    memDevelop.setD88Name(null);
                }

                if (StrUtil.isNotEmpty(importDevelopDTO.getPoliticsName())) {
                    List<String> politicsCodeList = new ArrayList<>();
                    List<String> politicsNameList = new ArrayList<>();
                    for (String s : importDevelopDTO.getPoliticsName().split(",")) {
                        if (d89Map.containsKey(s)) {
                            politicsCodeList.add(d89Map.get(s));
                            politicsNameList.add(s);
                        }
                    }
                    if (CollectionUtil.isNotEmpty(politicsCodeList)) {
                        memDevelop.setPoliticsCode(CollectionUtil.join(politicsCodeList, ","));
                        memDevelop.setPoliticsName(CollectionUtil.join(politicsNameList, ","));
                    }
                } else {
                    memDevelop.setPoliticsCode(null);
                    memDevelop.setPoliticsName(null);
                }

                memDevelop.setPhone(StrUtil.isNotEmpty(importDevelopDTO.getPhone()) ? importDevelopDTO.getPhone() : null);
                memDevelop.setJobNatureCode(d107Map.getOrDefault(importDevelopDTO.getJobNatureName(), null));

                if (StrUtil.isNotEmpty(importDevelopDTO.getApplyDate())) {
                    try {
                        DateTime dateTime = DateUtil.parse(importDevelopDTO.getApplyDate(), "yyyyMMdd");
                        memDevelop.setApplyDate(dateTime);
                    } catch (Exception e) {
                        return new OutMessage(500, "导入失败第" + rowNum + "行申请入党时间格式不对请以年月日的形式填写例如 20190101", null);
                    }
                } else {
                    memDevelop.setApplyDate(null);
                }

                if (d19Map.containsKey(importDevelopDTO.getD19Name())) {
                    memDevelop.setD19Code(d19Map.get(importDevelopDTO.getD19Name()));
                    memDevelop.setD19Name(importDevelopDTO.getD19Name());
//                    memDevelop.setD126Code(d19Map.get(importDevelopDTO.getD19Name()));
//                    memDevelop.setD126Name(importDevelopDTO.getD19Name());
                } else {
                    return new OutMessage(500, "导入失败第" + rowNum + "行专业技术职务信息不存在于字典表中，请对照系统的选项修改", null);
                }

                if (d20Map.containsKey(importDevelopDTO.getD20Name())) {
                    memDevelop.setD20Code(d20Map.get(importDevelopDTO.getD20Name()));
                    memDevelop.setD20Name(importDevelopDTO.getD20Name());
                } else {
                    return new OutMessage(500, "导入失败第" + rowNum + "行申请时新社会阶层信息不存在于字典表中，请对照系统的选项修改", null);
                }

                if (d21Map.containsKey(importDevelopDTO.getD21Name())) {
                    memDevelop.setD21Code(d21Map.get(importDevelopDTO.getD21Name()));
                    memDevelop.setD21Name(importDevelopDTO.getD21Name());
                } else {
                    return new OutMessage(500, "导入失败第" + rowNum + "行一线情况信息不存在于字典表中，请对照系统的选项修改", null);
                }

                if (StrUtil.equals(importDevelopDTO.getIsHighKnowledge(), "是")) {
                    memDevelop.setIsHighKnowledge(1);
                } else {
                    memDevelop.setIsHighKnowledge(null);
                }


                memDevelop.setAdvancedModelCode(d104Map.getOrDefault(importDevelopDTO.getAdvancedModelName(), null));

                memDevelop.setArchiveUnit(StrUtil.isNotEmpty(importDevelopDTO.getArchiveUnit()) ? importDevelopDTO.getArchiveUnit() : null);
                memDevelop.setHomeAddress(StrUtil.isNotEmpty(importDevelopDTO.getHomeAddress()) ? importDevelopDTO.getHomeAddress() : null);

                memDevelop.setReadingCollege(StrUtil.isNotEmpty(importDevelopDTO.getReadingCollege()) ? importDevelopDTO.getReadingCollege() : null);
                if (d88Map.containsKey(importDevelopDTO.getReadingProfessionalName())) {
                    memDevelop.setReadingProfessionalCode(d88Map.get(importDevelopDTO.getReadingProfessionalName()));
                    memDevelop.setReadingProfessionalName(importDevelopDTO.getReadingProfessionalName());
                } else {
                    memDevelop.setReadingProfessionalCode(null);
                    memDevelop.setReadingProfessionalName(null);
                }
                memDevelop.setEducationalSystem(importDevelopDTO.getEducationalSystem());

                if (StrUtil.isNotEmpty(importDevelopDTO.getEnterSchoolDate())) {
                    try {
                        DateTime dateTime = DateUtil.parse(importDevelopDTO.getEnterSchoolDate(), "yyyyMMdd");
                        memDevelop.setEnterSchoolDate(dateTime);
                    } catch (Exception e) {
                        return new OutMessage(500, "导入失败第" + rowNum + "行入学时间格式不对请以年月日的形式填写例如 20190101", null);
                    }
                } else {
                    memDevelop.setEnterSchoolDate(null);
                }

                memDevelop.setDigitalLotNo(StrKit.getRandomUUID());
                memDevelop.setProcessNode(ProcessNodeEnum.RD_1.getNode());
                saveMemDevelopList.add(memDevelop);

                MemDevelopProcess memDevelopProcess = new MemDevelopProcess();
                memDevelopProcess.setCode(StrKit.getRandomUUID());
                memDevelopProcess.setProcessNode(memDevelop.getProcessNode());
                memDevelopProcess.setDigitalLotNo(memDevelop.getDigitalLotNo());
                memDevelopProcess.setd08Code(memDevelop.getD08Code());
                memDevelopProcess.setExtendStarTime(null);
                memDevelopProcess.setExtendEndTime(null);
                memDevelopProcess.setPreviousCode(memDevelopProcess.getCode());
                memDevelopProcess.setCreateUser(UserConstant.USER_CONTEXT.get().getUser().getAccount());
                memDevelopProcess.setCreateTime(DateUtil.date());
                saveMemDevelopProcessList.add(memDevelopProcess);
            } else {
                return new OutMessage(500, "导入失败第" + rowNum + "行管理党组织信息在系统中不存在此组织", null);
            }
            rowNum++;
        }

        if (CollectionUtil.isNotEmpty(saveMemDevelopList)) {
            boolean b = this.saveBatch(saveMemDevelopList, saveMemDevelopList.size());
            if (b) {
                saveMemDevelopList.parallelStream().forEach(memDevelop -> iSyncMemService.syncMemDevelop(memDevelop.getCode()));
            }
        }
        if (CollectionUtil.isNotEmpty(saveMemDevelopProcessList)) {
            this.iMemDevelopProcessService.saveBatch(saveMemDevelopProcessList, 100);
        }
        fileInputStream.close();
        return new OutMessage(Status.SUCCESS);
    }

    /**
     * 修改发展党员
     *
     * @param developDTO
     * @return
     */
    @Override
    public OutMessage updateDevelop(MemDevelopNewDTO developDTO) throws Exception {
        MemDevelopDTO developDTO1 = new MemDevelopDTO();
        BeanUtil.copyProperties(developDTO, developDTO1);
        OutMessage out = memDevelopService.updateDevelop(developDTO1);
        if (Objects.isNull(out) || !Objects.equals(out.getCode(), Status.SUCCESS.getCode())) {
            return out;
        }
        // 档案文件不为空则进入走流程阶段，否则认定为编辑
        if (CollUtil.isNotEmpty(developDTO.getFilesList())) {
            MemDevelop memDevelop = memDevelopService.findByCode(developDTO.getCode());
            DevelopStepLogNewDTO logNewDTO = new DevelopStepLogNewDTO();
            logNewDTO.setFilesList(developDTO.getFilesList());
            logNewDTO.setProcessNode(developDTO.getProcessNode());
            logNewDTO.setOprationUser(developDTO.getOprationUser());
            logNewDTO.setDigitalLotNo(memDevelop.getDigitalLotNo());
            logNewDTO.setD08Code(memDevelop.getD08Code());
            boolean save = processFileDigital(logNewDTO, UserConstant.USER_CONTEXT.get().getUser());
            if (!save) {
                return new OutMessage<>(Status.FAIL);
            }
            out = ProcessTool.next(ProcessNodeEnum.RD_1.getNode(), developDTO.getCode(), null);
            if (Objects.equals(out.getCode(), Status.SUCCESS.getCode())) {
                // 可以异步执行
                ThreadUtil.execAsync(() -> {
                    // 档案完整度执行
                    Integer integrality = iMemDigitalService.getIntegrality(memDevelop.getDigitalLotNo());
                    LambdaUpdateWrapper<MemDevelop> updateWrapper = Wrappers.lambdaUpdate();
                    updateWrapper.set(MemDevelop::getIsIntegrality, integrality)
                            .eq(MemDevelop::getCode, memDevelop.getCode());
                    memDevelopService.update(updateWrapper);
                });
            }
            return out;
        }
        return out;
    }

    /**
     * 判断是否是我们自己的模板
     *
     * @param excelFilePath 模板地址
     * @return null 为是
     */
    private OutMessage proccessIsExcelFtl(String excelFilePath) throws Exception {
        if (!StrUtil.containsAny(excelFilePath, ".xlsm")) {
            return new OutMessage(Status.DEVELOP_UPLOAD_EXCEL_ERROR);
        } else {
            FileInputStream fileInputStream = new FileInputStream(excelFilePath);
            XSSFWorkbook xSSFWorkbook = new XSSFWorkbook(fileInputStream);
            boolean flag = true;
            XSSFSheet sheetAt = xSSFWorkbook.getSheetAt(0);
            if (ObjectUtil.isNotNull(sheetAt)) {
                XSSFRow row = sheetAt.getRow(0);
                if (ObjectUtil.isNotNull(row)) {
                    XSSFCell cell = row.getCell(25);
                    if (ObjectUtil.isNotNull(cell)) {
                        if (ObjectUtil.equal(cell.getCellType().getCode(), 1)) {
                            String stringCellValue = cell.getStringCellValue();
                            if (StrUtil.equals(stringCellValue, "入学时间(请以年月日的形式填写例如20190301)")) {
                                flag = false;
                            }
                        }
                    }
                }
            }
            fileInputStream.close();
            xSSFWorkbook.close();
            if (flag) {
                return new OutMessage(Status.DEVELOP_UPLOAD_EXCEL_ERROR);
            }
        }
        return null;
    }

    public Map<String, String> dicNameKey(List<Record> dictList) {
        return dictList.stream().filter(var -> StrUtil.isNotEmpty(var.getStr("name")) && StrUtil.isNotEmpty(var.getStr("key"))).collect(Collectors.toMap(key -> key.getStr("name"), value -> value.getStr("key"), (key1, key2) -> key1));
    }

    public OutMessage verifyImportData(List<ImportDevelopDTO> importDevelopDTOS) {
        //1:身份证为空 身份证重复
        Set<String> idcards = new HashSet<>();
        Map<String, Integer> orgMap = new HashMap<>();
        int rowNum = 2;
        for (ImportDevelopDTO importDevelopDTO : importDevelopDTOS) {
            if (StrUtil.isEmpty(importDevelopDTO.getIdcard())) {
                //身份证为空
                return new OutMessage(500, "导入失败第" + rowNum + "行身份证为空", null);
            }
            if (!idcards.contains(importDevelopDTO.getIdcard())) {
                idcards.add(importDevelopDTO.getIdcard());
            } else {
                return new OutMessage(500, "导入失败身份证" + importDevelopDTO.getIdcard() + "在EXCEL中有多个", null);
            }
            //判断是否为空的问题
            if (StrUtil.isEmpty(importDevelopDTO.getName())) {
                return new OutMessage(500, "导入失败第" + rowNum + "行姓名为空", null);
            }
            if (StrUtil.isEmpty(importDevelopDTO.getSexName())) {
                return new OutMessage(500, "导入失败第" + rowNum + "行性别为空", null);
            }
            if (StrUtil.isEmpty(importDevelopDTO.getBirthday())) {
                return new OutMessage(500, "导入失败第" + rowNum + "行出生日期为空", null);
            }
            if (StrUtil.isEmpty(importDevelopDTO.getD48Name())) {
                return new OutMessage(500, "导入失败第" + rowNum + "行籍贯为空", null);
            }
            if (StrUtil.isEmpty(importDevelopDTO.getD06Name())) {
                return new OutMessage(500, "导入失败第" + rowNum + "行民族为空", null);
            }
            if (StrUtil.isEmpty(importDevelopDTO.getD09Name())) {
                return new OutMessage(500, "导入失败第" + rowNum + "行申请时工作岗位为空", null);
            }
            if (StrUtil.isEmpty(importDevelopDTO.getD07Name())) {
                return new OutMessage(500, "导入失败第" + rowNum + "行申请时学历为空", null);
            }
            if (StrUtil.isEmpty(importDevelopDTO.getOrgName())) {
                orgMap.put(importDevelopDTO.getOrgName(), rowNum);
                return new OutMessage(500, "导入失败第" + rowNum + "行管理党组织为空", null);
            }
            if (StrUtil.isEmpty(importDevelopDTO.getApplyDate())) {
                return new OutMessage(500, "导入失败第" + rowNum + "行申请入党时间为空", null);
            }
            if (StrUtil.isEmpty(importDevelopDTO.getD21Name())) {
                return new OutMessage(500, "导入失败第" + rowNum + "行一线情况为空", null);
            }
            if (StrUtil.isEmpty(importDevelopDTO.getD20Name())) {
                return new OutMessage(500, "导入失败第" + rowNum + "行申请时新社会阶层为空", null);
            }
            if (StrUtil.isEmpty(importDevelopDTO.getD19Name())) {
                return new OutMessage(500, "导入失败第" + rowNum + "行专业技术职务为空", null);
            }
            rowNum++;
        }

        //判断身份证是否已经存在
        if (CollectionUtil.isNotEmpty(idcards)) {
            List<String> idcardList = new ArrayList<>(idcards);
            List<CheckIdCardDuplicateVO> mapList = memMapper.checkIdCardDuplicate(idcardList);
            if (CollUtil.isNotEmpty(mapList)) {
                StringBuilder builder = new StringBuilder();
                String idcard = mapList.get(0).getIdcard();
                builder.append("身份证号").append(idcard).append("在");
                List<String> orgStr = new ArrayList<>();
                for (CheckIdCardDuplicateVO checkIdCardDuplicateVO : mapList) {
                    if (StrUtil.equals(idcard, checkIdCardDuplicateVO.getIdcard())) {
                        String s = StrUtil.isNotEmpty(checkIdCardDuplicateVO.getContactPhone()) ? checkIdCardDuplicateVO.getContactPhone() : "";
                        orgStr.add(checkIdCardDuplicateVO.getOrgName() + "(组织联系方式：" + s + ")");
                    }
                }
                builder.append(CollUtil.join(orgStr, "、")).append("中的党员或发展党员中已存在，请核实");
                return new OutMessage<>(99999, builder.toString(), null);
            }
        }
        return null;
    }

    private OutMessage verifyOrgInfo(List<Org> orgList, String currManOrgCode) {
        for (Org org : orgList) {
            if (!StrUtil.startWith(org.getOrgCode(), currManOrgCode)) {
                return new OutMessage<>(500, "导入数据中的管理党组织：" + org.getName() + "不在当前账号管理下，请核查", null);
            }
            if (StrUtil.equals(org.getD01Code(), "A")) {
                return new OutMessage<>(500, "组织类别为其他（文件夹）下不能添加党员，请核查", null);
            }
        }
        return null;
    }
}
