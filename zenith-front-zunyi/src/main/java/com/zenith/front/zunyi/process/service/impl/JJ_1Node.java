package com.zenith.front.zunyi.process.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.zenith.front.api.mem.IMemDevelopService;
import com.zenith.front.common.constant.ProcessNodeEnum;
import com.zenith.front.common.kit.StrKit;
import com.zenith.front.model.bean.MemDevelop;
import com.zenith.front.model.message.OutMessage;
import com.zenith.front.model.message.Status;
import com.zenith.front.model.bean.MemDevelopProcess;
import com.zenith.front.zunyi.process.service.ProcessEngineService;
import com.zenith.front.zunyi.service.IMemDevelopProcessService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @since 2025-03-13 10:15
 */
@Slf4j
@Service
public class JJ_1Node implements ProcessEngineService {

    @Resource
    private IMemDevelopService memDevelopService;

    @Resource
    private IMemDevelopProcessService memDevelopProcessService;

    @Override
    public String getNode() {
        return ProcessNodeEnum.JJ_1.getNode();
    }

    @Override
    public OutMessage<ProcessNodeEnum> next(String currentNode, String businessId, Map<String, Object> paramMap) {
        return null;
    }

    private List<MemDevelopProcess> getMemDevelopProcessList1() {
        LambdaQueryWrapper<MemDevelopProcess> processWrapper = Wrappers.lambdaQuery();
        processWrapper.eq(MemDevelopProcess::getProcessNode, getNode());
        processWrapper.isNotNull(MemDevelopProcess::getDigitalLotNo);
        processWrapper.isNull(MemDevelopProcess::getApproveTime);
        processWrapper.isNull(MemDevelopProcess::getApproveUser);
        processWrapper.le(MemDevelopProcess::getExtendEndTime, DateUtil.date());
        // 上一个节点为空或者不属于第一次考察，这次则进入第一次考察
        processWrapper.apply("(ccp_mem_develop_process.previous_code is null or " +
                " exists (select 1 from ccp_mem_develop_process t1 where t1.code = ccp_mem_develop_process.previous_code and t1.process_node not in ('JJ_2', 'JJ_4')))");
        return this.memDevelopProcessService.list(processWrapper);
    }

    private List<MemDevelopProcess> getMemDevelopProcessList2() {
        LambdaQueryWrapper<MemDevelopProcess> processWrapper = Wrappers.lambdaQuery();
        processWrapper.eq(MemDevelopProcess::getProcessNode, getNode());
        processWrapper.isNotNull(MemDevelopProcess::getDigitalLotNo);
        processWrapper.isNull(MemDevelopProcess::getApproveTime);
        processWrapper.isNull(MemDevelopProcess::getApproveUser);
        processWrapper.le(MemDevelopProcess::getExtendEndTime, DateUtil.date());
        // 上一节点属于第一次考察或者超时，这次就进入第二次考察
        processWrapper.apply(" exists (select 1 from ccp_mem_develop_process t1 where t1.code = ccp_mem_develop_process.previous_code " +
                "and t1.process_node IN ('JJ_2', 'JJ_4')) ");
        return this.memDevelopProcessService.list(processWrapper);
    }

    @Override
    public OutMessage<?> task() {
        // JJ_1(待考察人员) ---> JJ_2(待第一次考察)
        task(getMemDevelopProcessList1(), ProcessNodeEnum.JJ_2.getNode());
        // JJ_1(待考察人员) ---> JJ_3(待第二次考察)
        task(getMemDevelopProcessList2(), ProcessNodeEnum.JJ_3.getNode());
        return new OutMessage<>(Status.SUCCESS);
    }

    private void task(List<MemDevelopProcess> memDevelopProcessList, String nextNode) {
        if (CollUtil.isNotEmpty(memDevelopProcessList)) {
            List<MemDevelop> memDevelopList = CollUtil.newArrayList();
            List<MemDevelopProcess> processCurrentList = CollUtil.newArrayList();
            List<MemDevelopProcess> processNextList = CollUtil.newArrayList();
            for (MemDevelopProcess processCurrent : memDevelopProcessList) {
                String digitalLotNo = processCurrent.getDigitalLotNo();
                String processNode = processCurrent.getProcessNode();
                LambdaQueryWrapper<MemDevelop> developWrapper = Wrappers.lambdaQuery();
                developWrapper.select(MemDevelop::getId, MemDevelop::getCode, MemDevelop::getDigitalLotNo, MemDevelop::getD08Code);
                developWrapper.eq(MemDevelop::getDigitalLotNo, digitalLotNo);
                developWrapper.eq(MemDevelop::getProcessNode, processNode);
                developWrapper.isNull(MemDevelop::getDeleteTime);
                developWrapper.last("limit 1");
                MemDevelop memDevelop = this.memDevelopService.getOne(developWrapper);
                if (ObjectUtil.isNotNull(memDevelop)) {
                    // 修改流程信息
                    memDevelop.setProcessNode(nextNode);
                    memDevelopList.add(memDevelop);
                    // 记录当前节点流程信息
                    processCurrent.setApproveUser("定时任务");
                    processCurrent.setApproveTime(DateUtil.date());
                    processCurrent.setUpdateUser("定时任务");
                    processCurrent.setUpdateTime(DateUtil.date());
                    processCurrentList.add(processCurrent);
                    // 记录下一节点流程信息
                    MemDevelopProcess processNext = new MemDevelopProcess();
                    processNext.setCode(StrKit.getRandomUUID());
                    processNext.setProcessNode(nextNode);
                    processNext.setDigitalLotNo(processCurrent.getDigitalLotNo());
                    processNext.setd08Code(processCurrent.getd08Code());
                    processNext.setExtendStarTime(processCurrent.getExtendStarTime());
                    processNext.setExtendEndTime(processCurrent.getExtendEndTime());
                    processNext.setPreviousCode(processCurrent.getCode());
                    processNext.setCreateUser("定时任务");
                    processNext.setCreateTime(DateUtil.date());
                    processNextList.add(processNext);
                }
            }
            if (CollUtil.isNotEmpty(memDevelopList)) {
                this.memDevelopService.updateBatchById(memDevelopList, 100);
            }
            if (CollUtil.isNotEmpty(processCurrentList)) {
                this.memDevelopProcessService.updateBatchById(processCurrentList, 100);
            }
            if (CollUtil.isNotEmpty(processNextList)) {
                this.memDevelopProcessService.saveBatch(processNextList, 100);
            }
            if (StrUtil.equals(nextNode, ProcessNodeEnum.JJ_2.getNode())) {
                log.info("完成【JJ_1(待考察人员) ---> JJ_2(待第一次考察)】定时任务.");
            }
            if (StrUtil.equals(nextNode, ProcessNodeEnum.JJ_3.getNode())) {
                log.info("完成【JJ_1(待考察人员) ---> JJ_3(待第二次考察)】定时任务.");
            }
        }
    }
}
