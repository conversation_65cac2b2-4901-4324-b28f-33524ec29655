package com.zenith.front.zunyi.process.service.impl;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.zenith.front.api.mem.IMemService;
import com.zenith.front.common.constant.ProcessNodeEnum;
import com.zenith.front.model.bean.Mem;
import com.zenith.front.model.message.OutMessage;
import com.zenith.front.model.message.Status;
import com.zenith.front.model.bean.MemDevelopProcess;
import com.zenith.front.zunyi.process.service.ProcessEngineService;
import com.zenith.front.zunyi.service.IMemDevelopProcessService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.Map;
import java.util.Objects;

/**
 * 转正前公示
 *
 * <AUTHOR>
 * @create_date 2025-02-24 14:38
 * @description
 */
@Service
@Slf4j
public class Ybq_3Node implements ProcessEngineService {

    @Resource
    private IMemDevelopProcessService developProcessService;
    @Resource
    private IMemService memService;

    /**
     * 当前流程节点名
     *
     * @return
     */
    @Override
    public String getNode() {
        return ProcessNodeEnum.YBQ_3.getNode();
    }

    /**
     * 下一步流程
     *
     * @param currentNode 当前节点
     * @param businessId  业务ID
     * @param paramMap    参数
     * @return
     */
    @Override
    public OutMessage<ProcessNodeEnum> next(String currentNode, String businessId, Map<String, Object> paramMap) {
        // 1、查询业务是否存在
        LambdaQueryWrapper<Mem> wrapper = Wrappers.lambdaQuery();
        wrapper.select(Mem::getId, Mem::getCode, Mem::getDigitalLotNo);
        wrapper.eq(Mem::getCode, businessId)
                .eq(Mem::getProcessNode, currentNode)
                // 预备党员
                .eq(Mem::getD08Code, "2")
                .isNull(Mem::getDeleteTime);
        Mem entity = memService.getOne(wrapper);
        if (Objects.isNull(entity) || StrUtil.isBlank(entity.getDigitalLotNo())) {
            log.error("业务信息不存在或档案批次唯一码不存在，业务ID：{}", businessId);
            return new OutMessage<>(5001, "业务信息不存在或档案批次唯一码不存在", null);
        }

        // 2、查询当前流程是否存在
        final String lotNo = entity.getDigitalLotNo();
        LambdaQueryWrapper<MemDevelopProcess> processLambdaQueryWrapper = Wrappers.lambdaQuery();
        processLambdaQueryWrapper.eq(MemDevelopProcess::getDigitalLotNo, lotNo)
                .eq(MemDevelopProcess::getProcessNode, currentNode)
                .isNull(MemDevelopProcess::getApproveTime);
        MemDevelopProcess currentProcess = developProcessService.getOne(processLambdaQueryWrapper);
        if (Objects.isNull(currentProcess) || StrUtil.isBlank(currentProcess.getCode())) {
            log.error("当前流程信息不存在，批次唯一码：{}", lotNo);
            return new OutMessage<>(5001, "当前流程信息不存在", null);
        }

        // 3、当前流程节点审核完成信息填写并保存
        currentProcess.setApproveUser(currentUser().getAccount());
        currentProcess.setApproveTime(new Date());
        developProcessService.updateById(currentProcess);

        // 4、生成下一步流程信息
        final ProcessNodeEnum nextNode = ProcessNodeEnum.YBQ_4_1;
        final MemDevelopProcess nextProcess = new MemDevelopProcess();
        nextProcess.setCode(IdUtil.simpleUUID());
        nextProcess.setDigitalLotNo(lotNo);
        nextProcess.setd08Code(currentProcess.getd08Code());
        nextProcess.setCreateTime(new Date());
        nextProcess.setCreateUser(currentUser().getAccount());
        nextProcess.setPreviousCode(currentProcess.getCode());
        nextProcess.setProcessNode(nextNode.getNode());
        nextProcess.setExtendStarTime(new Date());
        // 三个月内审批时间少于30天倒计时
        nextProcess.setExtendEndTime(DateUtil.offsetDay(DateUtil.offsetMonth(nextProcess.getExtendStarTime(), 3), -30));
        if (!developProcessService.save(nextProcess)) {
            log.error("流程流转失败，业务ID：{}", businessId);
            return new OutMessage<>(5001, "流程流转失败", null);
        }
        //  5、业务保存关键信息
//        entity.setProcessNode(nextProcess.getProcessNode());
//        memService.updateById(entity);
        LambdaUpdateWrapper<Mem> updateWrapper = Wrappers.lambdaUpdate();
        updateWrapper.set(Mem::getProcessNode, nextProcess.getProcessNode())
                .eq(Mem::getCode, entity.getCode());
        memService.update(updateWrapper);
        return new OutMessage<>(Status.SUCCESS, nextNode);
    }
}
