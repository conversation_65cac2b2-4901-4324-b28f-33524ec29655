package com.zenith.front.zunyi.service.impl;


import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.thread.ThreadUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.zenith.front.api.dict.IDictService;
import com.zenith.front.api.mem.IMemDevelopService;
import com.zenith.front.api.mem.IMemService;
import com.zenith.front.common.constant.CommonConstant;
import com.zenith.front.common.constant.DictConstant;
import com.zenith.front.common.constant.ProcessNodeCommon;
import com.zenith.front.common.constant.ProcessNodeEnum;
import com.zenith.front.common.kit.StrKit;
import com.zenith.front.core.cache.CacheUtils;
import com.zenith.front.dao.mapper.activist.ActivistTransferRecordMapper;
import com.zenith.front.dao.mapper.mem.*;
import com.zenith.front.dao.mapper.org.OrgMapper;
import com.zenith.front.dao.mapper.transfer.TransferRecordMapper;
import com.zenith.front.framework.file.core.MinioTemplate2;
import com.zenith.front.model.bean.*;
import com.zenith.front.model.constant.UserConstant;
import com.zenith.front.model.custom.Record;
import com.zenith.front.model.dto.SortDTO;
import com.zenith.front.model.message.OutMessage;
import com.zenith.front.model.message.Status;
import com.zenith.front.model.vo.DigitalCompletenessVO;
import com.zenith.front.zunyi.model.dto.*;
import com.zenith.front.zunyi.model.vo.ElectronicMemVO;
import com.zenith.front.zunyi.model.vo.ElectronicVO;
import com.zenith.front.zunyi.service.IMemDigitalAuditService;
import com.zenith.front.zunyi.service.IMemDigitalOperationLogService;
import com.zenith.front.zunyi.service.IMemDigitalService;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <p>
 * 党员（发展党员）数字档案表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-02-14
 */
@Service
public class MemDigitalServiceImpl extends ServiceImpl<MemDigitalMapper, MemDigital> implements IMemDigitalService {
    @Resource
    private MemMapper memMapper;
    @Resource
    private IMemService iMemService;
    @Resource
    private MemDevelopMapper memDevelopMapper;
    @Resource
    private IMemDevelopService iMemDevelopService;
    @Resource
    private IDictService dictService;
    @Resource
    private IMemDigitalOperationLogService memDigitalOperationLogService;
    @Resource
    private MinioTemplate2 minioTemplate2;
    @Resource
    private OrgMapper orgMapper;
    @Resource
    private TransferRecordMapper transferRecordMapper;
    @Resource
    private ActivistTransferRecordMapper activistTransferRecordMapper;
    @Resource
    private MemDevelopProcessMapper processMapper;
    @Resource
    private MemDevelopProcessAuditConfigMapper memDevelopProcessAuditConfigMapper;
    @Resource
    private MemDigitalCountMapper memDigitalCountMapper;
    @Resource
    private IMemDigitalAuditService iMemDigitalAuditService;



    /**
     * 档案目录
     *
     * <AUTHOR>
     */
    @Override
    public OutMessage memDigitalContents(String isCatalogue, String d08Code, String processNode, String memCode) {
        List<Record> recordNewList = memDigitalContentsList(isCatalogue, d08Code, processNode);
        // 如果是待考察人员阶段，则要区分是否已经考察过，并决定是否展示第五阶段目录
        if(Objects.equals(processNode, ProcessNodeEnum.YBQ_1_1.getNode())) {
            // 去流程中查询是否经过考察阶段
            LambdaQueryWrapper<Mem> lambdaQueryWrapper = Wrappers.lambdaQuery();
            lambdaQueryWrapper.select(Mem::getDigitalLotNo, Mem::getCode);
            lambdaQueryWrapper.isNull(Mem::getDeleteTime)
                    .eq(Mem::getCode, memCode)
                    .eq(Mem::getD08Code, CommonConstant.TWO);
            Mem mem = memMapper.selectOne(lambdaQueryWrapper);
            // 查询上级是否从满足转正跟满足考察过来的。
            if( !(Objects.nonNull(mem) && StrUtil.isNotBlank(mem.getDigitalLotNo()) && processMapper.parentYbqProcess(mem.getDigitalLotNo()) > 0)){
                // 不是的话则将档案目录第五阶段的剔除
                recordNewList = recordNewList.stream().filter(e -> !e.getStr("key").startsWith("5")).collect(Collectors.toList());
            }
        }

        // 如果是积极分子待考察人员阶段，则要区分是否已经考察过，并决定是否展示204"入党积极分子思想汇报"
        if(Objects.equals(processNode, ProcessNodeEnum.JJ_1.getNode()) || Objects.equals(processNode, ProcessNodeEnum.JJ_4.getNode())) {
            // 去流程中查询是否经过考察阶段
            LambdaQueryWrapper<MemDevelop> lambdaQueryWrapper = Wrappers.lambdaQuery();
            lambdaQueryWrapper.select(MemDevelop::getDigitalLotNo, MemDevelop::getCode);
            lambdaQueryWrapper.isNull(MemDevelop::getDeleteTime)
                    .eq(MemDevelop::getCode, memCode)
                    .eq(MemDevelop::getD08Code, CommonConstant.FOUR);
            MemDevelop mem = memDevelopMapper.selectOne(lambdaQueryWrapper);
            // 查询上级是否从待第一次考察、超半月过来的
            if( !(Objects.nonNull(mem) && StrUtil.isNotBlank(mem.getDigitalLotNo()) && processMapper.parentJjProcess(mem.getDigitalLotNo()) > 0)){
                // 不是的话则将204"入党积极分子思想汇报"的剔除
                recordNewList = recordNewList.stream().filter(e -> !e.getStr("key").equals("204")).collect(Collectors.toList());
            }
        }

        if (StrUtil.isBlank(isCatalogue) || StrUtil.equals(isCatalogue, CommonConstant.ONE)) {
            return new OutMessage<>(Status.SUCCESS, buildTree(recordNewList));
        } else {
            //0-只有固定的7个档案（入党申请书、入党积极分子、发展对象培养教育考察登记表、综合性政审报告、参加短期集中培训的结业证书、中国共产党入党志愿书、预备党员培训考察登记表、转正申请书）
            return new OutMessage<>(Status.SUCCESS, recordNewList);
        }
    }

    /**
     * 获取档案目录集合
     *
     * @param isCatalogue
     * @param d08Code
     * @return
     */
    private List<Record> memDigitalContentsList(String isCatalogue, String d08Code, String processNode) {
        if (StrUtil.isBlank(isCatalogue) || StrUtil.equals(isCatalogue, CommonConstant.ONE)) {
            List<Record> records = dictService.getAllNonHump("dict_d222");
            return this.getRecordNewList(records, d08Code, processNode);
        } else {
            //0-只有固定的7个档案（入党申请书、入党积极分子、发展对象培养教育考察登记表、综合性政审报告、参加短期集中培训的结业证书、中国共产党入党志愿书、预备党员培训考察登记表、转正申请书）
            return dictService.getDictByKeys(ProcessNodeCommon.OLD_ARCHIVE_MAP.keySet(), "dict_d222");
        }
    }

    /**
     * 党员档案材料查询
     *
     * @param digitalLotNo 档案唯一码
     * @param d222Code     目录code
     * @return
     */
    @Override
    public OutMessage listMemDigital(String digitalLotNo, String d222Code, String memCode) {
        if (StrUtil.isBlank(digitalLotNo)) {
            return new OutMessage<>(Status.SUCCESS);
        }

        // 是否已归档
        Integer isArchived = null;
        //获取党员档案
        final List<MemDigital> digitals = getDigitals(digitalLotNo, d222Code);
        if(CollUtil.isNotEmpty(digitals)) {
            Mem mem = iMemService.findByCode(memCode);
            if(Objects.nonNull(mem) && StrUtil.isNotBlank(mem.getCode())) {
                isArchived = mem.getIsArchived();
            }
        }
        for (MemDigital digital : digitals) {
            String path = digital.getPath();
            if (StrUtil.isNotBlank(path)) {
                digital.setPath(minioTemplate2.shareLink(path));
            }
            digital.setHistory(Objects.equals(isArchived, 1));
            // 申请许可到期时间
            Date applicantEndDate = digital.getApplicantEndDate();
            // 根据许可到期时间判断，只要当前时间小于数据许可到期时间，表示这个数据为当前许可期限数据
            if (Objects.nonNull(applicantEndDate) && applicantEndDate.getTime() >= DateUtil.date().getTime()) {
                digital.setHistory(false);
            }
        }
        //获取操作日志
        final List<MemDigitalOperationLog> operaterLogs = getOperaterLogs(digitalLotNo, d222Code);
        Record record = new Record();
        record.set("digitals", digitals);
        record.set("operaterLogs", operaterLogs);
        return new OutMessage<>(Status.SUCCESS, record);
    }

    /**
     * 获取档案类别
     *
     * @param digitalLotNo 档案批次唯一码
     * @return 1-绿色档案； 2-蓝色档案； 3-红色档案； 99-其他
     */
    @Override
    public Integer getDigitalType(String digitalLotNo) {
        LambdaQueryWrapper<Mem> wrapper = new LambdaQueryWrapper<Mem>().select(Mem::getIsSure)
                .eq(Mem::getDigitalLotNo, digitalLotNo)
                .orderByDesc(Mem::getDeleteTime).last("limit 1");
        Mem mem = iMemService.getOne(wrapper);
        if (ObjectUtil.isNull(mem)) {
            return 99;
        }
        // 党员身份认定：1-已认定；0-未认定
        Integer isSure = mem.getIsSure();
        // 查询档案中是否有《中国共产党入党志愿书》
        int count = count(new LambdaQueryWrapper<MemDigital>()
                .eq(MemDigital::getDigitalLotNo, digitalLotNo)
                .eq(MemDigital::getd222Code, "404")
                .isNull(MemDigital::getDeleteTime));

        // 档案类别-绿色：已上传入党志愿书，党员身份已认定的档案
        if (count > 0 && ObjectUtil.equals(isSure, 1)) {
            return CommonConstant.ONE_INT;
        }
        // 档案类别-蓝色：已上传入党志愿书，党员身份未认定的档案
        if (count > 0 && ObjectUtil.equals(isSure, 0)) {
            return CommonConstant.TWO_INT;
        }
        // 档案类别-红色：未上传入党志愿书，党员身份认定的档案
        if (count == 0 && ObjectUtil.equals(isSure, 1)) {
            return CommonConstant.THREE_INT;
        }
        return 99;
    }

    /**
     * 档案完整度：1-完整；0-不完整
     *
     * @param digitalLotNo 档案批次唯一码
     * @return 1-完整；0-不完整
     */
    @Override
    public Integer getIntegrality(String digitalLotNo) {
        if(StrUtil.isBlank(digitalLotNo)) {
            return CommonConstant.ZERO_INT;
        }
        List<DigitalCompletenessVO> data = this.withIntegrality(digitalLotNo);

        // 计算后都要清除旧的档案完整度，如果“档案完整度详细信息”都没得，那说明这个档案回退到某个节点了
        LambdaQueryWrapper<MemDigitalCount> deleteWrappers =  Wrappers.lambdaQuery();
        deleteWrappers.eq(MemDigitalCount::getDigitalLotNo, digitalLotNo);
        memDigitalCountMapper.delete(deleteWrappers);

        if(CollUtil.isNotEmpty(data)) {
            List<DigitalCompletenessVO> effective = new ArrayList<>();
            data.forEach(e -> effective.addAll(e.getChilds()));
            final Date createDate = new Date();
            List<MemDigitalCount> saveList = effective.stream().map(e -> {
                MemDigitalCount counts = new MemDigitalCount();
                counts.setCode(IdUtil.simpleUUID());
                counts.setD222Code(e.getD222Code());
                counts.setD222Name(e.getD222Name());
                counts.setDigitalLotNo(digitalLotNo);
                counts.setMinNum(e.getMinNum());
                counts.setNum(e.getNum());
                counts.setParentCode(e.getParentCode());
                counts.setProcessNode(e.getProcessNode());
                counts.setCreateTime(createDate);
                return counts;
            }).collect(Collectors.toList());
            if(CollUtil.isNotEmpty(saveList)) {
                memDigitalCountMapper.myInserBatch(saveList);
            }
        }

        if (CollUtil.isEmpty(data) || CollUtil.isNotEmpty(data) && data.stream().anyMatch(e -> !e.getIntegrality())) {
            return CommonConstant.ZERO_INT;
        }
        return CommonConstant.ONE_INT;
    }

    /**
     * 查询档案上传详细情况
     *
     * @param digitalLotNoList
     * @return
     */
    @Override
    public Map<String, List<DigitalCompletenessVO>> getBatchDigCount(Collection<String> digitalLotNoList) {
        // dict_d222
        final List<Record> records = dictService.getAllNonHump("dict_d222");
        // 顶级档案阶段目录
        final List<DigitalCompletenessVO> parentD222List = new ArrayList<>();
        for (Record record : records) {
            String parent = record.getStr("parent");
            String key = record.getStr("key");
            String name = record.getStr("name");
            if(StrUtil.equals(parent, "-1")) {
                DigitalCompletenessVO vo = new DigitalCompletenessVO();
                vo.setD222Code(key);
                vo.setD222Name(name);
                vo.setParentCode(parent);
                parentD222List.add(vo);
            }
        }
        // 批量查询
        List<DigitalCompletenessVO> dataList = memDigitalCountMapper.getDigCount(digitalLotNoList);

        // 根据档案唯一码分组
        Map<String, List<DigitalCompletenessVO>> digGroup = dataList.stream().collect(Collectors.groupingBy(DigitalCompletenessVO::getDigitalLotNo));

        digGroup.keySet().forEach(digitalLotNo -> {
            // 分组获取当前固定传的档案信息
            // 剔除出没有子档案的顶级目录
            Map<String, List<DigitalCompletenessVO>> groupDig = digGroup.get(digitalLotNo).stream().collect(Collectors.groupingBy(DigitalCompletenessVO::getParentCode));
            digGroup.put(digitalLotNo,
                    parentD222List.stream().map(e -> {
                        DigitalCompletenessVO vo = new  DigitalCompletenessVO();
                        BeanUtils.copyProperties(e, vo);
                        List<DigitalCompletenessVO> g1 = groupDig.getOrDefault(e.getD222Code(), null);
                        if(CollUtil.isNotEmpty(g1)) {
                            // 统计表存储的是每个流程节点上传的每个档案应上传跟实上传数。 所以这里处理相同档案类型合并成一条，统计总应上传跟实上传数量
                            Map<String, List<DigitalCompletenessVO>> g2 = g1.stream().collect(Collectors.groupingBy(DigitalCompletenessVO::getD222Code));
                            List<DigitalCompletenessVO> childList = new ArrayList<>();
                            for (String d222Code : g2.keySet()) {
                                DigitalCompletenessVO d2 = new DigitalCompletenessVO();
                                List<DigitalCompletenessVO> v2 = g2.get(d222Code);
                                BeanUtils.copyProperties(v2.get(0), d2);
                                d2.setNum(0);
                                d2.setMinNum(0);
                                for (DigitalCompletenessVO dvo : v2) {
                                    d2.setNum(dvo.getNum() + d2.getNum());
                                    d2.setMinNum(dvo.getMinNum() + d2.getMinNum());
                                }
                                childList.add(d2);
                            }
                            vo.setChilds(childList);
                        }
                        return vo;
                    }).filter(e -> CollUtil.isNotEmpty(e.getChilds())).collect(Collectors.toList()));
        });
        return digGroup;
    }

    /**
     * 获取固定的7个档案 或者 档案唯一码为空的
     * 没得档案唯一码的获取固定7个档案目录，只有历史中的正式党员并且没上传过档案的才没有档案唯一码
     * @param records dict_d222 档案目录全量
     * @param parentD222List 顶级档案阶段目录
     * @param d222CountMap 已上传的档案-数量
     * @param dataList 记录档案匹配信息
     * @return
     */
    private List<DigitalCompletenessVO> getNullDigOrIsCatalogueCount(List<Record> records, List<DigitalCompletenessVO> parentD222List,
                                                                     Map<String, Integer> d222CountMap, List<DigitalCompletenessVO> dataList) {
        // 只判断固定的7个档案的数据是否完整，只要每个档案至少有一张都算完整
        List<Record> d222Records = records.stream().filter(e -> ProcessNodeCommon.OLD_ARCHIVE_MAP.containsKey(e.getStr("key"))).collect(Collectors.toList());
        for (Record record : d222Records) {
            subtraction(dataList, d222CountMap, record, null);
        }
        Map<String, List<DigitalCompletenessVO>> groupDig = dataList.stream()
                // 固定档案中如果身份认定资料或说明材料 未上传，则不生成统计信息
                .filter(e -> !(Objects.equals(e.getD222Code(), "9902") && 0 == e.getNum())).collect(Collectors.groupingBy(DigitalCompletenessVO::getParentCode));
        for (DigitalCompletenessVO digitalCompletenessVO : parentD222List) {
            digitalCompletenessVO.setChilds(groupDig.getOrDefault(digitalCompletenessVO.getD222Code(), null));
        }
        // 剔除出没有子档案的顶级目录
        return parentD222List.stream().filter(e -> CollUtil.isNotEmpty(e.getChilds())).collect(Collectors.toList());
    }

    /**
     * 获取档案完整度详细信息
     * @param digitalLotNo
     * @return
     */
    @Override
    public List<DigitalCompletenessVO> withIntegrality(String digitalLotNo){
        // dict_d222
        final List<Record> records = dictService.getAllNonHump("dict_d222");
        // 顶级档案阶段目录
        final List<DigitalCompletenessVO> parentD222List = new ArrayList<>();
        // node流程 ：List<需要上传的档案code>
        final Map<String, List<Record>> nodeD222CodeMap = new HashMap<>();
        // 记录档案匹配信息
        final List<DigitalCompletenessVO> dataList = new ArrayList<>();
        // 已上传的档案-数量
        final Map<String, Integer> d222CountMap = new HashMap<>(100);
        for (Record record : records) {
            String parent = record.getStr("parent");
            String process = record.getStr("processNode");
            String key = record.getStr("key");
            String name = record.getStr("name");
            // 非顶级的并且流程数据不为空
            if(!Objects.equals(parent, "-1") && StrUtil.isNotBlank(process)) {
                String[] processList = process.split(CommonConstant.DOU_HAO_STRING);
                for (String node : processList) {
                    List<Record> d0222List = nodeD222CodeMap.getOrDefault(node,new ArrayList<>());
                    d0222List.add(record);
                    nodeD222CodeMap.put(node, d0222List);
                }
            }
            if(StrUtil.equals(parent, "-1")) {
                DigitalCompletenessVO vo = new DigitalCompletenessVO();
                vo.setD222Code(key);
                vo.setD222Name(name);
                vo.setParentCode(parent);
                parentD222List.add(vo);
            }
        }
        // 没有档案编号时直接返回固定7个档案目录
        if(StrUtil.isBlank(digitalLotNo)) {
            return this.getNullDigOrIsCatalogueCount(records, parentD222List, d222CountMap, dataList);
        }

        // 查询档案记录表
        List<MemDigital> list = Optional.ofNullable(list(new LambdaQueryWrapper<MemDigital>().select(MemDigital::getProcessNode, MemDigital::getd222Code)
                .eq(MemDigital::getDigitalLotNo, digitalLotNo)
                .isNull(MemDigital::getDeleteTime)
                .orderByDesc(MemDigital::getCreateTime))).orElse(new ArrayList<>());

        Map<String, List<MemDigital>> collect = list.stream().filter(e -> StrUtil.isNotBlank(e.getd222Code())).collect(Collectors.groupingBy(MemDigital::getd222Code));
        collect.forEach((k, v) -> d222CountMap.put(k, collect.get(k).size()));

        LambdaQueryWrapper<Mem> wrapper = new LambdaQueryWrapper<Mem>()
                .eq(Mem::getDigitalLotNo, digitalLotNo)
                .orderByDesc(Mem::getDeleteTime)
                .last("limit 1");
        wrapper.select(Mem::getCode, Mem::getIsCatalogue, Mem::getProcessNode);
        Mem mem = iMemService.getOne(wrapper);
        // isCatalogue  档案目录区分：0-只有固定的7个档案， 1-所有档案目录
        if (Objects.nonNull(mem) && Objects.equals(mem.getIsCatalogue(), 0)) {
            // 只判断固定的7个档案的数据是否完整，只要每个档案至少有一张都算完整
            return this.getNullDigOrIsCatalogueCount(records, parentD222List, d222CountMap, dataList);
        }

        // 获取基础信息当前流程节点
        String curNode = "";
        if (Objects.nonNull(mem)) {
            curNode = mem.getProcessNode();
        } else {
            LambdaQueryWrapper<MemDevelop> developWrapper = new LambdaQueryWrapper<MemDevelop>()
                    .eq(MemDevelop::getDigitalLotNo, digitalLotNo)
                    .orderByDesc(MemDevelop::getDeleteTime)
                    .last("limit 1");
            developWrapper.select(MemDevelop::getCode, MemDevelop::getProcessNode);
            MemDevelop memDevelop = memDevelopMapper.selectOne(developWrapper);
            if (Objects.nonNull(memDevelop)) {
                curNode = memDevelop.getProcessNode();
            }
        }
        final String processNode = curNode;
        if (StrUtil.isBlank(processNode)) {
            return new ArrayList<>();
        }

        // 完整度跟根据节点上传文件的数量有关联，每个节点都需要判断档案完整度
        Map<Integer, ProcessNodeEnum> nodeList = new HashMap<>();
        List<ProcessNodeEnum> nodeEnumList = Arrays.stream(ProcessNodeEnum.values())
                .sorted(Comparator.comparing(ProcessNodeEnum::getSort)).collect(Collectors.toList());
        for (ProcessNodeEnum nodeEnum : nodeEnumList) {
            if (StrUtil.equals(processNode, nodeEnum.getNode())) {
                nodeList.remove(nodeEnum.getSort());
                break;
            }
            nodeList.put(nodeEnum.getSort(), nodeEnum);
        }
        List<ProcessNodeEnum> courseList = new ArrayList<>(nodeList.values());

        // 1、节点为JJ_1、JJ_4、YBQ_1_1时要获取上一来计算流程正确过程，将不属于的流程节点剔除
        if(StrUtil.equalsAny(processNode, ProcessNodeEnum.JJ_1.getNode(), ProcessNodeEnum.JJ_4.getNode(), ProcessNodeEnum.YBQ_1_1.getNode())) {
            String previousProcessNode = processMapper.previousProcessNode(digitalLotNo);
            // JJ_1 的上一步是RD_4，则剔除 JJ_2
            if(StrUtil.equals(processNode, ProcessNodeEnum.JJ_1.getNode()) &&
                    (StrUtil.isBlank(previousProcessNode) || StrUtil.equals(previousProcessNode, ProcessNodeEnum.RD_4.getNode()))) {
                courseList.remove(ProcessNodeEnum.JJ_2);
            }
            // JJ_4 的上一步是 JJ_2，则剔除 JJ_3、JJ_5
            if(StrUtil.equals(processNode, ProcessNodeEnum.JJ_4.getNode()) &&
                    (StrUtil.isBlank(previousProcessNode) || StrUtil.equals(previousProcessNode, ProcessNodeEnum.JJ_2.getNode()))) {
                courseList.remove(ProcessNodeEnum.JJ_3);
                courseList.remove(ProcessNodeEnum.JJ_5);
                courseList.remove(ProcessNodeEnum.JJ_2);
            }
            // JJ_4 的上一步是 JJ_3，则剔除 JJ_5
            if(StrUtil.equals(processNode, ProcessNodeEnum.JJ_4.getNode()) &&
                    (StrUtil.isBlank(previousProcessNode) || StrUtil.equals(previousProcessNode, ProcessNodeEnum.JJ_3.getNode()))) {
                courseList.remove(ProcessNodeEnum.JJ_5);
                courseList.remove(ProcessNodeEnum.JJ_3);
            }

            // YBQ_1_1 的上一步是 FZ_7，则剔除 YBQ_1_2
            if(StrUtil.equals(processNode, ProcessNodeEnum.YBQ_1_1.getNode()) &&
                    (StrUtil.isBlank(previousProcessNode) || StrUtil.equals(previousProcessNode, ProcessNodeEnum.FZ_7.getNode()))) {
                courseList.remove(ProcessNodeEnum.YBQ_1_2);
            }
        }
        ProcessNodeEnum pne = ProcessNodeEnum.find(processNode);
        if (pne.getSort() >= 12) {
            // 过了JJ_6的流程，就不计算超半月未按时考察(属于中转节点)、持续考察人员(属于非必须得节点，可以跳过)的流程
            courseList.remove(ProcessNodeEnum.JJ_4);
            courseList.remove(ProcessNodeEnum.JJ_5);
        }

        courseList = courseList.stream().sorted(Comparator.comparing(ProcessNodeEnum::getSort)).collect(Collectors.toList());

        // 循环判断节点内的档案数量是否完整
        courseList.forEach(process -> {
            String node = process.getNode();
            List<Record> d222List = nodeD222CodeMap.getOrDefault(node, new ArrayList<>());
            d222List.forEach(record -> subtraction(dataList, d222CountMap, record, node));
        });

        Map<String, List<DigitalCompletenessVO>> groupDig = dataList.stream().collect(Collectors.groupingBy(DigitalCompletenessVO::getParentCode));
        for (DigitalCompletenessVO digitalCompletenessVO : parentD222List) {
            digitalCompletenessVO.setChilds(groupDig.getOrDefault(digitalCompletenessVO.getD222Code(), null));
        }
        // 剔除出没有子档案的顶级目录
        return parentD222List.stream().filter(e -> CollUtil.isNotEmpty(e.getChilds())).collect(Collectors.toList());
    }

    /**
     * 通过至少上传量设置缺少档案数量
     *
     * @param map          缺少的档案及数量
     * @param d222CountMap 数据库中已有档案代码及数量
     * @param d222Code     原型中需要上传的档案代码
     */
    public static void setNoArchivesMapByMin(Map<String, Integer> map, Map<String, Integer> d222CountMap, String d222Code) {
        Integer minNum = ProcessNodeCommon.MIN_ARCHIVE_MAP.getOrDefault(d222Code, 1);
        Integer dbCount = d222CountMap.get(d222Code);
        if (Objects.isNull(dbCount) || dbCount == 0) {
            map.put(d222Code, minNum);
        } else {
            if (dbCount < minNum) {
                // 设置缺少档案数量
                map.put(d222Code, Math.toIntExact(minNum - dbCount));
            }
        }
    }

    /**
     * 将数据库已有的数量顺序分配相减，最后不足的那个阶段则属于不完整阶段
     * @param dataList 记录当前流程跟档案的数量信息
     * @param d222CountMap 数据库中已有档案代码及数量
     * @param d222Record 原型中需要上传的档案字典信息
     * @param processNode 流程节点
     */
    public static void subtraction(List<DigitalCompletenessVO> dataList, Map<String, Integer> d222CountMap, Record d222Record, String processNode) {
        Integer minNum = 1;
        boolean special = false;
        String d222Code = d222Record.getStr("key");
        String d222Name = d222Record.getStr("name");
        String parentCode = d222Record.getStr("parent");

        // 获取特殊张数限制
        if(ProcessNodeCommon.MIN_NODE_ARCHIVE_MAP.containsKey(d222Code) && ProcessNodeCommon.MIN_NODE_ARCHIVE_MAP.get(d222Code).containsKey(processNode)) {
            minNum = ProcessNodeCommon.MIN_NODE_ARCHIVE_MAP.get(d222Code).get(processNode);
            special = true;
        }
        Integer dbCount = d222CountMap.getOrDefault(d222Code, 0);
        DigitalCompletenessVO vo = new DigitalCompletenessVO();
        vo.setProcessNode(processNode);
        vo.setD222Code(d222Code);
        vo.setD222Name(d222Name);
        vo.setParentCode(parentCode);
        vo.setMinNum(minNum);
        vo.setNum(dbCount);
        // 特殊张数限制的会共享总张数。这里减去后赋值，后续阶段接着计算
        if(special && dbCount >= minNum){
            d222CountMap.put(d222Code, dbCount - minNum);
            vo.setNum(minNum);
        }
        dataList.add(vo);
    }


    private List<MemDigitalOperationLog> getOperaterLogs(String digitalLotNo, String d222Code) {
        LambdaQueryWrapper<MemDigitalOperationLog> query = new LambdaQueryWrapper<MemDigitalOperationLog>()
                .eq(MemDigitalOperationLog::getDigitalLotNo, digitalLotNo)
                .eq(MemDigitalOperationLog::getD222Code, d222Code)
                .isNull(MemDigitalOperationLog::getDeleteTime)
                .orderByDesc(MemDigitalOperationLog::getOprationTime);
        return memDigitalOperationLogService.list(query);
    }

    @Override
    public List<MemDigital> getDigitals(String digitalLotNo, String d222Code) {
        LambdaQueryWrapper<MemDigital> query = new LambdaQueryWrapper<MemDigital>()
                .eq(MemDigital::getDigitalLotNo, digitalLotNo)
                .eq(MemDigital::getd222Code, d222Code)
                .isNull(MemDigital::getDeleteTime)
                .orderByAsc(MemDigital::getSort)
                .orderByAsc(MemDigital::getCreateTime)
                .orderByAsc(MemDigital::getCode);
        return this.list(query);
    }

    @Override
    public List<MemDigital> getUploadDigitalList(String digitalLotNo) {
        LambdaQueryWrapper<MemDigital> query = new LambdaQueryWrapper<MemDigital>()
                .eq(MemDigital::getDigitalLotNo, digitalLotNo)
                .isNull(MemDigital::getDeleteTime)
                .orderByAsc(MemDigital::getSort)
                .orderByAsc(MemDigital::getCreateTime)
                .orderByAsc(MemDigital::getCode);
        return this.list(query);
    }

    /**
     * 是否认定
     *
     * @param memCode
     * @param sure
     * @return
     */
    @Override
    public OutMessage isSure(String memCode, Integer sure) {
        if (Objects.isNull(sure)) {
            return new OutMessage(Status.NOT_NULL_ERROR);
        }
        LambdaQueryWrapper<Mem> query = new LambdaQueryWrapper<Mem>()
                .eq(Mem::getCode, memCode)
                .in(Mem::getD08Code, "1", "2")
                .isNull(Mem::getDeleteTime)
                .last(" LIMIT 1");
        query.select(Mem::getId, Mem::getCode, Mem::getDigitalLotNo);
        Mem mem = memMapper.selectOne(query);
        if (Objects.isNull(mem) || StrUtil.isBlank(mem.getCode())) {
            return new OutMessage(Status.MEM_IS_ERROR);
        }
        mem.setIsSure(sure);
        int b = memMapper.updateById(mem);

        if (b > 0) {
            // 可以异步执行
            ThreadUtil.execAsync(() -> {
                // 档案完整度执行
                Integer integrality = this.getIntegrality(mem.getDigitalLotNo());
                // 档案类别
                Integer digitalType = this.getDigitalType(mem.getDigitalLotNo());
                Mem updateMem = new Mem();
                updateMem.setId(mem.getId());
                updateMem.setIsIntegrality(integrality);
                updateMem.setDigitalType(digitalType);
                memMapper.updateById(updateMem);
            });
        }

        return new OutMessage(Status.SUCCESS);
    }

    /**
     * 档案清除
     */
    @Override
    public OutMessage<?> cleanDigital(CommonDto commonDto, User user) {
        List<String> codeList = commonDto.getCodeList();
        String digitalLotNo = commonDto.getDigitalLotNo();
        if (StrUtil.isBlank(digitalLotNo)) {
            return new OutMessage<>(Status.NOT_NULL_ERROR);
        }
        if (CollUtil.isEmpty(codeList)) {
            return new OutMessage<>(Status.SUCCESS);
        }
        LambdaQueryWrapper<MemDigital> wrapper = new LambdaQueryWrapper<MemDigital>()
                .isNull(MemDigital::getDeleteTime)
                .eq(MemDigital::getDigitalLotNo, commonDto.getDigitalLotNo())
                .in(MemDigital::getCode, codeList)
                .orderByAsc(MemDigital::getCreateTime).orderByAsc(MemDigital::getCode);
        List<MemDigital> list = list(wrapper);
        if (CollUtil.isEmpty(list)) {
            return new OutMessage<>(Status.SUCCESS);
        }
        String oprationUser = commonDto.getOprationUser();
        list.forEach(memDigital -> {
            memDigital.setDeleteTime(new Date());
            memDigital.setUpdateTime(new Date());
            memDigital.setUpdateUser(oprationUser);
        });
        boolean b = updateBatchById(list, list.size());
        if (b) {
            // 更新mem表入党宣誓材料标识
            List<MemDigital> digitals = this.getDigitals(digitalLotNo, "9901");
            if (CollUtil.isEmpty(digitals)) {
                LambdaUpdateWrapper<Mem> updateWrapper = Wrappers.lambdaUpdate();
                updateWrapper.set(Mem::getIsOath, 0)
                        .eq(Mem::getIsOath, 1).eq(Mem::getDigitalLotNo, digitalLotNo);
                iMemService.update(updateWrapper);
            }
            MemDigitalOperationLog operationLog = new MemDigitalOperationLog();
            operationLog.setCode(StrKit.getRandomUUID());
            operationLog.setProcessNode(CollUtil.join(list.stream().map(MemDigital::getProcessNode).collect(Collectors.toSet()), CommonConstant.DOU_HAO_STRING));
            operationLog.setDigitalLotNo(digitalLotNo);
            operationLog.setD222Code(CollUtil.join(list.stream().map(MemDigital::getd222Code).collect(Collectors.toSet()), CommonConstant.DOU_HAO_STRING));
            operationLog.setD222Name(CollUtil.join(list.stream().map(MemDigital::getd222Name).collect(Collectors.toSet()), CommonConstant.DOU_HAO_STRING));
            operationLog.setDigitalNames(CollUtil.join(list.stream().map(MemDigital::getName).collect(Collectors.toList()), CommonConstant.DOU_HAO_STRING));
            operationLog.setDigitalCodes(CollUtil.join(list.stream().map(MemDigital::getCode).collect(Collectors.toList()), CommonConstant.DOU_HAO_STRING));
            operationLog.setOprationTime(new Date());
            operationLog.setOprationUser(oprationUser);
            operationLog.setOprationOrgName(CacheUtils.getOrgName(user.getOrgId()));
            operationLog.setOprationType(CommonConstant.TWO_INT);
            operationLog.setCreateTime(new Date());
            operationLog.setCreateUser(user.getAccount());

            ThreadUtil.execAsync(() -> {
                memDigitalOperationLogService.save(operationLog);

                // 更新档案完整度、档案类别
                Integer integrality = getIntegrality(digitalLotNo);
                iMemDevelopService.update(new LambdaUpdateWrapper<MemDevelop>().set(MemDevelop::getIsIntegrality, integrality)
                        .eq(MemDevelop::getDigitalLotNo, digitalLotNo));
                // 是否上传入党志愿书
                Integer volunteer = getIsVolunteer(digitalLotNo);
                iMemService.update(new LambdaUpdateWrapper<Mem>()
                        .set(Mem::getIsIntegrality, integrality).set(Mem::getDigitalType, this.getDigitalType(digitalLotNo))
                        .set(Mem::getIsVolunteer, volunteer)
                        .eq(Mem::getDigitalLotNo, digitalLotNo));
            });
        }
        return new OutMessage<>(b ? Status.SUCCESS : Status.FAIL);
    }

    /**
     * 档案排序
     */
    @Override
    public OutMessage<?> sortDigital(CommonDto commonDto, User user) {
        String digitalLotNo = commonDto.getDigitalLotNo();
        List<SortDTO> sortDTOList = commonDto.getSortDTOList();
        if (StrUtil.isBlank(digitalLotNo) || CollUtil.isEmpty(sortDTOList)) {
            return new OutMessage<>(Status.NOT_NULL_ERROR);
        }
        Set<String> collect = sortDTOList.stream().map(SortDTO::getCode).collect(Collectors.toSet());
        if (CollUtil.isEmpty(collect)) {
            return new OutMessage<>(Status.SUCCESS);
        }

        LambdaQueryWrapper<MemDigital> wrapper = new LambdaQueryWrapper<MemDigital>()
                .eq(MemDigital::getDigitalLotNo, digitalLotNo)
                .in(MemDigital::getCode, collect)
                .isNull(MemDigital::getDeleteTime);
        List<MemDigital> list = list(wrapper);
        if (CollUtil.isNotEmpty(list)) {
            Map<String, Integer> integerMap = sortDTOList.stream().collect(Collectors.toMap(SortDTO::getCode, SortDTO::getSort));
            list.forEach(memDigital -> memDigital.setSort(integerMap.get(memDigital.getCode())));
            boolean b = updateBatchById(list, list.size());
            return new OutMessage<>(b ? Status.SUCCESS : Status.FAIL);
        }
        return new OutMessage<>(Status.SUCCESS);
    }

    /**
     * 获取档案操作日志列表
     */
    @Override
    public OutMessage getDigitalLogList(MemListNewDTO data, User user) {
        Integer pageNum = data.getPageNum();
        Integer pageSize = data.getPageSize();
        LambdaQueryWrapper<MemDigitalOperationLog> wrapper = new QueryWrapper<MemDigitalOperationLog>().lambda();
        wrapper.eq(MemDigitalOperationLog::getDigitalLotNo, data.getDigitalLotNo())
                .isNull(MemDigitalOperationLog::getDeleteTime);
        wrapper.orderByDesc(MemDigitalOperationLog::getCreateTime);

        Page<MemDigitalOperationLog> page = memDigitalOperationLogService.page(new Page<>(pageNum, pageSize), wrapper);
        return new OutMessage<>(Status.SUCCESS, page);
    }

    /**
     * 档案管理里面的上传档案
     */
    @Override
    public OutMessage uploadDigital(UploadFileDigitalDto digitalDto, User user) {
        String code = digitalDto.getCode();
        String d08Code = digitalDto.getD08Code();
        List<FileDigitalDto> filesList = digitalDto.getFilesList();
        if (StrUtil.isBlank(d08Code) || StrUtil.isBlank(code) || CollUtil.isEmpty(filesList)) {
            return new OutMessage<>(Status.NOT_NULL_ERROR);
        }
        // 前端没获取到档案批次码，从数据库中查询
        if (StrUtil.isBlank(digitalDto.getDigitalLotNo())) {
            Mem mem = iMemService.findAllByCode(code);
            String dbLotNo = Objects.nonNull(mem) ? mem.getDigitalLotNo() : null;
            if (StrUtil.isBlank(dbLotNo)) {
                MemDevelop memDevelop = iMemDevelopService.findAllByCode(code);
                dbLotNo = Objects.nonNull(memDevelop) ? memDevelop.getDigitalLotNo() : null;
            }
            digitalDto.setDigitalLotNo(dbLotNo);
        }
        // 旧数据首次进入上传档案，自动生成档案批次唯一码
        if (StrUtil.isBlank(digitalDto.getDigitalLotNo())) {
            String uuid = StrKit.getRandomUUID();
            iMemService.update(new LambdaUpdateWrapper<Mem>().set(Mem::getDigitalLotNo, uuid).isNull(Mem::getDigitalLotNo).eq(Mem::getCode, code));
            iMemDevelopService.update(new LambdaUpdateWrapper<MemDevelop>().set(MemDevelop::getDigitalLotNo, uuid).isNull(MemDevelop::getDigitalLotNo).eq(MemDevelop::getCode, code));
            digitalDto.setDigitalLotNo(uuid);
        }
        final String digitalLotNo = digitalDto.getDigitalLotNo();

        DevelopStepLogNewDTO stepLogNewDTO = new DevelopStepLogNewDTO();
        BeanUtils.copyProperties(digitalDto, stepLogNewDTO);
        //发展党员数字档案、数字档案操作日志
        boolean b = processFileDigital(stepLogNewDTO, user);
        if (b) {
            ThreadUtil.execAsync(() -> {
                // 更新mem表入党宣誓材料标识
                List<MemDigital> digitals = this.getDigitals(digitalLotNo, "9901");
                // 是否上传入党志愿书
                Integer volunteer = this.getIsVolunteer(digitalLotNo);

                Integer integrality = getIntegrality(digitalLotNo);
                iMemDevelopService.update(new LambdaUpdateWrapper<MemDevelop>().set(MemDevelop::getIsIntegrality, integrality)
                        .eq(MemDevelop::getDigitalLotNo, digitalLotNo).eq(MemDevelop::getCode, code));
                iMemService.update(new LambdaUpdateWrapper<Mem>().set(Mem::getIsIntegrality, integrality).set(Mem::getDigitalType, this.getDigitalType(digitalLotNo))
                        .set(Mem::getIsOath, CollUtil.isEmpty(digitals) ? 0 : 1)
                        .set(Mem::getIsVolunteer, volunteer)
                        .eq(Mem::getDigitalLotNo, digitalLotNo).eq(Mem::getCode, code));
            });
        }
        return new OutMessage<>(b ? Status.SUCCESS : Status.FAIL);
    }

    public boolean processFileDigital(DevelopStepLogNewDTO stepLogDTO, User user) {
        List<FileDigitalDto> filesList = stepLogDTO.getFilesList();
        if (CollUtil.isEmpty(filesList)) {
            return false;
        }

        List<MemDigital> saveList = new ArrayList<>();
        List<MemDigital> updateList = new ArrayList<>();
        // 数据库中的档案数据
        Map<String, List<FileDigitalDto>> digitalCodeMap = filesList.stream().filter(e -> StrUtil.isNotBlank(e.getCode()))
                .collect(Collectors.groupingBy(FileDigitalDto::getCode));

        if (CollUtil.isNotEmpty(digitalCodeMap)) {
            Set<String> codes = digitalCodeMap.keySet();
            LambdaQueryWrapper<MemDigital> wrapper = Wrappers.lambdaQuery();
            wrapper.select(MemDigital::getCode, MemDigital::getPath)
                    .in(MemDigital::getCode, codes);
            List<MemDigital> list = list(wrapper);
            // 获取数据库中的文件路径
            final Map<String, String> pathMap = list.stream().filter(e -> StrUtil.isNotBlank(e.getPath())).collect(Collectors.toMap(MemDigital::getCode, MemDigital::getPath));
            digitalCodeMap.forEach((key, value) -> {
                FileDigitalDto dto = value.get(0);
                MemDigital memDigital = new MemDigital();
                memDigital.setCode(key);
                memDigital.setSort(dto.getSort());
                if (Objects.equals(dto.getIsDelete(), 1)) {
                    memDigital.setDeleteTime(new Date());
                    memDigital.setUpdateTime(new Date());
                    memDigital.setUpdateUser(stepLogDTO.getOprationUser());
                    // 如果对这条信息进行了删除处理，相对应附件也删除处理
                    if(pathMap.containsKey(key)) {
                        minioTemplate2.delete(pathMap.get(key));
                    }
                }
                // 如果传递的参数文件路径是临时桶开头的，则要将原本的附件删除并把临时桶的拷贝到相对应桶中
                if(StrUtil.isNotBlank(dto.getPath()) && StrUtil.startWithIgnoreCase(dto.getPath(), MinioTemplate2.MINIO_TMP_BUCKET)) {
                    memDigital.setPath(minioTemplate2.copyTmpToTarget(dto.getPath()));
                    memDigital.setUpdateTime(new Date());
                    if(pathMap.containsKey(key)) {
                        // 如果这条信息数据库中有历史文件路径，则删除不需要的附件
                        minioTemplate2.delete(pathMap.get(key));
                    }
                }
                updateList.add(memDigital);
            });
        }

        // 新增的档案
        final List<FileDigitalDto> saveFilesList = filesList.stream().filter(e -> StrUtil.isBlank(e.getCode())).collect(Collectors.toList());
        if (CollUtil.isNotEmpty(saveFilesList)) {
            saveFilesList.forEach(e -> {
                // 数字档案表
                MemDigital memDigital = new MemDigital();
                memDigital.setCode(StrKit.getRandomUUID());
                memDigital.setProcessNode(stepLogDTO.getProcessNode());
                memDigital.setDigitalLotNo(stepLogDTO.getDigitalLotNo());
                memDigital.setName(e.getName());
                memDigital.setPath(minioTemplate2.copyTmpToTarget(e.getPath()));
                memDigital.setd222Code(e.getD222Code());
                memDigital.setd222Name(e.getD222Name());
                memDigital.setd08Code(stepLogDTO.getD08Code());
                memDigital.setSort(e.getSort());
                memDigital.setOprationUser(stepLogDTO.getOprationUser());
                memDigital.setOprationCode(stepLogDTO.getOprationCode());
                memDigital.setCreateTime(new Date());
                memDigital.setCreateUser(user.getAccount());
                memDigital.setFileSize(e.getFileSize());
                // 补充上传的申请许可到期时间
                memDigital.setApplicantEndDate(stepLogDTO.getApplicantEndDate());
                saveList.add(memDigital);
            });
        }

        List<MemDigitalOperationLog> logList = new ArrayList<>();
        String oprationOrgName = CacheUtils.getOrgName(user.getOrgId());
        // 数字档案操作日志：分类存储操作日志新增
        List<FileDigitalDto> list = new ArrayList<>();
        saveList.forEach(mem -> {
            FileDigitalDto dto = new FileDigitalDto();
            BeanUtils.copyProperties(mem, dto);
            list.add(dto);
        });
        if (CollUtil.isNotEmpty(list)) {
            logList.add(getMemDigitalOperationLog(stepLogDTO, list, 0, oprationOrgName, user));
        }
        // 数字档案操作日志：分类存储操作日志删除
        List<FileDigitalDto> delFilesList = filesList.stream()
                .filter(e -> StrUtil.isNotEmpty(e.getD222Code()) && StrUtil.isNotBlank(e.getCode()) && Objects.equals(e.getIsDelete(), 1)).collect(Collectors.toList());
        if (CollUtil.isNotEmpty(delFilesList)) {
            logList.add(getMemDigitalOperationLog(stepLogDTO, delFilesList, 1, oprationOrgName, user));
        }

        if (CollUtil.isNotEmpty(saveList)) {
            saveBatch(saveList, saveList.size());
        }
        if (CollUtil.isNotEmpty(updateList)) {
            updateBatchById(updateList, updateList.size());
        }

        if (CollUtil.isNotEmpty(logList)) {
            memDigitalOperationLogService.saveBatch(logList, logList.size());
        }
        return true;
    }

    private MemDigitalOperationLog getMemDigitalOperationLog(DevelopStepLogNewDTO stepLogDTO, List<FileDigitalDto> value, Integer isDelete,
                                                             String oprationOrgName, User user) {
        MemDigitalOperationLog operationLog = new MemDigitalOperationLog();
        operationLog.setCode(StrKit.getRandomUUID());
        operationLog.setProcessNode(stepLogDTO.getProcessNode());
        operationLog.setDigitalLotNo(stepLogDTO.getDigitalLotNo());
        operationLog.setD222Code(value.get(0).getD222Code());
        operationLog.setD222Name(value.get(0).getD222Name());
        operationLog.setDigitalNames(CollUtil.join(value.stream().map(FileDigitalDto::getName).collect(Collectors.toList()), CommonConstant.DOU_HAO_STRING));
        operationLog.setDigitalCodes(CollUtil.join(value.stream().map(FileDigitalDto::getCode).collect(Collectors.toList()), CommonConstant.DOU_HAO_STRING));
        operationLog.setOprationTime(new Date());
        operationLog.setOprationUser(stepLogDTO.getOprationUser());
        operationLog.setOprationOrgName(oprationOrgName);
        operationLog.setOprationCode(stepLogDTO.getOprationCode());
        if (isDelete == 1) {
            operationLog.setOprationType(CommonConstant.TWO_INT);
        } else {
            operationLog.setOprationType(CommonConstant.ONE_INT);
        }
        operationLog.setCreateTime(new Date());
        operationLog.setCreateUser(user.getAccount());
        return operationLog;
    }

    /**
     * 获取关系转接档案材料及日志
     *
     * @param digitalLotNo 档案唯一码
     * @param d222Code     目录code
     * @return 查询结果
     */
    @Override
    public OutMessage<?> transferMemDigitalList(String digitalLotNo, String d222Code) {
        if (StrUtil.isBlank(digitalLotNo)) {
            return new OutMessage<>(Status.SUCCESS);
        }
        //获取党员档案
        final List<MemDigital> digitals = this.getTransferMemDigitals(digitalLotNo, d222Code);
        for (MemDigital digital : digitals) {
            String path = digital.getPath();
            if (StrUtil.isNotBlank(path)) {
                digital.setPath(minioTemplate2.shareLink(path));
            }
        }
        //获取操作日志
        final List<MemDigitalOperationLog> operaterLogs = this.getTransferDigitalOperationLogs(digitalLotNo, d222Code);
        Record record = new Record();
        record.set("digitals", digitals);
        record.set("operaterLogs", operaterLogs);
        return new OutMessage<>(Status.SUCCESS, record);
    }

    /**
     * 根据档案唯一码查询档案
     *
     * @param digitalLotNo 档案唯一码
     * @param d222Code     目录信息，可为空
     * @return
     */
    public List<MemDigital> getTransferMemDigitals(String digitalLotNo, String d222Code) {
        LambdaQueryWrapper<MemDigital> query = new LambdaQueryWrapper<MemDigital>()
                .eq(MemDigital::getDigitalLotNo, digitalLotNo)
                .eq(StrUtil.isNotBlank(d222Code), MemDigital::getd222Code, d222Code)
                // 查询条件（要么 删除时间跟转出时间不为空表示这个数据是关系转接出去的，都为空表示数据是正常的。删除时间不为空并且转出时间为空表示这个数据在业务中本来就是不要的数据，并非转出删除的）
                .and(e -> e.or(e1 -> e1.isNull(MemDigital::getDeleteTime).isNull(MemDigital::getTransferTime))
                        .or(e2 -> e2.isNotNull(MemDigital::getDeleteTime).isNotNull(MemDigital::getTransferTime)))
                .orderByAsc(MemDigital::getSort)
                .orderByAsc(MemDigital::getCreateTime)
                .orderByAsc(MemDigital::getCode);
        return this.list(query);
    }

    private List<MemDigitalOperationLog> getTransferDigitalOperationLogs(String digitalLotNo, String d222Code) {
        LambdaQueryWrapper<MemDigitalOperationLog> query = new LambdaQueryWrapper<MemDigitalOperationLog>()
                .eq(MemDigitalOperationLog::getDigitalLotNo, digitalLotNo)
                .eq(MemDigitalOperationLog::getD222Code, d222Code)
                .orderByDesc(MemDigitalOperationLog::getOprationTime);
        return memDigitalOperationLogService.list(query);
    }

    /**
     * 用户是否基层党委及以上人员
     */
    @Override
    public boolean isJCDW() {
        String orgCode = UserConstant.USER_CONTEXT.get().getUser().getOrgCode();
        Org org = orgMapper.findOrgByOrgCode(orgCode);
        return Objects.nonNull(org) && StrUtil.isNotBlank(org.getCode()) && StrUtil.startWithAny(org.getD01Code(), "1", "2", "61", "911");
    }

    /**
     * 用户是否党总支人员
     *
     * @return
     */
    @Override
    public boolean isDZZ() {
        String orgCode = UserConstant.USER_CONTEXT.get().getUser().getOrgCode();
        Org org = orgMapper.findOrgByOrgCode(orgCode);
        return Objects.nonNull(org) && StrUtil.isNotBlank(org.getCode()) && StrUtil.startWithAny(org.getD01Code(), "62", "921", "61", "1", "2");
    }

    /**
     * 是否县级党委及其遵义数据节点顶级账号 或者根据配置表获取县委级别组织
     *
     * @return
     */
    @Override
    public boolean isXJDW() {
        String orgCode = UserConstant.USER_CONTEXT.get().getUser().getOrgCode();
        Org org = orgMapper.findOrgByOrgCode(orgCode);
        if(Objects.nonNull(org) && StrUtil.isNotBlank(org.getCode()) && StrUtil.startWithAny(org.getD01Code(), "14")) {
            return true;
        } else {
            // 查询配置表
            LambdaQueryWrapper<MemDevelopProcessAuditConfig> wrapper = Wrappers.lambdaQuery();
            wrapper.isNull(MemDevelopProcessAuditConfig::getDeleteTime)
                    .eq(MemDevelopProcessAuditConfig::getType, 1)
                    .eq(MemDevelopProcessAuditConfig::getOrgLevelCode, orgCode)
                    .last("LIMIT 1");
            int count = memDevelopProcessAuditConfigMapper.selectCount(wrapper);
            return count > 0;
        }
    }

    /**
     * 电子档案
     *
     * @param memCode
     * @return
     */
    @Override
    public OutMessage<?> electronic(String memCode, String transferId) {
        ElectronicVO vo = new ElectronicVO();
        ElectronicMemVO memVO = new ElectronicMemVO();
        String d08Code = null;
        List<Record> d222List;
        final Map<String, List<ElectronicVO.Catalogue>> digitalMap = new HashMap<>();
        // 工作性质字典表
        Map<String, String> d107Map = CacheUtils.getDic(DictConstant.DICT_D107).stream().filter(t -> Objects.nonNull(t.get("key"))).collect(Collectors.toMap(record -> record.get("key"), record1 -> record1.get("name"), (a, b) -> b, LinkedHashMap::new));
        // 政治面貌
        Map<String, String> d89Map = CacheUtils.getDic(DictConstant.DICT_D89).stream().filter(t -> Objects.nonNull(t.get("key"))).collect(Collectors.toMap(record -> record.get("key"), record1 -> record1.get("name"), (a, b) -> b, LinkedHashMap::new));
        // 关系转接中的目标组织ID
        String targetOrgId = null;

        // 关系转接方面的电子档案查询
        if (StrUtil.isNotBlank(transferId)) {
            JSONArray jsonArray = null;
            // 如果属于关系转接的，可以直接查询档案
            TransferRecord record = transferRecordMapper.selectById(transferId);
            if (Objects.nonNull(record) && StrUtil.isNotBlank(record.getMemId())) {
                targetOrgId = record.getTargetOrgId();
                memCode = record.getMemId();
                // 属于预备、正式党员关系转接
                Object extraData = record.getExtraData();
                if (Objects.nonNull(extraData) && extraData instanceof JSONObject) {
                    JSONObject memJsonObject = (JSONObject) extraData;
                    // 获取array
                    jsonArray = memJsonObject.getJSONArray("memDigital");
                    d08Code = CommonConstant.ONE;
                    Mem transFerMem = JSONObject.toJavaObject(memJsonObject, Mem.class);
                    // 处理返回基本信息值
                    this.setMemVo(transFerMem, memVO);
                }
            } else {
                // 积极份子转接
                ActivistTransferRecord activistTransferRecord = activistTransferRecordMapper.selectById(transferId);
                if (Objects.nonNull(activistTransferRecord)) {
                    targetOrgId = activistTransferRecord.getTargetOrgId();
                    if (Objects.nonNull(activistTransferRecord.getExtraData())) {
                        // 获取array
                        jsonArray = (JSONArray) activistTransferRecord.getExtraDataDigital();
                        MemDevelop transFerMem = JSONObject.toJavaObject((JSONObject)activistTransferRecord.getExtraData(), MemDevelop.class);
                        d08Code = Objects.nonNull(transFerMem) ? transFerMem.getD08Code() : CommonConstant.FOUR;
                        this.setMemVo(transFerMem, memVO, d107Map, d89Map);
                    }
                }
            }
            // 党员类型如果都没有就没必要查询档案了
            if(StrUtil.isNotBlank(d08Code)){
                // 获取目录
                d222List = memDigitalContentsList(null, d08Code, null);
                List<MemDigital> memDigitalList = new ArrayList<>();
                // 档案信息
                if (CollUtil.isNotEmpty(d222List)) {
                    if (Objects.nonNull(jsonArray)) {
                        memDigitalList = jsonArray.toJavaList(MemDigital.class);
                        if (CollUtil.isNotEmpty(memDigitalList)) {
                            for (MemDigital digital : memDigitalList) {
                                String path = digital.getPath();
                                if (StrUtil.isNotBlank(path)) {
                                    digital.setPath(minioTemplate2.shareLink(path));
                                }
                            }
                        }
                    }
                    // 预备党员阶段材料
                    digitalMap.put("2", generateCatalogue(d222List, memDigitalList, "2"));
                    // 发展对象阶段材料
                    digitalMap.put("3", generateCatalogue(d222List, memDigitalList, "3"));
                    // 积极份子阶段材料
                    digitalMap.put("4", generateCatalogue(d222List, memDigitalList, "4"));
                    // 入党申请人阶段材料
                    digitalMap.put("5", generateCatalogue(d222List, memDigitalList, "5"));
                    // 其他
                    digitalMap.put("-1", generateCatalogue(d222List, memDigitalList, "-1"));
                }
            } else {
                // 确认目标组织是否在本节点
                if(StrUtil.isNotBlank(targetOrgId)){
                    LambdaQueryWrapper<Org> orgWrapper =  Wrappers.lambdaQuery();
                    orgWrapper.eq(Org::getCode, targetOrgId)
                            .isNull(Org::getDeleteTime);
                    // 如果本节点存在的话，则根据党员在本地查询档案
                    if(orgMapper.selectCount(orgWrapper) > 0) {
                        this.withD08CodeElectronic(memCode, digitalMap, memVO, d107Map, d89Map);
                    }
                }
            }

        } else {
            this.withD08CodeElectronic(memCode, digitalMap, memVO, d107Map, d89Map);
        }
        vo.setMem(memVO);
        vo.setPhaseMap(digitalMap);
        return new OutMessage<>(Status.SUCCESS, vo);
    }

    /**
     * 是否上传入党志愿书：1-已上传；0-未上传
     *
     * @param digitalLotNo 档案批次唯一码
     */
    @Override
    public Integer getIsVolunteer(String digitalLotNo) {
        // 查询档案记录表
        int count = count(new LambdaQueryWrapper<MemDigital>()
                .eq(MemDigital::getDigitalLotNo, digitalLotNo)
                // 《中国共产党入党志愿书》
                .eq(MemDigital::getd222Code, "404")
                .isNull(MemDigital::getDeleteTime));
        return count > 0 ? 1 : 0;
    }

    /**
     * 根据党员类型获取电子档案
     * @param memCode 党员
     * @param digitalMap
     * @param memVO
     * @param d107Map
     * @param d89Map
     */
    private void withD08CodeElectronic(String memCode,
                                       Map<String, List<ElectronicVO.Catalogue>> digitalMap,
                                       ElectronicMemVO memVO,
                                       Map<String, String> d107Map,
                                       Map<String, String> d89Map){
        String isCatalogue = null;
        String digLotNo = null;
        String d08Code = null;
        String processNode = null;
        List<Record> d222List;

        // 关系转接中也有电子档案，删除的也要查出来
        LambdaQueryWrapper<Mem> query = new LambdaQueryWrapper<Mem>()
                .eq(Mem::getCode, memCode)
                .in(Mem::getD08Code, "1", "2");
        Mem mem = iMemService.getOne(query, false);
        // 党员中没得数据则从积极份子查询
        if (Objects.isNull(mem) || StrUtil.isBlank(mem.getCode())) {
            // 关系转接中也有电子档案，删除的也要查出来
            LambdaQueryWrapper<MemDevelop> developMem = new LambdaQueryWrapper<MemDevelop>()
                    .eq(MemDevelop::getCode, memCode)
                    .in(MemDevelop::getD08Code, "3", "4", "5");
            MemDevelop memDevelop = iMemDevelopService.getOne(developMem, false);
            if (Objects.nonNull(memDevelop) && StrUtil.isNotBlank(memDevelop.getCode())) {
                digLotNo = memDevelop.getDigitalLotNo();
                isCatalogue = "1";
                d08Code = memDevelop.getD08Code();
                processNode = memDevelop.getProcessNode();
                // 处理返回基本信息值
                this.setMemVo(memDevelop, memVO, d107Map, d89Map);
            }
        } else {
            digLotNo = mem.getDigitalLotNo();
            isCatalogue = Objects.isNull(mem.getIsCatalogue()) ? "0" : String.valueOf(mem.getIsCatalogue());
            d08Code = mem.getD08Code();
            processNode = mem.getProcessNode();
            // 处理返回基本信息值
            this.setMemVo(mem, memVO);
        }
        // 获取目录
        d222List = memDigitalContentsList(isCatalogue, d08Code, processNode);
        if (CollUtil.isNotEmpty(d222List)) {
            List<MemDigital> digitals = new ArrayList<>();
            if (StrUtil.isNotBlank(digLotNo)) {
                //获取党员档案
                digitals = this.getTransferMemDigitals(digLotNo, null);
                for (MemDigital digital : digitals) {
                    String path = digital.getPath();
                    if (StrUtil.isNotBlank(path)) {
                        digital.setPath(minioTemplate2.shareLink(path));
                    }
                }
            }
            // 预备党员阶段材料
            digitalMap.put("2", generateCatalogue(d222List, digitals, "2"));
            // 发展对象阶段材料
            digitalMap.put("3", generateCatalogue(d222List, digitals, "3"));
            // 积极份子阶段材料
            digitalMap.put("4", generateCatalogue(d222List, digitals, "4"));
            // 入党申请人阶段材料
            digitalMap.put("5", generateCatalogue(d222List, digitals, "5"));
            // 其他
            digitalMap.put("-1", generateCatalogue(d222List, digitals, "-1"));
        }
    }

    /**
     * 根据阶段获取目录及档案
     *
     * @param d222List
     * @param digitals
     * @param d08Code
     * @return
     */
    private List<ElectronicVO.Catalogue> generateCatalogue(List<Record> d222List, List<MemDigital> digitals, String d08Code) {
        // 排除父级为-1的
        List<ElectronicVO.Catalogue> catalogueList = d222List.stream()
                .filter(e -> !Objects.equals(e.getStr("parent"), "-1") && Objects.equals(d08Code, e.getStr("d08Code")))
                .map(e -> {
                    ElectronicVO.Catalogue catalogue = new ElectronicVO.Catalogue();
                    catalogue.setD222Code(e.getStr("key"));
                    catalogue.setD222Name(e.getStr("name"));
                    catalogue.setDigitalList(digitals.stream().filter(d -> Objects.equals(d.getd222Code(), catalogue.getD222Code())).collect(Collectors.toList()));
                    return catalogue;
                }).collect(Collectors.toList());
        return catalogueList;
    }

    private String getDigitalLotNo(String memCode) {
        LambdaQueryWrapper<Mem> query = new LambdaQueryWrapper<Mem>()
                .eq(Mem::getCode, memCode)
                .in(Mem::getD08Code, "1", "2")
                .isNull(Mem::getDeleteTime);
        final Mem mem = memMapper.selectOne(query);
        return mem.getDigitalLotNo();
    }

    /**
     * 根据d08Code获取档案目录
     *
     * @param records 档案字典表
     * @param d08Code 人员类型
     * @return 档案目录
     */
    private List<Record> getRecordNewList(List<Record> records, String d08Code, String processNode) {
        // 判断流程节点前提下要求党员类型属于非正式党员。可能出现一种情况：流程中党员转到非遵义节点并且转正了且流程节点字段数据未置空，当转回到遵义节点后这里判断目录有偏差，所以做个兼容
        if (StrUtil.isNotBlank(processNode) && Objects.nonNull(ProcessNodeEnum.find(processNode)) && !Objects.equals(d08Code, CommonConstant.ONE)) {
            // 根据流程节点获取
            Set<String> nodes = ProcessNodeEnum.historyStep(processNode);
            if (CollUtil.isNotEmpty(nodes) && !Objects.equals(processNode, ProcessNodeEnum.YBQ_5.getNode())) {
                // 筛选字典表配置好包含了的上面获取到的流程节点
                List<Record> recordList = records.stream().filter(e -> CollUtil.containsAny(StrUtil.split(e.getStr("processNode"), ","), nodes)).collect(Collectors.toList());
                dealWithParentData(recordList, records);
                return recordList;
            }
            return new ArrayList<>();
        } else {
            // 入党申请人
            if (StrUtil.equals(d08Code, CommonConstant.FIVE)) {
                return records.stream().filter(e -> StrUtil.equals(e.getStr("d08Code"), d08Code)).collect(Collectors.toList());
                // 积极分子
            } else if (StrUtil.equals(d08Code, CommonConstant.FOUR)) {
                List<Record> recordList = records.stream().filter(e -> StrUtil.equalsAny(e.getStr("d08Code"), CommonConstant.FOUR, CommonConstant.FIVE)).collect(Collectors.toList());
                dealWithParentData(recordList, records);
                return recordList;
                // 发展对象
            } else if (StrUtil.equals(d08Code, CommonConstant.THREE)) {
                List<Record> recordList = records.stream().filter(e -> StrUtil.equalsAny(e.getStr("d08Code"), CommonConstant.THREE, CommonConstant.FOUR, CommonConstant.FIVE)).collect(Collectors.toList());
                dealWithParentData(recordList, records);
                return recordList;
                // 预备党员，党员
            } else if (StrUtil.equalsAny(d08Code, CommonConstant.TWO, CommonConstant.ONE)) {
                return records;
            } else {
                return new ArrayList<>();
            }
        }
    }

    private void dealWithParentData(List<Record> recordNewList, List<Record> records) {
        Set<String> parentList = recordNewList.stream().filter(e -> StrUtil.equals(e.getStr("parent"), "-1")).map(e -> e.getStr("key")).collect(Collectors.toSet());
        Set<String> collect = recordNewList.stream().filter(e -> !StrUtil.equals(e.getStr("parent"), "-1"))
                .map(e -> e.getStr("parent")).collect(Collectors.toSet());
        collect.forEach(str -> {
            if (!parentList.contains(str)) {
                Optional<Record> optional = records.stream().filter(e -> StrUtil.equals(e.getStr("key"), str)).findFirst();
                optional.ifPresent(recordNewList::add);
            }
        });
    }

    public List<Record> buildTree(List<Record> records) {
        Map<String, Record> map = new HashMap<>();
        List<Record> roots = new ArrayList<>();
        for (Record record : records) {
            map.put(record.getStr("key"), record);
            record.set("children", new ArrayList<Record>());
        }
        // 再次遍历记录，构建树结构
        for (Record record : records) {
            String parentKey = record.getStr("parent");
            if (parentKey == null || !map.containsKey(parentKey)) {
                // 如果没有父节点或者父节点不在记录中，则认为是根节点
                roots.add(record);
            } else {
                // 否则，将该记录添加到其父节点的 children 列表中
                Record parentRecord = map.get(parentKey);
                List<Record> children = parentRecord.get("children");
                children.add(record);
            }
        }
        return roots;
    }


    /**
     * 处理党员基本信息值
     * @param mem
     * @param memVO
     */
    private void setMemVo(Mem mem, ElectronicMemVO memVO){
        memVO.setCode(mem.getCode())
                .setD08Code(mem.getD08Code())
                .setName(mem.getName())
                .setSexName(mem.getSexName())
                .setBirthday(mem.getBirthday())
                .setD48Name(mem.getD48Name())
                .setD06Name(mem.getD06Name())
                .setD09Name(mem.getD09Name())
                .setD07Name(mem.getD07Name())
                .setD154Name(mem.getD154Name())
                .setD89Name(mem.getD89Name())
                .setD21Name(mem.getD21Name())
                .setHasUnitStatistics(mem.getHasUnitStatistics())
                .setHomeAddress(mem.getHomeAddress());
    }


    /**
     * 处理党员基本信息值
     * @param memDevelop
     * @param memVO
     */
    private void setMemVo(MemDevelop memDevelop, ElectronicMemVO memVO, Map<String, String> d107Map, Map<String, String> d89Map){
        memVO.setCode(memDevelop.getCode())
                .setD08Code(memDevelop.getD08Code())
                .setName(memDevelop.getName())
                .setSexName(memDevelop.getSexName())
                .setBirthday(memDevelop.getBirthday())
                .setD48Name(memDevelop.getD48Name())
                .setD06Name(memDevelop.getD06Name())
                .setD09Name(memDevelop.getD09Name())
                .setD07Name(memDevelop.getD07Name())
                .setD154Name(memDevelop.getD154Name())
                .setD89Name(d89Map.get(memDevelop.getPoliticsCode()))
                .setD21Name(memDevelop.getD21Name())
                .setJobNatureCode(d107Map.get(memDevelop.getJobNatureCode()))
                .setHasUnitStatistics(memDevelop.getHasUnitStatistics())
                .setHomeAddress(memDevelop.getHomeAddress());
    }
}
