package com.zenith.front.zunyi.service;


import com.baomidou.mybatisplus.extension.service.IService;
import com.zenith.front.model.bean.MemDigital;
import com.zenith.front.model.bean.User;
import com.zenith.front.model.message.OutMessage;
import com.zenith.front.model.vo.DigitalCompletenessVO;
import com.zenith.front.zunyi.model.dto.CommonDto;
import com.zenith.front.zunyi.model.dto.MemListNewDTO;
import com.zenith.front.zunyi.model.dto.UploadFileDigitalDto;

import java.util.Collection;
import java.util.List;
import java.util.Map;

/**
 * <p>
 * 党员（发展党员）数字档案表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-02-14
 */
public interface IMemDigitalService extends IService<MemDigital> {

    /**
     * 档案目录
     *
     * <AUTHOR>
     */
    OutMessage memDigitalContents(String isCatalogue, String d08Code, String processNode, String memCode);

    /**
     * 档案材料
     *
     * @param digitalLotNo 档案唯一码
     * @param d222Code     目录code
     * @return
     */
    OutMessage listMemDigital(String digitalLotNo, String d222Code, String memCode);


    /**
     * 获取档案类别
     *
     * @param digitalLotNo 档案批次唯一码
     * @return 1-绿色档案； 2-蓝色档案； 3-红色档案； 99-其他
     */
    Integer getDigitalType(String digitalLotNo);

    /**
     * 获取档案完整度：1-完整；0-不完整
     *
     * @param digitalLotNo 档案批次唯一码
     * @return 1-完整；0-不完整
     */
    Integer getIntegrality(String digitalLotNo);

    /**
     * 根据档案类别获取党员档案
     *
     * @param digitalLotNo 档案批次唯一码
     * @param d222Code     档案类别
     * @return
     */
    List<MemDigital> getDigitals(String digitalLotNo, String d222Code);

    /**
     * 获取已上传的档案
     *
     * @param digitalLotNo 档案批次唯一码
     */
    List<MemDigital> getUploadDigitalList(String digitalLotNo);

    /**
     * 是否认定
     *
     * @param memCode
     * @param sure
     * @return
     */
    OutMessage isSure(String memCode, Integer sure);

    /**
     * 档案清除
     *
     * @return 操作结果
     */
    OutMessage cleanDigital(CommonDto commonDto, User user);

    /**
     * 档案排序
     */
    OutMessage sortDigital(CommonDto commonDto, User user);

    /**
     * 获取档案操作日志列表
     */
    OutMessage getDigitalLogList(MemListNewDTO data, User user);

    /**
     * 档案管理里面的上传档案
     */
    OutMessage uploadDigital(UploadFileDigitalDto digitalDto, User user);


    /**
     * 获取关系转接档案材料及日志
     *
     * @param digitalLotNo 档案唯一码
     * @param d222Code     目录code
     * @return 查询结果
     */
    OutMessage<?> transferMemDigitalList(String digitalLotNo, String d222Code);

    /**
     * 用户是否基层党委人员
     */
    boolean isJCDW();

    /**
     * 用户是否党总支人员
     *
     * @return
     */
    boolean isDZZ();

    /**
     * 是否县级党委
     * @return
     */
    boolean isXJDW();

    /**
     * 电子档案
     * @param memCode
     * @return
     */
    OutMessage<?> electronic(String memCode, String transferId);

    /**
     * 是否上传入党志愿书：1-已上传；0-未上传
     *
     * @param digitalLotNo 档案批次唯一码
     */
    Integer getIsVolunteer(String digitalLotNo);

    /**
     * 获取档案完整度详细信息
     *
     * @param digitalLotNo 档案批次唯一码
     */
    List<DigitalCompletenessVO> withIntegrality(String digitalLotNo);

    /**
     * 查询档案上传详细情况 <档案唯一码：档案信息>
     * @param digitalLotNoList
     * @return
     */
    Map<String, List<DigitalCompletenessVO>> getBatchDigCount(Collection<String> digitalLotNoList);
}
