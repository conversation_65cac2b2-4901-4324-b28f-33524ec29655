package com.zenith.front.zunyi.model.vo;

import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @create_date 2025-06-27 09:58
 * @description
 */
@Data
public class MemDigitalCountVO {

    private List<MemDigitalCountDetailVO> list;

    /**
     * 总计阶段完整-汇总
     */
    private Long stepDoneSum;
    /**
     * 总计阶段缺失-汇总
     */
    private Long stepMissSum;

    /**
     * 入党申请人阶段完整-汇总
     */
    private Long rdsqrDoneSum;
    /**
     * 入党申请人阶段缺失-汇总
     */
    private Long rdsqrMissSum;

    /**
     * 积极份子阶段完整-汇总
     */
    private Long jjfzDoneSum;
    /**
     * 积极份子阶段缺失-汇总
     */
    private Long jjfzMissSum;

    /**
     * 发展对象阶段完整-汇总
     */
    private Long fzdxDoneSum;
    /**
     * 发展对象阶段缺失-汇总
     */
    private Long fzdxMissSum;

    /**
     * 预备党员阶段完整-汇总
     */
    private Long ybdyDoneSum;
    /**
     * 预备党员阶段缺失-汇总
     */
    private Long ybdyMissSum;

    /**
     * 其他阶段完整-汇总
     */
    private Long otherDoneSum;
    /**
     * 其他阶段缺失-汇总
     */
    private Long otherMissSum;
}
