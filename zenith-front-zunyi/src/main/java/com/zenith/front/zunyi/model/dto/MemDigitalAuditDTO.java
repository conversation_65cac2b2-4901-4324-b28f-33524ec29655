package com.zenith.front.zunyi.model.dto;

import com.baomidou.mybatisplus.annotation.TableField;
import lombok.Data;

import java.util.Date;

/**
 * 导出、补充档案申请审核
 * <AUTHOR>
 * @create_date 2025-07-11 16:14
 * @description
 */
@Data
public class MemDigitalAuditDTO {

    private String code;

    /**
     * 申请人
     */
    private String applicantMemName;
    /**
     * 申请人code
     */
    private String applicantMemCode;
    /**
     * 申请日期
     */
    private Date applicantDate;
    /**
     * 申请导出理由\补充档案简述
     */
    private String applicantReason;
    /**
     * 申请状态：1-待审核；2-审核通过；3-审核拒绝
     */
    private Integer status;
    /**
     * 审核记录
     */
    private String remark;
    /**
     * 类型：1-导出审核； 2-补充档案审核
     */
    private Integer type;
    /**
     * 档案唯一码
     */
    private String digitalLotNo;
    /**
     * 党员code
     */
    private String memCode;
    /**
     * 党员姓名
     */
    private String memName;
    /**
     * 所在党支部层级码
     */
    private String memOrgLevelCode;
    /**
     * 所在党支部名
     */
    private String memOrgName;
}
