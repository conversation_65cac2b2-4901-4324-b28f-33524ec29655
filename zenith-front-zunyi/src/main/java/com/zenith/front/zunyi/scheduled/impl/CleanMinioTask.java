package com.zenith.front.zunyi.scheduled.impl;

import com.zenith.front.common.constant.UserConstant;
import com.zenith.front.framework.file.core.MinioTemplate2;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Async;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @create_date 2025-05-15 16:57
 * @description
 */
@Slf4j
@Component
public class CleanMinioTask {
    @Resource
    MinioTemplate2 minioTemplate2;

    /**
     * 每天凌晨3点清理临时桶
     */
    @Async
    @Scheduled(cron = "0 0 3 * * ? ")
    public void task() {
        // 非遵义的节点不用执行
        if (!UserConstant.HAS_ZUN_YI) {
            return;
        }
        log.info("遵义清理临时桶数据...开始");
        minioTemplate2.cleanTmp();
        log.info("遵义清理临时桶数据...结束");
    }
}
