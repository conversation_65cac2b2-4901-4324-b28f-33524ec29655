package com.zenith.front.zunyi.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.io.FileUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.zenith.front.api.dict.IDictService;
import com.zenith.front.common.constant.CommonConstant;
import com.zenith.front.common.kit.WebpUtil;
import com.zenith.front.dao.mapper.mem.MemDigitalMapper;
import com.zenith.front.dao.mapper.mem.MemDigitalOperationLogMapper;
import com.zenith.front.framework.file.core.MinioTemplate2;
import com.zenith.front.framework.file.util.FileExtUtil;
import com.zenith.front.model.constant.UserConstant;
import com.zenith.front.model.custom.Record;
import com.zenith.front.model.dto.DigitalDataDTO;
import com.zenith.front.model.message.OutMessage;
import com.zenith.front.model.message.Status;
import com.zenith.front.model.vo.MemDigitalDataVO;
import com.zenith.front.model.vo.MemDigitalOperationLogVO;
import com.zenith.front.zunyi.service.IMemDigitalDataService;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.stereotype.Service;
import org.springframework.util.ResourceUtils;

import javax.annotation.Resource;
import java.io.File;
import java.io.FileOutputStream;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2025/6/30
 */
@Slf4j
@Service
public class MemDigitalDataServiceImpl implements IMemDigitalDataService {

    @Resource
    private MemDigitalMapper memDigitalMapper;
    @Resource
    private MemDigitalOperationLogMapper memDigitalOperationLogMapper;
    @Resource
    private IDictService dictService;
    @Resource
    private MinioTemplate2 minioTemplate2;

    /**
     * 导出档案材料
     */
    @Override
    public OutMessage<?> exportDigitalData(DigitalDataDTO digitalDataDTO) {
        String all = digitalDataDTO.getIsExportAll();
        if (!StrUtil.equals(all, CommonConstant.ONE)) {
            if (CollUtil.isEmpty(digitalDataDTO.getDigitalLotNoList())) {
                return new OutMessage<>(Status.NOT_NULL_ERROR);
            }
        }
        List<MemDigitalDataVO> list;
        // 党员档案信息
        if (StrUtil.equals(digitalDataDTO.getType(), CommonConstant.ONE)) {
            list = memDigitalMapper.getMemDigitalData(digitalDataDTO);
        } else {
            // 发展党员档案信息
            list = memDigitalMapper.getDevelopMemDigitalData(digitalDataDTO);
        }
        if (CollUtil.isEmpty(list)) {
            return new OutMessage<>(500, "暂无档案材料需要导出！", null);
        }
        List<File> fileList = new ArrayList<>();
        String exportPath = CommonConstant.EXPORT_ROUTE;
        String watermark = StrUtil.format("本档案仅供{}账户{}阅览使用，不具备任何形式的法律效应",
                UserConstant.USER_CONTEXT.get().getUser().getAccount(), DateUtil.format(new Date(), "yyyy年MM月dd日"));
        try {
            String basePath = ResourceUtils.getURL(ResourceUtils.CLASSPATH_URL_PREFIX).getPath() + exportPath;
            File dir = new File(basePath + CommonConstant.TMP);
            if (!dir.exists()) {
                FileUtil.mkdir(dir);
            }
            // 档案类型字典表
            List<Record> records = dictService.getAllNonHump("dict_d222");
            Map<String, String> parentMap = records.stream().filter(e -> StrUtil.equals(e.getStr("parent"), "-1")).collect(Collectors.toMap(e -> e.getStr("key"), e -> e.getStr("name")));
            Map<String, String> dictD222Map = records.stream().collect(Collectors.toMap(e -> e.getStr("key"), e -> e.getStr("name")));

            Map<String, List<MemDigitalDataVO>> listMap = list.stream().collect(Collectors.groupingBy(MemDigitalDataVO::getDigitalLotNo));
            for (Map.Entry<String, List<MemDigitalDataVO>> entry : listMap.entrySet()) {
                List<MemDigitalDataVO> value = entry.getValue();
                if (CollUtil.isEmpty(value)) {
                    continue;
                }
                String memName = value.stream().findFirst().map(MemDigitalDataVO::getMemName).orElse("其他");
                // 某某档案目录
                File file = FileExtUtil.reNameFolder(dir.getPath(), memName);
                fileList.add(file);
                Map<String, List<MemDigitalDataVO>> d222ListMap = value.stream().filter(e -> StrUtil.isNotBlank(e.getD222Code())).collect(Collectors.groupingBy(MemDigitalDataVO::getD222Code));
                d222ListMap.forEach((k, v) -> {
                    if (CollUtil.isEmpty(v)) {
                        return;
                    }
                    // 获取中间文件夹
                    String tmpFileName1 = parentMap.getOrDefault(k.substring(0, 1), "其他");
                    String tmpFileName2 = dictD222Map.getOrDefault(k, k);

                    int index = 1;
                    for (MemDigitalDataVO dataVO : v) {
                        // 下载文件
                        byte[] downloadFile = minioTemplate2.downloadFile(dataVO.getPath());
                        if (ObjectUtil.isNull(downloadFile)) {
                            continue;
                        }
                        // 按顺序生成新的文件名
                        String fileName = String.format("%d_%s", index, dataVO.getName());
                        // 最新文件路径
                        String targetFilePath = String.format("%s/%s/%s/%s", file.getPath(), tmpFileName1, tmpFileName2, fileName);
                        FileUtil.writeBytes(downloadFile, targetFilePath);
                        // 文件属于webp的进行转换添加水印
                        if(FileUtil.extName(fileName).equalsIgnoreCase("webp")) {
                            String jpegFilePath = targetFilePath.substring(0, targetFilePath.lastIndexOf(".") ) + ".jpeg";
                            String[] command = WebpUtil.WEBP_TO_JPEG.clone();
                            Map<String, String> tempMap = new HashMap<>();
                            tempMap.put(WebpUtil.WEBP_PATH, targetFilePath.replaceAll("'", "'\\\\''"));
                            tempMap.put(WebpUtil.JPEG_PATH, jpegFilePath.replaceAll("'", "'\\\\''"));
                            for(int i = 0; i < command.length; i++) {
                                command[i] = StrUtil.format(command[i], tempMap);
                            }

                            boolean flag = WebpUtil.exec(command);
                            // 转换失败的话不加水印直接返回了
                            if(!flag) {
                                index++;
                                continue;
                            }
                            // 清空webp的文件
                            FileUtil.del(targetFilePath);
                            // 将jpeg的文件带获取加水印
                            targetFilePath = jpegFilePath;
                        }
                        // 添加图片水印
                        FileExtUtil.addWatermark(targetFilePath, watermark);
                        index++;
                    }
                });
            }

            // 开始压缩文件
            String zipFileName = String.format("DATA%s.zip", DateUtil.format(new Date(), "yyyyMMddHHmmssSSS"));
            File zipFile = FileUtil.file(basePath + "/" + zipFileName);
            FileExtUtil.zipFile(zipFile, fileList);
            // 返回文件路径
            Map<String, String> map = new HashMap<>(1);
            map.put("url", exportPath + zipFileName);
            return new OutMessage<>(Status.SUCCESS, map);
        } catch (Exception e) {
            log.error("导出文件失败！", e);
            return new OutMessage<>(Status.FAIL);
        } finally {
            // 清除中间文件
            fileList.forEach(FileUtil::del);
        }
    }

    /**
     * 导出档案操作日志
     */
    @Override
    public OutMessage<?> exportDigitalLogs(DigitalDataDTO digitalDataDTO) {
        String all = digitalDataDTO.getIsExportAll();
        if (!StrUtil.equals(all, CommonConstant.ONE)) {
            if (CollUtil.isEmpty(digitalDataDTO.getDigitalLotNoList())) {
                return new OutMessage<>(Status.NOT_NULL_ERROR);
            }
        }
        List<MemDigitalOperationLogVO> list;
        // 党员档案信息
        if (StrUtil.equals(digitalDataDTO.getType(), CommonConstant.ONE)) {
            list = memDigitalOperationLogMapper.getMemDigitalLogsData(digitalDataDTO);
        } else {
            // 发展党员档案信息
            list = memDigitalOperationLogMapper.getDevelopMemDigitalLogsData(digitalDataDTO);
        }
        if (CollUtil.isEmpty(list)) {
            return new OutMessage<>(500, "暂无档案操作日志需要导出！", null);
        }

        String exportPath = CommonConstant.EXPORT_ROUTE;
        String fileName = String.format("DLOG%s.xlsx", DateUtil.format(new Date(), "yyyyMMddHHmmssSSS"));
        try {
            String basePath = ResourceUtils.getURL(ResourceUtils.CLASSPATH_URL_PREFIX).getPath() + exportPath;
            File dir = new File(basePath + CommonConstant.TMP);
            if (!dir.exists()) {
                FileUtil.mkdir(dir);
            }
            Workbook workbook = new XSSFWorkbook();
            Sheet sheet = workbook.createSheet();
            // 创建样式
            CellStyle headerStyle = createHeaderStyle(workbook);
            CellStyle dateStyle = createDateStyle(workbook);
            List<String> headers = Arrays.asList("党员姓名", "操作人", "操作类型", "操作时间", "档案类别", "上传（党）组织", "档案资料名称");
            // 创建表头行
            createHeaderRow(sheet, headers, headerStyle);

            // 存储填充数据集合
            List<List<Object>> dataList = new ArrayList<>();
            for (MemDigitalOperationLogVO logVO : list) {
                List<Object> objects = Arrays.asList(
                        logVO.getMemName(),
                        logVO.getOprationUser(),
                        ObjectUtil.equals(logVO.getOprationType(), 1) ? "上传" : "删除",
                        logVO.getOprationTime(),
                        logVO.getD222Name(),
                        logVO.getOprationOrgName(),
                        logVO.getDigitalNames()
                );
                dataList.add(objects);
            }

            // 填充数据行
            fillDataRows(sheet, dataList, dateStyle);
            autoSizeColumns(workbook);
            // 写入文件
            File file = new File(basePath + fileName);
            FileOutputStream fileOutputStream = new FileOutputStream(file);
            workbook.write(fileOutputStream);
            fileOutputStream.close();
            workbook.close();
        } catch (Exception e) {
            log.error("导出文件失败！", e);
            return new OutMessage<>(Status.FAIL);
        }
        Map<String, String> map = new HashMap<>(1);
        map.put("url", exportPath + fileName);
        return new OutMessage<>(Status.SUCCESS, map);
    }


    /**
     * 创建表头样式
     */
    private static CellStyle createHeaderStyle(Workbook workbook) {
        CellStyle style = workbook.createCellStyle();

        // 设置背景色
        style.setFillForegroundColor(IndexedColors.WHITE.getIndex());
        style.setFillPattern(FillPatternType.SOLID_FOREGROUND);

        // 设置边框
        style.setBorderTop(BorderStyle.THIN);
        style.setBorderRight(BorderStyle.THIN);
        style.setBorderBottom(BorderStyle.THIN);
        style.setBorderLeft(BorderStyle.THIN);

        // 设置字体
        Font font = workbook.createFont();
        font.setBold(true);
        font.setFontHeightInPoints((short) 12);
        style.setFont(font);

        // 居中显示
        style.setAlignment(HorizontalAlignment.CENTER);
        style.setVerticalAlignment(VerticalAlignment.CENTER);

        return style;
    }

    /**
     * 创建表头行
     */
    private static void createHeaderRow(Sheet sheet, List<String> headers, CellStyle style) {
        Row headerRow = sheet.createRow(0);
        for (int i = 0; i < headers.size(); i++) {
            Cell cell = headerRow.createCell(i);
            cell.setCellValue(headers.get(i));
            cell.setCellStyle(style);
        }
    }

    /**
     * 创建日期样式
     */
    private static CellStyle createDateStyle(Workbook workbook) {
        CellStyle style = workbook.createCellStyle();
        style.setDataFormat(workbook.getCreationHelper().createDataFormat().getFormat("yyyy-mm-dd"));
        return style;
    }

    /**
     * 获取工作表名称
     *
     * @param sheetList
     * @param sheetName
     * @return
     */
    private String getSheetName(Set<String> sheetList, String sheetName) {
        if (!sheetList.contains(sheetName)) {
            return sheetName;
        }
        int count = 1;
        while (true) {
            String candidate = sheetName + count;
            if (!sheetList.contains(candidate)) {
                return candidate;
            }
            count++;
        }
    }

    /**
     * 填充数据行
     */
    private static void fillDataRows(Sheet sheet, List<List<Object>> dataRows, CellStyle dateStyle) {
        int rowNum = 1;
        for (List<Object> rowData : dataRows) {
            Row row = sheet.createRow(rowNum++);

            for (int colNum = 0; colNum < rowData.size(); colNum++) {
                Cell cell = row.createCell(colNum);
                Object value = rowData.get(colNum);
                // 根据数据类型设置单元格值
                if (value == null) {
                    cell.setCellValue("");
                } else if (value instanceof String) {
                    cell.setCellValue((String) value);
                } else if (value instanceof Number) {
                    if (value instanceof Integer || value instanceof Long) {
                        cell.setCellValue(((Number) value).doubleValue());
                    } else {
                        cell.setCellValue(((Number) value).doubleValue());
                    }
                } else if (value instanceof Boolean) {
                    cell.setCellValue((Boolean) value);
                } else if (value instanceof java.util.Date) {
                    cell.setCellValue((java.util.Date) value);
                    cell.setCellStyle(dateStyle);
                } else {
                    // 其他类型转为字符串
                    cell.setCellValue(value.toString());
                }
            }
        }
    }

    /**
     * 自动调整列宽
     */
    private static void autoSizeColumns(Workbook workbook) {
        int sheetCount = workbook.getNumberOfSheets();

        for (int i = 0; i < sheetCount; i++) {
            Sheet sheet = workbook.getSheetAt(i);
            Row row = sheet.getRow(0);
            if (row == null) {
                continue;
            }
            int lastCellNum = row.getLastCellNum();
            // 遍历每列
            for (int colNum = 0; colNum < lastCellNum; colNum++) {
                sheet.autoSizeColumn(colNum);
                int currentWidth = sheet.getColumnWidth(colNum);
                if (currentWidth < 10 * 256) {
                    sheet.setColumnWidth(colNum, 10 * 256);
                }
            }
        }
    }

}
