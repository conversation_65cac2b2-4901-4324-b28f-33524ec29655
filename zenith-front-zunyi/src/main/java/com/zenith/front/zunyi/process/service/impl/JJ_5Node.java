package com.zenith.front.zunyi.process.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.zenith.front.api.mem.IMemDevelopService;
import com.zenith.front.common.constant.ProcessNodeEnum;
import com.zenith.front.common.kit.StrKit;
import com.zenith.front.model.bean.MemDevelop;
import com.zenith.front.model.message.OutMessage;
import com.zenith.front.model.message.Status;
import com.zenith.front.model.bean.MemDevelopProcess;
import com.zenith.front.zunyi.process.service.ProcessEngineService;
import com.zenith.front.zunyi.service.IMemDevelopProcessService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * <AUTHOR>
 * @since 2025-02-28 14:15
 */
@Slf4j
@Service
public class JJ_5Node implements ProcessEngineService {

    @Resource
    private IMemDevelopService memDevelopService;

    @Resource
    private IMemDevelopProcessService memDevelopProcessService;

    @Override
    public String getNode() {
        return ProcessNodeEnum.JJ_5.getNode();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public OutMessage<ProcessNodeEnum> next(String currentNode, String businessId, Map<String, Object> paramMap) {
        // paramMap 参数需要传nextCode进入下一节点 (JJ_5持续考察、JJ_6考察完毕)
        if (MapUtil.isEmpty(paramMap) || ObjectUtil.isNull(paramMap.get("nextCode"))) {
            log.error("nextCode参数为空，无法进行下一节点的跳转，业务ID：{}", businessId);
            return new OutMessage<>(5001, "nextCode参数为空，无法进行下一节点的跳转", null);
        }
        String nextCode = paramMap.get("nextCode").toString();
        if (!StrUtil.equalsAny(nextCode, "JJ_5", "JJ_6")) {
            log.error("未知参数nextCode，无法进行下一节点的跳转，业务ID：{}", businessId);
            return new OutMessage<>(5001, "未知参数nextCode，无法进行下一节点的跳转", null);
        }
        // 获取考察党员基本信息
        LambdaQueryWrapper<MemDevelop> memDevelopLambdaQueryWrapper = Wrappers.lambdaQuery();
        memDevelopLambdaQueryWrapper.eq(MemDevelop::getCode, businessId);
        // 考察完毕从“超半月”节点发起，但前端弹窗直接传递的是JJ_5。实际数据节点还是JJ_4。这里懒得让前端修改了，做兼容处理
        memDevelopLambdaQueryWrapper.in(MemDevelop::getProcessNode, getNode(), ProcessNodeEnum.JJ_4.getNode());
        memDevelopLambdaQueryWrapper.isNull(MemDevelop::getDeleteTime);
        memDevelopLambdaQueryWrapper.last("limit 1");
        MemDevelop memDevelop = this.memDevelopService.getOne(memDevelopLambdaQueryWrapper);
        if(Objects.isNull(memDevelop) || StrUtil.isBlank(memDevelop.getDigitalLotNo())) {
            log.error("业务信息不存在或档案批次唯一码不存在，业务ID：{}", businessId);
            return new OutMessage<>(5001, "业务信息不存在或档案批次唯一码不存在", null);
        }
        String digitalLotNo = memDevelop.getDigitalLotNo();
        // 获取当前节点流程信息
        LambdaQueryWrapper<MemDevelopProcess> processCurrentWrapper = Wrappers.lambdaQuery();
        processCurrentWrapper.in(MemDevelopProcess::getProcessNode, memDevelop.getProcessNode());
        processCurrentWrapper.eq(MemDevelopProcess::getDigitalLotNo, digitalLotNo);
        processCurrentWrapper.isNull(MemDevelopProcess::getApproveTime);
        processCurrentWrapper.isNull(MemDevelopProcess::getApproveUser);
        processCurrentWrapper.last("limit 1");
        MemDevelopProcess processCurrent = this.memDevelopProcessService.getOne(processCurrentWrapper);
        if (ObjectUtil.isNull(processCurrent)) {
            log.error("当前节点流程不存在，业务ID：{}", businessId);
            return new OutMessage<>(5001, "当前节点流程不存在", null);
        }
        // 确认下一步流程节点
        ProcessNodeEnum nextNode = ProcessNodeEnum.find(nextCode);
        MemDevelopProcess processNext = new MemDevelopProcess();
        processNext.setCode(StrKit.getRandomUUID());
        processNext.setProcessNode(nextNode.getNode());
        processNext.setDigitalLotNo(processCurrent.getDigitalLotNo());
        processNext.setd08Code(processCurrent.getd08Code());
        processNext.setExtendStarTime(processCurrent.getExtendStarTime());
        processNext.setExtendEndTime((ObjectUtil.equals(nextNode, ProcessNodeEnum.JJ_5)) ? DateUtil.offsetDay(processCurrent.getExtendEndTime(), 180) : processCurrent.getExtendEndTime());
        processNext.setPreviousCode(processCurrent.getCode());
        processNext.setCreateUser(currentUser().getAccount());
        processNext.setCreateTime(DateUtil.date());
        // 修改当前节点流程信息
        processCurrent.setApproveUser(currentUser().getAccount());
        processCurrent.setApproveTime(DateUtil.date());
        processCurrent.setUpdateUser(currentUser().getAccount());
        processCurrent.setUpdateTime(DateUtil.date());
        boolean updateProcessCurrent = this.memDevelopProcessService.updateById(processCurrent);
        if (!updateProcessCurrent) {
            log.error("当前节点流程记录失败，业务ID：{}", businessId);
            return new OutMessage<>(5001, "当前节点流程记录失败", null);
        }
        // 记录下一节点流程信息
        boolean insertProcessNext = this.memDevelopProcessService.save(processNext);
        if (!insertProcessNext) {
            log.error("下一节点流程开启失败，业务ID：{}", businessId);
            return new OutMessage<>(5001, "下一节点流程开启失败", null);
        }
        // 修改下一流程节点
//        memDevelop.setProcessNode(processNext.getProcessNode());
//        boolean updateDevelop = this.memDevelopService.updateById(memDevelop);
        LambdaUpdateWrapper<MemDevelop> updateWrapper = Wrappers.lambdaUpdate();
        updateWrapper.set(MemDevelop::getProcessNode, processNext.getProcessNode())
                .eq(MemDevelop::getCode, memDevelop.getCode());
        boolean updateDevelop = memDevelopService.update(updateWrapper);
        if (!updateDevelop) {
            log.error("记录下一流程节点失败，业务ID：{}", businessId);
            return new OutMessage<>(5001, "记录下一流程节点失败", null);
        }
        return new OutMessage<>(Status.SUCCESS, nextNode);
    }

    @Override
    public OutMessage<?> task() {
        LambdaQueryWrapper<MemDevelopProcess> processWrapper = Wrappers.lambdaQuery();
        processWrapper.eq(MemDevelopProcess::getProcessNode, getNode());
        processWrapper.isNotNull(MemDevelopProcess::getDigitalLotNo);
        processWrapper.isNull(MemDevelopProcess::getApproveTime);
        processWrapper.isNull(MemDevelopProcess::getApproveUser);
        processWrapper.isNotNull(MemDevelopProcess::getExtendEndTime);
        DateTime offsetDay = DateUtil.offsetDay(DateUtil.date(), -15);
        processWrapper.le(MemDevelopProcess::getExtendEndTime, offsetDay);
        List<MemDevelopProcess> list = this.memDevelopProcessService.list(processWrapper);
        if (CollUtil.isNotEmpty(list)) {
            ProcessNodeEnum nextNode = ProcessNodeEnum.JJ_4;
            List<MemDevelop> memDevelopList = CollUtil.newArrayList();
            List<MemDevelopProcess> processCurrentList = CollUtil.newArrayList();
            List<MemDevelopProcess> processNextList = CollUtil.newArrayList();
            for (MemDevelopProcess processCurrent : list) {
                String digitalLotNo = processCurrent.getDigitalLotNo();
                LambdaQueryWrapper<MemDevelop> developWrapper = Wrappers.lambdaQuery();
                developWrapper.select(MemDevelop::getId, MemDevelop::getCode, MemDevelop::getDigitalLotNo, MemDevelop::getD08Code);
                developWrapper.eq(MemDevelop::getDigitalLotNo, digitalLotNo);
                developWrapper.eq(MemDevelop::getProcessNode, getNode());
                developWrapper.isNotNull(MemDevelop::getDigitalLotNo);
                developWrapper.isNull(MemDevelop::getDeleteTime);
                developWrapper.last("limit 1");
                MemDevelop memDevelop = this.memDevelopService.getOne(developWrapper);
                if (ObjectUtil.isNotNull(memDevelop)) {
                    // 修改流程信息
                    memDevelop.setProcessNode(nextNode.getNode());
                    memDevelopList.add(memDevelop);
                    // 记录当前节点流程信息
                    processCurrent.setApproveUser("定时任务");
                    processCurrent.setApproveTime(DateUtil.date());
                    processCurrent.setUpdateUser("定时任务");
                    processCurrent.setUpdateTime(DateUtil.date());
                    processCurrentList.add(processCurrent);
                    // 记录下一节点流程信息
                    MemDevelopProcess processNext = new MemDevelopProcess();
                    processNext.setCode(StrKit.getRandomUUID());
                    processNext.setProcessNode(nextNode.getNode());
                    processNext.setDigitalLotNo(processCurrent.getDigitalLotNo());
                    processNext.setd08Code(processCurrent.getd08Code());
                    processNext.setExtendStarTime(processCurrent.getExtendStarTime());
                    processNext.setExtendEndTime(processCurrent.getExtendEndTime());
                    processNext.setPreviousCode(processCurrent.getCode());
                    processNext.setCreateUser("定时任务");
                    processNext.setCreateTime(DateUtil.date());
                    processNextList.add(processNext);
                }
            }
            if (CollUtil.isNotEmpty(memDevelopList)) {
                this.memDevelopService.updateBatchById(memDevelopList, 100);
            }
            if (CollUtil.isNotEmpty(processCurrentList)) {
                this.memDevelopProcessService.updateBatchById(processCurrentList, 100);
            }
            if (CollUtil.isNotEmpty(processNextList)) {
                this.memDevelopProcessService.saveBatch(processNextList, 100);
            }
            log.info("完成【JJ_5(持续考察人员) ---> JJ_4(超半月未按时考察)】定时任务.");
        }
        return new OutMessage<>(Status.SUCCESS);
    }
}
