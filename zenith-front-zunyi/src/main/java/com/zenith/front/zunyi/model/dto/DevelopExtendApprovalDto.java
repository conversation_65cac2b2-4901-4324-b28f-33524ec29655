package com.zenith.front.zunyi.model.dto;

import com.zenith.front.model.validate.group.Common1Group;
import com.zenith.front.model.validate.group.Common2Group;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.util.Date;

/**
 * 发展对象延长审批信息
 *
 * <AUTHOR>
 * @since 2025/3/5
 */
@Data
public class DevelopExtendApprovalDto {
    /**
     * 发展党员标识
     */
    @NotBlank(groups = {Common1Group.class, Common2Group.class}, message = "memCode 不能为空")
    private String memCode;
    /**
     * 流程节点
     */
    @NotBlank(groups = {Common1Group.class, Common2Group.class}, message = "流程节点不能为空")
    private String processNode;
    /**
     * 档案批次唯一码
     */
    @NotBlank(groups = {Common1Group.class, Common2Group.class}, message = "档案批次唯一码不能为空")
    private String digitalLotNo;

    /**
     * 延长时间类型
     * * 1 一个月，2 两个月，3 三个月，9 半个月，0 自定义
     */
    @NotBlank(groups = Common1Group.class, message = "延长时间不能为空")
    private Integer isMark;
    /**
     * 扩展开始时间
     */
    private Date extendStarTime;

    /**
     * 扩展结束时间
     */
    private Date extendEndTime;

    /**
     * 延长审批说明
     */
    @NotBlank(groups = Common1Group.class, message = "延长说明不能为空")
    private String extendApproveExplain;
}
