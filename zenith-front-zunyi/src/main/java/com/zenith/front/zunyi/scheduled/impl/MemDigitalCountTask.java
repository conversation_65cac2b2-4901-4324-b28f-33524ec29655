package com.zenith.front.zunyi.scheduled.impl;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.zenith.front.api.mem.IMemDevelopService;
import com.zenith.front.api.mem.IMemService;
import com.zenith.front.common.constant.UserConstant;
import com.zenith.front.model.bean.Mem;
import com.zenith.front.model.bean.MemDevelop;
import com.zenith.front.zunyi.process.ProcessTool;
import com.zenith.front.zunyi.service.IMemDigitalService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Async;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 定时计算完整度详细信息
 * <AUTHOR>
 * @create_date 2025-05-15 15:31
 * @description
 */
@Slf4j
@Component("memDigitalCountTask")
public class MemDigitalCountTask {

    @Resource
    private IMemDevelopService iMemDevelopService;
    @Resource
    private IMemService imemService;
    @Resource
    private IMemDigitalService iMemDigitalService;

    @Async
    @Scheduled(cron = "0 0/15 * * * ?")
    public void task () {
        // 非遵义的节点不用执行流程任务
        if (!UserConstant.HAS_ZUN_YI) {
            return;
        }
        // 只计算统计表中没得关联的档案唯一码党员，这张表新增删除档案时计算的
        log.info("开始计算档案完整度详细信息..." );
        LambdaQueryWrapper<MemDevelop> wrapper = Wrappers.lambdaQuery();
        wrapper.select(MemDevelop::getId, MemDevelop::getDigitalLotNo);
        wrapper.isNull(MemDevelop::getDeleteTime)
                .isNotNull(MemDevelop::getDigitalLotNo)
                .in(MemDevelop::getD08Code, "3", "4", "5")
                // 获取（统计表没有的）  或者 （根据档案唯一码分组后 统计表中最大的创建时间比档案表中最大创建时间还晚的，说明有新的档案上传后异步计算完整度没有重计算到）
                .apply(" ( not exists (select 1 from ccp_mem_digital_count where ccp_mem_digital_count.digital_lot_no = ccp_mem_develop.digital_lot_no)" +
                        "  or ccp_mem_develop.digital_lot_no in (\n" +
                        " select t1.digital_lot_no  from \n" +
                        " (select digital_lot_no, max(create_time) cd from ccp_mem_digital_count group by digital_lot_no) t1, \n" +
                        " (select digital_lot_no, max(create_time) dd  from ccp_mem_digital cmd where delete_time is null group by digital_lot_no) t2 \n" +
                        " where t1.digital_lot_no = t2.digital_lot_no and t1.cd < t2.dd\n" +
                        "  )\n" +
                        ")");
        List<MemDevelop> memDevelops = iMemDevelopService.list(wrapper);
        if(CollUtil.isNotEmpty(memDevelops)) {
            memDevelops = memDevelops.stream().parallel().peek(e -> {
                // 获取档案完整度：1-完整；0-不完整
                int integrality = iMemDigitalService.getIntegrality(e.getDigitalLotNo());
                e.setIsIntegrality(integrality);
            }).collect(Collectors.toList());
            iMemDevelopService.updateBatchById(memDevelops);
        }

        LambdaQueryWrapper<Mem> memLambdaQueryWrapper = Wrappers.lambdaQuery();
        memLambdaQueryWrapper.select(Mem::getId, Mem::getDigitalLotNo);
        memLambdaQueryWrapper.isNull(Mem::getDeleteTime)
                .isNotNull(Mem::getDigitalLotNo)
                .in(Mem::getD08Code, "2", "1")
                // 获取（统计表没有的）  或者 （根据档案唯一码分组后 统计表中最大的创建时间比档案表中最大创建时间还晚的，说明有新的档案上传后异步计算完整度没有重计算到）
                .apply(" (not exists (select 1 from ccp_mem_digital_count where ccp_mem_digital_count.digital_lot_no = ccp_mem.digital_lot_no) " +
                        "   or ccp_mem.digital_lot_no in (\n" +
                        " select t1.digital_lot_no  from \n" +
                        " (select digital_lot_no, max(create_time) cd from ccp_mem_digital_count group by digital_lot_no) t1, \n" +
                        " (select digital_lot_no, max(create_time) dd  from ccp_mem_digital cmd where delete_time is null group by digital_lot_no) t2 \n" +
                        " where t1.digital_lot_no = t2.digital_lot_no and t1.cd < t2.dd\n" +
                        "  ) " +
                        ")");
        List<Mem> memList = imemService.list(memLambdaQueryWrapper);
        if(CollUtil.isNotEmpty(memList)) {
            memList = memList.stream().parallel().peek(e -> {
                // 获取档案完整度：1-完整；0-不完整
                int integrality = iMemDigitalService.getIntegrality(e.getDigitalLotNo());
                e.setIsIntegrality(integrality);
            }).collect(Collectors.toList());
            imemService.updateBatchById(memList);
        }
        log.info("入党申请人、积极分子、发展对象：" + memDevelops.size());
        log.info("正式、预备党员：" + memList.size());
        log.info("结束计算档案完整度详细信息...");
    }
}
