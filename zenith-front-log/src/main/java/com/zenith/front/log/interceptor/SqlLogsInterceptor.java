package com.zenith.front.log.interceptor;

import com.alibaba.druid.sql.ast.statement.SQLUpdateStatement;
import com.alibaba.druid.sql.dialect.postgresql.ast.stmt.PGDeleteStatement;
import com.alibaba.druid.sql.dialect.postgresql.ast.stmt.PGInsertStatement;
import com.alibaba.druid.sql.dialect.postgresql.parser.PGSQLStatementParser;
import com.baomidou.mybatisplus.core.toolkit.PluginUtils;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.baomidou.mybatisplus.extension.handlers.AbstractSqlParserHandler;
import com.zenith.front.common.constant.OperateConstant;
import com.zenith.front.model.constant.UserConstant;
import com.zenith.front.model.custom.UserTicket;
import com.zenith.front.log.Log;
import com.zenith.front.model.bean.OperateLog;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.ibatis.executor.Executor;
import org.apache.ibatis.executor.statement.StatementHandler;
import org.apache.ibatis.mapping.*;
import org.apache.ibatis.plugin.*;
import org.apache.ibatis.reflection.MetaObject;
import org.apache.ibatis.reflection.SystemMetaObject;
import org.apache.ibatis.session.Configuration;
import org.apache.ibatis.session.ResultHandler;
import org.apache.ibatis.session.RowBounds;
import org.apache.ibatis.type.TypeHandlerRegistry;
import org.springframework.stereotype.Component;

import java.sql.Connection;
import java.text.SimpleDateFormat;
import java.util.*;

/**
 * <AUTHOR>
 */
@Slf4j
@AllArgsConstructor
@Intercepts({
        @Signature(type = Executor.class, method = "query", args = {MappedStatement.class, Object.class, RowBounds.class, ResultHandler.class}),
        @Signature(type = StatementHandler.class, method = "prepare", args = {Connection.class, Integer.class})
})
@Component
public class SqlLogsInterceptor extends AbstractSqlParserHandler implements Interceptor {

    public static final Set<String> SKIP_TABLE_NAMES = new HashSet<String>(10) {
        private static final long serialVersionUID = -392393486845376992L;

        {
            add("sys_operate_log");
            add("sys_login_log");
            add("data_equals_record");
        }
    };

    @Override
    public Object intercept(Invocation invocation) throws Throwable {
        UserTicket userTicket = UserConstant.USER_CONTEXT.get();
        if (Objects.isNull(userTicket)) {
            return invocation.proceed();
        }
        StatementHandler statementHandler = PluginUtils.realTarget(invocation.getTarget());
        MetaObject metaObject = SystemMetaObject.forObject(statementHandler);
        MappedStatement mappedStatement = (MappedStatement) metaObject.getValue("delegate.mappedStatement");
        SqlCommandType sqlCommandType = mappedStatement.getSqlCommandType();
        if (SqlCommandType.SELECT.equals(sqlCommandType)) {
            return invocation.proceed();
        }
        BoundSql boundSql = (BoundSql) metaObject.getValue("delegate.boundSql");
        PGSQLStatementParser pgsqlStatementParser = new PGSQLStatementParser(boundSql.getSql());
        String simpleName = "";
        if (SqlCommandType.INSERT.equals(sqlCommandType)) {
            PGInsertStatement sqlInsertStatement = pgsqlStatementParser.parseInsert();
            simpleName = sqlInsertStatement.getTableName().getSimpleName();
        } else if (SqlCommandType.UPDATE.equals(sqlCommandType)) {
            SQLUpdateStatement sqlInsertStatement = pgsqlStatementParser.parseUpdateStatement();
            simpleName = sqlInsertStatement.getTableName().getSimpleName();
        } else if (SqlCommandType.DELETE.equals(sqlCommandType)) {
            PGDeleteStatement pgDeleteStatement = pgsqlStatementParser.parseDeleteStatement();
            simpleName = pgDeleteStatement.getTableName().getSimpleName();
        }
        if (SKIP_TABLE_NAMES.contains(simpleName)) {
            return invocation.proceed();
        }
        String sql = formatSql(boundSql, mappedStatement.getConfiguration());
        OperateConstant operateConstant = OperateConstant.getOperate(sql);
        OperateLog operateLog = new OperateLog();
        operateLog.setAccount(userTicket.getUser().getAccount());
        if (operateConstant != null) {
            operateLog.setOperate(operateConstant.getCode());
        }
        operateLog.setTableName(simpleName);
        operateLog.setSql(sql);
        operateLog.setCreateTime(new Date());
        Log.offer(operateLog);
        return invocation.proceed();
    }

    @Override
    public Object plugin(Object target) {
        return Plugin.wrap(target, this);
    }

    @Override
    public void setProperties(Properties properties) {

    }

    private static final ThreadLocal<SimpleDateFormat> DATE_FORMAT_THREAD_LOCAL = ThreadLocal.withInitial(() -> new SimpleDateFormat("yyyy-MM-dd HH:mm:ss"));

    /**
     * 获取完整的sql实体的信息
     *
     * @param boundSql
     * @return
     */
    private String formatSql(BoundSql boundSql, Configuration configuration) {
        String sql = boundSql.getSql();
        List<ParameterMapping> parameterMappings = boundSql.getParameterMappings();
        Object parameterObject = boundSql.getParameterObject();
        // 输入sql字符串空判断
        if (StringUtils.isBlank(sql)) {
            return "";
        }
        if (configuration == null) {
            return "";
        }
        TypeHandlerRegistry typeHandlerRegistry = configuration.getTypeHandlerRegistry();
        sql = beautifySql(sql);
        // 参考mybatis 源码 DefaultParameterHandler
        if (parameterMappings != null) {
            for (ParameterMapping parameterMapping : parameterMappings) {
                if (parameterMapping.getMode() != ParameterMode.OUT) {
                    Object value;
                    String propertyName = parameterMapping.getProperty();
                    if (boundSql.hasAdditionalParameter(propertyName)) {
                        value = boundSql.getAdditionalParameter(propertyName);
                    } else if (parameterObject == null) {
                        value = null;
                    } else if (typeHandlerRegistry.hasTypeHandler(parameterObject.getClass())) {
                        value = parameterObject;
                    } else {
                        MetaObject metaObject = configuration.newMetaObject(parameterObject);
                        value = metaObject.getValue(propertyName);
                    }
                    String paramValueStr = "";
                    if (value instanceof String) {
                        paramValueStr = "'" + value + "'";
                    } else if (value instanceof Date) {
                        paramValueStr = "'" + DATE_FORMAT_THREAD_LOCAL.get().format(value) + "'";
                    } else {
                        paramValueStr = value + "";
                    }
                    sql = sql.replaceFirst("\\?", paramValueStr);
                }
            }
        }
        return sql;
    }

    private String beautifySql(String sql) {
        sql = sql.replaceAll("[\\s\n ]+", " ");
        return sql;
    }

}
