package com.zenith.front.framework.file.util;

import cn.hutool.core.io.FileUtil;
import com.zenith.front.common.exception.ServiceException;
import lombok.extern.slf4j.Slf4j;
import net.lingala.zip4j.ZipFile;
import net.lingala.zip4j.model.ZipParameters;
import net.lingala.zip4j.model.enums.CompressionLevel;
import net.lingala.zip4j.model.enums.CompressionMethod;
import org.springframework.util.ResourceUtils;

import javax.imageio.ImageIO;
import java.awt.*;
import java.awt.geom.AffineTransform;
import java.awt.image.BufferedImage;
import java.io.File;
import java.util.List;

/**
 * 根据文件类型返回文件格式（文件夹）
 *
 * <AUTHOR>
 * @version V1.0
 * @date 2023/2/23 11:53
 */
@Slf4j
public class FileExtUtil {

    public static String getFileExtName(String mineType) {
        switch (mineType) {
            case "application/vnd.openxmlformats-officedocument.wordprocessingml.document":
            case "application/msword":
                return "doc";
            case "application/pdf":
                return "pdf";
            case "application/rtf":
                return "rtf";
            case "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet":
            case "application/vnd.ms-excel":
                return "xls";
            case "application/vnd.openxmlformats-officedocument.presentationml.presentation":
            case "application/vnd.ms-powerpoint":
                return "ppt";
            case "application/vnd.ms-works":
                return "wps";
            case "application/x-rar-compressed":
                return "rar";
            case "application/x-shockwave-flash":
                return "swf";
            case "application/zip":
                return "zip";

            case "audio/mpeg":
            case "audio/ogg":
            case "audio/x-m4a":
            case "audio/x-realaudio":
                return "audio";

            case "image/gif":
            case "image/jpeg":
            case "image/png":
            case "image/tiff":
            case "image/vnd.wap.wbmp":
            case "image/x-icon":
            case "image/x-jng":
            case "image/x-ms-bmp":
            case "image/svg+xml":
            case "image/webp":
                return "image";

            case "text/css":
            case "text/html":
            case "text/plain":
            case "text/xml":
                return "text";

            case "video/3gpp":
            case "video/mp4":
            case "video/mpeg":
            case "video/quicktime":
            case "video/webm":
            case "video/x-flv":
            case "video/x-m4v":
            case "video/x-ms-wmv":
            case "video/x-msvideo":
                return "video";
            default:
                return "unknown";
        }
    }


    /**
     * 重命名新文件夹
     *
     * @param parentDir 父目录路径
     * @param fileName  文件名
     * @return 新的文件
     */
    public static File reNameFolder(String parentDir, String fileName) {
        File parent = new File(parentDir);
        if (!FileUtil.exist(parent)) {
            FileUtil.mkdir(parent);
        }
        // 创建文件夹
        File targetFolder = new File(parent, fileName);
        if (!FileUtil.exist(targetFolder)) {
            FileUtil.mkdir(targetFolder);
            return targetFolder;
        }
        // 创建新文件夹
        int count = 1;
        while (true) {
            String newFolderName = fileName + count;
            targetFolder = new File(parent, newFolderName);

            if (!FileUtil.exist(targetFolder)) {
                FileUtil.mkdir(targetFolder);
                return targetFolder;
            }
            count++;
        }

    }

    /**
     * 文件压缩
     *
     * @param zipFile 压缩文件
     * @param files   待压缩的文件
     * @return 压缩文件
     */
    public static File zipFile(File zipFile, List<File> files) {
        String zipFilePath = zipFile.getPath();
        ZipParameters parameters = new ZipParameters();
        // 压缩方式
        parameters.setCompressionMethod(CompressionMethod.DEFLATE);
        // 压缩级别
        parameters.setCompressionLevel(CompressionLevel.NORMAL);
        parameters.setIncludeRootFolder(true);

        try (ZipFile zip = new ZipFile(zipFilePath)) {
            zip.setCharset(java.nio.charset.StandardCharsets.UTF_8);
            for (File file : files) {
                String path = file.getPath();
                if (file.isFile()) {
                    zip.addFile(file, parameters);
                } else {
                    zip.addFolder(FileUtil.file(path), parameters);
                }
            }
            return zip.getFile();
        } catch (Exception e) {
            throw new ServiceException(500, "压缩文件失败！" + e.getMessage());
        }
    }

    /**
     * 添加图片水印
     *
     * @param imgPath   图片路径
     * @param watermark 水印描述
     */
    public static void addWatermark(String imgPath, String watermark) {
        try {
            File file = FileUtil.file(imgPath);
            BufferedImage image = ImageIO.read(file);
            int width = image.getWidth();
            int height = image.getHeight();
            // 获取 Graphics2D
            Graphics2D g2d = (Graphics2D) image.getGraphics();
            // 启用抗锯齿
            g2d.setRenderingHint(RenderingHints.KEY_ANTIALIASING, RenderingHints.VALUE_ANTIALIAS_ON);

            // 设置字体和颜色
            int fontSize = Math.max(width, height) / 45;
            String path = ResourceUtils.getURL(ResourceUtils.CLASSPATH_URL_PREFIX).getPath();
            Font font = Font.createFont(Font.TRUETYPE_FONT, FileUtil.file(path + "public/fonts/simsun.ttc"))
                    .deriveFont(Font.BOLD, fontSize);
            g2d.setFont(font);
            g2d.setColor(Color.GRAY);
            // 设置透明度
            g2d.setComposite(AlphaComposite.getInstance(AlphaComposite.SRC_OVER, 0.6f));

            // 获取字体信息
            FontMetrics metrics = g2d.getFontMetrics();
            int w = metrics.stringWidth(watermark);
            int h = metrics.getHeight();
            // 旋转画布
            AffineTransform orig = g2d.getTransform();
            g2d.translate(width / 2, height / 2);
            g2d.rotate(Math.toRadians(-Math.toDegrees(Math.atan2(height, width))));

            // 绘制水印
            g2d.drawString(watermark, -w / 2, h / 4);

            // 还原画布
            g2d.setTransform(orig);
            g2d.dispose();
            ImageIO.write(image, "png", file);
        } catch (Exception e) {
            log.error("添加水印失败！", e);
        }
    }


}
