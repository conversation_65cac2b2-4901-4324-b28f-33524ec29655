package com.zenith.front.framework.file.ser;

import cn.hutool.core.util.StrUtil;
import com.fasterxml.jackson.core.JsonParser;
import com.fasterxml.jackson.databind.DeserializationContext;
import com.fasterxml.jackson.databind.JsonDeserializer;
import com.zenith.front.framework.file.core.MinioFilePath;

import java.io.IOException;

/**
 * 反序列化工具
 *
 * <AUTHOR>
 * @date 2023/3/16
 */
public class RemoveMinioBasePathDeserializer extends JsonDeserializer<String> {
    private final MinioFilePath minioFilePath;

    public RemoveMinioBasePathDeserializer(MinioFilePath minioFilePath) {
        this.minioFilePath = minioFilePath;
    }

    @Override
    public String deserialize(JsonParser jsonParser, DeserializationContext deserializationContext) throws IOException {
        String text = jsonParser.getText();
        if (StrUtil.isBlank(text)) {
            return null;
        }
        String bucketName = minioFilePath.basePathWithBucketName();
        if (StrUtil.contains(text, bucketName)) {
            return text.replace(bucketName, "");
        }
        return text;
    }

}
