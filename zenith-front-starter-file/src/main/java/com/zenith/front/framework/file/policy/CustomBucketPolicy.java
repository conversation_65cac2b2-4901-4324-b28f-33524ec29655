package com.zenith.front.framework.file.policy;

/**
 * 对指定桶的policy进行定制化配置
 * 在action中有s3:ListBucket这一项是allow的，所以我们可以在浏览器中遍历目录，删除即可
 *
 * <AUTHOR>
 * @version V1.0
 * @date 2023/2/23 14:37
 */
public class CustomBucketPolicy implements IBucketPolicy {

    private static final String CUSTOM_POLICY_JSON = "{\"Version\":\"2012-10-17\",\"Statement\":[{\"Effect\":\"Allow\",\"Principal\":{\"AWS\":[\"*\"]},\"Action\":[\"s3:ListBucketMultipartUploads\",\"s3:GetBucketLocation\"],\"Resource\":[\"arn:aws:s3:::" + BUCKET_NAME + "\"]},{\"Effect\":\"Allow\",\"Principal\":{\"AWS\":[\"*\"]},\"Action\":[\"s3:ListMultipartUploadParts\",\"s3:PutObject\",\"s3:AbortMultipartUpload\",\"s3:DeleteObject\",\"s3:GetObject\"],\"Resource\":[\"arn:aws:s3:::" + BUCKET_NAME + "/*\"]}]}";

    @Override
    public String createBucketPolicy(String bucketName) {
        return CUSTOM_POLICY_JSON.replace(BUCKET_NAME, bucketName);
    }
}
