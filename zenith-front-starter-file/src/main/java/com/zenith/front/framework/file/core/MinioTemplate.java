package com.zenith.front.framework.file.core;

import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.io.IoUtil;
import cn.hutool.core.util.StrUtil;
import com.zenith.front.common.kit.StrKit;
import com.zenith.front.framework.file.FileUploadResponse;
import com.zenith.front.framework.file.policy.BucketPolicyContent;
import com.zenith.front.framework.file.policy.BucketPolicyFactory;
import com.zenith.front.framework.file.util.FileExtUtil;
import com.zenith.front.framework.file.util.FileTypeUtils;
import io.minio.*;
import org.springframework.stereotype.Component;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;

/**
 * Minio 模板封装类
 *
 * <AUTHOR>
 * @version V1.0
 * @date 2023/2/22 11:09
 */
@Component
public class MinioTemplate {

    @Resource
    private MinioClient client;
    @Resource
    private MinioFilePath minioFilePath;

    /**
     * 上传
     *
     * @param folder 文件夹
     * @param file   文件
     * @return com.zenith.front.framework.file.FileUploadResponse
     * <AUTHOR>
     * @creed Talk is cheap,show me the code
     * @date 2023年02月22日 15时19分
     */
    public FileUploadResponse upload(String folder, MultipartFile file) throws Exception {
        String bucketName = minioFilePath.bucketName();
        return upload(bucketName, folder, file);
    }

    /**
     * 上传
     *
     * @param bucketName 桶名称，默认为节点key
     * @param file       文件
     * @return com.zenith.front.framework.file.FileUploadResponse
     * <AUTHOR>
     * @creed Talk is cheap,show me the code
     * @date 2023年02月22日 15时19分
     */
    private FileUploadResponse upload(String bucketName, String folder, MultipartFile file) throws Exception {
        //验证桶是否存在
        if (!bucketExists(bucketName)) {
            //创建桶
            makeBucket(bucketName);
        }
        //获取文件类型
        String mineType = FileTypeUtils.getMineType(file.getBytes());
        //文件名
        String originalFilename = file.getOriginalFilename();
        final String filename = StrUtil.replace(originalFilename, "'", "");
        // 设置存储对象名称 文件格式/文件名
        String datePath = DateUtil.format(DateUtil.date(), DatePattern.PURE_DATETIME_MS_PATTERN);
        String objectName = folder + MinioFilePath.FILE_SEPARATOR + FileExtUtil.getFileExtName(mineType) + MinioFilePath.FILE_SEPARATOR + datePath + MinioFilePath.FILE_SEPARATOR + filename;
        // 执行上传
        client.putObject(
                PutObjectArgs.builder()
                        // bucket 必须传递
                        .bucket(bucketName)
                        .contentType(file.getContentType())
                        // 相对路径作为 key
                        .object(objectName)
                        // 文件内容
                        .stream(file.getInputStream(), file.getSize(), -1)
                        .build()
        );
        return FileUploadResponse.builder().id(StrKit.getRandomUUID()).name(filename).url(objectName).build();
    }

    /**
     * 删除文件
     *
     * @param path 文件路径
     * <AUTHOR>
     * @creed Talk is cheap,show me the code
     * @date 2023年02月22日 15时22分
     */
    public void delete(String path) throws Exception {
        String bucketName = minioFilePath.bucketName();
        delete(bucketName, path);
    }

    /**
     * 删除文件
     *
     * @param bucketName 桶名称，默认为节点key
     * @param path       文件路径
     * <AUTHOR>
     * @creed Talk is cheap,show me the code
     * @date 2023年02月22日 15时22分
     */
    private void delete(String bucketName, String path) throws Exception {
        client.removeObject(RemoveObjectArgs.builder()
                // bucket 必须传递
                .bucket(bucketName)
                // 相对路径作为 key
                .object(path)
                .build());
    }

    /**
     * 获取文件
     *
     * @param path 文件路径
     * @return byte[]
     * <AUTHOR>
     * @creed Talk is cheap,show me the code
     * @date 2023年02月22日 15时23分
     */
    public byte[] getContent(String path) throws Exception {
        String bucketName = minioFilePath.bucketName();
        return getContent(bucketName, path);
    }

    /**
     * 获取文件
     *
     * @param bucketName 桶名称，默认为节点key
     * @param path       文件路径
     * @return byte[]
     * <AUTHOR>
     * @creed Talk is cheap,show me the code
     * @date 2023年02月22日 15时23分
     */
    private byte[] getContent(String bucketName, String path) throws Exception {
        GetObjectResponse response = client.getObject(GetObjectArgs.builder()
                // bucket 必须传递
                .bucket(bucketName)
                // 相对路径作为 key
                .object(path)
                .build());
        return IoUtil.readBytes(response);
    }

    /**
     * 验证桶是否存在
     *
     * @param bucketName 桶名称，默认为节点key
     * @return boolean 是否存在
     * <AUTHOR>
     * @creed Talk is cheap,show me the code
     * @date 2023年02月22日 16时16分
     */
    public boolean bucketExists(String bucketName) {
        BucketExistsArgs bucketExistsArgs = BucketExistsArgs.builder().bucket(bucketName).build();
        try {
            return client.bucketExists(bucketExistsArgs);
        } catch (Exception e) {
            e.printStackTrace();
            return false;
        }
    }

    /**
     * 创建桶,默认为自定义策略
     *
     * @param bucketName 桶名称，默认为节点key
     * @return boolean 是否创建成功
     * <AUTHOR>
     * @creed Talk is cheap,show me the code
     * @date 2023年02月22日 16时16分
     */
    public boolean makeBucket(String bucketName) {
        MakeBucketArgs makeBucketArgs = MakeBucketArgs.builder().bucket(bucketName).build();
        try {
            //创建存储桶
            client.makeBucket(makeBucketArgs);
            String config = BucketPolicyFactory.getBucketPolicy(BucketPolicyContent.CUSTOM).createBucketPolicy(bucketName);
            //设置自定义权限
            client.setBucketPolicy(SetBucketPolicyArgs.builder().bucket(bucketName).config(config).build());
        } catch (Exception e) {
            e.printStackTrace();
            return false;
        }
        return true;
    }

    /**
     * 删除存储bucket
     *
     * @param bucketName 桶名称，默认为节点key
     * @return boolean 是否成功
     * <AUTHOR>
     * @creed Talk is cheap,show me the code
     * @date 2023年02月22日 16时16分
     */
    public boolean removeBucket(String bucketName) {
        try {
            client.removeBucket(RemoveBucketArgs.builder().bucket(bucketName).build());
        } catch (Exception e) {
            e.printStackTrace();
            return false;
        }
        return true;
    }

}
