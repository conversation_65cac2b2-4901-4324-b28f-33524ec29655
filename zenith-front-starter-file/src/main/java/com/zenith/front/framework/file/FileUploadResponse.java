package com.zenith.front.framework.file;

import lombok.Builder;
import lombok.Data;

/**
 * 文件上传后响应实体
 *
 * <AUTHOR>
 * @version V1.0
 * @date 2023/2/22 17:20
 */
@Data
@Builder
public class FileUploadResponse {

    /**
     *
     */
    private String id;

    /**
     * 原文件名
     */
    private String name;

    /**
     * 访问路径
     */
    private String url;

    /**
     * 预览地址
     */
    private String previewPath;

    /**
     * 序号
     */
    private Integer sort;

    /**
     * 大小kb单位
     */
    private Long fileSize;
}
