package com.zenith.front.framework.file.policy;

/**
 * 生成桶策略
 *
 * <AUTHOR>
 * @version V1.0
 * @date 2023/2/23 13:54
 */
public interface IBucketPolicy {

    String BUCKET_NAME = "${bucket}";

    /**
     * 生成桶策略
     *
     * @param bucketName 桶名称
     * @return java.lang.String
     * <AUTHOR>
     * @creed Talk is cheap,show me the code
     * @date 2023年02月23日 15时01分
     */
    String createBucketPolicy(String bucketName);
}
