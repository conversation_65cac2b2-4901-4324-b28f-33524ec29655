<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <parent>
        <groupId>com.zenith</groupId>
        <artifactId>zenith-front-dj</artifactId>
        <version>1.1.10.301</version>
    </parent>

    <artifactId>zenith-front-api</artifactId>

    <dependencies>
        <dependency>
            <groupId>com.zenith</groupId>
            <artifactId>zenith-front-model</artifactId>
        </dependency>
        <dependency>
            <groupId>com.sjr</groupId>
            <artifactId>analysis-core</artifactId>
            <version>1.0.0</version>
            <scope>compile</scope>
        </dependency>
    </dependencies>

</project>
