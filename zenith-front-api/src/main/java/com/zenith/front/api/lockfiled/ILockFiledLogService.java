package com.zenith.front.api.lockfiled;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.zenith.front.model.dto.LockLogDTO;
import com.zenith.front.model.dto.LockLogMsgDTO;
import com.zenith.front.model.message.OutMessage;
import com.zenith.front.model.bean.LockFiledLog;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;

/**
 * <p>
 * 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-03-10
 */
public interface ILockFiledLogService extends IService<LockFiledLog> {

    /**
     * 保存日志
     *
     * @param uqCode       日志记录code
     * @param type         申请记录1 解锁记录2 锁定记录3
     * @param unLockObject 解锁对象 1 党员 2 单位 3 组织
     * @param unLockCode   解锁对象code
     * @param levelCode    层级码
     * @param orgCode      操作用户所在组织code
     * @param account      操作账号名称
     * @param memName      操作账号用户名
     * @param unlockReason 解锁理由
     * @param name         解锁对象名称
     * @param state        日志记录状态
     * @return
     */
    boolean saveLog(String uqCode, String type, String unLockObject, String unLockCode, String levelCode, String orgCode, String account
            , String memName, String unlockReason, String name, String state);

    /**
     * 根据集合批量报错日志
     *
     * @param lockFiledLogList 日志集合
     * @return
     */
    boolean saveBatchLog(List<LockFiledLog> lockFiledLogList);

    /**
     * 查询申请记录
     *
     * @param lockLogDTO 实体类
     * @return
     */
    Page<LockFiledLog> applicationRecord(LockLogDTO lockLogDTO);

    /**
     * 党员、党组织和单位的信息编辑界面上，要显示是谁锁定，谁批准解锁的
     *
     * @param lockLogMsgDTO 实体类
     * @return
     */
    String msg(LockLogMsgDTO lockLogMsgDTO);

    /**
     * 申请记录-拒绝解锁
     *
     * @param code         数据锁定日志记录主键
     * @param refuseReason 拒绝理由
     * @param account      操作账号
     * @return
     */
    OutMessage<?> refuse(String code, String refuseReason, String account);
}
