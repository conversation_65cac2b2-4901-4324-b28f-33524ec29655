package com.zenith.front.api.org;

import com.baomidou.mybatisplus.extension.service.IService;
import com.zenith.front.model.dto.OrgSlackDto;
import com.zenith.front.model.dto.OrgSlackListDto;
import com.zenith.front.model.message.OutMessage;
import com.zenith.front.model.bean.OrgSlack;

import java.text.ParseException;
import java.util.List;

/**
 * <p>
 * 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-08-05
 */
public interface IOrgSlackService extends IService<OrgSlack> {

    OrgSlack findCode(String code);

    /**
     * 获取涣散组织列表
     */
    OutMessage<?> getList(OrgSlackListDto orgListDTO) throws ParseException;

    /**
     * 根据code查找组织
     */
    OutMessage<?> findByCode(String code);

    /**
     * 新增涣散组织
     */
    OutMessage<?> addSlackOrg(OrgSlackDto orgDTO);

    /**
     * 编辑涣散组织
     */
    OutMessage<?> updateSlackOrg(OrgSlackDto orgDTO);

    /**
     * 删除涣散组织
     */
    OutMessage<?> delSlackOrg(String code);

    OutMessage<?> export(OrgSlackListDto orgListDTO) throws Exception;

    /**
     * 根据组织code查询软弱涣散党组织
     *
     * @param orgCode 组织code
     * @return
     */
    List<OrgSlack> findOrgSlackByOrgCode(String orgCode);

    /**
     * 获取本年软弱涣散党组织
     */
    List<OrgSlack> findSlackByOrgCode(String orgCode);
}
