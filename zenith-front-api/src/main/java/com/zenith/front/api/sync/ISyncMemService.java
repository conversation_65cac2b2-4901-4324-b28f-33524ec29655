package com.zenith.front.api.sync;

import com.zenith.front.model.bean.*;
import com.zenith.front.model.message.OutMessage;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2021/10/25
 */
public interface ISyncMemService {
    /**
     * 同步党员基本信息
     *
     * @param code 党员code
     * @param type 1基础信息，关联单位，3关联单位班子，4关联组织班子，5关联民主评议
     * @return 操作结果
     */
    OutMessage<?> syncMem(String code, String type);

    /**
     * 根据组织code获取关联单位
     *
     * @param orgCode 组织code
     * @return 关联单位信息
     */
    UnitOrgLinked getUnitOrgLinked(String orgCode);

    /**
     * 获取关联单位与上级党组织相同的所有组织
     *
     * @param orgLevelCode 组织层级码
     * @return 党组织code集合
     */
    List<String> findSubOrgCodeList(String orgLevelCode);

    /**
     * 同步发展党员相关信息
     *
     * @param code 发展党员code
     * @return 操作结果
     */
    OutMessage<?> syncMemDevelop(String code);


    /**
     * 同步人员奖惩相关信息
     *
     * @param code 奖惩code
     * @return 操作结果
     */
    OutMessage<?> syncMemReward(String code);

    /**
     * 同步流动党员相关信息
     *
     * @param code 流动党员code
     * @return 操作结果
     */
    OutMessage<?> syncMemFlow(String code);


    /**
     * 修改单位
     * 同步党员，发展党员，流动党员信息，本年度发展党员
     *
     * @param unitCode 单位code
     */
    OutMessage<?> syncMemByUnit(String unitCode);

    /**
     * 修改党组织
     * 同步党员，发展党员，流动党员信息，本年度发展党员
     *
     * @param orgCode 组织code
     */
    OutMessage<?> syncMemByOrg(String orgCode);

    /**
     *
     * 修改党组织
     * 同步党员，发展党员，流动党员信息，本年度发展党员
     * @param orgCode
     * @param mainUniCodeAccordBol 是否变更了单位
     */
    void syncMemD194Logic(String orgCode, boolean mainUniCodeAccordBol);

     void setMem(UnitAll unitAll, Mem mem);

    void setDevelopMem(UnitAll unitAll, MemDevelop memDevelop);

    void setDevelopStepLog(UnitAll unitAll, DevelopStepLog stepLog);

     boolean getMemReportByidCard(String code, String orgCode);
}
