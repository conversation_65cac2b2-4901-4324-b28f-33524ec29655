package com.zenith.front.api.mem;

import com.baomidou.mybatisplus.extension.service.IService;
import com.zenith.front.model.dto.MemManyDTO;
import com.zenith.front.model.dto.MemMultipleListDTO;
import com.zenith.front.model.message.OutMessage;
import com.zenith.front.model.bean.MemMany;

/**
 * <p>
 * 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-07-10
 */
public interface IMemManyService extends IService<MemMany> {

    /**
     * 根据组织及人员查找
     */
    MemMany findByMemCodeAndCurrOrgCode(String memCode, String orgCode);

    MemMany findByMemCodeAndJoinOrgCode(String memCode, String joinOrgCode);

    /**
     * 获取多重党员数量
     */
    Long getTotal(String orgCode);

    /**
     * 根据code查找多重党员
     *
     * @param code
     * @return
     */
    MemMany findByCode(String code);

    /**
     * 获取多重党员列表
     *
     * @param multipleListDTO
     * @return
     */
    OutMessage getList(MemMultipleListDTO multipleListDTO);

    /**
     * 新增多重党员
     *
     * @param memManyDTO
     * @return
     */
    OutMessage addMemMultiple(MemManyDTO memManyDTO) throws Exception;

    /**
     * 根据code查找多重党员
     *
     * @param code
     * @return
     */
    OutMessage findByCodeOut(String code);

    /**
     * 修改多重党员
     *
     * @param memManyDTO
     * @return
     */
    OutMessage updateMemMultiple(MemManyDTO memManyDTO) throws Exception;

    /**
     * 取消多重党员关联
     *
     * @param code
     * @return
     */
    OutMessage delMemMultiple(String code);

}
