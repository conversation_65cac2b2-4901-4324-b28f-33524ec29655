package com.zenith.front.api.org;

import com.baomidou.mybatisplus.extension.service.IService;
import com.zenith.front.model.dto.OrgContactDTO;
import com.zenith.front.model.dto.OrgContactListDTO;
import com.zenith.front.model.message.OutMessage;
import com.zenith.front.model.bean.OrgContactWork;

import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * <p>
 * 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-12-14
 */
public interface IOrgContactWorkService extends IService<OrgContactWork> {

    /**
     * 添加工作开展情况
     */
    OutMessage<?> addContactWork(OrgContactDTO data, String basePath);

    /**
     * 工作开展情况列表
     */
    OutMessage<?> contactWorkList(OrgContactListDTO data);

    /**
     * 修改工作开展情况
     */
    OutMessage<?> updateContactWork(OrgContactDTO data, String basePath);

    /**
     * 删除工作开展情况
     */
    OutMessage<?> deleteContactWork(String code);

    /**
     * 工作开展情况详情
     */
    OutMessage<?> findContactWork(String code);

    /**
     * 工作开展情况导出
     */
    OutMessage<?> outExportXlsx(OrgContactListDTO data) throws Exception;

    Map<String, List<OrgContactWork>> getListLikeOrgCode(Date startDate, Date endDate, String orgCode);

    List<OrgContactWork> getContactWorkList(String orgCode);
}
