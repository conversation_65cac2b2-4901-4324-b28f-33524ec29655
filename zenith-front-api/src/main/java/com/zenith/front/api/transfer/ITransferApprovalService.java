package com.zenith.front.api.transfer;

import com.baomidou.mybatisplus.extension.service.IService;
import com.zenith.front.model.bean.TransferApproval;

import java.util.Collection;
import java.util.List;

/**
 * <p>
 * 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-07-10
 */
public interface ITransferApprovalService extends IService<TransferApproval> {

    boolean rewriteSave(TransferApproval orgTransferApproval);

    boolean rewriteUpdateById(TransferApproval orgTransferApproval);

    /****
     * 通过转接记录id查询审批记录
     * @param recordId 转接记录id
     * */
    List<TransferApproval> findByRecordId(String recordId);


    TransferApproval findById(String currentApprovalId);

    /***
     * 通过父id和组织id查询子审批记录
     * @param parentId 父id
     * @param orgId 组织id
     * */
    TransferApproval findSubTransferApprovalByIdAndOrgId(String parentId, String orgId);

    /***
     * 通过recordId与parentId查询审批记录
     * @param recordId 转接记录id
     * @param parentId 审批记录父id
     * */
    TransferApproval findByRecordIdAndParentId(String recordId, String parentId);

    /***
     * 通过转接记录id查询审批记录中的公共节点
     * @param recordId 转接记录id
     * */
    TransferApproval findCommonNodeByRecordId(String recordId, String commonOrgId);

    /**
     * 根据审核id获取所有审核的id
     * @param approvalIds 转接审批记录的id集合
     * ***/
    List<TransferApproval> findApprovalByList(Collection<String> approvalIds);

    boolean batchUpdate(List<TransferApproval> updateList);
}
