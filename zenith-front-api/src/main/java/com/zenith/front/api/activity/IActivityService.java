package com.zenith.front.api.activity;

import com.baomidou.mybatisplus.extension.service.IService;
import com.zenith.front.model.dto.ActivityDTO;
import com.zenith.front.model.dto.LedgerDTO;
import com.zenith.front.model.message.OutMessage;
import com.zenith.front.model.bean.Activity;

import java.util.List;

/**
 * <p>
 * 活动表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-07-10
 */
public interface IActivityService extends IService<Activity> {

    OutMessage<Object> addAc(ActivityDTO dto);

    OutMessage<Object> findAcByCode(String code, String manageCode, Integer entryType, String memCode) throws Exception;

    OutMessage<Object> updateAc(ActivityDTO dto, String manageCode);

    OutMessage<Object> cancelAc(String code, String manageOrgCode);

    OutMessage<Object> listAC(String memCode, Integer pageNum, Integer pageSize, List<Integer> statusList, Integer entryType, String org_org_code, List<String> activityType, String activityName);

    Long getTotalByOrgCode(String orgCode);

    /**
     * 获取三会一课明细台账
     * @param data
     * @return
     */
    OutMessage<Object> getHreMeetingsAndOneClass(LedgerDTO data);

    /**
     * 主题党日计划
     * @param data
     * @return
     */
    OutMessage<Object> getThematicPartyDay(LedgerDTO data);

    OutMessage<Object> situation(String manageOrgCode);
}
