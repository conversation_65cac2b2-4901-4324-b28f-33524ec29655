package com.zenith.front.api.mem;

import com.baomidou.mybatisplus.extension.service.IService;
import com.zenith.front.model.bean.MemAllInfo;

import java.util.List;

/**
 * <p>
 * 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-10-25
 */
public interface IMemAllInfoService extends IService<MemAllInfo> {

    MemAllInfo findByCode(String code);

    MemAllInfo findNameByCode(String code);

    MemAllInfo memAllInfoNotDel(String code);

    List<MemAllInfo> findByOrgCode(String orgCode);

    /**
     * 根据人员code集合 查询all信息
     *
     * @param memCode 人员code集合
     * @return
     */
    List<MemAllInfo> findMemAllByCode(List<String> memCode);

    /**
     * 根据人员code 查询all信息
     *
     * @param memCode 人员code集合
     * @return
     */
    MemAllInfo findMemAllByCode(String memCode);

    Boolean batchUpdate(List<MemAllInfo> memAllInfos);

    /**
     * 导出本年内参加民主评议的党员
     * @param orgCode
     */
    void exportYearJoinDemocraticReviewParty(String orgCode);

    /**
     * 导出本年内受纪律处分党员
     * @param orgCode
     */
    void exportYearInDisgraceParty(String orgCode);
}
