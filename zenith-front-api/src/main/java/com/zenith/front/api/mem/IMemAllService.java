package com.zenith.front.api.mem;

import com.baomidou.mybatisplus.extension.service.IService;
import com.zenith.front.model.dto.ChartDataDTO;
import com.zenith.front.model.message.OutMessage;
import com.zenith.front.model.bean.MemAll;

import java.util.Map;

/**
 * <p>
 * 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-07-10
 */
public interface IMemAllService extends IService<MemAll> {

    /**
     * 获取人员总数
     *
     * @param data
     * @return
     */
    OutMessage getMemTotal(ChartDataDTO data);

    /**
     * 获取民族比例
     *
     * @param data
     * @return
     */
    OutMessage getNationRatioTotal(ChartDataDTO data);

    /**
     * 获取大专及以上的党员数
     *
     * @param data
     * @return
     */
    OutMessage getDzTotal(ChartDataDTO data);

    /**
     * 获取男女比例
     *
     * @param data
     * @return
     */
    OutMessage getSexRatioTotal(ChartDataDTO data);

    /**
     * 获取学历比例
     *
     * @param data
     * @return
     */
    OutMessage getEducationRatioTotal(ChartDataDTO data);

    /**
     * 获取工作岗位比例
     *
     * @param data
     * @return
     */
    OutMessage getJobRatioTotal(ChartDataDTO data);

    /**
     * 获取公有工作岗位比例
     *
     * @param data
     * @return
     */
    OutMessage getGyJobRatioTotal(ChartDataDTO data);

    /**
     * 获取非公有工作岗位比例
     *
     * @param data
     * @return
     */
    OutMessage getFgyJobRatioTotal(ChartDataDTO data);

    /**
     * 社会组织所有分类
     *
     * @param data
     * @return
     */
    OutMessage getShzzJobRatioTotal(ChartDataDTO data);

    /**
     * 获取农牧渔民所有分类
     *
     * @param data
     * @return
     */
    OutMessage getNmymJobRatioTotal1(ChartDataDTO data);

    /**
     * 获取困难党员数据
     *
     * @param data
     * @return
     */
    OutMessage getMemDifficultTotal(ChartDataDTO data);

    /**
     * 获取多重党员数据
     *
     * @param data
     * @return
     */
    OutMessage getManyMemTotal(ChartDataDTO data);

    /**
     * 获取历史党员数据
     *
     * @param data
     * @return
     */
    OutMessage getMemHistoryTotal(ChartDataDTO data);

    /**
     * 获取年龄分布图
     *
     * @param data
     * @return
     */
    OutMessage getAgeRatioTotal(ChartDataDTO data);

    /**
     * 获取党龄分布图
     *
     * @param data
     * @return
     */
    OutMessage getPartyAgeRatioTotal(ChartDataDTO data);

    /**
     * 获取党龄时间段分布图
     *
     * @param data
     * @return
     */
    OutMessage getPartyAgeParagraphRatioTotal(ChartDataDTO data);

    /**
     * 获取党员信息完整度平均值
     *
     * @param data
     * @return
     */
    OutMessage getMemRatio(ChartDataDTO data);

    /**
     * 人员类型分布图
     *
     * @param data
     * @return
     */
    OutMessage getMemLayout(ChartDataDTO data);

    Map<String, Map<String, Number>> getReportResultHtml_29(String reportCode, String orgCode, String orgLevelCode, Map<String, Map<String, Number>> resultMap, String tableYear);

    Map<String, Map<String, Number>> getReportResultHtml_30(String reportCode, String orgCode, String orgLevelCode, Map<String, Map<String, Number>> resultMap, String tableYear);

    Map<String, Map<String, Number>> getReportResultHtml_35(String reportCode, String orgCode, String orgLevelCode, Map<String, Map<String, Number>> resultMap, String tableYear);

    Map<String, Map<String, Number>> getReportResultHtml_33(String reportCode, String orgCode, String orgLevelCode, Map<String, Map<String, Number>> resultMap, String tableYear);

    Map<String, Map<String, Number>> getReportResultHtml_27(String reportCode, String orgCode, String orgLevelCode, Map<String, Map<String, Number>> resultMap, String tableYear);

    Map<String, Map<String, Number>> getReportResultHtml_26(String reportCode, String orgCode, String orgLevelCode, Map<String, Map<String, Number>> resultMap, String tableYear);

    Map<String, Map<String, Number>> getReportResultHtml_29_5(String orgLevelCode, Map<String, Map<String, Number>> resultMap, String tableYear);

    Map<String, Map<String, Number>> getReportResultHtml_29_13(String orgCode, String orgLevelCode, Map<String, Map<String, Number>> resultMap, String tableYear);

    // 2024年统
    Map<String, Map<String, Number>> getReportResultHtml2024_29(String reportCode, String orgCode, String orgLevelCode, Map<String, Map<String, Number>> resultMap, String tableYear);
    Map<String, Map<String, Number>> getReportResultHtml2024_30(String reportCode, String orgCode, String orgLevelCode, Map<String, Map<String, Number>> resultMap, String tableYear);
    Map<String, Map<String, Number>> getReportResultHtml2024_32(String reportCode, String orgCode, String orgLevelCode, Map<String, Map<String, Number>> resultMap, String tableYear);
    Map<String, Map<String, Number>> getReportResultHtml2024_32_13(String orgCode, String orgLevelCode, Map<String, Map<String, Number>> resultMap, String tableYear);
    Map<String, Map<String, Number>> getReportResultHtml2024_33(String reportCode, String orgCode, String orgLevelCode, Map<String, Map<String, Number>> resultMap, String tableYear);
    Map<String, Map<String, Number>> getReportResultHtml2024_36(String reportCode, String orgCode, String orgLevelCode, Map<String, Map<String, Number>> resultMap, String tableYear);
    Map<String, Map<String, Number>> getReportResultHtml2024_38(String reportCode, String orgCode, String orgLevelCode, Map<String, Map<String, Number>> resultMap, String tableYear);

}
