package com.zenith.front.api.activist;

import com.baomidou.mybatisplus.extension.service.IService;
import com.zenith.front.model.bean.ActivistTransferLog;
import com.zenith.front.model.bean.TransferLog;

import java.util.List;

/**
 * <p>
 * 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-05-09
 */
public interface IActivistTransferLogService extends IService<ActivistTransferLog> {

    /**
     * 保存审批日志
     *
     * @param handleApprovalId 处理审批的id
     * @param effectApprovalId 受影响的审批id
     * @param type             操作类型
     * @param reason           操作原因
     */
    boolean insertOrgTransferLog(String handleApprovalId, String effectApprovalId, Integer type, String reason);

    /**
     * 查询审批记录对应的日志
     */
    List<TransferLog> findByHandleApprovalId(String approvalId);
}
