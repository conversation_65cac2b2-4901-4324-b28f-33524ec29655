package com.zenith.front.api.org;

import com.baomidou.mybatisplus.extension.service.IService;
import com.zenith.front.model.dto.OrgContactDTO;
import com.zenith.front.model.message.OutMessage;
import com.zenith.front.model.bean.OrgExampleSite;

/**
 * <p>
 * 党支部标准化规范化建设达标尿范点情况 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-12-14
 */
public interface IOrgExampleSiteService extends IService<OrgExampleSite> {

    /**
     * 修改党支部达标/示范点标记
     */
    OutMessage<?> updateExampleSite(OrgContactDTO data);

    /**
     * 党支部达标/示范点标记
     */
    OutMessage<?> findExampleSiteById(String orgCode);
}
