package com.zenith.front.api.unit;

import com.baomidou.mybatisplus.extension.service.IService;
import com.zenith.front.model.bean.UnitOrgLinked;

import java.util.Collection;
import java.util.List;

/**
 * <p>
 * 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-07-10
 */
public interface IUnitOrgLinkedService extends IService<UnitOrgLinked> {

    UnitOrgLinked findByOrgCodeUseTwo(String code);

    boolean batchSave(List<UnitOrgLinked> unitOrgLinkedList);

    /**
     * 通过单位code查询
     *
     * @param code
     * @return
     */
    List<UnitOrgLinked> findByUnitCode(String code);

    /**
     * 根据单位删除
     *
     * @param code
     * @return
     */
    boolean delUnitOrgLinkedByUnit(String code);

    /**
     * 通过组织code查询
     *
     * @param code
     * @return
     */
    List<UnitOrgLinked> findByOrgCode(String code);

    /**
     * 通过多个组织code查询
     *
     * @param orgCodeList
     * @return
     */
    List<UnitOrgLinked> findByOrgCodeList(List<String> orgCodeList);

    /**
     * 根据组织删除
     *
     * @param code
     */
    boolean delUnitOrgLinkedByOrg(String code);

    /**
     * 根据组织查询主单位
     *
     * @param code
     * @return
     */
    UnitOrgLinked findByOrgCodeAndIsMainUnit(String code);

    /**
     * 根据组织集合查询所有党组织关联主单位
     *
     * @param orgCodes
     * @return
     */
    List<UnitOrgLinked> findByOrgCodeAndIsMainUnit(List<String> orgCodes);

    /**
     * 根据单位查询主组织
     *
     * @param code
     * @return
     */
    UnitOrgLinked findByUnitCodeAndIsMainOrg(String code);

    UnitOrgLinked findByUnitCodeAndIsMainAll(String unitCode);

    /**
     * 通过单位code 查询有住组织的单位
     */
    List<UnitOrgLinked> findByUnitCodesAndIsOrgMain(List<String> unitCodes, Integer isOrgMain);

    /**
     * 通过单位code 查询有住组织的单位
     */
    List<UnitOrgLinked> findByUnitCodes(List<String> unitCodes);

    /**
     * 查询所有单位关联关系情况
     **/
    List<UnitOrgLinked> findAllLinked();

    /**
     * 通过多个组织code和单位code查询
     *
     * @param orgCodeList
     */
    List<UnitOrgLinked> findByOrgCodeListAndUnitCode(Collection<String> orgCodeList, String unitCode);

}
