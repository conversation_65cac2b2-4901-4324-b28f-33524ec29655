package com.zenith.front.api.message;

import com.zenith.front.model.message.OutMessage;
import com.zenith.front.model.bean.MqMessage;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;

/**
 * <p>
 * 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-07-26
 */
public interface IMqMessageService extends IService<MqMessage> {

    /**
     * 消息提醒列表
     *
     * @param pageNum  分页：当前页
     * @param pageSize 分页：每页显示条数
     * @param orgCode  用户所在组织
     * @param account  账户
     * @return com.zenith.front.message.OutMessage<java.lang.Object>
     * <AUTHOR>
     * @creed Talk is cheap,show me the code
     * @date 2022年07月27日 16时47分
     */
    OutMessage<Object> findList(Integer pageNum, Integer pageSize, String orgCode, String account);

    /**
     * 消息查看
     *
     * @param code    消息主键
     * @param ignore  是否忽略
     * @param orgCode 层级码
     * @param account 账户
     * @return com.zenith.front.message.OutMessage<java.lang.Object>
     * <AUTHOR>
     * @creed Talk is cheap,show me the code
     * @date 2022年07月27日 17时09分
     */
    OutMessage<Object> cat(List<String> code, boolean ignore, String orgCode, String account);

    /**
     * 按类型删除消息提醒
     *
     * @param type 类型
     * <AUTHOR>
     * @creed Talk is cheap,show me the code
     * @date 2022年07月28日 17时01分
     */
    void deleteByType(String type);
}
