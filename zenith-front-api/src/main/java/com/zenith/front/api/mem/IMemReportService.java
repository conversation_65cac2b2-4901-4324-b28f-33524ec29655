package com.zenith.front.api.mem;

import com.baomidou.mybatisplus.extension.service.IService;
import com.zenith.front.model.vo.BaseReport;
import com.zenith.front.model.bean.MemReport;

import java.util.Date;
import java.util.List;

/**
 * <p>
 * 乡镇行政村等 领导班子统计表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-04-13
 */
public interface IMemReportService extends IService<MemReport> {
    /**
     * 同步组织班子成员信息
     */
    void syncOrgCommittee(String orgCode);

    /**
     * 同步单位班子成员信息
     */
    void syncUnitCommittee(String unitCode);

    /**
     * 同步驻村第一书记和驻村干部
     */
    void syncUnitResident(String unitCode);

    /**
     * 同步村后备干部
     */
    void syncUnitCountryside(String unitCode);

    /**
     * 同步党员培训情况
     */
    void syncMemTrainInfo(String memCode);

    /**
     * 初始化统计表数据
     */
    void initMemReport();

    /**
     * 根据组织层级码查询人员报表信息
     *
     * @param orgCode 组织层级码
     * @return
     */
    List<BaseReport> selectListByOrgCode(String orgCode);

    List<BaseReport> selectListByOrgCodeEntry(String orgCode);

    /**
     * 修改党员基础信息
     *
     * @param code       党员唯一标识符
     * @param name       党员姓名
     * @param idcard     党员身份证
     * @param memOrgCode 党员层级码
     * @param phone      党员联系方式
     * @param birthday   党员生日
     * @param age        党员年龄
     * @param sexCode    党员性别
     * @param sexName    党员性别
     * @param d07Code    党员学历
     * @param d07Name    党员学历
     * <AUTHOR>
     * @creed Talk is cheap,show me the code
     * @date 2022年05月16日 10时40分
     */
    void updateMemInfo(String code, String name, String idcard, String memOrgCode, String phone, Date birthday, Integer age, String sexCode
            , String sexName, String d07Code, String d07Name);

    /**
     * 查找符合条件的党员报表信息
     *
     * @param memCode 党员唯一标识符
     * @return com.zenith.front.model.MemReport
     * <AUTHOR>
     * @creed Talk is cheap,show me the code
     * @date 2022年05月16日 10时50分
     */
    List<MemReport> findByMemCode(String memCode);


    /**
     * 查找符合条件的党员报表信息
     *
     * @param name   党员姓名
     * @param idcard 党员身份证
     * @return com.zenith.front.model.MemReport
     * <AUTHOR>
     * @creed Talk is cheap,show me the code
     * @date 2022年05月16日 10时50分
     */
    List<MemReport> findByNameAndIdcard(String name, String idcard);
}
