package com.zenith.front.api.unit;

import com.baomidou.mybatisplus.extension.service.IService;
import com.zenith.front.model.bean.UnitExtend;

import java.util.List;

/**
 * <p>
 * 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-08-09
 */
public interface IUnitExtendService extends IService<UnitExtend> {

    /**
     * 查询单位扩展信息
     *
     * @param code
     * @return
     */
    UnitExtend findByCode(String code);

    /**
     * 查询单位扩展部分信息
     */
    List<UnitExtend> findAnyByCodes(List<String> unitCodeList);


}
