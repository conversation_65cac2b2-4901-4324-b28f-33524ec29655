package com.zenith.front.api.user;

import com.baomidou.mybatisplus.extension.service.IService;
import com.zenith.front.model.dto.UserRolePermissionDTO;
import com.zenith.front.model.message.OutMessage;
import com.zenith.front.model.bean.UserRolePermission;

import java.util.List;

/**
 * <p>
 * 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-07-10
 */
public interface IUserRolePermissionService extends IService<UserRolePermission> {

    /***
     * 批量保存用户角色权限信息
     * */
    boolean batchSave(List<UserRolePermission> userRolePermissionList);

    UserRolePermission findById(String currentUserRoleid);

    /***
     * 批量更新用户权限信息
     * */
    boolean batchUpdate(List<UserRolePermission> userRolePermissionList);

    /***
     * 清除用户的所有权限信息
     * */
    boolean cleanAll(String userId);

    /***
     * 编辑用户权限
     * */
    OutMessage<String> editUserPermission(List<UserRolePermissionDTO> data);

    /**
     * 根据角色id和用户id查找中间表
     *
     * @param userId
     * @param roleId
     * @return
     */
    UserRolePermission findByUserIdAndRoleId(String userId, String roleId);


    List<UserRolePermission> updateUserRolePermissionOrgCode(String newOrgCode);

    /**
     * 根据角色id集合查询中间表数据
     *
     * @param roleIds
     * @return
     */
    List<UserRolePermission> findByRoleIds(List<String> roleIds);

    /**
     * 根据角色id查询中间表数据
     *
     * @param roleId
     * @return
     */
    List<UserRolePermission> findByRoleId(String roleId);

    /***
     * 根据权限下标， 获取管理组织里面，所有有该权限的人
     * @param index
     * @param value
     * @param orgIdList
     * @return
     */

    List<UserRolePermission> findByIndexValue(Integer index,String value,List<String> orgIdList);
}
