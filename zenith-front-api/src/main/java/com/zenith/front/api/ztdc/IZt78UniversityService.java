package com.zenith.front.api.ztdc;

import com.baomidou.mybatisplus.extension.service.IService;
import com.zenith.front.model.dto.Zt78UniversityDto;
import com.zenith.front.model.message.OutMessage;
import com.zenith.front.model.bean.Zt78University;

/**
 * <p>
 * 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-08-13
 */
public interface IZt78UniversityService extends IService<Zt78University> {

    Zt78University findByUnitCode(String unitCode);

    OutMessage<?> saveZt78Data(Zt78UniversityDto data);

}
