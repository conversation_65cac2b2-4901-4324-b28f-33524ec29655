package com.zenith.front.api.org;

import com.zenith.front.model.dto.OrgDemocraticAppraisalDTO;
import com.zenith.front.model.message.OutMessage;
import com.zenith.front.model.bean.OrgAppraisal;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;

/**
 * <p>
 * 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-08-16
 */
public interface IOrgAppraisalService extends IService<OrgAppraisal> {

    /**
     * 党组织信息项中增加民主评议
     *
     * @param dto
     * @return
     */
    OutMessage save(OrgDemocraticAppraisalDTO dto);

    /**
     * 党组织信息项中删除民主评议
     *
     * @param code
     * @return
     */
    OutMessage remove(String code);

    /**
     * 党组织信息项中修改民主评议
     *
     * @param dto
     * @return
     */
    OutMessage update(OrgDemocraticAppraisalDTO dto);

    /**
     * 民主评议列表信息
     *
     * @param pageNum
     * @param pageSize
     * @param orgCode
     * @return
     */
    OutMessage list(Integer pageNum, Integer pageSize, String orgCode);

    /**
     * 查询机构code下的民主评议
     *
     * @param orgCode 机构code
     * @return java.util.List<com.zenith.front.model.OrgAppraisal>
     * <AUTHOR>
     * @creed Talk is cheap,show me the code
     * @date 2021年08月17日 21时56分
     */
    List<OrgAppraisal> findByCode(String orgCode);

    /**
     * 根据组织code查询民主评议
     *
     * @param orgCode 组织code
     * @return
     */
    List<OrgAppraisal> findOrgAppraisalByOrgCode(String orgCode);
}
