package com.zenith.front.api.transfer;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.extension.service.IService;
import com.zenith.front.model.dto.TransferRecordDTO;
import com.zenith.front.model.message.OutMessage;
import com.zenith.front.model.bean.TransferRecord;

import java.util.Set;

/**
 * <AUTHOR>
 */
public interface IOrgTransferRecordService extends IService<TransferRecord> {

    /***
     * 添加转接记录
     * */
    OutMessage<String> add(TransferRecordDTO data)  throws Exception;
    /**
     * 整建制处理转出组织的所有数据
     * **/
    void deailEntireData(TransferRecord transferRecord);

    /***
     * 整建制数据处理
     * */
    void  deailOrgTransferData(String srcOrgCode,JSONObject jsonObject);

    /**
     * 转接转出方档案相关信息处理
     * @param digLotNoSet
     */
    void deleteDigLotNo(Set<String> digLotNoSet);
}
