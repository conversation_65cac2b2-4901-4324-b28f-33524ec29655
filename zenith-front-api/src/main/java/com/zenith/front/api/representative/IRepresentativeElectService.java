package com.zenith.front.api.representative;

import com.baomidou.mybatisplus.extension.service.IService;
import com.zenith.front.model.dto.RepresentativeElectDTO;
import com.zenith.front.model.dto.RepresentativeListDTO;
import com.zenith.front.model.message.OutMessage;
import com.zenith.front.model.bean.RepresentativeElect;

import java.util.List;

/**
 * <p>
 * 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-07-10
 */
public interface IRepresentativeElectService extends IService<RepresentativeElect> {

    RepresentativeElect findByCode(String electCode);

    Long getTotalByOrgCode(String orgCode);

    /**
     * 获取自身及直属上级的界次
     *
     * @param list
     * @return
     */
    List<RepresentativeElect> findByOrgCode(List<String> list);

    /**
     * 获取届次信息列表
     *
     * @param messageData
     * @return
     */
    OutMessage getList(RepresentativeListDTO messageData);

    /**
     * 保存届次信息
     *
     * @param messageData
     * @return
     */
    OutMessage addElect(RepresentativeElectDTO messageData);

    /**
     * 通过单位查找届次信息
     *
     * @param unitCode
     * @return
     */
    OutMessage findByUnitCode(String unitCode);

    /**
     * 校验时间是否重叠
     *
     * @param messageData
     * @return
     */
    OutMessage checkElectDate(RepresentativeElectDTO messageData);

    /**
     * 获取单位届次
     *
     * @param code
     * @return
     */
    OutMessage findByCodeM(String code);

    /**
     * 修改单位届次信息
     *
     * @param messageData
     * @return
     */
    OutMessage updateElect(RepresentativeElectDTO messageData) throws Exception;

    /**
     * 删除单位届次信息
     *
     * @param code
     * @return
     */
    OutMessage delElect(String code);


}
