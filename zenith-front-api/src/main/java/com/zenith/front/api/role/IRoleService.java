package com.zenith.front.api.role;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.zenith.front.model.dto.RoleDTO;
import com.zenith.front.model.dto.UpdatePermissionDto;
import com.zenith.front.model.dto.UserDTO;
import com.zenith.front.model.message.OutMessage;
import com.zenith.front.model.bean.Role;
import com.zenith.front.model.vo.RoleVo;

import java.util.List;

/**
 * <p>
 * 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-07-10
 */
public interface IRoleService extends IService<Role> {


    Role findByIdAndValid(String roleID);

    /**
     * 查询默认角色
     *
     * @return com.zenith.front.model.Role
     * <AUTHOR>
     * @creed Talk is cheap,show me the code
     * @date 2022年08月16日
     */
    Role findDefaultRole();

    Role findById(String roleId);

    /**
     * 查询是否是统一节点下的是角色 ,排除过期角色 和被删除的角色
     *
     * @param roleId     子角色id
     * @param currRoleId 父角色id
     * @return
     */
    Role findSubRoleByIdAndParentId(String roleId, String currRoleId);

    /**
     * 查询父节点下的子角色包括过期角色
     *
     * @param roleId     角色id
     * @param currRoleId 父角色id
     * @return
     */
    Role findSubRoleByIdAndParentIdIncludeInvalidRole(String roleId, String currRoleId);

    /***
     * 更新角色orgCode
     * */
    boolean updateRoleOrgCode(String newOrgCode);


    Role findByIdMe(String id);

    /**
     * 查看是否还有下级,排除过期的角色 和 被删除的角色
     */
    List<Role> findListByParentId(String roleId);

    /**
     * 获取树过滤掉过期的角色
     */
    List<Role> getRoleTree(String roleId);

    Page<Role> getList(int pageNum, int pageSize, String roleID, String orgCode, String keyword);

    /**
     * 查询有效的角色列表
     */
    Page<Role> getListAndValid(int pageNum, int pageSize, String roleID);

    /**
     * 添加角色
     *
     * @param data
     * @return
     */
    OutMessage addRole(RoleDTO data);

    /**
     * 切换角色
     */
    OutMessage cutRole(String userId, String roleId);

    /**
     * 删除角色
     */
    OutMessage delRole(String roleId, String currRoleId);

    /**
     * 修改角色
     */
    OutMessage updateRole(RoleDTO data, String currRoleId);

    /**
     * 修改角色权限
     */
    OutMessage updateRolePermission(UpdatePermissionDto data);

    /**
     * 获取列表
     */
    OutMessage getListM(int pageNum, int pageSize, String currRoleId, String orgCode, String keyword);

    /**
     * 获取有效角色列表
     */
    OutMessage<Page<RoleVo>> getListAndValidM(int pageNum, int pageSize, String currRoleId);

    /**
     * 获取指定ID的角色
     */
    OutMessage getByIdM(String roleId);

    /**
     * 获取角色树
     */
    OutMessage getRoleTreeM(String currRoleId);


    /**
     * 获取所有使用该角色的用户名称
     */
    OutMessage<Page<UserDTO>> getUserRole(int pageNum, int pageSize, String roleId, String currManOrgCode);

}
