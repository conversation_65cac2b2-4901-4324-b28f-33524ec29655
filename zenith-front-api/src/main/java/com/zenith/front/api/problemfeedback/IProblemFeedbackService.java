package com.zenith.front.api.problemfeedback;

import com.zenith.front.model.dto.ProblemFeedbackDTO;
import com.zenith.front.model.dto.ProblemFeedbackListDTO;
import com.zenith.front.model.message.OutMessage;
import com.zenith.front.model.bean.ProblemFeedback;
import com.baomidou.mybatisplus.extension.service.IService;
import com.zenith.front.model.bean.User;

/**
 * <p>
 * 问题反馈 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-09-23
 */
public interface IProblemFeedbackService extends IService<ProblemFeedback> {

    OutMessage addOrUpdate(ProblemFeedbackDTO data, User user);

    OutMessage getList(ProblemFeedbackListDTO data);

    OutMessage details(String code);

    OutMessage delete(String code);

}
