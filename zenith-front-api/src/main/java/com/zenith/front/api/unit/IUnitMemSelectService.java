package com.zenith.front.api.unit;

import com.baomidou.mybatisplus.extension.service.IService;
import com.zenith.front.model.bean.UnitMemSelect;
import com.zenith.front.model.dto.UnitMemSelectBaseDTO;
import com.zenith.front.model.dto.UnitMemSelectDTO;
import com.zenith.front.model.dto.UnitMemSelectLeaveDTO;
import com.zenith.front.model.dto.UnitMemSelectListDTO;
import com.zenith.front.model.message.OutMessage;

/**
 * <p>
 * 选调生信息 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-07
 */
public interface IUnitMemSelectService extends IService<UnitMemSelect> {

    /**
     * 列表
     * @param dto
     * @return
     */
    OutMessage<?> list(UnitMemSelectListDTO dto);

    /**
     * 新增
     * @param dto
     * @return
     */
    OutMessage<?> add(UnitMemSelectDTO dto);

    /**
     * 编辑
     * @param dto
     * @param isLeave 是否离开， true-是
     * @return
     */
    OutMessage<?> update(UnitMemSelectBaseDTO dto, Boolean isLeave) throws Exception;

    /**
     * 离开
     * @param dto
     * @return
     */
    OutMessage<?> leave(UnitMemSelectLeaveDTO dto) throws Exception;

    /**
     * 删除
     * @param code
     * @return
     */
    OutMessage<?> delete(String code);
}
