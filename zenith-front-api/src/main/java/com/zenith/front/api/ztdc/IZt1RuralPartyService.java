package com.zenith.front.api.ztdc;

import com.zenith.front.model.dto.Zt1RuralPartyDTO;
import com.zenith.front.model.message.OutMessage;
import com.zenith.front.model.bean.Zt1RuralParty;
import com.baomidou.mybatisplus.extension.service.IService;

/**
 * <p>
 * 农村党建有关情况 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-08-13
 */
public interface IZt1RuralPartyService extends IService<Zt1RuralParty> {

    OutMessage addOne(Zt1RuralPartyDTO data);

    OutMessage findDataByCode(String unitCode, String type);
}
