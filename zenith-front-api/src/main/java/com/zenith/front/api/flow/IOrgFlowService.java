package com.zenith.front.api.flow;

import com.baomidou.mybatisplus.extension.service.IService;
import com.zenith.front.model.bean.Org;
import com.zenith.front.model.bean.OrgFlow;
import com.zenith.front.model.bean.OrgFlowAudit;
import com.zenith.front.model.dto.OrgListDTO;
import com.zenith.front.model.dto.OrgTreeDTO;
import com.zenith.front.model.dto.flow.OrgFlowDto;
import com.zenith.front.model.dto.flow.OrgFlowListDto;
import com.zenith.front.model.message.OutMessage;

import java.util.List;

public interface IOrgFlowService extends IService<OrgFlow> {

    //添加流动党组织
    OutMessage addOrgFlow(OrgFlowDto dto);

    void pushFlowExchange(OrgFlow orgFlow, String type, OrgFlowAudit audit);

    //修改
    OutMessage update(OrgFlowDto dto);

    //删除
    OutMessage delete(String code);

    //分页查询
    OutMessage list(OrgFlowListDto dto);

    //获取批准成立的党组织
    OutMessage findApproveOrg(Integer pageNum,Integer pageSize);

    List<Org> getOrgFlowTree(OrgTreeDTO orgTreeDTO);
    OutMessage getFLowSelectorList(OrgListDTO orgListDTO);

    OutMessage findById(Long id);

    OutMessage cancel(String code, String cancel);

    OutMessage detail(String code);

    void updateIsEnableById(Long id, Integer isEnable);

    void updateIsEnableByCode(String code, Integer isEnable);

    /**
     * 查询本级和直属下级组织（流动党组织和普通党组织）
     *
     * @param orgCode 层级码
     * @return
     */
    List<Org> selectUnderlingByOrgCode(String orgCode);
}
