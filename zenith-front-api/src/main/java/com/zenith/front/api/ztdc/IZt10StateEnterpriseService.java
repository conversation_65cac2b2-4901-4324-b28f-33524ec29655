package com.zenith.front.api.ztdc;

import com.baomidou.mybatisplus.extension.service.IService;
import com.zenith.front.model.dto.Zt10StateEnterpriseDto;
import com.zenith.front.model.message.OutMessage;
import com.zenith.front.model.bean.Zt10StateEnterprise;

/**
 * <p>
 * 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-08-13
 */
public interface IZt10StateEnterpriseService extends IService<Zt10StateEnterprise> {

    Zt10StateEnterprise findByUnitCode(String unitCode);

    OutMessage<?> saveZt10Data(Zt10StateEnterpriseDto data);

}
