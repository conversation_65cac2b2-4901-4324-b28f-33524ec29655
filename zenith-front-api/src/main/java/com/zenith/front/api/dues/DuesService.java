package com.zenith.front.api.dues;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.zenith.front.model.dto.DuesDTO;
import com.zenith.front.model.dto.DuesData;
import com.zenith.front.model.dto.DuesPageDto;
import com.zenith.front.model.dto.ImportExcelDevelopDTO;
import com.zenith.front.model.message.OutMessage;
import com.zenith.front.model.message.Status;
import com.zenith.front.model.bean.Dues;

import java.io.FileNotFoundException;
import java.io.IOException;
import java.text.ParseException;
import java.util.Date;
import java.util.List;
import java.util.Map;


/**
 * @author: D.watermelon
 * @date: 2023/1/3 10:19
 * @description: 入则恳恳以尽忠 出则谦谦以自悔
 */
public interface DuesService extends IService<Dues> {

    /**
     * 设置/修改党员党费缴纳起始时间
     * @param memCode
     * @param lastPayDate
     * @return Status
     */
    Status updateLastPayDate(String memCode, Date lastPayDate);

    /***
     * 设置单个党费标准
     * @param data
     * @return Status
     */
    Status settingStandard(DuesDTO data);

    /***
     *  获取党费缴纳列表
     * @param duesPageDto
     * @return
     */
    Page<DuesData> list(DuesPageDto duesPageDto);

    /**
     * 党费单个缴纳接口
     * @param data
     * @return
     */
    Status payDues(DuesDTO data);

    List<Map<String, Object>> filterMem(List<String> memCodeList);

    List<DuesData> findBatchFee(List<String> postMemData);

    OutMessage importExcelDudeStand(ImportExcelDevelopDTO data,String account) throws Exception;

    OutMessage importExcelDudes(ImportExcelDevelopDTO data,String account) throws FileNotFoundException;

    OutMessage templateDuesExport(String orgCode) throws Exception;

    OutMessage templateStandExport(String orgCode) throws Exception;
}
