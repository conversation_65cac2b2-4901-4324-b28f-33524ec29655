package com.zenith.front.api.trend;

import com.baomidou.mybatisplus.extension.service.IService;
import com.zenith.front.model.dto.TrendCheckDTO;
import com.zenith.front.model.dto.TrendCheckFilterDto;
import com.zenith.front.model.dto.TrendDTO;
import com.zenith.front.model.dto.TrendFilterDTO;
import com.zenith.front.model.message.OutMessage;
import com.zenith.front.model.bean.Trend;

/**
 * <p>
 * 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-07-10
 */
public interface ITrendService extends IService<Trend> {

    /**
     * 新增工作动态
     */
    OutMessage addTrend(TrendDTO data, String account);

    /**
     * 查看单个动态
     */
    OutMessage findTrendByCode(String code);

    /**
     * 撤销工作动态
     */
    OutMessage cancelTrend(TrendDTO data, String currManOrgCode);

    /**
     * 推送工作动态
     **/
    OutMessage pushTrend(String trendCode, String currManOrgCode);

    /**
     * 分页获取动态列表
     */
    OutMessage listTrend(TrendFilterDTO data, String currManOrgCode);

    /**
     * 编辑工作动态
     */
    OutMessage updateTrend(TrendDTO data, String currManOrgCode);

    /**
     * 删除工作动态
     */
    OutMessage delTrend(String code, String currManOrgCode);

    /**
     * 重新发布动态
     */
    OutMessage afreshTrend(String code, String currManOrgCode);

    /**
     * 推送工作动态到门户
     */
    OutMessage portalTrend(String code, String currManOrgCode);

    /**
     * 审核通过门户推荐
     */
    OutMessage checkTrend(TrendCheckDTO data, String currManOrgCode);

    /**
     * 获取我的审核列表（我的待审核，该我审核的，以及我的审核状态）
     */
    OutMessage checkTrendList(TrendCheckFilterDto data);
}
