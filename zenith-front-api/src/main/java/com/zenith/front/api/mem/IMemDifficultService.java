package com.zenith.front.api.mem;

import com.baomidou.mybatisplus.extension.service.IService;
import com.zenith.front.model.dto.MemDifficultDTO;
import com.zenith.front.model.message.OutMessage;
import com.zenith.front.model.bean.MemDifficult;

/**
 * <p>
 * 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-07-10
 */
public interface IMemDifficultService extends IService<MemDifficult> {

    MemDifficult findByMemCode(String memCode);

    MemDifficult findByCode(String code);

    /**
     * 新增困难党员
     *
     * @param memDifficultDTO
     * @return
     */
    OutMessage addMemDifficult(MemDifficultDTO memDifficultDTO) throws Exception;

    /**
     * 根据人员查询困难党员信息
     *
     * @param memCode
     * @return
     */
    OutMessage findByMemCodeOut(String memCode);

    /**
     * 修改困难党员信息
     *
     * @param memDifficultDTO
     * @return
     */
    OutMessage updateMemDifficult(MemDifficultDTO memDifficultDTO) throws Exception;

    /**
     * 获取困难党员列表
     *
     * @param pageNum
     * @param pageSize
     * @param orgCode
     * @param subordinate 是否显示下级
     * @param memName
     * @return
     */
    OutMessage getList(int pageNum, int pageSize, String orgCode, String subordinate, String memName);

    /**
     * 删除困难党员
     *
     * @param code
     * @return
     */
    OutMessage delMemDifficult(String code);

}
