package com.zenith.front.api.mem;

/**
 * 计算年龄任务
 *
 * <AUTHOR>
 * @version V1.0
 * @Package com.zenith.front.api.mem
 * @date 2022/5/16 15:28
 */
public interface ICalculateAgeTask {

    /**
     * 计算党员年龄
     *
     * <AUTHOR>
     * @creed Talk is cheap,show me the code
     * @date 2022年05月16日
     */
    void calculateMemAge();

    /**
     * 计算发展党员年龄
     *
     * <AUTHOR>
     * @creed Talk is cheap,show me the code
     * @date 2022年05月16日
     */
    void calculateDevelopMemAge();

    /**
     * 计算发展党员年龄
     *
     * <AUTHOR>
     * @creed Talk is cheap,show me the code
     * @date 2022年05月16日
     */
    void calculateDevelopStepLogAge();
}
