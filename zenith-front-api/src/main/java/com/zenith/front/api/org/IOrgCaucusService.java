package com.zenith.front.api.org;

import com.zenith.front.model.dto.OrgCaucusDTO;
import com.zenith.front.model.dto.OrgCaucusListDTO;
import com.zenith.front.model.message.OutMessage;
import com.zenith.front.model.bean.OrgCaucus;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;

/**
 * <p>
 * 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-09-16
 */
public interface IOrgCaucusService extends IService<OrgCaucus> {

    OutMessage addOrUpdate(OrgCaucusDTO data);

    OutMessage details(String code);

    OutMessage delete(String code);

    OutMessage getList(OrgCaucusListDTO data);

    /**
     * 根据组织code查询地方委员会情况
     *
     * @param orgCode 组织code
     * @return
     */
    List<OrgCaucus> findOrgCaucusByOrgCode(String orgCode);
}
