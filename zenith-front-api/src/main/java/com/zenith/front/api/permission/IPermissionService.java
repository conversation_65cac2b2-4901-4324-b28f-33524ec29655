package com.zenith.front.api.permission;

import com.baomidou.mybatisplus.extension.service.IService;
import com.zenith.front.model.dto.SelectPermissionDto;
import com.zenith.front.model.message.OutMessage;
import com.zenith.front.model.bean.Permission;

import java.util.List;

/**
 * <p>
 * 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-07-10
 */
public interface IPermissionService extends IService<Permission> {

    List<Permission> findAll();

    Long findCount();

    /**
     * 根据用户的角色Id查询他的能操作的权限列表
     */
    OutMessage findPermissionByRoleId(SelectPermissionDto data, String roleId);

    OutMessage<List<Permission>> findPermissionByUserId(String userId);

    /**
     * 通过权限id查询用户菜单
     */
    OutMessage<List<Permission>> findPermissionByPermissionId(String permissionId);

    /**
     * 根据用户登录权限查询用户的所有权限列表
     */
    OutMessage<List<Permission>> findPermissionList(String permission);


}
