package com.zenith.front.api.org;

import com.baomidou.mybatisplus.extension.service.IService;
import com.zenith.front.model.dto.LedgerDTO;
import com.zenith.front.model.dto.OrgGroupDTO;
import com.zenith.front.model.message.OutMessage;
import com.zenith.front.model.bean.OrgGroup;

import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * <p>
 * 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-07-10
 */
public interface IOrgGroupService extends IService<OrgGroup> {

    OrgGroup findByCode(String code);

    /**
     * 获取党小组
     */
    OutMessage getList(int pageNum, int pageSize, String orgCode);

    /**
     * 新增党小组
     */
    OutMessage addGroup(OrgGroupDTO orgGroupDTO);

    /**
     * 根据code查找党小组
     */
    OutMessage findByCodeM(String code);

    /**
     * 修改党小组
     */
    OutMessage updateGroup(OrgGroupDTO orgGroupDTO);

    /**
     * 删除党小组
     */
    OutMessage delGroup(String code);


    /**
     * 导出两委委员数据
     *
     * @param data
     */
    void exportTwoCommittee(LedgerDTO data);

    /**
     * 导出基层党组织换届情况统计表
     *
     * @param data
     */
    void exportChangeElection(LedgerDTO data);

    /**
     * 根据组织code查询党小组信息
     *
     * @param orgCode 组织code
     * @return
     */
    List<OrgGroup> findOrgGroupByOrgCode(String orgCode);

    Map<String, List<OrgGroup>> getListLikeOrgCode(String orgCode);

}
