package com.zenith.front.api.mem;

import com.baomidou.mybatisplus.extension.service.IService;
import com.zenith.front.model.dto.MemAbroadDTO;
import com.zenith.front.model.message.OutMessage;
import com.zenith.front.model.bean.MemAbroad;

/**
 * <p>
 * 出入国服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-07-10
 */
public interface IMemAbroadService extends IService<MemAbroad> {

    /**
     * 根据code查询信息
     *
     * @param code
     * @return
     */
    MemAbroad findByCode(String code);

    /**
     * 获取人员出入国信息列表
     *
     * @param memCode
     */
    OutMessage getList(int pageNum, int pageSize, String memCode);

    /**
     * 添加人员出国信息
     */
    OutMessage addMemAbroad(MemAbroadDTO memAbroadDTO);

    /**
     * 获取党员出入国信息
     *
     * @param code 人员标识
     * @return 查询结果
     */
    OutMessage findByCodeOut(String code);

    /**
     * 修改人员出国信息
     *
     * @param memAbroadDTO
     * @return
     */
    OutMessage updateMemAbroad(MemAbroadDTO memAbroadDTO) throws Exception;

    /**
     * 删除人员出国信息
     *
     * @param code
     * @return
     */
    OutMessage delMemAbroad(String code);

    /**
     * 添加回国信息
     *
     * @param memAbroadDTO
     * @return
     */
    OutMessage backMemAbroad(MemAbroadDTO memAbroadDTO);

    /**
     * 停止党籍
     *
     * @param memAbroadDTO
     * @return
     */
    OutMessage stop(MemAbroadDTO memAbroadDTO);

}
