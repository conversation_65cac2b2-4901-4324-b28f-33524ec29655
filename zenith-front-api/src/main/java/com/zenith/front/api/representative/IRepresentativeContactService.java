package com.zenith.front.api.representative;

import com.baomidou.mybatisplus.extension.service.IService;
import com.zenith.front.model.dto.RepresentativeContactDTO;
import com.zenith.front.model.dto.RepresentativeListDTO;
import com.zenith.front.model.message.OutMessage;
import com.zenith.front.model.bean.RepresentativeContact;

/**
 * <p>
 * 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-07-10
 */
public interface IRepresentativeContactService extends IService<RepresentativeContact> {

    Long getTotalByOrgCode(String orgCode);

    RepresentativeContact findByCode(String code);

    /**
     * 获取联络机构列表
     *
     * @param messageData
     * @return
     */
    OutMessage getList(RepresentativeListDTO messageData);

    /**
     * 保存联络机构
     *
     * @param messageData
     * @return
     */
    OutMessage addContact(RepresentativeContactDTO messageData) throws Exception;

    /**
     * 通过code查找
     *
     * @param code
     * @return
     */
    OutMessage findByCodeM(String code);

    /**
     * 修改联络机构
     *
     * @param messageData
     * @return
     */
    OutMessage updateContact(RepresentativeContactDTO messageData) throws Exception;

    /**
     * 删除联络机构
     *
     * @param code
     * @return
     */
    OutMessage delContact(String code);


}
