package com.zenith.front.api.mem;

import com.baomidou.mybatisplus.extension.service.IService;
import com.zenith.front.model.bean.MemFlowSyncLog;

import java.util.List;

/**
 * <p>
 * 流动党员服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-11-15
 */
public interface IMemFlowSyncLogService extends IService<MemFlowSyncLog> {

    /**
     * 查找当前库下的流程登记异常数据
     *
     * @return 异常数据集合
     */
    List<MemFlowSyncLog> selectAbnormalData();
}
