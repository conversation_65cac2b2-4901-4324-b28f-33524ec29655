package com.zenith.front.api.mem;

import com.baomidou.mybatisplus.extension.service.IService;
import com.zenith.front.model.bean.MemFlowSignAudit;
import com.zenith.front.model.dto.MemFlowSignAuditDto;
import com.zenith.front.model.dto.MemFlowSignRegisterDTO;
import com.zenith.front.model.message.OutMessage;

/**
 * <p>
 * 流动党员服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-07-10
 */
public interface IMemFlowSignAuditService extends IService<MemFlowSignAudit> {

    OutMessage<?> getMemFlowAuditList(MemFlowSignAuditDto dto);

    OutMessage<?> findMemFlowAuditById(String id);

    OutMessage<?> approveMemFlowAudit(MemFlowSignAuditDto data);

    OutMessage<?> approveMemFlowAudit1(MemFlowSignRegisterDTO data,String orgCode,String account);

}
