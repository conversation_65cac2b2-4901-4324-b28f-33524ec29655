package com.zenith.front.api.unit;

import com.baomidou.mybatisplus.extension.service.IService;
import com.zenith.front.model.dto.UnitTownshipCadreInfoDto;
import com.zenith.front.model.dto.UnitVillageIncomeListDTO;
import com.zenith.front.model.message.OutMessage;
import com.zenith.front.model.bean.UnitTownshipCadreInfo;

/**
 * <p>
 * 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-04-11
 */
public interface IUnitTownshipCadreInfoService extends IService<UnitTownshipCadreInfo> {

    OutMessage<?> getList(UnitVillageIncomeListDTO data);

    OutMessage<?> addOrUpdate(UnitTownshipCadreInfoDto data);

    OutMessage<?> findByCode(String unitCode);

    OutMessage<?> del(String code);
}
