package com.zenith.front.api.ztdc;

import com.zenith.front.model.dto.Zt2TeamOptimizeDTO;
import com.zenith.front.model.message.OutMessage;
import com.zenith.front.model.bean.Zt2TeamOptimize;
import com.baomidou.mybatisplus.extension.service.IService;

/**
 * <p>
 * 村党组织带头人队伍整体优化提升情况 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-08-13
 */
public interface IZt2TeamOptimizeService extends IService<Zt2TeamOptimize> {

    OutMessage addTwo(Zt2TeamOptimizeDTO data);
}
