package com.zenith.front.api.report;

import com.baomidou.mybatisplus.extension.service.IService;
import com.zenith.front.model.bean.StatisticsYear;
import com.zenith.front.model.vo.BetweenReportDateVo;

import java.util.Date;
import java.util.List;

/**
 * <p>
 * 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-02-06
 */
public interface IStatisticsYearService extends IService<StatisticsYear> {

    /***
     * 获取统计年度
     */
    String getStatisticalYear();

    /**
     * 是否统计年度
     */
    boolean isStatisticalYear(Date startDate, Date endDate);

    /**
     * 获取年度统计周期
     */
    List<Date> annualStatisticsPeriod();

    /**
     * 根据主键获取配置数据
     */
    StatisticsYear getStatisticsYear(String id);

    /**
     * 获取报告期内查询条件
     */
    String getReportDateSql(String field);

    /**
     * 获取报告期内查询条件
     */
    String getEsReportDateSql(String field);

    /**
     * 当前时间是否在报告期内
     *
     * @param date 待判断时间
     * @param type 1统计报告期 2出党等 3发展党员 4成为预备党员 5 数据统计模块
     * @return 是否在报告期内
     */
    BetweenReportDateVo isBeforeReportDate(Date date, String type);

    boolean isBetweenReportDate(Date date);

    /**
     * 更新服务器文件
     *
     * @param dtoPath 前端参数
     * @param dbPath  文件绝对路径
     */
    void removeMinioFile(String dtoPath, String dbPath);

}
