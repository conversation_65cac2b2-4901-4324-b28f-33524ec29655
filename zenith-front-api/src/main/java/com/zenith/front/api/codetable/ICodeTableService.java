package com.zenith.front.api.codetable;

import com.baomidou.mybatisplus.extension.service.IService;
import com.zenith.front.model.bean.CodeTable;
import com.zenith.front.model.message.OutMessage;

/**
 * <p>
 * 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-07-10
 */
public interface ICodeTableService extends IService<CodeTable> {

    OutMessage findTableConfig();

    OutMessage tableFind(String id);


    /**
     * 获取数据字典表分类（数据查询模块用）
     */
    OutMessage<?> findTableConfigNew();

    /**
     * 获取所有配置得字段信息项（数据查询模块用）
     */
    OutMessage<?> tableAllNew();
}
