package com.zenith.front.api.org;

import com.baomidou.mybatisplus.extension.service.IService;
import com.zenith.front.model.dto.OrgSlackRetifictionSaveDTO;
import com.zenith.front.model.message.OutMessage;
import com.zenith.front.model.bean.OrgSlackRectification;

/**
 * @date 20210827
 * <AUTHOR>
 */
public interface IOrgSlackRectificationService  extends IService<OrgSlackRectification> {

    /**
     * 获取涣散组织整备列表
     */
    OutMessage<?> getList(String slackCode);

    /**
     * 保存涣散组织整备列表
     */
    OutMessage<?> saveSlackRectificationList(OrgSlackRetifictionSaveDTO slackRectificationDTOList) throws Exception;

}
