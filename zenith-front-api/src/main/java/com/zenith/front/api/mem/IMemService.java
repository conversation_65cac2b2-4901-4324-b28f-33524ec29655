package com.zenith.front.api.mem;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.zenith.front.model.bean.Org;
import com.zenith.front.model.custom.Record;

import com.zenith.front.model.dto.*;
import com.zenith.front.model.message.OutMessage;
import com.zenith.front.model.bean.Mem;
import com.zenith.front.model.vo.ProbationaryPartyMemberBecomesRegularMessageVO;

import java.util.Collection;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * <p>
 * 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-07-10
 */
public interface IMemService extends IService<Mem> {
    /***
     * 根据姓名和身份证查询党员
     */
    Mem findMemByIdCardAndName(String memName,String idCard);

    /**
     * @param code
     * @return 查询未删除得人员（只包含未删除得人员）
     */
    Mem findByCode(String code, String... selectFieldName);

    Mem findByCodeNoEncrypt(String code);

    /**
     * 根据单位寻找当前统计单位下面得所有得党员
     * @param statisticalUnit
     * @return
     */
    List<Mem>findByStatistical(String statisticalUnit);

    Mem findByCodeToCount(String code);

    /**
     * @param code
     * @return 查询已经删除掉得人员（只包含删除得人员）
     */
    Mem findDelByCode(String code);

    /***
     *
     * @param code
     * @return 查询所有人员（删除和未删除都包含）
     */
    Mem findAllByCode(String code);
    Mem findAllByCodeNoEncrypt(String code);

    /**
     * 获取总人数
     */
    Long getMemTotal(LambdaQueryWrapper<Mem> condition);

    /**
     * 获取人员列表
     *
     * @param memListDTO
     * @return
     */
    OutMessage getList(MemListDTO memListDTO);

    /**
     * 新增党员
     *
     * @param memDTO
     * @return
     */
    OutMessage addMem(MemDTO memDTO);

    /**
     * 根据code查找
     * @param code
     * @param isHistory 是否历史党员
     * @param isCheck 是否校核
     * @return
     */
    OutMessage selectByCode(String code, boolean isHistory, boolean isCheck);

    /**
     * 修改党员
     *
     * @param memDTO
     * @return
     */
    OutMessage updateMem(MemDTO memDTO) throws Exception;

    /**
     * 离开党组织
     *
     * @param memDTO
     * @return
     */
    OutMessage leaveOrg(MemDTO memDTO) throws Exception;

    /**
     * 获取党员列表的组织层级
     *
     * @param memOrgCode
     * @return
     */
    OutMessage getMemOrgList(String memOrgCode);

    /**
     * 根据身份证和姓名查找党员
     *
     * @param name
     * @param idcard
     * @param orgCode
     * @return
     */
    OutMessage findByNameAndIdcard(String name, String idcard, String orgCode);

    /**
     * 预备党员转正
     *
     * @param developStepLogDTO
     * @return
     */
    OutMessage becomeFullMem(DevelopStepLogDTO developStepLogDTO) throws Exception;

    /**
     * 延长预备期
     *
     * @param developStepLogDTO
     * @return
     */
    OutMessage prolongationMem(DevelopStepLogDTO developStepLogDTO) throws Exception;

    /**
     * 撤销延长预备期
     *
     * @param memDTO
     * @return
     */
    OutMessage repealProlongationMem(MemDTO memDTO);

    /**
     * 修改党员最后缴费时间
     * // TODO: 2019/5/17 如果字段存在ccp_mem_all中,需要同步修改ccp_mem_all
     *
     * @param code
     * @param lastPayDate
     * @return
     */
    OutMessage updateLastPayDate(String code, Date lastPayDate);

    /**
     * 根据党小组集合获取党小组下面的人员
     */
    OutMessage findeMemByGroup(MemGroupDto data) throws Exception;

    /***
     * 更新人员的orgCode
     * */
    int updateMemOrgCode(String newOrgCode);


    boolean rewriteUpdateById(Mem updateMem);

    /**
     * 获取党员信息台账
     *
     * @param data
     * @return
     */
    OutMessage getMemInfo(LedgerDTO data);

    /**
     * 导出人员数据
     *
     * @param memListDTO
     */
    void exportMemData(MemListDTO memListDTO);

    /**
     * 获取党员的性别,民族,学历
     */
    Record getMemSexAndEudAndNation(String orgCode);

    /**
     * 获取党员年龄分布
     */
    Record getMemAgeDistribute(String orgCode);

    /**
     * 获取党员职业分类
     */
    Record getMemJobSort(String orgCode);

    /**
     * 多个code获取MemList
     *
     * @param code
     * @return
     */
    List<Mem> findMemByCodes(Collection<String> code);

    /**
     * 多个code获取MemList
     *
     * @param code
     * @return
     */
    List<Mem> findFlowMemByCodes(Collection<String> code);

    Map getListCondition(MemListDTO memListDTO);

    OutMessage getSchedule();

    /**
     * 党员退回到预备党员
     *
     * @param code
     * @return
     */
    OutMessage backToProbationary(String code);

    /**
     * 预备党员退回到发展党员
     *
     * @param code
     * @return
     */
    OutMessage backToDevelopMem(String code);

    /**
     * 失去联系类型
     *
     * @return
     */
    OutMessage lostContact(MemDTO memDTO);

    /**
     * 党务工作者列表
     *
     * @param pageNum  当前页
     * @param pageSize 每页显示条数
     * @param orgCode  机构码
     * @return
     */
    OutMessage<Object> partyWorkList(Integer pageNum, Integer pageSize, String orgCode);

    /**
     * 增加党务工作者
     *
     * @param dto     实体类
     * @param account 操作员
     * @return
     */
    OutMessage<Object> savePartyWork(SavePartyWorkDTO dto, String account);

    /**
     * 删除党务工作者标签
     *
     * @param data    实体类
     * @param account 操作员
     * @return
     */
    OutMessage<Object> delPartyWork(DelPartyWorkDTO data, String account);

    /**
     * 获取党员时间轴
     */
    OutMessage<?> findMemResume(String code);

    /**
     * 获取党员关联业务
     */
    OutMessage<?> findMemCorrelation(String code);

    Mem findByNameAndIdcard(String memName, String idcard);

    /**
     * 根据党姓名，出生年月，党支部层级查询党员
     * */
    Mem findByNameBirthdayAndOrgCode(String memName,String birthday,String orgCode);

    /**
     * 入党时间修正
     */
    OutMessage<?> joinPartyRevise(MemDTO data);

    /**
     * 根据条件进行分页查询党员
     *
     * @param pageNum        当前页
     * @param pageSize       每页显示条数
     * @param hasLocked      是否被锁定 1 true 0 false
     * @param hasSubordinate 是否显示下级 1 显示 0 不显示
     * @param memCode        党员层级码
     * @param keyword        搜索关键词
     * @return
     */
    Page<Mem> findPageByCondition(Integer pageNum, Integer pageSize, Boolean hasLocked, Boolean hasSubordinate, String memCode, String keyword);

    /**
     * 查询未锁定党员
     *
     * @param memOrgCode     组织层级码
     * @param hasSubordinate 是否包含下级
     * @return
     */
    List<Mem> findLockFieldsIsNull(String memOrgCode, Boolean hasSubordinate);

    /**
     * 更新锁定字段
     *
     * @param filedList 锁定字段
     * @return
     */
    boolean updateLockFields(List<String> filedList);

    /**
     * 根据组织code查询当前组织下有多少党员
     *
     * @param orgCode 组织code
     * @return
     */
    int selectCountByOrgCode(String orgCode);

    /**
     * 根据党组织层级码查询党组织下所有得党员
     * 注意：目前仅查询了党员姓名、生日、所在组织code 如需其他信息，请增加查询列
     * @param memOrgCode
     * @return list
     * */
    List<Mem> findByMemOrgCode(String memOrgCode);

    /**
     * 根据党员的党组织code(党组织唯一标识符)查询所有党员
     * 注意：目前仅查询了党员code、id，name 如需其他信息，请增加查询列
     * @param orgCodeList
     * @return list
     */
    List<Mem> findByMemOrgCode(List<String> orgCodeList,String unitCode);


    /**
     * 根据组织层级码查询层级码下面的党员
     *
     * @param memOrgCode     组织层级码
     * @param hasSubordinate 是否包含下级
     * @return
     */
    List<Mem> findMemByMemOrgCode(String memOrgCode, Boolean hasSubordinate);

    /**
     * 查询预备党员
     * 预备期到的时间(或延长到的时间) 在未来一个星期之内
     *
     * <AUTHOR>
     * @creed Talk is cheap,show me the code
     * @date 2022年07月28日 14时48分
     */
    List<ProbationaryPartyMemberBecomesRegularMessageVO> findProbationaryPartyMember();

    /**
     * 根据单位类别集合查询人员列表
     *
     * @param d04CodeList 单位类别集合
     * @return java.util.List<com.zenith.front.model.Mem>
     * <AUTHOR>
     * @creed Talk is cheap,show me the code
     * @date 2022年08月25日 10时14分
     */
    List<Mem> findMemByD04CodeList(List<String> d04CodeList);

    /**
     * 根据工作岗位集合查询人员列表
     *
     * @param d09Code 工作岗位集合
     * @return java.util.List<com.zenith.front.model.Mem>
     * <AUTHOR>
     * @creed Talk is cheap,show me the code
     * @date 2022年08月25日 10时14分
     */
    List<Mem> findCalculationGradeStudentByD09Code(Collection<String> d09Code);

    /**
     * 查找用户与党员关联的数据，部分列 name,phone
     *
     * @return java.util.List<com.zenith.front.model.Mem>
     * <AUTHOR>
     * @creed Talk is cheap,show me the code
     * @date 2023年03月08日 14时51分
     */
    List<Mem> findAssociatedUserData();

    boolean batchUpdate(List<Mem> memList);

    String findByIdCard(String code, String idcard);
}
