package com.zenith.front.api.codetable;

import com.baomidou.mybatisplus.extension.service.IService;
import com.zenith.front.model.dto.CodeTableColDTO;
import com.zenith.front.model.message.OutMessage;
import com.zenith.front.model.bean.CodeTableCol;

import java.util.LinkedHashMap;
import java.util.List;

/**
 * <p>
 * 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-07-10
 */
public interface ICodeTableColService extends IService<CodeTableCol> {

    List<CodeTableCol> findAllTableColByCode(String tableCode);

    OutMessage tableUp(CodeTableColDTO data);

    OutMessage tableColConfig(String id);

    OutMessage tableAll();

    /**
     * 查询启用的字典表配置
     *
     * @return
     */
    List<LinkedHashMap<String, Object>> selectListIsUse();
}
