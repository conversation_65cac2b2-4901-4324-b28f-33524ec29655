package com.zenith.front.api.unit;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.zenith.front.model.bean.OrgCommittee;
import com.zenith.front.model.bean.UnitCommitteeElect;
import com.zenith.front.model.dto.LedgerDTO;
import com.zenith.front.model.dto.UnitCommitteeDTO;
import com.zenith.front.model.dto.UnitCommitteeElectDTO;
import com.zenith.front.model.message.OutMessage;
import com.zenith.front.model.bean.UnitCommittee;
import com.zenith.front.model.vo.CurrCommitteeVO;
import com.zenith.front.model.vo.VillageLeaderListVO;

import java.util.List;
import java.util.Map;

/**
 * <p>
 * 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-07-10
 */
public interface IUnitCommitteeService extends IService<UnitCommittee> {

    /**
     * 获取单位班子成员列表
     *
     * @param pageNum
     * @param pageSize
     * @param unitCode
     * @param electCode
     * @return
     */
    OutMessage getList(int pageNum, int pageSize, String unitCode, String electCode, String leave);

    /***
     * 获取单位届次下得班子成员
     * **/
    List<UnitCommittee> findByElect(String electCode);

    /**
     * 获取单位届次下所有的班子成员
     */
    List<UnitCommittee> findAllByElect(String electCode);

    /**
     * 新增单位班子成员
     *
     * @param unitCommitteeDTO
     * @return
     */
    OutMessage addUnitCommittee(UnitCommitteeDTO unitCommitteeDTO);

    /**
     * 获取单位班子成员
     *
     * @param code
     * @return
     */
    OutMessage findByCode(String code);

    UnitCommittee selectByCode(String code);

    /**
     * 修改单位班子成员
     *
     * @param unitCommitteeDTO
     * @return
     */
    OutMessage updateUnitCommittee(UnitCommitteeDTO unitCommitteeDTO, Boolean isBack) throws Exception;

    /**
     * 删除单位班子成员
     *
     * @param code
     * @return
     */
    OutMessage delUnitCommittee(String code);

    /**
     * 根据人员获取职务信息列表
     *
     * @param pageNum
     * @param pageSize
     * @param memCode
     * @return
     */
    OutMessage getListByMemCode(int pageNum, int pageSize, String memCode);

    /**
     * 根据单位获取班子成员
     *
     * @param unitCode
     * @return
     */
    List<UnitCommittee> findByUnitCode(String unitCode);

    /**
     * 批量修改
     *
     * @param committeeList
     */
    boolean batchUpdate(List<UnitCommittee> committeeList);

    /**
     * 获取两委委员
     *
     * @param data
     * @return
     */
    OutMessage getTwoCommittee(LedgerDTO data);

    OutMessage getChangeElection(LedgerDTO data);

    /**
     * 获取单位领导班子届次列表查询
     *
     * @param pageNum
     * @param pageSize
     * @param unitCode
     * @return
     */
    OutMessage getElectList(int pageNum, int pageSize, String unitCode);

    OutMessage addElect(UnitCommitteeElectDTO electDTO);

    OutMessage updateElect(UnitCommitteeElectDTO electDTO);

    OutMessage delElect(String electCode);

    OutMessage findElect(String electCode);

    CurrCommitteeVO findCurrYearByMemCode(String memCode);

    /**
     * 从届内历史人员中撤销
     */
    OutMessage<?> backOut(UnitCommitteeDTO data) throws Exception;

    /**
     * 获取班子成员兼任信息
     *
     * @param unitCode 单位code
     * @param memCode  人员code
     * @return 查询结果
     */
    OutMessage<?> getUnitCommitteeSrcOrg(String unitCode, String memCode);

    /**
     * 同步数据到村社区
     *
     * @return
     */
    List<UnitCommittee> findAllBySyncVillager();

    /**
     * 查询村两委班子信息
     *
     * @param pageNum      当前页
     * @param pageSize     每页显示条数
     * @param orgLevelCode 层级码
     * @param includeChild 是否包含下级
     * @param queryStr     查询条件
     * @return com.baomidou.mybatisplus.extension.plugins.pagination.Page<com.zenith.front.vo.VillageLeaderListVO>
     * <AUTHOR>
     * @creed Talk is cheap,show me the code
     * @date 2023年03月02日 16时17分
     */
    Page<VillageLeaderListVO> findVillageLeaderList(Integer pageNum, Integer pageSize, String orgLevelCode, Boolean includeChild, String queryStr);

    /**
     * 查询村两委班子统计
     *
     * @param orgLevelCode 层级码
     * @param includeChild 是否包含下级
     * @param queryStr     查询条件
     * @return com.baomidou.mybatisplus.extension.plugins.pagination.Page<java.util.Map < java.lang.String, java.lang.Object>>
     * <AUTHOR>
     * @creed Talk is cheap,show me the code
     * @date 2023年03月02日 16时17分
     */
    List<Map<String, String>> getVillageLeaderStat(String orgLevelCode, Boolean includeChild, String queryStr);

    /**
     * 根据党员code获取党员是否有当前年份有在职的信息
     * 目前仅仅查询了届次code和当前数据的主键
     *
     * @return
     */
    List<UnitCommittee> findUnitCommitByMemCode(String memCode);

    /**
     * 根据党员code获取党员所有班子情况
     * */
    List<UnitCommittee> findUnitCommitByMemCodeAll(String memCode);

    /**
     * 根据党员身份证获取党员所有班子情况
     * */
     List<UnitCommittee> findUnitCommitByMemIdcard(String memIdcard);

    /**
     * 新增单位班子成员
     * @param unitCommitteeDTO
     * @param isCheck 是否只进行校验, 是的话在保存前返回
     * @return
     */
    OutMessage addUnitCommittee(UnitCommitteeDTO unitCommitteeDTO, Boolean isCheck);
}
