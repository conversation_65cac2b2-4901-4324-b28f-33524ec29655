package com.zenith.front.api.trend;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.zenith.front.model.modelview.TrendCheckView;

import java.util.ArrayList;

/**
 * <p>
 * 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-07-10
 */
public interface ITrendCheckViewService extends IService<TrendCheckView> {

    /**
     * 根据组织code获取与我相关的审核
     */
    Page<TrendCheckView> checkTrendList(String trendName, ArrayList<Integer> typeList, ArrayList<Integer> status, ArrayList<Integer> checkTypeList, String code, Integer pageNum, Integer pageSize);

}
