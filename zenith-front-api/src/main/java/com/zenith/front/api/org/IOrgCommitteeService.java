package com.zenith.front.api.org;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.zenith.front.model.dto.OrgCommitteeDTO;
import com.zenith.front.model.message.OutMessage;
import com.zenith.front.model.bean.OrgCommittee;

import java.util.List;

/**
 * <p>
 * 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-07-10
 */
public interface IOrgCommitteeService extends IService<OrgCommittee> {
    /**
     * 查询单个班子成员是否存在
     *
     * @param code
     * @return
     */
    OrgCommittee findCommitBycode(String code);

    /**
     * 根据届次信息分页获取班子成员列表
     *
     * @param pageNum
     * @param pageSize
     * @param electCode
     * @return
     */
    Page<OrgCommittee> getListByElectCode(int pageNum, int pageSize, String electCode, String leave);

    /**
     * 新增班子成员
     *
     * @param data
     * @return
     */
    OutMessage add(OrgCommitteeDTO data);

    /**
     * 编辑班子成员
     *
     * @param data
     * @return
     */
    OutMessage updateM(OrgCommitteeDTO data);

    /**
     * 移除班子成员
     *
     * @param data
     * @return
     */
    OutMessage del(OrgCommitteeDTO data);

    /**
     * 查询单个班子成员
     *
     * @param code
     * @param currManOrgCode
     * @return
     */
    OutMessage findCommitByCode(String code, String currManOrgCode);

    /**
     * 分页获取届次的班子成员
     *
     * @return
     */
    OutMessage getList(int pageNum, int pageSize, String electCode, String leave, String currManOrgCode);

    /**
     * 根据人员获取职务信息列表
     *
     * @return
     */
    OutMessage getListByMemCode(int pageNum, int pageSize, String memCode);

    /**
     * 根据党员code获取党员是否有当前年份有在职的信息
     * 目前仅仅查询了届次code和当前数据的主键
     *
     * @param memCode
     * @return
     */
    List<OrgCommittee> findOrgCommitByMemCode(String memCode,String orgCode);

    /**
     * 从届内历史人员中撤销
     */
    OutMessage<?> backOut(OrgCommitteeDTO data);

    /**
     * 获取班子成员兼任信息
     */
    OutMessage<?> getOrgCommitteeSrcUnit(String orgCode, String memCode);

    /**
     * 查找当前年度的组织领导班子
     *
     * @param memCode
     * @return
     */
    OrgCommittee findCurrYearByMemCode(String memCode);

    /***
     * 根据届次code查询届次下得在任人员
     * ***/

    List<OrgCommittee> findCommitByElect(String electCode);

    List<OrgCommittee> findAllCommitByElect(String electCode);

    /**
     * 获取本年度届内历史任职
     */
    List<OrgCommittee> findAllCommitByElectAndEnd(String electCode);

    /**
     * 同步数据到村社区系统
     *
     * @return
     */
    List<OrgCommittee> findAllBySyncVillager();

    /**
     * 根据身份证获取人员职务
     * @return
     */
    OutMessage getListByIdcard(int pageNum, int pageSize, String idcard);

    /**
     * 新增班子成员
     *
     * @param data
     * @param isCheck 是否只进行校验, 是的话在保存前返回
     * @return
     */
    OutMessage add(OrgCommitteeDTO data, Boolean isCheck);
}
