package com.zenith.front.api.mem;

import com.zenith.front.model.dto.MemTrainDTO;
import com.zenith.front.model.dto.MemTrainPageDTO;
import com.zenith.front.model.message.OutMessage;
import com.zenith.front.model.bean.MemTrain;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;

/**
 * <p>
 * 党员培训 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-08-05
 */
public interface IMemTrainService extends IService<MemTrain> {

    OutMessage addOrUpdate(MemTrainDTO data);

    OutMessage findTrainByCode(String code);

    OutMessage listTrainByCode(MemTrainPageDTO dto);

    OutMessage deleteTrainByCode(String code);

    OutMessage saveBatchTrain(List<MemTrainDTO> data);

    /**
     * 根据组织code查询人员培训信息
     *
     * @param orgCode 组织code
     * @return
     */
    List<MemTrain> findTrainByOrgCode(String orgCode);
}
