package com.zenith.front.api.unit;

import com.zenith.front.model.dto.UnitSiteConditionsDTO;
import com.zenith.front.model.dto.UnitSiteConditionsListDTO;
import com.zenith.front.model.message.OutMessage;
import com.zenith.front.model.bean.UnitSiteConditions;
import com.baomidou.mybatisplus.extension.service.IService;

/**
 * <p>
 * 经费场地情况 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-10-07
 */
public interface IUnitSiteConditionsService extends IService<UnitSiteConditions> {

    OutMessage addOrUpdate(UnitSiteConditionsDTO data);

    OutMessage getList(UnitSiteConditionsListDTO data);

    OutMessage findByCode(String code);

    OutMessage delByCode(String code);
}
