package com.zenith.front.api.org;

import com.zenith.front.model.dto.OrgIndustryListDto;
import com.zenith.front.model.dto.OrgSpecialNatureDTO;
import com.zenith.front.model.dto.OrgSpecialNatureListDTO;
import com.zenith.front.model.message.OutMessage;
import com.zenith.front.model.bean.OrgSpecialNature;
import com.baomidou.mybatisplus.extension.service.IService;

/**
 * <p>
 * 特殊性质的党组织 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-09-09
 */
public interface IOrgSpecialNatureService extends IService<OrgSpecialNature> {

    OutMessage addOrUpdate(OrgSpecialNatureDTO data);

    OutMessage details(String code);

    OutMessage getList(OrgSpecialNatureListDTO data);

    OutMessage delete(String code);

    OutMessage export(OrgIndustryListDto data) throws Exception;
}
