package com.zenith.front.api.ztdc;

import com.baomidou.mybatisplus.extension.service.IService;
import com.zenith.front.model.dto.Zt12InternetEnterprisDto;
import com.zenith.front.model.message.OutMessage;
import com.zenith.front.model.bean.Zt12InternetEnterpris;

/**
 * <p>
 * 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-08-13
 */
public interface IZt12InternetEnterprisService extends IService<Zt12InternetEnterpris> {

    Zt12InternetEnterpris findByUnitCode(String unitCode);

    OutMessage<?> saveZt12Data(Zt12InternetEnterprisDto data);
}
