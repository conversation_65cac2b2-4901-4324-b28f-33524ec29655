package com.zenith.front.api.mem;

import com.baomidou.mybatisplus.extension.service.IService;
import com.zenith.front.model.dto.MemTrainInfoDto;
import com.zenith.front.model.dto.MemTrainInfoListDto;
import com.zenith.front.model.message.OutMessage;
import com.zenith.front.model.bean.MemTrainInfo;

/**
 * <p>
 * 党员培训情况服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-04-11
 */
public interface IMemTrainInfoService extends IService<MemTrainInfo> {
    MemTrainInfo findByMemCode(String memCode);

    OutMessage<?> getList(MemTrainInfoListDto listDto);


    OutMessage<?> addOrUpdate(MemTrainInfoDto data);

    OutMessage<?> findByCode(String code);

    OutMessage<?> del(String code);

}
