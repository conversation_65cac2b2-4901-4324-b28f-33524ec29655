package com.zenith.front.api.org;

import com.baomidou.mybatisplus.extension.service.IService;
import com.zenith.front.model.custom.Record;
import com.zenith.front.model.dto.ChartDataDTO;
import com.zenith.front.model.message.OutMessage;
import com.zenith.front.model.bean.OrgAll;

import java.util.Collection;
import java.util.List;
import java.util.Map;

/**
 * <p>
 * 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-07-10
 */
public interface IOrgAllService extends IService<OrgAll> {

    List<OrgAll> findAllSubOrgByOrgCode(String orgCode);

    /**
     * 查询组织
     *
     * @param codeList
     * @return
     */
    List<OrgAll> findByOrgCodeList(Collection<String> codeList);

    /**
     * 根据组织层级码集合查询部分列信息
     *
     * @param orgCodeList 组织层级码集合
     * @return java.util.Map<java.lang.String, java.lang.String>
     * <AUTHOR>
     * @creed Talk is cheap,show me the code
     * @date 2023年03月10日 16时56分
     */
    Map<String, String> findMainUnitNameByOrgCodeList(Collection<String> orgCodeList);

    /**
     * 批量修改
     *
     * @param updateOrgAllList
     */
    boolean batchUpdate(List<OrgAll> updateOrgAllList);

    /**
     * 根据code查找
     *
     * @param code
     * @return
     */
    OrgAll findByCode(String code);

    /**
     * 根据code查找
     *
     * @param orgCode
     * @return
     */
    OrgAll findByOrgCode(String orgCode);

    /**
     * 获取党组织总数
     *
     * @param data
     * @return
     */
    OutMessage getOrgTotal(ChartDataDTO data);

    /**
     * 获取即将届满数
     *
     * @param data
     * @return
     */
    OutMessage getOrgElectTotal(ChartDataDTO data);

    /**
     * 组织类别
     *
     * @param data
     * @return
     */
    OutMessage getOrgTypeTotal(ChartDataDTO data);

    /**
     * 党委总数
     *
     * @param data
     * @return
     */
    OutMessage getDwRatioTotal(ChartDataDTO data);

    /**
     * 党支部总数
     *
     * @param data
     * @return
     */
    OutMessage getDzbRatioTotal(ChartDataDTO data);

    /**
     * 特设党支部数
     *
     * @param data
     * @return
     */
    OutMessage getTsdzbRatioTotal(ChartDataDTO data);

    /**
     * 党小组数
     *
     * @param data
     * @return
     */
    OutMessage getDxzRatioTotal(ChartDataDTO data);

    /**
     * 联合党支部数
     *
     * @param data
     * @return
     */
    OutMessage getLhdzbRatioTotal(ChartDataDTO data);

    /**
     * 建立单位情况
     *
     * @param data
     * @return
     */
    OutMessage getD02RatioTotal(ChartDataDTO data);

    /**
     * 组织类别中共各级1
     *
     * @param data
     * @return
     */
    OutMessage getZGTypeOneTotal(ChartDataDTO data);

    /**
     * 组织类别中共各级2
     *
     * @param data
     * @return
     */
    OutMessage getZGTypeTwoTotal(ChartDataDTO data);

    /**
     * 党组
     *
     * @param data
     * @return
     */
    OutMessage getDzTypeRatioTotal(ChartDataDTO data);

    /**
     * 国家工作部门党委
     *
     * @param data
     * @return
     */
    OutMessage getGjgzbwTypeRatioTotal(ChartDataDTO data);

    /**
     * 党的基层组织
     *
     * @param data
     * @return
     */
    OutMessage getDjcTypeRatioTotal(ChartDataDTO data);

    /**
     * 特设组织
     *
     * @param data
     * @return
     */
    OutMessage getTszzTypeRatioTotal(ChartDataDTO data);

    /**
     * 临时基层党组织
     *
     * @param data
     * @return
     */
    OutMessage getLsjcdzzTypeRatioTotal(ChartDataDTO data);

    /**
     * 隶属关系
     *
     * @param data
     * @return
     */
    OutMessage getAffiliationRatioTotal(ChartDataDTO data);

    /**
     * 乡镇隶属情况
     *
     * @param data
     * @return
     */
    OutMessage getXzAffiliationRatioTotal(ChartDataDTO data);

    /**
     * 社区建制村隶属情况
     *
     * @param data
     * @return
     */
    OutMessage getSqjwhAffiliationRatioTotal(ChartDataDTO data);

    /**
     * 获取组织类型分布图
     *
     * @param data
     * @return
     */
    OutMessage getOrgTypeRatio(ChartDataDTO data);

    /**
     * 获取组织信息完整度平均值
     *
     * @param data
     * @return
     */
    OutMessage getOrgRatio(ChartDataDTO data);

    /**
     * 三会一课类型
     *
     * @param data
     * @return
     */
    OutMessage getOrgActivityRatio(ChartDataDTO data);

    /**
     * 党费
     *
     * @param data
     * @return
     */
    OutMessage getOrgFeeRatio(ChartDataDTO data);

    /**
     * 换届信息
     *
     * @param data
     * @return
     */
    OutMessage getOrgElectRatio(ChartDataDTO data);

    /**
     * 组织信息,基层组织,部门工作党委,机关党委(中共各级工作委员会),基层党委,总支部,支部
     */
    Record getBasicOrgInfo(String orgCode);

    /**
     * 更新锁定字段
     *
     * @param filedList 锁定字段
     * @return
     */
    boolean updateLockFields(List<String> filedList);

    /**
     * 查询驻村系统组织树
     *
     * @return java.util.List<com.zenith.front.model.OrgAll>
     * <AUTHOR>
     * @creed Talk is cheap,show me the code
     * @date 2023年03月01日 09时04分
     */
    List<OrgAll> getVillageCommunityOrgList();

    /**
     * 根据组织主键查询层级码
     *
     * @param code 主键
     * @return java.lang.String
     * <AUTHOR>
     * @creed Talk is cheap,show me the code
     * @date 2023年03月03日 13时51分
     */
    String findOrgCodeByCode(String code);

    /**
     * 导出党委
     * @param orgCode
     */
    void exportPartyCommittee(String orgCode);

    /**
     * 导出党总支
     * @param orgCode
     */
    void exportPartyGeneralBranch(String orgCode);

    /**
     * 导出党支部
     * @param orgCode
     */
    void exportPartyBranch(String orgCode);

    /**
     * 导出本年内开展民主评议的支部
     * @param orgCode
     */
    void exportYearOrgInformation(String orgCode);

    /**
     * 导出在任党组织书记
     * @param orgCode
     */
    void exportInOfficeSecretary(String orgCode);
}
