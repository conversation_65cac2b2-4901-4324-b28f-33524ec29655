package com.zenith.front.api.org;

import com.zenith.front.model.dto.OrgTownshipLeadershipDTO;
import com.zenith.front.model.dto.OrgTownshipLeadershipListDTO;
import com.zenith.front.model.message.OutMessage;
import com.zenith.front.model.bean.OrgTownshipLeadership;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;

/**
 * <p>
 * 乡镇班子换届情况 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-11-17
 */
public interface IOrgTownshipLeadershipService extends IService<OrgTownshipLeadership> {

    OutMessage addOrUpdate(OrgTownshipLeadershipDTO data);

    OutMessage getList(OrgTownshipLeadershipListDTO data);

    OutMessage findByCode(String code);

    OutMessage delByCode(String code);

    /**
     * 根据组织code查询乡镇班子换届情况
     *
     * @param orgCode 组织code
     * @return
     */
    List<OrgTownshipLeadership> findOrgTownshipLeadershipByOrgCode(String orgCode);

}
