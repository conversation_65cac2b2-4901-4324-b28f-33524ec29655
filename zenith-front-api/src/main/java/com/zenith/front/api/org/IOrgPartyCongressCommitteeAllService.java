package com.zenith.front.api.org;

import com.zenith.front.model.bean.OrgPartyCongressCommitteeAll;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;

/**
 * <p>
 * 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-01-27
 */
public interface IOrgPartyCongressCommitteeAllService extends IService<OrgPartyCongressCommitteeAll> {

    /**
     * 根据届次code查询党代表届次信息
     *
     * @param electCodeList 届次code
     * @return
     */
    List<OrgPartyCongressCommitteeAll> findOrgPartyCongressCommitteeAllByElectCode(List<String> electCodeList);
}
