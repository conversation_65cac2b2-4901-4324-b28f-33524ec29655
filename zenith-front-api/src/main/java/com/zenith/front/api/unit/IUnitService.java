package com.zenith.front.api.unit;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.zenith.front.model.bean.UnitExchangeArea;
import com.zenith.front.model.dto.PeopleManagementDTO;
import com.zenith.front.model.dto.UnitDTO;
import com.zenith.front.model.dto.UnitListDTO;
import com.zenith.front.model.dto.UnitMvDTO;
import com.zenith.front.model.message.OutMessage;
import com.zenith.front.model.bean.Unit;

import java.util.Collection;
import java.util.List;

/**
 * <p>
 * 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-07-10
 */
public interface IUnitService extends IService<Unit> {

    Unit findByCode(String unitCode);

    /**
     * 获取单位列表
     *
     * @param unitListDTO
     * @return
     */
    OutMessage getList(UnitListDTO unitListDTO);

    /**
     * 新增单位
     * // TODO: 2019/4/10 所有业务的新增修改删除接口进行业务同步
     * // TODO: 2019/4/10 单位组织的关联
     * // TODO: 2019/4/13 新增单位 党建指导组织参数不对
     *
     * @param unitDTO
     * @return
     */
    OutMessage addUnit(UnitDTO unitDTO);

    /**
     * 获取单位
     *
     * @param code
     * @return
     */
    OutMessage finByCode(String code);

    /**
     * 修改单位
     *
     * @param unitDTO
     * @return
     */
    OutMessage updateUnit(UnitDTO unitDTO);

    /**
     * 删除单位
     *
     * @param code
     * @return
     */
    OutMessage delUnit(String code);

    /**
     * 导出单位数据
     *
     * @param unitListDTO
     */
    void exportUnitData(UnitListDTO unitListDTO);

    /**
     * 批量查询unit
     *
     * @param unitCodeList
     * @return
     */
    List<Unit> findByCodes(Collection<String> unitCodeList);

    boolean batchMainOrgByCodes(int isCreateOrg, String mainOrgCode, String mainOrgName, String mainOrgType, String mainOrgTypeCode, String mainUnitOrgCode, List<String> unitCodes);


    List<Unit> selectIdByCode(Collection<String> strings);

    @Deprecated
    OutMessage getMemByOrg(PeopleManagementDTO data);

    void updateD05ByIds(String key, String name, List<String> unitCodeList);

    OutMessage getUnitByOrg(String orgCode);

    /**
     * 获取与上级党组织相同组织关联的主单位
     */
    OutMessage<?> getMainUnitByOrg(String orgCode);

    OutMessage exportMem(PeopleManagementDTO data) throws Exception;

    OutMessage unitMv(UnitMvDTO data);

    /**
     * 根据条件进行分页查询党员
     *
     * @param pageNum           当前页
     * @param pageSize          每页显示条数
     * @param hasLocked         是否被锁定 1 true 0 false
     * @param hasSubordinate    是否显示下级 1 显示 0 不显示
     * @param manageUnitOrgCode 党员层级码
     * @param mainUnitOrgCode   党员层级码
     * @param keyword           搜索关键词
     * @return
     */
    Page<Unit> findPageByCondition(Integer pageNum, Integer pageSize, Boolean hasLocked, Boolean hasSubordinate, String manageUnitOrgCode, String mainUnitOrgCode, String keyword);

    /**
     * 更新锁定字段
     *
     * @param filedList 锁定字段
     * @return
     */
    boolean updateLockFields(List<String> filedList);

    /**
     * 查询单位中关联党组织相关信息
     *
     * @param code 组织code
     * @return
     */
    List<Unit> findByMainOrgCode(String code);

    /**
     * 根据单位类别查询单位列表
     *
     * @param d04CodeList 单位类别集合
     * @return
     */
    List<Unit> findByD04Code(List<String> d04CodeList);

    /**
     * 内网，单位交换区搜索
     *
     * @param dto
     * @return
     */
    OutMessage<Object> getUnitName(UnitListDTO dto);

    /**
     * 根据单位编码查询中间交换区单位
     *
     * @param unitCode 单位编码
     * @return
     */
    List<UnitExchangeArea> findExchangeUnit(String unitCode);

    /**
     * 导出乡镇(法人)
     * @param orgCode
     */
    void exportTownCorporation(String orgCode);

    /**
     * 导出城市社区(法人)
     * @param orgCode
     */
    void exportUrbanCommunityCorporation(String orgCode);

    /**
     * 导出乡镇社区(法人)
     * @param orgCode
     */
    void exportTownCommunityCorporation(String orgCode);

    /**
     * 导出行政村(法人)
     * @param orgCode
     */
    void exportAdministrativeVillageCorporation(String orgCode);
}
