package com.zenith.front.api.unit;

import com.baomidou.mybatisplus.extension.service.IService;
import com.zenith.front.model.dto.PrimaryKeyDTO;
import com.zenith.front.model.dto.UnitResidentDTO;
import com.zenith.front.model.dto.UnitResidentUpdateDTO;
import com.zenith.front.model.message.OutMessage;
import com.zenith.front.model.bean.UnitResident;

import java.util.List;
import java.util.Map;

/**
 * <p>
 * 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-04-11
 */
public interface IUnitResidentService extends IService<UnitResident> {

    /**
     * 添加驻村第一书记和驻村干部
     *
     * @param data    实体类
     * @param account 账号
     * @return
     */
    OutMessage<Object> doSave(UnitResidentDTO data, String account);

    /**
     * 删除驻村第一书记和驻村干部
     *
     * @param data    实体类
     * @param account 账号
     * @return
     */
    OutMessage<Object> doDel(PrimaryKeyDTO data, String account);

    /**
     * 修改驻村第一书记和驻村干部
     *
     * @param data    实体类
     * @param account 账号
     * @return
     */
    OutMessage<Object> doUpdate(UnitResidentUpdateDTO data, String account);

    /**
     * 查询驻村第一书记和驻村干部
     *
     * @param pageNum  页码
     * @param pageSize 每页显示条数
     * @param unitCode unitCode
     * @param leave    是否届内历史任职
     * @return
     */
    OutMessage<Object> doList(Integer pageNum, Integer pageSize, String unitCode, String leave);

    /**
     * 届内历史任职驻村第一书记和驻村干部撤销
     *
     * @param data    实体类
     * @param account 账号
     * @return
     */
    OutMessage<Object> backOut(PrimaryKeyDTO data, String account);

    /**
     * 根据届次查询驻村干部
     */
    @Deprecated
    List<UnitResident> findByElect(String electCode);

    /**
     * 根据unitCode查询驻村干部
     */
    List<UnitResident> findByUnitCode(String unitCode);

    /**
     * 查询驻村干部列表
     *
     * @return java.util.List<com.zenith.front.model.UnitResident>
     * <AUTHOR>
     * @creed Talk is cheap,show me the code
     * @date 2023年03月06日 13时50分
     */
    List<Map<String, Object>> findVillageCadresList();
}
