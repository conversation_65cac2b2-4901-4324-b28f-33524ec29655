package com.zenith.front.api.fee;

import com.baomidou.mybatisplus.extension.service.IService;
import com.zenith.front.model.dto.FeeDisburseDTO;
import com.zenith.front.model.dto.FeeDisburseListDTO;
import com.zenith.front.model.message.OutMessage;
import com.zenith.front.model.bean.FeeDisburse;
import com.zenith.front.model.bean.User;

/**
 * <p>
 *  服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-07-10
 */
public interface IFeeDisburseService extends IService<FeeDisburse> {


    /**
     * 获取党费支出列表
     * @param feeDisburseListDTO
     * @return
     */
    OutMessage getList(FeeDisburseListDTO feeDisburseListDTO);


    /**
     * 保存党费支出
     * @param feeDisburseDTO
     * @return
     */
    OutMessage saveFeeDisburse(FeeDisburseDTO feeDisburseDTO);

    /**
     * 修改党费支出
     * @param feeDisburseDTO
     * @return
     */
    OutMessage updateFeeDisburse(FeeDisburseDTO feeDisburseDTO);

    /**
     * 修改党费支出
     * @param code
     * @return
     */
    OutMessage delFeeDisburse(String code);


    /**
     * 获取微信流水
     * @param feeDisburseListDTO
     * @return
     */
    OutMessage getWxBillList(FeeDisburseListDTO feeDisburseListDTO);

    /**
     * 根据微信订单保存党费支出数据
     * @param feeDisburseDTO
     * @return
     */
    OutMessage saveByWxBill(FeeDisburseDTO feeDisburseDTO, User user);
}
