package com.zenith.front.api.logic;

import com.zenith.front.model.message.OutMessage;
import com.zenith.front.model.bean.LogicCheckIgnore;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.Set;

/**
 * <p>
 * 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-03-24
 */
public interface ILogicCheckIgnoreService extends IService<LogicCheckIgnore> {

    /**
     * 逻辑校核结果进行忽略
     *
     * @param logicCheckCode 逻辑校核主键
     * @param logicCheckName 逻辑校核名称
     * @param type           类型
     * @param code           忽略主键
     * @param reason         原因
     * @param updateAccount  操作用户
     * @return
     */
    OutMessage<Object> ignore(String logicCheckCode, String logicCheckName, String type, String code, String reason, String updateAccount);

    /**
     * 查询被忽略的校核结果
     *
     * @param orgCode 所属机构
     * @return java.util.Set<java.lang.String>
     * <AUTHOR>
     */
    Set<String> selectIgnoreCode(String orgCode);

    /**
     * 数据校验忽略列表
     *
     * @param logicCheckCode 数据校验条件主键
     * @param orgCode        所属机构
     * @param tableName      当前表名
     * @param pageNum        当前页
     * @param pageSize       每页显示条数
     * @return com.zenith.front.message.OutMessage<java.lang.Object>
     * <AUTHOR>
     * @creed Talk is cheap,show me the code
     * @date 2022年08月16日 11时17分
     */
    OutMessage<Object> ignoreList(String logicCheckCode, String orgCode, String tableName, Integer pageNum, Integer pageSize);

    /**
     * 导出数据校验忽略列表
     *
     * @param logicCheckCode 数据校验条件主键
     * @param orgCode        所属机构
     * @param tableName      当前表名
     * @param pageNum        当前页
     * @param pageSize       每页显示条数
     * <AUTHOR>
     * @creed Talk is cheap,show me the code
     * @date 2023年03月24日 11时17分
     */
    void exportIgnoreList(String logicCheckCode, String orgCode, String tableName, Integer pageNum, Integer pageSize);

    /**
     * 删除忽略结果
     *
     * @param id      被忽略的消息主键
     * @param account 操作账户
     * @return com.zenith.front.message.OutMessage<java.lang.Object>
     * <AUTHOR>
     * @creed Talk is cheap,show me the code
     * @date 2022年08月17日 13时57分
     */
    OutMessage<Object> delete(String id, String account);
}
