package com.zenith.front.api.org;

import com.zenith.front.model.dto.OrgAssessDTO;
import com.zenith.front.model.dto.OrgAssessListDTO;
import com.zenith.front.model.message.OutMessage;
import com.zenith.front.model.bean.OrgAssess;
import com.baomidou.mybatisplus.extension.service.IService;

/**
 * <p>
 * 考核信息 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-08-23
 */
public interface IOrgAssessService extends IService<OrgAssess> {

    OutMessage<?> addAssess(OrgAssessDTO orgDTO);

    OutMessage<?> deleteAssess(String code);

    OutMessage<?> detailsAssess(String code);

    OutMessage<?> getListAssess(OrgAssessListDTO data);
}
