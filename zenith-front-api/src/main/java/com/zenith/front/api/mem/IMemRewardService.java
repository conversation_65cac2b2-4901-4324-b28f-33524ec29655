package com.zenith.front.api.mem;

import com.baomidou.mybatisplus.extension.service.IService;
import com.zenith.front.model.custom.Record;
import com.zenith.front.model.dto.MemRewardDTO;
import com.zenith.front.model.message.OutMessage;
import com.zenith.front.model.bean.MemReward;

/**
 * <p>
 * 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-07-10
 */
public interface IMemRewardService extends IService<MemReward> {
    /**
     * 获取人员奖惩信息
     *
     * @param code
     * @return
     */
    MemReward findByCode(String code);

    /**
     * 获取人员奖惩列表
     */
    OutMessage getList(int pageNum, int pageSize, String memCode);

    /**
     * 新增人员奖惩
     *
     * @param memRewardDTO
     * @return
     */
    OutMessage addMemReward(MemRewardDTO memRewardDTO);


    /**
     * 获取人员奖惩信息
     *
     * @param code
     * @return
     */
    OutMessage findByCodeOut(String code);

    /**
     * 修改人员奖惩信息
     *
     * @param memRewardDTO
     * @return
     */
    OutMessage updateMemReward(MemRewardDTO memRewardDTO) throws Exception;

    /**
     * 删除人员奖惩
     *
     * @param code
     * @return
     */
    OutMessage delMemReward(String code);

    /**
     * 党内表彰,优秀共产党员,优秀党务工作者
     */
    Record getMemCommend(String orgCode);


}
