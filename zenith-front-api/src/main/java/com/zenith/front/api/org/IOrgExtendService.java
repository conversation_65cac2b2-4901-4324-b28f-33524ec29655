package com.zenith.front.api.org;

import com.zenith.front.model.dto.OrgExtendDTO;
import com.zenith.front.model.message.OutMessage;
import com.zenith.front.model.bean.OrgExtend;
import com.baomidou.mybatisplus.extension.service.IService;

/**
 * <p>
 *  服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-08-04
 */
public interface IOrgExtendService extends IService<OrgExtend> {

    /**
     * 添加组织扩展信息
     * @param data
     * @return
     */
    OutMessage addOrUpdate(OrgExtendDTO data);

    /**
     * 查询组织扩展信息详情
     * @param orgCode
     * @return
     */
    OutMessage findExtendByCode(String orgCode);
}
