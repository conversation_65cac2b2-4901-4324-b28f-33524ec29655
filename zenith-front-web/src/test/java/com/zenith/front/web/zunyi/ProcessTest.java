package com.zenith.front.web.zunyi;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.date.TimeInterval;
import cn.hutool.core.io.FileUtil;
import cn.hutool.core.io.unit.DataSizeUtil;
import com.luciad.imageio.webp.WebPWriteParam;
import org.apache.pdfbox.pdmodel.PDDocument;
import org.apache.pdfbox.rendering.ImageType;
import org.apache.pdfbox.rendering.PDFRenderer;
import org.junit.jupiter.api.Test;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;

import javax.imageio.IIOImage;
import javax.imageio.ImageIO;
import javax.imageio.ImageWriter;
import javax.imageio.stream.FileImageOutputStream;
import java.awt.image.BufferedImage;
import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ThreadPoolExecutor;

/**
 * <AUTHOR>
 * @create_date 2025-02-17 11:28
 * @description
 */
//@SpringBootTest
public class ProcessTest {

    private static final ThreadPoolTaskExecutor MINIO_EXECUTOR = new ThreadPoolTaskExecutor();

    static {
        // 核心线程数
        MINIO_EXECUTOR.setCorePoolSize(20);
        // 最大线程数
        MINIO_EXECUTOR.setMaxPoolSize(100);
        // 队列数量
        MINIO_EXECUTOR.setQueueCapacity(5);
        // 空闲线程存活时间 默认秒数
        MINIO_EXECUTOR.setKeepAliveSeconds(60);
        // 自定义线程名前缀
        MINIO_EXECUTOR.setThreadNamePrefix("MINIO_EXECUTOR-");
        // 拒绝策略 AbortPolicy-丢弃任务抛异常（默认） DiscardPolicy-丢弃不抛异常 DiscardOldestPolicy-丢弃队列最前面的任务，CallerRunsPolicy 由主线程（调用线程）处理该任务
        MINIO_EXECUTOR.setRejectedExecutionHandler(new ThreadPoolExecutor.CallerRunsPolicy());
        // 初始化线程池
        MINIO_EXECUTOR.initialize();
    }
    @Test
    void t3(){
        File fi = new File("C:\\Users\\<USER>\\Desktop\\朱萸\\扫描全能王 2025-04-25 10.26.pdf");
        System.out.println(fi.length()/1024);
    }

    @Test
    void t1() throws IOException {

        TimeInterval t = new TimeInterval();
        System.out.println("开始："+t.intervalSecond());
        File fi = new File("C:\\Users\\<USER>\\Desktop\\朱萸\\扫描全能王 2025-04-25 10.26.pdf");
        System.out.println(fi.length()/1024);
        PDDocument document = PDDocument.load(fi);
        int pageCount = document.getNumberOfPages();
        // 线程组
        List<CompletableFuture<Object>> supplierList = new ArrayList<>();
        for (int pageNum = 0; pageNum < pageCount; pageNum++) {
            // 顺序号
            final int finalPageNum = pageNum;
            PDFRenderer renderer = new PDFRenderer(document);
            supplierList.add(
                    CompletableFuture.supplyAsync(() -> {
                        BufferedImage originalImage;
                        try {
                            System.out.println("k："+t.intervalSecond() + "; finalPageNum="+finalPageNum);
                            originalImage = renderer.renderImageWithDPI(finalPageNum, 150, ImageType.RGB);
                            System.out.println("e："+t.intervalSecond() + "; finalPageNum="+finalPageNum);

                        } catch (IOException e) {
                            throw new RuntimeException(e.getMessage());
                        }
                        // 处理图片
                        // 生成唯一路径
                        String tempName = finalPageNum + "_"  + "(pdf提取).JPEG";

                        // 这里通过本地文件操作，比通过转换字节效率更快
                        File tmpFile = null;
                        FileImageOutputStream fileOutputStream = null;
                        FileInputStream localFile = null;
                        try {
                            tmpFile = new File("C:\\Users\\<USER>\\Desktop\\朱萸\\tt\\" + tempName);
                            fileOutputStream = new FileImageOutputStream(tmpFile);
                            // 设置成标准的RGB，否则有些解析错误
                            BufferedImage rgbImage = new BufferedImage(
                                    originalImage.getWidth(), originalImage.getHeight(), BufferedImage.TYPE_INT_RGB
                            );
                            rgbImage.getGraphics().drawImage(originalImage, 0, 0, null);
                            // webp
                            ImageWriter writer = ImageIO.getImageWritersByMIMEType("image/webp").next();
                            // 配置编码参数
                            WebPWriteParam writeParam = new WebPWriteParam(writer.getLocale());
                            // 设置压缩模式
                            writeParam.setCompressionMode(WebPWriteParam.LOSSLESS_COMPRESSION);
                            // 配置ImageWriter输出
                            writer.setOutput(fileOutputStream);
                            // 进行编码，重新生成新图片
                            writer.write(null, new IIOImage(rgbImage, null, null), writeParam);

//                            ImageIO.write(originalImage, "JPEG", fileOutputStream);
                            localFile = new FileInputStream(tmpFile);
                            // 执行上传
                        } catch (Exception e) {
                            throw new RuntimeException(e.getMessage());
                        } finally {
                            if (Objects.nonNull(fileOutputStream)) {
                                try {
                                    fileOutputStream.close();
                                } catch (IOException e) {
                                    e.printStackTrace();
                                }
                            }
                            if (Objects.nonNull(localFile)) {
                                try {
                                    localFile.close();
                                } catch (IOException e) {
                                    e.printStackTrace();
                                }
                            }
                            // 清理临时文件
                            if (Objects.nonNull(tmpFile)) {
//                                FileUtil.del(tmpFile);
                            }
                            // 主动释放buffer资源
                            if (Objects.nonNull(originalImage)) {
                                originalImage.flush();
                            }
                        }
                        System.out.println(finalPageNum+";tr"+Thread.currentThread().getName()+"循环："+t.intervalSecond());
                        // 生成文件信息返回前端
                        return null;
                    }, MINIO_EXECUTOR));
            // 每5个为一组执行
            if (supplierList.size() % 15 == 0 || pageNum == pageCount - 1) {
                CompletableFuture.allOf(supplierList.toArray(new CompletableFuture[0])).join();
                supplierList.clear();
            }

        }
        System.out.println("结束："+t.intervalSecond());

    }
}
