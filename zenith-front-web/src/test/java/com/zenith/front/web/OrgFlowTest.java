package com.zenith.front.web;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import cn.hutool.core.thread.ThreadUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.zenith.front.api.flow.FlowExchangeService;
import com.zenith.front.api.org.IOrgService;
import com.zenith.front.core.service.org.OrgServiceImpl;
import com.zenith.front.model.bean.Org;
import com.zenith.front.model.bean.OrgFlow;
import com.zenith.front.model.dto.flow.OrgFlowSync;
import com.zenith.front.model.dto.flow.OrgFlowSyncAll;
import com.zenith.front.model.message.OutMessage;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

/**
 * <AUTHOR>
 * @since 2025/1/17 15:33
 */
@SpringBootTest
public class OrgFlowTest {
    @Autowired
    private FlowExchangeService flowExchangeService;
    @Autowired
    private IOrgService orgService;
    @Test
    public void test(){
        // OrgFlowSyncAll orgFlowSyncAll = new OrgFlowSyncAll();
        //
        // OrgFlowSync orgFlow = new OrgFlowSync();
        // orgFlow.setId(0L);
        // orgFlow.setCode("setCode");
        // orgFlow.setEsId("setEsId");
        // orgFlow.setZbCode("setZbCode");
        // orgFlow.setOrgCode("setOrgCode");
        // orgFlow.setName("11111111222222");
        // orgFlow.setShortName("setShortName");
        // orgFlow.setPinyin("setPinyin");
        // orgFlow.setIsEnable(0);
        // orgFlow.setParentCode("setParentCode");
        // orgFlow.setD01Code("setD01Code");
        // orgFlow.setCreateDate(new Date());
        // orgFlow.setContacter("setContacter");
        // orgFlow.setContactPhone("setContactPhone");
        // orgFlow.setD200Code("setD200Code");
        // orgFlow.setApproveCode("setApproveCode");
        // orgFlow.setAdministrativeDivision("setAdministrativeDivision");
        // orgFlow.setSort(0);
        // orgFlow.setCreateTime(new Date());
        // orgFlow.setUpdateTime(new Date());
        // // orgFlow.setDeleteTime(new Date());
        // orgFlow.setRemark("");
        // orgFlowSyncAll.setOrgFlow(orgFlow);
        // OutMessage outMessage = flowExchangeService.pushFlow2Exchange(orgFlowSyncAll);
        // System.out.println(outMessage);
        // ThreadUtil.sleep(1000 * 20);
        // outMessage = flowExchangeService.pushFlow2Exchange(orgFlowSyncAll);
        // System.out.println(outMessage);
        // ThreadUtil.sleep(1000 * 20);
        // outMessage = flowExchangeService.pushFlow2Exchange(orgFlowSyncAll);
        // System.out.println(outMessage);
        List<String> codeList= new ArrayList<>();
        codeList.add("78A61F00E450419489B0138BDC5BB5E9");
        codeList.add("556e0c0593dc4b188098c2cf55404378");
        codeList.add("98f8a750f064445eb44f618973497c31");
        LambdaQueryWrapper<Org> objectLambdaQueryWrapper = new LambdaQueryWrapper<>();
        objectLambdaQueryWrapper.in(Org::getCode,codeList);
        List<Org> list = orgService.list(objectLambdaQueryWrapper);
        orgService.batchUpdate(list);
    }
}
