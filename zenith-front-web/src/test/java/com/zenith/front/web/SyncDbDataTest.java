package com.zenith.front.web;

import com.aspose.cells.LoadFormat;
import com.aspose.cells.LoadOptions;
import com.aspose.cells.Workbook;
import com.aspose.words.Document;
import com.aspose.words.DocumentBuilder;
import com.aspose.words.SaveFormat;
import com.jfinal.kit.PathKit;
import com.zenith.front.api.activist.IActivistTransferRecordService;
import com.zenith.front.api.mem.IMemService;
import com.zenith.front.api.org.IOrgAllService;
import com.zenith.front.api.unit.IUnitResidentService;
import com.zenith.front.core.analysis.service.ReportResultServiceImpl;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.util.ResourceUtils;

import javax.annotation.Resource;
import java.io.*;
import java.nio.charset.StandardCharsets;
import java.util.HashMap;
import java.util.Map;


/**
 * <AUTHOR>
 * @date 2021/11/8
 */
@SpringBootTest
public class SyncDbDataTest {
    @Resource
    private IOrgAllService orgAllService;
    @Resource
    private IMemService iMemService;
    @Resource
    private IUnitResidentService iUnitResidentService;
    @Resource
    private IUnitResidentService unitResidentService;
    @Resource
    private IActivistTransferRecordService iActivistTransferRecordService;

    public static String exchange_nginx_key = "f33911ed4a59f040";

    @Test
    public void test() throws Exception {
    }


    public static void main1(String[] args) throws Exception {
        String basePath = ResourceUtils.getURL(ResourceUtils.CLASSPATH_URL_PREFIX).getPath();
        String filePath = PathKit.getWebRootPath() + "/public/export/";
        File file = new File("src/main/resources/templates/html/29.html");
        InputStream inputStream = new ByteArrayInputStream(file.toString().getBytes());
        LoadOptions lo = new LoadOptions(LoadFormat.HTML);
        Workbook workbook = new Workbook(inputStream, lo);
//        workbook.save();
        inputStream.close();



//        Document document = new Document();
//        DocumentBuilder builder = new DocumentBuilder(document);
//        InputStreamReader streamReader = new InputStreamReader(is, StandardCharsets.UTF_8);
//        BufferedReader bufferedReader = new BufferedReader(streamReader);
//        String line;
//        StringBuilder html = new StringBuilder();
//        while ((line = bufferedReader.readLine()) != null) {
//            html.append(line);
//        }
//        bufferedReader.close();
//        builder.insertHtml(String.valueOf(html));
//        document.save(filePath, SaveFormat.DOCX);
//        streamReader.close();
//        is.close();

    }

}
