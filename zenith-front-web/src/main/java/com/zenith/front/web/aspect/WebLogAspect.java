package com.zenith.front.web.aspect;

import com.alibaba.fastjson.JSONArray;
import com.zenith.front.common.constant.PermissionConstant;
import com.zenith.front.common.context.GetParameterContextHolder;
import com.zenith.front.common.kit.IpKit;
import com.zenith.front.common.context.PostParameterContextHolder;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.*;
import org.springframework.http.HttpMethod;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.io.PrintWriter;
import java.io.StringWriter;
import java.util.Objects;

/**
 * <AUTHOR>
 */
@Aspect
@Component
@Slf4j
public class WebLogAspect {

    @Resource
    private HttpServletRequest request;

    public final static String REQUEST_NAME = "requestStartTime";

    private static final String NORMAL_LOG = "\n=============\n" +
            "clientIp:%s\n" +
            "requestURI:%s\n" +
            "method:%s\n" +
            "methodName:%s\n" +
            "args:%s\n" +
            "token:%s\n" +
            "return:%s\n" +
            "time:%d\n" +
            "请求成功\n=============";

    private static final String ERROR_LOG = "\n=============\n" +
            "clientIp:%s\n" +
            "requestURI:%s\n" +
            "method:%s\n" +
            "methodName:%s\n" +
            "args:%s\n" +
            "token:%s\n" +
            "return:%s\n" +
            "error:%s\n" +
            "time:%d\n" +
            "请求失败\n=============";

    /**
     * 声明controller包下为切点
     */
    @Pointcut("within(com.zenith.front.web.controller..*) || within(com.zenith.front.zunyi.controller..*)")
    public void webLog() {

    }

    /**
     * 在切点前织入
     */
    @Before("webLog()")
    public void doBefore() {
        //记录开始时间
        long requestStartTime = System.currentTimeMillis();
        request.setAttribute(REQUEST_NAME, requestStartTime);
    }

    /**
     * 环绕
     */
    @Around("webLog()")
    public Object doAround(ProceedingJoinPoint proceedingJoinPoint) throws Throwable {
        Object proceed = proceedingJoinPoint.proceed();
        long takeTime = System.currentTimeMillis() - (long) request.getAttribute(REQUEST_NAME);
        //接口结束后
        String method = request.getMethod();
        log.info(String.format(NORMAL_LOG,
                IpKit.getRealIp(request),
                request.getRequestURL().toString(),
                method,
                proceedingJoinPoint.getSignature().getName(),
                args(method),
                request.getHeader(PermissionConstant.HEADER_AUTHORIZATION),
                Objects.nonNull(proceed) ? JSONArray.toJSON(proceed).toString() : "",
                takeTime));
        return proceed;
    }

    @AfterThrowing(value = "webLog()", throwing = "throwable")
    public void doThrowing(JoinPoint joinPoint, Throwable throwable) {
        long takeTime = System.currentTimeMillis() - (long) request.getAttribute(REQUEST_NAME);
        String errMessage = "";
        try (
                StringWriter writer = new StringWriter();
                PrintWriter printWriter = new PrintWriter(writer)
        ) {
            throwable.printStackTrace(printWriter);
            errMessage = writer.toString();
        } catch (Exception e) {
            e.printStackTrace();
        }
        String method = request.getMethod();
        log.error(String.format(ERROR_LOG,
                IpKit.getRealIp(request),
                request.getRequestURI(),
                method,
                joinPoint.getSignature().getName(),
                args(method),
                request.getHeader(PermissionConstant.HEADER_AUTHORIZATION),
                "无",
                errMessage,
                takeTime));
    }

    private String args(String method) {
        String args = "";
        if (HttpMethod.GET.matches(method)) {
            args = GetParameterContextHolder.get();
        } else if (HttpMethod.POST.matches(method)) {
            String bodyString = PostParameterContextHolder.get();
            if (StringUtils.hasText(bodyString)) {
                args = bodyString
                        .replaceAll("\n", "")
                        .replaceAll("\r", "")
                        .replaceAll("\t", "")
                        .replaceAll(" ", "");
            }
        }
        return args;
    }
}
