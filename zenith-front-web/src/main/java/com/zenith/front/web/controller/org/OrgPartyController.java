package com.zenith.front.web.controller.org;


import com.zenith.front.api.org.IOrgPartyService;
import com.zenith.front.common.annotation.RequiresPermissions;
import com.zenith.front.common.constant.OperateConstant;
import com.zenith.front.model.dto.OrgPartyDTO;
import com.zenith.front.model.dto.OrgPartyListDTO;
import com.zenith.front.model.message.InMessage;
import com.zenith.front.model.message.OutMessage;
import com.zenith.front.model.message.Status;
import com.zenith.front.model.validate.annotation.Validate;
import com.zenith.front.model.validate.group.Common6Group;
import com.zenith.front.web.controller.BaseController;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.constraints.NotBlank;

/**
 * <AUTHOR>
 * @version 1.0
 * @Description: 党组
 * @date 2021-10-05
 */
@RestController
@RequestMapping("/org/party")
public class OrgPartyController extends BaseController {

    @Resource
    private IOrgPartyService orgPartyService;

    /**
     * 获取党小组
     *
     * @param inMessage 查询参数
     * @return
     */
    @PostMapping("/getList")
    @Validate
    @RequiresPermissions
    public OutMessage getList(@RequestBody InMessage<OrgPartyListDTO> inMessage) {
        return orgPartyService.getList(inMessage.getData());
    }

    /**
     * 新增党小组
     *
     * @param inMessage
     * @return
     */
    @PostMapping("/addParty")
    @Validate(group = Common6Group.class)
    @RequiresPermissions(opt = OperateConstant.INSERT)
    public OutMessage addParty(@RequestBody InMessage<OrgPartyDTO> inMessage) {
        OrgPartyDTO orgPartyDTO = inMessage.getData();
        if (!orgPartyDTO.getPartyOrgCode().startsWith(this.getCurrManOrgCode())) {
            return new OutMessage<>(Status.PERMISSION_DENIED);
        }
        return orgPartyService.addParty(orgPartyDTO);
    }

    /**
     * 根据code查找党小组
     *
     * @param code
     * @return
     */
    @GetMapping("/findByCode")
    @Validate
    @RequiresPermissions
    public OutMessage findByCode(@NotBlank(message = "code 不能为空") String code) {
        return orgPartyService.findByCode(code);
    }

    /**
     * 修改党小组
     *
     * @param inMessage
     * @return
     */
    @RequestMapping("/updateParty")
    @Validate(group = Common6Group.class)
    @RequiresPermissions(opt = OperateConstant.UPDATE)
    public OutMessage updateParty(@RequestBody InMessage<OrgPartyDTO> inMessage) throws Exception {
        OrgPartyDTO orgPartyDTO = inMessage.getData();
        if (!orgPartyDTO.getPartyOrgCode().startsWith(this.getCurrManOrgCode())) {
            return new OutMessage<>(Status.PERMISSION_DENIED);
        }
        return orgPartyService.updateParty(orgPartyDTO);
    }

    /**
     * 删除党小组
     *
     * @param code
     * @return
     */
    @GetMapping("/delParty")
    @Validate
    @RequiresPermissions(opt = OperateConstant.DELETE)
    public OutMessage delParty(@NotBlank(message = "code 不能为空") String code) {
        return orgPartyService.delParty(code);
    }


    /**
     *导出
     */
    @PostMapping("/export")
    @RequiresPermissions
    public OutMessage export(@RequestBody InMessage<OrgPartyListDTO> inMessage) throws Exception {
        return orgPartyService.export(inMessage.getData());
    }
}
