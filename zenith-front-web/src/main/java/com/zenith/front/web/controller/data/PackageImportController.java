package com.zenith.front.web.controller.data;

import com.zenith.front.api.dataexchange.IDataExchangeService;
import com.zenith.front.common.annotation.RequiresPermissions;
import com.zenith.front.model.message.OutMessage;
import com.zenith.front.model.message.Status;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * 仅用于内网，并且存在ccp_data_exchange表时使用此类
 */
@RestController
@RequestMapping("/data")
public class PackageImportController {

    @Resource
    IDataExchangeService dataExchangeService;

    /**
     * 查询内网数据包导入的最后更新时间
     */
    @RequiresPermissions
    @PostMapping("/lastUpdateTime")
    public OutMessage<Object> lastUpdateTime() {
        String dateStr = dataExchangeService.lastUpdateTime();
        return new OutMessage<>(Status.SUCCESS, dateStr);
    }
}
