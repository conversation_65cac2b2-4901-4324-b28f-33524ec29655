package com.zenith.front.web.scheduled;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;
import com.zenith.front.api.unit.IUnitCountrusideService;
import com.zenith.front.core.feign.client.VillageCommunityClient;
import com.zenith.front.core.feign.dto.PushUnitCountrysideDTO;
import com.zenith.front.core.feign.dto.VcUnitCountryside;
import com.zenith.front.model.bean.UnitCountryside;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.condition.ConditionalOnExpression;
import org.springframework.context.annotation.Configuration;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

@Slf4j
@Configuration
@ConditionalOnExpression("${vc.feign.enabled:true}&&${vc.unittest.open:true}")
public class PullVcUnitCountrysideTask extends AbstractSchedulingConfigurer {

    @Value("${vc.unit.cron}")
    private String cron;
    @Resource
    private IUnitCountrusideService iUnitCountrusideService;
    @Resource
    private VillageCommunityClient villageCommunityClient;

    @Override
    public void task() {
        List<UnitCountryside> list = iUnitCountrusideService.list();
        if (CollUtil.isEmpty(list)) {
            return;
        }
        List<VcUnitCountryside> result = new ArrayList<>();
        for (UnitCountryside e : list) {
            VcUnitCountryside vc = new VcUnitCountryside();
            BeanUtils.copyProperties(e, vc);
            vc.setCreateTimeStr(Objects.nonNull(e.getCreateTime()) ? DateUtil.format(e.getCreateTime(), DatePattern.NORM_DATETIME_PATTERN) : "");
            vc.setUpdateTimeStr(Objects.nonNull(e.getUpdateTime()) ? DateUtil.format(e.getUpdateTime(), DatePattern.NORM_DATETIME_PATTERN) : "");
            vc.setBirthday(Objects.nonNull(e.getBirthday()) ? DateUtil.format(e.getBirthday(), DatePattern.NORM_DATETIME_PATTERN) : "");
            vc.setTimestamp(Objects.nonNull(e.getTimestamp()) ? DateUtil.format(e.getTimestamp(), DatePattern.NORM_DATETIME_PATTERN) : "");
            vc.setLeaveTime(Objects.nonNull(e.getLeaveTime()) ? DateUtil.format(e.getLeaveTime(), DatePattern.NORM_DATETIME_PATTERN) : "");
            vc.setDeleteTime(Objects.nonNull(e.getDeleteTime()) ? DateUtil.formatDate(e.getDeleteTime()) : "");
            result.add(vc);
        }
        PushUnitCountrysideDTO dto = new PushUnitCountrysideDTO();
        dto.setList(result);
        dto.setNginxKey(exchangeNginxKey);
        villageCommunityClient.pushUnitCountryside(dto);

    }

    @Override
    public String cron() {
        return cron;
    }
}
