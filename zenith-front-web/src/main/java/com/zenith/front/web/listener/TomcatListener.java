package com.zenith.front.web.listener;

import org.springframework.boot.system.ApplicationHome;
import org.springframework.context.annotation.Profile;
import org.springframework.stereotype.Component;

import javax.servlet.ServletContextEvent;
import javax.servlet.ServletContextListener;
import java.io.File;

/**
 * <AUTHOR>
 */
@Component
@Profile("prod")
public class TomcatListener implements ServletContextListener {

    /**
     * 初始化tomcat时执行
     *
     * @param servletContextEvent
     */
    @Override
    public void contextInitialized(ServletContextEvent servletContextEvent) {
        ApplicationHome home = new ApplicationHome(this.getClass());
        File jarF = home.getSource();
        File parentFile = jarF.getParentFile().getParentFile();
        File file = new File(parentFile + File.separator + "public");
        //判断该文件夹是否存在，存在就删除，不存在就创建
        if (!file.isDirectory()) {
            file.mkdirs();
        }
    }
}
