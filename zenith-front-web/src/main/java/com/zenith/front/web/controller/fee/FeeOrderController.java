package com.zenith.front.web.controller.fee;

import com.zenith.front.api.fee.IFeeOrderService;
import com.zenith.front.common.annotation.RequiresPermissions;
import com.zenith.front.model.dto.FeeListDTO;
import com.zenith.front.model.message.InMessage;
import com.zenith.front.model.message.OutMessage;
import com.zenith.front.model.message.Status;
import com.zenith.front.model.validate.annotation.Validate;
import com.zenith.front.model.validate.group.Common4Group;
import com.zenith.front.model.validate.group.Common8Group;
import com.zenith.front.web.controller.BaseController;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @version 1.0
 * @Description: 党费订单
 * @date 2019/5/16 10:19
 */
@RestController
@RequestMapping("/fee/order")
public class FeeOrderController extends BaseController {

    @Resource
    private IFeeOrderService feeOrderService;

    /**
     * 获取党费详情列表
     *
     * @param inMessage
     * @return
     */
    @Validate(group = Common8Group.class)
    @RequiresPermissions
    @PostMapping("/getList")
    public OutMessage getList(@RequestBody InMessage<FeeListDTO> inMessage) {
        FeeListDTO feeListDTO = inMessage.getData();
        if (!feeListDTO.getMemOrgOrgCode().startsWith(this.getCurrManOrgCode())) {
            return new OutMessage<>(Status.PERMISSION_DENIED);
        }
        return feeOrderService.getList(feeListDTO);
    }

    /**
     * 获取党费缴纳情况
     *
     * @param inMessage
     * @return
     */
    @Validate(group = Common8Group.class)
    @RequiresPermissions
    @PostMapping("/paymentSituation")
    public OutMessage paymentSituation(@RequestBody InMessage<FeeListDTO> inMessage) {
        FeeListDTO feeListDTO = inMessage.getData();
        if (!feeListDTO.getMemOrgOrgCode().startsWith(this.getCurrManOrgCode())) {
            return new OutMessage<>(Status.PERMISSION_DENIED);
        }
        return feeOrderService.paymentSituation(feeListDTO);
    }


    ///**
    // * 获取微信支付二维码
    // * // TODO: 2019/5/17 缴费成功后更新人员表最后缴费时间数据
    // *
    // * @return
    // */
    //@Validate(group = Common1Group.class)
    //@RequiresPermissions
    //public OutMessage getWxpayQRCode(@RequestBody InMessage<PayDTO> inMessage) {
    //    // 获取自定义商户订单号
    //    String outTradeNo = WxPayUtil.getOutTradeNo();
//
    //    // 业务处理
    //    PayDTO payDTO = inMessage.getData();
    //    BigDecimal money = payDTO.getMoney().setScale(2, RoundingMode.HALF_UP);
    //    List<FeeOrderDTO> feeOrderDTOList = payDTO.getFeeOrderDTOList();
    //    if (feeOrderDTOList == null || feeOrderDTOList.size() == CommonConstant.ZERO_INT) {
    //        return new OutMessage<>(Status.NOT_CHOOSE_MEM);
    //    }
    //    // 比较缴费金额
    //    BigDecimal total = feeOrderDTOList.stream().map(FeeOrderDTO::getMoney)
    //            .reduce(BigDecimal.ZERO, BigDecimal::add).setScale(2, RoundingMode.HALF_UP);
    //    if (!money.equals(total)) {
    //        return new OutMessage<>(Status.PAY_MONEY_ERROR);
    //    }
//
    //    BigDecimal zero = new BigDecimal(0).setScale(2, RoundingMode.HALF_UP);
    //    if (money.compareTo(zero) == 0) {
    //        // 不需要走微信接口
    //        OutMessage saveFeeOrder = this.saveFeeOrder(feeOrderDTOList, outTradeNo);
    //        if (saveFeeOrder.getCode() != CommonConstant.ZERO_INT) {
    //            return saveFeeOrder;
    //        }
    //        return feeOrderService.freeMemPay(outTradeNo, outTradeNo, new Date(), CommonConstant.TWO_INT);
    //    }
//
    //    // 执行业务
    //    OutMessage saveFeeOrder = this.saveFeeOrder(feeOrderDTOList, outTradeNo);
    //    if (saveFeeOrder.getCode() != CommonConstant.ZERO_INT) {
    //        return saveFeeOrder;
    //    }
//
    //    // 获取支付二维码支付
    //    return this.getWxPayUrl(money, outTradeNo);
    //}
//
    ///**
    // * 保存人员党费订单信息
    // *
    // * @param feeOrderDTOList 订单集合
    // * @param outTradeNo      自定义商户订单号
    // * @return
    // */
    //private OutMessage saveFeeOrder(@RequestBody List<FeeOrderDTO> feeOrderDTOList, String outTradeNo) {
    //    return feeOrderService.saveFeeOrder(feeOrderDTOList, outTradeNo);
    //}

    ///**
    // * 获取支付二维码支付
    // *
    // * @param money
    // * @param outTradeNo
    // * @return
    // */
    //private OutMessage getWxPayUrl(BigDecimal money, String outTradeNo) {
    //    // 商品总金额
    //    int totalFee = WxPayUtil.payMoneyToInt(money);
    //    // 商品id
    //    String productId = WxPayUtil.getProductId();
    //    Map<String, String> resultMap = WxPayUtil.pcModeTwo("党费缴纳", productId, totalFee, this.getIp(), outTradeNo);
    //
    //    // 检查返回结果
    //    OutMessage outMessage = WxPayUtil.checkResultMap(resultMap);
    //    if (outMessage != null) {
    //        return outMessage;
    //    }
    //
    //    Map<String, String> map = new HashMap<>(2);
    //    map.put("codeUrl", resultMap.get("code_url"));
    //    // trade_type为NATIVE是有返回，可将该参数值生成二维码展示出来进行扫码支付
    //    map.put("outTradeNo", outTradeNo);
    //    map.put("QRCode", "1");
    //    return new OutMessage<>(Status.SUCCESS, map);
    //}

    ///**
    // * 微信支付回掉地址
    // *
    // * @throws IOException
    // */
    //public void wxPayNotifyUrl() throws IOException {
    //    HttpServletRequest request = this.getRequest();
    //    HttpServletResponse response = this.getResponse();
    //    boolean flag = WxPayUtil.wxPayNotify(request, function -> {
    //        //判断签名是否正确
    //        if (PaymentKit.verifyNotify(function, WxPayUtil.paternerKey)) {
    //            System.out.println("签名正确");
    //            if (CommonConstant.SUCCESS.equals(function.get("result_code"))) {
    //                // 支付成功,业务处理
    //                // 获取支付时间
    //                DateTime payDate = DateUtil.parse(function.get("time_end"), "yyyyMMddHHmmss");
    //                // 获取微信支付订单号
    //                String transactionId = function.get("transaction_id");
    //                // 商户订单号
    //                String outTradeNo = function.get("out_trade_no");
    //                // 数据处理
    //                OutMessage outMessage = feeOrderService.processData(outTradeNo, transactionId, payDate, CommonConstant.ONE_INT);
    //                return outMessage.getCode() == Status.SUCCESS.getCode();
    //            } else {
    //                return false;
    //            }
    //        }
    //        System.out.println("签名不正确");
    //        return false;
    //    });
//
    //    // 通知微信
    //    WxPayUtil.wxPayNotifyState(response, flag);
    //    renderNull();
    //}

    ///**
    // * 查询微信扫码支付订单状态
    // *
    // * @param outTradeNo 商户订单号
    // * @return
    // */
    //@Validate
    //@RequiresPermissions
    //public OutMessage wxPayOrderQuery(@NotBlank(message = "outTradeNo 不能为空") String outTradeNo) {
    //    Map<String, String> map = new HashMap<>(2);
//
    //    Map<String, String> resultMap = WxPayUtil.orderQueryByOutTradeNo(outTradeNo);
    //    // 检查返回结果
    //    OutMessage outMessage = WxPayUtil.checkResultMap(resultMap);
    //    if (outMessage != null) {
    //        map.put("success", "0");
    //        map.put("message", outMessage.getMessage());
    //        return new OutMessage<>(Status.SUCCESS, map);
    //    }
//
    //    String tradeState = resultMap.get("trade_state");
    //    if (StrKit.isBlank(tradeState) || !CommonConstant.SUCCESS.equals(tradeState)) {
    //        map.put("success", "0");
    //        map.put("message", tradeState);
    //        return new OutMessage<>(Status.SUCCESS, map);
    //    }
//
    //    // 订单支付成功
    //    // 微信订单号
    //    String transactionId = resultMap.get("transaction_id");
    //    // 支付完成时间
    //    String timeEnd = resultMap.get("time_end");
    //    DateTime payDate = DateUtil.parse(timeEnd, "yyyyMMddHHmmss");
    //    // 业务处理
    //    OutMessage processData = feeOrderService.processData(outTradeNo, transactionId, payDate, CommonConstant.TWO_INT);
    //    if (processData.getCode() == CommonConstant.ZERO_INT) {
    //        map.put("success", "1");
    //        map.put("message", "操作成功");
    //        return new OutMessage<>(Status.SUCCESS, map);
    //    } else {
    //        map.put("success", "0");
    //        map.put("message", processData.getMessage());
    //        return new OutMessage<>(Status.SUCCESS, map);
    //    }
    //}

    /**
     * 获取党费缴纳统计项 已缴纳金额,未缴纳金额
     *
     * @return
     */
    @Validate(group = Common4Group.class)
    @RequiresPermissions
    @PostMapping("/getPayTotalList")
    public OutMessage getPayTotalList(@RequestBody InMessage<FeeListDTO> inMessage) {
        FeeListDTO feeListDTO = inMessage.getData();
        if (!feeListDTO.getMemOrgOrgCode().startsWith(this.getCurrManOrgCode())) {
            return new OutMessage<>(Status.PERMISSION_DENIED);
        }
        return feeOrderService.getPayTotalList(feeListDTO);
    }

}
