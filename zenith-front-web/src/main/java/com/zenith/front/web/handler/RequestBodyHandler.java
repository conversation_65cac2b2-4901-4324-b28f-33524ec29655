package com.zenith.front.web.handler;

import com.zenith.front.common.annotation.ExcludeHttpBodyDecrypt;
import com.zenith.front.common.untils.AESUntil;
import com.zenith.front.common.context.PostParameterContextHolder;
import io.micrometer.core.instrument.util.IOUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.MethodParameter;
import org.springframework.http.HttpInputMessage;
import org.springframework.http.converter.HttpMessageConverter;
import org.springframework.web.bind.annotation.ControllerAdvice;
import org.springframework.web.servlet.mvc.method.annotation.RequestBodyAdvice;

import java.io.IOException;
import java.lang.reflect.Type;

import org.springframework.http.HttpHeaders;

import java.io.ByteArrayInputStream;
import java.io.InputStream;

/**
 * @author: D.watermelon
 * @date: 2022/7/6 11:07
 * @description: 入则恳恳以尽忠 出则谦谦以自悔
 */

@ControllerAdvice
@Slf4j
public class RequestBodyHandler implements RequestBodyAdvice {

    @Override
    public boolean supports(MethodParameter methodParameter, Type type, Class<? extends HttpMessageConverter<?>> aClass) {
        //排除解密注解
        boolean methodHasExcludeHttpBodyDecrypt = methodParameter.hasMethodAnnotation(ExcludeHttpBodyDecrypt.class);
        return !methodHasExcludeHttpBodyDecrypt;
    }

    @Override
    public HttpInputMessage beforeBodyRead(HttpInputMessage httpInputMessage, MethodParameter methodParameter, Type type, Class<? extends HttpMessageConverter<?>> aClass) throws IOException {
        if (httpInputMessage.getBody().available() <= 0) {
            return httpInputMessage;
        }
        byte[] requestDataByteNew = null;
        // 解密
        try {
            String bodyText = IOUtils.toString(httpInputMessage.getBody());
            String aesDecryptBoty = AESUntil.checkData(bodyText);
            requestDataByteNew = aesDecryptBoty.getBytes();
            //解密后的参数放入上下文
            PostParameterContextHolder.set(aesDecryptBoty);
        } catch (Exception e) {
            e.printStackTrace();
        }

        // 使用解密后的数据，构造新的读取流
        InputStream rawInputStream = new ByteArrayInputStream(requestDataByteNew);
        return new HttpInputMessage() {
            @Override
            public HttpHeaders getHeaders() {
                return httpInputMessage.getHeaders();
            }

            @Override
            public InputStream getBody() {
                return rawInputStream;
            }
        };
    }

    @Override
    public Object afterBodyRead(Object body, HttpInputMessage httpInputMessage, MethodParameter methodParameter, Type type, Class<? extends HttpMessageConverter<?>> aClass) {
        return body;
    }

    @Override
    public Object handleEmptyBody(Object body, HttpInputMessage httpInputMessage, MethodParameter methodParameter, Type type, Class<? extends HttpMessageConverter<?>> aClass) {
        return body;
    }

}
