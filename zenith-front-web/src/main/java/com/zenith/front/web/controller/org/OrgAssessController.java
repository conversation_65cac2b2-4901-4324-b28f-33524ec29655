package com.zenith.front.web.controller.org;

import com.zenith.front.api.org.IOrgAssessService;
import com.zenith.front.common.annotation.RequiresPermissions;
import com.zenith.front.common.constant.OperateConstant;
import com.zenith.front.model.dto.OrgAssessDTO;
import com.zenith.front.model.dto.OrgAssessListDTO;
import com.zenith.front.model.message.InMessage;
import com.zenith.front.model.message.OutMessage;
import com.zenith.front.model.validate.group.AddGroup;
import com.zenith.front.web.controller.BaseController;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.constraints.NotBlank;

/**
 * <AUTHOR>
 * 考核信息
 * @Date 2021/8/23 10:32
 * @Version 1.0
 */
@RestController
@RequestMapping("/org/assess")
public class OrgAssessController extends BaseController {

    @Resource
    private IOrgAssessService assessService;

    /**
     * 新增或修改考核信息
     */
    @PostMapping("/addOrUpdateAssess")
    @RequiresPermissions(opt = OperateConstant.INSERT)
    public OutMessage<?> addOrUpdateAssess(@Validated(value = AddGroup.class) @RequestBody InMessage<OrgAssessDTO> inMessage) throws Exception {
        OrgAssessDTO orgDTO = inMessage.getData();
        return assessService.addAssess(orgDTO);
    }

    /**
     * 删除考核信息
     */
    @GetMapping("/deleteAssess")
    @RequiresPermissions(opt = OperateConstant.DELETE)
    public OutMessage<?> deleteAssess(@NotBlank(message = "code 不能为空") String code){
        return assessService.deleteAssess(code);
    }

    /**
     * 考核信息详情
     */
    @GetMapping("/detailsAssess")
    @RequiresPermissions(opt = OperateConstant.INSERT)
    public OutMessage<?> detailsAssess(@NotBlank(message = "code 不能为空") String code){
        return assessService.detailsAssess(code);
    }

    /**
     * 考核信列表
     */
    @PostMapping("/getListAssess")
    @RequiresPermissions(opt = OperateConstant.QUERY)
    public OutMessage<?> getListAssess(@Validated(value = AddGroup.class) @RequestBody InMessage<OrgAssessListDTO> inMessage){
        OrgAssessListDTO data = inMessage.getData();
        return assessService.getListAssess(data);
    }
}
