package com.zenith.front.web.controller.mem;

import com.zenith.front.api.org.IOrgReviewersService;
import com.zenith.front.common.annotation.RequiresPermissions;
import com.zenith.front.common.constant.OperateConstant;
import com.zenith.front.model.dto.MemReviewersDTO;
import com.zenith.front.model.message.InMessage;
import com.zenith.front.model.message.OutMessage;
import com.zenith.front.web.controller.BaseController;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.constraints.Max;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * 民主评议-党员
 * @date 2021/08/16 15:32
 */
@Deprecated
@RestController
@RequestMapping("/mem/reviewers")
public class MemReviewersController extends BaseController {

    @Resource
    private IOrgReviewersService orgReviewersService;

    /**
     * 民主评议组织操作—添加评议人员
     */
    @PostMapping("/save")
    @RequiresPermissions(opt = OperateConstant.INSERT)
    public OutMessage save(@RequestBody InMessage<MemReviewersDTO> inMessage) {
        return orgReviewersService.doSave(inMessage.getData());
    }

    /**
     * 民主评议组织操作—删除评议人员
     */
    @GetMapping("/remove")
    @RequiresPermissions(opt = OperateConstant.DELETE)
    public OutMessage remove(String code) {
        return orgReviewersService.doRemove(code);
    }

    /**
     * 民主评议组织操作—修改评议人员
     */
    @PostMapping("/update")
    @RequiresPermissions(opt = OperateConstant.UPDATE)
    public OutMessage update(@RequestBody InMessage<MemReviewersDTO> inMessage) {
        return orgReviewersService.doUpdate(inMessage.getData());
    }

    /**
     * 民主评议组织操作—查询评议人员
     */
    @GetMapping("/list")
    @RequiresPermissions(opt = OperateConstant.QUERY)
    public OutMessage list(@Min(value = 1, message = "页码最小为1") int pageNum,
                           @Max(value = 100, message = "页大小范围在1-100") int pageSize,
                           @NotNull(message = "code 不能为空") String memCode) {
        return orgReviewersService.doList(pageNum, pageSize, memCode);
    }

    /**
     * 民主评议组织操作—查询评议人员
     */
    @GetMapping("/year")
    @RequiresPermissions(opt = OperateConstant.QUERY)
    public OutMessage year(@NotNull(message = "code 不能为空") String memCode) {
        return orgReviewersService.year(memCode);
    }
}
