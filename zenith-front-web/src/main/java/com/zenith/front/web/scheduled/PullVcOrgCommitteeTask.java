package com.zenith.front.web.scheduled;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.http.HttpUtil;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.zenith.front.api.mem.IMemAllInfoService;
import com.zenith.front.api.org.IOrgCommitteeService;
import com.zenith.front.api.unit.IUnitOrgLinkedService;
import com.zenith.front.core.feign.dto.PushCommitteeInfo;
import com.zenith.front.core.feign.dto.PushInfo;
import com.zenith.front.model.bean.MemAllInfo;
import com.zenith.front.model.bean.OrgCommittee;
import com.zenith.front.model.bean.UnitOrgLinked;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 定时推送村社区系统用户到中间交换区
 *
 * <AUTHOR>
 */
@Slf4j
@Component
public class PullVcOrgCommitteeTask extends AbstractSchedulingConfigurer {

    private String cron;
    @Resource
    private IOrgCommitteeService orgCommitteeService;
    @Resource
    private IUnitOrgLinkedService unitOrgLinkedService;
    @Resource
    private IMemAllInfoService memAllService;

    @Override
    public void task() {
        List<OrgCommittee> list = orgCommitteeService.findAllBySyncVillager();
        if (CollUtil.isEmpty(list)) {
            return;
        }
        List<String> memCodeList = list.stream().map(OrgCommittee::getMemCode)
                .filter(StrUtil::isNotBlank)
                .collect(Collectors.toList());
        //查找人员
        LambdaQueryWrapper<MemAllInfo> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.select(MemAllInfo::getCode, MemAllInfo::getSexCode,
                MemAllInfo::getBirthday, MemAllInfo::getIdcard, MemAllInfo::getName);
        queryWrapper.in(MemAllInfo::getCode, memCodeList);
        Map<String, MemAllInfo> memInfo = memAllService.list(queryWrapper).stream()
                .collect(Collectors.toMap(MemAllInfo::getCode, memAllInfo -> memAllInfo, (k1, k2) -> k2));

        // 关联单位
        List<String> orgCodeList = list.stream().map(OrgCommittee::getOrgCode).filter(StrUtil::isNotBlank).collect(Collectors.toList());
        LambdaQueryWrapper<UnitOrgLinked> queryWrapperLinked = new LambdaQueryWrapper<>();
        queryWrapperLinked.select(UnitOrgLinked::getOrgCode, UnitOrgLinked::getUnitCode);
        queryWrapperLinked.eq(UnitOrgLinked::getIsUnitMain, 1);
        queryWrapperLinked.in(UnitOrgLinked::getOrgCode, orgCodeList);
        Map<String, UnitOrgLinked> unitOrgLinkedMap = unitOrgLinkedService.list(queryWrapperLinked).stream()
                .collect(Collectors.toMap(UnitOrgLinked::getOrgCode, memAllInfo -> memAllInfo, (k1, k2) -> k2));

        List<PushCommitteeInfo> committeeInfoList = new ArrayList<>();

        for (OrgCommittee unitCommittee : list) {
            PushCommitteeInfo pushCommitteeInfo = new PushCommitteeInfo();
            pushCommitteeInfo.setId(unitCommittee.getCode());
            pushCommitteeInfo.setName(unitCommittee.getMemName());
            if (StrUtil.isBlank(unitCommittee.getMemCode())) {
                // 输入人员
                pushCommitteeInfo.setIdCard(unitCommittee.getMemIdcard());
                pushCommitteeInfo.setSexCode(unitCommittee.getSexCode());
                Date birthday = unitCommittee.getBirthday();
                if (Objects.nonNull(birthday)) {
                    pushCommitteeInfo.setBirthdayStr(DateUtil.format(birthday, DatePattern.NORM_DATE_PATTERN));
                }
            } else {
                MemAllInfo memAllInfo = memInfo.get(unitCommittee.getMemCode());
                if (Objects.nonNull(memAllInfo)) {
                    pushCommitteeInfo.setIdCard(memAllInfo.getIdcard());
                    pushCommitteeInfo.setSexCode(memAllInfo.getSexCode());
                    pushCommitteeInfo.setName(memAllInfo.getName());
                    Date birthday = memAllInfo.getBirthday();
                    if (Objects.nonNull(birthday)) {
                        pushCommitteeInfo.setBirthdayStr(DateUtil.format(birthday, DatePattern.NORM_DATE_PATTERN));
                    }
                }
            }
            UnitOrgLinked unitOrgLinked = unitOrgLinkedMap.get(unitCommittee.getOrgCode());
            if (Objects.nonNull(unitOrgLinked)) {
                pushCommitteeInfo.setUnitId(unitOrgLinked.getUnitCode());
                pushCommitteeInfo.setUnitCode(unitOrgLinked.getUnitCode());
            }

            pushCommitteeInfo.setOrgCode(unitCommittee.getOrgCode());
            pushCommitteeInfo.setJobLevel(unitCommittee.getD022Code());
            Date startDate = unitCommittee.getStartDate();
            if (Objects.nonNull(startDate)) {
                pushCommitteeInfo.setJobLevelStartDateStr(DateUtil.format(startDate, DatePattern.NORM_DATE_PATTERN));
            }
            committeeInfoList.add(pushCommitteeInfo);
        }

        int pageSize = 1000;
        int num = (committeeInfoList.size() - 1) / pageSize + 1;

        for (int i = 1; i <= num; i++) {
            List<PushCommitteeInfo> collect = committeeInfoList.stream().skip((long) (i - 1) * pageSize)
                    .limit(pageSize).collect(Collectors.toList());
            PushInfo pushInfo = new PushInfo();
            pushInfo.setList(collect);
            pushInfo.setFirst(i == 1);
            pushInfo.setLast(i == num);
            String post = HttpUtil.post(PullVcUnitCommitteeTask.pushUrl() + "/syncCommitteeInfo/receiveDnA01",
                    JSONUtil.toJsonStr(PullVcUnitCommitteeTask.cryptoData(pushInfo)));
            log.info("返回信息：{}", post);
        }

        log.info("已推送机构委员信息:{}条", committeeInfoList.size());
    }

    @Override
    public String cron() {
        // return "0/20 * * * * ?";
        return "0 45 2 * * ?";
    }
}
