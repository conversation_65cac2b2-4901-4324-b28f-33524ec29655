package com.zenith.front.web.controller.chars;

import com.zenith.front.api.mem.IMemService;
import com.zenith.front.common.annotation.RequiresPermissions;
import com.zenith.front.model.message.OutMessage;
import com.zenith.front.model.validate.annotation.Validate;
import com.zenith.front.web.controller.BaseController;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * 代办事项
 * @Date 2021/9/13 13:54
 * @Version 1.0
 */
@RestController
@RequestMapping("/chart/schedule")
public class ScheduleController extends BaseController {
    @Resource
    private IMemService memService;


    /**
     * 查看代办事项
     */
    @GetMapping("/getSchedule")
    @Validate
    @RequiresPermissions
    public OutMessage getSchedule() {
        return memService.getSchedule();
    }
}
