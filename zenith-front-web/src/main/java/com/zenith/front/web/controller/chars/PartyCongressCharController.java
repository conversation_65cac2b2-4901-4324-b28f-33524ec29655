package com.zenith.front.web.controller.chars;

import com.zenith.front.api.org.IOrgPartyCongressCommitteeService;
import com.zenith.front.common.annotation.RequiresPermissions;
import com.zenith.front.model.dto.ChartDataDTO;
import com.zenith.front.model.message.InMessage;
import com.zenith.front.model.message.OutMessage;
import com.zenith.front.web.controller.BaseController;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.validation.Valid;

/**
 * 党代表概括
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/chart/party/congress")
@Validated
public class PartyCongressCharController extends BaseController {

    @Resource
    private IOrgPartyCongressCommitteeService orgPartyCongressCommitteeService;

    /**
     * 获取男女比例
     */
    @RequiresPermissions
    @PostMapping("/getSexRatioTotal")
    public OutMessage<Object> getSexRatioTotal(@Valid @RequestBody InMessage<ChartDataDTO> inMessage) {
        return orgPartyCongressCommitteeService.getSexRatioTotal(inMessage.getData());
    }

    /**
     * 获取学历比例
     */
    @RequiresPermissions
    @PostMapping("/getEducationRatioTotal")
    public OutMessage<Object> getEducationRatioTotal(@Valid @RequestBody InMessage<ChartDataDTO> inMessage) {
        return orgPartyCongressCommitteeService.getEducationRatioTotal(inMessage.getData());
    }
}
