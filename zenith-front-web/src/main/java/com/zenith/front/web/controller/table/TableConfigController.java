package com.zenith.front.web.controller.table;

import com.zenith.front.api.codetable.ICodeTableColService;
import com.zenith.front.api.codetable.ICodeTableConfigService;
import com.zenith.front.api.codetable.ICodeTableService;
import com.zenith.front.api.dict.IDictService;
import com.zenith.front.common.kit.StrKit;
import com.zenith.front.core.cache.CacheUtils;
import com.zenith.front.model.custom.Record;
import com.zenith.front.model.dto.CodeTableColDTO;
import com.zenith.front.model.dto.CodeTableConfigDTO;
import com.zenith.front.model.dto.DicDTO;
import com.zenith.front.model.message.InMessage;
import com.zenith.front.model.message.OutMessage;
import com.zenith.front.model.message.Status;
import com.zenith.front.web.controller.BaseController;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;


/**
 * @author: <PERSON><PERSON>watermelon
 * @date: 2021/6/17 15:08
 * @description: 入则恳恳以尽忠 出则谦谦以自悔
 */
@RestController
@RequestMapping("/table")
public class TableConfigController extends BaseController {

    @Resource
    private ICodeTableService codeTableService;
    @Resource
    private ICodeTableColService codeTableColService;
    @Resource
    private IDictService dictService;
    @Resource
    private ICodeTableConfigService tableConfigService;

    /**
     * 获取数据字典表分类
     **/
    @GetMapping("/tableSelect")
    public OutMessage tableSelect() {
        return codeTableService.findTableConfig();
    }

    /**
     * 获取字典表字段相关设置
     **/
    @GetMapping("/tableFind")
    public OutMessage tableFind(String id) {
        return codeTableService.tableFind(id);
    }

    /**
     * 修改字典备注
     **/
    @PostMapping("/tableUp")
    public OutMessage tableUp(@RequestBody InMessage<CodeTableColDTO> inMessage) {
        CodeTableColDTO data = inMessage.getData();
        return codeTableColService.tableUp(data);
    }

    /**
     * 获取lable下拉字典
     **/
    @GetMapping("/tableLable")
    public OutMessage tableLable(String dicName) {
        if (StrKit.isBlank(dicName)) {
            return new OutMessage<>(Status.PARA_ERROR);
        }
        List<Record> dictList = CacheUtils.getDic(dicName);
        return new OutMessage<>(Status.SUCCESS, dictList);
    }

    /**
     * 修改字典表信息
     **/
    @PostMapping("/tableUpDic")
    public OutMessage tableUpDic(@RequestBody InMessage<DicDTO> inMessage) {
        DicDTO data = inMessage.getData();
        return dictService.tableUpDic(data);
    }

    /***
     * 获取所有配置得字段信息项，用于校核配置中得信息
     */
    @GetMapping("/tableAll")
    public OutMessage tableAll() {
        return codeTableColService.tableAll();
    }

    /***
     * 新增保存校验配置
     */
    @PostMapping("/tableCompare")
    public OutMessage tableCompare(@RequestBody InMessage<List<CodeTableConfigDTO>> inMessage) {
        List<CodeTableConfigDTO> data = inMessage.getData();
        return tableConfigService.tableCompare(data);
    }

    /***
     *查询配置条件
     */
    @GetMapping("/tableColSecect")
    public OutMessage tableColSecect(String id, String val) {
        return tableConfigService.tableColSecect(id, val);
    }

    /***
     * 获取逻辑配置中字典表界面
     */
    @GetMapping("/tableColonfig")
    public OutMessage tableColonfig(String colId, String dictName) {
        return tableConfigService.tableColonfig(colId, dictName);
    }


    /**
     * 获取前端字典校验逻辑
     */
    @GetMapping("/tableColConfig")
    public OutMessage tableColConfig(String id) {
        return codeTableColService.tableColConfig(id);
    }


    /**
     * 获取数据字典表分类（数据查询模块用）
     **/
    @GetMapping("/query/tableSelect")
    public OutMessage<?> tableSelectNew() {
        return codeTableService.findTableConfigNew();
    }

    /***
     * 获取所有配置得字段信息项（数据查询模块用）
     */
    @GetMapping("/query/tableAll")
    public OutMessage<?> tableAllNew() {
        return codeTableService.tableAllNew();
    }

}
