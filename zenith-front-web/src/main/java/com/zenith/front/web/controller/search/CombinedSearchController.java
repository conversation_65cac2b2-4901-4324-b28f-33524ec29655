package com.zenith.front.web.controller.search;

import cn.hutool.core.util.StrUtil;
import com.zenith.front.api.search.ICombinedSearchService;
import com.zenith.front.common.annotation.RequiresPermissions;
import com.zenith.front.core.kit.ExcelUtil;
import com.zenith.front.model.dto.CombinedSearchDto;
import com.zenith.front.model.dto.SearchMemDto;
import com.zenith.front.model.message.InMessage;
import com.zenith.front.model.message.OutMessage;
import com.zenith.front.model.message.Status;
import com.zenith.front.model.bean.User;
import com.zenith.front.web.controller.BaseController;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;

/**
 * 组合查询
 *
 * <AUTHOR>
 * @date 2022/3/7
 */
@Validated
@RestController
@RequestMapping("/search")
public class CombinedSearchController extends BaseController {
    @Resource
    private ICombinedSearchService iCombinedSearchService;

    /**
     * 党员信息查询
     */
    @PostMapping("/searchMem")
    @RequiresPermissions
    public OutMessage<?> searchMem(@Validated @RequestBody InMessage<SearchMemDto> inMessage) {
        User user = this.getCurrUser().getUser();
        if (StrUtil.equals(inMessage.getData().getType(), "2")) {
            return iCombinedSearchService.searchDevelopStepLog(inMessage.getData(), user);
        }
        return iCombinedSearchService.searchMem(inMessage.getData(), user);
    }


    /**
     * 组合查询
     */
    @PostMapping("/combined")
    @RequiresPermissions
    public OutMessage<?> combined(@Validated @RequestBody InMessage<CombinedSearchDto> inMessage) {
        User user = this.getCurrUser().getUser();
        return iCombinedSearchService.combined(inMessage.getData(), user);
    }

    /**
     * 保存查询方案
     */
    @PostMapping("/savePlan")
    @RequiresPermissions
    public OutMessage<?> savePlan(@Validated @RequestBody InMessage<CombinedSearchDto> inMessage) {
        User user = this.getCurrUser().getUser();
        return iCombinedSearchService.savePlan(inMessage.getData(), user);
    }


    /**
     * 导出分析查询列表
     */
    @RequiresPermissions
    @PostMapping("/exportSearchMem")
    public void exportSearchMem(@Validated @RequestBody InMessage<SearchMemDto> inMessage, HttpServletResponse response) {
        SearchMemDto searchMemDto = inMessage.getData();
        if (StrUtil.isNotEmpty(searchMemDto.getOrgLevelCode()) && !searchMemDto.getOrgLevelCode().startsWith(this.getCurrManOrgCode())) {
            ExcelUtil.exportErrorExcel(response, new OutMessage<>(Status.PERMISSION_DENIED));
            return;
        }
        User user = this.getCurrUser().getUser();
        if (StrUtil.equals(inMessage.getData().getType(), "2")) {
            iCombinedSearchService.exportDevelopStepLogExcel(inMessage.getData(), user);
        } else {
            iCombinedSearchService.exportMemExcel(searchMemDto, user);
        }
    }


    /**
     * 导出组合查询列表
     */
    @RequiresPermissions
    @PostMapping("/exportCombined")
    public void exportCombined(@Validated @RequestBody InMessage<CombinedSearchDto> inMessage, HttpServletResponse response) {
        CombinedSearchDto searchDto = inMessage.getData();
        if (StrUtil.isNotEmpty(searchDto.getOrgLevelCode()) && !searchDto.getOrgLevelCode().startsWith(this.getCurrManOrgCode())) {
            ExcelUtil.exportErrorExcel(response, new OutMessage<>(Status.PERMISSION_DENIED));
            return;
        }
        User user = this.getCurrUser().getUser();
        iCombinedSearchService.exportCombined(searchDto, user);
    }

}
