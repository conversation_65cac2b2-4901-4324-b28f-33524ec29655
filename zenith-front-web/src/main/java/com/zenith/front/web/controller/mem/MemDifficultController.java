package com.zenith.front.web.controller.mem;

import com.zenith.front.api.mem.IMemDifficultService;
import com.zenith.front.common.annotation.RequiresPermissions;
import com.zenith.front.common.constant.OperateConstant;
import com.zenith.front.model.dto.MemDifficultDTO;
import com.zenith.front.model.message.InMessage;
import com.zenith.front.model.message.OutMessage;
import com.zenith.front.model.message.Status;
import com.zenith.front.model.validate.annotation.Validate;
import com.zenith.front.model.validate.group.AddGroup;
import com.zenith.front.web.controller.BaseController;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.constraints.Max;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @version 1.0
 * @Description: 困难党员
 * @date 2019/4/11 16:19
 */
@RestController
@RequestMapping("/mem/difficult")
public class MemDifficultController extends BaseController {
    @Resource
    private IMemDifficultService memDifficultService;

    /**
     * 新增困难党员
     *
     * @param inMessage
     * @return
     */
    @PostMapping("/addMemDifficult")
    @Validate(group = AddGroup.class)
    @RequiresPermissions(opt = OperateConstant.INSERT)
    public OutMessage addMemDifficult(@RequestBody InMessage<MemDifficultDTO> inMessage) throws Exception {
        MemDifficultDTO memDifficultDTO = inMessage.getData();
        String diffOrgCode = memDifficultDTO.getDiffOrgCode();
        if (!diffOrgCode.startsWith(this.getCurrManOrgCode())) {
            return new OutMessage<>(Status.PERMISSION_DENIED);
        }
        return memDifficultService.addMemDifficult(memDifficultDTO);
    }

    /**
     * 根据人员查询困难党员信息
     *
     * @param memCode
     * @return
     */
    @GetMapping("/findByMemCode")
    @Validate
    @RequiresPermissions
    public OutMessage findByMemCode(@NotBlank(message = "memCode 不能为空") String memCode) {
        return memDifficultService.findByMemCodeOut(memCode);
    }

    /**
     * 修改困难党员信息
     *
     * @param inMessage
     * @return
     */
    @PostMapping("/updateMemDifficult")
    @Validate
    @RequiresPermissions(opt = OperateConstant.UPDATE)
    public OutMessage updateMemDifficult(@RequestBody InMessage<MemDifficultDTO> inMessage) throws Exception {
        MemDifficultDTO memDifficultDTO = inMessage.getData();
        String diffOrgCode = memDifficultDTO.getDiffOrgCode();
        if (!diffOrgCode.startsWith(this.getCurrManOrgCode())) {
            return new OutMessage<>(Status.PERMISSION_DENIED);
        }
        return memDifficultService.updateMemDifficult(memDifficultDTO);
    }

    /**
     * 获取困难党员列表
     *
     * @param pageNum
     * @param pageSize
     * @param orgCode
     * @param subordinate 是否显示下级 1 显示 0 不显示
     * @param memName
     * @return
     */
    @GetMapping("/getList")
    @Validate
    @RequiresPermissions
    public OutMessage getList(@Min(value = 1, message = "页码最小为1") int pageNum,
                              @Max(value = 100, message = "页大小范围在1-100") int pageSize,
                              @NotNull(message = "orgCode 不能为空") String orgCode,
                              String subordinate,
                              String memName) {
        if (!orgCode.startsWith(this.getCurrManOrgCode())) {
            return new OutMessage<>(Status.PERMISSION_DENIED);
        }
        return memDifficultService.getList(pageNum, pageSize, orgCode, subordinate, memName);
    }

    /**
     * 删除困难党员
     *
     * @param code
     * @return
     */
    @GetMapping("/delMemDifficult")
    @Validate
    @RequiresPermissions(opt = OperateConstant.DELETE)
    public OutMessage delMemDifficult(@NotBlank(message = "code 不能为空") String code) {
        return memDifficultService.delMemDifficult(code);
    }


}
