package com.zenith.front.web.controller.org;

import com.zenith.front.api.org.IOrgDevelopRightsService;
import com.zenith.front.common.annotation.RequiresPermissions;
import com.zenith.front.common.constant.OperateConstant;
import com.zenith.front.model.dto.CommonParamDto;
import com.zenith.front.model.dto.OrgListDTO;
import com.zenith.front.model.message.InMessage;
import com.zenith.front.model.message.OutMessage;
import com.zenith.front.model.message.Status;
import com.zenith.front.model.validate.group.Common1Group;
import com.zenith.front.web.controller.BaseController;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.constraints.NotBlank;

/**
 * 审批预备党员权限管理
 *
 * <AUTHOR>
 * @date 2021/12/15
 */
@Validated
@RestController
@RequestMapping("/org/develop/rights")
public class OrgDevelopRightsController extends BaseController {

    @Resource
    private IOrgDevelopRightsService iOrgDevelopRightsService;

    /**
     * 获取列表
     */
    @PostMapping("/getList")
    @RequiresPermissions
    public OutMessage<?> getList(@Validated(value = Common1Group.class) @RequestBody InMessage<OrgListDTO> inMessage) {
        OrgListDTO orgListDTO = inMessage.getData();
        if (!orgListDTO.getOrgCode().startsWith(this.getCurrManOrgCode())) {
            return new OutMessage<>(Status.PERMISSION_DENIED);
        }
        return iOrgDevelopRightsService.getList(orgListDTO);
    }

    /**
     * 新增
     */
    @PostMapping("/save")
    @RequiresPermissions(opt = OperateConstant.INSERT)
    public OutMessage<?> save(@RequestBody InMessage<CommonParamDto> inMessage) {
        return iOrgDevelopRightsService.save(inMessage.getData());
    }

    /**
     * 撤销
     */
    @GetMapping("/del")
    @RequiresPermissions
    public OutMessage<?> del(@NotBlank(message = "code 不能为空") String code) {
        return iOrgDevelopRightsService.del(code);
    }

    /**
     * 详情
     * @param orgCode 党组织code
     * @return
     */
    @GetMapping("/find")
    @RequiresPermissions
    public OutMessage<?> find(@NotBlank(message = "orgCode 不能为空") String orgCode) {
        return iOrgDevelopRightsService.find(orgCode);
    }


}
