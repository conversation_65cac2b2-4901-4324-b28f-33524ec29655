package com.zenith.front.web.scheduled;

import cn.hutool.core.collection.CollUtil;
import com.zenith.front.api.mem.IMemDevelopService;
import com.zenith.front.core.feign.client.VillageCommunityClient;
import com.zenith.front.core.feign.dto.PushDevelopMemInfoDTO;
import com.zenith.front.core.feign.dto.VcDevelopMemInfo;
import com.zenith.front.model.bean.MemDevelop;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.condition.ConditionalOnExpression;
import org.springframework.context.annotation.Configuration;

import javax.annotation.Resource;
import java.util.*;

/**
 * 定时推送入党申请人到中间交换区
 *
 * <AUTHOR>
 */
@Slf4j
@Configuration
@ConditionalOnExpression("${vc.feign.enabled:true}&&${vc.develop.open:true}")
public class PullVcDevelopMemTask extends AbstractSchedulingConfigurer {

    @Value("${vc.develop.cron}")
    private String cron;
    @Resource
    private IMemDevelopService memDevelopService;
    @Resource
    private VillageCommunityClient villageCommunityClient;

    @Override
    public void task() {
        List<MemDevelop> memDevelopList = memDevelopService.findMemByD04CodeList(D04_CODE);
        if (CollUtil.isEmpty(memDevelopList)) {
            return;
        }
        List<VcDevelopMemInfo> memInfoList = new ArrayList<>();
        for (MemDevelop memDevelop : memDevelopList) {
            VcDevelopMemInfo developMemInfo = new VcDevelopMemInfo();
            developMemInfo.setCode(memDevelop.getCode());
            developMemInfo.setName(memDevelop.getName());
            developMemInfo.setIdcard(memDevelop.getIdcard());
            developMemInfo.setD06Code(memDevelop.getD06Code());
            developMemInfo.setD06Name(memDevelop.getD06Name());
            developMemInfo.setD48Code(memDevelop.getD48Code());
            developMemInfo.setD48Name(memDevelop.getD48Name());
            developMemInfo.setSexCode(memDevelop.getSexCode());
            developMemInfo.setSexName(memDevelop.getSexName());
            developMemInfo.setPhone(memDevelop.getPhone());
            developMemInfo.setD07Code(memDevelop.getD07Code());
            developMemInfo.setD07Name(memDevelop.getD07Name());
            developMemInfo.setD09Code(memDevelop.getD09Code());
            developMemInfo.setD09Name(memDevelop.getD09Name());
            developMemInfo.setD08Code(memDevelop.getD08Code());
            developMemInfo.setD08Name(memDevelop.getD08Name());
            developMemInfo.setOrgCode(memDevelop.getOrgCode());
            developMemInfo.setOrgName(memDevelop.getOrgName());
            memInfoList.add(developMemInfo);
        }
        PushDevelopMemInfoDTO pushDevelopMemInfoDTO = new PushDevelopMemInfoDTO();
        pushDevelopMemInfoDTO.setList(memInfoList);
        pushDevelopMemInfoDTO.setNginxKey(exchangeNginxKey);
        villageCommunityClient.pushDevelopMemInfo(pushDevelopMemInfoDTO);
        log.info("已推送入党申请人信息到中间交换区:{}", memInfoList.size());
    }

    @Override
    public String cron() {
        return cron;
    }
}
