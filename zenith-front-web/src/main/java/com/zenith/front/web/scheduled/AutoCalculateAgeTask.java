package com.zenith.front.web.scheduled;

import com.zenith.front.api.mem.ICalculateAgeTask;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Configuration;

import javax.annotation.Resource;

/**
 * 计算党员年龄
 *
 * <AUTHOR>
 * @version V1.0
 * @Package com.zenith.front.web.scheduled
 * @date 2022/5/16 15:23
 */
@Slf4j
@Configuration
public class AutoCalculateAgeTask extends AbstractSchedulingConfigurer {

    @Value("${age.calc.open}")
    private Boolean open;
    @Value("${age.calc.cron}")
    private String cron;
    @Resource
    private ICalculateAgeTask calculateAgeTask;

    @Override
    public void task() {
        if (open) {
            calculateAgeTask.calculateMemAge();
            calculateAgeTask.calculateDevelopMemAge();
            calculateAgeTask.calculateDevelopStepLogAge();
        }
    }

    @Override
    public String cron() {
        return cron;
    }
}
