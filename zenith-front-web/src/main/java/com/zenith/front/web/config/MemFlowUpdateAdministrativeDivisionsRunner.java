package com.zenith.front.web.config;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.zenith.front.api.mem.IMemFlow1Service;
import com.zenith.front.api.org.IOrgService;
import com.zenith.front.common.constant.CommonConstant;
import com.zenith.front.model.dto.MemFlowUpdateAdministrativeDivisionsDTO;
import com.zenith.front.model.bean.MemFlow1;
import com.zenith.front.model.bean.Org;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.ApplicationArguments;
import org.springframework.boot.ApplicationRunner;

import javax.annotation.Resource;
import java.time.LocalDate;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

//@Component
@Slf4j
public class MemFlowUpdateAdministrativeDivisionsRunner implements ApplicationRunner {

    @Resource
    IMemFlow1Service memFlow1Service;
    @Resource
    IOrgService orgService;
    @Value("${sync_flow_pull}")
    private String syncFlowPull;

    @Override
    public void run(ApplicationArguments args) throws Exception {
        Org org = orgService.getOne(new LambdaQueryWrapper<Org>().orderByAsc(Org::getOrgCode).last(" LIMIT 1 ").select(Org::getOrgCode));
        String orgCode = org.getOrgCode();
        log.info("修复流动党员,当前节点层级:{}", orgCode);
        List<MemFlow1> list = memFlow1Service.list(new LambdaQueryWrapper<MemFlow1>()
                .eq(MemFlow1::getFlowOut, "1")
                .eq(MemFlow1::getOutPlaceCode, "2")
                .ne(MemFlow1::getFlowStep, "6")
                .ge(MemFlow1::getUpdateTime, DateUtil.beginOfDay(com.zenith.front.common.kit.DateUtil.localeDateToDate(LocalDate.of(2023, 1, 5))))
                .isNotNull(MemFlow1::getOutAdministrativeDivisionCode)
                .likeRight(MemFlow1::getMemOrgOrgCode, orgCode)
        );
        List<String> result = new ArrayList<>();
        String replaceUrl = syncFlowPull.replace("/api/flow/pull", "");

        for (MemFlow1 memFlow1 : list) {
            MemFlowUpdateAdministrativeDivisionsDTO memFlowUpdateAdministrativeDivisionsDTO = new MemFlowUpdateAdministrativeDivisionsDTO();
            memFlowUpdateAdministrativeDivisionsDTO.setCode(memFlow1.getCode());
            memFlowUpdateAdministrativeDivisionsDTO.setOutAdministrativeDivisionCode(memFlow1.getOutAdministrativeDivisionCode());

            try {
                boolean flag = memFlow1Service.updateAdministrativeDivision(memFlowUpdateAdministrativeDivisionsDTO, orgCode, "内部管理员");
                if (!flag) {
                    log.warn("修复流动党员行政区划流动类型失败:{},当前节点层级:{}", memFlowUpdateAdministrativeDivisionsDTO, orgCode);
                }
            } catch (Exception e) {
                result.add(memFlowUpdateAdministrativeDivisionsDTO.getCode());
                log.error("修复流动党员行政区划流动类型失败:{},当前节点层级:{}", memFlowUpdateAdministrativeDivisionsDTO, orgCode);
                if (StrUtil.equals(memFlow1.getFlowStep(), "1")) {
                    memFlow1Service.syncSaveMemFlow(replaceUrl, Collections.singletonList(memFlow1), CommonConstant.ONE, "内部管理员");
                    log.error("同步异常数据到交换区:{},当前节点层级:{}", memFlow1, orgCode);
                }
            }
        }
        if (CollUtil.isNotEmpty(result)) {
            log.error("修复流动党员行政区划流动类型失败:{},当前节点层级:{}", String.join(",", result), orgCode);
        }
        log.info("修复流动党员任务已完成");
    }
}
