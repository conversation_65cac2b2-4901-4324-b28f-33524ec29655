package com.zenith.front.web.controller.mem;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.zenith.front.api.mem.IMemFlowService;
import com.zenith.front.common.annotation.RequiresPermissions;
import com.zenith.front.common.constant.OperateConstant;
import com.zenith.front.common.kit.StrKit;
import com.zenith.front.model.dto.MemFlowDTO;
import com.zenith.front.model.dto.MemFlowListDTO;
import com.zenith.front.model.message.InMessage;
import com.zenith.front.model.message.OutMessage;
import com.zenith.front.model.message.Status;
import com.zenith.front.model.validate.annotation.Validate;
import com.zenith.front.model.validate.group.*;
import com.zenith.front.web.controller.BaseController;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.HashMap;
import java.util.Map;

/**
 * 流动党员
 *
 * @author: D.watermelon
 * @date: 2019/4/16 14:40
 * @description: 入则恳恳以尽忠 出则谦谦以自悔
 */
@RestController
@RequestMapping("/flowmem")
public class MemFlowController extends BaseController {
    @Resource
    private IMemFlowService flowService;


    /**
     * 流动党员新增流出党员接口
     */
    @PostMapping("/addOutMem")
    @Validate(group = AddGroup.class)
    @RequiresPermissions(opt = OperateConstant.INSERT)
    public OutMessage addOutMem(@RequestBody InMessage<MemFlowDTO> inMessage) {
        MemFlowDTO data = inMessage.getData();
        OutMessage validate = validate(data);
        if (ObjectUtil.isNotNull(validate)) {
            return validate;
        }
        String currManOrgCode = this.getCurrManOrgCode();
        String memOrgCode = data.getMemOrgOrgCode();
        if (!memOrgCode.startsWith(currManOrgCode)) {
            return new OutMessage<>(Status.FLOW_OUT_MEM_NO_PERMISSION);
        }
        return flowService.addOutMem(data, 1, currManOrgCode, "out1");
    }

    /**
     * 获取流出党员列表
     */
    @PostMapping("/outMemList")
    @Validate
    @RequiresPermissions
    public OutMessage outMemList(@RequestBody InMessage<MemFlowListDTO> dto) {
        MemFlowListDTO data = dto.getData();
        return flowService.outMemList(data);
    }

    /**
     * 获取单个流出党员详情
     */
    @GetMapping("/findOutMemByCode")
    @Validate
    @RequiresPermissions
    public OutMessage findOutMemByCode(@NotNull(message = "流动信息code不允许为空") String code) {
        return flowService.findOutMemByCodeOut(code);
    }

    /**
     * 党员流回接口
     */
    @PostMapping("/backOutMem")
    @Validate(group = UpdateGroup.class)
    @RequiresPermissions(opt = OperateConstant.UPDATE)
    public OutMessage backOutMem(@RequestBody InMessage<MemFlowDTO> inMessage) throws Exception {
        MemFlowDTO data = inMessage.getData();
        // TODO: 2019/4/18  流回类型为系统内流回的时候,组织名称和组织
        String memCode = data.getMemCode();
        if (StrKit.notBlank(memCode) && !memCode.startsWith(this.getCurrManOrgCode())) {
            return new OutMessage<>(Status.FLOW_OUT_MEM_NO_PERMISSION);
        }
        // String backflowTypeCode = data.getBackflowTypeCode();
        // String backflowOrgCode = data.getBackflowOrgCode();
        // String backflowOrgOrgCode = data.getBackflowOrgOrgCode();
        // if (Integer.parseInt(backflowTypeCode) > 1) {
        //     if (StrKit.isBlank(backflowOrgCode)) {
        //         return new OutMessage<>(Status.FLOW_BACK_ORG_CODE);
        //     }
        //     if (StrKit.isBlank(backflowOrgOrgCode)) {
        //         return new OutMessage<>(Status.FLOW_BACK_ORG_ORG_CODE);
        //     }
        // }
        return flowService.backOutMem(data);
    }

    /**
     * 系统内流入登记
     * 由于人员的选择器改为了 姓名加身份证
     */
    @PostMapping("/addInMem")
    @Validate(group = Common1Group.class)
    @RequiresPermissions(opt = OperateConstant.INSERT)
    public OutMessage addInMem(@RequestBody InMessage<MemFlowDTO> inMessage) {
        MemFlowDTO data = inMessage.getData();
//        String memOrgCode = data.getMemOrgOrgCode();
        String currManOrgCode = this.getCurrManOrgCode();
//        if (!memOrgCode.startsWith(currManOrgCode)) {
//            return new OutMessage<>(Status.FLOW_OUT_MEM_NO_PERMISSION);
//        }
        return flowService.addOutMem(data, 2, currManOrgCode, "in1");
    }

    /**
     * 系统外流入登记
     */
    @PostMapping("/systemInMme")
    @Validate(group = Common2Group.class)
    @RequiresPermissions(opt = OperateConstant.INSERT)
    public OutMessage systemInMme(@RequestBody InMessage<MemFlowDTO> inMessage) {
        //isExplicitInflowOrg 默认为是
        MemFlowDTO data = inMessage.getData();
        data.setIsExplicitInflowOrg(1);//因为是市外流入,所以一定流入目的组织是明确的
        //isProvOut默认为是
        // TODO: 2019/4/18 这里需要配置化
        data.setIsProvOut(1);
        data.setIsProvOutName("跨省（直辖市）流动");
        return flowService.addOutMem(data, 2, this.getCurrManOrgCode(), "in2");
    }

    /**
     * 继续流动接口
     */
    @PostMapping("/continueFlow")
    @Validate(group = Common3Group.class)
    @RequiresPermissions(opt = OperateConstant.UPDATE)
    public OutMessage continueFlow(@RequestBody InMessage<MemFlowDTO> inMessage) throws Exception {
        MemFlowDTO data = inMessage.getData();
        String memOrgOrgCode = data.getMemOrgOrgCode();
        if (StrKit.notBlank(memOrgOrgCode) && !memOrgOrgCode.startsWith(this.getCurrManOrgCode())) {
            return new OutMessage<>(Status.FLOW_OUT_MEM_NO_PERMISSION);
        }

        OutMessage validate = validate(data);
        if (ObjectUtil.isNotNull(validate)) {
            return validate;
        }
        //获取新增市内或者市外转入的接口，然后增加log日志
        return flowService.continueFlow(data);
    }

    /**
     * 流动党员删除接口
     */
    @PostMapping("/delMemFlow")
    @Validate(group = DeleteGroup.class)
    @RequiresPermissions(opt = OperateConstant.DELETE)
    public OutMessage delMemFlow(@RequestBody InMessage<MemFlowDTO> inMessage) {
        MemFlowDTO data = inMessage.getData();
        return flowService.delMemFlow(data);
    }

    /**
     * 流入党员列表
     */
    @PostMapping("/inMemList")
    @Validate
    @RequiresPermissions
    public OutMessage inMemList(@RequestBody InMessage<MemFlowListDTO> inMessage) {
        return flowService.inMemList(inMessage.getData());
    }

    /**
     * 流动历史列表
     */
    @PostMapping("/historyFlowList")
    @Validate
    public OutMessage historyFlowList(@RequestBody InMessage<MemFlowListDTO> inMessage) {
        return flowService.historyFlowList(inMessage.getData());
    }


    private OutMessage validate(MemFlowDTO data) {
        Integer isExplicitInflowOrg = data.getIsExplicitInflowOrg();
        String outFlowOrgCode = null == data.getOutflowOrgCode() ? "" : data.getOutflowOrgCode();
        String outflowOrgName = null == data.getOutflowOrgName() ? "" : data.getOutflowOrgName();
        String outflowOrgOrgCode = null == data.getOutflowOrgOrgCode() ? "" : data.getOutflowOrgOrgCode();
        //TODO: 2019/4/17 增加流动类型是系统内，还是系统外，系统内需要填写org_code唯一主键，如果是系统外，就只需要填入组织名称就可以
        if (isExplicitInflowOrg > 0) {
            if (StrKit.isBlank(outflowOrgName)) {
                return new OutMessage<>(Status.FLOW_NOTNULL_ORG_ORG_NAME);
            }
            String outflowAreaId = data.getOutflowAreaId();
            // TODO: 2019/4/17 这里要做成配置化，系统内，系统外
            if (StrUtil.startWith(outflowAreaId, "52")) {
                if (StrKit.isBlank(outFlowOrgCode)) {
                    return new OutMessage<>(Status.FLOW_NOTNULL_ORG_CODE);
                }
                if (StrKit.isBlank(outflowOrgOrgCode)) {
                    return new OutMessage<>(Status.FLOW_NOTNULL_ORG_ORG_CODE);
                }
            }
        }
        return null;
    }

    /**
     * 获取党员所在组织的联系人和联系方式
     */
    @GetMapping("/findOrgByCode")
    @Validate
    @RequiresPermissions
    public OutMessage findOrgByCode(@NotNull(message = "code不允许为空") String code) {
        return flowService.findOrgByCode(code);
    }

    /**
     * 计算流动类型
     */
    @GetMapping("/countIsProvOut")
    @Validated
    public OutMessage countIsProvOut(@NotBlank(message = "name 不能为空") String name,
                                     @NotBlank(message = "key 不能为空") String key) {
        // 52 贵州
        // 后期计算逻辑可能会更改，暂时以这个逻辑为准
        Map<String, String> map = new HashMap<>(50);
        if (StrUtil.startWith(key, "52")) {
            if (StrUtil.containsAny(name, "县")) {
                map.put("id", "3");
                map.put("name", "市（地、州、盟）内跨县（市、区、旗）流动");
                return new OutMessage<>(Status.SUCCESS, map);
            } else if (StrUtil.containsAny(name, "市", "区", "自治州")) {
                map.put("id", "2");
                map.put("name", "省（区、市）内跨市（地、州、盟）流动");
                return new OutMessage<>(Status.SUCCESS, map);
            } else {
                map.put("id", "3");
                map.put("name", "市（地、州、盟）内跨县（市、区、旗）流动");
                return new OutMessage<>(Status.SUCCESS, map);
            }
        } else {
            map.put("id", "1");
            map.put("name", "跨省（区、市）流动");
            return new OutMessage<>(Status.SUCCESS, map);
        }
    }

    /**
     * 导出流出党员列表
     */
    @PostMapping("/exportOutMemList")
    @RequiresPermissions
    public OutMessage exportOutMemList(@RequestBody InMessage<MemFlowListDTO> dto) throws Exception {
        MemFlowListDTO data = dto.getData();
        return flowService.exportOutMemList(data);
    }

    /**
     * 导出流入党员列表
     */
    @PostMapping("/exportInMemList")
    @RequiresPermissions
    public OutMessage exportInMemList(@RequestBody InMessage<MemFlowListDTO> dto) throws Exception {
        MemFlowListDTO data = dto.getData();
        return flowService.exportInMemList(data);
    }
}
