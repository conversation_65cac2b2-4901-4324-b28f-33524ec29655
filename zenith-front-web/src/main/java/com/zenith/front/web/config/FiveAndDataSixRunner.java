package com.zenith.front.web.config;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.extension.conditions.update.LambdaUpdateChainWrapper;
import com.zenith.front.api.develop.IDevelopStepLogAllService;
import com.zenith.front.api.mem.IMemAllInfoService;
import com.zenith.front.api.mem.IMemDevelopAllService;
import com.zenith.front.api.mem.IMemService;
import com.zenith.front.api.org.IOrgAllService;
import com.zenith.front.api.org.IOrgCommitteeService;
import com.zenith.front.api.org.IOrgService;
import com.zenith.front.api.report.IStatisticsYearService;
import com.zenith.front.api.unit.IUnitCommitteeService;
import com.zenith.front.api.unit.IUnitService;
import com.zenith.front.common.constant.CommonConstant;
import com.zenith.front.common.encrypt.properties.EncryptProperties;
import com.zenith.front.common.kit.StrKit;
import com.zenith.front.common.untils.SM4Untils;
import com.zenith.front.core.service.sync.SyncUnitService;
import com.zenith.front.dao.mapper.develop.DevelopStepLogAllMapper;
import com.zenith.front.dao.mapper.mem.MemAllInfoMapper;
import com.zenith.front.dao.mapper.mem.MemDevelopAllMapper;
import com.zenith.front.dao.mapper.org.OrgAllMapper;
import com.zenith.front.dao.mapper.org.OrgCommitteeElectMapper;
import com.zenith.front.dao.mapper.unit.UnitAllMapper;
import com.zenith.front.dao.mapper.unit.UnitCommitteeElectMapper;
import com.zenith.front.dao.mapper.unit.UnitMapper;

import com.zenith.front.model.bean.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.CommandLineRunner;
import org.springframework.core.annotation.Order;

import javax.annotation.Resource;
import java.util.List;

/**
 * @author: D.watermelon
 * @date: 2022/1/23 0:16
 * @remark 用来处理第五表和第六表得出数情况（需要先执行王国超同步情况）
 * @description: 入则恳恳以尽忠 出则谦谦以自悔
 */
//@Component
@Order(105)
@Slf4j
public class FiveAndDataSixRunner   implements CommandLineRunner {
    @Resource
    OrgAllMapper orgAllMapper;
    @Resource
    private MemAllInfoMapper memAllInfoMapper;
    @Resource
    private MemDevelopAllMapper memDevelopAllMapper;
    @Resource
    private DevelopStepLogAllMapper developStepLogAllMapper;
    @Resource
    private IMemAllInfoService iMemAllInfoService;
    @Resource
    private IMemDevelopAllService iMemDevelopAllService;
    @Resource
    private IDevelopStepLogAllService iDevelopStepLogAllService;
    @Resource
    private IOrgAllService iOrgAllService;
    @Resource
    private IOrgService orgService;
    @Resource
    private IUnitService unitService;
    @Resource
    private OrgCommitteeElectMapper electMapper;
    @Resource
    private IOrgCommitteeService iOrgCommitteeService;
    @Resource
    private UnitCommitteeElectMapper unitCommitteeElectMapper;
    @Resource
    private IUnitCommitteeService iUnitCommitteeService;
    @Resource
    private UnitMapper unitMapper;
    @Resource
    private SyncUnitService syncUnitService;
    @Resource
    private UnitAllMapper unitAllMapper;
    @Resource
    private IMemService iMemService;
    @Resource
    private IStatisticsYearService iStatisticsYearService;



    @Override
    public void run(String... args) {
        if (true) {
            syncUnitService.processUnitCommitteeD25();
            log.info("<====开始执行处理第五表以及第六表得职务问题====>");
            // TODO: 2022/1/23 处理人员关于五表，六表出数问题，设置是否乡镇，是否社区行政村以及相关得专业合作政府工作部门
            this.deailWithUnit("0520");
            // TODO: 2022/1/23 处理人员关于五标，六表出数问题，设置人员得d022_code以及d25_code 乡镇班子书记委员等
            this.deailFiveSix("052");
            // TODO: 2022/1/23 单独处理第五表和第六表得主任任职问题（不在考虑法人单位得问题了）
            this.deailWithUnitSpecial("052");
            log.info("<====完成执行处理第五表以及第六表得职务问题====>");
            log.info("<==============执行人员是否本级=======================>");
            syncUnitService.initHasUnitOwnLevel();
            log.info("<==============执行人员是否本级执行完成=======================>");
            log.info("<==============执行修复专题调查表一的数据情况=======================>");
            this.zt1("052");
            log.info("<==============执行修复专题调查表一的数据情况完成=======================>");
            log.info("<==============执行修复专题调查表8数据情况=======================>");
            this.zt8();
            log.info("<==============执行修复专题调查表8数据情况完成=======================>");
        }
    }

    public void  zt8(){
        List<Org> list = orgService.list(new LambdaQueryWrapper<Org>().select(Org::getCode).isNull(Org::getDeleteTime));
        list.forEach(org -> {
            OrgAll orgAll = iOrgAllService.findByCode(org.getCode());
            if (ObjectUtil.isNull(orgAll)){
                return;
            }
            int memList = iMemService.count(new QueryWrapper<Mem>().lambda().eq(Mem::getOrgCode, orgAll.getCode()).in(Mem::getD08Code, "1", "2")
                    .isNull(Mem::getDeleteTime).apply("(is_transfer != 1 or is_transfer is null) and d09_code='515'"));
            if (ObjectUtil.isNotNull(memList)&&memList>0){
                orgAll.setYearDevelopGraduateNotTransfer(memList);
            }else {
                orgAll.setYearDevelopGraduateNotTransfer(CommonConstant.ZERO_INT);
            }
            iOrgAllService.updateById(orgAll);
        });


    }

    public void  zt1(String orgCode){
        List<UnitAll> unitAlls = unitAllMapper.selectList(new QueryWrapper<UnitAll>().lambda().select(
                UnitAll::getFirstSecretaryCode,
                UnitAll::getD155Code,
                UnitAll::getMainOrgCode,
                UnitAll::getMainUnitOrgCode,
                UnitAll::getMainOrgName).isNull(UnitAll::getDeleteTime).eq(UnitAll::getIsLegal, 1).likeRight(UnitAll::getMainUnitOrgCode, orgCode)
                .in(UnitAll::getD04Code, "922", "923").isNotNull(UnitAll::getFirstSecretaryCode).ne(UnitAll::getFirstSecretaryCode, ""));
        unitAlls.forEach(unitAll -> {
            String firstSecretaryCode = unitAll.getFirstSecretaryCode();
            MemAllInfo memAllInfo = iMemAllInfoService.memAllInfoNotDel(firstSecretaryCode);
            //更新第一书记所在的d115code
            if (ObjectUtil.isNull(memAllInfo)){
                return;
            }
            MemAllInfo updateMemAll=new MemAllInfo();
            updateMemAll.setId(memAllInfo.getId());
            updateMemAll.setD155Code(unitAll.getD155Code());
            memAllInfoMapper.updateById(updateMemAll);
            //看第一书记所在村是否有软弱涣散
            //orgSlackMapper.selectList(new QueryWrapper<OrgSlack>().lambda().eq(OrgSlack::getOrgCode,unitAll.getMainOrgCode()).apply(" AND to_char(neaten_time,'yyyy')=2022"));

        });

    }


    public void deailWithUnitSpecial(String orgCode){
        List<Unit> units = unitMapper.selectList(new QueryWrapper<Unit>().lambda().isNull(Unit::getDeleteTime).eq(Unit::getIsLegal, 1).likeRight(Unit::getMainUnitOrgCode,orgCode).in(Unit::getD04Code, "923", "921", "922"));
        String lastDate = com.zenith.front.common.kit.DateUtil.lastYearDay(iStatisticsYearService.getStatisticalYear());
        if (units.size()>0){
            units.forEach(unit -> {
                log.info("重新处理人员得单位班子以及届次班子名称"+unit.getName());
                this.checkUnitCommitData(unit.getCode(),lastDate);
            });
        }
    }

    public void deailWithUnit(String dataOrgCode) {
        //清除掉以前得数据
        this.clearData(dataOrgCode);
        log.info("<====清除第五表第六表人员数据问题====>");
        //获取需要处理得相关组织情况
        List<OrgAll> orgAlls = orgAllMapper.configOrgAll(dataOrgCode);
        if (ObjectUtil.isNotNull(orgAlls) && orgAlls.size() > 0) {
            orgAlls.forEach(orgAll -> {
                String d04Code = orgAll.getD04Code();
                String orgCode = orgAll.getOrgCode();
                log.info("处理组织数据类别"+orgAll.getD04Name());
                //国务院部 //地方政府工作部门
                if ("131".equals(d04Code) || "132".equals(d04Code)) {
                    //更新党组织all表
                    LambdaUpdateChainWrapper<OrgAll> orgAllLambdaUpdateChainWrapper = new LambdaUpdateChainWrapper<>(orgAllMapper);
                    orgAllLambdaUpdateChainWrapper.likeRight(OrgAll::getOrgCode, orgCode).isNull(OrgAll::getDeleteTime)
                            .set(OrgAll::getIsStateCouncil, CommonConstant.ONE_INT).update();
                }
                //农村专业技术协会
                if ("513".equals(d04Code)) {
                    //更新党组织all表
                    LambdaUpdateChainWrapper<OrgAll> orgAllLambdaUpdateChainWrapper = new LambdaUpdateChainWrapper<>(orgAllMapper);
                    orgAllLambdaUpdateChainWrapper.likeRight(OrgAll::getOrgCode, orgCode).isNull(OrgAll::getDeleteTime)
                            .set(OrgAll::getIsRuralExpertise, CommonConstant.ONE_INT).update();
                }
                //乡
                if ("9121".equals(d04Code)) {
                    this.updayeIsTowns(orgCode, CommonConstant.ONE_INT);
                }
                //镇
                if ("9122".equals(d04Code)) {
                    this.updayeIsTowns(orgCode, CommonConstant.TWO_INT);
                }
                //城市街道
                if ("911".equals(d04Code)) {
                    this.updayeIsTowns(orgCode, CommonConstant.THREE_INT);
                }
                //行政村
                if ("923".equals(d04Code)) {
                    this.updateIsCommunity(orgCode, CommonConstant.ONE_INT);
                }
                //城市社区
                if ("921".equals(d04Code)) {
                    this.updateIsCommunity(orgCode, CommonConstant.TWO_INT);
                }
                //乡镇社区
                if ("922".equals(d04Code)) {
                    this.updateIsCommunity(orgCode, CommonConstant.THREE_INT);
                }

                //农民专业合作社
                if ("94".equals(d04Code)) {
                    //更新党组织all表
                    LambdaUpdateChainWrapper<OrgAll> orgAllLambdaUpdateChainWrapper = new LambdaUpdateChainWrapper<>(orgAllMapper);
                    orgAllLambdaUpdateChainWrapper.likeRight(OrgAll::getOrgCode, orgCode).isNull(OrgAll::getDeleteTime)
                            .set(OrgAll::getIsFarmerMajor, CommonConstant.ONE_INT).update();
                }
                //家庭农场
                if ("95".equals(d04Code)) {
                    //更新党组织all表
                    LambdaUpdateChainWrapper<OrgAll> orgAllLambdaUpdateChainWrapper = new LambdaUpdateChainWrapper<>(orgAllMapper);
                    orgAllLambdaUpdateChainWrapper.likeRight(OrgAll::getOrgCode, orgCode).isNull(OrgAll::getDeleteTime)
                            .set(OrgAll::getIsFamilyFarm, CommonConstant.ONE_INT).update();
                }
            });
        }
        System.out.println("执行完成====》" + orgAlls.size());
    }

    public void updateIsCommunity(String orgCode, Object val) {
        LambdaUpdateChainWrapper<OrgAll> orgAllLambdaUpdateChainWrapper = new LambdaUpdateChainWrapper<>(orgAllMapper);
        orgAllLambdaUpdateChainWrapper.likeRight(OrgAll::getOrgCode, orgCode).isNull(OrgAll::getDeleteTime)
                .set(OrgAll::getIsCommunity, val).update();
        //更新党员all表
        LambdaUpdateChainWrapper<MemAllInfo> memAllInfoLambdaUpdateChainWrapper = new LambdaUpdateChainWrapper<>(memAllInfoMapper);
        memAllInfoLambdaUpdateChainWrapper.likeRight(MemAllInfo::getMemOrgCode, orgCode).isNull(MemAllInfo::getDeleteTime)
                .set(MemAllInfo::getIsCommunity, val).update();

        //更新发展党员表
        LambdaUpdateChainWrapper<MemDevelopAll> memDevelopAllLambdaUpdateChainWrapper = new LambdaUpdateChainWrapper<>(memDevelopAllMapper);

        memDevelopAllLambdaUpdateChainWrapper.likeRight(MemDevelopAll::getDevelopOrgCode,orgCode).isNull(MemDevelopAll::getDeleteTime)
                .set(MemDevelopAll::getIsCommunity,val).update();

        //更新发展党员日志表
        LambdaUpdateChainWrapper<DevelopStepLogAll> developStepLogAllLambdaUpdateChainWrapper = new LambdaUpdateChainWrapper<>(developStepLogAllMapper);
        developStepLogAllLambdaUpdateChainWrapper.likeRight(DevelopStepLogAll::getLogOrgCode,orgCode).isNull(DevelopStepLogAll::getDeleteTime)
                .set(DevelopStepLogAll::getIsCommunity,val).update();
    }

    public void updayeIsTowns(String orgCode, Object val) {
        //更新党组织all表
        LambdaUpdateChainWrapper<OrgAll> orgAllLambdaUpdateChainWrapper = new LambdaUpdateChainWrapper<>(orgAllMapper);
        orgAllLambdaUpdateChainWrapper.likeRight(OrgAll::getOrgCode, orgCode).isNull(OrgAll::getDeleteTime)
                .set(OrgAll::getIsTowns, val).update();
        //更新党员all表
        LambdaUpdateChainWrapper<MemAllInfo> memAllInfoLambdaUpdateChainWrapper = new LambdaUpdateChainWrapper<>(memAllInfoMapper);
        memAllInfoLambdaUpdateChainWrapper.likeRight(MemAllInfo::getMemOrgCode, orgCode).isNull(MemAllInfo::getDeleteTime)
                .set(MemAllInfo::getIsTowns, val).update();

        //更新发展党员表
        LambdaUpdateChainWrapper<MemDevelopAll> memDevelopAllLambdaUpdateChainWrapper = new LambdaUpdateChainWrapper<>(memDevelopAllMapper);

        memDevelopAllLambdaUpdateChainWrapper.likeRight(MemDevelopAll::getDevelopOrgCode,orgCode).isNull(MemDevelopAll::getDeleteTime)
                .set(MemDevelopAll::getIsTowns,val).update();

        //更新发展党员日志表
        LambdaUpdateChainWrapper<DevelopStepLogAll> developStepLogAllLambdaUpdateChainWrapper = new LambdaUpdateChainWrapper<>(developStepLogAllMapper);
        developStepLogAllLambdaUpdateChainWrapper.likeRight(DevelopStepLogAll::getLogOrgCode,orgCode).isNull(DevelopStepLogAll::getDeleteTime)
                .set(DevelopStepLogAll::getIsTowns,val).update();
    }

    public void clearData(String orgCode) {
        //处理memAll表
        iMemAllInfoService.update(new UpdateWrapper<MemAllInfo>().lambda()
                .set(MemAllInfo::getIsTowns, null)
                .isNull(MemAllInfo::getDeleteTime).likeRight(MemAllInfo::getMemOrgCode,orgCode));
        iMemAllInfoService.update(new UpdateWrapper<MemAllInfo>().lambda()
                .set(MemAllInfo::getIsCommunity, null)
                .isNull(MemAllInfo::getDeleteTime).likeRight(MemAllInfo::getMemOrgCode,orgCode));
        //更新发展党员表
        iMemDevelopAllService.update(new UpdateWrapper<MemDevelopAll>().lambda()
                .set(MemDevelopAll::getIsTowns, null)
                .isNull(MemDevelopAll::getDeleteTime));
        iMemDevelopAllService.update(new UpdateWrapper<MemDevelopAll>().lambda()
                .set(MemDevelopAll::getIsCommunity, null)
                .isNull(MemDevelopAll::getDeleteTime));

        //处理发展党员日志表
        iDevelopStepLogAllService.update(new UpdateWrapper<DevelopStepLogAll>().lambda()
                .set(DevelopStepLogAll::getIsTowns, null)
                .isNull(DevelopStepLogAll::getDeleteTime).eq(DevelopStepLogAll::getD08Code,"3").eq(DevelopStepLogAll::getTopreJoinOrgDateYear,2023));
        iDevelopStepLogAllService.update(new UpdateWrapper<DevelopStepLogAll>().lambda()
                .set(DevelopStepLogAll::getIsCommunity, null)
                .isNull(DevelopStepLogAll::getDeleteTime).eq(DevelopStepLogAll::getD08Code,"3").eq(DevelopStepLogAll::getTopreJoinOrgDateYear,2023));

        //更新组织表
        iOrgAllService.update(new UpdateWrapper<OrgAll>().lambda()
                .set(OrgAll::getIsTowns, null)
                .isNull(OrgAll::getDeleteTime));
        iOrgAllService.update(new UpdateWrapper<OrgAll>().lambda()
                .set(OrgAll::getIsCommunity, null)
                .isNull(OrgAll::getDeleteTime));
        iOrgAllService.update(new UpdateWrapper<OrgAll>().lambda()
                .set(OrgAll::getIsStateCouncil, null)
                .isNull(OrgAll::getDeleteTime));
        iOrgAllService.update(new UpdateWrapper<OrgAll>().lambda()
                .set(OrgAll::getIsRuralExpertise, null)
                .isNull(OrgAll::getDeleteTime));
        iOrgAllService.update(new UpdateWrapper<OrgAll>().lambda()
                .set(OrgAll::getIsFarmerMajor, null)
                .isNull(OrgAll::getDeleteTime));
        iOrgAllService.update(new UpdateWrapper<OrgAll>().lambda()
                .set(OrgAll::getIsFamilyFarm, null)
                .isNull(OrgAll::getDeleteTime));
    }


    /***
     * 重新处理人员得组织班子以及届次班子情况
     * ***/
    public void deailFiveSix(String orgCode){
        System.out.println("开始执行处理第五表以及第六表得职务问题====》");
        //获取所有行政村
        //清除掉所有这个两个字段得值
        System.out.println("清除所有第五表，第六表人员信息情况====》");
        iMemAllInfoService.update(new UpdateWrapper<MemAllInfo>().lambda()
                .set(MemAllInfo::getD022Code, null).set(MemAllInfo::getD022Name,null)
                .isNull(MemAllInfo::getDeleteTime));
        iMemAllInfoService.update(new UpdateWrapper<MemAllInfo>().lambda()
                .set(MemAllInfo::getD25Code, null).set(MemAllInfo::getD25Name,null)
                .isNull(MemAllInfo::getDeleteTime));
        // TODO: 2023/1/14 增加清除人员来源信息
        iMemAllInfoService.update(new UpdateWrapper<MemAllInfo>().lambda()
                .set(MemAllInfo::getUnitD121Code, null).set(MemAllInfo::getD121Name,null)
                .isNull(MemAllInfo::getDeleteTime));
        //获取所有行政村以及街道社区
        List<OrgAll> orgAlls = orgAllMapper.configOrgAll(orgCode);
        // TODO: 2022/1/18 这里要看一下他选择与上级党组织相同得主单位是什么
        if (ObjectUtil.isNotNull(orgAlls) && orgAlls.size() > 0) {
            orgAlls.forEach(orgAll -> {
                log.info("重新处理人员得组织班子以及届次班子情况类别"+orgAll.getD04Name());
                String d04Code = orgAll.getD04Code();
                String code = orgAll.getCode();
                //行政村
                if ("923".equals(d04Code)||"921".equals(d04Code)||"922".equals(d04Code)) {
                    this.deailWithOrgTeam(code,orgAll.getOrgCode());
                }
            });
        }
    }

    /***
     * code 党组织唯一标识符
     * orgCode 党组织层级码
     * ***/
    public void  deailWithOrgTeam(String code,String orgCode){
        //清空当前党组织下得人员得这两个数字
        //获取当前党组织得关联单位情况
        Org byOrgCode = orgService.findOrgByCode(code);
        if (ObjectUtil.isNull(byOrgCode)) {
            return;
        }
        String d02Code = byOrgCode.getD02Code();
        //1=独立法人单位，2与上级党组织相同，3不与上级党组织所在单位建立联合党支部，4与上级党组织所在单位建立联合党支部
        if (StrUtil.equalsAny(d02Code,CommonConstant.ONE,CommonConstant.THREE,CommonConstant.FOUR)){
            String lastDate = com.zenith.front.common.kit.DateUtil.lastYearDay(iStatisticsYearService.getStatisticalYear());
            //获取党组织得主单位;
            String mainUnitCode = byOrgCode.getMainUnitCode();
            Unit unitServiceByCode = unitService.findByCode(mainUnitCode);
            if (ObjectUtil.isNull(unitServiceByCode)) {
                return;
            }
            String d04Code = unitServiceByCode.getD04Code();
            //行政村//城市社区//乡镇社区
            if ("923".equals(d04Code)||"921".equals(d04Code)||"922".equals(d04Code)) {
                this.checkOrgCommitData(code,lastDate);
                // TODO: 2022/1/23 因为含有一个联合党支部关联两个社区和村得情况， 所以不能根据法人单位主要组织处理
                //this.checkUnitCommitData(mainUnitCode,lastDate);
            }
        }
    }

    public void checkOrgCommitData(String orgCode,String lastDate){
        //处理党组织班子
        //获取党组织最新届次
        OrgCommitteeElect nowElect = electMapper.findNewestElect(orgCode, lastDate);
        if (ObjectUtil.isNull(nowElect)) {
            return;
        }
        String electCode = nowElect.getCode();
        //获取最新届次人员
        List<OrgCommittee> commitByElect = iOrgCommitteeService.findCommitByElect(electCode);
        if (commitByElect.size()==CommonConstant.ZERO_INT) {
            return;
        }
        commitByElect.forEach(orgCommittee -> {
            String memCode = orgCommittee.getMemCode();
            String d022Name = orgCommittee.getD022Name();
            d022Name = StrUtil.isNotEmpty(d022Name) ? SM4Untils.encryptContent(EncryptProperties.nginxKey, d022Name) : d022Name;
            if (StrKit.notBlank(memCode)){
                LambdaUpdateChainWrapper<MemAllInfo> memAllInfoLambdaUpdateChainWrapper = new LambdaUpdateChainWrapper<>(memAllInfoMapper);
                memAllInfoLambdaUpdateChainWrapper.eq(MemAllInfo::getCode,memCode).set(MemAllInfo::getD022Code,orgCommittee.getD022Code())
                        .set(MemAllInfo::getD022Name, d022Name).update();
            }

            if (StrKit.isBlank(memCode)){
                LambdaUpdateChainWrapper<MemAllInfo> memAllInfoLambdaUpdateChainWrapper = new LambdaUpdateChainWrapper<>(memAllInfoMapper);
                memAllInfoLambdaUpdateChainWrapper.eq(MemAllInfo::getCode,orgCommittee.getCode()).set(MemAllInfo::getD022Code,orgCommittee.getD022Code())
                        .set(MemAllInfo::getD022Name, d022Name).update();
            }
        });
    }
    public void checkUnitCommitData(String unitCode,String lastDate){
        //清除单位班子以前的成员
        //获取班子最新届次
        UnitCommitteeElect newestElect = unitCommitteeElectMapper.findNewestElect(unitCode, lastDate);
        if (ObjectUtil.isNull(newestElect)) {
            return;
        }
        String unitElect = newestElect.getCode();
        //获取最新届次成员
        List<UnitCommittee> byElect = iUnitCommitteeService.findByElect(unitElect);
        if (byElect.size()==CommonConstant.ZERO_INT) {
            return;
        }
        byElect.forEach(unitCommittee -> {
            String memCode = unitCommittee.getMemCode();
            String unitCommitteeCode = unitCommittee.getCode();
            if (StrKit.notBlank(memCode)){
                LambdaUpdateChainWrapper<MemAllInfo> memAllInfoLambdaUpdateChainWrapper = new LambdaUpdateChainWrapper<>(memAllInfoMapper);
                memAllInfoLambdaUpdateChainWrapper.eq(MemAllInfo::getCode,memCode)
                        .set(MemAllInfo::getD25Code,unitCommittee.getD25Code())
                        .set(MemAllInfo::getD25Name,unitCommittee.getD25Name())
                        // TODO: 2023/1/14 增加同步村社区人员来源code以及代码
                        .set(MemAllInfo::getUnitD121Code,unitCommittee.getD0121Code())
                        .set(MemAllInfo::getUnitD121Name,unitCommittee.getD0121Name())
                        .update();
            }
            if (StrKit.isBlank(memCode)){
                LambdaUpdateChainWrapper<MemAllInfo> memAllInfoLambdaUpdateChainWrapper = new LambdaUpdateChainWrapper<>(memAllInfoMapper);
                memAllInfoLambdaUpdateChainWrapper.eq(MemAllInfo::getCode,unitCommitteeCode)
                        .set(MemAllInfo::getD25Code,unitCommittee.getD25Code())
                        .set(MemAllInfo::getD25Name,unitCommittee.getD25Name())
                        // TODO: 2023/1/14 增加同步村社区人员来源code以及代码
                        .set(MemAllInfo::getUnitD121Code,unitCommittee.getD0121Code())
                        .set(MemAllInfo::getUnitD121Name,unitCommittee.getD0121Name())
                        .update();
            }
        });
    }

}
