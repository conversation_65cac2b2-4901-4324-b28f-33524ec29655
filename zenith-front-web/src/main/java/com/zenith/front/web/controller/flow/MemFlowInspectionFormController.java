package com.zenith.front.web.controller.flow;

import com.zenith.front.api.mem.IMemFlowInspectionFormService;
import com.zenith.front.common.annotation.RequiresPermissions;
import com.zenith.front.common.constant.OperateConstant;
import com.zenith.front.model.dto.ReportExcelDTO;
import com.zenith.front.model.message.InMessage;
import com.zenith.front.model.message.OutMessage;
import com.zenith.front.model.validate.annotation.Validate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 流动党员工作督查表
 *
 * <AUTHOR>
 * @since 2025/3/13 10:38
 */
@RestController
@RequestMapping("/memFlowInspectionForm")
@Validated
public class MemFlowInspectionFormController {
    @Autowired
    private IMemFlowInspectionFormService memFlowInspectionFormService;

    @PostMapping("/realTimeGetData")
    @Validate
    @RequiresPermissions(opt = OperateConstant.QUERY)
    public OutMessage realTimeGetData(@RequestBody InMessage<ReportExcelDTO> dto) {
        return memFlowInspectionFormService.realTimeGetData(dto.getData());
    }

    @PostMapping("/InquireReportGetDat")
    @Validate
    @RequiresPermissions(opt = OperateConstant.QUERY)
    public OutMessage inquireReportGetDat(@RequestBody InMessage<ReportExcelDTO> dto) {
        return memFlowInspectionFormService.inquireReportGetDat(dto.getData());
    }

    @PostMapping("/inquireExportData")
    @Validate
    public void inquireExportData(@RequestBody InMessage<ReportExcelDTO> dto) throws Exception {
        memFlowInspectionFormService.inquireExportData(dto.getData());
    }







}
