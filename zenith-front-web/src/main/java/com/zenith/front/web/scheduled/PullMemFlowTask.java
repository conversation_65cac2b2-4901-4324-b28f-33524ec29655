package com.zenith.front.web.scheduled;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.zenith.front.api.mem.*;
import com.zenith.front.api.org.IOrgService;
import com.zenith.front.common.constant.CommonConstant;
import com.zenith.front.common.encrypt.utli.ReflectionUtil;
import com.zenith.front.common.kit.HttpKit;
import com.zenith.front.model.bean.Mem;
import com.zenith.front.model.bean.MemFlow1;
import com.zenith.front.model.message.OutMessage;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.condition.ConditionalOnExpression;
import org.springframework.context.annotation.Configuration;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.lang.reflect.Field;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 定时拉取交换区流动党员相关数据
 *
 * <AUTHOR>
 */
@Slf4j
@Configuration
@ConditionalOnExpression("${schedule.flow.open:true}")
public class PullMemFlowTask extends AbstractSchedulingConfigurer {

    @Value("${schedule.flow.cron}")
    private String cron;
    @Resource
    private IOrgService orgService;
    @Resource
    private IMemFlow1Service memFlow1Service;

    @Resource
    private IMemFlowMessageService memFlowMessageService;

    @Resource
    private IMemFlowEventsService memFlowEventsService;

    @Resource
    private IMemFlowSignService memFlowSignService;

    @Value("${sync_flow_pull}")
    private String syncFlowPull;


    /**
     * Field缓存，避免重复反射
     */
    private final Map<String, Field> fieldCache = new HashMap<>();

    @Override
    public void task() {
        log.info("同步流动党员信息定时任务启动================");
        //查询层级码长度为9的组织
        // todo 这个层级码用来查询交换区库，用来更新由本节点发起的流动党员并且状态已经发生改变的数据
        List<String> orgCodeList = orgService.findNodeByOrgCodeLength(9);
        //查询当前节点下，组织绑定的行政区划，对应的流动党员节点库
        // todo-2025-02-28 只查询14开头属于县的 并且机构层级码长度为9, 用来拉取需要导入自己库的流动党员信息
        // List<String> orgAdministrativeRegionList = orgService.findNodeByOrgAdministrativeRegion();
        List<String> orgAdministrativeRegionList =orgCodeList;
        Set<String> orgCodeSet = new HashSet<>();
        orgCodeSet.addAll(orgCodeList);
        orgCodeSet.addAll(orgAdministrativeRegionList);
        if (CollUtil.isEmpty(orgCodeSet)) {
            return;
        }
        String replaceUrl = syncFlowPull.replace("/api/flow/pull", "");
        Map<String, List<String>> map = new LinkedHashMap<>(10);
        map.put("orgCode", orgCodeList);
        map.put("extraOrgCode", orgAdministrativeRegionList);
        log.info("开始拉取党员信息，地址为"+replaceUrl + "/mem/flow/findCallbackList");
        String outResult = HttpKit.doPost(replaceUrl + "/mem/flow/findCallbackList", JSONUtil.toJsonStr(map), "UTF-8");
        if (StrUtil.equals(outResult, "-1")) {
            log.warn("Connect to :{} failed: Connection refused: connect", replaceUrl);
            return;
        }
        if (StringUtils.isEmpty(outResult)) {
            return;
        }
        OutMessage<?> resultOut = JSONUtil.toBean(outResult, OutMessage.class);
        if (Objects.isNull(resultOut.getData())) {
            log.warn("No data response");
            return;
        }
        List<MemFlow1> memFlowList = JSONUtil.toList(resultOut.getData().toString(), MemFlow1.class);
        if (CollUtil.isEmpty(memFlowList)) {
            return;
        }
        memFlowList.forEach(memFlow1 -> {
            memFlow1.setUpdateTime(null);
            memFlow1.setUpdateAccount(null);
        });
        // log.info("返回数据为" + JSONUtil.toJsonStr(memFlowList));
        // 使用优化的保存方法
        optimizedSaveOrUpdate(memFlowList);
        // TODO: 2025/2/26 增加对方发送提醒，我们自动纳管
        List<MemFlow1> listAsLddyIdNotNull = memFlowList.stream().filter(memFlow -> {
            String lddyId = memFlow.getLddyId();
            return StrUtil.isNotEmpty(lddyId);
        }).collect(Collectors.toList());
        listAsLddyIdNotNull.forEach(memFlowAsId -> {
            //这里面包含了很多类型的数据，有可能是已经纳入管理了
            String lddyId = memFlowAsId.getLddyId();
            if (StrUtil.equalsIgnoreCase(lddyId, "null")) {
                return;
            }
            //查询是否存在相关流动信息
//            MemFlow1 byLddyId = memFlow1Service.findByLddyId(lddyId);
            MemFlow1 byLddyId = memFlow1Service.findByLddyIdByStep(lddyId,CommonConstant.ONE);
            //证明库里面已经被省外自动登记下载到了
            if (ObjectUtil.isNotNull(byLddyId)) {
                //判断状态时候已经纳管
                String flowStep = byLddyId.getFlowStep();
                //未纳入才需要纳管，其他状态不需要处理，可能拉到的时候已经流回，可能已经撤销
                if (flowStep.equals(CommonConstant.ONE)) {
                    // TODO: 2025/2/26 纳管党员数据
                    try {
                        memFlow1Service.updteFlowStepForFlowSign(lddyId, byLddyId.getCode());
                    } catch (Exception e) {
                        e.printStackTrace();
                    }
                }
            }
            //查询是否已经纳入管理了（有可能拉到提醒接收状态在前，流入登记拉取在后，需要容错）
        });

        //同步流动消息
        List<String> memFlowCodes = memFlowList.stream().map(s -> s.getCode()).collect(Collectors.toList());
        memFlowMessageService.pullFlowMessageExchange(memFlowCodes);

        //同步活动信息
        memFlowEventsService.syncAllUserMemFlowEvents(memFlowCodes);

        //同步流动提醒数据
        memFlowSignService.pullFlowSignExchange();

        // 同步5.31已纳入的目标组织为流出地成立的跨省流动信息数据
        memFlowSignService.pullFLow531Data();

        // 同步联系方式
        memFlowSignService.pullFLowLxfs(orgCodeList);
    }

    @Override
    public String cron() {
        return cron;
    }

    /**
     * 优化的保存或更新方法
     * 根据flowStep字段变化决定更新策略
     */
    private void optimizedSaveOrUpdate(List<MemFlow1> memFlowList) {
        if (CollUtil.isEmpty(memFlowList)) {
            return;
        }

        List<String> codes = memFlowList.stream()
            .map(MemFlow1::getCode)
            .collect(Collectors.toList());

        // 只查询code和flowStep字段，避免加密字段解密
        List<MemFlow1> existingData = memFlow1Service.list(
            new LambdaQueryWrapper<MemFlow1>()
                .select(MemFlow1::getCode, MemFlow1::getFlowStep)
                .in(MemFlow1::getCode, codes)
        );

        Map<String, String> existingStepMap = existingData.stream()
            .collect(Collectors.toMap(
                MemFlow1::getCode,
                MemFlow1::getFlowStep,
                (existing, replacement) -> existing
            ));

        // 分类数据
        List<MemFlow1> fullUpdateList = new ArrayList<>();
        List<MemFlow1> partialUpdateList = new ArrayList<>();
        List<MemFlow1> insertList = new ArrayList<>();

        for (MemFlow1 newData : memFlowList) {
            String existingStep = existingStepMap.get(newData.getCode());

            if (existingStep == null) {
                insertList.add(newData);
            } else if (!Objects.equals(existingStep, newData.getFlowStep())) {
                fullUpdateList.add(newData);
            } else {
                partialUpdateList.add(newData);
            }
        }

        log.info("数据分类完成 - 新增:{}条, 全量更新:{}条, 部分更新:{}条",
                 insertList.size(), fullUpdateList.size(), partialUpdateList.size());

        // 执行不同的更新策略
        if (CollUtil.isNotEmpty(insertList)) {
            log.info("执行新数据插入，{}条", insertList.size());
            memFlow1Service.saveBatch(insertList);
        }

        if (CollUtil.isNotEmpty(fullUpdateList)) {
            log.info("执行全量更新（包含加密字段），{}条", fullUpdateList.size());
            memFlow1Service.saveOrUpdateBatch(fullUpdateList);
        }

        if (CollUtil.isNotEmpty(partialUpdateList)) {
            log.info("执行部分更新（仅非加密字段），{}条", partialUpdateList.size());
            updateNonEncryptedFields(partialUpdateList);
        }
    }

    /**
     * 只更新非加密字段 - 简化版本
     */
    private void updateNonEncryptedFields(List<MemFlow1> dataList) {
        if (CollUtil.isEmpty(dataList)) {
            return;
        }

        log.info("执行非加密字段更新，共{}条数据", dataList.size());

        // 直接使用MyBatis-Plus的updateBatchById，但只设置非加密字段
        List<MemFlow1> updateList = new ArrayList<>();
        for (MemFlow1 data : dataList) {
            MemFlow1 updateData = new MemFlow1();
            updateData.setCode(data.getCode()); // 主键

            // 只设置非加密字段
            copyNonEncryptedFields(data, updateData);
            updateList.add(updateData);
        }

        // 使用MyBatis-Plus的批量更新
        memFlow1Service.updateBatchById(updateList);
    }


    /**
     * 复制非加密字段 - 使用Field缓存提高性能
     */
    private void copyNonEncryptedFields(MemFlow1 source, MemFlow1 target) {
        String className = MemFlow1.class.getName();
        List<String> nonEncryptFields = ReflectionUtil.NON_ENCRYPT_FIELD_MAP.get(className);

        if (CollUtil.isEmpty(nonEncryptFields)) {
            log.warn("未找到MemFlow1的非加密字段配置");
            return;
        }

        // 使用缓存避免重复反射
        for (String fieldName : nonEncryptFields) {
            try {
                // 先从缓存中获取Field，如果不存在则反射获取并缓存
                Field field = fieldCache.get(fieldName);
                if (field == null) {
                    try {
                        field = MemFlow1.class.getDeclaredField(fieldName);
                        field.setAccessible(true);
                        fieldCache.put(fieldName, field);
                    } catch (NoSuchFieldException e) {
                        log.warn("字段{}不存在，跳过", fieldName);
                        // 缓存一个特殊标记，避免重复尝试
                        fieldCache.put(fieldName, null);
                        continue;
                    }
                }

                // 如果field为null（之前反射失败过），跳过
                if (field == null) {
                    continue;
                }

                Object value = field.get(source);
                field.set(target, value);
            } catch (Exception e) {
                log.warn("复制字段{}失败：{}", fieldName, e.getMessage());
            }
        }
    }
}
