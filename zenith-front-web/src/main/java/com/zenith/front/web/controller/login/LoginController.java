package com.zenith.front.web.controller.login;

import cn.hutool.captcha.CaptchaUtil;
import cn.hutool.captcha.CircleCaptcha;
import cn.hutool.captcha.generator.RandomGenerator;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.crypto.digest.DigestUtil;
import com.fisherman.api.FmApi;
import com.zenith.front.api.log.ILoginLogService;
import com.zenith.front.api.user.IUserService;
import com.zenith.front.common.annotation.ExcludeHttpBodyDecrypt;
import com.zenith.front.common.annotation.RequiresPermissions;
import com.zenith.front.common.constant.CacheConstant;
import com.zenith.front.common.constant.CommonConstant;
import com.zenith.front.common.constant.LoginLogConstant;
import com.zenith.front.common.kit.HttpKit;
import com.zenith.front.common.kit.IpKit;
import com.zenith.front.common.kit.JwtUtil;
import com.zenith.front.common.kit.StrKit;
import com.zenith.front.common.password.FmApiUtil;
import com.zenith.front.common.untils.AESUntil;
import com.zenith.front.model.constant.UserConstant;
import com.zenith.front.core.cache.CacheUtils;
import com.zenith.front.model.dto.LoginDTO;
import com.zenith.front.model.dto.LoginLogDTO;
import com.zenith.front.model.dto.UserLoginFailedDTO;
import com.zenith.front.model.dto.VerifySignedDataDTO;
import com.zenith.front.model.message.InMessage;
import com.zenith.front.model.message.OutMessage;
import com.zenith.front.model.message.Status;
import com.zenith.front.model.bean.User;
import com.zenith.front.model.validate.annotation.Validate;
import com.zenith.front.model.validate.group.Common3Group;
import com.zenith.front.model.vo.LoginVO;
import com.zenith.front.web.controller.BaseController;
import io.jsonwebtoken.Claims;
import org.bouncycastle.util.Strings;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.Base64;
import java.util.UUID;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 */
@RestController
@RequestMapping("/login")
public class LoginController extends BaseController {

    @Value("${is-dev}")
    public boolean isDev;
    @Resource
    HttpServletRequest request;
    @Resource
    private IUserService userService;
    @Resource
    private ILoginLogService loginLogService;
    @Resource
    private RedisTemplate<String, String> redisTemplate;

    public OutMessage sheLogin(String name, String phone, String unit, String zhiwu, String youxiang, String zixunxuqiu) {
        return userService.sheLogin(name, phone, unit, zhiwu, youxiang, zixunxuqiu);
    }


    /**
     * 登录
     **/
    @Validate(group = Common3Group.class)
    @PostMapping
    public OutMessage<LoginVO> index(@RequestBody InMessage<LoginDTO> message) {
        LoginDTO data = message.getData();
        String appcode = data.getAppcode();
        String token = data.getToken();
        String targetUrl = data.getTargetUrl();

        //渝快政接入方式
        if (StrKit.notBlank(appcode) && StrKit.notBlank(token)) {
            String openId = this.checkOpen(appcode, token, targetUrl);
            if ("无".equals(openId)) {
                return new OutMessage<>(Status.USER_IS_LOCK);
            } else {
                //放入账号
                //根据OpenId获取账号数据
                User userByOpenId = userService.findUserByOpenId(openId);
                if (ObjectUtil.isNull(userByOpenId)) {
                    return new OutMessage<>(Status.USER_NOT_EXIST);
                }
                data.setOpenId(openId);
                data.setPassword(userByOpenId.getPassword());
                data.setAccount(userByOpenId.getAccount());
            }
        } else {
            //普通账号密码登录方式
            String account = data.getAccount();
            String password = data.getPassword();
            String captchaCode = data.getCaptchaCode();

            if (StrKit.isBlank(account)) {
                return new OutMessage<>(Status.USERNAME_ISEMPTY);
            }
            if (StrKit.isBlank(password)) {
                return new OutMessage<>(Status.PASSWORD_ISEMPTY);
            }

            // TODO: 2022/6/30 修复等保测评密码明文传输问题
            try {
                data.setPassword(AESUntil.checkData(password));
            } catch (Exception e) {
                e.printStackTrace();
                return new OutMessage<>(Status.LOGIN_FAILED);
            }
            if (StrKit.isBlank(captchaCode)) {
                return new OutMessage<>(Status.CODE_ERROR);
            }
            // !captchaCode.equalsIgnoreCase(LoginConstant.TEST_CAPTCHACODE) || !is_dev
            if (!isDev) {
                String code = redisTemplate.opsForValue().get(data.getUuid());
                if (StrKit.isBlank(code) || !code.equalsIgnoreCase(captchaCode)) {
                    return new OutMessage<>(Status.CODE_ERROR);
                }
                redisTemplate.delete(data.getUuid());
            }
        }
        //获取请求的IP地址
        String ip = IpKit.getRealIp(request);
        String ua = HttpKit.getUA(request);

        data.setIp(ip);
        data.setUa(ua);

        //记录用户登录日志
        LoginLogDTO loginLogDTO = new LoginLogDTO();
        loginLogDTO.setAccount(data.getAccount());
        loginLogDTO.setPassword(data.getPassword());
        loginLogDTO.setIp(ip);
        //用户登录失败次数记录
        UserLoginFailedDTO userLoginFailedDTO = CacheUtils.getUserLoginFailedDTO(CacheConstant.CACHE_USER_LOGIN_FAILED_COUNT_PREFIX + data.getAccount());
        //判断用户是否被锁定
        if (userLoginFailedDTO != null) {
            int count = userLoginFailedDTO.getCount();
            if (count >= com.zenith.front.common.constant.UserConstant.LOGIN_ERROR_MAX_NUM) {
                loginLogDTO.setIsOk(LoginLogConstant.NOT_OK);
                loginLogService.addLog(loginLogDTO);
                return new OutMessage<>(Status.USER_IS_LOCK);
            }
        }

        OutMessage<LoginVO> result = userService.login(data);
        if (Status.SUCCESS.getMessage().equalsIgnoreCase(result.getMessage())) {
            loginLogDTO.setIsOk(LoginLogConstant.OK);
        } else {
            loginLogDTO.setIsOk(LoginLogConstant.NOT_OK);
        }

        loginLogService.addLog(loginLogDTO);
        //记录登录次数,以及登录ip
        CacheUtils.putUserLoginIp(data.getAccount(), ip);
        CacheUtils.setUserLoginCount(data.getAccount());
        return result;
    }

    /***
     * 渲染验证码
     * */
    @ExcludeHttpBodyDecrypt
    @RequestMapping("/code")
    public void code(HttpServletResponse response) {
        String uuid = UUID.randomUUID().toString();
        // 自定义纯数字的验证码（随机4位数字，可重复）
        RandomGenerator randomGenerator = new RandomGenerator(CommonConstant.BASE_STR, CommonConstant.FOUR_INT);
        CircleCaptcha circleCaptcha = CaptchaUtil.createCircleCaptcha(120, 34, 4, 0);
        circleCaptcha.setGenerator(randomGenerator);
        // 重新生成code
        circleCaptcha.createCode();
        redisTemplate.opsForValue().set(uuid, circleCaptcha.getCode(), 3 * 60, TimeUnit.SECONDS);
        //自定义响应头，存放uuid
        response.setHeader("captcha", uuid);
        ServletOutputStream out = null;
        try {
            out = response.getOutputStream();
            circleCaptcha.write(out);
        } catch (IOException e) {
            e.printStackTrace();
        } finally {
            try {
                if (out != null) {
                    out.close();
                }
            } catch (IOException e) {
                e.printStackTrace();
            }
        }
    }

    public String checkOpen(String app, String tocken, String tar) {

        Claims claims = JwtUtil.parseJwt(tocken);
        Object orDefault = claims.getOrDefault("openId", "无:无");
        if (ObjectUtil.isNotNull(orDefault)) {
            return orDefault.toString();
        }
        return "无";
    }

    @PostMapping("needToChangePassword")
    @RequiresPermissions
    public OutMessage<Object> needToChangePassword() {
        String md5Hex = DigestUtil.md5Hex("sjr1809");
        User user = UserConstant.USER_CONTEXT.get().getUser();
        if (user.getPassword().equals(md5Hex) && !"admin".equals(user.getAccount())) {
            return new OutMessage<>(Status.SUCCESS, true);
        }
        return new OutMessage<>(Status.SUCCESS, false);
    }

    /**
     *
     * 重置密码链接
     */
    @RequestMapping("/resetPassword")
    public OutMessage  resetPassword(String account){
        return userService.resetPassword(account);
    }

    /**
     *
     * 删除用户链接
     */
    @RequestMapping("/deleteUser")
    public OutMessage deleteUser(String account){
        return userService.deleteUser(account);
    }

    /**
     *  签名验签生成随机数接口
     * @param len 生成随机数的长度
     * @return
     */
    @GetMapping("/genRandomData")
    public OutMessage genRandomData(int len){
        FmApi fmApi = FmApiUtil.getFmApi();
        byte[] bytes = fmApi.FM_DSVS_GenRandomData(len);
        String encodedString = Base64.getEncoder().encodeToString(bytes);
        return new OutMessage<>(Status.SUCCESS, encodedString);
    }

    /**
     * 验签接口
     * @return
     */
    @PostMapping("/verifySignedData")
    public OutMessage verifySignedData(@RequestBody InMessage<VerifySignedDataDTO> message){
        VerifySignedDataDTO data = message.getData();
        FmApi fmApi = FmApiUtil.getFmApi();
        byte[] inData = Base64.getDecoder().decode(data.getInDataStr());
        byte[] signBytes = Base64.getDecoder().decode(data.getSignBytesStr());
        byte[] certSign = data.getCertSignStr().getBytes();
        int rv2 = fmApi.FM_DSVS_VerifySignedData(1, certSign, null, inData, signBytes, 1);
        return new OutMessage<>(Status.SUCCESS, rv2);
    }
}
