package com.zenith.front.web.controller.org;

import com.zenith.front.api.org.IOrgAppraisalService;
import com.zenith.front.common.annotation.RequiresPermissions;
import com.zenith.front.common.constant.OperateConstant;
import com.zenith.front.model.dto.OrgDemocraticAppraisalDTO;
import com.zenith.front.model.message.InMessage;
import com.zenith.front.model.message.OutMessage;
import com.zenith.front.web.controller.BaseController;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.constraints.Max;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * 民主评议
 * @date 2021/08/16 15:32
 */
@RestController
@RequestMapping("/org/appraisal")
public class OrgAppraisalController extends BaseController {

    @Resource
    private IOrgAppraisalService orgAppraisalService;


    /**
     * 党组织信息项中增加民主评议
     */
    @PostMapping("/save")
    @RequiresPermissions(opt = OperateConstant.INSERT)
    public OutMessage save(@RequestBody InMessage<OrgDemocraticAppraisalDTO> inMessage) {
        return orgAppraisalService.save(inMessage.getData());
    }

    /**
     * 党组织信息项中删除民主评议
     */
    @GetMapping("/remove")
    @RequiresPermissions(opt = OperateConstant.DELETE)
    public OutMessage remove(@NotNull(message = "主键 不能为空") String code) {
        return orgAppraisalService.remove(code);
    }

    /**
     * 党组织信息项中修改民主评议
     */
    @PostMapping("/update")
    @RequiresPermissions(opt = OperateConstant.UPDATE)
    public OutMessage update(@RequestBody InMessage<OrgDemocraticAppraisalDTO> inMessage) {
        return orgAppraisalService.update(inMessage.getData());
    }

    /**
     * 民主评议列表信息
     */
    @GetMapping("/list")
    @RequiresPermissions(opt = OperateConstant.QUERY)
    public OutMessage list(@Min(value = 1, message = "页码最小为1") int pageNum,
                           @Max(value = 100, message = "页大小范围在1-100") int pageSize,
                           @NotNull(message = "orgCode 不能为空") String orgCode) {
        return orgAppraisalService.list(pageNum, pageSize, orgCode);
    }

}
