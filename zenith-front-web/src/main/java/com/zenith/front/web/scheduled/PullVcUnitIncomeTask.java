package com.zenith.front.web.scheduled;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;
import com.zenith.front.api.unit.IUnitIncomeService;
import com.zenith.front.core.feign.client.VillageCommunityClient;
import com.zenith.front.core.feign.dto.PushUnitIncomeDTO;
import com.zenith.front.core.feign.dto.VcUnitIncome;
import com.zenith.front.model.bean.UnitIncome;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.condition.ConditionalOnExpression;
import org.springframework.context.annotation.Configuration;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * 推送集体经济
 *
 * <AUTHOR>
 * @date 2022/11/15
 */
@Slf4j
@Configuration
@ConditionalOnExpression("${vc.feign.enabled:true}&&${vc.unit.open:true}")
public class PullVcUnitIncomeTask extends AbstractSchedulingConfigurer {

    @Value("${vc.unit.cron}")
    private String cron;
    @Resource
    private IUnitIncomeService iUnitIncomeService;
    @Resource
    private VillageCommunityClient villageCommunityClient;

    @Override
    public void task() {
        List<UnitIncome> list = iUnitIncomeService.list();
        if (CollUtil.isEmpty(list)) {
            return;
        }
        List<VcUnitIncome> result = new ArrayList<>();
        for (UnitIncome e : list) {
            VcUnitIncome vc = new VcUnitIncome();
            BeanUtils.copyProperties(e, vc);
            vc.setCreateTimeStr(Objects.nonNull(e.getCreateTime()) ? DateUtil.format(e.getCreateTime(), DatePattern.NORM_DATETIME_PATTERN) : "");
            vc.setUpdateTimeStr(Objects.nonNull(e.getUpdateTime()) ? DateUtil.format(e.getUpdateTime(), DatePattern.NORM_DATETIME_PATTERN) : "");
            vc.setDeleteTime(Objects.nonNull(e.getDeleteTime()) ? DateUtil.formatDate(e.getDeleteTime()) : "");
            result.add(vc);
        }
        PushUnitIncomeDTO dto = new PushUnitIncomeDTO();
        dto.setList(result);
        dto.setNginxKey(exchangeNginxKey);
        villageCommunityClient.pushUnitIncome(dto);
    }

    @Override
    public String cron() {
        return cron;
    }
}
