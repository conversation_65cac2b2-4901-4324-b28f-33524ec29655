package com.zenith.front.web.config;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.databind.AnnotationIntrospector;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.introspect.AnnotationIntrospectorPair;
import com.fasterxml.jackson.databind.module.SimpleModule;
import com.fasterxml.jackson.databind.ser.std.StdDelegatingSerializer;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import com.zenith.front.model.custom.Record;
import com.zenith.front.framework.file.introspect.MinioBasePathAnnotationIntrospector;
import com.zenith.front.model.message.RecordConvert;
import com.zenith.front.model.message.RecordSerializer;
import com.zenith.front.web.interceptor.*;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.http.converter.HttpMessageConverter;
import org.springframework.http.converter.json.MappingJackson2HttpMessageConverter;
import org.springframework.util.AntPathMatcher;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.servlet.config.annotation.InterceptorRegistry;
import org.springframework.web.servlet.config.annotation.PathMatchConfigurer;
import org.springframework.web.servlet.config.annotation.ResourceHandlerRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;
import java.math.BigDecimal;
import java.util.*;

/**
 * 拦截器配置
 *
 * <AUTHOR>
 */
@Configuration
public class CustomWebConfig implements WebMvcConfigurer {

    /**
     * 默认拦截器排除资源
     */
    private final List<String> excludePaths = Arrays.asList("/upload/**", "/login", "classpath:/static/", "^/druid.*", "/public/**", "/data/export");

    @Bean
    public PermissionInterceptor permissionInterceptor() {
        return new PermissionInterceptor();
    }

    /**
     * 注册拦截器
     */
    @Override
    public void addInterceptors(InterceptorRegistry registry) {
        registry.addInterceptor(permissionInterceptor()).addPathPatterns("/**").excludePathPatterns(excludePaths);
        //图表权限校验
//        registry.addInterceptor(new ChartInterceptor()).addPathPatterns("/chart/**");
        //组织层级码校验
        registry.addInterceptor(new LevelCodeInterceptor()).addPathPatterns("/**").excludePathPatterns(excludePaths);
    }

    @Override
    public void addResourceHandlers(ResourceHandlerRegistry registry) {
        // 将/static/**访问映射到classpath:/static/
        registry.addResourceHandler("/upload/**").addResourceLocations("classpath:/upload/");
        registry.addResourceHandler("/public/**").addResourceLocations("classpath:/public/");
        registry.addResourceHandler("/org_contact_work/**").addResourceLocations("classpath:/org_contact_work/");
        registry.addResourceHandler("/org_life/**").addResourceLocations("classpath:/org_life/");
    }

    @Override
    public void configureMessageConverters(List<HttpMessageConverter<?>> converters) {

        SimpleModule simpleModule = new SimpleModule();
        simpleModule.addSerializer(BigDecimal.class, ToStringSerializer.instance);
        simpleModule.addSerializer(new RecordSerializer(Record.class));
        simpleModule.addSerializer(Record.class, new StdDelegatingSerializer(new RecordConvert()));
        ObjectMapper objectMapper = new ObjectMapper();
        objectMapper.registerModule(simpleModule);
        objectMapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
        // 通过该方法对mapper对象进行设置，所有序列化的对象都将按改规则进行系列化
        // Include.Include.ALWAYS 默认
        // Include.NON_DEFAULT 属性为默认值不序列化
        // Include.NON_EMPTY 属性为 空（""） 或者为 NULL 都不序列化，则返回的json是没有这个字段的。
        // Include.NON_NULL 属性为NULL 不序列化,就是为null的字段不参加序列化
        objectMapper.setSerializationInclusion(JsonInclude.Include.NON_NULL);

        // 将自定义注解內省器加入到Jackson注解内省器集合里，AnnotationIntrospector是双向链表结构
        AnnotationIntrospector ai = objectMapper.getSerializationConfig().getAnnotationIntrospector();
        AnnotationIntrospector annotationIntrospector = AnnotationIntrospectorPair.pair(ai, minioPathAnnotationIntrospector());
        objectMapper.setAnnotationIntrospector(annotationIntrospector);

        MappingJackson2HttpMessageConverter jackson2HttpMessageConverter = new MappingJackson2HttpMessageConverter();
        jackson2HttpMessageConverter.setObjectMapper(objectMapper);
        converters.add(0, jackson2HttpMessageConverter);
    }

    @Bean
    public MinioBasePathAnnotationIntrospector minioPathAnnotationIntrospector() {
        return new MinioBasePathAnnotationIntrospector();
    }

    private void configurePathMatch(PathMatchConfigurer configurer, String prefix, String controllerPath) {
        // 创建路径匹配类，指定以'.'分隔
        AntPathMatcher antPathMatcher = new AntPathMatcher(".");
        // 指定匹配前缀
        // 满足：类上有RestController注解 && 该类的包名匹配指定的自定义包的表达式
        configurer.addPathPrefix(prefix, clazz -> clazz.isAnnotationPresent(RestController.class)
                && antPathMatcher.match(controllerPath, clazz.getPackage().getName()));
    }
}