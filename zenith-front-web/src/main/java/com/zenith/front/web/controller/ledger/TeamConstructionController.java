package com.zenith.front.web.controller.ledger;


import com.zenith.front.api.mem.IMemService;
import com.zenith.front.common.annotation.RequiresPermissions;
import com.zenith.front.common.annotation.VerifyLevelCode;
import com.zenith.front.model.dto.LedgerDTO;
import com.zenith.front.model.message.InMessage;
import com.zenith.front.model.message.OutMessage;
import com.zenith.front.model.validate.annotation.Validate;
import com.zenith.front.model.validate.group.Common1Group;
import com.zenith.front.model.validate.group.QueryGroup;
import com.zenith.front.web.controller.BaseController;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @version 1.0
 * @Description: 队伍建设
 * @date 2019/6/16 11:17
 */
@RestController
@RequestMapping("/ledger/teamConstruction")
public class TeamConstructionController extends BaseController {

    @Resource
    private IMemService iMemService;

    /**
     * 获取党员信息台账
     *
     * @return
     */
    @Validate(group = {Common1Group.class, QueryGroup.class})
    @RequiresPermissions
    @VerifyLevelCode
    @PostMapping("/getMemInfo")
    public OutMessage getMemInfo(@RequestBody InMessage<LedgerDTO> inMessage) {
        return iMemService.getMemInfo(inMessage.getData());
    }

}
