package com.zenith.front.web.controller.message;

import com.zenith.front.api.message.IMqMessageService;
import com.zenith.front.common.annotation.RequiresPermissions;
import com.zenith.front.model.dto.MessageIgnoreDTO;
import com.zenith.front.model.dto.MessageListDTO;
import com.zenith.front.model.message.InMessage;
import com.zenith.front.model.message.OutMessage;
import com.zenith.front.model.bean.User;
import com.zenith.front.web.controller.BaseController;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @version V1.0
 * @date 2022/7/27 16:31
 */
@RequestMapping("/message")
@RestController
public class MessageController extends BaseController {

    @Resource
    private IMqMessageService mqMessageService;

    @RequiresPermissions
    @PostMapping("list")
    public OutMessage<Object> list(@RequestBody InMessage<MessageListDTO> message) {
        MessageListDTO dto = message.getData();
        User user = this.getCurrUser().getUser();
        String orgCode = user.getOrgCode();
        String account = user.getAccount();
        return mqMessageService.findList(dto.getPageNum(), dto.getPageSize(), orgCode, account);
    }

    @RequiresPermissions
    @PostMapping("cat")
    public OutMessage<Object> cat(@RequestBody InMessage<MessageIgnoreDTO> message) {
        MessageIgnoreDTO dto = message.getData();
        User user = this.getCurrUser().getUser();
        String orgCode = user.getOrgCode();
        String account = user.getAccount();
        return mqMessageService.cat(dto.getCode(), false, orgCode, account);
    }

    @RequiresPermissions
    @PostMapping("ignore")
    public OutMessage<Object> ignore(@RequestBody InMessage<MessageIgnoreDTO> message) {
        MessageIgnoreDTO dto = message.getData();
        String account = this.getCurrUser().getUser().getAccount();
        return mqMessageService.cat(dto.getCode(), true, null, account);
    }
}
