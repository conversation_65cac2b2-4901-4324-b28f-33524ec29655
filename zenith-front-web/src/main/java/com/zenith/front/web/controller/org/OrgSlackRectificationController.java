package com.zenith.front.web.controller.org;

import com.zenith.front.api.org.IOrgSlackRectificationService;
import com.zenith.front.model.dto.OrgSlackRetifictionSaveDTO;
import com.zenith.front.model.message.InMessage;
import com.zenith.front.model.message.OutMessage;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @date 2021/8/27
 */
@Validated
@RestController
@RequestMapping("/org/slack/rectification")
public class OrgSlackRectificationController {

    @Resource
    private IOrgSlackRectificationService iOrgSlackRectificationService;

    /**
     * 获取涣散组织整备列表
     */
    @GetMapping("/getList")
    public OutMessage getList(String code){
        return iOrgSlackRectificationService.getList(code);
    }

    /**
     * 保存涣散组织整备列表
     */
    @PostMapping("saveSlackRectificationList")
    public OutMessage saveSlackRectificationList(@RequestBody InMessage<OrgSlackRetifictionSaveDTO> slackRectificationDTOList) throws Exception{
        return iOrgSlackRectificationService.saveSlackRectificationList(slackRectificationDTOList.getData());
    }
}
