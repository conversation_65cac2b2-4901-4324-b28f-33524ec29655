package com.zenith.front.web.scheduled;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.zenith.front.api.mem.IMemService;
import com.zenith.front.api.sync.ISyncMemService;
import com.zenith.front.api.transfer.IOrgTransferRecordService;
import com.zenith.front.api.transfer.ITransferApprovalService;
import com.zenith.front.api.transfer.ITransferRecordService;
import com.zenith.front.common.constant.CommonConstant;
import com.zenith.front.common.kit.HttpKit;
import com.zenith.front.dao.mapper.transfer.TransferApprovalMapper;
import com.zenith.front.dao.mapper.transfer.TransferRecordMapper;
import com.zenith.front.model.message.OutMessage;
import com.zenith.front.model.bean.Mem;
import com.zenith.front.model.bean.TransferApproval;
import com.zenith.front.model.bean.TransferRecord;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Configuration;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @author: D.watermelon
 * @date: 2021/11/15 23:55
 * @description: 入则恳恳以尽忠 出则谦谦以自悔
 */
@Configuration
public class PullTrensFerTask extends AbstractSchedulingConfigurer {
    @Value("${schedule.flow.open}")
    private Boolean open;
    @Value("${schedule.flow.cron}")
    private String cron;
    private static final Logger log = LoggerFactory.getLogger(PullTrensFerTask.class);
    @Resource(name = "transferRecordService")
    private ITransferRecordService transferRecordService;
    @Resource
    private ITransferApprovalService transferApprovalService;
    @Resource
    private IOrgTransferRecordService iOrgTransferRecordService;

    @Value("${exchange_nginx_key}")
    private String nodeKey;
    @Value("${sync_flow_push}")
    private String sync_flow_push;

    @Resource
    private TransferRecordMapper recordMapper;
    @Resource
    protected IMemService memService;
    @Resource
    private TransferApprovalMapper transferApprovalMapper;
    @Resource
    private ISyncMemService iSyncMemService;

    @Override
    public void task() {
        if (open) {
            //http://172.16.23.40:9099/api/flow/push
            String replaceUrl = sync_flow_push.replace("/api/flow/push", "");
            System.out.println("请求中间交换区地址=====>" + replaceUrl);
            List<TransferRecord> deailList = new ArrayList<>();
            List<String> inList = new ArrayList<>();
            List<String> outList = new ArrayList<>();
            List<TransferRecord> inOrgListRecord=new ArrayList<>();
            List<TransferRecord> outOrgListRecord=new ArrayList<>();
            // TODO: 2021/11/17 请求中间交换区中，转接到我的目的节点的数据
            JSONObject postJsonOne =new JSONObject();
            postJsonOne.put("type",CommonConstant.ONE_INT);
            postJsonOne.put("orgCode",nodeKey);
            String rultMessageAsTypeOne = HttpKit.doPost(replaceUrl + "/transfer/findData",postJsonOne,"UTF-8");
            this.deailData(rultMessageAsTypeOne, 1, deailList, inList,inOrgListRecord);
            // TODO: 2021/11/17 请求中间节点是我发起的数据回来处理
            JSONObject postJsonTwo =new JSONObject();
            postJsonTwo.put("type",CommonConstant.TWO_INT);
            postJsonTwo.put("orgCode",nodeKey);
            String rultMessageAsTypeTwo = HttpKit.doPost(replaceUrl + "/transfer/findData",postJsonTwo,"UTF-8" );
            this.deailData(rultMessageAsTypeTwo, 2, deailList, outList,outOrgListRecord);
            // TODO: 2021/12/23 存在一种情况，如果人先转出再回来或者是一个人先进来然后又出去，这种恢复人员得状态就会存在问题

            // TODO: 2021/12/28 获取我发起的整建制转接
            JSONObject postJsonThree= new JSONObject();
            postJsonThree.put("type",CommonConstant.THREE_INT);
            postJsonThree.put("orgCode",nodeKey);
            String rultMessageAsTypeThree = HttpKit.doPost(replaceUrl + "/transfer/findData",postJsonThree,"UTF-8" );
            this.deailEntireData(rultMessageAsTypeThree,inOrgListRecord);
            if (deailList.size() > 0) {
                //先根据时间排序，然后根据时间排序来循环处理数据
                deailList.sort(Comparator.comparing(TransferRecord::getCreateTime));
                deailList.forEach(transferRecord -> {
                    String type = transferRecord.getType();
                    //整建制和转出省外不需要处理数据情况，或者是不按照这种情况处理
                    if ("223".equals(type)|| "212".equals(type)) {
                        return;
                    }
                    //如果是我转出的并且已经完成的，需要置换为非空
                    String memId = transferRecord.getMemId();
                    String id = transferRecord.getId();
                    if (outList.contains(id)) {
                        //如果本地数据节点是属于未删除状态，则需要打上删除标识
//                        Mem allByCode = memService.findByCode(memId);
                        Mem allByCode = memService.findByCodeNoEncrypt(memId);
                        if (ObjectUtil.isNotNull(allByCode)) {
                            Mem mem = new Mem();
                            mem.setDeleteTime(new Date(1723121723000L));
                            mem.setId(allByCode.getId());
                            boolean updateById = memService.rewriteUpdateById(mem);
                            if (updateById){
                                iSyncMemService.syncMem(memId, CommonConstant.ONE);
                            }
                            // todo 2025-03-20:并且需要把本地的档案信息处理
                            if(StrUtil.isNotBlank(allByCode.getDigitalLotNo())) {
                                iOrgTransferRecordService.deleteDigLotNo(new HashSet<String>(){{add(allByCode.getDigitalLotNo());}});
                            }
                        }
                    }
                    //如果是转入到的并且已经完成的，需要置换为空
                    if (inList.contains(id)) {
                        //如果本地数据节点是属于删除状态，因为是转入到我这里得，所以需要重置为可见状态，并且这样也会产生一个bug，就是说转入到我这里得然后我自己离开党组织得会恢复回来。
//                        Mem allByCode = memService.findAllByCode(memId);
                        Mem allByCode = memService.findAllByCodeNoEncrypt(memId);
                        if (ObjectUtil.isNotNull(allByCode)) {
                            Mem mem = new Mem();
                            mem.setId(allByCode.getId());
                            mem.setUpdateTime(new Date(1723121723000L));
                            boolean update = memService.update(mem, new UpdateWrapper<Mem>().lambda()
                                    .set(Mem::getDeleteTime, null)
                                    .eq(Mem::getId, mem.getId()).isNull(Mem::getD12Code));
                            if (update){
                                iSyncMemService.syncMem(memId, CommonConstant.ONE);
                            }
                        }
                    }
                });
            }
            // TODO: 2021/12/9 请求处理中间交换区因为组织架构调整撤回的转接
            JSONObject postJson =new JSONObject();
            postJson.put("key",nodeKey);
            String rultMessageAsUnDo = HttpKit.doPost(replaceUrl + "/transfer/undoFindData",postJson,"UTF-8");
            this.deailUnDoData(rultMessageAsUnDo);
            log.warn("定时任务执行完成========>");
        }
    }

    public void deailUnDoData(String rultMessageAsUnDo) {
        JSONObject parse = JSONObject.parseObject(rultMessageAsUnDo);
        if (parse.containsKey("data")) {
            JSONArray datas = parse.getJSONArray("data");
            datas.forEach(o -> {
                JSONObject dataObject = (JSONObject) o;
                TransferRecord byId = transferRecordService.getById(dataObject.getString("transferId"));
                if (ObjectUtil.isNotNull(byId) && byId.getStatus().equals(CommonConstant.ZERO_INT)) {
                    // TODO: 2023/6/8 得增加撤销人员得处理，今年以来， 有48条数据，需要考虑同一个人，反复进入的问题。
                    transferRecordService.backTransfer(byId.getId(), "因当发起党组织或接收党组织发生架构调整系统自动撤回所有关系转接");
                }
            });
        }
    }

    private void deailData(String rultMessage, Integer type, List<TransferRecord> deailList, List<String> list,List<TransferRecord> orgListRecord) {
        JSONObject parse = JSONObject.parseObject(rultMessage);
        if (parse.containsKey("data")) {
            JSONArray datas = parse.getJSONArray("data");
            List<TransferApproval> transferApprovals = new ArrayList<>();
            List<TransferRecord> transferRecords = new ArrayList<>();
            List<TransferRecord> deleteTransFerList = new ArrayList<>();
            datas.forEach(o -> {
                JSONObject dataObject = (JSONObject) o;
                String letterNumber = dataObject.getString("letterNumber");
                // 上传介绍信中间交换信息
                String letterMessage = dataObject.getString("letterMessage");
                // 上传介绍信中间交换区反馈状态码
                String letterMessageCode = dataObject.getString("letterMessageCode");
                JSONObject data = dataObject.getJSONObject("data");
                data.put("letterNumber",letterNumber);
                data.put("letterMessage",letterMessage);
                data.put("letterMessageCode",letterMessageCode);

                JSONArray approval = data.getJSONArray("approval");
                data.remove("approval");
                JSONObject extraData = data.getJSONObject("extraData");
                data.remove("extraData");
                TransferRecord transferRecord = JSONObject.toJavaObject(data, TransferRecord.class);
                transferRecord.setExtraData(extraData);
                TransferRecord byId = transferRecordService.getById(transferRecord.getId());
                //删除本地数据
                if (ObjectUtil.isNotNull(byId)) {
                    deleteTransFerList.add(byId);
                }
                approval.forEach(object -> {
                    TransferApproval transferApproval = JSONObject.toJavaObject((JSONObject) object, TransferApproval.class);
                    transferApprovals.add(transferApproval);
                });

                Integer status = transferRecord.getStatus();
                if (type.equals(1) && status.equals(1)) {
                    list.add(transferRecord.getId());
                    orgListRecord.add(transferRecord);
                    deailList.add(transferRecord);
                }
                if (type.equals(2) && status.equals(1)) {
                    list.add(transferRecord.getId());
                    orgListRecord.add(transferRecord);
                    deailList.add(transferRecord);
                }
                transferRecords.add(transferRecord);
            });
            //删除处理
            if (deleteTransFerList.size() > 0) {
                deleteTransFerList.forEach(transferRecord -> {
                    String id = transferRecord.getId();
                    recordMapper.deleteById(transferRecord);
                    transferApprovalMapper.deleteTransferId(id);
                });
            }
            //新增入库
            if (transferRecords.size() > 0) {
                //transferRecordService.saveBatch(transferRecords);
                transferRecords.forEach(transferRecord -> transferRecordService.save(transferRecord));
            }
            if (transferApprovals.size() > 0) {
                //transferApprovalService.saveBatch(transferApprovals);
                transferApprovals.forEach(transferApproval -> transferApprovalService.save(transferApproval));
            }
        }
    }

    public void  deailEntireData(String rultMessage,List<TransferRecord> transferRecordList){
        JSONObject parse = JSONObject.parseObject(rultMessage);
        //单纯处理入库问题，并且记录需要处理的情况
        List<TransferRecord> deailList=new ArrayList<>();
        if (parse.containsKey("data")) {
            JSONArray datas = parse.getJSONArray("data");
            List<TransferApproval> transferApprovals = new ArrayList<>();
            List<TransferRecord> transferRecords = new ArrayList<>();
            List<TransferRecord> deleteTransFerList = new ArrayList<>();
            datas.forEach(o -> {
                JSONObject dataObject = (JSONObject) o;
                JSONObject data = dataObject.getJSONObject("data");
                JSONArray approval = data.getJSONArray("approval");
                data.remove("approval");
                JSONObject extraData = data.getJSONObject("extraData");
                data.remove("extraData");
                TransferRecord transferRecord = JSONObject.toJavaObject(data, TransferRecord.class);
                transferRecord.setExtraData(extraData);
                TransferRecord byId = transferRecordService.getById(transferRecord.getId());
                //删除本地数据
                if (ObjectUtil.isNotNull(byId)) {
                    deleteTransFerList.add(byId);
                }
                approval.forEach(object -> {
                    TransferApproval transferApproval = JSONObject.toJavaObject((JSONObject) object, TransferApproval.class);
                    transferApprovals.add(transferApproval);
                });
                Integer status = transferRecord.getStatus();
                if (status.equals(1)) {
                    deailList.add(transferRecord);
                }
                transferRecords.add(transferRecord);
            });
            //删除处理
            if (deleteTransFerList.size() > 0) {
                deleteTransFerList.forEach(transferRecord -> {
                    String id = transferRecord.getId();
                    recordMapper.deleteById(transferRecord);
                    transferApprovalMapper.deleteTransferId(id);
                });
            }
            //新增入库
            if (transferRecords.size() > 0) {
                transferRecordService.saveBatch(transferRecords);
            }
            if (transferApprovals.size() > 0) {
                transferApprovalService.saveBatch(transferApprovals);
            }
        }
        List<String> inList = transferRecordList.stream().map(TransferRecord::getSrcOrgId).collect(Collectors.toList());
        if (deailList.size()>0){
            for (TransferRecord transferRecord : deailList) {
                //需要特殊处理一下，同一个组织，在两个数据节点之间反复转得情况
                String srcOrgId = transferRecord.getSrcOrgId();
                Date outCreateTime = transferRecord.getCreateTime();
                //处理是否有同个id相互转得情况
                if (inList.contains(srcOrgId)){
                    Map<String, List<TransferRecord>> inTransferMap = transferRecordList.stream().collect(Collectors.groupingBy(TransferRecord::getSrcOrgId));
                    List<TransferRecord> transferRecords = inTransferMap.get(srcOrgId);
                    transferRecords.sort(Comparator.comparing(TransferRecord::getCreateTime));
                    TransferRecord lastInTransfer = transferRecords.get(transferRecords.size() - CommonConstant.ONE_INT);
                    Date lastCreateTime = lastInTransfer.getCreateTime();
                    //如果转入得时间晚于转出得时间，则不需要删除,反之则删除
                    if (lastCreateTime.before(outCreateTime)){
                        iOrgTransferRecordService.deailEntireData(transferRecord);
                    }
                }else {
                    iOrgTransferRecordService.deailEntireData(transferRecord);
                }
            }

        }
    }

    @Override
    public String cron() {
        return cron;
    }

}
