package com.zenith.front.web.scheduled;

import com.zenith.front.api.dataexchange.IExchangeXmlService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @Date 2022/1/11 21:53
 * @Version 1.0
 */
//@Configuration
public class CheckAndRepeatIdCardTask extends AbstractSchedulingConfigurer {

    @Resource
    private IExchangeXmlService exchangeXmlService;

    @Value("${idCard.open}")
    private Boolean open;
    @Value("${idCard.cron}")
    private String cron;

    private static final Logger log = LoggerFactory.getLogger(CheckAndRepeatIdCardTask.class);

    @Override
    public void task() {
        if (open) {
            log.warn("------------------开启身份证验证定时任务----------------------");
            //定时任务身份证错误信息
            exchangeXmlService.checkIdCard();
            //定时任务初始化身份证重复信息
            exchangeXmlService.repeatIdCard();
            log.warn("------------------身份证验证定时任务已完成----------------------");
        }
    }

    @Override
    public String cron() {
        return cron;
    }
}
