package com.zenith.front.web.controller.data;


import com.zenith.front.api.org.IOrgService;
import com.zenith.front.common.annotation.RequiresPermissions;
import com.zenith.front.core.kit.ExcelUtil;
import com.zenith.front.model.dto.OrgListDTO;
import com.zenith.front.model.message.InMessage;
import com.zenith.front.model.message.OutMessage;
import com.zenith.front.model.message.Status;
import com.zenith.front.model.validate.annotation.Validate;
import com.zenith.front.model.validate.group.Common1Group;
import com.zenith.front.model.validate.group.Common2Group;
import com.zenith.front.web.controller.BaseController;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;

/**
 * <AUTHOR>
 * @version 1.0
 * @Description: 组织
 * @date 2019/5/7 11:43
 */
@RestController
@RequestMapping("/data/org")
public class OrgDataController extends BaseController {

    @Resource
    private IOrgService iOrgService;

    /**
     * 导出组织数据
     *
     * @return
     */
    @Validate(group = {Common1Group.class, Common2Group.class})
    @RequiresPermissions
    @PostMapping("/exportData")
    public void exportData(@RequestBody InMessage<OrgListDTO> inMessage, HttpServletResponse response) {
        OrgListDTO messageData = inMessage.getData();
        String orgCode = messageData.getOrgCode();
        if (!orgCode.startsWith(this.getCurrManOrgCode())) {
            ExcelUtil.exportErrorExcel(response, new OutMessage<>(Status.PERMISSION_DENIED));
            return;
        }
        iOrgService.exportOrgData(messageData);
    }
}
