package com.zenith.front.web.controller.sync;

import com.zenith.front.api.sync.ISyncMemService;
import com.zenith.front.core.service.sync.SyncOrgRecognitionServiceImpl;
import com.zenith.front.core.service.sync.SyncOrgSlackServiceImpl;
import com.zenith.front.model.message.OutMessage;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @date 2021/10/25
 */
@Validated
@RestController
@RequestMapping("/sync")
public class SyncDataController {
    @Resource
    private ISyncMemService iSyncMemService;
    @Resource
    private SyncOrgSlackServiceImpl syncOrgSlackService;
    @Resource
    private SyncOrgRecognitionServiceImpl syncOrgRecognitionService;


    @GetMapping("/syncMem")
    public OutMessage<?> syncMem(@NotNull(message = "code 不能为空") String code, String type) {
        return iSyncMemService.syncMem(code, type);
    }


    @GetMapping("/syncMemDevelop")
    public OutMessage<?> syncMemDevelop(@NotNull(message = "code 不能为空") String code) {
        return iSyncMemService.syncMemDevelop(code);
    }


    @GetMapping("/syncMemReward")
    public OutMessage<?> syncMemReward(@NotNull(message = "code 不能为空") String code) {
        return iSyncMemService.syncMemReward(code);
    }


    @GetMapping("/syncMemFlow")
    public OutMessage<?> syncMemFlow(@NotNull(message = "code 不能为空") String code) {
        return iSyncMemService.syncMemFlow(code);
    }


    @GetMapping("/syncOrgSlack")
    public OutMessage<?> syncOrgSlack(@NotNull(message = "code 不能为空") String code) {
        return syncOrgSlackService.syncOrgSlack(code);
    }

    @GetMapping("/syncOrgRecognition")
    public OutMessage<?> syncOrgRecognition(@NotNull(message = "code 不能为空") String code) {
        return syncOrgRecognitionService.syncOrgRecognition(code);
    }


}
