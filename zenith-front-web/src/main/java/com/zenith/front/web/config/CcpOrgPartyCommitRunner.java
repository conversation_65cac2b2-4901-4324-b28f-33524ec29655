package com.zenith.front.web.config;

import com.zenith.front.api.org.IOrgPartyCongressCommitteeService;
import com.zenith.front.dao.mapper.org.OrgPartyCongressCommitteeMapper;
import com.zenith.front.model.bean.OrgPartyCongressCommittee;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.CommandLineRunner;
import org.springframework.core.annotation.Order;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;
import java.util.concurrent.atomic.AtomicReference;

/**
 * @author: D.watermelon
 * @date: 2022/1/24 17:06
 * @Remark 剔除关于党代表重复得相关人，处理掉重复得党代表
 * @description: 入则恳恳以尽忠 出则谦谦以自悔
 */

//@Component
@Order(103)
@Slf4j
public class CcpOrgPartyCommitRunner implements CommandLineRunner {
    @Resource
    private OrgPartyCongressCommitteeMapper orgPartyCongressCommitteeMapper;
    @Resource
    private IOrgPartyCongressCommitteeService  iOrgPartyCongressCommitteeService;

    @Override
    public void run(String... args) {
        log.info("系统开始处理重复党代表问题");
        //获取库里面所有得人员重复得code
        AtomicReference<Integer> total = new AtomicReference<>(0);
        List<OrgPartyCongressCommittee> allRepeatCommit = orgPartyCongressCommitteeMapper.findAllRepeatCommit();
        allRepeatCommit.forEach(orgPartyCongressCommittee -> {
            String memCode = orgPartyCongressCommittee.getMemCode();
            //查询单个人员是否重复党代表问题
            List<OrgPartyCongressCommittee> repeatByMemCode = orgPartyCongressCommitteeMapper.findRepeatByMemCode(memCode);
            //证明有重复数据
            if (repeatByMemCode.size() > 0) {
                String orgCode = repeatByMemCode.get(0).getOrgCode();
                //获取重复数据，选择其中一条删除掉
                List<OrgPartyCongressCommittee> allByMemCode = orgPartyCongressCommitteeMapper.findAllByMemCodeAndOrgCode(memCode,orgCode);
                if (allByMemCode.size()>1){
                    OrgPartyCongressCommittee updateCommit = allByMemCode.get(0);
                    updateCommit.setDeleteTime(new Date(1659953898000L));
                    iOrgPartyCongressCommitteeService.updateById(updateCommit);
                    total.getAndSet(total.get() + 1);
                }
            }
        });
        log.info("系统处理重复党代表问题查询{}条，处理:{}条",allRepeatCommit.size(),total.get());
    }
}
