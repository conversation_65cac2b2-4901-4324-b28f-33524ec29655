package com.zenith.front.web.runner;

import cn.hutool.core.thread.ThreadUtil;
import cn.hutool.core.util.StrUtil;
import com.b1809.analysis.Analysis;
import com.b1809.analysis.entity.DbType;
import com.b1809.analysis.entity.StartConfig;
import com.b1809.analysis.extend.GenSqlConditionFuc;
import com.b1809.analysis.extend.ITableCount;
import com.b1809.analysis.plugin.cache.CachePluginConfig;
import com.b1809.analysis.plugin.db.DbPluginConfig;
import com.b1809.analysis.plugin.elasticsearch.EsPluginConfig;
import com.b1809.analysis.result.ReportResult;
import com.b1809.analysis.rule.ReportRule;
import com.b1809.analysis.rule.ReportVerificationRule;
import com.b1809.sync.DbSyncStart;
import com.b1809.sync.config.SyncConfig;
import com.b1809.sync.pg.config.PgConfig;
import com.jfinal.plugin.activerecord.dialect.PostgreSqlDialect;
import com.zenith.front.api.analysis.IAnalysisService;
import com.zenith.front.common.annualstatisticsconf.AnnualStatisticsConstant;
import com.zenith.front.common.password.FMSym;
import com.zenith.front.core.analysis.cache.AnalysisCacheImpl;
import com.zenith.front.core.analysis.consumer.EsConsumer;
import com.zenith.front.core.analysis.count.*;
import com.zenith.front.core.analysis.count.history.*;
import com.zenith.front.core.analysis.count.year2023.*;
import com.zenith.front.core.analysis.count.year2024.*;
import com.zenith.front.core.analysis.ext.condition.*;
import com.zenith.front.core.analysis.ext.condition.year2021.*;
import com.zenith.front.core.analysis.ext.condition.year2022.*;
import com.zenith.front.core.analysis.ext.condition.year2023.*;
import com.zenith.front.core.analysis.ext.condition.year2024.*;
import com.zenith.front.core.service.sync.SyncOrgRecognitionServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.ApplicationArguments;
import org.springframework.boot.ApplicationRunner;
import org.springframework.context.annotation.Import;
import org.springframework.stereotype.Component;
import org.springframework.util.ResourceUtils;

import javax.annotation.Resource;
import java.net.URLDecoder;
import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

/**
 * 启动事件
 *
 * <AUTHOR>
 */
@Component
@Slf4j
@Import({ReportResult.class, ReportRule.class, ReportVerificationRule.class, Analysis.class, DbSyncStart.class})
public class StartedEvent implements ApplicationRunner {
    @Resource
    private IAnalysisService analysisService;
    @Value("${es.ip}")
    private String esIp;
    @Value("${es.port}")
    private Integer esPort;
    @Value("${es.username}")
    private String esUsername;
    @Value("${es.password}")
    private String esPassword;
    @Value("${es.enable}")
    private boolean esAnalysisEnable;
    @Value("${spring.datasource.url}")
    private String dbUrl;
    @Value("${spring.datasource.username}")
    private String dbUsername;
    @Value("${spring.datasource.password}")
    private String dbPassword;
    @Value("${sync.es.slot_name}")
    private String slotName;
    @Resource
    private AnalysisCacheImpl analysisCache;
    @Resource
    private Analysis analysis;
    @Resource
    private DbSyncStart dbSyncStart;
    @Resource
    private SyncOrgRecognitionServiceImpl syncOrgRecognitionService;

    @Override
    public void run(ApplicationArguments args) {
        startAnalysis();
        startDbSync();

    }



    /**
     * 初始统计模块
     */
    private void startAnalysis() {
        if (!esAnalysisEnable) {
            log.warn("Es统计插件未启动！");
            return;
        }

        // 内网: 年统执行-在es执行前处理数据
//        syncOrgRecognitionService.syncSjRecognitionData();
//        syncOrgRecognitionService.syncOrgPartyCongressCommittee();
//        syncOrgRecognitionService.setNonPublicParty();


        // es 插件配置
        final EsPluginConfig elastic = EsPluginConfig.builder().ip(esIp).port(esPort)
                .username(esUsername).password(esPassword)
                .conditionFucList(getConditionFucList())
                .tableCountList(getTableCountList()).build();

        // db 插件配置
        final DbPluginConfig db = DbPluginConfig.builder().url(dbUrl)
                .username(dbUsername).password(dbPassword)
                .dialect(new PostgreSqlDialect()).build();

        // 缓存插件
        final CachePluginConfig cacheConfig = CachePluginConfig.builder()
                .hasCache(false).analysisCache(analysisCache).build();

        final boolean start = analysis.setConfig(StartConfig.builder()
                .dbConfig(db)
                .esConfig(elastic)
                // todo 线上环境换成 DbType.ES
                .dbType(DbType.ES)
                .cacheConfig(cacheConfig)
                .build()).start();

        if (!start) {
            log.error("Analysis Core Start Fail!");
        } else {
            ThreadUtil.execute(() -> analysisService.init(null));
            log.info("Analysis Core Start Success!");
        }
    }

    private List<GenSqlConditionFuc> getConditionFucList() {
        // 查询条件扩展类
        List<GenSqlConditionFuc> conditionFucList = new ArrayList<>(16);
        conditionFucList.add(new MemDevelopAllCondition());
        conditionFucList.add(new MemAllCondition());
        conditionFucList.add(new OrgAllCondition());
        conditionFucList.add(new OrgRecognitionAllCondition());
        conditionFucList.add(new OrgSlackAllCondition());
        conditionFucList.add(new UnitAllCondition());
        conditionFucList.add(new OrgIndustryAllCondition());
        conditionFucList.add(new DevelopStepLogAllCondition());
        conditionFucList.add(new OrgPartyCongressCommitteeAllCondition());
        conditionFucList.add(new TransferStatisticsCondition());
        conditionFucList.add(new OrgPartyCondition());
        conditionFucList.add(new MemFlowStatisticsCondition());
        conditionFucList.add(new OrgCommitteeCondition());
        conditionFucList.add(new MemReportCondition());

        // 2024年度
        conditionFucList.add(new MemDevelopAllCondition2024());
        conditionFucList.add(new MemAllCondition2024());
        conditionFucList.add(new OrgAllCondition2024());
        conditionFucList.add(new OrgRecognitionAllCondition2024());
        conditionFucList.add(new OrgSlackAllCondition2024());
        conditionFucList.add(new UnitAllCondition2024());
        conditionFucList.add(new OrgIndustryAllCondition2024());
        conditionFucList.add(new DevelopStepLogAllCondition2024());
        conditionFucList.add(new OrgPartyCongressCommitteeAllCondition2024());
        conditionFucList.add(new TransferStatisticsCondition2024());
        conditionFucList.add(new OrgPartyCondition2024());
        conditionFucList.add(new MemFlowStatisticsCondition2024());
        conditionFucList.add(new OrgCommitteeCondition2024());
        conditionFucList.add(new MemReportCondition2024());

        // 2023年度
        conditionFucList.add(new MemDevelopAllCondition2023());
        conditionFucList.add(new MemAllCondition2023());
        conditionFucList.add(new OrgAllCondition2023());
        conditionFucList.add(new OrgRecognitionAllCondition2023());
        conditionFucList.add(new OrgSlackAllCondition2023());
        conditionFucList.add(new UnitAllCondition2023());
        conditionFucList.add(new OrgIndustryAllCondition2023());
        conditionFucList.add(new DevelopStepLogAllCondition2023());
        conditionFucList.add(new OrgPartyCongressCommitteeAllCondition2023());
        conditionFucList.add(new TransferStatisticsCondition2023());
        conditionFucList.add(new OrgPartyCondition2023());
        conditionFucList.add(new MemFlowStatisticsCondition2023());
        conditionFucList.add(new OrgCommitteeCondition2023());
        conditionFucList.add(new MemReportCondition2023());

        conditionFucList.add(new MemDevelopAllCondition202302());
        conditionFucList.add(new MemAllCondition202302());
        conditionFucList.add(new OrgAllCondition202302());
        conditionFucList.add(new OrgRecognitionAllCondition202302());
        conditionFucList.add(new OrgSlackAllCondition202302());
        conditionFucList.add(new UnitAllCondition202302());
        conditionFucList.add(new OrgIndustryAllCondition202302());
        conditionFucList.add(new DevelopStepLogAllCondition202302());
        conditionFucList.add(new OrgPartyCongressCommitteeAllCondition202302());
        conditionFucList.add(new TransferStatisticsCondition202302());
        conditionFucList.add(new OrgPartyCondition202302());
        conditionFucList.add(new MemFlowStatisticsCondition202302());
        conditionFucList.add(new OrgCommitteeCondition202302());
        conditionFucList.add(new MemReportCondition202302());

        //2021年度
        conditionFucList.add(new MemFlowAll2021Condition());
        conditionFucList.add(new MemDevelopAllCondition2021());
        conditionFucList.add(new MemAllCondition2021());
        conditionFucList.add(new MemRewardAll2021Condition());
        conditionFucList.add(new OrgAllCondition2021());
        conditionFucList.add(new OrgRecognitionAllCondition2021());
        conditionFucList.add(new OrgSlackAllCondition2021());
        conditionFucList.add(new UnitAllCondition2021());
        conditionFucList.add(new OrgIndustryAllCondition2021());
        conditionFucList.add(new DevelopStepLogAllCondition2021());
        conditionFucList.add(new OrgPartyCongressCommitteeAllCondition2021());
        conditionFucList.add(new OrgPartyCondition2021());
        //2022半年度
        conditionFucList.add(new MemAllCondition20220630());
        conditionFucList.add(new DevelopStepLogAllCondition20220630());
        conditionFucList.add(new OrgAllCondition20220630());
        conditionFucList.add(new TransferStatisticsCondition20220630());
        conditionFucList.add(new UnitAllCondition20220630());
        conditionFucList.add(new OrgPartyCondition20220630());
        //2022年
        conditionFucList.add(new DevelopStepLogAllConditionA());
        conditionFucList.add(new DevelopStepLogAllConditionB());
        conditionFucList.add(new MemAllCondition2022A());
        conditionFucList.add(new MemAllCondition2022B());
        conditionFucList.add(new MemDevelopAllConditionA());
        conditionFucList.add(new MemDevelopAllConditionB());
        conditionFucList.add(new MemFlowStatisticsConditionA());
        conditionFucList.add(new MemFlowStatisticsConditionB());
        conditionFucList.add(new OrgAllConditionA());
        conditionFucList.add(new OrgAllConditionB());
        conditionFucList.add(new OrgIndustryAllConditionA());
        conditionFucList.add(new OrgIndustryAllConditionB());
        conditionFucList.add(new OrgPartyConditionA());
        conditionFucList.add(new OrgPartyConditionB());
        conditionFucList.add(new OrgPartyCongressCommitteeAllConditionA());
        conditionFucList.add(new OrgPartyCongressCommitteeAllConditionB());
        conditionFucList.add(new OrgRecognitionAllConditionA());
        conditionFucList.add(new OrgRecognitionAllConditionB());
        conditionFucList.add(new OrgSlackAllConditionA());
        conditionFucList.add(new OrgSlackAllConditionB());
        conditionFucList.add(new TransferStatisticsConditionA());
        conditionFucList.add(new TransferStatisticsConditionB());
        conditionFucList.add(new UnitAllConditionA());
        conditionFucList.add(new UnitAllConditionB());

        // 统计自定义查询条件注册 todo 设置DbType.ES可以不用手动注册（es中初始化注册）
        for (GenSqlConditionFuc genSqlConditionFuc : conditionFucList) {
            genSqlConditionFuc.register();
        }

        return conditionFucList;
    }

    private List<ITableCount> getTableCountList() {
        // 无法配置的特殊表扩展类
        List<ITableCount> list = new ArrayList<>(16);
        list.add(new Html1Count());
        list.add(new Html7Count());
        list.add(new Html7CountBackups());
        list.add(new Html8Count());
        list.add(new Html9Count());
        list.add(new Html10Count());
        list.add(new Html14Count());
        list.add(new Html15Count());
        list.add(new Html26Count());
        list.add(new Html40Count());
        list.add(new Html41Count());
        list.add(new Html42Count());
        list.add(new Html46Count());
        list.add(new Html46CountBackups());
        list.add(new Html53Count());
        list.add(new Html53CountBackups());
        list.add(new Html59Count());
        list.add(new Html62Count());

        // 2024年度
        list.add(new Html1Count2024());
        list.add(new Html7Count2024());
        list.add(new Html7CountBackups2024());
        list.add(new Html8Count2024());
        list.add(new Html9Count2024());
        list.add(new Html10Count2024());
        list.add(new Html14Count2024());
        list.add(new Html15Count2024());
        list.add(new Html26Count2024());
        list.add(new Html40Count2024());
        list.add(new Html41Count2024());
        list.add(new Html42Count2024());
        list.add(new Html46Count2024());
        list.add(new Html46CountBackups2024());
        list.add(new Html53Count2024());
        list.add(new Html53CountBackups2024());
        list.add(new Html59Count2024());
        list.add(new Html62Count2024());

        //2021年度
        list.add(new Html6Count2021());
        list.add(new Html7Count2021());
        list.add(new Html9Count2021());
        list.add(new Html13Count2022());
        list.add(new Html14Count2021());
        list.add(new Html15Count2021());
        list.add(new Html24Count2021());
        list.add(new Html37Count2021());
        list.add(new Html43Count2021());
        list.add(new Html45Count2021());
        list.add(new Html48Count2021());
        list.add(new Html52Count2021());
        list.add(new Html55Count2021());
        //2022半年度
        list.add(new Html1Count20220630());
        list.add(new Html24Count20220630());
        //2022年度
        list.add(new Html1Count2022());
        list.add(new Html6Count2022A());
        list.add(new Html6Count2022B());
        list.add(new Html6CountBackups2022A());
        list.add(new Html6CountBackups2022B());
        list.add(new Html7Count2022());
        list.add(new Html8Count2022());
        list.add(new Html9Count2022());
        list.add(new Html13Count2022());
        list.add(new Html14Count2022());
        list.add(new Html24Count2022());
        list.add(new Html37Count2022());
        list.add(new Html38Count2022());
        list.add(new Html39Count2022());
        list.add(new Html43Count2022());
        list.add(new Html43CountBackups2022());
        list.add(new Html48Count2022());
        list.add(new Html48CountBackups2022());
        list.add(new Html52Count2022());
        list.add(new Html55Count2022());
        // 2023年度
        list.add(new Html1Count2023());
        list.add(new Html7Count202302());
        list.add(new Html7CountBackups202302());
        list.add(new Html8Count2023());
        list.add(new Html9Count2023());
        list.add(new Html10Count2023());
        list.add(new Html14Count2023());
        list.add(new Html14Count2023());
        list.add(new Html26Count2023());
        list.add(new Html40Count2023());
        list.add(new Html41Count2023());
        list.add(new Html42Count2023());
        list.add(new Html46Count202302());
        list.add(new Html46CountBackups202302());
        list.add(new Html52Count2023());
        list.add(new Html52CountBackups2023());
        list.add(new Html58Count2023());
        list.add(new Html61Count2023());

        // 统计实现类注册 todo 设置DbType.ES可以不用手动注册（es中初始化注册）
        for (ITableCount iTableCount : list) {
            iTableCount.register();
        }

        return list;
    }

    /**
     * 启动数据同步模块
     */
    public void startDbSync() {
        if (!AnnualStatisticsConstant.SYNC_ES_ENABLE) {
            log.warn("数据库同步Es插件未启动！");
            return;
        }
        final PgConfig pgConfig = new PgConfig()
                .setUrl(dbUrl)
                .setUserName(dbUsername)
                .setPassword(dbPassword)
                .setSyncTableSet(getSyncTableName());
        if (StrUtil.isNotBlank(slotName)) {
            pgConfig.setSlotName(slotName);
        }

        final boolean start = dbSyncStart.setConfig(new SyncConfig()
                        .setPgConfig(pgConfig)
                        .setConsumer(new EsConsumer()))
                .start();
        if (!start) {
            log.error("Dbsync Module Start Fail!");
        } else {
            log.info("Dbsync Module Start Success!");
        }
    }

    private Set<String> getSyncTableName() {
        Set<String> set = new HashSet<>();
        set.add("ccp_mem_all");
        set.add("ccp_mem_develop_all");
//        set.add("ccp_mem_flow_all");
        set.add("ccp_org_all");
        set.add("ccp_mem_reward_all");
        set.add("ccp_org_recognition_all");
        set.add("ccp_org_slack_all");
        set.add("ccp_unit_all");
        set.add("ccp_org_industry_all");
        set.add("ccp_develop_step_log_all");
        set.add("ccp_org_party_congress_committee_all");
        set.add("ccp_transfer_statistics");
        set.add("ccp_org_party");
        set.add("mem_flow_all");
        set.add("ccp_mem_report");
        return set;
    }
}
