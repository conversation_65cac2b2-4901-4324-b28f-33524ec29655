package com.zenith.front.web.controller.mem;

import com.zenith.front.api.mem.IMemFlowSignService;
import com.zenith.front.common.annotation.RequiresPermissions;
import com.zenith.front.common.constant.OperateConstant;
import com.zenith.front.model.dto.MemFlowSignDTO;
import com.zenith.front.model.message.InMessage;
import com.zenith.front.model.message.OutMessage;
import com.zenith.front.model.validate.annotation.Validate;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @since 2025-01-21 21:10
 **/
@RestController
@RequestMapping("/mem/flow/sign")
public class MemFlowSignController {

    @Resource
    private IMemFlowSignService service;

    @Validate
    @PostMapping("/add")
    @RequiresPermissions(opt = OperateConstant.INSERT)
    public OutMessage add(@RequestBody InMessage<MemFlowSignDTO> dto) {
        return service.add(dto.getData());
    }

}
