package com.zenith.front.web.controller.mem;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.zenith.front.api.mem.IMemFlowMessageService;
import com.zenith.front.common.annotation.ExcludeHttpBodyDecrypt;
import com.zenith.front.common.annotation.RequiresPermissions;
import com.zenith.front.common.constant.OperateConstant;
import com.zenith.front.model.bean.MemFlowMessage;
import com.zenith.front.model.dto.MemFlowMessageDto;
import com.zenith.front.model.message.InMessage;
import com.zenith.front.model.message.OutMessage;
import com.zenith.front.model.validate.annotation.Validate;
import com.zenith.front.model.validate.group.Common1Group;
import com.zenith.front.model.validate.group.Common2Group;
import com.zenith.front.web.controller.BaseController;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

@RestController
@RequestMapping("/memFlowMessage")
public class MemFlowMessageController extends BaseController {

    @Resource
    IMemFlowMessageService service;

    /**
     * 添加消息
     * @param dto
     * @return
     */
    @PostMapping("/add")
    @Validate(group = {Common1Group.class})
    @RequiresPermissions(opt = OperateConstant.INSERT)
    public OutMessage add(@RequestBody InMessage<MemFlowMessageDto> dto) {
        return service.add(dto.getData());
    }

    @PostMapping("/list")
    @Validate(group = Common2Group.class)
    @RequiresPermissions(opt = OperateConstant.QUERY)
    public OutMessage<Page<MemFlowMessage>> list(@RequestBody InMessage<MemFlowMessageDto> dto) {
        return service.list(dto.getData());
    }





}
