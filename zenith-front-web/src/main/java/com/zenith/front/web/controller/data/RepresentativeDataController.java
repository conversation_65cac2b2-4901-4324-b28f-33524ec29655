package com.zenith.front.web.controller.data;


import com.zenith.front.api.representative.IRepresentativeService;
import com.zenith.front.common.annotation.RequiresPermissions;
import com.zenith.front.core.kit.ExcelUtil;
import com.zenith.front.model.dto.RepresentativeListDTO;
import com.zenith.front.model.message.InMessage;
import com.zenith.front.model.message.OutMessage;
import com.zenith.front.model.message.Status;
import com.zenith.front.model.validate.annotation.Validate;
import com.zenith.front.model.validate.group.Common1Group;
import com.zenith.front.model.validate.group.Common2Group;
import com.zenith.front.web.controller.BaseController;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;

/**
 * <AUTHOR>
 * @version 1.0
 * @Description: 党代表
 * @date 2019/5/7 18:31
 */
@RestController
@RequestMapping("/data/Representative")
public class RepresentativeDataController extends BaseController {

    @Resource
    private IRepresentativeService iRepresentativeService;

    /**
     * 导出党代表数据
     *
     * @return
     */
    @Validate(group = {Common1Group.class, Common2Group.class})
    @RequiresPermissions
    @PostMapping("/exportData")
    public void exportData(@RequestBody InMessage<RepresentativeListDTO> inMessage, HttpServletResponse response) {
        RepresentativeListDTO representativeListDTO = inMessage.getData();
        String orgCodeLevel = representativeListDTO.getOrgCodeLevel();
        if (!orgCodeLevel.startsWith(this.getCurrManOrgCode())) {
            ExcelUtil.exportErrorExcel(response, new OutMessage<>(Status.PERMISSION_DENIED));
            return;
        }
        iRepresentativeService.exportRepresentativeData(representativeListDTO);
    }
}
