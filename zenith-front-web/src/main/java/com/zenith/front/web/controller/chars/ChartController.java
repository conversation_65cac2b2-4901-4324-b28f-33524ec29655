package com.zenith.front.web.controller.chars;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.date.LocalDateTimeUtil;
import cn.hutool.core.util.StrUtil;
import com.zenith.front.api.dict.IDictChartService;
import com.zenith.front.api.mem.IMemFlowInspectionFormService;
import com.zenith.front.common.annotation.RequiresPermissions;
import com.zenith.front.core.service.dict.DataStatisticalServiceImpl;
import com.zenith.front.model.dto.ChartDataDTO;
import com.zenith.front.model.dto.ChartDataPeggListDto;
import com.zenith.front.model.dto.ChartsListDTO;
import com.zenith.front.model.dto.ReportExcelDTO;
import com.zenith.front.model.message.InMessage;
import com.zenith.front.model.message.OutMessage;
import com.zenith.front.model.validate.annotation.Validate;
import com.zenith.front.web.controller.BaseController;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.constraints.NotBlank;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.ZoneId;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @Description: 概况统一请求
 * @date 2019/4/28 11:01
 */
@Validated
@RestController
@RequestMapping("/chart")
public class ChartController extends BaseController {

    @Resource
    private IDictChartService chartService;
    @Resource
    private DataStatisticalServiceImpl dataStatisticalService;
    @Resource
    private IMemFlowInspectionFormService memFlowInspectionFormService;

    /**
     * 保存概况展示项
     *
     * @param inMessage
     * @return
     */
    @RequiresPermissions
    @PostMapping("/saveChart")
    public OutMessage saveChart(@RequestBody InMessage<ChartsListDTO> inMessage) throws Exception {
        ChartsListDTO messageData = inMessage.getData();
        return chartService.saveChart(messageData);
    }

    /**
     * 根据类型获取模块概况
     *
     * @param chartType
     * @return
     */
    @Validate
    @RequiresPermissions
    @GetMapping("/findByChartType")
    public OutMessage findByChartType(@NotBlank(message = "chartType 不能为空") String chartType) {
        return chartService.findByChartType(chartType);
    }

    /**
     * 获取卡片字典表
     *
     * @param chartType
     * @return
     */
    @Validate
    @RequiresPermissions
    @GetMapping("/getDictChartList")
    public OutMessage getDictChartList(@NotBlank(message = "chartType 不能为空") String chartType) {
        return chartService.getDictChartList(chartType);
    }


    /**
     * 获取首页统计数据
     */
    @RequiresPermissions
    @PostMapping("/getIndexCount")
    public OutMessage<?> getIndexCount(@Validated @RequestBody InMessage<ChartDataDTO> inMessage) throws Exception {
        return chartService.getIndexCount(inMessage.getData());
    }

    @RequiresPermissions
    @PostMapping("/getIndexData")
    public OutMessage<?> getIndexData(@Validated @RequestBody InMessage<ChartDataDTO> inMessage) throws Exception {
        return chartService.getIndexData(inMessage.getData());
    }

    /**
     * 获取数据统计反查列表导出
     */
    @RequiresPermissions
    @PostMapping("/exportIndexDataCountPegging")
    public void exportIndexDataCountPegging(@Validated @RequestBody InMessage<ChartDataPeggListDto> inMessage) {
        dataStatisticalService.exportIndexDataCountPegging(inMessage.getData());
    }

    /**
     * 获取数据统计反查列表
     */
    @RequiresPermissions
    @PostMapping("/getIndexDataCountPegging")
    public OutMessage<?> getIndexDataCountPegging(@Validated @RequestBody InMessage<ChartDataPeggListDto> inMessage) {
        return dataStatisticalService.getIndexDataCountPegging(inMessage.getData());
    }

    /**
     * 获取年报表数据
     */
    @RequiresPermissions
    @PostMapping("/getYearReport")
    public OutMessage<?> getYearReport(@Validated @RequestBody InMessage<ChartDataDTO> inMessage) throws Exception {
        return chartService.getYearReport(inMessage.getData());
    }


    /**
     * 导出年报表数据word
     */
    @RequiresPermissions
    @PostMapping("/exportYearReport")
    public OutMessage<?> exportYearReport(@Validated @RequestBody InMessage<ChartDataDTO> inMessage) throws Exception {
        return chartService.exportYearReport(inMessage.getData());
    }

    /**
     * 可支持统计的调度表
     */
    @RequiresPermissions
    @PostMapping("/supportExcel")
    public OutMessage<?> supportExcel() {
        return chartService.supportExcel();
    }

    /**
     * 农村党建调度表
     */
    @RequiresPermissions
    @PostMapping("/reportExcel")
    public OutMessage<?> reportExcel(@Validated @RequestBody InMessage<ReportExcelDTO> inMessage) {
        return chartService.reportExcel(inMessage.getData());
    }

    /**
     * 农村党建调度表反查
     */
    @RequiresPermissions
    @PostMapping("/peggingExcel")
    public OutMessage<?> peggingExcel(@Validated @RequestBody InMessage<ReportExcelDTO> inMessage) {
        return chartService.peggingExcel(inMessage.getData());
    }

    /**
     * 农村党建调度表导出
     */
    @RequiresPermissions
    @PostMapping("/exportExcel")
    public void exportExcel(@Validated @RequestBody InMessage<ReportExcelDTO> inMessage) throws Exception {
        chartService.exportExcel(inMessage.getData());
    }

    /**
     * 党组织设置情况督查表
     */
    @RequiresPermissions
    @PostMapping("/workSupervision")
    public OutMessage<?> workSupervision(@Validated @RequestBody InMessage<ReportExcelDTO> inMessage) {
        return chartService.workSupervision(inMessage.getData());
    }

    /**
     * 党组织设置情况督查表导出
     */
    @RequiresPermissions
    @PostMapping("/exportWorkSupervisionExcel")
    public void exportWorkSupervisionExcel(@Validated @RequestBody InMessage<ReportExcelDTO> inMessage) throws Exception {
        chartService.exportWorkSupervisionExcel(inMessage.getData());
    }

    /**
     * 党组织设置情况督查表反查
     */
    //@RequiresPermissions
    @PostMapping("/peggingWorkSupervision")
    public OutMessage<?> peggingWorkSupervision(@Validated @RequestBody InMessage<ReportExcelDTO> inMessage) {
        return chartService.peggingWorkSupervision(inMessage.getData());
    }

    /**
     * 党组织设置情况督查表反查导出
     */
    @RequiresPermissions
    @PostMapping("/exportWorkSupervisionPeggingExcel")
    public void exportWorkSupervisionPeggingExcel(@Validated @RequestBody InMessage<ReportExcelDTO> inMessage) throws Exception {
        if (StrUtil.equals(inMessage.getData().getReportCode(), "1")) {
            chartService.exportWorkSupervisionPeggingExcel(inMessage.getData());
        } else {
            chartService.exportWorkSupervisionPeggingExcelNew(inMessage.getData());
        }
    }




    /**
     * dyx
     * 党组织设置情况督查表新增统计项
     */
    @RequiresPermissions
    @PostMapping("/workSupervisionNew")
    public OutMessage<?> workSupervisionNew(@Validated @RequestBody InMessage<ReportExcelDTO> inMessage) throws ParseException {
        return chartService.workSupervisionNew(inMessage.getData());
    }


    /**
     * 农村党建调度表new新增统计项
     */
    @RequiresPermissions
    @PostMapping("/reportNewExcel")
    public OutMessage<?> reportNewExcel(@Validated @RequestBody InMessage<ReportExcelDTO> inMessage) {
        return chartService.reportNewExcel(inMessage.getData());
    }


    /**
     * 党支部联系点工作开展情况统计表
     * @param inMessage
     * @return
     */
    @RequiresPermissions
    @PostMapping("/contactWorkCondition")
    public OutMessage<?> contactWorkCondition(@Validated @RequestBody InMessage<ReportExcelDTO> inMessage) {
        return chartService.contactWorkCondition(inMessage.getData());
    }

    /**
     * 党组织设置情况督查表反查
     */
    @RequiresPermissions
    @PostMapping("/peggingWorkSupervisionNew")
    public OutMessage<?> peggingWorkSupervisionNew(@Validated @RequestBody InMessage<ReportExcelDTO> inMessage) {
        return chartService.peggingWorkSupervisionNew(inMessage.getData());
    }

    /**
     * 党支部联系点工作开展情况统计表
     * @param inMessage
     * @return
     */
    @RequiresPermissions
    @PostMapping("/peggingContactWorkCondition")
    public OutMessage<?> peggingContactWorkCondition(@Validated @RequestBody InMessage<ReportExcelDTO> inMessage) {
        return chartService.peggingContactWorkCondition(inMessage.getData());
    }

    /**
     * 反查通用接口
     * 1.发展党员
     */
    @RequiresPermissions
    @PostMapping("/peggingExcelNew")
    public OutMessage<?> peggingExcelNew(@Validated @RequestBody InMessage<ReportExcelDTO> inMessage) {
        return chartService.peggingExcelNew(inMessage.getData());
    }

    /**
     * grassRoots/grassRootsOrgLifeExcel（基层/基层党组织落实党的组织生活制度情况督查表）
     */
    @RequiresPermissions
    @PostMapping("/grassRootsOrgLifeExcel")
    public OutMessage<?> grassRootsOrgLifeExcel(@Validated @RequestBody InMessage<ReportExcelDTO> inMessage) throws ParseException {
        return chartService.grassRootsOrgLifeExcel(inMessage.getData());
    }

    /**
     * 工作督查表反查导出
     */
    @RequiresPermissions
    @PostMapping("/exportWorkSupervisionPeggingExcelNew")
    public void exportWorkSupervisionPeggingExcelNew(@Validated @RequestBody InMessage<ReportExcelDTO> inMessage) throws Exception {
         chartService.exportWorkSupervisionPeggingExcelNew(inMessage.getData());
    }


    /**
     * 共有党员总数
     * @param inMessage
     * @return
     * @throws Exception
     */
    @RequiresPermissions
    @PostMapping("/getMemListData")
    public OutMessage<?> getMemListData(@Validated @RequestBody InMessage<ChartDataPeggListDto> inMessage) {
        return chartService.getMemListData(inMessage.getData());
    }

    /**
     * 获取入党申请人
     * @param inMessage
     * @return
     */
    @RequiresPermissions
    @PostMapping("/getJoinPartyPeople")
    public OutMessage<?> getJoinPartyPeople(@Validated @RequestBody InMessage<ChartDataPeggListDto> inMessage) {
        return chartService.getJoinPartyPeople(inMessage.getData());
    }

    /**
     * 获取入党积极分子
     * @param inMessage
     * @return
     */
    @RequiresPermissions
    @PostMapping("/getJoinPartyActivist")
    public OutMessage<?> getJoinPartyActivist(@Validated @RequestBody InMessage<ChartDataPeggListDto> inMessage) {
        return chartService.getJoinPartyActivist(inMessage.getData());
    }

    /**
     * 入党发展对象
     * @param inMessage
     * @return
     */
    @RequiresPermissions
    @PostMapping("/getJoinPartyDevelopmentTarget")
    public OutMessage<?> getJoinPartyDevelopmentTarget(@Validated @RequestBody InMessage<ChartDataPeggListDto> inMessage) {
        return chartService.getJoinPartyDevelopmentTarget(inMessage.getData());
    }

    /**
     * 本年发展党员
     * @param inMessage
     * @return
     */
    @RequiresPermissions
    @PostMapping("/getYearDevelopmentParty")
    public OutMessage<?> getYearDevelopmentParty(@Validated @RequestBody InMessage<ChartDataPeggListDto> inMessage) {
        return chartService.getYearDevelopmentParty(inMessage.getData());
    }

    /**
     * 本年内参加民主评议的党员
     * @param inMessage
     * @return
     */
    @RequiresPermissions
    @PostMapping("/getYearJoinDemocraticReviewParty")
    public OutMessage<?> getYearJoinDemocraticReviewParty(@Validated @RequestBody InMessage<ChartDataPeggListDto> inMessage) {
        return chartService.getYearJoinDemocraticReviewParty(inMessage.getData());
    }

    /**
     * 党代表
     * @param inMessage
     * @return
     */
    @RequiresPermissions
    @PostMapping("/getPartyRepresentative")
    public OutMessage<?> getPartyRepresentative(@Validated @RequestBody InMessage<ChartDataPeggListDto> inMessage) {
        return chartService.getPartyRepresentative(inMessage.getData());
    }

    /**
     * 流入党员
     * @param inMessage
     * @return
     */
    @RequiresPermissions
    @PostMapping("/getInflowParty")
    public OutMessage<?> getInflowParty(@Validated @RequestBody InMessage<ChartDataPeggListDto> inMessage) {
        return chartService.getInflowParty(inMessage.getData());
    }

    /**
     * 流出党员
     * @param inMessage
     * @return
     */
    @RequiresPermissions
    @PostMapping("/getFlowOutParty")
    public OutMessage<?> getFlowOutParty(@Validated @RequestBody InMessage<ChartDataPeggListDto> inMessage) {
        return chartService.getFlowOutParty(inMessage.getData());
    }

    /**
     * 本年内受纪律处分党员
     * @param inMessage
     * @return
     */
    @RequiresPermissions
    @PostMapping("/getYearInDisgraceParty")
    public OutMessage<?> getYearInDisgraceParty(@Validated @RequestBody InMessage<ChartDataPeggListDto> inMessage) {
        return chartService.getYearInDisgraceParty(inMessage.getData());
    }

    /**
     * 本年内党员参加培训人次
     * @param inMessage
     * @return
     */
    @RequiresPermissions
    @PostMapping("/getYearPartyJoinTrain")
    public OutMessage<?> getYearPartyJoinTrain(@Validated @RequestBody InMessage<ChartDataPeggListDto> inMessage) {
        return chartService.getYearPartyJoinTrain(inMessage.getData());
    }

    /**
     * 本年内基层党委开展培训期数
     * @param inMessage
     * @return
     */
    @RequiresPermissions
    @PostMapping("/getYearTrainNumber")
    public OutMessage<?> getYearTrainNumber(@Validated @RequestBody InMessage<ChartDataPeggListDto> inMessage) {
        return chartService.getYearTrainNumber(inMessage.getData());
    }

    /**
     * 本年内基层党委开展培训人次
     * @param inMessage
     * @return
     */
    @RequiresPermissions
    @PostMapping("/getYearTrainPeople")
    public OutMessage<?> getYearTrainPeople(@Validated @RequestBody InMessage<ChartDataPeggListDto> inMessage) {
        return chartService.getYearTrainPeople(inMessage.getData());
    }

    /**
     * 党委
     * @param inMessage
     * @return
     */
    @RequiresPermissions
    @PostMapping("/getPartyCommittee")
    public OutMessage<?> getPartyCommittee(@Validated @RequestBody InMessage<ChartDataPeggListDto> inMessage) {
        return chartService.getPartyCommittee(inMessage.getData());
    }

    /**
     * 党总支
     * @param inMessage
     * @return
     */
    @RequiresPermissions
    @PostMapping("/getPartyGeneralBranch")
    public OutMessage<?> getPartyGeneralBranch(@Validated @RequestBody InMessage<ChartDataPeggListDto> inMessage) {
        return chartService.getPartyGeneralBranch(inMessage.getData());
    }

    /**
     * 党支部
     * @param inMessage
     * @return
     */
    @RequiresPermissions
    @PostMapping("/getPartyBranch")
    public OutMessage<?> getPartyBranch(@Validated @RequestBody InMessage<ChartDataPeggListDto> inMessage) {
        return chartService.getPartyBranch(inMessage.getData());
    }

    /**
     * 行业党组织
     * @param inMessage
     * @return
     */
    @RequiresPermissions
    @PostMapping("/getPartyOrgIndustry")
    public OutMessage<?> getPartyOrgIndustry(@Validated @RequestBody InMessage<ChartDataPeggListDto> inMessage) {
        return chartService.getPartyOrgIndustry(inMessage.getData());
    }

    /**
     * 本年内开展民主评议的支部
     * @param inMessage
     * @return
     */
    @RequiresPermissions
    @PostMapping("/getYearOrgInformation")
    public OutMessage<?> getYearOrgInformation(@Validated @RequestBody InMessage<ChartDataPeggListDto> inMessage) {
        return chartService.getYearOrgInformation(inMessage.getData());
    }

    /**
     * 在任党组织书记
     * @param inMessage
     * @return
     */
    @RequiresPermissions
    @PostMapping("/getInOfficeSecretary")
    public OutMessage<?> getInOfficeSecretary(@Validated @RequestBody InMessage<ChartDataPeggListDto> inMessage) {
        return chartService.getInOfficeSecretary(inMessage.getData());
    }

    /**
     * 乡镇（法人）
     * @param inMessage
     * @return
     */
    @RequiresPermissions
    @PostMapping("/getTownCorporation")
    public OutMessage<?> getTownCorporation(@Validated @RequestBody InMessage<ChartDataPeggListDto> inMessage) {
        return chartService.getTownCorporation(inMessage.getData());
    }

    /**
     * 城市社区(法人)
     * @param inMessage
     * @return
     */
    @RequiresPermissions
    @PostMapping("/getUrbanCommunityCorporation")
    public OutMessage<?> getUrbanCommunityCorporation(@Validated @RequestBody InMessage<ChartDataPeggListDto> inMessage) {
        return chartService.getUrbanCommunityCorporation(inMessage.getData());
    }

    /**
     * 乡镇社区(法人)
     * @param inMessage
     * @return
     */
    @RequiresPermissions
    @PostMapping("/getTownCommunityCorporation")
    public OutMessage<?> getTownCommunityCorporation(@Validated @RequestBody InMessage<ChartDataPeggListDto> inMessage) {
        return chartService.getTownCommunityCorporation(inMessage.getData());
    }

    /**
     * 行政村(法人)
     * @param inMessage
     * @return
     */
    @RequiresPermissions
    @PostMapping("/getAdministrativeVillageCorporation")
    public OutMessage<?> getAdministrativeVillageCorporation(@Validated @RequestBody InMessage<ChartDataPeggListDto> inMessage) {
        return chartService.getAdministrativeVillageCorporation(inMessage.getData());
    }

    /**
     * 获取共有党员总数、共有党的基层组织、共有城市街道(法人) 数据反查列表
     * @param inMessage
     * @return
     */
    @RequiresPermissions
    @PostMapping("/getIndexDataPeggingList")
    public OutMessage<?> getIndexDataPeggingList(@Validated @RequestBody InMessage<ChartDataPeggListDto> inMessage) {
        return chartService.getIndexDataPeggingList(inMessage.getData());
    }

    /**
     * 获取流动党员工作督查表
     * @param inMessage
     * @return
     */
    @RequiresPermissions
    @PostMapping("/memFlowInspectionForm")
    public OutMessage<?> memFlowInspectionForm(@Validated @RequestBody InMessage<ReportExcelDTO> inMessage) {
        return memFlowInspectionFormService.getData(inMessage.getData());
    }

    /**
     * 导出流动党员工作督查表
     */
    @RequiresPermissions
    @PostMapping("/exportMemFlowInspectionForm")
    public void exportMemFlowInspectionForm(@Validated @RequestBody InMessage<ReportExcelDTO> inMessage) throws Exception {
        memFlowInspectionFormService.exportData(inMessage.getData());
    }

}
