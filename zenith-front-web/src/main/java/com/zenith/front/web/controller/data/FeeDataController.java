package com.zenith.front.web.controller.data;


import com.zenith.front.api.fee.IFeeService;
import com.zenith.front.common.annotation.RequiresPermissions;
import com.zenith.front.core.kit.ExcelUtil;
import com.zenith.front.model.dto.FeeListDTO;
import com.zenith.front.model.message.InMessage;
import com.zenith.front.model.message.OutMessage;
import com.zenith.front.model.message.Status;
import com.zenith.front.model.validate.annotation.Validate;
import com.zenith.front.model.validate.group.Common6Group;
import com.zenith.front.web.controller.BaseController;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;

/**
 * <AUTHOR>
 * @version 1.0
 * @Description: 党费导出
 * @date 2019/5/21 14:20
 */
@RestController
@RequestMapping("/data/fee")
public class FeeDataController extends BaseController {

    @Resource
    private IFeeService feeService;

    /**
     * 党费导出
     *
     * @return
     */
    @Validate(group = Common6Group.class)
    @RequiresPermissions
    @PostMapping("/exportData")
    public void exportData(@RequestBody InMessage<FeeListDTO> inMessage, HttpServletResponse response) {
        FeeListDTO feeListDTO = inMessage.getData();
        String memOrgOrgCode = feeListDTO.getMemOrgOrgCode();
        if (!memOrgOrgCode.startsWith(this.getCurrManOrgCode())) {
            ExcelUtil.exportErrorExcel(response, new OutMessage<>(Status.PERMISSION_DENIED));
            return;
        }
        feeService.exportFeeData(feeListDTO);
    }

}
