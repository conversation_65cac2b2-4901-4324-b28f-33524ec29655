package com.zenith.front.web.controller.trend;

import cn.hutool.core.util.ObjectUtil;
import com.zenith.front.api.trend.ITrendService;
import com.zenith.front.common.annotation.RequiresPermissions;
import com.zenith.front.model.constant.UserConstant;
import com.zenith.front.model.dto.TrendCheckDTO;
import com.zenith.front.model.dto.TrendCheckFilterDto;
import com.zenith.front.model.dto.TrendDTO;
import com.zenith.front.model.dto.TrendFilterDTO;
import com.zenith.front.model.message.InMessage;
import com.zenith.front.model.message.OutMessage;
import com.zenith.front.model.message.Status;
import com.zenith.front.model.custom.UserTicket;
import com.zenith.front.model.validate.annotation.Validate;
import com.zenith.front.model.validate.group.AddGroup;
import com.zenith.front.model.validate.group.Common1Group;
import com.zenith.front.model.validate.group.Common2Group;
import com.zenith.front.model.validate.group.UpdateGroup;
import com.zenith.front.web.controller.BaseController;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.constraints.NotNull;
import java.util.ArrayList;

/**
 * @author: D.watermelon
 * @date: 2019/6/10 8:42
 * @description: 入则恳恳以尽忠 出则谦谦以自悔
 * 工作动态相关的接口
 */
@RestController
@RequestMapping("/trend")
public class TrendController extends BaseController {

    @Resource
    private ITrendService trendService;

    /**
     * 新增工作动态（预发布状态）
     */
    @PostMapping("/addTrend")
    @RequiresPermissions
    @Validate(group = AddGroup.class)
    public OutMessage addTrend(@RequestBody InMessage<TrendDTO> inMessage) {
        UserTicket userTicket = UserConstant.USER_CONTEXT.get();
        String account = userTicket.getUser().getAccount();
        return trendService.addTrend(inMessage.getData(), account);
    }

    /**
     * 查看单个工作动态
     */
    @GetMapping("/findTrendByCode")
    @RequiresPermissions
    public OutMessage findTrendByCode(@NotNull(message = "动态标识不能为空") String code) {

        return trendService.findTrendByCode(code);
    }

    /**
     * 撤回工作动态（已发布状态）
     */
    @PostMapping("/cancelTrend")
    @RequiresPermissions
    @Validate(group = Common1Group.class)
    public OutMessage cancelTrend(@RequestBody InMessage<TrendDTO> inMessage) {
        String currManOrgCode = this.getCurrManOrgCode();

        return trendService.cancelTrend(inMessage.getData(), currManOrgCode);
    }

    /**
     * 发布工作动态
     */
    @PostMapping("/pushTrend")
    @RequiresPermissions
    @Validate(group = Common1Group.class)
    public OutMessage pushTrend(@RequestBody InMessage<TrendDTO> inMessage) {
        //基础信息校验-- 校验是否是自己及其上级帮自己发布
        String currManOrgCode = "500";//this.getCurrManOrgCode();

        return trendService.pushTrend(inMessage.getData().getCode(), currManOrgCode);
    }

    /**
     * 工作动态列表
     */
    @PostMapping("/listTrend")
    @RequiresPermissions
    @Validate(group = Common2Group.class)
    public OutMessage listTrend(@RequestBody InMessage<TrendFilterDTO> inMessage) {
        String currManOrgCode = "500";//this.getCurrManOrgCode();

        return trendService.listTrend(inMessage.getData(), currManOrgCode);
    }

    /**
     * 编辑工作动态
     */
    @PostMapping("/updateTrend")
    @RequiresPermissions
    @Validate(group = UpdateGroup.class)
    public OutMessage updateTrend(@RequestBody InMessage<TrendDTO> inMessage) {
        String currManOrgCode = "500";//this.getCurrManOrgCode();

        return trendService.updateTrend(inMessage.getData(), currManOrgCode);

    }

    /**
     * 删除工作动态
     */
    @PostMapping("/delTrend")
    @RequiresPermissions
    @Validate(group = Common1Group.class)
    public OutMessage delTrend(@RequestBody InMessage<TrendDTO> inMessage) {
        String currManOrgCode = "500";//this.getCurrManOrgCode();

        return trendService.delTrend(inMessage.getData().getCode(), currManOrgCode);
    }

    /**
     * 重新发布动态
     */
    @PostMapping("/afreshTrend")
    @RequiresPermissions
    @Validate(group = Common1Group.class)
    public OutMessage afreshTrend(@RequestBody InMessage<TrendDTO> inMessage) {
        String currManOrgCode = "500";//this.getCurrManOrgCode();
        return trendService.afreshTrend(inMessage.getData().getCode(), currManOrgCode);
    }

    /**
     * 推送工作动态到门户
     */
    @PostMapping("/portalTrend")
    @RequiresPermissions
    @Validate(group = Common1Group.class)
    public OutMessage portalTrend(@RequestBody InMessage<TrendDTO> inMessage) {
        String currManOrgCode = this.getCurrManOrgCode();
        return trendService.portalTrend(inMessage.getData().getCode(), currManOrgCode);
    }

    /**
     * 审核通过门户推荐
     */
    @PostMapping("/checkTrend")
    @RequiresPermissions
    @Validate(group = AddGroup.class)
    public OutMessage checkTrend(@RequestBody InMessage<TrendCheckDTO> inMessage) {
        String currManOrgCode = this.getCurrManOrgCode();
        return trendService.checkTrend(inMessage.getData(), currManOrgCode);
    }

    /**
     * 获取我的审核列表（我的待审核，该我审核的，以及我的审核状态）
     */
    @PostMapping("/checkTrendList")
    @RequiresPermissions
    @Validate(group = Common1Group.class)
    public OutMessage checkTrendList(@RequestBody InMessage<TrendCheckFilterDto> inMessage) {

        TrendCheckFilterDto data = inMessage.getData();
        ArrayList<Integer> checkTypeList = data.getCheckTypeList();
        if (ObjectUtil.isNull(checkTypeList)) {
            return new OutMessage(Status.ORG_CHECK_STATUS);
        }

        return trendService.checkTrendList(inMessage.getData());
    }

}
