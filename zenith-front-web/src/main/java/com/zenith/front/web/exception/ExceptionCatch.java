package com.zenith.front.web.exception;

import com.fasterxml.jackson.databind.exc.MismatchedInputException;
import com.zenith.front.common.exception.JMJException;
import com.zenith.front.common.exception.LogicValidatorException;
import com.zenith.front.common.exception.ServiceException;
import com.zenith.front.common.kit.PrintExceptionLogUtil;
import com.zenith.front.core.encrypt.DataConsistencyException;
import org.mybatis.spring.MyBatisSystemException;
import com.zenith.front.model.message.OutMessage;
import com.zenith.front.model.message.Status;
import lombok.extern.slf4j.Slf4j;
import org.postgresql.util.PSQLException;
import org.springframework.context.support.DefaultMessageSourceResolvable;
import org.springframework.dao.DataAccessResourceFailureException;
import org.springframework.http.HttpStatus;
import org.springframework.http.converter.HttpMessageNotReadableException;
import org.springframework.validation.BindException;
import org.springframework.web.HttpRequestMethodNotSupportedException;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.springframework.web.bind.MissingServletRequestParameterException;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.RestControllerAdvice;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.validation.ConstraintViolation;
import javax.validation.ConstraintViolationException;
import java.util.concurrent.atomic.AtomicReference;

/**
 * 全局异常处理
 *
 * <AUTHOR>
 * @creed Talk is cheap,show me the code
 * @date 2020年12月13日 11时39分
 */
@RestControllerAdvice
@Slf4j
public class ExceptionCatch {

    @Resource
    private HttpServletRequest request;
    @Resource
    private HttpServletResponse response;

    /**
     * 单个参数异常处理
     * 处理请求参数格式错误 @RequestParam上validate失败后抛出的异常
     */
    @ExceptionHandler(value = ConstraintViolationException.class)
    public OutMessage<?> constraintViolationException(ConstraintViolationException ex) {
        AtomicReference<String> stringAtomicReference = new AtomicReference<>("");
        ex.getConstraintViolations().stream().map(ConstraintViolation::getMessage).findFirst().ifPresent(stringAtomicReference::set);
        return validateResultFormat(stringAtomicReference.get(), ex, request);
    }

    /**
     * 一般参数校验绑定异常处理
     * 处理Get请求中 使用@Valid 验证路径中请求实体校验失败后抛出的异常
     */
    @ExceptionHandler(value = BindException.class)
    public OutMessage<?> bindException(BindException ex) {
        AtomicReference<String> stringAtomicReference = new AtomicReference<>("");
        ex.getBindingResult().getAllErrors().stream().map(DefaultMessageSourceResolvable::getDefaultMessage).findFirst()
                .ifPresent(stringAtomicReference::set);
        return validateResultFormat(stringAtomicReference.get(), ex, request);
    }

    /**
     * JSON参数校验绑定异常处理
     * 处理请求参数格式错误 @RequestBody上validate失败后抛出的异常
     */
    @ExceptionHandler(value = MethodArgumentNotValidException.class)
    public OutMessage<?> methodArgumentNotValidException(MethodArgumentNotValidException ex) {
        AtomicReference<String> stringAtomicReference = new AtomicReference<>("");
        ex.getBindingResult().getAllErrors().stream().map(DefaultMessageSourceResolvable::getDefaultMessage).findFirst()
                .ifPresent(stringAtomicReference::set);
        return validateResultFormat(stringAtomicReference.get(), ex, request);
    }

    /**
     * 逻辑校验异常处理
     */
    @ExceptionHandler(value = LogicValidatorException.class)
    public OutMessage<?> logicValidatorException(LogicValidatorException ex) {
        return validateResultFormat(ex.getMessage(), ex, request);
    }

    @ExceptionHandler({Exception.class})
    public void exception(Exception ex) {
        log.error("Controller Exception[" + request.getRequestURI() + "]-" + ex.getMessage(), ex);
        PrintExceptionLogUtil.errorLog(ex, response);
    }

    @ExceptionHandler({PSQLException.class})
    public OutMessage<?> exception(PSQLException ex) {
        return resultFormat(ex.getMessage(), ex, request);
    }

    @ExceptionHandler({DataAccessResourceFailureException.class})
    public OutMessage<?> exception(DataAccessResourceFailureException ex) {
        return resultFormat(ex.getMessage(), ex, request);
    }

    @ExceptionHandler({ServiceException.class})
    public OutMessage<?> serviceException(ServiceException ex) {
        return resultFormat(ex.getMessage(), ex, request);
    }

    /**
     * 加密机异常处理
     */
    @ExceptionHandler({JMJException.class})
    public OutMessage<?> jmjException(JMJException ex) {
        // 记录详细的加密机异常日志
        log.error("加密机服务异常：{}", ex.getMessage(), ex);

        // 根据异常消息判断具体的错误类型
        String message = ex.getMessage();
        if (message != null) {
            if (message.contains("加密数据失败")) {
                return new OutMessage<>(Status.JMJ_ENCRYPT_ERROR.getCode(), Status.JMJ_ENCRYPT_ERROR.getMessage(), null);
            } else if (message.contains("解密数据失败")) {
                return new OutMessage<>(Status.JMJ_DECRYPT_ERROR.getCode(), Status.JMJ_DECRYPT_ERROR.getMessage(), null);
            }
        }

        // 默认返回通用加密机异常
        return new OutMessage<>(Status.JMJ_ERROR.getCode(), Status.JMJ_ERROR.getMessage(), null);
    }

    /**
     * MyBatis系统异常处理（处理数据一致性检查异常和加密机异常）
     */
    @ExceptionHandler({MyBatisSystemException.class})
    public OutMessage<?> myBatisSystemException(MyBatisSystemException ex) {
        // 检查是否为特定异常，限制查找深度防止死循环
        Throwable cause = ex.getCause();
        int depth = 0;
        final int MAX_DEPTH = 5; // 最多查找5层，足够覆盖正常的异常包装

        while (cause != null && depth < MAX_DEPTH) {
            // 检查数据一致性异常
            if (cause instanceof DataConsistencyException) {
                // 记录详细的安全日志
                log.error("数据一致性检查失败，可能存在数据篡改：{}", cause.getMessage());

                // 返回详细的篡改信息给前端
                String message = "操作失败，数据完整性验证失败！" + cause.getMessage();
                return new OutMessage<>(Status.DATA_EXCEPTION.getCode(), message, null);
            }

            // 检查加密机异常
            if (cause instanceof JMJException) {
                // 记录详细的加密机异常日志
                log.error("MyBatis操作中发生加密机异常：{}", cause.getMessage(), cause);

                // 根据异常消息判断具体的错误类型
                String message = cause.getMessage();
                if (message != null) {
                    if (message.contains("加密数据失败")) {
                        return new OutMessage<>(Status.JMJ_ENCRYPT_ERROR.getCode(), Status.JMJ_ENCRYPT_ERROR.getMessage(), null);
                    } else if (message.contains("解密数据失败")) {
                        return new OutMessage<>(Status.JMJ_DECRYPT_ERROR.getCode(), Status.JMJ_DECRYPT_ERROR.getMessage(), null);
                    }
                }

                // 默认返回通用加密机异常
                return new OutMessage<>(Status.JMJ_ERROR.getCode(), Status.JMJ_ERROR.getMessage(), null);
            }

            cause = cause.getCause();
            depth++;
        }

        // 如果不是特定异常，重新抛出让其他异常处理器处理
        throw ex;
    }




    /**
     * Jackson反序列化异常
     */
    @ExceptionHandler(MismatchedInputException.class)
    public OutMessage<?> mismatchedInputException(MismatchedInputException ex) {
        return resultFormat(Status.SYSTEM_ERROR, "Jackson反序列化异常", ex, request);
    }

    /**
     * Http请求方式错误
     */
    @ExceptionHandler(HttpRequestMethodNotSupportedException.class)
    public OutMessage<?> HttpRequestMethodNotSupportedException(HttpRequestMethodNotSupportedException ex) {
        return new OutMessage<>(HttpStatus.BAD_REQUEST.value(), "请求方式不支持", null);
    }

    /**
     * Http消息不可读异常返回特定的信息
     */
    @ExceptionHandler(HttpMessageNotReadableException.class)
    public OutMessage<?> httpMessageNotReadableException(HttpMessageNotReadableException ex) {
        return resultFormat(Status.SYSTEM_ERROR, "参数格式无效", ex, request);
    }

    /**
     * 参数缺失异常处理
     */
    @ExceptionHandler(MissingServletRequestParameterException.class)
    public OutMessage<?> handleMissingRequestParameter(MissingServletRequestParameterException ex) {
        return resultFormat(Status.PARA_VALIDATE_MISS, ex.getParameterName(), ex, request);
    }

    private <T extends Throwable> OutMessage<?> resultFormat(int code, String message, T ex, HttpServletRequest request) {
        log.error("Controller Exception[" + request.getRequestURI() + "]-" + code + "：" + message, ex);
        return new OutMessage<>(code, message, null);
    }

    private <T extends Throwable> OutMessage<?> resultFormat(Status status, String message, T ex, HttpServletRequest request) {
        log.error("Controller Exception[" + request.getRequestURI() + "]-" + status.getCode() + "：" + ex.getMessage(), ex);
        return new OutMessage<>(status.getCode(), String.format(status.getMessage(), message), null);
    }

    private <T extends Throwable> OutMessage<?> validateResultFormat(String message, T ex, HttpServletRequest request) {
        log.error("Controller Exception[" + request.getRequestURI() + "]-" + HttpStatus.BAD_REQUEST.value() + "：" + message, ex);
        return new OutMessage<>(HttpStatus.BAD_REQUEST.value(), message, null);
    }

    private <T extends Throwable> OutMessage<?> resultFormat(String message, T ex, HttpServletRequest request) {
        log.error("Controller Exception[" + request.getRequestURI() + "]-" + Status.FAIL.getCode() + "：" + ex.getMessage(), ex);
        return new OutMessage<>(Status.FAIL.getCode(), message, null);
    }

}
