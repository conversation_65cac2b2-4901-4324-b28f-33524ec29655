package com.zenith.front.web.controller.ztdc;

import com.zenith.front.api.ztdc.*;
import com.zenith.front.common.annotation.RequiresPermissions;
import com.zenith.front.common.constant.OperateConstant;

import com.zenith.front.model.dto.*;
import com.zenith.front.model.message.InMessage;
import com.zenith.front.model.message.OutMessage;
import com.zenith.front.model.validate.group.AddGroup;
import com.zenith.front.web.controller.BaseController;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.constraints.NotBlank;

/**
 * 专题调查表
 *
 * <AUTHOR>
 * @date 2021/08/13
 */
@RestController
@RequestMapping("/ztdc")
@Validated
public class ZtdcController extends BaseController {

    @Resource
    private IZt13SocialOrganService zt13SocialOrganService;
    @Resource
    private IZt12InternetEnterprisService zt12InternetEnterprisService;
    @Resource
    private IZt11NonPublicEnterpriseService zt11NonPublicEnterpriseService;
    @Resource
    private IZt10StateEnterpriseService zt10StateEnterpriseService;
    @Resource
    private IZt78UniversityService zt78UniversityService;

    /**
     * 新增专题调查表十三
     */
    @PostMapping("/saveZt13Data")
    @RequiresPermissions(opt = OperateConstant.INSERT)
    public OutMessage<?> saveZt13Data(@Validated(value = AddGroup.class) @RequestBody InMessage<Zt13SocialOrganDto> inMessage) {
        return zt13SocialOrganService.saveZt13Data(inMessage.getData());
    }

    /**
     * 新增专题调查表十二
     */
    @PostMapping("/saveZt12Data")
    @RequiresPermissions(opt = OperateConstant.INSERT)
    public OutMessage<?> saveZt12Data(@Validated(value = AddGroup.class) @RequestBody InMessage<Zt12InternetEnterprisDto> inMessage) {
        return zt12InternetEnterprisService.saveZt12Data(inMessage.getData());
    }

    /**
     * 新增专题调查表十一
     */
    @PostMapping("/saveZt11Data")
    @RequiresPermissions(opt = OperateConstant.INSERT)
    public OutMessage<?> saveZt11Data(@Validated(value = AddGroup.class) @RequestBody InMessage<Zt11NonPublicEnterpriseDto> inMessage) {
        return zt11NonPublicEnterpriseService.saveZt11Data(inMessage.getData());
    }

    /**
     * 新增专题调查表十
     */
    @PostMapping("/saveZt10Data")
    @RequiresPermissions(opt = OperateConstant.INSERT)
    public OutMessage<?> saveZt10Data(@Validated(value = AddGroup.class) @RequestBody InMessage<Zt10StateEnterpriseDto> inMessage) {
        return zt10StateEnterpriseService.saveZt10Data(inMessage.getData());
    }


    /**
     * 新增专题调查表七八
     */
    @PostMapping("/saveZt78Data")
    @RequiresPermissions(opt = OperateConstant.INSERT)
    public OutMessage<?> saveZt78Data(@Validated(value = AddGroup.class) @RequestBody InMessage<Zt78UniversityDto> inMessage) {
        return zt78UniversityService.saveZt78Data(inMessage.getData());
    }

    /**
     * 获取专题调查表数据
     */
    @GetMapping("/findZtDataByCode")
    @RequiresPermissions
    public OutMessage<?> findZtDataByCode(@NotBlank(message = "unitCode 不能为空") String unitCode,
                                          @NotBlank(message = "type 不能为空") String type) {
        return zt13SocialOrganService.findZtDataByCode(unitCode, type);
    }


}
