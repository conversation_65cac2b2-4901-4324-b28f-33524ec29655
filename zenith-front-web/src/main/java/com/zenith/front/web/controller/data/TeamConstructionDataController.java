package com.zenith.front.web.controller.data;

import com.zenith.front.api.mem.IMemDevelopService;
import com.zenith.front.common.annotation.RequiresPermissions;
import com.zenith.front.common.annotation.VerifyLevelCode;
import com.zenith.front.model.dto.LedgerDTO;
import com.zenith.front.model.message.InMessage;
import com.zenith.front.model.validate.annotation.Validate;
import com.zenith.front.model.validate.group.Common1Group;
import com.zenith.front.web.controller.BaseController;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @version 1.0
 * @Description: 队伍建设
 * @date 2019/6/16 11:39
 */
@RestController
@RequestMapping("/data/teamConstructionData")
public class TeamConstructionDataController extends BaseController {

    @Resource
    private IMemDevelopService memDevelopService;

    /**
     * 导出党员信息台账
     *
     * @return
     */
    @Validate(group = Common1Group.class)
    @RequiresPermissions
    @VerifyLevelCode
    @PostMapping("/exportMemInfo")
    public void exportMemInfo(@RequestBody InMessage<LedgerDTO> inMessage) {
        memDevelopService.exportMemInfo(inMessage.getData());
    }

}
