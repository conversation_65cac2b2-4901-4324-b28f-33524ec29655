package com.zenith.front.web.controller.org;

import com.zenith.front.api.org.IOrgCaucusService;
import com.zenith.front.common.annotation.RequiresPermissions;
import com.zenith.front.common.constant.OperateConstant;
import com.zenith.front.model.dto.OrgCaucusDTO;
import com.zenith.front.model.dto.OrgCaucusListDTO;
import com.zenith.front.model.message.InMessage;
import com.zenith.front.model.message.OutMessage;
import com.zenith.front.model.message.Status;
import com.zenith.front.model.validate.annotation.Validate;
import com.zenith.front.model.validate.group.AddGroup;
import com.zenith.front.web.controller.BaseController;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @Date 2021/9/16 10:19
 * @Version 1.0
 */
@RestController
@RequestMapping("/org/caucus")
public class OrgCaucusController extends BaseController {

    @Resource
    private IOrgCaucusService caucusService;

    /**
     * 添加地方委员会
     */
    @PostMapping("/add")
    @RequiresPermissions(opt = OperateConstant.INSERT)
    @Validate(group = AddGroup.class)
    public OutMessage add(@RequestBody InMessage<OrgCaucusDTO> inMessage) {
        if (!inMessage.getData().getCaucusOrgCode().startsWith(this.getCurrManOrgCode())) {
            return new OutMessage<>(Status.PERMISSION_DENIED);
        }
        return caucusService.addOrUpdate(inMessage.getData());
    }

    /**
     * 查看地方委员会
     */
    @GetMapping("/details")
    @Validate
    @RequiresPermissions
    public OutMessage details(@NotNull(message = "code不能为空") String code) {
        return caucusService.details(code);
    }

    /**
     * 删除地方委员会
     */
    @GetMapping("/delete")
    @RequiresPermissions(opt = OperateConstant.DELETE)
    public OutMessage delete(@NotBlank(message = "code 不能为空") String code){
        return caucusService.delete(code);
    }

    /**
     * 地方委员会列表
     */
    @PostMapping("/getList")
    @RequiresPermissions(opt = OperateConstant.QUERY)
    public OutMessage getList(@Validated(value = AddGroup.class) @RequestBody InMessage<OrgCaucusListDTO> inMessage){
        OrgCaucusListDTO data = inMessage.getData();
        return caucusService.getList(data);
    }

}
