package com.zenith.front.web.scheduled;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;
import com.zenith.front.api.unit.IUnitCollectiveEconomicService;
import com.zenith.front.core.cache.CacheUtils;
import com.zenith.front.core.feign.client.VillageCommunityClient;
import com.zenith.front.core.feign.dto.PushUnitCollectiveEconomicDTO;
import com.zenith.front.core.feign.dto.VcUnitCollectiveEconomic;
import com.zenith.front.core.kit.CollectionUtil;
import com.zenith.front.model.bean.UnitCollectiveEconomic;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.condition.ConditionalOnExpression;
import org.springframework.context.annotation.Configuration;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * 推送集体经济
 *
 * <AUTHOR>
 * @date 2022/11/15
 */
@Slf4j
@Configuration
@ConditionalOnExpression("${vc.feign.enabled:true}&&${vc.unit.open:true}")
public class PullVcUnitCollectiveEconomicTask extends AbstractSchedulingConfigurer {

    @Value("${vc.unit.cron}")
    private String cron;
    @Resource
    private IUnitCollectiveEconomicService iUnitCollectiveEconomicService;
    @Resource
    private VillageCommunityClient villageCommunityClient;

    @Override
    public void task() {
        List<UnitCollectiveEconomic> list = iUnitCollectiveEconomicService.list();
        if (CollUtil.isEmpty(list)) {
            return;
        }
        Map<String, String> d128Map = CollectionUtil.listRecordToMap(CacheUtils.getDic("dict_d128"), "key", "name");
        Map<String, String> d129Map = CollectionUtil.listRecordToMap(CacheUtils.getDic("dict_d129"), "key", "name");
        List<VcUnitCollectiveEconomic> result = new ArrayList<>();
        for (UnitCollectiveEconomic e : list) {
            VcUnitCollectiveEconomic vc = new VcUnitCollectiveEconomic();
            BeanUtils.copyProperties(e, vc);
            vc.setD128Name(d128Map.get(e.getD128Code()));
            vc.setD129Name(d129Map.get(e.getD129Code()));
            vc.setCreateTimeStr(Objects.nonNull(e.getCreateTime()) ? DateUtil.format(e.getCreateTime(), DatePattern.NORM_DATETIME_PATTERN) : "");
            vc.setUpdateTimeStr(Objects.nonNull(e.getUpdateTime()) ? DateUtil.format(e.getUpdateTime(), DatePattern.NORM_DATETIME_PATTERN) : "");
            vc.setDeleteTime(Objects.nonNull(e.getDeleteTime()) ? DateUtil.formatDate(e.getDeleteTime()) : "");
            result.add(vc);
        }
        PushUnitCollectiveEconomicDTO dto = new PushUnitCollectiveEconomicDTO();
        dto.setList(result);
        dto.setNginxKey(exchangeNginxKey);
        villageCommunityClient.pushUnitCollectiveEconomic(dto);
        log.info("推送集体经济到中间交换区:{}", list.size());
    }

    @Override
    public String cron() {
        return cron;
    }
}
