//package com.zenith.front.web.controller;
//
//import com.zenith.front.common.annotation.RequiresPermissions;
//import com.zenith.front.common.kit.StrKit;
//import com.zenith.front.message.OutMessage;
//import com.zenith.front.message.Status;
//
//public class WeixinApiController extends ApiController {
//
//    /**
//     * 删除公众号菜单
//     */
//    @RequiresPermissions
//    public OutMessage<String> deleteMenu() {
//        String appId = getPara("appId");
//        if(StrKit.isBlank(appId)){
//            return new OutMessage<>(Status.WX_APP_ID_NULL_ERROR);
//        }
//        ApiResult apiResult = MenuApi.deleteMenu();
//        if (apiResult.isSucceed()) {
//            return new OutMessage<>(Status.SUCCESS);
//        } else {
//            return new OutMessage<>(Status.FAIL,apiResult.getErrorMsg());
//        }
//    }
//
//    /**
//     * 创建菜单
//     */
//    @RequiresPermissions
//    public OutMessage<String> createMenu() {
//        String appId = getPara("appId");
//        if(StrKit.isBlank(appId)){
//            return new OutMessage<>(Status.WX_APP_ID_NULL_ERROR);
//        }
//        //TODO 通过查询数据库
//        ApiResult apiResult = MenuApi.createMenu("json");
//        if (apiResult.isSucceed()) {
//           return new OutMessage<>(Status.SUCCESS);
//        } else {
//           return new OutMessage<>(Status.FAIL,apiResult.getErrorMsg());
//        }
//    }
//
//}
//
