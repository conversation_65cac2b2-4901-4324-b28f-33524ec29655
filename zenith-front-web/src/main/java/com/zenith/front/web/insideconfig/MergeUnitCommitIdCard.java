package com.zenith.front.web.insideconfig;

import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.zenith.front.api.mem.IMemAllInfoService;
import com.zenith.front.dao.mapper.mem.MemAllInfoMapper;
import com.zenith.front.model.bean.MemAllInfo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.CommandLineRunner;
import org.springframework.core.annotation.Order;

import javax.annotation.Resource;
import java.util.List;

/**
 * @author: D.watermelon
 * @date: 2023/1/14 20:13
 * @description: 入则恳恳以尽忠 出则谦谦以自悔
 * @remark 处理村班子人员来源与党员身份证重复问题
 */
//@Component
@Order(118)
@Slf4j
public class MergeUnitCommitIdCard implements CommandLineRunner {
    @Resource
    private MemAllInfoMapper memAllInfoMapper;
    @Resource
    private IMemAllInfoService iMemAllInfoService;

    @Override
    public void run(String... args) {
        this.mergeIdCard();
    }

    public void  mergeIdCard(){
        //获取所有d08类别为3的人，并且
        List<MemAllInfo> memAllByLeftD08 = memAllInfoMapper.findMemAllByLeftD08();
        log.info("处理第五表补充资料人员重复问题数据 "+memAllByLeftD08.size());
        memAllByLeftD08.forEach(memAllInfo -> {
            String code = memAllInfo.getCode();
            String commitMemCode = memAllInfo.getEsId();
            String unitD121Code = memAllInfo.getUnitD121Code();
            String unitD121Name = memAllInfo.getUnitD121Name();

            //设置党员的值
            iMemAllInfoService.update(new UpdateWrapper<MemAllInfo>().lambda()
                    .set(MemAllInfo::getUnitD121Code, unitD121Code).set(MemAllInfo::getD121Name,unitD121Name)
                    .isNull(MemAllInfo::getDeleteTime).eq(MemAllInfo::getCode,code));

            //清空d08=3的值，防止重复出数
            iMemAllInfoService.update(new UpdateWrapper<MemAllInfo>().lambda()
                    .set(MemAllInfo::getUnitD121Code, unitD121Code).set(MemAllInfo::getD121Name,unitD121Name)
                    .isNull(MemAllInfo::getDeleteTime).eq(MemAllInfo::getCode,commitMemCode).eq(MemAllInfo::getD08Code,"3"));
        });

    }
}
