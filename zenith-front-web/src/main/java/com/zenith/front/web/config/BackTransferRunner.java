package com.zenith.front.web.config;

import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.zenith.front.api.org.IOrgService;
import com.zenith.front.api.transfer.ITransferRecordService;
import com.zenith.front.common.constant.CommonConstant;
import com.zenith.front.model.dto.TransferRecordDTO;
import com.zenith.front.model.bean.Org;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.CommandLineRunner;
import org.springframework.core.annotation.Order;

import javax.annotation.Resource;
import java.util.List;

/**
 * @author: D.watermelon
 * @date: 2022/1/22 22:11
 * @remark 年度统计最后阶段的阶段，需要自动退回全部关系转接（含整建制）
 * @description: 入则恳恳以尽忠 出则谦谦以自悔
 */

//@Component
@Order(105)
@Slf4j
public class BackTransferRunner  implements CommandLineRunner {
    @Resource
    private ITransferRecordService transferRecordService;
    @Value("${baseOrgOrgCode}")
    private String baseOrgOrgCode;
    @Resource
    private IOrgService orgService;

    @Override
    public void run(String... args)  {
        System.out.println("开始执行关系转接撤回====》");
        if (true){
            Org byOrgCode = orgService.findByOrgCode(baseOrgOrgCode);
            System.out.println("开始执行关系转接撤回是否为空====》"+ObjectUtil.isNull(byOrgCode));
            if (ObjectUtil.isNotNull(byOrgCode)){
                String orgCode = byOrgCode.getCode();
                //获取当前数据库节点发起的所有关系关系转接
                Page<TransferRecordDTO> outTransferPage = transferRecordService.findOutTransferPage(CommonConstant.TENTHOUSAND, CommonConstant.ONE_INT, null, orgCode, null, false,null,null);
                System.out.println("开始执行关系转接撤回page对象====》"+outTransferPage);
                List<TransferRecordDTO> transferRecordDTOS = outTransferPage.getRecords();
                if (ObjectUtil.isNotNull(transferRecordDTOS)&&transferRecordDTOS.size()>0){
                    transferRecordDTOS.forEach(transferRecordDTO -> {
                        String transferRecordId = transferRecordDTO.getId();
                        transferRecordService.backTransfer(transferRecordId, "因为年度统计，系统自动撤销关系转接");
                        log.info("因年度统计自动退回系统关系转接：转出组织{}==>转入组织{}",transferRecordDTO.getSrcOrgName(),transferRecordDTO.getTargetOrgName());
                    });
                }
                log.info("系统自动退回关系转接数量:{}条",transferRecordDTOS.size());
            }
        }

    }
}
