package com.zenith.front.web.interceptor;

import cn.hutool.core.util.ObjectUtil;
import com.zenith.front.api.role.IRoleService;
import com.zenith.front.common.annotation.RequiresPermissions;
import com.zenith.front.common.constant.CommonConstant;
import com.zenith.front.common.constant.OperateConstant;
import com.zenith.front.common.constant.PermissionConstant;
import com.zenith.front.common.constant.RequiresSignature;
import com.zenith.front.common.exception.PermissionException;
import com.zenith.front.common.kit.HttpKit;
import com.zenith.front.common.kit.IpKit;
import com.zenith.front.common.kit.StrKit;
import com.zenith.front.common.safe.SigParseFuc;
import com.zenith.front.common.safe.Signature;
import com.zenith.front.model.constant.UserConstant;
import com.zenith.front.core.cache.CacheUtils;
import com.zenith.front.core.kit.TokenUtils;
import com.zenith.front.model.message.Status;
import com.zenith.front.model.bean.Role;
import com.zenith.front.model.custom.UserTicket;
import com.zenith.front.web.jsonrender.JsonRender;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.lang.NonNull;
import org.springframework.web.method.HandlerMethod;
import org.springframework.web.servlet.HandlerInterceptor;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.Objects;

/**
 * <AUTHOR>
 * @version 1.0
 * @Description: 权限校验
 * @date 2019/3/11 17:02
 */
public class PermissionInterceptor implements HandlerInterceptor {

    @Resource
    private IRoleService roleService;

    @Value("${is-dev}")
    public boolean is_dev;

    @Override
    public boolean preHandle(@NonNull HttpServletRequest request, @NonNull HttpServletResponse response, @NonNull Object handler) {
        if (handler instanceof HandlerMethod) {
            HandlerMethod handlerMethod = (HandlerMethod) handler;
            // 验证请求的方法是否进行权限校验
            RequiresPermissions requiresPermissions = handlerMethod.getMethodAnnotation(RequiresPermissions.class);
            if (Objects.isNull(requiresPermissions)) {
                // 没有权限校验,放行
                return true;
            }
            // 有权限校验,则用户必须处于登录状态
            String token = request.getHeader(PermissionConstant.HEADER_AUTHORIZATION);
            // header中没有token
            if (StrKit.isBlank(token)) {
                JsonRender.render(response, Status.NOT_LOGIN);
                return false;
            }
            //如果是测试用的token 直接放行
            if (StrKit.equals(PermissionConstant.TEST_TOKEN, token) && is_dev) {
                return true;
            }

            String ip = IpKit.getRealIp(request);
            String ua = HttpKit.getUA(request);
            boolean verify = TokenUtils.verify(token, ip, ua);
            if (!verify) {
                JsonRender.render(response, Status.TOKEN_AUTH_FAILD);
                return false;
            }
            //判断该请求是否要验证签名
            RequiresSignature signatureOpt = requiresPermissions.signature();
            if (!RequiresSignature.NONE.equals(signatureOpt)) {
                Signature signature = SIG_PARSE_FUC_MAP.get(signatureOpt).parse(request);
                if (signature == null) {
                    JsonRender.render(response, Status.SIG_ERROR);
                    return false;
                }
                boolean isOk = signature.verify();
                if (!isOk) {
                    JsonRender.render(response, Status.SIG_ERROR);
                    return false;
                }
            }
            // 根据token查询
            UserTicket userTicket = CacheUtils.getUserCache(token);
            // 获取角色id
            String roleID = userTicket.getUserRolePermission().getRoleId();
            Role role = roleService.findByIdAndValid(roleID);
            if (ObjectUtil.isNull(role)) {
                // 角色已经过期
                JsonRender.render(response, Status.ROLE_PAST);
                return false;
            }
            // 认证通过新增有效时间
            CacheUtils.refreshUserCache(token);
            CacheUtils.refreshUserToken(userTicket.getUser().getId());
            //放入上下文中
            UserConstant.USER_CONTEXT.set(userTicket);
            Integer readOnly = userTicket.getUser().getReadOnly();
            //用户只读
            if (CommonConstant.ONE_INT == readOnly) {
                OperateConstant opt = requiresPermissions.opt();
                //非查询操作,拒绝操作
                if (OperateConstant.QUERY.equals(opt)) {
                    return true;
                } else {
                    JsonRender.render(response, Status.USER_READ_ONLY_ERROR);
                    return false;
                }
            }
            // 获取注解上的权限
            int[] values = requiresPermissions.value();
            if (values[0] == -1 && values.length == 1) {
                // 注解没有参数,只需要登录权限
                return true;
            }
            String permission = userTicket.getUserRolePermission().getPermission();
            boolean flag = true;
            for (int index : values) {
                // 校验权限是否配置错误
                if (index < 1 || index > permission.length()) {
                    throw new PermissionException("权限配置错误,url: " + request.getRequestURI());
                }

                //  是否有权限,注解配置从1开始,string方法从0开始
                String isPermission = String.valueOf(permission.charAt(index - 1));
                if (PermissionConstant.NO_ACCESS.equals(isPermission)) {
                    flag = false;
                    break;
                }
            }
            if (!flag) {
                // 没有权限
                JsonRender.render(response, Status.PERMISSION_DENIED);
                return false;
            }
        }
        return true;
    }

    /***
     * 解析url签名
     * */
    private static final SigParseFuc QUERY_PARSE = httpServletRequest -> {
        //获取token
        String token = httpServletRequest.getHeader(PermissionConstant.HEADER_AUTHORIZATION);
        String sig = httpServletRequest.getHeader(Signature.SIG_HEADER);
        String queryString = httpServletRequest.getQueryString();
        String[] split = queryString.split("&");
        //进行字典排序
        ArrayList<String> args = new ArrayList<>(split.length);
        for (int i = 0; i < split.length; i++) {
            args.add(i, split[i]);
        }
        Collections.sort(args);
        String builder = String.join("", args);
        return Signature.parseSignature(token, sig, builder);
    };
    /***
     * 解析payload签名
     * */
    private static final SigParseFuc PAYLOAD_PARSE = httpServletRequest -> {
        String token = httpServletRequest.getHeader(PermissionConstant.HEADER_AUTHORIZATION);
        String sig = httpServletRequest.getHeader(Signature.SIG_HEADER);
        String payloadData = HttpKit.readData(httpServletRequest);
        return Signature.parseSignature(token, sig, payloadData);
    };

    private static final HashMap<RequiresSignature, SigParseFuc> SIG_PARSE_FUC_MAP = new HashMap<RequiresSignature, SigParseFuc>() {
        private static final long serialVersionUID = -4464974532077060083L;

        {
            put(RequiresSignature.QUERY_SIGNATURE, QUERY_PARSE);
            put(RequiresSignature.PAYLOAD_SIGNATURE, PAYLOAD_PARSE);
        }
    };

}
