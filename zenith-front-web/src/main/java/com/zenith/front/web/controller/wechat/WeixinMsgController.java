//package com.zenith.front.web.controller;
//
///**
// * 将此 WeixinMsgController 在 YourJFinalConfig 中注册路由，
// * 并设置好 weixin 开发者中心的 URL 与 token ，使 URL 指向该
// * WeixinMsgController 继承自父类 MsgController 的 index
// * 方法即可直接运行看效果，在此基础之上修改相关的方法即可进行实际项目开发
// * <p>
// * 注意：高版本 jfinal 需要在 configRoute(Routes routes) 中配置
// * routes.setMappingSuperClass(true);
// * 才能将超类 MsgController 中的 index() 映射为 action
// */
//public class WeixinMsgController extends MsgControllerAdapter {
//
//    @Override
//    protected void processInFollowEvent(InFollowEvent inFollowEvent) {
//
//    }
//
//    @Override
//    protected void processInTextMsg(InTextMsg inTextMsg) {
//        OutTextMsg outMsg = new OutTextMsg(inTextMsg);
//        LogKit.info("微信消息:" + inTextMsg.getContent());
//        outMsg.setContent(inTextMsg.getFromUserName());
//        render(outMsg);
//    }
//
//    @Override
//    protected void processInMenuEvent(InMenuEvent inMenuEvent) {
//
//    }
//}
//
//
//
//
//
//
