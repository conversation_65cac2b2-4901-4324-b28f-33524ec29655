package com.zenith.front.web.controller;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.io.FileUtil;
import cn.hutool.core.util.StrUtil;
import com.zenith.front.common.annotation.RequiresPermissions;
import com.zenith.front.common.constant.CommonConstant;
import com.zenith.front.common.kit.*;
import com.zenith.front.model.constant.UserConstant;
import com.zenith.front.model.custom.Record;
import com.zenith.front.model.message.OutMessage;
import com.zenith.front.model.message.Status;
import com.zenith.front.model.custom.UserTicket;
import org.springframework.stereotype.Controller;
import org.springframework.util.ResourceUtils;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.*;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Controller
public class BaseController {

    @Resource
    public HttpServletRequest httpServletRequest;

    /**
     * 获取当前用户账号
     *
     * @return
     */
    protected UserTicket getCurrUser() {
        return UserConstant.USER_CONTEXT.get();
    }

    /**
     * 获取当前用户管理的组织code
     *
     * @return
     */
    protected String getCurrManOrgCode() {
        return UserConstant.USER_CONTEXT.get().getUserRolePermission().getOrgCode();
    }

    /**
     * 获取ip
     *
     * @return
     */
    protected String getIp() {
        String ip = IpKit.getRealIp(httpServletRequest);
        if (StrKit.isBlank(ip)) {
            ip = "127.0.0.1";
        }
        return ip;
    }

    /**
     * 获取当前用户的权限字符串
     *
     * @return
     */
    protected String getCurrPermission() {
        return UserConstant.USER_CONTEXT.get().getUserRolePermission().getPermission();
    }

    public static String basePath;

    static {
        try {
            basePath = ResourceUtils.getURL(ResourceUtils.CLASSPATH_URL_PREFIX).getPath();
        } catch (FileNotFoundException e) {
            e.printStackTrace();
        }
    }

    /**
     * 获取当前用户的角色id
     *
     * @return
     */
    protected String getCurrRoleId() {
        return UserConstant.USER_CONTEXT.get().getUserRolePermission().getRoleId();
    }

    static final FileRenamePolicy DEFAULT_FILE_RENAME_POLICY = new DefaultFileRenamePolicy();

    /**
     *
     * @param files
     * @param fileSuffixStr 允许上传的文件名后缀,英文逗号分隔
     * @return
     * @throws Exception
     */
    @PostMapping("/base/upload")
    @ResponseBody
    @RequiresPermissions
    public OutMessage upload(@RequestParam("file") MultipartFile[] files, String fileSuffixStr) throws Exception {
        List<MultipartFile> multipartFileList = Arrays.stream(files).filter(multipartFile -> !multipartFile.isEmpty()).collect(Collectors.toList());
        if (CollUtil.isEmpty(multipartFileList)) {
            return new OutMessage<>(Status.FILE_NULL);
        }

        // 如果白名单不为空，并且存在附件名不符合白名单，则返回错误信息
        List<String> fileSuffix = null;
        if (StrUtil.isNotBlank(fileSuffixStr)) {
            fileSuffix = Arrays.asList(fileSuffixStr.split(CommonConstant.DOU_HAO_STRING));
        }
        final List<String> finalFileSuffix = fileSuffix;
        if(CollUtil.isNotEmpty(fileSuffix) && !multipartFileList.stream().filter(e -> StrUtil.isNotBlank(e.getOriginalFilename()))
                .allMatch(e -> finalFileSuffix.contains(FileUtil.getSuffix(e.getOriginalFilename()).toLowerCase()))) {
            return new OutMessage<>(9991, StrUtil.format("只能上传指定格式文件：{}", CollUtil.join(fileSuffix, "、")), null);
        }

        List<Record> records = new ArrayList<>();
        String folder = "upload";
        for (MultipartFile uploadFile : multipartFileList) {
            Record record = new Record();
            // 获取文件名
            String name = uploadFile.getOriginalFilename();
            if (StrUtil.isBlank(name)) {
                name = StrKit.getRandomUUID();
            }
            String toFileName = name.replaceAll(" ", "");
            toFileName = new String(toFileName.getBytes(), StandardCharsets.UTF_8);
            //String dest = basePath + "/" + folder + "/" + toFileName;
            String dest = basePath + System.getProperty("file.separator") + folder + System.getProperty("file.separator") + toFileName;
            //String dest=basePath+ "/" +folder+ "/" +toFileName;
            File file = new File(dest);
            if (!file.getParentFile().exists()) {
                file.getParentFile().mkdir();
            }
            //文件重命名，防止覆盖
            File renameFile = DEFAULT_FILE_RENAME_POLICY.rename(file);
            uploadFile.transferTo(renameFile);
            record.set("name", renameFile.getName());
            record.set("id", StrKit.getRandomUUID());
            record.set("url", System.getProperty("file.separator") + "upload" + System.getProperty("file.separator") + renameFile.getName());
            records.add(record);
        }
        return new OutMessage<>(Status.SUCCESS, records);
    }

    @PostMapping("/export/rdsqr")
    @ResponseBody
    @RequiresPermissions
    public OutMessage exportRdsqr(@RequestParam("file") MultipartFile[] files) throws Exception {
        return this.upload(files, CollUtil.join(Arrays.asList("xlsm"), CommonConstant.DOU_HAO_STRING));
    }

    /**
     * 将 /upload 改为 /obtain/attachment 路径名
     * @param name
     * @param response
     * @throws Exception
     */
    @GetMapping(value = "/obtain/attachment")
    public void upload(@RequestParam("name") String name, HttpServletResponse response) throws Exception {
        File file = new File(basePath + System.getProperty("file.separator") + "upload" + System.getProperty("file.separator") + name);
        response.setContentType("application/octet-stream");
        response.addHeader("Content-Disposition", "attachment;filename=" + URLEncoder.encode(name, "UTF-8"));

        byte[] buffer = new byte[1024];
        try (FileInputStream fis = new FileInputStream(file);
             BufferedInputStream bis = new BufferedInputStream(fis)) {
            OutputStream os = response.getOutputStream();
            int i = bis.read(buffer);
            while (i != -1) {
                os.write(buffer, 0, i);
                i = bis.read(buffer);
            }
        }
    }
}
