package com.zenith.front.web.controller.fee;

import com.zenith.front.api.fee.IFeeService;
import com.zenith.front.common.annotation.RequiresPermissions;
import com.zenith.front.model.dto.FeeDTO;
import com.zenith.front.model.dto.FeeListDTO;
import com.zenith.front.model.message.InMessage;
import com.zenith.front.model.message.OutMessage;
import com.zenith.front.model.message.Status;
import com.zenith.front.model.bean.User;
import com.zenith.front.model.validate.annotation.Validate;
import com.zenith.front.model.validate.group.Common1Group;
import com.zenith.front.model.validate.group.Common3Group;
import com.zenith.front.model.validate.group.Common4Group;
import com.zenith.front.web.controller.BaseController;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @Description: 党费标准
 * @date 2019/5/15 11:09
 */
@RestController
@RequestMapping("/fee")
public class FeeController extends BaseController {

    @Resource
    private IFeeService feeService;

    /**
     * 获取党费标准列表
     *
     * @param inMessage
     * @return
     */
    @Validate(group = Common4Group.class)
    @RequiresPermissions
//    @VerifyLevelCode(levelCode = "memOrgOrgCode")
    @PostMapping("/getList")
    public OutMessage getList(@RequestBody InMessage<FeeListDTO> inMessage) {
        FeeListDTO feeListDTO = inMessage.getData();
        return feeService.getList(feeListDTO);
    }

    /**
     * 保存党费标准
     *
     * @param inMessage
     * @return
     */
    @Validate(group = Common1Group.class)
    @RequiresPermissions
    @PostMapping("/saveFee")
    public OutMessage saveFee(@RequestBody InMessage<FeeDTO> inMessage) throws Exception {
        FeeDTO feeDTO = inMessage.getData();
        if (!feeDTO.getMemOrgOrgCode().startsWith(this.getCurrManOrgCode())) {
            return new OutMessage<>(Status.PERMISSION_DENIED);
        }
        User user = this.getCurrUser().getUser();
        return feeService.saveFee(feeDTO, user);
    }

    /**
     * 查询党员党费应支付信息
     *
     * @param inMessage
     * @return
     */
    @Validate(group = Common3Group.class)
    @RequiresPermissions
    @PostMapping("/getPayList")
    public OutMessage getPayList(@RequestBody InMessage<List<FeeListDTO>> inMessage) {
        return feeService.getPayList(inMessage.getData());
    }

    /**
     * 获取党费标准统计项 已缴纳人数,未缴纳人数,未设置人数
     *
     * @return
     */
    @Validate(group = Common4Group.class)
    @RequiresPermissions
    @PostMapping("/getCountList")
    public OutMessage getCountList(@RequestBody InMessage<FeeListDTO> inMessage) {
        FeeListDTO feeListDTO = inMessage.getData();
        if (!feeListDTO.getMemOrgOrgCode().startsWith(this.getCurrManOrgCode())) {
            return new OutMessage<>(Status.PERMISSION_DENIED);
        }
        return feeService.getCountList(feeListDTO);
    }

}
