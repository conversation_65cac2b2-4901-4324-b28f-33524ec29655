package com.zenith.front.web.controller.data;


import com.zenith.front.api.unit.IUnitService;
import com.zenith.front.common.annotation.RequiresPermissions;
import com.zenith.front.model.dto.UnitListDTO;
import com.zenith.front.model.message.InMessage;
import com.zenith.front.model.validate.annotation.Validate;
import com.zenith.front.model.validate.group.Common2Group;
import com.zenith.front.web.controller.BaseController;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @version 1.0
 * @Description: 单位
 * @date 2019/5/7 16:11
 */
@RestController
@RequestMapping("/data/unit")
public class UnitDataController extends BaseController {

    @Resource
    private IUnitService unitService;

    /**
     * 导出单位数据
     *
     * @return
     */
    @Validate(group = Common2Group.class)
    @RequiresPermissions
    @PostMapping("/exportData")
    public void exportData(@RequestBody InMessage<UnitListDTO> inMessage) {
        UnitListDTO unitListDTO = inMessage.getData();
        unitService.exportUnitData(unitListDTO);
    }
}
