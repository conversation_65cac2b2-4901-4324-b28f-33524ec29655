package com.zenith.front.web.scheduled;

import com.google.common.util.concurrent.ThreadFactoryBuilder;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.scheduling.annotation.SchedulingConfigurer;
import org.springframework.scheduling.config.ScheduledTaskRegistrar;
import org.springframework.scheduling.support.CronTrigger;

import javax.annotation.Resource;
import java.util.Arrays;
import java.util.List;
import java.util.concurrent.*;

/**
 * <AUTHOR>
 */
public abstract class AbstractSchedulingConfigurer implements SchedulingConfigurer {

    protected static final List<String> D04_CODE = Arrays.asList("92", "921", "922", "923");

    @Value("${exchange_nginx_key}")
    protected String exchangeNginxKey;

    @Bean
    public Executor taskScheduler() {
        //设置线程名称
        ThreadFactory namedThreadFactory = new ThreadFactoryBuilder().setNameFormat("thread-pool-%d").build();
        //创建线程池
        return new ScheduledThreadPoolExecutor(3, namedThreadFactory, new ThreadPoolExecutor.CallerRunsPolicy());
    }

    @Resource
    private Executor taskScheduler;

    @Override
    public void configureTasks(ScheduledTaskRegistrar taskRegistrar) {
        taskRegistrar.setScheduler(taskScheduler);
        taskRegistrar.addTriggerTask(
                //执行定时任务
                this::task,
                //设置触发器
                triggerContext -> {
                    //获取定时任务周期表达式
                    CronTrigger trigger = new CronTrigger(cron());
                    return trigger.nextExecutionTime(triggerContext);
                }
        );
    }

    /**
     * 执行定时任务
     */
    public abstract void task();

    /**
     * 获取定时任务周期表达式
     *
     * @return 定时任务周期表达式
     */
    public abstract String cron();

}
