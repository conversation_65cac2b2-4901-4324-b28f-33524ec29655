package com.zenith.front.web.config;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.zenith.front.api.mem.IMemAllInfoService;
import com.zenith.front.api.mem.IMemService;
import com.zenith.front.api.org.IOrgDevelopRightsService;
import com.zenith.front.api.org.IOrgService;
import com.zenith.front.api.transfer.ITransferApprovalService;
import com.zenith.front.api.transfer.ITransferRecordService;
import com.zenith.front.api.transfer.ITransferStaticsService;
import com.zenith.front.common.constant.CommonConstant;
import com.zenith.front.common.kit.HttpKit;
import com.zenith.front.common.kit.StrKit;

import com.zenith.front.core.analysis.count.Html1Count;
import com.zenith.front.model.bean.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.CommandLineRunner;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * @author: D.watermelon
 * @date: 2022/6/21 9:49
 * @description: 入则恳恳以尽忠 出则谦谦以自悔
 * 处理修复整建制数据道人员的memAll配合统计
 *
 */

//@Component
//@Order(107)
//@Slf4j
public class TransfrerDverallData implements CommandLineRunner {

    @Value("${sync_flow_push}")
    private String sync_flow_push;

    @Value("${exchange_nginx_key}")
    private String transferId;

    @Resource(name = "transferRecordService")
    private ITransferRecordService transferRecordService;

    @Resource
    private IMemAllInfoService iMemAllInfoService;

    @Resource
    private IMemService iMemService;

    @Resource
    private IOrgService iOrgService;

    @Value("${baseOrgOrgCode}")
    private String baseOrgOrgCode;

    @Resource
    private ITransferStaticsService iTransferStaticsService;

    @Resource
    private ITransferApprovalService iTransferApprovalService;

    @Resource
    private IOrgDevelopRightsService iOrgDevelopRightsService;

    static final Date START_DATE = DateUtil.beginOfYear(DateUtil.parse(Html1Count.TABLE_YEAR, "yyyy"));
    static final Date END_DATE = DateUtil.endOfYear(DateUtil.parse(Html1Count.TABLE_YEAR, "yyyy"));

    @Override
    public void run(String... args)  {

        //清空计算表
        iTransferStaticsService.getBaseMapper().delete(new QueryWrapper<TransferStatistics>().lambda().isNull(TransferStatistics::getDeleteTime));
        // TODO: 2022/6/21 重新处理关系转接数据， 现在先跑，后面再处理成实时得
        //处理交换区非内跨节点个人关系转接
        //SELECT * FROM ccp_transfer_record WHERE common_org_id <>'5048C51OE8B74ACF891A1EE5143F85A7' and create_time >'2022-05-01 00:00:00' and "type" <> '29' and type ='21' and status =1
        List<TransferRecord> transferRecords = transferRecordService.getBaseMapper().selectList(new QueryWrapper<TransferRecord>().lambda()
                .ne(TransferRecord::getCommonOrgId, "5048C51OE8B74ACF891A1EE5143F85A7")
                .gt(TransferRecord::getCreateTime, START_DATE)
                .le(TransferRecord::getCreateTime, END_DATE)
                .ne(TransferRecord::getType, "29")
                .eq(TransferRecord::getType, "21")
                .eq(TransferRecord::getStatus, 1));
        List<TransferStatistics> tansferPopleList= new ArrayList<>();
        transferRecords.forEach(transferRecord -> {
            String memId = transferRecord.getMemId();
            //就获取人员信息
            MemAllInfo byCode = iMemAllInfoService.findByCode(memId);
            JSONObject memJsonObject=new JSONObject();
            if (ObjectUtil.isNotNull(byCode)){
                memJsonObject = JSONObject.parseObject(JSONObject.toJSONString(byCode));
            }

            //获取组织信息
            Org srcOrg = iOrgService.findOrgByOrgId(transferRecord.getSrcOrgId());
            Org tarOrg = iOrgService.findOrgByOrgId(transferRecord.getTargetOrgId());
            //生成转出记录
            TransferStatistics srcTransferStatistics = this.createTransferStatistics(transferRecord.getId(),
                    memId, transferRecord.getName(), CommonConstant.TWO_INT, CommonConstant.ONE_INT,
                    transferRecord.getSrcOrgId(), ObjectUtil.isNull(srcOrg) ? baseOrgOrgCode : srcOrg.getOrgCode(), transferRecord.getSrcOrgName(),
                    transferRecord.getTargetOrgName(), transferRecord.getCreateTime(), memJsonObject.toJSONString(),"省内个人转接",transferRecord);
            //生成转入记录
            TransferStatistics tarTransferStatistics = this.createTransferStatistics(transferRecord.getId(),
                    memId, transferRecord.getName(), CommonConstant.ONE_INT, CommonConstant.ONE_INT,
                    transferRecord.getTargetOrgId(), ObjectUtil.isNull(tarOrg) ? baseOrgOrgCode : tarOrg.getOrgCode(), transferRecord.getSrcOrgName(),
                    transferRecord.getTargetOrgName(), transferRecord.getCreateTime(), memJsonObject.toJSONString(),"省内个人转接",transferRecord);
            tansferPopleList.add(srcTransferStatistics);
            tansferPopleList.add(tarTransferStatistics);
        });

        //处理交换区非内跨节点整建制转接
        List<TransferRecord> transferOrgRecords = transferRecordService.getBaseMapper().selectList(new QueryWrapper<TransferRecord>().lambda()
                .ne(TransferRecord::getCommonOrgId, "5048C51OE8B74ACF891A1EE5143F85A7")
                .gt(TransferRecord::getCreateTime, START_DATE)
                .le(TransferRecord::getCreateTime, END_DATE)
                .ne(TransferRecord::getType, "29")
                .eq(TransferRecord::getType, "212")
                .eq(TransferRecord::getStatus, 1));
        for (TransferRecord transferRecord : transferOrgRecords) {
            //获取组织信息
            Org srcOrg = iOrgService.findOrgByOrgId(transferRecord.getSrcOrgId());
            Org tarOrg = iOrgService.findOrgByOrgId(transferRecord.getTargetOrgId());
            if(Objects.isNull(srcOrg)){
                continue;
            }
            //获取转出的人员情况
            List<Mem> memByMemOrgCode = iMemService.findMemByMemOrgCode(srcOrg.getOrgCode(), true);
            memByMemOrgCode.forEach(mem -> {
                JSONObject memJsonObject = JSONObject.parseObject(JSONObject.toJSONString(mem));
                //生成转出记录
                TransferStatistics srcTransferStatistics = this.createTransferStatistics(transferRecord.getId(),
                        mem.getCode(), mem.getName(), CommonConstant.TWO_INT, CommonConstant.TWO_INT,
                        transferRecord.getSrcOrgId(), ObjectUtil.isNull(srcOrg) ? baseOrgOrgCode : srcOrg.getOrgCode(), transferRecord.getSrcOrgName(),
                        transferRecord.getTargetOrgName(), transferRecord.getCreateTime(), memJsonObject.toJSONString(),"省内整建制转接",transferRecord);
                //生成转入记录
                TransferStatistics tarTransferStatistics = this.createTransferStatistics(transferRecord.getId(),
                        mem.getCode(), mem.getName(), CommonConstant.ONE_INT, CommonConstant.TWO_INT,
                        transferRecord.getTargetOrgId(), ObjectUtil.isNull(tarOrg) ? baseOrgOrgCode : tarOrg.getOrgCode(), transferRecord.getSrcOrgName(),
                        transferRecord.getTargetOrgName(), transferRecord.getCreateTime(), memJsonObject.toJSONString(),"省内整建制转接",transferRecord);
                tansferPopleList.add(srcTransferStatistics);
                tansferPopleList.add(tarTransferStatistics);
            });
        }

        //获取当前节点下的所有组织
        List<Org> allSubOrgByOrgCode = iOrgService.findAllSubOrgByOrgCode(baseOrgOrgCode);
        List<String> orgCodeS = allSubOrgByOrgCode.stream().map(Org::getCode).collect(Collectors.toList());
        if (orgCodeS.size()>0){
            //处理交换区内跨节点个人关系转出记录
            List<TransferRecord> transferOutRecords = transferRecordService.getBaseMapper().selectList(new QueryWrapper<TransferRecord>().lambda()
                    .eq(TransferRecord::getCommonOrgId, "5048C51OE8B74ACF891A1EE5143F85A7")
                    .gt(TransferRecord::getCreateTime, START_DATE)
                    .le(TransferRecord::getCreateTime, END_DATE)
                    .ne(TransferRecord::getType, "29")
                    .eq(TransferRecord::getType, "21")
                    .eq(TransferRecord::getStatus, 1)
                    .in(TransferRecord::getSrcOrgId,orgCodeS));
            transferOutRecords.forEach(transferRecord -> {
                JSONObject memJsonObject=new JSONObject();
                Object extraData = transferRecord.getExtraData();
                if (ObjectUtil.isNotNull(extraData)){
                    if (extraData instanceof String){
                        memJsonObject=JSONObject.parseObject((String) extraData);
                    }
                    if (extraData instanceof JSONObject){
                        memJsonObject= (JSONObject) extraData;
                    }
                }
                String memId = transferRecord.getMemId();
                //获取组织信息
                Org srcOrg = iOrgService.findOrgByOrgId(transferRecord.getSrcOrgId());
                //生成转出记录
                TransferStatistics srcTransferStatistics = this.createTransferStatistics(transferRecord.getId(),
                        memId, transferRecord.getName(), CommonConstant.TWO_INT, CommonConstant.ONE_INT,
                        transferRecord.getSrcOrgId(), ObjectUtil.isNull(srcOrg) ? baseOrgOrgCode : srcOrg.getOrgCode(), transferRecord.getSrcOrgName(),
                        transferRecord.getTargetOrgName(), transferRecord.getCreateTime(), memJsonObject.toJSONString(),"省内个人转接",transferRecord);
                tansferPopleList.add(srcTransferStatistics);
            });
            //处理交换区内跨节点个人关系转入记录
            List<TransferRecord> transferInRecords = transferRecordService.getBaseMapper().selectList(new QueryWrapper<TransferRecord>().lambda()
                    .eq(TransferRecord::getCommonOrgId, "5048C51OE8B74ACF891A1EE5143F85A7")
                    .gt(TransferRecord::getCreateTime, START_DATE)
                    .le(TransferRecord::getCreateTime, END_DATE)
                    .ne(TransferRecord::getType, "29")
                    .eq(TransferRecord::getType, "21")
                    .eq(TransferRecord::getStatus, 1)
                    .in(TransferRecord::getTargetOrgId,orgCodeS));
            transferInRecords.forEach(transferRecord -> {
                String memId = transferRecord.getMemId();
                //就获取人员信息
                JSONObject memJsonObject=new JSONObject();
                Object extraData = transferRecord.getExtraData();
                if (ObjectUtil.isNotNull(extraData)){
                    if ((String.class).isInstance(extraData)){
                        memJsonObject=JSONObject.parseObject((String) extraData);
                    }
                    if ((JSONObject.class).isInstance(extraData)) {
                        memJsonObject = (JSONObject) extraData;
                    }
                }
                //获取组织信息
                Org tarOrg = iOrgService.findOrgByOrgId(transferRecord.getTargetOrgId());
                //生成转入记录
                TransferStatistics tarTransferStatistics = this.createTransferStatistics(transferRecord.getId(),
                        memId, transferRecord.getName(), CommonConstant.ONE_INT, CommonConstant.ONE_INT,
                        transferRecord.getTargetOrgId(), ObjectUtil.isNull(tarOrg) ? baseOrgOrgCode : tarOrg.getOrgCode(), transferRecord.getSrcOrgName(),
                        transferRecord.getTargetOrgName(), transferRecord.getCreateTime(), memJsonObject.toJSONString(),"省内个人转接",transferRecord);
                tansferPopleList.add(tarTransferStatistics);
            });
        }



        //处理交换区内跨节点整建制转出
        String replaceUrl = sync_flow_push.replace("/api/flow/push", "");
        // TODO: 2021/12/28 获取我发起的整建制转接
        JSONObject postJson =new JSONObject();
        postJson.put("type",CommonConstant.THREE_INT);
        postJson.put("orgCode",transferId);
        String rultMessage = HttpKit.doPost(replaceUrl + "/transfer/findData",postJson,"UTF-8" );
        JSONObject parse = JSONObject.parseObject(rultMessage);
        //单纯处理入库问题，并且记录需要处理的情况
        if (parse.containsKey("data")) {
            JSONArray datas = parse.getJSONArray("data");
            List<String> transferRecordIds = datas.stream().map(o -> {
                JSONObject dataObject = (JSONObject) o;
                JSONObject data = dataObject.getJSONObject("data");
                TransferRecord transferRecord = JSONObject.toJavaObject(data, TransferRecord.class);
                return transferRecord.getId();
            }).collect(Collectors.toList());
            //获取关系转接数据
            if (transferRecordIds.size()>0){
                List<TransferRecord> transferOutOrgRecords = transferRecordService.getBaseMapper().selectList(new QueryWrapper<TransferRecord>().lambda()
                        .eq(TransferRecord::getCommonOrgId, "5048C51OE8B74ACF891A1EE5143F85A7")
                        .gt(TransferRecord::getCreateTime, START_DATE)
                        .le(TransferRecord::getCreateTime, END_DATE)
                        .ne(TransferRecord::getType, "29")
                        .eq(TransferRecord::getType, "212")
                        .eq(TransferRecord::getStatus, 1)
                        .in(TransferRecord::getId,transferRecordIds));
                transferOutOrgRecords.forEach(transferRecord -> {
                    String dataText = transferRecord.getDataText();
                    JSONObject jsonDataObject = JSONObject.parseObject(dataText);
                    JSONArray ccp_mem = jsonDataObject.getJSONArray("ccp_mem");
                    List<Mem> memList = (List) this.checkDta(ccp_mem, Mem.class);
                    //获取组织信息
                    Org srcOrg = iOrgService.findOrgByOrgId(transferRecord.getSrcOrgId());
                    memList.forEach(mem -> {
                        //生成转出记录
                        JSONObject memJsonObject=new JSONObject();
                        if (ObjectUtil.isNotNull(mem)){
                            memJsonObject = JSONObject.parseObject(JSONObject.toJSONString(mem));
                        }
                        TransferStatistics srcTransferStatistics = this.createTransferStatistics(transferRecord.getId(),
                                mem.getCode(), mem.getName(), CommonConstant.TWO_INT, CommonConstant.TWO_INT,
                                transferRecord.getSrcOrgId(), ObjectUtil.isNull(srcOrg) ? baseOrgOrgCode : srcOrg.getOrgCode(), transferRecord.getSrcOrgName(),
                                transferRecord.getTargetOrgName(), transferRecord.getCreateTime(), memJsonObject.toJSONString(),"省内整建制转出",transferRecord);
                        tansferPopleList.add(srcTransferStatistics);

                    });
                });
            }
        }

        if (orgCodeS.size()>0){
            //处理交换区内跨节点整建制转入
            List<TransferRecord> transferInOrgRecords = transferRecordService.getBaseMapper().selectList(new QueryWrapper<TransferRecord>().lambda()
                    .eq(TransferRecord::getCommonOrgId, "5048C51OE8B74ACF891A1EE5143F85A7")
                    .gt(TransferRecord::getCreateTime, START_DATE)
                    .le(TransferRecord::getCreateTime, END_DATE)
                    .ne(TransferRecord::getType, "29")
                    .eq(TransferRecord::getType, "212")
                    .eq(TransferRecord::getStatus, 1)
                    .in(TransferRecord::getTargetOrgId,orgCodeS));
            transferInOrgRecords.forEach(transferRecord -> {
                String dataText = transferRecord.getDataText();
                JSONObject jsonDataObject = JSONObject.parseObject(dataText);
                JSONArray ccp_mem = jsonDataObject.getJSONArray("ccp_mem");
                List<Mem> memList = (List) this.checkDta(ccp_mem, Mem.class);
                memList.forEach(mem -> {
                    //生成转入记录
                    JSONObject memJsonObject=new JSONObject();
                    if (ObjectUtil.isNotNull(mem)){
                        memJsonObject = JSONObject.parseObject(JSONObject.toJSONString(mem));
                    }
                    Org tarOrg = iOrgService.findOrgByOrgId(transferRecord.getTargetOrgId());
                    TransferStatistics tarTransferStatistics = this.createTransferStatistics(transferRecord.getId(),
                            mem.getCode(), mem.getName(), CommonConstant.ONE_INT, CommonConstant.TWO_INT,
                            transferRecord.getTargetOrgId(), ObjectUtil.isNull(tarOrg) ? baseOrgOrgCode : tarOrg.getOrgCode(), transferRecord.getSrcOrgName(),
                            transferRecord.getTargetOrgName(), transferRecord.getCreateTime(), memJsonObject.toJSONString(),"省内整建制转入",transferRecord);
                    tansferPopleList.add(tarTransferStatistics);
                });
            });
        }
        //处理转出到中间交换区和非中间交换区转出
        List<TransferRecord> transferRecordOutMem = transferRecordService.getBaseMapper().selectList(new QueryWrapper<TransferRecord>().lambda()
                .gt(TransferRecord::getCreateTime, START_DATE)
                .le(TransferRecord::getCreateTime, END_DATE)
                .eq(TransferRecord::getStatus, 1).and(transferRecordLambdaQueryWrapper -> transferRecordLambdaQueryWrapper.eq(TransferRecord::getType, "223").or()
                                .eq(TransferRecord::getType, "224"))
                );
        transferRecordOutMem.forEach(transferRecord -> {
            String memId = transferRecord.getMemId();
            //就获取人员信息
            MemAllInfo byCode = iMemAllInfoService.findByCode(memId);
            JSONObject memJsonObject=new JSONObject();
            if (ObjectUtil.isNotNull(byCode)){
                memJsonObject = JSONObject.parseObject(JSONObject.toJSONString(byCode));
            }
            //获取组织信息
            Org srcOrg = iOrgService.findOrgByOrgId(transferRecord.getSrcOrgId());
            //生成转出记录
            TransferStatistics srcTransferStatistics = this.createTransferStatistics(transferRecord.getId(),
                    memId, transferRecord.getName(), CommonConstant.TWO_INT, CommonConstant.ONE_INT,
                    transferRecord.getSrcOrgId(), ObjectUtil.isNull(srcOrg) ? baseOrgOrgCode : srcOrg.getOrgCode(), transferRecord.getSrcOrgName(),
                    transferRecord.getTargetOrgName(), transferRecord.getCreateTime(), memJsonObject.toJSONString(),"省外个人转出",transferRecord);
            tansferPopleList.add(srcTransferStatistics);
        });

        //处理生成非中间交换区转入(通过关系转接补录方式进入)
        //删除以前生成的记录
        // todo: 加上状态为已完成的。转接中的未新增数据到mem表，删除后无法通过mem生成转入数据
        transferRecordService.remove(new QueryWrapper<TransferRecord>().lambda().eq(TransferRecord::getType, "125").eq(TransferRecord::getStatus, 1));
        List<Mem> memList = iMemService.getBaseMapper().selectList(new QueryWrapper<Mem>().lambda().isNotNull(Mem::getOutlandTransferInYear)
                .gt(Mem::getOutlandTransferInYear, START_DATE).le(Mem::getOutlandTransferInYear, END_DATE).in(Mem::getD08Code, "1", "2"));
        memList.forEach(mem -> {
            //就获取人员信息
            MemAllInfo byCode = iMemAllInfoService.findByCode(mem.getCode());
            String orgCode = byCode.getOrgCode();
            Org byOrgCode = iOrgService.findOrgByCode(orgCode);
            if (ObjectUtil.isNull(byOrgCode)){
                return;
            }
            //具有预备党员权限节点(根据党支部获取上级具有预备党员权限党组织code)
            String permissionOrgCode=byOrgCode.getParentCode();
            List<Org> allParentOrg = iOrgService.findAllParentOrg(orgCode);
            if (allParentOrg.size()>CommonConstant.ZERO_INT){
                List<OrgDevelopRights> orgDevelopRightsByCodeList = iOrgDevelopRightsService.findOrgDevelopRightsByCodeList(allParentOrg.stream().map(Org::getCode).collect(Collectors.toList()));
                if (ObjectUtil.isNotNull(orgDevelopRightsByCodeList)&&orgDevelopRightsByCodeList.size()>CommonConstant.ONE_INT){
                    permissionOrgCode = orgDevelopRightsByCodeList.get(orgDevelopRightsByCodeList.size() - CommonConstant.ONE_INT).getOrgCode();
                }

            }
            List<String> srcOrg= new ArrayList<>();
            List<String> tarOrg= new ArrayList<>();
            String branchApprovalId = StrKit.getRandomUUID();
            //生成transfer_record 表
            TransferRecord transferRecord = new TransferRecord();
            String recordId = StrKit.getRandomUUID();
            transferRecord.setId(recordId);
            transferRecord.setUserId("系统生成");
            transferRecord.setName(mem.getName());
            transferRecord.setMemId(mem.getCode());
            transferRecord.setSrcOrgId("系统生成");
            String outlandTransferOrg = mem.getOutlandTransferOrg();
            if (StrUtil.isEmpty(outlandTransferOrg)){
                outlandTransferOrg="未找到组织";
            }
            transferRecord.setSrcOrgName(outlandTransferOrg);
            transferRecord.setTargetOrgId(mem.getOrgCode());
            transferRecord.setTargetOrgName(byOrgCode.getName());
            transferRecord.setCommonOrgId("5048C51OE8B74ACF891A1EE5143F85A7");
            transferRecord.setCommonOrgName("中共贵州省委员会");
            transferRecord.setMemFeeEndTime(null);
            transferRecord.setMemFeeStandard(null);
            transferRecord.setOutType("");
            transferRecord.setInType("125");
            transferRecord.setType("125");
            transferRecord.setCurrentApprovalId(branchApprovalId);
            transferRecord.setStatus(CommonConstant.ONE_INT);
            transferRecord.setUpdateTime(new Date());
            transferRecord.setReason("系统生成关系转接");
            transferRecord.setCreateTime(mem.getOutlandTransferInYear());

            srcOrg.add("5048C51OE8B74ACF891A1EE5143F85A7");
            srcOrg.add(StrKit.getRandomUUID());
            tarOrg.add("5048C51OE8B74ACF891A1EE5143F85A7");
            //生成审核节点相关情况以及审核节点通过意见
            //原节点审核记录
            TransferApproval sourceApproval = new TransferApproval();
            String sourceApprovalId = StrKit.getRandomUUID();
            sourceApproval.setId(sourceApprovalId);
            sourceApproval.setRecordId(recordId);
            sourceApproval.setUserId("系统生成");
            sourceApproval.setOrgId(outlandTransferOrg);
            sourceApproval.setNextOrgId("5048C51OE8B74ACF891A1EE5143F85A7");
            sourceApproval.setIsInstead(CommonConstant.ZERO_INT);
            sourceApproval.setStatus(CommonConstant.ONE_INT);
            sourceApproval.setParentId(CommonConstant.MINUS_ZERO);
            sourceApproval.setCreateTime(new Date());
            sourceApproval.setDirection(CommonConstant.ZERO_INT);
            sourceApproval.setHandlerMan("系统生成");
            //中间节点
            TransferApproval middleApproval = new TransferApproval();
            String middleApprovalId = StrKit.getRandomUUID();
            middleApproval.setId(middleApprovalId);
            middleApproval.setRecordId(recordId);
            middleApproval.setUserId("系统生成");
            middleApproval.setOrgId("5048C51OE8B74ACF891A1EE5143F85A7");
            middleApproval.setNextOrgId(permissionOrgCode);
            middleApproval.setIsInstead(CommonConstant.ZERO_INT);
            middleApproval.setStatus(CommonConstant.ONE_INT);
            middleApproval.setParentId(sourceApprovalId);
            middleApproval.setCreateTime(new Date());
            middleApproval.setDirection(CommonConstant.TWO_INT);
            middleApproval.setHandlerMan("系统");
            //权限节点
            TransferApproval permissionApproval = new TransferApproval();
            String permissionApprovalId = StrKit.getRandomUUID();
            permissionApproval.setId(permissionApprovalId);
            permissionApproval.setRecordId(recordId);
            permissionApproval.setUserId("系统生成");
            permissionApproval.setOrgId(permissionOrgCode);
            permissionApproval.setNextOrgId(orgCode);
            permissionApproval.setIsInstead(CommonConstant.ZERO_INT);
            permissionApproval.setStatus(CommonConstant.ONE_INT);
            permissionApproval.setParentId(middleApprovalId);
            permissionApproval.setCreateTime(new Date());
            permissionApproval.setDirection(CommonConstant.ONE_INT);
            permissionApproval.setHandlerMan("系统");
            tarOrg.add(permissionOrgCode);
            //党支部节点
            TransferApproval branchApproval = new TransferApproval();
            branchApproval.setId(branchApprovalId);
            branchApproval.setRecordId(recordId);
            branchApproval.setUserId("系统生成");
            branchApproval.setOrgId(orgCode);
            branchApproval.setNextOrgId("");
            branchApproval.setIsInstead(CommonConstant.ZERO_INT);
            branchApproval.setStatus(CommonConstant.ONE_INT);
            branchApproval.setParentId(permissionApprovalId);
            branchApproval.setCreateTime(new Date());
            branchApproval.setDirection(CommonConstant.ONE_INT);
            branchApproval.setHandlerMan("系统");
            tarOrg.add(orgCode);

            transferRecord.setSrcOrgRelationRelAsList(srcOrg);
            transferRecord.setTargetOrgRelationAsList(tarOrg);
            transferRecord.setSrcOrgRelation(srcOrg);
            transferRecord.setTargetOrgRelation(tarOrg);

            //保存入库
            transferRecordService.save(transferRecord);
            iTransferApprovalService.save(sourceApproval);
            iTransferApprovalService.save(middleApproval);
            iTransferApprovalService.save(permissionApproval);
            iTransferApprovalService.save(branchApproval);
        });


        //处理中间交换区转入以及非中间交换区转入(通过关系转接补录方式进入)
        List<TransferRecord> transferRecordInMem = transferRecordService.getBaseMapper().selectList(new QueryWrapper<TransferRecord>().lambda()
                .gt(TransferRecord::getCreateTime, START_DATE)
                .le(TransferRecord::getCreateTime, END_DATE)
                .eq(TransferRecord::getStatus, 1).in(TransferRecord::getType, "124","125"));
        transferRecordInMem.forEach(transferRecord -> {
            String memId = transferRecord.getMemId();
            //就获取人员信息
            MemAllInfo byCode = iMemAllInfoService.findByCode(memId);
            JSONObject memJsonObject=new JSONObject();
            if (ObjectUtil.isNotNull(byCode)){
                memJsonObject = JSONObject.parseObject(JSONObject.toJSONString(byCode));
            }

            //获取组织信息
            Org tarOrg = iOrgService.findOrgByOrgId(transferRecord.getTargetOrgId());
            //生成转入记录
            TransferStatistics tarTransferStatistics = this.createTransferStatistics(transferRecord.getId(),
                    memId, transferRecord.getName(), CommonConstant.ONE_INT, CommonConstant.ONE_INT,
                    transferRecord.getTargetOrgId(), ObjectUtil.isNull(tarOrg) ? baseOrgOrgCode : tarOrg.getOrgCode(), transferRecord.getSrcOrgName(),
                    transferRecord.getTargetOrgName(), transferRecord.getCreateTime(), memJsonObject.toJSONString(),"省外个人转入",transferRecord);
            tansferPopleList.add(tarTransferStatistics);

        });
        //数据保存入库
        boolean b = iTransferStaticsService.saveBatch(tansferPopleList, 1000);

    }

    private List<Object> checkDta(JSONArray jsonArray, Class clazz) {
        List<Object> returnList = new ArrayList<>();
        for (Object jsonObject : jsonArray) {
            JSONObject object = (JSONObject) jsonObject;
            object.remove("id");
            returnList.add(JSONObject.toJavaObject(object, clazz));
        }
        return returnList;
    }

    public  TransferStatistics createTransferStatistics(String  transferId,String memId,String memName, Integer transferType,
                                                        Integer transferCategory, String transferOrgCode, String transferOrgOrgCode,
                                                        String transfrerOutOrgname, String transferinOrgName, Date transferTime,
                                                        String transferMessage,String transferTypeName,TransferRecord transferRecord){
        //生成需要保存的表结构
        TransferStatistics transferStatistics =new TransferStatistics();

        transferStatistics.setId(StrKit.getRandomUUID());
        transferStatistics.setTransferId(transferId);
        transferStatistics.setTransferMemCode(memId);
        transferStatistics.setTransferMemName(memName);
        //关系转接类型（1关系转入；2关系转出）
        transferStatistics.setTransferType(transferType);
        //关系转接类别（1.个人关系转接；整建制转接）
        transferStatistics.setTransferCategory(transferCategory);
        transferStatistics.setTransferOrgCode(transferOrgCode);
        transferStatistics.setTransferOrgOrgCode(transferOrgOrgCode);
        transferStatistics.setTransfrerOutOrgname(transfrerOutOrgname);
        transferStatistics.setTransferinOrgName(transferinOrgName);
        transferStatistics.setTransferTime(transferTime);
        transferStatistics.setTransferMemMessage(transferMessage);
        transferStatistics.setCreateTime(new Date());
        transferStatistics.setTransferTypeName(transferTypeName);

        //处理接收时间
        String currentApprovalId = transferRecord.getCurrentApprovalId();
        TransferApproval currentApproval = iTransferApprovalService.findById(currentApprovalId);

        if (ObjectUtil.isNotNull(currentApproval)){
            transferStatistics.setTansferReceiveTime(currentApproval.getCreateTime());
        }else {
            transferStatistics.setTansferReceiveTime(new Date());
        }
        return transferStatistics;
    }

}
