package com.zenith.front.web.runner;

import com.b1809.analysis.Analysis;
import com.b1809.sync.DbSyncStart;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.PreDestroy;
import javax.annotation.Resource;

/**
 * 关闭事件
 *
 * <AUTHOR>
 * @since 2021/8/2 9:53
 */
@Component
@Slf4j
public class DisposableEvent {
    @Resource
    private Analysis analysis;
    @Resource
    private DbSyncStart dbSyncStart;

    @PreDestroy
    public void destroy() {
        analysisStop();
        dbSyncStop();
    }

    private void analysisStop() {
        final boolean stop = analysis.stop();
        if (!stop) {
            log.error("Analysis Core Stop Fail!");
        } else {
            log.info("Analysis Core Stop Success!");
        }
    }

    private void dbSyncStop() {
        final boolean stop = dbSyncStart.stop();
        if (!stop) {
            log.error("Dbsync Module Stop Fail!");
        } else {
            log.info("Dbsync Module Stop Success!");
        }
    }
}
