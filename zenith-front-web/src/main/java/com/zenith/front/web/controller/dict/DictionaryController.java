package com.zenith.front.web.controller.dict;

import com.zenith.front.api.dict.IDictService;
import com.zenith.front.common.annotation.RequiresPermissions;
import com.zenith.front.common.kit.StrKit;
import com.zenith.front.core.cache.CacheUtils;
import com.zenith.front.model.custom.Record;
import com.zenith.front.model.dto.DicDTO;
import com.zenith.front.model.dto.DicNormalDTO;
import com.zenith.front.model.message.InMessage;
import com.zenith.front.model.message.OutMessage;
import com.zenith.front.model.message.Status;
import com.zenith.front.model.validate.annotation.Validate;
import com.zenith.front.web.controller.BaseController;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.constraints.NotBlank;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * <AUTHOR>
 */
@Validated
@RestController
@RequestMapping("/dictionary")
public class DictionaryController extends BaseController {

    @Resource
    IDictService dictService;

    /**
     * 根据字典类型获取字典的值
     *
     * @date 2019/03/24
     * <AUTHOR>
     */
    @PostMapping("getDictionaryList")
    @RequiresPermissions
    public OutMessage<List<Record>> getDictionaryList(@RequestBody InMessage<DicDTO> inMessage) {
        DicDTO data = inMessage.getData();
        String dicName = data.getDicName();
        if (StrKit.isBlank(dicName)) {
            return new OutMessage<>(Status.PARA_ERROR);
        }
        List<Record> records = dictService.getDic(dicName);
        return new OutMessage<>(Status.SUCCESS, records);
    }

    /**
     * 获取籍贯字典表
     *
     * @param parent
     * @return
     */
    @Validate
    @RequiresPermissions
    @GetMapping("/getDictD48List")
    public OutMessage<List<Record>> getDictD48List(@NotBlank(message = "parent 不能为空") String parent) {
        return new OutMessage<>(Status.SUCCESS, CacheUtils.getDicD48(parent));
    }

    /**
     * 获取导出字典表
     *
     * @param tableName
     * @return
     */
    @Validate
    @RequiresPermissions
    @GetMapping("/getExportList")
    public OutMessage<List<Record>> getExportList(@NotBlank(message = "tableName 不能为空") String tableName) {
        return dictService.getExportList(tableName);
    }


    /**
     * 根据逻辑配置获取匹配项；1、国民经济行业匹配生产性服务行业
     * @param inMessage
     * @return
     */
    @PostMapping("/normalList")
    @RequiresPermissions
    public OutMessage<Map<String, String>> normalList(@RequestBody InMessage<DicNormalDTO> inMessage) {
        return new OutMessage<>(Status.SUCCESS, dictService.normalList(inMessage.getData()));
    }


    /**
     * 获取籍贯字典表当前层级及下级
     *
     * @param parent
     * @return
     */
    @Validate
    @RequiresPermissions
    @GetMapping("/getDictD48JuniorList")
    public OutMessage<List<Record>> getDictD48JuniorList(String parent, String keyWord) {
        return new OutMessage<>(Status.SUCCESS, dictService.getDictD48JuniorList(parent, keyWord));
    }


}
