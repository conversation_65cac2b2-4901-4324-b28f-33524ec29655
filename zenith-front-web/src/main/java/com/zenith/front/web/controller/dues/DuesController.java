package com.zenith.front.web.controller.dues;

import cn.hutool.core.date.DateUtil;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.zenith.front.api.dues.DuesService;
import com.zenith.front.common.annotation.ExcludeHttpBodyDecrypt;
import com.zenith.front.common.annotation.RequiresPermissions;
import com.zenith.front.common.constant.CommonConstant;
import com.zenith.front.model.dto.*;
import com.zenith.front.model.message.InMessage;
import com.zenith.front.model.message.OutMessage;
import com.zenith.front.model.message.Status;
import com.zenith.front.model.validate.annotation.Validate;
import com.zenith.front.model.validate.group.Common1Group;
import com.zenith.front.web.controller.BaseController;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.io.FileNotFoundException;
import java.text.ParseException;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * @author: D.watermelon
 * @date: 2022/12/20 16:53
 * @description: 入则恳恳以尽忠 出则谦谦以自悔
 */
@RestController
@RequestMapping("/dues")
public class DuesController extends BaseController {

    @Autowired
    private DuesService duesService;

    /**
     * 党费基本信息列表查询信息
     */
    @RequiresPermissions
    @PostMapping("/list")
    public OutMessage list(@RequestBody InMessage<DuesPageDto> duesPageDto){
        DuesPageDto duesData = duesPageDto.getData();
        Page<DuesData> list = duesService.list(duesData);
        return new OutMessage<>(Status.SUCCESS,list);
    }

    /**
     * 设置党费缴纳起缴时间（含修改）
     */
    @RequiresPermissions
    @PostMapping("/settingDate")
    public OutMessage settingMemDate(@RequestBody InMessage<MemDTO> memDTOData){
        MemDTO data = memDTOData.getData();
        String memCode = data.getCode();
        Date lastPayDate = data.getLastPayDate();
        Status status = duesService.updateLastPayDate(memCode, lastPayDate);
        return new OutMessage(status);
    }

    /***
     * 设置党费标准（单个设置）
     */
    @RequiresPermissions
    @PostMapping("/settingStandard")
    public OutMessage settingStandard(@RequestBody InMessage<DuesDTO> feeDTOInMessage){
        DuesDTO data = feeDTOInMessage.getData();
        if (!data.getMemOrgOrgCode().startsWith(this.getCurrManOrgCode())) {
            return new OutMessage<>(Status.PERMISSION_DENIED);
        }
        data.setUpdateAccount(this.getCurrUser().getUser().getAccount());
        Status status = duesService.settingStandard(data);
        return new OutMessage(status);
    }


    /***
     * 设置党费标准（批量设置）
     */
    @RequiresPermissions
    @PostMapping("/batchStandard")
    public OutMessage batchStandard(@RequestBody InMessage<List<DuesDTO>> dataMessage){
        List<DuesDTO> data = dataMessage.getData();
        List<Status> configListStatus= new ArrayList<>();
        data.forEach(duesDTO -> {
            duesDTO.setUpdateAccount(this.getCurrUser().getUser().getAccount());
            // TODO: 2023/6/21 批量设置，默认应用到全年
            duesDTO.setIsYearly(CommonConstant.ONE_INT);
            Date standardContinueToDate = duesDTO.getStandardContinueToDate();
            duesDTO.setYear(DateUtil.year(standardContinueToDate));
            configListStatus.add(duesService.settingStandard(duesDTO));
        });
        return new OutMessage<>(Status.SUCCESS,configListStatus);
    }

    /***
     * 批量设置党费标准列表
     */
    @RequiresPermissions
    @PostMapping("/listStandard")
    public OutMessage listStandard(@RequestBody InMessage<List<DuesDTO>> postData){
        List<DuesDTO> data = postData.getData();
        List<String> postMemData =new ArrayList<>();
        for (DuesDTO datum : data) {
            postMemData.add(datum.getMemCode());
        }
        List<Map<String, Object>> returnMessage = duesService.filterMem(postMemData);
        return new OutMessage<>(Status.SUCCESS,returnMessage);
    }


    /***
     * 党费缴纳（单个缴纳）
     */
    @RequiresPermissions
    @PostMapping("/payDues")
    public OutMessage payment(@RequestBody InMessage<DuesDTO> feeDTOInMessage){
        DuesDTO data = feeDTOInMessage.getData();
        data.setUpdateAccount(this.getCurrUser().getUser().getAccount());
        return new OutMessage(duesService.payDues(data));
    }


    /***
     * 党费缴纳（批量）
     */
    @RequiresPermissions
    @PostMapping("/batchPayment")
    public OutMessage batchPayment(@RequestBody InMessage<List<DuesDTO>> feePayData){
        List<DuesDTO> data = feePayData.getData();
        List<Status> payListStatus= new ArrayList<>();
        data.forEach(duesDTO -> {
            duesDTO.setUpdateAccount(this.getCurrUser().getUser().getAccount());
            Status status = duesService.payDues(duesDTO);
            payListStatus.add(status);
        });
        return new OutMessage<>(Status.SUCCESS,payListStatus);
    }

    /***
     * 批量缴纳党费列表
     */
    @RequiresPermissions
    @PostMapping("/listPayment")
    public OutMessage listPayment(@RequestBody InMessage<List<DuesDTO>> postData){
        List<DuesDTO> data = postData.getData();
        List<String> postMemData =new ArrayList<>();
        for (DuesDTO datum : data) {
            postMemData.add(datum.getCode());
        }
        List<DuesData> batchFee = duesService.findBatchFee(postMemData);
        return new OutMessage<>(Status.SUCCESS,batchFee);
    }


    /***
     * 统计信息（党费缴纳）
     */
    @RequiresPermissions
    @GetMapping("/statisticsPayment")
    public OutMessage statisticsPayment(){
        return new OutMessage(Status.SUCCESS);
    }

    /***
     * 历史党员党费缴纳列表
     */
    @RequiresPermissions
    @GetMapping("/historyPayment")
    public OutMessage historyPayment(){
        return new OutMessage(Status.SUCCESS);
    }

    /***
     * 党费标准导入
     */
    @PostMapping("/importExcelStand")
    @RequiresPermissions
    public OutMessage importExcelDudeStand(@RequestBody InMessage<ImportExcelDevelopDTO> inMessage) throws Exception {
        ImportExcelDevelopDTO data = inMessage.getData();
        String orgCode = data.getOrgCode();
        String currManOrgCode = this.getCurrManOrgCode();
        if (!orgCode.startsWith(currManOrgCode)){
            return new OutMessage(Status.PERMISSION_INDEX_OUT);
        }
        return duesService.importExcelDudeStand(data,this.getCurrUser().getUser().getAccount());
    }

    /***
     * 党费缴纳导入
     */
    @PostMapping("/importExcelDude")
    @RequiresPermissions
    public OutMessage importExcelDudes(@RequestBody InMessage<ImportExcelDevelopDTO> inMessage) throws FileNotFoundException {
        ImportExcelDevelopDTO data = inMessage.getData();
        String orgCode = data.getOrgCode();
        String currManOrgCode = this.getCurrManOrgCode();
        if (!orgCode.startsWith(currManOrgCode)){
            return new OutMessage(Status.PERMISSION_INDEX_OUT);
        }
        return  duesService.importExcelDudes(data,this.getCurrUser().getUser().getAccount());
    }

    /**
     * 党费缴纳模板导出
     * */
    @PostMapping("/templateDues")
    @RequiresPermissions
    public OutMessage templateDuesExport(@RequestBody InMessage<ImportExcelDevelopDTO> inMessage) throws Exception {
        String orgCode = inMessage.getData().getOrgCode();
        return duesService.templateDuesExport(orgCode);
    }

    /**
     * 党费标注模板导出
     * */
    @PostMapping("/templateStand")
    @RequiresPermissions
    public OutMessage templateStandExport(@RequestBody InMessage<ImportExcelDevelopDTO> inMessage) throws Exception {
        String orgCode = inMessage.getData().getOrgCode();
        return duesService.templateStandExport(orgCode);
    }

}
