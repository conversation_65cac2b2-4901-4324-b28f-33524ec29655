package com.zenith.front.web.scheduled;

import cn.hutool.core.collection.CollUtil;
import com.zenith.front.api.user.IUserService;
import com.zenith.front.common.constant.CommonConstant;
import com.zenith.front.core.feign.client.VillageCommunityClient;
import com.zenith.front.core.feign.dto.PushUserInfoDTO;
import com.zenith.front.core.feign.dto.VcUserInfo;
import com.zenith.front.model.vo.UserPO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.condition.ConditionalOnExpression;
import org.springframework.context.annotation.Configuration;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

/**
 * 定时推送村社区系统用户到中间交换区
 *
 * <AUTHOR>
 */
@Slf4j
@Configuration
@ConditionalOnExpression("${vc.feign.enabled:true}&&${vc.user.open:true}")
public class PullVcUserTask extends AbstractSchedulingConfigurer {

    @Value("${vc.user.cron}")
    private String cron;
    @Resource
    private IUserService userService;
    @Resource
    private VillageCommunityClient villageCommunityClient;

    @Override
    public void task() {
        List<UserPO> userList = userService.findUserByManagementSystem(CommonConstant.TWO);
        if (CollUtil.isEmpty(userList)) {
            return;
        }
        List<VcUserInfo> userInfoList = new ArrayList<>();
        for (UserPO userPO : userList) {
            VcUserInfo userInfo = new VcUserInfo();
            userInfo.setId(userPO.getId());
            userInfo.setAccount(userPO.getAccount());
            userInfo.setPassword(userPO.getPassword());
            userInfo.setIdCard(userPO.getIdcard());
            userInfoList.add(userInfo);
        }
        PushUserInfoDTO pushUserInfoDTO = new PushUserInfoDTO();
        pushUserInfoDTO.setList(userInfoList);
        pushUserInfoDTO.setNginxKey(exchangeNginxKey);
        villageCommunityClient.pushUserInfo(pushUserInfoDTO);
        log.info("已推送用户信息到中间交换区:{}", userInfoList.size());
    }

    @Override
    public String cron() {
        return cron;
    }
}
