package com.zenith.front.web.controller.data;

import com.zenith.front.api.org.IOrgGroupService;
import com.zenith.front.common.annotation.RequiresPermissions;
import com.zenith.front.common.annotation.VerifyLevelCode;
import com.zenith.front.model.dto.LedgerDTO;
import com.zenith.front.model.message.InMessage;
import com.zenith.front.model.validate.annotation.Validate;
import com.zenith.front.model.validate.group.Common1Group;
import com.zenith.front.web.controller.BaseController;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @version 1.0
 * @Description: 班子建设
 * @date 2019/6/13 16:09
 */
@RestController
@RequestMapping("/data/groupconstruction")
public class GroupConstructionDataController extends BaseController {

    @Resource
    private IOrgGroupService iOrgGroupService;

    /**
     * 导出两委委员数据
     *
     * @return
     */
    @Validate(group = Common1Group.class)
    @RequiresPermissions
    @VerifyLevelCode
    @PostMapping("/exportTwoCommittee")
    public void exportTwoCommittee(@RequestBody InMessage<LedgerDTO> inMessage) {
        iOrgGroupService.exportTwoCommittee(inMessage.getData());
    }

    /**
     * 导出基层党组织换届情况统计表
     *
     * @return
     */
    @Validate(group = Common1Group.class)
    @RequiresPermissions
    @VerifyLevelCode
    @PostMapping("/exportChangeElection")
    public void exportChangeElection(@RequestBody InMessage<LedgerDTO> inMessage) {
        iOrgGroupService.exportChangeElection(inMessage.getData());
    }

}
