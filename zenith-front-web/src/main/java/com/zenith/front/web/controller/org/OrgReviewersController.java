package com.zenith.front.web.controller.org;

import com.zenith.front.api.org.IOrgReviewersService;
import com.zenith.front.common.annotation.RequiresPermissions;
import com.zenith.front.common.constant.OperateConstant;
import com.zenith.front.model.dto.OrgReviewersDTO;
import com.zenith.front.model.dto.OrgReviewersUpdateDTO;
import com.zenith.front.model.message.InMessage;
import com.zenith.front.model.message.OutMessage;
import com.zenith.front.web.controller.BaseController;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import javax.validation.constraints.Max;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * 民主评议-评议人员
 * @date 2021/08/16 15:32
 */
@RestController
@RequestMapping("/org/reviewers")
@Validated
public class OrgReviewersController extends BaseController {

    @Resource
    private IOrgReviewersService orgReviewersService;

    /**
     * 民主评议组织操作—添加评议人员
     */
    @PostMapping("/save")
    @RequiresPermissions(opt = OperateConstant.INSERT)
    public OutMessage save(@Valid @RequestBody InMessage<OrgReviewersDTO> inMessage) {
        return orgReviewersService.save(inMessage.getData());
    }

    /**
     * 吧
     * 民主评议组织操作—删除评议人员
     */
    @GetMapping("/remove")
    @RequiresPermissions(opt = OperateConstant.DELETE)
    public OutMessage remove(@NotNull(message = "code 不能为空") String code) {
        return orgReviewersService.remove(code);
    }

    /**
     * 民主评议组织操作—修改评议人员
     */
    @PostMapping("/update")
    @RequiresPermissions(opt = OperateConstant.UPDATE)
    public OutMessage update(@Valid @RequestBody InMessage<OrgReviewersUpdateDTO> inMessage) {
        return orgReviewersService.update(inMessage.getData());
    }

    /**
     * 民主评议组织操作—查询评议人员
     */
    @GetMapping("/list")
    @RequiresPermissions(opt = OperateConstant.QUERY)
    public OutMessage list(@Min(value = 1, message = "页码最小为1") int pageNum,
                           @Max(value = 100, message = "页大小范围在1-100") int pageSize,
                           @NotNull(message = "appraisalCode 不能为空") String appraisalCode) {
        return orgReviewersService.list(pageNum, pageSize, appraisalCode);
    }

    /**
     * 民主评议组织操作—批量修改时查询评议人员
     */
    @GetMapping("/listMem")
    @RequiresPermissions(opt = OperateConstant.QUERY)
    public OutMessage listMem(@Min(value = 1, message = "页码最小为1") int pageNum,
                              @Max(value = 100, message = "页大小范围在1-100") int pageSize,
                              @NotNull(message = "code 不能为空") String code) {
        return orgReviewersService.listMem(pageNum, pageSize, code);
    }

}
