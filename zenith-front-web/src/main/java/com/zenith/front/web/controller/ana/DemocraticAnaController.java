package com.zenith.front.web.controller.ana;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.zenith.front.api.democraticreview.IDemocraticReviewService;
import com.zenith.front.common.annotation.RequiresPermissions;
import com.zenith.front.model.message.OutMessage;
import com.zenith.front.model.validate.annotation.Validate;
import com.zenith.front.model.vo.DemocraticAnaVO;
import com.zenith.front.model.vo.YpyPageVO;
import com.zenith.front.web.controller.BaseController;
import org.hibernate.validator.constraints.Range;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @Description: 民主评议
 * @date 2019/6/24 9:37
 */
@RestController
@RequestMapping("/ana/democratic")
public class DemocraticAnaController extends BaseController {

    @Resource
    private IDemocraticReviewService service;


    @RequiresPermissions
    @Validate
    @GetMapping("/index")
    public OutMessage<DemocraticAnaVO> index(
            @NotNull(message = "orgID不能为空")
            String orgID,
            @NotNull(message = "orgCode不能为空")
            String orgCode,
            @NotNull(message = "pageNum不能为空")
            @Min(value = 1L,message = "最小值为1")
            Integer pageNum
            ){

        return service.indexCount(orgID,orgCode,pageNum);
    }

    @RequiresPermissions
    @Validate
    @GetMapping("/ypyPage")
    public OutMessage<Page<YpyPageVO>> ypyPage(
            @NotNull(message = "页码不能为空")
            @Min(value = 1L,message = "页码最小值为1")
            Integer pageNum,
            @NotNull(message = "也大小不能为空")
            @Range(min = 1L,max = 100L,message = "页大小为1-100")
            Integer pageSize,
            @NotBlank(message = "组织id不能为空")
            String orgID){
        return service.ypyPage(pageNum,pageSize,orgID);
    }

    @RequiresPermissions
    @Validate
    @PostMapping("/wpyPage")
    public OutMessage<Page<YpyPageVO>> wpyPage(
            @NotNull(message = "页码不能为空")
            @Min(value = 1L,message = "页码最小值为1")
                    Integer pageNum,
            @NotNull(message = "也大小不能为空")
            @Range(min = 1L,max = 100L,message = "页大小为1-100")
                    Integer pageSize,
            @NotBlank(message = "组织id不能为空")
                    String orgID){
        return service.wpyPage(pageNum,pageSize,orgID);
    }

}
