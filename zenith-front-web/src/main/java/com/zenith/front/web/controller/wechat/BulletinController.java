package com.zenith.front.web.controller.wechat;

import com.zenith.front.api.mem.IBulletinService;
import com.zenith.front.common.annotation.RequiresPermissions;
import com.zenith.front.common.annotation.VerifyLevelCode;
import com.zenith.front.model.message.OutMessage;
import com.zenith.front.model.validate.annotation.Validate;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.validation.constraints.NotBlank;

/**
 * <AUTHOR>
 * @Description: 党委党内统计公报
 * @date 2019/6/16 14:51
 */
@RestController
@RequestMapping("/wx/bulletin")
public class BulletinController extends WxBaseController {
    @Resource
    private IBulletinService bulletinService;

    /**
     * 获取基础信息
     *
     * @return
     */
    @GetMapping("/getBasicInfo")
    @Validate
    @RequiresPermissions
    @VerifyLevelCode
    public OutMessage getBasicInfo(@NotBlank String orgCode) {
        return bulletinService.getBasicInfo(orgCode);
    }

    /**
     * 获取党员信息
     *
     * @param orgCode
     * @return
     */
    @GetMapping("/getMemInfo")
    @Validate
    @RequiresPermissions
    @VerifyLevelCode
    public OutMessage getMemInfo(@NotBlank String orgCode) {
        return bulletinService.getMemInfo(orgCode);
    }

    /**
     * 获取发展党员情况
     *
     * @param orgCode
     * @return
     */
    @GetMapping("/getMemDevelopInfo")
    @Validate
    @RequiresPermissions
    @VerifyLevelCode
    public OutMessage getMemDevelopInfo(@NotBlank String orgCode) {
        return bulletinService.getMemDevelopInfo(orgCode);
    }

    /**
     * 党内表彰
     *
     * @param orgCode
     * @return
     */
    @GetMapping("/getPartyCommend")
    @Validate
    @RequiresPermissions
    @VerifyLevelCode
    public OutMessage getPartyCommend(@NotBlank String orgCode) {
        return bulletinService.getPartyCommend(orgCode);
    }

    /**
     * 申请入党情况
     *
     * @param orgCode
     * @return
     */
    @GetMapping("/getApplyJoinParty")
    @Validate
    @RequiresPermissions
    @VerifyLevelCode
    public OutMessage getApplyJoinParty(@NotBlank String orgCode) {
        return bulletinService.getApplyJoinParty(orgCode);
    }

    /**
     * 党组织情况
     *
     * @param orgCode
     * @return
     */
    @GetMapping("/getOrgInfo")
    @Validate
    @RequiresPermissions
    @VerifyLevelCode
    public OutMessage getOrgInfo(@NotBlank String orgCode) {
        return bulletinService.getOrgInfo(orgCode);
    }

}
