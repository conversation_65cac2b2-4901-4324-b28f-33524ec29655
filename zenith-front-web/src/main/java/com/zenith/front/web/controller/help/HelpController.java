package com.zenith.front.web.controller.help;

import cn.hutool.core.util.StrUtil;
import com.zenith.front.common.annotation.RequiresPermissions;
import com.zenith.front.model.message.OutMessage;
import com.zenith.front.model.message.Status;
import com.zenith.front.web.controller.BaseController;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @version V1.0
 * @Package com.zenith.front.web.controller.help
 * @date 2022/5/31 17:50
 */
@RestController
@RequestMapping("help")
public class HelpController extends BaseController {

    @GetMapping
    @RequiresPermissions
    public OutMessage<?> help() {
        Map<String, String> map = new HashMap<>(10);
        String host = this.httpServletRequest.getHeader("Host");
        if (host.contains(":")) {
            host = StrUtil.subBefore(host, ":", true);
        }
        map.put("url", "http://" + host + ":999");
        return new OutMessage<>(Status.SUCCESS, map);
    }
}
