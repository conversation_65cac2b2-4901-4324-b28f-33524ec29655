package com.zenith.front.web.controller.chars;


import com.zenith.front.api.fee.IFeeService;
import com.zenith.front.common.annotation.RequiresPermissions;
import com.zenith.front.model.dto.ChartDataDTO;
import com.zenith.front.model.message.InMessage;
import com.zenith.front.model.message.OutMessage;
import com.zenith.front.model.validate.annotation.Validate;
import com.zenith.front.model.validate.group.Common1Group;
import com.zenith.front.web.controller.BaseController;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @Description 党费概况
 * @date 2019/6/18 9:23
 */
@RestController
@RequestMapping("/chart/fee")
public class FeeCharController extends BaseController {


    @Resource
    private IFeeService feeCharService;


    /**
     * 党费标准人数统计
     *
     * @param inMessage
     * @return
     */
    @Validate(group = Common1Group.class)
    @RequiresPermissions
    @PostMapping("/getFeeStandardTotal")
    public OutMessage getFeeStandardTotal(@RequestBody InMessage<ChartDataDTO> inMessage) {
        return feeCharService.getFeeStandardTotal(inMessage.getData());
    }

    /**
     * 党费缴纳人数统计
     *
     * @param inMessage
     * @return
     */
    @Validate(group = Common1Group.class)
    @RequiresPermissions
    @PostMapping("/getFeePayTotal")
    public OutMessage getFeePayTotal(@RequestBody InMessage<ChartDataDTO> inMessage) {
        return feeCharService.getFeePayTotal(inMessage.getData());
    }

    /**
     * 党费缴纳金额统计
     *
     * @param inMessage
     * @return
     */
    @Validate(group = Common1Group.class)
    @RequiresPermissions
    @PostMapping("/getFeePayMoneyTotal")
    public OutMessage getFeePayMoneyTotal(@RequestBody InMessage<ChartDataDTO> inMessage) {
        return feeCharService.getFeePayMoneyTotal(inMessage.getData());
    }


    /**
     * 党费支出金额统计
     *
     * @param inMessage
     * @return
     */
    @Validate(group = Common1Group.class)
    @RequiresPermissions
    @PostMapping("/getFeeDisburseTotal")
    public OutMessage getFeeDisburseTotal(@RequestBody InMessage<ChartDataDTO> inMessage) {
        return feeCharService.getFeeDisburseTotal(inMessage.getData());
    }

    /**
     * 按月统计党费缴纳
     *
     * @param inMessage
     * @return
     */
    @Validate(group = Common1Group.class)
    @RequiresPermissions
    @PostMapping("/getFeePayByMonth")
    public OutMessage getFeePayByMonth(@RequestBody InMessage<ChartDataDTO> inMessage) {
        return feeCharService.getFeePayByMonth(inMessage.getData());
    }

    /**
     * 按月统计党费支出
     *
     * @param inMessage
     * @return
     */
    @Validate(group = Common1Group.class)
    @RequiresPermissions
    @PostMapping("/getFeeDisburseByMonth")
    public OutMessage getFeeDisburseByMonth(@RequestBody InMessage<ChartDataDTO> inMessage) {
        return feeCharService.getFeeDisburseByMonth(inMessage.getData());
    }

    /**
     * 按类型统计党费支出
     *
     * @param inMessage
     * @return
     */
    @Validate(group = Common1Group.class)
    @RequiresPermissions
    @PostMapping("/getFeeDisburseByType")
    public OutMessage getFeeDisburseByType(@RequestBody InMessage<ChartDataDTO> inMessage) {
        return feeCharService.getFeeDisburseByType(inMessage.getData());
    }

    /**
     * 按类型统计党费支出占比
     *
     * @param inMessage
     * @return
     */
    @Validate(group = Common1Group.class)
    @RequiresPermissions
    @PostMapping("/getFeeDisburseByTypeRatio")
    public OutMessage getFeeDisburseByTypeRatio(@RequestBody InMessage<ChartDataDTO> inMessage) {
        return feeCharService.getFeeDisburseByTypeRatio(inMessage.getData());
    }

    /**
     * 按录入类型统计党费支出占比
     *
     * @param inMessage
     * @return
     */
    @Validate(group = Common1Group.class)
    @RequiresPermissions
    @PostMapping("/getFeeDisburseByInputRatio")
    public OutMessage getFeeDisburseByInputRatio(@RequestBody InMessage<ChartDataDTO> inMessage) {
        return feeCharService.getFeeDisburseByInputRatio(inMessage.getData());
    }

    /**
     * 按计算类型统计党费缴纳占比
     *
     * @param inMessage
     * @return
     */
    @Validate(group = Common1Group.class)
    @RequiresPermissions
    @PostMapping("/getFeeByTypeRatio")
    public OutMessage getFeeByTypeRatio(@RequestBody InMessage<ChartDataDTO> inMessage) {
        return feeCharService.getFeeByTypeRatio(inMessage.getData());
    }

    /**
     * 统计总支出金额，总结余金额
     *
     * @param inMessage
     * @return
     */
    @Validate(group = Common1Group.class)
    @RequiresPermissions
    @PostMapping("/getFeeDisburseAndSurplus")
    public OutMessage getFeeDisburseAndSurplus(@RequestBody InMessage<ChartDataDTO> inMessage) {
        return feeCharService.getFeeDisburseAndSurplus(inMessage.getData());
    }

    /**
     * 统计总下拨金额，已下拨金额
     *
     * @param inMessage
     * @return
     */
    @Validate(group = Common1Group.class)
    @RequiresPermissions
    @PostMapping("/getFeeAllocateTotal")
    public OutMessage getFeeAllocateTotal(@RequestBody InMessage<ChartDataDTO> inMessage) {
        return feeCharService.getFeeAllocateTotal(inMessage.getData());
    }

    /**
     * 统计总收取金额，已对账金额？
     *
     * @param inMessage
     * @return
     */
    @Validate(group = Common1Group.class)
    @RequiresPermissions
    @PostMapping("/getFeeAndCheckTotal")
    public OutMessage getFeeAndCheckTotal(@RequestBody InMessage<ChartDataDTO> inMessage) {
        return feeCharService.getFeeAndCheckTotal(inMessage.getData());
    }

    /**
     * 统计下拨党委，下拨党支部
     *
     * @param inMessage
     * @return
     */
    @Validate(group = Common1Group.class)
    @RequiresPermissions
    @PostMapping("/getFeeAllocateOrgCount")
    public OutMessage getFeeAllocateOrgCount(@RequestBody InMessage<ChartDataDTO> inMessage) {
        return feeCharService.getFeeAllocateOrgCount(inMessage.getData());
    }

    /**
     * 下拨金额月份统计
     *
     * @param inMessage
     * @return
     */
    @Validate(group = Common1Group.class)
    @RequiresPermissions
    @PostMapping("/getFeeAllocateByMonth")
    public OutMessage getFeeAllocateByMonth(@RequestBody InMessage<ChartDataDTO> inMessage) {
        return feeCharService.getFeeAllocateByMonth(inMessage.getData());
    }

    /**
     * 党费对账金额月份统计
     *
     * @param inMessage
     * @return
     */
    @Validate(group = Common1Group.class)
    @RequiresPermissions
    public OutMessage getFeeCheckByMonth(InMessage<ChartDataDTO> inMessage) {
        return null;
    }


}
