package com.zenith.front.web.config;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.zenith.front.api.activist.IActivistTransferRecordService;
import com.zenith.front.api.develop.IDevelopStepLogAllService;
import com.zenith.front.api.develop.IDevelopStepLogService;
import com.zenith.front.api.lockfiled.ILockFiledLogService;
import com.zenith.front.api.mem.*;
import com.zenith.front.api.org.*;
import com.zenith.front.api.transfer.ITransferRecordService;
import com.zenith.front.api.unit.IUnitCommitteeService;
import com.zenith.front.api.unit.IUnitCommunityService;
import com.zenith.front.api.unit.IUnitCountrusideService;
import com.zenith.front.api.unit.IUnitResidentService;
import com.zenith.front.common.constant.DictConstant;
import com.zenith.front.common.untils.SM4Untils;
import com.zenith.front.core.cache.CacheUtils;
import com.zenith.front.core.kit.CollectionUtil;

import com.zenith.front.model.bean.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.CommandLineRunner;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
//@Component
@Slf4j
public class EncryptSpecialFieldsRunner implements CommandLineRunner {

    @Resource
    IMemService memService;
    @Resource
    IMemAllInfoService memAllService;
    @Resource
    IMemDevelopService memDevelopService;
    @Resource
    IMemDevelopAllService memDevelopAllService;
    @Resource
    IDevelopStepLogService developStepLogService;
    @Resource
    IDevelopStepLogAllService developStepLogAllService;
    @Resource
    IActivistTransferRecordService iActivistTransferRecordService;
    @Resource
    ILockFiledLogService iLockFiledLogService;
    @Resource
    IMemDifficultService iMemDifficultService;
    @Resource
    IMemFlowService iMemFlowService;
    @Resource
    IMemFlowAllService iMemFlowAllService;
    @Resource
    IMemHsitoryService iMemHistoryService;
    @Resource
    IMemLogService iMemLogService;
    @Resource
    IMemReportService iMemReportService;
    @Resource
    IUnitResidentService unitResidentService;
    @Resource
    IUnitCountrusideService unitCountrusideService;
    @Resource
    IUnitCommitteeService unitCommitteeService;
    @Resource
    IUnitCommunityService unitCommunityService;
    @Resource
    ITransferRecordService transferRecordService;
    @Resource
    IOrgReviewersService orgReviewersService;
    @Resource
    IOrgPartyCongressCommitteeAllService orgPartyCongressCommitteeAllService;
    @Resource
    IOrgPartyCongressCommitteeService orgPartyCongressCommitteeService;
    @Resource
    IOrgHistoryService orgHistoryService;
    @Resource
    IOrgCommitteeService orgCommitteeService;
    @Resource
    IOrgAllService orgAllService;
    @Resource
    IOrgService orgService;
    @Value("${exchange_nginx_key}")
    private String exchange_nginx_key;

    @Override
    public void run(String... args) throws Exception {
        List<Mem> memList = memService.list(new LambdaQueryWrapper<Mem>().select(Mem::getId, Mem::getName, Mem::getIdcard, Mem::getPhone, Mem::getHomeAddress));
        memList.forEach(t -> {
            if (StringUtils.hasText(t.getName())) {
                t.setName(SM4Untils.encryptContent(exchange_nginx_key, t.getName()));
            }
            if (StringUtils.hasText(t.getIdcard())) {
                t.setIdcard(SM4Untils.encryptContent(exchange_nginx_key, t.getIdcard()));
            }
            if (StringUtils.hasText(t.getPhone())) {
                t.setPhone(SM4Untils.encryptContent(exchange_nginx_key, t.getPhone()));
            }
            if (StringUtils.hasText(t.getHomeAddress())) {
                t.setHomeAddress(SM4Untils.encryptContent(exchange_nginx_key, t.getHomeAddress()));
            }
        });
        if (CollUtil.isNotEmpty(memList)) {
            memService.updateBatchById(memList, memList.size());
            log.info("mem表特殊字段加密完成:{}", memList.size());
        }

        Map<String, String> d22Map = CollectionUtil.listRecordToMap(CacheUtils.getDic(DictConstant.DICT_D22), "key", "name");
        List<MemAllInfo> memAllList = memAllService.list(new LambdaQueryWrapper<MemAllInfo>().select(MemAllInfo::getId, MemAllInfo::getName, MemAllInfo::getIdcard, MemAllInfo::getPhone, MemAllInfo::getD022Code));
        memAllList.forEach(t -> {
            if (StringUtils.hasText(t.getName())) {
                t.setName(SM4Untils.encryptContent(exchange_nginx_key, t.getName()));
            }
            if (StringUtils.hasText(t.getIdcard())) {
                t.setIdcard(SM4Untils.encryptContent(exchange_nginx_key, t.getIdcard()));
            }
            if (StringUtils.hasText(t.getPhone())) {
                t.setPhone(SM4Untils.encryptContent(exchange_nginx_key, t.getPhone()));
            }
            String d022Code = t.getD022Code();
            if (StringUtils.hasText(d022Code)) {
                List<String> d22Name = Arrays.stream(d022Code.split(",")).filter(d22Map::containsKey).map(d22Map::get).collect(Collectors.toList());
                t.setD022Name(SM4Untils.encryptContent(exchange_nginx_key, CollUtil.join(d22Name, ",")));
            }
        });
        if (CollUtil.isNotEmpty(memAllList)) {
            memAllService.updateBatchById(memAllList, memAllList.size());
            log.info("memAll表特殊字段加密完成:{}", memAllList.size());
        }

        List<MemDevelop> developList = memDevelopService.list(new LambdaQueryWrapper<MemDevelop>().select(MemDevelop::getId, MemDevelop::getName, MemDevelop::getIdcard, MemDevelop::getPhone, MemDevelop::getHomeAddress));
        developList.forEach(t -> {
            if (StringUtils.hasText(t.getName())) {
                t.setName(SM4Untils.encryptContent(exchange_nginx_key, t.getName()));
            }
            if (StringUtils.hasText(t.getIdcard())) {
                t.setIdcard(SM4Untils.encryptContent(exchange_nginx_key, t.getIdcard()));
            }
            if (StringUtils.hasText(t.getPhone())) {
                t.setPhone(SM4Untils.encryptContent(exchange_nginx_key, t.getPhone()));
            }
            if (StringUtils.hasText(t.getHomeAddress())) {
                t.setHomeAddress(SM4Untils.encryptContent(exchange_nginx_key, t.getHomeAddress()));
            }
        });
        if (CollUtil.isNotEmpty(developList)) {
            memDevelopService.updateBatchById(developList, developList.size());
            log.info("memDevelop表特殊字段加密完成:{}", developList.size());
        }

        List<MemDevelopAll> developAllList = memDevelopAllService.list(new LambdaQueryWrapper<MemDevelopAll>().select(MemDevelopAll::getId, MemDevelopAll::getName, MemDevelopAll::getIdcard, MemDevelopAll::getPhone));
        developAllList.forEach(t -> {
            if (StringUtils.hasText(t.getName())) {
                t.setName(SM4Untils.encryptContent(exchange_nginx_key, t.getName()));
            }
            if (StringUtils.hasText(t.getIdcard())) {
                t.setIdcard(SM4Untils.encryptContent(exchange_nginx_key, t.getIdcard()));
            }
            if (StringUtils.hasText(t.getPhone())) {
                t.setPhone(SM4Untils.encryptContent(exchange_nginx_key, t.getPhone()));
            }
        });
        if (CollUtil.isNotEmpty(developAllList)) {
            memDevelopAllService.updateBatchById(developAllList, developAllList.size());
            log.info("memDevelopAll表特殊字段加密完成:{}", developAllList.size());
        }

        //  ccp_develop_step_log name idcard phone home_address
        List<DevelopStepLog> developStepLogList = developStepLogService.list(new LambdaQueryWrapper<DevelopStepLog>().select(DevelopStepLog::getId, DevelopStepLog::getName, DevelopStepLog::getIdcard, DevelopStepLog::getPhone, DevelopStepLog::getHomeAddress));
        developStepLogList.forEach(t -> {
            if (StringUtils.hasText(t.getName())) {
                t.setName(SM4Untils.encryptContent(exchange_nginx_key, t.getName()));
            }
            if (StringUtils.hasText(t.getIdcard())) {
                t.setIdcard(SM4Untils.encryptContent(exchange_nginx_key, t.getIdcard()));
            }
            if (StringUtils.hasText(t.getPhone())) {
                t.setPhone(SM4Untils.encryptContent(exchange_nginx_key, t.getPhone()));
            }
            if (StringUtils.hasText(t.getHomeAddress())) {
                t.setHomeAddress(SM4Untils.encryptContent(exchange_nginx_key, t.getHomeAddress()));
            }
        });
        if (CollUtil.isNotEmpty(developStepLogList)) {
            developStepLogService.updateBatchById(developStepLogList, developStepLogList.size());
            log.info("developStepLog表特殊字段加密完成:{}", developStepLogList.size());
        }

        //  ccp_develop_step_log_all name idcard phone home_address
        List<DevelopStepLogAll> developStepLogAllList = developStepLogAllService.list(new LambdaQueryWrapper<DevelopStepLogAll>().select(DevelopStepLogAll::getId, DevelopStepLogAll::getName, DevelopStepLogAll::getIdcard, DevelopStepLogAll::getPhone, DevelopStepLogAll::getHomeAddress));
        developStepLogAllList.forEach(t -> {
            if (StringUtils.hasText(t.getName())) {
                t.setName(SM4Untils.encryptContent(exchange_nginx_key, t.getName()));
            }
            if (StringUtils.hasText(t.getIdcard())) {
                t.setIdcard(SM4Untils.encryptContent(exchange_nginx_key, t.getIdcard()));
            }
            if (StringUtils.hasText(t.getPhone())) {
                t.setPhone(SM4Untils.encryptContent(exchange_nginx_key, t.getPhone()));
            }
            if (StringUtils.hasText(t.getHomeAddress())) {
                t.setHomeAddress(SM4Untils.encryptContent(exchange_nginx_key, t.getHomeAddress()));
            }
        });
        if (CollUtil.isNotEmpty(developStepLogAllList)) {
            developStepLogAllService.updateBatchById(developStepLogAllList, developStepLogAllList.size());
            log.info("developStepLogAll表特殊字段加密完成:{}", developStepLogAllList.size());
        }

        //  ccp_activist_transfer_record name
//        List<ActivistTransferRecord> activistTransferRecords = iActivistTransferRecordService.list(new LambdaQueryWrapper<ActivistTransferRecord>().select(ActivistTransferRecord::getId, ActivistTransferRecord::getName));
//        activistTransferRecords.forEach(t -> {
//            if (StringUtils.hasText(t.getName())) {
//                t.setName(SM4Untils.encryptContent(exchange_nginx_key, t.getName()));
//            }
//        });
//        if (CollUtil.isNotEmpty(activistTransferRecords)) {
//            iActivistTransferRecordService.updateBatchById(activistTransferRecords, activistTransferRecords.size());
//            log.info("ccp_activist_transfer_record表特殊字段加密完成:{}", activistTransferRecords.size());
//        }

        //  ccp_lock_filed_log unlock_name
        List<LockFiledLog> lockFiledLogs = iLockFiledLogService.list(new LambdaQueryWrapper<LockFiledLog>().select(LockFiledLog::getId, LockFiledLog::getUnlockName));
        lockFiledLogs.forEach(t -> {
            if (StringUtils.hasText(t.getUnlockName())) {
                t.setUnlockName(SM4Untils.encryptContent(exchange_nginx_key, t.getUnlockName()));
            }
        });
        if (CollUtil.isNotEmpty(lockFiledLogs)) {
            iLockFiledLogService.updateBatchById(lockFiledLogs, lockFiledLogs.size());
            log.info("ccp_lock_filed_log表特殊字段加密完成:{}", lockFiledLogs.size());
        }

        //  ccp_mem_difficult mem_name
        List<MemDifficult> memDifficults = iMemDifficultService.list(new LambdaQueryWrapper<MemDifficult>().select(MemDifficult::getId, MemDifficult::getMemName));
        memDifficults.forEach(t -> {
            if (StringUtils.hasText(t.getMemName())) {
                t.setMemName(SM4Untils.encryptContent(exchange_nginx_key, t.getMemName()));
            }
        });
        if (CollUtil.isNotEmpty(memDifficults)) {
            iMemDifficultService.updateBatchById(memDifficults, memDifficults.size());
            log.info("ccp_mem_difficult表特殊字段加密完成:{}", memDifficults.size());
        }

        //  ccp_mem_flow idcard memName
        List<MemFlow> memFlows = iMemFlowService.list(new LambdaQueryWrapper<MemFlow>().select(MemFlow::getId, MemFlow::getIdcard, MemFlow::getMemName));
        List<MemFlow> tmpMemFlows = new ArrayList<>();
        if (CollUtil.isNotEmpty(memFlows)) {
            memFlows.forEach(t -> {
                boolean a = StringUtils.hasText(t.getIdcard());
                if (a) {
                    t.setIdcard(SM4Untils.encryptContent(exchange_nginx_key, t.getIdcard()));
                }
                boolean b = StringUtils.hasText(t.getMemName());
                if (b) {
                    t.setMemName(SM4Untils.encryptContent(exchange_nginx_key, t.getMemName()));
                }
                if (a || b) {
                    tmpMemFlows.add(t);
                }
            });
            if (CollUtil.isNotEmpty(tmpMemFlows)) {
                iMemFlowService.updateBatchById(tmpMemFlows, tmpMemFlows.size());
                log.info("ccp_mem_flow表特殊字段加密完成:{}", tmpMemFlows.size());
            }
        }

        //  ccp_mem_flow_all idcard memName
        List<MemFlowAll> memFlowAlls = iMemFlowAllService.list(new LambdaQueryWrapper<MemFlowAll>().select(MemFlowAll::getId, MemFlowAll::getIdcard, MemFlowAll::getMemName));
        List<MemFlowAll> tmpMemFlowAlls = new ArrayList<>();
        if (CollUtil.isNotEmpty(memFlowAlls)) {
            memFlowAlls.forEach(t -> {
                boolean a = StringUtils.hasText(t.getIdcard());
                if (a) {
                    t.setIdcard(SM4Untils.encryptContent(exchange_nginx_key, t.getIdcard()));
                }
                boolean b = StringUtils.hasText(t.getMemName());
                if (b) {
                    t.setMemName(SM4Untils.encryptContent(exchange_nginx_key, t.getMemName()));
                }
                if (a || b) {
                    tmpMemFlowAlls.add(t);
                }
            });
            if (CollUtil.isNotEmpty(tmpMemFlowAlls)) {
                iMemFlowAllService.updateBatchById(tmpMemFlowAlls, tmpMemFlowAlls.size());
                log.info("ccp_mem_flow_all表特殊字段加密完成:{}", tmpMemFlowAlls.size());
            }
        }

        //  ccp_mem_history name idcard phone home_address
        List<MemHistory> memHistoryList = iMemHistoryService.list(new LambdaQueryWrapper<MemHistory>().select(MemHistory::getId, MemHistory::getName, MemHistory::getIdcard, MemHistory::getPhone, MemHistory::getHomeAddress));
        if (CollUtil.isNotEmpty(memHistoryList)) {
            memHistoryList.forEach(t -> {
                if (StringUtils.hasText(t.getName())) {
                    t.setName(SM4Untils.encryptContent(exchange_nginx_key, t.getName()));
                }
                if (StringUtils.hasText(t.getIdcard())) {
                    t.setIdcard(SM4Untils.encryptContent(exchange_nginx_key, t.getIdcard()));
                }
                if (StringUtils.hasText(t.getPhone())) {
                    t.setPhone(SM4Untils.encryptContent(exchange_nginx_key, t.getPhone()));
                }
                if (StringUtils.hasText(t.getHomeAddress())) {
                    t.setHomeAddress(SM4Untils.encryptContent(exchange_nginx_key, t.getHomeAddress()));
                }
            });
            iMemHistoryService.updateBatchById(memHistoryList, memHistoryList.size());
            log.info("ccp_mem_history表特殊字段加密完成:{}", memHistoryList.size());
        }

        //  ccp_mem_log name idcard phone home_address
        List<MemLog> memLogList = iMemLogService.list(new LambdaQueryWrapper<MemLog>().select(MemLog::getId, MemLog::getName, MemLog::getIdcard, MemLog::getPhone, MemLog::getHomeAddress));
        if (CollUtil.isNotEmpty(memLogList)) {
            memLogList.forEach(t -> {
                if (StringUtils.hasText(t.getName())) {
                    t.setName(SM4Untils.encryptContent(exchange_nginx_key, t.getName()));
                }
                if (StringUtils.hasText(t.getIdcard())) {
                    t.setIdcard(SM4Untils.encryptContent(exchange_nginx_key, t.getIdcard()));
                }
                if (StringUtils.hasText(t.getPhone())) {
                    t.setPhone(SM4Untils.encryptContent(exchange_nginx_key, t.getPhone()));
                }
                if (StringUtils.hasText(t.getHomeAddress())) {
                    t.setHomeAddress(SM4Untils.encryptContent(exchange_nginx_key, t.getHomeAddress()));
                }
            });
            iMemLogService.updateBatchById(memLogList, memLogList.size());
            log.info("ccp_mem_log表特殊字段加密完成:{}", memLogList.size());
        }

        //  ccp_mem_report name idcard phone d022_name
        List<MemReport> memReportList = iMemReportService.list(new LambdaQueryWrapper<MemReport>().select(MemReport::getId, MemReport::getName, MemReport::getIdcard, MemReport::getPhone, MemReport::getD022Code));
        List<MemReport> tmpMemReportList = new ArrayList<>();
        if (CollUtil.isNotEmpty(memReportList)) {
            memReportList.forEach(t -> {
                boolean a = StringUtils.hasText(t.getName());
                if (a) {
                    t.setName(SM4Untils.encryptContent(exchange_nginx_key, t.getName()));
                }
                boolean b = StringUtils.hasText(t.getIdcard());
                if (b) {
                    t.setIdcard(SM4Untils.encryptContent(exchange_nginx_key, t.getIdcard()));
                }
                boolean c = StringUtils.hasText(t.getPhone());
                if (c) {
                    t.setPhone(SM4Untils.encryptContent(exchange_nginx_key, t.getPhone()));
                }
                String d022Code = t.getD022Code();
                boolean d = StringUtils.hasText(d022Code);
                if (d) {
                    List<String> d22Name = Arrays.stream(d022Code.split(",")).filter(d22Map::containsKey).map(d22Map::get).collect(Collectors.toList());
                    t.setD022Name(SM4Untils.encryptContent(exchange_nginx_key, CollUtil.join(d22Name, ",")));
                }
                if (a || b || c || d) {
                    tmpMemReportList.add(t);
                }
            });
            if (CollUtil.isNotEmpty(tmpMemReportList)) {
                iMemReportService.updateBatchById(tmpMemReportList, tmpMemReportList.size());
                log.info("ccp_mem_report表特殊字段加密完成:{}", tmpMemReportList.size());
            }
        }
        //  ccp_mem_reward name idcard phone d022_name


        //  ccp_org contacter contact_phone secretary
        List<Org> orgList = orgService.list(new LambdaQueryWrapper<Org>().select(Org::getId, Org::getContacter, Org::getContactPhone, Org::getSecretary));
        List<Org> tmpOrgList = new ArrayList<>();
        orgList.forEach(t -> {
            boolean a = StringUtils.hasText(t.getContacter());
            if (a) {
                t.setContacter(SM4Untils.encryptContent(exchange_nginx_key, t.getContacter()));
            }
            boolean b = StringUtils.hasText(t.getContactPhone());
            if (b) {
                t.setContactPhone(SM4Untils.encryptContent(exchange_nginx_key, t.getContactPhone()));
            }
            boolean c = StringUtils.hasText(t.getSecretary());
            if (c) {
                t.setSecretary(SM4Untils.encryptContent(exchange_nginx_key, t.getSecretary()));
            }
            if (a || b || c) {
                tmpOrgList.add(t);
            }
        });
        if (CollUtil.isNotEmpty(tmpOrgList)) {
            orgService.updateBatchById(tmpOrgList, tmpOrgList.size());
            log.info("org表特殊字段加密完成:{}", tmpOrgList.size());
        }

        //  ccp_org_all contacter contact_phone secretary
        List<OrgAll> orgAllList = orgAllService.list(new LambdaQueryWrapper<OrgAll>().select(OrgAll::getId, OrgAll::getContacter, OrgAll::getContactPhone, OrgAll::getSecretary));
        List<OrgAll> tmpOrgAllList = new ArrayList<>();
        orgAllList.forEach(t -> {
            boolean a = StringUtils.hasText(t.getContacter());
            if (a) {
                t.setContacter(SM4Untils.encryptContent(exchange_nginx_key, t.getContacter()));
            }
            boolean b = StringUtils.hasText(t.getContactPhone());
            if (b) {
                t.setContactPhone(SM4Untils.encryptContent(exchange_nginx_key, t.getContactPhone()));
            }
            boolean c = StringUtils.hasText(t.getSecretary());
            if (c) {
                t.setSecretary(SM4Untils.encryptContent(exchange_nginx_key, t.getSecretary()));
            }
            if (a || b || c) {
                tmpOrgAllList.add(t);
            }
        });
        if (CollUtil.isNotEmpty(tmpOrgAllList)) {
            orgAllService.updateBatchById(tmpOrgAllList, tmpOrgAllList.size());
            log.info("orgAll表特殊字段加密完成:{}", tmpOrgAllList.size());
        }

        //  ccp_org_committee mem_name mem_idcard d022_name
        List<OrgCommittee> orgCommitteeList = orgCommitteeService.list(new LambdaQueryWrapper<OrgCommittee>().select(OrgCommittee::getId, OrgCommittee::getMemName, OrgCommittee::getMemIdcard, OrgCommittee::getD022Code));
        List<OrgCommittee> tmpOrgCommitteeList = new ArrayList<>();
        orgCommitteeList.forEach(t -> {
            boolean a = StringUtils.hasText(t.getMemName());
            if (a) {
                t.setMemName(SM4Untils.encryptContent(exchange_nginx_key, t.getMemName()));
            }
            boolean b = StringUtils.hasText(t.getMemIdcard());
            if (b) {
                t.setMemIdcard(SM4Untils.encryptContent(exchange_nginx_key, t.getMemIdcard()));
            }
            String d022Code = t.getD022Code();
            boolean c = StringUtils.hasText(d022Code);
            if (c) {
                // TODO: 2022/10/25 处理d022的值
                List<String> d22Name = Arrays.stream(d022Code.split(",")).filter(d22Map::containsKey).map(d22Map::get).collect(Collectors.toList());
                String join = String.join(",", d22Name);
                t.setD022Name(SM4Untils.encryptContent(exchange_nginx_key, join));
            }
            if (a || b || c) {
                tmpOrgCommitteeList.add(t);
            }
        });
        if (CollUtil.isNotEmpty(tmpOrgCommitteeList)) {
            orgCommitteeService.updateBatchById(tmpOrgCommitteeList, tmpOrgCommitteeList.size());
            log.info("orgCommittee表特殊字段加密完成:{}", tmpOrgCommitteeList.size());
        }

        //  ccp_org_history contacter contact_phone secretary
        List<OrgHistory> orgHistoryList = orgHistoryService.list(new LambdaQueryWrapper<OrgHistory>().select(OrgHistory::getId, OrgHistory::getContacter, OrgHistory::getContactPhone, OrgHistory::getSecretary));
        List<OrgHistory> tmpOrgHistoryList = new ArrayList<>();
        orgHistoryList.forEach(t -> {
            boolean a = StringUtils.hasText(t.getContacter());
            if (a) {
                t.setContacter(SM4Untils.encryptContent(exchange_nginx_key, t.getContacter()));
            }
            boolean b = StringUtils.hasText(t.getContactPhone());
            if (b) {
                t.setContactPhone(SM4Untils.encryptContent(exchange_nginx_key, t.getContactPhone()));
            }
            boolean c = StringUtils.hasText(t.getSecretary());
            if (c) {
                t.setSecretary(SM4Untils.encryptContent(exchange_nginx_key, t.getSecretary()));
            }
            if (a || b || c) {
                tmpOrgHistoryList.add(t);
            }
        });
        if (CollUtil.isNotEmpty(tmpOrgHistoryList)) {
            orgHistoryService.updateBatchById(tmpOrgHistoryList, tmpOrgHistoryList.size());
            log.info("orgHistory表特殊字段加密完成:{}", tmpOrgHistoryList.size());
        }

        //  ccp_org_party_congress_committee mem_name mem_idcard
        List<OrgPartyCongressCommittee> orgPartyCongressCommitteeList = orgPartyCongressCommitteeService.list(new LambdaQueryWrapper<OrgPartyCongressCommittee>().select(OrgPartyCongressCommittee::getId, OrgPartyCongressCommittee::getMemName, OrgPartyCongressCommittee::getMemIdcard));
        List<OrgPartyCongressCommittee> tmpOrgPartyCongressCommitteeList = new ArrayList<>();
        orgPartyCongressCommitteeList.forEach(t -> {
            boolean a = StringUtils.hasText(t.getMemName());
            if (a) {
                t.setMemName(SM4Untils.encryptContent(exchange_nginx_key, t.getMemName()));
            }
            boolean b = StringUtils.hasText(t.getMemIdcard());
            if (b) {
                t.setMemIdcard(SM4Untils.encryptContent(exchange_nginx_key, t.getMemIdcard()));
            }
            if (a || b) {
                tmpOrgPartyCongressCommitteeList.add(t);
            }
        });
        if (CollUtil.isNotEmpty(tmpOrgPartyCongressCommitteeList)) {
            orgPartyCongressCommitteeService.updateBatchById(tmpOrgPartyCongressCommitteeList, tmpOrgPartyCongressCommitteeList.size());
            log.info("orgPartyCongressCommittee表特殊字段加密完成:{}", tmpOrgPartyCongressCommitteeList.size());
        }

        //  ccp_org_party_congress_committee_all mem_name mem_idcard
        List<OrgPartyCongressCommitteeAll> orgPartyCongressCommitteeAllList = orgPartyCongressCommitteeAllService.list(new LambdaQueryWrapper<OrgPartyCongressCommitteeAll>().select(OrgPartyCongressCommitteeAll::getId, OrgPartyCongressCommitteeAll::getMemName, OrgPartyCongressCommitteeAll::getMemIdcard));
        List<OrgPartyCongressCommitteeAll> tmpOrgPartyCongressCommitteeAllList = new ArrayList<>();
        orgPartyCongressCommitteeAllList.forEach(t -> {
            boolean a = StringUtils.hasText(t.getMemName());
            if (a) {
                t.setMemName(SM4Untils.encryptContent(exchange_nginx_key, t.getMemName()));
            }
            boolean b = StringUtils.hasText(t.getMemIdcard());
            if (b) {
                t.setMemIdcard(SM4Untils.encryptContent(exchange_nginx_key, t.getMemIdcard()));
            }
            if (a || b) {
                tmpOrgPartyCongressCommitteeAllList.add(t);
            }
        });
        if (CollUtil.isNotEmpty(tmpOrgPartyCongressCommitteeAllList)) {
            orgPartyCongressCommitteeAllService.updateBatchById(tmpOrgPartyCongressCommitteeAllList, tmpOrgPartyCongressCommitteeAllList.size());
            log.info("orgPartyCongressCommitteeAll表特殊字段加密完成:{}", tmpOrgPartyCongressCommitteeAllList.size());
        }

        //  ccp_org_reviewers mem_name
        List<OrgReviewers> orgReviewersList = orgReviewersService.list(new LambdaQueryWrapper<OrgReviewers>().select(OrgReviewers::getCode, OrgReviewers::getMemName));
        List<OrgReviewers> tmpOrgReviewersList = new ArrayList<>();
        orgReviewersList.forEach(t -> {
            boolean a = StringUtils.hasText(t.getMemName());
            if (a) {
                t.setMemName(SM4Untils.encryptContent(exchange_nginx_key, t.getMemName()));
                tmpOrgReviewersList.add(t);
            }
        });
        if (CollUtil.isNotEmpty(tmpOrgReviewersList)) {
            orgReviewersService.updateBatchById(tmpOrgReviewersList, tmpOrgReviewersList.size());
            log.info("orgReviewers表特殊字段加密完成:{}", tmpOrgReviewersList.size());
        }

        //  ccp_transfer_record mem_name
//        List<TransferRecord> transferRecordList = transferRecordService.list(new LambdaQueryWrapper<TransferRecord>().select(TransferRecord::getId, TransferRecord::getName));
//        List<TransferRecord> tmpTransferRecordList =new ArrayList<>();
//        transferRecordList.forEach(t -> {
//            if (StringUtils.hasText(t.getName())) {
//                t.setName(SM4Untils.encryptContent(exchange_nginx_key, t.getName()));
//                tmpTransferRecordList.add(t);
//            }
//        });
//        if (CollUtil.isNotEmpty(tmpTransferRecordList)) {
//            transferRecordService.updateBatchById(tmpTransferRecordList, tmpTransferRecordList.size());
//            log.info("transferRecord表特殊字段加密完成:{}", tmpTransferRecordList.size());
//        }

        //  ccp_unit_community first_secretary_name
        List<UnitCommunity> unitCommunityList = unitCommunityService.list(new LambdaQueryWrapper<UnitCommunity>().select(UnitCommunity::getId, UnitCommunity::getFirstSecretaryName));
        List<UnitCommunity> tmpUnitCommunityList = new ArrayList<>();
        unitCommunityList.forEach(t -> {
            if (StringUtils.hasText(t.getFirstSecretaryName())) {
                t.setFirstSecretaryName(SM4Untils.encryptContent(exchange_nginx_key, t.getFirstSecretaryName()));
                tmpUnitCommunityList.add(t);
            }
        });
        if (CollUtil.isNotEmpty(tmpUnitCommunityList)) {
            unitCommunityService.updateBatchById(tmpUnitCommunityList, tmpUnitCommunityList.size());
            log.info("unitCommunity表特殊字段加密完成:{}", tmpUnitCommunityList.size());
        }

        //  ccp_unit_committee mem_name
        List<UnitCommittee> unitCommitteeList = unitCommitteeService.list(new LambdaQueryWrapper<UnitCommittee>().select(UnitCommittee::getId, UnitCommittee::getMemName, UnitCommittee::getMemIdcard));
        List<UnitCommittee> tmpUnitCommitteeList = new ArrayList<>();
        unitCommitteeList.forEach(t -> {
            boolean a = StringUtils.hasText(t.getMemName());
            if (a) {
                t.setMemName(SM4Untils.encryptContent(exchange_nginx_key, t.getMemName()));
            }
            boolean b = StringUtils.hasText(t.getMemIdcard());
            if (b) {
                t.setMemIdcard(SM4Untils.encryptContent(exchange_nginx_key, t.getMemIdcard()));
            }
            if (a || b) {
                tmpUnitCommitteeList.add(t);
            }
        });
        if (CollUtil.isNotEmpty(tmpUnitCommitteeList)) {
            unitCommitteeService.updateBatchById(tmpUnitCommitteeList, tmpUnitCommitteeList.size());
            log.info("unitCommittee表特殊字段加密完成:{}", tmpUnitCommitteeList.size());
        }

        //  ccp_unit_countryside mem_name mem_idcard phone
        List<UnitCountryside> unitCountrysideList = unitCountrusideService.list(new LambdaQueryWrapper<UnitCountryside>().select(UnitCountryside::getId, UnitCountryside::getMemName, UnitCountryside::getMemIdcard, UnitCountryside::getPhone));
        List<UnitCountryside> tmpUnitCountrysideList = new ArrayList<>();
        unitCountrysideList.forEach(t -> {
            boolean a = StringUtils.hasText(t.getMemName());
            if (a) {
                t.setMemName(SM4Untils.encryptContent(exchange_nginx_key, t.getMemName()));
            }
            boolean b = StringUtils.hasText(t.getMemIdcard());
            if (b) {
                t.setMemIdcard(SM4Untils.encryptContent(exchange_nginx_key, t.getMemIdcard()));
            }
            boolean c = StringUtils.hasText(t.getPhone());
            if (c) {
                t.setPhone(SM4Untils.encryptContent(exchange_nginx_key, t.getPhone()));
            }
            if (a || b || c) {
                tmpUnitCountrysideList.add(t);
            }
        });
        if (CollUtil.isNotEmpty(tmpUnitCountrysideList)) {
            unitCountrusideService.updateBatchById(tmpUnitCountrysideList, tmpUnitCountrysideList.size());
            log.info("unitCountryside表特殊字段加密完成:{}", tmpUnitCountrysideList.size());
        }

        //  ccp_unit_resident mem_name mem_idcard
        List<UnitResident> unitResidentList = unitResidentService.list(new LambdaQueryWrapper<UnitResident>().select(UnitResident::getId, UnitResident::getMemName, UnitResident::getMemIdcard));
        List<UnitResident> tmpUnitResidentList = new ArrayList<>();
        unitResidentList.forEach(t -> {
            boolean b = StringUtils.hasText(t.getMemName());
            if (b) {
                t.setMemName(SM4Untils.encryptContent(exchange_nginx_key, t.getMemName()));
            }
            boolean a = StringUtils.hasText(t.getMemIdcard());
            if (a) {
                t.setMemIdcard(SM4Untils.encryptContent(exchange_nginx_key, t.getMemIdcard()));
            }
            if (a || b) {
                tmpUnitResidentList.add(t);
            }
        });
        if (CollUtil.isNotEmpty(tmpUnitResidentList)) {
            unitResidentService.updateBatchById(tmpUnitResidentList, tmpUnitResidentList.size());
            log.info("unitResident表特殊字段加密完成:{}", tmpUnitResidentList.size());
        }
        log.info("特殊字段加密任务已完成");
    }
}
