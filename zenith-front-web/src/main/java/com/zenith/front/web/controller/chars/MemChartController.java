package com.zenith.front.web.controller.chars;


import com.zenith.front.api.mem.IMemAllService;
import com.zenith.front.common.annotation.RequiresPermissions;
import com.zenith.front.model.dto.ChartDataDTO;
import com.zenith.front.model.message.InMessage;
import com.zenith.front.model.message.OutMessage;
import com.zenith.front.model.validate.annotation.Validate;
import com.zenith.front.model.validate.group.Common1Group;
import com.zenith.front.web.controller.BaseController;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @version 1.0
 * @Description: 党员概况
 * @date 2019/4/28 10:57
 */
@RestController
@RequestMapping("/chart/mem")
public class MemChartController extends BaseController {

    @Resource
    private IMemAllService memChartService;

    /**
     * 获取人员总数
     *
     * @param inMessage
     * @return
     */
    @Validate(group = Common1Group.class)
    @RequiresPermissions
    @PostMapping("/getMemTotal")
    public OutMessage getMemTotal(@RequestBody InMessage<ChartDataDTO> inMessage) {
        return memChartService.getMemTotal(inMessage.getData());
    }

    /**
     * 获取民族比例
     *
     * @param inMessage
     * @return
     */
    @Validate(group = Common1Group.class)
    @RequiresPermissions
    @PostMapping("/getNationRatioTotal")
    public OutMessage getNationRatioTotal(@RequestBody InMessage<ChartDataDTO> inMessage) {
        return memChartService.getNationRatioTotal(inMessage.getData());
    }

    /**
     * 获取大专及以上的党员数
     *
     * @param inMessage
     * @return
     */
    @Validate(group = Common1Group.class)
    @RequiresPermissions
    @PostMapping("/getDzTotal")
    public OutMessage getDzTotal(@RequestBody InMessage<ChartDataDTO> inMessage) {
        return memChartService.getDzTotal(inMessage.getData());
    }

    /**
     * 获取男女比例
     *
     * @param inMessage
     * @return
     */
    @Validate(group = Common1Group.class)
    @RequiresPermissions
    @PostMapping("/getSexRatioTotal")
    public OutMessage getSexRatioTotal(@RequestBody InMessage<ChartDataDTO> inMessage) {
        return memChartService.getSexRatioTotal(inMessage.getData());
    }

    /**
     * 获取学历比例
     *
     * @param inMessage
     * @return
     */
    @Validate(group = Common1Group.class)
    @RequiresPermissions
    @PostMapping("/getEducationRatioTotal")
    public OutMessage getEducationRatioTotal(@RequestBody InMessage<ChartDataDTO> inMessage) {
        return memChartService.getEducationRatioTotal(inMessage.getData());
    }

    /**
     * 获取工作岗位比例
     *
     * @param inMessage
     * @return
     */
    @Validate(group = Common1Group.class)
    @RequiresPermissions
    @PostMapping("/getJobRatioTotal")
    public OutMessage getJobRatioTotal(@RequestBody InMessage<ChartDataDTO> inMessage) {
        return memChartService.getJobRatioTotal(inMessage.getData());
    }

    /**
     * 获取公有工作岗位比例
     *
     * @param inMessage
     * @return
     */
    @Validate(group = Common1Group.class)
    @RequiresPermissions
    @PostMapping("/getGyJobRatioTotal")
    public OutMessage getGyJobRatioTotal(@RequestBody InMessage<ChartDataDTO> inMessage) {
        return memChartService.getGyJobRatioTotal(inMessage.getData());
    }

    /**
     * 获取非公有工作岗位比例
     *
     * @param inMessage
     * @return
     */
    @Validate(group = Common1Group.class)
    @RequiresPermissions
    @PostMapping("/getFgyJobRatioTotal")
    public OutMessage getFgyJobRatioTotal(@RequestBody InMessage<ChartDataDTO> inMessage) {
        return memChartService.getFgyJobRatioTotal(inMessage.getData());
    }

    /**
     * 社会组织所有分类
     *
     * @param inMessage
     * @return
     */
    @Validate(group = Common1Group.class)
    @RequiresPermissions
    @PostMapping("/getShzzJobRatioTotal")
    public OutMessage getShzzJobRatioTotal(@RequestBody InMessage<ChartDataDTO> inMessage) {
        return memChartService.getShzzJobRatioTotal(inMessage.getData());
    }

    /**
     * 获取农牧渔民所有分类
     *
     * @param inMessage
     * @return
     */
    @Validate(group = Common1Group.class)
    @RequiresPermissions
    @PostMapping("/getNmymJobRatioTotal1")
    public OutMessage getNmymJobRatioTotal1(@RequestBody InMessage<ChartDataDTO> inMessage) {
        return memChartService.getNmymJobRatioTotal1(inMessage.getData());
    }

    /**
     * 获取困难党员数据
     *
     * @param inMessage
     * @return
     */
    @Validate(group = Common1Group.class)
    @RequiresPermissions
    @PostMapping("/getMemDifficultTotal")
    public OutMessage getMemDifficultTotal(@RequestBody InMessage<ChartDataDTO> inMessage) {
        return memChartService.getMemDifficultTotal(inMessage.getData());
    }

    /**
     * 获取多重党员数据
     *
     * @param inMessage
     * @return
     */
    @Validate(group = Common1Group.class)
    @RequiresPermissions
    @PostMapping("/getManyMemTotal")
    public OutMessage getManyMemTotal(@RequestBody InMessage<ChartDataDTO> inMessage) {
        return memChartService.getManyMemTotal(inMessage.getData());
    }

    /**
     * 获取历史党员数据
     *
     * @param inMessage
     * @return
     */
    @Validate(group = Common1Group.class)
    @RequiresPermissions
    @PostMapping("/getMemHistoryTotal")
    public OutMessage getMemHistoryTotal(@RequestBody InMessage<ChartDataDTO> inMessage) {
        return memChartService.getMemHistoryTotal(inMessage.getData());
    }

    /**
     * 获取年龄分布图
     *
     * @param inMessage
     * @return
     */
    @Validate(group = Common1Group.class)
    @RequiresPermissions
    @PostMapping("/getAgeRatioTotal")
    public OutMessage getAgeRatioTotal(@RequestBody InMessage<ChartDataDTO> inMessage) {
        return memChartService.getAgeRatioTotal(inMessage.getData());
    }

    /**
     * 获取党龄分布图
     *
     * @param inMessage
     * @return
     */
    @Validate(group = Common1Group.class)
    @RequiresPermissions
    @PostMapping("/getPartyAgeRatioTotal")
    public OutMessage getPartyAgeRatioTotal(@RequestBody InMessage<ChartDataDTO> inMessage) {
        return memChartService.getPartyAgeRatioTotal(inMessage.getData());
    }

    /**
     * 获取党龄时间段分布图
     *
     * @param inMessage
     * @return
     */
    @Validate(group = Common1Group.class)
    @RequiresPermissions
    @PostMapping("/getPartyAgeParagraphRatioTotal")
    public OutMessage getPartyAgeParagraphRatioTotal(@RequestBody InMessage<ChartDataDTO> inMessage) {
        return memChartService.getPartyAgeParagraphRatioTotal(inMessage.getData());
    }

    /**
     * 获取党员信息完整度平均值
     *
     * @return
     */
    @Validate(group = Common1Group.class)
    @RequiresPermissions
    @PostMapping("/getMemRatio")
    public OutMessage getMemRatio(@RequestBody InMessage<ChartDataDTO> inMessage) {
        return memChartService.getMemRatio(inMessage.getData());
    }

    /**
     * 人员类型分布图
     *
     * @return
     */
    @Validate(group = Common1Group.class)
    @RequiresPermissions
    @PostMapping("/getMemLayout")
    public OutMessage getMemLayout(@RequestBody InMessage<ChartDataDTO> inMessage) {
        return memChartService.getMemLayout(inMessage.getData());
    }

}
