//package com.zenith.front.web.config;
//
//import cn.hutool.core.collection.CollUtil;
//import com.baomidou.mybatisplus.core.toolkit.Wrappers;
//import com.zenith.front.api.org.IOrgAllService;
//import com.zenith.front.api.unit.IUnitAllService;
//import com.zenith.front.api.unit.IUnitResidentService;
//import com.zenith.front.core.datasource.DynamicDatasourceUtil;
//import com.zenith.front.model.OrgAll;
//import com.zenith.front.model.UnitAll;
//import org.springframework.boot.CommandLineRunner;
//
//import javax.annotation.Resource;
//import java.util.Comparator;
//import java.util.List;
//import java.util.Map;
//import java.util.concurrent.atomic.AtomicReference;
//import java.util.stream.Collectors;
//
///**
// * 驻村系统组织树，单位初始化
// *
// * <AUTHOR>
// * @version V1.0
// * @date 2023/3/1 9:01
// */
////@Component
//public class VcDataInitRunner implements CommandLineRunner {
//
//    @Resource
//    private IOrgAllService orgAllService;
//    @Resource
//    private OrgInfoService orgInfoService;
//    @Resource
//    private OrgInfoConverter orgInfoConverter;
//    @Resource
//    private IUnitAllService unitAllService;
//    @Resource
//    private IUnitResidentService unitResidentService;
//    @Resource
//    private A01Service a01Service;
//
//    @Override
//    public void run(String... args) throws Exception {
//        AtomicReference<String> stringAtomicReference = new AtomicReference<>();
//        //查询驻村系统组织树
//        List<OrgAll> orgAllList = orgAllService.getVillageCommunityOrgList();
//        if (CollUtil.isNotEmpty(orgAllList)) {
//            List<String> orgCodeList = orgAllList.stream().map(OrgAll::getOrgCode).collect(Collectors.toList());
//            //按字符串长度进行排序
//            List<String> orgCodeSortList = orgCodeList.stream().sorted(Comparator.comparingInt(String::length)).collect(Collectors.toList());
//            String orgCode = orgCodeSortList.get(0);
//            stringAtomicReference.set(orgCode);
//            //删除当前库下面的组织数据
//            DynamicDatasourceUtil.change(DynamicDatasourceUtil.STATION_DATASOURCE, () -> orgInfoService.deleteBatchByLevelCode(orgCode));
//            List<OrgInfo> orgInfos = orgInfoConverter.orgAllListToEntityList(orgAllList);
//            //同步到驻村系统总库
//            DynamicDatasourceUtil.change(DynamicDatasourceUtil.STATION_DATASOURCE, () -> orgInfoService.saveBatch(orgInfos));
//        }
//        //删除当前库下面的单位数据
//        DynamicDatasourceUtil.change(DynamicDatasourceUtil.STATION_DATASOURCE, () -> unitAllService.remove(Wrappers.emptyWrapper()));
//        //初始化驻村系统单位 91,92开头
//        List<UnitAll> unitAllList = unitAllService.findVcUnitList();
//        if (CollUtil.isNotEmpty(unitAllList)) {
//            DynamicDatasourceUtil.change(DynamicDatasourceUtil.STATION_DATASOURCE, () -> unitAllService.saveBatch(unitAllList));
//        }
//        //初始化驻村干部数据
//        this.initVcUnitResident(stringAtomicReference.get());
//    }
//
//    /**
//     * 初始化驻村干部数据
//     */
//    private void initVcUnitResident(String orgCode) {
//        //先删除
//        DynamicDatasourceUtil.change(DynamicDatasourceUtil.STATION_DATASOURCE, () -> a01Service.deleteBatchByLevelCode(orgCode));
//        List<Map<String, Object>> residentList = unitResidentService.findVillageCadresList();
//        if (CollUtil.isNotEmpty(residentList)) {
//            List<A01> a01List = residentList.stream().map(item -> JackSonUtil.mapToBean(item, A01.class)).collect(Collectors.toList());
//            DynamicDatasourceUtil.change(DynamicDatasourceUtil.STATION_DATASOURCE, () -> a01Service.saveBatch(a01List, a01List.size()));
//        }
//    }
//
//}
