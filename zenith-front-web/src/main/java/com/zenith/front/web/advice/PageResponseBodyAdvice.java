package com.zenith.front.web.advice;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.zenith.front.model.message.OutMessage;
import org.springframework.core.MethodParameter;
import org.springframework.core.annotation.AnnotatedElementUtils;
import org.springframework.core.annotation.Order;
import org.springframework.http.MediaType;
import org.springframework.http.converter.HttpMessageConverter;
import org.springframework.http.server.ServerHttpRequest;
import org.springframework.http.server.ServerHttpResponse;
import org.springframework.lang.NonNull;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.bind.annotation.RestControllerAdvice;
import org.springframework.web.servlet.mvc.method.annotation.ResponseBodyAdvice;

import java.util.List;

/**
 * <AUTHOR>
 */
@RestControllerAdvice
@Order(6)
public class PageResponseBodyAdvice implements ResponseBodyAdvice<Object> {

    @Override
    public boolean supports(MethodParameter returnType, @NonNull Class<? extends HttpMessageConverter<?>> aClass) {
        return AnnotatedElementUtils.hasAnnotation(returnType.getContainingClass(), RestController.class) || returnType.hasMethodAnnotation(ResponseBody.class);
    }

    @Override
    public Object beforeBodyWrite(Object obj, @NonNull MethodParameter methodParameter, @NonNull MediaType mediaType, @NonNull Class<? extends HttpMessageConverter<?>> aClass, @NonNull ServerHttpRequest serverHttpRequest, @NonNull ServerHttpResponse serverHttpResponse) {
        if (obj instanceof OutMessage) {
            OutMessage outMessage = (OutMessage) obj;
            Object data = outMessage.getData();
            if (data instanceof Page) {
                Page<?> mybatisPage = (Page<?>) data;
                List<?> records = mybatisPage.getRecords();
                long current = mybatisPage.getCurrent();
                long size = mybatisPage.getSize();
                long pages = mybatisPage.getPages();
                long total = mybatisPage.getTotal();

                com.zenith.front.model.custom.Page page = new com.zenith.front.model.custom.Page();
                page.setList(records);
                page.setPageNumber(Math.toIntExact(current));
                page.setPageSize(Math.toIntExact(size));
                page.setTotalPage(Math.toIntExact(pages));
                page.setTotalRow(Math.toIntExact(total));

                outMessage.setData(page);
            }
            return outMessage;
        }
        return obj;
    }
}
