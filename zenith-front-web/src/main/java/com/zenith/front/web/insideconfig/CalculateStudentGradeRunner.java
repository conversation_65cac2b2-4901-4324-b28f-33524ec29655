package com.zenith.front.web.insideconfig;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.StrUtil;
import com.zenith.front.api.develop.IDevelopStepLogService;
import com.zenith.front.api.mem.IMemDevelopService;
import com.zenith.front.api.mem.IMemService;
import com.zenith.front.api.report.IStatisticsYearService;
import com.zenith.front.common.kit.DateUtil;
import com.zenith.front.core.cache.CacheUtils;
import com.zenith.front.core.kit.CollectionUtil;
import com.zenith.front.model.custom.Record;
import com.zenith.front.dao.mapper.develop.DevelopStepLogAllMapper;
import com.zenith.front.dao.mapper.mem.MemAllMapper;
import com.zenith.front.dao.mapper.mem.MemDevelopAllMapper;
import com.zenith.front.model.bean.DevelopStepLog;
import com.zenith.front.model.bean.Mem;
import com.zenith.front.model.bean.MemDevelop;
import com.zenith.front.model.bean.StatisticsYear;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.ApplicationArguments;
import org.springframework.boot.ApplicationRunner;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.time.LocalDate;
import java.util.*;

/**
 * 每年10月30跑一次
 * <p>
 * 计算学生年级,更改其工作岗位
 * <p>
 * 对于党员和入党申请人：
 * 统计时间（20221231）-入学时间<1年：一年级学生
 * 学制-1>统计时间-入学时间>1年：其他年级学生
 * 学制>统计时间-入学时间>学制-1：毕业年级学生
 * 统计时间-入学时间>学制：学生毕业未就业人员。
 * <p>
 * 对于本年发展党员：
 * 入党时间-入学时间<1年：一年级学生
 * 学制-1>入党时间-入学时间>1年：其他年级学生
 * 学制>入党时间-入学时间>学制-1：毕业年级学生
 * 入党时间-入学时间>学制：学生毕业未就业人员。
 *
 * <AUTHOR>
 */
//@Component
@Slf4j
public class CalculateStudentGradeRunner implements ApplicationRunner {

    @Resource
    private IMemService memService;
    @Resource
    private MemAllMapper memAllMapper;
    @Resource
    private IMemDevelopService memDevelopService;
    @Resource
    private MemDevelopAllMapper memDevelopAllMapper;
    @Resource
    private IDevelopStepLogService developStepLogService;
    @Resource
    private DevelopStepLogAllMapper developStepLogAllMapper;
    @Resource
    private IStatisticsYearService iStatisticsYearService;


    private static final List<String> D09_CODE_LIST = Arrays.asList("311", "312", "313", "321", "322", "323", "331", "332", "333");

    /**
     * 党员、本年度发展党员、发展党员 错误学制数据处理纠正
     */
    private static final Map<String, String> EDUCATIONAL_SYSTEM_ERROR_MAP = new HashMap<String, String>(){{
        put("两年", "2");
        put("5年制", "5");
        put("三年", "3");
        put("4年", "4");
        put("4年全日制", "4");
        put("四年制", "4");
        put("本科四年", "4");
        put("五年", "5");
        put("1年", "1");
        put("两年制", "2");
        put("三年制", "3");
        put("四年", "4");
        put("3年制", "3");
        put("三", "3");
        put("3年", "3");
        put("2年", "2");
        put("4年制", "4");
        put("5年", "5");
        put("二年制", "2");
        put("五年制", "5");
        put("四", "4");
    }};

    @Override
    public void run(ApplicationArguments args) {
        //工作岗位
        String dicName = "dict_d09";
        List<Record> records = CacheUtils.getDic(dicName);
        if (CollUtil.isEmpty(records)) {
            log.warn("Dictionary table :{} is not loaded,please check", dicName);
            return;
        }
        Map<String, String> d09Map = CollectionUtil.listRecordToMap(records, "key", "name");
        List<Mem> memList = memService.findCalculationGradeStudentByD09Code(D09_CODE_LIST);
        //统计时间（党员和入党申请人）
        StatisticsYear statisticsYear = iStatisticsYearService.getStatisticsYear("1");
        LocalDate statisticalLocalDate = DateUtil.dateToLocaleDate(statisticsYear.getEndDate());
        List<Mem> updateMemList = this.calculateMemGrade(memList, statisticalLocalDate, d09Map);
        if (CollUtil.isNotEmpty(updateMemList)) {
            boolean flag = memService.updateBatchById(updateMemList);
            //同步更新All表数据
            if (flag) {
                memAllMapper.updateD09Code();
            }
            log.info("Update the number of student Party members' posts:{}", updateMemList.size());
        }
        List<MemDevelop> memDevelopList = memDevelopService.findCalculationGradeStudentByD09Code(D09_CODE_LIST);
        List<MemDevelop> updateMemDevelopList = this.calculateMemDevelopGrade(memDevelopList, statisticalLocalDate, d09Map);
        if (CollUtil.isNotEmpty(updateMemDevelopList)) {
            boolean flag = memDevelopService.updateBatchById(updateMemDevelopList);
            if (flag) {
                memDevelopAllMapper.updateD09Code();
            }
            log.info("Update the number of posts of student applicants for Party membership:{}", updateMemDevelopList.size());
        }

        // todo 2025-01-12  本年度发展党员不需要计算学制 -- 王察
        // todo 2025-01-14 本年度发展党员需要计算学制 -- 夏云松
        List<DevelopStepLog> developStepLogList = developStepLogService.findCalculationGradeStudentByD09Code(D09_CODE_LIST, iStatisticsYearService.annualStatisticsPeriod());
        List<DevelopStepLog> updateDevelopStepLogList = this.calculateDevelopStepLogGrade(developStepLogList, d09Map);
        if (CollUtil.isNotEmpty(updateDevelopStepLogList)) {
            boolean flag = developStepLogService.updateBatchById(updateDevelopStepLogList);
            if (flag) {
                developStepLogAllMapper.updateD09Code();
            }
            log.info("Update the number of posts for students to recruit Party members this year:{}", updateDevelopStepLogList.size());
        }
    }

    /**
     * 根据统计时间，入学时间及学制计算党员的工作岗位
     *
     * @param list                 党员集合
     * @param statisticalLocalDate 统计时间
     * @param d09Map               字典表d09
     * @return 被更新的党员集合
     */
    private List<Mem> calculateMemGrade(List<Mem> list, LocalDate statisticalLocalDate, Map<String, String> d09Map) {
        if (CollUtil.isEmpty(list)) {
            return Collections.emptyList();
        }
        List<Mem> updateList = new ArrayList<>();
        for (Mem t : list) {
            Date enterSchoolDate = t.getEnterSchoolDate();
            if (Objects.isNull(enterSchoolDate)) {
                continue;
            }
            // todo 2024-01-07 处理党员中文学制
            if(StrUtil.isNotEmpty(t.getEducationalSystem()) &&
                    EDUCATIONAL_SYSTEM_ERROR_MAP.containsKey(t.getEducationalSystem().replaceAll(" ", ""))){
                t.setEducationalSystem(EDUCATIONAL_SYSTEM_ERROR_MAP.get(t.getEducationalSystem().replaceAll(" ", "")));
            }

            final String d09Code = t.getD09Code();
            final String d07Code = t.getD07Code();
            LocalDate enterSchoolLocalDate = DateUtil.dateToLocaleDate(enterSchoolDate);
            String currentD09Code = calculateD09Code(d09Code, enterSchoolLocalDate, statisticalLocalDate, graduationLocalDate(enterSchoolLocalDate, t.getEducationalSystem()), d07Code);
            if (!StrUtil.equals(d09Code, currentD09Code)) {
                Mem mem = new Mem();
                mem.setId(t.getId());
                mem.setD09Code(currentD09Code);
                mem.setD09Name(d09Map.get(currentD09Code));
                mem.setEducationalSystem(t.getEducationalSystem());
                updateList.add(mem);
            }
        }
        return updateList;
    }

    /**
     * 根据统计时间，入学时间及学制计算入党申请人的工作岗位
     *
     * @param list                 党员集合
     * @param statisticalLocalDate 统计时间
     * @param d09Map               字典表d09
     * @return 被更新的入党申请人集合
     */
    private List<MemDevelop> calculateMemDevelopGrade(List<MemDevelop> list, LocalDate statisticalLocalDate, Map<String, String> d09Map) {
        if (CollUtil.isEmpty(list)) {
            return Collections.emptyList();
        }
        List<MemDevelop> updateList = new ArrayList<>();
        for (MemDevelop t : list) {
            Date enterSchoolDate = t.getEnterSchoolDate();
            if (Objects.isNull(enterSchoolDate)) {
                continue;
            }
            // todo 2024-01-07 处理党员中文学制
            if(StrUtil.isNotEmpty(t.getEducationalSystem()) &&
                    EDUCATIONAL_SYSTEM_ERROR_MAP.containsKey(t.getEducationalSystem().replaceAll(" ", ""))){
                t.setEducationalSystem(EDUCATIONAL_SYSTEM_ERROR_MAP.get(t.getEducationalSystem().replaceAll(" ", "")));
            }

            final String d09Code = t.getD09Code();
            LocalDate enterSchoolLocalDate = DateUtil.dateToLocaleDate(enterSchoolDate);
            String currentD09Code = calculateD09Code(d09Code, enterSchoolLocalDate, statisticalLocalDate, graduationLocalDate(enterSchoolLocalDate, t.getEducationalSystem()));
            if (!StrUtil.equals(d09Code, currentD09Code)) {
                MemDevelop memDevelop = new MemDevelop();
                memDevelop.setId(t.getId());
                memDevelop.setD09Code(currentD09Code);
                memDevelop.setD09Name(d09Map.get(currentD09Code));
                memDevelop.setEducationalSystem(t.getEducationalSystem());
                updateList.add(memDevelop);
            }
        }
        return updateList;
    }

    /**
     * 根据统计时间，入学时间及学制计算本年度发展党员的工作岗位
     *
     * @param list   党员集合
     * @param d09Map 字典表d09
     * @return 被更新的本年度发展党员集合
     */
    private List<DevelopStepLog> calculateDevelopStepLogGrade(List<DevelopStepLog> list, Map<String, String> d09Map) {
        if (CollUtil.isEmpty(list)) {
            return Collections.emptyList();
        }
        List<DevelopStepLog> updateList = new ArrayList<>();
        for (DevelopStepLog t : list) {
            Date joinOrgDate = t.getTopreJoinOrgDate();
            if (Objects.isNull(joinOrgDate)) {
                continue;
            }
            Date enterSchoolDate = t.getEnterSchoolDate();
            if (Objects.isNull(enterSchoolDate)) {
                continue;
            }
            // todo 2024-01-07 处理党员中文学制
            if(StrUtil.isNotEmpty(t.getEducationalSystem()) &&
                    EDUCATIONAL_SYSTEM_ERROR_MAP.containsKey(t.getEducationalSystem().replaceAll(" ", ""))){
                t.setEducationalSystem(EDUCATIONAL_SYSTEM_ERROR_MAP.get(t.getEducationalSystem().replaceAll(" ", "")));
            }

            final String d09Code = t.getD09Code();
            final String d07Code = t.getD07Code();
            LocalDate enterSchoolLocalDate = DateUtil.dateToLocaleDate(enterSchoolDate);
            String currentD09Code = calculateD09Code(d09Code, enterSchoolLocalDate, DateUtil.dateToLocaleDate(joinOrgDate), graduationLocalDate(enterSchoolLocalDate, t.getEducationalSystem()), d07Code);
            if (!StrUtil.equals(d09Code, currentD09Code)) {
                DevelopStepLog developStepLog = new DevelopStepLog();
                developStepLog.setId(t.getId());
                developStepLog.setD09Code(currentD09Code);
                developStepLog.setD09Name(d09Map.get(currentD09Code));
                developStepLog.setEducationalSystem(t.getEducationalSystem());
                updateList.add(developStepLog);
            }
        }
        return updateList;
    }

    /**
     * 计算学生毕业时间
     *
     * @param enterSchoolLocalDate 入学时间
     * @param educationalSystem    学制
     * @return 毕业时间
     */
    public static LocalDate graduationLocalDate(LocalDate enterSchoolLocalDate, String educationalSystem) {
        if (Objects.isNull(enterSchoolLocalDate) || StrUtil.isBlank(educationalSystem)) {
            return null;
        }
        if (NumberUtil.isInteger(educationalSystem)) {
            int educationalSystemInt = Integer.parseInt(educationalSystem);
            if (educationalSystemInt <= 0) {
                return null;
            }
            return enterSchoolLocalDate.plusYears(educationalSystemInt);
        } else if (NumberUtil.isDouble(educationalSystem)) {
            double educationalSystemDouble = Double.parseDouble(educationalSystem);
            int educationalSystemInt = (int) educationalSystemDouble;
            if (educationalSystemInt <= 0) {
                return null;
            }
            //针对处理2.5年制学籍
            String decimalStr = educationalSystem.substring(educationalSystem.indexOf("."));
            if (StrUtil.equals(decimalStr, ".5")) {
                return enterSchoolLocalDate.plusYears(educationalSystemInt).plusMonths(6);
            }
        }
        return null;
    }

    /**
     * 根据时间差计算当前统计年份学生的工作岗位
     * @param d09Code   工作岗位
     * @param enterSchoolLocalDate  入学时间
     * @param currentLocalDate  统计时间或入党时间
     * @param graduationLocalDate   毕业时间
     * @return  计算后的工作岗位
     */
    private String calculateD09Code(String d09Code, LocalDate enterSchoolLocalDate, LocalDate currentLocalDate, LocalDate graduationLocalDate) {
        return calculateD09Code(d09Code, enterSchoolLocalDate, currentLocalDate, graduationLocalDate, null);
    }

    /**
     * 根据时间差计算当前统计年份学生的工作岗位
     *  todo: 2025-01-15 (王察) 1、学历是大专的，工作岗位是本科下的，学制是2年的。根据入学时间计算第一年属于本科其他年级，1年后属于本科毕业岗位。(只有正式、预备党员，本年度发展党员)
     *
     * @param d09Code              工作岗位
     * @param enterSchoolLocalDate 入学时间
     * @param currentLocalDate     统计时间或入党时间
     * @param graduationLocalDate  毕业时间
     * @param d07Code  学历
     * @return 计算后的工作岗位
     */
    private String calculateD09Code(String d09Code, LocalDate enterSchoolLocalDate, LocalDate currentLocalDate, LocalDate graduationLocalDate, String d07Code) {
        if (StringUtils.isEmpty(d09Code)) {
            return d09Code;
        }
        boolean flag = Objects.nonNull(graduationLocalDate);
        //31 研究生 32 大学本科学生 33 大学专科学生
        if (StrUtil.startWith(d09Code, "31")) {
            //统计时间-入学时间<1年：一年级学生  -> 统计时间<1年+入学时间 -> 1年+入学时间>统计时间
            if (enterSchoolLocalDate.plusYears(1).isAfter(currentLocalDate)) {
                //312 研究生一年级学生
                return "312";
            }
            if (flag) {
                //学制-1>统计时间-入学时间>1年：其他年级学生 -> 学制+入学时间>统计时间+1   统计时间-入学时间>1年 -> 统计时间>入学时间+1年
                if (graduationLocalDate.isAfter(currentLocalDate.plusYears(1)) && (currentLocalDate.isAfter(enterSchoolLocalDate.plusYears(1)))) {
                    //313 研究生其他年级学生
                    return "313";
                }
                //学制>统计时间-入学时间>学制-1：毕业年级学生 -> 学制>统计时间-入学时间 学制+入学时间>统计时间, 统计时间-入学时间>学制-1  统计时间+1>学制+入学时间
                if (graduationLocalDate.isAfter(currentLocalDate) && (currentLocalDate.plusYears(1).isAfter(graduationLocalDate))) {
                    //311 研究生毕业年级学生
                    return "311";
                }
            }
        } else if (StrUtil.startWith(d09Code, "32")) {
            // todo: 2025-01-15 (王察) 1、学历是大专的，工作岗位是本科下的，学制是2年的。根据入学时间计算第一年属于本科其他年级，1年后属于本科毕业岗位。(只有正式、预备党员，本年度发展党员)
            // todo: 2025-01-15 学生党员计算岗位：学历是34-大学普通班也加上。 学制是3年的前两年算作其他年级，后一年以上算作毕业年级。 -- 王察
            if(StrUtil.equalsAny(d07Code, "31", "34") && flag){
                // 入学时间加上2年等于毕业时间属于学制两年的
                if( enterSchoolLocalDate.plusYears(2).compareTo(graduationLocalDate) == 0){
                    //  入学时间+1 大于 统计时间
                    if (enterSchoolLocalDate.plusYears(1).isAfter(currentLocalDate)) {
                        //323 大学本科其他年级学生
                        return "323";
                    }
                    // 毕业时间大于 统计时间  && 统计时间+1年 大于 毕业时间
                    if (graduationLocalDate.isAfter(currentLocalDate) && (currentLocalDate.plusYears(1).isAfter(graduationLocalDate))) {
                        //321 大学本科毕业年级学生
                        return "321";
                    }
                }

                // 入学时间加上3年等于毕业时间属于学制三年的
                if( enterSchoolLocalDate.plusYears(3).compareTo(graduationLocalDate) == 0){
                    //  入学时间+2 大于 统计时间
                    if (enterSchoolLocalDate.plusYears(2).isAfter(currentLocalDate)) {
                        //323 大学本科其他年级学生
                        return "323";
                    }
                    // 毕业时间大于 统计时间  && 统计时间+1年 大于 毕业时间
                    if (graduationLocalDate.isAfter(currentLocalDate) && (currentLocalDate.plusYears(1).isAfter(graduationLocalDate))) {
                        //321 大学本科毕业年级学生
                        return "321";
                    }
                }
            }
            if (enterSchoolLocalDate.plusYears(1).isAfter(currentLocalDate)) {
                //322 大学本科一年级学生
                return "322";
            }
            if (flag) {
                //学制-1>统计时间-入学时间>1年：其他年级学生
                if (graduationLocalDate.isAfter(currentLocalDate.plusYears(1)) && (currentLocalDate.isAfter(enterSchoolLocalDate.plusYears(1)))) {
                    //323 大学本科其他年级学生
                    return "323";
                }
                //学制>统计时间-入学时间>学制-1：毕业年级学生
                if (graduationLocalDate.isAfter(currentLocalDate) && (currentLocalDate.plusYears(1).isAfter(graduationLocalDate))) {
                    //321 大学本科毕业年级学生
                    return "321";
                }
            }
        } else if (StrUtil.startWith(d09Code, "33")) {
            if (enterSchoolLocalDate.plusYears(1).isAfter(currentLocalDate)) {
                //332 大学专科一年级学生
                return "332";
            }
            if (flag) {
                //学制-1>统计时间-入学时间>1年：其他年级学生
                if (graduationLocalDate.isAfter(currentLocalDate.plusYears(1)) && (currentLocalDate.isAfter(enterSchoolLocalDate.plusYears(1)))) {
                    //333 大学专科其他年级学生
                    return "333";
                }
                //学制>统计时间-入学时间>学制-1：毕业年级学生
                if (graduationLocalDate.isAfter(currentLocalDate) && (currentLocalDate.plusYears(1).isAfter(graduationLocalDate))) {
                    //331 大学专科毕业年级学生
                    return "331";
                }
            }
        }
        if (flag) {
            //统计时间-入学时间>学制：学生毕业未就业人员 -> 统计时间>学制+入学时间
            if (currentLocalDate.isAfter(graduationLocalDate)) {
                //515 学生毕业未就业的人员
                return "515";
            }
        }
        return d09Code;
    }
}
