package com.zenith.front.web.controller.unit;

import com.zenith.front.api.unit.IUnitMemSelectService;
import com.zenith.front.common.annotation.RequiresPermissions;
import com.zenith.front.common.constant.OperateConstant;
import com.zenith.front.model.dto.UnitMemSelectBaseDTO;
import com.zenith.front.model.dto.UnitMemSelectDTO;
import com.zenith.front.model.dto.UnitMemSelectLeaveDTO;
import com.zenith.front.model.dto.UnitMemSelectListDTO;
import com.zenith.front.model.message.InMessage;
import com.zenith.front.model.message.OutMessage;
import com.zenith.front.model.validate.annotation.Validate;
import com.zenith.front.web.controller.BaseController;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

/**
 * 选调生控制层
 * <AUTHOR>
 * @create_date 2025-05-07 16:26
 * @description
 */
@RestController
@RequestMapping("/unit/memSelect")
public class UnitMemSelectController extends BaseController {

    @Resource
    private IUnitMemSelectService unitMemSelectService;

    /**
     * 列表
     * @param inMessage
     * @return
     */
    @PostMapping("/list")
    @Validate
    @RequiresPermissions
    public OutMessage<?> list(@RequestBody InMessage<UnitMemSelectListDTO> inMessage) {
        return unitMemSelectService.list(inMessage.getData());
    }

    /**
     * 新增
     * @param inMessage
     * @return
     */
    @PostMapping("/add")
    @Validate
    @RequiresPermissions(opt = OperateConstant.INSERT)
    public OutMessage<?> add(@RequestBody InMessage<UnitMemSelectDTO> inMessage) {
        return unitMemSelectService.add(inMessage.getData());
    }

    /**
     * 编辑
     * @param inMessage
     * @return
     */
    @PostMapping("/update")
    @Validate
    @RequiresPermissions(opt = OperateConstant.UPDATE)
    public OutMessage<?> update(@RequestBody InMessage<UnitMemSelectBaseDTO> inMessage) throws Exception {
        return unitMemSelectService.update(inMessage.getData(), false);
    }

    /**
     * 离开
     * @param inMessage
     * @return
     */
    @PostMapping("/leave")
    @Validate
    @RequiresPermissions
    public OutMessage<?> leave(@RequestBody InMessage<UnitMemSelectLeaveDTO> inMessage) throws Exception {
        return unitMemSelectService.leave(inMessage.getData());
    }

    /**
     * 删除
     * @param code
     * @return
     */
    @GetMapping("/delete")
    @Validate
    @RequiresPermissions
    public OutMessage<?> delete(String code) {
        return unitMemSelectService.delete(code);
    }
}
