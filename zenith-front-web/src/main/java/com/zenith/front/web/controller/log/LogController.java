package com.zenith.front.web.controller.log;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.zenith.front.api.log.ILoginLogService;
import com.zenith.front.common.annotation.RequiresPermissions;
import com.zenith.front.model.dto.LoginLogDTO;
import com.zenith.front.model.dto.PageDTO;
import com.zenith.front.model.message.InMessage;
import com.zenith.front.model.message.OutMessage;
import com.zenith.front.model.message.Status;
import com.zenith.front.model.validate.annotation.Validate;
import com.zenith.front.web.controller.BaseController;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @date 2019/4/44:25 PM
 */
@RestController
@RequestMapping("/log")
public class LogController extends BaseController {

    @Resource
    private ILoginLogService iLoginLogService;


    @PostMapping("/findByPage")
    @RequiresPermissions
    @Validate
    public OutMessage<Page<LoginLogDTO>> findByPage(@RequestBody InMessage<PageDTO> inMessage) {
        return new OutMessage<>(Status.SUCCESS, iLoginLogService.findByPage(inMessage.getData()));
    }
}
