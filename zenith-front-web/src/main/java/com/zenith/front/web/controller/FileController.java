package com.zenith.front.web.controller;

import com.zenith.front.framework.file.FileUploadResponse;
import com.zenith.front.framework.file.core.MinioTemplate;
import com.zenith.front.model.message.OutMessage;
import com.zenith.front.model.message.Status;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.util.Collections;

/**
 * minio 文件上传类
 *
 * <AUTHOR>
 * @version V1.0
 * @date 2023/2/23 10:39
 */
@Controller
public class FileController {

    @Resource
    private MinioTemplate minioTemplate;

    /**
     * 文件上传
     *
     * @param file 文件
     * @return com.zenith.front.message.OutMessage<?>
     * <AUTHOR>
     * @creed Talk is cheap,show me the code
     * @date 2023年02月23日 15时45分
     */
    @PostMapping("/base/putFile")
    @ResponseBody
    public OutMessage<?> putFile(@RequestParam("file") MultipartFile file) throws Exception {
        FileUploadResponse response = minioTemplate.upload("dw", file);
        return new OutMessage<>(Status.SUCCESS, Collections.singletonList(response));
    }

}
