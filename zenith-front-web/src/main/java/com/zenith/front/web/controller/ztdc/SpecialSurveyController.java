package com.zenith.front.web.controller.ztdc;

import com.zenith.front.api.ztdc.*;
import com.zenith.front.common.annotation.RequiresPermissions;
import com.zenith.front.common.constant.OperateConstant;

import com.zenith.front.model.dto.Zt1RuralPartyDTO;
import com.zenith.front.model.dto.Zt2TeamOptimizeDTO;
import com.zenith.front.model.dto.Zt3DevelopEconomyDTO;
import com.zenith.front.model.dto.Zt4BasicWordDTO;
import com.zenith.front.model.message.InMessage;
import com.zenith.front.model.message.OutMessage;
import com.zenith.front.model.message.Status;
import com.zenith.front.model.bean.Zt5CommunityParty;
import com.zenith.front.model.validate.annotation.Validate;
import com.zenith.front.model.validate.group.AddGroup;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.constraints.NotBlank;
import java.util.Objects;

/**
 * <AUTHOR>
 * 专题调查表
 * @Date 2021/8/13 18:19
 * @Version 1.0
 */
@RestController
@RequestMapping("/specialSurvey")
public class SpecialSurveyController {


    @Resource
    private IZt1RuralPartyService iZt1RuralPartyService;

    @Resource
    private IZt2TeamOptimizeService iZt2TeamOptimizeService;

    @Resource
    private IZt3DevelopEconomyService iZt3DevelopEconomyService;

    @Resource
    private IZt4BasicWordService iZt4BasicWordService;

    @Resource
    private IZt5CommunityPartyService iZt5CommunityPartyService;



    /**
     * 添加专题调查表一
     */
    @PostMapping("/addOne")
    @RequiresPermissions(opt = OperateConstant.INSERT)
    @Validate(group = AddGroup.class)
    public OutMessage addOne(@RequestBody InMessage<Zt1RuralPartyDTO> inMessage){
        if (Objects.isNull(inMessage.getData())) {
            return new OutMessage(Status.SUCCESS);
        }
        return iZt1RuralPartyService.addOne(inMessage.getData());
    }

    /**
     * 添加专题调查表二
     */
    @PostMapping("/addTwo")
    @RequiresPermissions(opt = OperateConstant.INSERT)
    @Validate(group = AddGroup.class)
    public OutMessage addTwo(@RequestBody InMessage<Zt2TeamOptimizeDTO> inMessage){
        if (Objects.isNull(inMessage.getData())) {
            return new OutMessage(Status.SUCCESS);
        }
        return iZt2TeamOptimizeService.addTwo(inMessage.getData());
    }

    /**
     * 添加专题调查表三
     */
    @PostMapping("/addThree")
    @RequiresPermissions(opt = OperateConstant.INSERT)
    @Validate(group = AddGroup.class)
    public OutMessage addThree(@RequestBody InMessage<Zt3DevelopEconomyDTO> inMessage){
        if (Objects.isNull(inMessage.getData())) {
            return new OutMessage(Status.SUCCESS);
        }
        return iZt3DevelopEconomyService.addThree(inMessage.getData());
    }

    /**
     * 添加专题调查表四
     */
    @PostMapping("/addFour")
    @RequiresPermissions(opt = OperateConstant.INSERT)
    @Validate(group = AddGroup.class)
    public OutMessage addFour(@RequestBody InMessage<Zt4BasicWordDTO> inMessage){
        if (Objects.isNull(inMessage.getData())) {
            return new OutMessage(Status.SUCCESS);
        }
        return iZt4BasicWordService.addFour(inMessage.getData());
    }

    /**
     * 添加专题调查表五
     */
    @PostMapping("/addFive")
    @RequiresPermissions(opt = OperateConstant.INSERT)
    @Validate(group = AddGroup.class)
    public OutMessage addFive(@RequestBody InMessage<Zt5CommunityParty> inMessage){
        if (Objects.isNull(inMessage.getData())) {
            return new OutMessage(Status.SUCCESS);
        }
        return iZt5CommunityPartyService.addFive(inMessage.getData());
    }

    /**
     * 获取专题调查信息
     */
    @GetMapping("/findDataByCode")
    @RequiresPermissions(opt = OperateConstant.QUERY)
    public OutMessage findDataByCode(@NotBlank(message = "unitCode 不能为空") String unitCode,@NotBlank(message = "type 不能为空") String type){
        return iZt1RuralPartyService.findDataByCode(unitCode,type);
    }

}
