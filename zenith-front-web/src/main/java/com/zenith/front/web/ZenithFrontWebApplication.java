package com.zenith.front.web;

import lombok.extern.slf4j.Slf4j;
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.actuate.autoconfigure.elasticsearch.ElasticSearchRestHealthContributorAutoConfiguration;
import org.springframework.boot.autoconfigure.EnableAutoConfiguration;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.web.embedded.tomcat.TomcatServletWebServerFactory;
import org.springframework.boot.web.servlet.server.ConfigurableServletWebServerFactory;
import org.springframework.context.annotation.Bean;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.transaction.annotation.EnableTransactionManagement;

import javax.annotation.PostConstruct;

/**
 * <AUTHOR>
 * proxyTargetClass = true 主要配置基于JDK的代理还是基于类的动态代理的配置，这种错误提示需要设置基于类的代理才行
 * EnableAspectJAutoProxy(exposeProxy = true)暴露AOP的Proxy对象
 */
@SpringBootApplication(scanBasePackages = {"com.zenith.front"}, exclude = ElasticSearchRestHealthContributorAutoConfiguration.class)
@MapperScan(basePackages = {"com.zenith.front.**.dao.mapper"})
@EnableTransactionManagement(proxyTargetClass = true)
@EnableScheduling
@EnableAsync
@Slf4j
public class ZenithFrontWebApplication {

    public static void main(String[] args) {
        SpringApplication.run(ZenithFrontWebApplication.class, args);
    }

    @Bean
    public ConfigurableServletWebServerFactory webServerFactory() {
        TomcatServletWebServerFactory factory = new TomcatServletWebServerFactory();
        factory.addConnectorCustomizers(connector -> {
            connector.setProperty("relaxedPathChars", "\"<>[\\]^`{|}");
            connector.setProperty("relaxedQueryChars", "|{}[]\\");
        });
        return factory;
    }

    @PostConstruct
    public void configureParallelism() {
        System.setProperty("java.util.concurrent.ForkJoinPool.common.parallelism", "6");
        log.info("设置ForkJoinPool并行度为6");
    }

}
