package com.zenith.front.web.config;

import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;

import java.util.concurrent.Executor;
import java.util.concurrent.ThreadPoolExecutor;

/**
 * 线程池配置
 *
 * <AUTHOR>
 * @creed Talk is cheap,show me the code
 * @date 2020年12月10日 12时06分
 */
@Configuration
@EnableAsync
public class ExecutorConfig {

    /**
     * 参数初始化
     */
    private static final int CPU_COUNT = Runtime.getRuntime().availableProcessors();
    /**
     * 核心线程数量大小
     */
//    private static final int CORE_POOL_SIZE = Math.max(2, Math.min(CPU_COUNT - 1, 4));
    private static final int CORE_POOL_SIZE = 4;
    /**
     * 线程池最大容纳线程数
     */
//    private static final int MAX_POOL_SIZE = CPU_COUNT * 2 + 1;
    private static final int MAX_POOL_SIZE = 6;
    /**
     * 阻塞队列
     */
    private static final int QUEUE_CAPACITY = 20;
    /**
     * 线程空闲后的存活时长
     */
    private static final int KEEP_ALIVE_SECONDS = 30;
    /**
     * 线程池名前缀
     */
    private static final String THREAD_NAME_PREFIX = "MySimpleExecutor-";

    @Bean
    public Executor mySimpleAsync() {
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
        // 核心线程数
        executor.setCorePoolSize(CORE_POOL_SIZE);
        // 最大线程数
        executor.setMaxPoolSize(MAX_POOL_SIZE);
        // 任务队列大小
        executor.setQueueCapacity(QUEUE_CAPACITY);
        // 线程前缀名
        executor.setThreadNamePrefix(THREAD_NAME_PREFIX);
        // 线程的空闲时间
        executor.setKeepAliveSeconds(KEEP_ALIVE_SECONDS);
        // 拒绝策略
        // CallerRunsPolicy()：交由调用方线程运行，比如 main 线程。
        // AbortPolicy()：直接抛出异常。
        // DiscardPolicy()：直接丢弃。
        // DiscardOldestPolicy()：丢弃队列中最老的任务。
        executor.setRejectedExecutionHandler(new ThreadPoolExecutor.CallerRunsPolicy());
        // 线程初始化
        executor.initialize();
        return executor;
    }
}
