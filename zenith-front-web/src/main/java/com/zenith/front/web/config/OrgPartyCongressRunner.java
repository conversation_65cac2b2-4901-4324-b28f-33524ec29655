package com.zenith.front.web.config;

import com.zenith.front.dao.mapper.org.OrgPartyCongressCommitteeAllMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.CommandLineRunner;
import org.springframework.core.annotation.Order;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 */
//@Component
@Order(101)
@Slf4j
public class OrgPartyCongressRunner implements CommandLineRunner {

    @Resource
    private OrgPartyCongressCommitteeAllMapper orgPartyCongressCommitteeAllMapper;
    @Value("${transferRecord}")
    private Boolean open = false;

    @Override
    public void run(String... args) {
        if (true) {
            orgPartyCongressCommitteeAllMapper.updateInfo();
            orgPartyCongressCommitteeAllMapper.updateInfo1();
        }
    }
}
