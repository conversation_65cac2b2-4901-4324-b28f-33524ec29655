package com.zenith.front.web.controller.mem;

import com.zenith.front.api.mem.IMemRewardService;
import com.zenith.front.common.annotation.RequiresPermissions;
import com.zenith.front.common.constant.OperateConstant;
import com.zenith.front.model.dto.MemRewardDTO;
import com.zenith.front.model.message.InMessage;
import com.zenith.front.model.message.OutMessage;
import com.zenith.front.model.message.Status;
import com.zenith.front.model.validate.annotation.Validate;
import com.zenith.front.model.validate.group.AddGroup;
import com.zenith.front.web.controller.BaseController;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.constraints.Max;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @version 1.0
 * @Description: 人员奖惩
 * @date 2019/4/10 17:42
 */
@RestController
@RequestMapping("/mem/reward")
public class MemRewardController extends BaseController {

    @Resource
    private IMemRewardService memRewardService;

    /**
     * 获取人员奖惩列表
     *
     * @param pageNum
     * @param pageSize
     * @param memCode
     * @return
     */
    @GetMapping("/getList")
    @Validate
    @RequiresPermissions
    public OutMessage getList(@Min(value = 1, message = "页码最小为1") int pageNum,
                              @Max(value = 100, message = "页大小范围在1-100") int pageSize,
                              @NotNull(message = "memCode 不能为空") String memCode) {
        return memRewardService.getList(pageNum, pageSize, memCode);
    }

    /**
     * 新增人员奖惩
     *
     * @param inMessage
     * @return
     */
    @PostMapping("/addMemReward")
    @Validate(group = AddGroup.class)
    @RequiresPermissions(opt = OperateConstant.INSERT)
    public OutMessage addMemReward(@RequestBody InMessage<MemRewardDTO> inMessage) {
        MemRewardDTO memRewardDTO = inMessage.getData();
        String rewardOrgCode = memRewardDTO.getRewardOrgCode();
        if (!rewardOrgCode.startsWith(this.getCurrManOrgCode())) {
            return new OutMessage<>(Status.PERMISSION_DENIED);
        }
        return memRewardService.addMemReward(memRewardDTO);
    }

    /**
     * 获取人员奖惩信息
     *
     * @param code
     * @return
     */
    @GetMapping("/findByCode")
    @Validate
    @RequiresPermissions
    public OutMessage findByCode(@NotBlank(message = "code 不能为空") String code) {
        return memRewardService.findByCodeOut(code);
    }

    /**
     * 修改人员奖惩信息
     *
     * @param inMessage
     * @return
     */
    @PostMapping("/updateMemReward")
    @Validate(group = AddGroup.class)
    @RequiresPermissions(opt = OperateConstant.UPDATE)
    public OutMessage updateMemReward(@RequestBody InMessage<MemRewardDTO> inMessage) throws Exception {
        MemRewardDTO memRewardDTO = inMessage.getData();
        String rewardOrgCode = memRewardDTO.getRewardOrgCode();
        if (!rewardOrgCode.startsWith(this.getCurrManOrgCode())) {
            return new OutMessage<>(Status.PERMISSION_DENIED);
        }
        return memRewardService.updateMemReward(memRewardDTO);
    }

    /**
     * 删除人员奖惩
     *
     * @param code
     * @return
     */
    @GetMapping("/delMemReward")
    @Validate
    @RequiresPermissions(opt = OperateConstant.DELETE)
    public OutMessage delMemReward(@NotBlank(message = "code 不能为空") String code) {
        return memRewardService.delMemReward(code);
    }
}
