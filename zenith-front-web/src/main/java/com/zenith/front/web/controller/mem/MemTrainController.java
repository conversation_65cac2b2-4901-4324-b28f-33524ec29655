package com.zenith.front.web.controller.mem;

import com.zenith.front.api.mem.IMemTrainService;
import com.zenith.front.common.annotation.RequiresPermissions;
import com.zenith.front.common.constant.OperateConstant;
import com.zenith.front.model.dto.MemTrainDTO;
import com.zenith.front.model.dto.MemTrainPageDTO;
import com.zenith.front.model.message.InMessage;
import com.zenith.front.model.message.OutMessage;
import com.zenith.front.model.message.Status;
import com.zenith.front.model.validate.annotation.Validate;
import com.zenith.front.model.validate.group.AddGroup;
import com.zenith.front.web.controller.BaseController;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.constraints.NotNull;
import java.util.Objects;

/**
 * <AUTHOR>
 * 人员培训
 * @Date 2021/8/5 16:57
 * @Version 1.0
 */
@RestController
@RequestMapping("/mem/train")
public class MemTrainController extends BaseController {

    @Resource
    private IMemTrainService trainService;

    /**
     * 添加或修改单个培训信息
     */
    @PostMapping("/addOrUpdate")
    @RequiresPermissions(opt = OperateConstant.INSERT)
    @Validate(group = AddGroup.class)
    public OutMessage addOrUpdate(@RequestBody InMessage<MemTrainDTO> inMessage) {
        if (Objects.isNull(inMessage.getData())) {
            return new OutMessage(Status.SUCCESS);
        }
        return trainService.addOrUpdate(inMessage.getData());
    }

    /**
     * 查看单个培训信息
     */
    @GetMapping("/findTrainByCode")
    @Validate
    @RequiresPermissions
    public OutMessage findExtendByCode(@NotNull(message = "code不能为空") String code) {
        return trainService.findTrainByCode(code);
    }


    /**
     * 查看培训信息列表
     */
    @PostMapping("/listTrainByCode")
    @Validate
    @RequiresPermissions
    public OutMessage listTrainByCode(@RequestBody InMessage<MemTrainPageDTO> inMessage) {
        return trainService.listTrainByCode(inMessage.getData());
    }

    /**
     * 删除单个培训信息
     */
    @GetMapping("/deleteTrainByCode")
    @Validate
    @RequiresPermissions
    public OutMessage deleteTrainByCode(@NotNull(message = "code不能为空") String code) {
        return trainService.deleteTrainByCode(code);
    }

}
