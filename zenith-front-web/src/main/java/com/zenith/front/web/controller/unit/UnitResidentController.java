package com.zenith.front.web.controller.unit;

import com.zenith.front.api.unit.IUnitResidentService;
import com.zenith.front.common.annotation.RequiresPermissions;

import com.zenith.front.model.dto.PrimaryKeyDTO;
import com.zenith.front.model.dto.UnitResidentDTO;
import com.zenith.front.model.dto.UnitResidentUpdateDTO;
import com.zenith.front.model.message.InMessage;
import com.zenith.front.model.message.OutMessage;
import com.zenith.front.model.validate.annotation.Validate;
import com.zenith.front.model.validate.group.AddGroup;
import com.zenith.front.web.controller.BaseController;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 */
@RestController
@RequestMapping("/unit/resident")
@Validated
public class UnitResidentController extends BaseController {

    @Resource
    private IUnitResidentService unitResidentService;

    /**
     * 添加驻村第一书记和驻村干部
     */
    @PostMapping("/save")
    @RequiresPermissions
    public OutMessage<Object> save(@Valid @RequestBody InMessage<UnitResidentDTO> inMessage) {
        String account = this.getCurrUser().getUser().getAccount();
        return unitResidentService.doSave(inMessage.getData(), account);
    }

    /**
     * 删除驻村第一书记和驻村干部
     */
    @PostMapping("/del")
    @RequiresPermissions
    public OutMessage<Object> del(@Valid @RequestBody InMessage<PrimaryKeyDTO> inMessage) {
        String account = this.getCurrUser().getUser().getAccount();
        return unitResidentService.doDel(inMessage.getData(), account);
    }

    /**
     * 修改驻村第一书记和驻村干部
     */
    @PostMapping("/update")
    @RequiresPermissions
    @Validate(group = AddGroup.class)
    public OutMessage<Object> update(@RequestBody InMessage<UnitResidentUpdateDTO> inMessage) {
        String account = this.getCurrUser().getUser().getAccount();
        return unitResidentService.doUpdate(inMessage.getData(), account);
    }

    /**
     * 查询驻村第一书记和驻村干部
     */
    @GetMapping("/list")
    @RequiresPermissions
    public OutMessage<Object> list(@NotBlank(message = "unitCode不能为空") String unitCode,
                                   @NotBlank(message = "leave不能为空") String leave,
                                   @Min(value = 1L, message = "最小值不能小于1")
                                   @NotNull(message = "页码不能为空") Integer pageNum,
                                   @NotNull(message = "每页显示条数不能为空") Integer pageSize) {
        return unitResidentService.doList(pageNum, pageSize, unitCode, leave);
    }

    /**
     * 届内历史任职驻村第一书记和驻村干部撤销
     */
    @PostMapping("/backOut")
    @RequiresPermissions
    @Validate(group = AddGroup.class)
    public OutMessage<Object> backOut(@RequestBody InMessage<PrimaryKeyDTO> inMessage) {
        String account = this.getCurrUser().getUser().getAccount();
        return unitResidentService.backOut(inMessage.getData(), account);
    }

}
