package com.zenith.front.web.controller.fee;

import com.zenith.front.api.fee.IFeeDisburseService;
import com.zenith.front.common.annotation.RequiresPermissions;
import com.zenith.front.model.dto.FeeDisburseDTO;
import com.zenith.front.model.dto.FeeDisburseListDTO;
import com.zenith.front.model.message.InMessage;
import com.zenith.front.model.message.OutMessage;
import com.zenith.front.model.message.Status;
import com.zenith.front.model.bean.User;
import com.zenith.front.model.validate.annotation.Validate;
import com.zenith.front.model.validate.group.Common1Group;
import com.zenith.front.model.validate.group.Common2Group;
import com.zenith.front.model.validate.group.Common3Group;
import com.zenith.front.model.validate.group.QueryGroup;
import com.zenith.front.web.controller.BaseController;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.constraints.NotBlank;

/**
 * <AUTHOR>
 * @version 1.0
 * @Description: 党费支出
 * @date 2019/6/4 14:17
 */
@RestController
@RequestMapping("/fee/disburse")
public class FeeDisburseController extends BaseController {

    @Resource
    private IFeeDisburseService feeDisburseService;


    /**
     * 获取党费支出列表
     *
     * @param inMessage
     * @return
     */
    @Validate(group = {Common1Group.class, QueryGroup.class})
    @RequiresPermissions
    @PostMapping("/getList")
    public OutMessage getList(@RequestBody InMessage<FeeDisburseListDTO> inMessage) {
        FeeDisburseListDTO feeDisburseListDTO = inMessage.getData();
        if (!feeDisburseListDTO.getDisburseOrgCode().startsWith(this.getCurrManOrgCode())) {
            return new OutMessage<>(Status.PERMISSION_DENIED);
        }
        return feeDisburseService.getList(feeDisburseListDTO);
    }

    /**
     * 保存党费支出
     *
     * @param inMessage
     * @return
     */
    @Validate(group = Common1Group.class)
    @RequiresPermissions
    @PostMapping("/saveFeeDisburse")
    public OutMessage saveFeeDisburse(@RequestBody InMessage<FeeDisburseDTO> inMessage) {
        FeeDisburseDTO feeDisburseDTO = inMessage.getData();
        if (!feeDisburseDTO.getDisburseOrgCode().startsWith(this.getCurrManOrgCode())) {
            return new OutMessage<>(Status.PERMISSION_DENIED);
        }
        return feeDisburseService.saveFeeDisburse(feeDisburseDTO);
    }

    /**
     * 修改党费支出
     *
     * @param inMessage
     * @return
     */
    @Validate(group = {Common1Group.class, Common2Group.class})
    @RequiresPermissions
    @PostMapping("/updateFeeDisburse")
    public OutMessage updateFeeDisburse(@RequestBody InMessage<FeeDisburseDTO> inMessage) {
        FeeDisburseDTO feeDisburseDTO = inMessage.getData();
        if (!feeDisburseDTO.getDisburseOrgCode().startsWith(this.getCurrManOrgCode())) {
            return new OutMessage<>(Status.PERMISSION_DENIED);
        }
        return feeDisburseService.updateFeeDisburse(feeDisburseDTO);
    }

    /**
     * 删除党费支出
     *
     * @param code
     * @return
     */
    @Validate(group = Common1Group.class)
    @RequiresPermissions
    @GetMapping("/delFeeDisburse")
    public OutMessage delFeeDisburse(@NotBlank(message = "code 不能为空") String code) {
        return feeDisburseService.delFeeDisburse(code);
    }

    /**
     * 获取微信流水
     *
     * @param inMessage
     * @return
     */
    @Validate(group = {Common2Group.class, QueryGroup.class})
    @RequiresPermissions
    @PostMapping("/getWxBillList")
    public OutMessage getWxBillList(@RequestBody InMessage<FeeDisburseListDTO> inMessage) {
        FeeDisburseListDTO feeDisburseListDTO = inMessage.getData();
        return feeDisburseService.getWxBillList(feeDisburseListDTO);
    }

    /**
     * 根据微信订单保存党费支出数据
     *
     * @param inMessage
     * @return
     */
    @Validate(group = Common3Group.class)
    @RequiresPermissions
    @PostMapping("/saveByWxBill")
    public OutMessage saveByWxBill(@RequestBody InMessage<FeeDisburseDTO> inMessage) {
        FeeDisburseDTO feeDisburseDTO = inMessage.getData();
        User user = this.getCurrUser().getUser();
        return feeDisburseService.saveByWxBill(feeDisburseDTO, user);
    }
}
