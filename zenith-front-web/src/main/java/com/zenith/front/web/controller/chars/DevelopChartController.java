package com.zenith.front.web.controller.chars;


import com.zenith.front.api.dict.IDictChartService;
import com.zenith.front.api.mem.IMemDevelopService;
import com.zenith.front.common.annotation.RequiresPermissions;
import com.zenith.front.common.constant.CommonConstant;
import com.zenith.front.model.dto.ChartDataDTO;
import com.zenith.front.model.message.InMessage;
import com.zenith.front.model.message.OutMessage;
import com.zenith.front.model.validate.annotation.Validate;
import com.zenith.front.model.validate.group.Common1Group;
import com.zenith.front.web.controller.BaseController;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @version 1.0
 * @Description: 发展党员概况
 * @date 2019/4/29 16:24
 */
@RestController
@RequestMapping("/chart/develop")
public class DevelopChartController extends BaseController {

    @Resource
    private IDictChartService chartService;

    @Resource
    private IMemDevelopService developService;

    /**
     * 获取发展党员总数
     *
     * @param inMessage
     * @return
     */
    @Validate(group = Common1Group.class)
    @RequiresPermissions
    @PostMapping("/getDevelopTotal")
    public OutMessage getDevelopTotal(@RequestBody InMessage<ChartDataDTO> inMessage) {
        return chartService.getDevelopTotal(inMessage.getData());
    }

    /**
     * 获取发展党员分类
     *
     * @param inMessage
     * @return
     */
    @Validate(group = Common1Group.class)
    @RequiresPermissions
    @PostMapping("/getDevelopTypeTotal")
    public OutMessage getDevelopTypeTotal(@RequestBody InMessage<ChartDataDTO> inMessage) {
        return developService.getDevelopTypeTotal(inMessage.getData());
    }

    /**
     * 获取男女比例
     *
     * @param inMessage
     * @return
     */
    @Validate(group = Common1Group.class)
    @RequiresPermissions
    @PostMapping("/getSexRatioTotal")
    public OutMessage getSexRatioTotal(@RequestBody InMessage<ChartDataDTO> inMessage) {
        return developService.getSexRatioTotal(inMessage.getData());
    }

    /**
     * 获取学历比例
     *
     * @param inMessage
     * @return
     */
    @Validate(group = Common1Group.class)
    @RequiresPermissions
    @PostMapping("/getEducationRatioTotal")
    public OutMessage getEducationRatioTotal(@RequestBody InMessage<ChartDataDTO> inMessage) {
        return developService.getEducationRatioTotal(inMessage.getData());
    }

    /**
     * 入党申请人总数
     *
     * @param inMessage
     * @return
     */
    @RequiresPermissions
    @Validate(group = Common1Group.class)
    @PostMapping("/getApplicantTotal")
    public OutMessage getApplicantTotal(@RequestBody InMessage<ChartDataDTO> inMessage) {
        return developService.getApplicantTotal(inMessage.getData(), CommonConstant.FIVE);
    }

    /**
     * 积极分子总数
     *
     * @param inMessage
     * @return
     */
    @RequiresPermissions
    @Validate(group = Common1Group.class)
    @PostMapping("/getActivistsTotal")
    public OutMessage getActivistsTotal(@RequestBody InMessage<ChartDataDTO> inMessage) {
        return developService.getApplicantTotal(inMessage.getData(), CommonConstant.FOUR);
    }

    /**
     * 发展对象总数
     *
     * @param inMessage
     * @return
     */
    @RequiresPermissions
    @Validate(group = Common1Group.class)
    @PostMapping("/getObjectTotal")
    public OutMessage getObjectTotal(@RequestBody InMessage<ChartDataDTO> inMessage) {
        return developService.getApplicantTotal(inMessage.getData(), CommonConstant.THREE);
    }

    /**
     * 发展党员一线情况统计
     */
    @RequiresPermissions
    @Validate(group = Common1Group.class)
    @PostMapping("/getLineSituation")
    public OutMessage getLineSituation(@RequestBody InMessage<ChartDataDTO> inMessage) {
        return developService.getLineSituation(inMessage.getData());
    }


    /**
     * 在岗职工，农牧渔民，解放军、武警,学生，离退休人员，其他人员
     */
    @RequiresPermissions
    @Validate(group = Common1Group.class)
    @PostMapping("/getJobType")
    public OutMessage getJobType(@RequestBody InMessage<ChartDataDTO> inMessage) {
        return developService.getJobType(inMessage.getData());
    }


    /**
     * 公有制单位所有分类
     */
    @RequiresPermissions
    @Validate(group = Common1Group.class)
    @PostMapping("/getPublicUnit")
    public OutMessage getPublicUnit(@RequestBody InMessage<ChartDataDTO> inMessage) {
        return developService.getPublicUnit(inMessage.getData(), "01");
    }


    /**
     * 非公有制单位所有分类
     */
    @RequiresPermissions
    @Validate(group = Common1Group.class)
    @PostMapping("/getNoPublicUnit")
    public OutMessage getNoPublicUnit(@RequestBody InMessage<ChartDataDTO> inMessage) {
        return developService.getPublicUnit(inMessage.getData(), "02");
    }

    /**
     * 社会组织所有分类
     */
    @RequiresPermissions
    @Validate(group = Common1Group.class)
    @PostMapping("/getSocietyUnit")
    public OutMessage getSocietyUnit(@RequestBody InMessage<ChartDataDTO> inMessage) {
        return developService.getPublicUnit(inMessage.getData(), "03");
    }


    /**
     * 农牧渔民所有分类
     */
    @RequiresPermissions
    @Validate(group = Common1Group.class)
    @PostMapping("/getFarmingUnit")
    public OutMessage getFarmingUnit(@RequestBody InMessage<ChartDataDTO> inMessage) {
        return developService.getPublicUnit(inMessage.getData(), "1");
    }

    /**
     * 发展党员中少数民族
     */
    @RequiresPermissions
    @Validate(group = Common1Group.class)
    @PostMapping("/getMinority")
    public OutMessage getMinority(@RequestBody InMessage<ChartDataDTO> inMessage) {
        return developService.getMinority(inMessage.getData());
    }

    /**
     * 新社会阶层情况
     */
    @RequiresPermissions
    @Validate(group = Common1Group.class)
    @PostMapping("/getNewSocial")
    public OutMessage getNewSocial(@RequestBody InMessage<ChartDataDTO> inMessage) {
        return developService.getNewSocial(inMessage.getData());
    }

    /**
     * 获取大专及以上的党员数
     *
     * @param inMessage
     * @return
     */
    @Validate(group = Common1Group.class)
    @RequiresPermissions
    @PostMapping("/getDzTotal")
    public OutMessage getDzTotal(@RequestBody InMessage<ChartDataDTO> inMessage) {
        return developService.getDzTotal(inMessage.getData());
    }


    /**
     * 首页发展党员情况
     *
     * @param
     * @return
     */
    @Validate(group = Common1Group.class)
    @RequiresPermissions
    @PostMapping("/memberRecruitment")
    public OutMessage memberRecruitment(@RequestBody InMessage<ChartDataDTO> inMessage) {
        return developService.memberRecruitment(inMessage.getData().getOrgCode());
    }

}
