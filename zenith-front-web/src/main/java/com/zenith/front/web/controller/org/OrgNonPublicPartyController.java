package com.zenith.front.web.controller.org;


import com.zenith.front.api.org.IOrgNonPublicPartyService;
import com.zenith.front.common.annotation.RequiresPermissions;
import com.zenith.front.common.constant.OperateConstant;
import com.zenith.front.model.dto.OrgNonPublicPartyDto;
import com.zenith.front.model.dto.OrgNonPublicPartyListDto;
import com.zenith.front.model.message.InMessage;
import com.zenith.front.model.message.OutMessage;
import com.zenith.front.model.validate.group.AddGroup;
import com.zenith.front.model.validate.group.Common1Group;
import com.zenith.front.model.validate.group.UpdateGroup;
import com.zenith.front.web.controller.BaseController;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.constraints.NotBlank;

/**
 * 非公党建情况
 *
 * <AUTHOR>
 * @date 2021/12/08
 */
@Validated
@RestController
@RequestMapping("/org/nonPublic/party")
public class OrgNonPublicPartyController extends BaseController {
    @Resource
    private IOrgNonPublicPartyService iOrgNonPublicPartyService;

    /**
     * 获取非公党建情况列表
     */
    @PostMapping("/getList")
    @RequiresPermissions
    public OutMessage<?> getList(@Validated(value = Common1Group.class) @RequestBody InMessage<OrgNonPublicPartyListDto> inMessage) {
        return iOrgNonPublicPartyService.getList(inMessage.getData());
    }


    /**
     * 新增非公党建情况
     */
    @PostMapping("/addNonPublic")
    @RequiresPermissions(opt = OperateConstant.INSERT)
    public OutMessage<?> addNonPublic(@Validated(value = AddGroup.class) @RequestBody InMessage<OrgNonPublicPartyDto> inMessage) {
        OrgNonPublicPartyDto orgDTO = inMessage.getData();
        return iOrgNonPublicPartyService.addNonPublic(orgDTO);
    }


    /**
     * 编辑非公党建情况
     */
    @PostMapping("/updateNonPublic")
    @RequiresPermissions(opt = OperateConstant.UPDATE)
    public OutMessage<?> updateNonPublic(@Validated(value = UpdateGroup.class) @RequestBody InMessage<OrgNonPublicPartyDto> inMessage) {
        OrgNonPublicPartyDto orgDTO = inMessage.getData();
        return iOrgNonPublicPartyService.updateNonPublic(orgDTO);
    }


    /**
     * 删除非公党建情况
     */
    @GetMapping("/delNonPublic")
    @RequiresPermissions
    public OutMessage<?> delNonPublic(@NotBlank(message = "code 不能为空") String code) {
        return iOrgNonPublicPartyService.delNonPublic(code);
    }


}
