package com.zenith.front.web.controller.mem;

import com.zenith.front.api.mem.IMemTrainInfoService;
import com.zenith.front.common.annotation.RequiresPermissions;
import com.zenith.front.common.constant.OperateConstant;
import com.zenith.front.model.dto.MemTrainInfoDto;
import com.zenith.front.model.dto.MemTrainInfoListDto;
import com.zenith.front.model.message.InMessage;
import com.zenith.front.model.message.OutMessage;
import com.zenith.front.web.controller.BaseController;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.constraints.NotBlank;

/**
 * 党员培训情况
 *
 * <AUTHOR>
 * @date 2022/4/11
 */
@Validated
@RestController
@RequestMapping("/mem/trainInfo")
public class MemTrainInfoController extends BaseController {
    @Resource
    private IMemTrainInfoService iMemTrainInfoService;

    /**
     * 党员培训列表
     */
    @PostMapping("/getList")
    @RequiresPermissions
    public OutMessage<?> getList(@Validated @RequestBody InMessage<MemTrainInfoListDto> inMessage) {
        return iMemTrainInfoService.getList(inMessage.getData());
    }

    /**
     * 添加或修改
     */
    @PostMapping("/addOrUpdate")
    @RequiresPermissions(opt = OperateConstant.INSERT)
    public OutMessage<?> addOrUpdate(@Validated @RequestBody InMessage<MemTrainInfoDto> inMessage) {
        return iMemTrainInfoService.addOrUpdate(inMessage.getData());
    }

    /**
     * 根据code查找
     */
    @GetMapping("/findByCode")
    @RequiresPermissions
    public OutMessage<?> findByCode(@NotBlank(message = "code 不能为空") String code) {
        return iMemTrainInfoService.findByCode(code);
    }

    /**
     * 删除
     */
    @GetMapping("/del")
    @RequiresPermissions
    public OutMessage<?> del(@NotBlank(message = "code 不能为空") String code) {
        return iMemTrainInfoService.del(code);
    }


}
