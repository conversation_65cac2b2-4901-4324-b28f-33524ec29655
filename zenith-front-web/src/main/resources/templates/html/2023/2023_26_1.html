<html xmlns:v="urn:schemas-microsoft-com:vml" xmlns:o="urn:schemas-microsoft-com:office:office" xmlns:x="urn:schemas-microsoft-com:office:excel" xmlns="http://www.w3.org/TR/REC-html40">
 <head>
  <meta http-equiv="Content-Type" content="text/html; charset=utf-8">
  <meta name="ProgId" content="Excel.Sheet">
  <meta name="Generator" content="WPS Office ET">
  <!--[if gte mso 9]>
   <xml>
    <o:DocumentProperties>
     <o:Author>Administrator</o:Author>
     <o:Created>2023-11-06T11:24:51</o:Created>
     <o:LastAuthor>Administrator</o:LastAuthor>
     <o:LastSaved>2023-11-06T11:42:34</o:LastSaved>
    </o:DocumentProperties>
    <o:CustomDocumentProperties>
     <o:ICV dt:dt="string">2D75857153B142328A6AD329F9A1C246_13</o:ICV>
     <o:KSOProductBuildVer dt:dt="string">2052-12.1.0.15712</o:KSOProductBuildVer>
    </o:CustomDocumentProperties>
   </xml>
  <![endif]-->
  <style>
<!-- @page
	{margin:1.00in 0.75in 1.00in 0.75in;
	mso-header-margin:0.50in;
	mso-footer-margin:0.50in;}
tr
	{mso-height-source:auto;
	mso-ruby-visibility:none;}
col
	{mso-width-source:auto;
	mso-ruby-visibility:none;}
br
	{mso-data-placement:same-cell;}
.font0
	{color:#000000;
	font-size:11.0pt;
	font-weight:400;
	font-style:normal;
	text-decoration:none;
	font-family:"宋体";
	mso-generic-font-family:auto;
	mso-font-charset:134;}
.font1
	{color:#000000;
	font-size:18.0pt;
	font-weight:700;
	font-style:normal;
	text-decoration:none;
	font-family:"方正小标宋简体";
	mso-generic-font-family:auto;
	mso-font-charset:134;}
.font2
	{color:#000000;
	font-size:14.0pt;
	font-weight:700;
	font-style:normal;
	text-decoration:none;
	font-family:"宋体";
	mso-generic-font-family:auto;
	mso-font-charset:134;}
.font3
	{color:#0000FF;
	font-size:14.0pt;
	font-weight:400;
	font-style:normal;
	text-decoration:none;
	font-family:"宋体";
	mso-generic-font-family:auto;
	mso-font-charset:134;}
.font4
	{color:#0000FF;
	font-size:11.0pt;
	font-weight:400;
	font-style:normal;
	text-decoration:underline;
	text-underline-style:single;
	font-family:"宋体";
	mso-generic-font-family:auto;
	mso-font-charset:0;}
.font5
	{color:#800080;
	font-size:11.0pt;
	font-weight:400;
	font-style:normal;
	text-decoration:underline;
	text-underline-style:single;
	font-family:"宋体";
	mso-generic-font-family:auto;
	mso-font-charset:0;}
.font6
	{color:#FF0000;
	font-size:11.0pt;
	font-weight:400;
	font-style:normal;
	text-decoration:none;
	font-family:"宋体";
	mso-generic-font-family:auto;
	mso-font-charset:0;}
.font7
	{color:#44546A;
	font-size:18.0pt;
	font-weight:700;
	font-style:normal;
	text-decoration:none;
	font-family:"宋体";
	mso-generic-font-family:auto;
	mso-font-charset:134;}
.font8
	{color:#7F7F7F;
	font-size:11.0pt;
	font-weight:400;
	font-style:italic;
	text-decoration:none;
	font-family:"宋体";
	mso-generic-font-family:auto;
	mso-font-charset:0;}
.font9
	{color:#44546A;
	font-size:15.0pt;
	font-weight:700;
	font-style:normal;
	text-decoration:none;
	font-family:"宋体";
	mso-generic-font-family:auto;
	mso-font-charset:134;}
.font10
	{color:#44546A;
	font-size:13.0pt;
	font-weight:700;
	font-style:normal;
	text-decoration:none;
	font-family:"宋体";
	mso-generic-font-family:auto;
	mso-font-charset:134;}
.font11
	{color:#44546A;
	font-size:11.0pt;
	font-weight:700;
	font-style:normal;
	text-decoration:none;
	font-family:"宋体";
	mso-generic-font-family:auto;
	mso-font-charset:134;}
.font12
	{color:#3F3F76;
	font-size:11.0pt;
	font-weight:400;
	font-style:normal;
	text-decoration:none;
	font-family:"宋体";
	mso-generic-font-family:auto;
	mso-font-charset:0;}
.font13
	{color:#3F3F3F;
	font-size:11.0pt;
	font-weight:700;
	font-style:normal;
	text-decoration:none;
	font-family:"宋体";
	mso-generic-font-family:auto;
	mso-font-charset:0;}
.font14
	{color:#FA7D00;
	font-size:11.0pt;
	font-weight:700;
	font-style:normal;
	text-decoration:none;
	font-family:"宋体";
	mso-generic-font-family:auto;
	mso-font-charset:0;}
.font15
	{color:#FFFFFF;
	font-size:11.0pt;
	font-weight:700;
	font-style:normal;
	text-decoration:none;
	font-family:"宋体";
	mso-generic-font-family:auto;
	mso-font-charset:0;}
.font16
	{color:#FA7D00;
	font-size:11.0pt;
	font-weight:400;
	font-style:normal;
	text-decoration:none;
	font-family:"宋体";
	mso-generic-font-family:auto;
	mso-font-charset:0;}
.font17
	{color:#000000;
	font-size:11.0pt;
	font-weight:700;
	font-style:normal;
	text-decoration:none;
	font-family:"宋体";
	mso-generic-font-family:auto;
	mso-font-charset:0;}
.font18
	{color:#006100;
	font-size:11.0pt;
	font-weight:400;
	font-style:normal;
	text-decoration:none;
	font-family:"宋体";
	mso-generic-font-family:auto;
	mso-font-charset:0;}
.font19
	{color:#9C0006;
	font-size:11.0pt;
	font-weight:400;
	font-style:normal;
	text-decoration:none;
	font-family:"宋体";
	mso-generic-font-family:auto;
	mso-font-charset:0;}
.font20
	{color:#9C6500;
	font-size:11.0pt;
	font-weight:400;
	font-style:normal;
	text-decoration:none;
	font-family:"宋体";
	mso-generic-font-family:auto;
	mso-font-charset:0;}
.font21
	{color:#FFFFFF;
	font-size:11.0pt;
	font-weight:400;
	font-style:normal;
	text-decoration:none;
	font-family:"宋体";
	mso-generic-font-family:auto;
	mso-font-charset:0;}
.font22
	{color:#000000;
	font-size:11.0pt;
	font-weight:400;
	font-style:normal;
	text-decoration:none;
	font-family:"宋体";
	mso-generic-font-family:auto;
	mso-font-charset:0;}
.font23
	{color:#000000;
	font-size:14.0pt;
	font-weight:700;
	font-style:normal;
	text-decoration:none;
	font-family:"Times New Roman";
	mso-generic-font-family:auto;
	mso-font-charset:134;}
.style0
	{mso-number-format:"General";
	text-align:general;
	vertical-align:middle;
	white-space:nowrap;
	mso-rotate:0;
	mso-pattern:auto;
	mso-background-source:auto;
	color:#000000;
	font-size:11.0pt;
	font-weight:400;
	font-style:normal;
	text-decoration:none;
	font-family:宋体;
	mso-generic-font-family:auto;
	mso-font-charset:134;
	border:none;
	mso-protection:locked visible;
	mso-style-name:"常规";
	mso-style-id:0;}
.style16
	{mso-number-format:"_ * \#\,\#\#0\.00_ \;_ * \\-\#\,\#\#0\.00_ \;_ * \0022-\0022??_ \;_ \@_ ";
	mso-style-name:"千位分隔";
	mso-style-id:3;}
.style17
	{mso-number-format:"_ \0022\00A5\0022* \#\,\#\#0\.00_ \;_ \0022\00A5\0022* \\-\#\,\#\#0\.00_ \;_ \0022\00A5\0022* \0022-\0022??_ \;_ \@_ ";
	mso-style-name:"货币";
	mso-style-id:4;}
.style18
	{mso-number-format:"0%";
	mso-style-name:"百分比";
	mso-style-id:5;}
.style19
	{mso-number-format:"_ * \#\,\#\#0_ \;_ * \\-\#\,\#\#0_ \;_ * \0022-\0022_ \;_ \@_ ";
	mso-style-name:"千位分隔[0]";
	mso-style-id:6;}
.style20
	{mso-number-format:"_ \0022\00A5\0022* \#\,\#\#0_ \;_ \0022\00A5\0022* \\-\#\,\#\#0_ \;_ \0022\00A5\0022* \0022-\0022_ \;_ \@_ ";
	mso-style-name:"货币[0]";
	mso-style-id:7;}
.style21
	{color:#0000FF;
	font-size:11.0pt;
	font-weight:400;
	font-style:normal;
	text-decoration:underline;
	text-underline-style:single;
	font-family:宋体;
	mso-generic-font-family:auto;
	mso-font-charset:0;
	mso-style-name:"超链接";
	mso-style-id:8;}
.style22
	{color:#800080;
	font-size:11.0pt;
	font-weight:400;
	font-style:normal;
	text-decoration:underline;
	text-underline-style:single;
	font-family:宋体;
	mso-generic-font-family:auto;
	mso-font-charset:0;
	mso-style-name:"已访问的超链接";
	mso-style-id:9;}
.style23
	{mso-pattern:auto none;
	background:#FFFFCC;
	border:.5pt solid #B2B2B2;
	mso-style-name:"注释";}
.style24
	{color:#FF0000;
	font-size:11.0pt;
	font-weight:400;
	font-style:normal;
	text-decoration:none;
	font-family:宋体;
	mso-generic-font-family:auto;
	mso-font-charset:0;
	mso-style-name:"警告文本";}
.style25
	{color:#44546A;
	font-size:18.0pt;
	font-weight:700;
	font-style:normal;
	text-decoration:none;
	font-family:宋体;
	mso-generic-font-family:auto;
	mso-font-charset:134;
	mso-style-name:"标题";}
.style26
	{color:#7F7F7F;
	font-size:11.0pt;
	font-weight:400;
	font-style:italic;
	text-decoration:none;
	font-family:宋体;
	mso-generic-font-family:auto;
	mso-font-charset:0;
	mso-style-name:"解释性文本";}
.style27
	{color:#44546A;
	font-size:15.0pt;
	font-weight:700;
	font-style:normal;
	text-decoration:none;
	font-family:宋体;
	mso-generic-font-family:auto;
	mso-font-charset:134;
	border-bottom:1.0pt solid #4874CB;
	mso-style-name:"标题 1";}
.style28
	{color:#44546A;
	font-size:13.0pt;
	font-weight:700;
	font-style:normal;
	text-decoration:none;
	font-family:宋体;
	mso-generic-font-family:auto;
	mso-font-charset:134;
	border-bottom:1.0pt solid #4874CB;
	mso-style-name:"标题 2";}
.style29
	{color:#44546A;
	font-size:11.0pt;
	font-weight:700;
	font-style:normal;
	text-decoration:none;
	font-family:宋体;
	mso-generic-font-family:auto;
	mso-font-charset:134;
	border-bottom:1.0pt solid #A3B8E4;
	mso-style-name:"标题 3";}
.style30
	{color:#44546A;
	font-size:11.0pt;
	font-weight:700;
	font-style:normal;
	text-decoration:none;
	font-family:宋体;
	mso-generic-font-family:auto;
	mso-font-charset:134;
	mso-style-name:"标题 4";}
.style31
	{mso-pattern:auto none;
	background:#FFCC99;
	color:#3F3F76;
	font-size:11.0pt;
	font-weight:400;
	font-style:normal;
	text-decoration:none;
	font-family:宋体;
	mso-generic-font-family:auto;
	mso-font-charset:0;
	border:.5pt solid #7F7F7F;
	mso-style-name:"输入";}
.style32
	{mso-pattern:auto none;
	background:#F2F2F2;
	color:#3F3F3F;
	font-size:11.0pt;
	font-weight:700;
	font-style:normal;
	text-decoration:none;
	font-family:宋体;
	mso-generic-font-family:auto;
	mso-font-charset:0;
	border:.5pt solid #3F3F3F;
	mso-style-name:"输出";}
.style33
	{mso-pattern:auto none;
	background:#F2F2F2;
	color:#FA7D00;
	font-size:11.0pt;
	font-weight:700;
	font-style:normal;
	text-decoration:none;
	font-family:宋体;
	mso-generic-font-family:auto;
	mso-font-charset:0;
	border:.5pt solid #7F7F7F;
	mso-style-name:"计算";}
.style34
	{mso-pattern:auto none;
	background:#A5A5A5;
	color:#FFFFFF;
	font-size:11.0pt;
	font-weight:700;
	font-style:normal;
	text-decoration:none;
	font-family:宋体;
	mso-generic-font-family:auto;
	mso-font-charset:0;
	border:2.0pt double #3F3F3F;
	mso-style-name:"检查单元格";}
.style35
	{color:#FA7D00;
	font-size:11.0pt;
	font-weight:400;
	font-style:normal;
	text-decoration:none;
	font-family:宋体;
	mso-generic-font-family:auto;
	mso-font-charset:0;
	border-bottom:2.0pt double #FF8001;
	mso-style-name:"链接单元格";}
.style36
	{color:#000000;
	font-size:11.0pt;
	font-weight:700;
	font-style:normal;
	text-decoration:none;
	font-family:宋体;
	mso-generic-font-family:auto;
	mso-font-charset:0;
	border-top:.5pt solid #4874CB;
	border-bottom:2.0pt double #4874CB;
	mso-style-name:"汇总";}
.style37
	{mso-pattern:auto none;
	background:#C6EFCE;
	color:#006100;
	font-size:11.0pt;
	font-weight:400;
	font-style:normal;
	text-decoration:none;
	font-family:宋体;
	mso-generic-font-family:auto;
	mso-font-charset:0;
	mso-style-name:"好";}
.style38
	{mso-pattern:auto none;
	background:#FFC7CE;
	color:#9C0006;
	font-size:11.0pt;
	font-weight:400;
	font-style:normal;
	text-decoration:none;
	font-family:宋体;
	mso-generic-font-family:auto;
	mso-font-charset:0;
	mso-style-name:"差";}
.style39
	{mso-pattern:auto none;
	background:#FFEB9C;
	color:#9C6500;
	font-size:11.0pt;
	font-weight:400;
	font-style:normal;
	text-decoration:none;
	font-family:宋体;
	mso-generic-font-family:auto;
	mso-font-charset:0;
	mso-style-name:"适中";}
.style40
	{mso-pattern:auto none;
	background:#4874CB;
	color:#FFFFFF;
	font-size:11.0pt;
	font-weight:400;
	font-style:normal;
	text-decoration:none;
	font-family:宋体;
	mso-generic-font-family:auto;
	mso-font-charset:0;
	mso-style-name:"强调文字颜色 1";}
.style41
	{mso-pattern:auto none;
	background:#D9E1F4;
	color:#000000;
	font-size:11.0pt;
	font-weight:400;
	font-style:normal;
	text-decoration:none;
	font-family:宋体;
	mso-generic-font-family:auto;
	mso-font-charset:0;
	mso-style-name:"20% - 强调文字颜色 1";}
.style42
	{mso-pattern:auto none;
	background:#B5C6EA;
	color:#000000;
	font-size:11.0pt;
	font-weight:400;
	font-style:normal;
	text-decoration:none;
	font-family:宋体;
	mso-generic-font-family:auto;
	mso-font-charset:0;
	mso-style-name:"40% - 强调文字颜色 1";}
.style43
	{mso-pattern:auto none;
	background:#91AADF;
	color:#FFFFFF;
	font-size:11.0pt;
	font-weight:400;
	font-style:normal;
	text-decoration:none;
	font-family:宋体;
	mso-generic-font-family:auto;
	mso-font-charset:0;
	mso-style-name:"60% - 强调文字颜色 1";}
.style44
	{mso-pattern:auto none;
	background:#EE822F;
	color:#FFFFFF;
	font-size:11.0pt;
	font-weight:400;
	font-style:normal;
	text-decoration:none;
	font-family:宋体;
	mso-generic-font-family:auto;
	mso-font-charset:0;
	mso-style-name:"强调文字颜色 2";}
.style45
	{mso-pattern:auto none;
	background:#FCE4D3;
	color:#000000;
	font-size:11.0pt;
	font-weight:400;
	font-style:normal;
	text-decoration:none;
	font-family:宋体;
	mso-generic-font-family:auto;
	mso-font-charset:0;
	mso-style-name:"20% - 强调文字颜色 2";}
.style46
	{mso-pattern:auto none;
	background:#F9CBAA;
	color:#000000;
	font-size:11.0pt;
	font-weight:400;
	font-style:normal;
	text-decoration:none;
	font-family:宋体;
	mso-generic-font-family:auto;
	mso-font-charset:0;
	mso-style-name:"40% - 强调文字颜色 2";}
.style47
	{mso-pattern:auto none;
	background:#F4B382;
	color:#FFFFFF;
	font-size:11.0pt;
	font-weight:400;
	font-style:normal;
	text-decoration:none;
	font-family:宋体;
	mso-generic-font-family:auto;
	mso-font-charset:0;
	mso-style-name:"60% - 强调文字颜色 2";}
.style48
	{mso-pattern:auto none;
	background:#F2BA02;
	color:#FFFFFF;
	font-size:11.0pt;
	font-weight:400;
	font-style:normal;
	text-decoration:none;
	font-family:宋体;
	mso-generic-font-family:auto;
	mso-font-charset:0;
	mso-style-name:"强调文字颜色 3";}
.style49
	{mso-pattern:auto none;
	background:#FFF3CA;
	color:#000000;
	font-size:11.0pt;
	font-weight:400;
	font-style:normal;
	text-decoration:none;
	font-family:宋体;
	mso-generic-font-family:auto;
	mso-font-charset:0;
	mso-style-name:"20% - 强调文字颜色 3";}
.style50
	{mso-pattern:auto none;
	background:#FEE796;
	color:#000000;
	font-size:11.0pt;
	font-weight:400;
	font-style:normal;
	text-decoration:none;
	font-family:宋体;
	mso-generic-font-family:auto;
	mso-font-charset:0;
	mso-style-name:"40% - 强调文字颜色 3";}
.style51
	{mso-pattern:auto none;
	background:#FEDB61;
	color:#FFFFFF;
	font-size:11.0pt;
	font-weight:400;
	font-style:normal;
	text-decoration:none;
	font-family:宋体;
	mso-generic-font-family:auto;
	mso-font-charset:0;
	mso-style-name:"60% - 强调文字颜色 3";}
.style52
	{mso-pattern:auto none;
	background:#75BD42;
	color:#FFFFFF;
	font-size:11.0pt;
	font-weight:400;
	font-style:normal;
	text-decoration:none;
	font-family:宋体;
	mso-generic-font-family:auto;
	mso-font-charset:0;
	mso-style-name:"强调文字颜色 4";}
.style53
	{mso-pattern:auto none;
	background:#E3F2D9;
	color:#000000;
	font-size:11.0pt;
	font-weight:400;
	font-style:normal;
	text-decoration:none;
	font-family:宋体;
	mso-generic-font-family:auto;
	mso-font-charset:0;
	mso-style-name:"20% - 强调文字颜色 4";}
.style54
	{mso-pattern:auto none;
	background:#C9E4B4;
	color:#000000;
	font-size:11.0pt;
	font-weight:400;
	font-style:normal;
	text-decoration:none;
	font-family:宋体;
	mso-generic-font-family:auto;
	mso-font-charset:0;
	mso-style-name:"40% - 强调文字颜色 4";}
.style55
	{mso-pattern:auto none;
	background:#ADD88D;
	color:#FFFFFF;
	font-size:11.0pt;
	font-weight:400;
	font-style:normal;
	text-decoration:none;
	font-family:宋体;
	mso-generic-font-family:auto;
	mso-font-charset:0;
	mso-style-name:"60% - 强调文字颜色 4";}
.style56
	{mso-pattern:auto none;
	background:#30C0B4;
	color:#FFFFFF;
	font-size:11.0pt;
	font-weight:400;
	font-style:normal;
	text-decoration:none;
	font-family:宋体;
	mso-generic-font-family:auto;
	mso-font-charset:0;
	mso-style-name:"强调文字颜色 5";}
.style57
	{mso-pattern:auto none;
	background:#D2F4F2;
	color:#000000;
	font-size:11.0pt;
	font-weight:400;
	font-style:normal;
	text-decoration:none;
	font-family:宋体;
	mso-generic-font-family:auto;
	mso-font-charset:0;
	mso-style-name:"20% - 强调文字颜色 5";}
.style58
	{mso-pattern:auto none;
	background:#A8EAE4;
	color:#000000;
	font-size:11.0pt;
	font-weight:400;
	font-style:normal;
	text-decoration:none;
	font-family:宋体;
	mso-generic-font-family:auto;
	mso-font-charset:0;
	mso-style-name:"40% - 强调文字颜色 5";}
.style59
	{mso-pattern:auto none;
	background:#7CDED7;
	color:#FFFFFF;
	font-size:11.0pt;
	font-weight:400;
	font-style:normal;
	text-decoration:none;
	font-family:宋体;
	mso-generic-font-family:auto;
	mso-font-charset:0;
	mso-style-name:"60% - 强调文字颜色 5";}
.style60
	{mso-pattern:auto none;
	background:#E54C5E;
	color:#FFFFFF;
	font-size:11.0pt;
	font-weight:400;
	font-style:normal;
	text-decoration:none;
	font-family:宋体;
	mso-generic-font-family:auto;
	mso-font-charset:0;
	mso-style-name:"强调文字颜色 6";}
.style61
	{mso-pattern:auto none;
	background:#FADADE;
	color:#000000;
	font-size:11.0pt;
	font-weight:400;
	font-style:normal;
	text-decoration:none;
	font-family:宋体;
	mso-generic-font-family:auto;
	mso-font-charset:0;
	mso-style-name:"20% - 强调文字颜色 6";}
.style62
	{mso-pattern:auto none;
	background:#F4B7BE;
	color:#000000;
	font-size:11.0pt;
	font-weight:400;
	font-style:normal;
	text-decoration:none;
	font-family:宋体;
	mso-generic-font-family:auto;
	mso-font-charset:0;
	mso-style-name:"40% - 强调文字颜色 6";}
.style63
	{mso-pattern:auto none;
	background:#EF949F;
	color:#FFFFFF;
	font-size:11.0pt;
	font-weight:400;
	font-style:normal;
	text-decoration:none;
	font-family:宋体;
	mso-generic-font-family:auto;
	mso-font-charset:0;
	mso-style-name:"60% - 强调文字颜色 6";}
td
	{mso-style-parent:style0;
	padding-top:1px;
	padding-right:1px;
	padding-left:1px;
	mso-ignore:padding;
	mso-number-format:"General";
	text-align:general;
	vertical-align:middle;
	white-space:nowrap;
	mso-rotate:0;
	mso-pattern:auto;
	mso-background-source:auto;
	color:#000000;
	font-size:11.0pt;
	font-weight:400;
	font-style:normal;
	text-decoration:none;
	font-family:宋体;
	mso-generic-font-family:auto;
	mso-font-charset:134;
	border:none;
	mso-protection:locked visible;}
.xl65
	{mso-style-parent:style0;
	text-align:center;
	font-size:18.0pt;
	font-weight:700;
	font-family:方正小标宋简体;
	mso-font-charset:134;}
.xl66
	{mso-style-parent:style0;
	text-align:center;
	font-size:18.0pt;
	font-weight:700;
	font-family:方正小标宋简体;
	mso-font-charset:134;}
.xl67
	{mso-style-parent:style0;
	text-align:center;
	white-space:normal;
	font-size:14.0pt;
	font-weight:700;
	mso-font-charset:134;
	border:.5pt solid windowtext;}
.xl68
	{mso-style-parent:style0;
	text-align:center;
	white-space:normal;
	font-size:14.0pt;
	font-weight:700;
	mso-font-charset:134;
	border:.5pt solid windowtext;}
.xl69
	{mso-style-parent:style0;
	text-align:center;
	color:#0000FF;
	font-size:14.0pt;
	mso-font-charset:134;
	border:.5pt solid windowtext;}
.xl70
	{mso-style-parent:style0;
	text-align:center;
	white-space:normal;
	font-size:14.0pt;
	font-weight:700;
	mso-font-charset:134;
	border-left:.5pt solid windowtext;
	border-top:.5pt solid windowtext;
	border-right:.5pt solid windowtext;}
.xl71
	{mso-style-parent:style0;
	text-align:center;
	white-space:normal;
	font-size:14.0pt;
	font-weight:700;
	mso-font-charset:134;
	border-left:.5pt solid windowtext;
	border-right:.5pt solid windowtext;
	border-bottom:.5pt solid windowtext;}
 -->  </style>
  <!--[if gte mso 9]>
   <xml>
    <x:ExcelWorkbook>
     <x:ExcelWorksheets>
      <x:ExcelWorksheet>
       <x:Name>第二十六表说明1</x:Name>
       <x:WorksheetOptions>
        <x:DefaultRowHeight>270</x:DefaultRowHeight>
        <x:Selected/>
        <x:Panes>
         <x:Pane>
          <x:Number>3</x:Number>
          <x:ActiveCol>2</x:ActiveCol>
          <x:ActiveRow>1</x:ActiveRow>
          <x:RangeSelection>C2:G2</x:RangeSelection>
         </x:Pane>
        </x:Panes>
        <x:ProtectContents>False</x:ProtectContents>
        <x:ProtectObjects>False</x:ProtectObjects>
        <x:ProtectScenarios>False</x:ProtectScenarios>
        <x:PageBreakZoom>100</x:PageBreakZoom>
        <x:Print>
         <x:PaperSizeIndex>9</x:PaperSizeIndex>
        </x:Print>
       </x:WorksheetOptions>
      </x:ExcelWorksheet>
     </x:ExcelWorksheets>
     <x:ProtectStructure>False</x:ProtectStructure>
     <x:ProtectWindows>False</x:ProtectWindows>
     <x:WindowHeight>12375</x:WindowHeight>
     <x:WindowWidth>27945</x:WindowWidth>
    </x:ExcelWorkbook>
    <x:SupBook>
     <x:Path>G:\A-重庆南华中天\A-工作\A-项目\A-贵州\文档\2023年党内统计年报表征求意见\报表说明\</x:Path>
    </x:SupBook>
   </xml>
  <![endif]-->
 </head>
 <body link="blue" vlink="purple" style="margin: 0;">
  <table width="1146" border="0" cellpadding="0" cellspacing="0" style='width:100%;border-collapse:collapse;table-layout:fixed;overflow-y: auto;'>
   <col width="172" style='mso-width-source:userset;mso-width-alt:5504;'/>
   <col width="117" style='mso-width-source:userset;mso-width-alt:3744;'/>
   <col width="68" style='mso-width-source:userset;mso-width-alt:2176;'/>
   <col width="72" style='width:54.00pt;'/>
   <col width="63" style='mso-width-source:userset;mso-width-alt:2016;'/>
   <col width="66" span="2" style='mso-width-source:userset;mso-width-alt:2112;'/>
   <col width="72" span="9" style='width:54.00pt;'/>
   <tr height="80" style='height:60.00pt;mso-height-source:userset;mso-height-alt:1200;'>
    <td class="xl65" height="80" width="1272" colspan="16" style='height:60.00pt;width:954.00pt;border-right:none;border-bottom:none;' >第二十六表<span style='mso-spacerun:yes;'>&nbsp; </span>“事业单位”党组织数量情况说明（A9）</td>
   </tr>
   <tr height="80" style='height:60.00pt;mso-height-source:userset;mso-height-alt:1200;' id="r1">
    <td class="xl67" height="160" rowspan="2" style='height:120.00pt;border-right:.5pt solid windowtext;border-bottom:.5pt solid windowtext;' >项 <font class="font23"> </font><font class="font2">目</font></td>
    <td class="xl67" rowspan="2" style='border-right:.5pt solid windowtext;border-bottom:.5pt solid windowtext;' >总 <font class="font23"> </font><font class="font2">数</font></td>
    <td class="xl67" colspan="5" style='border-right:.5pt solid windowtext;border-bottom:.5pt solid windowtext;' >教育</td>
    <td class="xl67" colspan="3" style='border-right:.5pt solid windowtext;border-bottom:.5pt solid windowtext;' >科研</td>
    <td class="xl67" rowspan="2" style='border-right:.5pt solid windowtext;border-bottom:.5pt solid windowtext;' >卫<br/><br/><font class="font23"><span style='mso-spacerun:yes;'>&nbsp;</span></font><font class="font2">生</font></td>
    <td class="xl67" rowspan="2" style='border-right:.5pt solid windowtext;border-bottom:.5pt solid windowtext;' >新闻<br/>出版<br/>广电</td>
    <td class="xl70" rowspan="2" style='border-right:.5pt solid windowtext;border-bottom:.5pt solid windowtext;' >文化<br/>艺术</td>
    <td class="xl67" rowspan="2" style='border-right:.5pt solid windowtext;border-bottom:.5pt solid windowtext;' >体 <font class="font23"> <br/><br/></font><font class="font2">育</font></td>
    <td class="xl67" rowspan="2" style='border-right:.5pt solid windowtext;border-bottom:.5pt solid windowtext;' >支持<br/>保障<br/>机关<br/>行使<br/>职能</td>
    <td class="xl67" rowspan="2" style='border-right:.5pt solid windowtext;border-bottom:.5pt solid windowtext;' >其 <font class="font23"> </font><font class="font2">他</font></td>
   </tr>
   <tr height="80" style='height:60.00pt;mso-height-source:userset;mso-height-alt:1200;'>
    <td class="xl67" >高等<br/>教育</td>
    <td class="xl67" >高中<br/>阶段<br/>教育</td>
    <td class="xl67" >义务<br/>教育</td>
    <td class="xl67" >学前<br/>教育</td>
    <td class="xl67" >其他</td>
    <td class="xl67" >基础性<br/>科研</td>
    <td class="xl67" >综合性<br/>科研</td>
    <td class="xl67" >其他</td>
   </tr>
   <tr height="60" style='height:45.00pt;mso-height-source:userset;mso-height-alt:900;'>
    <td class="xl67" height="60" style='height:45.00pt;' >合<font class="font23"><span style='mso-spacerun:yes;'>&nbsp; </span></font><font class="font2">计</font></td>
    <td class="xl69" onclick="sendMessage(this)" table-name="2023_26_1" table-cell="0_1_1" style="text-align: center;overflow: hidden;" >#(table0.cell_1_1 ?? == 0 ? '' : table0.cell_1_1??)</span></td>
    <td class="xl69" onclick="sendMessage(this)" table-name="2023_26_1" table-cell="0_1_2" style="text-align: center;overflow: hidden;" >#(table0.cell_1_2 ?? == 0 ? '' : table0.cell_1_2??)</span></td>
    <td class="xl69" onclick="sendMessage(this)" table-name="2023_26_1" table-cell="0_1_3" style="text-align: center;overflow: hidden;" >#(table0.cell_1_3 ?? == 0 ? '' : table0.cell_1_3??)</span></td>
    <td class="xl69" onclick="sendMessage(this)" table-name="2023_26_1" table-cell="0_1_4" style="text-align: center;overflow: hidden;" >#(table0.cell_1_4 ?? == 0 ? '' : table0.cell_1_4??)</span></td>
    <td class="xl69" onclick="sendMessage(this)" table-name="2023_26_1" table-cell="0_1_5" style="text-align: center;overflow: hidden;" >#(table0.cell_1_5 ?? == 0 ? '' : table0.cell_1_5??)</span></td>
    <td class="xl69" onclick="sendMessage(this)" table-name="2023_26_1" table-cell="0_1_6" style="text-align: center;overflow: hidden;" >#(table0.cell_1_6 ?? == 0 ? '' : table0.cell_1_6??)</span></td>
    <td class="xl69" onclick="sendMessage(this)" table-name="2023_26_1" table-cell="0_1_7" style="text-align: center;overflow: hidden;" >#(table0.cell_1_7 ?? == 0 ? '' : table0.cell_1_7??)</span></td>
    <td class="xl69" onclick="sendMessage(this)" table-name="2023_26_1" table-cell="0_1_8" style="text-align: center;overflow: hidden;" >#(table0.cell_1_8 ?? == 0 ? '' : table0.cell_1_8??)</span></td>
    <td class="xl69" onclick="sendMessage(this)" table-name="2023_26_1" table-cell="0_1_9" style="text-align: center;overflow: hidden;" >#(table0.cell_1_9 ?? == 0 ? '' : table0.cell_1_9??)</span></td>
    <td class="xl69" onclick="sendMessage(this)" table-name="2023_26_1" table-cell="0_1_10" style="text-align: center;overflow: hidden;" >#(table0.cell_1_10 ?? == 0 ? '' : table0.cell_1_10??)</span></td>
    <td class="xl69" onclick="sendMessage(this)" table-name="2023_26_1" table-cell="0_1_11" style="text-align: center;overflow: hidden;" >#(table0.cell_1_11 ?? == 0 ? '' : table0.cell_1_11??)</span></td>
    <td class="xl69" onclick="sendMessage(this)" table-name="2023_26_1" table-cell="0_1_12" style="text-align: center;overflow: hidden;" >#(table0.cell_1_12 ?? == 0 ? '' : table0.cell_1_12??)</span></td>
    <td class="xl69" onclick="sendMessage(this)" table-name="2023_26_1" table-cell="0_1_13" style="text-align: center;overflow: hidden;" >#(table0.cell_1_13 ?? == 0 ? '' : table0.cell_1_13??)</span></td>
    <td class="xl69" onclick="sendMessage(this)" table-name="2023_26_1" table-cell="0_1_14" style="text-align: center;overflow: hidden;" >#(table0.cell_1_14 ?? == 0 ? '' : table0.cell_1_14??)</span></td>
    <td class="xl69" onclick="sendMessage(this)" table-name="2023_26_1" table-cell="0_1_15" style="text-align: center;overflow: hidden;" >#(table0.cell_1_15 ?? == 0 ? '' : table0.cell_1_15??)</span></td>
   </tr>
   <tr height="60" style='height:45.00pt;mso-height-source:userset;mso-height-alt:900;'>
    <td class="xl67" height="60" style='height:45.00pt;' >党<font class="font23"><span style='mso-spacerun:yes;'>&nbsp; </span></font><font class="font2">委</font></td>
    <td class="xl69" onclick="sendMessage(this)" table-name="2023_26_1" table-cell="0_2_1" style="text-align: center;overflow: hidden;" >#(table0.cell_2_1 ?? == 0 ? '' : table0.cell_2_1??)</span></td>
    <td class="xl69" onclick="sendMessage(this)" table-name="2023_26_1" table-cell="0_2_2" style="text-align: center;overflow: hidden;" >#(table0.cell_2_2 ?? == 0 ? '' : table0.cell_2_2??)</span></td>
    <td class="xl69" onclick="sendMessage(this)" table-name="2023_26_1" table-cell="0_2_3" style="text-align: center;overflow: hidden;" >#(table0.cell_2_3 ?? == 0 ? '' : table0.cell_2_3??)</span></td>
    <td class="xl69" onclick="sendMessage(this)" table-name="2023_26_1" table-cell="0_2_4" style="text-align: center;overflow: hidden;" >#(table0.cell_2_4 ?? == 0 ? '' : table0.cell_2_4??)</span></td>
    <td class="xl69" onclick="sendMessage(this)" table-name="2023_26_1" table-cell="0_2_5" style="text-align: center;overflow: hidden;" >#(table0.cell_2_5 ?? == 0 ? '' : table0.cell_2_5??)</span></td>
    <td class="xl69" onclick="sendMessage(this)" table-name="2023_26_1" table-cell="0_2_6" style="text-align: center;overflow: hidden;" >#(table0.cell_2_6 ?? == 0 ? '' : table0.cell_2_6??)</span></td>
    <td class="xl69" onclick="sendMessage(this)" table-name="2023_26_1" table-cell="0_2_7" style="text-align: center;overflow: hidden;" >#(table0.cell_2_7 ?? == 0 ? '' : table0.cell_2_7??)</span></td>
    <td class="xl69" onclick="sendMessage(this)" table-name="2023_26_1" table-cell="0_2_8" style="text-align: center;overflow: hidden;" >#(table0.cell_2_8 ?? == 0 ? '' : table0.cell_2_8??)</span></td>
    <td class="xl69" onclick="sendMessage(this)" table-name="2023_26_1" table-cell="0_2_9" style="text-align: center;overflow: hidden;" >#(table0.cell_2_9 ?? == 0 ? '' : table0.cell_2_9??)</span></td>
    <td class="xl69" onclick="sendMessage(this)" table-name="2023_26_1" table-cell="0_2_10" style="text-align: center;overflow: hidden;" >#(table0.cell_2_10 ?? == 0 ? '' : table0.cell_2_10??)</span></td>
    <td class="xl69" onclick="sendMessage(this)" table-name="2023_26_1" table-cell="0_2_11" style="text-align: center;overflow: hidden;" >#(table0.cell_2_11 ?? == 0 ? '' : table0.cell_2_11??)</span></td>
    <td class="xl69" onclick="sendMessage(this)" table-name="2023_26_1" table-cell="0_2_12" style="text-align: center;overflow: hidden;" >#(table0.cell_2_12 ?? == 0 ? '' : table0.cell_2_12??)</span></td>
    <td class="xl69" onclick="sendMessage(this)" table-name="2023_26_1" table-cell="0_2_13" style="text-align: center;overflow: hidden;" >#(table0.cell_2_13 ?? == 0 ? '' : table0.cell_2_13??)</span></td>
    <td class="xl69" onclick="sendMessage(this)" table-name="2023_26_1" table-cell="0_2_14" style="text-align: center;overflow: hidden;" >#(table0.cell_2_14 ?? == 0 ? '' : table0.cell_2_14??)</span></td>
    <td class="xl69" onclick="sendMessage(this)" table-name="2023_26_1" table-cell="0_2_15" style="text-align: center;overflow: hidden;" >#(table0.cell_2_15 ?? == 0 ? '' : table0.cell_2_15??)</span></td>
   </tr>
   <tr height="60" style='height:45.00pt;mso-height-source:userset;mso-height-alt:900;'>
    <td class="xl67" height="60" style='height:45.00pt;' >党总支</td>
    <td class="xl69" onclick="sendMessage(this)" table-name="2023_26_1" table-cell="0_3_1" style="text-align: center;overflow: hidden;" >#(table0.cell_3_1 ?? == 0 ? '' : table0.cell_3_1??)</span></td>
    <td class="xl69" onclick="sendMessage(this)" table-name="2023_26_1" table-cell="0_3_2" style="text-align: center;overflow: hidden;" >#(table0.cell_3_2 ?? == 0 ? '' : table0.cell_3_2??)</span></td>
    <td class="xl69" onclick="sendMessage(this)" table-name="2023_26_1" table-cell="0_3_3" style="text-align: center;overflow: hidden;" >#(table0.cell_3_3 ?? == 0 ? '' : table0.cell_3_3??)</span></td>
    <td class="xl69" onclick="sendMessage(this)" table-name="2023_26_1" table-cell="0_3_4" style="text-align: center;overflow: hidden;" >#(table0.cell_3_4 ?? == 0 ? '' : table0.cell_3_4??)</span></td>
    <td class="xl69" onclick="sendMessage(this)" table-name="2023_26_1" table-cell="0_3_5" style="text-align: center;overflow: hidden;" >#(table0.cell_3_5 ?? == 0 ? '' : table0.cell_3_5??)</span></td>
    <td class="xl69" onclick="sendMessage(this)" table-name="2023_26_1" table-cell="0_3_6" style="text-align: center;overflow: hidden;" >#(table0.cell_3_6 ?? == 0 ? '' : table0.cell_3_6??)</span></td>
    <td class="xl69" onclick="sendMessage(this)" table-name="2023_26_1" table-cell="0_3_7" style="text-align: center;overflow: hidden;" >#(table0.cell_3_7 ?? == 0 ? '' : table0.cell_3_7??)</span></td>
    <td class="xl69" onclick="sendMessage(this)" table-name="2023_26_1" table-cell="0_3_8" style="text-align: center;overflow: hidden;" >#(table0.cell_3_8 ?? == 0 ? '' : table0.cell_3_8??)</span></td>
    <td class="xl69" onclick="sendMessage(this)" table-name="2023_26_1" table-cell="0_3_9" style="text-align: center;overflow: hidden;" >#(table0.cell_3_9 ?? == 0 ? '' : table0.cell_3_9??)</span></td>
    <td class="xl69" onclick="sendMessage(this)" table-name="2023_26_1" table-cell="0_3_10" style="text-align: center;overflow: hidden;" >#(table0.cell_3_10 ?? == 0 ? '' : table0.cell_3_10??)</span></td>
    <td class="xl69" onclick="sendMessage(this)" table-name="2023_26_1" table-cell="0_3_11" style="text-align: center;overflow: hidden;" >#(table0.cell_3_11 ?? == 0 ? '' : table0.cell_3_11??)</span></td>
    <td class="xl69" onclick="sendMessage(this)" table-name="2023_26_1" table-cell="0_3_12" style="text-align: center;overflow: hidden;" >#(table0.cell_3_12 ?? == 0 ? '' : table0.cell_3_12??)</span></td>
    <td class="xl69" onclick="sendMessage(this)" table-name="2023_26_1" table-cell="0_3_13" style="text-align: center;overflow: hidden;" >#(table0.cell_3_13 ?? == 0 ? '' : table0.cell_3_13??)</span></td>
    <td class="xl69" onclick="sendMessage(this)" table-name="2023_26_1" table-cell="0_3_14" style="text-align: center;overflow: hidden;" >#(table0.cell_3_14 ?? == 0 ? '' : table0.cell_3_14??)</span></td>
    <td class="xl69" onclick="sendMessage(this)" table-name="2023_26_1" table-cell="0_3_15" style="text-align: center;overflow: hidden;" >#(table0.cell_3_15 ?? == 0 ? '' : table0.cell_3_15??)</span></td>
   </tr>
   <tr height="60" style='height:45.00pt;mso-height-source:userset;mso-height-alt:900;'>
    <td class="xl67" height="60" style='height:45.00pt;' >支<font class="font23"><span style='mso-spacerun:yes;'>&nbsp; </span></font><font class="font2">部</font></td>
    <td class="xl69" onclick="sendMessage(this)" table-name="2023_26_1" table-cell="0_4_1" style="text-align: center;overflow: hidden;" >#(table0.cell_4_1 ?? == 0 ? '' : table0.cell_4_1??)</span></td>
    <td class="xl69" onclick="sendMessage(this)" table-name="2023_26_1" table-cell="0_4_2" style="text-align: center;overflow: hidden;" >#(table0.cell_4_2 ?? == 0 ? '' : table0.cell_4_2??)</span></td>
    <td class="xl69" onclick="sendMessage(this)" table-name="2023_26_1" table-cell="0_4_3" style="text-align: center;overflow: hidden;" >#(table0.cell_4_3 ?? == 0 ? '' : table0.cell_4_3??)</span></td>
    <td class="xl69" onclick="sendMessage(this)" table-name="2023_26_1" table-cell="0_4_4" style="text-align: center;overflow: hidden;" >#(table0.cell_4_4 ?? == 0 ? '' : table0.cell_4_4??)</span></td>
    <td class="xl69" onclick="sendMessage(this)" table-name="2023_26_1" table-cell="0_4_5" style="text-align: center;overflow: hidden;" >#(table0.cell_4_5 ?? == 0 ? '' : table0.cell_4_5??)</span></td>
    <td class="xl69" onclick="sendMessage(this)" table-name="2023_26_1" table-cell="0_4_6" style="text-align: center;overflow: hidden;" >#(table0.cell_4_6 ?? == 0 ? '' : table0.cell_4_6??)</span></td>
    <td class="xl69" onclick="sendMessage(this)" table-name="2023_26_1" table-cell="0_4_7" style="text-align: center;overflow: hidden;" >#(table0.cell_4_7 ?? == 0 ? '' : table0.cell_4_7??)</span></td>
    <td class="xl69" onclick="sendMessage(this)" table-name="2023_26_1" table-cell="0_4_8" style="text-align: center;overflow: hidden;" >#(table0.cell_4_8 ?? == 0 ? '' : table0.cell_4_8??)</span></td>
    <td class="xl69" onclick="sendMessage(this)" table-name="2023_26_1" table-cell="0_4_9" style="text-align: center;overflow: hidden;" >#(table0.cell_4_9 ?? == 0 ? '' : table0.cell_4_9??)</span></td>
    <td class="xl69" onclick="sendMessage(this)" table-name="2023_26_1" table-cell="0_4_10" style="text-align: center;overflow: hidden;" >#(table0.cell_4_10 ?? == 0 ? '' : table0.cell_4_10??)</span></td>
    <td class="xl69" onclick="sendMessage(this)" table-name="2023_26_1" table-cell="0_4_11" style="text-align: center;overflow: hidden;" >#(table0.cell_4_11 ?? == 0 ? '' : table0.cell_4_11??)</span></td>
    <td class="xl69" onclick="sendMessage(this)" table-name="2023_26_1" table-cell="0_4_12" style="text-align: center;overflow: hidden;" >#(table0.cell_4_12 ?? == 0 ? '' : table0.cell_4_12??)</span></td>
    <td class="xl69" onclick="sendMessage(this)" table-name="2023_26_1" table-cell="0_4_13" style="text-align: center;overflow: hidden;" >#(table0.cell_4_13 ?? == 0 ? '' : table0.cell_4_13??)</span></td>
    <td class="xl69" onclick="sendMessage(this)" table-name="2023_26_1" table-cell="0_4_14" style="text-align: center;overflow: hidden;" >#(table0.cell_4_14 ?? == 0 ? '' : table0.cell_4_14??)</span></td>
    <td class="xl69" onclick="sendMessage(this)" table-name="2023_26_1" table-cell="0_4_15" style="text-align: center;overflow: hidden;" >#(table0.cell_4_15 ?? == 0 ? '' : table0.cell_4_15??)</span></td>
   </tr>
   <![if supportMisalignedColumns]>
    <tr width="0" style='display:none;'>
     <td width="172" style='width:129;'></td>
     <td width="117" style='width:88;'></td>
     <td width="68" style='width:51;'></td>
     <td width="63" style='width:47;'></td>
     <td width="66" style='width:50;'></td>
    </tr>
   <![endif]>
  </table>
<script language="javascript" type="text/javascript">
 function ChangeRowspanHiddenData()
 {
  var node;
  var params=["r1"];
  for (var i = 0;i < params.length; i++)
  {
   node = document.getElementById(params[i]);
   if (node != null)
   {
    node.style.display = "";
   }
  }
 }
 ChangeRowspanHiddenData();
</script>
<script type="text/javascript">function sendMessage(cell){var obj=new Object();var tableName=cell.getAttribute("table-name");var tableCellIndex=cell.getAttribute("table-cell");obj['tableName']=tableName;obj['tableCellIndex']=tableCellIndex;console.log(obj);window.parent.postMessage(JSON.stringify(obj))}</script>
 </body>
</html>
