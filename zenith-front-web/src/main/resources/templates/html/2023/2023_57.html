<!doctype html public "-//w3c//dtd xhtml 1.0 transitional//en" "http://www.w3.org/tr/xhtml1/dtd/xhtml1-transitional.dtd">
<html xmlns:v="urn:schemas-microsoft-com:vml"
xmlns:o="urn:schemas-microsoft-com:office:office"
xmlns:x="urn:schemas-microsoft-com:office:excel"
xmlns="http://www.w3.org/TR/REC-html40">

<head>
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<meta name="ProgId" content="Excel.Sheet">
<meta name="Generator" content="Aspose.Cell 20.7.0">
<link rel="File-List" href="57_files/filelist.xml">
<link rel="Edit-Time-Data" href="57_files/editdata.mso">
<link rel="OLE-Object-Data" href="57_files/oledata.mso">
<!--[if gte mso 9]><xml>
 <o:DocumentProperties>
  <o:Author>dyx</o:Author>
  <o:Created>2023-11-27T10:43:43Z</o:Created>
  <o:LastSaved>2023-11-27T10:46:55Z</o:LastSaved>
</o:DocumentProperties>
</xml><![endif]-->
<style>
<!--table
 {mso-displayed-decimal-separator:"\.";
 mso-displayed-thousand-separator:"\,";}
@page
 {
 mso-header-data:"";
 mso-footer-data:"";
 margin:0in 0in 0in 0.3in;
 mso-header-margin:0.5in;
 mso-footer-margin:0.5in;
 mso-page-orientation:Landscape;
 mso-horizontal-page-align:center;
 mso-vertical-page-align:center;
 }
tr
 {mso-height-source:auto;
 mso-ruby-visibility:none;}
col
 {mso-width-source:auto;
 mso-ruby-visibility:none;}
br
 {mso-data-placement:same-cell;}
ruby
 {ruby-align:left;}
.style0
 {
 mso-number-format:General;
 text-align:general;
 vertical-align:bottom;
 white-space:nowrap;
 background:auto;
 mso-pattern:auto;
 color:#000000;
 font-size:10pt;
 font-weight:400;
 font-style:normal;
 font-family:"Arial","sans-serif";
 border:none;
 mso-protection:locked visible;
 mso-style-name:Normal;
 mso-style-id:0;}
.font0
 {
 color:#000000;
 font-size:10pt;
 font-weight:400;
 font-style:normal;
 font-family:"Arial","sans-serif"; }
.font1
 {
 color:#000000;
 font-size:10pt;
 font-weight:400;
 font-style:normal;
 font-family:"Arial","sans-serif"; }
.font2
 {
 color:#000000;
 font-size:14pt;
 font-weight:700;
 font-style:normal;
 font-family:"宋体","sans-serif"; }
.font3
 {
 color:#000000;
 font-size:10pt;
 font-weight:400;
 font-style:normal;
 font-family:"宋体","sans-serif"; }
.font4
 {
 color:#000000;
 font-size:9pt;
 font-weight:400;
 font-style:normal;
 font-family:"宋体","sans-serif"; }
.font5
 {
 color:#000000;
 font-size:10pt;
 font-weight:700;
 font-style:normal;
 font-family:"宋体","sans-serif"; }
.font6
 {
 color:#000000;
 font-size:11pt;
 font-weight:400;
 font-style:normal;
 font-family:"宋体","sans-serif"; }
.font7
 {
 color:#0000FF;
 font-size:11pt;
 font-weight:400;
 font-style:normal;
 text-decoration:underline;
 font-family:"宋体","sans-serif"; }
.font8
 {
 color:#800080;
 font-size:11pt;
 font-weight:400;
 font-style:normal;
 text-decoration:underline;
 font-family:"宋体","sans-serif"; }
.font9
 {
 color:#FF0000;
 font-size:11pt;
 font-weight:400;
 font-style:normal;
 font-family:"宋体","sans-serif"; }
.font10
 {
 color:#44546A;
 font-size:18pt;
 font-weight:700;
 font-style:normal;
 font-family:"宋体","sans-serif"; }
.font11
 {
 color:#7F7F7F;
 font-size:11pt;
 font-weight:400;
 font-style:italic;
 font-family:"宋体","sans-serif"; }
.font12
 {
 color:#44546A;
 font-size:15pt;
 font-weight:700;
 font-style:normal;
 font-family:"宋体","sans-serif"; }
.font13
 {
 color:#44546A;
 font-size:13pt;
 font-weight:700;
 font-style:normal;
 font-family:"宋体","sans-serif"; }
.font14
 {
 color:#44546A;
 font-size:11pt;
 font-weight:700;
 font-style:normal;
 font-family:"宋体","sans-serif"; }
.font15
 {
 color:#3F3F76;
 font-size:11pt;
 font-weight:400;
 font-style:normal;
 font-family:"宋体","sans-serif"; }
.font16
 {
 color:#3F3F3F;
 font-size:11pt;
 font-weight:700;
 font-style:normal;
 font-family:"宋体","sans-serif"; }
.font17
 {
 color:#FA7D00;
 font-size:11pt;
 font-weight:700;
 font-style:normal;
 font-family:"宋体","sans-serif"; }
.font18
 {
 color:#FFFFFF;
 font-size:11pt;
 font-weight:700;
 font-style:normal;
 font-family:"宋体","sans-serif"; }
.font19
 {
 color:#FA7D00;
 font-size:11pt;
 font-weight:400;
 font-style:normal;
 font-family:"宋体","sans-serif"; }
.font20
 {
 color:#000000;
 font-size:11pt;
 font-weight:700;
 font-style:normal;
 font-family:"宋体","sans-serif"; }
.font21
 {
 color:#006100;
 font-size:11pt;
 font-weight:400;
 font-style:normal;
 font-family:"宋体","sans-serif"; }
.font22
 {
 color:#9C0006;
 font-size:11pt;
 font-weight:400;
 font-style:normal;
 font-family:"宋体","sans-serif"; }
.font23
 {
 color:#9C6500;
 font-size:11pt;
 font-weight:400;
 font-style:normal;
 font-family:"宋体","sans-serif"; }
.font24
 {
 color:#FFFFFF;
 font-size:11pt;
 font-weight:400;
 font-style:normal;
 font-family:"宋体","sans-serif"; }
td
 {mso-style-parent:style0;
 mso-number-format:General;
 text-align:general;
 vertical-align:bottom;
 white-space:nowrap;
 background:auto;
 mso-pattern:auto;
 color:#000000;
 font-size:10pt;
 font-weight:400;
 font-style:normal;
 font-family:"Arial","sans-serif";
 border:none;
 mso-protection:locked visible;
 mso-ignore:padding;}
.style0
 {
 text-align:general;
 vertical-align:bottom;
 white-space:nowrap;
 background:auto;
 mso-pattern:auto;
 font-size:10pt;
 font-weight:400;
 font-style:normal;
 font-family:"Arial","sans-serif";
 mso-protection:locked visible;
 mso-style-name:"Normal";
 }
.style1
 {
 text-align:general;
 vertical-align:middle;
 white-space:nowrap;
 background:auto;
 mso-pattern:auto;
 color:#000000;
 font-size:10pt;
 font-weight:400;
 font-style:normal;
 font-family:"Arial","sans-serif";
 mso-protection:locked visible;
 }
.style2
 {
 text-align:general;
 vertical-align:middle;
 white-space:nowrap;
 background:auto;
 mso-pattern:auto;
 color:#000000;
 font-size:10pt;
 font-weight:400;
 font-style:normal;
 font-family:"Arial","sans-serif";
 mso-protection:locked visible;
 }
.style3
 {
 text-align:general;
 vertical-align:middle;
 white-space:nowrap;
 background:auto;
 mso-pattern:auto;
 color:#000000;
 font-size:10pt;
 font-weight:400;
 font-style:normal;
 font-family:"Arial","sans-serif";
 mso-protection:locked visible;
 }
.style4
 {
 text-align:general;
 vertical-align:middle;
 white-space:nowrap;
 background:auto;
 mso-pattern:auto;
 color:#000000;
 font-size:10pt;
 font-weight:400;
 font-style:normal;
 font-family:"Arial","sans-serif";
 mso-protection:locked visible;
 }
.style5
 {
 text-align:general;
 vertical-align:middle;
 white-space:nowrap;
 background:auto;
 mso-pattern:auto;
 color:#000000;
 font-size:10pt;
 font-weight:400;
 font-style:normal;
 font-family:"Arial","sans-serif";
 mso-protection:locked visible;
 }
.style6
 {
 text-align:general;
 vertical-align:middle;
 white-space:nowrap;
 background:auto;
 mso-pattern:auto;
 color:#000000;
 font-size:10pt;
 font-weight:400;
 font-style:normal;
 font-family:"Arial","sans-serif";
 mso-protection:locked visible;
 }
.style7
 {
 text-align:general;
 vertical-align:middle;
 white-space:nowrap;
 background:auto;
 mso-pattern:auto;
 color:#000000;
 font-size:10pt;
 font-weight:400;
 font-style:normal;
 font-family:"Arial","sans-serif";
 mso-protection:locked visible;
 }
.style8
 {
 text-align:general;
 vertical-align:middle;
 white-space:nowrap;
 background:auto;
 mso-pattern:auto;
 color:#000000;
 font-size:10pt;
 font-weight:400;
 font-style:normal;
 font-family:"Arial","sans-serif";
 mso-protection:locked visible;
 }
.style9
 {
 text-align:general;
 vertical-align:middle;
 white-space:nowrap;
 background:auto;
 mso-pattern:auto;
 color:#000000;
 font-size:10pt;
 font-weight:400;
 font-style:normal;
 font-family:"Arial","sans-serif";
 mso-protection:locked visible;
 }
.style10
 {
 text-align:general;
 vertical-align:middle;
 white-space:nowrap;
 background:auto;
 mso-pattern:auto;
 color:#000000;
 font-size:10pt;
 font-weight:400;
 font-style:normal;
 font-family:"Arial","sans-serif";
 mso-protection:locked visible;
 }
.style11
 {
 text-align:general;
 vertical-align:middle;
 white-space:nowrap;
 background:auto;
 mso-pattern:auto;
 color:#000000;
 font-size:10pt;
 font-weight:400;
 font-style:normal;
 font-family:"Arial","sans-serif";
 mso-protection:locked visible;
 }
.style12
 {
 text-align:general;
 vertical-align:middle;
 white-space:nowrap;
 background:auto;
 mso-pattern:auto;
 color:#000000;
 font-size:10pt;
 font-weight:400;
 font-style:normal;
 font-family:"Arial","sans-serif";
 mso-protection:locked visible;
 }
.style13
 {
 text-align:general;
 vertical-align:middle;
 white-space:nowrap;
 background:auto;
 mso-pattern:auto;
 color:#000000;
 font-size:10pt;
 font-weight:400;
 font-style:normal;
 font-family:"Arial","sans-serif";
 mso-protection:locked visible;
 }
.style14
 {
 text-align:general;
 vertical-align:middle;
 white-space:nowrap;
 background:auto;
 mso-pattern:auto;
 color:#000000;
 font-size:10pt;
 font-weight:400;
 font-style:normal;
 font-family:"Arial","sans-serif";
 mso-protection:locked visible;
 }
.x15
 {
 mso-style-parent:style0;
 mso-number-format:General;
 text-align:general;
 vertical-align:bottom;
 white-space:nowrap;
 background:auto;
 mso-pattern:auto;
 font-size:10pt;
 font-weight:400;
 font-style:normal;
 font-family:"Arial","sans-serif";
 mso-protection:locked visible;
 }
.x16
 {
 mso-style-parent:style0;
 mso-number-format:General;
 text-align:general;
 vertical-align:bottom;
 white-space:nowrap;
 background:auto;
 mso-pattern:auto;
 font-size:10pt;
 font-weight:400;
 font-style:normal;
 font-family:"Arial","sans-serif";
 mso-protection:locked visible;
 }
.x17
 {
 mso-style-parent:style0;
 mso-number-format:"\@";
 text-align:center;
 vertical-align:middle;
 white-space:nowrap;
 background:#FFFFFF;
 mso-pattern:auto none;
 font-size:14pt;
 font-weight:700;
 font-style:normal;
 font-family:"宋体","sans-serif";
 mso-protection:locked visible;
 }
.x18
 {
 mso-style-parent:style0;
 mso-number-format:"\@";
 text-align:left;
 vertical-align:middle;
 white-space:nowrap;
 background:#FFFFFF;
 mso-pattern:auto none;
 font-size:10pt;
 font-weight:400;
 font-style:normal;
 font-family:"宋体","sans-serif";
 mso-protection:locked visible;
 }
.x19
 {
 mso-style-parent:style0;
 mso-number-format:"\@";
 text-align:left;
 vertical-align:middle;
 white-space:nowrap;
 background:#FFFFFF;
 mso-pattern:auto none;
 font-size:9pt;
 font-weight:400;
 font-style:normal;
 font-family:"宋体","sans-serif";
 mso-protection:locked visible;
 }
.x20
 {
 mso-style-parent:style0;
 mso-number-format:"\@";
 text-align:center;
 vertical-align:middle;
 white-space:nowrap;
 background:#FFFFFF;
 mso-pattern:auto none;
 font-size:10pt;
 font-weight:400;
 font-style:normal;
 font-family:"宋体","sans-serif";
 border-top:2px solid #000000;
 border-right:none;
 border-bottom:none;
 border-left:none;
 mso-diagonal-down:none;
 mso-diagonal-up:none;
 mso-protection:locked visible;
 }
.x21
 {
 mso-style-parent:style0;
 mso-number-format:"\@";
 text-align:center;
 vertical-align:middle;
 white-space:nowrap;
 background:#FFFFFF;
 mso-pattern:auto none;
 font-size:10pt;
 font-weight:400;
 font-style:normal;
 font-family:"宋体","sans-serif";
 border-top:2px solid #000000;
 border-right:none;
 border-bottom:none;
 border-left:none;
 mso-diagonal-down:none;
 mso-diagonal-up:none;
 mso-protection:locked visible;
 }
.x22
 {
 mso-style-parent:style0;
 mso-number-format:"\@";
 text-align:center;
 vertical-align:middle;
 white-space:nowrap;
 background:#FFFFFF;
 mso-pattern:auto none;
 font-size:10pt;
 font-weight:400;
 font-style:normal;
 font-family:"宋体","sans-serif";
 border-top:2px solid #000000;
 border-right:none;
 border-bottom:none;
 border-left:none;
 mso-diagonal-down:none;
 mso-diagonal-up:none;
 mso-protection:locked visible;
 }
.x23
 {
 mso-style-parent:style0;
 mso-number-format:"\@";
 text-align:center;
 vertical-align:middle;
 white-space:nowrap;
 background:#FFFFFF;
 mso-pattern:auto none;
 font-size:10pt;
 font-weight:400;
 font-style:normal;
 font-family:"宋体","sans-serif";
 border-top:2px solid #000000;
 border-right:none;
 border-bottom:none;
 border-left:1px solid #000000;
 mso-diagonal-down:none;
 mso-diagonal-up:none;
 mso-protection:locked visible;
 }
.x24
 {
 mso-style-parent:style0;
 mso-number-format:"\@";
 text-align:center;
 vertical-align:middle;
 white-space:nowrap;
 background:#FFFFFF;
 mso-pattern:auto none;
 font-size:10pt;
 font-weight:400;
 font-style:normal;
 font-family:"宋体","sans-serif";
 mso-protection:locked visible;
 }
.x25
 {
 mso-style-parent:style0;
 mso-number-format:"\@";
 text-align:left;
 vertical-align:middle;
 white-space:nowrap;
 background:#FFFFFF;
 mso-pattern:auto none;
 font-size:10pt;
 font-weight:400;
 font-style:normal;
 font-family:"宋体","sans-serif";
 border-top:2px solid #000000;
 border-right:none;
 border-bottom:none;
 border-left:none;
 mso-diagonal-down:none;
 mso-diagonal-up:none;
 mso-protection:locked visible;
 }
.x26
 {
 mso-style-parent:style0;
 mso-number-format:"\@";
 text-align:center;
 vertical-align:middle;
 white-space:nowrap;
 background:#FFFFFF;
 mso-pattern:auto none;
 font-size:10pt;
 font-weight:400;
 font-style:normal;
 font-family:"宋体","sans-serif";
 border-top:none;
 border-right:none;
 border-bottom:none;
 border-left:1px solid #000000;
 mso-diagonal-down:none;
 mso-diagonal-up:none;
 mso-protection:locked visible;
 }
.x27
 {
 mso-style-parent:style0;
 mso-number-format:"\@";
 text-align:center;
 vertical-align:middle;
 white-space:nowrap;
 background:#FFFFFF;
 mso-pattern:auto none;
 font-size:10pt;
 font-weight:400;
 font-style:normal;
 font-family:"宋体","sans-serif";
 border-top:none;
 border-right:none;
 border-bottom:none;
 border-left:1px solid #000000;
 mso-diagonal-down:none;
 mso-diagonal-up:none;
 mso-protection:locked visible;
 }
.x28
 {
 mso-style-parent:style0;
 mso-number-format:"\@";
 text-align:center;
 vertical-align:middle;
 white-space:nowrap;
 background:#FFFFFF;
 mso-pattern:auto none;
 font-size:10pt;
 font-weight:400;
 font-style:normal;
 font-family:"宋体","sans-serif";
 border-top:1px solid #000000;
 border-right:none;
 border-bottom:none;
 border-left:none;
 mso-diagonal-down:none;
 mso-diagonal-up:none;
 mso-protection:locked visible;
 }
.x29
 {
 mso-style-parent:style0;
 mso-number-format:"\@";
 text-align:center;
 vertical-align:middle;
 white-space:nowrap;
 background:#FFFFFF;
 mso-pattern:auto none;
 font-size:10pt;
 font-weight:400;
 font-style:normal;
 font-family:"宋体","sans-serif";
 border-top:1px solid #000000;
 border-right:none;
 border-bottom:none;
 border-left:none;
 mso-diagonal-down:none;
 mso-diagonal-up:none;
 mso-protection:locked visible;
 }
.x30
 {
 mso-style-parent:style0;
 mso-number-format:"\@";
 text-align:center;
 vertical-align:middle;
 white-space:nowrap;
 background:#FFFFFF;
 mso-pattern:auto none;
 font-size:10pt;
 font-weight:400;
 font-style:normal;
 font-family:"宋体","sans-serif";
 border-top:1px solid #000000;
 border-right:none;
 border-bottom:none;
 border-left:none;
 mso-diagonal-down:none;
 mso-diagonal-up:none;
 mso-protection:locked visible;
 }
.x31
 {
 mso-style-parent:style0;
 mso-number-format:"\@";
 text-align:center;
 vertical-align:middle;
 white-space:nowrap;
 background:#FFFFFF;
 mso-pattern:auto none;
 font-size:10pt;
 font-weight:700;
 font-style:normal;
 font-family:"宋体","sans-serif";
 border-top:1px solid #000000;
 border-right:none;
 border-bottom:none;
 border-left:1px solid #000000;
 mso-diagonal-down:none;
 mso-diagonal-up:none;
 mso-protection:locked visible;
 }
.x32
 {
 mso-style-parent:style0;
 mso-number-format:"\@";
 text-align:center;
 vertical-align:middle;
 white-space:nowrap;
 background:#FFFFFF;
 mso-pattern:auto none;
 font-size:10pt;
 font-weight:700;
 font-style:normal;
 font-family:"宋体","sans-serif";
 border-top:1px solid #000000;
 border-right:none;
 border-bottom:none;
 border-left:none;
 mso-diagonal-down:none;
 mso-diagonal-up:none;
 mso-protection:locked visible;
 }
.x33
 {
 mso-style-parent:style0;
 mso-number-format:"\@";
 text-align:distributed;
 vertical-align:middle;
 white-space:nowrap;
 background:#FFFFFF;
 mso-pattern:auto none;
 font-size:10pt;
 font-weight:400;
 font-style:normal;
 font-family:"宋体","sans-serif";
 border-top:1px solid #000000;
 border-right:none;
 border-bottom:none;
 border-left:none;
 mso-diagonal-down:none;
 mso-diagonal-up:none;
 mso-protection:locked visible;
 }
.x34
 {
 mso-style-parent:style0;
 mso-number-format:"\@";
 text-align:center;
 vertical-align:middle;
 white-space:nowrap;
 background:#FFFFFF;
 mso-pattern:auto none;
 font-size:10pt;
 font-weight:700;
 font-style:normal;
 font-family:"宋体","sans-serif";
 border-top:1px solid #000000;
 border-right:none;
 border-bottom:none;
 border-left:1px solid #000000;
 mso-diagonal-down:none;
 mso-diagonal-up:none;
 mso-protection:locked visible;
 }
.x35
 {
 mso-style-parent:style0;
 mso-number-format:"\@";
 text-align:right;
 vertical-align:middle;
 white-space:nowrap;
 background:#FFC6AA;
 color:#0000FF;
 mso-pattern:auto none;
 font-size:10pt;
 font-weight:400;
 font-style:normal;
 font-family:"宋体","sans-serif";
 border-top:1px solid #000000;
 border-right:none;
 border-bottom:none;
 border-left:1px solid #000000;
 mso-diagonal-down:none;
 mso-diagonal-up:none;
 mso-protection:locked visible;
 }
.x36
 {
 mso-style-parent:style0;
 mso-number-format:"\@";
 text-align:distributed;
 vertical-align:middle;
 white-space:nowrap;
 background:#FFFFFF;
 mso-pattern:auto none;
 font-size:10pt;
 font-weight:400;
 font-style:normal;
 font-family:"宋体","sans-serif";
 mso-protection:locked visible;
 }
.x37
 {
 mso-style-parent:style0;
 mso-number-format:"\@";
 text-align:distributed;
 vertical-align:middle;
 white-space:nowrap;
 background:#FFFFFF;
 mso-pattern:auto none;
 font-size:10pt;
 font-weight:400;
 font-style:normal;
 font-family:"宋体","sans-serif";
 border-top:1px solid #000000;
 border-right:none;
 border-bottom:none;
 border-left:1px solid #000000;
 mso-diagonal-down:none;
 mso-diagonal-up:none;
 mso-protection:locked visible;
 }
.x38
 {
 mso-style-parent:style0;
 mso-number-format:"\@";
 text-align:left;
 vertical-align:middle;
 white-space:nowrap;
 background:#FFFFFF;
 mso-pattern:auto none;
 font-size:10pt;
 font-weight:400;
 font-style:normal;
 font-family:"宋体","sans-serif";
 border-top:none;
 border-right:none;
 border-bottom:none;
 border-left:1px solid #000000;
 mso-diagonal-down:none;
 mso-diagonal-up:none;
 mso-protection:locked visible;
 }
.x39
 {
 mso-style-parent:style0;
 mso-number-format:"\@";
 text-align:center;
 vertical-align:middle;
 white-space:nowrap;
 background:#FFFFFF;
 mso-pattern:auto none;
 font-size:10pt;
 font-weight:400;
 font-style:normal;
 font-family:"宋体","sans-serif";
 border-top:1px solid #000000;
 border-right:none;
 border-bottom:none;
 border-left:none;
 mso-diagonal-down:none;
 mso-diagonal-up:none;
 mso-protection:locked visible;
 }
.x40
 {
 mso-style-parent:style0;
 mso-number-format:"\@";
 text-align:center;
 vertical-align:middle;
 white-space:nowrap;
 background:#FFFFFF;
 mso-pattern:auto none;
 font-size:10pt;
 font-weight:400;
 font-style:normal;
 font-family:"宋体","sans-serif";
 border-top:2px solid #000000;
 border-right:none;
 border-bottom:none;
 border-left:none;
 mso-diagonal-down:none;
 mso-diagonal-up:none;
 mso-protection:locked visible;
 }
.x41
 {
 mso-style-parent:style0;
 mso-number-format:"\@";
 text-align:center;
 vertical-align:middle;
 white-space:normal;word-wrap:break-word;
 background:#FFFFFF;
 mso-pattern:auto none;
 font-size:10pt;
 font-weight:400;
 font-style:normal;
 font-family:"宋体","sans-serif";
 border-top:2px solid #000000;
 border-right:none;
 border-bottom:none;
 border-left:1px solid #000000;
 mso-diagonal-down:none;
 mso-diagonal-up:none;
 mso-protection:locked visible;
 }
.x42
 {
 mso-style-parent:style0;
 mso-number-format:"\@";
 text-align:center;
 vertical-align:middle;
 white-space:normal;word-wrap:break-word;
 background:#FFFFFF;
 mso-pattern:auto none;
 font-size:10pt;
 font-weight:400;
 font-style:normal;
 font-family:"宋体","sans-serif";
 border-top:1px solid #000000;
 border-right:none;
 border-bottom:none;
 border-left:1px solid #000000;
 mso-diagonal-down:none;
 mso-diagonal-up:none;
 mso-protection:locked visible;
 }
.x43
 {
 mso-style-parent:style0;
 mso-number-format:"\@";
 text-align:center;
 vertical-align:middle;
 white-space:normal;word-wrap:break-word;
 background:#FFFFFF;
 mso-pattern:auto none;
 font-size:10pt;
 font-weight:400;
 font-style:normal;
 font-family:"宋体","sans-serif";
 border-top:none;
 border-right:none;
 border-bottom:none;
 border-left:1px solid #000000;
 mso-diagonal-down:none;
 mso-diagonal-up:none;
 mso-protection:locked visible;
 }
.x44
 {
 mso-style-parent:style0;
 mso-number-format:"\@";
 text-align:center;
 vertical-align:middle;
 white-space:normal;word-wrap:break-word;
 background:#FFFFFF;
 mso-pattern:auto none;
 font-size:10pt;
 font-weight:400;
 font-style:normal;
 font-family:"宋体","sans-serif";
 border-top:none;
 border-right:none;
 border-bottom:none;
 border-left:1px solid #000000;
 mso-diagonal-down:none;
 mso-diagonal-up:none;
 mso-protection:locked visible;
 }
.x45
 {
 mso-style-parent:style0;
 mso-number-format:"\@";
 text-align:center;
 vertical-align:middle;
 white-space:normal;word-wrap:break-word;
 background:#FFFFFF;
 mso-pattern:auto none;
 font-size:10pt;
 font-weight:400;
 font-style:normal;
 font-family:"宋体","sans-serif";
 border-top:1px solid #000000;
 border-right:none;
 border-bottom:none;
 border-left:1px solid #000000;
 mso-diagonal-down:none;
 mso-diagonal-up:none;
 mso-protection:locked visible;
 }
.x46
 {
 mso-style-parent:style0;
 mso-number-format:"\@";
 text-align:right;
 vertical-align:middle;
 white-space:nowrap;
 background:#FFFFFF;
 mso-pattern:auto none;
 font-size:9pt;
 font-weight:400;
 font-style:normal;
 font-family:"宋体","sans-serif";
 mso-protection:locked visible;
 }
.x47
 {
 mso-style-parent:style0;
 mso-number-format:"\@";
 text-align:right;
 vertical-align:middle;
 white-space:nowrap;
 background:#FFFFFF;
 mso-pattern:auto none;
 font-size:10pt;
 font-weight:400;
 font-style:normal;
 font-family:"宋体","sans-serif";
 mso-protection:locked visible;
 }
.x48
{
 mso-style-parent:style0;
 mso-number-format:"\@";
 text-align:right;
 vertical-align:middle;
 white-space:nowrap;
 background:#D7D7FF;
 color:#0000FF;
 mso-pattern:auto none;
 font-size:10pt;
 font-weight:400;
 font-style:normal;
 font-family:"宋体","sans-serif";
 border-top:1px solid #000000;
 border-right:none;
 border-bottom:none;
 border-left:1px solid #000000;
 mso-diagonal-down:none;
 mso-diagonal-up:none;
 mso-protection:locked visible;
}
-->
</style>
<!--[if gte mso 9]><xml>
 <x:ExcelWorkbook>
  <x:ExcelWorksheets>
   <x:ExcelWorksheet>
    <x:Name>专题调查表十二</x:Name>
<x:WorksheetOptions>
 <x:StandardWidth>2048</x:StandardWidth>
 <x:Print>
  <x:FitWidth>0</x:FitWidth>
  <x:FitHeight>0</x:FitHeight>
  <x:LeftToRight/>
  <x:ValidPrinterInfo/>
  <x:PaperSizeIndex>9</x:PaperSizeIndex>
  <x:Scale>97</x:Scale>
  <x:HorizontalResolution>300</x:HorizontalResolution>
  <x:VerticalResolution>300</x:VerticalResolution>
 </x:Print>
 <x:Selected/>
</x:WorksheetOptions>
   </x:ExcelWorksheet>
  </x:ExcelWorksheets>
  <x:WindowHeight>12375</x:WindowHeight>
  <x:WindowWidth>27945</x:WindowWidth>
  <x:WindowTopX>240</x:WindowTopX>
  <x:WindowTopY>120</x:WindowTopY>
  <x:RefModeR1C1/>
  <x:TabRatio>600</x:TabRatio>
  <x:ActiveSheet>0</x:ActiveSheet>
 </x:ExcelWorkbook>
</xml><![endif]-->
</head>
<!--<body link='blue' vlink='purple' class='x16'>-->

<!--<table border='0' cellpadding='0' cellspacing='0' width='1022' style='border-collapse: -->
<!-- collapse;table-layout:fixed;width:766pt'>-->
<body link="blue" vlink="purple">
<table border="0" cellpadding="0" cellspacing="0" width="1120" style="border-collapse: collapse;table-layout:fixed; width: 100%;overflow-y: auto;">
 <col class='x16' width='35' style='mso-width-source:userset;background:none;width:26.25pt'>
 <col class='x16' width='14' style='mso-width-source:userset;background:none;width:10.5pt'>
 <col class='x16' width='21' style='mso-width-source:userset;background:none;width:15.75pt'>
 <col class='x16' width='14' style='mso-width-source:userset;background:none;width:10.5pt'>
 <col class='x16' width='119' style='mso-width-source:userset;background:none;width:89.25pt'>
 <col class='x16' width='14' style='mso-width-source:userset;background:none;width:10.5pt'>
 <col class='x16' width='35' style='mso-width-source:userset;background:none;width:26.25pt'>
 <col class='x16' width='70' span='11' style='mso-width-source:userset;background:none;width:52.5pt'>
 <tr height='33' style='mso-height-source:userset;height:24.75pt' id='r0'>
<td colspan='18' height='33' class='x17' width='1022' style='height:24.75pt;'>中小学校党建工作重点任务基本情况</td>
 </tr>
 <tr height='22' style='mso-height-source:userset;height:16.5pt' id='r1'>
<td colspan='4' height='20' class='x19' style='height:15pt;'>填报单位：#(info??'统计单位')</td>
<td colspan='7' class='x18'></td>
<td class='x18'></td>
<td class='x18'></td>
<td class='x18'></td>
<td class='x18'></td>
<td class='x18'></td>
<td colspan='2' class='x46'><span style='float:right'>专题调查表十二 </span></td>
 </tr>
 <tr height='33' style='mso-height-source:userset;height:24.75pt' id='r2'>
<td colspan='6' rowspan='3' height='196' class='x20' style='height:147pt;'>项<span style='mso-spacerun:yes;'>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; </span>目</td>
<td colspan='2' rowspan='3' height='196' class='x23' style='height:147pt;'>总<span style='mso-spacerun:yes;'>&nbsp; </span>数</td>
<td class='x40'></td>
<td class='x40'></td>
<td rowspan='3' height='196' class='x41' style='height:147pt;'>修订学校<br>党组织会<br>议、校长<br>办公会议<br>（校务会<br>议）的会<br>议制度和<br>议事规则<br>的</td>
<td rowspan='3' height='196' class='x41' style='height:147pt;'>党组织<br>书记、<br>校长分<br>设的</td>
<td rowspan='3' height='196' class='x41' style='height:147pt;'>党组织<br>书记不<br>兼任行<br>政领导<br>职务的</td>
<td rowspan='3' height='196' class='x41' style='height:147pt;'>校长系<br>中共党<br>员的</td>
<td class='x40'></td>
<td rowspan='3' height='196' class='x41' style='height:147pt;'>已建立学<br>校党组织<br>书记和校<br>长定期沟<br>通制度的</td>
<td rowspan='3' height='196' class='x41' style='height:147pt;'>已设立<br>党务工<br>作机构<br>的<br></td>
<td rowspan='3' height='196' class='x41' style='height:147pt;'>已配备专<br>职党务工<br>作人员的</td>
 </tr>
 <tr height='33' style='mso-height-source:userset;height:24.75pt' id='r3'>
<td rowspan='2' height='164' class='x42' style='height:123pt;'>实行党组<br>织领导的<br>校长负责<br>制的</td>
<td class='x39'></td>
<td rowspan='2' height='164' class='x42' style='height:123pt;'>校长担<br>任党组<br>织副书<br>记的</td>
 </tr>
 <tr height='132' style='mso-height-source:userset;height:99pt' id='r4'>
<td class='x45'>领导体<br>制已写<br>入学校<br>章程的</td>
 </tr>
 <tr height='55' style='mso-height-source:userset;height:41.25pt' id='r5'>
<td colspan='6' height='53' class='x28' style='height:39.75pt;'>甲</td>
<td colspan='2' class='x31'>A</td>
<td class='x34'>B</td>
<td class='x34'>C</td>
<td class='x34'>D</td>
<td class='x34'>E</td>
<td class='x34'>F</td>
<td class='x34'>G</td>
<td class='x34'>H</td>
<td class='x34'>I</td>
<td class='x34'>J</td>
<td class='x34'>K</td>
 </tr>
 <tr height='44' style='mso-height-source:userset;height:33pt' id='r6'>
<td colspan='5' height='43' class='x28' style='height:32.25pt;'>中小学校（个/名）</td>
<td class='x33'></td>
<td class='x34' x:num="1.0">1</td>
<td class='x48' table-name="dj2022wz_57" table-cell="0_1_1" onclick="sendMessage(this)" style="text-align: center;overflow: hidden;"> #(table0.cell_1_1 ?? == 0 ? '' : table0.cell_1_1??)   </td>
<td class='x48' table-name="dj2022wz_57" table-cell="0_1_2" onclick="sendMessage(this)" style="text-align: center;overflow: hidden;"> #(table0.cell_1_2 ?? == 0 ? '' : table0.cell_1_2??)   </td>
<td class='x48' table-name="dj2022wz_57" table-cell="0_1_3" onclick="sendMessage(this)" style="text-align: center;overflow: hidden;"> #(table0.cell_1_3 ?? == 0 ? '' : table0.cell_1_3??)   </td>
<td class='x48' table-name="dj2022wz_57" table-cell="0_1_4" onclick="sendMessage(this)" style="text-align: center;overflow: hidden;"> #(table0.cell_1_4 ?? == 0 ? '' : table0.cell_1_4??)   </td>
<td class='x48' table-name="dj2022wz_57" table-cell="0_1_5" onclick="sendMessage(this)" style="text-align: center;overflow: hidden;"> #(table0.cell_1_5 ?? == 0 ? '' : table0.cell_1_5??)   </td>
<td class='x48' table-name="dj2022wz_57" table-cell="0_1_6" onclick="sendMessage(this)" style="text-align: center;overflow: hidden;"> #(table0.cell_1_6 ?? == 0 ? '' : table0.cell_1_6??)   </td>
<td class='x48' table-name="dj2022wz_57" table-cell="0_1_7" onclick="sendMessage(this)" style="text-align: center;overflow: hidden;"> #(table0.cell_1_7 ?? == 0 ? '' : table0.cell_1_7??)   </td>
<td class='x48' table-name="dj2022wz_57" table-cell="0_1_8" onclick="sendMessage(this)" style="text-align: center;overflow: hidden;"> #(table0.cell_1_8 ?? == 0 ? '' : table0.cell_1_8??)   </td>
<td class='x48' table-name="dj2022wz_57" table-cell="0_1_9" onclick="sendMessage(this)" style="text-align: center;overflow: hidden;"> #(table0.cell_1_9 ?? == 0 ? '' : table0.cell_1_9??)   </td>
<td class='x48' table-name="dj2022wz_57" table-cell="0_1_10" onclick="sendMessage(this)" style="text-align: center;overflow: hidden;">#(table0.cell_1_10 ?? == 0 ? '' : table0.cell_1_10??) </td>
<td class='x48' table-name="dj2022wz_57" table-cell="0_1_11" onclick="sendMessage(this)" style="text-align: center;overflow: hidden;">#(table0.cell_1_11 ?? == 0 ? '' : table0.cell_1_11??) </td>
 </tr>
 <tr height='44' style='mso-height-source:userset;height:33pt' id='r7'>
<td rowspan='5' height='220' class='x36' style='height:165pt;'></td>
<td class='x37'></td>
<td colspan='3' class='x28'>小学</td>
<td class='x33'></td>
<td class='x34' x:num="2.0">2</td>
<td class='x35' table-name="dj2022wz_57" table-cell="0_2_1" onclick="sendMessage(this)" style="text-align: center;overflow: hidden;"> #(table0.cell_2_1 ?? == 0 ? '' : table0.cell_2_1??)  </td>
<td class='x35' table-name="dj2022wz_57" table-cell="0_2_2" onclick="sendMessage(this)" style="text-align: center;overflow: hidden;"> #(table0.cell_2_2 ?? == 0 ? '' : table0.cell_2_2??)  </td>
<td class='x35' table-name="dj2022wz_57" table-cell="0_2_3" onclick="sendMessage(this)" style="text-align: center;overflow: hidden;"> #(table0.cell_2_3 ?? == 0 ? '' : table0.cell_2_3??)  </td>
<td class='x35' table-name="dj2022wz_57" table-cell="0_2_4" onclick="sendMessage(this)" style="text-align: center;overflow: hidden;"> #(table0.cell_2_4 ?? == 0 ? '' : table0.cell_2_4??)  </td>
<td class='x35' table-name="dj2022wz_57" table-cell="0_2_5" onclick="sendMessage(this)" style="text-align: center;overflow: hidden;"> #(table0.cell_2_5 ?? == 0 ? '' : table0.cell_2_5??)  </td>
<td class='x35' table-name="dj2022wz_57" table-cell="0_2_6" onclick="sendMessage(this)" style="text-align: center;overflow: hidden;"> #(table0.cell_2_6 ?? == 0 ? '' : table0.cell_2_6??)  </td>
<td class='x35' table-name="dj2022wz_57" table-cell="0_2_7" onclick="sendMessage(this)" style="text-align: center;overflow: hidden;"> #(table0.cell_2_7 ?? == 0 ? '' : table0.cell_2_7??)  </td>
<td class='x35' table-name="dj2022wz_57" table-cell="0_2_8" onclick="sendMessage(this)" style="text-align: center;overflow: hidden;"> #(table0.cell_2_8 ?? == 0 ? '' : table0.cell_2_8??)  </td>
<td class='x35' table-name="dj2022wz_57" table-cell="0_2_9" onclick="sendMessage(this)" style="text-align: center;overflow: hidden;"> #(table0.cell_2_9 ?? == 0 ? '' : table0.cell_2_9??)  </td>
<td class='x35' table-name="dj2022wz_57" table-cell="0_2_10" onclick="sendMessage(this)" style="text-align: center;overflow: hidden;">#(table0.cell_2_10 ?? == 0 ? '' : table0.cell_2_10??)</td>
<td class='x35' table-name="dj2022wz_57" table-cell="0_2_11" onclick="sendMessage(this)" style="text-align: center;overflow: hidden;">#(table0.cell_2_11 ?? == 0 ? '' : table0.cell_2_11??)</td>
 </tr>
 <tr height='44' style='mso-height-source:userset;height:33pt' id='r8'>
<td class='x37'></td>
<td colspan='3' class='x28'>初中</td>
<td class='x33'></td>
<td class='x34' x:num="3.0">3</td>
<td class='x35'table-name="dj2022wz_57" table-cell="0_3_1" onclick="sendMessage(this)" style="text-align: center;overflow: hidden;"> #(table0.cell_3_1 ?? == 0 ? '' : table0.cell_3_1??)  </td>
<td class='x35'table-name="dj2022wz_57" table-cell="0_3_2" onclick="sendMessage(this)" style="text-align: center;overflow: hidden;"> #(table0.cell_3_2 ?? == 0 ? '' : table0.cell_3_2??)  </td>
<td class='x35'table-name="dj2022wz_57" table-cell="0_3_3" onclick="sendMessage(this)" style="text-align: center;overflow: hidden;"> #(table0.cell_3_3 ?? == 0 ? '' : table0.cell_3_3??)  </td>
<td class='x35'table-name="dj2022wz_57" table-cell="0_3_4" onclick="sendMessage(this)" style="text-align: center;overflow: hidden;"> #(table0.cell_3_4 ?? == 0 ? '' : table0.cell_3_4??)  </td>
<td class='x35'table-name="dj2022wz_57" table-cell="0_3_5" onclick="sendMessage(this)" style="text-align: center;overflow: hidden;"> #(table0.cell_3_5 ?? == 0 ? '' : table0.cell_3_5??)  </td>
<td class='x35'table-name="dj2022wz_57" table-cell="0_3_6" onclick="sendMessage(this)" style="text-align: center;overflow: hidden;"> #(table0.cell_3_6 ?? == 0 ? '' : table0.cell_3_6??)  </td>
<td class='x35'table-name="dj2022wz_57" table-cell="0_3_7" onclick="sendMessage(this)" style="text-align: center;overflow: hidden;"> #(table0.cell_3_7 ?? == 0 ? '' : table0.cell_3_7??)  </td>
<td class='x35'table-name="dj2022wz_57" table-cell="0_3_8" onclick="sendMessage(this)" style="text-align: center;overflow: hidden;"> #(table0.cell_3_8 ?? == 0 ? '' : table0.cell_3_8??)  </td>
<td class='x35'table-name="dj2022wz_57" table-cell="0_3_9" onclick="sendMessage(this)" style="text-align: center;overflow: hidden;"> #(table0.cell_3_9 ?? == 0 ? '' : table0.cell_3_9??)  </td>
<td class='x35'table-name="dj2022wz_57" table-cell="0_3_10" onclick="sendMessage(this)" style="text-align: center;overflow: hidden;">#(table0.cell_3_10 ?? == 0 ? '' : table0.cell_3_10??)</td>
<td class='x35'table-name="dj2022wz_57" table-cell="0_3_11" onclick="sendMessage(this)" style="text-align: center;overflow: hidden;">#(table0.cell_3_11 ?? == 0 ? '' : table0.cell_3_11??)</td>
 </tr>
 <tr height='44' style='mso-height-source:userset;height:33pt' id='r9'>
<td class='x37'></td>
<td colspan='3' class='x28'>高中</td>
<td class='x33'></td>
<td class='x34' x:num="4.0">4</td>
<td class='x48'table-name="dj2022wz_57" table-cell="0_4_1" onclick="sendMessage(this)" style="text-align: center;overflow: hidden;"> #(table0.cell_4_1 ?? == 0 ? '' : table0.cell_4_1??)  </td>
<td class='x48'table-name="dj2022wz_57" table-cell="0_4_2" onclick="sendMessage(this)" style="text-align: center;overflow: hidden;"> #(table0.cell_4_2 ?? == 0 ? '' : table0.cell_4_2??)  </td>
<td class='x48'table-name="dj2022wz_57" table-cell="0_4_3" onclick="sendMessage(this)" style="text-align: center;overflow: hidden;"> #(table0.cell_4_3 ?? == 0 ? '' : table0.cell_4_3??)  </td>
<td class='x48'table-name="dj2022wz_57" table-cell="0_4_4" onclick="sendMessage(this)" style="text-align: center;overflow: hidden;"> #(table0.cell_4_4 ?? == 0 ? '' : table0.cell_4_4??)  </td>
<td class='x48'table-name="dj2022wz_57" table-cell="0_4_5" onclick="sendMessage(this)" style="text-align: center;overflow: hidden;"> #(table0.cell_4_5 ?? == 0 ? '' : table0.cell_4_5??)  </td>
<td class='x48'table-name="dj2022wz_57" table-cell="0_4_6" onclick="sendMessage(this)" style="text-align: center;overflow: hidden;"> #(table0.cell_4_6 ?? == 0 ? '' : table0.cell_4_6??)  </td>
<td class='x48'table-name="dj2022wz_57" table-cell="0_4_7" onclick="sendMessage(this)" style="text-align: center;overflow: hidden;"> #(table0.cell_4_7 ?? == 0 ? '' : table0.cell_4_7??)  </td>
<td class='x48'table-name="dj2022wz_57" table-cell="0_4_8" onclick="sendMessage(this)" style="text-align: center;overflow: hidden;"> #(table0.cell_4_8 ?? == 0 ? '' : table0.cell_4_8??)  </td>
<td class='x48'table-name="dj2022wz_57" table-cell="0_4_9" onclick="sendMessage(this)" style="text-align: center;overflow: hidden;"> #(table0.cell_4_9 ?? == 0 ? '' : table0.cell_4_9??)  </td>
<td class='x48'table-name="dj2022wz_57" table-cell="0_4_10" onclick="sendMessage(this)" style="text-align: center;overflow: hidden;">#(table0.cell_4_10 ?? == 0 ? '' : table0.cell_4_10??)</td>
<td class='x48'table-name="dj2022wz_57" table-cell="0_4_11" onclick="sendMessage(this)" style="text-align: center;overflow: hidden;">#(table0.cell_4_11 ?? == 0 ? '' : table0.cell_4_11??)</td>
 </tr>
 <tr height='44' style='mso-height-source:userset;height:33pt' id='r10'>
<td class='x38'></td>
<td class='x18'></td>
<td class='x37'></td>
<td class='x39'>普通高中</td>
<td class='x33'></td>
<td class='x34' x:num="5.0">5</td>
<td class='x35' table-name="dj2022wz_57" table-cell="0_5_1" onclick="sendMessage(this)" style="text-align: center;overflow: hidden;"> #(table0.cell_5_1 ?? == 0 ? '' : table0.cell_5_1??)  </td>
<td class='x35' table-name="dj2022wz_57" table-cell="0_5_2" onclick="sendMessage(this)" style="text-align: center;overflow: hidden;"> #(table0.cell_5_2 ?? == 0 ? '' : table0.cell_5_2??)  </td>
<td class='x35' table-name="dj2022wz_57" table-cell="0_5_3" onclick="sendMessage(this)" style="text-align: center;overflow: hidden;"> #(table0.cell_5_3 ?? == 0 ? '' : table0.cell_5_3??)  </td>
<td class='x35' table-name="dj2022wz_57" table-cell="0_5_4" onclick="sendMessage(this)" style="text-align: center;overflow: hidden;"> #(table0.cell_5_4 ?? == 0 ? '' : table0.cell_5_4??)  </td>
<td class='x35' table-name="dj2022wz_57" table-cell="0_5_5" onclick="sendMessage(this)" style="text-align: center;overflow: hidden;"> #(table0.cell_5_5 ?? == 0 ? '' : table0.cell_5_5??)  </td>
<td class='x35' table-name="dj2022wz_57" table-cell="0_5_6" onclick="sendMessage(this)" style="text-align: center;overflow: hidden;"> #(table0.cell_5_6 ?? == 0 ? '' : table0.cell_5_6??)  </td>
<td class='x35' table-name="dj2022wz_57" table-cell="0_5_7" onclick="sendMessage(this)" style="text-align: center;overflow: hidden;"> #(table0.cell_5_7 ?? == 0 ? '' : table0.cell_5_7??)  </td>
<td class='x35' table-name="dj2022wz_57" table-cell="0_5_8" onclick="sendMessage(this)" style="text-align: center;overflow: hidden;"> #(table0.cell_5_8 ?? == 0 ? '' : table0.cell_5_8??)  </td>
<td class='x35' table-name="dj2022wz_57" table-cell="0_5_9" onclick="sendMessage(this)" style="text-align: center;overflow: hidden;"> #(table0.cell_5_9 ?? == 0 ? '' : table0.cell_5_9??)  </td>
<td class='x35' table-name="dj2022wz_57" table-cell="0_5_10" onclick="sendMessage(this)" style="text-align: center;overflow: hidden;">#(table0.cell_5_10 ?? == 0 ? '' : table0.cell_5_10??)</td>
<td class='x35' table-name="dj2022wz_57" table-cell="0_5_11" onclick="sendMessage(this)" style="text-align: center;overflow: hidden;">#(table0.cell_5_11 ?? == 0 ? '' : table0.cell_5_11??)</td>
 </tr>
 <tr height='44' style='mso-height-source:userset;height:33pt' id='r11'>
<td class='x38'></td>
<td class='x18'></td>
<td class='x37'></td>
<td class='x39'>中等职业学校</td>
<td class='x33'></td>
<td class='x34' x:num="6.0">6</td>
<td class='x35'table-name="dj2022wz_57" table-cell="0_6_1" onclick="sendMessage(this)" style="text-align: center;overflow: hidden;"> #(table0.cell_6_1 ?? == 0 ? '' : table0.cell_6_1??)  </td>
<td class='x35'table-name="dj2022wz_57" table-cell="0_6_2" onclick="sendMessage(this)" style="text-align: center;overflow: hidden;"> #(table0.cell_6_2 ?? == 0 ? '' : table0.cell_6_2??)  </td>
<td class='x35'table-name="dj2022wz_57" table-cell="0_6_3" onclick="sendMessage(this)" style="text-align: center;overflow: hidden;"> #(table0.cell_6_3 ?? == 0 ? '' : table0.cell_6_3??)  </td>
<td class='x35'table-name="dj2022wz_57" table-cell="0_6_4" onclick="sendMessage(this)" style="text-align: center;overflow: hidden;"> #(table0.cell_6_4 ?? == 0 ? '' : table0.cell_6_4??)  </td>
<td class='x35'table-name="dj2022wz_57" table-cell="0_6_5" onclick="sendMessage(this)" style="text-align: center;overflow: hidden;"> #(table0.cell_6_5 ?? == 0 ? '' : table0.cell_6_5??)  </td>
<td class='x35'table-name="dj2022wz_57" table-cell="0_6_6" onclick="sendMessage(this)" style="text-align: center;overflow: hidden;"> #(table0.cell_6_6 ?? == 0 ? '' : table0.cell_6_6??)  </td>
<td class='x35'table-name="dj2022wz_57" table-cell="0_6_7" onclick="sendMessage(this)" style="text-align: center;overflow: hidden;"> #(table0.cell_6_7 ?? == 0 ? '' : table0.cell_6_7??)  </td>
<td class='x35'table-name="dj2022wz_57" table-cell="0_6_8" onclick="sendMessage(this)" style="text-align: center;overflow: hidden;"> #(table0.cell_6_8 ?? == 0 ? '' : table0.cell_6_8??)  </td>
<td class='x35'table-name="dj2022wz_57" table-cell="0_6_9" onclick="sendMessage(this)" style="text-align: center;overflow: hidden;"> #(table0.cell_6_9 ?? == 0 ? '' : table0.cell_6_9??)  </td>
<td class='x35'table-name="dj2022wz_57" table-cell="0_6_10" onclick="sendMessage(this)" style="text-align: center;overflow: hidden;">#(table0.cell_6_10 ?? == 0 ? '' : table0.cell_6_10??)</td>
<td class='x35'table-name="dj2022wz_57" table-cell="0_6_11" onclick="sendMessage(this)" style="text-align: center;overflow: hidden;">#(table0.cell_6_11 ?? == 0 ? '' : table0.cell_6_11??)</td>
 </tr>
 <tr height='16' style='mso-height-source:userset;height:12.4pt' id='r12'>
<td height='14' class='x25' style='height:10.9pt;'></td>
<td class='x25'></td>
<td class='x25'></td>
<td class='x25'></td>
<td class='x25'></td>
<td class='x25'></td>
<td class='x25'></td>
<td class='x25'></td>
<td class='x25'></td>
<td class='x25'></td>
<td class='x25'></td>
<td class='x25'></td>
<td class='x25'></td>
<td class='x25'></td>
<td class='x25'></td>
<td class='x25'></td>
<td class='x25'></td>
<td class='x25'></td>
 </tr>
<![if supportMisalignedColumns]>
 <tr height='0' style='display:none'>
  <td width='35' style='width:26.25pt'></td>
  <td width='14' style='width:10.5pt'></td>
  <td width='21' style='width:15.75pt'></td>
  <td width='14' style='width:10.5pt'></td>
  <td width='119' style='width:89.25pt'></td>
  <td width='14' style='width:10.5pt'></td>
  <td width='35' style='width:26.25pt'></td>
  <td width='70' style='width:52.5pt'></td>
  <td width='70' style='width:52.5pt'></td>
  <td width='70' style='width:52.5pt'></td>
  <td width='70' style='width:52.5pt'></td>
  <td width='70' style='width:52.5pt'></td>
  <td width='70' style='width:52.5pt'></td>
  <td width='70' style='width:52.5pt'></td>
  <td width='70' style='width:52.5pt'></td>
  <td width='70' style='width:52.5pt'></td>
  <td width='70' style='width:52.5pt'></td>
  <td width='70' style='width:52.5pt'></td>
 </tr>
 <![endif]>
</table>

<script language = 'javascript' type='text/javascript'>
 function ChangeRowspanHiddenData()
 {
   var node;
   var params=["r2","r3","r7"];
   for (var i = 0;i < params.length; i++)
   {
       node = document.getElementById(params[i]);
       if (node != null)
       {
           node.style.display = "";
       }
   }
 }
 ChangeRowspanHiddenData();
</script>
</body>

</html>
