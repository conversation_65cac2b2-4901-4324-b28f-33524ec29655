<!DOCTYPE html PUBLIC "-//w3c//dtd xhtml 1.0 transitional//en" "http://www.w3.org/tr/xhtml1/dtd/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/TR/REC-html40">
 <head> 
  <meta http-equiv="Content-Type" content="text/html; charset=UTF-8"> 
  <meta name="ProgId" content="Excel.Sheet"> 
  <meta name="Generator" content="Aspose.Cell 19.7.0"> 
  <link rel="File-List" href="/filelist.xml"> 
  <link rel="Edit-Time-Data" href="/editdata.mso"> 
  <link rel="OLE-Object-Data" href="/oledata.mso"> 
  <!--[if gte mso 9]><xml>
 <o:DocumentProperties>
  <o:Created>2022-01-09T09:45:25Z</o:Created>
  <o:LastSaved>2022-01-10T09:01:45Z</o:LastSaved>
</o:DocumentProperties>
</xml><![endif]--> 
  <style>
<!--table
 {mso-displayed-decimal-separator:"\.";
 mso-displayed-thousand-separator:"\,";}
@page
 {
 mso-header-data:"";
 mso-footer-data:"";
 margin:0.98425in 0.74803in 0.98425in 0.74803in;
 mso-header-margin:0.51181in;
 mso-footer-margin:0.51181in;
 mso-page-orientation:Portrait;
 }
tr
 {mso-height-source:auto;
 mso-ruby-visibility:none;}
col
 {mso-width-source:auto;
 mso-ruby-visibility:none;}
br
 {mso-data-placement:same-cell;}
ruby
 {ruby-align:left;}
.style0
 {
 mso-number-format:General;
 text-align:general;
 vertical-align:bottom;
 white-space:nowrap;
 background:white;
 mso-pattern:auto;
 color:#000000;
 font-size:10pt;
 font-weight:400;
 font-style:normal;
 font-family:"宋体";
 border:none;
 mso-protection:locked visible;
 mso-style-name:Normal;
 mso-style-id:0;}
.font0
 {
 color:#000000;
 font-size:10pt;
 font-weight:400;
 font-style:normal;
 font-family:"宋体"; }
.font1
 {
 color:#000000;
 font-size:11pt;
 font-weight:400;
 font-style:normal;
 font-family:"宋体"; }
.font2
 {
 color:#000000;
 font-size:17pt;
 font-weight:700;
 font-style:normal;
 font-family:"宋体"; }
.font3
 {
 color:#000000;
 font-size:12pt;
 font-weight:700;
 font-style:normal;
 font-family:"宋体"; }
.font4
 {
 color:#0000FF;
 font-size:11pt;
 font-weight:400;
 font-style:normal;
 font-family:"宋体"; }
.font5
 {
 color:#000000;
 font-size:12pt;
 font-weight:400;
 font-style:normal;
 font-family:"宋体"; }
.font6
 {
 color:#000000;
 font-size:11pt;
 font-weight:400;
 font-style:normal;
 font-family:"宋体"; }
.font7
 {
 color:#0000FF;
 font-size:12pt;
 font-weight:400;
 font-style:normal;
 font-family:"宋体"; }
.font8
 {
 color:#000000;
 font-size:10pt;
 font-weight:400;
 font-style:normal;
 font-family:"宋体"; }
.font9
 {
 color:#000000;
 font-size:11pt;
 font-weight:700;
 font-style:normal;
 font-family:"宋体"; }
.font10
 {
 color:#000000;
 font-size:12pt;
 font-weight:400;
 font-style:normal;
 font-family:"宋体"; }
.font11
 {
 color:#660066;
 font-size:11pt;
 font-weight:400;
 font-style:normal;
 font-family:"宋体"; }
.font12
 {
 color:#FFFFFF;
 font-size:11pt;
 font-weight:400;
 font-style:normal;
 font-family:"宋体"; }
.font13
 {
 color:#800000;
 font-size:11pt;
 font-weight:400;
 font-style:normal;
 font-family:"宋体"; }
.font14
 {
 color:#0000FF;
 font-size:11pt;
 font-weight:400;
 font-style:normal;
 text-decoration:underline;
 font-family:"宋体"; }
.font15
 {
 color:#800080;
 font-size:11pt;
 font-weight:400;
 font-style:normal;
 text-decoration:underline;
 font-family:"宋体"; }
.font16
 {
 color:#5C4033;
 font-size:11pt;
 font-weight:700;
 font-style:normal;
 font-family:"宋体"; }
.font17
 {
 color:#FF0000;
 font-size:11pt;
 font-weight:400;
 font-style:normal;
 font-family:"宋体"; }
.font18
 {
 color:#993366;
 font-size:11pt;
 font-weight:700;
 font-style:normal;
 font-family:"宋体"; }
.font19
 {
 color:#5C4033;
 font-size:18pt;
 font-weight:700;
 font-style:normal;
 font-family:"宋体"; }
.font20
 {
 color:#808080;
 font-size:11pt;
 font-weight:400;
 font-style:italic;
 font-family:"宋体"; }
.font21
 {
 color:#5C4033;
 font-size:15pt;
 font-weight:700;
 font-style:normal;
 font-family:"宋体"; }
.font22
 {
 color:#5C4033;
 font-size:13pt;
 font-weight:700;
 font-style:normal;
 font-family:"宋体"; }
.font23
 {
 color:#FFFFFF;
 font-size:11pt;
 font-weight:700;
 font-style:normal;
 font-family:"宋体"; }
.font24
 {
 color:#008000;
 font-size:11pt;
 font-weight:400;
 font-style:normal;
 font-family:"宋体"; }
.font25
 {
 color:#993366;
 font-size:11pt;
 font-weight:400;
 font-style:normal;
 font-family:"宋体"; }
.font26
 {
 color:#808000;
 font-size:11pt;
 font-weight:400;
 font-style:normal;
 font-family:"宋体"; }
.font27
 {
 color:#FFFFFF;
 font-size:11pt;
 font-weight:400;
 font-style:normal;
 font-family:"宋体"; }
.font28
 {
 color:#000000;
 font-size:11pt;
 font-weight:400;
 font-style:normal;
 font-family:"宋体"; }
.font29
 {
 color:#9C6500;
 font-size:11pt;
 font-weight:400;
 font-style:normal;
 font-family:"宋体"; }
.font30
 {
 color:#006100;
 font-size:11pt;
 font-weight:400;
 font-style:normal;
 font-family:"宋体"; }
.font31
 {
 color:#000000;
 font-size:11pt;
 font-weight:700;
 font-style:normal;
 font-family:"宋体"; }
.font32
 {
 color:#FA7D00;
 font-size:11pt;
 font-weight:400;
 font-style:normal;
 font-family:"宋体"; }
.font33
 {
 color:#FFFFFF;
 font-size:11pt;
 font-weight:700;
 font-style:normal;
 font-family:"宋体"; }
.font34
 {
 color:#FA7D00;
 font-size:11pt;
 font-weight:700;
 font-style:normal;
 font-family:"宋体"; }
.font35
 {
 color:#3F3F3F;
 font-size:11pt;
 font-weight:700;
 font-style:normal;
 font-family:"宋体"; }
.font36
 {
 color:#44546A;
 font-size:11pt;
 font-weight:700;
 font-style:normal;
 font-family:"宋体"; }
.font37
 {
 color:#44546A;
 font-size:13pt;
 font-weight:700;
 font-style:normal;
 font-family:"宋体"; }
.font38
 {
 color:#44546A;
 font-size:15pt;
 font-weight:700;
 font-style:normal;
 font-family:"宋体"; }
.font39
 {
 color:#7F7F7F;
 font-size:11pt;
 font-weight:400;
 font-style:italic;
 font-family:"宋体"; }
.font40
 {
 color:#44546A;
 font-size:18pt;
 font-weight:700;
 font-style:normal;
 font-family:"宋体"; }
.font41
 {
 color:#FF0000;
 font-size:11pt;
 font-weight:400;
 font-style:normal;
 font-family:"宋体"; }
.font42
 {
 color:#000000;
 font-size:11pt;
 font-weight:400;
 font-style:normal;
 font-family:"宋体"; }
.font43
 {
 color:#800080;
 font-size:11pt;
 font-weight:400;
 font-style:normal;
 text-decoration:underline;
 font-family:"宋体"; }
.font44
 {
 color:#0000FF;
 font-size:11pt;
 font-weight:400;
 font-style:normal;
 text-decoration:underline;
 font-family:"宋体"; }
.font45
 {
 color:#9C0006;
 font-size:11pt;
 font-weight:400;
 font-style:normal;
 font-family:"宋体"; }
.font46
 {
 color:#3F3F76;
 font-size:11pt;
 font-weight:400;
 font-style:normal;
 font-family:"宋体"; }
td
 {mso-style-parent:style0;
 mso-number-format:General;
 text-align:general;
 vertical-align:bottom;
 white-space:nowrap;
 background:white;
 mso-pattern:auto;
 color:#000000;
 font-size:10pt;
 font-weight:400;
 font-style:normal;
 font-family:"宋体";
 border:none;
 mso-protection:locked visible;
 mso-ignore:padding;}
.style0
 {
 text-align:general;
 vertical-align:bottom;
 white-space:nowrap;
 background:white;
 mso-pattern:auto;
 font-size:10pt;
 font-weight:400;
 font-style:normal;
 font-family:"宋体";
 border:none;
 mso-protection:locked visible;
 mso-style-name:"Normal";
 }
.style1
 {
 text-align:general;
 vertical-align:middle;
 white-space:nowrap;
 background:white;
 mso-pattern:auto;
 font-size:11pt;
 font-weight:400;
 font-style:normal;
 font-family:"宋体";
 border:none;
 mso-protection:locked visible;
 }
.style2
 {
 text-align:general;
 vertical-align:middle;
 white-space:nowrap;
 background:white;
 mso-pattern:auto;
 font-size:11pt;
 font-weight:400;
 font-style:normal;
 font-family:"宋体";
 border:none;
 mso-protection:locked visible;
 }
.style3
 {
 text-align:general;
 vertical-align:middle;
 white-space:nowrap;
 background:white;
 mso-pattern:auto;
 font-size:11pt;
 font-weight:400;
 font-style:normal;
 font-family:"宋体";
 border:none;
 mso-protection:locked visible;
 }
.style4
 {
 text-align:general;
 vertical-align:middle;
 white-space:nowrap;
 background:white;
 mso-pattern:auto;
 font-size:11pt;
 font-weight:400;
 font-style:normal;
 font-family:"宋体";
 border:none;
 mso-protection:locked visible;
 }
.style5
 {
 text-align:general;
 vertical-align:middle;
 white-space:nowrap;
 background:white;
 mso-pattern:auto;
 font-size:10pt;
 font-weight:400;
 font-style:normal;
 font-family:"宋体";
 border:none;
 mso-protection:locked visible;
 }
.style6
 {
 text-align:general;
 vertical-align:middle;
 white-space:nowrap;
 background:white;
 mso-pattern:auto;
 font-size:10pt;
 font-weight:400;
 font-style:normal;
 font-family:"宋体";
 border:none;
 mso-protection:locked visible;
 }
.style7
 {
 text-align:general;
 vertical-align:middle;
 white-space:nowrap;
 background:white;
 mso-pattern:auto;
 font-size:10pt;
 font-weight:400;
 font-style:normal;
 font-family:"宋体";
 border:none;
 mso-protection:locked visible;
 }
.style8
 {
 text-align:general;
 vertical-align:middle;
 white-space:nowrap;
 background:white;
 mso-pattern:auto;
 font-size:10pt;
 font-weight:400;
 font-style:normal;
 font-family:"宋体";
 border:none;
 mso-protection:locked visible;
 }
.style9
 {
 text-align:general;
 vertical-align:middle;
 white-space:nowrap;
 background:white;
 mso-pattern:auto;
 font-size:10pt;
 font-weight:400;
 font-style:normal;
 font-family:"宋体";
 border:none;
 mso-protection:locked visible;
 }
.style10
 {
 text-align:general;
 vertical-align:middle;
 white-space:nowrap;
 background:white;
 mso-pattern:auto;
 font-size:10pt;
 font-weight:400;
 font-style:normal;
 font-family:"宋体";
 border:none;
 mso-protection:locked visible;
 }
.style11
 {
 text-align:general;
 vertical-align:middle;
 white-space:nowrap;
 background:white;
 mso-pattern:auto;
 font-size:10pt;
 font-weight:400;
 font-style:normal;
 font-family:"宋体";
 border:none;
 mso-protection:locked visible;
 }
.style12
 {
 text-align:general;
 vertical-align:middle;
 white-space:nowrap;
 background:white;
 mso-pattern:auto;
 font-size:10pt;
 font-weight:400;
 font-style:normal;
 font-family:"宋体";
 border:none;
 mso-protection:locked visible;
 }
.style13
 {
 text-align:general;
 vertical-align:middle;
 white-space:nowrap;
 background:white;
 mso-pattern:auto;
 font-size:10pt;
 font-weight:400;
 font-style:normal;
 font-family:"宋体";
 border:none;
 mso-protection:locked visible;
 }
.style14
 {
 text-align:general;
 vertical-align:middle;
 white-space:nowrap;
 background:white;
 mso-pattern:auto;
 font-size:10pt;
 font-weight:400;
 font-style:normal;
 font-family:"宋体";
 border:none;
 mso-protection:locked visible;
 }
.x15
 {
 mso-style-parent:style0;
 mso-number-format:General;
 text-align:general;
 vertical-align:bottom;
 white-space:nowrap;
 background:white;
 mso-pattern:auto;
 font-size:10pt;
 font-weight:400;
 font-style:normal;
 font-family:"宋体";
 border:none;
 mso-protection:locked visible;
 }
.style16
 {
 mso-number-format:"_ \0022￥\0022* \#\,\#\#0_ \;_ \0022￥\0022* \\-\#\,\#\#0_ \;_ \0022￥\0022* \0022-\0022_ \;_ \@_ ";
 text-align:general;
 vertical-align:bottom;
 white-space:nowrap;
 background:white;
 mso-pattern:auto;
 font-size:12pt;
 font-weight:400;
 font-style:normal;
 font-family:"宋体";
 border:none;
 mso-protection:locked visible;
 mso-style-name:"Currency [0]";
 }
.style17
 {
 text-align:general;
 vertical-align:middle;
 white-space:nowrap;
 background:#EDEDED;
 mso-pattern:auto none;
 color:#000000;
 font-size:11pt;
 font-weight:400;
 font-style:normal;
 font-family:"宋体";
 border:none;
 mso-protection:locked visible;
 mso-style-name:"20% - 强调文字颜色 3";
 }
.style18
 {
 text-align:general;
 vertical-align:middle;
 white-space:nowrap;
 background:#FFCC99;
 mso-pattern:auto none;
 color:#3F3F76;
 font-size:11pt;
 font-weight:400;
 font-style:normal;
 font-family:"宋体";
 border-top:1px solid #7F7F7F;
 border-right:1px solid #7F7F7F;
 border-bottom:1px solid #7F7F7F;
 border-left:1px solid #7F7F7F;
 mso-diagonal-down:none;
 mso-diagonal-up:none;
 mso-protection:locked visible;
 mso-style-name:"输入";
 }
.style19
 {
 mso-number-format:"_ \0022￥\0022* \#\,\#\#0\.00_ \;_ \0022￥\0022* \\-\#\,\#\#0\.00_ \;_ \0022￥\0022* \0022-\0022??_ \;_ \@_ ";
 text-align:general;
 vertical-align:bottom;
 white-space:nowrap;
 background:white;
 mso-pattern:auto;
 font-size:12pt;
 font-weight:400;
 font-style:normal;
 font-family:"宋体";
 border:none;
 mso-protection:locked visible;
 mso-style-name:"Currency";
 }
.style20
 {
 mso-number-format:"_ * \#\,\#\#0_ \;_ * \\-\#\,\#\#0_ \;_ * \0022-\0022_ \;_ \@_ ";
 text-align:general;
 vertical-align:bottom;
 white-space:nowrap;
 background:white;
 mso-pattern:auto;
 font-size:12pt;
 font-weight:400;
 font-style:normal;
 font-family:"宋体";
 border:none;
 mso-protection:locked visible;
 mso-style-name:"Comma [0]";
 }
.style21
 {
 text-align:general;
 vertical-align:middle;
 white-space:nowrap;
 background:#DBDBDB;
 mso-pattern:auto none;
 color:#000000;
 font-size:11pt;
 font-weight:400;
 font-style:normal;
 font-family:"宋体";
 border:none;
 mso-protection:locked visible;
 mso-style-name:"40% - 强调文字颜色 3";
 }
.style22
 {
 text-align:general;
 vertical-align:middle;
 white-space:nowrap;
 background:#FFC7CE;
 mso-pattern:auto none;
 color:#9C0006;
 font-size:11pt;
 font-weight:400;
 font-style:normal;
 font-family:"宋体";
 border:none;
 mso-protection:locked visible;
 mso-style-name:"差";
 }
.style23
 {
 mso-number-format:"_ * \#\,\#\#0\.00_ \;_ * \\-\#\,\#\#0\.00_ \;_ * \0022-\0022??_ \;_ \@_ ";
 text-align:general;
 vertical-align:bottom;
 white-space:nowrap;
 background:white;
 mso-pattern:auto;
 font-size:12pt;
 font-weight:400;
 font-style:normal;
 font-family:"宋体";
 border:none;
 mso-protection:locked visible;
 mso-style-name:"Comma";
 }
.style24
 {
 text-align:general;
 vertical-align:middle;
 white-space:nowrap;
 background:#C9C9C9;
 mso-pattern:auto none;
 color:#FFFFFF;
 font-size:11pt;
 font-weight:400;
 font-style:normal;
 font-family:"宋体";
 border:none;
 mso-protection:locked visible;
 mso-style-name:"60% - 强调文字颜色 3";
 }
.style25
 {
 text-align:general;
 vertical-align:middle;
 white-space:nowrap;
 background:white;
 mso-pattern:auto;
 color:#0000FF;
 font-size:11pt;
 font-weight:400;
 font-style:normal;
 text-decoration:underline;
 font-family:"宋体";
 border:none;
 mso-protection:locked visible;
 mso-style-name:"Hyperlink";
 }
.style26
 {
 mso-number-format:"0%";
 text-align:general;
 vertical-align:bottom;
 white-space:nowrap;
 background:white;
 mso-pattern:auto;
 font-size:12pt;
 font-weight:400;
 font-style:normal;
 font-family:"宋体";
 border:none;
 mso-protection:locked visible;
 mso-style-name:"Percent";
 }
.style27
 {
 text-align:general;
 vertical-align:middle;
 white-space:nowrap;
 background:white;
 mso-pattern:auto;
 color:#800080;
 font-size:11pt;
 font-weight:400;
 font-style:normal;
 text-decoration:underline;
 font-family:"宋体";
 border:none;
 mso-protection:locked visible;
 mso-style-name:"Followed Hyperlink";
 }
.style28
 {
 text-align:general;
 vertical-align:middle;
 white-space:nowrap;
 background:#FFFFCC;
 mso-pattern:auto none;
 color:#000000;
 font-size:11pt;
 font-weight:400;
 font-style:normal;
 font-family:"宋体";
 border-top:1px solid #B2B2B2;
 border-right:1px solid #B2B2B2;
 border-bottom:1px solid #B2B2B2;
 border-left:1px solid #B2B2B2;
 mso-diagonal-down:none;
 mso-diagonal-up:none;
 mso-protection:locked visible;
 mso-style-name:"注释";
 }
.style29
 {
 text-align:general;
 vertical-align:middle;
 white-space:nowrap;
 background:#F4B183;
 mso-pattern:auto none;
 color:#FFFFFF;
 font-size:11pt;
 font-weight:400;
 font-style:normal;
 font-family:"宋体";
 border:none;
 mso-protection:locked visible;
 mso-style-name:"60% - 强调文字颜色 2";
 }
.style30
 {
 text-align:general;
 vertical-align:middle;
 white-space:nowrap;
 background:white;
 mso-pattern:auto;
 color:#44546A;
 font-size:11pt;
 font-weight:700;
 font-style:normal;
 font-family:"宋体";
 border:none;
 mso-protection:locked visible;
 mso-style-name:"标题 4";
 }
.style31
 {
 text-align:general;
 vertical-align:middle;
 white-space:nowrap;
 background:white;
 mso-pattern:auto;
 color:#FF0000;
 font-size:11pt;
 font-weight:400;
 font-style:normal;
 font-family:"宋体";
 border:none;
 mso-protection:locked visible;
 mso-style-name:"警告文本";
 }
.style32
 {
 text-align:general;
 vertical-align:middle;
 white-space:nowrap;
 background:white;
 mso-pattern:auto;
 color:#44546A;
 font-size:18pt;
 font-weight:700;
 font-style:normal;
 font-family:"宋体";
 border:none;
 mso-protection:locked visible;
 mso-style-name:"标题";
 }
.style33
 {
 text-align:general;
 vertical-align:middle;
 white-space:nowrap;
 background:white;
 mso-pattern:auto;
 color:#7F7F7F;
 font-size:11pt;
 font-weight:400;
 font-style:italic;
 font-family:"宋体";
 border:none;
 mso-protection:locked visible;
 mso-style-name:"解释性文本";
 }
.style34
 {
 text-align:general;
 vertical-align:middle;
 white-space:nowrap;
 background:white;
 mso-pattern:auto;
 color:#44546A;
 font-size:15pt;
 font-weight:700;
 font-style:normal;
 font-family:"宋体";
 border-top:none;
 border-right:none;
 border-bottom:2px solid #5B9BD5;
 border-left:none;
 mso-diagonal-down:none;
 mso-diagonal-up:none;
 mso-protection:locked visible;
 mso-style-name:"标题 1";
 }
.style35
 {
 text-align:general;
 vertical-align:middle;
 white-space:nowrap;
 background:white;
 mso-pattern:auto;
 color:#44546A;
 font-size:13pt;
 font-weight:700;
 font-style:normal;
 font-family:"宋体";
 border-top:none;
 border-right:none;
 border-bottom:2px solid #5B9BD5;
 border-left:none;
 mso-diagonal-down:none;
 mso-diagonal-up:none;
 mso-protection:locked visible;
 mso-style-name:"标题 2";
 }
.style36
 {
 text-align:general;
 vertical-align:middle;
 white-space:nowrap;
 background:#9DC3E6;
 mso-pattern:auto none;
 color:#FFFFFF;
 font-size:11pt;
 font-weight:400;
 font-style:normal;
 font-family:"宋体";
 border:none;
 mso-protection:locked visible;
 mso-style-name:"60% - 强调文字颜色 1";
 }
.style37
 {
 text-align:general;
 vertical-align:middle;
 white-space:nowrap;
 background:white;
 mso-pattern:auto;
 color:#44546A;
 font-size:11pt;
 font-weight:700;
 font-style:normal;
 font-family:"宋体";
 border-top:none;
 border-right:none;
 border-bottom:2px solid #ADCDEA;
 border-left:none;
 mso-diagonal-down:none;
 mso-diagonal-up:none;
 mso-protection:locked visible;
 mso-style-name:"标题 3";
 }
.style38
 {
 text-align:general;
 vertical-align:middle;
 white-space:nowrap;
 background:#FFD966;
 mso-pattern:auto none;
 color:#FFFFFF;
 font-size:11pt;
 font-weight:400;
 font-style:normal;
 font-family:"宋体";
 border:none;
 mso-protection:locked visible;
 mso-style-name:"60% - 强调文字颜色 4";
 }
.style39
 {
 text-align:general;
 vertical-align:middle;
 white-space:nowrap;
 background:#F2F2F2;
 mso-pattern:auto none;
 color:#3F3F3F;
 font-size:11pt;
 font-weight:700;
 font-style:normal;
 font-family:"宋体";
 border-top:1px solid #3F3F3F;
 border-right:1px solid #3F3F3F;
 border-bottom:1px solid #3F3F3F;
 border-left:1px solid #3F3F3F;
 mso-diagonal-down:none;
 mso-diagonal-up:none;
 mso-protection:locked visible;
 mso-style-name:"输出";
 }
.style40
 {
 text-align:general;
 vertical-align:middle;
 white-space:nowrap;
 background:#F2F2F2;
 mso-pattern:auto none;
 color:#FA7D00;
 font-size:11pt;
 font-weight:700;
 font-style:normal;
 font-family:"宋体";
 border-top:1px solid #7F7F7F;
 border-right:1px solid #7F7F7F;
 border-bottom:1px solid #7F7F7F;
 border-left:1px solid #7F7F7F;
 mso-diagonal-down:none;
 mso-diagonal-up:none;
 mso-protection:locked visible;
 mso-style-name:"计算";
 }
.style41
 {
 text-align:general;
 vertical-align:middle;
 white-space:nowrap;
 background:#A5A5A5;
 mso-pattern:auto none;
 color:#FFFFFF;
 font-size:11pt;
 font-weight:700;
 font-style:normal;
 font-family:"宋体";
 border-top:4px double #3F3F3F;
 border-right:4px double #3F3F3F;
 border-bottom:4px double #3F3F3F;
 border-left:4px double #3F3F3F;
 mso-diagonal-down:none;
 mso-diagonal-up:none;
 mso-protection:locked visible;
 mso-style-name:"检查单元格";
 }
.style42
 {
 text-align:general;
 vertical-align:middle;
 white-space:nowrap;
 background:#E2F0D9;
 mso-pattern:auto none;
 color:#000000;
 font-size:11pt;
 font-weight:400;
 font-style:normal;
 font-family:"宋体";
 border:none;
 mso-protection:locked visible;
 mso-style-name:"20% - 强调文字颜色 6";
 }
.style43
 {
 text-align:general;
 vertical-align:middle;
 white-space:nowrap;
 background:#ED7D31;
 mso-pattern:auto none;
 color:#FFFFFF;
 font-size:11pt;
 font-weight:400;
 font-style:normal;
 font-family:"宋体";
 border:none;
 mso-protection:locked visible;
 mso-style-name:"强调文字颜色 2";
 }
.style44
 {
 text-align:general;
 vertical-align:middle;
 white-space:nowrap;
 background:white;
 mso-pattern:auto;
 color:#FA7D00;
 font-size:11pt;
 font-weight:400;
 font-style:normal;
 font-family:"宋体";
 border-top:none;
 border-right:none;
 border-bottom:4px double #FF8001;
 border-left:none;
 mso-diagonal-down:none;
 mso-diagonal-up:none;
 mso-protection:locked visible;
 mso-style-name:"链接单元格";
 }
.style45
 {
 text-align:general;
 vertical-align:middle;
 white-space:nowrap;
 background:white;
 mso-pattern:auto;
 color:#000000;
 font-size:11pt;
 font-weight:700;
 font-style:normal;
 font-family:"宋体";
 border-top:1px solid #5B9BD5;
 border-right:none;
 border-bottom:4px double #5B9BD5;
 border-left:none;
 mso-diagonal-down:none;
 mso-diagonal-up:none;
 mso-protection:locked visible;
 mso-style-name:"汇总";
 }
.style46
 {
 text-align:general;
 vertical-align:middle;
 white-space:nowrap;
 background:#C6EFCE;
 mso-pattern:auto none;
 color:#006100;
 font-size:11pt;
 font-weight:400;
 font-style:normal;
 font-family:"宋体";
 border:none;
 mso-protection:locked visible;
 mso-style-name:"好";
 }
.style47
 {
 text-align:general;
 vertical-align:middle;
 white-space:nowrap;
 background:#FFEB9C;
 mso-pattern:auto none;
 color:#9C6500;
 font-size:11pt;
 font-weight:400;
 font-style:normal;
 font-family:"宋体";
 border:none;
 mso-protection:locked visible;
 mso-style-name:"适中";
 }
.style48
 {
 text-align:general;
 vertical-align:middle;
 white-space:nowrap;
 background:#DAE3F3;
 mso-pattern:auto none;
 color:#000000;
 font-size:11pt;
 font-weight:400;
 font-style:normal;
 font-family:"宋体";
 border:none;
 mso-protection:locked visible;
 mso-style-name:"20% - 强调文字颜色 5";
 }
.style49
 {
 text-align:general;
 vertical-align:middle;
 white-space:nowrap;
 background:#5B9BD5;
 mso-pattern:auto none;
 color:#FFFFFF;
 font-size:11pt;
 font-weight:400;
 font-style:normal;
 font-family:"宋体";
 border:none;
 mso-protection:locked visible;
 mso-style-name:"强调文字颜色 1";
 }
.style50
 {
 text-align:general;
 vertical-align:middle;
 white-space:nowrap;
 background:#DEEBF7;
 mso-pattern:auto none;
 color:#000000;
 font-size:11pt;
 font-weight:400;
 font-style:normal;
 font-family:"宋体";
 border:none;
 mso-protection:locked visible;
 mso-style-name:"20% - 强调文字颜色 1";
 }
.style51
 {
 text-align:general;
 vertical-align:middle;
 white-space:nowrap;
 background:#BDD7EE;
 mso-pattern:auto none;
 color:#000000;
 font-size:11pt;
 font-weight:400;
 font-style:normal;
 font-family:"宋体";
 border:none;
 mso-protection:locked visible;
 mso-style-name:"40% - 强调文字颜色 1";
 }
.style52
 {
 text-align:general;
 vertical-align:middle;
 white-space:nowrap;
 background:#FBE5D6;
 mso-pattern:auto none;
 color:#000000;
 font-size:11pt;
 font-weight:400;
 font-style:normal;
 font-family:"宋体";
 border:none;
 mso-protection:locked visible;
 mso-style-name:"20% - 强调文字颜色 2";
 }
.style53
 {
 text-align:general;
 vertical-align:middle;
 white-space:nowrap;
 background:#F8CBAD;
 mso-pattern:auto none;
 color:#000000;
 font-size:11pt;
 font-weight:400;
 font-style:normal;
 font-family:"宋体";
 border:none;
 mso-protection:locked visible;
 mso-style-name:"40% - 强调文字颜色 2";
 }
.style54
 {
 text-align:general;
 vertical-align:middle;
 white-space:nowrap;
 background:#A5A5A5;
 mso-pattern:auto none;
 color:#FFFFFF;
 font-size:11pt;
 font-weight:400;
 font-style:normal;
 font-family:"宋体";
 border:none;
 mso-protection:locked visible;
 mso-style-name:"强调文字颜色 3";
 }
.style55
 {
 text-align:general;
 vertical-align:middle;
 white-space:nowrap;
 background:#FFC000;
 mso-pattern:auto none;
 color:#FFFFFF;
 font-size:11pt;
 font-weight:400;
 font-style:normal;
 font-family:"宋体";
 border:none;
 mso-protection:locked visible;
 mso-style-name:"强调文字颜色 4";
 }
.style56
 {
 text-align:general;
 vertical-align:middle;
 white-space:nowrap;
 background:#FFF2CC;
 mso-pattern:auto none;
 color:#000000;
 font-size:11pt;
 font-weight:400;
 font-style:normal;
 font-family:"宋体";
 border:none;
 mso-protection:locked visible;
 mso-style-name:"20% - 强调文字颜色 4";
 }
.style57
 {
 text-align:general;
 vertical-align:middle;
 white-space:nowrap;
 background:#FFE699;
 mso-pattern:auto none;
 color:#000000;
 font-size:11pt;
 font-weight:400;
 font-style:normal;
 font-family:"宋体";
 border:none;
 mso-protection:locked visible;
 mso-style-name:"40% - 强调文字颜色 4";
 }
.style58
 {
 text-align:general;
 vertical-align:middle;
 white-space:nowrap;
 background:#4472C4;
 mso-pattern:auto none;
 color:#FFFFFF;
 font-size:11pt;
 font-weight:400;
 font-style:normal;
 font-family:"宋体";
 border:none;
 mso-protection:locked visible;
 mso-style-name:"强调文字颜色 5";
 }
.style59
 {
 text-align:general;
 vertical-align:middle;
 white-space:nowrap;
 background:#B4C7E7;
 mso-pattern:auto none;
 color:#000000;
 font-size:11pt;
 font-weight:400;
 font-style:normal;
 font-family:"宋体";
 border:none;
 mso-protection:locked visible;
 mso-style-name:"40% - 强调文字颜色 5";
 }
.style60
 {
 text-align:general;
 vertical-align:middle;
 white-space:nowrap;
 background:#8FAADC;
 mso-pattern:auto none;
 color:#FFFFFF;
 font-size:11pt;
 font-weight:400;
 font-style:normal;
 font-family:"宋体";
 border:none;
 mso-protection:locked visible;
 mso-style-name:"60% - 强调文字颜色 5";
 }
.style61
 {
 text-align:general;
 vertical-align:middle;
 white-space:nowrap;
 background:#70AD47;
 mso-pattern:auto none;
 color:#FFFFFF;
 font-size:11pt;
 font-weight:400;
 font-style:normal;
 font-family:"宋体";
 border:none;
 mso-protection:locked visible;
 mso-style-name:"强调文字颜色 6";
 }
.style62
 {
 text-align:general;
 vertical-align:middle;
 white-space:nowrap;
 background:#C5E0B4;
 mso-pattern:auto none;
 color:#000000;
 font-size:11pt;
 font-weight:400;
 font-style:normal;
 font-family:"宋体";
 border:none;
 mso-protection:locked visible;
 mso-style-name:"40% - 强调文字颜色 6";
 }
.style63
 {
 text-align:general;
 vertical-align:middle;
 white-space:nowrap;
 background:#A9D18E;
 mso-pattern:auto none;
 color:#FFFFFF;
 font-size:11pt;
 font-weight:400;
 font-style:normal;
 font-family:"宋体";
 border:none;
 mso-protection:locked visible;
 mso-style-name:"60% - 强调文字颜色 6";
 }
.x64
 {
 mso-style-parent:style0;
 mso-number-format:General;
 text-align:center;
 vertical-align:middle;
 white-space:nowrap;
 background:white;
 mso-pattern:auto;
 color:#000000;
 font-size:17pt;
 font-weight:700;
 font-style:normal;
 font-family:"宋体";
 border-top:none;
 border-right:none;
 border-bottom:1px solid windowtext;
 border-left:none;
 mso-diagonal-down:none;
 mso-diagonal-up:none;
 mso-protection:locked visible;
 }
.x65
 {
 mso-style-parent:style0;
 mso-number-format:General;
 text-align:center;
 vertical-align:middle;
 white-space:normal;word-wrap:break-word;
 background:white;
 mso-pattern:auto;
 color:#000000;
 font-size:12pt;
 font-weight:700;
 font-style:normal;
 font-family:"宋体";
 border-top:1px solid windowtext;
 border-right:1px solid windowtext;
 border-bottom:1px solid windowtext;
 border-left:1px solid windowtext;
 mso-diagonal-down:none;
 mso-diagonal-up:none;
 mso-protection:locked visible;
 }
.x66
 {
 mso-style-parent:style0;
 mso-number-format:General;
 text-align:center;
 vertical-align:middle;
 white-space:nowrap;
 background:white;
 mso-pattern:auto;
 color:#000000;
 font-size:12pt;
 font-weight:700;
 font-style:normal;
 font-family:"宋体";
 border-top:1px solid windowtext;
 border-right:1px solid windowtext;
 border-bottom:1px solid windowtext;
 border-left:1px solid windowtext;
 mso-diagonal-down:none;
 mso-diagonal-up:none;
 mso-protection:locked visible;
 }
.x67
 {
 mso-style-parent:style0;
 mso-number-format:"0_ ";
 text-align:right;
 vertical-align:middle;
 white-space:nowrap;
 background:white;
 mso-pattern:auto;
 color:#0000FF;
 font-size:11pt;
 font-weight:400;
 font-style:normal;
 font-family:"宋体";
 border-top:1px solid windowtext;
 border-right:1px solid windowtext;
 border-bottom:1px solid windowtext;
 border-left:1px solid windowtext;
 mso-diagonal-down:none;
 mso-diagonal-up:none;
 mso-protection:locked visible;
 }
.x68
 {
 mso-style-parent:style0;
 mso-number-format:"0_ ";
 text-align:right;
 vertical-align:middle;
 white-space:nowrap;
 background:#CCCCFF;
 mso-pattern:auto none;
 color:#0000FF;
 font-size:11pt;
 font-weight:400;
 font-style:normal;
 font-family:"宋体";
 border-top:1px solid windowtext;
 border-right:1px solid windowtext;
 border-bottom:1px solid windowtext;
 border-left:1px solid windowtext;
 mso-diagonal-down:none;
 mso-diagonal-up:none;
 mso-protection:locked visible;
 }
.x69
 {
 mso-style-parent:style0;
 mso-number-format:General;
 text-align:center;
 vertical-align:middle;
 white-space:nowrap;
 background:white;
 mso-pattern:auto;
 color:#000000;
 font-size:17pt;
 font-weight:700;
 font-style:normal;
 font-family:"宋体";
 border:none;
 mso-protection:locked visible;
 }
.x70
 {
 mso-style-parent:style0;
 mso-number-format:General;
 text-align:general;
 vertical-align:bottom;
 white-space:nowrap;
 background:white;
 mso-pattern:auto;
 color:#000000;
 font-size:12pt;
 font-weight:400;
 font-style:normal;
 font-family:"宋体";
 border:none;
 mso-protection:locked visible;
 }
.x71
 {
 mso-style-parent:style0;
 mso-number-format:General;
 text-align:left;
 vertical-align:top;
 white-space:normal;word-wrap:break-word;
 background:white;
 mso-pattern:auto;
 color:#0000FF;
 font-size:11pt;
 font-weight:400;
 font-style:normal;
 font-family:"宋体";
 border:none;
 mso-protection:locked visible;
 }
.x72
 {
 mso-style-parent:style0;
 mso-number-format:General;
 text-align:left;
 vertical-align:top;
 white-space:normal;word-wrap:break-word;
 background:white;
 mso-pattern:auto;
 color:#000000;
 font-size:11pt;
 font-weight:400;
 font-style:normal;
 font-family:"宋体";
 border:none;
 mso-protection:locked visible;
 }
.x73
 {
 mso-style-parent:style0;
 mso-number-format:General;
 text-align:left;
 vertical-align:bottom;
 white-space:nowrap;
 background:white;
 mso-pattern:auto;
 color:#000000;
 font-size:12pt;
 font-weight:400;
 font-style:normal;
 font-family:"宋体";
 border:none;
 mso-protection:locked visible;
 }
.x74
 {
 mso-style-parent:style0;
 mso-number-format:General;
 text-align:general;
 vertical-align:bottom;
 white-space:normal;word-wrap:break-word;
 background:white;
 mso-pattern:auto;
 color:#000000;
 font-size:12pt;
 font-weight:400;
 font-style:normal;
 font-family:"宋体";
 border:none;
 mso-protection:locked visible;
 }
.x75
 {
 mso-style-parent:style0;
 mso-number-format:General;
 text-align:general;
 vertical-align:top;
 white-space:normal;word-wrap:break-word;
 background:white;
 mso-pattern:auto;
 color:#0000FF;
 font-size:11pt;
 font-weight:400;
 font-style:normal;
 font-family:"宋体";
 border:none;
 mso-protection:locked visible;
 }
.x76
 {
 mso-style-parent:style0;
 mso-number-format:General;
 text-align:general;
 vertical-align:top;
 white-space:normal;word-wrap:break-word;
 background:white;
 mso-pattern:auto;
 color:#000000;
 font-size:11pt;
 font-weight:400;
 font-style:normal;
 font-family:"宋体";
 border:none;
 mso-protection:locked visible;
 }
.x77
 {
 mso-style-parent:style0;
 mso-number-format:General;
 text-align:left;
 vertical-align:middle;
 white-space:nowrap;
 background:white;
 mso-pattern:auto;
 color:#000000;
 font-size:12pt;
 font-weight:400;
 font-style:normal;
 font-family:"宋体";
 border:none;
 mso-protection:locked visible;
 }
.x78
 {
 mso-style-parent:style0;
 mso-number-format:General;
 text-align:center;
 vertical-align:middle;
 white-space:nowrap;
 background:white;
 mso-pattern:auto;
 color:#000000;
 font-size:12pt;
 font-weight:400;
 font-style:normal;
 font-family:"宋体";
 border:none;
 mso-protection:locked visible;
 }
.x79
 {
 mso-style-parent:style0;
 mso-number-format:"0_ ";
 text-align:center;
 vertical-align:middle;
 white-space:nowrap;
 background:white;
 mso-pattern:auto;
 color:#000000;
 font-size:12pt;
 font-weight:400;
 font-style:normal;
 font-family:"宋体";
 border:none;
 mso-protection:locked visible;
 }
.x80
 {
 mso-style-parent:style0;
 mso-number-format:"0_ ";
 text-align:center;
 vertical-align:middle;
 white-space:nowrap;
 background:white;
 mso-pattern:auto;
 color:#0000FF;
 font-size:12pt;
 font-weight:400;
 font-style:normal;
 font-family:"宋体";
 border:none;
 mso-protection:locked visible;
 }
.x81
 {
 mso-style-parent:style0;
 mso-number-format:General;
 text-align:left;
 vertical-align:middle;
 white-space:nowrap;
 background:#FFFFFF;
 mso-pattern:auto none;
 color:#000000;
 font-size:12pt;
 font-weight:400;
 font-style:normal;
 font-family:"宋体";
 border:none;
 mso-protection:locked visible;
 }
.x82
 {
 mso-style-parent:style0;
 mso-number-format:General;
 text-align:center;
 vertical-align:middle;
 white-space:nowrap;
 background:#FFFFFF;
 mso-pattern:auto none;
 color:#000000;
 font-size:12pt;
 font-weight:400;
 font-style:normal;
 font-family:"宋体";
 border:none;
 mso-protection:locked visible;
 }
.x83
 {
 mso-style-parent:style0;
 mso-number-format:General;
 text-align:center;
 vertical-align:middle;
 white-space:nowrap;
 background:#C0C0C0;
 mso-pattern:auto none;
 color:#000000;
 font-size:12pt;
 font-weight:400;
 font-style:normal;
 font-family:"宋体";
 border:none;
 mso-protection:locked visible;
 }
.x84
 {
 mso-style-parent:style0;
 mso-number-format:General;
 text-align:center;
 vertical-align:bottom;
 white-space:nowrap;
 background:white;
 mso-pattern:auto;
 color:#000000;
 font-size:10pt;
 font-weight:400;
 font-style:normal;
 font-family:"宋体";
 border:none;
 mso-protection:locked visible;
 }
.x85
 {
 mso-style-parent:style0;
 mso-number-format:General;
 text-align:general;
 vertical-align:middle;
 white-space:nowrap;
 background:#C0C0C0;
 mso-pattern:auto none;
 color:#000000;
 font-size:12pt;
 font-weight:400;
 font-style:normal;
 font-family:"宋体";
 border:none;
 mso-protection:locked visible;
 }
.x86
 {
 mso-style-parent:style0;
 mso-number-format:General;
 text-align:general;
 vertical-align:middle;
 white-space:nowrap;
 background:white;
 mso-pattern:auto;
 color:#000000;
 font-size:12pt;
 font-weight:400;
 font-style:normal;
 font-family:"宋体";
 border:none;
 mso-protection:locked visible;
 }
.x87
 {
 mso-style-parent:style0;
 mso-number-format:General;
 text-align:center;
 vertical-align:middle;
 white-space:normal;word-wrap:break-word;
 background:white;
 mso-pattern:auto;
 color:#000000;
 font-size:11pt;
 font-weight:700;
 font-style:normal;
 font-family:"宋体";
 border-top:1px solid windowtext;
 border-right:1px solid windowtext;
 border-bottom:1px solid windowtext;
 border-left:1px solid windowtext;
 mso-diagonal-down:none;
 mso-diagonal-up:none;
 mso-protection:locked visible;
 }
.x88
 {
 mso-style-parent:style0;
 mso-number-format:General;
 text-align:center;
 vertical-align:middle;
 white-space:nowrap;
 background:white;
 mso-pattern:auto;
 color:#000000;
 font-size:11pt;
 font-weight:700;
 font-style:normal;
 font-family:"宋体";
 border-top:1px solid windowtext;
 border-right:1px solid windowtext;
 border-bottom:1px solid windowtext;
 border-left:1px solid windowtext;
 mso-diagonal-down:none;
 mso-diagonal-up:none;
 mso-protection:locked visible;
 }
.x89
 {
 mso-style-parent:style0;
 mso-number-format:General;
 text-align:center;
 vertical-align:middle;
 white-space:nowrap;
 background:white;
 mso-pattern:auto;
 color:#000000;
 font-size:11pt;
 font-weight:700;
 font-style:normal;
 font-family:"宋体";
 border-top:1px solid windowtext;
 border-right:1px solid windowtext;
 border-bottom:none;
 border-left:1px solid windowtext;
 mso-diagonal-down:none;
 mso-diagonal-up:none;
 mso-protection:locked visible;
 }
.x90
 {
 mso-style-parent:style0;
 mso-number-format:General;
 text-align:center;
 vertical-align:middle;
 white-space:nowrap;
 background:white;
 mso-pattern:auto;
 color:#000000;
 font-size:11pt;
 font-weight:700;
 font-style:normal;
 font-family:"宋体";
 border-top:1px solid windowtext;
 border-right:none;
 border-bottom:1px solid windowtext;
 border-left:1px solid windowtext;
 mso-diagonal-down:none;
 mso-diagonal-up:none;
 mso-protection:locked visible;
 }
.x91
 {
 mso-style-parent:style0;
 mso-number-format:General;
 text-align:center;
 vertical-align:middle;
 white-space:nowrap;
 background:white;
 mso-pattern:auto;
 color:#000000;
 font-size:11pt;
 font-weight:700;
 font-style:normal;
 font-family:"宋体";
 border-top:1px solid windowtext;
 border-right:1px solid windowtext;
 border-bottom:1px solid windowtext;
 border-left:none;
 mso-diagonal-down:none;
 mso-diagonal-up:none;
 mso-protection:locked visible;
 }
.x92
 {
 mso-style-parent:style0;
 mso-number-format:General;
 text-align:center;
 vertical-align:middle;
 white-space:nowrap;
 background:white;
 mso-pattern:auto;
 color:#000000;
 font-size:11pt;
 font-weight:700;
 font-style:normal;
 font-family:"宋体";
 border-top:none;
 border-right:1px solid windowtext;
 border-bottom:none;
 border-left:1px solid windowtext;
 mso-diagonal-down:none;
 mso-diagonal-up:none;
 mso-protection:locked visible;
 }
.x93
 {
 mso-style-parent:style0;
 mso-number-format:General;
 text-align:center;
 vertical-align:middle;
 white-space:nowrap;
 background:white;
 mso-pattern:auto;
 color:#000000;
 font-size:11pt;
 font-weight:700;
 font-style:normal;
 font-family:"宋体";
 border-top:1px solid windowtext;
 border-right:1px solid windowtext;
 border-bottom:1px solid windowtext;
 border-left:1px solid windowtext;
 mso-diagonal-down:none;
 mso-diagonal-up:none;
 mso-protection:locked visible;
 }
.x94
 {
 mso-style-parent:style0;
 mso-number-format:General;
 text-align:right;
 vertical-align:middle;
 white-space:nowrap;
 background:#CCCCFF;
 mso-pattern:auto none;
 color:#0000FF;
 font-size:11pt;
 font-weight:400;
 font-style:normal;
 font-family:"宋体";
 border-top:1px solid windowtext;
 border-right:1px solid windowtext;
 border-bottom:1px solid windowtext;
 border-left:1px solid windowtext;
 mso-diagonal-down:none;
 mso-diagonal-up:none;
 mso-protection:locked visible;
 }
-->
</style> 
  <!--[if gte mso 9]><xml>
 <x:ExcelWorkbook>
  <x:ExcelWorksheets>
   <x:ExcelWorksheet>
    <x:Name>第二十六表说明2</x:Name>
<x:WorksheetOptions>
 <x:StandardWidth>2048</x:StandardWidth>
 <x:Print>
  <x:ValidPrinterInfo/>
  <x:PaperSizeIndex>9</x:PaperSizeIndex>
  <x:HorizontalResolution>600</x:HorizontalResolution>
  <x:VerticalResolution>600</x:VerticalResolution>
  <x:PrintErrors>Blank</x:PrintErrors>
 </x:Print>
 <x:Selected/>
 <x:ProtectContents>False</x:ProtectContents>
 <x:ProtectObjects>False</x:ProtectObjects>
 <x:ProtectScenarios>False</x:ProtectScenarios>
</x:WorksheetOptions>
   </x:ExcelWorksheet>
  </x:ExcelWorksheets>
  <x:WindowHeight>9300</x:WindowHeight>
  <x:WindowWidth>22368</x:WindowWidth>
  <x:WindowTopX>0</x:WindowTopX>
  <x:WindowTopY>0</x:WindowTopY>
  <x:RefModeR1C1/>
  <x:TabRatio>600</x:TabRatio>
  <x:ActiveSheet>0</x:ActiveSheet>
 </x:ExcelWorkbook>
</xml><![endif]--> 
 </head> 
 <body link="blue" vlink="purple" style="margin: 0;"> 
  <table border="0" cellpadding="0" cellspacing="0" width="980" style="border-collapse: collapse;table-layout:fixed; width: 100%;overflow-y: auto;"> 
   <colgroup>
    <col width="140" span="7" style="mso-width-source:userset;width:140px"> 
   </colgroup>
   <tbody>
    <tr height="50" style="mso-height-source:userset;height:37.5pt" id="r0"> 
     <td colspan="7" height="49" class="x64" width="980" style="border-bottom:1px solid windowtext;height:36.75pt;">第二十六表 “其他”党组织数量情况说明（A16）（乡镇社区作为村统计）</td>
    </tr> 
    <tr height="80" style="mso-height-source:userset;height:60pt" id="r1"> 
     <td height="78" class="x87" style="height:58.5pt;">乡镇<br>党组织</td> 
     <td class="x87">街道<br>党组织</td> 
     <td class="x87">社区（居委会）<br>党组织</td> 
     <td class="x87">行政村<br>党组织</td> 
     <td class="x87">个体工商户<br>党组织</td> 
     <td class="x88">其 他</td> 
     <td class="x88">合 计</td> 
    </tr> 
    <tr height="80" style="mso-height-source:userset;height:60pt" id="r3">
     <td height="78" class="x67" style="text-align: center;overflow: hidden;" table-name="bbsm_第二十六表说明2" table-cell="0_1_1" onclick="sendMessage(this)">#(table0.cell_1_1 ?? == 0 ? '' : table0.cell_1_1??)</td> 
     <td class="x67" table-name="bbsm_第二十六表说明2" table-cell="0_1_2" onclick="sendMessage(this)" style="text-align: center;overflow: hidden;">#(table0.cell_1_2 ?? == 0 ? '' : table0.cell_1_2??)</td> 
     <td class="x67" table-name="bbsm_第二十六表说明2" table-cell="0_1_3" onclick="sendMessage(this)" style="text-align: center;overflow: hidden;">#(table0.cell_1_3 ?? == 0 ? '' : table0.cell_1_3??)</td> 
     <td class="x67" table-name="bbsm_第二十六表说明2" table-cell="0_1_4" onclick="sendMessage(this)" style="text-align: center;overflow: hidden;">#(table0.cell_1_4 ?? == 0 ? '' : table0.cell_1_4??)</td> 
     <td class="x67" table-name="bbsm_第二十六表说明2" table-cell="0_1_5" onclick="sendMessage(this)" style="text-align: center;overflow: hidden;">#(table0.cell_1_5 ?? == 0 ? '' : table0.cell_1_5??)</td> 
     <td class="x67" table-name="bbsm_第二十六表说明2" table-cell="0_1_6" onclick="sendMessage(this)" style="text-align: center;overflow: hidden;">#(table0.cell_1_6 ?? == 0 ? '' : table0.cell_1_6??)</td> 
     <td class="x68" table-name="bbsm_第二十六表说明2" table-cell="0_1_7" onclick="sendMessage(this)" style="text-align: center;overflow: hidden;">#(table0.cell_1_7 ?? == 0 ? '' : table0.cell_1_7??)</td> 
    </tr> 
    <!--[if supportMisalignedColumns]--> 
    <tr height="0" style="display:none"> 
     <td width="140" style="width:105pt"></td> 
     <td width="140" style="width:105pt"></td> 
     <td width="140" style="width:105pt"></td> 
     <td width="140" style="width:105pt"></td> 
     <td width="140" style="width:105pt"></td> 
     <td width="140" style="width:105pt"></td> 
     <td width="140" style="width:105pt"></td> 
    </tr> 
    <!--[endif]--> 
   </tbody>
  </table>
  <script type="text/javascript">function sendMessage(cell) {
   var obj = new Object();
   var tableName = cell.getAttribute("table-name");
   var tableCellIndex = cell.getAttribute("table-cell");
   obj['tableName'] = tableName;
   obj['tableCellIndex'] = tableCellIndex;
   console.log(obj);
   window.parent.postMessage(JSON.stringify(obj))
  }</script>
 </body>
</html>