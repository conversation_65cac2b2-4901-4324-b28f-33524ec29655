<!doctype html public "-//w3c//dtd xhtml 1.0 transitional//en" "http://www.w3.org/tr/xhtml1/dtd/xhtml1-transitional.dtd">
<html xmlns:v="urn:schemas-microsoft-com:vml"
xmlns:o="urn:schemas-microsoft-com:office:office"
xmlns:x="urn:schemas-microsoft-com:office:excel"
xmlns="http://www.w3.org/TR/REC-html40">

<head>
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<meta name="ProgId" content="Excel.Sheet">
<meta name="Generator" content="Aspose.Cell 20.7.0">
<link rel="File-List" href="19.1_files/filelist.xml">
<link rel="Edit-Time-Data" href="19.1_files/editdata.mso">
<link rel="OLE-Object-Data" href="19.1_files/oledata.mso">
<!--[if gte mso 9]><xml>
 <o:DocumentProperties>
  <o:Author>Administrator</o:Author>
  <o:Created>2023-11-27T12:39:21Z</o:Created>
  <o:LastSaved>2023-11-27T12:40:17Z</o:LastSaved>
</o:DocumentProperties>
</xml><![endif]-->
<style>
<!--table
 {mso-displayed-decimal-separator:"\.";
 mso-displayed-thousand-separator:"\,";}
@page
 {
 mso-header-data:"";
 mso-footer-data:"";
 margin:0in 0in 0in 0.3in;
 mso-header-margin:0.5in;
 mso-footer-margin:0.5in;
 mso-page-orientation:Landscape;
 mso-horizontal-page-align:center;
 mso-vertical-page-align:center;
 }
tr
 {mso-height-source:auto;
 mso-ruby-visibility:none;}
col
 {mso-width-source:auto;
 mso-ruby-visibility:none;}
br
 {mso-data-placement:same-cell;}
ruby
 {ruby-align:left;}
.style0
 {
 mso-number-format:General;
 text-align:general;
 vertical-align:bottom;
 white-space:nowrap;
 background:auto;
 mso-pattern:auto;
 color:#000000;
 font-size:10pt;
 font-weight:400;
 font-style:normal;
 font-family:"Arial","sans-serif";
 border:none;
 mso-protection:locked visible;
 mso-style-name:Normal;
 mso-style-id:0;}
.font0
 {
 color:#000000;
 font-size:10pt;
 font-weight:400;
 font-style:normal;
 font-family:"Arial","sans-serif"; }
.font1
 {
 color:#000000;
 font-size:10pt;
 font-weight:400;
 font-style:normal;
 font-family:"Arial","sans-serif"; }
.font2
 {
 color:#000000;
 font-size:15pt;
 font-weight:700;
 font-style:normal;
 font-family:"宋体","sans-serif"; }
.font3
 {
 color:#000000;
 font-size:11pt;
 font-weight:400;
 font-style:normal;
 font-family:"宋体","sans-serif"; }
.font4
 {
 color:#000000;
 font-size:11pt;
 font-weight:700;
 font-style:normal;
 font-family:"宋体","sans-serif"; }
.font5
 {
 color:#000000;
 font-size:11pt;
 font-weight:400;
 font-style:normal;
 font-family:"宋体","sans-serif"; }
.font6
 {
 color:#3F3F76;
 font-size:11pt;
 font-weight:400;
 font-style:normal;
 font-family:"宋体","sans-serif"; }
.font7
 {
 color:#9C0006;
 font-size:11pt;
 font-weight:400;
 font-style:normal;
 font-family:"宋体","sans-serif"; }
.font8
 {
 color:#FFFFFF;
 font-size:11pt;
 font-weight:400;
 font-style:normal;
 font-family:"宋体","sans-serif"; }
.font9
 {
 color:#0000FF;
 font-size:11pt;
 font-weight:400;
 font-style:normal;
 text-decoration:underline;
 font-family:"宋体","sans-serif"; }
.font10
 {
 color:#800080;
 font-size:11pt;
 font-weight:400;
 font-style:normal;
 text-decoration:underline;
 font-family:"宋体","sans-serif"; }
.font11
 {
 color:#44546A;
 font-size:11pt;
 font-weight:700;
 font-style:normal;
 font-family:"宋体","sans-serif"; }
.font12
 {
 color:#FF0000;
 font-size:11pt;
 font-weight:400;
 font-style:normal;
 font-family:"宋体","sans-serif"; }
.font13
 {
 color:#44546A;
 font-size:18pt;
 font-weight:700;
 font-style:normal;
 font-family:"宋体","sans-serif"; }
.font14
 {
 color:#7F7F7F;
 font-size:11pt;
 font-weight:400;
 font-style:italic;
 font-family:"宋体","sans-serif"; }
.font15
 {
 color:#44546A;
 font-size:15pt;
 font-weight:700;
 font-style:normal;
 font-family:"宋体","sans-serif"; }
.font16
 {
 color:#44546A;
 font-size:13pt;
 font-weight:700;
 font-style:normal;
 font-family:"宋体","sans-serif"; }
.font17
 {
 color:#3F3F3F;
 font-size:11pt;
 font-weight:700;
 font-style:normal;
 font-family:"宋体","sans-serif"; }
.font18
 {
 color:#FA7D00;
 font-size:11pt;
 font-weight:700;
 font-style:normal;
 font-family:"宋体","sans-serif"; }
.font19
 {
 color:#FFFFFF;
 font-size:11pt;
 font-weight:700;
 font-style:normal;
 font-family:"宋体","sans-serif"; }
.font20
 {
 color:#FA7D00;
 font-size:11pt;
 font-weight:400;
 font-style:normal;
 font-family:"宋体","sans-serif"; }
.font21
 {
 color:#000000;
 font-size:11pt;
 font-weight:700;
 font-style:normal;
 font-family:"宋体","sans-serif"; }
.font22
 {
 color:#006100;
 font-size:11pt;
 font-weight:400;
 font-style:normal;
 font-family:"宋体","sans-serif"; }
.font23
 {
 color:#9C6500;
 font-size:11pt;
 font-weight:400;
 font-style:normal;
 font-family:"宋体","sans-serif"; }
td
 {mso-style-parent:style0;
 mso-number-format:General;
 text-align:general;
 vertical-align:bottom;
 white-space:nowrap;
 background:auto;
 mso-pattern:auto;
 color:#000000;
 font-size:10pt;
 font-weight:400;
 font-style:normal;
 font-family:"Arial","sans-serif";
 border:none;
 mso-protection:locked visible;
 mso-ignore:padding;}
.style0
 {
 text-align:general;
 vertical-align:bottom;
 white-space:nowrap;
 background:auto;
 mso-pattern:auto;
 font-size:10pt;
 font-weight:400;
 font-style:normal;
 font-family:"Arial","sans-serif";
 mso-protection:locked visible;
 mso-style-name:"Normal";
 }
.style1
 {
 text-align:general;
 vertical-align:middle;
 white-space:nowrap;
 background:auto;
 mso-pattern:auto;
 color:#000000;
 font-size:10pt;
 font-weight:400;
 font-style:normal;
 font-family:"Arial","sans-serif";
 mso-protection:locked visible;
 }
.style2
 {
 text-align:general;
 vertical-align:middle;
 white-space:nowrap;
 background:auto;
 mso-pattern:auto;
 color:#000000;
 font-size:10pt;
 font-weight:400;
 font-style:normal;
 font-family:"Arial","sans-serif";
 mso-protection:locked visible;
 }
.style3
 {
 text-align:general;
 vertical-align:middle;
 white-space:nowrap;
 background:auto;
 mso-pattern:auto;
 color:#000000;
 font-size:10pt;
 font-weight:400;
 font-style:normal;
 font-family:"Arial","sans-serif";
 mso-protection:locked visible;
 }
.style4
 {
 text-align:general;
 vertical-align:middle;
 white-space:nowrap;
 background:auto;
 mso-pattern:auto;
 color:#000000;
 font-size:10pt;
 font-weight:400;
 font-style:normal;
 font-family:"Arial","sans-serif";
 mso-protection:locked visible;
 }
.style5
 {
 text-align:general;
 vertical-align:middle;
 white-space:nowrap;
 background:auto;
 mso-pattern:auto;
 color:#000000;
 font-size:10pt;
 font-weight:400;
 font-style:normal;
 font-family:"Arial","sans-serif";
 mso-protection:locked visible;
 }
.style6
 {
 text-align:general;
 vertical-align:middle;
 white-space:nowrap;
 background:auto;
 mso-pattern:auto;
 color:#000000;
 font-size:10pt;
 font-weight:400;
 font-style:normal;
 font-family:"Arial","sans-serif";
 mso-protection:locked visible;
 }
.style7
 {
 text-align:general;
 vertical-align:middle;
 white-space:nowrap;
 background:auto;
 mso-pattern:auto;
 color:#000000;
 font-size:10pt;
 font-weight:400;
 font-style:normal;
 font-family:"Arial","sans-serif";
 mso-protection:locked visible;
 }
.style8
 {
 text-align:general;
 vertical-align:middle;
 white-space:nowrap;
 background:auto;
 mso-pattern:auto;
 color:#000000;
 font-size:10pt;
 font-weight:400;
 font-style:normal;
 font-family:"Arial","sans-serif";
 mso-protection:locked visible;
 }
.style9
 {
 text-align:general;
 vertical-align:middle;
 white-space:nowrap;
 background:auto;
 mso-pattern:auto;
 color:#000000;
 font-size:10pt;
 font-weight:400;
 font-style:normal;
 font-family:"Arial","sans-serif";
 mso-protection:locked visible;
 }
.style10
 {
 text-align:general;
 vertical-align:middle;
 white-space:nowrap;
 background:auto;
 mso-pattern:auto;
 color:#000000;
 font-size:10pt;
 font-weight:400;
 font-style:normal;
 font-family:"Arial","sans-serif";
 mso-protection:locked visible;
 }
.style11
 {
 text-align:general;
 vertical-align:middle;
 white-space:nowrap;
 background:auto;
 mso-pattern:auto;
 color:#000000;
 font-size:10pt;
 font-weight:400;
 font-style:normal;
 font-family:"Arial","sans-serif";
 mso-protection:locked visible;
 }
.style12
 {
 text-align:general;
 vertical-align:middle;
 white-space:nowrap;
 background:auto;
 mso-pattern:auto;
 color:#000000;
 font-size:10pt;
 font-weight:400;
 font-style:normal;
 font-family:"Arial","sans-serif";
 mso-protection:locked visible;
 }
.style13
 {
 text-align:general;
 vertical-align:middle;
 white-space:nowrap;
 background:auto;
 mso-pattern:auto;
 color:#000000;
 font-size:10pt;
 font-weight:400;
 font-style:normal;
 font-family:"Arial","sans-serif";
 mso-protection:locked visible;
 }
.style14
 {
 text-align:general;
 vertical-align:middle;
 white-space:nowrap;
 background:auto;
 mso-pattern:auto;
 color:#000000;
 font-size:10pt;
 font-weight:400;
 font-style:normal;
 font-family:"Arial","sans-serif";
 mso-protection:locked visible;
 }
.x15
 {
 mso-style-parent:style0;
 mso-number-format:General;
 text-align:general;
 vertical-align:bottom;
 white-space:nowrap;
 background:auto;
 mso-pattern:auto;
 font-size:10pt;
 font-weight:400;
 font-style:normal;
 font-family:"Arial","sans-serif";
 mso-protection:locked visible;
 }
.x16
 {
 mso-style-parent:style0;
 mso-number-format:General;
 text-align:general;
 vertical-align:bottom;
 white-space:nowrap;
 background:auto;
 mso-pattern:auto;
 font-size:10pt;
 font-weight:400;
 font-style:normal;
 font-family:"Arial","sans-serif";
 mso-protection:locked visible;
 }
.x17
 {
 mso-style-parent:style0;
 mso-number-format:"\@";
 text-align:center;
 vertical-align:middle;
 white-space:nowrap;
 background:#FFFFFF;
 mso-pattern:auto none;
 font-size:15pt;
 font-weight:700;
 font-style:normal;
 font-family:"宋体","sans-serif";
 mso-protection:locked visible;
 }
.x18
 {
 mso-style-parent:style0;
 mso-number-format:"\@";
 text-align:center;
 vertical-align:middle;
 white-space:nowrap;
 background:#FFFFFF;
 mso-pattern:auto none;
 font-size:11pt;
 font-weight:400;
 font-style:normal;
 font-family:"宋体","sans-serif";
 mso-protection:locked visible;
 }
.x19
 {
 mso-style-parent:style0;
 mso-number-format:"\@";
 text-align:left;
 vertical-align:middle;
 white-space:nowrap;
 background:#FFFFFF;
 mso-pattern:auto none;
 font-size:11pt;
 font-weight:400;
 font-style:normal;
 font-family:"宋体","sans-serif";
 mso-protection:locked visible;
 }
.x20
 {
 mso-style-parent:style0;
 mso-number-format:"\@";
 text-align:center;
 vertical-align:middle;
 white-space:nowrap;
 background:#FFFFFF;
 mso-pattern:auto none;
 font-size:11pt;
 font-weight:700;
 font-style:normal;
 font-family:"宋体","sans-serif";
 border-top:1px solid #000000;
 border-right:none;
 border-bottom:none;
 border-left:none;
 mso-diagonal-down:none;
 mso-diagonal-up:none;
 mso-protection:locked visible;
 }
.x21
 {
 mso-style-parent:style0;
 mso-number-format:"\@";
 text-align:center;
 vertical-align:middle;
 white-space:nowrap;
 background:#FFFFFF;
 mso-pattern:auto none;
 font-size:11pt;
 font-weight:700;
 font-style:normal;
 font-family:"宋体","sans-serif";
 border-top:1px solid #000000;
 border-right:none;
 border-bottom:none;
 border-left:none;
 mso-diagonal-down:none;
 mso-diagonal-up:none;
 mso-protection:locked visible;
 }
.x22
 {
 mso-style-parent:style0;
 mso-number-format:"\@";
 text-align:center;
 vertical-align:middle;
 white-space:nowrap;
 background:#FFFFFF;
 mso-pattern:auto none;
 font-size:11pt;
 font-weight:700;
 font-style:normal;
 font-family:"宋体","sans-serif";
 border-top:1px solid #000000;
 border-right:none;
 border-bottom:none;
 border-left:1px solid #000000;
 mso-diagonal-down:none;
 mso-diagonal-up:none;
 mso-protection:locked visible;
 }
.x23
 {
 mso-style-parent:style0;
 mso-number-format:"\@";
 text-align:center;
 vertical-align:middle;
 white-space:normal;word-wrap:break-word;
 background:#FFFFFF;
 mso-pattern:auto none;
 font-size:11pt;
 font-weight:700;
 font-style:normal;
 font-family:"宋体","sans-serif";
 border-top:1px solid #000000;
 border-right:none;
 border-bottom:none;
 border-left:1px solid #000000;
 mso-diagonal-down:none;
 mso-diagonal-up:none;
 mso-protection:locked visible;
 }
.x24
 {
 mso-style-parent:style0;
 mso-number-format:"\@";
 text-align:right;
 vertical-align:middle;
 white-space:nowrap;
 background:#FFFFFF;
 mso-pattern:auto none;
 font-size:11pt;
 font-weight:400;
 font-style:normal;
 font-family:"宋体","sans-serif";
 border-top:1px solid #000000;
 border-right:none;
 border-bottom:none;
 border-left:1px solid #000000;
 mso-diagonal-down:none;
 mso-diagonal-up:none;
 mso-protection:locked visible;
 }
.x25
 {
 mso-style-parent:style0;
 mso-number-format:"\@";
 text-align:center;
 vertical-align:middle;
 white-space:normal;word-wrap:break-word;
 background:#FFFFFF;
 mso-pattern:auto none;
 font-size:11pt;
 font-weight:700;
 font-style:normal;
 font-family:"宋体","sans-serif";
 border-top:1px solid #000000;
 border-right:none;
 border-bottom:none;
 border-left:none;
 mso-diagonal-down:none;
 mso-diagonal-up:none;
 mso-protection:locked visible;
 }
.x26
 {
 mso-style-parent:style0;
 mso-number-format:"\@";
 text-align:center;
 vertical-align:middle;
 white-space:normal;word-wrap:break-word;
 background:#FFFFFF;
 mso-pattern:auto none;
 font-size:11pt;
 font-weight:700;
 font-style:normal;
 font-family:"宋体","sans-serif";
 border-top:1px solid #000000;
 border-right:none;
 border-bottom:none;
 border-left:none;
 mso-diagonal-down:none;
 mso-diagonal-up:none;
 mso-protection:locked visible;
 }
.x27
 {
 mso-style-parent:style0;
 mso-number-format:"\@";
 text-align:left;
 vertical-align:middle;
 white-space:nowrap;
 background:#FFFFFF;
 mso-pattern:auto none;
 font-size:11pt;
 font-weight:400;
 font-style:normal;
 font-family:"宋体","sans-serif";
 border-top:1px solid #000000;
 border-right:none;
 border-bottom:none;
 border-left:none;
 mso-diagonal-down:none;
 mso-diagonal-up:none;
 mso-protection:locked visible;
 }
.x28
 {
 mso-style-parent:style0;
 mso-number-format:"\@";
 text-align:left;
 vertical-align:middle;
 white-space:nowrap;
 background:#FFFFFF;
 mso-pattern:auto none;
 font-size:11pt;
 font-weight:400;
 font-style:normal;
 font-family:"宋体","sans-serif";
 border-top:none;
 border-right:none;
 border-bottom:none;
 border-left:1px solid #000000;
 mso-diagonal-down:none;
 mso-diagonal-up:none;
 mso-protection:locked visible;
 }
 .xl68
	{mso-style-parent:style0;
	text-align:center;
	color:#0000FF;
	font-size:14.0pt;
	mso-font-charset:134;
	border:.5pt solid windowtext;}
-->
</style>
<!--[if gte mso 9]><xml>
 <x:ExcelWorkbook>
  <x:ExcelWorksheets>
   <x:ExcelWorksheet>
    <x:Name>第十九表说明1</x:Name>
<x:WorksheetOptions>
 <x:StandardWidth>2048</x:StandardWidth>
 <x:Print>
  <x:FitWidth>0</x:FitWidth>
  <x:FitHeight>0</x:FitHeight>
  <x:LeftToRight/>
  <x:ValidPrinterInfo/>
  <x:PaperSizeIndex>9</x:PaperSizeIndex>
  <x:Scale>97</x:Scale>
  <x:HorizontalResolution>300</x:HorizontalResolution>
  <x:VerticalResolution>300</x:VerticalResolution>
 </x:Print>
 <x:Selected/>
 <x:ProtectContents>False</x:ProtectContents>
 <x:ProtectObjects>False</x:ProtectObjects>
 <x:ProtectScenarios>False</x:ProtectScenarios>
</x:WorksheetOptions>
   </x:ExcelWorksheet>
  </x:ExcelWorksheets>
  <x:WindowHeight>12540</x:WindowHeight>
  <x:WindowWidth>28125</x:WindowWidth>
  <x:WindowTopX>240</x:WindowTopX>
  <x:WindowTopY>120</x:WindowTopY>
  <x:RefModeR1C1/>
  <x:TabRatio>600</x:TabRatio>
  <x:ActiveSheet>0</x:ActiveSheet>
 </x:ExcelWorkbook>
</xml><![endif]-->
</head>
<body link="blue" vlink="purple" style="margin: 0;">
  <table width="1024" border="0" cellpadding="0" cellspacing="0" style='width:100%;border-collapse:collapse;table-layout:fixed;overflow-y: auto;'>
 <col class='x16' width='49' style='mso-width-source:userset;background:none;width:36.75pt'>
 <col class='x16' width='91' style='mso-width-source:userset;background:none;width:68.25pt'>
 <col class='x16' width='98' style='mso-width-source:userset;background:none;width:73.5pt'>
 <col class='x16' width='77' style='mso-width-source:userset;background:none;width:57.75pt'>
 <col class='x16' width='91' style='mso-width-source:userset;background:none;width:68.25pt'>
 <col class='x16' width='77' span='8' style='mso-width-source:userset;background:none;width:57.75pt'>
 <col class='x16' width='21' style='mso-width-source:userset;background:none;width:15.75pt'>
 <tr height='55' style='mso-height-source:userset;height:41.25pt' id='r0'>
<td colspan='13' height='54' class='x17' width='1022' style='height:40.5pt;'>第十九表<span style='mso-spacerun:yes;'>&nbsp; </span>“生产性服务业”发展党员数量情况说明（H1）</td>
<td class='x19' width='21' style='width:15.75pt;'></td>
 </tr>
 <tr height='127' style='mso-height-source:userset;height:95.7pt' id='r1'>
<td colspan='2' height='125' class='x20' style='height:94.2pt;'>项目</td>
<td class='x22'>总数</td>
<td class='x23'>研发设<br>计与其<br>他技术<br>服务</td>
<td class='x23'>货物运输、<br>通用航空<br>生产、仓储<br>和邮政快<br>递服务</td>
<td class='x23'>信息<br>服务</td>
<td class='x23'>金融<br>服务</td>
<td class='x23'>节能与<br>环保服<br>务</td>
<td class='x23'>生产性<br>租赁<br>服务</td>
<td class='x22'>商务服务</td>
<td class='x23'>人力资源<br>管理与职<br>业教育培<br>训服务</td>
<td class='x23'>批发与贸<br>易经纪代<br>理服务</td>
<td class='x23'><br>生产性支<br>持服务</td>
<td class='x28'></td>
 </tr>
 <tr height='55' style='mso-height-source:userset;height:41.25pt' id='r2'>
<td colspan='2' height='53' class='x20' style='height:39.75pt;'>合计</td>
<td class="xl68"  onclick="sendMessage(this)" table-name="2023_19_1" table-cell="0_1_1" style="text-align: center;overflow: hidden;">#(table0.cell_1_1 ?? == 0 ? '' : table0.cell_1_1??)</span></td>
    <td class="xl68"  onclick="sendMessage(this)" table-name="2023_19_1" table-cell="0_1_2" style="text-align: center;overflow: hidden;">#(table0.cell_1_2 ?? == 0 ? '' : table0.cell_1_2??)</span></td>
    <td class="xl68"  onclick="sendMessage(this)" table-name="2023_19_1" table-cell="0_1_3" style="text-align: center;overflow: hidden;">#(table0.cell_1_3 ?? == 0 ? '' : table0.cell_1_3??)</span></td>
    <td class="xl68"  onclick="sendMessage(this)" table-name="2023_19_1" table-cell="0_1_4" style="text-align: center;overflow: hidden;">#(table0.cell_1_4 ?? == 0 ? '' : table0.cell_1_4??)</span></td>
    <td class="xl68"  onclick="sendMessage(this)" table-name="2023_19_1" table-cell="0_1_5" style="text-align: center;overflow: hidden;">#(table0.cell_1_5 ?? == 0 ? '' : table0.cell_1_5??)</span></td>
    <td class="xl68"  onclick="sendMessage(this)" table-name="2023_19_1" table-cell="0_1_6" style="text-align: center;overflow: hidden;">#(table0.cell_1_6 ?? == 0 ? '' : table0.cell_1_6??)</span></td>
    <td class="xl68"  onclick="sendMessage(this)" table-name="2023_19_1" table-cell="0_1_7" style="text-align: center;overflow: hidden;">#(table0.cell_1_7 ?? == 0 ? '' : table0.cell_1_7??)</span></td>
    <td class="xl68"  onclick="sendMessage(this)" table-name="2023_19_1" table-cell="0_1_8" style="text-align: center;overflow: hidden;">#(table0.cell_1_8 ?? == 0 ? '' : table0.cell_1_8??)</span></td>
    <td class="xl68"  onclick="sendMessage(this)" table-name="2023_19_1" table-cell="0_1_9" style="text-align: center;overflow: hidden;">#(table0.cell_1_9 ?? == 0 ? '' : table0.cell_1_9??)</span></td>
    <td class="xl68"  onclick="sendMessage(this)" table-name="2023_19_1" table-cell="0_1_10" style="text-align: center;overflow: hidden;">#(table0.cell_1_10 ?? == 0 ? '' : table0.cell_1_10??)</span></td>
    <td class="xl68"  onclick="sendMessage(this)" table-name="2023_19_1" table-cell="0_1_11" style="text-align: center;overflow: hidden;">#(table0.cell_1_11 ?? == 0 ? '' : table0.cell_1_11??)</span></td>
 </tr>
 <tr height='55' style='mso-height-source:userset;height:41.25pt' id='r3'>
<td colspan='2' height='53' class='x20' style='height:39.75pt;'>事业单位</td>
<td class="xl68"  onclick="sendMessage(this)" table-name="2023_19_1" table-cell="0_2_1" style="text-align: center;overflow: hidden;">#(table0.cell_2_1 ?? == 0 ? '' : table0.cell_2_1??)</span></td>
    <td class="xl68"  onclick="sendMessage(this)" table-name="2023_19_1" table-cell="0_2_2" style="text-align: center;overflow: hidden;">#(table0.cell_2_2 ?? == 0 ? '' : table0.cell_2_2??)</span></td>
    <td class="xl68"  onclick="sendMessage(this)" table-name="2023_19_1" table-cell="0_2_3"style="text-align: center;overflow: hidden;">#(table0.cell_2_3 ?? == 0 ? '' : table0.cell_2_3??)</span></td>
    <td class="xl68"  onclick="sendMessage(this)" table-name="2023_19_1" table-cell="0_2_4" style="text-align: center;overflow: hidden;">#(table0.cell_2_4 ?? == 0 ? '' : table0.cell_2_4??)</span></td>
    <td class="xl68"  onclick="sendMessage(this)" table-name="2023_19_1" table-cell="0_2_5" style="text-align: center;overflow: hidden;">#(table0.cell_2_5 ?? == 0 ? '' : table0.cell_2_5??)</span></td>
    <td class="xl68"  onclick="sendMessage(this)" table-name="2023_19_1" table-cell="0_2_6" style="text-align: center;overflow: hidden;">#(table0.cell_2_6 ?? == 0 ? '' : table0.cell_2_6??)</span></td>
    <td class="xl68"  onclick="sendMessage(this)" table-name="2023_19_1" table-cell="0_2_7" style="text-align: center;overflow: hidden;">#(table0.cell_2_7 ?? == 0 ? '' : table0.cell_2_7??)</span></td>
    <td class="xl68"  onclick="sendMessage(this)" table-name="2023_19_1" table-cell="0_2_8" style="text-align: center;overflow: hidden;">#(table0.cell_2_8 ?? == 0 ? '' : table0.cell_2_8??)</span></td>
    <td class="xl68"  onclick="sendMessage(this)" table-name="2023_19_1" table-cell="0_2_9" style="text-align: center;overflow: hidden;">#(table0.cell_2_9 ?? == 0 ? '' : table0.cell_2_9??)</span></td>
    <td class="xl68"  onclick="sendMessage(this)" table-name="2023_19_1" table-cell="0_2_10" style="text-align: center;overflow: hidden;">#(table0.cell_2_10 ?? == 0 ? '' : table0.cell_2_10??)</span></td>
    <td class="xl68"  onclick="sendMessage(this)" table-name="2023_19_1" table-cell="0_2_11" style="text-align: center;overflow: hidden;">#(table0.cell_2_11 ?? == 0 ? '' : table0.cell_2_11??)</span></td>
 </tr>
 <tr height='55' style='mso-height-source:userset;height:41.25pt' id='r4'>
<td colspan='2' height='53' class='x25' style='height:39.75pt;'>公有制经济控制<br>企业</td>
<td class="xl68"  onclick="sendMessage(this)" table-name="2023_19_1" table-cell="0_3_1" style="text-align: center;overflow: hidden;">#(table0.cell_3_1 ?? == 0 ? '' : table0.cell_3_1??)</span></td>
    <td class="xl68"  onclick="sendMessage(this)" table-name="2023_19_1" table-cell="0_3_2" style="text-align: center;overflow: hidden;">#(table0.cell_3_2 ?? == 0 ? '' : table0.cell_3_2??)</span></td>
    <td class="xl68"  onclick="sendMessage(this)" table-name="2023_19_1" table-cell="0_3_3" style="text-align: center;overflow: hidden;">#(table0.cell_3_3 ?? == 0 ? '' : table0.cell_3_3??)</span></td>
    <td class="xl68"  onclick="sendMessage(this)" table-name="2023_19_1" table-cell="0_3_4" style="text-align: center;overflow: hidden;">#(table0.cell_3_4 ?? == 0 ? '' : table0.cell_3_4??)</span></td>
    <td class="xl68"  onclick="sendMessage(this)" table-name="2023_19_1" table-cell="0_3_5" style="text-align: center;overflow: hidden;">#(table0.cell_3_5 ?? == 0 ? '' : table0.cell_3_5??)</span></td>
    <td class="xl68"  onclick="sendMessage(this)" table-name="2023_19_1" table-cell="0_3_6" style="text-align: center;overflow: hidden;">#(table0.cell_3_6 ?? == 0 ? '' : table0.cell_3_6??)</span></td>
    <td class="xl68"  onclick="sendMessage(this)" table-name="2023_19_1" table-cell="0_3_7" style="text-align: center;overflow: hidden;">#(table0.cell_3_7 ?? == 0 ? '' : table0.cell_3_7??)</span></td>
    <td class="xl68"  onclick="sendMessage(this)" table-name="2023_19_1" table-cell="0_3_8" style="text-align: center;overflow: hidden;">#(table0.cell_3_8 ?? == 0 ? '' : table0.cell_3_8??)</span></td>
    <td class="xl68"  onclick="sendMessage(this)" table-name="2023_19_1" table-cell="0_3_9"  style="text-align: center;overflow: hidden;">#(table0.cell_3_9 ?? == 0 ? '' : table0.cell_3_9??)</span></td>
    <td class="xl68"  onclick="sendMessage(this)" table-name="2023_19_1" table-cell="0_3_10" style="text-align: center;overflow: hidden;">#(table0.cell_3_10 ?? == 0 ? '' : table0.cell_3_10??)</span></td>
    <td class="xl68"  onclick="sendMessage(this)" table-name="2023_19_1" table-cell="0_3_11" style="text-align: center;overflow: hidden;">#(table0.cell_3_11 ?? == 0 ? '' : table0.cell_3_11??)</span></td>
 </tr>
 <tr height='55' style='mso-height-source:userset;height:41.25pt' id='r5'>
<td colspan='2' height='53' class='x25' style='height:39.75pt;'>非公有制经济<br>控制企业</td>
<td   class="xl68"  onclick="sendMessage(this)" table-name="2023_19_1" table-cell="0_4_1" style="text-align: center;overflow: hidden;">#(table0.cell_4_1 ?? == 0 ? '' : table0.cell_4_1??)</span></td>
    <td  class="xl68"  onclick="sendMessage(this)" table-name="2023_19_1" table-cell="0_4_2" style="text-align: center;overflow: hidden;">#(table0.cell_4_2 ?? == 0 ? '' : table0.cell_4_2??)</span></td>
    <td  class="xl68"  onclick="sendMessage(this)" table-name="2023_19_1" table-cell="0_4_3" style="text-align: center;overflow: hidden;">#(table0.cell_4_3 ?? == 0 ? '' : table0.cell_4_3??)</span></td>
    <td  class="xl68"  onclick="sendMessage(this)" table-name="2023_19_1" table-cell="0_4_4" style="text-align: center;overflow: hidden;">#(table0.cell_4_4 ?? == 0 ? '' : table0.cell_4_4??)</span></td>
    <td  class="xl68"  onclick="sendMessage(this)" table-name="2023_19_1" table-cell="0_4_5" style="text-align: center;overflow: hidden;">#(table0.cell_4_5 ?? == 0 ? '' : table0.cell_4_5??)</span></td>
    <td  class="xl68"  onclick="sendMessage(this)" table-name="2023_19_1" table-cell="0_4_6" style="text-align: center;overflow: hidden;">#(table0.cell_4_6 ?? == 0 ? '' : table0.cell_4_6??)</span></td>
    <td  class="xl68"  onclick="sendMessage(this)" table-name="2023_19_1" table-cell="0_4_7" style="text-align: center;overflow: hidden;">#(table0.cell_4_7 ?? == 0 ? '' : table0.cell_4_7??)</span></td>
    <td  class="xl68"  onclick="sendMessage(this)" table-name="2023_19_1" table-cell="0_4_8" style="text-align: center;overflow: hidden;">#(table0.cell_4_8 ?? == 0 ? '' : table0.cell_4_8??)</span></td>
    <td  class="xl68"  onclick="sendMessage(this)" table-name="2023_19_1" table-cell="0_4_9" style="text-align: center;overflow: hidden;">#(table0.cell_4_9 ?? == 0 ? '' : table0.cell_4_9??)</span></td>
    <td  class="xl68"  onclick="sendMessage(this)" table-name="2023_19_1" table-cell="0_4_10" style="text-align: center;overflow: hidden;">#(table0.cell_4_10 ?? == 0 ? '' : table0.cell_4_10??)</span></td>
    <td  class="xl68"  onclick="sendMessage(this)" table-name="2023_19_1" table-cell="0_4_11" style="text-align: center;overflow: hidden;">#(table0.cell_4_11 ?? == 0 ? '' : table0.cell_4_11??)</span></td>
 </tr>
 <tr height='55' style='mso-height-source:userset;height:41.25pt' id='r6'>
<td colspan='2' height='53' class='x20' style='height:39.75pt;'>社会组织</td>
<td class="xl68"  onclick="sendMessage(this)" table-name="2023_19_1" table-cell="0_5_1" style="text-align: center;overflow: hidden;">#(table0.cell_5_1 ?? == 0 ? '' : table0.cell_5_1??)</span></td>
    <td class="xl68"  onclick="sendMessage(this)" table-name="2023_19_1" table-cell="0_5_2" style="text-align: center;overflow: hidden;">#(table0.cell_5_2 ?? == 0 ? '' : table0.cell_5_2??)</span></td>
    <td class="xl68"  onclick="sendMessage(this)" table-name="2023_19_1" table-cell="0_5_3" style="text-align: center;overflow: hidden;">#(table0.cell_5_3 ?? == 0 ? '' : table0.cell_5_3??)</span></td>
    <td class="xl68"  onclick="sendMessage(this)" table-name="2023_19_1" table-cell="0_5_4" style="text-align: center;overflow: hidden;">#(table0.cell_5_4 ?? == 0 ? '' : table0.cell_5_4??)</span></td>
    <td class="xl68"  onclick="sendMessage(this)" table-name="2023_19_1" table-cell="0_5_5" style="text-align: center;overflow: hidden;">#(table0.cell_5_5 ?? == 0 ? '' : table0.cell_5_5??)</span></td>
    <td class="xl68"  onclick="sendMessage(this)" table-name="2023_19_1" table-cell="0_5_6" style="text-align: center;overflow: hidden;">#(table0.cell_5_6 ?? == 0 ? '' : table0.cell_5_6??)</span></td>
    <td class="xl68"  onclick="sendMessage(this)" table-name="2023_19_1" table-cell="0_5_7" style="text-align: center;overflow: hidden;">#(table0.cell_5_7 ?? == 0 ? '' : table0.cell_5_7??)</span></td>
    <td class="xl68"  onclick="sendMessage(this)" table-name="2023_19_1" table-cell="0_5_8" style="text-align: center;overflow: hidden;">#(table0.cell_5_8 ?? == 0 ? '' : table0.cell_5_8??)</span></td>
    <td class="xl68"  onclick="sendMessage(this)" table-name="2023_19_1" table-cell="0_5_9" style="text-align: center;overflow: hidden;">#(table0.cell_5_9 ?? == 0 ? '' : table0.cell_5_9??)</span></td>
    <td class="xl68"  onclick="sendMessage(this)" table-name="2023_19_1" table-cell="0_5_10" style="text-align: center;overflow: hidden;">#(table0.cell_5_10 ?? == 0 ? '' : table0.cell_5_10??)</span></td>
    <td class="xl68"  onclick="sendMessage(this)" table-name="2023_19_1" table-cell="0_5_11" style="text-align: center;overflow: hidden;">#(table0.cell_5_11 ?? == 0 ? '' : table0.cell_5_11??)</span></td>
 </tr>
 <tr height='55' style='mso-height-source:userset;height:41.25pt' id='r7'>
<td colspan='2' height='54' class='x20' style='height:40.5pt;'>农牧渔民</td>
<td class="xl68"  onclick="sendMessage(this)" table-name="2023_19_1" table-cell="0_6_1" style="text-align: center;overflow: hidden;">#(table0.cell_6_1 ?? == 0 ? '' : table0.cell_6_1??)</span></td>
    <td class="xl68"  onclick="sendMessage(this)" table-name="2023_19_1" table-cell="0_6_2" style="text-align: center;overflow: hidden;">#(table0.cell_6_2 ?? == 0 ? '' : table0.cell_6_2??)</span></td>
    <td class="xl68"  onclick="sendMessage(this)" table-name="2023_19_1" table-cell="0_6_3" style="text-align: center;overflow: hidden;">#(table0.cell_6_3 ?? == 0 ? '' : table0.cell_6_3??)</span></td>
    <td class="xl68"  onclick="sendMessage(this)" table-name="2023_19_1" table-cell="0_6_4" style="text-align: center;overflow: hidden;">#(table0.cell_6_4 ?? == 0 ? '' : table0.cell_6_4??)</span></td>
    <td class="xl68"  onclick="sendMessage(this)" table-name="2023_19_1" table-cell="0_6_5" style="text-align: center;overflow: hidden;">#(table0.cell_6_5 ?? == 0 ? '' : table0.cell_6_5??)</span></td>
    <td class="xl68"  onclick="sendMessage(this)" table-name="2023_19_1" table-cell="0_6_6" style="text-align: center;overflow: hidden;">#(table0.cell_6_6 ?? == 0 ? '' : table0.cell_6_6??)</span></td>
    <td class="xl68"  onclick="sendMessage(this)" table-name="2023_19_1" table-cell="0_6_7" style="text-align: center;overflow: hidden;">#(table0.cell_6_7 ?? == 0 ? '' : table0.cell_6_7??)</span></td>
    <td class="xl68"  onclick="sendMessage(this)" table-name="2023_19_1" table-cell="0_6_8" style="text-align: center;overflow: hidden;">#(table0.cell_6_8 ?? == 0 ? '' : table0.cell_6_8??)</span></td>
    <td class="xl68"  onclick="sendMessage(this)" table-name="2023_19_1" table-cell="0_6_9" style="text-align: center;overflow: hidden;">#(table0.cell_6_9 ?? == 0 ? '' : table0.cell_6_9??)</span></td>
    <td class="xl68"  onclick="sendMessage(this)" table-name="2023_19_1" table-cell="0_6_10" style="text-align: center;overflow: hidden;">#(table0.cell_6_10 ?? == 0 ? '' : table0.cell_6_10??)</span></td>
    <td class="xl68"  onclick="sendMessage(this)" table-name="2023_19_1" table-cell="0_6_11" style="text-align: center;overflow: hidden;">#(table0.cell_6_11 ?? == 0 ? '' : table0.cell_6_11??)</span></td>
 </tr>
 <tr height='55' style='mso-height-source:userset;height:41.25pt' id='r8'>
<td height='54' class='x18' style='height:40.5pt;'></td>
<td class='x23'>外出务工<br>经商人员</td>
<td class="xl68"  onclick="sendMessage(this)" table-name="2023_19_1" table-cell="0_7_1" style="text-align: center;overflow: hidden;">#(table0.cell_7_1 ?? == 0 ? '' : table0.cell_7_1??)</span></td>
    <td class="xl68"  onclick="sendMessage(this)" table-name="2023_19_1" table-cell="0_7_2" style="text-align: center;overflow: hidden;">#(table0.cell_7_2 ?? == 0 ? '' : table0.cell_7_2??)</span></td>
    <td class="xl68"  onclick="sendMessage(this)" table-name="2023_19_1" table-cell="0_7_3" style="text-align: center;overflow: hidden;">#(table0.cell_7_3 ?? == 0 ? '' : table0.cell_7_3??)</span></td>
    <td class="xl68"  onclick="sendMessage(this)" table-name="2023_19_1" table-cell="0_7_4" style="text-align: center;overflow: hidden;">#(table0.cell_7_4 ?? == 0 ? '' : table0.cell_7_4??)</span></td>
    <td class="xl68"  onclick="sendMessage(this)" table-name="2023_19_1" table-cell="0_7_5" style="text-align: center;overflow: hidden;">#(table0.cell_7_5 ?? == 0 ? '' : table0.cell_7_5??)</span></td>
    <td class="xl68"  onclick="sendMessage(this)" table-name="2023_19_1" table-cell="0_7_6" style="text-align: center;overflow: hidden;">#(table0.cell_7_6 ?? == 0 ? '' : table0.cell_7_6??)</span></td>
    <td class="xl68"  onclick="sendMessage(this)" table-name="2023_19_1" table-cell="0_7_7" style="text-align: center;overflow: hidden;">#(table0.cell_7_7 ?? == 0 ? '' : table0.cell_7_7??)</span></td>
    <td class="xl68"  onclick="sendMessage(this)" table-name="2023_19_1" table-cell="0_7_8" style="text-align: center;overflow: hidden;">#(table0.cell_7_8 ?? == 0 ? '' : table0.cell_7_8??)</span></td>
    <td class="xl68"  onclick="sendMessage(this)" table-name="2023_19_1" table-cell="0_7_9" style="text-align: center;overflow: hidden;">#(table0.cell_7_9 ?? == 0 ? '' : table0.cell_7_9??)</span></td>
    <td class="xl68"  onclick="sendMessage(this)" table-name="2023_19_1" table-cell="0_7_10" style="text-align: center;overflow: hidden;">#(table0.cell_7_10 ?? == 0 ? '' : table0.cell_7_10??)</span></td>
    <td class="xl68"  onclick="sendMessage(this)" table-name="2023_19_1" table-cell="0_7_11" style="text-align: center;overflow: hidden;">#(table0.cell_7_11 ?? == 0 ? '' : table0.cell_7_11??)</span></td>
 </tr>
 <tr height='55' style='mso-height-source:userset;height:41.25pt' id='r9'>
<td colspan='2' height='53' class='x20' style='height:39.75pt;'>其他</td>
<td class="xl68"  onclick="sendMessage(this)" table-name="2023_19_1" table-cell="0_8_1" style="text-align: center;overflow: hidden;">#(table0.cell_8_1 ?? == 0 ? '' : table0.cell_8_1??)</span></td>
    <td class="xl68"  onclick="sendMessage(this)" table-name="2023_19_1" table-cell="0_8_2" style="text-align: center;overflow: hidden;">#(table0.cell_8_2 ?? == 0 ? '' : table0.cell_8_2??)</span></td>
    <td class="xl68"  onclick="sendMessage(this)" table-name="2023_19_1" table-cell="0_8_3" style="text-align: center;overflow: hidden;">#(table0.cell_8_3 ?? == 0 ? '' : table0.cell_8_3??)</span></td>
    <td class="xl68"  onclick="sendMessage(this)" table-name="2023_19_1" table-cell="0_8_4" style="text-align: center;overflow: hidden;">#(table0.cell_8_4 ?? == 0 ? '' : table0.cell_8_4??)</span></td>
    <td class="xl68"  onclick="sendMessage(this)" table-name="2023_19_1" table-cell="0_8_5" style="text-align: center;overflow: hidden;">#(table0.cell_8_5 ?? == 0 ? '' : table0.cell_8_5??)</span></td>
    <td class="xl68"  onclick="sendMessage(this)" table-name="2023_19_1" table-cell="0_8_6" style="text-align: center;overflow: hidden;">#(table0.cell_8_6 ?? == 0 ? '' : table0.cell_8_6??)</span></td>
    <td class="xl68"  onclick="sendMessage(this)" table-name="2023_19_1" table-cell="0_8_7" style="text-align: center;overflow: hidden;">#(table0.cell_8_7 ?? == 0 ? '' : table0.cell_8_7??)</span></td>
    <td class="xl68"  onclick="sendMessage(this)" table-name="2023_19_1" table-cell="0_8_8" style="text-align: center;overflow: hidden;">#(table0.cell_8_8 ?? == 0 ? '' : table0.cell_8_8??)</span></td>
    <td class="xl68"  onclick="sendMessage(this)" table-name="2023_19_1" table-cell="0_8_9" style="text-align: center;overflow: hidden;">#(table0.cell_8_9 ?? == 0 ? '' : table0.cell_8_9??)</span></td>
    <td class="xl68"  onclick="sendMessage(this)" table-name="2023_19_1" table-cell="0_8_10" style="text-align: center;overflow: hidden;">#(table0.cell_8_10 ?? == 0 ? '' : table0.cell_8_10??)</span></td>
    <td class="xl68"  onclick="sendMessage(this)" table-name="2023_19_1" table-cell="0_8_11" style="text-align: center;overflow: hidden;">#(table0.cell_8_11 ?? == 0 ? '' : table0.cell_8_11??)</span></td>
 </tr>
 <tr height='26' style='mso-height-source:userset;height:19.8pt' id='r10'>
<td height='25' class='x27' style='height:19.05pt;'></td>
<td class='x27'></td>
<td class='x27'></td>
<td class='x27'></td>
<td class='x27'></td>
<td class='x27'></td>
<td class='x27'></td>
<td class='x27'></td>
<td class='x27'></td>
<td class='x27'></td>
<td class='x27'></td>
<td class='x27'></td>
<td class='x27'></td>
<td class='x19'></td>
 </tr>
<![if supportMisalignedColumns]>
 <tr height='0' style='display:none'>
  <td width='49' style='width:36.75pt'></td>
  <td width='91' style='width:68.25pt'></td>
  <td width='98' style='width:73.5pt'></td>
  <td width='77' style='width:57.75pt'></td>
  <td width='91' style='width:68.25pt'></td>
  <td width='77' style='width:57.75pt'></td>
  <td width='77' style='width:57.75pt'></td>
  <td width='77' style='width:57.75pt'></td>
  <td width='77' style='width:57.75pt'></td>
  <td width='77' style='width:57.75pt'></td>
  <td width='77' style='width:57.75pt'></td>
  <td width='77' style='width:57.75pt'></td>
  <td width='77' style='width:57.75pt'></td>
  <td width='21' style='width:15.75pt'></td>
 </tr>
 <![endif]>
</table>
<script type="text/javascript">function sendMessage(cell){var obj=new Object();var tableName=cell.getAttribute("table-name");var tableCellIndex=cell.getAttribute("table-cell");obj['tableName']=tableName;obj['tableCellIndex']=tableCellIndex;console.log(obj);window.parent.postMessage(JSON.stringify(obj))}</script>

</body>

</html>
