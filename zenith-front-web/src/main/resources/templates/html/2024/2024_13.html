<!DOCTYPE html PUBLIC "-//w3c//dtd xhtml 1.0 transitional//en" "http://www.w3.org/tr/xhtml1/dtd/xhtml1-transitional.dtd">
<html xmlns:v="urn:schemas-microsoft-com:vml" xmlns:o="urn:schemas-microsoft-com:office:office" xmlns:x="urn:schemas-microsoft-com:office:excel" xmlns="http://www.w3.org/TR/REC-html40">
 <head> 
  <meta http-equiv="Content-Type" content="text/html; charset=UTF-8"> 
  <meta name="ProgId" content="Excel.Sheet"> 
  <meta name="Generator" content="Aspose.Cell 19.7.0"> 
  <link rel="File-List" href="/filelist.xml"> 
  <link rel="Edit-Time-Data" href="/editdata.mso"> 
  <link rel="OLE-Object-Data" href="/oledata.mso"> 
  <!--[if gte mso 9]><xml>
 <o:DocumentProperties>
  <o:Created>2024-11-01T07:56:46Z</o:Created>
  <o:LastSaved>2024-11-01T07:57:02Z</o:LastSaved>
</o:DocumentProperties>
</xml><![endif]--> 
  <style>
<!--table
 {mso-displayed-decimal-separator:"\.";
 mso-displayed-thousand-separator:"\,";}
@page
 {
 mso-header-data:"";
 mso-footer-data:"";
 margin:0in 0in 0in 0.3in;
 mso-header-margin:0.5in;
 mso-footer-margin:0.5in;
 mso-page-orientation:Landscape;
 mso-horizontal-page-align:center;
 mso-vertical-page-align:center;
 }
tr
 {mso-height-source:auto;
 mso-ruby-visibility:none;}
col
 {mso-width-source:auto;
 mso-ruby-visibility:none;}
br
 {mso-data-placement:same-cell;}
ruby
 {ruby-align:left;}
.style0
 {
 mso-number-format:General;
 text-align:general;
 vertical-align:bottom;
 white-space:nowrap;
 background:white;
 mso-pattern:auto;
 color:#000000;
 font-size:10pt;
 font-weight:400;
 font-style:normal;
 font-family:"Arial","sans-serif";
 border:none;
 mso-protection:locked visible;
 mso-style-name:Normal;
 mso-style-id:0;}
.font0
 {
 color:#000000;
 font-size:10pt;
 font-weight:400;
 font-style:normal;
 font-family:"Arial","sans-serif"; }
.font1
 {
 color:#000000;
 font-size:11pt;
 font-weight:400;
 font-style:normal;
 font-family:"宋体"; }
.font2
 {
 color:#000000;
 font-size:10pt;
 font-weight:400;
 font-style:normal;
 font-family:"宋体"; }
.font3
 {
 color:#000000;
 font-size:25pt;
 font-weight:700;
 font-style:normal;
 font-family:"宋体"; }
.font4
 {
 color:#000000;
 font-size:15pt;
 font-weight:400;
 font-style:normal;
 font-family:"宋体"; }
.font5
 {
 color:#000000;
 font-size:15pt;
 font-weight:700;
 font-style:normal;
 font-family:"宋体"; }
.font6
 {
 color:#000000;
 font-size:14pt;
 font-weight:700;
 font-style:normal;
 font-family:"宋体"; }
.font7
 {
 color:#0000FF;
 font-size:10pt;
 font-weight:400;
 font-style:normal;
 font-family:"宋体"; }
.font8
 {
 color:#000000;
 font-size:10pt;
 font-weight:700;
 font-style:normal;
 font-family:"宋体"; }
.font9
 {
 color:#000000;
 font-size:9pt;
 font-weight:400;
 font-style:normal;
 font-family:"宋体"; }
.font10
 {
 color:#0000FF;
 font-size:9pt;
 font-weight:400;
 font-style:normal;
 font-family:"宋体"; }
.font11
 {
 color:#0000C0;
 font-size:10pt;
 font-weight:400;
 font-style:normal;
 font-family:"宋体"; }
.font12
 {
 color:#000000;
 font-size:11pt;
 font-weight:400;
 font-style:normal;
 font-family:"宋体"; }
.font13
 {
 color:#000000;
 font-size:11pt;
 font-weight:700;
 font-style:normal;
 font-family:"宋体"; }
.font14
 {
 color:#0000FF;
 font-size:11pt;
 font-weight:400;
 font-style:normal;
 text-decoration:underline;
 font-family:"宋体"; }
.font15
 {
 color:#800080;
 font-size:11pt;
 font-weight:400;
 font-style:normal;
 text-decoration:underline;
 font-family:"宋体"; }
.font16
 {
 color:#FF0000;
 font-size:11pt;
 font-weight:400;
 font-style:normal;
 font-family:"宋体"; }
.font17
 {
 color:#333399;
 font-size:18pt;
 font-weight:700;
 font-style:normal;
 font-family:"宋体"; }
.font18
 {
 color:#808080;
 font-size:11pt;
 font-weight:400;
 font-style:italic;
 font-family:"宋体"; }
.font19
 {
 color:#333399;
 font-size:15pt;
 font-weight:700;
 font-style:normal;
 font-family:"宋体"; }
.font20
 {
 color:#333399;
 font-size:13pt;
 font-weight:700;
 font-style:normal;
 font-family:"宋体"; }
.font21
 {
 color:#333399;
 font-size:11pt;
 font-weight:700;
 font-style:normal;
 font-family:"宋体"; }
.font22
 {
 color:#333399;
 font-size:11pt;
 font-weight:400;
 font-style:normal;
 font-family:"宋体"; }
.font23
 {
 color:#333333;
 font-size:11pt;
 font-weight:700;
 font-style:normal;
 font-family:"宋体"; }
.font24
 {
 color:#FF6600;
 font-size:11pt;
 font-weight:700;
 font-style:normal;
 font-family:"宋体"; }
.font25
 {
 color:#FFFFFF;
 font-size:11pt;
 font-weight:700;
 font-style:normal;
 font-family:"宋体"; }
.font26
 {
 color:#FF6600;
 font-size:11pt;
 font-weight:400;
 font-style:normal;
 font-family:"宋体"; }
.font27
 {
 color:#008000;
 font-size:11pt;
 font-weight:400;
 font-style:normal;
 font-family:"宋体"; }
.font28
 {
 color:#993300;
 font-size:11pt;
 font-weight:400;
 font-style:normal;
 font-family:"宋体"; }
.font29
 {
 color:#808000;
 font-size:11pt;
 font-weight:400;
 font-style:normal;
 font-family:"宋体"; }
.font30
 {
 color:#FFFFFF;
 font-size:11pt;
 font-weight:400;
 font-style:normal;
 font-family:"宋体"; }
.font31
 {
 color:#FFFFFF;
 font-size:11pt;
 font-weight:400;
 font-style:normal;
 font-family:"宋体"; }
.font32
 {
 color:#000000;
 font-size:11pt;
 font-weight:400;
 font-style:normal;
 font-family:"宋体"; }
.font33
 {
 color:#9C6500;
 font-size:11pt;
 font-weight:400;
 font-style:normal;
 font-family:"宋体"; }
.font34
 {
 color:#9C0006;
 font-size:11pt;
 font-weight:400;
 font-style:normal;
 font-family:"宋体"; }
.font35
 {
 color:#006100;
 font-size:11pt;
 font-weight:400;
 font-style:normal;
 font-family:"宋体"; }
.font36
 {
 color:#000000;
 font-size:11pt;
 font-weight:700;
 font-style:normal;
 font-family:"宋体"; }
.font37
 {
 color:#FA7D00;
 font-size:11pt;
 font-weight:400;
 font-style:normal;
 font-family:"宋体"; }
.font38
 {
 color:#FFFFFF;
 font-size:11pt;
 font-weight:700;
 font-style:normal;
 font-family:"宋体"; }
.font39
 {
 color:#FA7D00;
 font-size:11pt;
 font-weight:700;
 font-style:normal;
 font-family:"宋体"; }
.font40
 {
 color:#3F3F3F;
 font-size:11pt;
 font-weight:700;
 font-style:normal;
 font-family:"宋体"; }
.font41
 {
 color:#3F3F76;
 font-size:11pt;
 font-weight:400;
 font-style:normal;
 font-family:"宋体"; }
.font42
 {
 color:#44546A;
 font-size:11pt;
 font-weight:700;
 font-style:normal;
 font-family:"宋体"; }
.font43
 {
 color:#44546A;
 font-size:13pt;
 font-weight:700;
 font-style:normal;
 font-family:"宋体"; }
.font44
 {
 color:#44546A;
 font-size:15pt;
 font-weight:700;
 font-style:normal;
 font-family:"宋体"; }
.font45
 {
 color:#7F7F7F;
 font-size:11pt;
 font-weight:400;
 font-style:italic;
 font-family:"宋体"; }
.font46
 {
 color:#44546A;
 font-size:18pt;
 font-weight:700;
 font-style:normal;
 font-family:"宋体"; }
.font47
 {
 color:#FF0000;
 font-size:11pt;
 font-weight:400;
 font-style:normal;
 font-family:"宋体"; }
.font48
 {
 color:#000000;
 font-size:11pt;
 font-weight:400;
 font-style:normal;
 font-family:"宋体"; }
.font49
 {
 color:#800080;
 font-size:11pt;
 font-weight:400;
 font-style:normal;
 text-decoration:underline;
 font-family:"宋体"; }
.font50
 {
 color:#0000FF;
 font-size:11pt;
 font-weight:400;
 font-style:normal;
 text-decoration:underline;
 font-family:"宋体"; }
td
 {mso-style-parent:style0;
 mso-number-format:General;
 text-align:general;
 vertical-align:bottom;
 white-space:nowrap;
 background:white;
 mso-pattern:auto;
 color:#000000;
 font-size:10pt;
 font-weight:400;
 font-style:normal;
 font-family:"Arial","sans-serif";
 border:none;
 mso-protection:locked visible;
 mso-ignore:padding;}
.style0
 {
 text-align:general;
 vertical-align:bottom;
 white-space:nowrap;
 background:white;
 mso-pattern:auto;
 font-size:10pt;
 font-weight:400;
 font-style:normal;
 font-family:"Arial","sans-serif";
 border:none;
 mso-protection:locked visible;
 mso-style-name:"Normal";
 }
.style1
 {
 text-align:general;
 vertical-align:middle;
 white-space:nowrap;
 background:white;
 mso-pattern:auto;
 font-size:11pt;
 font-weight:400;
 font-style:normal;
 font-family:"宋体";
 border:none;
 mso-protection:locked visible;
 }
.style2
 {
 text-align:general;
 vertical-align:middle;
 white-space:nowrap;
 background:white;
 mso-pattern:auto;
 font-size:11pt;
 font-weight:400;
 font-style:normal;
 font-family:"宋体";
 border:none;
 mso-protection:locked visible;
 }
.style3
 {
 text-align:general;
 vertical-align:middle;
 white-space:nowrap;
 background:white;
 mso-pattern:auto;
 font-size:11pt;
 font-weight:400;
 font-style:normal;
 font-family:"宋体";
 border:none;
 mso-protection:locked visible;
 }
.style4
 {
 text-align:general;
 vertical-align:middle;
 white-space:nowrap;
 background:white;
 mso-pattern:auto;
 font-size:11pt;
 font-weight:400;
 font-style:normal;
 font-family:"宋体";
 border:none;
 mso-protection:locked visible;
 }
.style5
 {
 text-align:general;
 vertical-align:middle;
 white-space:nowrap;
 background:white;
 mso-pattern:auto;
 font-size:10pt;
 font-weight:400;
 font-style:normal;
 font-family:"Arial","sans-serif";
 border:none;
 mso-protection:locked visible;
 }
.style6
 {
 text-align:general;
 vertical-align:middle;
 white-space:nowrap;
 background:white;
 mso-pattern:auto;
 font-size:10pt;
 font-weight:400;
 font-style:normal;
 font-family:"Arial","sans-serif";
 border:none;
 mso-protection:locked visible;
 }
.style7
 {
 text-align:general;
 vertical-align:middle;
 white-space:nowrap;
 background:white;
 mso-pattern:auto;
 font-size:10pt;
 font-weight:400;
 font-style:normal;
 font-family:"Arial","sans-serif";
 border:none;
 mso-protection:locked visible;
 }
.style8
 {
 text-align:general;
 vertical-align:middle;
 white-space:nowrap;
 background:white;
 mso-pattern:auto;
 font-size:10pt;
 font-weight:400;
 font-style:normal;
 font-family:"Arial","sans-serif";
 border:none;
 mso-protection:locked visible;
 }
.style9
 {
 text-align:general;
 vertical-align:middle;
 white-space:nowrap;
 background:white;
 mso-pattern:auto;
 font-size:10pt;
 font-weight:400;
 font-style:normal;
 font-family:"Arial","sans-serif";
 border:none;
 mso-protection:locked visible;
 }
.style10
 {
 text-align:general;
 vertical-align:middle;
 white-space:nowrap;
 background:white;
 mso-pattern:auto;
 font-size:10pt;
 font-weight:400;
 font-style:normal;
 font-family:"Arial","sans-serif";
 border:none;
 mso-protection:locked visible;
 }
.style11
 {
 text-align:general;
 vertical-align:middle;
 white-space:nowrap;
 background:white;
 mso-pattern:auto;
 font-size:10pt;
 font-weight:400;
 font-style:normal;
 font-family:"Arial","sans-serif";
 border:none;
 mso-protection:locked visible;
 }
.style12
 {
 text-align:general;
 vertical-align:middle;
 white-space:nowrap;
 background:white;
 mso-pattern:auto;
 font-size:10pt;
 font-weight:400;
 font-style:normal;
 font-family:"Arial","sans-serif";
 border:none;
 mso-protection:locked visible;
 }
.style13
 {
 text-align:general;
 vertical-align:middle;
 white-space:nowrap;
 background:white;
 mso-pattern:auto;
 font-size:10pt;
 font-weight:400;
 font-style:normal;
 font-family:"Arial","sans-serif";
 border:none;
 mso-protection:locked visible;
 }
.style14
 {
 text-align:general;
 vertical-align:middle;
 white-space:nowrap;
 background:white;
 mso-pattern:auto;
 font-size:10pt;
 font-weight:400;
 font-style:normal;
 font-family:"Arial","sans-serif";
 border:none;
 mso-protection:locked visible;
 }
.x15
 {
 mso-style-parent:style0;
 mso-number-format:General;
 text-align:general;
 vertical-align:bottom;
 white-space:nowrap;
 background:white;
 mso-pattern:auto;
 font-size:10pt;
 font-weight:400;
 font-style:normal;
 font-family:"Arial","sans-serif";
 border:none;
 mso-protection:locked visible;
 }
.style16
 {
 mso-number-format:"_ * \#\,\#\#0\.00_ \;_ * \\-\#\,\#\#0\.00_ \;_ * \0022-\0022??_ \;_ \@_ ";
 text-align:general;
 vertical-align:bottom;
 white-space:nowrap;
 background:white;
 mso-pattern:auto;
 font-size:10pt;
 font-weight:400;
 font-style:normal;
 font-family:"Arial","sans-serif";
 border:none;
 mso-protection:locked visible;
 mso-style-name:"Comma";
 }
.style17
 {
 mso-number-format:"_ \0022￥\0022* \#\,\#\#0\.00_ \;_ \0022￥\0022* \\-\#\,\#\#0\.00_ \;_ \0022￥\0022* \0022-\0022??_ \;_ \@_ ";
 text-align:general;
 vertical-align:bottom;
 white-space:nowrap;
 background:white;
 mso-pattern:auto;
 font-size:10pt;
 font-weight:400;
 font-style:normal;
 font-family:"Arial","sans-serif";
 border:none;
 mso-protection:locked visible;
 mso-style-name:"Currency";
 }
.style18
 {
 mso-number-format:"0%";
 text-align:general;
 vertical-align:bottom;
 white-space:nowrap;
 background:white;
 mso-pattern:auto;
 font-size:10pt;
 font-weight:400;
 font-style:normal;
 font-family:"Arial","sans-serif";
 border:none;
 mso-protection:locked visible;
 mso-style-name:"Percent";
 }
.style19
 {
 mso-number-format:"_ * \#\,\#\#0_ \;_ * \\-\#\,\#\#0_ \;_ * \0022-\0022_ \;_ \@_ ";
 text-align:general;
 vertical-align:bottom;
 white-space:nowrap;
 background:white;
 mso-pattern:auto;
 font-size:10pt;
 font-weight:400;
 font-style:normal;
 font-family:"Arial","sans-serif";
 border:none;
 mso-protection:locked visible;
 mso-style-name:"Comma [0]";
 }
.style20
 {
 mso-number-format:"_ \0022￥\0022* \#\,\#\#0_ \;_ \0022￥\0022* \\-\#\,\#\#0_ \;_ \0022￥\0022* \0022-\0022_ \;_ \@_ ";
 text-align:general;
 vertical-align:bottom;
 white-space:nowrap;
 background:white;
 mso-pattern:auto;
 font-size:10pt;
 font-weight:400;
 font-style:normal;
 font-family:"Arial","sans-serif";
 border:none;
 mso-protection:locked visible;
 mso-style-name:"Currency [0]";
 }
.style21
 {
 text-align:general;
 vertical-align:middle;
 white-space:nowrap;
 background:white;
 mso-pattern:auto;
 color:#0000FF;
 font-size:11pt;
 font-weight:400;
 font-style:normal;
 text-decoration:underline;
 font-family:"宋体";
 border:none;
 mso-protection:locked visible;
 mso-style-name:"Hyperlink";
 }
.style22
 {
 text-align:general;
 vertical-align:middle;
 white-space:nowrap;
 background:white;
 mso-pattern:auto;
 color:#800080;
 font-size:11pt;
 font-weight:400;
 font-style:normal;
 text-decoration:underline;
 font-family:"宋体";
 border:none;
 mso-protection:locked visible;
 mso-style-name:"Followed Hyperlink";
 }
.style23
 {
 text-align:general;
 vertical-align:middle;
 white-space:nowrap;
 background:#FFFFCC;
 mso-pattern:auto none;
 color:#000000;
 font-size:11pt;
 font-weight:400;
 font-style:normal;
 font-family:"宋体";
 border-top:1px solid #B2B2B2;
 border-right:1px solid #B2B2B2;
 border-bottom:1px solid #B2B2B2;
 border-left:1px solid #B2B2B2;
 mso-diagonal-down:none;
 mso-diagonal-up:none;
 mso-protection:locked visible;
 mso-style-name:"注释";
 }
.style24
 {
 text-align:general;
 vertical-align:middle;
 white-space:nowrap;
 background:white;
 mso-pattern:auto;
 color:#FF0000;
 font-size:11pt;
 font-weight:400;
 font-style:normal;
 font-family:"宋体";
 border:none;
 mso-protection:locked visible;
 mso-style-name:"警告文本";
 }
.style25
 {
 text-align:general;
 vertical-align:middle;
 white-space:nowrap;
 background:white;
 mso-pattern:auto;
 color:#44546A;
 font-size:18pt;
 font-weight:700;
 font-style:normal;
 font-family:"宋体";
 border:none;
 mso-protection:locked visible;
 mso-style-name:"标题";
 }
.style26
 {
 text-align:general;
 vertical-align:middle;
 white-space:nowrap;
 background:white;
 mso-pattern:auto;
 color:#7F7F7F;
 font-size:11pt;
 font-weight:400;
 font-style:italic;
 font-family:"宋体";
 border:none;
 mso-protection:locked visible;
 mso-style-name:"解释性文本";
 }
.style27
 {
 text-align:general;
 vertical-align:middle;
 white-space:nowrap;
 background:white;
 mso-pattern:auto;
 color:#44546A;
 font-size:15pt;
 font-weight:700;
 font-style:normal;
 font-family:"宋体";
 border-top:none;
 border-right:none;
 border-bottom:2px solid #5B9BD5;
 border-left:none;
 mso-diagonal-down:none;
 mso-diagonal-up:none;
 mso-protection:locked visible;
 mso-style-name:"标题 1";
 }
.style28
 {
 text-align:general;
 vertical-align:middle;
 white-space:nowrap;
 background:white;
 mso-pattern:auto;
 color:#44546A;
 font-size:13pt;
 font-weight:700;
 font-style:normal;
 font-family:"宋体";
 border-top:none;
 border-right:none;
 border-bottom:2px solid #5B9BD5;
 border-left:none;
 mso-diagonal-down:none;
 mso-diagonal-up:none;
 mso-protection:locked visible;
 mso-style-name:"标题 2";
 }
.style29
 {
 text-align:general;
 vertical-align:middle;
 white-space:nowrap;
 background:white;
 mso-pattern:auto;
 color:#44546A;
 font-size:11pt;
 font-weight:700;
 font-style:normal;
 font-family:"宋体";
 border-top:none;
 border-right:none;
 border-bottom:2px solid #ADCDEA;
 border-left:none;
 mso-diagonal-down:none;
 mso-diagonal-up:none;
 mso-protection:locked visible;
 mso-style-name:"标题 3";
 }
.style30
 {
 text-align:general;
 vertical-align:middle;
 white-space:nowrap;
 background:white;
 mso-pattern:auto;
 color:#44546A;
 font-size:11pt;
 font-weight:700;
 font-style:normal;
 font-family:"宋体";
 border:none;
 mso-protection:locked visible;
 mso-style-name:"标题 4";
 }
.style31
 {
 text-align:general;
 vertical-align:middle;
 white-space:nowrap;
 background:#FFCC99;
 mso-pattern:auto none;
 color:#3F3F76;
 font-size:11pt;
 font-weight:400;
 font-style:normal;
 font-family:"宋体";
 border-top:1px solid #7F7F7F;
 border-right:1px solid #7F7F7F;
 border-bottom:1px solid #7F7F7F;
 border-left:1px solid #7F7F7F;
 mso-diagonal-down:none;
 mso-diagonal-up:none;
 mso-protection:locked visible;
 mso-style-name:"输入";
 }
.style32
 {
 text-align:general;
 vertical-align:middle;
 white-space:nowrap;
 background:#F2F2F2;
 mso-pattern:auto none;
 color:#3F3F3F;
 font-size:11pt;
 font-weight:700;
 font-style:normal;
 font-family:"宋体";
 border-top:1px solid #3F3F3F;
 border-right:1px solid #3F3F3F;
 border-bottom:1px solid #3F3F3F;
 border-left:1px solid #3F3F3F;
 mso-diagonal-down:none;
 mso-diagonal-up:none;
 mso-protection:locked visible;
 mso-style-name:"输出";
 }
.style33
 {
 text-align:general;
 vertical-align:middle;
 white-space:nowrap;
 background:#F2F2F2;
 mso-pattern:auto none;
 color:#FA7D00;
 font-size:11pt;
 font-weight:700;
 font-style:normal;
 font-family:"宋体";
 border-top:1px solid #7F7F7F;
 border-right:1px solid #7F7F7F;
 border-bottom:1px solid #7F7F7F;
 border-left:1px solid #7F7F7F;
 mso-diagonal-down:none;
 mso-diagonal-up:none;
 mso-protection:locked visible;
 mso-style-name:"计算";
 }
.style34
 {
 text-align:general;
 vertical-align:middle;
 white-space:nowrap;
 background:#A5A5A5;
 mso-pattern:auto none;
 color:#FFFFFF;
 font-size:11pt;
 font-weight:700;
 font-style:normal;
 font-family:"宋体";
 border-top:4px double #3F3F3F;
 border-right:4px double #3F3F3F;
 border-bottom:4px double #3F3F3F;
 border-left:4px double #3F3F3F;
 mso-diagonal-down:none;
 mso-diagonal-up:none;
 mso-protection:locked visible;
 mso-style-name:"检查单元格";
 }
.style35
 {
 text-align:general;
 vertical-align:middle;
 white-space:nowrap;
 background:white;
 mso-pattern:auto;
 color:#FA7D00;
 font-size:11pt;
 font-weight:400;
 font-style:normal;
 font-family:"宋体";
 border-top:none;
 border-right:none;
 border-bottom:4px double #FF8001;
 border-left:none;
 mso-diagonal-down:none;
 mso-diagonal-up:none;
 mso-protection:locked visible;
 mso-style-name:"链接单元格";
 }
.style36
 {
 text-align:general;
 vertical-align:middle;
 white-space:nowrap;
 background:white;
 mso-pattern:auto;
 color:#000000;
 font-size:11pt;
 font-weight:700;
 font-style:normal;
 font-family:"宋体";
 border-top:1px solid #5B9BD5;
 border-right:none;
 border-bottom:4px double #5B9BD5;
 border-left:none;
 mso-diagonal-down:none;
 mso-diagonal-up:none;
 mso-protection:locked visible;
 mso-style-name:"汇总";
 }
.style37
 {
 text-align:general;
 vertical-align:middle;
 white-space:nowrap;
 background:#C6EFCE;
 mso-pattern:auto none;
 color:#006100;
 font-size:11pt;
 font-weight:400;
 font-style:normal;
 font-family:"宋体";
 border:none;
 mso-protection:locked visible;
 mso-style-name:"好";
 }
.style38
 {
 text-align:general;
 vertical-align:middle;
 white-space:nowrap;
 background:#FFC7CE;
 mso-pattern:auto none;
 color:#9C0006;
 font-size:11pt;
 font-weight:400;
 font-style:normal;
 font-family:"宋体";
 border:none;
 mso-protection:locked visible;
 mso-style-name:"差";
 }
.style39
 {
 text-align:general;
 vertical-align:middle;
 white-space:nowrap;
 background:#FFEB9C;
 mso-pattern:auto none;
 color:#9C6500;
 font-size:11pt;
 font-weight:400;
 font-style:normal;
 font-family:"宋体";
 border:none;
 mso-protection:locked visible;
 mso-style-name:"适中";
 }
.style40
 {
 text-align:general;
 vertical-align:middle;
 white-space:nowrap;
 background:#5B9BD5;
 mso-pattern:auto none;
 color:#FFFFFF;
 font-size:11pt;
 font-weight:400;
 font-style:normal;
 font-family:"宋体";
 border:none;
 mso-protection:locked visible;
 mso-style-name:"强调文字颜色 1";
 }
.style41
 {
 text-align:general;
 vertical-align:middle;
 white-space:nowrap;
 background:#DEEBF7;
 mso-pattern:auto none;
 color:#000000;
 font-size:11pt;
 font-weight:400;
 font-style:normal;
 font-family:"宋体";
 border:none;
 mso-protection:locked visible;
 mso-style-name:"20% - 强调文字颜色 1";
 }
.style42
 {
 text-align:general;
 vertical-align:middle;
 white-space:nowrap;
 background:#BDD7EE;
 mso-pattern:auto none;
 color:#000000;
 font-size:11pt;
 font-weight:400;
 font-style:normal;
 font-family:"宋体";
 border:none;
 mso-protection:locked visible;
 mso-style-name:"40% - 强调文字颜色 1";
 }
.style43
 {
 text-align:general;
 vertical-align:middle;
 white-space:nowrap;
 background:#9DC3E6;
 mso-pattern:auto none;
 color:#FFFFFF;
 font-size:11pt;
 font-weight:400;
 font-style:normal;
 font-family:"宋体";
 border:none;
 mso-protection:locked visible;
 mso-style-name:"60% - 强调文字颜色 1";
 }
.style44
 {
 text-align:general;
 vertical-align:middle;
 white-space:nowrap;
 background:#ED7D31;
 mso-pattern:auto none;
 color:#FFFFFF;
 font-size:11pt;
 font-weight:400;
 font-style:normal;
 font-family:"宋体";
 border:none;
 mso-protection:locked visible;
 mso-style-name:"强调文字颜色 2";
 }
.style45
 {
 text-align:general;
 vertical-align:middle;
 white-space:nowrap;
 background:#FBE5D6;
 mso-pattern:auto none;
 color:#000000;
 font-size:11pt;
 font-weight:400;
 font-style:normal;
 font-family:"宋体";
 border:none;
 mso-protection:locked visible;
 mso-style-name:"20% - 强调文字颜色 2";
 }
.style46
 {
 text-align:general;
 vertical-align:middle;
 white-space:nowrap;
 background:#F8CBAD;
 mso-pattern:auto none;
 color:#000000;
 font-size:11pt;
 font-weight:400;
 font-style:normal;
 font-family:"宋体";
 border:none;
 mso-protection:locked visible;
 mso-style-name:"40% - 强调文字颜色 2";
 }
.style47
 {
 text-align:general;
 vertical-align:middle;
 white-space:nowrap;
 background:#F4B183;
 mso-pattern:auto none;
 color:#FFFFFF;
 font-size:11pt;
 font-weight:400;
 font-style:normal;
 font-family:"宋体";
 border:none;
 mso-protection:locked visible;
 mso-style-name:"60% - 强调文字颜色 2";
 }
.style48
 {
 text-align:general;
 vertical-align:middle;
 white-space:nowrap;
 background:#A5A5A5;
 mso-pattern:auto none;
 color:#FFFFFF;
 font-size:11pt;
 font-weight:400;
 font-style:normal;
 font-family:"宋体";
 border:none;
 mso-protection:locked visible;
 mso-style-name:"强调文字颜色 3";
 }
.style49
 {
 text-align:general;
 vertical-align:middle;
 white-space:nowrap;
 background:#EDEDED;
 mso-pattern:auto none;
 color:#000000;
 font-size:11pt;
 font-weight:400;
 font-style:normal;
 font-family:"宋体";
 border:none;
 mso-protection:locked visible;
 mso-style-name:"20% - 强调文字颜色 3";
 }
.style50
 {
 text-align:general;
 vertical-align:middle;
 white-space:nowrap;
 background:#DBDBDB;
 mso-pattern:auto none;
 color:#000000;
 font-size:11pt;
 font-weight:400;
 font-style:normal;
 font-family:"宋体";
 border:none;
 mso-protection:locked visible;
 mso-style-name:"40% - 强调文字颜色 3";
 }
.style51
 {
 text-align:general;
 vertical-align:middle;
 white-space:nowrap;
 background:#C9C9C9;
 mso-pattern:auto none;
 color:#FFFFFF;
 font-size:11pt;
 font-weight:400;
 font-style:normal;
 font-family:"宋体";
 border:none;
 mso-protection:locked visible;
 mso-style-name:"60% - 强调文字颜色 3";
 }
.style52
 {
 text-align:general;
 vertical-align:middle;
 white-space:nowrap;
 background:#FFC000;
 mso-pattern:auto none;
 color:#FFFFFF;
 font-size:11pt;
 font-weight:400;
 font-style:normal;
 font-family:"宋体";
 border:none;
 mso-protection:locked visible;
 mso-style-name:"强调文字颜色 4";
 }
.style53
 {
 text-align:general;
 vertical-align:middle;
 white-space:nowrap;
 background:#FFF2CC;
 mso-pattern:auto none;
 color:#000000;
 font-size:11pt;
 font-weight:400;
 font-style:normal;
 font-family:"宋体";
 border:none;
 mso-protection:locked visible;
 mso-style-name:"20% - 强调文字颜色 4";
 }
.style54
 {
 text-align:general;
 vertical-align:middle;
 white-space:nowrap;
 background:#FFE699;
 mso-pattern:auto none;
 color:#000000;
 font-size:11pt;
 font-weight:400;
 font-style:normal;
 font-family:"宋体";
 border:none;
 mso-protection:locked visible;
 mso-style-name:"40% - 强调文字颜色 4";
 }
.style55
 {
 text-align:general;
 vertical-align:middle;
 white-space:nowrap;
 background:#FFD966;
 mso-pattern:auto none;
 color:#FFFFFF;
 font-size:11pt;
 font-weight:400;
 font-style:normal;
 font-family:"宋体";
 border:none;
 mso-protection:locked visible;
 mso-style-name:"60% - 强调文字颜色 4";
 }
.style56
 {
 text-align:general;
 vertical-align:middle;
 white-space:nowrap;
 background:#4472C4;
 mso-pattern:auto none;
 color:#FFFFFF;
 font-size:11pt;
 font-weight:400;
 font-style:normal;
 font-family:"宋体";
 border:none;
 mso-protection:locked visible;
 mso-style-name:"强调文字颜色 5";
 }
.style57
 {
 text-align:general;
 vertical-align:middle;
 white-space:nowrap;
 background:#DAE3F3;
 mso-pattern:auto none;
 color:#000000;
 font-size:11pt;
 font-weight:400;
 font-style:normal;
 font-family:"宋体";
 border:none;
 mso-protection:locked visible;
 mso-style-name:"20% - 强调文字颜色 5";
 }
.style58
 {
 text-align:general;
 vertical-align:middle;
 white-space:nowrap;
 background:#B4C7E7;
 mso-pattern:auto none;
 color:#000000;
 font-size:11pt;
 font-weight:400;
 font-style:normal;
 font-family:"宋体";
 border:none;
 mso-protection:locked visible;
 mso-style-name:"40% - 强调文字颜色 5";
 }
.style59
 {
 text-align:general;
 vertical-align:middle;
 white-space:nowrap;
 background:#8FAADC;
 mso-pattern:auto none;
 color:#FFFFFF;
 font-size:11pt;
 font-weight:400;
 font-style:normal;
 font-family:"宋体";
 border:none;
 mso-protection:locked visible;
 mso-style-name:"60% - 强调文字颜色 5";
 }
.style60
 {
 text-align:general;
 vertical-align:middle;
 white-space:nowrap;
 background:#70AD47;
 mso-pattern:auto none;
 color:#FFFFFF;
 font-size:11pt;
 font-weight:400;
 font-style:normal;
 font-family:"宋体";
 border:none;
 mso-protection:locked visible;
 mso-style-name:"强调文字颜色 6";
 }
.style61
 {
 text-align:general;
 vertical-align:middle;
 white-space:nowrap;
 background:#E2F0D9;
 mso-pattern:auto none;
 color:#000000;
 font-size:11pt;
 font-weight:400;
 font-style:normal;
 font-family:"宋体";
 border:none;
 mso-protection:locked visible;
 mso-style-name:"20% - 强调文字颜色 6";
 }
.style62
 {
 text-align:general;
 vertical-align:middle;
 white-space:nowrap;
 background:#C5E0B4;
 mso-pattern:auto none;
 color:#000000;
 font-size:11pt;
 font-weight:400;
 font-style:normal;
 font-family:"宋体";
 border:none;
 mso-protection:locked visible;
 mso-style-name:"40% - 强调文字颜色 6";
 }
.style63
 {
 text-align:general;
 vertical-align:middle;
 white-space:nowrap;
 background:#A9D18E;
 mso-pattern:auto none;
 color:#FFFFFF;
 font-size:11pt;
 font-weight:400;
 font-style:normal;
 font-family:"宋体";
 border:none;
 mso-protection:locked visible;
 mso-style-name:"60% - 强调文字颜色 6";
 }
.x64
 {
 mso-style-parent:style0;
 mso-number-format:"\@";
 text-align:left;
 vertical-align:middle;
 white-space:nowrap;
 background:#FFFFFF;
 mso-pattern:auto none;
 color:#000000;
 font-size:10pt;
 font-weight:400;
 font-style:normal;
 font-family:"宋体";
 border:none;
 mso-protection:locked visible;
 }
.x65
 {
 mso-style-parent:style0;
 mso-number-format:"\@";
 text-align:center;
 vertical-align:middle;
 white-space:nowrap;
 background:#FFFFFF;
 mso-pattern:auto none;
 color:#000000;
 font-size:25pt;
 font-weight:700;
 font-style:normal;
 font-family:"宋体";
 border:none;
 mso-protection:locked visible;
 }
.x66
 {
 mso-style-parent:style0;
 mso-number-format:"\@";
 text-align:left;
 vertical-align:middle;
 white-space:nowrap;
 background:#FFFFFF;
 mso-pattern:auto none;
 color:#000000;
 font-size:10pt;
 font-weight:400;
 font-style:normal;
 font-family:"宋体";
 border:none;
 mso-protection:locked visible;
 }
.x67
 {
 mso-style-parent:style0;
 mso-number-format:"\@";
 text-align:left;
 vertical-align:middle;
 white-space:normal;word-wrap:break-word;
 background:#FFFFFF;
 mso-pattern:auto none;
 color:#000000;
 font-size:15pt;
 font-weight:400;
 font-style:normal;
 font-family:"宋体";
 border:none;
 mso-protection:locked visible;
 }
.x68
 {
 mso-style-parent:style0;
 mso-number-format:"\@";
 text-align:right;
 vertical-align:middle;
 white-space:nowrap;
 background:#FFFFFF;
 mso-pattern:auto none;
 color:#000000;
 font-size:10pt;
 font-weight:400;
 font-style:normal;
 font-family:"宋体";
 border:none;
 mso-protection:locked visible;
 }
.x69
 {
 mso-style-parent:style0;
 mso-number-format:"\@";
 text-align:right;
 vertical-align:middle;
 white-space:nowrap;
 background:#FFFFFF;
 mso-pattern:auto none;
 color:#000000;
 font-size:15pt;
 font-weight:700;
 font-style:normal;
 font-family:"宋体";
 border:none;
 mso-protection:locked visible;
 }
.x70
 {
 mso-style-parent:style0;
 mso-number-format:"\@";
 text-align:left;
 vertical-align:middle;
 white-space:nowrap;
 background:#FFFFFF;
 mso-pattern:auto none;
 color:#000000;
 font-size:10pt;
 font-weight:400;
 font-style:normal;
 font-family:"宋体";
 border-top:1px solid #010000;
 border-right:none;
 border-bottom:none;
 border-left:none;
 mso-diagonal-down:none;
 mso-diagonal-up:none;
 mso-protection:locked visible;
 }
.x71
 {
 mso-style-parent:style0;
 mso-number-format:"\@";
 text-align:left;
 vertical-align:middle;
 white-space:nowrap;
 background:#FFFFFF;
 mso-pattern:auto none;
 color:#000000;
 font-size:10pt;
 font-weight:400;
 font-style:normal;
 font-family:"宋体";
 border-top:1px solid #010000;
 border-right:none;
 border-bottom:none;
 border-left:none;
 mso-diagonal-down:none;
 mso-diagonal-up:none;
 mso-protection:locked visible;
 }
.x72
 {
 mso-style-parent:style0;
 mso-number-format:"\@";
 text-align:left;
 vertical-align:middle;
 white-space:nowrap;
 background:#FFFFFF;
 mso-pattern:auto none;
 color:#000000;
 font-size:15pt;
 font-weight:400;
 font-style:normal;
 font-family:"宋体";
 border:none;
 mso-protection:locked visible;
 }
.x73
 {
 mso-style-parent:style0;
 mso-number-format:"\@";
 text-align:right;
 vertical-align:middle;
 white-space:nowrap;
 background:#FFFFFF;
 mso-pattern:auto none;
 color:#000000;
 font-size:10pt;
 font-weight:400;
 font-style:normal;
 font-family:"宋体";
 border-top:1px solid #010000;
 border-right:none;
 border-bottom:none;
 border-left:none;
 mso-diagonal-down:none;
 mso-diagonal-up:none;
 mso-protection:locked visible;
 }
.x74
 {
 mso-style-parent:style0;
 mso-number-format:"\@";
 text-align:left;
 vertical-align:middle;
 white-space:nowrap;
 background:#FFFFFF;
 mso-pattern:auto none;
 color:#000000;
 font-size:15pt;
 font-weight:700;
 font-style:normal;
 font-family:"宋体";
 border:none;
 mso-protection:locked visible;
 }
.x75
 {
 mso-style-parent:style0;
 mso-number-format:"\@";
 text-align:center;
 vertical-align:middle;
 white-space:nowrap;
 background:#FFFFFF;
 mso-pattern:auto none;
 color:#000000;
 font-size:14pt;
 font-weight:700;
 font-style:normal;
 font-family:"宋体";
 border:none;
 mso-protection:locked visible;
 }
.x76
 {
 mso-style-parent:style0;
 mso-number-format:"\@";
 text-align:left;
 vertical-align:middle;
 white-space:nowrap;
 background:#FFFFFF;
 mso-pattern:auto none;
 color:#000000;
 font-size:10pt;
 font-weight:400;
 font-style:normal;
 font-family:"宋体";
 border-top:1px solid #010000;
 border-right:none;
 border-bottom:none;
 border-left:none;
 mso-diagonal-down:none;
 mso-diagonal-up:none;
 mso-protection:locked visible;
 }
.x77
 {
 mso-style-parent:style0;
 mso-number-format:"\@";
 text-align:left;
 vertical-align:middle;
 white-space:nowrap;
 background:#FFFFFF;
 mso-pattern:auto none;
 color:#000000;
 font-size:10pt;
 font-weight:400;
 font-style:normal;
 font-family:"宋体";
 border-top:1px solid #010000;
 border-right:none;
 border-bottom:none;
 border-left:none;
 mso-diagonal-down:none;
 mso-diagonal-up:none;
 mso-protection:locked visible;
 }
.x78
 {
 mso-style-parent:style0;
 mso-number-format:"\@";
 text-align:distributed;
 vertical-align:middle;
 white-space:nowrap;
 background:#FFFFFF;
 mso-pattern:auto none;
 color:#000000;
 font-size:10pt;
 font-weight:400;
 font-style:normal;
 font-family:"宋体";
 border-top:2px solid #010000;
 border-right:none;
 border-bottom:none;
 border-left:none;
 mso-diagonal-down:none;
 mso-diagonal-up:none;
 mso-protection:locked visible;
 }
.x79
 {
 mso-style-parent:style0;
 mso-number-format:"\@";
 text-align:distributed;
 vertical-align:middle;
 white-space:nowrap;
 background:#FFFFFF;
 mso-pattern:auto none;
 color:#000000;
 font-size:10pt;
 font-weight:400;
 font-style:normal;
 font-family:"宋体";
 border-top:2px solid #010000;
 border-right:none;
 border-bottom:none;
 border-left:none;
 mso-diagonal-down:none;
 mso-diagonal-up:none;
 mso-protection:locked visible;
 }
.x80
 {
 mso-style-parent:style0;
 mso-number-format:"\@";
 text-align:distributed;
 vertical-align:middle;
 white-space:nowrap;
 background:#FFFFFF;
 mso-pattern:auto none;
 color:#000000;
 font-size:10pt;
 font-weight:400;
 font-style:normal;
 font-family:"宋体";
 border:none;
 mso-protection:locked visible;
 }
.x81
 {
 mso-style-parent:style0;
 mso-number-format:"\@";
 text-align:center;
 vertical-align:middle;
 white-space:nowrap;
 background:#FFFFFF;
 mso-pattern:auto none;
 color:#000000;
 font-size:10pt;
 font-weight:400;
 font-style:normal;
 font-family:"宋体";
 border-top:1px solid #010000;
 border-right:none;
 border-bottom:none;
 border-left:none;
 mso-diagonal-down:none;
 mso-diagonal-up:none;
 mso-protection:locked visible;
 }
.x82
 {
 mso-style-parent:style0;
 mso-number-format:"\@";
 text-align:center;
 vertical-align:middle;
 white-space:nowrap;
 background:#FFFFFF;
 mso-pattern:auto none;
 color:#000000;
 font-size:10pt;
 font-weight:400;
 font-style:normal;
 font-family:"宋体";
 border-top:1px solid #010000;
 border-right:none;
 border-bottom:none;
 border-left:none;
 mso-diagonal-down:none;
 mso-diagonal-up:none;
 mso-protection:locked visible;
 }
.x83
 {
 mso-style-parent:style0;
 mso-number-format:"\@";
 text-align:center;
 vertical-align:middle;
 white-space:nowrap;
 background:#FFFFFF;
 mso-pattern:auto none;
 color:#000000;
 font-size:10pt;
 font-weight:400;
 font-style:normal;
 font-family:"宋体";
 border-top:1px solid #010000;
 border-right:none;
 border-bottom:none;
 border-left:none;
 mso-diagonal-down:none;
 mso-diagonal-up:none;
 mso-protection:locked visible;
 }
.x84
 {
 mso-style-parent:style0;
 mso-number-format:"\@";
 text-align:distributed;
 vertical-align:middle;
 white-space:nowrap;
 background:#FFFFFF;
 mso-pattern:auto none;
 color:#000000;
 font-size:10pt;
 font-weight:400;
 font-style:normal;
 font-family:"宋体";
 border-top:1px solid #010000;
 border-right:none;
 border-bottom:none;
 border-left:none;
 mso-diagonal-down:none;
 mso-diagonal-up:none;
 mso-protection:locked visible;
 }
.x85
 {
 mso-style-parent:style0;
 mso-number-format:"\@";
 text-align:distributed;
 vertical-align:middle;
 white-space:nowrap;
 background:#FFFFFF;
 mso-pattern:auto none;
 color:#000000;
 font-size:10pt;
 font-weight:400;
 font-style:normal;
 font-family:"宋体";
 border-top:1px solid #010000;
 border-right:none;
 border-bottom:none;
 border-left:none;
 mso-diagonal-down:none;
 mso-diagonal-up:none;
 mso-protection:locked visible;
 }
.x86
 {
 mso-style-parent:style0;
 mso-number-format:"\@";
 text-align:distributed;
 vertical-align:middle;
 white-space:nowrap;
 background:#FFFFFF;
 mso-pattern:auto none;
 color:#000000;
 font-size:10pt;
 font-weight:400;
 font-style:normal;
 font-family:"宋体";
 border-top:1px solid #010000;
 border-right:none;
 border-bottom:none;
 border-left:none;
 mso-diagonal-down:none;
 mso-diagonal-up:none;
 mso-protection:locked visible;
 }
.x87
 {
 mso-style-parent:style0;
 mso-number-format:"\@";
 text-align:distributed;
 vertical-align:middle;
 white-space:nowrap;
 background:#FFFFFF;
 mso-pattern:auto none;
 color:#000000;
 font-size:10pt;
 font-weight:400;
 font-style:normal;
 font-family:"宋体";
 border-top:1px solid #010000;
 border-right:none;
 border-bottom:none;
 border-left:1px solid #010000;
 mso-diagonal-down:none;
 mso-diagonal-up:none;
 mso-protection:locked visible;
 }
.x88
 {
 mso-style-parent:style0;
 mso-number-format:"\@";
 text-align:distributed;
 vertical-align:middle;
 white-space:nowrap;
 background:#FFFFFF;
 mso-pattern:auto none;
 color:#000000;
 font-size:10pt;
 font-weight:400;
 font-style:normal;
 font-family:"宋体";
 border-top:none;
 border-right:none;
 border-bottom:none;
 border-left:1px solid #010000;
 mso-diagonal-down:none;
 mso-diagonal-up:none;
 mso-protection:locked visible;
 }
.x89
 {
 mso-style-parent:style0;
 mso-number-format:"\@";
 text-align:left;
 vertical-align:middle;
 white-space:nowrap;
 background:#FFFFFF;
 mso-pattern:auto none;
 color:#000000;
 font-size:10pt;
 font-weight:400;
 font-style:normal;
 font-family:"宋体";
 border-top:2px solid #010000;
 border-right:none;
 border-bottom:none;
 border-left:none;
 mso-diagonal-down:none;
 mso-diagonal-up:none;
 mso-protection:locked visible;
 }
.x90
 {
 mso-style-parent:style0;
 mso-number-format:"\@";
 text-align:left;
 vertical-align:middle;
 white-space:nowrap;
 background:#FFFFFF;
 mso-pattern:auto none;
 color:#000000;
 font-size:10pt;
 font-weight:400;
 font-style:normal;
 font-family:"宋体";
 border-top:2px solid #010000;
 border-right:none;
 border-bottom:none;
 border-left:none;
 mso-diagonal-down:none;
 mso-diagonal-up:none;
 mso-protection:locked visible;
 }
.x91
 {
 mso-style-parent:style0;
 mso-number-format:"\@";
 text-align:center;
 vertical-align:middle;
 white-space:nowrap;
 background:#FFC6AA;
 mso-pattern:auto none;
 color:#0000FF;
 font-size:10pt;
 font-weight:400;
 font-style:normal;
 font-family:"宋体";
 border:none;
 mso-protection:locked visible;
 }
.x92
 {
 mso-style-parent:style0;
 mso-number-format:"\@";
 text-align:distributed;
 vertical-align:middle;
 white-space:nowrap;
 background:#FFFFFF;
 mso-pattern:auto none;
 color:#000000;
 font-size:10pt;
 font-weight:400;
 font-style:normal;
 font-family:"宋体";
 border-top:2px solid #010000;
 border-right:none;
 border-bottom:none;
 border-left:none;
 mso-diagonal-down:none;
 mso-diagonal-up:none;
 mso-protection:locked visible;
 }
.x93
 {
 mso-style-parent:style0;
 mso-number-format:"\@";
 text-align:center;
 vertical-align:middle;
 white-space:nowrap;
 background:#FFFFFF;
 mso-pattern:auto none;
 color:#000000;
 font-size:10pt;
 font-weight:400;
 font-style:normal;
 font-family:"宋体";
 border-top:2px solid #010000;
 border-right:none;
 border-bottom:none;
 border-left:1px solid #010000;
 mso-diagonal-down:none;
 mso-diagonal-up:none;
 mso-protection:locked visible;
 }
.x94
 {
 mso-style-parent:style0;
 mso-number-format:"\@";
 text-align:center;
 vertical-align:middle;
 white-space:nowrap;
 background:#FFFFFF;
 mso-pattern:auto none;
 color:#000000;
 font-size:10pt;
 font-weight:400;
 font-style:normal;
 font-family:"宋体";
 border-top:2px solid #010000;
 border-right:none;
 border-bottom:none;
 border-left:none;
 mso-diagonal-down:none;
 mso-diagonal-up:none;
 mso-protection:locked visible;
 }
.x95
 {
 mso-style-parent:style0;
 mso-number-format:"\@";
 text-align:center;
 vertical-align:middle;
 white-space:nowrap;
 background:#FFFFFF;
 mso-pattern:auto none;
 color:#000000;
 font-size:10pt;
 font-weight:400;
 font-style:normal;
 font-family:"宋体";
 border-top:2px solid #010000;
 border-right:none;
 border-bottom:none;
 border-left:none;
 mso-diagonal-down:none;
 mso-diagonal-up:none;
 mso-protection:locked visible;
 }
.x96
 {
 mso-style-parent:style0;
 mso-number-format:"\@";
 text-align:center;
 vertical-align:middle;
 white-space:nowrap;
 background:#FFFFFF;
 mso-pattern:auto none;
 color:#000000;
 font-size:10pt;
 font-weight:400;
 font-style:normal;
 font-family:"宋体";
 border-top:2px solid #010000;
 border-right:none;
 border-bottom:none;
 border-left:1px solid #010000;
 mso-diagonal-down:none;
 mso-diagonal-up:none;
 mso-protection:locked visible;
 }
.x97
 {
 mso-style-parent:style0;
 mso-number-format:"\@";
 text-align:center;
 vertical-align:middle;
 white-space:nowrap;
 background:#FFFFFF;
 mso-pattern:auto none;
 color:#000000;
 font-size:10pt;
 font-weight:400;
 font-style:normal;
 font-family:"宋体";
 border-top:2px solid #010000;
 border-right:none;
 border-bottom:none;
 border-left:none;
 mso-diagonal-down:none;
 mso-diagonal-up:none;
 mso-protection:locked visible;
 }
.x98
 {
 mso-style-parent:style0;
 mso-number-format:"\@";
 text-align:center;
 vertical-align:middle;
 white-space:nowrap;
 background:#FFFFFF;
 mso-pattern:auto none;
 color:#000000;
 font-size:10pt;
 font-weight:400;
 font-style:normal;
 font-family:"宋体";
 border-top:none;
 border-right:none;
 border-bottom:none;
 border-left:1px solid #010000;
 mso-diagonal-down:none;
 mso-diagonal-up:none;
 mso-protection:locked visible;
 }
.x99
 {
 mso-style-parent:style0;
 mso-number-format:"\@";
 text-align:center;
 vertical-align:middle;
 white-space:nowrap;
 background:#FFFFFF;
 mso-pattern:auto none;
 color:#000000;
 font-size:10pt;
 font-weight:400;
 font-style:normal;
 font-family:"宋体";
 border-top:none;
 border-right:none;
 border-bottom:none;
 border-left:1px solid #010000;
 mso-diagonal-down:none;
 mso-diagonal-up:none;
 mso-protection:locked visible;
 }
.x100
 {
 mso-style-parent:style0;
 mso-number-format:"\@";
 text-align:center;
 vertical-align:middle;
 white-space:normal;word-wrap:break-word;
 background:#FFFFFF;
 mso-pattern:auto none;
 color:#000000;
 font-size:10pt;
 font-weight:400;
 font-style:normal;
 font-family:"宋体";
 border-top:1px solid #010000;
 border-right:none;
 border-bottom:none;
 border-left:1px solid #010000;
 mso-diagonal-down:none;
 mso-diagonal-up:none;
 mso-protection:locked visible;
 }
.x101
 {
 mso-style-parent:style0;
 mso-number-format:"\@";
 text-align:center;
 vertical-align:middle;
 white-space:nowrap;
 background:#FFFFFF;
 mso-pattern:auto none;
 color:#000000;
 font-size:10pt;
 font-weight:700;
 font-style:normal;
 font-family:"宋体";
 border-top:1px solid #010000;
 border-right:none;
 border-bottom:none;
 border-left:1px solid #010000;
 mso-diagonal-down:none;
 mso-diagonal-up:none;
 mso-protection:locked visible;
 }
.x102
 {
 mso-style-parent:style0;
 mso-number-format:"\@";
 text-align:center;
 vertical-align:middle;
 white-space:nowrap;
 background:#FFFFFF;
 mso-pattern:auto none;
 color:#000000;
 font-size:10pt;
 font-weight:700;
 font-style:normal;
 font-family:"宋体";
 border-top:1px solid #010000;
 border-right:none;
 border-bottom:none;
 border-left:none;
 mso-diagonal-down:none;
 mso-diagonal-up:none;
 mso-protection:locked visible;
 }
.x103
 {
 mso-style-parent:style0;
 mso-number-format:"\@";
 text-align:center;
 vertical-align:middle;
 white-space:nowrap;
 background:#FFFFFF;
 mso-pattern:auto none;
 color:#000000;
 font-size:10pt;
 font-weight:700;
 font-style:normal;
 font-family:"宋体";
 border-top:1px solid #010000;
 border-right:none;
 border-bottom:none;
 border-left:none;
 mso-diagonal-down:none;
 mso-diagonal-up:none;
 mso-protection:locked visible;
 }
.x104
 {
 mso-style-parent:style0;
 mso-number-format:"\@";
 text-align:center;
 vertical-align:middle;
 white-space:nowrap;
 background:#FFFFFF;
 mso-pattern:auto none;
 color:#000000;
 font-size:10pt;
 font-weight:700;
 font-style:normal;
 font-family:"宋体";
 border-top:1px solid #010000;
 border-right:none;
 border-bottom:none;
 border-left:1px solid #010000;
 mso-diagonal-down:none;
 mso-diagonal-up:none;
 mso-protection:locked visible;
 }
.x105
 {
 mso-style-parent:style0;
 mso-number-format:"\@";
 text-align:right;
 vertical-align:middle;
 white-space:nowrap;
 background:#D7D7FF;
 mso-pattern:auto none;
 color:#0000FF;
 font-size:10pt;
 font-weight:400;
 font-style:normal;
 font-family:"宋体";
 border-top:1px solid #010000;
 border-right:none;
 border-bottom:none;
 border-left:1px solid #010000;
 mso-diagonal-down:none;
 mso-diagonal-up:none;
 mso-protection:locked visible;
 }
.x106
 {
 mso-style-parent:style0;
 mso-number-format:"\@";
 text-align:right;
 vertical-align:middle;
 white-space:nowrap;
 background:#D7D7FF;
 mso-pattern:auto none;
 color:#0000FF;
 font-size:10pt;
 font-weight:400;
 font-style:normal;
 font-family:"宋体";
 border-top:1px solid #010000;
 border-right:none;
 border-bottom:none;
 border-left:none;
 mso-diagonal-down:none;
 mso-diagonal-up:none;
 mso-protection:locked visible;
 }
.x107
 {
 mso-style-parent:style0;
 mso-number-format:"\@";
 text-align:right;
 vertical-align:middle;
 white-space:nowrap;
 background:#FFC6AA;
 mso-pattern:auto none;
 color:#0000FF;
 font-size:10pt;
 font-weight:400;
 font-style:normal;
 font-family:"宋体";
 border-top:1px solid #010000;
 border-right:none;
 border-bottom:none;
 border-left:1px solid #010000;
 mso-diagonal-down:none;
 mso-diagonal-up:none;
 mso-protection:locked visible;
 }
.x108
 {
 mso-style-parent:style0;
 mso-number-format:"\@";
 text-align:right;
 vertical-align:middle;
 white-space:nowrap;
 background:#FFC6AA;
 mso-pattern:auto none;
 color:#0000FF;
 font-size:10pt;
 font-weight:400;
 font-style:normal;
 font-family:"宋体";
 border-top:1px solid #010000;
 border-right:none;
 border-bottom:none;
 border-left:none;
 mso-diagonal-down:none;
 mso-diagonal-up:none;
 mso-protection:locked visible;
 }
.x109
 {
 mso-style-parent:style0;
 mso-number-format:"\@";
 text-align:right;
 vertical-align:middle;
 white-space:nowrap;
 background:#FFC6AA;
 mso-pattern:auto none;
 color:#0000FF;
 font-size:10pt;
 font-weight:400;
 font-style:normal;
 font-family:"宋体";
 border-top:1px solid #010000;
 border-right:none;
 border-bottom:none;
 border-left:1px solid #010000;
 mso-diagonal-down:none;
 mso-diagonal-up:none;
 mso-protection:locked visible;
 }
.x110
 {
 mso-style-parent:style0;
 mso-number-format:"\@";
 text-align:left;
 vertical-align:middle;
 white-space:nowrap;
 background:#FFFFFF;
 mso-pattern:auto none;
 color:#000000;
 font-size:10pt;
 font-weight:400;
 font-style:normal;
 font-family:"宋体";
 border-top:2px solid #010000;
 border-right:none;
 border-bottom:none;
 border-left:none;
 mso-diagonal-down:none;
 mso-diagonal-up:none;
 mso-protection:locked visible;
 }
.x111
 {
 mso-style-parent:style0;
 mso-number-format:"\@";
 text-align:center;
 vertical-align:middle;
 white-space:nowrap;
 background:#FFC6AA;
 mso-pattern:auto none;
 color:#0000FF;
 font-size:10pt;
 font-weight:400;
 font-style:normal;
 font-family:"宋体";
 border-top:2px solid #010000;
 border-right:none;
 border-bottom:none;
 border-left:none;
 mso-diagonal-down:none;
 mso-diagonal-up:none;
 mso-protection:locked visible;
 }
.x112
 {
 mso-style-parent:style0;
 mso-number-format:"\@";
 text-align:center;
 vertical-align:middle;
 white-space:nowrap;
 background:#FFC6AA;
 mso-pattern:auto none;
 color:#0000FF;
 font-size:10pt;
 font-weight:400;
 font-style:normal;
 font-family:"宋体";
 border-top:2px solid #010000;
 border-right:none;
 border-bottom:none;
 border-left:none;
 mso-diagonal-down:none;
 mso-diagonal-up:none;
 mso-protection:locked visible;
 }
.x113
 {
 mso-style-parent:style0;
 mso-number-format:"\@";
 text-align:center;
 vertical-align:middle;
 white-space:nowrap;
 background:#FFC6AA;
 mso-pattern:auto none;
 color:#0000FF;
 font-size:10pt;
 font-weight:400;
 font-style:normal;
 font-family:"宋体";
 border-top:2px solid #010000;
 border-right:none;
 border-bottom:none;
 border-left:none;
 mso-diagonal-down:none;
 mso-diagonal-up:none;
 mso-protection:locked visible;
 }
.x114
 {
 mso-style-parent:style0;
 mso-number-format:"\@";
 text-align:center;
 vertical-align:middle;
 white-space:nowrap;
 background:#FFFFFF;
 mso-pattern:auto none;
 color:#000000;
 font-size:10pt;
 font-weight:400;
 font-style:normal;
 font-family:"宋体";
 border-top:2px solid #010000;
 border-right:none;
 border-bottom:none;
 border-left:none;
 mso-diagonal-down:none;
 mso-diagonal-up:none;
 mso-protection:locked visible;
 }
.x115
 {
 mso-style-parent:style0;
 mso-number-format:"\@";
 text-align:center;
 vertical-align:middle;
 white-space:normal;word-wrap:break-word;
 background:#FFFFFF;
 mso-pattern:auto none;
 color:#000000;
 font-size:10pt;
 font-weight:400;
 font-style:normal;
 font-family:"宋体";
 border-top:1px solid #010000;
 border-right:none;
 border-bottom:none;
 border-left:1px solid #010000;
 mso-diagonal-down:none;
 mso-diagonal-up:none;
 mso-protection:locked visible;
 }
.x116
 {
 mso-style-parent:style0;
 mso-number-format:"\@";
 text-align:center;
 vertical-align:middle;
 white-space:normal;word-wrap:break-word;
 background:#FFFFFF;
 mso-pattern:auto none;
 color:#000000;
 font-size:10pt;
 font-weight:400;
 font-style:normal;
 font-family:"宋体";
 border-top:1px solid #010000;
 border-right:none;
 border-bottom:none;
 border-left:none;
 mso-diagonal-down:none;
 mso-diagonal-up:none;
 mso-protection:locked visible;
 }
.x117
 {
 mso-style-parent:style0;
 mso-number-format:"\@";
 text-align:center;
 vertical-align:middle;
 white-space:nowrap;
 background:#FFFFFF;
 mso-pattern:auto none;
 color:#000000;
 font-size:10pt;
 font-weight:400;
 font-style:normal;
 font-family:"宋体";
 border-top:none;
 border-right:none;
 border-bottom:none;
 border-left:1px solid #010000;
 mso-diagonal-down:none;
 mso-diagonal-up:none;
 mso-protection:locked visible;
 }
.x118
 {
 mso-style-parent:style0;
 mso-number-format:"\@";
 text-align:left;
 vertical-align:middle;
 white-space:nowrap;
 background:#FFFFFF;
 mso-pattern:auto none;
 color:#000000;
 font-size:10pt;
 font-weight:400;
 font-style:normal;
 font-family:"宋体";
 border-top:2px solid #010000;
 border-right:none;
 border-bottom:none;
 border-left:none;
 mso-diagonal-down:none;
 mso-diagonal-up:none;
 mso-protection:locked visible;
 }
.x119
 {
 mso-style-parent:style0;
 mso-number-format:"\@";
 text-align:center;
 vertical-align:middle;
 white-space:normal;word-wrap:break-word;
 background:#FFFFFF;
 mso-pattern:auto none;
 color:#000000;
 font-size:10pt;
 font-weight:400;
 font-style:normal;
 font-family:"宋体";
 border-top:2px solid #010000;
 border-right:none;
 border-bottom:none;
 border-left:none;
 mso-diagonal-down:none;
 mso-diagonal-up:none;
 mso-protection:locked visible;
 }
.x120
 {
 mso-style-parent:style0;
 mso-number-format:"\@";
 text-align:center;
 vertical-align:middle;
 white-space:normal;word-wrap:break-word;
 background:#FFFFFF;
 mso-pattern:auto none;
 color:#000000;
 font-size:10pt;
 font-weight:400;
 font-style:normal;
 font-family:"宋体";
 border-top:2px solid #010000;
 border-right:none;
 border-bottom:none;
 border-left:1px solid #010000;
 mso-diagonal-down:none;
 mso-diagonal-up:none;
 mso-protection:locked visible;
 }
.x121
 {
 mso-style-parent:style0;
 mso-number-format:"\@";
 text-align:center;
 vertical-align:middle;
 white-space:normal;word-wrap:break-word;
 background:#FFFFFF;
 mso-pattern:auto none;
 color:#000000;
 font-size:10pt;
 font-weight:400;
 font-style:normal;
 font-family:"宋体";
 border-top:2px solid #010000;
 border-right:none;
 border-bottom:none;
 border-left:none;
 mso-diagonal-down:none;
 mso-diagonal-up:none;
 mso-protection:locked visible;
 }
.x122
 {
 mso-style-parent:style0;
 mso-number-format:"\@";
 text-align:center;
 vertical-align:middle;
 white-space:nowrap;
 background:#FFFFFF;
 mso-pattern:auto none;
 color:#000000;
 font-size:10pt;
 font-weight:400;
 font-style:normal;
 font-family:"宋体";
 border-top:2px solid #010000;
 border-right:none;
 border-bottom:none;
 border-left:none;
 mso-diagonal-down:none;
 mso-diagonal-up:none;
 mso-protection:locked visible;
 }
.x123
 {
 mso-style-parent:style0;
 mso-number-format:"\@";
 text-align:center;
 vertical-align:middle;
 white-space:nowrap;
 background:#FFFFFF;
 mso-pattern:auto none;
 color:#000000;
 font-size:10pt;
 font-weight:400;
 font-style:normal;
 font-family:"宋体";
 border-top:2px solid #010000;
 border-right:none;
 border-bottom:none;
 border-left:none;
 mso-diagonal-down:none;
 mso-diagonal-up:none;
 mso-protection:locked visible;
 }
.x124
 {
 mso-style-parent:style0;
 mso-number-format:"\@";
 text-align:center;
 vertical-align:middle;
 white-space:normal;word-wrap:break-word;
 background:#FFFFFF;
 mso-pattern:auto none;
 color:#000000;
 font-size:10pt;
 font-weight:400;
 font-style:normal;
 font-family:"宋体";
 border:none;
 mso-protection:locked visible;
 }
.x125
 {
 mso-style-parent:style0;
 mso-number-format:"\@";
 text-align:center;
 vertical-align:middle;
 white-space:normal;word-wrap:break-word;
 background:#FFFFFF;
 mso-pattern:auto none;
 color:#000000;
 font-size:10pt;
 font-weight:400;
 font-style:normal;
 font-family:"宋体";
 border-top:none;
 border-right:none;
 border-bottom:none;
 border-left:1px solid #010000;
 mso-diagonal-down:none;
 mso-diagonal-up:none;
 mso-protection:locked visible;
 }
.x126
 {
 mso-style-parent:style0;
 mso-number-format:"\@";
 text-align:center;
 vertical-align:middle;
 white-space:normal;word-wrap:break-word;
 background:#FFFFFF;
 mso-pattern:auto none;
 color:#000000;
 font-size:10pt;
 font-weight:400;
 font-style:normal;
 font-family:"宋体";
 border-top:1px solid #010000;
 border-right:none;
 border-bottom:none;
 border-left:1px solid #010000;
 mso-diagonal-down:none;
 mso-diagonal-up:none;
 mso-protection:locked visible;
 }
.x127
 {
 mso-style-parent:style0;
 mso-number-format:"\@";
 text-align:center;
 vertical-align:middle;
 white-space:nowrap;
 background:#FFFFFF;
 mso-pattern:auto none;
 color:#000000;
 font-size:10pt;
 font-weight:400;
 font-style:normal;
 font-family:"宋体";
 border-top:1px solid #010000;
 border-right:none;
 border-bottom:none;
 border-left:none;
 mso-diagonal-down:none;
 mso-diagonal-up:none;
 mso-protection:locked visible;
 }
.x128
 {
 mso-style-parent:style0;
 mso-number-format:"\@";
 text-align:center;
 vertical-align:middle;
 white-space:normal;word-wrap:break-word;
 background:#FFFFFF;
 mso-pattern:auto none;
 color:#000000;
 font-size:10pt;
 font-weight:400;
 font-style:normal;
 font-family:"宋体";
 border-top:none;
 border-right:none;
 border-bottom:none;
 border-left:1px solid #010000;
 mso-diagonal-down:none;
 mso-diagonal-up:none;
 mso-protection:locked visible;
 }
.x129
 {
 mso-style-parent:style0;
 mso-number-format:"\@";
 text-align:center;
 vertical-align:middle;
 white-space:normal;word-wrap:break-word;
 background:#FFFFFF;
 mso-pattern:auto none;
 color:#000000;
 font-size:10pt;
 font-weight:400;
 font-style:normal;
 font-family:"宋体";
 border-top:none;
 border-right:none;
 border-bottom:none;
 border-left:1px solid #010000;
 mso-diagonal-down:none;
 mso-diagonal-up:none;
 mso-protection:locked visible;
 }
.x130
 {
 mso-style-parent:style0;
 mso-number-format:"\@";
 text-align:center;
 vertical-align:middle;
 white-space:normal;word-wrap:break-word;
 background:#FFFFFF;
 mso-pattern:auto none;
 color:#000000;
 font-size:10pt;
 font-weight:400;
 font-style:normal;
 font-family:"宋体";
 border-top:none;
 border-right:none;
 border-bottom:none;
 border-left:1px solid #010000;
 mso-diagonal-down:none;
 mso-diagonal-up:none;
 mso-protection:locked visible;
 }
.x131
 {
 mso-style-parent:style0;
 mso-number-format:"\@";
 text-align:right;
 vertical-align:middle;
 white-space:nowrap;
 background:#D7D7FF;
 mso-pattern:auto none;
 color:#0000FF;
 font-size:10pt;
 font-weight:400;
 font-style:normal;
 font-family:"宋体";
 border-top:1px solid #010000;
 border-right:none;
 border-bottom:none;
 border-left:1px solid #010000;
 mso-diagonal-down:none;
 mso-diagonal-up:none;
 mso-protection:locked visible;
 }
.x132
 {
 mso-style-parent:style0;
 mso-number-format:"\@";
 text-align:center;
 vertical-align:middle;
 white-space:normal;word-wrap:break-word;
 background:#FFFFFF;
 mso-pattern:auto none;
 color:#000000;
 font-size:10pt;
 font-weight:400;
 font-style:normal;
 font-family:"宋体";
 border-top:1px solid #010000;
 border-right:none;
 border-bottom:none;
 border-left:none;
 mso-diagonal-down:none;
 mso-diagonal-up:none;
 mso-protection:locked visible;
 }
.x133
 {
 mso-style-parent:style0;
 mso-number-format:"\@";
 text-align:center;
 vertical-align:middle;
 white-space:normal;word-wrap:break-word;
 background:#FFFFFF;
 mso-pattern:auto none;
 color:#000000;
 font-size:10pt;
 font-weight:400;
 font-style:normal;
 font-family:"宋体";
 border-top:2px solid #010000;
 border-right:none;
 border-bottom:none;
 border-left:1px solid #010000;
 mso-diagonal-down:none;
 mso-diagonal-up:none;
 mso-protection:locked visible;
 }
.x134
 {
 mso-style-parent:style0;
 mso-number-format:"\@";
 text-align:center;
 vertical-align:middle;
 white-space:normal;word-wrap:break-word;
 background:#FFFFFF;
 mso-pattern:auto none;
 color:#000000;
 font-size:10pt;
 font-weight:400;
 font-style:normal;
 font-family:"宋体";
 border-top:1px solid #010000;
 border-right:none;
 border-bottom:none;
 border-left:1px solid #010000;
 mso-diagonal-down:none;
 mso-diagonal-up:none;
 mso-protection:locked visible;
 }
.x135
 {
 mso-style-parent:style0;
 mso-number-format:"\@";
 text-align:center;
 vertical-align:middle;
 white-space:normal;word-wrap:break-word;
 background:#FFFFFF;
 mso-pattern:auto none;
 color:#000000;
 font-size:10pt;
 font-weight:400;
 font-style:normal;
 font-family:"宋体";
 border-top:1px solid #010000;
 border-right:none;
 border-bottom:none;
 border-left:none;
 mso-diagonal-down:none;
 mso-diagonal-up:none;
 mso-protection:locked visible;
 }
.x136
 {
 mso-style-parent:style0;
 mso-number-format:"\@";
 text-align:center;
 vertical-align:middle;
 white-space:nowrap;
 background:#FFFFFF;
 mso-pattern:auto none;
 color:#000000;
 font-size:10pt;
 font-weight:400;
 font-style:normal;
 font-family:"宋体";
 border-top:1px solid #010000;
 border-right:none;
 border-bottom:none;
 border-left:1px solid #010000;
 mso-diagonal-down:none;
 mso-diagonal-up:none;
 mso-protection:locked visible;
 }
.x137
 {
 mso-style-parent:style0;
 mso-number-format:"\@";
 text-align:center;
 vertical-align:middle;
 white-space:normal;word-wrap:break-word;
 background:#FFFFFF;
 mso-pattern:auto none;
 color:#000000;
 font-size:10pt;
 font-weight:400;
 font-style:normal;
 font-family:"宋体";
 border-top:2px solid #010000;
 border-right:none;
 border-bottom:none;
 border-left:1px solid #010000;
 mso-diagonal-down:none;
 mso-diagonal-up:none;
 mso-protection:locked visible;
 }
.x138
 {
 mso-style-parent:style0;
 mso-number-format:"\@";
 text-align:center;
 vertical-align:middle;
 white-space:normal;word-wrap:break-word;
 background:#FFFFFF;
 mso-pattern:auto none;
 color:#000000;
 font-size:10pt;
 font-weight:400;
 font-style:normal;
 font-family:"宋体";
 border-top:2px solid #010000;
 border-right:none;
 border-bottom:none;
 border-left:none;
 mso-diagonal-down:none;
 mso-diagonal-up:none;
 mso-protection:locked visible;
 }
.x139
 {
 mso-style-parent:style0;
 mso-number-format:"\@";
 text-align:right;
 vertical-align:middle;
 white-space:normal;word-wrap:break-word;
 background:#D7D7FF;
 mso-pattern:auto none;
 color:#0000FF;
 font-size:10pt;
 font-weight:400;
 font-style:normal;
 font-family:"宋体";
 border-top:1px solid #010000;
 border-right:none;
 border-bottom:none;
 border-left:1px solid #010000;
 mso-diagonal-down:none;
 mso-diagonal-up:none;
 mso-protection:locked visible;
 }
.x140
 {
 mso-style-parent:style0;
 mso-number-format:"\@";
 text-align:right;
 vertical-align:middle;
 white-space:normal;word-wrap:break-word;
 background:#FFC6AA;
 mso-pattern:auto none;
 color:#0000FF;
 font-size:10pt;
 font-weight:400;
 font-style:normal;
 font-family:"宋体";
 border-top:1px solid #010000;
 border-right:none;
 border-bottom:none;
 border-left:1px solid #010000;
 mso-diagonal-down:none;
 mso-diagonal-up:none;
 mso-protection:locked visible;
 }
.x141
 {
 mso-style-parent:style0;
 mso-number-format:"\@";
 text-align:center;
 vertical-align:middle;
 white-space:normal;word-wrap:break-word;
 background:#FFFFFF;
 mso-pattern:auto none;
 color:#000000;
 font-size:10pt;
 font-weight:400;
 font-style:normal;
 font-family:"宋体";
 border-top:2px solid #010000;
 border-right:none;
 border-bottom:none;
 border-left:none;
 mso-diagonal-down:none;
 mso-diagonal-up:none;
 mso-protection:locked visible;
 }
.x142
 {
 mso-style-parent:style0;
 mso-number-format:"\@";
 text-align:center;
 vertical-align:middle;
 white-space:nowrap;
 background:#FFFFFF;
 mso-pattern:auto none;
 color:#000000;
 font-size:10pt;
 font-weight:400;
 font-style:normal;
 font-family:"宋体";
 border-top:1px solid #010000;
 border-right:none;
 border-bottom:none;
 border-left:1px solid #010000;
 mso-diagonal-down:none;
 mso-diagonal-up:none;
 mso-protection:locked visible;
 }
.x143
 {
 mso-style-parent:style0;
 mso-number-format:"\@";
 text-align:center;
 vertical-align:middle;
 white-space:normal;word-wrap:break-word;
 background:#FFFFFF;
 mso-pattern:auto none;
 color:#000000;
 font-size:10pt;
 font-weight:400;
 font-style:normal;
 font-family:"宋体";
 border-top:none;
 border-right:none;
 border-bottom:none;
 border-left:1px solid #010000;
 mso-diagonal-down:none;
 mso-diagonal-up:none;
 mso-protection:locked visible;
 }
.x144
 {
 mso-style-parent:style0;
 mso-number-format:"\@";
 text-align:center;
 vertical-align:middle;
 white-space:normal;word-wrap:break-word;
 background:#FFFFFF;
 mso-pattern:auto none;
 color:#000000;
 font-size:10pt;
 font-weight:400;
 font-style:normal;
 font-family:"宋体";
 border-top:2px solid #010000;
 border-right:none;
 border-bottom:none;
 border-left:none;
 mso-diagonal-down:none;
 mso-diagonal-up:none;
 mso-protection:locked visible;
 }
.x145
 {
 mso-style-parent:style0;
 mso-number-format:"\@";
 text-align:left;
 vertical-align:middle;
 white-space:nowrap;
 background:#FFFFFF;
 mso-pattern:auto none;
 color:#000000;
 font-size:9pt;
 font-weight:400;
 font-style:normal;
 font-family:"宋体";
 border:none;
 mso-protection:locked visible;
 }
.x146
 {
 mso-style-parent:style0;
 mso-number-format:"\@";
 text-align:center;
 vertical-align:middle;
 white-space:nowrap;
 background:#FFFFFF;
 mso-pattern:auto none;
 color:#000000;
 font-size:10pt;
 font-weight:400;
 font-style:normal;
 font-family:"宋体";
 border-top:2px solid #010000;
 border-right:none;
 border-bottom:none;
 border-left:none;
 mso-diagonal-down:none;
 mso-diagonal-up:none;
 mso-protection:locked visible;
 }
.x147
 {
 mso-style-parent:style0;
 mso-number-format:"\@";
 text-align:center;
 vertical-align:middle;
 white-space:nowrap;
 background:#FFFFFF;
 mso-pattern:auto none;
 color:#000000;
 font-size:10pt;
 font-weight:400;
 font-style:normal;
 font-family:"宋体";
 border-top:none;
 border-right:none;
 border-bottom:none;
 border-left:1px solid #010000;
 mso-diagonal-down:none;
 mso-diagonal-up:none;
 mso-protection:locked visible;
 }
.x148
 {
 mso-style-parent:style0;
 mso-number-format:"\@";
 text-align:distributed;
 vertical-align:middle;
 white-space:nowrap;
 background:#FFFFFF;
 mso-pattern:auto none;
 color:#000000;
 font-size:10pt;
 font-weight:400;
 font-style:normal;
 font-family:"宋体";
 border-top:1px solid #010000;
 border-right:none;
 border-bottom:none;
 border-left:none;
 mso-diagonal-down:none;
 mso-diagonal-up:none;
 mso-protection:locked visible;
 }
.x149
 {
 mso-style-parent:style0;
 mso-number-format:"\@";
 text-align:left;
 vertical-align:middle;
 white-space:nowrap;
 background:#FFFFFF;
 mso-pattern:auto none;
 color:#000000;
 font-size:10pt;
 font-weight:400;
 font-style:normal;
 font-family:"宋体";
 border-top:none;
 border-right:none;
 border-bottom:none;
 border-left:1px solid #010000;
 mso-diagonal-down:none;
 mso-diagonal-up:none;
 mso-protection:locked visible;
 }
.x150
 {
 mso-style-parent:style0;
 mso-number-format:"\@";
 text-align:center;
 vertical-align:middle;
 white-space:normal;word-wrap:break-word;
 background:#FFFFFF;
 mso-pattern:auto none;
 color:#000000;
 font-size:9pt;
 font-weight:400;
 font-style:normal;
 font-family:"宋体";
 border-top:1px solid #010000;
 border-right:none;
 border-bottom:none;
 border-left:1px solid #010000;
 mso-diagonal-down:none;
 mso-diagonal-up:none;
 mso-protection:locked visible;
 }
.x151
 {
 mso-style-parent:style0;
 mso-number-format:"\@";
 text-align:center;
 vertical-align:middle;
 white-space:normal;word-wrap:break-word;
 background:#FFFFFF;
 mso-pattern:auto none;
 color:#000000;
 font-size:9pt;
 font-weight:400;
 font-style:normal;
 font-family:"宋体";
 border-top:none;
 border-right:none;
 border-bottom:none;
 border-left:1px solid #010000;
 mso-diagonal-down:none;
 mso-diagonal-up:none;
 mso-protection:locked visible;
 }
.x152
 {
 mso-style-parent:style0;
 mso-number-format:"\@";
 text-align:center;
 vertical-align:middle;
 white-space:normal;word-wrap:break-word;
 background:#FFFFFF;
 mso-pattern:auto none;
 color:#000000;
 font-size:9pt;
 font-weight:400;
 font-style:normal;
 font-family:"宋体";
 border-top:none;
 border-right:none;
 border-bottom:none;
 border-left:1px solid #010000;
 mso-diagonal-down:none;
 mso-diagonal-up:none;
 mso-protection:locked visible;
 }
.x153
 {
 mso-style-parent:style0;
 mso-number-format:"\@";
 text-align:center;
 vertical-align:middle;
 white-space:normal;word-wrap:break-word;
 background:#FFFFFF;
 mso-pattern:auto none;
 color:#000000;
 font-size:9pt;
 font-weight:400;
 font-style:normal;
 font-family:"宋体";
 border-top:1px solid #010000;
 border-right:none;
 border-bottom:none;
 border-left:1px solid #010000;
 mso-diagonal-down:none;
 mso-diagonal-up:none;
 mso-protection:locked visible;
 }
.x154
 {
 mso-style-parent:style0;
 mso-number-format:"\@";
 text-align:right;
 vertical-align:middle;
 white-space:nowrap;
 background:#FFFFFF;
 mso-pattern:auto none;
 color:#000000;
 font-size:9pt;
 font-weight:400;
 font-style:normal;
 font-family:"宋体";
 border:none;
 mso-protection:locked visible;
 }
.x155
 {
 mso-style-parent:style0;
 mso-number-format:"\@";
 text-align:center;
 vertical-align:middle;
 white-space:nowrap;
 background:#FFFFFF;
 mso-pattern:auto none;
 color:#000000;
 font-size:10pt;
 font-weight:400;
 font-style:normal;
 font-family:"宋体";
 border-top:none;
 border-right:none;
 border-bottom:none;
 border-left:1px solid #010000;
 mso-diagonal-down:none;
 mso-diagonal-up:none;
 mso-protection:locked visible;
 }
.x156
 {
 mso-style-parent:style0;
 mso-number-format:"\@";
 text-align:right;
 vertical-align:middle;
 white-space:nowrap;
 background:#FFFFFF;
 mso-pattern:auto none;
 color:#000000;
 font-size:10pt;
 font-weight:400;
 font-style:normal;
 font-family:"宋体";
 border-top:2px solid #010000;
 border-right:none;
 border-bottom:none;
 border-left:none;
 mso-diagonal-down:none;
 mso-diagonal-up:none;
 mso-protection:locked visible;
 }
.x157
 {
 mso-style-parent:style0;
 mso-number-format:"\@";
 text-align:center;
 vertical-align:middle;
 white-space:normal;word-wrap:break-word;
 background:#FFFFFF;
 mso-pattern:auto none;
 color:#000000;
 font-size:10pt;
 font-weight:400;
 font-style:normal;
 font-family:"宋体";
 border-top:1px solid #010000;
 border-right:none;
 border-bottom:none;
 border-left:none;
 mso-diagonal-down:none;
 mso-diagonal-up:none;
 mso-protection:locked visible;
 }
.x158
 {
 mso-style-parent:style0;
 mso-number-format:"\@";
 text-align:center;
 vertical-align:middle;
 white-space:normal;word-wrap:break-word;
 background:#FFFFFF;
 mso-pattern:auto none;
 color:#000000;
 font-size:10pt;
 font-weight:400;
 font-style:normal;
 font-family:"宋体";
 border-top:1px solid #010000;
 border-right:none;
 border-bottom:none;
 border-left:none;
 mso-diagonal-down:none;
 mso-diagonal-up:none;
 mso-protection:locked visible;
 }
.x159
 {
 mso-style-parent:style0;
 mso-number-format:"\@";
 text-align:center;
 vertical-align:middle;
 white-space:normal;word-wrap:break-word;
 background:#FFFFFF;
 mso-pattern:auto none;
 color:#000000;
 font-size:10pt;
 font-weight:400;
 font-style:normal;
 font-family:"宋体";
 border-top:1px solid #010000;
 border-right:none;
 border-bottom:none;
 border-left:none;
 mso-diagonal-down:none;
 mso-diagonal-up:none;
 mso-protection:locked visible;
 }
.x160
 {
 mso-style-parent:style0;
 mso-number-format:"\@";
 text-align:center;
 vertical-align:middle;
 white-space:nowrap;
 background:#FFFFFF;
 mso-pattern:auto none;
 color:#000000;
 font-size:10pt;
 font-weight:400;
 font-style:normal;
 font-family:"宋体";
 border-top:2px solid #010000;
 border-right:none;
 border-bottom:none;
 border-left:none;
 mso-diagonal-down:none;
 mso-diagonal-up:none;
 mso-protection:locked visible;
 }
.x161
 {
 mso-style-parent:style0;
 mso-number-format:"\@";
 text-align:right;
 vertical-align:middle;
 white-space:nowrap;
 background:#D7D7FF;
 mso-pattern:auto none;
 color:#0000FF;
 font-size:9pt;
 font-weight:400;
 font-style:normal;
 font-family:"宋体";
 border-top:1px solid #010000;
 border-right:none;
 border-bottom:none;
 border-left:1px solid #010000;
 mso-diagonal-down:none;
 mso-diagonal-up:none;
 mso-protection:locked visible;
 }
.x162
 {
 mso-style-parent:style0;
 mso-number-format:"\@";
 text-align:right;
 vertical-align:middle;
 white-space:nowrap;
 background:#FFC6AA;
 mso-pattern:auto none;
 color:#0000FF;
 font-size:9pt;
 font-weight:400;
 font-style:normal;
 font-family:"宋体";
 border-top:1px solid #010000;
 border-right:none;
 border-bottom:none;
 border-left:1px solid #010000;
 mso-diagonal-down:none;
 mso-diagonal-up:none;
 mso-protection:locked visible;
 }
.x163
 {
 mso-style-parent:style0;
 mso-number-format:"\@";
 text-align:right;
 vertical-align:middle;
 white-space:normal;word-wrap:break-word;
 background:#D7D7FF;
 mso-pattern:auto none;
 color:#0000FF;
 font-size:9pt;
 font-weight:400;
 font-style:normal;
 font-family:"宋体";
 border-top:1px solid #010000;
 border-right:none;
 border-bottom:none;
 border-left:1px solid #010000;
 mso-diagonal-down:none;
 mso-diagonal-up:none;
 mso-protection:locked visible;
 }
.x164
 {
 mso-style-parent:style0;
 mso-number-format:"\@";
 text-align:right;
 vertical-align:middle;
 white-space:normal;word-wrap:break-word;
 background:#FFC6AA;
 mso-pattern:auto none;
 color:#0000FF;
 font-size:9pt;
 font-weight:400;
 font-style:normal;
 font-family:"宋体";
 border-top:1px solid #010000;
 border-right:none;
 border-bottom:none;
 border-left:1px solid #010000;
 mso-diagonal-down:none;
 mso-diagonal-up:none;
 mso-protection:locked visible;
 }
.x165
 {
 mso-style-parent:style0;
 mso-number-format:"\@";
 text-align:center;
 vertical-align:middle;
 white-space:nowrap;
 background:#FFFFFF;
 mso-pattern:auto none;
 color:#000000;
 font-size:10pt;
 font-weight:400;
 font-style:normal;
 font-family:"宋体";
 border-top:1px solid #010000;
 border-right:none;
 border-bottom:none;
 border-left:1px solid #010000;
 mso-diagonal-down:none;
 mso-diagonal-up:none;
 mso-protection:locked visible;
 }
.x166
 {
 mso-style-parent:style0;
 mso-number-format:"\@";
 text-align:center;
 vertical-align:middle;
 white-space:nowrap;
 background:#FFFFFF;
 mso-pattern:auto none;
 color:#000000;
 font-size:10pt;
 font-weight:400;
 font-style:normal;
 font-family:"宋体";
 border-top:1px solid #010000;
 border-right:none;
 border-bottom:none;
 border-left:none;
 mso-diagonal-down:none;
 mso-diagonal-up:none;
 mso-protection:locked visible;
 }
.x167
 {
 mso-style-parent:style0;
 mso-number-format:"\@";
 text-align:right;
 vertical-align:middle;
 white-space:nowrap;
 background:#D7D7FF;
 mso-pattern:auto none;
 color:#0000FF;
 font-size:9pt;
 font-weight:400;
 font-style:normal;
 font-family:"宋体";
 border-top:1px solid #010000;
 border-right:none;
 border-bottom:none;
 border-left:1px solid #010000;
 mso-diagonal-down:none;
 mso-diagonal-up:none;
 mso-protection:locked visible;
 }
.x168
 {
 mso-style-parent:style0;
 mso-number-format:"\@";
 text-align:right;
 vertical-align:middle;
 white-space:nowrap;
 background:#D7D7FF;
 mso-pattern:auto none;
 color:#0000FF;
 font-size:9pt;
 font-weight:400;
 font-style:normal;
 font-family:"宋体";
 border-top:1px solid #010000;
 border-right:none;
 border-bottom:none;
 border-left:none;
 mso-diagonal-down:none;
 mso-diagonal-up:none;
 mso-protection:locked visible;
 }
.x169
 {
 mso-style-parent:style0;
 mso-number-format:"\@";
 text-align:right;
 vertical-align:middle;
 white-space:nowrap;
 background:#FFC6AA;
 mso-pattern:auto none;
 color:#0000FF;
 font-size:9pt;
 font-weight:400;
 font-style:normal;
 font-family:"宋体";
 border-top:1px solid #010000;
 border-right:none;
 border-bottom:none;
 border-left:1px solid #010000;
 mso-diagonal-down:none;
 mso-diagonal-up:none;
 mso-protection:locked visible;
 }
.x170
 {
 mso-style-parent:style0;
 mso-number-format:"\@";
 text-align:right;
 vertical-align:middle;
 white-space:nowrap;
 background:#FFC6AA;
 mso-pattern:auto none;
 color:#0000FF;
 font-size:9pt;
 font-weight:400;
 font-style:normal;
 font-family:"宋体";
 border-top:1px solid #010000;
 border-right:none;
 border-bottom:none;
 border-left:none;
 mso-diagonal-down:none;
 mso-diagonal-up:none;
 mso-protection:locked visible;
 }
.x171
 {
 mso-style-parent:style0;
 mso-number-format:"\@";
 text-align:center;
 vertical-align:middle;
 white-space:nowrap;
 background:#FFFFFF;
 mso-pattern:auto none;
 color:#000000;
 font-size:9pt;
 font-weight:400;
 font-style:normal;
 font-family:"宋体";
 border-top:1px solid #010000;
 border-right:none;
 border-bottom:none;
 border-left:1px solid #010000;
 mso-diagonal-down:none;
 mso-diagonal-up:none;
 mso-protection:locked visible;
 }
.x172
 {
 mso-style-parent:style0;
 mso-number-format:"\@";
 text-align:center;
 vertical-align:middle;
 white-space:nowrap;
 background:#FFC6AA;
 mso-pattern:auto none;
 color:#0000FF;
 font-size:10pt;
 font-weight:400;
 font-style:normal;
 font-family:"宋体";
 border-top:2px solid #010000;
 border-right:none;
 border-bottom:none;
 border-left:none;
 mso-diagonal-down:none;
 mso-diagonal-up:none;
 mso-protection:locked visible;
 }
.x173
 {
 mso-style-parent:style0;
 mso-number-format:"\@";
 text-align:center;
 vertical-align:middle;
 white-space:nowrap;
 background:#FFFFFF;
 mso-pattern:auto none;
 color:#000000;
 font-size:10pt;
 font-weight:400;
 font-style:normal;
 font-family:"宋体";
 border-top:1px solid #010000;
 border-right:none;
 border-bottom:none;
 border-left:none;
 mso-diagonal-down:none;
 mso-diagonal-up:none;
 mso-protection:locked visible;
 }
.x174
 {
 mso-style-parent:style0;
 mso-number-format:"\@";
 text-align:distributed;
 vertical-align:middle;
 white-space:normal;word-wrap:break-word;
 background:#FFFFFF;
 mso-pattern:auto none;
 color:#000000;
 font-size:10pt;
 font-weight:400;
 font-style:normal;
 font-family:"宋体";
 border-top:1px solid #010000;
 border-right:none;
 border-bottom:none;
 border-left:none;
 mso-diagonal-down:none;
 mso-diagonal-up:none;
 mso-protection:locked visible;
 }
.x175
 {
 mso-style-parent:style0;
 mso-number-format:"\@";
 text-align:center;
 vertical-align:middle;
 white-space:nowrap;
 background:#FFFFFF;
 mso-pattern:auto none;
 color:#000000;
 font-size:10pt;
 font-weight:400;
 font-style:normal;
 font-family:"宋体";
 border-top:2px solid #010000;
 border-right:none;
 border-bottom:none;
 border-left:1px solid #010000;
 mso-diagonal-down:none;
 mso-diagonal-up:none;
 mso-protection:locked visible;
 }
.x176
 {
 mso-style-parent:style0;
 mso-number-format:"\@";
 text-align:center;
 vertical-align:middle;
 white-space:normal;word-wrap:break-word;
 background:#FFFFFF;
 mso-pattern:auto none;
 color:#000000;
 font-size:10pt;
 font-weight:400;
 font-style:normal;
 font-family:"宋体";
 border-top:none;
 border-right:none;
 border-bottom:none;
 border-left:1px solid #010000;
 mso-diagonal-down:none;
 mso-diagonal-up:none;
 mso-protection:locked visible;
 }
.x177
 {
 mso-style-parent:style0;
 mso-number-format:"\@";
 text-align:left;
 vertical-align:middle;
 white-space:nowrap;
 background:#FFFFFF;
 mso-pattern:auto none;
 color:#000000;
 font-size:10pt;
 font-weight:400;
 font-style:normal;
 font-family:"宋体";
 border-top:2px solid #010000;
 border-right:none;
 border-bottom:none;
 border-left:1px solid #010000;
 mso-diagonal-down:none;
 mso-diagonal-up:none;
 mso-protection:locked visible;
 }
.x178
 {
 mso-style-parent:style0;
 mso-number-format:"\@";
 text-align:right;
 vertical-align:middle;
 white-space:normal;word-wrap:break-word;
 background:#FFC6AA;
 mso-pattern:auto none;
 color:#0000FF;
 font-size:10pt;
 font-weight:400;
 font-style:normal;
 font-family:"宋体";
 border-top:1px solid #010000;
 border-right:none;
 border-bottom:none;
 border-left:1px solid #010000;
 mso-diagonal-down:none;
 mso-diagonal-up:none;
 mso-protection:locked visible;
 }
.x179
 {
 mso-style-parent:style0;
 mso-number-format:"\@";
 text-align:right;
 vertical-align:middle;
 white-space:normal;word-wrap:break-word;
 background:#FFC6AA;
 mso-pattern:auto none;
 color:#0000FF;
 font-size:10pt;
 font-weight:400;
 font-style:normal;
 font-family:"宋体";
 border-top:1px solid #010000;
 border-right:none;
 border-bottom:none;
 border-left:none;
 mso-diagonal-down:none;
 mso-diagonal-up:none;
 mso-protection:locked visible;
 }
.x180
 {
 mso-style-parent:style0;
 mso-number-format:"\@";
 text-align:right;
 vertical-align:middle;
 white-space:nowrap;
 background:#FFFFFF;
 mso-pattern:auto none;
 color:#0000FF;
 font-size:10pt;
 font-weight:400;
 font-style:normal;
 font-family:"宋体";
 border-top:1px solid #010000;
 border-right:none;
 border-bottom:none;
 border-left:1px solid #010000;
 mso-diagonal-down:none;
 mso-diagonal-up:none;
 mso-protection:locked visible;
 }
.x181
 {
 mso-style-parent:style0;
 mso-number-format:"\@";
 text-align:center;
 vertical-align:middle;
 white-space:normal;word-wrap:break-word;
 background:#FFFFFF;
 mso-pattern:auto none;
 color:#000000;
 font-size:10pt;
 font-weight:400;
 font-style:normal;
 font-family:"宋体";
 border-top:1px solid #010000;
 border-right:none;
 border-bottom:none;
 border-left:none;
 mso-diagonal-down:none;
 mso-diagonal-up:none;
 mso-protection:locked visible;
 }
.x182
 {
 mso-style-parent:style0;
 mso-number-format:"\@";
 text-align:distributed;
 vertical-align:middle;
 white-space:nowrap;
 background:#FFFFFF;
 mso-pattern:auto none;
 color:#000000;
 font-size:10pt;
 font-weight:400;
 font-style:normal;
 font-family:"宋体";
 border-top:2px solid #010000;
 border-right:none;
 border-bottom:none;
 border-left:1px solid #010000;
 mso-diagonal-down:none;
 mso-diagonal-up:none;
 mso-protection:locked visible;
 }
.x183
 {
 mso-style-parent:style0;
 mso-number-format:"\@";
 text-align:distributed;
 vertical-align:middle;
 white-space:nowrap;
 background:#FFFFFF;
 mso-pattern:auto none;
 color:#000000;
 font-size:10pt;
 font-weight:400;
 font-style:normal;
 font-family:"宋体";
 border-top:none;
 border-right:none;
 border-bottom:none;
 border-left:1px solid #010000;
 mso-diagonal-down:none;
 mso-diagonal-up:none;
 mso-protection:locked visible;
 }
.x184
 {
 mso-style-parent:style0;
 mso-number-format:"\@";
 text-align:right;
 vertical-align:middle;
 white-space:nowrap;
 background:#FFFFFF;
 mso-pattern:auto none;
 color:#0000FF;
 font-size:10pt;
 font-weight:400;
 font-style:normal;
 font-family:"宋体";
 border-top:1px solid #010000;
 border-right:none;
 border-bottom:none;
 border-left:none;
 mso-diagonal-down:none;
 mso-diagonal-up:none;
 mso-protection:locked visible;
 }
.x185
 {
 mso-style-parent:style0;
 mso-number-format:"\@";
 text-align:right;
 vertical-align:middle;
 white-space:nowrap;
 background:#FFFFFF;
 mso-pattern:auto none;
 color:#0000FF;
 font-size:10pt;
 font-weight:400;
 font-style:normal;
 font-family:"宋体";
 border-top:1px solid #010000;
 border-right:none;
 border-bottom:none;
 border-left:1px solid #010000;
 mso-diagonal-down:none;
 mso-diagonal-up:none;
 mso-protection:locked visible;
 }
.x186
 {
 mso-style-parent:style0;
 mso-number-format:"\@";
 text-align:left;
 vertical-align:middle;
 white-space:nowrap;
 background:#FFFFFF;
 mso-pattern:auto none;
 color:#000000;
 font-size:10pt;
 font-weight:400;
 font-style:normal;
 font-family:"宋体";
 border-top:1px solid #010000;
 border-right:none;
 border-bottom:none;
 border-left:1px solid #010000;
 mso-diagonal-down:none;
 mso-diagonal-up:none;
 mso-protection:locked visible;
 }
.x187
 {
 mso-style-parent:style0;
 mso-number-format:"\@";
 text-align:center;
 vertical-align:middle;
 white-space:nowrap;
 background:#D7D7FF;
 mso-pattern:auto none;
 color:#0000FF;
 font-size:10pt;
 font-weight:400;
 font-style:normal;
 font-family:"宋体";
 border-top:2px solid #010000;
 border-right:none;
 border-bottom:none;
 border-left:none;
 mso-diagonal-down:none;
 mso-diagonal-up:none;
 mso-protection:locked visible;
 }
.x188
 {
 mso-style-parent:style0;
 mso-number-format:"\@";
 text-align:center;
 vertical-align:middle;
 white-space:nowrap;
 background:#D7D7FF;
 mso-pattern:auto none;
 color:#0000FF;
 font-size:10pt;
 font-weight:400;
 font-style:normal;
 font-family:"宋体";
 border-top:2px solid #010000;
 border-right:none;
 border-bottom:none;
 border-left:none;
 mso-diagonal-down:none;
 mso-diagonal-up:none;
 mso-protection:locked visible;
 }
.x189
 {
 mso-style-parent:style0;
 mso-number-format:"\@";
 text-align:distributed;
 vertical-align:middle;
 white-space:normal;word-wrap:break-word;
 background:#FFFFFF;
 mso-pattern:auto none;
 color:#000000;
 font-size:10pt;
 font-weight:400;
 font-style:normal;
 font-family:"宋体";
 border-top:1px solid #010000;
 border-right:none;
 border-bottom:none;
 border-left:none;
 mso-diagonal-down:none;
 mso-diagonal-up:none;
 mso-protection:locked visible;
 }
.x190
 {
 mso-style-parent:style0;
 mso-number-format:"\@";
 text-align:distributed;
 vertical-align:middle;
 white-space:normal;word-wrap:break-word;
 background:#FFFFFF;
 mso-pattern:auto none;
 color:#000000;
 font-size:10pt;
 font-weight:400;
 font-style:normal;
 font-family:"宋体";
 border-top:1px solid #010000;
 border-right:none;
 border-bottom:none;
 border-left:none;
 mso-diagonal-down:none;
 mso-diagonal-up:none;
 mso-protection:locked visible;
 }
.x191
 {
 mso-style-parent:style0;
 mso-number-format:"\@";
 text-align:distributed;
 vertical-align:middle;
 white-space:normal;word-wrap:break-word;
 background:#FFFFFF;
 mso-pattern:auto none;
 color:#000000;
 font-size:10pt;
 font-weight:400;
 font-style:normal;
 font-family:"宋体";
 border-top:1px solid #010000;
 border-right:none;
 border-bottom:none;
 border-left:none;
 mso-diagonal-down:none;
 mso-diagonal-up:none;
 mso-protection:locked visible;
 }
.x192
 {
 mso-style-parent:style0;
 mso-number-format:"\@";
 text-align:distributed;
 vertical-align:middle;
 white-space:normal;word-wrap:break-word;
 background:#FFFFFF;
 mso-pattern:auto none;
 color:#000000;
 font-size:10pt;
 font-weight:400;
 font-style:normal;
 font-family:"宋体";
 border-top:1px solid #010000;
 border-right:none;
 border-bottom:none;
 border-left:none;
 mso-diagonal-down:none;
 mso-diagonal-up:none;
 mso-protection:locked visible;
 }
.x193
 {
 mso-style-parent:style0;
 mso-number-format:"\@";
 text-align:distributed;
 vertical-align:middle;
 white-space:normal;word-wrap:break-word;
 background:#FFFFFF;
 mso-pattern:auto none;
 color:#000000;
 font-size:10pt;
 font-weight:400;
 font-style:normal;
 font-family:"宋体";
 border-top:1px solid #010000;
 border-right:none;
 border-bottom:none;
 border-left:none;
 mso-diagonal-down:none;
 mso-diagonal-up:none;
 mso-protection:locked visible;
 }
.x194
 {
 mso-style-parent:style0;
 mso-number-format:"\@";
 text-align:distributed;
 vertical-align:middle;
 white-space:normal;word-wrap:break-word;
 background:#FFFFFF;
 mso-pattern:auto none;
 color:#000000;
 font-size:10pt;
 font-weight:400;
 font-style:normal;
 font-family:"宋体";
 border-top:1px solid #010000;
 border-right:none;
 border-bottom:none;
 border-left:none;
 mso-diagonal-down:none;
 mso-diagonal-up:none;
 mso-protection:locked visible;
 }
.x195
 {
 mso-style-parent:style0;
 mso-number-format:"\@";
 text-align:center;
 vertical-align:middle;
 white-space:nowrap;
 background:#FFFFFF;
 mso-pattern:auto none;
 color:#000000;
 font-size:10pt;
 font-weight:700;
 font-style:normal;
 font-family:"宋体";
 border-top:1px solid #010000;
 border-right:none;
 border-bottom:none;
 border-left:1px solid #010000;
 mso-diagonal-down:none;
 mso-diagonal-up:none;
 mso-protection:locked visible;
 }
.x196
 {
 mso-style-parent:style0;
 mso-number-format:"\@";
 text-align:right;
 vertical-align:middle;
 white-space:nowrap;
 background:#FFFFFF;
 mso-pattern:auto none;
 color:#0000FF;
 font-size:10pt;
 font-weight:400;
 font-style:normal;
 font-family:"宋体";
 border-top:1px solid #010000;
 border-right:none;
 border-bottom:none;
 border-left:1px solid #010000;
 mso-diagonal-down:none;
 mso-diagonal-up:none;
 mso-protection:locked visible;
 }
.x197
 {
 mso-style-parent:style0;
 mso-number-format:"\@";
 text-align:right;
 vertical-align:middle;
 white-space:nowrap;
 background:#FFFFFF;
 mso-pattern:auto none;
 color:#0000FF;
 font-size:10pt;
 font-weight:400;
 font-style:normal;
 font-family:"宋体";
 border-top:1px solid #010000;
 border-right:none;
 border-bottom:none;
 border-left:1px solid #010000;
 mso-diagonal-down:none;
 mso-diagonal-up:none;
 mso-protection:locked visible;
 }
.x198
 {
 mso-style-parent:style0;
 mso-number-format:"\@";
 text-align:distributed;
 vertical-align:middle;
 white-space:normal;word-wrap:break-word;
 background:#FFFFFF;
 mso-pattern:auto none;
 color:#000000;
 font-size:10pt;
 font-weight:400;
 font-style:normal;
 font-family:"宋体";
 border:none;
 mso-protection:locked visible;
 }
.x199
 {
 mso-style-parent:style0;
 mso-number-format:"\@";
 text-align:center;
 vertical-align:middle;
 white-space:nowrap;
 background:#FFFFFF;
 mso-pattern:auto none;
 color:#000000;
 font-size:10pt;
 font-weight:700;
 font-style:normal;
 font-family:"宋体";
 border-top:none;
 border-right:none;
 border-bottom:none;
 border-left:1px solid #010000;
 mso-diagonal-down:none;
 mso-diagonal-up:none;
 mso-protection:locked visible;
 }
.x200
 {
 mso-style-parent:style0;
 mso-number-format:"\@";
 text-align:right;
 vertical-align:middle;
 white-space:nowrap;
 background:#FFFFFF;
 mso-pattern:auto none;
 color:#0000FF;
 font-size:10pt;
 font-weight:400;
 font-style:normal;
 font-family:"宋体";
 border-top:none;
 border-right:none;
 border-bottom:none;
 border-left:1px solid #010000;
 mso-diagonal-down:none;
 mso-diagonal-up:none;
 mso-protection:locked visible;
 }
.x201
 {
 mso-style-parent:style0;
 mso-number-format:"\@";
 text-align:right;
 vertical-align:middle;
 white-space:nowrap;
 background:#FFFFFF;
 mso-pattern:auto none;
 color:#0000FF;
 font-size:10pt;
 font-weight:400;
 font-style:normal;
 font-family:"宋体";
 border-top:none;
 border-right:none;
 border-bottom:none;
 border-left:1px solid #010000;
 mso-diagonal-down:none;
 mso-diagonal-up:none;
 mso-protection:locked visible;
 }
.x202
 {
 mso-style-parent:style0;
 mso-number-format:"\@";
 text-align:right;
 vertical-align:middle;
 white-space:nowrap;
 background:#FFFFFF;
 mso-pattern:auto none;
 color:#0000FF;
 font-size:10pt;
 font-weight:400;
 font-style:normal;
 font-family:"宋体";
 border-top:1px solid #010000;
 border-right:none;
 border-bottom:none;
 border-left:none;
 mso-diagonal-down:none;
 mso-diagonal-up:none;
 mso-protection:locked visible;
 }
.x203
 {
 mso-style-parent:style0;
 mso-number-format:"\@";
 text-align:right;
 vertical-align:middle;
 white-space:nowrap;
 background:#FFFFFF;
 mso-pattern:auto none;
 color:#0000FF;
 font-size:10pt;
 font-weight:400;
 font-style:normal;
 font-family:"宋体";
 border:none;
 mso-protection:locked visible;
 }
.x204
 {
 mso-style-parent:style0;
 mso-number-format:"\@";
 text-align:distributed;
 vertical-align:middle;
 white-space:nowrap;
 background:#FFFFFF;
 mso-pattern:auto none;
 color:#000000;
 font-size:10pt;
 font-weight:400;
 font-style:normal;
 font-family:"宋体";
 border-top:2px solid #010000;
 border-right:none;
 border-bottom:none;
 border-left:1px solid #010000;
 mso-diagonal-down:none;
 mso-diagonal-up:none;
 mso-protection:locked visible;
 }
.x205
 {
 mso-style-parent:style0;
 mso-number-format:"\@";
 text-align:distributed;
 vertical-align:middle;
 white-space:normal;word-wrap:break-word;
 background:#FFFFFF;
 mso-pattern:auto none;
 color:#000000;
 font-size:10pt;
 font-weight:400;
 font-style:normal;
 font-family:"宋体";
 border-top:2px solid #010000;
 border-right:none;
 border-bottom:none;
 border-left:none;
 mso-diagonal-down:none;
 mso-diagonal-up:none;
 mso-protection:locked visible;
 }
.x206
 {
 mso-style-parent:style0;
 mso-number-format:"\@";
 text-align:distributed;
 vertical-align:middle;
 white-space:nowrap;
 background:#FFFFFF;
 mso-pattern:auto none;
 color:#000000;
 font-size:10pt;
 font-weight:400;
 font-style:normal;
 font-family:"宋体";
 border-top:none;
 border-right:none;
 border-bottom:none;
 border-left:1px solid #010000;
 mso-diagonal-down:none;
 mso-diagonal-up:none;
 mso-protection:locked visible;
 }
.x207
 {
 mso-style-parent:style0;
 mso-number-format:"\@";
 text-align:right;
 vertical-align:middle;
 white-space:nowrap;
 background:#D7D7FF;
 mso-pattern:auto none;
 color:#0000FF;
 font-size:10pt;
 font-weight:400;
 font-style:normal;
 font-family:"宋体";
 border-top:1px solid #010000;
 border-right:none;
 border-bottom:none;
 border-left:none;
 mso-diagonal-down:none;
 mso-diagonal-up:none;
 mso-protection:locked visible;
 }
.x208
 {
 mso-style-parent:style0;
 mso-number-format:"\@";
 text-align:right;
 vertical-align:middle;
 white-space:nowrap;
 background:#FFC6AA;
 mso-pattern:auto none;
 color:#0000FF;
 font-size:10pt;
 font-weight:400;
 font-style:normal;
 font-family:"宋体";
 border-top:1px solid #010000;
 border-right:none;
 border-bottom:none;
 border-left:none;
 mso-diagonal-down:none;
 mso-diagonal-up:none;
 mso-protection:locked visible;
 }
.x209
 {
 mso-style-parent:style0;
 mso-number-format:"\@";
 text-align:distributed;
 vertical-align:middle;
 white-space:normal;word-wrap:break-word;
 background:#FFFFFF;
 mso-pattern:auto none;
 color:#000000;
 font-size:10pt;
 font-weight:400;
 font-style:normal;
 font-family:"宋体";
 border-top:2px solid #010000;
 border-right:none;
 border-bottom:none;
 border-left:none;
 mso-diagonal-down:none;
 mso-diagonal-up:none;
 mso-protection:locked visible;
 }
.x210
 {
 mso-style-parent:style0;
 mso-number-format:"\@";
 text-align:distributed;
 vertical-align:middle;
 white-space:nowrap;
 background:#FFFFFF;
 mso-pattern:auto none;
 color:#000000;
 font-size:10pt;
 font-weight:400;
 font-style:normal;
 font-family:"宋体";
 border-top:2px solid #010000;
 border-right:none;
 border-bottom:none;
 border-left:none;
 mso-diagonal-down:none;
 mso-diagonal-up:none;
 mso-protection:locked visible;
 }
.x211
 {
 mso-style-parent:style0;
 mso-number-format:"\@";
 text-align:distributed;
 vertical-align:middle;
 white-space:normal;word-wrap:break-word;
 background:#FFFFFF;
 mso-pattern:auto none;
 color:#000000;
 font-size:10pt;
 font-weight:400;
 font-style:normal;
 font-family:"宋体";
 border-top:2px solid #010000;
 border-right:none;
 border-bottom:none;
 border-left:none;
 mso-diagonal-down:none;
 mso-diagonal-up:none;
 mso-protection:locked visible;
 }
.x212
 {
 mso-style-parent:style0;
 mso-number-format:"\@";
 text-align:center;
 vertical-align:middle;
 white-space:normal;word-wrap:break-word;
 background:#FFFFFF;
 mso-pattern:auto none;
 color:#000000;
 font-size:10pt;
 font-weight:400;
 font-style:normal;
 font-family:"宋体";
 border-top:2px solid #010000;
 border-right:none;
 border-bottom:none;
 border-left:none;
 mso-diagonal-down:none;
 mso-diagonal-up:none;
 mso-protection:locked visible;
 }
.x213
 {
 mso-style-parent:style0;
 mso-number-format:"\@";
 text-align:center;
 vertical-align:middle;
 white-space:nowrap;
 background:#FFFFFF;
 mso-pattern:auto none;
 color:#0000FF;
 font-size:10pt;
 font-weight:400;
 font-style:normal;
 font-family:"宋体";
 border-top:2px solid #010000;
 border-right:none;
 border-bottom:none;
 border-left:none;
 mso-diagonal-down:none;
 mso-diagonal-up:none;
 mso-protection:locked visible;
 }
.x214
 {
 mso-style-parent:style0;
 mso-number-format:"\@";
 text-align:center;
 vertical-align:middle;
 white-space:nowrap;
 background:#FFFFFF;
 mso-pattern:auto none;
 color:#0000FF;
 font-size:10pt;
 font-weight:400;
 font-style:normal;
 font-family:"宋体";
 border-top:2px solid #010000;
 border-right:none;
 border-bottom:none;
 border-left:none;
 mso-diagonal-down:none;
 mso-diagonal-up:none;
 mso-protection:locked visible;
 }
.x215
 {
 mso-style-parent:style0;
 mso-number-format:"\@";
 text-align:center;
 vertical-align:middle;
 white-space:nowrap;
 background:#FFFFFF;
 mso-pattern:auto none;
 color:#0000FF;
 font-size:10pt;
 font-weight:400;
 font-style:normal;
 font-family:"宋体";
 border:none;
 mso-protection:locked visible;
 }
.x216
 {
 mso-style-parent:style0;
 mso-number-format:"\@";
 text-align:right;
 vertical-align:middle;
 white-space:nowrap;
 background:#FFFFFF;
 mso-pattern:auto none;
 color:#0000FF;
 font-size:10pt;
 font-weight:400;
 font-style:normal;
 font-family:"宋体";
 border-top:1px solid #010000;
 border-right:none;
 border-bottom:none;
 border-left:none;
 mso-diagonal-down:none;
 mso-diagonal-up:none;
 mso-protection:locked visible;
 }
.x217
 {
 mso-style-parent:style0;
 mso-number-format:"\@";
 text-align:center;
 vertical-align:middle;
 white-space:nowrap;
 background:#D7D7FF;
 mso-pattern:auto none;
 color:#0000FF;
 font-size:10pt;
 font-weight:400;
 font-style:normal;
 font-family:"宋体";
 border:none;
 mso-protection:locked visible;
 }
.x218
 {
 mso-style-parent:style0;
 mso-number-format:"\@";
 text-align:center;
 vertical-align:middle;
 white-space:nowrap;
 background:#FFFFFF;
 mso-pattern:auto none;
 color:#000000;
 font-size:10pt;
 font-weight:400;
 font-style:normal;
 font-family:"宋体";
 border-top:1px solid #010000;
 border-right:none;
 border-bottom:none;
 border-left:1px solid #010000;
 mso-diagonal-down:none;
 mso-diagonal-up:none;
 mso-protection:locked visible;
 }
.x219
 {
 mso-style-parent:style0;
 mso-number-format:"\@";
 text-align:center;
 vertical-align:middle;
 white-space:nowrap;
 background:#FFFFFF;
 mso-pattern:auto none;
 color:#000000;
 font-size:10pt;
 font-weight:400;
 font-style:normal;
 font-family:"宋体";
 border-top:none;
 border-right:none;
 border-bottom:none;
 border-left:1px solid #010000;
 mso-diagonal-down:none;
 mso-diagonal-up:none;
 mso-protection:locked visible;
 }
.x220
 {
 mso-style-parent:style0;
 mso-number-format:"\@";
 text-align:right;
 vertical-align:middle;
 white-space:nowrap;
 background:#FFFFFF;
 mso-pattern:auto none;
 color:#000000;
 font-size:10pt;
 font-weight:400;
 font-style:normal;
 font-family:"宋体";
 border-top:none;
 border-right:none;
 border-bottom:none;
 border-left:1px solid #010000;
 mso-diagonal-down:none;
 mso-diagonal-up:none;
 mso-protection:locked visible;
 }
.x221
 {
 mso-style-parent:style0;
 mso-number-format:"\@";
 text-align:right;
 vertical-align:middle;
 white-space:nowrap;
 background:#D7D7FF;
 mso-pattern:auto none;
 color:#0000C0;
 font-size:10pt;
 font-weight:400;
 font-style:normal;
 font-family:"宋体";
 border-top:1px solid #010000;
 border-right:none;
 border-bottom:none;
 border-left:1px solid #010000;
 mso-diagonal-down:none;
 mso-diagonal-up:none;
 mso-protection:locked visible;
 }
.x222
 {
 mso-style-parent:style0;
 mso-number-format:"\@";
 text-align:right;
 vertical-align:middle;
 white-space:nowrap;
 background:#D7D7FF;
 mso-pattern:auto none;
 color:#0000C0;
 font-size:10pt;
 font-weight:400;
 font-style:normal;
 font-family:"宋体";
 border-top:1px solid #010000;
 border-right:none;
 border-bottom:none;
 border-left:none;
 mso-diagonal-down:none;
 mso-diagonal-up:none;
 mso-protection:locked visible;
 }
.x223
 {
 mso-style-parent:style0;
 mso-number-format:"\@";
 text-align:right;
 vertical-align:middle;
 white-space:nowrap;
 background:#FFFFFF;
 mso-pattern:auto none;
 color:#0000C0;
 font-size:10pt;
 font-weight:400;
 font-style:normal;
 font-family:"宋体";
 border-top:1px solid #010000;
 border-right:none;
 border-bottom:none;
 border-left:1px solid #010000;
 mso-diagonal-down:none;
 mso-diagonal-up:none;
 mso-protection:locked visible;
 }
.x224
 {
 mso-style-parent:style0;
 mso-number-format:"\@";
 text-align:center;
 vertical-align:middle;
 white-space:nowrap;
 background:#FFFFFF;
 mso-pattern:auto none;
 color:#0000FF;
 font-size:10pt;
 font-weight:400;
 font-style:normal;
 font-family:"宋体";
 border-top:2px solid #010000;
 border-right:none;
 border-bottom:none;
 border-left:none;
 mso-diagonal-down:none;
 mso-diagonal-up:none;
 mso-protection:locked visible;
 }
.x225
 {
 mso-style-parent:style0;
 mso-number-format:"\@";
 text-align:right;
 vertical-align:middle;
 white-space:nowrap;
 background:#D7D7FF;
 mso-pattern:auto none;
 color:#0000C0;
 font-size:10pt;
 font-weight:400;
 font-style:normal;
 font-family:"宋体";
 border-top:1px solid #010000;
 border-right:none;
 border-bottom:none;
 border-left:1px solid #010000;
 mso-diagonal-down:none;
 mso-diagonal-up:none;
 mso-protection:locked visible;
 }
.x226
 {
 mso-style-parent:style0;
 mso-number-format:"\@";
 text-align:right;
 vertical-align:middle;
 white-space:nowrap;
 background:#FFFFFF;
 mso-pattern:auto none;
 color:#0000C0;
 font-size:10pt;
 font-weight:400;
 font-style:normal;
 font-family:"宋体";
 border-top:1px solid #010000;
 border-right:none;
 border-bottom:none;
 border-left:none;
 mso-diagonal-down:none;
 mso-diagonal-up:none;
 mso-protection:locked visible;
 }
.x227
 {
 mso-style-parent:style0;
 mso-number-format:"\@";
 text-align:right;
 vertical-align:middle;
 white-space:nowrap;
 background:#FFFFFF;
 mso-pattern:auto none;
 color:#0000C0;
 font-size:10pt;
 font-weight:400;
 font-style:normal;
 font-family:"宋体";
 border-top:1px solid #010000;
 border-right:none;
 border-bottom:none;
 border-left:1px solid #010000;
 mso-diagonal-down:none;
 mso-diagonal-up:none;
 mso-protection:locked visible;
 }
.x228
 {
 mso-style-parent:style0;
 mso-number-format:"\@";
 text-align:distributed;
 vertical-align:middle;
 white-space:nowrap;
 background:#FFFFFF;
 mso-pattern:auto none;
 color:#000000;
 font-size:10pt;
 font-weight:400;
 font-style:normal;
 font-family:"宋体";
 border-top:1px solid #010000;
 border-right:none;
 border-bottom:none;
 border-left:1px solid #010000;
 mso-diagonal-down:none;
 mso-diagonal-up:none;
 mso-protection:locked visible;
 }
.x229
 {
 mso-style-parent:style0;
 mso-number-format:"\@";
 text-align:distributed;
 vertical-align:middle;
 white-space:nowrap;
 background:#FFFFFF;
 mso-pattern:auto none;
 color:#000000;
 font-size:10pt;
 font-weight:400;
 font-style:normal;
 font-family:"宋体";
 border-top:none;
 border-right:none;
 border-bottom:none;
 border-left:1px solid #010000;
 mso-diagonal-down:none;
 mso-diagonal-up:none;
 mso-protection:locked visible;
 }
.x230
 {
 mso-style-parent:style0;
 mso-number-format:"\@";
 text-align:distributed;
 vertical-align:middle;
 white-space:nowrap;
 background:#FFFFFF;
 mso-pattern:auto none;
 color:#000000;
 font-size:10pt;
 font-weight:400;
 font-style:normal;
 font-family:"宋体";
 border-top:none;
 border-right:none;
 border-bottom:none;
 border-left:1px solid #010000;
 mso-diagonal-down:none;
 mso-diagonal-up:none;
 mso-protection:locked visible;
 }
.x231
 {
 mso-style-parent:style0;
 mso-number-format:"\@";
 text-align:distributed;
 vertical-align:middle;
 white-space:nowrap;
 background:#FFFFFF;
 mso-pattern:auto none;
 color:#000000;
 font-size:10pt;
 font-weight:400;
 font-style:normal;
 font-family:"宋体";
 border-top:2px solid #010000;
 border-right:none;
 border-bottom:none;
 border-left:none;
 mso-diagonal-down:none;
 mso-diagonal-up:none;
 mso-protection:locked visible;
 }
.x232
 {
 mso-style-parent:style0;
 mso-number-format:"\@";
 text-align:distributed;
 vertical-align:middle;
 white-space:nowrap;
 background:#FFFFFF;
 mso-pattern:auto none;
 color:#000000;
 font-size:10pt;
 font-weight:400;
 font-style:normal;
 font-family:"宋体";
 border-top:2px solid #010000;
 border-right:none;
 border-bottom:none;
 border-left:none;
 mso-diagonal-down:none;
 mso-diagonal-up:none;
 mso-protection:locked visible;
 }
.x233
 {
 mso-style-parent:style0;
 mso-number-format:"\@";
 text-align:distributed;
 vertical-align:middle;
 white-space:nowrap;
 background:#FFFFFF;
 mso-pattern:auto none;
 color:#000000;
 font-size:10pt;
 font-weight:400;
 font-style:normal;
 font-family:"宋体";
 border-top:2px solid #010000;
 border-right:none;
 border-bottom:none;
 border-left:none;
 mso-diagonal-down:none;
 mso-diagonal-up:none;
 mso-protection:locked visible;
 }
.x234
 {
 mso-style-parent:style0;
 mso-number-format:"\@";
 text-align:right;
 vertical-align:middle;
 white-space:nowrap;
 background:#BFF4EB;
 mso-pattern:auto none;
 color:#0000FF;
 font-size:10pt;
 font-weight:400;
 font-style:normal;
 font-family:"宋体";
 border-top:1px solid #010000;
 border-right:none;
 border-bottom:none;
 border-left:1px solid #010000;
 mso-diagonal-down:none;
 mso-diagonal-up:none;
 mso-protection:locked visible;
 }
.x235
 {
 mso-style-parent:style0;
 mso-number-format:"\@";
 text-align:right;
 vertical-align:middle;
 white-space:nowrap;
 background:#BFF4EB;
 mso-pattern:auto none;
 color:#0000FF;
 font-size:10pt;
 font-weight:400;
 font-style:normal;
 font-family:"宋体";
 border-top:1px solid #010000;
 border-right:none;
 border-bottom:none;
 border-left:none;
 mso-diagonal-down:none;
 mso-diagonal-up:none;
 mso-protection:locked visible;
 }
.x236
 {
 mso-style-parent:style0;
 mso-number-format:"\@";
 text-align:center;
 vertical-align:middle;
 white-space:normal;word-wrap:break-word;
 background:#FFFFFF;
 mso-pattern:auto none;
 color:#000000;
 font-size:10pt;
 font-weight:400;
 font-style:normal;
 font-family:"宋体";
 border-top:1px solid #010000;
 border-right:none;
 border-bottom:none;
 border-left:none;
 mso-diagonal-down:none;
 mso-diagonal-up:none;
 mso-protection:locked visible;
 }
.x237
 {
 mso-style-parent:style0;
 mso-number-format:"\@";
 text-align:center;
 vertical-align:middle;
 white-space:normal;word-wrap:break-word;
 background:#FFFFFF;
 mso-pattern:auto none;
 color:#000000;
 font-size:10pt;
 font-weight:400;
 font-style:normal;
 font-family:"宋体";
 border-top:2px solid #010000;
 border-right:none;
 border-bottom:none;
 border-left:1px solid #010000;
 mso-diagonal-down:none;
 mso-diagonal-up:none;
 mso-protection:locked visible;
 }
.x238
 {
 mso-style-parent:style0;
 mso-number-format:"\@";
 text-align:distributed;
 vertical-align:middle;
 white-space:nowrap;
 background:#FFFFFF;
 mso-pattern:auto none;
 color:#000000;
 font-size:10pt;
 font-weight:400;
 font-style:normal;
 font-family:"宋体";
 border-top:none;
 border-right:none;
 border-bottom:none;
 border-left:1px solid #010000;
 mso-diagonal-down:none;
 mso-diagonal-up:none;
 mso-protection:locked visible;
 }
.x239
 {
 mso-style-parent:style0;
 mso-number-format:"\@";
 text-align:center;
 vertical-align:middle;
 white-space:nowrap;
 background:#FFFFFF;
 mso-pattern:auto none;
 color:#000000;
 font-size:10pt;
 font-weight:400;
 font-style:normal;
 font-family:"宋体";
 border-top:none;
 border-right:none;
 border-bottom:none;
 border-left:1px solid #010000;
 mso-diagonal-down:none;
 mso-diagonal-up:none;
 mso-protection:locked visible;
 }
.x240
 {
 mso-style-parent:style0;
 mso-number-format:"\@";
 text-align:distributed;
 vertical-align:middle;
 white-space:nowrap;
 background:#FFFFFF;
 mso-pattern:auto none;
 color:#000000;
 font-size:10pt;
 font-weight:400;
 font-style:normal;
 font-family:"宋体";
 border-top:2px solid #010000;
 border-right:none;
 border-bottom:none;
 border-left:1px solid #010000;
 mso-diagonal-down:none;
 mso-diagonal-up:none;
 mso-protection:locked visible;
 }
.x241
 {
 mso-style-parent:style0;
 mso-number-format:"\@";
 text-align:right;
 vertical-align:middle;
 white-space:nowrap;
 background:#FFFFFF;
 mso-pattern:auto none;
 color:#0000FF;
 font-size:9pt;
 font-weight:400;
 font-style:normal;
 font-family:"宋体";
 border-top:1px solid #010000;
 border-right:none;
 border-bottom:none;
 border-left:1px solid #010000;
 mso-diagonal-down:none;
 mso-diagonal-up:none;
 mso-protection:locked visible;
 }
.x242
 {
 mso-style-parent:style0;
 mso-number-format:"\@";
 text-align:center;
 vertical-align:middle;
 white-space:nowrap;
 background:#FFFFFF;
 mso-pattern:auto none;
 color:#0000FF;
 font-size:10pt;
 font-weight:400;
 font-style:normal;
 font-family:"宋体";
 border-top:2px solid #010000;
 border-right:none;
 border-bottom:none;
 border-left:none;
 mso-diagonal-down:none;
 mso-diagonal-up:none;
 mso-protection:locked visible;
 }
.x243
 {
 mso-style-parent:style0;
 mso-number-format:"\@";
 text-align:center;
 vertical-align:middle;
 white-space:nowrap;
 background:#FFFFFF;
 mso-pattern:auto none;
 color:#000000;
 font-size:10pt;
 font-weight:400;
 font-style:normal;
 font-family:"宋体";
 border-top:2px solid #010000;
 border-right:none;
 border-bottom:none;
 border-left:1px solid #010000;
 mso-diagonal-down:none;
 mso-diagonal-up:none;
 mso-protection:locked visible;
 }
.x244
 {
 mso-style-parent:style0;
 mso-number-format:"\@";
 text-align:left;
 vertical-align:middle;
 white-space:nowrap;
 background:#FFFFFF;
 mso-pattern:auto none;
 color:#000000;
 font-size:10pt;
 font-weight:400;
 font-style:normal;
 font-family:"宋体";
 border-top:none;
 border-right:none;
 border-bottom:none;
 border-left:1px solid #010000;
 mso-diagonal-down:none;
 mso-diagonal-up:none;
 mso-protection:locked visible;
 }
.x245
 {
 mso-style-parent:style0;
 mso-number-format:"\@";
 text-align:left;
 vertical-align:middle;
 white-space:nowrap;
 background:#FFFFFF;
 mso-pattern:auto none;
 color:#000000;
 font-size:10pt;
 font-weight:400;
 font-style:normal;
 font-family:"宋体";
 border-top:none;
 border-right:none;
 border-bottom:none;
 border-left:1px solid #010000;
 mso-diagonal-down:none;
 mso-diagonal-up:none;
 mso-protection:locked visible;
 }
.x246
 {
 mso-style-parent:style0;
 mso-number-format:"\@";
 text-align:left;
 vertical-align:middle;
 white-space:nowrap;
 background:#FFFFFF;
 mso-pattern:auto none;
 color:#000000;
 font-size:10pt;
 font-weight:400;
 font-style:normal;
 font-family:"宋体";
 border-top:none;
 border-right:none;
 border-bottom:none;
 border-left:1px solid #010000;
 mso-diagonal-down:none;
 mso-diagonal-up:none;
 mso-protection:locked visible;
 }
.x247
 {
 mso-style-parent:style0;
 mso-number-format:"\@";
 text-align:center;
 vertical-align:middle;
 white-space:nowrap;
 background:#FFFFFF;
 mso-pattern:auto none;
 color:#000000;
 font-size:9pt;
 font-weight:400;
 font-style:normal;
 font-family:"宋体";
 border:none;
 mso-protection:locked visible;
 }
.x248
 {
 mso-style-parent:style0;
 mso-number-format:"\@";
 text-align:center;
 vertical-align:middle;
 white-space:nowrap;
 background:#FFFFFF;
 mso-pattern:auto none;
 color:#000000;
 font-size:9pt;
 font-weight:400;
 font-style:normal;
 font-family:"宋体";
 border-top:2px solid #010000;
 border-right:none;
 border-bottom:none;
 border-left:none;
 mso-diagonal-down:none;
 mso-diagonal-up:none;
 mso-protection:locked visible;
 }
.x249
 {
 mso-style-parent:style0;
 mso-number-format:"\@";
 text-align:left;
 vertical-align:middle;
 white-space:nowrap;
 background:#FFFFFF;
 mso-pattern:auto none;
 color:#000000;
 font-size:9pt;
 font-weight:400;
 font-style:normal;
 font-family:"宋体";
 border-top:2px solid #010000;
 border-right:none;
 border-bottom:none;
 border-left:none;
 mso-diagonal-down:none;
 mso-diagonal-up:none;
 mso-protection:locked visible;
 }
.x250
 {
 mso-style-parent:style0;
 mso-number-format:"\@";
 text-align:center;
 vertical-align:middle;
 white-space:nowrap;
 background:#FFFFFF;
 mso-pattern:auto none;
 color:#000000;
 font-size:9pt;
 font-weight:400;
 font-style:normal;
 font-family:"宋体";
 border-top:1px solid #010000;
 border-right:none;
 border-bottom:none;
 border-left:none;
 mso-diagonal-down:none;
 mso-diagonal-up:none;
 mso-protection:locked visible;
 }
.x251
 {
 mso-style-parent:style0;
 mso-number-format:"\@";
 text-align:left;
 vertical-align:middle;
 white-space:nowrap;
 background:#FFFFFF;
 mso-pattern:auto none;
 color:#000000;
 font-size:9pt;
 font-weight:400;
 font-style:normal;
 font-family:"宋体";
 border-top:1px solid #010000;
 border-right:none;
 border-bottom:none;
 border-left:none;
 mso-diagonal-down:none;
 mso-diagonal-up:none;
 mso-protection:locked visible;
 }
.x252
 {
 mso-style-parent:style0;
 mso-number-format:"\@";
 text-align:left;
 vertical-align:middle;
 white-space:nowrap;
 background:#FFFFFF;
 mso-pattern:auto none;
 color:#000000;
 font-size:9pt;
 font-weight:400;
 font-style:normal;
 font-family:"宋体";
 border-top:1px solid #010000;
 border-right:none;
 border-bottom:none;
 border-left:1px solid #010000;
 mso-diagonal-down:none;
 mso-diagonal-up:none;
 mso-protection:locked visible;
 }
.x253
 {
 mso-style-parent:style0;
 mso-number-format:"\@";
 text-align:left;
 vertical-align:middle;
 white-space:nowrap;
 background:#FFFFFF;
 mso-pattern:auto none;
 color:#000000;
 font-size:9pt;
 font-weight:400;
 font-style:normal;
 font-family:"宋体";
 border-top:none;
 border-right:none;
 border-bottom:none;
 border-left:1px solid #010000;
 mso-diagonal-down:none;
 mso-diagonal-up:none;
 mso-protection:locked visible;
 }
.x254
 {
 mso-style-parent:style0;
 mso-number-format:"\@";
 text-align:distributed;
 vertical-align:middle;
 white-space:nowrap;
 background:#FFFFFF;
 mso-pattern:auto none;
 color:#000000;
 font-size:9pt;
 font-weight:400;
 font-style:normal;
 font-family:"宋体";
 border:none;
 mso-protection:locked visible;
 }
.x255
 {
 mso-style-parent:style0;
 mso-number-format:"\@";
 text-align:distributed;
 vertical-align:middle;
 white-space:nowrap;
 background:#FFFFFF;
 mso-pattern:auto none;
 color:#000000;
 font-size:9pt;
 font-weight:400;
 font-style:normal;
 font-family:"宋体";
 border-top:1px solid #010000;
 border-right:none;
 border-bottom:none;
 border-left:1px solid #010000;
 mso-diagonal-down:none;
 mso-diagonal-up:none;
 mso-protection:locked visible;
 }
.x256
 {
 mso-style-parent:style0;
 mso-number-format:"\@";
 text-align:distributed;
 vertical-align:middle;
 white-space:nowrap;
 background:#FFFFFF;
 mso-pattern:auto none;
 color:#000000;
 font-size:9pt;
 font-weight:400;
 font-style:normal;
 font-family:"宋体";
 border-top:none;
 border-right:none;
 border-bottom:none;
 border-left:1px solid #010000;
 mso-diagonal-down:none;
 mso-diagonal-up:none;
 mso-protection:locked visible;
 }
.x257
 {
 mso-style-parent:style0;
 mso-number-format:"\@";
 text-align:left;
 vertical-align:middle;
 white-space:nowrap;
 background:#FFFFFF;
 mso-pattern:auto none;
 color:#000000;
 font-size:11pt;
 font-weight:400;
 font-style:normal;
 font-family:"宋体";
 border:none;
 mso-protection:locked visible;
 }
.x258
 {
 mso-style-parent:style0;
 mso-number-format:"\@";
 text-align:center;
 vertical-align:middle;
 white-space:nowrap;
 background:#FFFFFF;
 mso-pattern:auto none;
 color:#000000;
 font-size:11pt;
 font-weight:400;
 font-style:normal;
 font-family:"宋体";
 border:none;
 mso-protection:locked visible;
 }
.x259
 {
 mso-style-parent:style0;
 mso-number-format:"\@";
 text-align:left;
 vertical-align:middle;
 white-space:nowrap;
 background:#FFFFFF;
 mso-pattern:auto none;
 color:#000000;
 font-size:11pt;
 font-weight:400;
 font-style:normal;
 font-family:"宋体";
 border-top:2px solid #010000;
 border-right:none;
 border-bottom:none;
 border-left:none;
 mso-diagonal-down:none;
 mso-diagonal-up:none;
 mso-protection:locked visible;
 }
.x260
 {
 mso-style-parent:style0;
 mso-number-format:"\@";
 text-align:left;
 vertical-align:middle;
 white-space:nowrap;
 background:#FFFFFF;
 mso-pattern:auto none;
 color:#000000;
 font-size:11pt;
 font-weight:400;
 font-style:normal;
 font-family:"宋体";
 border-top:2px solid #010000;
 border-right:none;
 border-bottom:none;
 border-left:1px solid #010000;
 mso-diagonal-down:none;
 mso-diagonal-up:none;
 mso-protection:locked visible;
 }
.x261
 {
 mso-style-parent:style0;
 mso-number-format:"\@";
 text-align:left;
 vertical-align:middle;
 white-space:nowrap;
 background:#FFFFFF;
 mso-pattern:auto none;
 color:#000000;
 font-size:11pt;
 font-weight:400;
 font-style:normal;
 font-family:"宋体";
 border-top:1px solid #010000;
 border-right:none;
 border-bottom:none;
 border-left:none;
 mso-diagonal-down:none;
 mso-diagonal-up:none;
 mso-protection:locked visible;
 }
.x262
 {
 mso-style-parent:style0;
 mso-number-format:"\@";
 text-align:left;
 vertical-align:middle;
 white-space:nowrap;
 background:#FFFFFF;
 mso-pattern:auto none;
 color:#000000;
 font-size:11pt;
 font-weight:400;
 font-style:normal;
 font-family:"宋体";
 border-top:1px solid #010000;
 border-right:none;
 border-bottom:none;
 border-left:1px solid #010000;
 mso-diagonal-down:none;
 mso-diagonal-up:none;
 mso-protection:locked visible;
 }
.x263
 {
 mso-style-parent:style0;
 mso-number-format:"\@";
 text-align:left;
 vertical-align:middle;
 white-space:nowrap;
 background:#FFFFFF;
 mso-pattern:auto none;
 color:#000000;
 font-size:11pt;
 font-weight:400;
 font-style:normal;
 font-family:"宋体";
 border-top:none;
 border-right:none;
 border-bottom:none;
 border-left:1px solid #010000;
 mso-diagonal-down:none;
 mso-diagonal-up:none;
 mso-protection:locked visible;
 }
.x264
 {
 mso-style-parent:style0;
 mso-number-format:"\@";
 text-align:distributed;
 vertical-align:middle;
 white-space:nowrap;
 background:#FFFFFF;
 mso-pattern:auto none;
 color:#000000;
 font-size:11pt;
 font-weight:400;
 font-style:normal;
 font-family:"宋体";
 border:none;
 mso-protection:locked visible;
 }
.x265
 {
 mso-style-parent:style0;
 mso-number-format:"\@";
 text-align:left;
 vertical-align:middle;
 white-space:nowrap;
 background:#FFFFFF;
 mso-pattern:auto none;
 color:#000000;
 font-size:11pt;
 font-weight:400;
 font-style:normal;
 font-family:"宋体";
 border-top:2px solid #010000;
 border-right:none;
 border-bottom:none;
 border-left:none;
 mso-diagonal-down:none;
 mso-diagonal-up:none;
 mso-protection:locked visible;
 }
.x266
 {
 mso-style-parent:style0;
 mso-number-format:"\@";
 text-align:left;
 vertical-align:middle;
 white-space:nowrap;
 background:#FFFFFF;
 mso-pattern:auto none;
 color:#000000;
 font-size:11pt;
 font-weight:400;
 font-style:normal;
 font-family:"宋体";
 border-top:2px solid #010000;
 border-right:none;
 border-bottom:none;
 border-left:none;
 mso-diagonal-down:none;
 mso-diagonal-up:none;
 mso-protection:locked visible;
 }
.x267
 {
 mso-style-parent:style0;
 mso-number-format:"\@";
 text-align:left;
 vertical-align:middle;
 white-space:nowrap;
 background:#FFFFFF;
 mso-pattern:auto none;
 color:#000000;
 font-size:11pt;
 font-weight:400;
 font-style:normal;
 font-family:"宋体";
 border-top:2px solid #010000;
 border-right:none;
 border-bottom:none;
 border-left:none;
 mso-diagonal-down:none;
 mso-diagonal-up:none;
 mso-protection:locked visible;
 }
.x268
 {
 mso-style-parent:style0;
 mso-number-format:"\@";
 text-align:right;
 vertical-align:middle;
 white-space:nowrap;
 background:#FFFFFF;
 mso-pattern:auto none;
 color:#000000;
 font-size:11pt;
 font-weight:400;
 font-style:normal;
 font-family:"宋体";
 border:none;
 mso-protection:locked visible;
 }
.x269
 {
 mso-style-parent:style0;
 mso-number-format:"\@";
 text-align:center;
 vertical-align:middle;
 white-space:nowrap;
 background:#FFFFFF;
 mso-pattern:auto none;
 color:#000000;
 font-size:11pt;
 font-weight:700;
 font-style:normal;
 font-family:"宋体";
 border-top:1px solid #010000;
 border-right:none;
 border-bottom:none;
 border-left:1px solid #010000;
 mso-diagonal-down:none;
 mso-diagonal-up:none;
 mso-protection:locked visible;
 }
.x270
 {
 mso-style-parent:style0;
 mso-number-format:"\@";
 text-align:center;
 vertical-align:middle;
 white-space:nowrap;
 background:#FFFFFF;
 mso-pattern:auto none;
 color:#000000;
 font-size:11pt;
 font-weight:700;
 font-style:normal;
 font-family:"宋体";
 border-top:1px solid #010000;
 border-right:none;
 border-bottom:none;
 border-left:none;
 mso-diagonal-down:none;
 mso-diagonal-up:none;
 mso-protection:locked visible;
 }
.x271
 {
 mso-style-parent:style0;
 mso-number-format:"\@";
 text-align:right;
 vertical-align:middle;
 white-space:nowrap;
 background:#BFF4EB;
 mso-pattern:auto none;
 color:#0000FF;
 font-size:10pt;
 font-weight:400;
 font-style:normal;
 font-family:"宋体";
 border-top:1px solid #010000;
 border-right:none;
 border-bottom:none;
 border-left:1px solid #010000;
 mso-diagonal-down:none;
 mso-diagonal-up:none;
 mso-protection:locked visible;
 }
.x272
 {
 mso-style-parent:style0;
 mso-number-format:"\@";
 text-align:left;
 vertical-align:middle;
 white-space:nowrap;
 background:#FFFFFF;
 mso-pattern:auto none;
 color:#000000;
 font-size:10pt;
 font-weight:400;
 font-style:normal;
 font-family:"宋体";
 border-top:2px solid #010000;
 border-right:none;
 border-bottom:none;
 border-left:1px solid #010000;
 mso-diagonal-down:none;
 mso-diagonal-up:none;
 mso-protection:locked visible;
 }
.x273
 {
 mso-style-parent:style0;
 mso-number-format:"\@";
 text-align:left;
 vertical-align:middle;
 white-space:nowrap;
 background:#FFFFFF;
 mso-pattern:auto none;
 color:#000000;
 font-size:10pt;
 font-weight:400;
 font-style:normal;
 font-family:"宋体";
 border-top:none;
 border-right:none;
 border-bottom:none;
 border-left:1px solid #010000;
 mso-diagonal-down:none;
 mso-diagonal-up:none;
 mso-protection:locked visible;
 }
.x274
 {
 mso-style-parent:style0;
 mso-number-format:"\@";
 text-align:left;
 vertical-align:middle;
 white-space:nowrap;
 background:#FFFFFF;
 mso-pattern:auto none;
 color:#000000;
 font-size:10pt;
 font-weight:400;
 font-style:normal;
 font-family:"宋体";
 border-top:none;
 border-right:none;
 border-bottom:none;
 border-left:1px solid #010000;
 mso-diagonal-down:none;
 mso-diagonal-up:none;
 mso-protection:locked visible;
 }
.x275
 {
 mso-style-parent:style0;
 mso-number-format:"\@";
 text-align:center;
 vertical-align:middle;
 white-space:nowrap;
 background:#FFFFFF;
 mso-pattern:auto none;
 color:#000000;
 font-size:10pt;
 font-weight:400;
 font-style:normal;
 font-family:"宋体";
 border-top:1px solid #010000;
 border-right:none;
 border-bottom:none;
 border-left:none;
 mso-diagonal-down:none;
 mso-diagonal-up:none;
 mso-protection:locked visible;
 }
.x276
 {
 mso-style-parent:style0;
 mso-number-format:"\@";
 text-align:center;
 vertical-align:middle;
 white-space:nowrap;
 background:#FFFFFF;
 mso-pattern:auto none;
 color:#000000;
 font-size:10pt;
 font-weight:400;
 font-style:normal;
 font-family:"宋体";
 border-top:1px solid #010000;
 border-right:none;
 border-bottom:none;
 border-left:none;
 mso-diagonal-down:none;
 mso-diagonal-up:none;
 mso-protection:locked visible;
 }
.x277
 {
 mso-style-parent:style0;
 mso-number-format:"\@";
 text-align:center;
 vertical-align:middle;
 white-space:nowrap;
 background:#FFFFFF;
 mso-pattern:auto none;
 color:#000000;
 font-size:15pt;
 font-weight:700;
 font-style:normal;
 font-family:"宋体";
 border:none;
 mso-protection:locked visible;
 }
.x278
 {
 mso-style-parent:style0;
 mso-number-format:"\@";
 text-align:center;
 vertical-align:middle;
 white-space:normal;word-wrap:break-word;
 background:#FFFFFF;
 mso-pattern:auto none;
 color:#0000FF;
 font-size:10pt;
 font-weight:400;
 font-style:normal;
 font-family:"宋体";
 border:none;
 mso-protection:locked visible;
 }
.x279
 {
 mso-style-parent:style0;
 mso-number-format:"\@";
 text-align:distributed;
 vertical-align:middle;
 white-space:nowrap;
 background:#FFFFFF;
 mso-pattern:auto none;
 color:#000000;
 font-size:10pt;
 font-weight:400;
 font-style:normal;
 font-family:"宋体";
 border-top:1px solid #010000;
 border-right:none;
 border-bottom:none;
 border-left:none;
 mso-diagonal-down:none;
 mso-diagonal-up:none;
 mso-protection:locked visible;
 }
.x280
 {
 mso-style-parent:style0;
 mso-number-format:"\@";
 text-align:distributed;
 vertical-align:middle;
 white-space:nowrap;
 background:#FFFFFF;
 mso-pattern:auto none;
 color:#000000;
 font-size:10pt;
 font-weight:400;
 font-style:normal;
 font-family:"宋体";
 border-top:1px solid #010000;
 border-right:none;
 border-bottom:none;
 border-left:none;
 mso-diagonal-down:none;
 mso-diagonal-up:none;
 mso-protection:locked visible;
 }
.x281
 {
 mso-style-parent:style0;
 mso-number-format:"\@";
 text-align:distributed;
 vertical-align:middle;
 white-space:nowrap;
 background:#FFFFFF;
 mso-pattern:auto none;
 color:#000000;
 font-size:10pt;
 font-weight:400;
 font-style:normal;
 font-family:"宋体";
 border-top:1px solid #010000;
 border-right:none;
 border-bottom:none;
 border-left:none;
 mso-diagonal-down:none;
 mso-diagonal-up:none;
 mso-protection:locked visible;
 }
.x282
 {
 mso-style-parent:style0;
 mso-number-format:"\@";
 text-align:right;
 vertical-align:middle;
 white-space:nowrap;
 background:#FFC6AA;
 mso-pattern:auto none;
 color:#0000FF;
 font-size:10pt;
 font-weight:400;
 font-style:normal;
 font-family:"宋体";
 border-top:1px solid #010000;
 border-right:none;
 border-bottom:none;
 border-left:1px solid #010000;
 mso-diagonal-down:none;
 mso-diagonal-up:none;
 mso-protection:locked visible;
 }
.x283
 {
 mso-style-parent:style0;
 mso-number-format:"\@";
 text-align:right;
 vertical-align:middle;
 white-space:nowrap;
 background:#FFC6AA;
 mso-pattern:auto none;
 color:#0000FF;
 font-size:10pt;
 font-weight:400;
 font-style:normal;
 font-family:"宋体";
 border-top:1px solid #010000;
 border-right:none;
 border-bottom:none;
 border-left:none;
 mso-diagonal-down:none;
 mso-diagonal-up:none;
 mso-protection:locked visible;
 }
.x284
 {
 mso-style-parent:style0;
 mso-number-format:"\@";
 text-align:right;
 vertical-align:middle;
 white-space:nowrap;
 background:#FFC6AA;
 mso-pattern:auto none;
 color:#0000FF;
 font-size:10pt;
 font-weight:400;
 font-style:normal;
 font-family:"宋体";
 border-top:none;
 border-right:none;
 border-bottom:none;
 border-left:1px solid #010000;
 mso-diagonal-down:none;
 mso-diagonal-up:none;
 mso-protection:locked visible;
 }
.x285
 {
 mso-style-parent:style0;
 mso-number-format:"\@";
 text-align:right;
 vertical-align:middle;
 white-space:nowrap;
 background:#FFC6AA;
 mso-pattern:auto none;
 color:#0000FF;
 font-size:10pt;
 font-weight:400;
 font-style:normal;
 font-family:"宋体";
 border:none;
 mso-protection:locked visible;
 }
.x286
 {
 mso-style-parent:style0;
 mso-number-format:"\@";
 text-align:distributed;
 vertical-align:middle;
 white-space:nowrap;
 background:#FFFFFF;
 mso-pattern:auto none;
 color:#000000;
 font-size:10pt;
 font-weight:400;
 font-style:normal;
 font-family:"宋体";
 border-top:2px solid #010000;
 border-right:none;
 border-bottom:none;
 border-left:1px solid #010000;
 mso-diagonal-down:none;
 mso-diagonal-up:none;
 mso-protection:locked visible;
 }
-->
</style> 
  <!--[if gte mso 9]><xml>
 <x:ExcelWorkbook>
  <x:ExcelWorksheets>
   <x:ExcelWorksheet>
    <x:Name>第十三表</x:Name>
<x:WorksheetOptions>
 <x:StandardWidth>2048</x:StandardWidth>
 <x:Print>
  <x:FitWidth>0</x:FitWidth>
  <x:FitHeight>0</x:FitHeight>
  <x:LeftToRight/>
  <x:ValidPrinterInfo/>
  <x:PaperSizeIndex>9</x:PaperSizeIndex>
  <x:Scale>98</x:Scale>
  <x:HorizontalResolution>300</x:HorizontalResolution>
  <x:VerticalResolution>300</x:VerticalResolution>
 </x:Print>
 <x:Selected/>
 <x:ProtectContents>False</x:ProtectContents>
 <x:ProtectObjects>False</x:ProtectObjects>
 <x:ProtectScenarios>False</x:ProtectScenarios>
</x:WorksheetOptions>
   </x:ExcelWorksheet>
  </x:ExcelWorksheets>
  <x:WindowHeight>12375</x:WindowHeight>
  <x:WindowWidth>27945</x:WindowWidth>
  <x:WindowTopX>0</x:WindowTopX>
  <x:WindowTopY>0</x:WindowTopY>
  <x:RefModeR1C1/>
  <x:TabRatio>600</x:TabRatio>
  <x:ActiveSheet>0</x:ActiveSheet>
 </x:ExcelWorkbook>
</xml><![endif]--> 
 </head> 
 <body link="blue" vlink="purple" style="margin: 0;"> 
  <table border="0" cellpadding="0" cellspacing="0" width="1029" style="border-collapse: collapse;table-layout:fixed; width: 100%;overflow-y: auto;"> 
   <colgroup>
    <col width="35" style="mso-width-source:userset;width:35px"> 
    <col width="7" style="mso-width-source:userset;width:7px"> 
    <col width="42" style="mso-width-source:userset;width:42px"> 
    <col width="133" style="mso-width-source:userset;width:133px"> 
    <col width="91" style="mso-width-source:userset;width:91px"> 
    <col width="7" style="mso-width-source:userset;width:7px"> 
    <col width="35" style="mso-width-source:userset;width:35px"> 
    <col width="49" style="mso-width-source:userset;width:49px"> 
    <col width="84" style="mso-width-source:userset;width:84px"> 
    <col width="63" span="2" style="mso-width-source:userset;width:63px"> 
    <col width="105" span="4" style="mso-width-source:userset;width:105px"> 
   </colgroup>
   <tbody>
    <tr height="33" style="mso-height-source:userset;height:24.75pt" id="r0"> 
     <td colspan="15" height="33" class="x75" width="1029" style="height:24.75pt;">党员受党的纪律处分情况（一）</td> 
    </tr> 
    <tr height="22" style="mso-height-source:userset;height:16.5pt" id="r1"> 
     <td colspan="6" height="20" class="x66" style="height:15pt;">填报单位：#(info??'统计单位')</td> 
     <td colspan="2" class="x64" x:num="1"></td> 
     <td class="x68"><span style="float:right">起止时间：</span></td> 
     <td colspan="4" class="x64">2024年1月1日至12月31日</td> 
     <td class="x64"></td> 
     <td class="x68"><span style="float:right">第十三表 </span></td> 
    </tr> 
    <tr height="66" style="mso-height-source:userset;height:49.5pt" id="r2"> 
     <td colspan="5" height="63" class="x231" style="height:47.25pt;">项 目</td> 
     <td class="x118"></td> 
     <td colspan="3" class="x96">总<span style="mso-spacerun:yes;font-family:&quot;Times New Roman&quot;;">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; </span>数</td> 
     <td colspan="2" class="x96">警<span style="mso-spacerun:yes;font-family:&quot;Times New Roman&quot;;">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; </span>告</td> 
     <td class="x175">严重警告</td> 
     <td class="x175">撤销党内职务</td> 
     <td class="x175">留党察看</td> 
     <td class="x175">开除党籍</td> 
    </tr> 
    <tr height="44" style="mso-height-source:userset;height:33pt" id="r3"> 
     <td colspan="5" height="42" class="x81" style="height:31.5pt;">甲</td> 
     <td class="x70"></td> 
     <td colspan="3" class="x101">A</td> 
     <td colspan="2" class="x101">B</td> 
     <td class="x104">C</td> 
     <td class="x104">D</td> 
     <td class="x104">E</td> 
     <td class="x104">F</td> 
    </tr> 
    <tr height="38" style="mso-height-source:userset;height:28.9pt" id="r4"> 
     <td height="37" class="x148" style="height:28.15pt;"></td> 
     <td class="x148"></td> 
     <td colspan="3" class="x84">总计</td> 
     <td class="x70"></td> 
     <td class="x104" x:num="1">1</td> 
     <td colspan="2" class="x105" table-name="dj2024_13" table-cell="0_1_1" onclick="sendMessage(this)" style="text-align: center;overflow: hidden;">#(table0.cell_1_1 ?? == 0 ? '' : table0.cell_1_1??)</td> 
     <td colspan="2" class="x105" table-name="dj2024_13" table-cell="0_1_2" onclick="sendMessage(this)" style="text-align: center;overflow: hidden;">#(table0.cell_1_2 ?? == 0 ? '' : table0.cell_1_2??)</td> 
     <td class="x131" table-name="dj2024_13" table-cell="0_1_3" onclick="sendMessage(this)" style="text-align: center;overflow: hidden;">#(table0.cell_1_3 ?? == 0 ? '' : table0.cell_1_3??)</td> 
     <td class="x131" table-name="dj2024_13" table-cell="0_1_4" onclick="sendMessage(this)" style="text-align: center;overflow: hidden;">#(table0.cell_1_4 ?? == 0 ? '' : table0.cell_1_4??)</td> 
     <td class="x131" table-name="dj2024_13" table-cell="0_1_5" onclick="sendMessage(this)" style="text-align: center;overflow: hidden;">#(table0.cell_1_5 ?? == 0 ? '' : table0.cell_1_5??)</td> 
     <td class="x131" table-name="dj2024_13" table-cell="0_1_6" onclick="sendMessage(this)" style="text-align: center;overflow: hidden;">#(table0.cell_1_6 ?? == 0 ? '' : table0.cell_1_6??)</td> 
    </tr> 
    <tr height="38" style="mso-height-source:userset;height:28.9pt" id="r5"> 
     <td rowspan="2" height="77" class="x66" style="height:57.8pt;"></td> 
     <td class="x186"></td> 
     <td colspan="3" class="x84">厅<span style="mso-spacerun:yes;font-family:&quot;Times New Roman&quot;;">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; </span>局<span style="mso-spacerun:yes;font-family:&quot;Times New Roman&quot;;">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; </span>级</td> 
     <td class="x70"></td> 
     <td class="x104" x:num="2">2</td> 
     <td colspan="2" class="x105" table-name="dj2024_13" table-cell="0_2_1" onclick="sendMessage(this)" style="text-align: center;overflow: hidden;">#(table0.cell_2_1 ?? == 0 ? '' : table0.cell_2_1??)</td> 
     <td colspan="2" class="x180" table-name="dj2024_13" table-cell="0_2_2" onclick="sendMessage(this)" style="text-align: center;overflow: hidden;">#(table0.cell_2_2 ?? == 0 ? '' : table0.cell_2_2??)</td> 
     <td class="x185" table-name="dj2024_13" table-cell="0_2_3" onclick="sendMessage(this)" style="text-align: center;overflow: hidden;">#(table0.cell_2_3 ?? == 0 ? '' : table0.cell_2_3??)</td> 
     <td class="x185" table-name="dj2024_13" table-cell="0_2_4" onclick="sendMessage(this)" style="text-align: center;overflow: hidden;">#(table0.cell_2_4 ?? == 0 ? '' : table0.cell_2_4??)</td> 
     <td class="x185" table-name="dj2024_13" table-cell="0_2_5" onclick="sendMessage(this)" style="text-align: center;overflow: hidden;">#(table0.cell_2_5 ?? == 0 ? '' : table0.cell_2_5??)</td> 
     <td class="x185" table-name="dj2024_13" table-cell="0_2_6" onclick="sendMessage(this)" style="text-align: center;overflow: hidden;">#(table0.cell_2_6 ?? == 0 ? '' : table0.cell_2_6??)</td> 
    </tr> 
    <tr height="38" style="mso-height-source:userset;height:28.9pt" id="r6"> 
     <td class="x186"></td> 
     <td colspan="3" class="x84">县<span style="mso-spacerun:yes;font-family:&quot;Times New Roman&quot;;">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; </span>处<span style="mso-spacerun:yes;font-family:&quot;Times New Roman&quot;;">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; </span>级</td> 
     <td class="x70"></td> 
     <td class="x104" x:num="3">3</td> 
     <td colspan="2" class="x105" table-name="dj2024_13" table-cell="0_3_1" onclick="sendMessage(this)" style="text-align: center;overflow: hidden;">#(table0.cell_3_1 ?? == 0 ? '' : table0.cell_3_1??)</td> 
     <td colspan="2" class="x180" table-name="dj2024_13" table-cell="0_3_2" onclick="sendMessage(this)" style="text-align: center;overflow: hidden;">#(table0.cell_3_2 ?? == 0 ? '' : table0.cell_3_2??)</td> 
     <td class="x185" table-name="dj2024_13" table-cell="0_3_3" onclick="sendMessage(this)" style="text-align: center;overflow: hidden;">#(table0.cell_3_3 ?? == 0 ? '' : table0.cell_3_3??)</td> 
     <td class="x185" table-name="dj2024_13" table-cell="0_3_4" onclick="sendMessage(this)" style="text-align: center;overflow: hidden;">#(table0.cell_3_4 ?? == 0 ? '' : table0.cell_3_4??)</td> 
     <td class="x185" table-name="dj2024_13" table-cell="0_3_5" onclick="sendMessage(this)" style="text-align: center;overflow: hidden;">#(table0.cell_3_5 ?? == 0 ? '' : table0.cell_3_5??)</td> 
     <td class="x185" table-name="dj2024_13" table-cell="0_3_6" onclick="sendMessage(this)" style="text-align: center;overflow: hidden;">#(table0.cell_3_6 ?? == 0 ? '' : table0.cell_3_6??)</td> 
    </tr> 
    <tr height="38" style="mso-height-source:userset;height:28.9pt" id="r7"> 
     <td rowspan="4" height="153" class="x181" style="height:114.85pt;">年<br><br>龄</td> 
     <td class="x186"></td> 
     <td colspan="3" class="x84">35<span style="mso-spacerun:yes;font-family:&quot;Times New Roman&quot;;">&nbsp;&nbsp;&nbsp;&nbsp; </span>岁<span style="mso-spacerun:yes;font-family:&quot;Times New Roman&quot;;">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; </span>及<span style="mso-spacerun:yes;font-family:&quot;Times New Roman&quot;;">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; </span>以<span style="mso-spacerun:yes;font-family:&quot;Times New Roman&quot;;">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; </span>下</td> 
     <td class="x70"></td> 
     <td class="x104" x:num="4">4</td> 
     <td colspan="2" class="x105" table-name="dj2024_13" table-cell="0_4_1" onclick="sendMessage(this)" style="text-align: center;overflow: hidden;">#(table0.cell_4_1 ?? == 0 ? '' : table0.cell_4_1??)</td> 
     <td colspan="2" class="x180" table-name="dj2024_13" table-cell="0_4_2" onclick="sendMessage(this)" style="text-align: center;overflow: hidden;">#(table0.cell_4_2 ?? == 0 ? '' : table0.cell_4_2??)</td> 
     <td class="x185" table-name="dj2024_13" table-cell="0_4_3" onclick="sendMessage(this)" style="text-align: center;overflow: hidden;">#(table0.cell_4_3 ?? == 0 ? '' : table0.cell_4_3??)</td> 
     <td class="x185" table-name="dj2024_13" table-cell="0_4_4" onclick="sendMessage(this)" style="text-align: center;overflow: hidden;">#(table0.cell_4_4 ?? == 0 ? '' : table0.cell_4_4??)</td> 
     <td class="x185" table-name="dj2024_13" table-cell="0_4_5" onclick="sendMessage(this)" style="text-align: center;overflow: hidden;">#(table0.cell_4_5 ?? == 0 ? '' : table0.cell_4_5??)</td> 
     <td class="x185" table-name="dj2024_13" table-cell="0_4_6" onclick="sendMessage(this)" style="text-align: center;overflow: hidden;">#(table0.cell_4_6 ?? == 0 ? '' : table0.cell_4_6??)</td> 
    </tr> 
    <tr height="38" style="mso-height-source:userset;height:28.9pt" id="r8"> 
     <td class="x186"></td> 
     <td colspan="3" class="x84">36<span style="mso-spacerun:yes;font-family:&quot;Times New Roman&quot;;">&nbsp;&nbsp;&nbsp;&nbsp; </span>岁<span style="mso-spacerun:yes;font-family:&quot;Times New Roman&quot;;">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; </span>至<span style="mso-spacerun:yes;font-family:&quot;Times New Roman&quot;;">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; </span>55<span style="mso-spacerun:yes;font-family:&quot;Times New Roman&quot;;">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; </span>岁</td> 
     <td class="x70"></td> 
     <td class="x104" x:num="5">5</td> 
     <td colspan="2" class="x105" table-name="dj2024_13" table-cell="0_5_1" onclick="sendMessage(this)" style="text-align: center;overflow: hidden;">#(table0.cell_5_1 ?? == 0 ? '' : table0.cell_5_1??)</td> 
     <td colspan="2" class="x180" table-name="dj2024_13" table-cell="0_5_2" onclick="sendMessage(this)" style="text-align: center;overflow: hidden;">#(table0.cell_5_2 ?? == 0 ? '' : table0.cell_5_2??)</td> 
     <td class="x185" table-name="dj2024_13" table-cell="0_5_3" onclick="sendMessage(this)" style="text-align: center;overflow: hidden;">#(table0.cell_5_3 ?? == 0 ? '' : table0.cell_5_3??)</td> 
     <td class="x185" table-name="dj2024_13" table-cell="0_5_4" onclick="sendMessage(this)" style="text-align: center;overflow: hidden;">#(table0.cell_5_4 ?? == 0 ? '' : table0.cell_5_4??)</td> 
     <td class="x185" table-name="dj2024_13" table-cell="0_5_5" onclick="sendMessage(this)" style="text-align: center;overflow: hidden;">#(table0.cell_5_5 ?? == 0 ? '' : table0.cell_5_5??)</td> 
     <td class="x185" table-name="dj2024_13" table-cell="0_5_6" onclick="sendMessage(this)" style="text-align: center;overflow: hidden;">#(table0.cell_5_6 ?? == 0 ? '' : table0.cell_5_6??)</td> 
    </tr> 
    <tr height="38" style="mso-height-source:userset;height:28.9pt" id="r9"> 
     <td class="x186"></td> 
     <td colspan="3" class="x84">56<span style="mso-spacerun:yes;font-family:&quot;Times New Roman&quot;;">&nbsp;&nbsp;&nbsp;&nbsp; </span>岁<span style="mso-spacerun:yes;font-family:&quot;Times New Roman&quot;;">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; </span>至<span style="mso-spacerun:yes;font-family:&quot;Times New Roman&quot;;">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; </span>60<span style="mso-spacerun:yes;font-family:&quot;Times New Roman&quot;;">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; </span>岁</td> 
     <td class="x70"></td> 
     <td class="x104" x:num="6">6</td> 
     <td colspan="2" class="x105" table-name="dj2024_13" table-cell="0_6_1" onclick="sendMessage(this)" style="text-align: center;overflow: hidden;">#(table0.cell_6_1 ?? == 0 ? '' : table0.cell_6_1??)</td> 
     <td colspan="2" class="x180" table-name="dj2024_13" table-cell="0_6_2" onclick="sendMessage(this)" style="text-align: center;overflow: hidden;">#(table0.cell_6_2 ?? == 0 ? '' : table0.cell_6_2??)</td> 
     <td class="x185" table-name="dj2024_13" table-cell="0_6_3" onclick="sendMessage(this)" style="text-align: center;overflow: hidden;">#(table0.cell_6_3 ?? == 0 ? '' : table0.cell_6_3??)</td> 
     <td class="x185" table-name="dj2024_13" table-cell="0_6_4" onclick="sendMessage(this)" style="text-align: center;overflow: hidden;">#(table0.cell_6_4 ?? == 0 ? '' : table0.cell_6_4??)</td> 
     <td class="x185" table-name="dj2024_13" table-cell="0_6_5" onclick="sendMessage(this)" style="text-align: center;overflow: hidden;">#(table0.cell_6_5 ?? == 0 ? '' : table0.cell_6_5??)</td> 
     <td class="x185" table-name="dj2024_13" table-cell="0_6_6" onclick="sendMessage(this)" style="text-align: center;overflow: hidden;">#(table0.cell_6_6 ?? == 0 ? '' : table0.cell_6_6??)</td> 
    </tr> 
    <tr height="38" style="mso-height-source:userset;height:28.9pt" id="r10"> 
     <td class="x186"></td> 
     <td colspan="3" class="x84">61<span style="mso-spacerun:yes;font-family:&quot;Times New Roman&quot;;">&nbsp;&nbsp;&nbsp;&nbsp; </span>岁<span style="mso-spacerun:yes;font-family:&quot;Times New Roman&quot;;">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; </span>及<span style="mso-spacerun:yes;font-family:&quot;Times New Roman&quot;;">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; </span>以<span style="mso-spacerun:yes;font-family:&quot;Times New Roman&quot;;">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; </span>上</td> 
     <td class="x70"></td> 
     <td class="x104" x:num="7">7</td> 
     <td colspan="2" class="x105" table-name="dj2024_13" table-cell="0_7_1" onclick="sendMessage(this)" style="text-align: center;overflow: hidden;">#(table0.cell_7_1 ?? == 0 ? '' : table0.cell_7_1??)</td> 
     <td colspan="2" class="x180" table-name="dj2024_13" table-cell="0_7_2" onclick="sendMessage(this)" style="text-align: center;overflow: hidden;">#(table0.cell_7_2 ?? == 0 ? '' : table0.cell_7_2??)</td> 
     <td class="x185" table-name="dj2024_13" table-cell="0_7_3" onclick="sendMessage(this)" style="text-align: center;overflow: hidden;">#(table0.cell_7_3 ?? == 0 ? '' : table0.cell_7_3??)</td> 
     <td class="x185" table-name="dj2024_13" table-cell="0_7_4" onclick="sendMessage(this)" style="text-align: center;overflow: hidden;">#(table0.cell_7_4 ?? == 0 ? '' : table0.cell_7_4??)</td> 
     <td class="x185" table-name="dj2024_13" table-cell="0_7_5" onclick="sendMessage(this)" style="text-align: center;overflow: hidden;">#(table0.cell_7_5 ?? == 0 ? '' : table0.cell_7_5??)</td> 
     <td class="x185" table-name="dj2024_13" table-cell="0_7_6" onclick="sendMessage(this)" style="text-align: center;overflow: hidden;">#(table0.cell_7_6 ?? == 0 ? '' : table0.cell_7_6??)</td> 
    </tr> 
    <tr height="38" style="mso-height-source:userset;height:28.9pt" id="r11"> 
     <td rowspan="6" height="230" class="x181" style="height:172.65pt;">入<br><br>党<br><br>时<br><br>间</td> 
     <td class="x186"></td> 
     <td colspan="3" class="x84">1966<span style="mso-spacerun:yes;font-family:&quot;Times New Roman&quot;;">&nbsp; </span>年<span style="mso-spacerun:yes;font-family:&quot;Times New Roman&quot;;">&nbsp; </span>4<span style="mso-spacerun:yes;font-family:&quot;Times New Roman&quot;;">&nbsp;&nbsp; </span>月<span style="mso-spacerun:yes;font-family:&quot;Times New Roman&quot;;">&nbsp;&nbsp; </span>及<span style="mso-spacerun:yes;font-family:&quot;Times New Roman&quot;;">&nbsp;&nbsp;&nbsp; </span>以<span style="mso-spacerun:yes;font-family:&quot;Times New Roman&quot;;">&nbsp;&nbsp;&nbsp; </span>前</td> 
     <td class="x70"></td> 
     <td class="x104" x:num="8">8</td> 
     <td colspan="2" class="x105" table-name="dj2024_13" table-cell="0_8_1" onclick="sendMessage(this)" style="text-align: center;overflow: hidden;">#(table0.cell_8_1 ?? == 0 ? '' : table0.cell_8_1??)</td> 
     <td colspan="2" class="x180" table-name="dj2024_13" table-cell="0_8_2" onclick="sendMessage(this)" style="text-align: center;overflow: hidden;">#(table0.cell_8_2 ?? == 0 ? '' : table0.cell_8_2??)</td> 
     <td class="x185" table-name="dj2024_13" table-cell="0_8_3" onclick="sendMessage(this)" style="text-align: center;overflow: hidden;">#(table0.cell_8_3 ?? == 0 ? '' : table0.cell_8_3??)</td> 
     <td class="x185" table-name="dj2024_13" table-cell="0_8_4" onclick="sendMessage(this)" style="text-align: center;overflow: hidden;">#(table0.cell_8_4 ?? == 0 ? '' : table0.cell_8_4??)</td> 
     <td class="x185" table-name="dj2024_13" table-cell="0_8_5" onclick="sendMessage(this)" style="text-align: center;overflow: hidden;">#(table0.cell_8_5 ?? == 0 ? '' : table0.cell_8_5??)</td> 
     <td class="x185" table-name="dj2024_13" table-cell="0_8_6" onclick="sendMessage(this)" style="text-align: center;overflow: hidden;">#(table0.cell_8_6 ?? == 0 ? '' : table0.cell_8_6??)</td> 
    </tr> 
    <tr height="38" style="mso-height-source:userset;height:28.9pt" id="r12"> 
     <td class="x186"></td> 
     <td colspan="3" class="x84">1966<span style="mso-spacerun:yes;font-family:&quot;Times New Roman&quot;;">&nbsp; </span>年<span style="mso-spacerun:yes;font-family:&quot;Times New Roman&quot;;">&nbsp; </span>5<span style="mso-spacerun:yes;font-family:&quot;Times New Roman&quot;;">&nbsp; </span>月<span style="mso-spacerun:yes;font-family:&quot;Times New Roman&quot;;">&nbsp; </span>至<span style="mso-spacerun:yes;font-family:&quot;Times New Roman&quot;;">&nbsp; </span>1976<span style="mso-spacerun:yes;font-family:&quot;Times New Roman&quot;;">&nbsp; </span>年 10 月</td> 
     <td class="x70"></td> 
     <td class="x104" x:num="9">9</td> 
     <td colspan="2" class="x105" table-name="dj2024_13" table-cell="0_9_1" onclick="sendMessage(this)" style="text-align: center;overflow: hidden;">#(table0.cell_9_1 ?? == 0 ? '' : table0.cell_9_1??)</td> 
     <td colspan="2" class="x180" table-name="dj2024_13" table-cell="0_9_2" onclick="sendMessage(this)" style="text-align: center;overflow: hidden;">#(table0.cell_9_2 ?? == 0 ? '' : table0.cell_9_2??)</td> 
     <td class="x185" table-name="dj2024_13" table-cell="0_9_3" onclick="sendMessage(this)" style="text-align: center;overflow: hidden;">#(table0.cell_9_3 ?? == 0 ? '' : table0.cell_9_3??)</td> 
     <td class="x185" table-name="dj2024_13" table-cell="0_9_4" onclick="sendMessage(this)" style="text-align: center;overflow: hidden;">#(table0.cell_9_4 ?? == 0 ? '' : table0.cell_9_4??)</td> 
     <td class="x185" table-name="dj2024_13" table-cell="0_9_5" onclick="sendMessage(this)" style="text-align: center;overflow: hidden;">#(table0.cell_9_5 ?? == 0 ? '' : table0.cell_9_5??)</td> 
     <td class="x185" table-name="dj2024_13" table-cell="0_9_6" onclick="sendMessage(this)" style="text-align: center;overflow: hidden;">#(table0.cell_9_6 ?? == 0 ? '' : table0.cell_9_6??)</td> 
    </tr> 
    <tr height="38" style="mso-height-source:userset;height:28.9pt" id="r13"> 
     <td class="x186"></td> 
     <td colspan="3" class="x84">1976<span style="mso-spacerun:yes;font-family:&quot;Times New Roman&quot;;">&nbsp; </span>年<span style="mso-spacerun:yes;font-family:&quot;Times New Roman&quot;;">&nbsp; </span>11 月<span style="mso-spacerun:yes;font-family:&quot;Times New Roman&quot;;">&nbsp; </span>至<span style="mso-spacerun:yes;font-family:&quot;Times New Roman&quot;;">&nbsp; </span>1978<span style="mso-spacerun:yes;font-family:&quot;Times New Roman&quot;;">&nbsp; </span>年 12 月</td> 
     <td class="x70"></td> 
     <td class="x104" x:num="10">10</td> 
     <td colspan="2" class="x105" table-name="dj2024_13" table-cell="0_10_1" onclick="sendMessage(this)" style="text-align: center;overflow: hidden;">#(table0.cell_10_1 ?? == 0 ? '' : table0.cell_10_1??)</td> 
     <td colspan="2" class="x180" table-name="dj2024_13" table-cell="0_10_2" onclick="sendMessage(this)" style="text-align: center;overflow: hidden;">#(table0.cell_10_2 ?? == 0 ? '' : table0.cell_10_2??)</td> 
     <td class="x185" table-name="dj2024_13" table-cell="0_10_3" onclick="sendMessage(this)" style="text-align: center;overflow: hidden;">#(table0.cell_10_3 ?? == 0 ? '' : table0.cell_10_3??)</td> 
     <td class="x185" table-name="dj2024_13" table-cell="0_10_4" onclick="sendMessage(this)" style="text-align: center;overflow: hidden;">#(table0.cell_10_4 ?? == 0 ? '' : table0.cell_10_4??)</td> 
     <td class="x185" table-name="dj2024_13" table-cell="0_10_5" onclick="sendMessage(this)" style="text-align: center;overflow: hidden;">#(table0.cell_10_5 ?? == 0 ? '' : table0.cell_10_5??)</td> 
     <td class="x185" table-name="dj2024_13" table-cell="0_10_6" onclick="sendMessage(this)" style="text-align: center;overflow: hidden;">#(table0.cell_10_6 ?? == 0 ? '' : table0.cell_10_6??)</td> 
    </tr> 
    <tr height="38" style="mso-height-source:userset;height:28.9pt" id="r14"> 
     <td class="x186"></td> 
     <td colspan="3" class="x84">1979<span style="mso-spacerun:yes;font-family:&quot;Times New Roman&quot;;">&nbsp; </span>年<span style="mso-spacerun:yes;font-family:&quot;Times New Roman&quot;;">&nbsp; </span>1<span style="mso-spacerun:yes;font-family:&quot;Times New Roman&quot;;">&nbsp; </span>月<span style="mso-spacerun:yes;font-family:&quot;Times New Roman&quot;;">&nbsp; </span>至<span style="mso-spacerun:yes;font-family:&quot;Times New Roman&quot;;">&nbsp; </span>2002<span style="mso-spacerun:yes;font-family:&quot;Times New Roman&quot;;">&nbsp; </span>年 10 月</td> 
     <td class="x70"></td> 
     <td class="x104" x:num="11">11</td> 
     <td colspan="2" class="x105" table-name="dj2024_13" table-cell="0_11_1" onclick="sendMessage(this)" style="text-align: center;overflow: hidden;">#(table0.cell_11_1 ?? == 0 ? '' : table0.cell_11_1??)</td> 
     <td colspan="2" class="x180" table-name="dj2024_13" table-cell="0_11_2" onclick="sendMessage(this)" style="text-align: center;overflow: hidden;">#(table0.cell_11_2 ?? == 0 ? '' : table0.cell_11_2??)</td> 
     <td class="x185" table-name="dj2024_13" table-cell="0_11_3" onclick="sendMessage(this)" style="text-align: center;overflow: hidden;">#(table0.cell_11_3 ?? == 0 ? '' : table0.cell_11_3??)</td> 
     <td class="x185" table-name="dj2024_13" table-cell="0_11_4" onclick="sendMessage(this)" style="text-align: center;overflow: hidden;">#(table0.cell_11_4 ?? == 0 ? '' : table0.cell_11_4??)</td> 
     <td class="x185" table-name="dj2024_13" table-cell="0_11_5" onclick="sendMessage(this)" style="text-align: center;overflow: hidden;">#(table0.cell_11_5 ?? == 0 ? '' : table0.cell_11_5??)</td> 
     <td class="x185" table-name="dj2024_13" table-cell="0_11_6" onclick="sendMessage(this)" style="text-align: center;overflow: hidden;">#(table0.cell_11_6 ?? == 0 ? '' : table0.cell_11_6??)</td> 
    </tr> 
    <tr height="38" style="mso-height-source:userset;height:28.9pt" id="r15"> 
     <td class="x186"></td> 
     <td colspan="3" class="x84">2002<span style="mso-spacerun:yes;font-family:&quot;Times New Roman&quot;;">&nbsp; </span>年<span style="mso-spacerun:yes;font-family:&quot;Times New Roman&quot;;">&nbsp; </span>11 月<span style="mso-spacerun:yes;font-family:&quot;Times New Roman&quot;;">&nbsp; </span>至<span style="mso-spacerun:yes;font-family:&quot;Times New Roman&quot;;">&nbsp; </span>2012<span style="mso-spacerun:yes;font-family:&quot;Times New Roman&quot;;">&nbsp; </span>年 10 月</td> 
     <td class="x70"></td> 
     <td class="x104" x:num="12">12</td> 
     <td colspan="2" class="x105" table-name="dj2024_13" table-cell="0_12_1" onclick="sendMessage(this)" style="text-align: center;overflow: hidden;">#(table0.cell_12_1 ?? == 0 ? '' : table0.cell_12_1??)</td> 
     <td colspan="2" class="x180" table-name="dj2024_13" table-cell="0_12_2" onclick="sendMessage(this)" style="text-align: center;overflow: hidden;">#(table0.cell_12_2 ?? == 0 ? '' : table0.cell_12_2??)</td> 
     <td class="x185" table-name="dj2024_13" table-cell="0_12_3" onclick="sendMessage(this)" style="text-align: center;overflow: hidden;">#(table0.cell_12_3 ?? == 0 ? '' : table0.cell_12_3??)</td> 
     <td class="x185" table-name="dj2024_13" table-cell="0_12_4" onclick="sendMessage(this)" style="text-align: center;overflow: hidden;">#(table0.cell_12_4 ?? == 0 ? '' : table0.cell_12_4??)</td> 
     <td class="x185" table-name="dj2024_13" table-cell="0_12_5" onclick="sendMessage(this)" style="text-align: center;overflow: hidden;">#(table0.cell_12_5 ?? == 0 ? '' : table0.cell_12_5??)</td> 
     <td class="x185" table-name="dj2024_13" table-cell="0_12_6" onclick="sendMessage(this)" style="text-align: center;overflow: hidden;">#(table0.cell_12_6 ?? == 0 ? '' : table0.cell_12_6??)</td> 
    </tr> 
    <tr height="38" style="mso-height-source:userset;height:28.9pt" id="r16"> 
     <td class="x186"></td> 
     <td colspan="3" class="x84">2012<span style="mso-spacerun:yes;font-family:&quot;Times New Roman&quot;;">&nbsp; </span>年<span style="mso-spacerun:yes;font-family:&quot;Times New Roman&quot;;">&nbsp; </span>11<span style="mso-spacerun:yes;font-family:&quot;Times New Roman&quot;;">&nbsp;&nbsp; </span>月<span style="mso-spacerun:yes;font-family:&quot;Times New Roman&quot;;">&nbsp;&nbsp;&nbsp; </span>及<span style="mso-spacerun:yes;font-family:&quot;Times New Roman&quot;;">&nbsp;&nbsp;&nbsp; </span>以<span style="mso-spacerun:yes;font-family:&quot;Times New Roman&quot;;">&nbsp;&nbsp;&nbsp; </span>后</td> 
     <td class="x70"></td> 
     <td class="x104" x:num="13">13</td> 
     <td colspan="2" class="x105" table-name="dj2024_13" table-cell="0_13_1" onclick="sendMessage(this)" style="text-align: center;overflow: hidden;">#(table0.cell_13_1 ?? == 0 ? '' : table0.cell_13_1??)</td> 
     <td colspan="2" class="x180" table-name="dj2024_13" table-cell="0_13_2" onclick="sendMessage(this)" style="text-align: center;overflow: hidden;">#(table0.cell_13_2 ?? == 0 ? '' : table0.cell_13_2??)</td> 
     <td class="x185" table-name="dj2024_13" table-cell="0_13_3" onclick="sendMessage(this)" style="text-align: center;overflow: hidden;">#(table0.cell_13_3 ?? == 0 ? '' : table0.cell_13_3??)</td> 
     <td class="x185" table-name="dj2024_13" table-cell="0_13_4" onclick="sendMessage(this)" style="text-align: center;overflow: hidden;">#(table0.cell_13_4 ?? == 0 ? '' : table0.cell_13_4??)</td> 
     <td class="x185" table-name="dj2024_13" table-cell="0_13_5" onclick="sendMessage(this)" style="text-align: center;overflow: hidden;">#(table0.cell_13_5 ?? == 0 ? '' : table0.cell_13_5??)</td> 
     <td class="x185" table-name="dj2024_13" table-cell="0_13_6" onclick="sendMessage(this)" style="text-align: center;overflow: hidden;">#(table0.cell_13_6 ?? == 0 ? '' : table0.cell_13_6??)</td> 
    </tr> 
    <tr height="22" style="mso-height-source:userset;height:16.5pt" id="r17"> 
     <td colspan="4" height="20" class="x89" style="height:15pt;">补充资料：受到改组处理的党组织</td> 
     <td class="x224" table-name="dj2024_13_replenish" table-cell="r_1" onclick="sendMessage(this)" style="text-align: center;overflow: hidden;">#(replenish.cell_1 ?? == 0 ? '' : replenish.cell_1??)</td> 
     <td colspan="4" class="x123">个，受到解散处理的党组织</td> 
     <td colspan="2" class="x213" table-name="dj2024_13_replenish" table-cell="r_2" onclick="sendMessage(this)" style="text-align: center;overflow: hidden;">#(replenish.cell_2 ?? == 0 ? '' : replenish.cell_2??)</td> 
     <td class="x118">个。</td> 
     <td class="x118"></td> 
     <td class="x118"></td> 
     <td class="x118"></td> 
    </tr> 
    <!--[if supportMisalignedColumns]--> 
    <tr height="0" style="display:none"> 
     <td width="35" style="width:26.25pt"></td> 
     <td width="7" style="width:5.25pt"></td> 
     <td width="42" style="width:31.5pt"></td> 
     <td width="133" style="width:99.75pt"></td> 
     <td width="91" style="width:68.25pt"></td> 
     <td width="7" style="width:5.25pt"></td> 
     <td width="35" style="width:26.25pt"></td> 
     <td width="49" style="width:36.75pt"></td> 
     <td width="84" style="width:63pt"></td> 
     <td width="63" style="width:47.25pt"></td> 
     <td width="63" style="width:47.25pt"></td> 
     <td width="105" style="width:78.75pt"></td> 
     <td width="105" style="width:78.75pt"></td> 
     <td width="105" style="width:78.75pt"></td> 
     <td width="105" style="width:78.75pt"></td> 
    </tr> 
    <!--[endif]--> 
   </tbody>
  </table> 
  <script language="javascript" type="text/javascript">
 function ChangeRowspanHiddenData()
 {
   var node;
   var params=["r5","r7","r11"];
   for (var i = 0;i < params.length; i++)
   {
       node = document.getElementById(params[i]);
       if (node != null)
       {
           node.style.display = "";
       }
   }
 }
 ChangeRowspanHiddenData();
</script>   
  <script type="text/javascript">function sendMessage(cell){var obj=new Object();var tableName=cell.getAttribute("table-name");var tableCellIndex=cell.getAttribute("table-cell");obj['tableName']=tableName;obj['tableCellIndex']=tableCellIndex;console.log(obj);window.parent.postMessage(JSON.stringify(obj))}</script>
 </body>
</html>