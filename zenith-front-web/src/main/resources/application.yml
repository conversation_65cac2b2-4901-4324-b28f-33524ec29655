server:
  port: @port@
  error:
    include-exception: true
  tomcat:
    basedir: ./tmp/tomcat/
spring:
  profiles:
    active: @env@
  application:
    name: zenith-front-dj
  #banner:
  #  location: config/banner.txt
  #  charset: UTF-8
  jackson:
    default-property-inclusion: non_null
    serialization:
      WRITE_DATES_AS_TIMESTAMPS: true
  mvc:
    servlet:
      #path: /api
      load-on-startup: 0
    #出现错误时, 直接抛出异常
    throw-exception-if-no-handler-found: true
    #springboot升级到2.2.0RELEASE后，上传文件报FileUploadException，因使用logback过滤器引起，解决方案，增加以下配置:
    hiddenmethod:
      filter:
        enabled: true
  #可访问的静态资源路径
  resources:
    #static-locations: classpath:/static/
    #不要为我们工程中的资源文件建立映射
    add-mappings: true
  main:
    allow-bean-definition-overriding: true
    banner-mode: off # 关闭SpringBoot启动图标(banner)
  servlet:
    multipart:
      enabled: true
      max-file-size: 20MB       #(这里是限制的文件大小)
      max-request-size: 20MB    #(这里是限制的文件大小)
  datasource:
    url: @datasource.url@
    username: @datasource.username@
    password: @datasource.password@
    driver-class-name: @datasource.driver@
    ###################以下为druid增加的配置###########################
    type: com.alibaba.druid.pool.DruidDataSource
    druid:
      db-type: postgresql
      #开启sql语句自动加载，只认识名为： schema-all.sql和schema.sql和data-all.sql和data.sql的sql语句
      #    initialization-mode: always

      # 下面为连接池的补充设置，应用到上面所有数据源中
      # 初始化大小，最小，最大
      initialSize: 5
      # 最小连接池数量
      minIdle: 5
      # 最大连接池数量
      maxActive: 20
      # 配置获取连接等待超时的时间
      maxWait: 60000
      #既作为检测的间隔时间又作为testWhileIdel执行的依据
      timeBetweenEvictionRunsMillis: 60000
      #销毁线程时检测当前连接的最后活动时间和当前时间差大于该值时，关闭当前连接
      minEvictableIdleTimeMillis: 300000
      #用来检测连接是否有效的sql 必须是一个查询语句
      validationQuery: SELECT 1
      #申请连接的时候检测，如果空闲时间大于timeBetweenEvictionRunsMillis，执行validationQuery检测连接是否有效。
      testWhileIdle: true
      #申请连接时会执行validationQuery检测连接是否有效,开启会降低性能,默认为true
      testOnBorrow: false
      #归还连接时会执行validationQuery检测连接是否有效,开启会降低性能,默认为true
      testOnReturn: false
      #当数据库抛出不可恢复的异常时,抛弃该连接
      #exception-sorter: true
      #是否缓存preparedStatement,mysql5.5+建议开启
      poolPreparedStatements: true
      #当值大于0时poolPreparedStatements会自动修改为true
      maxPoolPreparedStatementPerConnectionSize: 20
      useGlobalDataSourceStat: true
      #通过connectProperties属性来打开mergeSql功能；慢SQL记录
      #connectionProperties: druid.stat.mergeSql=true;druid.stat.slowSqlMillis=500：
      # 配置监控统计拦截的filters，去掉后监控界面sql无法统计，'wall'用于防火墙
      #filters: stat,wall

      #配置监控属性： 在druid-starter的： com.alibaba.druid.spring.boot.autoconfigure.stat包下进行的逻辑配置
      web-stat-filter: # WebStatFilter配置，
        enabled: true #默认为false，表示不使用WebStatFilter配置，就是属性名去短线
        url-pattern: /* #拦截该项目下的一切请求
        exclusions: /druid/*,*.js,*.gif,*.jpg,*.bmp,*.png,*.css,*.ico  #对这些请求放行
        session-stat-enable: true
        principal-session-name: session_name
        principal-cookie-name: cookie_name
      stat-view-servlet: # StatViewServlet配置
        enabled: true #默认为false，表示不使用StatViewServlet配置，就是属性名去短线
        url-pattern: /druid/*  #配置DruidStatViewServlet的访问地址。后台监控页面的访问地址
        reset-enable: false #禁用HTML页面上的“重置”功能，会把所有监控的数据全部清空，一般不使用
        login-username: admin #监控页面登录的用户名
        login-password: 1809 #监控页面登录的密码
        allow: 127.0.0.1,***********/24  #IP白名单(没有配置或者为空，则允许所有访问)。允许谁访问druid后台，默认允许全部用户访问。
        deny:  #IP黑名单 (存在共同时，deny优先于allow)。不允许谁进行访问druid后台，
      #Spring监控配置，说明请参考Druid Github Wiki，配置_Druid和Spring关联监控配置
      #aop-patterns: com.lcf.service.*
      #自定义sql文件名，点进去后这个是一个list
      #schema:
      #- classpath:department.sql #可以指定多个文件
  redis:
    redisson:
      file: classpath:redisson.yml
    timeout: 2000 # 连接或读取超时时长（毫秒）
    database: 2
    jedis:
      pool:
        max-active: 8 # 连接池最大连接数（使用负值表示没有限制）
        max-wait: 800 # 连接池最大阻塞等待时间（使用负值表示没有限制）
        max-idle: 8 # 连接池中的最大空闲连接
        min-idle: 2 # 连接池中的最小空闲连接
mybatis-plus:
  mapper-locations: classpath*:com/zenith/front/dao/mapper/**/*.xml,classpath*:/mapper/**.xml
  type-aliases-package: com.zenith.front.model,com.zenith.front.dao.encrypt.alias.Encrypt
  type-handlers-package: com.zenith.front.dao.encrypt.typeHandler.EncryptTypeHandler
  #mybatis日志输出到控制台
  configuration:
    #log-impl: org.apache.ibatis.logging.stdout.StdOutImpl #关键部分。用来显示sql
    map-underscore-to-camel-case: true
    cache-enabled: false
    jdbc-type-for-null: 'null'
    #解决查询结果值为null时，字段不显示的问题
    call-setters-on-nulls: true
  global-config:
    db-config:
      #主键类型  0:"数据库ID自增", 1:"用户输入ID",2:"全局唯一ID (数字类型唯一ID)", 3:"全局唯一ID UUID";
      id-type: AUTO
    banner: off # 关闭mybatisplus启动图标
logging:
  config: classpath:logback-spring.xml
  level:
    com:
      zenith:
        front:
          dao:
            mapper: debug   #debug只输出sql语句 trace 打印sql语句也打印sql执行结果集
    com.zenith.front.station.dao: debug
# 是否开发模式
is-dev: true
# 加密配置
encrypt:
  private-key: d9878c8f0c464127
  enable: true
  # 微信服务器回调所用的 token
token: b45cffe084dd3d20d928bee85e7b0f21
# 测试用的账号
openId: wx0626a1ecbf3beb07
appSecret: bce7f9fd49387ef5484110dac309912f
#是否对消息进行加密，是否对消息进行加密，对应于微信平台的消息加解密方式，false支持明文模式及兼容模式，true支持安全模式及兼容模式
encryptMessage: false
encodingAesKey: nHH2jBL6neN0MBtSKxRPRokQq9eop1pMJS9Wd1M5ilQ
# 使用于生成工作动态发布到门户端顶层审核code
baseOrgOrgCode: 500
baseOrgCode: 500
es:
  ip: 127.0.0.1
  port: 9200
  username: elastic
  password: 20191809
  enable: true

# 当前统计年度
statistical:
  year: 20220101,20221231

#身份证认证配置
server_url: http://sfrz.gzdata.com.cn:8088/dbo-service/api/v1/user/auth2/realNameAuthentication
accessKeyId: RL8gKoP7tSCkmzLbpxR7CA==
accessKeyScrect: AmrW23u3TZ8wZUKai7P2W1T2IBcc24GNSJHyIMNoKw4=

## 数据同步插槽名称
sync:
  ## 数据库同步Es配置
  es:
    ## 是否启用数据库同步
    enable: false
    ## 数据插槽名称
    slot_name: default_slot
  db:
    ## 是否启用业务表同步数据库宽表
    enable: false

#是否启用中间交换区
exchange: false

#身份证中间交换区验证链接
exchange_user_link: http://127.0.0.1/api/login/exist

#登录回调IP
exchange_user_login: http://127.0.0.1:12101/login

#新增用户同步链接
exchange_user_add: http://127.0.0.1/api/login/add

#新增流出党员同步和拉取链接
sync_flow_push: http://127.0.0.1:8087/api/flow/push
sync_flow_pull: http://127.0.0.1:8087/api/flow/pull

#用户中间交换区反代key
exchange_nginx_key: 5b4965fa710e4079
#zip解密密码
unzip:
  password: sjr1809

#中间交换区请求key
exchange_key: Zenith

schedule:
  #流动党员任务调度
  flow:
    #是否开启定时任务
    open:
      true
    #定时任务表达式
    cron:
      #五分钟一次
      0 */1 * * * ?
age:
  #计算年纪任务调度
  calc:
    #是否开启定时任务
    open:
      true
    #定时任务表达式
    cron:
      #每晚凌晨2点
      0 0 2 * * ?
#逻辑校验
logic-validator:
  open: true

# 排除节点
excludeOrgCode: 052008101,052008102

#身份证查重或格式错误
idCard:
  inner:
    false
  #是否开启定时任务-外网设为false 内网设为ture
  open:
    true
  #定时任务表达式
  cron:
    #每晚凌晨2点
    0 0 4 * * ?
transferRecord: false

# xxl-job
xxl:
  job:
    enabled: false
    admin:
      addresses: http://127.0.0.1:8086/xxl-job-admin # 调度中心部署跟地址 [选填]：如调度中心集群部署存在多个地址则用逗号分隔。执行器将会使用该地址进行"执行器心跳注册"和"任务结果回调"；为空则关闭自动注册；
    executor:
      appname: lab-28-executor # 执行器 AppName [选填]：执行器心跳注册分组依据；为空则关闭自动注册
      ip: # 执行器IP [选填]：默认为空表示自动获取IP，多网卡时可手动设置指定IP，该IP不会绑定Host仅作为通讯实用；地址信息用于 "执行器注册" 和 "调度中心请求并触发任务"；
      port: 6666 # ### 执行器端口号 [选填]：小于等于0则自动获取；默认端口为9999，单机部署多个执行器时，注意要配置不同执行器端口；
      logpath: /Users/<USER>/logs/xxl-job/lab-28-executor # 执行器运行日志文件存储磁盘路径 [选填] ：需要对该路径拥有读写权限；为空则使用默认路径；
      logretentiondays: 30 # 执行器日志文件保存天数 [选填] ： 过期日志自动清理, 限制值大于等于3时生效; 否则, 如-1, 关闭自动清理功能；
    accessToken: default_token # 执行器通讯TOKEN [选填]：非空时启用；

vc:
  feign:
    host: http://127.0.0.1:8079
  #村社区党员任务调度
  mem:
    #是否开启定时任务
    open:
      true
    #定时任务表达式
    cron:
      #每晚凌晨1点10
      0 10 1 * * ?
  #村社区单位任务调度
  unit:
    #是否开启定时任务
    open:
      true
    #定时任务表达式
    cron:
      #每晚凌晨1点15
      0 15 1 * * ?
  #村社区用户任务调度
  user:
    #是否开启定时任务
    open:
      true
    #定时任务表达式
    cron:
      #每晚凌晨1点20
      0 20 1 * * ?
  #村社区发展党员任务调度
  develop:
    #是否开启定时任务
    open:
      true
    #定时任务表达式
    cron:
      #每晚凌晨1点25
      0 25 1 * * ?
minio:
  enabled: true
  endpoint: http://*************:9000
  accessKey: wcpTaj5BItiY4NKf
  secretKey: 0A1TfOYLENQTcxiD7b3r2Ndx1YtIQxCk
  # http://*************:5000/
  #用户名
  #MINIO_ROOT_USER="minio"
  #密码
  #MINIO_ROOT_PASSWORD="12345678"