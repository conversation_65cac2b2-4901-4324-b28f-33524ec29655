CREATE OR REPLACE FUNCTION "public"."delete_repeat"("tablename" varchar)
    RETURNS "pg_catalog"."int8" AS $BODY$
DECLARE
    rec RECORD;
    sql varchar;
BEGIN
    RAISE INFO '处理表名:%', tablename;
    IF NOT exists(select * from pg_class where relname = tablename) then
        RAISE INFO '处理表不存在:%', tablename;
        RETURN -1;
    END IF;

    FOR rec in EXECUTE('select id from '||tablename||' group by id having count(id) > 1') LOOP
            RAISE INFO 'id:%', rec.id;
            sql := 'delete from ' || tablename || ' where id = ''' || rec.id|| ''' and
		ctid <> (select min(ctid) from ' || tablename || ' where id = ''' || rec.id|| ''')';
            execute sql;
            RAISE INFO 'sql:%', sql;
        END LOOP;
    RETURN 1;
END;
$BODY$
    LANGUAGE plpgsql VOLATILE COST 100