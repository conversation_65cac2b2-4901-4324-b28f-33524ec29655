<?xml version="1.0" encoding="UTF-8"?>
<configuration scan="true" scanPeriod="300 seconds">

    <property name="LOG_PATH" value="./logs"/>

    <property name="CONSOLE_LOG_PATTERN" value="%date{yyyy-MM-dd HH:mm:ss} | %highlight(%-5level) | %boldYellow(%thread) | %boldGreen(%logger.%method) | %highlight(%msg%n)"/>
    <property name="FILE_PATTERN" value="%date{yyyy-MM-dd HH:mm:ss} | %-1level | %thread | %logger.%method | %msg%n"/>

    <!-- 级别从高到低 OFF 、 FATAL 、 ERROR 、 WARN 、 INFO 、 DEBUG 、 TRACE 、 ALL -->

    <statusListener class="ch.qos.logback.core.status.NopStatusListener" />

    <appender name="console" class="ch.qos.logback.core.ConsoleAppender">
        <encoder charset="UTF8">
            <pattern>${CONSOLE_LOG_PATTERN}</pattern>
        </encoder>
        <layout class="ch.qos.logback.classic.PatternLayout">
            <pattern>${CONSOLE_LOG_PATTERN}</pattern>
        </layout>
        <filter class="ch.qos.logback.classic.filter.ThresholdFilter">
            <level>TRACE</level>
        </filter>

    </appender>

    <appender name="log-console" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <rollingPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedRollingPolicy">
            <fileNamePattern>${LOG_PATH}/dj-%d{yyyy-MM-dd}-%i.log</fileNamePattern>
            <maxHistory>10</maxHistory>
            <maxFileSize>100MB</maxFileSize>
        </rollingPolicy>
        <append>true</append>
        <filter class="ch.qos.logback.classic.filter.ThresholdFilter">
            <!-- 临界值过滤器，过滤掉低于指定临界值的日志。当日志级别等于或高于临界值时，过滤器返回NEUTRAL；当日志级别低于临界值时，日志会被拒绝。-->
            <level>TRACE</level>
        </filter>
        <layout class="ch.qos.logback.classic.PatternLayout">
            <pattern>${FILE_PATTERN}</pattern>
        </layout>
    </appender>

    <appender name="async-log-console" class="ch.qos.logback.classic.AsyncAppender">
        <discardingThreshold>0</discardingThreshold>
        <queueSize>256</queueSize>
        <appender-ref ref="log-console"/>
    </appender>

    <!--mybatis log configure-->
    <logger name="com.apache.ibatis" level="TRACE"/>

    <root level="INFO">
        <appender-ref ref="console" />
        <appender-ref ref="async-log-console" />
    </root>

</configuration>