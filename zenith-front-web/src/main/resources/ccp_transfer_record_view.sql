DROP VIEW "public"."ccp_transfer_src_org_view";

CREATE VIEW "public"."ccp_transfer_src_org_view" AS  SELECT ccp_transfer_record.id,
    ccp_transfer_record.user_id,
    ccp_transfer_record.name,
    ccp_transfer_record.mem_id,
    ccp_transfer_record.src_org_id,
    ccp_transfer_record.src_org_name,
    ccp_transfer_record.target_org_id,
    ccp_transfer_record.target_org_name,
    ccp_transfer_record.common_org_id,
    ccp_transfer_record.common_org_name,
    ccp_transfer_record.src_org_relation,
    ccp_transfer_record.target_org_relation,
    ccp_transfer_record.mem_fee_end_time,
    ccp_transfer_record.mem_fee_standard,
    ccp_transfer_record.out_type,
    ccp_transfer_record.in_type,
    ccp_transfer_record.type,
    ccp_transfer_record.current_approval_id,
    ccp_transfer_record.status,
    ccp_transfer_record.reason,
    ccp_transfer_record.create_time,
    ccp_transfer_record.update_time,
    ccp_transfer_record.extra_data,
    ccp_transfer_record.letter_url,
    ccp_transfer_record.effect_mems,
    ccp_transfer_record.d92_code,
    ccp_transfer_record.d92_name,
    ccp_transfer_record.remark,
    ccp_transfer_record.src_org_relation_rel,
    ccp_transfer_record.target_org_relation_rel,
    ccp_transfer_record.whether_extend_prep_period,
    ccp_transfer_record.data_text,
    ccp_transfer_record.transfer_out_time,
    ccp_org.org_code
   FROM ccp_transfer_record
     LEFT JOIN ccp_org ON ccp_transfer_record.src_org_id::text = ccp_org.code::text
  WHERE ccp_org.delete_time IS NULL;

ALTER TABLE "public"."ccp_transfer_src_org_view" OWNER TO "postgres";

DROP VIEW "public"."ccp_transfer_tar_org_view";

CREATE VIEW "public"."ccp_transfer_tar_org_view" AS  SELECT ccp_transfer_record.id,
    ccp_transfer_record.user_id,
    ccp_transfer_record.name,
    ccp_transfer_record.mem_id,
    ccp_transfer_record.src_org_id,
    ccp_transfer_record.src_org_name,
    ccp_transfer_record.target_org_id,
    ccp_transfer_record.target_org_name,
    ccp_transfer_record.common_org_id,
    ccp_transfer_record.common_org_name,
    ccp_transfer_record.src_org_relation,
    ccp_transfer_record.target_org_relation,
    ccp_transfer_record.mem_fee_end_time,
    ccp_transfer_record.mem_fee_standard,
    ccp_transfer_record.out_type,
    ccp_transfer_record.in_type,
    ccp_transfer_record.type,
    ccp_transfer_record.current_approval_id,
    ccp_transfer_record.status,
    ccp_transfer_record.reason,
    ccp_transfer_record.create_time,
    ccp_transfer_record.update_time,
    ccp_transfer_record.extra_data,
    ccp_transfer_record.letter_url,
    ccp_transfer_record.effect_mems,
    ccp_transfer_record.d92_code,
    ccp_transfer_record.d92_name,
    ccp_transfer_record.remark,
    ccp_transfer_record.src_org_relation_rel,
    ccp_transfer_record.target_org_relation_rel,
    ccp_transfer_record.whether_extend_prep_period,
    ccp_transfer_record.data_text,
    ccp_transfer_record.transfer_out_time,
    ccp_org.org_code
   FROM ccp_transfer_record
     LEFT JOIN ccp_org ON ccp_transfer_record.target_org_id::text = ccp_org.code::text
  WHERE ccp_org.delete_time IS NULL;

ALTER TABLE "public"."ccp_transfer_tar_org_view" OWNER TO "postgres";