# 拉取一个jre为1.8的docker镜像
FROM openjdk:8-jre-alpine3.9
# 声明作者
MAINTAINER lihp<<EMAIL>>
# 工作目录
WORKDIR /home/<USER>
RUN echo http://mirrors.aliyun.com/alpine/v3.10/main/ > /etc/apk/repositories &&  echo http://mirrors.aliyun.com/alpine/v3.10/community/ >> /etc/apk/repositories
RUN apk add curl
# Docker报错NullPointerException at sun.awt.FontConfiguration.getVersion,安装字体库
RUN apk add --update font-adobe-100dpi ttf-dejavu fontconfig
# 环境配置
ENV TZ=Asia/Shanghai
RUN apk add tzdata && cp /usr/share/zoneinfo/$TZ /etc/localtime && echo $TZ > /etc/timezone