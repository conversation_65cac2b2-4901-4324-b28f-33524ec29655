<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zenith.front.dao.mapper.ykz.SyncUnitMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.zenith.front.model.bean.SyncUnit">
        <id column="id" property="id" />
        <result column="actual" property="actual" />
        <result column="name" property="name" />
        <result column="fullName" property="fullName" />
        <result column="code" property="code" />
        <result column="parentId" property="parentId" />
        <result column="levelCode" property="levelCode" />
        <result column="openId" property="openId" />
        <result column="type" property="type" />
        <result column="tel" property="tel" />
        <result column="orderNo" property="orderNo" />
        <result column="state" property="state" />
        <result column="unitnote" property="unitnote" />
        <result column="zwddId" property="zwddId" />
        <result column="unitAddress" property="unitAddress" />
        <result column="contact" property="contact" />
        <result column="create_time" property="createTime" />
        <result column="update_time" property="updateTime" />
        <result column="is_delete" property="isDelete" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, actual, name, fullName, code, parentId, levelCode, openId, type, tel, orderNo, state, unitnote, zwddId, unitAddress, contact, create_time, update_time, is_delete
    </sql>

</mapper>
