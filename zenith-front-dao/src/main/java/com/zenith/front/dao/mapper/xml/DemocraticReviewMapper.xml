<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zenith.front.dao.mapper.democraticreview.DemocraticReviewMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.zenith.front.model.bean.DemocraticReview">
        <id column="id" property="id" />
        <result column="code" property="code" />
        <result column="org_code" property="orgCode" />
        <result column="review_org_code" property="reviewOrgCode" />
        <result column="year" property="year" />
        <result column="exclude_mem_set" property="excludeMemSet" />
        <result column="start_time" property="startTime" />
        <result column="end_time" property="endTime" />
        <result column="deadline_time" property="deadlineTime" />
        <result column="status" property="status" />
        <result column="score" property="score" />
        <result column="creator_mem_code" property="creatorMemCode" />
        <result column="create_time" property="createTime" />
        <result column="update_time" property="updateTime" />
        <result column="delete_time" property="deleteTime" />
        <result column="update_account" property="updateAccount" />
        <result column="exclude_mem_count" property="excludeMemCount" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, code, org_code, review_org_code, year, exclude_mem_set, start_time, end_time, deadline_time, status, score, creator_mem_code, create_time, update_time, delete_time, update_account, exclude_mem_count
    </sql>

    <select id="ana" resultType="com.zenith.front.model.vo.DemocraticVO">
        SELECT
            "ccp_democratic_review_lead"."d69_code",
            COUNT ( "ccp_democratic_review_lead"."d69_code" ),
            "ccp_democratic_review_lead"."lead_org_code"
        FROM
            "ccp_democratic_review"
                INNER JOIN "ccp_org" ON "ccp_democratic_review"."org_code" = "ccp_org"."code"
                INNER JOIN "ccp_democratic_review_lead" ON "ccp_democratic_review_lead"."review_code" = "ccp_democratic_review"."code"
        WHERE
            "ccp_democratic_review"."status" = 3
          AND "ccp_democratic_review"."year" = to_char( CURRENT_DATE, 'yyyy' )
          AND "ccp_org"."org_code" LIKE CONCAT(#{orgCode},'%')
          AND "ccp_democratic_review"."delete_time" IS NULL
        GROUP BY
            "ccp_democratic_review_lead"."d69_code",
            "ccp_democratic_review_lead"."lead_org_code"
    </select>

    <select id="countMemTotal" resultType="com.zenith.front.model.vo.DemocraticVO">
        SELECT
            "ccp_org"."org_code",
            COUNT (1)
        FROM
            "ccp_mem"
                INNER JOIN "ccp_org" ON "ccp_mem"."org_code" = "ccp_org"."code"
        WHERE
            "ccp_org"."delete_time" IS NULL
          AND "ccp_org"."org_code" like CONCAT(#{orgCode},'%')
        GROUP BY "ccp_org"."org_code"
    </select>

    <select id="ypyPage" resultType="com.zenith.front.model.vo.YpyPageVO">
        SELECT
            "ccp_democratic_review_mem"."d69_code",
            "ccp_democratic_review_mem"."d69_name",
            "ccp_mem"."sex_name",
            "ccp_mem"."d08_name",
            "ccp_democratic_review_mem"."update_time",
            "ccp_mem"."name"
        FROM
            "ccp_democratic_review_mem"
                INNER JOIN "ccp_mem" ON "ccp_democratic_review_mem"."review_mem_code" = "ccp_mem"."code"
        WHERE
            "ccp_democratic_review_mem"."org_code" = #{orgId}
          AND "ccp_democratic_review_mem"."delete_time" IS NULL
          AND "ccp_democratic_review_mem"."year" = to_char(
                CURRENT_DATE,
                'yyyy'
            )
          and "ccp_democratic_review_mem"."update_time" is not null
    </select>

    <select id="wpyPage" resultType="com.zenith.front.model.vo.YpyPageVO">
        SELECT
            "ccp_democratic_review_mem"."d69_code",
            "ccp_democratic_review_mem"."d69_name",
            "ccp_mem"."sex_name",
            "ccp_mem"."d08_name",
            "ccp_democratic_review_mem"."update_time",
            "ccp_mem"."name"
        FROM
            "ccp_democratic_review_mem"
                INNER JOIN "ccp_mem" ON "ccp_democratic_review_mem"."review_mem_code" = "ccp_mem"."code"
        WHERE
            "ccp_democratic_review_mem"."org_code" = #{orgId}
          AND "ccp_democratic_review_mem"."delete_time" IS NULL
          AND "ccp_democratic_review_mem"."year" = to_char(
                CURRENT_DATE,
                'yyyy'
            )
          and "ccp_democratic_review_mem"."update_time" is null
    </select>

</mapper>
