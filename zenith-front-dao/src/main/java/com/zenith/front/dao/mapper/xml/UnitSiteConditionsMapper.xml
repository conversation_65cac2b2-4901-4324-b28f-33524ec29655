<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zenith.front.dao.mapper.unit.UnitSiteConditionsMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.zenith.front.model.bean.UnitSiteConditions">
        <id column="code" property="code" />
        <result column="es_id" property="esId" />
        <result column="time" property="time" />
        <result column="fiscal_funds" property="fiscalFunds" />
        <result column="party_expenses" property="partyExpenses" />
        <result column="activity_service_center" property="activityServiceCenter" />
        <result column="new_activity_service_center" property="newActivityServiceCenter" />
        <result column="unit_code" property="unitCode" />
        <result column="org_code" property="orgCode" />
        <result column="org_name" property="orgName" />
        <result column="create_time" property="createTime" />
        <result column="update_time" property="updateTime" />
        <result column="delete_time" property="deleteTime" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        code, es_id, time, fiscal_funds, party_expenses, activity_service_center, new_activity_service_center, unit_code, org_code, org_name, create_time, update_time, delete_time
    </sql>

</mapper>
