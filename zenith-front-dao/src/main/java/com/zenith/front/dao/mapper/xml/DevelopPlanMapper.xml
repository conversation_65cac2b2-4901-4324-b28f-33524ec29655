<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zenith.front.dao.mapper.develop.DevelopPlanMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.zenith.front.model.bean.DevelopPlan">
        <id column="id" property="id" />
        <result column="org_code" property="orgCode" />
        <result column="org_zb_code" property="orgZbCode" />
        <result column="year" property="year" />
        <result column="total_number" property="totalNumber" />
        <result column="used_number" property="usedNumber" />
        <result column="undone_number" property="undoneNumber" />
        <result column="create_time" property="createTime" />
        <result column="update_time" property="updateTime" />
        <result column="delete_time" property="deleteTime" />
        <result column="timestamp" property="timestamp" />
        <result column="code" property="code" />
        <result column="is_history" property="isHistory" />
        <result column="update_account" property="updateAccount" />
        <result column="unfinished_number" property="unfinishedNumber" />
        <result column="reality_unfinished_number" property="realityUnfinishedNumber" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, org_code, org_zb_code, year, total_number, used_number, undone_number, create_time, update_time, delete_time, timestamp, code, is_history, update_account, unfinished_number, reality_unfinished_number
    </sql>

    <select id="getIndexList" resultType="java.util.Map">
        SELECT ccp_org.code,
        ccp_org.org_code,
        ccp_org."name",
        ccp_develop_plan.total_number,
        ccp_develop_plan.used_number,
        ccp_develop_plan.undone_number,
        ccp_develop_plan."year"
        FROM ccp_org
        LEFT JOIN ccp_develop_plan ON ccp_org.code = ccp_develop_plan.org_code AND ccp_develop_plan."year" =
        (select cast(extract(YEAR from now()) as VARCHAR))
        WHERE
        ccp_org.delete_time is NULL
        and ccp_develop_plan.delete_time is NULL
        <choose>
            <when test="hasSubordinate !=null and hasSubordinate">
                and ccp_org.org_code = #{memOrgCode}
            </when>
            <otherwise>
                and ccp_org.org_code LIKE concat(#{memOrgCode}, '%')
            </otherwise>
        </choose>
        ORDER BY length(ccp_org.org_code), ccp_org.create_time DESC, ccp_org."id" DESC
    </select>

    <select id="findSubIndexCount" resultType="java.lang.Long">
        SELECT sum(ccp_develop_plan.total_number)
        FROM ccp_org
                 LEFT JOIN ccp_develop_plan ON ccp_org.code = ccp_develop_plan.org_code AND ccp_develop_plan."year" =
                                                                                            (select cast(extract(YEAR from now()) as VARCHAR))
        WHERE ccp_org.org_code != #{memOrgCode}
          and ccp_org.org_code like concat(#{memOrgCode}
            , '%')
          and ccp_org.delete_time is NULL
          and ccp_develop_plan.delete_time is NULL
    </select>

    <select id="selectListByOrgCode" resultType="com.zenith.front.model.bean.DevelopPlan">
        SELECT *
        FROM "ccp_develop_plan"
        WHERE org_code IN (SELECT code FROM ccp_org WHERE org_code like concat(#{orgCode}, '%'))
    </select>

</mapper>
