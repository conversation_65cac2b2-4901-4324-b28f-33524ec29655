package com.zenith.front.dao.mapper.dict;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.zenith.front.model.bean.*;
import com.zenith.front.model.vo.*;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;
import java.util.Map;

/**
 * <p>
 * Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2021-07-10
 */
public interface DictChartMapper extends BaseMapper<DictChart> {

    /**
     * 根据组织层级码查询党代表数
     */
    Long findDdbJobByOrgCode(@Param("orgCode") String orgCode);

    /**
     * 党组织书记数
     */
    Long findDzzSjByOrgCode(@Param("orgCode") String orgCode);

    /**
     * 村委成员
     */
    Long findUnitCommitteeByOrgCode(@Param("orgCode") String orgCode);

    @Select("SELECT count(1) from ccp_mem_report where module_type='1' and d25_code is not null and d04_code like '92%' and mem_org_code like CONCAT(#{orgCode}, '%')")
    Long findUnitCommittee(@Param("orgCode") String orgCode);

    /**
     * 村(社区)委主任
     */
    Long findUnitZrByOrgCode(@Param("orgCode") String orgCode);

    @Select("SELECT count(1) from ccp_mem_report where module_type='1' and d25_code in ('41','51') and d04_code like '92%' and mem_org_code like CONCAT(#{orgCode}, '%')")
    Long findUnitZr(@Param("orgCode") String orgCode);

    /**
     * 村支委成员
     */
    Long findCzwCommitteeByOrgCode(@Param("orgCode") String orgCode);

    @Select("SELECT count(1) from ccp_mem_report where module_type='1' and d022_code is not null and d04_code like '92%' and mem_org_code like CONCAT(#{orgCode}, '%')")
    Long findCzwCommittee(@Param("orgCode") String orgCode);

    /**
     * 村(社区)党组织书记
     */
    Long findCzwSjByOrgCode(@Param("orgCode") String orgCode);

    @Select("SELECT count(1) from ccp_mem_report where module_type='1' and d022_code='1' and d04_code like '92%' and mem_org_code like CONCAT(#{orgCode}, '%')")
    Long findCzwSj(@Param("orgCode") String orgCode);

    /**
     * 村(社区)两委成员
     */
    @Select("SELECT count(1) from ccp_mem_report where module_type='1' and d04_code like '92%' and mem_org_code like CONCAT(#{orgCode}, '%')")
    Long findCsqByOrgCode(@Param("orgCode") String orgCode);

    //    @Select("SELECT count(1) from ccp_mem_flow where delete_time is null and flow_add_type=2 and outflow_org_org_code like CONCAT(#{orgCode}, '%');")
    @Select("SELECT count(1) FROM mem_flow LEFT JOIN ccp_org ON mem_flow.in_org_code = ccp_org.code WHERE mem_flow.out_place_code NOT IN ('3', '4') and (mem_flow.flow_step = '2') AND ccp_org.delete_time IS NULL" +
            " and ((org_code like CONCAT(#{orgCode},'%') and in_org_code is not null) or ((out_org_branch_org_code like CONCAT(#{orgCode},'%') and in_org_code is null)))")
    Long findMemFlowIn(@Param("orgCode") String orgCode);

    //    @Select("SELECT count(1) from ccp_mem_flow where delete_time is null and flow_add_type=1 and flow_status!=2 and mem_org_org_code like CONCAT(#{orgCode}, '%');")
    @Select("SELECT count(1) from mem_flow where ((flow_step ='2' and flow_out = '1' and flow_in = '1') or (flow_step ='1' and flow_out = '1' and flow_in = '0')) and mem_org_org_code like CONCAT(#{orgCode}, '%');")
    Long findMemFlowOut(@Param("orgCode") String orgCode);

    @Select("SELECT count(1) from ccp_mem_reward t1 INNER JOIN ccp_mem on ccp_mem.code=t1.mem_code where reward_org_code like CONCAT(#{orgCode}, '%') and t1.delete_time is null AND t1.d029_code like 'C1%' " +
            "AND (( ccp_mem.delete_time IS NULL AND ( is_transfer <> 1 OR is_transfer IS NULL ) ) OR ( ccp_mem.delete_time IS NOT NULL AND d12_code LIKE'2%' ) )  and type=0 and to_char(start_date,'yyyy') = #{year} GROUP BY t1.mem_code")
    List<Map<String, Object>> findMemRewardCount(@Param("orgCode") String orgCode, @Param("year") String year);

    @Select("SELECT count(1) from ccp_mem_reward t1 INNER JOIN ccp_mem on ccp_mem.code=t1.mem_code where reward_org_code like CONCAT(#{orgCode}, '%') and t1.delete_time is null " +
            "AND ((is_transfer <> 1 OR is_transfer IS NULL) and d08_code in('1','2') AND d12_code='21' )  and type=0 and to_char(start_date,'yyyy') =#{year} GROUP BY t1.mem_code")
    List<Map<String, Object>> findMemRewardCount0(@Param("orgCode") String orgCode, @Param("year") String year);

    @Select("SELECT count(1) from ccp_mem_reward t1 where reward_org_code like CONCAT(#{orgCode}, '%') and t1.delete_time is null and type=1 and to_char(start_date,'yyyy') = #{year} GROUP BY t1.mem_code")
    List<Map<String, Object>> findMemRewardCount1(@Param("orgCode") String orgCode, @Param("year") String year);

    /**
     * 本年内党员参加培训人次
     * TODO 2024.12.06 今年统计只有党委跟党总支才会录入培训人次
     */
    @Select("SELECT COALESCE(sum(train_total),0) from ccp_mem_train where delete_time is null and org_org_code like CONCAT(#{orgCode}, '%') and  \"year\"=#{year} " +
            " and exists(SELECT 1 from ccp_org where ccp_org.code = ccp_mem_train.org_code and ccp_org.delete_time is null and (is_dissolve is null or is_dissolve!=1) and ccp_org.d01_code in ('61','62','911','921'));")
    Long findMemTrainCount(@Param("orgCode") String orgCode, @Param("year") Integer year);

    @Select("SELECT COALESCE(sum(to_number(level_party_class,'99999')),0) from ccp_mem_train where delete_time is null and org_org_code like CONCAT(#{orgCode}, '%') and  \"year\"=#{year};")
    Long findMemTrainCount2(@Param("orgCode") String orgCode, @Param("year") Integer year);

    @Select("SELECT COALESCE(sum(level_party_member),0) from ccp_mem_train where delete_time is null and org_org_code like CONCAT(#{orgCode}, '%') and  \"year\"=#{year};")
    Long findMemTrainCount3(@Param("orgCode") String orgCode, @Param("year") Integer year);

    @Select("SELECT count(1) from ccp_org_industry where delete_time is null and industry_org_code like CONCAT(#{orgCode}, '%');")
    Long findOrgIndustry(@Param("orgCode") String orgCode);

    @Select("SELECT count(1) FROM ccp_unit_countryside t1 inner join ccp_unit_all t2 on t1.unit_code = t2.code and t2.delete_time is null where t2.create_unit_org_code like CONCAT(#{orgCode}, '%') and t1.\"delete_time\" is null and t1.type='1'")
    Long findSQGZZ(@Param("orgCode") String orgCode);

    @Select("SELECT count(1) FROM ccp_unit_countryside t1 inner join ccp_unit_all t2 on t1.unit_code = t2.code and t2.delete_time is null where t2.create_unit_org_code like CONCAT(#{orgCode}, '%') and t1.\"delete_time\" is null and t1.type='2'")
    Long findSQHBGB(@Param("orgCode") String orgCode);

    @Select("SELECT COALESCE(sum(year_amount),0) from ccp_unit_all where delete_time is null and (create_unit_org_code like CONCAT(#{orgCode}, '%') or main_unit_org_code like CONCAT(#{orgCode}, '%'))")
    Double findYearAmount(@Param("orgCode") String orgCode);

    @Select("SELECT t1.* from ccp_org_committee_elect t1 INNER JOIN ccp_org on ccp_org.code=t1.org_code where elect_org_code like CONCAT(#{orgCode}, '%') and t1.delete_time is null and ccp_org.delete_time is null " +
            "and d01_code in('61','62','631','632','633','634','911','921','931','932') ORDER BY t1.tenure_end_date desc;")
    List<OrgCommitteeElect> findCommitteeElect(@Param("orgCode") String orgCode);

    /**
     * 党代表
     * @param page
     * @param orgCode
     * @return
     */
    Page<OrgPartyCongressCommitteeVO> getPartyRepresentative(@Param("page") Page<OrgPartyCongressCommitteeVO> page, @Param("orgCode") String orgCode);

    /**
     * 流入党员
     * @param page
     * @param orgCode
     * @return
     */
    Page<MemFlow1VO> getInflowParty(@Param("page") Page<MemFlow1VO> page, @Param("orgCode") String orgCode);

    /**
     * 流出党员
     * @param page
     * @param orgCode
     * @return
     */
    Page<MemFlow1VO> getFlowOutParty(@Param("page") Page<MemFlow1VO> page, @Param("orgCode") String orgCode);

    /**
     * 本年内党员参加培训人次
     */
    Page<ChartDataPeggListVo> getYearPartyJoinTrain(@Param("page") Page<ChartDataPeggListVo> page, @Param("year") Integer year, @Param("orgCode") String orgCode);

    List<ChartDataPeggListVo> exportYearPartyJoinTrain(@Param("year") Integer year, @Param("orgCode") String orgCode);

    /**
     * 本年内基层党委开展培训期数
     */
    Page<MemTrain> getYearTrainNumber(@Param("page") Page<MemTrain> page, @Param("year") Integer year, @Param("orgCode") String orgCode);

    /**
     * 本年内基层党委开展培训人次
     */
    Page<MemTrain> getYearTrainPeople(@Param("page") Page<MemTrain> page, @Param("year") Integer year, @Param("orgCode") String orgCode);

    /**
     * 本年内参加民主评议的党员
     * @param page
     * @param orgCode
     * @param year
     * @param lastYear
     * @return
     */
    Page<MemAllInfoVO>  getYearJoinDemocraticReviewParty(@Param("page") Page<MemAllInfoVO> page, @Param("orgCode") String orgCode, @Param("year") String year, @Param("lastYear") String lastYear);

    /**
     *
     * @param page
     * @param orgCode
     * @return
     */
    Page<OrgAllVO> getYearOrgInformation(@Param("page") Page<OrgAllVO> page, @Param("orgCode") String orgCode);

    /**
     *
     * @param page
     * @param orgCode
     * @return
     */
    Page<Unit> getTownCorporation(@Param("page") Page<Unit> page,@Param("orgCode") String orgCode);

    /***
     *
     * @param page
     * @param orgCode
     * @return
     */
    Page<Unit> getUrbanCommunityCorporation(@Param("page") Page<Unit> page,@Param("orgCode") String orgCode);

    /**
     *
     * @param page
     * @param orgCode
     * @return
     */
    Page<Unit> getTownCommunityCorporation(@Param("page") Page<Unit> page,@Param("orgCode") String orgCode);

    /**
     *
     * @param page
     * @param orgCode
     * @return
     */
    Page<Unit> getAdministrativeVillageCorporation(@Param("page") Page<Unit> page,@Param("orgCode") String orgCode);

}
