package com.zenith.front.dao.mapper.org;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.zenith.front.model.bean.OrgContactMem;
import com.zenith.front.model.bean.OrgContactWork;
import com.zenith.front.model.vo.CountVo;
import com.zenith.front.model.vo.OrgContactMemWorkVo;
import org.springframework.data.repository.query.Param;

import java.util.List;

/**
 * <p>
 * Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2022-12-14
 */
public interface OrgContactWorkMapper extends BaseMapper<OrgContactWork> {

    List<OrgContactWork> getContactWorkList(@Param("orgCode") String orgCode);

    Page<OrgContactMemWorkVo> getContactWorkPage(@Param("page") Page<OrgContactMemWorkVo> page, @Param("orgCode") String orgCode, @Param("d156Code") String d156Code);

    Page<OrgContactMemWorkVo> getContactWorkLePage(@Param("page") Page<OrgContactMemWorkVo> page, @Param("orgCode") String orgCode, @Param("d156Code") String d156Code, @Param("num") int num);

    List<CountVo> getContactWorkListByD156Code(@Param("orgCode") String orgCode, @Param("d156Code") String d156Code, @Param("year") String year);

    Page<OrgContactMemWorkVo> getContactWorkByPoint(@Param("page") Page<OrgContactMemWorkVo> page, @Param("orgCode") String orgCode, @Param("d156Code") String d156Code, @Param("year") String year, @Param("num") int num);
}
