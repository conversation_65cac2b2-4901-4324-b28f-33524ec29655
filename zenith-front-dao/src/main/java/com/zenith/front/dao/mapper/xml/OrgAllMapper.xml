<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zenith.front.dao.mapper.org.OrgAllMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.zenith.front.model.bean.OrgAll">
        <id column="id" property="id"/>
        <result column="code" property="code"/>
        <result column="es_id" property="esId"/>
        <result column="zb_code" property="zbCode"/>
        <result column="org_code" property="orgCode"/>
        <result column="name" property="name"/>
        <result column="short_name" property="shortName"/>
        <result column="pinyin" property="pinyin"/>
        <result column="parent_org_code" property="parentOrgCode"/>
        <result column="is_leaf" property="isLeaf"/>
        <result column="parent_name" property="parentName"/>
        <result column="parent_code" property="parentCode"/>
        <result column="d01_name" property="d01Name"/>
        <result column="d01_code" property="d01Code"/>
        <result column="org_type" property="orgType"/>
        <result column="d03_name" property="d03Name"/>
        <result column="d03_code" property="d03Code"/>
        <result column="d02_name" property="d02Name"/>
        <result column="d02_code" property="d02Code"/>
        <result column="is_retire" property="isRetire"/>
        <result column="is_flow" property="isFlow"/>
        <result column="secretary" property="secretary"
                typeHandler="com.zenith.front.common.encrypt.typehandler.EncryptTypeHandler"/>
        <result column="secretary_code" property="secretaryCode"/>
        <result column="contacter" property="contacter"
                typeHandler="com.zenith.front.common.encrypt.typehandler.EncryptTypeHandler"/>
        <result column="contact_phone" property="contactPhone"
                typeHandler="com.zenith.front.common.encrypt.typehandler.EncryptTypeHandler"/>
        <result column="fax_number" property="faxNumber"/>
        <result column="post_address" property="postAddress"/>
        <result column="post_code" property="postCode"/>
        <result column="units" property="units"/>
        <result column="create_date" property="createDate"/>
        <result column="sort" property="sort"/>
        <result column="create_time" property="createTime"/>
        <result column="update_time" property="updateTime"/>
        <result column="delete_time" property="deleteTime"/>
        <result column="keywords" property="keywords"/>
        <result column="remark" property="remark"/>
        <result column="address_position" property="addressPosition"/>
        <result column="timestamp" property="timestamp"/>
        <result column="ratio" property="ratio"/>
        <result column="main_unit_code" property="mainUnitCode"/>
        <result column="main_unit_type" property="mainUnitType"/>
        <result column="main_unit_name" property="mainUnitName"/>
        <result column="is_history" property="isHistory"/>
        <result column="elect_pople" property="electPople"/>
        <result column="elect_secretary" property="electSecretary"/>
        <result column="date" property="date"/>
        <result column="document_num" property="documentNum"/>
        <result column="reason" property="reason"/>
        <result column="is_dissolve" property="isDissolve"/>
        <result column="d04_code" property="d04Code"/>
        <result column="d04_name" property="d04Name"/>
        <result column="unit_code" property="unitCode"/>
        <result column="unit_name" property="unitName"/>
        <result column="d35_code" property="d35Code"/>
        <result column="d35_name" property="d35Name"/>
        <result column="d05_code" property="d05Code"/>
        <result column="d05_name" property="d05Name"/>
        <result column="situation" property="situation"/>
        <result column="congress_situation" property="congressSituation"/>
        <result column="representatives" property="representatives"/>
        <result column="party_committee" property="partyCommittee"/>
        <result column="standing_committee" property="standingCommittee"/>
        <result column="party_alternate_committee" property="partyAlternateCommittee"/>
        <result column="inspection_committee" property="inspectionCommittee"/>
        <result column="inspection_standing_committee" property="inspectionStandingCommittee"/>
        <result column="whole_situation" property="wholeSituation"/>
        <result column="life_situation" property="lifeSituation"/>
        <result column="attend_member" property="attendMember"/>
        <result column="participants" property="participants"/>
        <result column="reviewers_year" property="reviewersYear"/>
        <result column="has_join_reviewers" property="hasJoinReviewers"/>
        <result column="has_end_reviewers" property="hasEndReviewers"/>
        <result column="d16_code" property="d16Code"/>
        <result column="d16_name" property="d16Name"/>
        <result column="has_internal_institutions" property="hasInternalInstitutions"/>
        <result column="has_internal_institutions_transition" property="hasInternalInstitutionsTransition"/>
        <result column="year" property="year"/>
        <result column="train_total" property="trainTotal"/>
        <result column="school_train" property="schoolTrain"/>
        <result column="new_train" property="newTrain"/>
        <result column="youth_train" property="youthTrain"/>
        <result column="elderly_train" property="elderlyTrain"/>
        <result column="flow_train" property="flowTrain"/>
        <result column="laid_off_train" property="laidOffTrain"/>
        <result column="minority_areas_train" property="minorityAreasTrain"/>
        <result column="organization_secretary" property="organizationSecretary"/>
        <result column="county_party_committee" property="countyPartyCommittee"/>
        <result column="level_organizations_secretary" property="levelOrganizationsSecretary"/>
        <result column="community_party" property="communityParty"/>
        <result column="has_will_lesson" property="hasWillLesson"/>
        <result column="has_party_day" property="hasPartyDay"/>
        <result column="provincial_train_class" property="provincialTrainClass"/>
        <result column="provincial_train_member" property="provincialTrainMember"/>
        <result column="city_train_class" property="cityTrainClass"/>
        <result column="city_train_member" property="cityTrainMember"/>
        <result column="county_train_class" property="countyTrainClass"/>
        <result column="county_train_member" property="countyTrainMember"/>
        <result column="level_party_class" property="levelPartyClass"/>
        <result column="level_party_member" property="levelPartyMember"/>
        <result column="rural_party_villages" property="ruralPartyVillages"/>
        <result column="remote_education" property="remoteEducation"/>
        <result column="remote_education_villages" property="remoteEducationVillages"/>
        <result column="remote_education_administrative_village" property="remoteEducationAdministrativeVillage"/>
        <result column="remote_education_committee" property="remoteEducationCommittee"/>
        <result column="internet" property="internet"/>
        <result column="wired" property="wired"/>
        <result column="satellite" property="satellite"/>
        <result column="site_administrator" property="siteAdministrator"/>
        <result column="villages_cadres" property="villagesCadres"/>
        <result column="village_community" property="villageCommunity"/>
        <result column="volunteers" property="volunteers"/>
        <result column="rural_remote_education_party" property="ruralRemoteEducationParty"/>
        <result column="has_work_committee" property="hasWorkCommittee"/>
        <result column="has_working_body" property="hasWorkingBody"/>
        <result column="working_body_number" property="workingBodyNumber"/>
        <result column="manage_organization_number" property="manageOrganizationNumber"/>
        <result column="connect_organization_number" property="connectOrganizationNumber"/>
        <result column="fiscal_funds" property="fiscalFunds"/>
        <result column="party_expenses" property="partyExpenses"/>
        <result column="activity_service_center" property="activityServiceCenter"/>
        <result column="new_activity_service_center" property="newActivityServiceCenter"/>
        <result column="year_develop_mem" property="yearDevelopMem"/>
        <result column="year_develop_teacher" property="yearDevelopTeacher"/>
        <result column="year_develop_student" property="yearDevelopStudent"/>
        <result column="year_develop_graduate" property="yearDevelopGraduate"/>
        <result column="d109_code" property="d109Code"/>
        <result column="d109_name" property="d109Name"/>
        <result column="secretary_count" property="secretaryCount"/>
        <result column="year_develop_graduate_not_transfer" property="yearDevelopGraduateNotTransfer"/>
        <result column="lead_cadres_towns" property="leadCadresTowns"/>
        <result column="village_lead_cadres" property="villageLeadCadres"/>
        <result column="first_secretary_cadres" property="firstSecretaryCadres"/>
        <result column="village_leaders" property="villageLeaders"/>
        <result column="has_remove_income_less_5w" property="hasRemoveIncomeLess5w"/>
        <result column="has_over_fifty_no_income" property="hasOverFiftyNoIncome"/>
        <result column="has_annual_meet" property="hasAnnualMeet"/>
        <result column="d42_code" property="d42Code"/>
        <result column="year_active_mem" property="yearActiveMem"/>
        <result column="is_towns" property="isTowns"/>
        <result column="is_community" property="isCommunity"/>
        <result column="is_state_council" property="isStateCouncil"/>
        <result column="is_rural_expertise" property="isRuralExpertise"/>
        <result column="is_farmer_major" property="isFarmerMajor"/>
        <result column="is_family_farm" property="isFamilyFarm"/>
        <result column="year_develop_mem_medicine" property="yearDevelopMemMedicine"/>
        <result column="lockFields" property="lockFields" javaType="Object"
                typeHandler="com.zenith.front.common.jsonb.JsonbTypeHandler"/>
        <result column="is_approval_mem" property="isApprovalMem"/>
        <result column="is_czglbm" property="isCzglbm"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, code, es_id, zb_code, org_code, name, short_name, pinyin, parent_org_code, is_leaf, parent_name, parent_code, d01_name, d01_code, org_type, d03_name, d03_code, d02_name, d02_code, is_retire, is_flow, secretary, secretary_code, contacter, contact_phone, fax_number, post_address, post_code, units, create_date, sort, create_time, update_time, delete_time, keywords, remark, address_position, timestamp, ratio, main_unit_code, main_unit_type, main_unit_name, is_history, elect_pople, elect_secretary, date, document_num, reason, is_dissolve, d04_code, d04_name, unit_code, unit_name, d35_code, d35_name, d05_code, d05_name, situation, congress_situation, representatives, party_committee, standing_committee, party_alternate_committee, inspection_committee, inspection_standing_committee, whole_situation, life_situation, attend_member, participants, reviewers_year, has_join_reviewers, has_end_reviewers, d16_code, d16_name, has_internal_institutions, has_internal_institutions_transition, year, train_total, school_train, new_train, youth_train, elderly_train, flow_train, laid_off_train, minority_areas_train, organization_secretary, county_party_committee, level_organizations_secretary, community_party, has_will_lesson, has_party_day, provincial_train_class, provincial_train_member, city_train_class, city_train_member, county_train_class, county_train_member, level_party_class, level_party_member, rural_party_villages, remote_education, remote_education_villages, remote_education_administrative_village, remote_education_committee, internet, wired, satellite, site_administrator, villages_cadres, village_community, volunteers, rural_remote_education_party, has_work_committee, has_working_body, working_body_number, manage_organization_number, connect_organization_number, fiscal_funds, party_expenses, activity_service_center, new_activity_service_center, year_develop_mem, year_develop_teacher, year_develop_student, year_develop_graduate, d109_code, d109_name, secretary_count, year_develop_graduate_not_transfer, lead_cadres_towns, village_lead_cadres, first_secretary_cadres, village_leaders, has_remove_income_less_5w, has_over_fifty_no_income, has_annual_meet, d42_code, year_active_mem, is_towns, is_community, is_state_council, is_rural_expertise, is_farmer_major, is_family_farm, year_develop_mem_medicine, lock_fields, is_approval_mem, is_czglbm
    </sql>

    <update id="updateLockFields">
        UPDATE ccp_org_all
        SET lock_fields = #{lockFields,javaType=Object,typeHandler=com.zenith.front.common.jsonb.JsonbTypeHandler},
            update_time = now()
        WHERE lock_fields IS NOT NULL
    </update>

    <select id="getOrgSortList" resultMap="BaseResultMap">
        SELECT code, org_code, name, d01_name, contacter, contact_phone, secretary
        from ccp_org_all
        where org_code like CONCAT(#{orgCode}, '___')
          AND delete_time is null
          and (is_dissolve is null or is_dissolve!=1)
        ORDER BY length("org_code"), sort, "create_time" DESC, "id" DESC
    </select>

    <select id="configOrgAll" resultMap="BaseResultMap">
        SELECT aa.*
        FROM (SELECT code,
                     org_code,
                     d04_code,
                     d04_name,
                     main_unit_code
              FROM ccp_org_all
              WHERE delete_time IS NULL
                AND (main_unit_code IS NOT NULL AND main_unit_code != '')
                AND (is_dissolve = 0 OR is_dissolve IS NULL)
                AND d04_code IN ('131', '132', '513', '9121', '9122', '911', '921', '922', '923', '94', '95')
              ORDER BY org_code DESC) AS aa
                 LEFT JOIN ccp_unit ON aa.main_unit_code = ccp_unit.code
        WHERE ccp_unit.is_legal = 1
          and aa.org_code like CONCAT(#{orgCode}, '%')
        ORDER BY org_code
    </select>


    <select id="configOrgAllById" resultMap="BaseResultMap">
        SELECT aa.*
        FROM (SELECT code,
                     org_code,
                     d04_code,
                     d04_name,
                     main_unit_code
              FROM ccp_org_all
              WHERE delete_time IS NULL
                AND code = #{code}
                AND (main_unit_code IS NOT NULL AND main_unit_code != '')
                AND (is_dissolve = 0 OR is_dissolve IS NULL)
                AND d04_code IN ('131', '132', '513', '9121', '9122', '911', '921', '922', '923', '94', '95')
              ORDER BY org_code DESC) AS aa
                 LEFT JOIN ccp_unit ON aa.main_unit_code = ccp_unit.code
        WHERE ccp_unit.is_legal = 1
    </select>

    <select id="getVillageCommunityOrgList" resultMap="BaseResultMap">
        SELECT ccp_org_all.code,
               ccp_org_all.org_code,
               T.org_code AS parent_level_code,
               ccp_org_all.NAME,
               ccp_org_all.short_name,
               ccp_org_all.sort,
               CASE
                   WHEN length("ccp_org_all".org_code) = 3 THEN '2'
                   WHEN length("ccp_org_all".org_code) = 6 THEN '3'
                   WHEN length("ccp_org_all".org_code) = 9 THEN '4'
                   WHEN substr("ccp_org_all".d04_code, 1, 2) = '91' THEN '5'
                   WHEN substr("ccp_org_all".d04_code, 1, 2) = '92' THEN '6'
                   ELSE '-1' END AS org_type,
               ccp_org_all.create_time,
               ccp_org_all.update_time,
               ccp_org_all.parent_code,
               ccp_org_all.d01_code,
               ccp_org_all.d02_code,
               ccp_org_all.d03_code,
               ccp_org_all.d04_code,
               ccp_org_all.contacter,
               ccp_org_all.contact_phone,
               ccp_org_all.fax_number,
               ccp_org_all.post_address,
               ccp_org_all.post_code,
               ccp_org_all.main_unit_code,
               ccp_org_all.main_unit_name,
               ccp_org_all.is_approval_mem,
               ccp_org_all.d16_code
        FROM "ccp_org_all"
                 LEFT JOIN ccp_org_all T ON T.code = ccp_org_all.parent_code
        WHERE ccp_org_all.delete_time IS NULL
                AND ccp_org_all.d02_code NOT IN ('2','4')
                AND ((substr( "ccp_org_all".d04_code, 1, 1 ) = '4' AND substr( "ccp_org_all".d16_code, 1, 1 ) NOT IN ( '3', '4', '5' ))
                OR substr( "ccp_org_all".d04_code, 1, 1 ) NOT IN ( '4', '5' ))
    </select>

    <sql id="queryTwoVillageCommitteesCondition">
        AND d04_code IN ( '911', '9121', '9122', '922', '923' ) AND d02_code NOT IN ( '2', '4' )
    </sql>

    <select id="selectUnderlingByOrgCode" resultType="com.zenith.front.model.bean.OrgAll">
        SELECT name, code, org_code, d04_code, main_unit_name
        FROM ccp_org_all
        WHERE delete_time IS NULL
          <include refid="com.zenith.front.dao.mapper.org.OrgMapper.queryUnderlingCondition"/>
          <include refid="queryTwoVillageCommitteesCondition"/>
        ORDER BY char_length(org_code),sort,create_time DESC,id DESC
    </select>

    <select id="exportYearOrgInformation" resultType="com.zenith.front.model.vo.OrgAllVO">
        SELECT * FROM ccp_org_all
        WHERE delete_time IS NULL
          AND appraisal_org_code LIKE CONCAT ( #{ orgCode },'%' )
          AND d01_code IN ( '631', '632', '634', '931', '932' )
          AND has_join_reviewers = 1
          AND ( is_dissolve != 1 OR is_dissolve IS NULL )
    </select>
    <select id="getTopOrg" resultType="com.zenith.front.model.bean.OrgAll">
        select * from ccp_org_all where delete_time is null order by length(org_code) limit 1
    </select>
</mapper>
