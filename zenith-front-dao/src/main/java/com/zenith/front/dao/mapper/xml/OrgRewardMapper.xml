<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zenith.front.dao.mapper.org.OrgRewardMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.zenith.front.model.bean.OrgReward">
        <id column="id" property="id" />
        <result column="code" property="code" />
        <result column="es_id" property="esId" />
        <result column="d42_code" property="d42Code" />
        <result column="d42_name" property="d42Name" />
        <result column="org_code" property="orgCode" />
        <result column="reward_org_code" property="rewardOrgCode" />
        <result column="d47_code" property="d47Code" />
        <result column="d47_name" property="d47Name" />
        <result column="start_date" property="startDate" />
        <result column="remark" property="remark" />
        <result column="type" property="type" />
        <result column="commendation" property="commendation" />
        <result column="commendation_name" property="commendationName" />
        <result column="timestamp" property="timestamp" />
        <result column="delete_time" property="deleteTime" />
        <result column="create_time" property="createTime" />
        <result column="update_time" property="updateTime" />
        <result column="zb_code" property="zbCode" />
        <result column="is_history" property="isHistory" />
        <result column="update_account" property="updateAccount" />
        <result column="approve_organ" property="approveOrgan" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, code, es_id, d42_code, d42_name, org_code, reward_org_code, d47_code, d47_name, start_date, remark, type, commendation, commendation_name, timestamp, delete_time, create_time, update_time, zb_code, is_history, update_account, approve_organ
    </sql>

</mapper>
