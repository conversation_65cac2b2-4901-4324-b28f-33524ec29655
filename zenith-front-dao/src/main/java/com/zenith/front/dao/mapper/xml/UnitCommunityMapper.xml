<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zenith.front.dao.mapper.unit.UnitCommunityMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.zenith.front.model.bean.UnitCommunity">
        <id column="id" property="id"/>
        <result column="code" property="code"/>
        <result column="es_id" property="esId"/>
        <result column="unit_code" property="unitCode"/>
        <result column="year" property="year"/>
        <result column="first_secretary_select" property="firstSecretarySelect"/>
        <result column="first_secretary_code" property="firstSecretaryCode"/>
        <result column="first_secretary_name" property="firstSecretaryName"
                typeHandler="com.zenith.front.common.encrypt.typehandler.EncryptTypeHandler"/>
        <result column="has_thousand" property="hasThousand"/>
        <result column="has_bundled" property="hasBundled"/>
        <result column="promoted_num" property="promotedNum"/>
        <result column="adjusted_num" property="adjustedNum"/>
        <result column="operating_expenses" property="operatingExpenses"/>
        <result column="village_per" property="villagePer"/>
        <result column="secretary_salary" property="secretarySalary"/>
        <result column="space_area" property="spaceArea"/>
        <result column="new_expand_area" property="newExpandArea"/>
        <result column="secretary_party_num" property="secretaryPartyNum"/>
        <result column="secretary_promoted_num" property="secretaryPromotedNum"/>
        <result column="community_money_num" property="communityMoneyNum"/>
        <result column="community_serving_people" property="communityServingPeople"/>
        <result column="community_masses" property="communityMasses"/>
        <result column="secretary_training_num" property="secretaryTrainingNum"/>
        <result column="create_time" property="createTime"/>
        <result column="update_time" property="updateTime"/>
        <result column="delete_time" property="deleteTime"/>
        <result column="timestamp" property="timestamp"/>
        <result column="update_account" property="updateAccount"/>
        <result column="has_first_secretary" property="hasFirstSecretary"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, code, es_id, unit_code, year, first_secretary_select, first_secretary_code, first_secretary_name, has_thousand, has_bundled, promoted_num, adjusted_num, operating_expenses, village_per, secretary_salary, space_area, new_expand_area, secretary_party_num, secretary_promoted_num, community_money_num, community_serving_people, community_masses, secretary_training_num, create_time, update_time, delete_time, timestamp, update_account, has_first_secretary
    </sql>

    <select id="selectListByOrgCode" resultMap="BaseResultMap">
        SELECT *
        FROM "ccp_unit_community"
        WHERE unit_code IN (SELECT code FROM ccp_unit WHERE create_unit_org_code LIKE CONCAT(#{ orgCode }, '%'))
    </select>
</mapper>
