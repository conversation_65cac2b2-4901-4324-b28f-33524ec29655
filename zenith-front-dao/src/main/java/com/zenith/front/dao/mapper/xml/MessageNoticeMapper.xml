<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zenith.front.dao.mapper.message.MessageNoticeMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.zenith.front.model.bean.MessageNotice">
        <id column="id" property="id"/>
        <result column="code" property="code"/>
        <result column="reply_context" property="replyContext"/>
        <result column="is_select" property="isSelect"/>
        <result column="notice_plan" property="noticePlan"/>
        <result column="file" property="file"/>
        <result column="time" property="time"/>
        <result column="message_code" property="messageCode"/>
        <result column="notice_message_code" property="noticeMessageCode"/>
        <result column="message_type" property="messageType"/>
        <result column="receive_code" property="receiveCode"/>
        <result column="receive_org_code" property="receiveOrgCode"/>
        <result column="create_time" property="createTime"/>
        <result column="update_time" property="updateTime"/>
        <result column="delete_time" property="deleteTime"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, code, reply_context, is_select, notice_plan, file, time, message_code, notice_message_code, message_type, receive_code, receive_org_code, create_time, update_time, delete_time
    </sql>

    <select id="getListByCode" resultType="com.zenith.front.model.vo.MessageTypeCountVo">
        SELECT message_type,count(message_type)as total
        FROM ccp_message_notice
        WHERE delete_time IS NULL AND receive_code=#{code}
        AND is_select='0' GROUP BY message_type
    </select>

</mapper>
