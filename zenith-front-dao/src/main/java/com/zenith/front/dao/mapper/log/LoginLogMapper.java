package com.zenith.front.dao.mapper.log;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.zenith.front.model.bean.LoginLog;
import org.apache.ibatis.annotations.Param;

/**
 * <p>
 * Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2021-07-10
 */
public interface LoginLogMapper extends BaseMapper<LoginLog> {

    Page<LoginLog> findByPage(@Param("page") Page<LoginLog> page, @Param("code") String code, @Param("keyWord") String keyWord);
}
