<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zenith.front.dao.mapper.table.TableRulerMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.zenith.front.model.bean.TableRuler">
        <id column="id" property="id" />
        <result column="table" property="table" />
        <result column="table_name" property="tableName" />
        <result column="ruler_type" property="rulerType" />
        <result column="remark" property="remark" />
        <result column="ruler" property="ruler" />
        <result column="is_del" property="isDel" />
        <result column="createTime" property="createTime" />
        <result column="type" property="type" />
        <result column="filter" property="filter" />
        <result column="front_data" property="frontData" />
        <result column="uuid" property="uuid" />
        <result column="isRowAll" property="isRowAll" />
        <result column="isColumnAll" property="isColumnAll" />
        <result column="table_ruler" property="tableRuler" />
        <result column="connection_table" property="connectionTable" />
        <result column="spit_row" property="spitRow" />
        <result column="spit_column" property="spitColumn" />
        <result column="table_type" property="tableType" />
        <result column="is_forcibly" property="isForcibly" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, table, table_name, ruler_type, remark, ruler, is_del, createTime, type, filter, front_data, uuid, isRowAll, isColumnAll, table_ruler, connection_table, spit_row, spit_column, table_type, is_forcibly
    </sql>

</mapper>
