package com.zenith.front.dao.mapper.unit;

import com.zenith.front.model.bean.UnitReport;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2022-04-13
 */
public interface UnitReportMapper extends BaseMapper<UnitReport> {

    /**
     * 根据组织层级码查询单位报表信息
     *
     * @param orgCode 组织层级码
     * @return
     */
    List<UnitReport> selectListByOrgCode(@Param("orgCode") String orgCode);
}
