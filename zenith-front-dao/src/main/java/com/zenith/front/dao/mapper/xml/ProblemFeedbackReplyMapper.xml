<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zenith.front.dao.mapper.problemfeedback.ProblemFeedbackReplyMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.zenith.front.model.bean.ProblemFeedbackReply">
        <id column="code" property="code" />
        <result column="problem_id" property="problemId" />
        <result column="user_id" property="userId" />
        <result column="user_name" property="userName" />
        <result column="content" property="content" />
        <result column="item_id" property="itemId" />
        <result column="reply_sup_id" property="replySupId" />
        <result column="reply_user_id" property="replyUserId" />
        <result column="reply_user_name" property="replyUserName" />
        <result column="reply_time" property="replyTime" />
        <result column="create_time" property="createTime" />
        <result column="root_code" property="rootCode" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        code, problem_id, user_id, user_name, content, item_id, reply_sup_id, reply_user_id, reply_user_name, reply_time, create_time, root_code
    </sql>

</mapper>
