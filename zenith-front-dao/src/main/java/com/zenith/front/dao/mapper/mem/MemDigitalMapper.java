package com.zenith.front.dao.mapper.mem;


import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.zenith.front.model.bean.MemDigital;
import com.zenith.front.model.dto.DigitalDataDTO;
import com.zenith.front.model.vo.MemDigitalDataVO;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.Collection;
import java.util.List;
import java.util.Map;

/**
 * <p>
 * 党员（发展党员）数字档案表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025-02-14
 */
public interface MemDigitalMapper extends BaseMapper<MemDigital> {

    Integer selectDigitalCount(@Param("digitalLotNo") String digitalLotNo, @Param("processNode") String processNode, @Param("d222Code") String d222Code);

    Integer myInserBatch(Collection<MemDigital> entityList);

    /**
     * 根据档案批次码分类查询最高排序号
     *
     * @param digitalLotNo 档案批次唯一码
     * @return 各类别最高排序号
     */
    @Select("SELECT d222_code,COALESCE(max(sort),0) sort from ccp_mem_digital WHERE digital_lot_no = #{digitalLotNo} and delete_time is null and d222_code is not null GROUP BY d222_code")
    List<Map<String, Object>> selectD222AllCount(@Param("digitalLotNo") String digitalLotNo);

    /**
     * 获取党员信息档案
     *
     * @param digitalDataDTO 请求参数
     * @return 党员档案列表
     */
    List<MemDigitalDataVO> getMemDigitalData(@Param("dto") DigitalDataDTO digitalDataDTO);

    /**
     * 获取发展党员档案信息
     *
     * @param digitalDataDTO 请求参数
     * @return 发展党员档案列表
     */
    List<MemDigitalDataVO> getDevelopMemDigitalData(@Param("dto") DigitalDataDTO digitalDataDTO);
}
