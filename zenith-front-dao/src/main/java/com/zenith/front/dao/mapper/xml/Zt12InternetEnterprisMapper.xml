<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zenith.front.mapper.Zt12InternetEnterprisMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.zenith.front.model.bean.Zt12InternetEnterpris">
        <id column="code" property="code" />
        <result column="unit_code" property="unitCode" />
        <result column="build_unite_branch_num" property="buildUniteBranchNum" />
        <result column="above_bachelor_num" property="aboveBachelorNum" />
        <result column="above_graduate_num" property="aboveGraduateNum" />
        <result column="party_num" property="partyNum" />
        <result column="current_year_develop_num" property="currentYearDevelopNum" />
        <result column="not_turned_relation_num" property="notTurnedRelationNum" />
        <result column="secretary_num" property="secretaryNum" />
        <result column="secretary_hold_middle_manager_num" property="secretaryHoldMiddleManagerNum" />
        <result column="is_principal_party" property="isPrincipalParty" />
        <result column="create_time" property="createTime" />
        <result column="update_time" property="updateTime" />
        <result column="delete_time" property="deleteTime" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        code, unit_code, build_unite_branch_num, above_bachelor_num, above_graduate_num, party_num, current_year_develop_num, not_turned_relation_num, secretary_num, secretary_hold_middle_manager_num, is_principal_party, create_time, update_time, delete_time
    </sql>

</mapper>
