<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zenith.front.dao.mapper.transfer.TransferEffectMemsMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.zenith.front.model.bean.TransferEffectMems">
        <id column="id" property="id" />
        <result column="record_id" property="recordId" />
        <result column="mem_id" property="memId" />
        <result column="mem_name" property="memName" />
        <result column="org_id" property="orgId" />
        <result column="org_code" property="orgCode" />
        <result column="org_name" property="orgName" />
        <result column="create_time" property="createTime" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, record_id, mem_id, mem_name, org_id, org_code, org_name, create_time
    </sql>

    <select id="selectListByOrgCode" resultType="com.zenith.front.model.bean.TransferEffectMems">
        SELECT *
        from ccp_transfer_effect_mems
        WHERE record_id in (SELECT record_id
                            FROM "ccp_transfer_approval"
                            WHERE org_id IN (SELECT code FROM ccp_org WHERE org_code LIKE concat(#{orgCode}, '%')))
    </select>

</mapper>
