package com.zenith.front.dao.mapper.org;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.zenith.front.model.bean.OrgReward;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

/**
 * <p>
 *  Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2021-07-10
 */
public interface OrgRewardMapper extends BaseMapper<OrgReward> {

    @Select("${sql}")
    Integer getOrgCommend(@Param("sql") String sql);

    int saveBatchTmp(OrgReward orgReward);
}
