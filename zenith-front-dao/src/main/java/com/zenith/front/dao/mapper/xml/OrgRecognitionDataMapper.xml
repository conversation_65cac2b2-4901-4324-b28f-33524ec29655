<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zenith.front.dao.mapper.org.OrgRecognitionDataMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.zenith.front.model.bean.OrgRecognitionData">
        <id column="code" property="code"/>
        <result column="recognition_code" property="recognitionCode"/>
        <result column="recognition_object" property="recognitionObject"/>
        <result column="recognition_level" property="recognitionLevel"/>
        <result column="number" property="number"/>
        <result column="delete_time" property="deleteTime"/>
        <result column="create_time" property="createTime"/>
        <result column="update_time" property="updateTime"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        code, recognition_code, recognition_object, recognition_level, number, delete_time, create_time, update_time
    </sql>

    <select id="selectListByOrgCode" resultType="com.zenith.front.model.bean.OrgRecognitionData">
        SELECT *
        FROM ccp_org_recognition_data
        WHERE recognition_code IN
              (SELECT code FROM "ccp_org_recognition" WHERE recognition_org_code like CONCAT(#{orgCode}, '%'))
    </select>

</mapper>
