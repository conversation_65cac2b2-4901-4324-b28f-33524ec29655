package com.zenith.front.dao.mapper.codetable;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.zenith.front.model.modelview.CodeTableConfigView;
import com.zenith.front.model.vo.DecryptMap;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

/**
 * <AUTHOR>
 */
public interface CodeTableConfigViewMapper extends BaseMapper<CodeTableConfigView> {

    /**
     * 查询数据校核数量
     *
     * @param sql 查询sql
     * @return
     */
    @Select("${sql}")
    int selectCheckCount(String sql);

    /**
     * 分页查询校核结果
     *
     * @param page 分页
     * @param sql  查询sql
     * @return
     */
    @Select("${sql}")
    Page<DecryptMap<String, Object>> pageMap(@Param("page") Page page, String sql);
}




