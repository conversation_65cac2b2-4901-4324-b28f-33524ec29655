<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zenith.front.dao.mapper.activist.ActivistTransferRecordMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.zenith.front.model.bean.ActivistTransferRecord">
        <id column="id" property="id"/>
        <result column="user_id" property="userId"/>
        <result column="name" property="name"/>
        <result column="mem_id" property="memId"/>
        <result column="src_org_id" property="srcOrgId"/>
        <result column="src_org_name" property="srcOrgName"/>
        <result column="target_org_id" property="targetOrgId"/>
        <result column="target_org_name" property="targetOrgName"/>
        <result column="common_org_id" property="commonOrgId"/>
        <result column="common_org_name" property="commonOrgName"/>
        <result column="src_org_relation" property="srcOrgRelation"/>
        <result column="target_org_relation" property="targetOrgRelation"/>
        <result column="out_type" property="outType"/>
        <result column="in_type" property="inType"/>
        <result column="type" property="type"/>
        <result column="current_approval_id" property="currentApprovalId"/>
        <result column="status" property="status"/>
        <result column="reason" property="reason"/>
        <result column="create_time" property="createTime"/>
        <result column="update_time" property="updateTime"/>
        <result column="extra_data" property="extraData"/>
        <result column="letter_url" property="letterUrl"/>
        <result column="effect_mems" property="effectMems"/>
        <result column="d92_code" property="d92Code"/>
        <result column="d92_name" property="d92Name"/>
        <result column="remark" property="remark"/>
        <result column="src_org_relation_rel" property="srcOrgRelationRel"/>
        <result column="target_org_relation_rel" property="targetOrgRelationRel"/>
        <result column="whether_extend_prep_period" property="whetherExtendPrepPeriod"/>
        <result column="data_text" property="dataText"/>
        <result column="transfer_out_time" property="transferOutTime"/>
        <result column="d146_code" property="d146Code"/>
        <result column="d146_name" property="d146Name"/>
        <result column="report_time" property="reportTime"/>
        <result column="out_d04_code" property="outD04Code"/>
        <result column="extra_data_log" property="extraDataLog"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, user_id, name, mem_id, src_org_id, src_org_name, target_org_id, target_org_name, common_org_id, common_org_name, src_org_relation, target_org_relation, out_type, in_type, type, current_approval_id, status, reason, create_time, update_time, extra_data, letter_url, effect_mems, d92_code, d92_name, remark, src_org_relation_rel, target_org_relation_rel, whether_extend_prep_period, data_text, transfer_out_time, d146_code, d146_name, report_time, out_d04_code, extra_data_log
    </sql>

    <select id="findOutByPage" resultMap="BaseResultMap">
        SELECT ccp_activist_transfer_record.*, ccp_org.org_code
        FROM ccp_activist_transfer_record
                 LEFT JOIN ccp_org ON ccp_activist_transfer_record.src_org_id::text = ccp_org.code::text
        WHERE ccp_org.delete_time IS NULL AND ${sql}
    </select>

    <select id="findInByPage" resultMap="BaseResultMap">
        SELECT ccp_activist_transfer_record.*, ccp_org.org_code
        FROM ccp_activist_transfer_record
                 LEFT JOIN ccp_org ON ccp_activist_transfer_record.target_org_id::text = ccp_org.code::text
        WHERE ccp_org.delete_time IS NULL AND ${sql}
    </select>

    <select id="findOutTotalByConditon" resultType="java.lang.Long">
        SELECT count(1)
        FROM ccp_activist_transfer_record
                 LEFT JOIN ccp_org ON ccp_activist_transfer_record.src_org_id::text = ccp_org.code::text
        WHERE ccp_org.delete_time IS NULL AND ${sql}
    </select>

    <select id="findInTotalByConditon" resultType="java.lang.Long">
        SELECT count(1)
        FROM ccp_activist_transfer_record
                 LEFT JOIN ccp_org ON ccp_activist_transfer_record.target_org_id::text = ccp_org.code::text
        WHERE ccp_org.delete_time IS NULL AND ${sql}
    </select>

    <select id="findOutMessage" resultType="com.zenith.front.model.vo.ActivistsTransferOutMessageVO">
        SELECT ccp_activist_transfer_record.name,
               ccp_activist_transfer_record.create_time,
               ccp_org."name"                             AS orgName,
               ccp_activist_transfer_approval.next_org_id as orgCode
        FROM ccp_activist_transfer_record
                 LEFT JOIN ccp_org ON ccp_activist_transfer_record.src_org_id :: TEXT = ccp_org.code :: TEXT
                 LEFT JOIN ccp_activist_transfer_approval
        ON ccp_activist_transfer_approval.ID :: TEXT = ccp_activist_transfer_record.current_approval_id :: TEXT
        WHERE
            ccp_org.delete_time IS NULL
          AND ccp_org."code" IS NOT NULL
          AND "type" != '29'
          AND out_type LIKE '2%'
          AND ccp_activist_transfer_record.status = 0
    </select>

    <select id="findInMessage" resultType="com.zenith.front.model.vo.ActivistsTransferInMessageVO">
        SELECT ccp_activist_transfer_record.name,
               ccp_activist_transfer_record.create_time,
               ccp_org."name"                             AS orgName,
               ccp_activist_transfer_approval.next_org_id as orgCode
        FROM ccp_activist_transfer_record
                 LEFT JOIN ccp_org ON ccp_activist_transfer_record.target_org_id :: TEXT = ccp_org.code ::TEXT
                 LEFT JOIN ccp_activist_transfer_approval
        ON ccp_activist_transfer_approval.ID :: TEXT = ccp_activist_transfer_record.current_approval_id :: TEXT
        WHERE
            ccp_org.delete_time IS NULL
          AND "type" != '29'
          AND in_type LIKE '1%'
          AND ccp_activist_transfer_record.status = 0
    </select>

</mapper>
