<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zenith.front.dao.mapper.task.TaskExecutionMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.zenith.front.model.bean.TaskExecution">
        <id column="task_execution_id" property="taskExecutionId" />
        <result column="start_time" property="startTime" />
        <result column="end_time" property="endTime" />
        <result column="task_name" property="taskName" />
        <result column="exit_code" property="exitCode" />
        <result column="exit_message" property="exitMessage" />
        <result column="error_message" property="errorMessage" />
        <result column="last_updated" property="lastUpdated" />
        <result column="external_execution_id" property="externalExecutionId" />
        <result column="parent_execution_id" property="parentExecutionId" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        task_execution_id, start_time, end_time, task_name, exit_code, exit_message, error_message, last_updated, external_execution_id, parent_execution_id
    </sql>

</mapper>
