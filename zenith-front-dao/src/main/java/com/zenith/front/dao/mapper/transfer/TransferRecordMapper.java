package com.zenith.front.dao.mapper.transfer;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.zenith.front.model.bean.TransferRecord;
import com.zenith.front.model.vo.OptionVO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2021-07-10
 */
public interface TransferRecordMapper extends BaseMapper<TransferRecord> {

    List<TransferRecord> findRecordByOrgId(@Param("orgId") String orgId);

    OptionVO getTransfer(@Param("code") String code);

    OptionVO getTransferOut(@Param("code") String code);

    OptionVO getTransferIn(@Param("code") String code);

    OptionVO getTransferOrg(@Param("code") String code);

    OptionVO getTransferOrgMem(@Param("code") String code);

    /**
     * 查询转接记录中存在的重复数据
     *
     * @return 重复数据code
     */
    List<String> checkDuplicateRecord();

    /**
     * 删除重复记录
     *
     * @param id code
     */
    void removalDuplicate(@Param("id") String id);
}
