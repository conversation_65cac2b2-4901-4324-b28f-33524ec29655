<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zenith.front.dao.mapper.org.OrgSlackAllMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.zenith.front.model.bean.OrgSlackAll">
        <id column="id" property="id" />
        <result column="code" property="code" />
        <result column="zb_code" property="zbCode" />
        <result column="org_code" property="orgCode" />
        <result column="slack_org_code" property="slackOrgCode" />
        <result column="name" property="name" />
        <result column="neaten_time" property="neatenTime" />
        <result column="year" property="year" />
        <result column="has_neaten" property="hasNeaten" />
        <result column="neaten_endtime" property="neatenEndtime" />
        <result column="d74_code1" property="d74Code1" />
        <result column="d74_code2" property="d74Code2" />
        <result column="d74_code3" property="d74Code3" />
        <result column="d74_code4" property="d74Code4" />
        <result column="d74_code5" property="d74Code5" />
        <result column="d74_code6" property="d74Code6" />
        <result column="d74_code7" property="d74Code7" />
        <result column="d74_code8" property="d74Code8" />
        <result column="d74_code9" property="d74Code9" />
        <result column="d74_code10" property="d74Code10" />
        <result column="d74_code11" property="d74Code11" />
        <result column="early_qp_secretary" property="earlyQpSecretary" />
        <result column="has_year_selected" property="hasYearSelected" />
        <result column="early_tz_secretary" property="earlyTzSecretary" />
        <result column="has_year_adjust" property="hasYearAdjust" />
        <result column="train_secretary" property="trainSecretary" />
        <result column="lc_county_level_leader" property="lcCountyLevelLeader" />
        <result column="bc_county_level_leader" property="bcCountyLevelLeader" />
        <result column="first_secretary" property="firstSecretary" />
        <result column="jdbf_county_level_unit" property="jdbfCountyLevelUnit" />
        <result column="two_levels_listed" property="twoLevelsListed" />
        <result column="special_rectification" property="specialRectification" />
        <result column="solve_problems" property="solveProblems" />
        <result column="look_into_laws" property="lookIntoLaws" />
        <result column="create_time" property="createTime" />
        <result column="update_time" property="updateTime" />
        <result column="delete_time" property="deleteTime" />
        <result column="d04_code" property="d04Code" />
        <result column="d04_name" property="d04Name" />
        <result column="unit_code" property="unitCode" />
        <result column="unit_name" property="unitName" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, code, zb_code, org_code, slack_org_code, name, neaten_time, year, has_neaten, neaten_endtime, d74_code1, d74_code2, d74_code3, d74_code4, d74_code5, d74_code6, d74_code7, d74_code8, d74_code9, d74_code10, d74_code11, early_qp_secretary, has_year_selected, early_tz_secretary, has_year_adjust, train_secretary, lc_county_level_leader, bc_county_level_leader, first_secretary, jdbf_county_level_unit, two_levels_listed, special_rectification, solve_problems, look_into_laws, create_time, update_time, delete_time, d04_code, d04_name, unit_code, unit_name
    </sql>

</mapper>
