<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zenith.front.dao.mapper.trend.TrendMapper">


    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.zenith.front.model.bean.Trend">
        <id column="id" property="id" />
        <result column="code" property="code" />
        <result column="tittle" property="tittle" />
        <result column="context" property="context" />
        <result column="type" property="type" />
        <result column="select_type" property="selectType" />
        <result column="push_status" property="pushStatus" />
        <result column="select_count" property="selectCount" />
        <result column="praise_count" property="praiseCount" />
        <result column="collect_count" property="collectCount" />
        <result column="is_notice" property="isNotice" />
        <result column="is_cancel" property="isCancel" />
        <result column="files" property="files" />
        <result column="create_time" property="createTime" />
        <result column="update_time" property="updateTime" />
        <result column="delete_time" property="deleteTime" />
        <result column="create_org_code" property="createOrgCode" />
        <result column="create_org_org_code" property="createOrgOrgCode" />
        <result column="create_org_set" property="createOrgSet" />
        <result column="is_portal" property="isPortal" />
        <result column="create_account" property="createAccount" />
        <result column="check_person" property="checkPerson" />
        <result column="title_photo" property="titlePhoto" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, code, tittle, context, type, select_type, push_status, select_count, praise_count, collect_count, is_notice, is_cancel, files, create_time, update_time, delete_time, create_org_code, create_org_org_code, create_org_set, is_portal, create_account, check_person, title_photo
    </sql>
</mapper>
