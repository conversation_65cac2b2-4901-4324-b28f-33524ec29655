<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zenith.front.dao.mapper.org.OrgPartyCongressCommitteeAllMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.zenith.front.model.bean.OrgPartyCongressCommitteeAll">
        <id column="id" property="id" />
        <result column="code" property="code" />
        <result column="es_id" property="esId" />
        <result column="mem_code" property="memCode" />
        <result column="mem_name" property="memName" />
        <result column="mem_idcard" property="memIdcard" />
        <result column="sex_code" property="sexCode" />
        <result column="sex_name" property="sexName" />
        <result column="birthday" property="birthday" />
        <result column="d07_code" property="d07Code" />
        <result column="d07_name" property="d07Name" />
        <result column="mem_type_code" property="memTypeCode" />
        <result column="mem_type_name" property="memTypeName" />
        <result column="start_date" property="startDate" />
        <result column="end_date" property="endDate" />
        <result column="d105_code" property="d105Code" />
        <result column="d105_name" property="d105Name" />
        <result column="d106_code" property="d106Code" />
        <result column="d106_name" property="d106Name" />
        <result column="elect_code" property="electCode" />
        <result column="zb_code" property="zbCode" />
        <result column="org_code" property="orgCode" />
        <result column="position_org_code" property="positionOrgCode" />
        <result column="position_org_name" property="positionOrgName" />
        <result column="is_history" property="isHistory" />
        <result column="create_time" property="createTime" />
        <result column="update_time" property="updateTime" />
        <result column="delete_time" property="deleteTime" />
        <result column="timestamp" property="timestamp" />
        <result column="update_account" property="updateAccount" />
        <result column="d124_code" property="d124Code" />
        <result column="d124_name" property="d124Name" />
        <result column="is_province_party" property="isProvinceParty" />
        <result column="is_city_party" property="isCityParty" />
        <result column="is_county_party" property="isCountyParty" />
        <result column="is_town_party" property="isTownParty" />
        <result column="d01_code" property="d01Code" />
        <result column="d06_code" property="d06Code" />
        <result column="age" property="age" />
        <result column="d09_code" property="d09Code" />
        <result column="slot" property="slot" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, code, es_id, mem_code, mem_name, mem_idcard, sex_code, sex_name, birthday, d07_code, d07_name, mem_type_code, mem_type_name, start_date, end_date, d105_code, d105_name, d106_code, d106_name, elect_code, zb_code, org_code, position_org_code, position_org_name, is_history, create_time, update_time, delete_time, timestamp, update_account, d124_code, d124_name, is_province_party, is_city_party, is_county_party, is_town_party, d01_code, d06_code, age, d09_code, slot
    </sql>

    <update id="updateInfo">
        UPDATE ccp_org_party_congress_committee_all
        SET delete_time = T.delete_time,
            d105_code = T.d105_code
        FROM
            ccp_org_party_congress_committee T
        WHERE
            T.code = ccp_org_party_congress_committee_all.code;
    </update>

    <update id="updateInfo1">
        UPDATE "ccp_org_party_congress_committee_all"
        SET mem_name = T.NAME,
            mem_idcard = T.idcard,
            sex_code = T.sex_code,
            sex_name = T.sex_name,
            birthday = T.birthday,
            age = T.age,
            d06_code = T.d06_code,
            d07_code = T.d07_code,
            d07_name = T.d07_name,
            d124_code = T.d09_code,
            d124_name = T.d09_name
        FROM
            ccp_mem T
        WHERE
            T.code = ccp_org_party_congress_committee_all.mem_code;
    </update>

</mapper>
