<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zenith.front.dao.mapper.unit.UnitCommitteeMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.zenith.front.model.bean.UnitCommittee">
        <id column="id" property="id" />
        <result column="code" property="code" />
        <result column="es_id" property="esId" />
        <result column="mem_code" property="memCode" />
        <result column="mem_name" property="memName" typeHandler="com.zenith.front.common.encrypt.typehandler.EncryptTypeHandler"/>
        <result column="d25_code" property="d25Code" />
        <result column="d25_name" property="d25Name" />
        <result column="file_number" property="fileNumber" />
        <result column="start_date" property="startDate" />
        <result column="end_date" property="endDate" />
        <result column="is_incumbent" property="isIncumbent" />
        <result column="d26_code" property="d26Code" />
        <result column="d26_name" property="d26Name" />
        <result column="mem_type_code" property="memTypeCode" />
        <result column="mem_type_name" property="memTypeName" />
        <result column="mem_idcard" property="memIdcard" typeHandler="com.zenith.front.common.encrypt.typehandler.EncryptTypeHandler"/>
        <result column="d07_code" property="d07Code" />
        <result column="d07_name" property="d07Name" />
        <result column="birthday" property="birthday" />
        <result column="sex_code" property="sexCode" />
        <result column="sex_name" property="sexName" />
        <result column="unit_code" property="unitCode" />
        <result column="unit_name" property="unitName" />
        <result column="person_name" property="personName" />
        <result column="remark" property="remark" />
        <result column="create_time" property="createTime" />
        <result column="update_time" property="updateTime" />
        <result column="delete_time" property="deleteTime" />
        <result column="timestamp" property="timestamp" />
        <result column="is_history" property="isHistory" />
        <result column="update_account" property="updateAccount" />
        <result column="elect_code" property="electCode" />
        <result column="has_village_transfer_student" property="hasVillageTransferStudent" />
        <result column="reward" property="reward" />
        <result column="d144_code" property="d144Code" />
        <result column="d144_name" property="d144Name" />
        <result column="endowment_insurance_for_urban_employees" property="endowmentInsuranceForUrbanEmployees" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, code, es_id, mem_code, mem_name, d25_code, d25_name, file_number, start_date, end_date, is_incumbent, d26_code, d26_name, mem_type_code, mem_type_name, mem_idcard, d07_code, d07_name, birthday, sex_code, sex_name, unit_code, unit_name, person_name, remark, create_time, update_time, delete_time, timestamp, is_history, update_account, elect_code, has_village_transfer_student, reward, d144_code, d144_name, endowment_insurance_for_urban_employees
    </sql>

    <select id="getTwoCommitteeList" resultType="com.zenith.front.model.vo.TwoCommitteeVO">
        with
        "record1s" as (
        select distinct on ("ccp_org_committee"."mem_code")
        "ccp_org_committee"."d022_name" as "d022Name",
        "ccp_org_committee"."mem_code" as "orgCommitteeMemCode"
        from "ccp_org_committee"
        where "ccp_org_committee"."delete_time" is null
        ),
        "record2s" as (
        select
        "ccp_unit_all"."is_create_org" as "isCreateOrg",
        "ccp_unit_all"."manage_org_code" as "manageOrgCode",
        "ccp_unit_all"."main_unit_org_code" as "mainUnitOrgCode",
        "ccp_unit_committee"."mem_code" as "memCode",
        "ccp_unit_committee"."mem_name" as "memName",
        "ccp_unit_committee"."d25_name" as "d25Name",
        "ccp_unit_committee"."d26_name" as "d26Name",
        "ccp_unit_committee"."mem_idcard" as "memIdcard",
        "ccp_unit_committee"."is_incumbent" as "isIncumbent",
        "ccp_unit_committee"."remark",
        "ccp_mem"."name",
        "ccp_mem"."sex_name" as "sexName",
        "ccp_mem"."join_org_date" as "joinOrgDate",
        "ccp_mem"."full_member_date" as "fullMemberDate",
        "ccp_mem"."is_farmer" as "isFarmer",
        "ccp_mem"."d07_name" as "d07Name",
        "ccp_mem"."d19_name" as "d19Name",
        "ccp_mem"."idcard",
        "ccp_mem"."phone",
        "ccp_mem"."birthday"
        from "ccp_unit_all"
        join "ccp_unit_committee"
        on "ccp_unit_all"."code" = "ccp_unit_committee"."unit_code"
        left outer join "ccp_mem"
        on "ccp_unit_committee"."mem_code" = "ccp_mem"."code"
        where (
        (
        (
        "ccp_unit_all"."is_create_org" = 1
        and "ccp_unit_all"."main_unit_org_code" like CONCAT(#{orgCode},'%')
        )
        or (
        "ccp_unit_all"."is_create_org" = 0
        and "ccp_unit_all"."manage_org_code" like CONCAT(#{orgCode},'%')
        )
        )
        and "ccp_unit_committee"."d25_code" in (
        '41', '42', '43', '51', '52', '53'
        )
        <if test="startTime != null and endTime != null">
            AND "ccp_unit_committee"."start_date" BETWEEN #{startTime} AND #{endTime}
        </if>
        <if test="startTime != null and endTime == null ">
            and "ccp_unit_committee"."start_date" &gt;= #{startTime}
        </if>
        <if test="startTime == null and endTime != null ">
            and "ccp_unit_committee"."start_date" &lt;= #{endTime}
        </if>
        and "ccp_unit_all"."delete_time" is null
        and "ccp_unit_committee"."delete_time" is null
        and "ccp_mem"."delete_time" is null
        )
        )
        select *
        from "record2s"
        left outer join "record1s"
        on "record2s"."memCode" = "record1s"."orgCommitteeMemCode"
        order by "record2s"."manageOrgCode"
    </select>

    <select id="findCurrentJobByMemCode" resultMap="BaseResultMap">
        SELECT t1.*
        from ccp_unit_committee t1
                 INNER JOIN ccp_unit_committee_elect t2 ON t1.elect_code = t2.code and t2.delete_time is null
        where t1.delete_time is null
          AND t1.end_date is null
          AND t1.mem_code = #{memCode}
          AND t2.tenure_start_date &lt;= #{lastYearDay}
          AND t2.tenure_end_date &gt;= #{lastYearDay}
          ORDER BY t2.tenure_start_date DESC
    </select>

    <select id="selectListByOrgCode" resultMap="BaseResultMap">
        SELECT *
        FROM "ccp_unit_committee"
        WHERE elect_code IN (SELECT code
                             FROM "ccp_unit_committee_elect"
                             WHERE unit_code IN (SELECT code
                                                 FROM ccp_unit
                                                 WHERE create_unit_org_code LIKE CONCAT(#{ orgCode }, '%')))
    </select>

    <select id="findVillageLeaderList" resultType="com.zenith.front.model.vo.DecryptVillageLeaderMap">
        SELECT *
        FROM (SELECT ccp_unit_committee.code        AS "id",
                     CASE WHEN mem_type_code = '1' THEN T.NAME WHEN mem_type_code = '0' THEN mem_name ELSE '' END AS "a0101",
                     d25_name                       AS "d25_name",
                     ''                             AS "a0192a",
                     start_date                     AS "a0288",
                     ccp_unit_committee.sex_code    AS "sex_code",
                     T.sex_code                     AS "mem_sex_code",
                     T.d06_code                     AS "mem_d06_code",
                     ccp_unit_committee.birthday    AS "a0107",
                     T.birthday                     AS "mem_birthday",
                     T.d48_code                     AS "d48_code",
                     T.join_org_date                AS "a0144",
                     T.join_work_date               AS "a0134",
                     ccp_unit_committee.start_date  AS "villageTakeTime",
                     ccp_unit_committee.d07_code    AS "d07_code",
                     T.d07_code                     AS "mem_d07_code",
                     ccp_unit_committee.photo_path  AS "a0198",
                     ccp_unit_committee.create_time AS "create_time",
                     '1'                            AS "type",
                     ccp_unit_committee.mem_type_code    AS "mem_type_code"
              FROM ccp_unit_committee
                       LEFT JOIN ccp_mem T ON T.code = ccp_unit_committee.mem_code
                       LEFT JOIN ccp_unit u ON u.code = ccp_unit_committee.unit_code
              WHERE ccp_unit_committee.delete_time IS NULL
                AND ccp_unit_committee.end_date IS NULL
                <if test="name != null and name != ''">
                    AND ccp_unit_committee.mem_name LIKE CONCAT('%', #{name}, '%')
                </if>
                AND ccp_unit_committee.elect_code IN (
                            SELECT (SELECT UNNEST(ARRAY_AGG(code ORDER BY tenure_end_date DESC)) LIMIT 1)
                            FROM ccp_unit_committee_elect
                            WHERE delete_time IS NULL
                            GROUP BY unit_code
                                                      )
                AND u.d04_code IN ('922', '923')
                <if test="orgLevelCode != null and orgLevelCode != '' ">
                    <choose>
                        <when test="includeChild != null and includeChild == true">
                            AND u.main_unit_org_code LIKE CONCAT(#{orgLevelCode},'%')
                        </when>
                        <otherwise>
                            AND u.main_unit_org_code = #{orgLevelCode}
                        </otherwise>
                    </choose>
                </if>
              UNION ALL
              SELECT ccp_org_committee.code        AS "id",
                     CASE WHEN mem_type_code = '1' THEN T.NAME WHEN mem_type_code = '0' THEN mem_name ELSE '' END AS "a0101",
                     ''                            AS "d25_name",
                     d022_name                     AS "a0192a",
                     start_date                    AS "a0288",
                     ccp_org_committee.sex_code    AS "sex_code",
                     T.sex_code                    AS "mem_sex_code",
                     T.d06_code                    AS "mem_d06_code",
                     ccp_org_committee.birthday    AS "a0107",
                     T.birthday                    AS "mem_birthday",
                     T.d48_code                    AS "d48_code",
                     T.join_org_date               AS "a0144",
                     T.join_work_date              AS "a0134",
                     ccp_org_committee.start_date  AS "villageTakeTime",
                     ccp_org_committee.d07_code    AS "d07_code",
                     T.d07_code                    AS "mem_d07_code",
                     ccp_org_committee.photo_path  AS "a0198",
                     ccp_org_committee.create_time AS "create_time",
                     '2'                           AS "type",
                     ccp_org_committee.mem_type_code    AS "mem_type_code"
              FROM ccp_org_committee
                       LEFT JOIN ccp_mem T ON T.code = ccp_org_committee.mem_code
                       LEFT JOIN ccp_org_all C ON C.code = ccp_org_committee.org_code
              WHERE ccp_org_committee.delete_time IS NULL
                AND ccp_org_committee.end_date IS NULL
                AND ccp_org_committee.elect_code IN (
                    SELECT (SELECT UNNEST(ARRAY_AGG(code ORDER BY tenure_end_date DESC)) LIMIT 1)
                    FROM ccp_org_committee_elect
                    WHERE delete_time IS NULL
                    GROUP BY org_code
        )
                AND C.d02_code NOT IN ('2', '4')
                AND C.d04_code IN ('922', '923')
                <if test="name != null and name != ''">
                    AND ccp_org_committee.mem_name LIKE CONCAT('%', #{name}, '%')
                </if>
                <if test="orgLevelCode != null and orgLevelCode != '' ">
                    <choose>
                        <when test="includeChild != null and includeChild == true">
                            AND C.org_code LIKE CONCAT(#{orgLevelCode},'%')
                        </when>
                        <otherwise>
                            AND C.org_code = #{orgLevelCode}
                        </otherwise>
                    </choose>
                </if>
              ) A
        ORDER BY A.create_time DESC,A."id" desc
    </select>

    <select id="getVillageLeaderStat" resultType="java.util.Map">
        SELECT ccp_unit_committee.code     AS "code",
               u.main_unit_org_code        AS "org_code",
               ccp_unit_committee.sex_code AS "sex_code",
               T.sex_code                  AS "mem_sex_code",
               T.d06_code                  AS "mem_d06_code",
               ccp_unit_committee.birthday AS "birthday",
               T.birthday                  AS "mem_birthday",
               ccp_unit_committee.d07_code AS "d07_code",
               T.d07_code                  AS "mem_d07_code",
               d25_code                    AS "d25_code",
               ''                          AS "d022_code",
               ccp_unit_committee.mem_code AS "mem_code",
               '1'                         AS "type"
        FROM ccp_unit_committee
                 INNER JOIN ccp_unit u ON u.code = ccp_unit_committee.unit_code
                 LEFT JOIN ccp_mem T ON T.code = ccp_unit_committee.mem_code
        WHERE ccp_unit_committee.delete_time IS NULL
          AND ccp_unit_committee.end_date IS NULL
          AND ccp_unit_committee.elect_code IN
              (
              SELECT (SELECT UNNEST(ARRAY_AGG(code ORDER BY tenure_end_date DESC)) LIMIT 1)
               FROM ccp_unit_committee_elect
               WHERE delete_time IS NULL
               GROUP BY unit_code
               )
          AND u.d04_code IN ('922', '923')
            <if test="name != null and name != ''">
                AND ccp_unit_committee.mem_name LIKE CONCAT('%', #{name}, '%')
            </if>
            <if test="orgLevelCode != null and orgLevelCode != '' ">
                <choose>
                    <when test="includeChild != null and includeChild == true">
                        AND u.main_unit_org_code LIKE CONCAT(#{orgLevelCode},'%')
                    </when>
                    <otherwise>
                        AND u.main_unit_org_code = #{orgLevelCode}
                    </otherwise>
                </choose>
            </if>

        UNION ALL
        SELECT ccp_org_committee.code     AS "code",
               C.org_code                 AS "org_code",
               ccp_org_committee.sex_code AS "sex_code",
               T.sex_code                 AS "mem_sex_code",
               T.d06_code                 AS "mem_d06_code",
               ccp_org_committee.birthday AS "birthday",
               T.birthday                 AS "mem_birthday",
               ccp_org_committee.d07_code AS "d07_code",
               T.d07_code                 AS "mem_d07_code",
               ''                         AS "d25_code",
               d022_code                  AS "d022_code",
               ccp_org_committee.mem_code AS "mem_code",
               '2'                        AS "type"
        FROM ccp_org_committee
                 LEFT JOIN ccp_mem T ON T.code = ccp_org_committee.mem_code
                 LEFT JOIN ccp_org_all C ON C.code = ccp_org_committee.org_code
        WHERE ccp_org_committee.delete_time IS NULL
          AND ccp_org_committee.end_date IS NULL
          AND ccp_org_committee.elect_code IN
              (
              SELECT (SELECT UNNEST(ARRAY_AGG(code ORDER BY tenure_end_date DESC)) LIMIT 1)
               FROM ccp_org_committee_elect
               WHERE delete_time IS NULL
               GROUP BY org_code
               )
          AND C.d02_code NOT IN ('2', '4')
          AND C.d04_code IN ('922', '923')
            <if test="name != null and name != ''">
                AND ccp_org_committee.mem_name LIKE CONCAT('%', #{name}, '%')
            </if>
            <if test="orgLevelCode != null and orgLevelCode != '' ">
                <choose>
                    <when test="includeChild != null and includeChild == true">
                        AND C.org_code LIKE CONCAT(#{orgLevelCode},'%')
                    </when>
                    <otherwise>
                        AND C.org_code = #{orgLevelCode}
                    </otherwise>
                </choose>
            </if>
    </select>

    <select id="selectCount" resultType="com.zenith.front.model.vo.UnitCommiteeCountVo">
        SELECT
            uc."has_village_transfer_student",
            uc."start_date",
            uc."is_incumbent",
            uc."d144_code",
            ma."sex_code",
            ma."d07_code",
            ma."d08_code"
        FROM
            "ccp_unit_committee" uc
                LEFT JOIN "ccp_mem_all" ma ON uc."mem_code" = ma."code"
        WHERE
                elect_code IN ( SELECT CODE FROM "ccp_unit_committee_elect" WHERE unit_code IN ( SELECT CODE FROM ccp_unit WHERE create_unit_org_code LIKE CONCAT(#{orgLevelCode},'%') ) and uc."has_village_transfer_student"='1')
    </select>

    <select id="getTwoCommitteeBycount" resultType="com.zenith.front.model.vo.TwoCommitteeVO">
        with
        "record1s" as (
        select distinct on ("ccp_org_committee"."mem_code")
        "ccp_org_committee"."d022_name" as "d022Name",
        "ccp_org_committee"."mem_code" as "orgCommitteeMemCode"
        from "ccp_org_committee"
        where "ccp_org_committee"."delete_time" is null
        ),
        "record2s" as (
        select
        "ccp_unit_all"."is_create_org" as "isCreateOrg",
        "ccp_unit_all"."manage_org_code" as "manageOrgCode",
        "ccp_unit_all"."main_unit_org_code" as "mainUnitOrgCode",
        "ccp_unit_committee"."mem_code" as "memCode",
        "ccp_unit_committee"."mem_name" as "memName",
        "ccp_unit_committee"."d25_name" as "d25Name",
        "ccp_unit_committee"."d26_name" as "d26Name",
        "ccp_unit_committee"."mem_idcard" as "memIdcard",
        "ccp_unit_committee"."d144_code" as "d144Code",
        "ccp_unit_committee"."is_incumbent" as "isIncumbent",
        "ccp_unit_committee"."remark",
        "ccp_mem"."name",
        "ccp_mem"."sex_name" as "sexName",
        "ccp_mem"."join_org_date" as "joinOrgDate",
        "ccp_mem"."full_member_date" as "fullMemberDate",
        "ccp_mem"."is_farmer" as "isFarmer",
        "ccp_mem"."d07_name" as "d07Name",
        "ccp_mem"."d19_name" as "d19Name",
        "ccp_mem"."idcard",
        "ccp_mem"."phone",
        "ccp_mem"."birthday"
        from "ccp_unit_all"
        join "ccp_unit_committee"
        on "ccp_unit_all"."code" = "ccp_unit_committee"."unit_code"
        left outer join "ccp_mem"
        on "ccp_unit_committee"."mem_code" = "ccp_mem"."code"
        where (
        (
        (
        "ccp_unit_all"."is_create_org" = 1
        and "ccp_unit_all"."main_unit_org_code" like CONCAT(#{orgCode},'%')
        )
        or (
        "ccp_unit_all"."is_create_org" = 0
        and "ccp_unit_all"."manage_org_code" like CONCAT(#{orgCode},'%')
        )
        )
        and "ccp_unit_committee"."d25_code" in (
        '41', '42', '43', '51', '52', '53'
        )
        <if test="startTime != null and endTime != null">
            AND "ccp_unit_committee"."start_date" BETWEEN #{startTime} AND #{endTime}
        </if>
        <if test="startTime != null and endTime == null ">
            and "ccp_unit_committee"."start_date" &gt;= #{startTime}
        </if>
        <if test="startTime == null and endTime != null ">
            and "ccp_unit_committee"."start_date" &lt;= #{endTime}
        </if>
        and "ccp_unit_all"."delete_time" is null
        and "ccp_unit_committee"."delete_time" is null
        and "ccp_mem"."delete_time" is null
        )
        )
        select *
        from "record2s"
        left outer join "record1s"
        on "record2s"."memCode" = "record1s"."orgCommitteeMemCode"
        order by "record2s"."manageOrgCode"
    </select>

    <select id="findJobsBycode" resultMap="BaseResultMap">
        SELECT
            c.*
        FROM
            (
                SELECT
                    ccp_unit_committee_elect.unit_code,
                    ccp_unit_committee_elect.code,
                    tenure_end_date,
                    ROW_NUMBER() OVER (PARTITION BY ccp_unit_committee_elect.unit_code ORDER BY tenure_end_date DESC) AS rn
                FROM
                    ccp_unit_committee_elect , ccp_unit where ccp_unit_committee_elect.unit_code = ccp_unit.code
                                                          and ccp_unit.delete_time is null
            ) e
                JOIN ccp_unit_committee c ON e.code = c.elect_code
        where e.rn = 1
          and c.mem_code = #{memCode}
          and c.end_date is null and c.delete_time is null and c.mem_type_code = '1'
    </select>

</mapper>
