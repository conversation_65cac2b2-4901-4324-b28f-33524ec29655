package com.zenith.front.dao.mapper.org;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.zenith.front.model.bean.OrgSlack;
import com.zenith.front.model.vo.OrgSlackListVo;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

/**
 * <p>
 * Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2021-08-05
 */
public interface OrgSlackMapper extends BaseMapper<OrgSlack> {

    @Select("${sql}")
    Page<OrgSlackListVo> getListPage(@Param("page") Page<OrgSlackListVo> page, @Param("sql") String sql);

}
