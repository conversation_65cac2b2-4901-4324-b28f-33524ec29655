package com.zenith.front.dao.mapper.unit;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.zenith.front.model.bean.UnitCommitteeElect;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2021-07-10
 */
public interface UnitCommitteeElectMapper extends BaseMapper<UnitCommitteeElect> {

    /**
     * 批量新增
     *
     * @param list
     * @return
     */
    int saveBatch(@Param("list") List<UnitCommitteeElect> list);

    /**
     * 根据机构层级码查询
     *
     * @param orgCode 机构层级码
     * @return
     */
    List<UnitCommitteeElect> selectListByOrgCode(@Param("orgCode") String orgCode);

    UnitCommitteeElect findNewestElect(@Param("unitCode") String unitCode, @Param("lasDate") String lastDate);
}
