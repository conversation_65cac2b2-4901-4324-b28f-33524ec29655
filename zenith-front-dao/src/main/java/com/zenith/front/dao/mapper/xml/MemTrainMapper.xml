<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zenith.front.dao.mapper.mem.MemTrainMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.zenith.front.model.bean.MemTrain">
        <id column="code" property="code" />
        <result column="org_code" property="orgCode" />
        <result column="create_time" property="createTime" />
        <result column="update_time" property="updateTime" />
        <result column="delete_time" property="deleteTime" />
        <result column="org_org_code" property="orgOrgCode" />
        <result column="year" property="year" />
        <result column="train_total" property="trainTotal" />
        <result column="school_train" property="schoolTrain" />
        <result column="new_train" property="newTrain" />
        <result column="youth_train" property="youthTrain" />
        <result column="elderly_train" property="elderlyTrain" />
        <result column="flow_train" property="flowTrain" />
        <result column="laid_off_train" property="laidOffTrain" />
        <result column="minority_areas_train" property="minorityAreasTrain" />
        <result column="organization_secretary" property="organizationSecretary" />
        <result column="county_party_committee" property="countyPartyCommittee" />
        <result column="level_organizations_secretary" property="levelOrganizationsSecretary" />
        <result column="community_party" property="communityParty" />
        <result column="has_will_lesson" property="hasWillLesson" />
        <result column="has_party_day" property="hasPartyDay" />
        <result column="provincial_train_class" property="provincialTrainClass" />
        <result column="provincial_train_member" property="provincialTrainMember" />
        <result column="city_train_class" property="cityTrainClass" />
        <result column="city_train_member" property="cityTrainMember" />
        <result column="county_train_class" property="countyTrainClass" />
        <result column="county_train_member" property="countyTrainMember" />
        <result column="level_party_class" property="levelPartyClass" />
        <result column="level_party_member" property="levelPartyMember" />
        <result column="rural_party_villages" property="ruralPartyVillages" />
        <result column="remote_education" property="remoteEducation" />
        <result column="remote_education_villages" property="remoteEducationVillages" />
        <result column="remote_education_administrative_village" property="remoteEducationAdministrativeVillage" />
        <result column="remote_education_committee" property="remoteEducationCommittee" />
        <result column="internet" property="internet" />
        <result column="wired" property="wired" />
        <result column="satellite" property="satellite" />
        <result column="site_administrator" property="siteAdministrator" />
        <result column="villages_cadres" property="villagesCadres" />
        <result column="village_community" property="villageCommunity" />
        <result column="volunteers" property="volunteers" />
        <result column="rural_remote_education_party" property="ruralRemoteEducationParty" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        code, org_code, create_time, update_time, delete_time, org_org_code, year, train_total, school_train, new_train, youth_train, elderly_train, flow_train, laid_off_train, minority_areas_train, organization_secretary, county_party_committee, level_organizations_secretary, community_party, has_will_lesson, has_party_day, provincial_train_class, provincial_train_member, city_train_class, city_train_member, county_train_class, county_train_member, level_party_class, level_party_member, rural_party_villages, remote_education, remote_education_villages, remote_education_administrative_village, remote_education_committee, internet, wired, satellite, site_administrator, villages_cadres, village_community, volunteers, rural_remote_education_party
    </sql>

</mapper>
