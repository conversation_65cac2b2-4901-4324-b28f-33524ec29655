<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zenith.front.dao.mapper.dict.DictActivityTypeMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.zenith.front.model.bean.DictActivityType">
        <id column="id" property="id" />
        <result column="key" property="key" />
        <result column="name" property="name" />
        <result column="pinyin" property="pinyin" />
        <result column="parent" property="parent" />
        <result column="is_leaf" property="isLeaf" />
        <result column="remark" property="remark" />
        <result column="disabled" property="disabled" />
        <result column="enabled" property="enabled" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, key, name, pinyin, parent, is_leaf, remark, disabled, enabled
    </sql>

</mapper>
