<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zenith.front.dao.mapper.org.OrgNonPublicPartyMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.zenith.front.model.bean.OrgNonPublicParty">
        <id column="code" property="code" />
        <result column="zb_code" property="zbCode" />
        <result column="org_code" property="orgCode" />
        <result column="org_level_code" property="orgLevelCode" />
        <result column="year" property="year" />
        <result column="has_work_committee" property="hasWorkCommittee" />
        <result column="has_working_body" property="hasWorkingBody" />
        <result column="working_body_number" property="workingBodyNumber" />
        <result column="manage_organization_number" property="manageOrganizationNumber" />
        <result column="connect_organization_number" property="connectOrganizationNumber" />
        <result column="fiscal_funds" property="fiscalFunds" />
        <result column="party_expenses" property="partyExpenses" />
        <result column="activity_service_center" property="activityServiceCenter" />
        <result column="new_activity_service_center" property="newActivityServiceCenter" />
        <result column="create_time" property="createTime" />
        <result column="update_time" property="updateTime" />
        <result column="delete_time" property="deleteTime" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        code, zb_code, org_code, org_level_code, year, has_work_committee, has_working_body, working_body_number, manage_organization_number, connect_organization_number, fiscal_funds, party_expenses, activity_service_center, new_activity_service_center, create_time, update_time, delete_time
    </sql>

</mapper>
