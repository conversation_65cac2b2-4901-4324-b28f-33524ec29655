<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zenith.front.dao.mapper.mem.MemDigitalMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.zenith.front.model.bean.MemDigital">
        <id column="code" property="code" />
        <result column="digital_lot_no" property="digitalLotNo" />
        <result column="name" property="name" />
        <result column="path" property="path" />
        <result column="d222_code" property="d222Code" />
        <result column="d222_name" property="d222Name" />
        <result column="d08_code" property="d08Code" />
        <result column="sort" property="sort" />
        <result column="opration_user" property="oprationUser" />
        <result column="create_time" property="createTime" />
        <result column="create_user" property="createUser" />
        <result column="update_time" property="updateTime" />
        <result column="update_user" property="updateUser" />
        <result column="delete_time" property="deleteTime" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        code, digital_lot_no, name, path, d222_code, d222_name, d08_code, sort, opration_user, create_time, create_user, update_time, update_user, delete_time
    </sql>

    <select id="selectDigitalCount" resultType="java.lang.Integer">
        select count(1) from ccp_mem_digital where digital_lot_no = #{digitalLotNo} and process_node = #{processNode} and d222_code = #{d222Code} and delete_time is null
    </select>


    <select id="getMemDigitalData" resultType="com.zenith.front.model.vo.MemDigitalDataVO">
        SELECT ccp_mem.name "memName",ccp_mem_digital.* from ccp_mem_digital
        INNER JOIN ccp_mem ON ccp_mem_digital.digital_lot_no = ccp_mem.digital_lot_no
        WHERE ccp_mem_digital.delete_time is NULL
        AND ccp_mem.delete_time IS NULL AND ccp_mem.digital_lot_no IS NOT NULL
        AND ccp_mem.d08_code IN ('1', '2') AND (is_transfer!=1 OR is_transfer is null)
        <choose>
            <when test="dto.subordinate==0">
                AND ccp_mem.mem_org_code = #{dto.memOrgCode}
            </when>
            <otherwise>
                AND ccp_mem.mem_org_code like concat(#{dto.memOrgCode},'%')
            </otherwise>
        </choose>
        <if test="dto.isExportAll != null and dto.isExportAll != 1 ">
            AND ccp_mem.digital_lot_no IN
            <foreach collection="dto.digitalLotNoList" item="item" index="index" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        ORDER BY sort,d222_code,path
        LIMIT 100000
    </select>

    <select id="getDevelopMemDigitalData" resultType="com.zenith.front.model.vo.MemDigitalDataVO">
        SELECT ccp_mem.name "memName", ccp_mem_digital.* from ccp_mem_digital
        INNER JOIN ccp_mem_develop AS ccp_mem ON ccp_mem_digital.digital_lot_no = ccp_mem.digital_lot_no
        WHERE ccp_mem_digital.delete_time is NULL
        AND ccp_mem.delete_time IS NULL AND ccp_mem.digital_lot_no IS NOT NULL
        AND ccp_mem.d08_code IN ('3', '4', '5') AND (is_transfer!=1 OR is_transfer is null)
        <choose>
            <when test="dto.subordinate==0">
                AND ccp_mem.develop_org_code = #{dto.memOrgCode}
            </when>
            <otherwise>
                AND ccp_mem.develop_org_code like concat(#{dto.memOrgCode},'%')
            </otherwise>
        </choose>
        <if test="dto.isExportAll != null and dto.isExportAll != 1 ">
            AND ccp_mem.digital_lot_no IN
            <foreach collection="dto.digitalLotNoList" item="item" index="index" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        ORDER BY sort,d222_code,path
        LIMIT 10000
    </select>

</mapper>
