<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.zenith.front.dao.mapper.flow.DataEqualsRecordMapper">
    <insert id="insertBatchWithAutoId" parameterType="java.util.List" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO data_equals_record (
        biz_id, biz_table_name, biz_data, biz_data_encrypt,
        create_time, create_user, update_time, update_user,
        delete_time, remark, timestamp
        ) VALUES
        <foreach collection="list" item="item" separator=",">
            (
            #{item.bizId}, #{item.bizTableName}, #{item.bizData}, #{item.bizDataEncrypt},
            #{item.createTime}, #{item.createUser}, #{item.updateTime}, #{item.updateUser},
            #{item.deleteTime}, #{item.remark}, #{item.timestamp}
            )
        </foreach>
    </insert>

    <select id="findByBizIdAndBizTableName" resultType="com.zenith.front.model.bean.DataEqualsRecord">
        select * from data_equals_record where biz_id = #{bizId} and biz_table_name =#{bizTableName} order by create_time  desc limit 1
    </select>

    <select id="findBizTableIdByCode" resultType="java.lang.Object">
        select ${idStr} from ${tableName} where code = #{code} limit 1
    </select>
</mapper>

