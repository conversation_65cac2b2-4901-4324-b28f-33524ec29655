<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zenith.front.dao.mapper.logic.LogicCheckIgnoreMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.zenith.front.model.bean.LogicCheckIgnore">
        <id column="id" property="id" />
        <result column="logic_check_code" property="logicCheckCode" />
        <result column="logic_check_name" property="logicCheckName" />
        <result column="type" property="type" />
        <result column="code" property="code" />
        <result column="reason" property="reason" />
        <result column="update_account" property="updateAccount" />
        <result column="create_time" property="createTime" />
        <result column="delete_time" property="deleteTime" />
        <result column="delete_account" property="deleteAccount" />
        <result column="org_code" property="orgCode" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, logic_check_code, logic_check_name, type, code, reason, update_account, create_time, delete_time, delete_account, org_code
    </sql>

    <select id="ignoreList" resultType="com.zenith.front.model.vo.LogicIgnoreListVO">
        SELECT
        t.logic_check_name,
        t.id,
        t.reason,
        t.update_account,
        t.create_time,
        <choose>
            <when test="tableName=='ccp_org' || tableName=='ccp_org_all'">
                t1.name AS orgName
            </when>
            <when test="tableName=='ccp_mem' || tableName=='ccp_mem_all'">
                t1.name AS orgName,
                t2.name AS name,
                t2.birthday AS birthday,
                t2.join_org_date AS joinOrgDate,
                t2.full_member_date AS fullMemberDate
            </when>
            <when test="tableName=='ccp_unit' || tableName=='ccp_unit_all'">
                t3.main_org_name AS orgName,
                t3.name AS unitName
            </when>
            <when test="tableName=='ccp_mem_develop' || tableName=='ccp_mem_develop_all'">
                t1.name AS orgName,
                t4.name AS name
            </when>
            <when test="tableName=='ccp_develop_step_log' || tableName=='ccp_develop_step_log_all'">
                t5.org_name AS orgName,
                t5.name AS name
            </when>
            <when test="tableName=='mem_flow'">
                t6.mem_org_name AS orgName,
                t6.mem_name AS name
            </when>
            <otherwise>

            </otherwise>
        </choose>
        FROM
        "ccp_logic_check_ignore" t
        <choose>
            <when test="tableName=='ccp_org' || tableName=='ccp_org_all'">
                LEFT JOIN ccp_org t1 ON t1.code = t.code
            </when>
            <when test="tableName=='ccp_mem' || tableName=='ccp_mem_all'">
                LEFT JOIN ccp_mem t2 ON t2.code = t.code
                LEFT JOIN ccp_org t1 ON t1.code = t2.org_code
            </when>
            <when test="tableName=='ccp_unit' || tableName=='ccp_unit_all'">
                LEFT JOIN ccp_unit t3 ON t3.code = t.code
            </when>
            <when test="tableName=='ccp_mem_develop' || tableName=='ccp_mem_develop_all'">
                LEFT JOIN ccp_mem_develop t4 ON t4.code = t.code
                LEFT JOIN ccp_org t1 ON t1.code = t4.org_code
            </when>
            <when test="tableName=='ccp_develop_step_log' || tableName=='ccp_develop_step_log_all'">
                LEFT JOIN ccp_develop_step_log t5 ON t5.code = t.code
            </when>
            <when test="tableName=='mem_flow'">
                LEFT JOIN mem_flow t6 ON t6.code = t.code
            </when>
        </choose>
        where
        t.delete_time is null AND t.org_code LIKE CONCAT(#{orgCode}, '%')
        <if test="logicCheckCode !='' and logicCheckCode !=null">
            AND t.logic_check_code = #{logicCheckCode}
        </if>
    </select>

</mapper>
