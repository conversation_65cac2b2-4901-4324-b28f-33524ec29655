<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zenith.front.dao.mapper.mem.MemDevelopAllMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.zenith.front.model.bean.MemDevelopAll">
        <id column="id" property="id" />
        <result column="code" property="code" />
        <result column="es_id" property="esId" />
        <result column="name" property="name" typeHandler="com.zenith.front.common.encrypt.typehandler.EncryptTypeHandler"/>
        <result column="pinyin" property="pinyin" />
        <result column="idcard" property="idcard" typeHandler="com.zenith.front.common.encrypt.typehandler.EncryptTypeHandler"/>
        <result column="d06_code" property="d06Code" />
        <result column="d06_name" property="d06Name" />
        <result column="d48_code" property="d48Code" />
        <result column="d48_name" property="d48Name" />
        <result column="sex_code" property="sexCode" />
        <result column="sex_name" property="sexName" />
        <result column="birthday" property="birthday" />
        <result column="phone" property="phone" typeHandler="com.zenith.front.common.encrypt.typehandler.EncryptTypeHandler"/>
        <result column="d07_code" property="d07Code" />
        <result column="d07_name" property="d07Name" />
        <result column="d09_code" property="d09Code" />
        <result column="d09_name" property="d09Name" />
        <result column="age" property="age" />
        <result column="is_dispatch" property="isDispatch" />
        <result column="d21_code" property="d21Code" />
        <result column="d21_name" property="d21Name" />
        <result column="d19_name" property="d19Name" />
        <result column="d19_code" property="d19Code" />
        <result column="d20_name" property="d20Name" />
        <result column="d20_code" property="d20Code" />
        <result column="advanced_model_code" property="advancedModelCode" />
        <result column="politics_code" property="politicsCode" />
        <result column="politics_name" property="politicsName" />
        <result column="has_dead" property="hasDead" />
        <result column="dead_time" property="deadTime" />
        <result column="ratification_time" property="ratificationTime" />
        <result column="d08_code" property="d08Code" />
        <result column="d08_name" property="d08Name" />
        <result column="d04_code" property="d04Code" />
        <result column="d04_name" property="d04Name" />
        <result column="unit_code" property="unitCode" />
        <result column="unit_name" property="unitName" />
        <result column="create_time" property="createTime" />
        <result column="update_time" property="updateTime" />
        <result column="delete_time" property="deleteTime" />
        <result column="d27_code" property="d27Code" />
        <result column="d27_name" property="d27Name" />
        <result column="apply_date_curr_year" property="applyDateCurrYear" />
        <result column="develop_org_code" property="developOrgCode" />
        <result column="org_code" property="orgCode" />
        <result column="personnel_relations_unit" property="personnelRelationsUnit" />
        <result column="personnel_d04_code" property="personnelD04Code" />
        <result column="personnel_d04_name" property="personnelD04Name" />
        <result column="statistical_unit" property="statisticalUnit" />
        <result column="has_unit_statistics" property="hasUnitStatistics" />
        <result column="unit_information" property="unitInformation" />
        <result column="has_unit_province" property="hasUnitProvince" />
        <result column="timestamp" property="timestamp" />
        <result column="is_farmer" property="isFarmer" />
        <result column="topre_join_org_date_year" property="topreJoinOrgDateYear" />
        <result column="is_towns" property="isTowns" />
        <result column="is_community" property="isCommunity" />
        <result column="is_transfer" property="isTransfer" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, code, es_id, name, pinyin, idcard, d06_code, d06_name, d48_code, d48_name, sex_code, sex_name, birthday, phone, d07_code, d07_name, d09_code, d09_name, age, is_dispatch, d21_code, d21_name, d19_name, d19_code, d20_name, d20_code, advanced_model_code, politics_code, politics_name, has_dead, dead_time, ratification_time, d08_code, d08_name, d04_code, d04_name, unit_code, unit_name, create_time, update_time, delete_time, d27_code, d27_name, apply_date_curr_year, develop_org_code, org_code, personnel_relations_unit, personnel_d04_code, personnel_d04_name, statistical_unit, has_unit_statistics, unit_information, has_unit_province, timestamp, is_farmer, topre_join_org_date_year, is_towns, is_community, is_transfer
    </sql>

    <update id="updateD09Code">
        UPDATE ccp_mem_develop_all
        SET d09_code = T.d09_code,d09_name = T.d09_name
        FROM
            ccp_mem_develop T
        WHERE
            T.code = ccp_mem_develop_all.code
          AND ( T.d09_code LIKE '3%' OR T.d09_code LIKE '515%' )
    </update>

</mapper>
