<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zenith.front.dao.mapper.ztdc.Zt5CommunityPartyMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.zenith.front.model.bean.Zt5CommunityParty">
        <id column="code" property="code" />
        <result column="unit_code" property="unitCode" />
        <result column="party_worker" property="partyWorker" />
        <result column="college_or_above" property="collegeOrAbove" />
        <result column="government_streets_select" property="governmentStreetsSelect" />
        <result column="select_from_veterans" property="selectFromVeterans" />
        <result column="civil_service_recruitment" property="civilServiceRecruitment" />
        <result column="select_into_establishment" property="selectIntoEstablishment" />
        <result column="representative_member" property="representativeMember" />
        <result column="less_than_past_year" property="lessThanPastYear" />
        <result column="community_work_wage" property="communityWorkWage" />
        <result column="community_secretary_wage" property="communitySecretaryWage" />
        <result column="job_instructor" property="jobInstructor" />
        <result column="job_level_workers_community" property="jobLevelWorkersCommunity" />
        <result column="practice_filing_management" property="practiceFilingManagement" />
        <result column="into_budget" property="intoBudget" />
        <result column="into_budget_money" property="intoBudgetMoney" />
        <result column="service_funds" property="serviceFunds" />
        <result column="annual_service_funds" property="annualServiceFunds" />
        <result column="has_committee_system" property="hasCommitteeSystem" />
        <result column="members_report_annual_service" property="membersReportAnnualService" />
        <result column="members_report_community" property="membersReportCommunity" />
        <result column="create_time" property="createTime" />
        <result column="update_time" property="updateTime" />
        <result column="delete_time" property="deleteTime" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        code, unit_code, party_worker, college_or_above, government_streets_select, select_from_veterans, civil_service_recruitment, select_into_establishment, representative_member, less_than_past_year, community_work_wage, community_secretary_wage, job_instructor, job_level_workers_community, practice_filing_management, into_budget, into_budget_money, service_funds, annual_service_funds, has_committee_system, members_report_annual_service, members_report_community, create_time, update_time, delete_time
    </sql>

</mapper>
