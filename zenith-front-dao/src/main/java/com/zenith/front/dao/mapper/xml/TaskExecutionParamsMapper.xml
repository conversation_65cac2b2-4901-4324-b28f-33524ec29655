<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zenith.front.dao.mapper.task.TaskExecutionParamsMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.zenith.front.model.bean.TaskExecutionParams">
        <id column="task_execution_id" property="taskExecutionId" />
        <result column="task_param" property="taskParam" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        task_execution_id, task_param
    </sql>

</mapper>
