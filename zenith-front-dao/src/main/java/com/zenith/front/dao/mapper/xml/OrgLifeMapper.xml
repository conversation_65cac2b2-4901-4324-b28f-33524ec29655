<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zenith.front.dao.mapper.org.OrgLifeMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.zenith.front.model.bean.OrgLife">
        <id column="code" property="code"/>
        <result column="org_code" property="orgCode"/>
        <result column="org_level_code" property="orgLevelCode"/>
        <result column="activity_name" property="activityName"/>
<!--        <result column="d158_code" property="d158Code"/>-->
<!--        <result column="d158_name" property="d158Name"/>-->
        <result column="activity_time" property="activityTime"/>
        <result column="speaker" property="speaker"/>
        <result column="has_more_org_join" property="hasMoreOrgJoin"/>
        <result column="join_org_code_list" property="joinOrgCodeList"/>
        <result column="join_org_code_list_name" property="joinOrgCodeListName"/>
        <result column="mem_code_list" property="memCodeList"/>
        <result column="mem_code_list_name" property="memCodeListName"/>
        <result column="leave_mem_code_list" property="leaveMemCodeList"/>
        <result column="leave_mem_code_list_name" property="leaveMemCodeListName"/>
        <result column="activity_location" property="activityLocation"/>
        <result column="activity_record" property="activityRecord"/>
        <result column="file_path" property="filePath"/>
        <result column="create_time" property="createTime"/>
        <result column="update_time" property="updateTime"/>
        <result column="delete_time" property="deleteTime"/>
        <result column="create_user" property="createUser"/>
        <result column="manage_org_code" property="manageOrgCode"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        code
        , org_code, org_level_code, activity_name, d158_code, d158_name, activity_time, speaker, has_more_org_join, join_org_code_list, join_org_code_list_name, mem_code_list, mem_code_list_name, leave_mem_code_list, leave_mem_code_list_name, activity_location, activity_record, file_path, create_time, update_time, delete_time, create_user, manage_org_code
    </sql>

    <select id="getOrgLifeList" resultType="com.zenith.front.model.bean.OrgLife">
        SELECT t1.*
        from ccp_org_life t1
        INNER JOIN ccp_org_life_part t2 on t2.org_life_code = t1.code
        where t1.delete_time is null
        and t2.org_level_code like CONCAT(#{data.orgCode}, '%')

        <if test="data.activityName != null and data.activityName != ''">
            and t1.activity_name like concat('%', #{data.activityName},'%')
        </if>
        <if test="data.startTime != null ">
            and t1.activity_time &gt;= #{data.startTime}
        </if>
        <if test="data.endTime != null">
            and t1.activity_time &lt;= #{data.startTime}
        </if>
        <if test="data.d158Code!=null and data.d158Code.size != 0">
            <foreach item="item" index="index" collection="data.d158Code" open="AND (" close=")" separator="or">
                string_to_array( "t1"."d158_code", ',' ) @> string_to_array(#{item}, ',' )
            </foreach>
        </if>
        GROUP BY t1.code
        ORDER BY t1.activity_time desc
    </select>
    <select id="getListLikeOrgCode" resultType="com.zenith.front.model.bean.OrgLife">
        SELECT
            t1.*
        FROM
            ccp_org_life t1
                INNER JOIN ccp_org_life_part t2 ON t2.org_life_code = t1.code
        WHERE
            t1.delete_time IS NULL
          AND t2.org_level_code LIKE CONCAT ( #{orgCode}, '%' )
        <if test="startDate != null ">
            and t1.activity_time &gt;= #{startDate}
        </if>
        <if test="endDate != null">
            and t1.activity_time &lt;= #{endDate}
        </if>
        GROUP BY t1.code
    </select>


    <select id="getOrgLifeCOde" resultType="java.lang.String">
        SELECT ccp_org_life.org_level_code
        FROM ccp_org_life
        WHERE ccp_org_life.delete_time IS NULL
          AND d158_code IS NOT NULL
    </select>




<!--    <select id="getOrgLifeByOrgCodeListAndTime" resultType="com.zenith.front.model.bean.OrgLife">-->
<!--        SELECT t1.*-->
<!--        from ccp_org_life t1-->

<!--        where t1.delete_time is null-->
<!--            and t1.activity_time BETWEEN #{beginTime} and #{endTime}-->

<!--        <if test="orgCodeList != null and orgCodeList.size > 0">-->
<!--            AND t1.org_level_code IN-->
<!--            <foreach collection="orgCodeList" item="item" index="index" open="(" separator="," close=")">-->
<!--                #{item}-->
<!--            </foreach>-->
<!--        </if>-->
<!--        and t1.d158_code in ('1','4')-->
<!--        ORDER BY t1.activity_time desc-->
<!--    </select>-->

<!--优化下面sql-->
<!--    <select id="getOrgLifeByOrgCodeListAndTime" resultType="com.zenith.front.model.vo.OrgLifeGetMAQVO">-->
<!--        SELECT-->
<!--        b.org_level_code as orgCode,-->
<!--        monthdate,-->
<!--        count( b.* )-->
<!--        FROM-->
<!--            (-->
<!--                SELECT-->
<!--                    to_char ( date, 'YYYY-MM' ) AS monthdate-->
<!--                FROM-->
<!--                    generate_series  ( date_trunc ( 'month', to_date ( #{beginTime}, 'yyyymm' ) ),-->
<!--                                       date_trunc ( 'month', to_date ( #{endTime}, 'yyyymm' ) ), '1 month' ) AS time( date )-->
<!--            ) a-->
<!--                LEFT JOIN ccp_org_life b ON monthdate = to_char( b.activity_time, 'YYYY-MM' )-->
<!--                and (b.d158_code like '%1%' or b.d158_code like '%4%' or b.d158_code like '%6%')-->
<!--                and b.delete_time is null-->
<!--        <if test="orgCodeList != null and orgCodeList.size > 0">-->
<!--            AND  b.org_level_code IN-->
<!--            <foreach collection="orgCodeList" item="item" index="index" open="(" separator="," close=")">-->
<!--                #{item}-->
<!--            </foreach>-->
<!--        </if>-->

<!--        GROUP BY-->
<!--            monthdate,org_level_code-->
<!--        ORDER BY-->
<!--            monthdate-->
<!--    </select>-->


    <select id="getOrgLifeByOrgCodeListAndTime" resultType="com.zenith.front.model.vo.OrgLifeGetMAQVO">
        SELECT
        b.org_level_code AS orgCode,
        to_char ( date_trunc ( 'month', b.activity_time ), 'YYYY-MM' ) AS monthdate,
        COUNT( b.* ) AS count
        FROM
        ccp_org_life b
        WHERE
        to_char ( b.activity_time, 'YYYY-MM' )BETWEEN #{beginTime}
        AND #{endTime}
        AND ( b.d158_code LIKE '%1%' OR b.d158_code LIKE '%4%' OR b.d158_code LIKE '%6%' )
        <if test="orgCodeList != null and orgCodeList.size > 0">
            AND  b.org_level_code IN
            <foreach collection="orgCodeList" item="item" index="index" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>

        AND b.delete_time IS NULL
        GROUP BY
        monthdate,
        org_level_code
        ORDER BY
        monthdate
    </select>


    <select id="getOrgLifeBy3" resultType="com.zenith.front.model.vo.OrgLifeGetMAQVO">
<!--        SELECT-->
<!--            b.org_level_code as orgCode,-->
<!--            monthdate,-->
<!--            count( b.* )-->
<!--        FROM-->
<!--            (-->
<!--                SELECT-->
<!--                    to_char ( date, 'YYYY-MM' ) AS monthdate-->
<!--                FROM-->
<!--                    generate_series  ( date_trunc ( 'month', to_date ( #{beginTime}, 'yyyymm' ) ),-->
<!--                                       date_trunc ( 'month', to_date ( #{endTime}, 'yyyymm' ) ), '1 month' ) AS time( date )-->
<!--            ) a-->
<!--                LEFT JOIN ccp_org_life b ON monthdate = to_char( b.activity_time, 'YYYY-MM' )-->
<!--                and b.d158_code  like '%3%'-->
<!--                and b.delete_time is null-->
<!--        <if test="orgCodeList != null and orgCodeList.size > 0">-->
<!--            AND  b.org_level_code IN-->
<!--            <foreach collection="orgCodeList" item="item" index="index" open="(" separator="," close=")">-->
<!--                #{item}-->
<!--            </foreach>-->
<!--        </if>-->

<!--        GROUP BY-->
<!--            monthdate,org_level_code-->
<!--        ORDER BY-->
<!--            monthdate-->
        SELECT
        b.org_level_code AS orgCode,
        to_char ( date_trunc ( 'month', b.activity_time ), 'YYYY-MM' ) AS monthdate,
        COUNT( b.* ) AS count
        FROM
        ccp_org_life b
        WHERE
        to_char ( b.activity_time, 'YYYY-MM' ) BETWEEN #{beginTime}
        AND  #{endTime}
        AND b.d158_code LIKE '%3%'
        AND b.delete_time IS NULL
        <if test="orgCodeList != null and orgCodeList.size > 0">
            AND  b.org_level_code IN
            <foreach collection="orgCodeList" item="item" index="index" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        GROUP BY
        monthdate,
        org_level_code
        ORDER BY
        monthdate
    </select>

    <select id="getOrgLifeByD1582" resultType="com.zenith.front.model.vo.OrgLifeGetMAQVO">
<!--        SELECT-->
<!--            b.org_level_code as orgCode,-->
<!--            monthdate,-->
<!--            count( b.* )-->
<!--        FROM-->
<!--            (-->
<!--                SELECT-->
<!--                    to_char ( date, 'YYYY-MM' ) AS monthdate-->
<!--                FROM-->
<!--                    generate_series  ( date_trunc ( 'month', to_date ( #{beginTime}, 'yyyymm' ) ),-->
<!--                                       date_trunc ( 'month', to_date ( #{endTime}, 'yyyymm' ) ), '1 month' ) AS time( date )-->
<!--            ) a-->
<!--                LEFT JOIN ccp_org_life b ON monthdate = to_char( b.activity_time, 'YYYY-MM' )-->
<!--                and b.delete_time is null-->
<!--                and b.d158_code  like '%2%'-->
<!--        <if test="orgCodeList != null and orgCodeList.size > 0">-->
<!--            AND  b.org_level_code IN-->
<!--            <foreach collection="orgCodeList" item="item" index="index" open="(" separator="," close=")">-->
<!--                #{item}-->
<!--            </foreach>-->
<!--        </if>-->

<!--        GROUP BY-->
<!--            monthdate,org_level_code-->
<!--        ORDER BY-->
<!--            monthdate-->
        SELECT
        b.org_level_code AS orgCode,
        to_char ( date_trunc ( 'month', b.activity_time ), 'YYYY-MM' ) AS monthdate,
        COUNT( b.* ) AS count
        FROM
        ccp_org_life b
        WHERE
        to_char ( b.activity_time, 'YYYY-MM' ) BETWEEN  #{beginTime}
        AND #{endTime}
        AND b.delete_time IS NULL
        AND b.d158_code LIKE '%2%'
        <if test="orgCodeList != null and orgCodeList.size > 0">
            AND  b.org_level_code IN
            <foreach collection="orgCodeList" item="item" index="index" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        GROUP BY
        monthdate,
        org_level_code
        ORDER BY
        monthdate
    </select>




<!--    <select id="getOrgLifeBy3" resultType="com.zenith.front.model.bean.OrgLife">-->
<!--        SELECT t1.*-->
<!--        from ccp_org_life t1-->

<!--        where t1.delete_time is null-->
<!--            and t1.activity_time BETWEEN #{beginTime} and #{endTime}-->

<!--        <if test="orgCodeList != null and orgCodeList.size > 0">-->
<!--            AND t1.org_level_code IN-->
<!--            <foreach collection="orgCodeList" item="item" index="index" open="(" separator="," close=")">-->
<!--                #{item}-->
<!--            </foreach>-->
<!--        </if>-->
<!--        and t1.d158_code = '3'-->
<!--        ORDER BY t1.activity_time desc-->
<!--    </select>-->


    <select id="quartertjByOne" resultType="com.zenith.front.model.vo.OrgLifeGetMAQVO">
<!--        SELECT-->
<!--            b.org_level_code as orgCode,-->
<!--            monthdate,-->
<!--            count( b.* )-->
<!--        FROM-->
<!--            (-->
<!--                SELECT-->
<!--                    to_char ( date, 'YYYY-MM' ) AS monthdate-->
<!--                FROM-->
<!--                    generate_series  ( date_trunc ( 'QUARTER', to_date ( #{beginTime}, 'yyyymm' ) ),-->
<!--                                       date_trunc ( 'QUARTER', to_date ( #{endTime}, 'yyyymm' ) ), '1 month' ) AS time( date )-->
<!--            ) a-->
<!--                LEFT JOIN ccp_org_life b ON monthdate = to_char( b.activity_time, 'YYYY-MM' )-->
<!--                and b.d158_code  like '%1%'-->
<!--                and b.delete_time is null-->
<!--        <if test="orgCodeList != null and orgCodeList.size > 0">-->
<!--            AND  b.org_level_code IN-->
<!--            <foreach collection="orgCodeList" item="item" index="index" open="(" separator="," close=")">-->
<!--                #{item}-->
<!--            </foreach>-->
<!--        </if>-->

<!--        GROUP BY-->
<!--            monthdate,org_level_code-->
<!--        ORDER BY-->
<!--            monthdate-->
        SELECT
        b.org_level_code AS orgCode,
        to_char ( date_trunc ( 'month', b.activity_time ), 'YYYY-MM' ) AS monthdate,
        COUNT( b.* ) AS count
        FROM
        ccp_org_life b
        WHERE
        to_char ( b.activity_time, 'YYYY-MM' ) BETWEEN #{beginTime}
        AND #{endTime}
        AND b.d158_code LIKE '%1%'
        AND b.delete_time IS NULL
        <if test="orgCodeList != null and orgCodeList.size > 0">
            AND  b.org_level_code IN
            <foreach collection="orgCodeList" item="item" index="index" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>

        GROUP BY
        monthdate,
        org_level_code
        ORDER BY
        monthdate
    </select>

    <select id="quartertjByFour" resultType="com.zenith.front.model.vo.OrgLifeGetMAQVO">
<!--        SELECT-->
<!--            b.org_level_code as orgCode,-->
<!--            monthdate,-->
<!--            count( b.* )-->
<!--        FROM-->
<!--            (-->
<!--                SELECT-->
<!--                    to_char ( date, 'YYYY-MM' ) AS monthdate-->
<!--                FROM-->
<!--                    generate_series  ( date_trunc ( 'QUARTER', to_date ( #{beginTime}, 'yyyymm' ) ),-->
<!--                                       date_trunc ( 'QUARTER', to_date ( #{endTime}, 'yyyymm' ) ), '1 month' ) AS time( date )-->
<!--            ) a-->
<!--                LEFT JOIN ccp_org_life b ON monthdate = to_char( b.activity_time, 'YYYY-MM' )-->
<!--                and b.d158_code  like '%4%'-->
<!--                and b.delete_time is null-->
<!--        <if test="orgCodeList != null and orgCodeList.size > 0">-->
<!--            AND  b.org_level_code IN-->
<!--            <foreach collection="orgCodeList" item="item" index="index" open="(" separator="," close=")">-->
<!--                #{item}-->
<!--            </foreach>-->
<!--        </if>-->


<!--        GROUP BY-->
<!--            monthdate,org_level_code-->
<!--        ORDER BY-->
<!--            monthdate-->
        SELECT
        b.org_level_code AS orgCode,
        to_char ( date_trunc ( 'month', b.activity_time ), 'YYYY-MM' ) AS monthdate,
        COUNT( b.* ) AS count
        FROM
        ccp_org_life b
        WHERE
        to_char ( b.activity_time, 'YYYY-MM' ) BETWEEN #{beginTime}
        AND #{endTime}
        AND b.d158_code LIKE '%4%'
        AND b.delete_time IS NULL
        <if test="orgCodeList != null and orgCodeList.size > 0">
            AND  b.org_level_code IN
            <foreach collection="orgCodeList" item="item" index="index" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        GROUP BY
        monthdate,
        org_level_code
        ORDER BY
        monthdate
    </select>

        <select id="findByD1585" resultType="com.zenith.front.model.bean.OrgLife">
            SELECT t1.*
            from ccp_org_life t1

            where t1.delete_time is null
                and t1.activity_time BETWEEN #{beginTime} and #{endTime}

            <if test="orgCodeList != null and orgCodeList.size > 0">
                AND t1.org_level_code IN
                <foreach collection="orgCodeList" item="item" index="index" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            and t1.d158_code like '%5%'
            and t1.delete_time is null
            ORDER BY t1.activity_time desc
        </select>




    <select id="getBetweenMonth" resultType="java.lang.String">
        SELECT
            monthdate
        FROM
            (
                SELECT
                    to_char ( date, 'YYYY-MM-dd' ) AS monthdate
                FROM
                    generate_series  ( date_trunc ( 'QUARTER', to_date ( #{beginTime}, 'yyyymm' ) ),
                                       date_trunc ( 'month', to_date ( #{quarterEndTime}, 'yyyymm' ) ), '1 month' ) AS time( date )
            ) a
                LEFT JOIN ccp_org_life b ON monthdate = to_char( b.activity_time, 'YYYY-MM' )
        GROUP BY
            monthdate
        ORDER BY
            monthdate
    </select>



<!--    <select id="getOrgLifeList" resultType="com.zenith.front.model.bean.OrgLife">-->
<!--        SELECT t1.*,t3.name-->
<!--        from ccp_org_life t1-->
<!--        INNER JOIN ccp_org_life_part t2 on t2.org_life_code = t1.code-->
<!--        INNER JOIN ccp_org t3 on t3.org_code=t1.org_level_code-->
<!--        where t1.delete_time is null-->
<!--        and t2.org_level_code like CONCAT(#{data.orgCode}, '%')-->

<!--        <if test="data.activityName != null and data.activityName != ''">-->
<!--            and t1.activity_name like concat('%', #{data.activityName},'%')-->
<!--        </if>-->
<!--        <if test="data.startTime != null ">-->
<!--            and t1.activity_time &gt;= #{data.startTime}-->
<!--        </if>-->
<!--        <if test="data.endTime != null">-->
<!--            and t1.activity_time &lt;= #{data.startTime}-->
<!--        </if>-->
<!--        <if test="data.d158Code!=null and data.d158Code.size != 0">-->
<!--            <foreach item="item" index="index" collection="data.d158Code" open="AND (" close=")" separator="or">-->
<!--                string_to_array( "t1"."d158_code", ',' ) @> string_to_array(#{item}, ',' )-->
<!--            </foreach>-->
<!--        </if>-->
<!--        GROUP BY t1.code ,t3.name-->
<!--        ORDER BY t1.activity_time  desc-->
<!--    </select>-->

</mapper>
