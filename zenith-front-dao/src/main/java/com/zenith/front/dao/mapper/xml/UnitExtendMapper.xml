<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zenith.front.dao.mapper.unit.UnitExtendMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.zenith.front.model.bean.UnitExtend">
        <id column="id" property="id"/>
        <result column="code" property="code"/>
        <result column="is_czglbm" property="isCzglbm"/>
        <result column="b30_1" property="b301"/>
        <result column="has_major_deputy_secretary" property="hasMajorDeputySecretary"/>
        <result column="d80_code" property="d80Code"/>
        <result column="on_post_num" property="onPostNum"/>
        <result column="b30_a12" property="b30A12"/>
        <result column="member_num" property="memberNum"/>
        <result column="is_dfzgbm" property="isDfzgbm"/>
        <result column="under_thirty_five_num" property="underThirtyFiveNum"/>
        <result column="principal" property="principal"/>
        <result column="is_unit_mem" property="isUnitMem"/>
        <result column="is_committee_represent" property="isCommitteeRepresent"/>
        <result column="is_org_contact" property="isOrgContact"/>
        <result column="is_vol_team" property="isVolTeam"/>
        <result column="d17_code" property="d17Code"/>
        <result column="d16_code" property="d16Code"/>
        <result column="d79_code" property="d79Code"/>
        <result column="tec_num" property="tecNum"/>
        <result column="worker_num" property="workerNum"/>
        <result column="is_work_union" property="isWorkUnion"/>
        <result column="is_work_union_member" property="isWorkUnionMember"/>
        <result column="is_teenager" property="isTeenager"/>
        <result column="is_teenager_member" property="isTeenagerMember"/>
        <result column="is_women_federation" property="isWomenFederation"/>
        <result column="is_women_federation_member" property="isWomenFederationMember"/>
        <result column="bZT6_10" property="bzt610"/>
        <result column="d78_code" property="d78Code"/>
        <result column="is_org_service" property="isOrgService"/>
        <result column="is_org_instructor" property="isOrgInstructor"/>
        <result column="is_decoupl_industry" property="isDecouplIndustry"/>
        <result column="lslxgw" property="lslxgw"/>
        <result column="lsjggw" property="lsjggw"/>
        <result column="lshyzg" property="lshyzg"/>
        <result column="sjyhyzg" property="sjyhyzg"/>
        <result column="zzgzry" property="zzgzry"/>
        <result column="d77_code" property="d77Code"/>
        <result column="zaigang_gaoji" property="zaigangGaoji"/>
        <result column="house_num" property="houseNum"/>
        <result column="house_person_num" property="housePersonNum"/>
        <result column="permanent_population" property="permanentPopulation"/>
        <result column="last_year_income" property="lastYearIncome"/>
        <result column="d76_code" property="d76Code"/>
        <result column="bZT4_4" property="bzt44"/>
        <result column="bZT4_5" property="bzt45"/>
        <result column="bZT4_6" property="bzt46"/>
        <result column="bZT4_7" property="bzt47"/>
        <result column="is_poor_village" property="isPoorVillage"/>
        <result column="is_village_committee" property="isVillageCommittee"/>
        <result column="natural_village_num" property="naturalVillageNum"/>
        <result column="collective_economy_gdp" property="collectiveEconomyGdp"/>
        <result column="first_sec_name" property="firstSecName"/>
        <result column="first_sec_id" property="firstSecId"/>
        <result column="total_year" property="totalYear"/>
        <result column="b32_12" property="b3212"/>
        <result column="bZT4_12" property="bzt412"/>
        <result column="bZT4_29" property="bzt429"/>
        <result column="bZT4_10" property="bzt410"/>
        <result column="bZT4_26" property="bzt426"/>
        <result column="b32_10" property="b3210"/>
        <result column="bZT4_11" property="bzt411"/>
        <result column="b32_15" property="b3215"/>
        <result column="b32_5" property="b325"/>
        <result column="bZT4_14" property="bzt414"/>
        <result column="b32_3" property="b323"/>
        <result column="b32_11" property="b3211"/>
        <result column="bZT4_16" property="bzt416"/>
        <result column="bZT4_27" property="bzt427"/>
        <result column="b32_2" property="b322"/>
        <result column="bZT4_18" property="bzt418"/>
        <result column="bZT4_20" property="bzt420"/>
        <result column="b32_9" property="b329"/>
        <result column="bZT4_15" property="bzt415"/>
        <result column="b32_6" property="b326"/>
        <result column="bZT4_9" property="bzt49"/>
        <result column="bZT4_28" property="bzt428"/>
        <result column="b32_4" property="b324"/>
        <result column="bZT4_17" property="bzt417"/>
        <result column="b32_7" property="b327"/>
        <result column="bZT4_8" property="bzt48"/>
        <result column="bZT4_22" property="bzt422"/>
        <result column="bZT4_13" property="bzt413"/>
        <result column="bZT4_19" property="bzt419"/>
        <result column="b32_14" property="b3214"/>
        <result column="bZT4_25" property="bzt425"/>
        <result column="bZT4_21" property="bzt421"/>
        <result column="bZT4_24" property="bzt424"/>
        <result column="bZT4_23" property="bzt423"/>
        <result column="b32_8" property="b328"/>
        <result column="b32_13" property="b3213"/>
        <result column="b6_1" property="b61"/>
        <result column="b6_2" property="b62"/>
        <result column="b6_3" property="b63"/>
        <result column="b6_4" property="b64"/>
        <result column="b6_5" property="b65"/>
        <result column="b6_6" property="b66"/>
        <result column="b6_7" property="b67"/>
        <result column="b6_8" property="b68"/>
        <result column="b6_9" property="b69"/>
        <result column="b6_10" property="b610"/>
        <result column="is_worke_committee" property="isWorkeCommittee"/>
        <result column="start_date" property="startDate"/>
        <result column="main_party_secretary" property="mainPartySecretary"/>
        <result column="org_leader_charge" property="orgLeaderCharge"/>
        <result column="d81_code" property="d81Code"/>
        <result column="ldtzyyzc" property="ldtzyyzc"/>
        <result column="is_party_work_write" property="isPartyWorkWrite"/>
        <result column="is_open_org_assess" property="isOpenOrgAssess"/>
        <result column="is_leader_separate" property="isLeaderSeparate"/>
        <result column="leader_is_gcdy" property="leaderIsGcdy"/>
        <result column="is_leader_deputy_secretary" property="isLeaderDeputySecretary"/>
        <result column="is_set_org_party" property="isSetOrgParty"/>
        <result column="secretary_is_inside_leader" property="secretaryIsInsideLeader"/>
        <result column="sjsdtr" property="sjsdtr"/>
        <result column="year_train_org_mem" property="yearTrainOrgMem"/>
        <result column="is_year_expiration_org" property="isYearExpirationOrg"/>
        <result column="is_year_org_change" property="isYearOrgChange"/>
        <result column="year_develop_mem" property="yearDevelopMem"/>
        <result column="year_develop_mem_medicine" property="yearDevelopMemMedicine"/>
        <result column="rdjjfz" property="rdjjfz"/>
        <result column="b7_3" property="b73"/>
        <result column="b7_4" property="b74"/>
        <result column="b7_1" property="b71"/>
        <result column="b7_2" property="b72"/>
        <result column="b7_8" property="b78"/>
        <result column="b7_5" property="b75"/>
        <result column="b7_6" property="b76"/>
        <result column="b7_7" property="b77"/>
        <result column="create_time" property="createTime"/>
        <result column="update_time" property="updateTime"/>
        <result column="delete_time" property="deleteTime"/>
        <result column="has_standing_committee" property="hasStandingCommittee"/>
        <result column="d109_code" property="d109Code"/>
        <result column="has_report_implementation" property="hasReportImplementation"/>
        <result column="has_office_procedure" property="hasOfficeProcedure"/>
        <result column="school_has_reports_local" property="schoolHasReportsLocal"/>
        <result column="has_secretary_university_committee" property="hasSecretaryUniversityCommittee"/>
        <result column="has_president_party_member" property="hasPresidentPartyMember"/>
        <result column="has_deputy_party_secretary" property="hasDeputyPartySecretary"/>
        <result column="d95_code" property="d95Code"/>
        <result column="d95_name" property="d95Name"/>
        <result column="d110_code" property="d110Code"/>
        <result column="d110_name" property="d110Name"/>
        <result column="d109_name" property="d109Name"/>
        <result column="d111_code" property="d111Code"/>
        <result column="d111_name" property="d111Name"/>
        <result column="has_responsibility_system" property="hasResponsibilitySystem"/>
        <result column="has_dean_party_member" property="hasDeanPartyMember"/>
        <result column="has_dean_party_secretary" property="hasDeanPartySecretary"/>
        <result column="has_sjsdtr" property="hasSjsdtr"/>
        <result column="has_secretaryisinsideleader" property="hasSecretaryisinsideleader"/>
        <result column="has_dean_responsibility_system" property="hasDeanResponsibilitySystem"/>
        <result column="d112_code" property="d112Code"/>
        <result column="d112_name" property="d112Name"/>
        <result column="d114_code" property="d114Code"/>
        <result column="has_party_work" property="hasPartyWork"/>
        <result column="has_firm_level" property="hasFirmLevel"/>
        <result column="firm_level_name" property="firmLevelName"/>
        <result column="d115_code" property="d115Code"/>
        <result column="has_directors" property="hasDirectors"/>
        <result column="has_chairman_secretary" property="hasChairmanSecretary"/>
        <result column="has_proportionate_funding" property="hasProportionateFunding"/>
        <result column="has_branch_to_catch" property="hasBranchToCatch"/>
        <result column="has_by_leader" property="hasByLeader"/>
        <result column="has_same_treatment" property="hasSameTreatment"/>
        <result column="has_public_company" property="hasPublicCompany"/>
        <result column="has_articles_incorporation" property="hasArticlesIncorporation"/>
        <result column="has_prepositional_procedure" property="hasPrepositionalProcedure"/>
        <result column="has_responsible_person" property="hasResponsiblePerson"/>
        <result column="branches" property="branches"/>
        <result column="party_organization_num" property="partyOrganizationNum"/>
        <result column="have_been_established" property="haveBeenEstablished"/>
        <result column="party_members" property="partyMembers"/>
        <result column="has_non_public_party" property="hasNonPublicParty"/>
        <result column="has_special_agencies" property="hasSpecialAgencies"/>
        <result column="staff_office_numbers" property="staffOfficeNumbers"/>
        <result column="non_public_enterprises" property="nonPublicEnterprises"/>
        <result column="has_community_access" property="hasCommunityAccess"/>
        <result column="has_joint_units" property="hasJointUnits"/>
        <result column="has_lower_social" property="hasLowerSocial"/>
        <result column="community_building_number" property="communityBuildingNumber"/>
        <result column="has_community_positions" property="hasCommunityPositions"/>
        <result column="community_office_space" property="communityOfficeSpace"/>
        <result column="has_parttime_system" property="hasParttimeSystem"/>
        <result column="has_poor_village" property="hasPoorVillage"/>
        <result column="has_four_two_open_work" property="hasFourTwoOpenWork"/>
        <result column="has_community_supervisory" property="hasCommunitySupervisory"/>
        <result column="employees_number" property="employeesNumber"/>
        <result column="has_instructor_contact" property="hasInstructorContact"/>
        <result column="has_union_organization" property="hasUnionOrganization"/>
        <result column="not_turned_party" property="notTurnedParty"/>
        <result column="has_organization_secretary" property="hasOrganizationSecretary"/>
        <result column="technical_personnel" property="technicalPersonnel"/>
        <result column="party_senior_title" property="partySeniorTitle"/>
        <result column="has_volunteer_organization" property="hasVolunteerOrganization"/>
        <result column="has_examination_power" property="hasExaminationPower"/>
        <result column="has_cancel_investment_promotion" property="hasCancelInvestmentPromotion"/>
        <result column="has_work_mechanism" property="hasWorkMechanism"/>
        <result column="has_included_committee" property="hasIncludedCommittee"/>
        <result column="has_group_service_center" property="hasGroupServiceCenter"/>
        <result column="has_party_build_endeavor" property="hasPartyBuildEndeavor"/>
        <result column="has_representative" property="hasRepresentative"/>
        <result column="has_proper_secretary" property="hasProperSecretary"/>
        <result column="absorbed_tissue_number" property="absorbedTissueNumber"/>
        <result column="has_head_party" property="hasHeadParty"/>
        <result column="included_financial" property="includedFinancial"/>
        <result column="special_funds_masses" property="specialFundsMasses"/>
        <result column="has_community_report" property="hasCommunityReport"/>
        <result column="has_secretary_committee" property="hasSecretaryCommittee"/>
        <result column="has_tissue_committee" property="hasTissueCommittee"/>
        <result column="has_propaganda_committee" property="hasPropagandaCommittee"/>
        <result column="has_front_committee" property="hasFrontCommittee"/>
        <result column="community_workers_salary" property="communityWorkersSalary"/>
        <result column="community_secretary_salary" property="communitySecretarySalary"/>
        <result column="above_bk_education" property="aboveBkEducation"/>
        <result column="above_yjs_education" property="aboveYjsEducation"/>
        <result column="has_secretary_high_level" property="hasSecretaryHighLevel"/>
        <result column="has_level_secretary" property="hasLevelSecretary"/>
        <result column="household_registration" property="householdRegistration"/>
        <result column="registered_population" property="registeredPopulation"/>
        <result column="has_industry_province" property="hasIndustryProvince"/>
        <result column="graduate_student" property="graduateStudent"/>
        <result column="undergraduate_student" property="undergraduateStudent"/>
        <result column="junior_college_student" property="juniorCollegeStudent"/>
        <result column="middle_technical_students" property="middleTechnicalStudents"/>
        <result column="teachers_institutions_higher" property="teachersInstitutionsHigher"/>
        <result column="teachers_higher_women" property="teachersHigherWomen"/>
        <result column="teachers_age_thirty_five_below" property="teachersAgeThirtyFiveBelow"/>
        <result column="report_community_member" property="reportCommunityMember"/>
        <result column="has_clerk_position" property="hasClerkPosition"/>
        <result column="has_secretary_economy" property="hasSecretaryEconomy"/>
        <result column="technical_secondary_student" property="technicalSecondaryStudent"/>
        <result column="is_allocate_dean" property="isAllocateDean"/>
        <result column="is_allocate_secretary" property="isAllocateSecretary"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id
        , code, is_czglbm, b30_1, has_major_deputy_secretary, d80_code, on_post_num, b30_a12, member_num, is_dfzgbm, under_thirty_five_num, principal, is_unit_mem, is_committee_represent, is_org_contact, is_vol_team, d17_code, d16_code, d79_code, tec_num, worker_num, is_work_union, is_work_union_member, is_teenager, is_teenager_member, is_women_federation, is_women_federation_member, bZT6_10, d78_code, is_org_service, is_org_instructor, is_decoupl_industry, lslxgw, lsjggw, lshyzg, sjyhyzg, zzgzry, d77_code, zaigang_gaoji, house_num, house_person_num, permanent_population, last_year_income, d76_code, bZT4_4, bZT4_5, bZT4_6, bZT4_7, is_poor_village, is_village_committee, natural_village_num, collective_economy_gdp, first_sec_name, first_sec_id, total_year, b32_12, bZT4_12, bZT4_29, bZT4_10, bZT4_26, b32_10, bZT4_11, b32_15, b32_5, bZT4_14, b32_3, b32_11, bZT4_16, bZT4_27, b32_2, bZT4_18, bZT4_20, b32_9, bZT4_15, b32_6, bZT4_9, bZT4_28, b32_4, bZT4_17, b32_7, bZT4_8, bZT4_22, bZT4_13, bZT4_19, b32_14, bZT4_25, bZT4_21, bZT4_24, bZT4_23, b32_8, b32_13, b6_1, b6_2, b6_3, b6_4, b6_5, b6_6, b6_7, b6_8, b6_9, b6_10, is_worke_committee, start_date, main_party_secretary, org_leader_charge, d81_code, ldtzyyzc, is_party_work_write, is_open_org_assess, is_leader_separate, leader_is_gcdy, is_leader_deputy_secretary, is_set_org_party, secretary_is_inside_leader, sjsdtr, year_train_org_mem, is_year_expiration_org, is_year_org_change, year_develop_mem, year_develop_mem_medicine, rdjjfz, b7_3, b7_4, b7_1, b7_2, b7_8, b7_5, b7_6, b7_7, create_time, update_time, delete_time, has_standing_committee, d109_code, has_report_implementation, has_office_procedure, school_has_reports_local, has_secretary_university_committee, has_president_party_member, has_deputy_party_secretary, d95_code, d95_name, d110_code, d110_name, d109_name, d111_code, d111_name, has_responsibility_system, has_dean_party_member, has_dean_party_secretary, has_sjsdtr, has_secretaryisinsideleader, has_dean_responsibility_system, d112_code, d112_name, d114_code, has_party_work, has_firm_level, firm_level_name, d115_code, has_directors, has_chairman_secretary, has_proportionate_funding, has_branch_to_catch, has_by_leader, has_same_treatment, has_public_company, has_articles_incorporation, has_prepositional_procedure, has_responsible_person, branches, party_organization_num, have_been_established, party_members, has_non_public_party, has_special_agencies, staff_office_numbers, non_public_enterprises, has_community_access, has_joint_units, has_lower_social, community_building_number, has_community_positions, community_office_space, has_parttime_system, has_poor_village, has_four_two_open_work, has_community_supervisory, employees_number, has_instructor_contact, has_union_organization, not_turned_party, has_organization_secretary, technical_personnel, party_senior_title, has_volunteer_organization, has_examination_power, has_cancel_investment_promotion, has_work_mechanism, has_included_committee, has_group_service_center, has_party_build_endeavor, has_representative, has_proper_secretary, absorbed_tissue_number, has_head_party, included_financial, special_funds_masses, has_community_report, has_secretary_committee, has_tissue_committee, has_propaganda_committee, has_front_committee, community_workers_salary, community_secretary_salary, above_bk_education, above_yjs_education, has_secretary_high_level, has_level_secretary, household_registration, registered_population, has_industry_province, graduate_student, undergraduate_student, junior_college_student, middle_technical_students, teachers_institutions_higher, teachers_higher_women, teachers_age_thirty_five_below, report_community_member, has_clerk_position, has_secretary_economy, technical_secondary_student, is_allocate_dean, is_allocate_secretary
    </sql>
    <update id="updateByIdSql">
        update
            ccp_unit_extend
        set code                               = #{unitExtend.code},
            is_czglbm                          = #{unitExtend.isCzglbm},
            b30_1                              = #{unitExtend.b301},
            has_major_deputy_secretary         = #{unitExtend.hasMajorDeputySecretary},
            d80_code                           = #{unitExtend.d80Code},
            on_post_num                        = #{unitExtend.onPostNum},
            b30_a12                            = #{unitExtend.b30A12},
            member_num                         = #{unitExtend.memberNum},
            is_dfzgbm                          = #{unitExtend.isDfzgbm},
            under_thirty_five_num              = #{unitExtend.underThirtyFiveNum},
            principal                          = #{unitExtend.principal},
            is_unit_mem                        = #{unitExtend.isUnitMem},
            is_committee_represent             = #{unitExtend.isCommitteeRepresent},
            is_org_contact                     = #{unitExtend.isOrgContact},
            is_vol_team                        = #{unitExtend.isVolTeam},
            d17_code                           = #{unitExtend.d17Code},
            d16_code                           = #{unitExtend.d16Code},
            d79_code                           = #{unitExtend.d79Code},
            tec_num                            = #{unitExtend.tecNum},
            worker_num                         = #{unitExtend.workerNum},
            is_work_union                      = #{unitExtend.isWorkUnion},
            is_work_union_member               = #{unitExtend.isWorkUnionMember},
            is_teenager                        = #{unitExtend.isTeenager},
            is_teenager_member                 = #{unitExtend.isTeenagerMember},
            is_women_federation                = #{unitExtend.isWomenFederation},
            is_women_federation_member         = #{unitExtend.isWomenFederationMember},
            "bZT6_10"                          = #{unitExtend.bzt610},
            d78_code                           = #{unitExtend.d78Code},
            is_org_service                     = #{unitExtend.isOrgService},
            is_org_instructor                  = #{unitExtend.isOrgInstructor},
            is_decoupl_industry                = #{unitExtend.isDecouplIndustry},
            lslxgw                             = #{unitExtend.lslxgw},
            lsjggw                             = #{unitExtend.lsjggw},
            lshyzg                             = #{unitExtend.lshyzg},
            sjyhyzg                            = #{unitExtend.sjyhyzg},
            zzgzry                             = #{unitExtend.zzgzry},
            d77_code                           = #{unitExtend.d77Code},
            zaigang_gaoji                      = #{unitExtend.zaigangGaoji},
            house_num                          = #{unitExtend.houseNum},
            house_person_num                   = #{unitExtend.housePersonNum},
            permanent_population               = #{unitExtend.permanentPopulation},
            last_year_income                   = #{unitExtend.lastYearIncome},
            d76_code                           = #{unitExtend.d76Code},
            "bZT4_4"                           = #{unitExtend.bzt44},
            "bZT4_5"                           = #{unitExtend.bzt45},
            "bZT4_6"                           = #{unitExtend.bzt46},
            "bZT4_7"                           = #{unitExtend.bzt47},
            is_poor_village                    = #{unitExtend.isPoorVillage},
            is_village_committee               = #{unitExtend.isVillageCommittee},
            natural_village_num                = #{unitExtend.naturalVillageNum},
            collective_economy_gdp             = #{unitExtend.collectiveEconomyGdp},
            first_sec_name                     = #{unitExtend.firstSecName},
            first_sec_id                       = #{unitExtend.firstSecId},
            total_year                         = #{unitExtend.totalYear},
            b32_12                             = #{unitExtend.b3212},
            "bZT4_12"                          = #{unitExtend.bzt412},
            "bZT4_29"                          = #{unitExtend.bzt429},
            "bZT4_10"                          = #{unitExtend.bzt410},
            "bZT4_26"                          = #{unitExtend.bzt426},
            "b32_10"                           = #{unitExtend.b3210},
            "bZT4_11"                          = #{unitExtend.bzt411},
            "b32_15"                           = #{unitExtend.b3215},
            "b32_5"                            = #{unitExtend.b325},
            "bZT4_14"                          = #{unitExtend.bzt414},
            "b32_3"                            = #{unitExtend.b323},
            "b32_11"                           = #{unitExtend.b3211},
            "bZT4_16"                          = #{unitExtend.bzt416},
            "bZT4_27"                          = #{unitExtend.bzt427},
            "b32_2"                            = #{unitExtend.b322},
            "bZT4_18"                          = #{unitExtend.bzt418},
            "bZT4_20"                          = #{unitExtend.bzt420},
            "b32_9"                            = #{unitExtend.b329},
            "bZT4_15"                          = #{unitExtend.bzt415},
            "b32_6"                            = #{unitExtend.b326},
            "bZT4_9"                           = #{unitExtend.bzt49},
            "bZT4_28"                          = #{unitExtend.bzt428},
            "b32_4"                            = #{unitExtend.b324},
            "bZT4_17"                          = #{unitExtend.bzt417},
            "b32_7"                            = #{unitExtend.b327},
            "bZT4_8"                           = #{unitExtend.bzt48},
            "bZT4_22"                          = #{unitExtend.bzt422},
            "bZT4_13"                          = #{unitExtend.bzt413},
            "bZT4_19"                          = #{unitExtend.bzt419},
            "b32_14"                           = #{unitExtend.b3214},
            "bZT4_25"                          = #{unitExtend.bzt425},
            "bZT4_21"                          = #{unitExtend.bzt421},
            "bZT4_24"                          = #{unitExtend.bzt424},
            "bZT4_23"                          = #{unitExtend.bzt423},
            "b32_8"                            = #{unitExtend.b328},
            "b32_13"                           = #{unitExtend.b3213},
            "b6_1"                             = #{unitExtend.b61},
            b6_2                               = #{unitExtend.b62},
            b6_3                               = #{unitExtend.b63},
            b6_4                               = #{unitExtend.b64},
            b6_5                               = #{unitExtend.b65},
            b6_6                               = #{unitExtend.b66},
            b6_7                               = #{unitExtend.b67},
            b6_8                               = #{unitExtend.b68},
            b6_9                               = #{unitExtend.b69},
            b6_10                              = #{unitExtend.b610},
            is_worke_committee                 = #{unitExtend.isWorkeCommittee},
            start_date                         = #{unitExtend.startDate},
            main_party_secretary               = #{unitExtend.mainPartySecretary},
            org_leader_charge                  = #{unitExtend.orgLeaderCharge},
            d81_code                           = #{unitExtend.d81Code},
            ldtzyyzc                           = #{unitExtend.ldtzyyzc},
            is_party_work_write                = #{unitExtend.isPartyWorkWrite},
            is_open_org_assess                 = #{unitExtend.isOpenOrgAssess},
            is_leader_separate                 = #{unitExtend.isLeaderSeparate},
            leader_is_gcdy                     = #{unitExtend.leaderIsGcdy},
            is_leader_deputy_secretary         = #{unitExtend.isLeaderDeputySecretary},
            is_set_org_party                   = #{unitExtend.isSetOrgParty},
            secretary_is_inside_leader         = #{unitExtend.secretaryIsInsideLeader},
            sjsdtr                             = #{unitExtend.sjsdtr},
            year_train_org_mem                 = #{unitExtend.yearTrainOrgMem},
            is_year_expiration_org             = #{unitExtend.isYearExpirationOrg},
            is_year_org_change                 = #{unitExtend.isYearOrgChange},
            year_develop_mem                   = #{unitExtend.yearDevelopMem},
            year_develop_mem_medicine          = #{unitExtend.yearDevelopMemMedicine},
            rdjjfz                             = #{unitExtend.rdjjfz},
            b7_3                               = #{unitExtend.b73},
            b7_4                               = #{unitExtend.b74},
            b7_1                               = #{unitExtend.b71},
            b7_2                               = #{unitExtend.b72},
            b7_8                               = #{unitExtend.b78},
            b7_5                               = #{unitExtend.b75},
            b7_6                               = #{unitExtend.b76},
            b7_7                               = #{unitExtend.b77},
            create_time                        = #{unitExtend.createTime},
            update_time                        = #{unitExtend.updateTime},
            delete_time                        = #{unitExtend.deleteTime},
            has_standing_committee             = #{unitExtend.hasStandingCommittee},
            has_report_implementation          = #{unitExtend.hasReportImplementation},
            has_office_procedure               = #{unitExtend.hasOfficeProcedure},
            school_has_reports_local           = #{unitExtend.schoolHasReportsLocal},
            has_secretary_university_committee = #{unitExtend.hasSecretaryUniversityCommittee},
            has_president_party_member         = #{unitExtend.hasPresidentPartyMember},
            has_deputy_party_secretary         = #{unitExtend.hasDeputyPartySecretary},
            d95_code                           = #{unitExtend.d95Code},
            d95_name                           = #{unitExtend.d95Name},
            d109_code                          = #{unitExtend.d109Code},
            d109_name                          = #{unitExtend.d109Name},
            d110_code                          = #{unitExtend.d110Code},
            d110_name                          = #{unitExtend.d110Name},
            d111_code                          = #{unitExtend.d111Code},
            d111_name                          = #{unitExtend.d111Name},
            has_responsibility_system          = #{unitExtend.hasResponsibilitySystem},
            has_dean_party_member              = #{unitExtend.hasDeanPartyMember},
            has_dean_party_secretary           = #{unitExtend.hasDeanPartySecretary},
            has_sjsdtr                         = #{unitExtend.hasSjsdtr},
            has_secretaryisinsideleader        = #{unitExtend.hasSecretaryisinsideleader},
            has_dean_responsibility_system     = #{unitExtend.hasDeanResponsibilitySystem},
            d112_code                          = #{unitExtend.d112Code},
            d112_name                          = #{unitExtend.d112Name},
            d114_code                          = #{unitExtend.d114Code},
            has_party_work                     = #{unitExtend.hasPartyWork},
            has_firm_level                     = #{unitExtend.hasFirmLevel},
            firm_level_name                    = #{unitExtend.firmLevelName},
            d115_code                          = #{unitExtend.d115Code},
            has_directors                      = #{unitExtend.hasDirectors},
            has_chairman_secretary             = #{unitExtend.hasChairmanSecretary},
            has_proportionate_funding          = #{unitExtend.hasProportionateFunding},
            has_branch_to_catch                = #{unitExtend.hasBranchToCatch},
            has_by_leader                      = #{unitExtend.hasByLeader},
            has_same_treatment                 = #{unitExtend.hasSameTreatment},
            has_public_company                 = #{unitExtend.hasPublicCompany},
            has_articles_incorporation         = #{unitExtend.hasArticlesIncorporation},
            has_prepositional_procedure        = #{unitExtend.hasPrepositionalProcedure},
            has_responsible_person             = #{unitExtend.hasResponsiblePerson},
            branches                           = #{unitExtend.branches},
            party_organization_num             = #{unitExtend.partyOrganizationNum},
            have_been_established              = #{unitExtend.haveBeenEstablished},
            party_members                      = #{unitExtend.partyMembers},
            has_non_public_party               = #{unitExtend.hasNonPublicParty},
            has_special_agencies               = #{unitExtend.hasSpecialAgencies},
            staff_office_numbers               = #{unitExtend.staffOfficeNumbers},
            non_public_enterprises             = #{unitExtend.nonPublicEnterprises},
            has_community_access               = #{unitExtend.hasCommunityAccess},
            has_joint_units                    = #{unitExtend.hasJointUnits},
            has_lower_social                   = #{unitExtend.hasLowerSocial},
            community_workers_salary           = #{unitExtend.communityWorkersSalary},
            community_secretary_salary         = #{unitExtend.communitySecretarySalary},
            community_building_number          = #{unitExtend.communityBuildingNumber},
            has_community_positions            = #{unitExtend.hasCommunityPositions},
            community_office_space             = #{unitExtend.communityOfficeSpace},
            has_parttime_system                = #{unitExtend.hasParttimeSystem},
            has_poor_village                   = #{unitExtend.hasPoorVillage},
            has_four_two_open_work             = #{unitExtend.hasFourTwoOpenWork},
            has_community_supervisory          = #{unitExtend.hasCommunitySupervisory},
            technical_personnel                = #{unitExtend.technicalPersonnel},
            party_senior_title                 = #{unitExtend.partySeniorTitle},
            has_volunteer_organization         = #{unitExtend.hasVolunteerOrganization},
            has_examination_power              = #{unitExtend.hasExaminationPower},
            has_cancel_investment_promotion    = #{unitExtend.hasCancelInvestmentPromotion},
            has_work_mechanism                 = #{unitExtend.hasWorkMechanism},
            has_included_committee             = #{unitExtend.hasIncludedCommittee},
            has_group_service_center           = #{unitExtend.hasGroupServiceCenter},
            has_party_build_endeavor           = #{unitExtend.hasPartyBuildEndeavor},
            has_instructor_contact             = #{unitExtend.hasInstructorContact},
            has_union_organization             = #{unitExtend.hasUnionOrganization},
            not_turned_party                   = #{unitExtend.notTurnedParty},
            has_organization_secretary         = #{unitExtend.hasOrganizationSecretary},
            employees_number                   = #{unitExtend.employeesNumber},
            has_representative                 = #{unitExtend.hasRepresentative},
            has_proper_secretary               = #{unitExtend.hasProperSecretary},
            absorbed_tissue_number             = #{unitExtend.absorbedTissueNumber},
            has_head_party                     = #{unitExtend.hasHeadParty},
            included_financial                 = #{unitExtend.includedFinancial},
            special_funds_masses               = #{unitExtend.specialFundsMasses},
            has_community_report               = #{unitExtend.hasCommunityReport},
            has_secretary_committee            = #{unitExtend.hasSecretaryCommittee},
            has_tissue_committee               = #{unitExtend.hasTissueCommittee},
            has_propaganda_committee           = #{unitExtend.hasPropagandaCommittee},
            has_front_committee                = #{unitExtend.hasFrontCommittee},
            above_bk_education                 = #{unitExtend.aboveBkEducation},
            above_yjs_education                = #{unitExtend.aboveYjsEducation},
            has_secretary_high_level           = #{unitExtend.hasSecretaryHighLevel},
            has_level_secretary                = #{unitExtend.hasLevelSecretary},
            household_registration             = #{unitExtend.householdRegistration},
            registered_population              = #{unitExtend.registeredPopulation},
            has_industry_province              = #{unitExtend.hasIndustryProvince},
            graduate_student                   = #{unitExtend.graduateStudent},
            undergraduate_student              = #{unitExtend.undergraduateStudent},
            junior_college_student             = #{unitExtend.juniorCollegeStudent},
            middle_technical_students          = #{unitExtend.middleTechnicalStudents},
            teachers_institutions_higher       = #{unitExtend.teachersInstitutionsHigher},
            teachers_higher_women              = #{unitExtend.teachersHigherWomen},
            teachers_age_thirty_five_below     = #{unitExtend.teachersAgeThirtyFiveBelow},
            report_community_member            = #{unitExtend.reportCommunityMember},
            has_clerk_position                 = #{unitExtend.hasClerkPosition},
            has_secretary_economy              = #{unitExtend.hasSecretaryEconomy},
            technical_secondary_student        = #{unitExtend.technicalSecondaryStudent},
            is_allocate_dean                   = #{unitExtend.isAllocateDean},
            is_allocate_secretary              = #{unitExtend.isAllocateSecretary},
            has_labour_union                   = #{unitExtend.hasLabourUnion},
            has_youth_league                   = #{unitExtend.hasYouthLeague},
            has_womens_federation              = #{unitExtend.hasWomensFederation},
            has_set_grid                       = #{unitExtend.hasSetGrid},
            has_included_grid_worker           = #{unitExtend.hasIncludedGridWorker},
            d194_code                          = #{unitExtend.d194Code},
            d195_code                          = #{unitExtend.d195Code},
            ysgz_is                            = #{unitExtend.ysgzIs},
            yjldqgt_is                         = #{unitExtend.yjldqgtIs},
            ysldwgzjg_is                       = #{unitExtend.ysldwgzjgIs},
            ypbzzdwgzry_is                     = #{unitExtend.ypbzzdwgzryIs},
            "year"                               = #{unitExtend.year},
            first_secretary_select             = #{unitExtend.firstSecretarySelect},
            first_secretary_code               = #{unitExtend.firstSecretaryCode},
            first_secretary_name               = #{unitExtend.firstSecretaryName},
            secretary_training_num             = #{unitExtend.secretaryTrainingNum},
            has_thousand                       = #{unitExtend.hasThousand},
            has_bundled                        = #{unitExtend.hasBundled},
            promoted_num                       = #{unitExtend.promotedNum},
            adjusted_num                       = #{unitExtend.adjustedNum},
            operating_expenses                 = #{unitExtend.operatingExpenses},
            village_per                        = #{unitExtend.villagePer},
            secretary_salary                   = #{unitExtend.secretarySalary},
            space_area                         = #{unitExtend.spaceArea},
            new_expand_area                    = #{unitExtend.newExpandArea},
            secretary_party_num                = #{unitExtend.secretaryPartyNum},
            secretary_employ_sybz_num          = #{unitExtend.secretaryEmploySybzNum},
            secretary_promoted_num             = #{unitExtend.secretaryPromotedNum},
            community_money_num                = #{unitExtend.communityMoneyNum},
            community_serving_people           = #{unitExtend.communityServingPeople},
            community_masses                   = #{unitExtend.communityMasses},
            has_first_secretary                = #{unitExtend.hasFirstSecretary},
            update_account                     = #{unitExtend.updateAccount},
            unit_community_code                = #{unitExtend.unitCommunityCode}
        where id = #{unitExtend.id}

    </update>
    <select id="findByUnitCodeAndYear" resultType="com.zenith.front.model.bean.UnitExtend">
        SELECT * from ccp_unit_extend WHERE delete_time is null and code = #{unitCode} and year = #{year}
    </select>

</mapper>
