package com.zenith.front.dao.mapper.org;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.zenith.front.model.bean.OrgLife;
import com.zenith.front.model.dto.OrgListDTO;
import com.zenith.front.model.bean.Org;
import com.zenith.front.model.vo.*;

import org.apache.ibatis.annotations.MapKey;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * <p>
 * Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2021-07-10
 */
public interface OrgMapper extends BaseMapper<Org> {

    /***
     * 查询目标组织的上级组织
     * @param orgId 目标组织的orgID
     * @return 目标组织的所有上级组织, 包括自己节点
     * */
    @Select("<script>" + "select * from find_relation_up(#{orgId})" + "</script>")
    List<Org> findAllParentOrg(@Param("orgId") String orgId);

    @Select("<script>" + "select * from find_relation_up(#{subOrgId}) where code = #{parentOrgId}" + "</script>")
    Org isSubOrg(@Param("parentOrgId") String parentOrgId, @Param("subOrgId") String subOrgId);

    Page<FeeAllVO> findOrgCodeByPage(Page<FeeAllVO> page, @Param("allocateOrgCode") String allocateOrgCode, @Param("orgName") String orgName);

    List<Org> findOrgByParentCode(@Param("allocateOrgCode") String allocateOrgCode);

    int updateOrgLeaf(@Param("code") String code, @Param("orgCode") String orgCode);


    int updateNotOrgLeaf(@Param("code") String code, @Param("orgCode") String orgCode);

    Long getOrgTotal(@Param("orgCode") String orgCode, @Param("startDate") Date startDate, @Param("endDate") Date endDate);

    List<DevelopTypeVO> getListRecord(@Param("orgCode") String orgCode, @Param("startDate") Date startDate, @Param("endDate") Date endDate);

    Long getOrgElectTotal(@Param("orgCode") String orgCode, @Param("startDate") Date startDate, @Param("endDate") Date endDate, @Param("toSqlDate") java.sql.Date toSqlDate);

    OptionVO getRecord(@Param("orgCode") String orgCode, @Param("startDate") Date startDate, @Param("endDate") Date endDate);

    OptionVO getDwRatio(@Param("orgCode") String orgCode, @Param("startDate") Date startDate, @Param("endDate") Date endDate);

    OptionVO getDzbRatio(@Param("orgCode") String orgCode, @Param("startDate") Date startDate, @Param("endDate") Date endDate);

    Long getTsdzbRatio(@Param("orgCode") String orgCode, @Param("startDate") Date startDate, @Param("endDate") Date endDate);

    Long getTotal(@Param("orgCode") String orgCode, @Param("startDate") Date startDate, @Param("endDate") Date endDate);

    Long getLhdzbRatio(@Param("orgCode") String orgCode, @Param("startDate") Date startDate, @Param("endDate") Date endDate);

    List<MemAllVO> getD02Ratio(@Param("orgCode") String orgCode, @Param("startDate") Date startDate, @Param("endDate") Date endDate);

    OptionVO getZGTypeOne(@Param("orgCode") String orgCode, @Param("startDate") Date startDate, @Param("endDate") Date endDate);

    OptionVO getZGTypeTwo(@Param("orgCode") String orgCode, @Param("startDate") Date startDate, @Param("endDate") Date endDate);

    OptionVO getDzTypeRatio(@Param("orgCode") String orgCode, @Param("startDate") Date startDate, @Param("endDate") Date endDate);

    OptionVO getGjgzbwTypeRatio(@Param("orgCode") String orgCode, @Param("startDate") Date startDate, @Param("endDate") Date endDate);

    OptionVO getDjcTypeRatio(@Param("orgCode") String orgCode, @Param("startDate") Date startDate, @Param("endDate") Date endDate);

    OptionVO getTszzTypeRatio(@Param("orgCode") String orgCode, @Param("startDate") Date startDate, @Param("endDate") Date endDate);

    OptionVO getLsjcdzzTypeRatio(@Param("orgCode") String orgCode, @Param("startDate") Date startDate, @Param("endDate") Date endDate);

    OptionVO getAffiliationRatio(@Param("orgCode") String orgCode, @Param("startDate") Date startDate, @Param("endDate") Date endDate);

    OptionVO getXzAffiliationRatio(@Param("orgCode") String orgCode, @Param("startDate") Date startDate, @Param("endDate") Date endDate);

    OptionVO getSqjwhAffiliationRatio(@Param("orgCode") String orgCode, @Param("startDate") Date startDate, @Param("endDate") Date endDate);

    List<DevelopTypeVO> getOrgTypeRatio(@Param("orgCode") String orgCode);

    BigDecimal getOrgRatio(@Param("orgCode") String orgCode);

    ElectRatioVO getOrgElectRatio(@Param("orgCode") String orgCode, @Param("format") String format, @Param("toSqlDate") Date toSqlDate);

    List<SubOrgVO> findSubOrgCode(@Param("sql") String sql);

    Map<String, Object> getBasicOrgInfo(@Param("orgCode") String orgCode);

    /**
     * 根据机构层级码查询
     *
     * @param orgCode 机构层级码
     * @return
     */
    Org findOrgByOrgCode(@Param("orgCode") String orgCode);

    /**
     * 根据机构节点长度查询机构
     *
     * @param length 节点层级码长度
     * @return
     */
    List<Org> findOrgNodeByLength(int length);

    /**
     * 递归查询（从子到父）
     *
     * @param code 当前组织code
     * @return
     */
    List<Org> fromSonToFather(@Param("code") String code);

    /**
     * 递归查询（从父到子）
     *
     * @param code 当前组织code
     * @return
     */
    List<Org> fromFatherToSon(@Param("code") String code);


    int saveBatchTmp(Org org);

    /**
     * 更新锁定字段
     *
     * @param lockFields 锁定字段
     * @return
     */
    int updateLockFields(@Param("lockFields") Object lockFields);

    /**
     * 处理到村任职补助经费使用率字段
     *
     * @param orgCode 层级码
     * @return
     */
    List<Org> selectListByOrgCode(@Param("orgCode") String orgCode);

    /**
     * 查询本级和直属下级组织
     *
     * @param orgCode 层级码
     * @return
     */
    List<Org> selectUnderlingByOrgCode(@Param("orgCode") String orgCode);

    /**
     * 直属党组织多于300个的党组织数
     *
     * @param orgCode 层级码
     * @param num     300
     * @return
     */
    List<String> selectDirectOrgGt300(@Param("orgCode") String orgCode, @Param("num") int num);

    /**
     * 组织下所有党总支
     *
     * @param orgCode 层级码
     * @return
     */
    List<String> selectGeneralPartyBranch(@Param("orgCode") String orgCode);

    /**
     * 组织下各支部党员数
     *
     * @param orgCode 层级码
     * @return
     */
    @MapKey("org_code")
    List<Map<String, Object>> selectPartyBranchMemCountList(@Param("orgCode") String orgCode);




    /**
     * 组织下各支部党员数
     *
     * @param orgCode 层级码
     * @return
     */
    @MapKey("org_code")
    List<Map<String, Object>> selectPartyBranchMemCountListNew(@Param("orgCode") String orgCode);

    /**
     * 反查直属党组织多于300个的党组织数
     *
     * @param page    分页
     * @param orgCode 层级码
     * @return
     */
    Page<Org> selectPageDirectOrgGt300(@Param("page") Page<Org> page, @Param("orgCode") String orgCode);

    /**
     * 反查直属党组织多于300个的党组织数
     *
     * @param orgCode 层级码
     * @return
     */
    List<Org> selectListDirectOrgGt300(@Param("orgCode") String orgCode);

    /**
     * 党员数少于3的党支部数
     *
     * @param page    分页
     * @param orgCode 层级码
     * @param left    左区间
     * @param right   右区间
     * @return
     */
    Page<Org> selectPageMemByOrgCode(@Param("page") Page<Org> page, @Param("orgCode") String orgCode, @Param("left") Integer left, @Param("right") Integer right);

    /**
     * 党员数少于3的党支部数
     *
     * @param orgCode 层级码
     * @param left    左区间
     * @param right   右区间
     * @return
     */
    List<Org> selectListMemByOrgCode(@Param("orgCode") String orgCode, @Param("left") Integer left, @Param("right") Integer right);

    /**
     * 配备职务的基层党组织数
     *
     * @param page     分页
     * @param orgCode  层级码
     * @param d022Code 党内职务
     * @return
     */
    Page<Org> findOrgCommitteePageByOrgCode(@Param("page") Page<Org> page, @Param("orgCode") String orgCode, @Param("d022Code") String d022Code);

    Page<Org> findOrgCommitteePageNotEquipByOrgCode(@Param("page") Page<Org> page, @Param("orgCode") String orgCode, @Param("d022Code") String d022Code);

    /**
     * 配备职务的基层党组织数
     *
     * @param orgCode  层级码
     * @param d022Code 党内职务
     * @return
     */
    List<Org> findOrgCommitteeListByOrgCode(@Param("orgCode") String orgCode, @Param("d022Code") String d022Code);

    /**
     * 党员数小于50或大于100的党总支数
     *
     * @param page    分页
     * @param orgCode 层级码
     * @return
     */
    Page<Org> selectGeneralPartyBranchPageByOrgCode(@Param("page") Page<Org> page, @Param("orgCode") String orgCode);

    /**
     * 党员数小于50或大于100的党总支数
     *
     * @param orgCode 层级码
     * @return
     */
    List<Org> selectGeneralPartyBranchListByOrgCode(@Param("orgCode") String orgCode);


    Page<Org> getOrgDevelopRightsList(@Param("page") Page page, @Param("orgListDTO") OrgListDTO orgListDTO);

    /**
     * 查询层级码长度为9的组织
     *
     * @param length 长度
     * @return
     */
    List<String> findNodeByOrgCodeLength(@Param("length") int length);

    /**
     * 查询组织行政区划对应的层级码
     *
     * @return
     */
    List<String> findNodeByOrgAdministrativeRegion();

    List<String> findD01CodeLike63(@Param("orgCode") String orgCode);

    /**
     * 查询党支部总数
     * @param page
     * @param orgCode
     * @return
     */
    Page<Org> selectPagePartyBranchTotal(@Param("page") Page<Org> page, @Param("orgCode") String orgCode);

    /**
     * 基层党委总数,党员数小于100的基层党委数
     * @param page
     * @param orgCode
     * @return
     */
    Page<Org> findPartyCommitteePageByOrgCodeMemLe100(@Param("page") Page<Org> page, @Param("orgCode") String orgCode);

    /**
     * 正式党员数少于3的党支部数，正式党员人数>7的支部数
     * @param page
     * @param orgCode
     * @param left
     * @param right
     * @return
     */
    Page<Org> selectFormalMemPageByOrgCode(@Param("page") Page<Org> page, @Param("orgCode") String orgCode, @Param("left") Integer left, @Param("right") Integer right, @Param("orgCodeList") List<String> orgCodeList);

    List<Org> selectFormalMemPageByOrgCodeSelectSql(@Param("orgCode") String orgCode, @Param("left") Integer left, @Param("right") Integer right, @Param("orgCodeList") List<String> orgCodeList,
                                           @Param("selectSql") String selectSql);
    List<Org> selectFormalMemPageByOrgCodeNew( @Param("orgCode") String orgCode, @Param("left") Integer left, @Param("right") Integer right, @Param("orgCodeList") List<String> orgCodeList);

    List<Org> selectFormalMemPageByOrgCodeNewEncry( @Param("orgCode") String orgCode, @Param("left") Integer left, @Param("right") Integer right, @Param("orgCodeList") List<String> orgCodeList);

    List<MemCountVO> findMemByOrgCode(@Param("orgCodeList") List<String> orgCodeList);

    /**
     * 组织下各支部正式党员数
     *
     * @param orgCode 层级码
     * @return
     */
    @MapKey("orgCode")
    List<Map<String, Object>> selectPartyBranchFormalMemCountList(@Param("orgCode") String orgCode);

    List<BranchCommitteeVo> getListBranchCommitteeLikeOrgCode(@Param("orgCode") String orgCode);

    List<BranchCommitteeVo> getListBranchCommitteeLikeOrgCodeNew(@Param("orgCode") String orgCode);

    Page<Org> selectOrgGroupPageByOrgCode(@Param("page") Page<Org> page, @Param("orgCode") String orgCode, @Param("isSet") int isSet, @Param("startDate") Date startDate, @Param("endDate") Date endDate);

    Page<Org> selectOrgNoLiftMeetingList(Page<Org> page, String orgCode, Date startDate, Date endDate);

    Page<Org> selectOrgNoAppraisalList(Page<Org> page, String orgCode, Date startDate, Date endDate);

    Page<Org> selectOrgNoAppraisalListNew(Page<Org> page, String orgCode, Date startDate, Date endDate);

    Page<Org> selectPagePartyGeneralBranchTotal(Page<Org> page, String orgCode);

    @MapKey("orgCode")
    List<Map<String, Object>> selectPartyGeneralBranchMemCountMap(String orgCode, String isHav);

    @MapKey("orgCode")
    List<Map<String, Object>> selectBasePartyCommitteeMemCountMap(String orgCode, String isHav);

    @MapKey("orgCode")
    List<Map<String, Object>> selectPartyMemCountMap(@Param("orgCode") String orgCode, @Param("isHav") String isHav, @Param("d01Code") String d01Code, @Param("isFormal") String isFormal);

    Page<Org> selectOrgPageByOrgCodeList(@Param("page") Page<Org> page, @Param("orgCodeList") List<String> orgCodeList);

    Page<Org> selectOrgByShouldChangeTerm(@Param("page") Page<Org> page, @Param("orgCode") String orgCode);

    List<String> getAllOrgCodeByD01CodeToReportNewExcel(@Param("orgCode") List<String> orgCode);

    List<Org> selectOrgIsDzzOrDw(String orgCode);

    List<Org> getOrgNameByOrgCodeList(@Param("orgCode") List<String> orgCode);

    /**
     * 是否遵义节点
     * @return
     */
    Integer hasZunyiOrgNode(@Param("orgList") List<String> orgList);
}
