package com.zenith.front.dao.mapper.fee;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.zenith.front.common.encrypt.typehandler.EncryptTypeHandler;
import com.zenith.front.model.dto.FeeListDTO;
import com.zenith.front.model.bean.FeeOrder;
import com.zenith.front.model.vo.ExcelDecryptMap;
import com.zenith.front.model.vo.PaymentSituationVO;
import org.apache.ibatis.annotations.*;

import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * <p>
 * Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2021-07-10
 */
@Mapper
public interface FeeOrderMapper extends BaseMapper<FeeOrder> {

    Map<String, Object> getOneBySql(@Param("sql") String sql);

    @Results(id = "resultMap", value = {
            @Result(column = "人员姓名", property = "name", typeHandler = EncryptTypeHandler.class),
            @Result(column = "身份证号", property = "idcard", typeHandler = EncryptTypeHandler.class)
    })

    @Select("${sql}")
    List<ExcelDecryptMap<String, Object>> getListMapBySql(@Param("sql") String sql);

    Long getTotalBysql(@Param("sql") String sql);

    Page<FeeOrder> getList(@Param("page") Page<FeeOrder> page, @Param("dto") FeeListDTO dto, @Param("beginOfMonth") Date beginOfMonth, @Param("endOfMonth") Date endOfMonth);

    PaymentSituationVO getMoney(@Param("memOrgOrgCode") String memOrgOrgCode);

    List<FeeOrder> getIsPayCount(@Param("memOrgOrgCode") String memOrgOrgCode);
}
