<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zenith.front.dao.mapper.mem.MemRewardMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.zenith.front.model.bean.MemReward">
        <id column="id" property="id" />
        <result column="code" property="code" />
        <result column="es_id" property="esId" />
        <result column="d029_code" property="d029Code" />
        <result column="d029_name" property="d029Name" />
        <result column="mem_code" property="memCode" />
        <result column="reward_org_code" property="rewardOrgCode" />
        <result column="org_code" property="orgCode" />
        <result column="start_date" property="startDate" />
        <result column="end_date" property="endDate" />
        <result column="remark" property="remark" />
        <result column="file_number" property="fileNumber" />
        <result column="d030_code" property="d030Code" />
        <result column="d030_name" property="d030Name" />
        <result column="org_name" property="orgName" />
        <result column="d52_name" property="d52Name" />
        <result column="d52_code" property="d52Code" />
        <result column="d51_code" property="d51Code" />
        <result column="d51_name" property="d51Name" />
        <result column="type" property="type" />
        <result column="delete_time" property="deleteTime" />
        <result column="create_time" property="createTime" />
        <result column="update_time" property="updateTime" />
        <result column="org_entry_code" property="orgEntryCode" />
        <result column="org_entry_name" property="orgEntryName" />
        <result column="timestamp" property="timestamp" />
        <result column="commendation" property="commendation" />
        <result column="zb_code" property="zbCode" />
        <result column="is_history" property="isHistory" />
        <result column="update_account" property="updateAccount" />
        <result column="d47_name" property="d47Name" />
        <result column="d47_code" property="d47Code" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, code, es_id, d029_code, d029_name, mem_code, reward_org_code, org_code, start_date, end_date, remark, file_number, d030_code, d030_name, org_name, d52_name, d52_code, d51_code, d51_name, type, delete_time, create_time, update_time, org_entry_code, org_entry_name, timestamp, commendation, zb_code, is_history, update_account, d47_name, d47_code
    </sql>

</mapper>
