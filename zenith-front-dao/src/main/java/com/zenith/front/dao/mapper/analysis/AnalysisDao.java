package com.zenith.front.dao.mapper.analysis;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.SelectProvider;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Map;

import static com.zenith.front.dao.mapper.analysis.SqlProvider.*;

/**
 * <AUTHOR>
 * @since 2021/7/29 10:38
 */
@Repository
public interface AnalysisDao extends BaseMapper<Object> {
    @Select("SELECT * FROM report_rule_config order by id;")
    List<Map<String, Object>> findAllReport();

    @SelectProvider(type = SqlProvider.class, method = "getSql")
    List<Map<String, Object>> findList(@Param(SQL_FIELD) String sql);

    @SelectProvider(type = SqlProvider.class, method = "getSql")
    Map<String, Object> findFirst(@Param(SQL_FIELD) String sql);

    @SelectProvider(type = SqlProvider.class, method = "getPageSql")
    Page<Map<String, Object>> findPage(Page<Map<String, Object>> page, @Param(SQL_FIELD) String sql);

    @Select("select * from report_rule2020_config where year = #{year} order by id;")
    List<Map<String, Object>> findAll2020Report(@Param("year") Integer year);

    @Select("select * from report_rule${year}_config order by id;")
    List<Map<String, Object>> findPreviousYears(String year);
}
