package com.zenith.front.dao.mapper.org;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.zenith.front.model.bean.OrgSlackRectification;
import com.zenith.front.model.vo.OrgSlackListVo;
import com.zenith.front.model.vo.OrgSlackRectificationListVo;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

/**
 * <AUTHOR>
 * @date 2021/8/27
 */
public interface OrgSlackRectificationMapper extends BaseMapper<OrgSlackRectification> {

    @Select("${sql}")
    Page<OrgSlackListVo> getListPage(@Param("page") Page<OrgSlackRectificationListVo> page, @Param("sql") String sql);
}
