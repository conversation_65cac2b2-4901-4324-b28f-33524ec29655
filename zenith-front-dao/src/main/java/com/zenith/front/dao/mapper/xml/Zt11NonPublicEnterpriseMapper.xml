<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zenith.front.mapper.Zt11NonPublicEnterpriseMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.zenith.front.model.bean.Zt11NonPublicEnterprise">
        <id column="code" property="code" />
        <result column="unit_code" property="unitCode" />
        <result column="rely_organ_non_public_committee_num" property="relyOrganNonPublicCommitteeNum" />
        <result column="offices_non_public_committee_num" property="officesNonPublicCommitteeNum" />
        <result column="offices_staffing_num" property="officesStaffingNum" />
        <result column="direct_manage_non_public_organ_num" property="directManageNonPublicOrganNum" />
        <result column="direct_contact_non_public_organ_num" property="directContactNonPublicOrganNum" />
        <result column="secretary_num" property="secretaryNum" />
        <result column="secretary_hold_middle_manager_num" property="secretaryHoldMiddleManagerNum" />
        <result column="above_college_num" property="aboveCollegeNum" />
        <result column="principal_party_num" property="principalPartyNum" />
        <result column="principal_non_party_num" property="principalNonPartyNum" />
        <result column="special_financial" property="specialFinancial" />
        <result column="fee_subsidies" property="feeSubsidies" />
        <result column="activity_service_center_num" property="activityServiceCenterNum" />
        <result column="when_the_new_num" property="whenTheNewNum" />
        <result column="create_time" property="createTime" />
        <result column="update_time" property="updateTime" />
        <result column="delete_time" property="deleteTime" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        code, unit_code, rely_organ_non_public_committee_num, offices_non_public_committee_num, offices_staffing_num, direct_manage_non_public_organ_num, direct_contact_non_public_organ_num, secretary_num, secretary_hold_middle_manager_num, above_college_num, principal_party_num, principal_non_party_num, special_financial, fee_subsidies, activity_service_center_num, when_the_new_num, create_time, update_time, delete_time
    </sql>

</mapper>
