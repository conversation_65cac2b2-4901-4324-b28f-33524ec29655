package com.zenith.front.dao.mapper.unit;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.zenith.front.model.bean.UnitIncome;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * <p>
 * 收入情况 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2021-09-10
 */
public interface UnitIncomeMapper extends BaseMapper<UnitIncome> {

    /**
     * 根据机构层级码查询
     *
     * @param orgCode 机构层级码
     * @return
     */
    List<UnitIncome> selectListByOrgCode(@Param("orgCode") String orgCode);

    /**
     * 根据单位code获取收入支出情况
     *
     * @param unitCode 单位code
     * @return 收入支出情况
     */
    List<UnitIncome> findByUnitCode(@Param("unitCode") String unitCode, @Param("year") Integer year);


    List<UnitIncome> findByUnitIncomeGtZero(@Param("orgLevelCode")  String orgLevelCode);

    List<UnitIncome> findByUnitIncomeGtZeroLefive(@Param("orgLevelCode")  String orgLevelCode);
    List<UnitIncome> findByUnitIncomeBetweenFiftyAndHundred(@Param("orgLevelCode")  String orgLevelCode);
    List<UnitIncome> findByUnitIncomeGtHundred(@Param("orgLevelCode") String orgLevelCode);
    List<UnitIncome> selectCountByOrgCode(@Param("orgLevelCode") String orgLevelCode);

}
