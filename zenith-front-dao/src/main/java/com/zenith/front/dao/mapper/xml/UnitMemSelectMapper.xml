<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zenith.front.dao.mapper.unit.UnitMemSelectMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.zenith.front.model.bean.UnitMemSelect">
        <id column="code" property="code" />
        <result column="mem_type_code" property="memTypeCode" />
        <result column="mem_type_name" property="memTypeName" />
        <result column="mem_name" property="memName" />
        <result column="sex_code" property="sexCode" />
        <result column="sex_name" property="sexName" />
        <result column="mem_idcard" property="memIdcard" />
        <result column="birthday" property="birthday" />
        <result column="d89_code" property="d89Code" />
        <result column="d89_name" property="d89Name" />
        <result column="d07_code" property="d07Code" />
        <result column="d07_name" property="d07Name" />
        <result column="phone" property="phone" />
        <result column="start_time" property="startTime" />
        <result column="end_time" property="endTime" />
        <result column="d144_code" property="d144Code" />
        <result column="d144_name" property="d144Name" />
        <result column="is_double_first" property="isDoubleFirst" />
        <result column="double_first_name" property="doubleFirstName" />
        <result column="leave_time" property="leaveTime" />
        <result column="leave_remark" property="leaveRemark" />
        <result column="unit_code" property="unitCode" />
        <result column="unit_name" property="unitName" />
        <result column="org_level_code" property="orgLevelCode" />
        <result column="is_unit_mem" property="isUnitMem" />
        <result column="is_org_mem" property="isOrgMem" />
        <result column="is_cadre_mem" property="isCadreMem" />
        <result column="is_work_mem" property="isWorkMem" />
        <result column="is_reserve_mem" property="isReserveMem" />
        <result column="unit_mem_d25_code" property="unitMemD25Code" />
        <result column="unit_mem_d25_name" property="unitMemD25Name" />
        <result column="unit_mem_file_number" property="unitMemFileNumber" />
        <result column="unit_mem_start_date" property="unitMemStartDate" />
        <result column="unit_mem_reward" property="unitMemReward" />
        <result column="unit_mem_endowment_insurance_for_urban_employees" property="unitMemEndowmentInsuranceForUrbanEmployees" />
        <result column="unit_mem_d0121_code" property="unitMemD0121Code" />
        <result column="unit_mem_d0121_name" property="unitMemD0121Name" />
        <result column="unit_mem_remark" property="unitMemRemark" />
        <result column="unit_mem_is_incumbent" property="unitMemIsIncumbent" />
        <result column="unit_mem_d26_code" property="unitMemD26Code" />
        <result column="unit_mem_d26_name" property="unitMemD26Name" />
        <result column="unit_mem_elect_code" property="unitMemElectCode" />
        <result column="org_mem_d022_code" property="orgMemD022Code" />
        <result column="org_mem_d022_name" property="orgMemD022Name" />
        <result column="org_mem_duty_explain" property="orgMemDutyExplain" />
        <result column="org_mem_start_date" property="orgMemStartDate" />
        <result column="org_mem_file_number" property="orgMemFileNumber" />
        <result column="org_mem_elect_code" property="orgMemElectCode" />
        <result column="org_mem_has_middle_management" property="orgMemHasMiddleManagement" />
        <result column="org_mem_d121_code" property="orgMemD121Code" />
        <result column="org_mem_d121_name" property="orgMemD121Name" />
        <result column="org_mem_has_part_training" property="orgMemHasPartTraining" />
        <result column="org_mem_reward" property="orgMemReward" />
        <result column="org_mem_endowment_insurance_for_urban_employees" property="orgMemEndowmentInsuranceForUrbanEmployees" />
        <result column="cadre_mem_d140_code" property="cadreMemD140Code" />
        <result column="cadre_mem_d140_name" property="cadreMemD140Name" />
        <result column="cadre_mem_d141_code" property="cadreMemD141Code" />
        <result column="cadre_mem_d141_name" property="cadreMemD141Name" />
        <result column="cadre_mem_start_date" property="cadreMemStartDate" />
        <result column="cadre_mem_resident_date" property="cadreMemResidentDate" />
        <result column="cadre_mem_dispatch_position" property="cadreMemDispatchPosition" />
        <result column="cadre_mem_d197_code" property="cadreMemD197Code" />
        <result column="cadre_mem_d197_name" property="cadreMemD197Name" />
        <result column="work_mem_has_leaders_help_people" property="workMemHasLeadersHelpPeople" />
        <result column="work_mem_subsidies" property="workMemSubsidies" />
        <result column="work_mem_has_party_work" property="workMemHasPartyWork" />
        <result column="work_mem_has_two_one_member" property="workMemHasTwoOneMember" />
        <result column="work_mem_d116_name" property="workMemD116Name" />
        <result column="work_mem_d116_code" property="workMemD116Code" />
        <result column="work_mem_d143_code" property="workMemD143Code" />
        <result column="work_mem_d143_name" property="workMemD143Name" />
        <result column="work_mem_remark" property="workMemRemark" />
        <result column="reserve_mem_remark" property="reserveMemRemark" />
        <result column="reserve_mem_has_leaders_help_people" property="reserveMemHasLeadersHelpPeople" />
        <result column="reserve_mem_subsidies" property="reserveMemSubsidies" />
        <result column="reserve_mem_d143_code" property="reserveMemD143Code" />
        <result column="reserve_mem_d143_name" property="reserveMemD143Name" />
        <result column="reserve_mem_is_work_village" property="reserveMemIsWorkVillage" />
        <result column="reserve_mem_help_unit" property="reserveMemHelpUnit" />
        <result column="reserve_mem_help_mem" property="reserveMemHelpMem" />
        <result column="reserve_mem_now_job" property="reserveMemNowJob" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        code, mem_type_code, mem_type_name, mem_name, sex_code, sex_name, mem_idcard, birthday, d89_code, d89_name, d07_code, d07_name, phone, start_time, end_time, d144_code, d144_name, is_double_first, double_first_name, leave_time, leave_remark, unit_code, unit_name, org_level_code, is_unit_mem, is_org_mem, is_cadre_mem, is_work_mem, is_reserve_mem, unit_mem_d25_code, unit_mem_d25_name, unit_mem_file_number, unit_mem_start_date, unit_mem_reward, unit_mem_endowment_insurance_for_urban_employees, unit_mem_d0121_code, unit_mem_d0121_name, unit_mem_remark, unit_mem_is_incumbent, unit_mem_d26_code, unit_mem_d26_name, unit_mem_elect_code, org_mem_d022_code, org_mem_d022_name, org_mem_duty_explain, org_mem_start_date, org_mem_file_number, org_mem_elect_code, org_mem_has_middle_management, org_mem_d121_code, org_mem_d121_name, org_mem_has_part_training, org_mem_reward, org_mem_endowment_insurance_for_urban_employees, cadre_mem_d140_code, cadre_mem_d140_name, cadre_mem_d141_code, cadre_mem_d141_name, cadre_mem_start_date, cadre_mem_resident_date, cadre_mem_dispatch_position, cadre_mem_d197_code, cadre_mem_d197_name, work_mem_has_leaders_help_people, work_mem_subsidies, work_mem_has_party_work, work_mem_has_two_one_member, work_mem_d116_name, work_mem_d116_code, work_mem_d143_code, work_mem_d143_name, work_mem_remark, reserve_mem_remark, reserve_mem_has_leaders_help_people, reserve_mem_subsidies, reserve_mem_d143_code, reserve_mem_d143_name, reserve_mem_is_work_village, reserve_mem_help_unit, reserve_mem_help_mem, reserve_mem_now_job
    </sql>

</mapper>
