<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zenith.front.dao.mapper.org.OrgLifePartMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.zenith.front.model.bean.OrgLifePart">
        <id column="code" property="code"/>
        <result column="org_life_code" property="orgLifeCode"/>
        <result column="org_code" property="orgCode"/>
        <result column="org_level_code" property="orgLevelCode"/>
        <result column="name" property="name"/>
        <result column="create_time" property="createTime"/>
        <result column="update_time" property="updateTime"/>
        <result column="delete_time" property="deleteTime"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        code
        , org_life_code, org_code, org_level_code,name, create_time, update_time, delete_time
    </sql>

</mapper>
