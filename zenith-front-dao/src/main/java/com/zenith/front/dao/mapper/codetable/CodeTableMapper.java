package com.zenith.front.dao.mapper.codetable;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.zenith.front.model.bean.CodeTable;

import java.util.LinkedHashMap;
import java.util.List;

/**
 * <p>
 * Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2021-07-10
 */
public interface CodeTableMapper extends BaseMapper<CodeTable> {

    /**
     * 查询启用的字典表配置
     *
     * @return
     */
    List<LinkedHashMap<String, Object>> selectListIsUse();
}
