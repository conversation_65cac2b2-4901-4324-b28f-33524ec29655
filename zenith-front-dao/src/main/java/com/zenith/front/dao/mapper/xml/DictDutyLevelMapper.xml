<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zenith.front.dao.mapper.dict.DictDutyLevelMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.zenith.front.model.bean.DictDutyLevel">
        <id column="id" property="id" />
        <result column="key" property="key" />
        <result column="name" property="name" />
        <result column="parent" property="parent" />
        <result column="is_leaf" property="isLeaf" />
        <result column="sort" property="sort" />
        <result column="disabled" property="disabled" />
        <result column="remark" property="remark" />
        <result column="enabled" property="enabled" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, key, name, parent, is_leaf, sort, disabled, remark, enabled
    </sql>

</mapper>
