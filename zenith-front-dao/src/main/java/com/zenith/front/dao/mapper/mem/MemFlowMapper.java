package com.zenith.front.dao.mapper.mem;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.zenith.front.dao.mapper.analysis.SqlProvider;
import com.zenith.front.model.bean.MemFlow;
import com.zenith.front.model.vo.MemFlow1VO;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.SelectProvider;

import java.util.List;
import java.util.Map;

/**
 * <p>
 * Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2021-07-10
 */
public interface MemFlowMapper extends BaseMapper<MemFlow> {

    /**
     * 根据层级码查询行政村下面得流出党员
     *
     * @param orgCode 层级码
     * @return
     */
    List<MemFlow> selectListByOrgCode(@Param("orgCode") String orgCode);

    /**
     * 流入党员
     * @param orgCode
     * @return
     */
    List<MemFlow1VO> exportInflowParty(@Param("orgCode") String orgCode);

    /**
     * 流出党员
     * @param orgCode
     * @return
     */
    List<MemFlow1VO> exportFlowOutParty(@Param("orgCode") String orgCode);

    /**
     * 督察表根据配置sql查询数据
     * @return
     */
    @SelectProvider(type = SqlProvider.class, method = "getSql")
    Map<String, Object> findMapBySql(@Param("sql") String sql);
}
