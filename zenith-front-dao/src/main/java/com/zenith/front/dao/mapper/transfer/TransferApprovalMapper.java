package com.zenith.front.dao.mapper.transfer;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.zenith.front.model.bean.TransferApproval;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2021-07-10
 */
public interface TransferApprovalMapper extends BaseMapper<TransferApproval> {

    /**
     * 组织机构码查询
     *
     * @param orgCode 机构码
     * @return
     */
    List<TransferApproval> selectListByOrgCode(@Param("orgCode") String orgCode);


    void deleteTransferId(@Param("id") String id);

}
