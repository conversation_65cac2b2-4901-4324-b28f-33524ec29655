<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zenith.front.dao.mapper.message.MessageMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.zenith.front.model.bean.Message">
        <id column="id" property="id" />
        <result column="code" property="code" />
        <result column="name" property="name" />
        <result column="context" property="context" />
        <result column="messgae_plan" property="messgaePlan" />
        <result column="messgae_to_codes" property="messgaeToCodes" />
        <result column="file" property="file" />
        <result column="time" property="time" />
        <result column="from_type" property="fromType" />
        <result column="add_account" property="addAccount" />
        <result column="status" property="status" />
        <result column="select_codes" property="selectCodes" />
        <result column="is_reply" property="isReply" />
        <result column="message_type" property="messageType" />
        <result column="reply_codes" property="replyCodes" />
        <result column="crate_org_code" property="crateOrgCode" />
        <result column="create_org_org_code" property="createOrgOrgCode" />
        <result column="create_org_set" property="createOrgSet" />
        <result column="create_time" property="createTime" />
        <result column="update_time" property="updateTime" />
        <result column="delete_time" property="deleteTime" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, code, name, context, messgae_plan, messgae_to_codes, file, time, from_type, add_account, status, select_codes, is_reply, message_type, reply_codes, crate_org_code, create_org_org_code, create_org_set, create_time, update_time, delete_time
    </sql>

</mapper>
