<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zenith.front.dao.mapper.ztdc.Zt3DevelopEconomyMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.zenith.front.model.bean.Zt3DevelopEconomy">
        <id column="code" property="code" />
        <result column="unit_code" property="unitCode" />
        <result column="has_economic_organization" property="hasEconomicOrganization" />
        <result column="has_economic_organization_head" property="hasEconomicOrganizationHead" />
        <result column="eliminate_economic_county" property="eliminateEconomicCounty" />
        <result column="fifty_economic_county" property="fiftyEconomicCounty" />
        <result column="compiled_plan_province" property="compiledPlanProvince" />
        <result column="development_measure_county" property="developmentMeasureCounty" />
        <result column="included_county_leadership_county" property="includedCountyLeadershipCounty" />
        <result column="create_time" property="createTime" />
        <result column="update_time" property="updateTime" />
        <result column="delete_time" property="deleteTime" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        code, unit_code, has_economic_organization, has_economic_organization_head, eliminate_economic_county, fifty_economic_county, compiled_plan_province, development_measure_county, included_county_leadership_county, create_time, update_time, delete_time
    </sql>

</mapper>
