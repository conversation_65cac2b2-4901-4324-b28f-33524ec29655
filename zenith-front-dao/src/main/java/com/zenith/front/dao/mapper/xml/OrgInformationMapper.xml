<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zenith.front.dao.mapper.org.OrgInformationMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.zenith.front.model.bean.OrgInformation">
        <id column="id" property="id"/>
        <result column="code" property="code"/>
        <result column="es_id" property="esId"/>
        <result column="zb_code" property="zbCode"/>
        <result column="org_code" property="orgCode"/>
        <result column="name" property="name"/>
        <result column="short_name" property="shortName"/>
        <result column="pinyin" property="pinyin"/>
        <result column="is_leaf" property="isLeaf"/>
        <result column="parent_code" property="parentCode"/>
        <result column="d01_code" property="d01Code"/>
        <result column="org_type" property="orgType"/>
        <result column="d03_code" property="d03Code"/>
        <result column="d02_code" property="d02Code"/>
        <result column="is_retire" property="isRetire"/>
        <result column="d04_code" property="d04Code"/>
        <result column="d04_name" property="d04Name"/>
        <result column="unit_code" property="unitCode"/>
        <result column="unit_name" property="unitName"/>
        <result column="d35_code" property="d35Code"/>
        <result column="d35_name" property="d35Name"/>
        <result column="d05_code" property="d05Code"/>
        <result column="d05_name" property="d05Name"/>
        <result column="create_time" property="createTime"/>
        <result column="update_time" property="updateTime"/>
        <result column="delete_time" property="deleteTime"/>
        <result column="situation" property="situation"/>
        <result column="congress_situation" property="congressSituation"/>
        <result column="representatives" property="representatives"/>
        <result column="party_committee" property="partyCommittee"/>
        <result column="standing_committee" property="standingCommittee"/>
        <result column="party_alternate_committee" property="partyAlternateCommittee"/>
        <result column="inspection_committee" property="inspectionCommittee"/>
        <result column="inspection_standing_committee" property="inspectionStandingCommittee"/>
        <result column="whole_situation" property="wholeSituation"/>
        <result column="life_situation" property="lifeSituation"/>
        <result column="attend_member" property="attendMember"/>
        <result column="participants" property="participants"/>
        <result column="reviewers_year" property="reviewersYear"/>
        <result column="has_join_reviewers" property="hasJoinReviewers"/>
        <result column="has_end_reviewers" property="hasEndReviewers"/>
        <result column="d16_code" property="d16Code"/>
        <result column="d16_name" property="d16Name"/>
        <result column="has_internal_institutions" property="hasInternalInstitutions"/>
        <result column="has_internal_institutions_transition" property="hasInternalInstitutionsTransition"/>
        <result column="is_dissolve" property="isDissolve"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id
        , code, es_id, zb_code, org_code, name, short_name, pinyin, is_leaf, parent_code, d01_code, org_type, d03_code, d02_code, is_retire, d04_code, d04_name, unit_code, unit_name, d35_code, d35_name, d05_code, d05_name, create_time, update_time, delete_time, situation, congress_situation, representatives, party_committee, standing_committee, party_alternate_committee, inspection_committee, inspection_standing_committee, whole_situation, life_situation, attend_member, participants, reviewers_year, has_join_reviewers, has_end_reviewers, d16_code, d16_name, has_internal_institutions, has_internal_institutions_transition, is_dissolve
    </sql>

</mapper>
