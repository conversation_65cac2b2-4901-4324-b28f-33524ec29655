<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zenith.front.mapper.OrgSlackMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.zenith.front.model.bean.OrgSlack">
        <id column="id" property="id" />
        <result column="code" property="code" />
        <result column="zb_code" property="zbCode" />
        <result column="org_code" property="orgCode" />
        <result column="slack_org_code" property="slackOrgCode" />
        <result column="d74_code" property="d74Code" />
        <result column="d74_name" property="d74Name" />
        <result column="neaten_time" property="neatenTime" />
        <result column="is_filing_poverty_village" property="isFilingPovertyVillage" />
        <result column="early_lack_secretary_num" property="earlyLackSecretaryNum" />
        <result column="current_year_selected" property="currentYearSelected" />
        <result column="early_adjust_secretary_num" property="earlyAdjustSecretaryNum" />
        <result column="current_year_adjusted" property="currentYearAdjusted" />
        <result column="train_secretary_num" property="trainSecretaryNum" />
        <result column="lian_village_leader_num" property="lianVillageLeaderNum" />
        <result column="bao_village_leader_num" property="baoVillageLeaderNum" />
        <result column="select_secretary_num" property="selectSecretaryNum" />
        <result column="twinning_unit_num" property="twinningUnitNum" />
        <result column="two_level_listing_village_num" property="twoLevelListingVillageNum" />
        <result column="wage_special_num" property="wageSpecialNum" />
        <result column="solve_problem_num" property="solveProblemNum" />
        <result column="treat_violate_num" property="treatViolateNum" />
        <result column="create_time" property="createTime" />
        <result column="update_time" property="updateTime" />
        <result column="delete_time" property="deleteTime" />
        <result column="has_neaten" property="hasNeaten" />
        <result column="d04_code" property="d04Code" />
        <result column="neaten_endtime" property="neatenEndTime" />
        <result column="reason" property="reason" />
        <result column="township_assessment" property="townshipAssessment" />
        <result column="county_level_assessment" property="countyLevelAssessment" />
    </resultMap>
    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, code, zb_code, org_code, slack_org_code, d74_code, d74_name, neaten_time, is_filing_poverty_village, early_lack_secretary_num, current_year_selected, early_adjust_secretary_num, current_year_adjusted, train_secretary_num, lian_village_leader_num, bao_village_leader_num, select_secretary_num, twinning_unit_num, two_level_listing_village_num, wage_special_num, solve_problem_num, treat_violate_num, create_time, update_time, delete_time, has_neaten, d04_code, neaten_endtime, reason, township_assessment, county_level_assessment
    </sql>

</mapper>
