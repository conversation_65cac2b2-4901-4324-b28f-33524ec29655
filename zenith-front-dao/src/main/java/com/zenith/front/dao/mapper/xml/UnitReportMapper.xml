<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zenith.front.dao.mapper.unit.UnitReportMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.zenith.front.model.bean.UnitReport">
        <id column="code" property="code" />
        <result column="name" property="name" />
        <result column="d04_code" property="d04Code" />
        <result column="d04_name" property="d04Name" />
        <result column="d35_code" property="d35Code" />
        <result column="d35_name" property="d35Name" />
        <result column="main_org_code" property="mainOrgCode" />
        <result column="main_org_name" property="mainOrgName" />
        <result column="at_number" property="atNumber" />
        <result column="at_administrative" property="atAdministrative" />
        <result column="at_career" property="atCareer" />
        <result column="vacancy" property="vacancy" />
        <result column="vacancy_administrative" property="vacancyAdministrative" />
        <result column="vacancy_career" property="vacancyCareer" />
        <result column="seconded_num" property="secondedNum" />
        <result column="at_proportion" property="atProportion" />
        <result column="provincial_above" property="provincialAbove" />
        <result column="provincial" property="provincial" />
        <result column="city" property="city" />
        <result column="county" property="county" />
        <result column="join_above_county_train_num" property="joinAboveCountyTrainNum" />
        <result column="village_join_urban_worker_num" property="villageJoinUrbanWorkerNum" />
        <result column="join_proportion" property="joinProportion" />
        <result column="create_unit_org_code" property="createUnitOrgCode" />
        <result column="create_org_code" property="createOrgCode" />
        <result column="financial_support_enforced" property="financialSupportEnforced" />
        <result column="enforced" property="enforced" />
        <result column="completed_acceptance_projects" property="completedAcceptanceProjects" />
        <result column="income_obtained" property="incomeObtained" />
        <result column="number_of_students_to_be_transferred_to_the_village" property="numberOfStudentsToBeTransferredToTheVillage" />
        <result column="create_time" property="createTime" />
        <result column="update_time" property="updateTime" />
        <result column="delete_time" property="deleteTime" />
        <result column="number_of_non_governmental_members" property="numberOfNonGovernmentalMembers" />
        <result column="whether_there_are_college_graduates_working_in_the_village" property="whetherThereAreCollegeGraduatesWorkingInTheVillage" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        code, name, d04_code, d04_name, d35_code, d35_name, main_org_code, main_org_name, at_number, at_administrative, at_career, vacancy, vacancy_administrative, vacancy_career, seconded_num, at_proportion, provincial_above, provincial, city, county, join_above_county_train_num, village_join_urban_worker_num, join_proportion, create_unit_org_code, create_org_code, financial_support_enforced, enforced, completed_acceptance_projects, income_obtained, number_of_students_to_be_transferred_to_the_village, create_time, update_time, delete_time, number_of_non_governmental_members, whether_there_are_college_graduates_working_in_the_village
    </sql>

    <select id="selectListByOrgCode" resultMap="BaseResultMap">
        SELECT * from ccp_unit_report WHERE delete_time is null and create_unit_org_code LIKE CONCAT(#{orgCode} , '%')
    </select>

</mapper>
