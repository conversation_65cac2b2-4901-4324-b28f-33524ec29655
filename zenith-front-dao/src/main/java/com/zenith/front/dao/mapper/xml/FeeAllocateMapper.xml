<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zenith.front.dao.mapper.fee.FeeAllocateMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.zenith.front.model.bean.FeeAllocate">
        <id column="id" property="id" />
        <result column="code" property="code" />
        <result column="org_code" property="orgCode" />
        <result column="allocate_org_code" property="allocateOrgCode" />
        <result column="full_mem_num" property="fullMemNum" />
        <result column="pro_mem_num" property="proMemNum" />
        <result column="retirement_mem_num" property="retirementMemNum" />
        <result column="init_org_code_set" property="initOrgCodeSet" />
        <result column="allocate_ratio" property="allocateRatio" />
        <result column="allocate_money" property="allocateMoney" />
        <result column="allocate_type" property="allocateType" />
        <result column="is_allocate" property="isAllocate" />
        <result column="update_account" property="updateAccount" />
        <result column="create_time" property="createTime" />
        <result column="update_time" property="updateTime" />
        <result column="delete_time" property="deleteTime" />
        <result column="allocate_time" property="allocateTime" />
        <result column="money" property="money" />
        <result column="should_pay_money" property="shouldPayMoney" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, code, org_code, allocate_org_code, full_mem_num, pro_mem_num, retirement_mem_num, init_org_code_set, allocate_ratio, allocate_money, allocate_type, is_allocate, update_account, create_time, update_time, delete_time, allocate_time, money, should_pay_money
    </sql>

    <select id="getList" resultType="com.zenith.front.model.bean.FeeAllocate">
        select *
        from "ccp_fee_allocate"
        where (
        "allocate_org_code" IN
        <foreach collection="list" item="item" index="index" open="(" separator="," close=")">
            #{item}
        </foreach>
        and "allocate_time" >= #{beginDate}
        and "allocate_time" &lt;= #{endDate}
        and "delete_time" is null
        )
        order by "allocate_org_code" desc
    </select>

    <select id="findTotalByAllocateTime" resultType="java.lang.Long">
        select count(1)
        from "ccp_fee_allocate"
        where (
        "allocate_org_code" like CONCAT(#{allocateOrgCode},'___')
        and "allocate_time" >= #{beginDate}
        and "allocate_time" &lt;= #{endDate}
        and "delete_time" is null
        )
    </select>

</mapper>
