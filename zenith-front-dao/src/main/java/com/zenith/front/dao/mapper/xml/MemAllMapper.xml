<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zenith.front.dao.mapper.mem.MemAllMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.zenith.front.model.bean.MemAll">
        <id column="id" property="id"/>
        <result column="code" property="code"/>
        <result column="es_id" property="esId"/>
        <result column="name" property="name"/>
        <result column="pinyin" property="pinyin"/>
        <result column="idcard" property="idcard"/>
        <result column="mem_org_code" property="memOrgCode"/>
        <result column="org_name" property="orgName"/>
        <result column="org_zb_code" property="orgZbCode"/>
        <result column="org_code" property="orgCode"/>
        <result column="phone" property="phone"/>
        <result column="other_tel" property="otherTel"/>
        <result column="photo" property="photo"/>
        <result column="join_org_date" property="joinOrgDate"/>
        <result column="full_member_date" property="fullMemberDate"/>
        <result column="dues_standard_name" property="duesStandardName"/>
        <result column="dues_standard_code" property="duesStandardCode"/>
        <result column="dues_price" property="duesPrice"/>
        <result column="last_pay_date" property="lastPayDate"/>
        <result column="dues_paid" property="duesPaid"/>
        <result column="mem_zb_key" property="memZbKey"/>
        <result column="mem_extend_zb_key" property="memExtendZbKey"/>
        <result column="create_time" property="createTime"/>
        <result column="remark" property="remark"/>
        <result column="timestamp" property="timestamp"/>
        <result column="update_time" property="updateTime"/>
        <result column="delete_time" property="deleteTime"/>
        <result column="marry_code" property="marryCode"/>
        <result column="join_work_date" property="joinWorkDate"/>
        <result column="archive_unit" property="archiveUnit"/>
        <result column="home_address" property="homeAddress"/>
        <result column="leave_org_type_name" property="leaveOrgTypeName"/>
        <result column="leave_org_type_code" property="leaveOrgTypeCode"/>
        <result column="leave_org_date" property="leaveOrgDate"/>
        <result column="leave_org_duty_level_name" property="leaveOrgDutyLevelName"/>
        <result column="leave_org_duty_level_code" property="leaveOrgDutyLevelCode"/>
        <result column="leave_reason_name" property="leaveReasonName"/>
        <result column="leave_reason_code" property="leaveReasonCode"/>
        <result column="lost_contact_date" property="lostContactDate"/>
        <result column="lost_contact_type_code" property="lostContactTypeCode"/>
        <result column="lost_contact_type_name" property="lostContactTypeName"/>
        <result column="org_group_id" property="orgGroupId"/>
        <result column="org_group_name" property="orgGroupName"/>
        <result column="technical_name" property="technicalName"/>
        <result column="technical_code" property="technicalCode"/>
        <result column="stratum_type_name" property="stratumTypeName"/>
        <result column="stratum_type_code" property="stratumTypeCode"/>
        <result column="one_line_code" property="oneLineCode"/>
        <result column="one_line_name" property="oneLineName"/>
        <result column="birthday" property="birthday"/>
        <result column="age" property="age"/>
        <result column="sex_code" property="sexCode"/>
        <result column="sex_name" property="sexName"/>
        <result column="nation_code" property="nationCode"/>
        <result column="nation_name" property="nationName"/>
        <result column="native_place_code" property="nativePlaceCode"/>
        <result column="native_place_name" property="nativePlaceName"/>
        <result column="education_code" property="educationCode"/>
        <result column="education_name" property="educationName"/>
        <result column="type_code" property="typeCode"/>
        <result column="type_name" property="typeName"/>
        <result column="job_code" property="jobCode"/>
        <result column="job_name" property="jobName"/>
        <result column="is_farmer" property="isFarmer"/>
        <result column="apply_date" property="applyDate"/>
        <result column="active_date" property="activeDate"/>
        <result column="join_org_party_date" property="joinOrgPartyDate"/>
        <result column="object_date" property="objectDate"/>
        <result column="join_org_mode_name" property="joinOrgModeName"/>
        <result column="join_org_mode_code" property="joinOrgModeCode"/>
        <result column="add_org_type_name" property="addOrgTypeName"/>
        <result column="add_org_type_code" property="addOrgTypeCode"/>
        <result column="prepare_turn_party_code" property="prepareTurnPartyCode"/>
        <result column="prepare_turn_party_name" property="prepareTurnPartyName"/>
        <result column="is_lost" property="isLost"/>
        <result column="large_pay_state" property="largePayState"/>
        <result column="branch_org_zb_code" property="branchOrgZbCode"/>
        <result column="branch_org_name" property="branchOrgName"/>
        <result column="branch_org_key" property="branchOrgKey"/>
        <result column="branch_org_code" property="branchOrgCode"/>
        <result column="out_branch_org_name" property="outBranchOrgName"/>
        <result column="is_dispatch" property="isDispatch"/>
        <result column="is_idcard_repeat" property="isIdcardRepeat"/>
        <result column="is_idcard_legal" property="isIdcardLegal"/>
        <result column="ratio" property="ratio"/>
        <result column="extend_prepar_date" property="extendPreparDate"/>
        <result column="cancel_extend_date_reason" property="cancelExtendDateReason"/>
        <result column="flow_status" property="flowStatus"/>
        <result column="recover_party_reason" property="recoverPartyReason"/>
        <result column="stop_party_reason" property="stopPartyReason"/>
        <result column="work_post" property="workPost"/>
        <result column="settle_area" property="settleArea"/>
        <result column="stop_party_date" property="stopPartyDate"/>
        <result column="recover_party_date" property="recoverPartyDate"/>
        <result column="mem_code" property="memCode"/>
        <result column="open_id" property="openId"/>
        <result column="start_pay_date" property="startPayDate"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, code, es_id, name, pinyin, idcard, mem_org_code, org_name, org_zb_code, org_code, phone, other_tel, photo, join_org_date, full_member_date, dues_standard_name, dues_standard_code, dues_price, last_pay_date, dues_paid, mem_zb_key, mem_extend_zb_key, create_time, remark, timestamp, update_time, delete_time, marry_code, join_work_date, archive_unit, home_address, leave_org_type_name, leave_org_type_code, leave_org_date, leave_org_duty_level_name, leave_org_duty_level_code, leave_reason_name, leave_reason_code, lost_contact_date, lost_contact_type_code, lost_contact_type_name, org_group_id, org_group_name, technical_name, technical_code, stratum_type_name, stratum_type_code, one_line_code, one_line_name, birthday, age, sex_code, sex_name, nation_code, nation_name, native_place_code, native_place_name, education_code, education_name, type_code, type_name, job_code, job_name, is_farmer, apply_date, active_date, join_org_party_date, object_date, join_org_mode_name, join_org_mode_code, add_org_type_name, add_org_type_code, prepare_turn_party_code, prepare_turn_party_name, is_lost, large_pay_state, branch_org_zb_code, branch_org_name, branch_org_key, branch_org_code, out_branch_org_name, is_dispatch, is_idcard_repeat, is_idcard_legal, ratio, extend_prepar_date, cancel_extend_date_reason, flow_status, recover_party_reason, stop_party_reason, work_post, settle_area, stop_party_date, recover_party_date, mem_code, open_id, start_pay_date
    </sql>

    <select id="getMemTotal" resultType="com.zenith.front.model.vo.MemAllVO">
        select
        "d08_code" as "d08Code",
        count(1)
        from "ccp_mem"
        where (
        "mem_org_code" like CONCAT(#{orgCode}, '%')
        <if test="startDate != null and endDate != null">
            and "full_member_date" BETWEEN #{startDate} AND #{endDate}
        </if>
        <if test="startDate != null and endDate == null ">
            and "full_member_date" &gt;= #{startDate}
        </if>
        <if test="startDate == null and endDate != null ">
            and "full_member_date" &lt;= #{endDate}
        </if>
        and "delete_time" is null
        and "d08_code" in (
        '1', '2'
        ) and (is_transfer != 1 or is_transfer is null)
        )
        group by "d08_code"
    </select>

    <select id="getNationRatioTotal" resultType="java.lang.Long">
        select count(1)
        from "ccp_mem"
        where (
        "mem_org_code" like CONCAT(#{orgCode}, '%')
        <if test="startDate != null and endDate != null">
            and "full_member_date" BETWEEN #{startDate} AND #{endDate}
        </if>
        <if test="startDate != null and endDate == null ">
            and "full_member_date" &gt;= #{startDate}
        </if>
        <if test="startDate == null and endDate != null ">
            and "full_member_date" &lt;= #{endDate}
        </if>
        and "delete_time" is null
        and "d08_code" in (
        '1', '2'
        )
        )
    </select>

    <select id="getRecord" resultType="java.lang.Long">
        select count(1)
        from "ccp_mem"
        where (
        "mem_org_code" like CONCAT(#{orgCode}, '%')
        <if test="startDate != null">
            and "full_member_date" &gt;= #{startDate}
        </if>
        <if test="endDate != null ">
            and "full_member_date" &lt;= #{endDate}
        </if>
        and "delete_time" is null
        and "d08_code" in (
        '1', '2'
        )
        and "d06_code" in ('01')
        )
    </select>

    <select id="getOne" resultType="com.zenith.front.model.vo.OptionVO">
        select
        count(1) as "total",
        sum(case
        when cast("d07_code" as varchar) like '1%' then 1
        else 0
        end) as "one",
        sum(case
        when cast("d07_code" as varchar) like '2%' then 1
        else 0
        end) as "two",
        sum(case
        when cast("d07_code" as varchar) like '3%' then 1
        else 0
        end) as "three"
        from "ccp_mem"
        where (
        "mem_org_code" like CONCAT(#{orgCode}, '%')
        <if test="startDate != null and endDate != null">
            and "full_member_date" BETWEEN #{startDate} AND #{endDate}
        </if>
        <if test="startDate != null and endDate == null ">
            and "full_member_date" &gt;= #{startDate}
        </if>
        <if test="startDate == null and endDate != null ">
            and "full_member_date" &lt;= #{endDate}
        </if>
        and "delete_time" is null
        and "d08_code" in (
        '1', '2'
        )
        and (
        cast("d07_code" as varchar) like '1%'
        or cast("d07_code" as varchar) like '2%'
        or cast("d07_code" as varchar) like '3%'
        )
        )
    </select>

    <select id="getSexRatioTotal" resultType="com.zenith.front.model.vo.MemAllVO">
        select
        "sex_code" as "sexCode",
        count(sex_code)
        from "ccp_mem"
        where (
        "mem_org_code" like CONCAT(#{orgCode}, '%')
        <if test="startDate != null and endDate != null">
            and "full_member_date" BETWEEN #{startDate} AND #{endDate}
        </if>
        <if test="startDate != null and endDate == null ">
            and "full_member_date" &gt;= #{startDate}
        </if>
        <if test="startDate == null and endDate != null ">
            and "full_member_date" &lt;= #{endDate}
        </if>
        and "delete_time" is null
        and "d08_code" in (
        '1', '2'
        )
        )
        group by "sex_code"
    </select>

    <select id="getEducationRatio" resultType="com.zenith.front.model.vo.OptionVO">
        select
        count(1) as "total",
        sum(case
        when cast("d07_code" as varchar) like '1%' then 1
        else 0
        end) as "one",
        sum(case
        when cast("d07_code" as varchar) like '2%' then 1
        else 0
        end) as "two",
        sum(case
        when cast("d07_code" as varchar) like '3%' then 1
        else 0
        end) as "three",
        sum(case
        when cast("d07_code" as varchar) like '4%' then 1
        else 0
        end) as "four",
        sum(case
        when cast("d07_code" as varchar) like '5%' then 1
        else 0
        end) as "five",
        sum(case
        when cast("d07_code" as varchar) like '6%' then 1
        else 0
        end) as "six",
        sum(case
        when cast("d07_code" as varchar) like '7%' then 1
        else 0
        end) as "seven",
        sum(case
        when cast("d07_code" as varchar) like '8%' then 1
        else 0
        end) as "eight",
        sum(case
        when cast("d07_code" as varchar) like '9%' then 1
        else 0
        end) as "nine"
        from "ccp_mem"
        where (
        "mem_org_code" like CONCAT(#{orgCode}, '%')
        <if test="startDate != null and endDate != null">
            and "full_member_date" BETWEEN #{startDate} AND #{endDate}
        </if>
        <if test="startDate != null and endDate == null ">
            and "full_member_date" &gt;= #{startDate}
        </if>
        <if test="startDate == null and endDate != null ">
            and "full_member_date" &lt;= #{endDate}
        </if>
        and "delete_time" is null
        and "d08_code" in (
        '1', '2'
        )
        )
    </select>

    <select id="getJobRatio" resultType="com.zenith.front.model.vo.OptionVO">
        select
        count(1) as "total",
        sum(case
        when cast("d09_code" as varchar) like '0%' then 1
        else 0
        end) as "one",
        sum(case
        when cast("d09_code" as varchar) like '1%' then 1
        else 0
        end) as "two",
        sum(case
        when cast("d09_code" as varchar) like '2%' then 1
        else 0
        end) as "three",
        sum(case
        when cast("d09_code" as varchar) like '3%' then 1
        else 0
        end) as "four",
        sum(case
        when cast("d09_code" as varchar) like '4%' then 1
        else 0
        end) as "five",
        sum(case
        when cast("d09_code" as varchar) like '5%' then 1
        else 0
        end) as "six"
        from "ccp_mem"
        where (
        "mem_org_code" like CONCAT(#{orgCode}, '%')
        <if test="startDate != null and endDate != null">
            and "full_member_date" BETWEEN #{startDate} AND #{endDate}
        </if>
        <if test="startDate != null and endDate == null ">
            and "full_member_date" &gt;= #{startDate}
        </if>
        <if test="startDate == null and endDate != null ">
            and "full_member_date" &lt;= #{endDate}
        </if>
        and "delete_time" is null
        and "d08_code" in (
        '1', '2'
        )
        )
    </select>

    <select id="getGyJobRatio" resultType="com.zenith.front.model.vo.OptionVO">
        select
        count(1) as "total",
        sum(case
        when cast("d09_code" as varchar) like '011%' then 1
        else 0
        end) as "one",
        sum(case
        when cast("d09_code" as varchar) like '012%' then 1
        else 0
        end) as "two",
        sum(case
        when cast("d09_code" as varchar) like '013%' then 1
        else 0
        end) as "three",
        sum(case
        when cast("d09_code" as varchar) like '014%' then 1
        else 0
        end) as "four",
        sum(case
        when cast("d09_code" as varchar) like '015%' then 1
        else 0
        end) as "five",
        sum(case
        when cast("d09_code" as varchar) like '016%' then 1
        else 0
        end) as "six"
        from "ccp_mem"
        where (
        "mem_org_code" like CONCAT(#{orgCode}, '%')
        <if test="startDate != null and endDate != null">
            and "full_member_date" BETWEEN #{startDate} AND #{endDate}
        </if>
        <if test="startDate != null and endDate == null ">
            and "full_member_date" &gt;= #{startDate}
        </if>
        <if test="startDate == null and endDate != null ">
            and "full_member_date" &lt;= #{endDate}
        </if>
        and "delete_time" is null
        and "d08_code" in (
        '1', '2'
        )
        and cast("d09_code" as varchar) like '01%'
        )
    </select>

    <select id="getFgyJobRatio" resultType="com.zenith.front.model.vo.OptionVO">
        select
        count(1) as "total",
        sum(case
        when cast("d09_code" as varchar) like '021%' then 1
        else 0
        end) as "one",
        sum(case
        when cast("d09_code" as varchar) like '022%' then 1
        else 0
        end) as "two",
        sum(case
        when cast("d09_code" as varchar) like '023%' then 1
        else 0
        end) as "three"
        from "ccp_mem"
        where (
        "mem_org_code" like CONCAT(#{orgCode}, '%')
        <if test="startDate != null and endDate != null">
            and "full_member_date" BETWEEN #{startDate} AND #{endDate}
        </if>
        <if test="startDate != null and endDate == null ">
            and "full_member_date" &gt;= #{startDate}
        </if>
        <if test="startDate == null and endDate != null ">
            and "full_member_date" &lt;= #{endDate}
        </if>
        and "delete_time" is null
        and "d08_code" in (
        '1', '2'
        )
        and cast("d09_code" as varchar) like '02%'
        )
    </select>

    <select id="getListRecord" resultType="com.zenith.front.model.vo.MemAllVO">
        select
        substring("d09_code", 1, 3) as "d09Code",
        count(1)
        from "ccp_mem"
        where (
        "mem_org_code" like CONCAT(#{orgCode}, '%')
        <if test="startDate != null and endDate != null">
            and "full_member_date" BETWEEN #{startDate} AND #{endDate}
        </if>
        <if test="startDate != null and endDate == null ">
            and "full_member_date" &gt;= #{startDate}
        </if>
        <if test="startDate == null and endDate != null ">
            and "full_member_date" &lt;= #{endDate}
        </if>
        and "delete_time" is null
        and "d08_code" in (
        '1', '2'
        )
        and "d09_code" like '03%'
        )
        group by substring("d09_code", 1, 3)
    </select>

    <select id="getNmymJobRatio" resultType="com.zenith.front.model.vo.MemAllVO">
        select
        substring("d09_code", 1, 3) as "d09Code",
        count(1)
        from "ccp_mem"
        where (
        "mem_org_code" like CONCAT(#{orgCode}, '%')
        <if test="startDate != null and endDate != null">
            and "full_member_date" BETWEEN #{startDate} AND #{endDate}
        </if>
        <if test="startDate != null and endDate == null ">
            and "full_member_date" &gt;= #{startDate}
        </if>
        <if test="startDate == null and endDate != null ">
            and "full_member_date" &lt;= #{endDate}
        </if>
        and "delete_time" is null
        and "d08_code" in (
        '1', '2'
        )
        and "d09_code" like '1%'
        )
        group by substring("d09_code", 1, 3)
    </select>

    <select id="getMemDifficult" resultType="com.zenith.front.model.vo.MemAllVO">
        select
        "d08_code" as "d08Code",
        count(1)
        from "ccp_mem_difficult"
        where (
        "diff_org_code" like CONCAT(#{orgCode}, '%')
        <if test="startDate != null and endDate != null">
            and "create_time" BETWEEN #{startDate} AND #{endDate}
        </if>
        <if test="startDate != null and endDate == null ">
            and "create_time" &gt;= #{startDate}
        </if>
        <if test="startDate == null and endDate != null ">
            and "create_time" &lt;= #{endDate}
        </if>
        and "delete_time" is null
        and "d08_code" in (
        '1', '2'
        )
        and "is_difficulty" = 1
        )
        group by "d08_code"
    </select>

    <select id="getManyMem" resultType="com.zenith.front.model.vo.MemAllVO">
        select
        "d08_code" as "d08Code",
        count(1)
        from "ccp_mem_many"
        where (
        "join_many_org_code" like CONCAT(#{orgCode}, '%')
        <if test="startDate != null and endDate != null">
            and "create_time" BETWEEN #{startDate} AND #{endDate}
        </if>
        <if test="startDate != null and endDate == null ">
            and "create_time" &gt;= #{startDate}
        </if>
        <if test="startDate == null and endDate != null ">
            and "create_time" &lt;= #{endDate}
        </if>
        and "delete_time" is null
        and "d08_code" in (
        '1', '2'
        )
        )
        group by "d08_code"
    </select>

    <select id="getMemHistory" resultType="com.zenith.front.model.vo.MemAllVO">
        select "d08_code" as "d08Code",
               count(1)
        from "ccp_mem"
        where (
                      cast("mem_org_code" as varchar) like CONCAT(#{orgCode}, '%')
                      and "delete_time" is not null
                      and "d08_code" in (
                                         '1', '2'
                      )
                    and (  d12_code is not null or d50_code is not null)
                  )
        group by "d08_code"
    </select>

    <select id="getAgeRatio" resultType="com.zenith.front.model.vo.OptionVO">
        select
        sum(case
        when "age" &lt;= 30 then 1
        else 0
        end) as "one",
        sum(case
        when (
        "age" &lt;= 40
        and "age" > 30
        ) then 1
        else 0
        end) as "two",
        sum(case
        when (
        "age" &lt;= 50
        and "age" > 40
        ) then 1
        else 0
        end) as "three",
        sum(case
        when (
        "age" &lt;= 60
        and "age" > 50
        ) then 1
        else 0
        end) as "four",
        sum(case
        when (
        "age" &lt;= 70
        and "age" > 61
        ) then 1
        else 0
        end) as "five",
        sum(case
        when "age" > 70 then 1
        else 0
        end) as "six"
        from "ccp_mem"
        where (
        "mem_org_code" like CONCAT(#{orgCode}, '%')
        <if test="startDate != null and endDate != null">
            and "create_time" BETWEEN #{startDate} AND #{endDate}
        </if>
        <if test="startDate != null and endDate == null ">
            and "create_time" &gt;= #{startDate}
        </if>
        <if test="startDate == null and endDate != null ">
            and "create_time" &lt;= #{endDate}
        </if>
        and "delete_time" is null
        and "d08_code" in (
        '1', '2'
        )
        )
    </select>

    <select id="getPartyAgeRatio" resultType="com.zenith.front.model.vo.OptionVO">
        select
        sum(case
        when EXTRACT (YEAR FROM AGE(join_org_date)) &lt;= 5 then 1
        else 0
        end) as "one",
        sum(case
        when (
        EXTRACT (YEAR FROM AGE(join_org_date)) &lt;= 10
        and EXTRACT (YEAR FROM AGE(join_org_date)) > 5
        ) then 1
        else 0
        end) as "two",
        sum(case
        when (
        EXTRACT (YEAR FROM AGE(join_org_date)) &lt;= 15
        and EXTRACT (YEAR FROM AGE(join_org_date)) > 10
        ) then 1
        else 0
        end) as "three",
        sum(case
        when (
        EXTRACT (YEAR FROM AGE(join_org_date)) &lt;= 20
        and EXTRACT (YEAR FROM AGE(join_org_date)) > 15
        ) then 1
        else 0
        end) as "four",
        sum(case
        when (
        EXTRACT (YEAR FROM AGE(join_org_date)) &lt;= 25
        and EXTRACT (YEAR FROM AGE(join_org_date)) > 20
        ) then 1
        else 0
        end) as "five",
        sum(case
        when (
        EXTRACT (YEAR FROM AGE(join_org_date)) &lt;= 30
        and EXTRACT (YEAR FROM AGE(join_org_date)) > 25
        ) then 1
        else 0
        end) as "six",
        sum(case
        when EXTRACT (YEAR FROM AGE(join_org_date)) > 30 then 1
        else 0
        end) as "seven"
        from "ccp_mem"
        where (
        "mem_org_code" like CONCAT(#{orgCode}, '%')
        <if test="startDate != null and endDate != null">
            and "create_time" BETWEEN #{startDate} AND #{endDate}
        </if>
        <if test="startDate != null and endDate == null ">
            and "create_time" &gt;= #{startDate}
        </if>
        <if test="startDate == null and endDate != null ">
            and "create_time" &lt;= #{endDate}
        </if>
        and "delete_time" is null
        and "d08_code" in (
        '1', '2'
        )
        )
    </select>

    <select id="getPartyAgeParagraphRatio" resultType="com.zenith.front.model.vo.OptionVO">
        select
        sum(case
        when "join_org_date" &lt;= date '1937-07-06' then 1
        else 0
        end) as "one",
        sum(case
        when (
        "join_org_date" &lt;= date '1945-09-02'
        and "join_org_date" >= date '1937-07-07'
        ) then 1
        else 0
        end) as "two",
        sum(case
        when (
        "join_org_date" &lt;= date '1949-09-01'
        and "join_org_date" >= date '1945-09-03'
        ) then 1
        else 0
        end) as "three",
        sum(case
        when (
        "join_org_date" &lt;= date '1966-04-01'
        and "join_org_date" >= date '1949-10-01'
        ) then 1
        else 0
        end) as "four",
        sum(case
        when (
        "join_org_date" &lt;= date '1976-10-01'
        and "join_org_date" >= date '1966-05-01'
        ) then 1
        else 0
        end) as "five",
        sum(case
        when (
        "join_org_date" &lt;= date '1978-12-01'
        and "join_org_date" >= date '1976-11-01'
        ) then 1
        else 0
        end) as "six",
        sum(case
        when (
        "join_org_date" &lt;= date '2002-10-01'
        and "join_org_date" >= date '1979-01-01'
        ) then 1
        else 0
        end) as "seven",
        sum(case
        when (
        "join_org_date" &lt;= date '2012-10-01'
        and "join_org_date" >= date '2002-11-01'
        ) then 1
        else 0
        end) as "eight",
        sum(case
        when "join_org_date" >= date '2012-11-01' then 1
        else 0
        end) as "nine"
        from "ccp_mem"
        where (
        "mem_org_code" like CONCAT(#{orgCode}, '%')
        <if test="startDate != null and endDate != null">
            and "create_time" BETWEEN #{startDate} AND #{endDate}
        </if>
        <if test="startDate != null and endDate == null ">
            and "create_time" &gt;= #{startDate}
        </if>
        <if test="startDate == null and endDate != null ">
            and "create_time" &lt;= #{endDate}
        </if>
        and "delete_time" is null
        and "d08_code" in (
        '1', '2'
        )
        )
    </select>

    <select id="getMemRatio" resultType="java.math.BigDecimal">
        select round(
                       avg(cast("ratio" as numeric)),
                       2
                   ) as "ratio"
        from "ccp_mem"
        where (
                      "mem_org_code" like CONCAT(#{orgCode}, '%')
                      and "d08_code" in (
                                         '1', '2'
                      )
                      and "delete_time" is null
                  )
    </select>

    <select id="getMemLayout" resultType="com.zenith.front.model.vo.MemAllVO">
        select "d08_code" as "d08Code",
               count(1)
        from "ccp_mem"
        where (
                      "mem_org_code" like CONCAT(#{orgCode}, '%')
                      and "d08_code" in (
                                         '1', '2'
                      )
                      and "delete_time" is null
                  )
        group by "d08_code"
    </select>

    <select id="getMemDevelopLayout" resultType="com.zenith.front.model.vo.MemAllVO">
        select "d08_code" as "d08Code",
               count(1)
        from "ccp_mem_develop"
        where (
                      "develop_org_code" like CONCAT(#{orgCode}, '%')
                      and "d08_code" in (
                                         '3', '4', '5'
                      )
                      and "delete_time" is null
                  )
        group by "d08_code"
    </select>

    <update id="updateD09Code">
        UPDATE ccp_mem_all
        SET d09_code = T.d09_code,d09_name = T.d09_name,
            educational_system = T.educational_system
        FROM
            ccp_mem T
        WHERE
            T.code = ccp_mem_all.code
          AND ( T.d09_code LIKE '3%' OR T.d09_code LIKE '515%' )
    </update>

</mapper>
