package com.zenith.front.dao.mapper.org;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.zenith.front.model.bean.Org;
import com.zenith.front.model.bean.OrgContactMem;
import com.zenith.front.model.vo.OrgContactMemWorkVo;
import org.apache.ibatis.annotations.MapKey;
import org.springframework.data.repository.query.Param;

import java.util.List;
import java.util.Map;

/**
 * <p>
 * 党支部工作联系人 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2022-12-14
 */
public interface OrgContactMemMapper extends BaseMapper<OrgContactMem> {

    @MapKey("orgCode")
    List<Map<String, Object>> getContactMemMap(@Param("orgCode") String orgCode);

    List<OrgContactMem> getContactPointList(@Param("orgCode") String orgCode);

    List<OrgContactMem> getContactPointListByD156Code(@Param("orgCode") String orgCode, @Param("d156Code") String d156Code, @Param("symbol") String symbol, @Param("num") int num, @Param("year") String year);

    Page<OrgContactMemWorkVo> getContactPointPage(@Param("page") Page<OrgContactMemWorkVo> page, @Param("orgCode") String orgCode);
}
