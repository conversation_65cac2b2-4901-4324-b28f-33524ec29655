<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zenith.front.dao.mapper.mem.MemFlowOldMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.zenith.front.model.bean.MemFlowOld">
        <id column="id" property="id" />
        <result column="code" property="code" />
        <result column="es_id" property="esId" />
        <result column="birthday" property="birthday" />
        <result column="idcard" property="idcard" />
        <result column="gender" property="gender" />
        <result column="gender_name" property="genderName" />
        <result column="mem_type_name" property="memTypeName" />
        <result column="mem_type_code" property="memTypeCode" />
        <result column="mem_code" property="memCode" />
        <result column="mem_name" property="memName" />
        <result column="nation_name" property="nationName" />
        <result column="nation_code" property="nationCode" />
        <result column="mem_org_name" property="memOrgName" />
        <result column="flow_mem_org_code" property="flowMemOrgCode" />
        <result column="flow_org_code" property="flowOrgCode" />
        <result column="flow_type" property="flowType" />
        <result column="is_hold" property="isHold" />
        <result column="is_explicit_inflow_org" property="isExplicitInflowOrg" />
        <result column="is_prov_out" property="isProvOut" />
        <result column="is_prov_out_name" property="isProvOutName" />
        <result column="outflow_date" property="outflowDate" />
        <result column="outflow_type_code" property="outflowTypeCode" />
        <result column="outflow_type_name" property="outflowTypeName" />
        <result column="outflow_org_code" property="outflowOrgCode" />
        <result column="outflow_org_name" property="outflowOrgName" />
        <result column="outflow_org_org_code" property="outflowOrgOrgCode" />
        <result column="outflow_reason_code" property="outflowReasonCode" />
        <result column="outflow_reason_name" property="outflowReasonName" />
        <result column="outflow_unit_code" property="outflowUnitCode" />
        <result column="outflow_unit_name" property="outflowUnitName" />
        <result column="outflow_unit_type_code" property="outflowUnitTypeCode" />
        <result column="outflow_unit_type_name" property="outflowUnitTypeName" />
        <result column="outflow_area" property="outflowArea" />
        <result column="outflow_org_linkman" property="outflowOrgLinkman" />
        <result column="outflow_org_phone" property="outflowOrgPhone" />
        <result column="inflow_org_name" property="inflowOrgName" />
        <result column="inflow_org_org_code" property="inflowOrgOrgCode" />
        <result column="inflow_org_code" property="inflowOrgCode" />
        <result column="inflow_type_code" property="inflowTypeCode" />
        <result column="inflow_type_name" property="inflowTypeName" />
        <result column="inflow_area_id" property="inflowAreaId" />
        <result column="inflow_area_name" property="inflowAreaName" />
        <result column="inflow_unit_code" property="inflowUnitCode" />
        <result column="inflow_unit_name" property="inflowUnitName" />
        <result column="inflow_unit_type_code" property="inflowUnitTypeCode" />
        <result column="inflow_unit_type_name" property="inflowUnitTypeName" />
        <result column="inflow_work_type_name" property="inflowWorkTypeName" />
        <result column="inflow_work_type_code" property="inflowWorkTypeCode" />
        <result column="inflow_new_level_name" property="inflowNewLevelName" />
        <result column="inflow_new_level_code" property="inflowNewLevelCode" />
        <result column="inflow_edu_name" property="inflowEduName" />
        <result column="inflow_edu_code" property="inflowEduCode" />
        <result column="flow_org_contact" property="flowOrgContact" />
        <result column="inflow_date" property="inflowDate" />
        <result column="inflow_type" property="inflowType" />
        <result column="reason" property="reason" />
        <result column="reflow_type" property="reflowType" />
        <result column="reflow_type_name" property="reflowTypeName" />
        <result column="reflow_reason" property="reflowReason" />
        <result column="reflow_date" property="reflowDate" />
        <result column="stage" property="stage" />
        <result column="status" property="status" />
        <result column="state" property="state" />
        <result column="create_time" property="createTime" />
        <result column="update_time" property="updateTime" />
        <result column="timestamp" property="timestamp" />
        <result column="delete_time" property="deleteTime" />
        <result column="outflow_unit_economy_code" property="outflowUnitEconomyCode" />
        <result column="inflow_unit_economy_code" property="inflowUnitEconomyCode" />
        <result column="inflow_zb_code" property="inflowZbCode" />
        <result column="outflow_zb_code" property="outflowZbCode" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, code, es_id, birthday, idcard, gender, gender_name, mem_type_name, mem_type_code, mem_code, mem_name, nation_name, nation_code, mem_org_name, flow_mem_org_code, flow_org_code, flow_type, is_hold, is_explicit_inflow_org, is_prov_out, is_prov_out_name, outflow_date, outflow_type_code, outflow_type_name, outflow_org_code, outflow_org_name, outflow_org_org_code, outflow_reason_code, outflow_reason_name, outflow_unit_code, outflow_unit_name, outflow_unit_type_code, outflow_unit_type_name, outflow_area, outflow_org_linkman, outflow_org_phone, inflow_org_name, inflow_org_org_code, inflow_org_code, inflow_type_code, inflow_type_name, inflow_area_id, inflow_area_name, inflow_unit_code, inflow_unit_name, inflow_unit_type_code, inflow_unit_type_name, inflow_work_type_name, inflow_work_type_code, inflow_new_level_name, inflow_new_level_code, inflow_edu_name, inflow_edu_code, flow_org_contact, inflow_date, inflow_type, reason, reflow_type, reflow_type_name, reflow_reason, reflow_date, stage, status, state, create_time, update_time, timestamp, delete_time, outflow_unit_economy_code, inflow_unit_economy_code, inflow_zb_code, outflow_zb_code
    </sql>

</mapper>
