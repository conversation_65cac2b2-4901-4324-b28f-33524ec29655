<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zenith.front.dao.mapper.org.OrgReviewersMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.zenith.front.model.bean.OrgReviewers">
        <id column="code" property="code" />
        <result column="appraisal_code" property="appraisalCode" />
        <result column="mem_code" property="memCode" />
        <result column="result" property="result" />
        <result column="reason" property="reason" />
        <result column="opinion" property="opinion" />
        <result column="situation" property="situation" />
        <result column="handling_reasons" property="handlingReasons" />
        <result column="create_time" property="createTime" />
        <result column="update_time" property="updateTime" />
        <result column="delete_time" property="deleteTime" />
        <result column="org_code" property="orgCode" />
        <result column="mem_name" property="memName" typeHandler="com.zenith.front.common.encrypt.typehandler.EncryptTypeHandler"/>
        <result column="stop_party_date" property="stopPartyDate" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        code, appraisal_code, mem_code, result, reason, opinion, situation, handling_reasons, create_time, update_time, delete_time, org_code, mem_name, stop_party_date
    </sql>

    <select id="page" resultType="com.zenith.front.model.vo.MemReviewersVo">
        SELECT t1.code,
               to_char(t2.year, 'YYYY') AS year,
               t1.result,
               t1.appraisal_code        as appraisalCode
        FROM ccp_org_reviewers t1
                 LEFT JOIN ccp_org_appraisal t2
                           ON t2.CODE = t1.appraisal_code
        WHERE t1.mem_code = #{memCode}
          AND t1.delete_time IS NULL
        ORDER BY year DESC
    </select>

    <select id="pageMem" resultType="com.zenith.front.model.vo.OrgReviewersMemVO">
        SELECT t1.code,
               t2.name,
               t2.sex_name as sexName,
               t2.idcard,
               t2.phone,
               t2.d08_name as d08Name,
               t2.org_code as orgName
        FROM ccp_org_reviewers t1
                 LEFT JOIN ccp_mem t2 ON t2.CODE = t1.mem_code
        WHERE t1.delete_time IS NULL
          AND t2.delete_time IS NULL
          AND appraisal_code = #{code}
        ORDER BY t1.create_time DESC
    </select>

    <select id="findLatestReviewerByMemCode" resultMap="BaseResultMap">
        SELECT t1.*
        from ccp_org_reviewers t1
                 INNER JOIN ccp_org_appraisal t2 on t1.appraisal_code = t2.code and t2.delete_time is NULL
                 INNER JOIN ccp_org t3 on t3.code = t2.org_code and t3.delete_time is null
        WHERE t1.mem_code = #{memCode}
          and t1.delete_time is NULL
          AND (to_char(t2."year", 'yyyy') = #{countYear} or to_char(t2."year", 'yyyy') = #{lastYear})
          AND to_char(t2."end_time", 'yyyy') = #{countYear}
        ORDER BY t2."end_time" DESC,t2.create_time DESC
    </select>

    <select id="selectListByOrgCode" resultMap="BaseResultMap">
        SELECT *
        FROM ccp_org_reviewers
        WHERE appraisal_code IN (SELECT code
                                 FROM ccp_org_appraisal
                                 WHERE org_code IN (SELECT code
                                                    FROM ccp_org
                                                    WHERE org_code like CONCAT(#{orgCode}, '%')
                                                    ORDER BY "length"(org_code), org_code))
    </select>

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap1" type="com.zenith.front.model.vo.OrgReviewersVO">
        <id column="code" property="code" />
        <result column="org_name" property="orgName" />
        <result column="d08_name" property="d08Name" />
        <result column="result" property="result" />
        <result column="reason" property="reason" />
        <result column="opinion" property="opinion" />
        <result column="situation" property="situation" />
        <result column="handling_reasons" property="handlingReasons" />
        <result column="mem_name" property="memName" typeHandler="com.zenith.front.common.encrypt.typehandler.EncryptTypeHandler"/>
        <result column="stop_party_date" property="stopPartyDate" />
    </resultMap>
    <select id="getOrgAppraisals" resultType="com.zenith.front.model.vo.OrgReviewersVO">
        select
            co.name as org_name,
            cm.d08_name,
            cor.*
        from
            ccp_org_reviewers cor
        left join ccp_mem cm
            on cor.mem_code  = cm.code
        left join ccp_org_appraisal coa
            on cor.appraisal_code = coa.code
        left join ccp_org co
            on co.code = coa.org_code
        where
            co.org_code like concat( #{orgCode}, '%')
            and coa.delete_time is null
            and EXTRACT(YEAR FROM year) = #{year}
            and cor.delete_time is null
            and cor.result in ('1','4','7','2','3','5','8')

    </select>

    <select id="getOrgAppraisalsEntry" resultMap="BaseResultMap1">
        select
            co.name as org_name,
            cm.d08_name,
            cor.code,
            cor.result,
            cor.reason,
            cor.opinion,
            cor.situation,
            cor.handling_reasons,
            cor.stop_party_date
        from
            ccp_org_reviewers cor
                left join ccp_mem cm
                          on cor.mem_code  = cm.code
                left join ccp_org_appraisal coa
                          on cor.appraisal_code = coa.code
                left join ccp_org co
                          on co.code = coa.org_code
        where
            co.org_code like concat( #{orgCode}, '%')
          and coa.delete_time is null
          and EXTRACT(YEAR FROM year) = #{year}
          and cor.delete_time is null
          and cor.result in ('1','4','7','2','3','5','8')

    </select>
</mapper>
