package com.zenith.front.dao.mapper.orgreport;

import com.zenith.front.model.bean.Org;
import com.zenith.front.model.bean.OrgReport;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2022-03-11
 */
public interface OrgReportMapper extends BaseMapper<OrgReport> {

    /**
     * 根据组织code和报表code从上往下递归查询
     *
     * @param parentCode 组织code
     * @param reportCode 报表code
     * @return
     */
    List<Org> recursionFromTopToBottom(@Param("parentCode") String parentCode, @Param("reportCode") String reportCode);
}
