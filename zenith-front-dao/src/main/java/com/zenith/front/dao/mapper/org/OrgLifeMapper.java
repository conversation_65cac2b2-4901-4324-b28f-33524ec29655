package com.zenith.front.dao.mapper.org;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.zenith.front.model.dto.OrgLifeListDTO;
import com.zenith.front.model.bean.OrgLife;
import com.zenith.front.model.vo.OrgLifeGetMAQVO;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;

/**
 * <p>
 * Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2022-12-15
 */
public interface OrgLifeMapper extends BaseMapper<OrgLife> {

    Page<OrgLife> getOrgLifeList(@Param("page") Page<OrgLife> page, @Param("data") OrgLifeListDTO data);

    List<String> getOrgLifeCOde();

    //未设立党小组的党支部在统计时间每月组织的党员活动
    List<OrgLifeGetMAQVO> getOrgLifeByOrgCodeListAndTime(@Param("orgCodeList") List<String> orgCodeList, @Param("beginTime") String beginTime, @Param("endTime") String endTime );
    //未坚持每季度至少召开一次党员大会的支部数（统计时间内每季度至少召开一次）
    List<OrgLifeGetMAQVO> quartertjByOne(@Param("orgCodeList") List<String> orgCodeList, @Param("beginTime") String beginTime,@Param("endTime") String endTime );
    //未按月召开支委会的支部数（统计时间内每月至少召开一次）
    List<OrgLifeGetMAQVO> getOrgLifeBy3(@Param("orgCodeList") List<String> orgCodeList, @Param("beginTime") String beginTime,@Param("endTime") String endTime );

    //未坚持每季度至少组织党员上一次党课的支部数（统计时间内每季度至少召开一次，即1、2、3月至少一次，下同）
    List<OrgLifeGetMAQVO> quartertjByFour(@Param("orgCodeList") List<String> orgCodeList, @Param("beginTime") String beginTime,@Param("endTime") String endTime );
    //设立党小组的党支部
    //未坚持每季度至少召开一次党员大会的支部数（统计时间内每季度至少召开一次）

    //未按月召开支委会的支部数（统计时间内每月至少召开一次）
//    List<OrgLifeGetMAQVO> wayzkzwh(@Param("orgCodeList")List<String> orgCodeList, @Param("beginTime") String beginTime,@Param("endTime") String endTime );
    //未按月召开党小组会的支部数（统计时间内每月至少召开一次）getOrgLifeByD1582
    List<OrgLifeGetMAQVO> getOrgLifeByD1582(@Param("orgCodeList")List<String> orgCodeList, @Param("beginTime") String beginTime,@Param("endTime") String endTime );
    //未坚持每季度至少组织党员上一次党课的支部数（统计时间内每季度至少召开一次）

    List<OrgLife> findByD1585(@Param("orgCodeList") List<String> orgCodeList, @Param("beginTime") Date beginTime,@Param("endTime") Date endTime );

    List<String> getBetweenMonth( @Param("beginTime") String beginTime,@Param("quarterEndTime") String endTime );





    List<OrgLife> getListLikeOrgCode(@Param("startDate") Date startDate, @Param("endDate") Date endDate, @Param("orgCode") String orgCode);
}
