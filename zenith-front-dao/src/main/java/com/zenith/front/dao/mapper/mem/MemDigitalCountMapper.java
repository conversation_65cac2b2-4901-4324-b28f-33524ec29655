package com.zenith.front.dao.mapper.mem;


import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.zenith.front.model.bean.MemDigitalCount;
import com.zenith.front.model.vo.DigitalCompletenessVO;
import com.zenith.front.model.vo.DigitalComprehensiveVO;
import com.zenith.front.model.vo.DigitalCountListVO;
import org.apache.ibatis.annotations.Param;

import java.util.Collection;
import java.util.List;
import java.util.Map;


/**
 * 档案上传资料统计表
 */
public interface MemDigitalCountMapper extends BaseMapper<MemDigitalCount> {
    Integer myInserBatch(Collection<MemDigitalCount> entityList);

    /**
     * 查询档案上传详细情况
     * @param digitalLotNoList
     * @return
     */
    List<DigitalCompletenessVO> getDigCount(@Param("digitalLotNoList") Collection<String> digitalLotNoList);


    /**
     * 查询入党申请人、预备、正式党员档案完成情况汇总信息
     * @param orgLevelCode
     * @return
     */
    List<DigitalCountListVO> memCountList(@Param("orgLevelCode") String orgLevelCode, @Param("memName") String memName);

    /**
     * 综合档案统计查询(党员部分)
     * @param orgLevelCode
     * @return
     */
    List<DigitalComprehensiveVO> comprehensiveMemList(@Param("orgLevelCode") String orgLevelCode);

    /**
     * 综合档案统计查询(关系转接部分)
     * @param orgLevelCode
     * @return
     */
    List<Map<String, String>> comprehensiveTransferList(@Param("orgLevelCode") String orgLevelCode);

}
