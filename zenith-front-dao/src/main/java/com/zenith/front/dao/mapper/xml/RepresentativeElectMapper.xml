<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zenith.front.dao.mapper.representative.RepresentativeElectMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.zenith.front.model.bean.RepresentativeElect">
        <id column="id" property="id" />
        <result column="code" property="code" />
        <result column="es_id" property="esId" />
        <result column="elect_org_code" property="electOrgCode" />
        <result column="unit_code" property="unitCode" />
        <result column="unit_name" property="unitName" />
        <result column="d61_code" property="d61Code" />
        <result column="d61_name" property="d61Name" />
        <result column="elect_num" property="electNum" />
        <result column="elect_name" property="electName" />
        <result column="start_date" property="startDate" />
        <result column="end_date" property="endDate" />
        <result column="is_now_elect" property="isNowElect" />
        <result column="is_country_test" property="isCountryTest" />
        <result column="create_time" property="createTime" />
        <result column="update_time" property="updateTime" />
        <result column="timestamp" property="timestamp" />
        <result column="delete_time" property="deleteTime" />
        <result column="zb_code" property="zbCode" />
        <result column="org_code" property="orgCode" />
        <result column="is_history" property="isHistory" />
        <result column="update_account" property="updateAccount" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, code, es_id, elect_org_code, unit_code, unit_name, d61_code, d61_name, elect_num, elect_name, start_date, end_date, is_now_elect, is_country_test, create_time, update_time, timestamp, delete_time, zb_code, org_code, is_history, update_account
    </sql>
</mapper>
