package com.zenith.front.dao.mapper.flow;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.zenith.front.model.bean.Org;
import com.zenith.front.model.bean.OrgFlow;
import com.zenith.front.model.dto.flow.OrgFlowListAuditDto;
import com.zenith.front.model.vo.OrgFlowVo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

import java.util.List;

@Mapper
public interface OrgFlowMapper extends BaseMapper<OrgFlow> {

    @Select("select * from ccp_org_flow where delete_time is null and code = #{code} limit 1")
    OrgFlow getOrgFlowByCode(@Param("code") String code);

    Integer getOrgDevelopRightsNum(@Param("orgCode") String orgCode);

    Page<OrgFlowVo> selectOrgFlowPage(@Param("page") Page<OrgFlowVo> page, @Param("dto") OrgFlowListAuditDto dto);

    @Update("update ccp_org_flow set is_enable = #{isEnable} where id = #{id}")
    void updateIsEnableById(@Param("id") Long id, @Param("isEnable") Integer isEnable);

    @Update("update ccp_org_flow set is_enable = #{isEnable} where code = #{code}")
    void updateIsEnableByCode(@Param("code") String code, @Param("isEnable") Integer isEnable);

    /**
     * 查询本级和直属下级组织
     *
     * @param orgCode 层级码
     * @return
     */
    List<Org> selectUnderlingByOrgCode(@Param("orgCode") String orgCode);
}
