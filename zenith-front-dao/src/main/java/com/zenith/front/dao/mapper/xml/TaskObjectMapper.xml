<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zenith.front.dao.mapper.task.TaskObjectMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.zenith.front.model.bean.TaskObject">
        <id column="id" property="id" />
        <result column="code" property="code" />
        <result column="task_code" property="taskCode" />
        <result column="object_code" property="objectCode" />
        <result column="object_org_code" property="objectOrgCode" />
        <result column="commit_context" property="commitContext" />
        <result column="commit_file" property="commitFile" />
        <result column="status" property="status" />
        <result column="create_time" property="createTime" />
        <result column="update_time" property="updateTime" />
        <result column="delete_time" property="deleteTime" />
        <result column="remark" property="remark" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, code, task_code, object_code, object_org_code, commit_context, commit_file, status, create_time, update_time, delete_time, remark
    </sql>


</mapper>
