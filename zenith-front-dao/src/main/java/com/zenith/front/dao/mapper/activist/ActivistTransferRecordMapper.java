package com.zenith.front.dao.mapper.activist;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.zenith.front.model.bean.ActivistTransferRecord;
import com.zenith.front.model.vo.ActivistsTransferInMessageVO;
import com.zenith.front.model.vo.ActivistsTransferOutMessageVO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 积极分子转接记录表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2022-05-09
 */
public interface ActivistTransferRecordMapper extends BaseMapper<ActivistTransferRecord> {

    /**
     * 关系转出
     */
    List<ActivistTransferRecord> findOutByPage(@Param("sql") String sql);

    /**
     * 关系转出总数
     */
    Long findOutTotalByConditon(@Param("sql") String sql);

    /**
     * 关系转入
     */
    List<ActivistTransferRecord> findInByPage(@Param("sql") String sql);

    /**
     * 关系转入总数
     */
    Long findInTotalByConditon(@Param("sql") String sql);

    /**
     * 积极分子转出
     * @return java.util.List<com.zenith.front.vo.ActivistsTransferOutMessageVO>
     * <AUTHOR>
     * @creed Talk is cheap,show me the code
     * @date 2022年08月01日 10时28分
     */
    List<ActivistsTransferOutMessageVO> findOutMessage();

    /**
     * 积极分子转入
     * @return java.util.List<com.zenith.front.vo.ActivistsTransferInMessageVO>
     * <AUTHOR>
     * @creed Talk is cheap,show me the code
     * @date 2022年08月01日 10时28分
     */
    List<ActivistsTransferInMessageVO> findInMessage();
}
