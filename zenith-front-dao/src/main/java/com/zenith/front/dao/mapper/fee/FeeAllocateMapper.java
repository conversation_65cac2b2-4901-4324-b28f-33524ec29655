package com.zenith.front.dao.mapper.fee;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.zenith.front.model.bean.FeeAllocate;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;

/**
 * <p>
 *  Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2021-07-10
 */
public interface FeeAllocateMapper extends BaseMapper<FeeAllocate> {

    List<FeeAllocate> getList(@Param("list") List<String> list,@Param("beginDate") Date beginDate,@Param("endDate") Date endDate);

    Long findTotalByAllocateTime(@Param("allocateOrgCode") String allocateOrgCode,@Param("beginDate") Date beginDate,@Param("endDate") Date endDate);
}
