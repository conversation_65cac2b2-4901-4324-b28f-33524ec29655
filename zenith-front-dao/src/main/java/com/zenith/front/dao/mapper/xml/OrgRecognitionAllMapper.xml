<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zenith.front.dao.mapper.org.OrgRecognitionAllMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.zenith.front.model.bean.OrgRecognitionAll">
        <id column="code" property="code" />
        <result column="org_code" property="orgCode" />
        <result column="org_level_code" property="orgLevelCode" />
        <result column="number" property="number" />
        <result column="recognition_object" property="recognitionObject" />
        <result column="recognition_level" property="recognitionLevel" />
        <result column="delete_time" property="deleteTime" />
        <result column="recognition_type" property="recognitionType" />
        <result column="committee_party" property="committeeParty" />
        <result column="committee_worker" property="committeeWorker" />
        <result column="difficult_party" property="difficultParty" />
        <result column="difficult_worker" property="difficultWorker" />
        <result column="add_good_party" property="addGoodParty" />
        <result column="id" property="id" />
        <result column="anniversary_situation" property="anniversarySituation" />
        <result column="year" property="year" />
        <result column="d01_code" property="d01Code" />
        <result column="file_no" property="fileNo" />
        <result column="annual" property="annual" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        code, org_code, org_level_code, number, recognition_object, recognition_level, delete_time, recognition_type, committee_party, committee_worker, difficult_party, difficult_worker, add_good_party, id, anniversary_situation, year, d01_code, file_no, annual
    </sql>

</mapper>
