package com.zenith.front.dao.mapper.flow;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.zenith.front.model.bean.DataEqualsRecord;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.Collection;

/**
 * <p>
 * 数据交换步骤 持久层
 * </p>
 *
 * <AUTHOR>
 * @date 2025-01-17 14:48:28
 */
@Mapper
public interface DataEqualsRecordMapper extends BaseMapper<DataEqualsRecord> {

    Integer myInserBatch(Collection<DataEqualsRecord> entityList);

    Integer insertBatchWithAutoId(Collection<DataEqualsRecord> entityList);

    DataEqualsRecord findByBizIdAndBizTableName(@Param("bizId") String bizId,@Param("bizTableName") String bizTableName);

    Object findBizTableIdByCode(@Param("tableName") String tableName, @Param("code") String code, @Param("idStr") String idStr);
}
