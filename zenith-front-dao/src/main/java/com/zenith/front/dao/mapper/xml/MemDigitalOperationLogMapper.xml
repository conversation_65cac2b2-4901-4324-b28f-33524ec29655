<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zenith.front.dao.mapper.mem.MemDigitalOperationLogMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.zenith.front.model.bean.MemDigitalOperationLog">
        <id column="code" property="code"/>
        <result column="digital_lot_no" property="digitalLotNo"/>
        <result column="d222_code" property="d222Code"/>
        <result column="d222_name" property="d222Name"/>
        <result column="digital_names" property="digitalNames"/>
        <result column="digital_codes" property="digitalCodes"/>
        <result column="opration_time" property="oprationTime"/>
        <result column="opration_user" property="oprationUser"/>
        <result column="opration_org_name" property="oprationOrgName"/>
        <result column="opration_type" property="oprationType"/>
        <result column="create_time" property="createTime"/>
        <result column="create_user" property="createUser"/>
        <result column="update_time" property="updateTime"/>
        <result column="update_user" property="updateUser"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        code
        , digital_lot_no, d222_code, d222_name, digital_names, digital_codes, opration_time, opration_user, opration_org_name, opration_type, create_time, create_user, update_time, update_user
    </sql>

    <select id="getMemDigitalLogsData" resultType="com.zenith.front.model.vo.MemDigitalOperationLogVO">
        SELECT ccp_mem.name "memName", ccp_mem_digital_operation_log.*
        from ccp_mem_digital_operation_log
        INNER JOIN ccp_mem on ccp_mem_digital_operation_log.digital_lot_no = ccp_mem.digital_lot_no
        WHERE ccp_mem_digital_operation_log.delete_time is NULL
        AND ccp_mem.delete_time IS NULL
        AND ccp_mem.digital_lot_no IS NOT NULL
        AND ccp_mem.d08_code IN ('1', '2')
        AND (is_transfer!=1 OR is_transfer is null)
        <choose>
            <when test="dto.subordinate==0">
                AND ccp_mem.mem_org_code = #{dto.memOrgCode}
            </when>
            <otherwise>
                AND ccp_mem.mem_org_code like concat(#{dto.memOrgCode},'%')
            </otherwise>
        </choose>
        <if test="dto.isExportAll != null and dto.isExportAll != 1 ">
            AND ccp_mem.digital_lot_no IN
            <foreach collection="dto.digitalLotNoList" item="item" index="index" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        ORDER BY ccp_mem_digital_operation_log.create_time DESC
        LIMIT 100000
    </select>

    <select id="getDevelopMemDigitalLogsData" resultType="com.zenith.front.model.vo.MemDigitalOperationLogVO">
        SELECT ccp_mem.name "memName", ccp_mem_digital_operation_log.*
        from ccp_mem_digital_operation_log
        INNER JOIN ccp_mem_develop as ccp_mem on ccp_mem_digital_operation_log.digital_lot_no = ccp_mem.digital_lot_no
        WHERE ccp_mem_digital_operation_log.delete_time is NULL
        AND ccp_mem.delete_time IS NULL
        AND ccp_mem.digital_lot_no IS NOT NULL
        AND ccp_mem.d08_code IN ('3', '4', '5')
        AND (is_transfer!=1 OR is_transfer is null)
        <choose>
            <when test="dto.subordinate==0">
                AND ccp_mem.develop_org_code = #{dto.memOrgCode}
            </when>
            <otherwise>
                AND ccp_mem.develop_org_code like concat(#{dto.memOrgCode},'%')
            </otherwise>
        </choose>
        <if test="dto.isExportAll != null and dto.isExportAll != 1 ">
            AND ccp_mem.digital_lot_no IN
            <foreach collection="dto.digitalLotNoList" item="item" index="index" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        ORDER BY ccp_mem_digital_operation_log.create_time DESC
        LIMIT 100000
    </select>

</mapper>
