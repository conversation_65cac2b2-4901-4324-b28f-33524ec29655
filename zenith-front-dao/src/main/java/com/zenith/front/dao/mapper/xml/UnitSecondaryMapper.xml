<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zenith.front.dao.mapper.unit.UnitSecondaryMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.zenith.front.model.bean.UnitSecondary">
        <id column="id" property="id" />
        <result column="code" property="code" />
        <result column="es_id" property="esId" />
        <result column="faculty_name" property="facultyName" />
        <result column="d110_code" property="d110Code" />
        <result column="d110_name" property="d110Name" />
        <result column="unit_code" property="unitCode" />
        <result column="org_code" property="orgCode" />
        <result column="org_name" property="orgName" />
        <result column="create_time" property="createTime" />
        <result column="update_time" property="updateTime" />
        <result column="delete_time" property="deleteTime" />
        <result column="timestamp" property="timestamp" />
        <result column="update_account" property="updateAccount" />
        <result column="remark" property="remark" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, code, es_id, faculty_name, d110_code, d110_name, unit_code, org_code, org_name, create_time, update_time, delete_time, timestamp, update_account, remark
    </sql>

    <select id="selectListByOrgCode" resultType="com.zenith.front.model.bean.UnitSecondary">
        SELECT *
        FROM "ccp_unit_secondary"
        WHERE org_code IN (SELECT code FROM ccp_org WHERE org_code LIKE CONCAT(#{orgCode}, '%'))
    </select>

</mapper>
