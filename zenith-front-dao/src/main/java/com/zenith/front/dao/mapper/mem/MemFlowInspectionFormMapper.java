package com.zenith.front.dao.mapper.mem;


import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.zenith.front.model.bean.MemFlow1;
import com.zenith.front.model.bean.MemFlowInspectionForm;
import com.zenith.front.model.bean.OrgFlow;
import com.zenith.front.model.dto.MemFlowInspectionFormListDTO;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
 * <p>
 * 流动党员-工作督察表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025-02-20
 */
public interface MemFlowInspectionFormMapper extends BaseMapper<MemFlowInspectionForm> {

    /**
     * 获取最新的数据
     * @return
     */
    List<MemFlowInspectionForm> findNewList(String orgLevelCode, String type);

    //流出预警
    Map<String,Object> flowOutWarn(@Param("dto") MemFlowInspectionFormListDTO dto);

    //流入预警
    Map<String,Object> flowInWarn(@Param("dto") MemFlowInspectionFormListDTO dto);

    //流入预警-县级流入库
    Map<String,Object> flowInWarnCounty(@Param("dto") MemFlowInspectionFormListDTO dto);

    //当前流出
    Map<String,Object> flowOutCurrent1(@Param("dto") MemFlowInspectionFormListDTO dto);

    Map<String,Object> flowOutCurrent2(@Param("dto") MemFlowInspectionFormListDTO dto);

    Map<String,Object> flowOutCurrent3(@Param("dto") MemFlowInspectionFormListDTO dto);

    //时段流出
    Map<String,Object> flowOutInterval(@Param("dto") MemFlowInspectionFormListDTO dto);

    //当前流入
    Map<String,Object> flowInCurren(@Param("dto") MemFlowInspectionFormListDTO dto);

    //当前流出县级留入库
    Map<String,Object> flowInCurrenCounty(@Param("dto") MemFlowInspectionFormListDTO dto);

    //当前流出本地创建的流出类型流动党组织
    Map<String,Object> flowInCurrenFLow(@Param("dto") MemFlowInspectionFormListDTO dto);

    //时段流入
    Map<String,Object> flowInInterval1(@Param("dto") MemFlowInspectionFormListDTO dto);

    Map<String,Object> flowInInterval2(@Param("dto") MemFlowInspectionFormListDTO dto);

    //流出去向
    Map<String,Object> flowOutGo1(@Param("dto") MemFlowInspectionFormListDTO dto);

    Map<String,Object> flowOutGo2(@Param("dto") MemFlowInspectionFormListDTO dto);

    //流出去向已纳管
    Map<String,Object> flowOutGoNanotube(@Param("dto") MemFlowInspectionFormListDTO dto);

    //流入来源
    Map<String,Object> flowInGo(@Param("dto") MemFlowInspectionFormListDTO dto);

    Map<String,Object> flowInGoNanotube(@Param("dto") MemFlowInspectionFormListDTO dto);

    //流动党员-流动党员登记-接收进展情況
    Map<String,Object> flowReceive(@Param("dto") MemFlowInspectionFormListDTO dto);





    //反查接口==========================
    //流出预警
    Page<MemFlow1> inquireFlowOutWarn(@Param("page") Page page, @Param("dto") MemFlowInspectionFormListDTO dto);

    //流出预警查询 对方未备案流动党员党组织
    Page<OrgFlow> inquireFlowOutWarn1(@Param("page") Page page, @Param("dto") MemFlowInspectionFormListDTO dto);

    //流入预警
    Page<MemFlow1> inquireFlowInWarn(@Param("page") Page page, @Param("dto") MemFlowInspectionFormListDTO dto);

    Page<OrgFlow> inquireFlowInWarn1(@Param("page") Page page, @Param("dto") MemFlowInspectionFormListDTO dto);

    //当前流出
    Page<MemFlow1> inquireFlowOutCurrent1(@Param("page") Page page, @Param("dto") MemFlowInspectionFormListDTO dto);

    Page<MemFlow1> inquireFlowOutCurrent2(@Param("page") Page page, @Param("dto") MemFlowInspectionFormListDTO dto);

    Page<MemFlow1> inquireFlowOutCurrent3(@Param("page") Page page, @Param("dto") MemFlowInspectionFormListDTO dto);

    //时段流出
    Page<MemFlow1> inquireFlowOutInterval(@Param("page") Page page, @Param("dto") MemFlowInspectionFormListDTO dto);

    //当前流入
    Page<MemFlow1> inquireFlowInCurren(@Param("page") Page page, @Param("dto") MemFlowInspectionFormListDTO dto);

    Page<MemFlow1> inquireFlowInCurrenFLow(@Param("page") Page page, @Param("dto") MemFlowInspectionFormListDTO dto);

    //时段流入
    Page<MemFlow1> inquireFlowInInterval1(@Param("page") Page page, @Param("dto") MemFlowInspectionFormListDTO dto);

    Page<MemFlow1> inquireFlowInInterval2(@Param("page") Page page, @Param("dto") MemFlowInspectionFormListDTO dto);

    //流出去向
    Page<MemFlow1> inquireFlowOutGo1(@Param("page") Page page, @Param("dto") MemFlowInspectionFormListDTO dto);

    Page<MemFlow1> inquireFlowOutGo2(@Param("page") Page page, @Param("dto") MemFlowInspectionFormListDTO dto);

    //流出去向已纳管
    Page<MemFlow1> inquireFlowOutGoNanotube(@Param("page") Page page, @Param("dto") MemFlowInspectionFormListDTO dto);

    //流入来源
    Page<MemFlow1> inquireFlowInGo(@Param("page") Page page, @Param("dto") MemFlowInspectionFormListDTO dto);

    //流入来源已纳管
    Page<MemFlow1> inquireFlowInGoNanotube(@Param("page") Page page, @Param("dto") MemFlowInspectionFormListDTO dto);


    //流动党员-流动党员登记-接收进展情況
    Page<MemFlow1> inquireFlowReceive1(@Param("page") Page page, @Param("dto") MemFlowInspectionFormListDTO dto);

    Page<MemFlow1> inquireFlowReceive2(@Param("page") Page page, @Param("dto") MemFlowInspectionFormListDTO dto);




}
