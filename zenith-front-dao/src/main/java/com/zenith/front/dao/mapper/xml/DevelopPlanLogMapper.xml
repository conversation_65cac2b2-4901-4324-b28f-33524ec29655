<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zenith.front.dao.mapper.develop.DevelopPlanLogMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.zenith.front.model.bean.DevelopPlanLog">
        <id column="id" property="id" />
        <result column="org_code" property="orgCode" />
        <result column="org_zb_code" property="orgZbCode" />
        <result column="title" property="title" />
        <result column="content" property="content" />
        <result column="update_account" property="updateAccount" />
        <result column="code" property="code" />
        <result column="create_time" property="createTime" />
        <result column="update_time" property="updateTime" />
        <result column="delete_time" property="deleteTime" />
        <result column="timestamp" property="timestamp" />
        <result column="is_history" property="isHistory" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, org_code, org_zb_code, title, content, update_account, code, create_time, update_time, delete_time, timestamp, is_history
    </sql>

    <select id="selectListByOrgCode" resultType="com.zenith.front.model.bean.DevelopPlanLog">
        SELECT *
        FROM "ccp_develop_plan_log"
        WHERE org_code IN (SELECT code FROM ccp_org WHERE org_code like concat(#{orgCode}, '%'))
    </select>

</mapper>
