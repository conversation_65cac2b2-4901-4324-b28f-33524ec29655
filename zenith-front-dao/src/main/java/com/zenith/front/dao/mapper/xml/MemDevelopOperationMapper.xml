<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zenith.front.dao.mapper.mem.MemDevelopOperationMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.zenith.front.model.bean.MemDevelopOperation">
        <id column="code" property="code" />
        <result column="develop_code" property="developCode" />
        <result column="d07_code" property="d07Code" />
        <result column="d07_name" property="d07Name" />
        <result column="d09_code" property="d09Code" />
        <result column="d09_name" property="d09Name" />
        <result column="d19_name" property="d19Name" />
        <result column="d19_code" property="d19Code" />
        <result column="d20_name" property="d20Name" />
        <result column="d20_code" property="d20Code" />
        <result column="d21_code" property="d21Code" />
        <result column="d21_name" property="d21Name" />
        <result column="is_farmer" property="isFarmer" />
        <result column="is_high_knowledge" property="isHighKnowledge" />
        <result column="advanced_model_code" property="advancedModelCode" />
        <result column="is_dispatch" property="isDispatch" />
        <result column="byyx" property="byyx" />
        <result column="d88_code" property="d88Code" />
        <result column="d88_name" property="d88Name" />
        <result column="has_young_farmers" property="hasYoungFarmers" />
        <result column="has_worker" property="hasWorker" />
        <result column="d08_name" property="d08Name" />
        <result column="d08_code" property="d08Code" />
        <result column="create_time" property="createTime" />
        <result column="update_time" property="updateTime" />
        <result column="delete_time" property="deleteTime" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        code, develop_code, d07_code, d07_name, d09_code, d09_name, d19_name, d19_code, d20_name, d20_code, d21_code, d21_name, is_farmer, is_high_knowledge, advanced_model_code, is_dispatch, byyx, d88_code, d88_name, has_young_farmers, has_worker, d08_name, d08_code, create_time, update_time, delete_time
    </sql>

    <select id="selectListByOrgCode" resultType="com.zenith.front.model.bean.MemDevelopOperation">
        SELECT *
        FROM "ccp_mem_develop_operation"
        WHERE develop_code IN (SELECT code FROM ccp_mem_develop WHERE develop_org_code LIKE CONCAT(#{orgCode}, '%'))
    </select>

</mapper>
