package com.zenith.front.dao.mapper.unit;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.zenith.front.model.bean.UnitAll;
import com.zenith.front.model.vo.UnitVO;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;
import java.util.Map;

/**
 * <p>
 * Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2021-07-10
 */
public interface UnitAllMapper extends BaseMapper<UnitAll> {

    @Select("${sql}")
    Map<String, Object> findBySQLGetMapValue(@Param("sql") String sql);

    /**
     * 根据机构code查询主单位信息
     *
     * @param orgCode 机构code
     * @return
     */
    UnitAll getUnitByOrgCode(@Param("orgCode") String orgCode);

    List<UnitVO> getReportResultHtml_29_5(@Param("orgLevelCode") String orgLevelCode);

    /**
     * 修复unitAll中关联组织和创建组织不一致的数据
     **/
    List<UnitAll> findRepairUnitCtreateOrgCode();

    /**
     * 更新锁定字段
     *
     * @param lockFields 锁定字段
     * @return
     */
    int updateLockFields(@Param("lockFields") Object lockFields);


    List<String> getXZCOrgCode(@Param("orgCode") String orgCode);
}
