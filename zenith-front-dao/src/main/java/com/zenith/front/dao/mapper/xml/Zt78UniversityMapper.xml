<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zenith.front.mapper.Zt78UniversityMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.zenith.front.model.bean.Zt78University">
        <id column="code" property="code" />
        <result column="unit_code" property="unitCode" />
        <result column="has_stand_committee" property="hasStandCommittee" />
        <result column="special_report" property="specialReport" />
        <result column="revised_rules" property="revisedRules" />
        <result column="secretary_to_district" property="secretaryToDistrict" />
        <result column="secretary_to_school" property="secretaryToSchool" />
        <result column="party_member" property="partyMember" />
        <result column="deputy_secretary" property="deputySecretary" />
        <result column="has_secretary_office" property="hasSecretaryOffice" />
        <result column="has_secretary_no_office" property="hasSecretaryNoOffice" />
        <result column="has_minister_office" property="hasMinisterOffice" />
        <result column="has_minister_no_office" property="hasMinisterNoOffice" />
        <result column="has_propaganda_office" property="hasPropagandaOffice" />
        <result column="has_propaganda_no_office" property="hasPropagandaNoOffice" />
        <result column="has_unite_office" property="hasUniteOffice" />
        <result column="has_unite_no_office" property="hasUniteNoOffice" />
        <result column="is_affiliated_college" property="isAffiliatedCollege" />
        <result column="d82_code" property="d82Code" />
        <result column="d82_name" property="d82Name" />
        <result column="year_yxdwsj_join_train_num" property="yearYxdwsjJoinTrainNum" />
        <result column="year_dzbsj_join_train_num" property="yearDzbsjJoinTrainNum" />
        <result column="year_rqjmdyxdzbsj_num" property="yearRqjmdyxdzbsjNum" />
        <result column="is_year_change_secret" property="isYearChangeSecret" />
        <result column="year_develop_party" property="yearDevelopParty" />
        <result column="year_develop_teacher_party" property="yearDevelopTeacherParty" />
        <result column="year_develop_student_party" property="yearDevelopStudentParty" />
        <result column="year_develop_graduate_party" property="yearDevelopGraduateParty" />
        <result column="is_not_turned_relation" property="isNotTurnedRelation" />
        <result column="not_turned_relation_bysdy_num" property="notTurnedRelationBysdyNum" />
        <result column="create_time" property="createTime" />
        <result column="update_time" property="updateTime" />
        <result column="delete_time" property="deleteTime" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        code, unit_code, has_stand_committee, special_report, revised_rules, secretary_to_district, secretary_to_school, party_member, deputy_secretary, has_secretary_office, has_secretary_no_office, has_minister_office, has_minister_no_office, has_propaganda_office, has_propaganda_no_office, has_unite_office, has_unite_no_office, is_affiliated_college, d82_code, d82_name, year_yxdwsj_join_train_num, year_dzbsj_join_train_num, year_rqjmdyxdzbsj_num, is_year_change_secret, year_develop_party, year_develop_teacher_party, year_develop_student_party, year_develop_graduate_party, is_not_turned_relation, not_turned_relation_bysdy_num, create_time, update_time, delete_time
    </sql>

</mapper>
