<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zenith.front.dao.mapper.mem.MemFlowMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.zenith.front.model.bean.MemFlow">
        <id column="id" property="id" />
        <result column="code" property="code" />
        <result column="es_id" property="esId" />
        <result column="birthday" property="birthday" />
        <result column="idcard" property="idcard" typeHandler="com.zenith.front.common.encrypt.typehandler.EncryptTypeHandler"/>
        <result column="gender" property="gender" />
        <result column="gender_name" property="genderName" />
        <result column="mem_type_name" property="memTypeName" />
        <result column="mem_type_code" property="memTypeCode" />
        <result column="mem_code" property="memCode" />
        <result column="mem_name" property="memName" typeHandler="com.zenith.front.common.encrypt.typehandler.EncryptTypeHandler"/>
        <result column="nation_name" property="nationName" />
        <result column="nation_code" property="nationCode" />
        <result column="mem_org_name" property="memOrgName" />
        <result column="mem_org_org_code" property="memOrgOrgCode" />
        <result column="mem_org_code" property="memOrgCode" />
        <result column="is_hold" property="isHold" />
        <result column="is_explicit_inflow_org" property="isExplicitInflowOrg" />
        <result column="is_prov_out" property="isProvOut" />
        <result column="is_prov_out_name" property="isProvOutName" />
        <result column="outflow_date" property="outflowDate" />
        <result column="outflow_reason" property="outflowReason" />
        <result column="outflow_type_code" property="outflowTypeCode" />
        <result column="outflow_type_name" property="outflowTypeName" />
        <result column="outflow_org_code" property="outflowOrgCode" />
        <result column="outflow_org_name" property="outflowOrgName" />
        <result column="outflow_org_org_code" property="outflowOrgOrgCode" />
        <result column="outflow_reason_code" property="outflowReasonCode" />
        <result column="outflow_reason_name" property="outflowReasonName" />
        <result column="outflow_unit_code" property="outflowUnitCode" />
        <result column="outflow_unit_name" property="outflowUnitName" />
        <result column="outflow_unit_type_code" property="outflowUnitTypeCode" />
        <result column="outflow_unit_type_name" property="outflowUnitTypeName" />
        <result column="outflow_area_name" property="outflowAreaName" />
        <result column="outflow_org_linkman" property="outflowOrgLinkman" />
        <result column="outflow_org_phone" property="outflowOrgPhone" />
        <result column="backflow_date" property="backflowDate" />
        <result column="backflow_reason" property="backflowReason" />
        <result column="backflow_type_code" property="backflowTypeCode" />
        <result column="backflow_type_name" property="backflowTypeName" />
        <result column="backflow_org_code" property="backflowOrgCode" />
        <result column="backflow_org_name" property="backflowOrgName" />
        <result column="backflow_org_org_code" property="backflowOrgOrgCode" />
        <result column="backflow_unit_code" property="backflowUnitCode" />
        <result column="backflow_unit_name" property="backflowUnitName" />
        <result column="backflow_unit_type_code" property="backflowUnitTypeCode" />
        <result column="backflow_unit_type_name" property="backflowUnitTypeName" />
        <result column="backflow_org_linkman" property="backflowOrgLinkman" />
        <result column="backflow_org_phone" property="backflowOrgPhone" />
        <result column="backflow_job_code" property="backflowJobCode" />
        <result column="backflow_job_name" property="backflowJobName" />
        <result column="flow_status" property="flowStatus" />
        <result column="flow_add_type" property="flowAddType" />
        <result column="create_time" property="createTime" />
        <result column="update_time" property="updateTime" />
        <result column="timestamp" property="timestamp" />
        <result column="delete_time" property="deleteTime" />
        <result column="outflow_area_id" property="outflowAreaId" />
        <result column="update_account" property="updateAccount" />
        <result column="outflow_edu_code" property="outflowEduCode" />
        <result column="outflow_edu_name" property="outflowEduName" />
        <result column="backflow_is_hold" property="backflowIsHold" />
        <result column="stratum_type_name" property="stratumTypeName" />
        <result column="stratum_type_code" property="stratumTypeCode" />
        <result column="outflow_job_code" property="outflowJobCode" />
        <result column="outflow_job_name" property="outflowJobName" />
        <result column="match_situation" property="matchSituation" />
        <result column="join_org_date" property="joinOrgDate" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, code, es_id, birthday, idcard, gender, gender_name, mem_type_name, mem_type_code, mem_code, mem_name, nation_name, nation_code, mem_org_name, mem_org_org_code, mem_org_code, is_hold, is_explicit_inflow_org, is_prov_out, is_prov_out_name, outflow_date, outflow_reason, outflow_type_code, outflow_type_name, outflow_org_code, outflow_org_name, outflow_org_org_code, outflow_reason_code, outflow_reason_name, outflow_unit_code, outflow_unit_name, outflow_unit_type_code, outflow_unit_type_name, outflow_area_name, outflow_org_linkman, outflow_org_phone, backflow_date, backflow_reason, backflow_type_code, backflow_type_name, backflow_org_code, backflow_org_name, backflow_org_org_code, backflow_unit_code, backflow_unit_name, backflow_unit_type_code, backflow_unit_type_name, backflow_org_linkman, backflow_org_phone, backflow_job_code, backflow_job_name, flow_status, flow_add_type, create_time, update_time, timestamp, delete_time, outflow_area_id, update_account, outflow_edu_code, outflow_edu_name, backflow_is_hold, stratum_type_name, stratum_type_code, outflow_job_code, outflow_job_name, match_situation, join_org_date
    </sql>

    <select id="selectListByOrgCode" resultType="com.zenith.front.model.bean.MemFlow">
        SELECT ccp_mem_flow.code, ccp_mem_flow.mem_org_org_code AS memOrgOrgCode, ccp_mem_flow.flow_status as flowStatus
        FROM "ccp_mem_flow"
                 LEFT JOIN ccp_org ON ccp_org.code = ccp_mem_flow.mem_org_code
                 LEFT JOIN ccp_unit ON ccp_org.main_unit_code = ccp_unit.code
        WHERE ccp_mem_flow.delete_time IS NULL
          AND ccp_mem_flow.flow_status = '1'
          AND ccp_mem_flow.flow_add_type = '1'
          AND ccp_org.delete_time IS NULL
          AND (is_dissolve IS NULL OR is_dissolve &lt;&gt; 1)
          AND ccp_unit.d04_code in ('922', '923')
          AND mem_org_org_code LIKE concat(#{orgCode}, '%')
    </select>

    <select id="exportInflowParty" resultType="com.zenith.front.model.vo.MemFlow1VO">
        SELECT mem_flow.*
        FROM
            mem_flow
                LEFT JOIN ccp_org ON mem_flow.in_org_code = ccp_org.code
        WHERE
            mem_flow.out_place_code NOT IN ( '3', '4' )
          AND ( mem_flow.flow_step = '2' )
          AND ccp_org.delete_time IS NULL
          AND (
            ( org_code LIKE CONCAT ( #{ orgCode }, '%' ) AND in_org_code IS NOT NULL )
                OR ( ( out_org_branch_org_code LIKE CONCAT ( #{ orgCode }, '%' ) AND in_org_code IS NULL ) )
            )
    </select>

    <select id="exportFlowOutParty" resultType="com.zenith.front.model.vo.MemFlow1VO">
        SELECT *
        FROM
            mem_flow
        WHERE
            (
                ( flow_step = '2' AND flow_out = '1' AND flow_in = '1' )
                    OR ( flow_step = '1' AND flow_out = '1' AND flow_in = '0' )
                )
          AND mem_org_org_code LIKE CONCAT ( #{ orgCode }, '%' )
    </select>
</mapper>
