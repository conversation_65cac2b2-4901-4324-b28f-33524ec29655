<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zenith.front.dao.mapper.mem.MemFlowInspectionFormMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.zenith.front.model.bean.MemFlowInspectionForm">
        <id column="id" property="id" />
        <result column="create_time" property="createTime" />
        <result column="org_level_code" property="orgLevelCode" />
        <result column="a1" property="a1" />
        <result column="a2" property="a2" />
        <result column="a3" property="a3" />
        <result column="a4" property="a4" />
        <result column="a5" property="a5" />
        <result column="a6" property="a6" />
        <result column="a7" property="a7" />
        <result column="a8" property="a8" />
        <result column="a9" property="a9" />
        <result column="a10" property="a10" />
        <result column="a11" property="a11" />
        <result column="a12" property="a12" />
        <result column="a13" property="a13" />
        <result column="a14" property="a14" />
        <result column="a15" property="a15" />
        <result column="a16" property="a16" />
        <result column="a17" property="a17" />
        <result column="a18" property="a18" />
        <result column="a19" property="a19" />
        <result column="a20" property="a20" />
        <result column="a21" property="a21" />
        <result column="b1" property="b1" />
        <result column="b2" property="b2" />
        <result column="b3" property="b3" />
        <result column="b4" property="b4" />
        <result column="b5" property="b5" />
        <result column="b6" property="b6" />
        <result column="b7" property="b7" />
        <result column="b8" property="b8" />
        <result column="b9" property="b9" />
        <result column="b10" property="b10" />
        <result column="b11" property="b11" />
        <result column="b12" property="b12" />
        <result column="b13" property="b13" />
        <result column="b14" property="b14" />
        <result column="b15" property="b15" />
        <result column="b16" property="b16" />
        <result column="b17" property="b17" />
        <result column="b18" property="b18" />
        <result column="b19" property="b19" />
        <result column="b20" property="b20" />
        <result column="b21" property="b21" />
        <result column="b22" property="b22" />
        <result column="b23" property="b23" />
        <result column="b24" property="b24" />
        <result column="b25" property="b25" />
        <result column="b26" property="b26" />
        <result column="b27" property="b27" />
        <result column="b28" property="b28" />
        <result column="b29" property="b29" />
        <result column="b30" property="b30" />
        <result column="b31" property="b31" />
        <result column="b32" property="b32" />
        <result column="b33" property="b33" />
        <result column="b34" property="b34" />
        <result column="c1" property="c1" />
        <result column="c2" property="c2" />
        <result column="c3" property="c3" />
        <result column="c4" property="c4" />
        <result column="c5" property="c5" />
        <result column="c6" property="c6" />
        <result column="c7" property="c7" />
        <result column="c8" property="c8" />
        <result column="c9" property="c9" />
        <result column="c10" property="c10" />
        <result column="c11" property="c11" />
        <result column="c12" property="c12" />
        <result column="c13" property="c13" />
        <result column="c14" property="c14" />
        <result column="c15" property="c15" />
        <result column="c16" property="c16" />
        <result column="c17" property="c17" />
        <result column="d1" property="d1" />
        <result column="d2" property="d2" />
        <result column="d3" property="d3" />
        <result column="d4" property="d4" />
        <result column="d5" property="d5" />
        <result column="d6" property="d6" />
        <result column="d7" property="d7" />
        <result column="d8" property="d8" />
        <result column="d9" property="d9" />
        <result column="d10" property="d10" />
        <result column="d11" property="d11" />
        <result column="d12" property="d12" />
        <result column="d13" property="d13" />
        <result column="d14" property="d14" />
        <result column="d15" property="d15" />
        <result column="d16" property="d16" />
        <result column="d17" property="d17" />
        <result column="d18" property="d18" />
        <result column="d19" property="d19" />
        <result column="d20" property="d20" />
        <result column="d21" property="d21" />
        <result column="d22" property="d22" />
        <result column="d23" property="d23" />
        <result column="d24" property="d24" />
        <result column="d25" property="d25" />
        <result column="d26" property="d26" />
        <result column="d27" property="d27" />
        <result column="d28" property="d28" />
        <result column="d29" property="d29" />
        <result column="d30" property="d30" />
        <result column="e1" property="e1" />
        <result column="e2" property="e2" />
        <result column="e3" property="e3" />
        <result column="e4" property="e4" />
        <result column="e5" property="e5" />
        <result column="e6" property="e6" />
        <result column="e7" property="e7" />
        <result column="e8" property="e8" />
        <result column="e9" property="e9" />
        <result column="e10" property="e10" />
        <result column="e11" property="e11" />
        <result column="e12" property="e12" />
        <result column="e13" property="e13" />
        <result column="e14" property="e14" />
        <result column="e15" property="e15" />
        <result column="e16" property="e16" />
        <result column="e17" property="e17" />
        <result column="e18" property="e18" />
        <result column="e19" property="e19" />
        <result column="e20" property="e20" />
        <result column="e21" property="e21" />
        <result column="e22" property="e22" />
        <result column="e23" property="e23" />
        <result column="e24" property="e24" />
        <result column="e25" property="e25" />
        <result column="e26" property="e26" />
        <result column="e27" property="e27" />
        <result column="e28" property="e28" />
        <result column="e29" property="e29" />
        <result column="e30" property="e30" />
        <result column="e31" property="e31" />
        <result column="e32" property="e32" />
        <result column="e33" property="e33" />
        <result column="e34" property="e34" />
        <result column="e35" property="e35" />
        <result column="e36" property="e36" />
        <result column="e37" property="e37" />
        <result column="e38" property="e38" />
        <result column="e39" property="e39" />
        <result column="e40" property="e40" />
        <result column="e41" property="e41" />
        <result column="e42" property="e42" />
        <result column="e43" property="e43" />
        <result column="e44" property="e44" />
        <result column="e45" property="e45" />
        <result column="e46" property="e46" />
        <result column="e47" property="e47" />
        <result column="e48" property="e48" />
        <result column="e49" property="e49" />
        <result column="e50" property="e50" />
        <result column="e51" property="e51" />
        <result column="e52" property="e52" />
        <result column="e53" property="e53" />
        <result column="e54" property="e54" />
        <result column="e55" property="e55" />
        <result column="e56" property="e56" />
        <result column="e57" property="e57" />
        <result column="e58" property="e58" />
        <result column="e59" property="e59" />
        <result column="e60" property="e60" />
        <result column="e61" property="e61" />
        <result column="e62" property="e62" />
        <result column="e63" property="e63" />
        <result column="e64" property="e64" />
        <result column="e65" property="e65" />
        <result column="e66" property="e66" />
        <result column="e67" property="e67" />
        <result column="e68" property="e68" />
        <result column="e69" property="e69" />
        <result column="e70" property="e70" />
        <result column="f1" property="f1" />
        <result column="f2" property="f2" />
        <result column="f3" property="f3" />
        <result column="f4" property="f4" />
        <result column="f5" property="f5" />
        <result column="f6" property="f6" />
        <result column="f7" property="f7" />
        <result column="f8" property="f8" />
        <result column="f9" property="f9" />
        <result column="f10" property="f10" />
        <result column="f11" property="f11" />
        <result column="f12" property="f12" />
        <result column="f13" property="f13" />
        <result column="f14" property="f14" />
        <result column="f15" property="f15" />
        <result column="f16" property="f16" />
        <result column="f17" property="f17" />
        <result column="f18" property="f18" />
        <result column="f19" property="f19" />
        <result column="f20" property="f20" />
        <result column="f21" property="f21" />
        <result column="f22" property="f22" />
        <result column="f23" property="f23" />
        <result column="f24" property="f24" />
        <result column="f25" property="f25" />
        <result column="f26" property="f26" />
        <result column="f27" property="f27" />
        <result column="f28" property="f28" />
        <result column="f29" property="f29" />
        <result column="f30" property="f30" />
        <result column="f31" property="f31" />
        <result column="f32" property="f32" />
        <result column="f33" property="f33" />
        <result column="f34" property="f34" />
        <result column="f35" property="f35" />
        <result column="f36" property="f36" />
        <result column="f37" property="f37" />
        <result column="f38" property="f38" />
        <result column="f39" property="f39" />
        <result column="f40" property="f40" />
        <result column="f41" property="f41" />
        <result column="f42" property="f42" />
        <result column="f43" property="f43" />
        <result column="f44" property="f44" />
        <result column="f45" property="f45" />
        <result column="f46" property="f46" />
        <result column="f47" property="f47" />
        <result column="f48" property="f48" />
        <result column="f49" property="f49" />
        <result column="f50" property="f50" />
        <result column="f51" property="f51" />
        <result column="f52" property="f52" />
        <result column="f53" property="f53" />
        <result column="f54" property="f54" />
        <result column="f55" property="f55" />
        <result column="f56" property="f56" />
        <result column="f57" property="f57" />
        <result column="f58" property="f58" />
        <result column="f59" property="f59" />
        <result column="f60" property="f60" />
        <result column="f61" property="f61" />
        <result column="f62" property="f62" />
        <result column="f63" property="f63" />
        <result column="f64" property="f64" />
        <result column="f65" property="f65" />
        <result column="f66" property="f66" />
        <result column="f67" property="f67" />
        <result column="f68" property="f68" />
        <result column="f69" property="f69" />
        <result column="f70" property="f70" />
        <result column="org_name" property="orgName" />
    </resultMap>

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap1" type="com.zenith.front.model.bean.MemFlow1">
        <id column="code" property="code"/>
        <result column="flow_uq_code" property="flowUqCode"/>
        <result column="mem_code" property="memCode"/>
        <result column="mem_name" property="memName"
                typeHandler="com.zenith.front.common.encrypt.typehandler.EncryptTypeHandler"/>
        <result column="mem_sex_code" property="memSexCode"/>
        <result column="mem_sex_name" property="memSexName"/>
        <result column="mem_idcard" property="memIdcard"
                typeHandler="com.zenith.front.common.encrypt.typehandler.EncryptTypeHandler"/>
        <result column="mem_phone" property="memPhone"
                typeHandler="com.zenith.front.common.encrypt.typehandler.EncryptTypeHandler"/>
        <result column="mem_org_code" property="memOrgCode"/>
        <result column="mem_org_name" property="memOrgName"/>
        <result column="mem_org_org_code" property="memOrgOrgCode"/>
        <result column="mem_org_phone" property="memOrgPhone"
                typeHandler="com.zenith.front.common.encrypt.typehandler.EncryptTypeHandler"/>
        <result column="mem_d09_code" property="memD09Code"/>
        <result column="mem_d09_name" property="memD09Name"/>
        <result column="mem_info" property="memInfo"/>
        <result column="is_province" property="isProvince"/>
        <result column="cross_node" property="crossNode"/>
        <result column="out_place_code" property="outPlaceCode"/>
        <result column="out_place_name" property="outPlaceName"/>
        <result column="out_org_code" property="outOrgCode"/>
        <result column="out_org_name" property="outOrgName"/>
        <result column="out_org_branch_code" property="outOrgBranchCode"/>
        <result column="out_org_branch_name" property="outOrgBranchName"/>
        <result column="out_org_branch_org_code" property="outOrgBranchOrgCode"/>
        <result column="out_administrative_division_code" property="outAdministrativeDivisionCode"/>
        <result column="out_administrative_division_name" property="outAdministrativeDivisionName"/>
        <result column="lost_contact_code" property="lostContactCode"/>
        <result column="lost_contact_name" property="lostContactName"/>
        <result column="flow_type_code" property="flowTypeCode"/>
        <result column="flow_type_name" property="flowTypeName"/>
        <result column="flow_reason_code" property="flowReasonCode"/>
        <result column="flow_reason_name" property="flowReasonName"/>
        <result column="out_time" property="outTime"/>
        <result column="register_time" property="registerTime"/>
        <result column="out_org_remarks" property="outOrgRemarks"/>
        <result column="out_org_d04_code" property="outOrgD04Code"/>
        <result column="out_org_d04_name" property="outOrgD04Name"/>
        <result column="party_expenses_out_time" property="partyExpensesOutTime"/>
        <result column="party_expenses_in_time" property="partyExpensesInTime"/>
        <result column="in_org_life" property="inOrgLife"/>
        <result column="in_org_life_code" property="inOrgLifeCode"/>
        <result column="in_org_life_name" property="inOrgLifeName"/>
        <result column="in_feedback" property="inFeedback"/>
        <result column="mz_appraisal" property="mzAppraisal"/>
        <result column="is_hold" property="isHold"/>
        <result column="has_flow_back" property="hasFlowBack"/>
        <result column="flow_back_time" property="flowBackTime"/>
        <result column="cancel_time" property="cancelTime"/>
        <result column="stop_time" property="stopTime"/>
        <result column="flow_out" property="flowOut"/>
        <result column="flow_in" property="flowIn"/>
        <result column="flow_step" property="flowStep"/>
        <result column="in_org_code" property="inOrgCode"/>
        <result column="in_org_name" property="inOrgName"/>
        <result column="in_org_phone" property="inOrgPhone"
                typeHandler="com.zenith.front.common.encrypt.typehandler.EncryptTypeHandler"/>
        <result column="in_org_d04_code" property="inOrgD04Code"/>
        <result column="in_org_d04_name" property="inOrgD04Name"/>
        <result column="in_unit_d04_code" property="inUnitD04Code"/>
        <result column="in_unit_d04_name" property="inUnitD04Name"/>
        <result column="in_unit_d16_code" property="inUnitD16Code"/>
        <result column="in_unit_d16_name" property="inUnitD16Name"/>
        <result column="in_mem_d09_code" property="inMemD09Code"/>
        <result column="in_mem_d09_name" property="inMemD09Name"/>
        <result column="in_mem_d20_code" property="inMemD20Code"/>
        <result column="in_mem_d20_name" property="inMemD20Name"/>
        <result column="in_mem_phone" property="inMemPhone"
                typeHandler="com.zenith.front.common.encrypt.typehandler.EncryptTypeHandler"/>
        <result column="in_receiving_time" property="inReceivingTime"/>
        <result column="move_to_county_time" property="moveToCountyTime"/>
        <result column="has_county_library" property="hasCountyLibrary"/>
        <result column="reject_reason_code" property="rejectReasonCode"/>
        <result column="reject_reason_name" property="rejectReasonName"/>
        <result column="reject_time" property="rejectTime"/>
        <result column="reject_org_code" property="rejectOrgCode"/>
        <result column="reject_org_name" property="rejectOrgName"/>
        <result column="reject_org_phone" property="rejectOrgPhone"
                typeHandler="com.zenith.front.common.encrypt.typehandler.EncryptTypeHandler"/>
        <result column="create_time" property="createTime"/>
        <result column="update_time" property="updateTime"/>
        <result column="update_account" property="updateAccount"/>
        <result column="paired_contact" property="pairedContact"
                typeHandler="com.zenith.front.common.encrypt.typehandler.EncryptTypeHandler"/>
        <result column="paired_contact_phone" property="pairedContactPhone"
                typeHandler="com.zenith.front.common.encrypt.typehandler.EncryptTypeHandler"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, create_time, org_level_code, a1, a2, a3, a4, a5, a6, a7, a8, a9, a10, a11, a12, a13, a14, a15, a16, a17, a18, a19, a20, a21, b1, b2, b3, b4, b5, b6, b7, b8, b9, b10, b11, b12, b13, b14, b15, b16, b17, b18, b19, b20, b21, b22, b23, b24, b25, b26, b27, b28, b29, b30, b31, b32, b33, b34, c1, c2, c3, c4, c5, c6, c7, c8, c9, c10, c11, c12, c13, c14, c15, c16, c17, d1, d2, d3, d4, d5, d6, d7, d8, d9, d10, d11, d12, d13, d14, d15, d16, d17, d18, d19, d20, d21, d22, d23, d24, d25, d26, d27, d28, d29, d30, e1, e2, e3, e4, e5, e6, e7, e8, e9, e10, e11, e12, e13, e14, e15, e16, e17, e18, e19, e20, e21, e22, e23, e24, e25, e26, e27, e28, e29, e30, e31, e32, e33, e34, e35, e36, e37, e38, e39, e40, e41, e42, e43, e44, e45, e46, e47, e48, e49, e50, e51, e52, e53, e54, e55, e56, e57, e58, e59, e60, e61, e62, e63, e64, e65, e66, e67, e68, e69, e70, f1, f2, f3, f4, f5, f6, f7, f8, f9, f10, f11, f12, f13, f14, f15, f16, f17, f18, f19, f20, f21, f22, f23, f24, f25, f26, f27, f28, f29, f30, f31, f32, f33, f34, f35, f36, f37, f38, f39, f40, f41, f42, f43, f44, f45, f46, f47, f48, f49, f50, f51, f52, f53, f54, f55, f56, f57, f58, f59, f60, f61, f62, f63, f64, f65, f66, f67, f68, f69, f70, org_name
    </sql>
    <select id="findNewList" resultType="com.zenith.front.model.bean.MemFlowInspectionForm">
        select *
        from (select *,
                     ROW_NUMBER() OVER(PARTITION BY org_level_code  ORDER BY create_time  DESC) as rn
              from ccp_mem_flow_inspection_form where type = #{type} ) t
        where t.rn = 1
          and t.org_level_code like concat(#{orgLevelCode}, '%')
        ORDER BY LENGTH(org_level_code), org_level_code
    </select>

    <select id="flowOutWarn" resultType="java.util.Map">
        select
        sum(case when f.flow_out = '1' and f.flow_step in ('1','2') then 1 else 0 end)  as n1,
        sum(case when f.flow_out = '1' and f.flow_step = '1' and  flow_type_code in ('1','2','3')   then 1 else 0 end)  as n2,
        sum(case when f.flow_out = '1' and f.flow_step = '1' and  flow_type_code in ('2','3') then 1 else 0 end)  as n3,
        sum(case when f.flow_out = '1' and f.flow_step = '1' and  flow_type_code = '1' then 1 else 0 end)  as n4,
        sum(case when register_time::timestamp +'20 day' &lt; now() and f.flow_out = '1' and f.flow_step = '1' and  flow_type_code in ('2','3') then 1 else 0 end)  as n5,
        0 as n6,
        sum(case when register_time::timestamp +'30 day' &lt; now() and f.flow_out = '1' and f.flow_step = '1' and  flow_type_code = '1' then 1 else 0 end)  as n7,
        0 as n8,
        sum(case when register_time::timestamp +'90 day' &lt; now() and f.flow_step in ('1','2') and f.flow_out = '1' and out_place_code = '3' then 1 else 0 end)  as n9,
        0 as n10,
        sum(case when f.flow_step in ('1','2') and f.flow_out = '1' and out_place_code = '3' then 1 else 0 end)  as n18,
        sum(case when register_time::timestamp + '1 year'::interval &lt; now() and f.flow_step in ('1','2') and f.flow_out = '1' and out_place_code = '4' then 1 else 0 end)  as n11,
        0 as n12,
        sum(case when f.flow_step in ('1','2') and f.flow_out = '1' and out_place_code = '4' then 1 else 0 end) as n17,
        sum(case when register_time::timestamp + '2 year'::interval &lt; now() and f.flow_step in ('1','2') and f.flow_out = '1' and out_place_code = '4' then 1 else 0 end)  as n13,
        0 as n14,
        0 as n15,
        (select count(cof.id) from ccp_org_flow cof left join ccp_org_flow_audit cofa on cof.code = cofa.flow_org_code
        where cof.d200_code = '2' and cofa.status = '0' and cof.delete_time is null and source_type = '1'
        <if test="dto.orgCode != dto.topOrgCode">
            and cof.org_code like concat(#{dto.orgCode},'%')
        </if>
        ) as n16
        from mem_flow f
        where mem_org_org_code like concat(#{dto.orgCode}, '%')
    </select>

    <select id="flowInWarn" resultType="java.util.Map">
        <include refid="flowIn"/>
    </select>

    <select id="flowInWarnCounty" resultType="java.util.Map">
        <include refid="flowIn"/>
        and f.has_county_library = '1'
    </select>
    <sql id="flowIn">
        select
            sum(case when flow_step in('1', '2')  then 1 else 0 end)  as o1,
            sum(case when f.flow_step = '1'  then 1 else 0 end)  as o2,
            sum(case when f.flow_step = '1' and  flow_type_code in ('2','3')  then 1 else 0 end)  as o3,
            sum(case when f.flow_step = '1' and  flow_type_code = '1'  then 1 else 0 end)  as o4,
            sum(case when register_time::timestamp +'20 day' &lt; now() and f.flow_step = '1' and  flow_type_code in ('2','3') then 1 else 0 end)  as o5,
            0 as o6,
            sum(case when register_time::timestamp +'30 day' &lt; now() and f.flow_step = '1' and  flow_type_code = '1' then 1 else 0 end)  as o7,
            0 as o8,
            <if test="dto.orgCode == dto.topOrgCode">
                (select count(cof.id) from ccp_org_flow cof left join ccp_org_flow_audit cofa on cof.code = cofa.flow_org_code
                where cof.d200_code = '2' and cofa.status = '0' and cof.delete_time is null and source_type = '2') as o9
            </if>
            <if test="dto.orgCode != dto.topOrgCode">
                0 as o9
            </if>

        from
            (select
                 f.*
             from mem_flow f
                      LEFT JOIN ccp_org g ON f.in_org_code = g.code
             where
                 out_place_code not in ('3', '4')
               and g.delete_time IS NULL
               and (
                 ((org_code like concat(#{dto.orgCode}, '%') and in_org_code is not null or
                   out_org_branch_org_code like concat(#{dto.orgCode}, '%') and in_org_code is null) and has_county_library != '1')
            or
            ((org_code like concat(#{dto.orgCode}, '%')
            and in_org_code is not null or out_org_branch_org_code like concat(#{dto.orgCode}, '%')
            and in_org_code is null)
            and has_county_library = '1')
            )
        UNION
        select
            f.*
        from mem_flow f
                 LEFT JOIN ccp_org_flow cof ON f.in_org_code = cof.code
        where
            out_place_code not in ('3', '4')
          and cof.delete_time IS NULL
          and (
            ((org_code like concat(#{dto.orgCode}, '%') and in_org_code is not null or
              out_org_branch_org_code like concat(#{dto.orgCode}, '%') and in_org_code is null) and has_county_library != '1')
           or
            ((org_code like concat(#{dto.orgCode}, '%')
          and in_org_code is not null or out_org_branch_org_code like concat(#{dto.orgCode}, '%')
          and in_org_code is null)
          and has_county_library = '1')
            )) f  where 1=1
            <if test="dto.orgCode != dto.topOrgCode">
                and f.has_county_library != '1'
            </if>
    </sql>

    <select id="flowOutCurrent1" resultType="java.util.Map">
        select
            sum(1)  as a1,
            sum(case when f.flow_type_code = '1' and (f.flow_step = '2' and f.flow_out = '1' and f.flow_in = '1')  then 1 else 0 end) as a2,
            sum(case when f.flow_type_code = '1' and (f.flow_step = '1' and f.flow_out = '1' and f.flow_in = '0')  then 1 else 0 end) as a3,
            sum(case when f.flow_type_code = '2' and (f.flow_step = '2' and f.flow_out = '1' and f.flow_in = '1')  then 1 else 0 end) as a4,
            sum(case when f.flow_type_code = '2' and (f.flow_step = '1' and f.flow_out = '1' and f.flow_in = '0')  then 1 else 0 end) as a5,
            sum(case when f.flow_type_code = '3' and (f.flow_step = '2' and f.flow_out = '1' and f.flow_in = '1')  then 1 else 0 end) as a6,
            sum(case when f.flow_type_code = '3' and (f.flow_step = '1' and f.flow_out = '1' and f.flow_in = '0')  then 1 else 0 end) as a7,
            sum(case when f.out_place_code = '3' and (f.flow_step = '1' and f.flow_out = '1' and f.flow_in = '0') then 1 else 0 end) as a8,
            sum(case when f.out_place_code = '4' and (f.flow_step = '1' and f.flow_out = '1' and f.flow_in = '0') then 1 else 0 end) as a9,

            sum(case when f.out_place_code = '1' and (f.flow_step = '2' and f.flow_out = '1' and f.flow_in = '1')  then 1 else 0 end) as a10,
            sum(case when f.out_place_code = '1' and (f.flow_step = '1' and f.flow_out = '1' and f.flow_in = '0')  then 1 else 0 end) as a11,

            sum(case when f.out_place_code = '5' and (f.flow_step = '2' and f.flow_out = '1' and f.flow_in = '1')  then 1 else 0 end) as a12,
            sum(case when f.out_place_code = '5' and (f.flow_step = '1' and f.flow_out = '1' and f.flow_in = '0')  then 1 else 0 end) as a13,

            sum(case when f.out_place_code = '2' and (f.flow_step = '2' and f.flow_out = '1' and f.flow_in = '1')  then 1 else 0 end) as a14,
            sum(case when f.out_place_code = '2' and (f.flow_step = '1' and f.flow_out = '1' and f.flow_in = '0')  then 1 else 0 end) as a15,
            sum(case when f.out_place_code = '3' then 1 else 0 end) as a16,
            sum(case when f.out_place_code = '4' then 1 else 0 end) as a17
        from mem_flow f
        where
            (f.flow_step = '1' and f.flow_out = '1' and f.flow_in = '0'
                or f.flow_step = '2' and f.flow_out = '1' and f.flow_in = '1')
          and f.mem_org_org_code like concat(#{dto.orgCode}, '%')
    </select>

    <select id="flowOutCurrent2" resultType="java.util.Map">
        select
        sum( case when register_time::timestamp+'30 day' &gt;= now() or register_time is null then 1 else 0 end) as a18,
        sum(case when (register_time::timestamp+'30 day' &gt;= now() or register_time is null) and has_county_library = '1' then 1 else 0 end) as a19,
        sum( case when register_time::timestamp+'30 day' &lt; now() then 1 else 0 end) as a20,
        sum(case when register_time::timestamp+'30 day' &lt; now() and has_county_library = '1' then 1 else 0 end) as a21
        from mem_flow f
        where
        out_place_code not in ('3', '4')
        and f.flow_step = '1'
        and mem_org_org_code like concat(#{dto.orgCode}, '%')
    </select>

    <select id="flowOutCurrent3" resultType="java.util.Map">
        select
            sum(case when f.flow_step = '3' AND f.flow_out = '1' then 1 else 0 end) as a22,
            sum(case when f.flow_step = '3' AND f.flow_out = '1' and f.flow_type_code in ('2','3') and f.reject_reason_name != '超期终止' then 1 else 0 end) as a23,
            sum(case when f.flow_step = '3' AND f.flow_out = '1' and f.flow_type_code in ('2','3') and f.reject_reason_name = '超期终止' then 1 else 0 end) as a24,
            sum(case when f.flow_step = '3' AND f.flow_out = '1' and f.flow_type_code = '1' and (f.reject_reason_name !='超期终止' or f.reject_reason_name is null) then 1 else 0 end) as a25,
            sum(case when f.flow_step = '3' AND f.flow_out = '1' and f.flow_type_code = '1' and f.reject_reason_name = '超期终止' then 1 else 0 end) as a26,
            SUM(case when f.flow_step = '5' and f.flow_out = '1' then 1 else 0 end) as a27,
            SUM(case when f.flow_out = '1' and flow_step = '5' and f.flow_type_code in ('2','3') then 1 else 0 end) as a28,
            SUM(case when f.flow_out = '1' and flow_step = '5' and f.flow_type_code = '1' then 1 else 0 end) as a29,
            SUM(case when f.is_farmer = '1' then 1 else 0 end) as a30

        from mem_flow f
        where
            f.flow_out = '1' and mem_org_org_code like concat(#{dto.orgCode}, '%')
    </select>

    <select id="flowOutInterval" resultType="java.util.Map">
        select
            SUM(case when f.flow_out = '1' then 1 else 0 end) as b1,
            SUM(case when f.flow_out = '1' and f.flow_type_code = '1' then 1 else 0 end) as b2,
            SUM(case when f.flow_out = '1' and f.flow_type_code in ('2','3') then 1 else 0 end) as b3,
            SUM(case when f.flow_out = '1' and f.out_place_code = '3' then 1 else 0 end) as b4,
            SUM(case when f.flow_out = '1' and f.out_place_code = '4' then 1 else 0 end) as b5,
            SUM(case when f.flow_out = '1' and flow_step = '2' then 1 else 0 end) as b6,
            SUM(case when f.flow_out = '1' and flow_step = '2' and f.flow_type_code = '1' then 1 else 0 end) as b7,
            SUM(case when f.flow_out = '1' and flow_step = '2' and f.flow_type_code in ('2','3') then 1 else 0 end) as b8,
            SUM(case when f.flow_out = '1' and flow_step = '5' then 1 else 0 end) as b9,
            SUM(case when f.flow_out = '1' and flow_step = '5' and f.flow_type_code = '1' then 1 else 0 end) as b10,
            SUM(case when f.flow_out = '1' and flow_step = '5' and f.flow_type_code in ('2','3') then 1 else 0 end) as b11,
            SUM(case when f.flow_out = '1' and flow_step = '5' and f.out_place_code = '3' then 1 else 0 end) as b12,
            SUM(case when f.flow_out = '1' and flow_step = '5' and f.out_place_code = '4' then 1 else 0 end) as b13,
            SUM(case when f.lddy_id in (select s.code from ccp_mem_flow_sign s left join ccp_mem_flow_sign_audit a on s.code = a.sign_code and a.delete_time is null
                                        where s.delete_time is null and a.status = '1') then 1 else 0 end) as b14,
            SUM(case when f.lddy_id in (select s.code from ccp_mem_flow_sign s left join ccp_mem_flow_sign_audit a on s.code = a.sign_code and a.delete_time is null
                                        where s.delete_time is null and a.status = '1') then 1 else 0 end) as b15,
            0 as b16,
            SUM(case when f.flow_out = '1' and flow_step = '3' and reject_reason_name != '超期终止' then 1 else 0 end) as b17,
            SUM(case when f.flow_out = '1' and flow_step = '3' and reject_reason_name != '超期终止' and f.flow_type_code = '1' then 1 else 0 end) as b18,
            SUM(case when f.flow_out = '1' and flow_step = '3' and reject_reason_name != '超期终止' and f.flow_type_code in ('2','3') then 1 else 0 end) as b19,
            SUM(case when f.flow_out = '1' and reject_reason_name = '超期终止' and flow_step = '3' then 1 else 0 end) as b20,
            SUM(case when f.flow_out = '1' and reject_reason_name = '超期终止' and flow_step = '3' and f.flow_type_code = '1' then 1 else 0 end) as b21,
            SUM(case when f.flow_out = '1' and reject_reason_name = '超期终止' and flow_step = '3' and f.flow_type_code in ('2','3') then 1 else 0 end) as b22,
            sum(case when f.out_place_code = '2' then 1 else 0 end) as b23,
            sum(case when f.out_place_code = '2' and f.flow_out = '1' and flow_step = '2' then 1 else 0 end) as b24,
            sum(case when f.out_place_code = '2' and f.flow_out = '1' and reject_reason_name != '超期终止' and flow_step = '3' then 1 else 0 end) as b25,
            sum(case when f.out_place_code = '2' and f.flow_out = '1' and reject_reason_name = '超期终止' and flow_step = '3' then 1 else 0 end) as b26,
            sum(case when f.out_place_code = '2' and f.flow_out = '1' and flow_step = '6' then 1 else 0 end) as b27,
            0 as b28,
            sum(case when f.flow_out = '1' and flow_step = '6' then 1 else 0 end) as b29,
            sum(case when f.flow_out = '1' and flow_step = '6' and f.flow_type_code = '1' then 1 else 0 end) as b30,
            sum(case when f.flow_out = '1' and flow_step = '6' and f.flow_type_code in ('2','3') then 1 else 0 end) as b31,
            0 as b32,
            0 as b33,
            0 as b34
        from
            mem_flow f
                left join ccp_org g on
                    g.delete_time is null
                    and f.mem_org_org_code = g.org_code
                    and (g.is_dissolve is null
                    or g.is_dissolve != 1)
        where
            f.flow_out = '1' and mem_org_org_code like concat(#{dto.orgCode}, '%')
            <if test="dto.startDate!=null and dto.endDate ==null">
                and f.register_time &gt;= #{dto.startDate}
            </if>
             <if test="dto.endDate!=null and dto.startDate ==null">
                and f.register_time &lt;= #{dto.endDate}
             </if>
             <if test="dto.startDate!=null and dto.endDate != null">
                and f.register_time BETWEEN #{dto.startDate} AND #{dto.endDate}
             </if>
             <if test="dto.startDate==null and dto.endDate == null">
                and f.register_time BETWEEN CURRENT_DATE - INTERVAL '10 days' AND CURRENT_DATE
             </if>

    </select>

    <select id="flowInCurren" resultType="java.util.Map">
        select
        SUM(case when flow_step in('1','2') then 1 else 0 end) as c1,
        SUM(case when flow_step = '2' and flow_type_code = '1' then 1 else 0 end) as c2,
        SUM(case when flow_step = '1' and flow_type_code = '1' then 1 else 0 end) as c3,
        SUM(case when flow_step = '2' and flow_type_code = '2' then 1 else 0 end) as c4,
        SUM(case when flow_step = '1' and flow_type_code = '2' then 1 else 0 end) as c5,
        SUM(case when flow_step = '2' and flow_type_code = '3' then 1 else 0 end) as c6,
        SUM(case when flow_step = '1' and flow_type_code = '3' then 1 else 0 end) as c7,
        sum(case when flow_step = '2' and f.out_place_code = '1' then 1 else 0 end) as c8,
        sum(case when flow_step = '1' and f.out_place_code = '1' then 1 else 0 end) as c9,
        sum(case when flow_step = '2' and f.out_place_code = '5' then 1 else 0 end) as c10,
        sum(case when flow_step = '1' and f.out_place_code = '5' then 1 else 0 end) as c11,
        sum(case when flow_step = '2' and f.out_place_code = '2' then 1 else 0 end) as c12,
        sum(case when flow_step = '1' and f.out_place_code = '2' then 1 else 0 end) as c13,
        sum( case when flow_step = '1' and  (register_time::timestamp+'30 day' &gt;= now() or register_time is null)  then 1 else 0 end) as c14,
        sum(case when flow_step = '1' and (register_time::timestamp+'30 day' &gt;= now() or register_time is null)  and has_county_library = '1' then 1 else 0 end) as c15,
        sum( case when flow_step = '1' and register_time::timestamp+'30 day' &lt; now()   then 1 else 0 end) as c16,
        sum(case when flow_step = '1' and register_time::timestamp+'30 day' &lt; now() and has_county_library = '1' then 1 else 0 end) as c17,
        sum(case when flow_step = '2' and ((lrd_is_farmer is null or lrd_is_farmer = '') and (flow_mem_type_code is null or flow_mem_type_code = '')) then 1 else 0 end) as c18,
        sum(case when flow_step = '2' and ((lrd_is_farmer is not null and lrd_is_farmer != '') or (flow_mem_type_code is not null and flow_mem_type_code != '')) then 1 else 0 end) c19,
        sum(case when flow_step = '2' and lrd_is_farmer = '1' then 1 else 0 end) as c20,
        sum(case when flow_step ='2' and (flow_mem_type_code ~ '(^|,)1(,|$)') then 1 else 0 end) as c21,
        sum(case when flow_step ='2' and (flow_mem_type_code ~ '(^|,)31(,|$)') then 1 else 0 end) as c22,
        sum(case when flow_step ='2' and (flow_mem_type_code ~ '(^|,)32(,|$)') then 1 else 0 end) as c23,
        sum(case when flow_step ='2' and (flow_mem_type_code ~ '(^|,)33(,|$)') then 1 else 0 end) as c24,
        sum(case when flow_step ='2' and (flow_mem_type_code ~ '(^|,)34(,|$)') then 1 else 0 end) as c25,
        sum(case when flow_step ='2' and (flow_mem_type_code ~ '(^|,)35(,|$)') then 1 else 0 end) as c26,
        sum(case when flow_step ='2' and (flow_mem_type_code ~ '(^|,)36(,|$)') then 1 else 0 end) as c27,
        sum(case when flow_step ='2' and (flow_mem_type_code ~ '(^|,)2(,|$)') then 1 else 0 end) as c28,
        sum(case when flow_step ='2' and (flow_mem_type_code ~ '(^|,)4(,|$)') then 1 else 0 end) as c29

        from
        (select
        f.*
        from
        mem_flow f
        LEFT JOIN ccp_org g ON f.in_org_code = g.code
        where
        out_place_code not in ('3', '4')
        and g.delete_time IS NULL
        and (
        ((org_code like concat(#{dto.orgCode}, '%') and in_org_code is not null or
        out_org_branch_org_code like concat(#{dto.orgCode}, '%') and in_org_code is null) and has_county_library != '1')
        or
        ((org_code like concat(#{dto.orgCode}, '%')
        and in_org_code is not null or out_org_branch_org_code like concat(#{dto.orgCode}, '%')
        and in_org_code is null)
        and has_county_library = '1')
        )
        union
        select
        f.*
        from
        mem_flow f
        LEFT JOIN ccp_org_flow g ON f.in_org_code = g.code
        where
        out_place_code not in ('3', '4')
        and g.delete_time IS NULL
        and (
        ((org_code like concat(#{dto.orgCode}, '%') and in_org_code is not null or
        out_org_branch_org_code like concat(#{dto.orgCode}, '%') and in_org_code is null) and has_county_library != '1')
        or
        ((org_code like concat(#{dto.orgCode}, '%')
        and in_org_code is not null or out_org_branch_org_code like concat(#{dto.orgCode}, '%')
        and in_org_code is null)
        and has_county_library = '1')
        )) f where 1=1
        <if test="dto.orgCode != dto.topOrgCode">
            and f.has_county_library != '1'
        </if>
    </select>

    <select id="flowInCurrenCounty" resultType="java.util.Map">
        select
            SUM(case when flow_step in('1','2') then 1 else 0 end) as c1,
            SUM(case when flow_step = '1' and flow_type_code = '1' then 1 else 0 end) as c3,
            SUM(case when flow_step = '1' and flow_type_code = '2' then 1 else 0 end) as c5,
            SUM(case when flow_step = '1' and flow_type_code = '3' then 1 else 0 end) as c7
        from
            (select
                 f.*
             from
                 mem_flow f
                     LEFT JOIN ccp_org g ON f.in_org_code = g.code
             where
                 out_place_code not in ('3', '4')
               and g.delete_time IS NULL
               and (
                 ((org_code like concat(#{dto.orgCode}, '%') and in_org_code is not null or
                   out_org_branch_org_code like concat(#{dto.orgCode}, '%') and in_org_code is null) and has_county_library != '1')
               or
                ((org_code like concat(#{dto.orgCode}, '%')
                and in_org_code is not null or out_org_branch_org_code like concat(#{dto.orgCode}, '%')
                and in_org_code is null)
                and has_county_library = '1')
                )
            union
            select
                f.*
            from
                mem_flow f
                    LEFT JOIN ccp_org_flow g ON f.in_org_code = g.code
            where
                out_place_code not in ('3', '4')
              and g.delete_time IS NULL
              and (
                ((org_code like concat(#{dto.orgCode}, '%') and in_org_code is not null or
                  out_org_branch_org_code like concat(#{dto.orgCode}, '%') and in_org_code is null) and has_county_library != '1')
               or
                ((org_code like concat(#{dto.orgCode}, '%')
              and in_org_code is not null or out_org_branch_org_code like concat(#{dto.orgCode}, '%')
              and in_org_code is null)
              and has_county_library = '1')
                )) f where f.has_county_library = '1' and f.flow_step = '1'
    </select>

    <select id="flowInCurrenFLow" resultType="java.util.Map">
        select
            SUM(case when flow_step in('1','2') then 1 else 0 end) as c1,
            SUM(case when flow_step = '2' and flow_type_code = '1' then 1 else 0 end) as c2,
            SUM(case when flow_step = '2' and flow_type_code = '2' then 1 else 0 end) as c4,
            SUM(case when flow_step = '2' and flow_type_code = '3' then 1 else 0 end) as c6,
            sum(case when flow_step = '2' and f.out_place_code = '5' then 1 else 0 end) as c10,
            sum(case when flow_step = '2' and ((lrd_is_farmer is null or lrd_is_farmer = '') and (flow_mem_type_code is null or flow_mem_type_code = '')) then 1 else 0 end) as c18,
            sum(case when flow_step = '2' and ((lrd_is_farmer is not null and lrd_is_farmer != '') or (flow_mem_type_code is not null and flow_mem_type_code != '')) then 1 else 0 end) c19,
            sum(case when flow_step = '2' and lrd_is_farmer = '1' then 1 else 0 end) as c20,
            sum(case when flow_step ='2' and (flow_mem_type_code ~ '(^|,)1(,|$)') then 1 else 0 end) as c21,
            sum(case when flow_step ='2' and (flow_mem_type_code ~ '(^|,)31(,|$)') then 1 else 0 end) as c22,
            sum(case when flow_step ='2' and (flow_mem_type_code ~ '(^|,)32(,|$)') then 1 else 0 end) as c23,
            sum(case when flow_step ='2' and (flow_mem_type_code ~ '(^|,)33(,|$)') then 1 else 0 end) as c24,
            sum(case when flow_step ='2' and (flow_mem_type_code ~ '(^|,)34(,|$)') then 1 else 0 end) as c25,
            sum(case when flow_step ='2' and (flow_mem_type_code ~ '(^|,)35(,|$)') then 1 else 0 end) as c26,
            sum(case when flow_step ='2' and (flow_mem_type_code ~ '(^|,)36(,|$)') then 1 else 0 end) as c27,
            sum(case when flow_step ='2' and (flow_mem_type_code ~ '(^|,)2(,|$)') then 1 else 0 end) as c28,
            sum(case when flow_step ='2' and (flow_mem_type_code ~ '(^|,)4(,|$)') then 1 else 0 end) as c29

        from
            (select
                 f.*
             from
                 mem_flow f
                     LEFT JOIN ccp_org g ON f.in_org_code = g.code
             where
                 out_place_code not in ('3', '4')
               and g.delete_time IS NULL
               and (
                 ((org_code like concat(#{dto.orgCode}, '%') and in_org_code is not null or
                   out_org_branch_org_code like concat(#{dto.orgCode}, '%') and in_org_code is null) and has_county_library != '1')
               or
                ((org_code like concat(#{dto.orgCode}, '%')
                and in_org_code is not null or out_org_branch_org_code like concat(#{dto.orgCode}, '%')
                and in_org_code is null)
                and has_county_library = '1')
                )
        union
        select
            f.*
        from
            mem_flow f
                LEFT JOIN ccp_org_flow g ON f.in_org_code = g.code
        where
            out_place_code not in ('3', '4')
          and g.delete_time IS NULL
          and (
            ((org_code like concat(#{dto.orgCode}, '%') and in_org_code is not null or
              out_org_branch_org_code like concat(#{dto.orgCode}, '%') and in_org_code is null) and has_county_library != '1')
           or
            ((org_code like concat(#{dto.orgCode}, '%')
          and in_org_code is not null or out_org_branch_org_code like concat(#{dto.orgCode}, '%')
          and in_org_code is null)
          and has_county_library = '1')
            )) f where f.out_org_branch_code in (select ccp_org_flow.code from ccp_org_flow where d200_code ='2' and is_enable ='1' and source_type = '1')
    </select>

    <select id="flowInInterval1" resultType="java.util.Map">
        select
        sum(1) as d1,
        sum(case when f.is_province = '1'  then 1 else 0 end) as d2,
        sum(case when f.is_province = '0'  then 1 else 0 end) as d3,
        sum(case when f.flow_step = '2' AND f.has_county_library!='1'  then 1 else 0 end) as d4,
        sum(case when f.flow_step = '2' AND f.has_county_library!='1' AND f.is_province = '1' then 1 else 0 end) as d5,
        sum(case when f.flow_step = '2' AND f.has_county_library!='1' AND f.is_province = '0' then 1 else 0 end) as d6,
        sum(case when f.flow_step = '5' AND f.flow_back_time IS NOT NULL then 1 else 0 end) as d7,
        sum(case when f.flow_step = '5' AND f.flow_back_time IS NOT NULL AND f.is_province = '1' then 1 else 0 end) as d8,
        sum(case when f.flow_step = '5' AND f.flow_back_time IS NOT NULL AND f.is_province = '0' then 1 else 0 end) as d9,
        sum(case when f.flow_step = '3' AND f.reject_time IS NOT NULL then 1 else 0 end) as d10,
        sum(case when f.flow_step = '3' AND f.reject_time IS NOT NULL AND f.is_province = '1' then 1 else 0 end) as d11,
        sum(case when f.flow_step = '3' AND f.reject_time IS NOT NULL AND f.is_province = '0' then 1 else 0 end) as d12,
        sum(case when f.flow_step = '3' AND f.reject_time IS NOT NULL AND f.reject_reason_name ='超期终止' then 1 else 0 end) as d13,
        sum(case when f.flow_step = '3' AND f.reject_time IS NOT NULL AND f.reject_reason_name ='超期终止' AND f.is_province = '1' then 1 else 0 end) as d14,
        sum(case when f.flow_step = '3' AND f.reject_time IS NOT NULL AND f.reject_reason_name ='超期终止' AND f.is_province = '0' then 1 else 0 end) as d15,
        sum(case when f.flow_step in ('2','3','6') AND f.has_county_library='1' then 1 else 0 end) as d16,
        sum(case when f.flow_step = '2' AND  f.has_county_library='1' then 1 else 0 end) as d17,
        sum(case when f.flow_step = '3' AND  f.has_county_library='1' and  f.reject_reason_name != '超期终止' then 1 else 0 end) as d18,
        sum(case when f.flow_step = '3' AND  f.has_county_library='1' and  f.reject_reason_name ='超期终止' then 1 else 0 end) as d19,
        sum(case when f.flow_step = '6' AND  f.has_county_library='1'then 1 else 0 end) as d20,
        0 AS d21,
        sum(case when f.flow_step = '6' AND f.cancel_time IS NOT NULL then 1 else 0 end) as d22,
        sum(case when f.flow_step = '6' AND f.cancel_time IS NOT NULL AND f.is_province = '1' then 1 else 0 end) as d23,
        sum(case when f.flow_step = '6' AND f.cancel_time IS NOT NULL AND f.is_province = '0' then 1 else 0 end) as d24,
        0 AS d25,
        0 AS d26,
        0 AS d27
        from
        (select
        f.*
        from mem_flow f
        LEFT JOIN ccp_org ON f.in_org_code = ccp_org.code	AND ccp_org.delete_time IS NULL
        where
        ((ccp_org.org_code like concat(#{dto.orgCode}, '%') AND f."in_org_code" IS NOT NULL)
        OR (f.out_org_branch_org_code like concat(#{dto.orgCode}, '%') AND f."in_org_code" IS NULL))
        AND f.out_place_code NOT IN ( '3', '4' )
        union
        select
        f.*
        from mem_flow f
        LEFT JOIN ccp_org_flow ON f.in_org_code = ccp_org_flow.code	AND ccp_org_flow.delete_time IS NULL
        where
        ((ccp_org_flow.org_code like concat(#{dto.orgCode}, '%') AND f."in_org_code" IS NOT NULL)
        OR (f.out_org_branch_org_code like concat(#{dto.orgCode}, '%') AND f."in_org_code" IS NULL))
        AND f.out_place_code NOT IN ( '3', '4' )
        ) f
        where 1=1
        <if test="dto.startDate!=null and dto.endDate ==null">
            and f.register_time &gt;= #{dto.startDate}
        </if>
        <if test="dto.endDate!=null and dto.startDate ==null">
            and f.register_time &lt;= #{dto.endDate}
        </if>
        <if test="dto.startDate!=null and dto.endDate != null">
            and f.register_time BETWEEN #{dto.startDate} AND #{dto.endDate}
        </if>
        <if test="dto.startDate==null and dto.endDate == null">
            and f.register_time BETWEEN CURRENT_DATE - INTERVAL '10 days' AND CURRENT_DATE
        </if>
    </select>

    <select id="flowInInterval2" resultType="java.util.Map">
        select
            sum(case when f.flow_step = '2'  and f.in_org_code in
                                                 (select ccp_org_flow.code from ccp_org_flow where d200_code ='1' and is_enable ='1') then 1 else 0 end) AS d28,
            sum(case when f.flow_step = '2'  and f.in_org_code in
                                                 (select ccp_org_flow.code from ccp_org_flow where d200_code ='2' and is_enable ='1' and source_type = '1') then 1 else 0 end) AS d29,
            sum(case when f.flow_step = '2'  and f.in_org_code in
                                                 (select ccp_org_flow.code from ccp_org_flow where d200_code ='2' and is_enable ='1' and source_type = '2') then 1 else 0 end) AS d30
        from mem_flow f
                 LEFT JOIN ccp_org_flow ON f.in_org_code = ccp_org_flow.code	AND ccp_org_flow.delete_time IS NULL
        where
            ((ccp_org_flow.org_code like concat(#{dto.orgCode}, '%') AND f."in_org_code" IS NOT NULL)
                OR (f.out_org_branch_org_code like concat(#{dto.orgCode}, '%') AND f."in_org_code" IS NULL))
          AND f.out_place_code NOT IN ( '3', '4' )
            <if test="dto.startDate!=null and dto.endDate ==null">
                and f.register_time &gt;= #{dto.startDate}
            </if>
            <if test="dto.endDate!=null and dto.startDate ==null">
                and f.register_time &lt;= #{dto.endDate}
            </if>
            <if test="dto.startDate!=null and dto.endDate != null">
                and f.register_time BETWEEN #{dto.startDate} AND #{dto.endDate}
            </if>
            <if test="dto.startDate==null and dto.endDate == null">
                and f.register_time BETWEEN CURRENT_DATE - INTERVAL '10 days' AND CURRENT_DATE
            </if>
    </select>


    <select id="flowOutGo1" resultType="java.util.Map">

        select
            sum(case when f.flow_type_code = '1' and out_administrative_division_code like '11%'  then 1 else 0 end) as e1,
            sum(case when f.flow_type_code = '1' and out_administrative_division_code like '12%'  then 1 else 0 end) as e3,
            sum(case when f.flow_type_code = '1' and out_administrative_division_code like '13%'  then 1 else 0 end) as e5,
            sum(case when f.flow_type_code = '1' and out_administrative_division_code like '14%'  then 1 else 0 end) as e7,
            sum(case when f.flow_type_code = '1' and out_administrative_division_code like '15%'  then 1 else 0 end) as e9,
            sum(case when f.flow_type_code = '1' and out_administrative_division_code like '21%'  then 1 else 0 end) as e11,
            sum(case when f.flow_type_code = '1' and out_administrative_division_code like '22%'  then 1 else 0 end) as e13,
            sum(case when f.flow_type_code = '1' and out_administrative_division_code like '23%'  then 1 else 0 end) as e15,
            sum(case when f.flow_type_code = '1' and out_administrative_division_code like '31%'  then 1 else 0 end) as e17,
            sum(case when f.flow_type_code = '1' and out_administrative_division_code like '32%'  then 1 else 0 end) as e19,
            sum(case when f.flow_type_code = '1' and out_administrative_division_code like '33%'  then 1 else 0 end) as e21,
            sum(case when f.flow_type_code = '1' and out_administrative_division_code like '34%'  then 1 else 0 end) as e23,
            sum(case when f.flow_type_code = '1' and out_administrative_division_code like '35%'  then 1 else 0 end) as e25,
            sum(case when f.flow_type_code = '1' and out_administrative_division_code like '36%'  then 1 else 0 end) as e27,
            sum(case when f.flow_type_code = '1' and out_administrative_division_code like '37%'  then 1 else 0 end) as e29,
            sum(case when f.flow_type_code = '1' and out_administrative_division_code like '41%'  then 1 else 0 end) as e31,
            sum(case when f.flow_type_code = '1' and out_administrative_division_code like '42%'  then 1 else 0 end) as e33,
            sum(case when f.flow_type_code = '1' and out_administrative_division_code like '43%'  then 1 else 0 end) as e35,
            sum(case when f.flow_type_code = '1' and out_administrative_division_code like '44%'  then 1 else 0 end) as e37,
            sum(case when f.flow_type_code = '1' and out_administrative_division_code like '45%'  then 1 else 0 end) as e39,
            sum(case when f.flow_type_code = '1' and out_administrative_division_code like '46%'  then 1 else 0 end) as e41,
            sum(case when f.flow_type_code = '1' and out_administrative_division_code like '50%'  then 1 else 0 end) as e43,
            sum(case when f.flow_type_code = '1' and out_administrative_division_code like '51%'  then 1 else 0 end) as e45,
            sum(case when f.flow_type_code in ('2','3')  then 1 else 0 end) as e47,
            sum(case when f.flow_type_code = '1' and out_administrative_division_code like '53%'  then 1 else 0 end) as e49,
            sum(case when f.flow_type_code = '1' and out_administrative_division_code like '54%'  then 1 else 0 end) as e51,
            sum(case when f.flow_type_code = '1' and out_administrative_division_code like '61%'  then 1 else 0 end) as e53,
            sum(case when f.flow_type_code = '1' and out_administrative_division_code like '62%'  then 1 else 0 end) as e55,
            sum(case when f.flow_type_code = '1' and out_administrative_division_code like '63%'  then 1 else 0 end) as e57,
            sum(case when f.flow_type_code = '1' and out_administrative_division_code like '64%'  then 1 else 0 end) as e59,
            sum(case when f.flow_type_code = '1' and out_administrative_division_code like '65%'  then 1 else 0 end) as e61,
            sum(case when f.flow_type_code = '1' and out_administrative_division_code like '71%'  then 1 else 0 end) as e63,
            sum(case when f.flow_type_code = '1' and out_administrative_division_code like '81%'  then 1 else 0 end) as e65,
            sum(case when f.flow_type_code = '1' and out_administrative_division_code like '82%'  then 1 else 0 end) as e67,
            0 AS e69

        from mem_flow f
                 LEFT JOIN ccp_org g ON f.in_org_code = g.code
        where
            (((f.flow_step = '1' and f.flow_out = '1' and f.flow_in = '0') OR (f.flow_step = '2' and f.flow_out = '1' and f.flow_in = '1')) and mem_org_org_code like concat(#{dto.orgCode}, '%'))
          AND f.out_place_code NOT IN ( '3', '4' )
    </select>

    <select id="flowOutGo2" resultType="java.util.Map">
        select
            sum(case when f.out_place_code = '3' then 1 else 0 end) as e71,
            sum(case when f.out_place_code = '4'  then 1 else 0 end) as e72
        from
            mem_flow f
        where
            f.flow_out = '1' and mem_org_org_code like concat(#{dto.orgCode}, '%')
            AND f.out_place_code IN ( '3', '4' ) and flow_step in ('1','2')
    </select>

    <select id="flowOutGoNanotube" resultType="java.util.Map">

        select
            sum(case when f.flow_type_code = '1' and (f.flow_step = '2' and f.flow_out = '1' and f.flow_in = '1') and out_administrative_division_code like '11%'  then 1 else 0 end) as g1,
            sum(case when f.flow_type_code = '1' and (f.flow_step = '2' and f.flow_out = '1' and f.flow_in = '1') and out_administrative_division_code like '12%'  then 1 else 0 end) as g2,
            sum(case when f.flow_type_code = '1' and (f.flow_step = '2' and f.flow_out = '1' and f.flow_in = '1') and out_administrative_division_code like '13%'  then 1 else 0 end) as g3,
            sum(case when f.flow_type_code = '1' and (f.flow_step = '2' and f.flow_out = '1' and f.flow_in = '1') and out_administrative_division_code like '14%'  then 1 else 0 end) as g4,
            sum(case when f.flow_type_code = '1' and (f.flow_step = '2' and f.flow_out = '1' and f.flow_in = '1') and out_administrative_division_code like '15%'  then 1 else 0 end) as g5,
            sum(case when f.flow_type_code = '1' and (f.flow_step = '2' and f.flow_out = '1' and f.flow_in = '1') and out_administrative_division_code like '21%'  then 1 else 0 end) as g6,
            sum(case when f.flow_type_code = '1' and (f.flow_step = '2' and f.flow_out = '1' and f.flow_in = '1') and out_administrative_division_code like '22%'  then 1 else 0 end) as g7,
            sum(case when f.flow_type_code = '1' and (f.flow_step = '2' and f.flow_out = '1' and f.flow_in = '1') and out_administrative_division_code like '23%'  then 1 else 0 end) as g8,
            sum(case when f.flow_type_code = '1' and (f.flow_step = '2' and f.flow_out = '1' and f.flow_in = '1') and out_administrative_division_code like '31%'  then 1 else 0 end) as g9,
            sum(case when f.flow_type_code = '1' and (f.flow_step = '2' and f.flow_out = '1' and f.flow_in = '1') and out_administrative_division_code like '32%'  then 1 else 0 end) as g10,
            sum(case when f.flow_type_code = '1' and (f.flow_step = '2' and f.flow_out = '1' and f.flow_in = '1') and out_administrative_division_code like '33%'  then 1 else 0 end) as g11,
            sum(case when f.flow_type_code = '1' and (f.flow_step = '2' and f.flow_out = '1' and f.flow_in = '1') and out_administrative_division_code like '34%'  then 1 else 0 end) as g12,
            sum(case when f.flow_type_code = '1' and (f.flow_step = '2' and f.flow_out = '1' and f.flow_in = '1') and out_administrative_division_code like '35%'  then 1 else 0 end) as g13,
            sum(case when f.flow_type_code = '1' and (f.flow_step = '2' and f.flow_out = '1' and f.flow_in = '1') and out_administrative_division_code like '36%'  then 1 else 0 end) as g14,
            sum(case when f.flow_type_code = '1' and (f.flow_step = '2' and f.flow_out = '1' and f.flow_in = '1') and out_administrative_division_code like '37%'  then 1 else 0 end) as g15,
            sum(case when f.flow_type_code = '1' and (f.flow_step = '2' and f.flow_out = '1' and f.flow_in = '1') and out_administrative_division_code like '41%'  then 1 else 0 end) as g16,
            sum(case when f.flow_type_code = '1' and (f.flow_step = '2' and f.flow_out = '1' and f.flow_in = '1') and out_administrative_division_code like '42%'  then 1 else 0 end) as g17,
            sum(case when f.flow_type_code = '1' and (f.flow_step = '2' and f.flow_out = '1' and f.flow_in = '1') and out_administrative_division_code like '43%'  then 1 else 0 end) as g18,
            sum(case when f.flow_type_code = '1' and (f.flow_step = '2' and f.flow_out = '1' and f.flow_in = '1') and out_administrative_division_code like '44%'  then 1 else 0 end) as g19,
            sum(case when f.flow_type_code = '1' and (f.flow_step = '2' and f.flow_out = '1' and f.flow_in = '1') and out_administrative_division_code like '45%'  then 1 else 0 end) as g20,
            sum(case when f.flow_type_code = '1' and (f.flow_step = '2' and f.flow_out = '1' and f.flow_in = '1') and out_administrative_division_code like '46%'  then 1 else 0 end) as g21,
            sum(case when f.flow_type_code = '1' and (f.flow_step = '2' and f.flow_out = '1' and f.flow_in = '1') and out_administrative_division_code like '50%'  then 1 else 0 end) as g22,
            sum(case when f.flow_type_code = '1' and (f.flow_step = '2' and f.flow_out = '1' and f.flow_in = '1') and out_administrative_division_code like '51%'  then 1 else 0 end) as g23,
            sum(case when f.flow_type_code in ('2','3') and (f.flow_step = '2' and f.flow_out = '1' and f.flow_in = '1')  then 1 else 0 end) as g24,
            sum(case when f.flow_type_code = '1' and (f.flow_step = '2' and f.flow_out = '1' and f.flow_in = '1') and out_administrative_division_code like '53%'  then 1 else 0 end) as g25,
            sum(case when f.flow_type_code = '1' and (f.flow_step = '2' and f.flow_out = '1' and f.flow_in = '1') and out_administrative_division_code like '54%'  then 1 else 0 end) as g26,
            sum(case when f.flow_type_code = '1' and (f.flow_step = '2' and f.flow_out = '1' and f.flow_in = '1') and out_administrative_division_code like '61%'  then 1 else 0 end) as g27,
            sum(case when f.flow_type_code = '1' and (f.flow_step = '2' and f.flow_out = '1' and f.flow_in = '1') and out_administrative_division_code like '62%'  then 1 else 0 end) as g28,
            sum(case when f.flow_type_code = '1' and (f.flow_step = '2' and f.flow_out = '1' and f.flow_in = '1') and out_administrative_division_code like '63%'  then 1 else 0 end) as g29,
            sum(case when f.flow_type_code = '1' and (f.flow_step = '2' and f.flow_out = '1' and f.flow_in = '1') and out_administrative_division_code like '64%'  then 1 else 0 end) as g30,
            sum(case when f.flow_type_code = '1' and (f.flow_step = '2' and f.flow_out = '1' and f.flow_in = '1') and out_administrative_division_code like '65%'  then 1 else 0 end) as g31,
            sum(case when f.flow_type_code = '1' and (f.flow_step = '2' and f.flow_out = '1' and f.flow_in = '1') and out_administrative_division_code like '71%'  then 1 else 0 end) as g32,
            sum(case when f.flow_type_code = '1' and (f.flow_step = '2' and f.flow_out = '1' and f.flow_in = '1') and out_administrative_division_code like '81%'  then 1 else 0 end) as g33,
            sum(case when f.flow_type_code = '1' and (f.flow_step = '2' and f.flow_out = '1' and f.flow_in = '1') and out_administrative_division_code like '82%'  then 1 else 0 end) as g34,
            sum(case when f.out_place_code = '3' and (f.flow_step = '2' and f.flow_out = '1' and f.flow_in = '1') then 1 else 0 end) as g35,
            sum(case when f.out_place_code = '4' and (f.flow_step = '2' and f.flow_out = '1' and f.flow_in = '1') then 1 else 0 end) as g36
        from mem_flow f
        where
            (((f.flow_step = '1' and f.flow_out = '1' and f.flow_in = '0') OR (f.flow_step = '2' and f.flow_out = '1' and f.flow_in = '1')) and mem_org_org_code like concat(#{dto.orgCode}, '%'))
          </select>
    <select id="flowInGo" resultType="java.util.Map">

        select
            sum(case when f.flow_type_code = '1' and flow_uq_code like '011%'  then 1 else 0 end) as f1,
            sum(case when f.flow_type_code = '1' and flow_uq_code like '012%'  then 1 else 0 end) as f3,
            sum(case when f.flow_type_code = '1' and flow_uq_code like '013%'  then 1 else 0 end) as f5,
            sum(case when f.flow_type_code = '1' and flow_uq_code like '014%'  then 1 else 0 end) as f7,
            sum(case when f.flow_type_code = '1' and flow_uq_code like '015%'  then 1 else 0 end) as f9,
            sum(case when f.flow_type_code = '1' and flow_uq_code like '021%'  then 1 else 0 end) as f11,
            sum(case when f.flow_type_code = '1' and flow_uq_code like '022%'  then 1 else 0 end) as f13,
            sum(case when f.flow_type_code = '1' and flow_uq_code like '023%'  then 1 else 0 end) as f15,
            sum(case when f.flow_type_code = '1' and flow_uq_code like '031%'  then 1 else 0 end) as f17,
            sum(case when f.flow_type_code = '1' and flow_uq_code like '032%'  then 1 else 0 end) as f19,
            sum(case when f.flow_type_code = '1' and flow_uq_code like '033%'  then 1 else 0 end) as f21,
            sum(case when f.flow_type_code = '1' and flow_uq_code like '034%'  then 1 else 0 end) as f23,
            sum(case when f.flow_type_code = '1' and flow_uq_code like '035%'  then 1 else 0 end) as f25,
            sum(case when f.flow_type_code = '1' and flow_uq_code like '036%'  then 1 else 0 end) as f27,
            sum(case when f.flow_type_code = '1' and flow_uq_code like '037%'  then 1 else 0 end) as f29,
            sum(case when f.flow_type_code = '1' and flow_uq_code like '041%'  then 1 else 0 end) as f31,
            sum(case when f.flow_type_code = '1' and flow_uq_code like '042%'  then 1 else 0 end) as f33,
            sum(case when f.flow_type_code = '1' and flow_uq_code like '043%'  then 1 else 0 end) as f35,
            sum(case when f.flow_type_code = '1' and flow_uq_code like '044%'  then 1 else 0 end) as f37,
            sum(case when f.flow_type_code = '1' and flow_uq_code like '045%'  then 1 else 0 end) as f39,
            sum(case when f.flow_type_code = '1' and flow_uq_code like '046%'  then 1 else 0 end) as f41,
            sum(case when f.flow_type_code = '1' and flow_uq_code like '050%'  then 1 else 0 end) as f43,
            sum(case when f.flow_type_code = '1' and flow_uq_code like '051%'  then 1 else 0 end) as f45,
            sum(case when flow_type_code in ('2','3')  then 1 else 0 end) as f47,
            sum(case when f.flow_type_code = '1' and flow_uq_code like '053%'  then 1 else 0 end) as f49,
            sum(case when f.flow_type_code = '1' and flow_uq_code like '054%'  then 1 else 0 end) as f51,
            sum(case when f.flow_type_code = '1' and flow_uq_code like '061%'  then 1 else 0 end) as f53,
            sum(case when f.flow_type_code = '1' and flow_uq_code like '062%'  then 1 else 0 end) as f55,
            sum(case when f.flow_type_code = '1' and flow_uq_code like '063%'  then 1 else 0 end) as f57,
            sum(case when f.flow_type_code = '1' and flow_uq_code like '064%'  then 1 else 0 end) as f59,
            sum(case when f.flow_type_code = '1' and flow_uq_code like '065%'  then 1 else 0 end) as f61,
            sum(case when f.flow_type_code = '1' and flow_uq_code like '071%'  then 1 else 0 end) as f63,
            sum(case when f.flow_type_code = '1' and flow_uq_code like '081%'  then 1 else 0 end) as f65,
            sum(case when f.flow_type_code = '1' and flow_uq_code like '082%'  then 1 else 0 end) as f67,
            0 AS f69
        from
            (select
                 f.*
             from
                 mem_flow f
                     LEFT JOIN ccp_org g ON f.in_org_code = g.code
             where
                 out_place_code not in ('3', '4')
               and g.delete_time IS NULL
               and (
                 ((org_code like concat(#{dto.orgCode}, '%') and in_org_code is not null or
                   out_org_branch_org_code like concat(#{dto.orgCode}, '%') and in_org_code is null) and has_county_library != '1')
                or
                ((org_code like concat(#{dto.orgCode}, '%')
                and in_org_code is not null or out_org_branch_org_code like concat(#{dto.orgCode}, '%')
                and in_org_code is null)
                and has_county_library = '1')
                )
        union
        select
            f.*
        from
            mem_flow f
                LEFT JOIN ccp_org_flow g ON f.in_org_code = g.code
        where
            out_place_code not in ('3', '4')
          and g.delete_time IS NULL
          and (
            ((org_code like concat(#{dto.orgCode}, '%') and in_org_code is not null or
              out_org_branch_org_code like concat(#{dto.orgCode}, '%') and in_org_code is null) and has_county_library != '1')
           or
            ((org_code like concat(#{dto.orgCode}, '%')
          and in_org_code is not null or out_org_branch_org_code like concat(#{dto.orgCode}, '%')
          and in_org_code is null)
          and has_county_library = '1')
            )) f where f.flow_step in ('1','2');
    </select>

    <select id="flowInGoNanotube" resultType="java.util.Map">
        select
            sum(case when f.flow_type_code = '1' and flow_uq_code like '011%'  then 1 else 0 end) as j1,
            sum(case when f.flow_type_code = '1' and flow_uq_code like '012%'  then 1 else 0 end) as j2,
            sum(case when f.flow_type_code = '1' and flow_uq_code like '013%'  then 1 else 0 end) as j3,
            sum(case when f.flow_type_code = '1' and flow_uq_code like '014%'  then 1 else 0 end) as j4,
            sum(case when f.flow_type_code = '1' and flow_uq_code like '015%'  then 1 else 0 end) as j5,
            sum(case when f.flow_type_code = '1' and flow_uq_code like '021%'  then 1 else 0 end) as j6,
            sum(case when f.flow_type_code = '1' and flow_uq_code like '022%'  then 1 else 0 end) as j7,
            sum(case when f.flow_type_code = '1' and flow_uq_code like '023%'  then 1 else 0 end) as j8,
            sum(case when f.flow_type_code = '1' and flow_uq_code like '031%'  then 1 else 0 end) as j9,
            sum(case when f.flow_type_code = '1' and flow_uq_code like '032%'  then 1 else 0 end) as j10,
            sum(case when f.flow_type_code = '1' and flow_uq_code like '033%'  then 1 else 0 end) as j11,
            sum(case when f.flow_type_code = '1' and flow_uq_code like '034%'  then 1 else 0 end) as j12,
            sum(case when f.flow_type_code = '1' and flow_uq_code like '035%'  then 1 else 0 end) as j13,
            sum(case when f.flow_type_code = '1' and flow_uq_code like '036%'  then 1 else 0 end) as j14,
            sum(case when f.flow_type_code = '1' and flow_uq_code like '037%'  then 1 else 0 end) as j15,
            sum(case when f.flow_type_code = '1' and flow_uq_code like '041%'  then 1 else 0 end) as j16,
            sum(case when f.flow_type_code = '1' and flow_uq_code like '042%'  then 1 else 0 end) as j17,
            sum(case when f.flow_type_code = '1' and flow_uq_code like '043%'  then 1 else 0 end) as j18,
            sum(case when f.flow_type_code = '1' and flow_uq_code like '044%'  then 1 else 0 end) as j19,
            sum(case when f.flow_type_code = '1' and flow_uq_code like '045%'  then 1 else 0 end) as j20,
            sum(case when f.flow_type_code = '1' and flow_uq_code like '046%'  then 1 else 0 end) as j21,
            sum(case when f.flow_type_code = '1' and flow_uq_code like '050%'  then 1 else 0 end) as j22,
            sum(case when f.flow_type_code = '1' and flow_uq_code like '051%'  then 1 else 0 end) as j23,
            sum(case when flow_type_code in ('2','3')  then 1 else 0 end) as j24,
            sum(case when f.flow_type_code = '1' and flow_uq_code like '053%'  then 1 else 0 end) as j25,
            sum(case when f.flow_type_code = '1' and flow_uq_code like '054%'  then 1 else 0 end) as j26,
            sum(case when f.flow_type_code = '1' and flow_uq_code like '061%'  then 1 else 0 end) as j27,
            sum(case when f.flow_type_code = '1' and flow_uq_code like '062%'  then 1 else 0 end) as j28,
            sum(case when f.flow_type_code = '1' and flow_uq_code like '063%'  then 1 else 0 end) as j29,
            sum(case when f.flow_type_code = '1' and flow_uq_code like '064%'  then 1 else 0 end) as j30,
            sum(case when f.flow_type_code = '1' and flow_uq_code like '065%'  then 1 else 0 end) as j31,
            sum(case when f.flow_type_code = '1' and flow_uq_code like '071%'  then 1 else 0 end) as j32,
            sum(case when f.flow_type_code = '1' and flow_uq_code like '081%'  then 1 else 0 end) as j33,
            sum(case when f.flow_type_code = '1' and flow_uq_code like '082%'  then 1 else 0 end) as j34,
            0 AS j35
        from
            (select
                 f.*
             from
                 mem_flow f
                     LEFT JOIN ccp_org g ON f.in_org_code = g.code
             where
                 out_place_code not in ('3', '4')
               and g.delete_time IS NULL
               and (
                 ((org_code like concat(#{dto.orgCode}, '%') and in_org_code is not null or
                   out_org_branch_org_code like concat(#{dto.orgCode}, '%') and in_org_code is null) and has_county_library != '1')
               or
                ((org_code like concat(#{dto.orgCode}, '%')
                and in_org_code is not null or out_org_branch_org_code like concat(#{dto.orgCode}, '%')
                and in_org_code is null)
                and has_county_library = '1')
                )
        union
        select
            f.*
        from
            mem_flow f
                LEFT JOIN ccp_org_flow g ON f.in_org_code = g.code
        where
            out_place_code not in ('3', '4')
          and g.delete_time IS NULL
          and (
            ((org_code like concat(#{dto.orgCode}, '%') and in_org_code is not null or
              out_org_branch_org_code like concat(#{dto.orgCode}, '%') and in_org_code is null) and has_county_library != '1')
           or
            ((org_code like concat(#{dto.orgCode}, '%')
          and in_org_code is not null or out_org_branch_org_code like concat(#{dto.orgCode}, '%')
          and in_org_code is null)
          and has_county_library = '1')
            )) f where f.flow_step in ('2');
    </select>

    <select id="flowReceive" resultType="java.util.Map">
        select
            sum(case when f.flow_type_code in ('2','3') and f.flow_step in ('1','2') then 1 else 0 end) as z1,
            sum(case when f.flow_type_code in ('2','3') and f.flow_step in ('2') then 1 else 0 end) as z2,
            sum(case when f.register_time >= '2024-11-01' and f.flow_type_code in ('2','3') and flow_step = '3' and reject_reason_name = '超期终止'  then 1 else 0 end) as z3,
            sum(case when f.register_time >= '2024-11-01' and f.flow_type_code in ('2','3') and flow_step = '3' and reject_reason_name != '超期终止'  then 1 else 0 end) as z4,
            sum(case when (f.flow_type_code in ('1') or f.out_org_branch_code in (select ccp_org_flow.code from ccp_org_flow where d200_code ='2' and is_enable ='1' and source_type = '1') ) and flow_step in ('1','2')  then 1 else 0 end) as z5,
            sum(case when f.flow_type_code in ('1') and f.flow_step in ('2') and out_place_code in ('1') then 1 else 0 end) as z6,
            sum(case when (f.flow_type_code in ('1') or f.out_org_branch_code in (select ccp_org_flow.code from ccp_org_flow where d200_code ='2' and is_enable ='1' and source_type = '1') ) and f.flow_step in ('2') and out_place_code in ('5') then 1 else 0 end) as z7,
            sum(case when f.register_time >= '2024-11-01' and (f.flow_type_code in ('1') or f.out_org_branch_code in (select ccp_org_flow.code from ccp_org_flow where d200_code ='2' and is_enable ='1' and source_type = '1') ) and flow_step = '3' and reject_reason_name = '超期终止' then 1 else 0 end) as z8,
            sum(case when f.register_time >= '2024-11-01' and (f.flow_type_code in ('1') or f.out_org_branch_code in (select ccp_org_flow.code from ccp_org_flow where d200_code ='2' and is_enable ='1' and source_type = '1') ) and flow_step = '3' and reject_reason_name != '超期终止' then 1 else 0 end) as z9,
            (select count(f.*) from
                (select
                     f.*
                 from
                     mem_flow f
                         LEFT JOIN ccp_org g ON f.in_org_code = g.code
                 where
                     out_place_code not in ('3', '4')
                   and g.delete_time IS NULL
                   and (org_code like concat(#{dto.orgCode}, '%')
                            and in_org_code is not null or out_org_branch_org_code like concat(#{dto.orgCode}, '%')
                            and in_org_code is null)
                   and has_county_library = '1'
                 union
                 select
                     f.*
                 from
                     mem_flow f
                         LEFT JOIN ccp_org_flow g ON f.in_org_code = g.code
                 where
                     out_place_code not in ('3', '4')
                   and g.delete_time IS NULL
                   and (org_code like concat(#{dto.orgCode}, '%')
                            and in_org_code is not null or out_org_branch_org_code like concat(#{dto.orgCode}, '%')
                            and in_org_code is null)
                   and has_county_library = '1') f where f.flow_step = '1') as z10
        from
            mem_flow f
                left join ccp_org g on
                    g.delete_time is null
                    and f.mem_org_org_code = g.org_code
                    and (g.is_dissolve is null
                    or g.is_dissolve != 1)
        where
            f.flow_out = '1' and mem_org_org_code like concat(#{dto.orgCode}, '%');
    </select>

    <select id="inquireFlowOutWarn" resultMap="BaseResultMap1">
        select
            f.*
        from mem_flow f
        where mem_org_org_code like concat(#{dto.orgCode}, '%') ${dto.strSql}
    </select>

    <select id="inquireFlowOutWarn1" resultType="com.zenith.front.model.bean.OrgFlow">
        select cof.*
        from ccp_org_flow cof
        left join ccp_org_flow_audit cofa on cof.code = cofa.flow_org_code
        where cof.d200_code = '2' and cofa.status = '0' and cof.delete_time is null and source_type = '1'
        <if test="dto.orgCode != dto.topOrgCode">
            and cof.org_code like concat(#{dto.orgCode},'%')
        </if>

    </select>


    <select id="inquireFlowInWarn" resultMap="BaseResultMap1">
        select
            f.*
        from
        (select
        f.*
        from mem_flow f
        LEFT JOIN ccp_org g ON f.in_org_code = g.code
        where
        out_place_code not in ('3', '4')
        and g.delete_time IS NULL
        and (
        ((org_code like concat(#{dto.orgCode}, '%') and in_org_code is not null or
        out_org_branch_org_code like concat(#{dto.orgCode}, '%') and in_org_code is null) and has_county_library != '1')
        or
        ((org_code like concat(#{dto.orgCode}, '%')
        and in_org_code is not null or out_org_branch_org_code like concat(#{dto.orgCode}, '%')
        and in_org_code is null)
        and has_county_library = '1')
        )
        UNION
        select
        f.*
        from mem_flow f
        LEFT JOIN ccp_org_flow cof ON f.in_org_code = cof.code
        where
        out_place_code not in ('3', '4')
        and cof.delete_time IS NULL
        and (
        ((org_code like concat(#{dto.orgCode}, '%') and in_org_code is not null or
        out_org_branch_org_code like concat(#{dto.orgCode}, '%') and in_org_code is null) and has_county_library != '1')
        or
        ((org_code like concat(#{dto.orgCode}, '%')
        and in_org_code is not null or out_org_branch_org_code like concat(#{dto.orgCode}, '%')
        and in_org_code is null)
        and has_county_library = '1')
        )) f  where 1=1
        <if test="dto.orgCode != dto.topOrgCode">
            and f.has_county_library != '1'
        </if>
        ${dto.strSql}
    </select>

    <select id="inquireFlowInWarn1" resultType="com.zenith.front.model.bean.OrgFlow">
        select cof.* from ccp_org_flow cof
        left join ccp_org_flow_audit cofa on cof.code = cofa.flow_org_code
        where cof.d200_code = '2' and cofa.status = '0' and cof.delete_time is null and source_type = '2'
        <if test="dto.orgCode != dto.topOrgCode">
            and cof.org_code like concat(#{dto.orgCode},'%')
        </if>
    </select>


    <select id="inquireFlowOutCurrent1" resultMap="BaseResultMap1">
        select
            f.*
        from mem_flow f
        where
            (f.flow_step = '1' and f.flow_out = '1' and f.flow_in = '0'
                or f.flow_step = '2' and f.flow_out = '1' and f.flow_in = '1')
          and f.mem_org_org_code like concat(#{dto.orgCode}, '%')
          ${dto.strSql}
    </select>

    <select id="inquireFlowOutCurrent2" resultMap="BaseResultMap1">
        select
            f.*
        from mem_flow f
        where
            out_place_code not in ('3', '4')
          and f.flow_step = '1'
          and mem_org_org_code like concat(#{dto.orgCode}, '%')
         ${dto.strSql}
    </select>

    <select id="inquireFlowOutCurrent3" resultMap="BaseResultMap1">
        select
            f.*
        from mem_flow f
        where
            f.flow_out = '1' and mem_org_org_code like concat(#{dto.orgCode}, '%')
            ${dto.strSql}
    </select>

    <select id="inquireFlowOutInterval" resultMap="BaseResultMap1">
        select
            f.*
        from
            mem_flow f
                left join ccp_org g on
                    g.delete_time is null
                    and f.mem_org_org_code = g.org_code
                    and (g.is_dissolve is null
                    or g.is_dissolve != 1)
        where
            f.flow_out = '1' and mem_org_org_code like concat(#{dto.orgCode}, '%')
        <if test="dto.startDate!=null and dto.endDate ==null">
            and f.register_time &gt;= #{dto.startDate}
        </if>
        <if test="dto.endDate!=null and dto.startDate ==null">
            and f.register_time &lt;= #{dto.endDate}
        </if>
        <if test="dto.startDate!=null and dto.endDate != null">
            and f.register_time BETWEEN #{dto.startDate} AND #{dto.endDate}
        </if>
        <if test="dto.startDate==null and dto.endDate == null">
            and f.register_time BETWEEN CURRENT_DATE - INTERVAL '10 days' AND CURRENT_DATE
        </if>
        ${dto.strSql}
    </select>

    <select id="inquireFlowInCurren" resultMap="BaseResultMap1">
        select
            f.*
        from
        (select
        f.*
        from
        mem_flow f
        LEFT JOIN ccp_org g ON f.in_org_code = g.code
        where
        out_place_code not in ('3', '4')
        and g.delete_time IS NULL
        and (
        ((org_code like concat(#{dto.orgCode}, '%') and in_org_code is not null or
        out_org_branch_org_code like concat(#{dto.orgCode}, '%') and in_org_code is null) and has_county_library != '1')
        or
        ((org_code like concat(#{dto.orgCode}, '%')
        and in_org_code is not null or out_org_branch_org_code like concat(#{dto.orgCode}, '%')
        and in_org_code is null)
        and has_county_library = '1')
        )
        union
        select
        f.*
        from
        mem_flow f
        LEFT JOIN ccp_org_flow g ON f.in_org_code = g.code
        where
        out_place_code not in ('3', '4')
        and g.delete_time IS NULL
        and (
        ((org_code like concat(#{dto.orgCode}, '%') and in_org_code is not null or
        out_org_branch_org_code like concat(#{dto.orgCode}, '%') and in_org_code is null) and has_county_library != '1')
        or
        ((org_code like concat(#{dto.orgCode}, '%')
        and in_org_code is not null or out_org_branch_org_code like concat(#{dto.orgCode}, '%')
        and in_org_code is null)
        and has_county_library = '1')
        )) f where 1=1
        <if test="dto.orgCode != dto.topOrgCode">
            and f.has_county_library != '1'
        </if>
        ${dto.strSql}
    </select>

    <select id="inquireFlowInCurrenFLow" resultMap="BaseResultMap1">
        select
            f.*
        from
            (select
                 f.*
             from
                 mem_flow f
                     LEFT JOIN ccp_org g ON f.in_org_code = g.code
             where
                 out_place_code not in ('3', '4')
               and g.delete_time IS NULL
               and (
                 ((org_code like concat(#{dto.orgCode}, '%') and in_org_code is not null or
                   out_org_branch_org_code like concat(#{dto.orgCode}, '%') and in_org_code is null) and has_county_library != '1')
            or
                ((org_code like concat(#{dto.orgCode}, '%')
            and in_org_code is not null or out_org_branch_org_code like concat(#{dto.orgCode}, '%')
            and in_org_code is null)
            and has_county_library = '1')
            )
        union
        select
            f.*
        from
            mem_flow f
                LEFT JOIN ccp_org_flow g ON f.in_org_code = g.code
        where
            out_place_code not in ('3', '4')
          and g.delete_time IS NULL
          and (
            ((org_code like concat(#{dto.orgCode}, '%') and in_org_code is not null or
              out_org_branch_org_code like concat(#{dto.orgCode}, '%') and in_org_code is null) and has_county_library != '1')
           or
            ((org_code like concat(#{dto.orgCode}, '%')
          and in_org_code is not null or out_org_branch_org_code like concat(#{dto.orgCode}, '%')
          and in_org_code is null)
          and has_county_library = '1')
            )) f where f.out_org_branch_code in (select ccp_org_flow.code from ccp_org_flow where d200_code ='2' and is_enable ='1' and source_type = '1')
          ${dto.strSql}
    </select>



    <select id="inquireFlowInInterval1" resultMap="BaseResultMap1">
        select
            f.*
        from
            (select
                 f.*
             from mem_flow f
                      LEFT JOIN ccp_org ON f.in_org_code = ccp_org.code	AND ccp_org.delete_time IS NULL
             where
                 ((ccp_org.org_code like concat(#{dto.orgCode}, '%') AND f."in_org_code" IS NOT NULL)
                     OR (f.out_org_branch_org_code like concat(#{dto.orgCode}, '%') AND f."in_org_code" IS NULL))
               AND f.out_place_code NOT IN ( '3', '4' )
             union
             select
                 f.*
             from mem_flow f
                 LEFT JOIN ccp_org_flow ON f.in_org_code = ccp_org_flow.code	AND ccp_org_flow.delete_time IS NULL
             where
                 ((ccp_org_flow.org_code like concat(#{dto.orgCode}, '%') AND f."in_org_code" IS NOT NULL)
                OR (f.out_org_branch_org_code like concat(#{dto.orgCode}, '%') AND f."in_org_code" IS NULL))
               AND f.out_place_code NOT IN ( '3', '4' )
               ) f where 1=1
                <if test="dto.startDate!=null and dto.endDate ==null">
                    and f.register_time &gt;= #{dto.startDate}
                </if>
                <if test="dto.endDate!=null and dto.startDate ==null">
                    and f.register_time &lt;= #{dto.endDate}
                </if>
                <if test="dto.startDate!=null and dto.endDate != null">
                    and f.register_time BETWEEN #{dto.startDate} AND #{dto.endDate}
                </if>
                <if test="dto.startDate==null and dto.endDate == null">
                    and f.register_time BETWEEN CURRENT_DATE - INTERVAL '10 days' AND CURRENT_DATE
                </if>
               ${dto.strSql}
    </select>

    <select id="inquireFlowInInterval2" resultMap="BaseResultMap1">
        select
            f.*
        from mem_flow f
                 LEFT JOIN ccp_org_flow ON f.in_org_code = ccp_org_flow.code	AND ccp_org_flow.delete_time IS NULL
        where
            ((ccp_org_flow.org_code like concat(#{dto.orgCode}, '%') AND f."in_org_code" IS NOT NULL)
                OR (f.out_org_branch_org_code like concat(#{dto.orgCode}, '%') AND f."in_org_code" IS NULL))
          AND f.out_place_code NOT IN ( '3', '4' )
            <if test="dto.startDate!=null and dto.endDate ==null">
                and f.register_time &gt;= #{dto.startDate}
            </if>
            <if test="dto.endDate!=null and dto.startDate ==null">
                and f.register_time &lt;= #{dto.endDate}
            </if>
            <if test="dto.startDate!=null and dto.endDate != null">
                and f.register_time BETWEEN #{dto.startDate} AND #{dto.endDate}
            </if>
            <if test="dto.startDate==null and dto.endDate == null">
                and f.register_time BETWEEN CURRENT_DATE - INTERVAL '10 days' AND CURRENT_DATE
            </if>
            ${dto.strSql}
    </select>


    <select id="inquireFlowOutGo1" resultMap="BaseResultMap1">

        select
            f.*
        from mem_flow f
                 LEFT JOIN ccp_org g ON f.in_org_code = g.code
        where
            (((f.flow_step = '1' and f.flow_out = '1' and f.flow_in = '0') OR (f.flow_step = '2' and f.flow_out = '1' and f.flow_in = '1')) and mem_org_org_code like concat(#{dto.orgCode}, '%'))
          AND f.out_place_code NOT IN ( '3', '4' )
            ${dto.strSql}
    </select>

    <select id="inquireFlowOutGo2" resultMap="BaseResultMap1">
        select
            f.*
        from
            mem_flow f
        where
            f.flow_out = '1' and mem_org_org_code like concat(#{dto.orgCode}, '%')
          AND f.out_place_code IN ( '3', '4' ) and flow_step in ('1','2')
            ${dto.strSql}
    </select>

    <select id="inquireFlowOutGoNanotube" resultMap="BaseResultMap1">
        select
            f.*
        from mem_flow f
        where
            (((f.flow_step = '1' and f.flow_out = '1' and f.flow_in = '0') OR (f.flow_step = '2' and f.flow_out = '1' and f.flow_in = '1')) and mem_org_org_code like concat(#{dto.orgCode}, '%'))
            and (f.flow_step = '2' and f.flow_out = '1' and f.flow_in = '1') ${dto.strSql}
    </select>

    <select id="inquireFlowInGo" resultMap="BaseResultMap1">
        select
            f.*
        from
            (select
                 f.*
             from
                 mem_flow f
                     LEFT JOIN ccp_org g ON f.in_org_code = g.code
             where
                 out_place_code not in ('3', '4')
               and g.delete_time IS NULL
               and (
                 ((org_code like concat(#{dto.orgCode}, '%') and in_org_code is not null or
                   out_org_branch_org_code like concat(#{dto.orgCode}, '%') and in_org_code is null) and has_county_library != '1')
            or
                ((org_code like concat(#{dto.orgCode}, '%')
            and in_org_code is not null or out_org_branch_org_code like concat(#{dto.orgCode}, '%')
            and in_org_code is null)
            and has_county_library = '1')
            )
        union
        select
            f.*
        from
            mem_flow f
                LEFT JOIN ccp_org_flow g ON f.in_org_code = g.code
        where
            out_place_code not in ('3', '4')
          and g.delete_time IS NULL
          and (
            ((org_code like concat(#{dto.orgCode}, '%') and in_org_code is not null or
              out_org_branch_org_code like concat(#{dto.orgCode}, '%') and in_org_code is null) and has_county_library != '1')
           or
            ((org_code like concat(#{dto.orgCode}, '%')
          and in_org_code is not null or out_org_branch_org_code like concat(#{dto.orgCode}, '%')
          and in_org_code is null)
          and has_county_library = '1')
            )) f where f.flow_step in ('1','2') ${dto.strSql}
    </select>

    <select id="inquireFlowInGoNanotube" resultMap="BaseResultMap1">
        select
            f.*
        from
            (select
                 f.*
             from
                 mem_flow f
                     LEFT JOIN ccp_org g ON f.in_org_code = g.code
             where
                 out_place_code not in ('3', '4')
               and g.delete_time IS NULL
               and (
                 ((org_code like concat(#{dto.orgCode}, '%') and in_org_code is not null or
                   out_org_branch_org_code like concat(#{dto.orgCode}, '%') and in_org_code is null) and has_county_library != '1')
            or
                ((org_code like concat(#{dto.orgCode}, '%')
            and in_org_code is not null or out_org_branch_org_code like concat(#{dto.orgCode}, '%')
            and in_org_code is null)
            and has_county_library = '1')
            )
        union
        select
            f.*
        from
            mem_flow f
                LEFT JOIN ccp_org_flow g ON f.in_org_code = g.code
        where
            out_place_code not in ('3', '4')
          and g.delete_time IS NULL
          and (
            ((org_code like concat(#{dto.orgCode}, '%') and in_org_code is not null or
              out_org_branch_org_code like concat(#{dto.orgCode}, '%') and in_org_code is null) and has_county_library != '1')
           or
            ((org_code like concat(#{dto.orgCode}, '%')
          and in_org_code is not null or out_org_branch_org_code like concat(#{dto.orgCode}, '%')
          and in_org_code is null)
          and has_county_library = '1')
            )) f where f.flow_step in ('2') ${dto.strSql}
    </select>


    <select id="inquireFlowReceive1" resultMap="BaseResultMap1">
        select
            f.*
        from
            mem_flow f
                left join ccp_org g on
                    g.delete_time is null
                    and f.mem_org_org_code = g.org_code
                    and (g.is_dissolve is null
                    or g.is_dissolve != 1)
        where
            f.flow_out = '1' and mem_org_org_code like concat(#{dto.orgCode}, '%') ${dto.strSql}
    </select>

    <select id="inquireFlowReceive2" resultMap="BaseResultMap1">
        select f.*
        from
            (select
                 f.*
             from
                 mem_flow f
                     LEFT JOIN ccp_org g ON f.in_org_code = g.code
             where
                 out_place_code not in ('3', '4')
               and g.delete_time IS NULL
               and (org_code like concat(#{dto.orgCode}, '%')
                        and in_org_code is not null or out_org_branch_org_code like concat(#{dto.orgCode}, '%')
                        and in_org_code is null)
               and has_county_library = '1'
             union
             select
                 f.*
             from
                 mem_flow f
                     LEFT JOIN ccp_org_flow g ON f.in_org_code = g.code
             where
                 out_place_code not in ('3', '4')
               and g.delete_time IS NULL
               and (org_code like concat(#{dto.orgCode}, '%')
                        and in_org_code is not null or out_org_branch_org_code like concat(#{dto.orgCode}, '%')
                        and in_org_code is null)
               and has_county_library = '1') f where f.flow_step = '1'
    </select>


</mapper>
