package com.zenith.front.dao.mapper.message;

import com.zenith.front.model.bean.MqMessages;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;

/**
 * <p>
 * Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2022-07-26
 */
public interface MqMessagesMapper extends BaseMapper<MqMessages> {

    /**
     * 根据type删除消息
     *
     * @param type 消息类型
     */
    void deleteByType(@Param("type") String type);
}
