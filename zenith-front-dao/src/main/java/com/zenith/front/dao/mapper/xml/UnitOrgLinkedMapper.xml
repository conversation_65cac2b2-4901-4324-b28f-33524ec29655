<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zenith.front.dao.mapper.unit.UnitOrgLinkedMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.zenith.front.model.bean.UnitOrgLinked">
        <id column="id" property="id" />
        <result column="code" property="code" />
        <result column="es_id" property="esId" />
        <result column="org_code" property="orgCode" />
        <result column="org_name" property="orgName" />
        <result column="unit_code" property="unitCode" />
        <result column="unit_name" property="unitName" />
        <result column="timestamp" property="timestamp" />
        <result column="update_time" property="updateTime" />
        <result column="delete_time" property="deleteTime" />
        <result column="create_time" property="createTime" />
        <result column="org_type" property="orgType" />
        <result column="org_type_name" property="orgTypeName" />
        <result column="unit_type" property="unitType" />
        <result column="unit_type_name" property="unitTypeName" />
        <result column="linked_org_code" property="linkedOrgCode" />
        <result column="is_org_main" property="isOrgMain" />
        <result column="is_unit_main" property="isUnitMain" />
        <result column="org_type_code" property="orgTypeCode" />
        <result column="zb_code" property="zbCode" />
        <result column="is_history" property="isHistory" />
        <result column="update_account" property="updateAccount" />
    </resultMap>
    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, code, es_id, org_code, org_name, unit_code, unit_name, timestamp, update_time, delete_time, create_time, org_type, org_type_name, unit_type, unit_type_name, linked_org_code, is_org_main, is_unit_main, org_type_code, zb_code, is_history, update_account
    </sql>
</mapper>
