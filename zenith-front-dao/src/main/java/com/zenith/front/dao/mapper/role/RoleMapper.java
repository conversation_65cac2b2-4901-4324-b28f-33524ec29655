package com.zenith.front.dao.mapper.role;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.zenith.front.model.bean.Role;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2021-07-10
 */
public interface RoleMapper extends BaseMapper<Role> {

    Role findSubRoleByIdAndParentId(@Param(value = "sqlStr") String sqlStr, @Param("roleId") String roleId);

    Role findSubRoleByIdAndParentIdIncludeInvalidRole(@Param("treeSql") String treeSql, @Param("roleId") String roleId);

    List<Role> findListByParentId(@Param("treeSql") String treeSql, @Param("roleId") String roleId);

    List<Role> getRoleTree(@Param("treeSql") String treeSql, @Param("roleId") String roleId);

    Page<Role> getList(@Param("page") Page<Role> page, @Param("treeSql") String treeSql, @Param("orgCode") String orgCode, @Param("keyword") String keyword);

    Page<Role> getListAndValid(@Param("page") Page<Role> page, @Param("treeSql") String treeSql, @Param("roleTypeCode") Integer roleTypeCode, @Param("isDelete") Integer isDelete);

    int updateRoleOrgCode(@Param("newOrgCode") String newOrgCode);

    int DelById(@Param("roleId") String roleId);
}
