package com.zenith.front.dao.mapper.fee;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.zenith.front.model.bean.Fee;
import com.zenith.front.model.vo.FeeCountListVO;
import com.zenith.front.model.vo.FeePayVO;
import com.zenith.front.model.vo.FeeRatioVO;
import com.zenith.front.model.vo.WjnListVO;
import org.apache.ibatis.annotations.Param;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * <p>
 *  Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2021-07-10
 */
public interface FeeMapper extends BaseMapper<Fee> {

    List<FeeCountListVO> countFee(@Param("orgCode") String orgCode, @Param("parse") Date parse, @Param("zero") BigDecimal zero, @Param("year") String year, @Param("month") String month);

    Page<WjnListVO> getFeeMemList(Page<WjnListVO> page,@Param("orgCode") String orgCode,@Param("year") String year, @Param("month") String month);

    List<Fee> getPayList(@Param("memCode") String memCode,@Param("lastPayDateLast") java.sql.Date lastPayDateLast,@Param("deadlineLast") java.sql.Date deadlineLast);

    Integer getOrgNotSetFeeMem(@Param("orgCode") String orgCode,@Param("year") String year,@Param("month") String month);

    Long getFeePayCount(@Param("orgCode") String orgCode,@Param("year") String year,@Param("month") String month,@Param("value") String value);

    BigDecimal getOrgFeeTotalByMonth(@Param("orgCode") String orgCode,@Param("year") String year,@Param("month") String month);

    BigDecimal getOrgFeePayTotalByMonth(@Param("orgCode") String orgCode,@Param("year") String year,@Param("month") String month);

    BigDecimal getFeeDisburseCount(@Param("orgCode") String orgCode,@Param("value") String value);

    List<FeePayVO> getOrgFeePayGroupByMonth(@Param("orgCode") String orgCode,@Param("year") String year);

    List<FeePayVO> getOrgFeeDisburseGroupByMonth(@Param("orgCode") String orgCode);

    List<FeePayVO> getOrgFeeDisburseGroupByType(@Param("orgCode") String orgCode);

    List<FeePayVO> getOrgFeeDisburseGroupByInput(@Param("orgCode") String orgCode);

    List<FeePayVO> getOrgFeePayGroupByType(@Param("orgCode") String orgCode);

    BigDecimal getOrgFeeAllocate(@Param("orgCode") String orgCode);

    BigDecimal getFeePay(@Param("orgCode") String orgCode,@Param("year") String year,@Param("month") String month,@Param("value") String value);

    List<FeePayVO> getFeeAllocateOrg(@Param("orgCode") String orgCode);

    List<FeePayVO> getFeeAllocateGroupByMonth(@Param("orgCode") String orgCode);

    FeeRatioVO getOrgFeeRatio(@Param("orgCode") String orgCode,@Param("toSqlDate") Date toSqlDate);

    List<Map<String,Object>> getTotalList(@Param("sql") String sql);

}
