package com.zenith.front.dao.mapper.develop;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.zenith.front.model.bean.DevelopPlan;
import org.apache.ibatis.annotations.MapKey;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
 * <p>
 * Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2021-07-10
 */
public interface DevelopPlanMapper extends BaseMapper<DevelopPlan> {
    /**
     * 获取指标数列表
     *
     * @param page
     * @param memOrgCode
     * @param hasSubordinate 是否显示下级
     * @return
     */
    @MapKey("code")
    Page<Map<String, Object>> getIndexList(@Param("page") Page page, @Param("memOrgCode") String memOrgCode, @Param("hasSubordinate") Boolean hasSubordinate);

    /**
     * 查找下级分配指标
     *
     * @param memOrgCode
     * @return
     */
    Long findSubIndexCount(@Param("memOrgCode") String memOrgCode);

    /**
     * 根据机构层级码查询
     *
     * @param orgCode 机构层级码
     * @return
     */
    List<DevelopPlan> selectListByOrgCode(@Param("orgCode") String orgCode);
}
