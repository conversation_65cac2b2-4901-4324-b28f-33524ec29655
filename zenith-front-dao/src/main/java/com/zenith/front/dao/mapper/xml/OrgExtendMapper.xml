<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zenith.front.dao.mapper.org.OrgExtendMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.zenith.front.model.bean.OrgExtend">
        <id column="id" property="id" />
        <result column="code" property="code" />
        <result column="zb_code" property="zbCode" />
        <result column="org_code" property="orgCode" />
        <result column="appraisal_situation" property="appraisalSituation" />
        <result column="join_review_mems" property="joinReviewMems" />
        <result column="end_review_mems" property="endReviewMems" />
        <result column="recognition_mems" property="recognitionMems" />
        <result column="evaluate_unqualified_mems" property="evaluateUnqualifiedMems" />
        <result column="deadline_correct_mems" property="deadlineCorrectMems" />
        <result column="stop_mems" property="stopMems" />
        <result column="persuade_dismiss_mems" property="persuadeDismissMems" />
        <result column="remove_yourself_mems" property="removeYourselfMems" />
        <result column="violation_discipline_mems" property="violationDisciplineMems" />
        <result column="disciplinary_outside_mems" property="disciplinaryOutsideMems" />
        <result column="persuade_persuade_mems" property="persuadePersuadeMems" />
        <result column="train_mem_total" property="trainMemTotal" />
        <result column="basic_secretary_train" property="basicSecretaryTrain" />
        <result column="non_public_society_organ_train_mem" property="nonPublicSocietyOrganTrainMem" />
        <result column="train_new_mem" property="trainNewMem" />
        <result column="train_mem_flow_" property="trainMemFlow" />
        <result column="train_frontier_basic_mem" property="trainFrontierBasicMem" />
        <result column="train_skill_mem" property="trainSkillMem" />
        <result column="country_secretary_train_" property="countrySecretaryTrain" />
        <result column="worker_train_" property="workerTrain" />
        <result column="state_owned_secretary_train" property="stateOwnedSecretaryTrain" />
        <result column="non_public_company_secretary_train" property="nonPublicCompanySecretaryTrain" />
        <result column="train_mem_standard_basic_organ" property="trainMemStandardBasicOrgan" />
        <result column="train_secretary_standard_basic_organ" property="trainSecretaryStandardBasicOrgan" />
        <result column="train_mem_towns" property="trainMemTowns" />
        <result column="mem_edu_site_num" property="memEduSiteNum" />
        <result column="town_edu_site_num" property="townEduSiteNum" />
        <result column="community_edu_site_num" property="communityEduSiteNum" />
        <result column="village_edu_site_num" property="villageEduSiteNum" />
        <result column="internet_edu_site_num" property="internetEduSiteNum" />
        <result column="wired_edu_site_num" property="wiredEduSiteNum" />
        <result column="satellite_edu_site_num" property="satelliteEduSiteNum" />
        <result column="edu_site_manage_num" property="eduSiteManageNum" />
        <result column="town_edu_site_manage_num" property="townEduSiteManageNum" />
        <result column="village_edu_site_manage_num" property="villageEduSiteManageNum" />
        <result column="edu_site_volunteer_num" property="eduSiteVolunteerNum" />
        <result column="village_long_edu_mem_num" property="villageLongEduMemNum" />
        <result column="d73_code" property="d73Code" />
        <result column="d73_name" property="d73Name" />
        <result column="current_year_award_mem_num" property="currentYearAwardMemNum" />
        <result column="earthquake_relief_award_mem_num" property="earthquakeReliefAwardMemNum" />
        <result column="has_accept_flow_mem" property="hasAcceptFlowMem" />
        <result column="not_relation_mem_build" property="notRelationMemBuild" />
        <result column="out_party_abroad_mem" property="outPartyAbroadMem" />
        <result column="out_party_back_home_mem" property="outPartyBackHomeMem" />
        <result column="current_year_end_tenure" property="currentYearEndTenure" />
        <result column="current_year_change_term" property="currentYearChangeTerm" />
        <result column="last_year_mem_total" property="lastYearMemTotal" />
        <result column="current_year_join_num" property="currentYearJoinNum" />
        <result column="current_year_reconvert_num" property="currentYearReconvertNum" />
        <result column="stop_current_year_reconvert_num" property="stopCurrentYearReconvertNum" />
        <result column="current_year_into_relation_num" property="currentYearIntoRelationNum" />
        <result column="current_year_fixed_into_num" property="currentYearFixedIntoNum" />
        <result column="current_year_fixed_out_num" property="currentYearFixedOutNum" />
        <result column="current_year_out_relation_num" property="currentYearOutRelationNum" />
        <result column="current_year_die_mem_num" property="currentYearDieMemNum" />
        <result column="current_year_out_relation_letter_num" property="currentYearOutRelationLetterNum" />
        <result column="provincial_approval_mem_num" property="provincialApprovalMemNum" />
        <result column="elected_by_direct" property="electedByDirect" />
        <result column="deputy_secretary_elect" property="deputySecretaryElect" />
        <result column="appraise_basic_leader_team" property="appraiseBasicLeaderTeam" />
        <result column="attend_meet_basic_organ_num" property="attendMeetBasicOrganNum" />
        <result column="join_audit_mem_num" property="joinAuditMemNum" />
        <result column="create_time" property="createTime" />
        <result column="update_time" property="updateTime" />
        <result column="delete_time" property="deleteTime" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, code, zb_code, org_code, appraisal_situation, join_review_mems, end_review_mems, recognition_mems, evaluate_unqualified_mems, deadline_correct_mems, stop_mems, persuade_dismiss_mems, remove_yourself_mems, violation_discipline_mems, disciplinary_outside_mems, persuade_persuade_mems, train_mem_total, basic_secretary_train, non_public_society_organ_train_mem, train_new_mem, train_mem_flow_, train_frontier_basic_mem, train_skill_mem, country_secretary_train_, worker_train_, state_owned_secretary_train, non_public_company_secretary_train, train_mem_standard_basic_organ, train_secretary_standard_basic_organ, train_mem_towns, mem_edu_site_num, town_edu_site_num, community_edu_site_num, village_edu_site_num, internet_edu_site_num, wired_edu_site_num, satellite_edu_site_num, edu_site_manage_num, town_edu_site_manage_num, village_edu_site_manage_num, edu_site_volunteer_num, village_long_edu_mem_num, d73_code, d73_name, current_year_award_mem_num, earthquake_relief_award_mem_num, has_accept_flow_mem, not_relation_mem_build, out_party_abroad_mem, out_party_back_home_mem, current_year_end_tenure, current_year_change_term, last_year_mem_total, current_year_join_num, current_year_reconvert_num, stop_current_year_reconvert_num, current_year_into_relation_num, current_year_fixed_into_num, current_year_fixed_out_num, current_year_out_relation_num, current_year_die_mem_num, current_year_out_relation_letter_num, provincial_approval_mem_num, elected_by_direct, deputy_secretary_elect, appraise_basic_leader_team, attend_meet_basic_organ_num, join_audit_mem_num, create_time, update_time, delete_time
    </sql>

</mapper>
