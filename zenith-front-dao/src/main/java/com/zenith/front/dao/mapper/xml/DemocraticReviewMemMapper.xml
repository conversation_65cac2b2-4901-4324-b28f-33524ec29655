<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zenith.front.dao.mapper.democraticreview.DemocraticReviewMemMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.zenith.front.model.bean.DemocraticReviewMem">
        <id column="id" property="id" />
        <result column="code" property="code" />
        <result column="review_mem_org_code" property="reviewMemOrgCode" />
        <result column="org_code" property="orgCode" />
        <result column="review_mem_code" property="reviewMemCode" />
        <result column="target_mem_code" property="targetMemCode" />
        <result column="year" property="year" />
        <result column="d69_code" property="d69Code" />
        <result column="d69_name" property="d69Name" />
        <result column="reason" property="reason" />
        <result column="create_time" property="createTime" />
        <result column="update_time" property="updateTime" />
        <result column="delete_time" property="deleteTime" />
        <result column="review_code" property="reviewCode" />
        <result column="target_sex_code" property="targetSexCode" />
        <result column="target_d08_code" property="targetD08Code" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, code, review_mem_org_code, org_code, review_mem_code, target_mem_code, year, d69_code, d69_name, reason, create_time, update_time, delete_time, review_code, target_sex_code, target_d08_code
    </sql>

</mapper>
