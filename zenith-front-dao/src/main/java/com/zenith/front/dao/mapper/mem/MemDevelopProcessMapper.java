package com.zenith.front.dao.mapper.mem;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.zenith.front.model.bean.MemDevelopProcess;
import com.zenith.front.model.dto.MemListDTO;
import com.zenith.front.model.vo.MemAuditListVo;
import org.apache.ibatis.annotations.Param;

import java.util.Collection;

/**
 * <p>
 * 党员（发展党员）流程信息表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025-02-14
 */
public interface MemDevelopProcessMapper extends BaseMapper<MemDevelopProcess> {

    Integer myInserBatch(Collection<MemDevelopProcess> entityList);

    /**
     * 获取预备党员审核列表
     *
     * @param page 分页参数
     * @param dto  请求参数
     * @return 查询结果
     */
    Page<MemAuditListVo> getMemDevelopAuditList(@Param("page") Page<MemAuditListVo> page, @Param("dto") MemListDTO dto);

    /**
     * 获取预备党员转正审核列表
     *
     * @param page 分页参数
     * @param dto  请求参数
     * @return 查询结果
     */
    Page<MemAuditListVo> getMemAuditList(@Param("page") Page<MemAuditListVo> page, @Param("dto") MemListDTO dto);

    /**
     * 查询上级是否从满足转正跟满足考察过来的
     * @param digitalLotNo
     * @return
     */
    int parentYbqProcess(String digitalLotNo);

    /**
     * 查询上级是否从待第一次考察、超半月过来的
     * @param digitalLotNo
     * @return
     */
    int parentJjProcess(String digitalLotNo);

    /**
     * 获取最新流程的上一个节点
     * @param digitalLotNo
     * @return
     */
    String previousProcessNode(String digitalLotNo);
}
