<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zenith.front.dao.mapper.role.RoleMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.zenith.front.model.bean.Role">
        <id column="id" property="id"/>
        <result column="name" property="name"/>
        <result column="permission" property="permission"/>
        <result column="valid_time" property="validTime"/>
        <result column="create_time" property="createTime"/>
        <result column="update_time" property="updateTime"/>
        <result column="parent_id" property="parentId"/>
        <result column="create_account" property="createAccount"/>
        <result column="role_type_code" property="roleTypeCode"/>
        <result column="update_account" property="updateAccount"/>
        <result column="is_delete" property="isDelete"/>
        <result column="org_code" property="orgCode"/>
        <result column="org_id" property="orgId"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, name, permission, valid_time, create_time, update_time, parent_id, create_account, role_type_code, update_account, is_delete, org_code, org_id
    </sql>

    <update id="updateRoleOrgCode">
        UPDATE "sys_role"
        SET "org_code" = "ccp_org"."org_code"
            FROM
	    "ccp_org"
        WHERE
            ( "ccp_org"."org_code" LIKE concat(#{newOrgCode},'%') AND "sys_role"."org_id" = "ccp_org"."code" )
    </update>

    <delete id="DelById">
        DELETE FROM sys_role
        WHERE id=#{roleId};
    </delete>

    <select id="findSubRoleByIdAndParentId" resultType="com.zenith.front.model.bean.Role">
        ${sqlStr}
        SELECT
            *
        FROM
            "role_tree"
        WHERE
            ( "role_tree"."id" = #{roleId} AND ( "role_tree"."valid_time" > CURRENT_TIMESTAMP OR "role_tree"."valid_time" IS NULL ) AND "role_tree"."is_delete" = 0 )
        LIMIT 1
    </select>
    <select id="findSubRoleByIdAndParentIdIncludeInvalidRole" resultType="com.zenith.front.model.bean.Role">
        ${treeSql}
        SELECT
            *
        FROM
            "role_tree"
        WHERE
            "role_tree"."id" = #{roleId} AND "role_tree"."is_delete"=0
    </select>
    <select id="findListByParentId" resultType="com.zenith.front.model.bean.Role">
        ${treeSql}
        SELECT
            *
        FROM
            "role_tree"
        WHERE
            "role_tree"."id" != #{roleId} AND ("role_tree"."valid_time" > CURRENT_TIMESTAMP OR "role_tree"."valid_time" IS NULL) AND "role_tree"."is_delete"=0
    </select>
    <select id="getRoleTree" resultType="com.zenith.front.model.bean.Role">
        ${treeSql}
        SELECT
            *
        FROM
            "role_tree"
        WHERE
            ("role_tree"."valid_time" > CURRENT_TIMESTAMP OR "role_tree"."valid_time" IS NULL) AND "role_tree"."is_delete"=0
    </select>
    <select id="getList" resultType="com.zenith.front.model.bean.Role">
        ${treeSql}
        SELECT * FROM "role_tree"
        WHERE "role_tree"."role_type_code" <![CDATA[ <> ]]> 1 AND "is_delete"=0
        <if test="orgCode!=null and orgCode!=''">
            AND "role_tree"."org_code" like concat(#{orgCode},'%')
        </if>
        <if test="keyword!=null and keyword!=''">
            AND CAST ( "role_tree"."name" AS VARCHAR ) like concat('%',#{keyword},'%')
        </if>
        ORDER BY "role_tree"."create_time"

    </select>
    <select id="getListAndValid" resultType="com.zenith.front.model.bean.Role">
        ${treeSql}
        SELECT * FROM "role_tree"
        WHERE "role_tree"."role_type_code" <![CDATA[ <> ]]>  #{roleTypeCode}
        AND ("role_tree"."valid_time" > CURRENT_TIMESTAMP OR "role_tree"."valid_time" IS NULL) AND "role_tree"."is_delete"=#{isDelete}
    </select>

</mapper>
