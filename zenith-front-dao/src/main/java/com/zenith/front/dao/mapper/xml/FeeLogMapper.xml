<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zenith.front.dao.mapper.fee.FeeLogMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.zenith.front.model.bean.FeeLog">
        <id column="id" property="id" />
        <result column="code" property="code" />
        <result column="remark" property="remark" />
        <result column="fee_code" property="feeCode" />
        <result column="creator_account" property="creatorAccount" />
        <result column="detail" property="detail" />
        <result column="mem_code" property="memCode" />
        <result column="mem_org_code" property="memOrgCode" />
        <result column="mem_org_org_code" property="memOrgOrgCode" />
        <result column="money" property="money" />
        <result column="base" property="base" />
        <result column="stand" property="stand" />
        <result column="reason" property="reason" />
        <result column="d49_code" property="d49Code" />
        <result column="d49_name" property="d49Name" />
        <result column="create_time" property="createTime" />
        <result column="update_time" property="updateTime" />
        <result column="delete_time" property="deleteTime" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, code, remark, fee_code, creator_account, detail, mem_code, mem_org_code, mem_org_org_code, money, base, stand, reason, d49_code, d49_name, create_time, update_time, delete_time
    </sql>

</mapper>
