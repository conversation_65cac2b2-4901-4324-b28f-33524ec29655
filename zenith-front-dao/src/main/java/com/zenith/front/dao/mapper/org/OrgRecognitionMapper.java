package com.zenith.front.dao.mapper.org;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.zenith.front.model.bean.OrgRecognition;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.zenith.front.model.vo.OrgRecognitionVO;
import org.apache.ibatis.annotations.Param;

/**
 * <p>
 * 表彰 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2021-09-09
 */
public interface OrgRecognitionMapper extends BaseMapper<OrgRecognition> {

    Page<OrgRecognitionVO> getPage(Page<OrgRecognitionVO> page,@Param("orgCode") String orgCode);

    Page<OrgRecognition> getPageByAnnual(Page<OrgRecognition> page,@Param("year") String year,@Param("orgCode") String orgCode);

    int saveBatchTmp(OrgRecognition orgCommitteeElect);
}
