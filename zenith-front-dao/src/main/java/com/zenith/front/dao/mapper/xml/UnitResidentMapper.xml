<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zenith.front.dao.mapper.unit.UnitResidentMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.zenith.front.model.bean.UnitResident">
        <id column="id" property="id"/>
        <result column="code" property="code"/>
        <result column="es_id" property="esId"/>
        <result column="elect_code" property="electCode"/>
        <result column="mem_code" property="memCode"/>
        <result column="mem_name" property="memName"
                typeHandler="com.zenith.front.common.encrypt.typehandler.EncryptTypeHandler"/>
        <result column="d139_code" property="d139Code"/>
        <result column="d139_name" property="d139Name"/>
        <result column="d140_code" property="d140Code"/>
        <result column="d140_name" property="d140Name"/>
        <result column="d141_code" property="d141Code"/>
        <result column="d141_name" property="d141Name"/>
        <result column="sex_code" property="sexCode"/>
        <result column="sex_name" property="sexName"/>
        <result column="mem_idcard" property="memIdcard"
                typeHandler="com.zenith.front.common.encrypt.typehandler.EncryptTypeHandler"/>
        <result column="mem_birthday" property="memBirthday"/>
        <result column="d07_code" property="d07Code"/>
        <result column="d07_name" property="d07Name"/>
        <result column="start_date" property="startDate"/>
        <result column="end_date" property="endDate"/>
        <result column="has_village_transfer_student" property="hasVillageTransferStudent"/>
        <result column="remark" property="remark"/>
        <result column="create_time" property="createTime"/>
        <result column="update_time" property="updateTime"/>
        <result column="delete_time" property="deleteTime"/>
        <result column="timestamp" property="timestamp"/>
        <result column="is_history" property="isHistory"/>
        <result column="update_account" property="updateAccount"/>
        <result column="unit_code" property="unitCode"/>
        <result column="resident_date" property="residentDate"/>
        <result column="dispatch_position" property="dispatchPosition"/>
        <result column="d144_code" property="d144Code"/>
        <result column="d144_name" property="d144Name"/>
    </resultMap>


    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id , code, es_id, elect_code, mem_code, mem_name, d139_code, d139_name, d140_code, d140_name, d141_code, d141_name, sex_code, sex_name, mem_idcard, mem_birthday, d07_code, d07_name, start_date, end_date, has_village_transfer_student, remark, create_time, update_time, delete_time, timestamp, is_history, update_account, unit_code, resident_date, dispatch_position, d144_code, d144_name
    </sql>

    <select id="findVillageCadresList" resultType="com.zenith.front.model.vo.DecryptUnitResidentMap">
        SELECT ccp_unit_resident.*,
               u.main_unit_org_code,
               u.main_org_code,
               u.name           AS "unit_name",
               m.sex_code       AS "mem_sex_code",
               m.idcard,
               m.birthday,
               m.d07_code       AS "mem_d07_code",
               m.d06_code       AS "d06_code",
               m.d48_code       AS "d48_code",
               m.join_org_date  AS "join_org_date",
               m.join_work_date AS "join_work_date"
        FROM ccp_unit_resident
                 LEFT JOIN ccp_unit u ON u.code = ccp_unit_resident.unit_code
                 LEFT JOIN ccp_mem m ON m.code = ccp_unit_resident.mem_code
        WHERE ccp_unit_resident.delete_time is null
        ORDER BY ccp_unit_resident.create_time,
                 ccp_unit_resident.id
    </select>

</mapper>
