<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zenith.front.dao.mapper.dict.DictChartMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.zenith.front.model.bean.DictChart">
        <id column="id" property="id" />
        <result column="key" property="key" />
        <result column="name" property="name" />
        <result column="pinyin" property="pinyin" />
        <result column="method_name" property="methodName" />
        <result column="parent" property="parent" />
        <result column="is_leaf" property="isLeaf" />
        <result column="sort" property="sort" />
        <result column="type" property="type" />
        <result column="para_name" property="paraName" />
        <result column="default" property="default" />
        <result column="is_enable" property="isEnable" />
        <result column="remark" property="remark" />
        <result column="disabled" property="disabled" />
        <result column="enabled" property="enabled" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, key, name, pinyin, method_name, parent, is_leaf, sort, type, para_name, default, is_enable, remark, disabled, enabled
    </sql>

    <select id="findDdbJobByOrgCode" resultType="java.lang.Long">
        SELECT count(1)
        from ccp_org_party_congress_committee t1
                 INNER JOIN ccp_org_party_congress_elect t2 ON t1.elect_code = t2.code and t2.delete_time is null
        where t1.delete_time is null
          AND t1.end_date is null
          AND t2.tenure_start_date &lt;= CURRENT_DATE
          AND t2.tenure_end_date &gt;= CURRENT_DATE
          and t2.elect_org_code LIKE CONCAT(#{orgCode}, '%')
    </select>

    <select id="getPartyRepresentative" resultType="com.zenith.front.model.vo.OrgPartyCongressCommitteeVO">
        SELECT t1.*
        from ccp_org_party_congress_committee t1
                 INNER JOIN ccp_org_party_congress_elect t2 ON t1.elect_code = t2.code and t2.delete_time is null
        where t1.delete_time is null
          AND t1.end_date is null
          AND t2.tenure_start_date &lt;= CURRENT_DATE
          AND t2.tenure_end_date >= CURRENT_DATE
          and t2.elect_org_code LIKE CONCAT(#{orgCode}, '%')
    </select>



    <select id="findDzzSjByOrgCode" resultType="java.lang.Long">
        SELECT count(1)
        FROM ccp_org_all
        WHERE delete_time IS NULL
          AND (is_dissolve IS NULL OR is_dissolve != 1)
          AND secretary IS NOT NULL
          AND secretary!='' AND org_code LIKE CONCAT(#{orgCode}
            , '%')
    </select>

    <select id="findUnitCommitteeByOrgCode" resultType="java.lang.Long">
        SELECT count(1)
        from ccp_unit_committee t1
                 INNER JOIN ccp_unit_committee_elect t2 ON t1.elect_code = t2.code and t2.delete_time is null
                 INNER JOIN ccp_unit t3 on t1.unit_code = t3.code
        where t1.delete_time is null
          AND t1.end_date is null
          AND t2.tenure_start_date &lt;= CURRENT_DATE
          AND t2.tenure_end_date &gt;= CURRENT_DATE
          AND t3.create_unit_org_code like CONCAT(#{orgCode}, '%')
    </select>

    <select id="findUnitZrByOrgCode" resultType="java.lang.Long">
        SELECT count(1)
        from ccp_unit_committee t1
                 INNER JOIN ccp_unit_committee_elect t2 ON t1.elect_code = t2.code and t2.delete_time is null
                 INNER JOIN ccp_unit t3 on t1.unit_code = t3.code
        where t1.delete_time is null
          AND t1.end_date is null
          AND d25_code in ('41', '51')
          AND t2.tenure_start_date &lt;= CURRENT_DATE
          AND t2.tenure_end_date &gt;= CURRENT_DATE
          AND t3.create_unit_org_code like CONCAT(#{orgCode}, '%')
    </select>

    <select id="findCzwCommitteeByOrgCode" resultType="java.lang.Long">
        SELECT count(1)
        from ccp_org_committee t1
                 INNER JOIN ccp_org_committee_elect t2 ON t1.elect_code = t2.code and t2.delete_time is null
                 INNER JOIN ccp_unit_org_linked t3 on t3.org_code = t1.org_code and t3.delete_time is null
        where t1.delete_time is null
          AND t1.end_date is null
          AND position_org_code like CONCAT(#{orgCode}, '%')
          AND t3.unit_type like '92%'
          AND t2.tenure_start_date &lt;= CURRENT_DATE
          AND t2.tenure_end_date &gt;= CURRENT_DATE
    </select>

    <select id="findCzwSjByOrgCode" resultType="java.lang.Long">
        SELECT count(1)
        from ccp_org_committee t1
                 INNER JOIN ccp_org_committee_elect t2 ON t1.elect_code = t2.code and t2.delete_time is null
                 INNER JOIN ccp_unit_org_linked t3 on t3.org_code = t1.org_code and t3.delete_time is null
        where t1.delete_time is null
          AND t1.end_date is null
          AND d022_code = '1'
          AND position_org_code like CONCAT(#{orgCode}, '%')
          AND t3.unit_type like '92%'
          AND t2.tenure_start_date &lt;= CURRENT_DATE
          AND t2.tenure_end_date &gt;= CURRENT_DATE
    </select>
    <select id="getInflowParty" resultType="com.zenith.front.model.vo.MemFlow1VO">
        SELECT mem_flow.*
        FROM
            mem_flow
                LEFT JOIN ccp_org ON mem_flow.in_org_code = ccp_org.code
        WHERE
            mem_flow.out_place_code NOT IN ( '3', '4' )
          AND ( mem_flow.flow_step = '2' )
          AND ccp_org.delete_time IS NULL
          AND (
            ( org_code LIKE CONCAT ( #{ orgCode }, '%' ) AND in_org_code IS NOT NULL )
                OR ( ( out_org_branch_org_code LIKE CONCAT ( #{ orgCode }, '%' ) AND in_org_code IS NULL ) )
            )
    </select>

    <select id="getFlowOutParty" resultType="com.zenith.front.model.vo.MemFlow1VO">
        SELECT *
        FROM
            mem_flow
        WHERE
            (
                ( flow_step = '2' AND flow_out = '1' AND flow_in = '1' )
                    OR ( flow_step = '1' AND flow_out = '1' AND flow_in = '0' )
                )
          AND mem_org_org_code LIKE CONCAT ( #{ orgCode }, '%' )
    </select>

    <select id="getYearPartyJoinTrain" resultType="com.zenith.front.model.vo.ChartDataPeggListVo">
        SELECT ccp_org."name",
               ccp_org.d01_code,
               ccp_org.contacter,
               ccp_org.contact_phone,
               ccp_org.secretary,
               ccp_mem_train.train_total
        FROM ccp_mem_train
                 INNER JOIN ccp_org ON ccp_org.code = ccp_mem_train.org_code AND ccp_org.delete_time IS NULL AND
                                       (is_dissolve IS NULL OR is_dissolve!=1) AND
                                       ccp_org.d01_code IN ('61', '62', '911', '921')
        WHERE ccp_mem_train.delete_time IS NULL
          AND train_total IS NOT NULL
          AND org_org_code LIKE CONCAT(#{ orgCode }, '%')
          AND "year" = #{year}
        ORDER BY length(ccp_org."org_code"), sort, ccp_org."create_time" DESC, ccp_org."id" DESC
    </select>
    <select id="exportYearPartyJoinTrain" resultType="com.zenith.front.model.vo.ChartDataPeggListVo">
        SELECT ccp_org."name",
               ccp_org.d01_code,
               ccp_org.contacter,
               ccp_org.contact_phone,
               ccp_org.secretary,
               ccp_mem_train.train_total
        FROM ccp_mem_train
                 INNER JOIN ccp_org ON ccp_org.code = ccp_mem_train.org_code AND ccp_org.delete_time IS NULL AND
                                       (is_dissolve IS NULL OR is_dissolve!=1) AND
                                       ccp_org.d01_code IN ('61', '62', '911', '921')
        WHERE ccp_mem_train.delete_time IS NULL
          AND train_total IS NOT NULL
          AND org_org_code LIKE CONCAT(#{ orgCode }, '%')
          AND "year" = #{year}
        ORDER BY length(ccp_org."org_code"), sort, ccp_org."create_time" DESC, ccp_org."id" DESC
    </select>
    <select id="getYearTrainNumber" resultType="java.util.Map">
        SELECT *
        FROM
            ccp_mem_train
        WHERE
            delete_time IS NULL
          and level_party_class is not null
          AND org_org_code LIKE CONCAT ( #{ orgCode }, '%' )
          AND "year" =#{year};
    </select>

    <select id="getYearTrainPeople" resultType="java.util.Map">
        SELECT
            *
        FROM
            ccp_mem_train
        WHERE
            delete_time IS NULL
          and level_party_member is not null
          AND org_org_code LIKE CONCAT ( #{ orgCode }, '%' )
          AND "year" =#{year};
    </select>

    <select id="getYearJoinDemocraticReviewParty" resultType="com.zenith.front.model.vo.MemAllInfoVO">
        SELECT
            *
              from ccp_mem_all
              where appraisal_org_code like CONCAT(#{orgCode}, '%')
                and has_join_reviewers=1
                and d08_code in ('1','2')
                and delete_time is null
    </select>

    <select id="getYearOrgInformation" resultType="com.zenith.front.model.vo.OrgAllVO">
        SELECT * FROM ccp_org_all
        WHERE delete_time IS NULL
          AND appraisal_org_code LIKE CONCAT ( #{ orgCode },'%' )
          AND d01_code IN ( '631', '632', '634', '931', '932' )
          AND has_join_reviewers = 1
          AND ( is_dissolve != 1 OR is_dissolve IS NULL )
    </select>

    <select id="getTownCorporation" resultType="java.util.Map">
        select * from ccp_unit
                 where delete_time is null
                   and create_unit_org_code LIKE CONCAT ( #{ orgCode },'%' )
                   and d04_code like '912%'
                   and is_legal = '1'
    </select>

    <select id="getUrbanCommunityCorporation" resultType="java.util.Map">
        select * from ccp_unit
        where delete_time is null
          and create_unit_org_code LIKE CONCAT ( #{ orgCode },'%' )
          and d04_code = '921'
          and is_legal = '1'
    </select>

    <select id="getTownCommunityCorporation" resultType="java.util.Map">
        select * from ccp_unit
        where delete_time is null
          and create_unit_org_code LIKE CONCAT ( #{ orgCode },'%' )
          and d04_code = '922'
          and is_legal = '1'
    </select>

    <select id="getAdministrativeVillageCorporation" resultType="java.util.Map">
        select * from ccp_unit
        where delete_time is null
          and create_unit_org_code LIKE CONCAT ( #{ orgCode },'%' )
          and d04_code = '923'
          and is_legal = '1'
    </select>
</mapper>
