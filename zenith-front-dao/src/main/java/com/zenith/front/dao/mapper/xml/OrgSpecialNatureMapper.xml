<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zenith.front.dao.mapper.org.OrgSpecialNatureMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.zenith.front.model.bean.OrgSpecialNature">
        <id column="id" property="id" />
        <result column="code" property="code" />
        <result column="org_code" property="orgCode" />
        <result column="special_org_code" property="specialOrgCode" />
        <result column="special_org_name" property="specialOrgName" />
        <result column="special_org_nature" property="specialOrgNature" />
        <result column="membership_function" property="membershipFunction" />
        <result column="set_up_date" property="setUpDate" />
        <result column="associated_organization" property="associatedOrganization" />
        <result column="has_associated_org" property="hasAssociatedOrg" />
        <result column="delete_time" property="deleteTime" />
        <result column="create_time" property="createTime" />
        <result column="update_time" property="updateTime" />
        <result column="collective_economy" property="collectiveEconomy" />
        <result column="specialOrgType" property="specialOrgType" />
        <result column="org_type" property="orgType" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, code, org_code, special_org_code, special_org_name, special_org_nature, membership_function, set_up_date, associated_organization, has_associated_org, delete_time, create_time, update_time, collective_economy, specialOrgType, org_type
    </sql>

</mapper>
