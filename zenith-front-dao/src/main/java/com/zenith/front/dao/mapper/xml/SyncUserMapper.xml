<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zenith.front.dao.mapper.ykz.SyncUserMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.zenith.front.model.bean.SyncUser">
        <id column="type" property="type" />
        <result column="fullname" property="fullname" />
        <result column="account" property="account" />
        <result column="tel" property="tel" />
        <result column="status" property="status" />
        <result column="sex" property="sex" />
        <result column="birthday" property="birthday" />
        <result column="email" property="email" />
        <result column="address" property="address" />
        <result column="remake" property="remake" />
        <result column="zwddId" property="zwddId" />
        <result column="openId" property="openId" />
        <result column="idCard" property="idCard" />
        <result column="unitIds" property="unitIds" />
        <result column="create_time" property="createTime" />
        <result column="update_time" property="updateTime" />
        <result column="is_delete" property="isDelete" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        type, fullname, account, tel, status, sex, birthday, email, address, remake, zwddId, openId, idCard, unitIds, create_time, update_time, is_delete
    </sql>

</mapper>
