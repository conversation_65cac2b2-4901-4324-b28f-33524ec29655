package com.zenith.front.dao.mapper.message;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.zenith.front.model.bean.MessageNotice;
import com.zenith.front.model.vo.MessageTypeCountVo;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 通知表，会通过消息表ccp_message进行单个拆分到次表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2021-07-10
 */
public interface MessageNoticeMapper extends BaseMapper<MessageNotice> {

    /**
     * 根据code获取相关类型通知类别
     *
     * @param code
     * @return
     */
    List<MessageTypeCountVo> getListByCode(@Param("code") String code);

}
