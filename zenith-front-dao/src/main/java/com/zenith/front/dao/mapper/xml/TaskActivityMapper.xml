<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zenith.front.dao.mapper.task.TaskActivityMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.zenith.front.model.bean.TaskActivity">
        <id column="id" property="id" />
        <result column="code" property="code" />
        <result column="task_code" property="taskCode" />
        <result column="activity_code" property="activityCode" />
        <result column="task_object_code" property="taskObjectCode" />
        <result column="create_time" property="createTime" />
        <result column="update_time" property="updateTime" />
        <result column="delete_time" property="deleteTime" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, code, task_code, activity_code, task_object_code, create_time, update_time, delete_time
    </sql>

</mapper>
