<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zenith.front.dao.mapper.transfer.TransferLogMapper">

    <resultMap id="BaseResultMap" type="com.zenith.front.model.bean.TransferLog">
        <id column="id" property="id" />
        <result column="handle_approval_id" property="handleApprovalId" />
        <result column="effect_approval_id" property="effectApprovalId" />
        <result column="type" property="type" />
        <result column="reason" property="reason" />
        <result column="create_time" property="createTime" />
        <result column="update_time" property="updateTime" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, handle_approval_id, effect_approval_id, type, reason, create_time, update_time
    </sql>

</mapper>
