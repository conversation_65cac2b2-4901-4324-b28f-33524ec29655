package com.zenith.front.dao.mapper.mem;

import com.zenith.front.model.bean.MemDevelopOperation;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 发展党员操作 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2021-09-26
 */
public interface MemDevelopOperationMapper extends BaseMapper<MemDevelopOperation> {

    /**
     * 根据机构层级码查询
     *
     * @param orgCode 机构层级码
     * @return
     */
    List<MemDevelopOperation> selectListByOrgCode(@Param("orgCode") String orgCode);
}
