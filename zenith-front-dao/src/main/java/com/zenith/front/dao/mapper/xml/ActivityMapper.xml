<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zenith.front.dao.mapper.activity.ActivityMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.zenith.front.model.bean.Activity">
        <id column="id" property="id" />
        <result column="code" property="code" />
        <result column="name" property="name" />
        <result column="ac_org_code" property="acOrgCode" />
        <result column="ac_org_org_code" property="acOrgOrgCode" />
        <result column="group_key" property="groupKey" />
        <result column="group_name" property="groupName" />
        <result column="group_code" property="groupCode" />
        <result column="hold_time" property="holdTime" />
        <result column="type_codes" property="typeCodes" />
        <result column="type_names" property="typeNames" />
        <result column="location" property="location" />
        <result column="address" property="address" />
        <result column="hostor_count" property="hostorCount" />
        <result column="lecturer_count" property="lecturerCount" />
        <result column="leader_count" property="leaderCount" />
        <result column="mem_count" property="memCount" />
        <result column="attend_count" property="attendCount" />
        <result column="absence_count" property="absenceCount" />
        <result column="content" property="content" />
        <result column="status" property="status" />
        <result column="is_open" property="isOpen" />
        <result column="view_count" property="viewCount" />
        <result column="good_count" property="goodCount" />
        <result column="has_file" property="hasFile" />
        <result column="create_type" property="createType" />
        <result column="timestamp" property="timestamp" />
        <result column="has_image" property="hasImage" />
        <result column="has_video" property="hasVideo" />
        <result column="has_voice" property="hasVoice" />
        <result column="has_document" property="hasDocument" />
        <result column="mem_filter" property="memFilter" />
        <result column="cancel_reason" property="cancelReason" />
        <result column="creator_account" property="creatorAccount" />
        <result column="ac_plan" property="acPlan" />
        <result column="ac_plan_file" property="acPlanFile" />
        <result column="can_review" property="canReview" />
        <result column="notice_plan" property="noticePlan" />
        <result column="create_time" property="createTime" />
        <result column="update_time" property="updateTime" />
        <result column="delete_time" property="deleteTime" />
        <result column="end_time" property="endTime" />
        <result column="entry_org_code_set" property="entryOrgCodeSet" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, code, name, ac_org_code, ac_org_org_code, group_key, group_name, group_code, hold_time, type_codes, type_names, location, address, hostor_count, lecturer_count, leader_count, mem_count, attend_count, absence_count, content, status, is_open, view_count, good_count, has_file, create_type, timestamp, has_image, has_video, has_voice, has_document, mem_filter, cancel_reason, creator_account, ac_plan, ac_plan_file, can_review, notice_plan, create_time, update_time, delete_time, end_time, entry_org_code_set
    </sql>

    <select id="getHreMeetingsAndOneClass" resultType="com.zenith.front.model.vo.HreMeetingsVO">
        select
        "code",
        "ac_org_code" as "acOrgCode",
        "ac_org_org_code" as "acOrgOrgCode",
        "name",
        "hold_time" as "holdTime",
        "address",
        "type_names" as "typeNames",
        "creator_account" as "creatorAccount",
        "mem_count" as "memCount",
        "absence_count" as "absenceCount"
        from "ccp_activity"
        where (
        (
        (type_codes ?? '21')
        or (type_codes ?? '22')
        or (type_codes ?? '23')
        or (type_codes ?? '24')
        )
        and "ac_org_org_code" like CONCAT(#{orgCode},'%')
        and "delete_time" is null
        <if test="isLeaf">
            and "ac_org_org_code"=#{orgCode}
        </if>
        <if test="!isLeaf">
            and "ac_org_org_code" like CONCAT(#{orgCode},'%')
            and "ac_org_org_code" !=#{orgCode}
        </if>
        )
        order by
        "ac_org_org_code",
        "id" desc
    </select>
    <select id="getThematicPartyDayOrg" resultType="com.zenith.front.model.vo.HreMeetingsVO">
        select "ac_org_org_code" as "acOrgOrgCode"
        from "ccp_activity"
        where (
        (
        (type_codes ?? '13')
        or (type_codes ?? '25')
        )
        and "ac_org_org_code" like CONCAT(#{orgCode},'%')
        and "ac_org_org_code" != #{orgCode}
        and "delete_time" is null
        and "hold_time" >= #{beginOfYear}
        and "hold_time" &lt;= #{endOfYear}
        )
        group by "ac_org_org_code"
        order by "ac_org_org_code"
    </select>
    <select id="getThematicPartyDay" resultType="com.zenith.front.model.bean.Activity">
        select
        "code",
        "ac_org_code",
        "ac_org_org_code",
        "name",
        "mem_count",
        "absence_count",
        "hold_time"
        from "ccp_activity"
        where (
        (
        (type_codes ?? '13')
        or (type_codes ?? '25')
        )
        and "ac_org_org_code" IN
        <foreach collection="list" item="item" index="index" open="(" separator="," close=")">
            #{item}
        </foreach>
        and "delete_time" is null
        <if test="beginOfYear!=null">
            and "hold_time" >= #{beginOfYear}
        </if>
        <if test="endOfYear!=null">
            and "hold_time" &lt;= #{endOfYear}
        </if>
        )
        order by "hold_time" desc
    </select>
    <select id="getOrgActivityRatio" resultType="com.zenith.front.model.vo.OptionVO">
        select
            sum(case
                    when (type_codes ?? '21') then 1
                    else 0
                end) as "one",
            sum(case
                    when (type_codes ?? '22') then 1
                    else 0
                end) as "two",
            sum(case
                    when (type_codes ?? '23') then 1
                    else 0
                end) as "three",
            sum(case
                    when (type_codes ?? '24') then 1
                    else 0
                end) as "four"
        from "ccp_activity"
        where (
                      "ac_org_org_code" like CONCAT(#{orgCode},'%')
                      and "status" = 3
                      and "delete_time" is null
                  )
    </select>
    <select id="situationActivityCount" resultType="com.zenith.front.model.vo.SituationActivityVO">
        select
            count(1) as activity
        from
            ccp_activity
        where
           "ac_org_org_code" like CONCAT(#{manageOrgCode},'%')
           and delete_time is null
    </select>
    <select id="getIsAttend" resultType="com.zenith.front.model.bean.ActivityMem">
        select
            ccp_activity_mem.mem_code
        from
            ccp_activity_mem
                left join ccp_activity on
                ccp_activity_mem.activity_code = ccp_activity.code
        where
            ccp_activity_mem.delete_time is null
          and ccp_activity.delete_time is null
          and is_absence=#{zeroInt}
        group by ccp_activity_mem.mem_code
    </select>

</mapper>
