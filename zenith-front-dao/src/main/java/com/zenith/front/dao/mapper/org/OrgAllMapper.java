package com.zenith.front.dao.mapper.org;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.zenith.front.model.bean.OrgAll;
import com.zenith.front.model.vo.OrgAllVO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2021-07-10
 */
public interface OrgAllMapper extends BaseMapper<OrgAll> {

    List<OrgAll> getOrgSortList(@Param("orgCode") String orgCode);

    int saveBatchTmp(OrgAll orgAll);

    List<OrgAll> configOrgAll(@Param("orgCode") String orgCode);

    List<OrgAll> configOrgAllById(@Param("code") String code);

    /**
     * 更新锁定字段
     *
     * @param lockFields 锁定字段
     * @return
     */
    int updateLockFields(@Param("lockFields") Object lockFields);

    /**
     * 查询驻村系统组织树
     *
     * @return java.util.List<com.zenith.front.model.OrgAll>
     * <AUTHOR>
     * @creed Talk is cheap,show me the code
     * @date 2023年03月01日 09时04分
     */
    List<OrgAll> getVillageCommunityOrgList();

    /**
     * 查询本级和直属下级组织
     *
     * @param orgCode 层级码
     * @return
     */
    List<OrgAll> selectUnderlingByOrgCode(@Param("orgCode") String orgCode);

    /**
     *
     * @param orgCode
     * @return
     */
    List<OrgAllVO> exportYearOrgInformation(@Param("orgCode") String orgCode);

    OrgAll getTopOrg();
}
