package com.zenith.front.dao.mapper.mem;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.zenith.front.model.bean.LogicCheck;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.zenith.front.model.vo.DecryptMap;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

/**
 * <AUTHOR>
 */
public interface LogicCheckMapper extends BaseMapper<LogicCheck> {

    /**
     * 分页查询结果
     *
     * @param page     分页参数
     * @param checkSql 查询sql语句
     * @return
     */
    @Select("${checkSql}")
    Page<DecryptMap<String, Object>> page(@Param(value = "page") Page page, @Param(value = "checkSql") String checkSql);

    /**
     * 查询总数
     *
     * @param checkSql 查询sql语句
     * @return
     */
    @Select("${checkSql}")
    int count(@Param(value = "checkSql") String checkSql);

}




