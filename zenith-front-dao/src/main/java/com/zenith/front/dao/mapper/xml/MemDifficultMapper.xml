<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zenith.front.dao.mapper.mem.MemDifficultMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.zenith.front.model.bean.MemDifficult">
        <id column="id" property="id" />
        <result column="code" property="code" />
        <result column="mem_code" property="memCode" />
        <result column="mem_name" property="memName" typeHandler="com.zenith.front.common.encrypt.typehandler.EncryptTypeHandler"/>
        <result column="sex_name" property="sexName" />
        <result column="sex_code" property="sexCode" />
        <result column="d08_name" property="d08Name" />
        <result column="d08_code" property="d08Code" />
        <result column="org_name" property="orgName" />
        <result column="org_code" property="orgCode" />
        <result column="diff_org_code" property="diffOrgCode" />
        <result column="org_zb_code" property="orgZbCode" />
        <result column="is_difficulty" property="isDifficulty" />
        <result column="is_disability" property="isDisability" />
        <result column="year_income" property="yearIncome" />
        <result column="d53_code" property="d53Code" />
        <result column="d53_name" property="d53Name" />
        <result column="d54_code" property="d54Code" />
        <result column="d54_name" property="d54Name" />
        <result column="d55_code" property="d55Code" />
        <result column="d55_name" property="d55Name" />
        <result column="is_disease" property="isDisease" />
        <result column="d56_code" property="d56Code" />
        <result column="d56_name" property="d56Name" />
        <result column="files" property="files" />
        <result column="is_serious_difficulty" property="isSeriousDifficulty" />
        <result column="is_low_income" property="isLowIncome" />
        <result column="is_serious_disease" property="isSeriousDisease" />
        <result column="is_edu_caused" property="isEduCaused" />
        <result column="difficulty_reason" property="difficultyReason" />
        <result column="pension_amount" property="pensionAmount" />
        <result column="difficulty_remark" property="difficultyRemark" />
        <result column="check_unit" property="checkUnit" />
        <result column="create_time" property="createTime" />
        <result column="update_time" property="updateTime" />
        <result column="delete_time" property="deleteTime" />
        <result column="is_history" property="isHistory" />
        <result column="update_account" property="updateAccount" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, code, mem_code, mem_name, sex_name, sex_code, d08_name, d08_code, org_name, org_code, diff_org_code, org_zb_code, is_difficulty, is_disability, year_income, d53_code, d53_name, d54_code, d54_name, d55_code, d55_name, is_disease, d56_code, d56_name, files, is_serious_difficulty, is_low_income, is_serious_disease, is_edu_caused, difficulty_reason, pension_amount, difficulty_remark, check_unit, create_time, update_time, delete_time, is_history, update_account
    </sql>

</mapper>
