package com.zenith.front.dao.mapper.decrypting;

import com.zenith.front.model.entity.DecryptingTaskRecord;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

public interface DecryptingInformationMapper {

    List<Map<String,String>> getDecryptingInformation(@Param("tableName")String tableName,@Param("fields")String fields);

    void updateDecryptingInformation(@Param("sql")String sql, @Param("list") List<Map<String,String>> list, @Param("tableName")String tableName);

    void updateDecryptingInformation1(@Param("sql") String sql);

    // ========== 新增状态跟踪相关方法 ==========

    /**
     * 插入任务记录
     */
    void insertTaskRecord(DecryptingTaskRecord record);

    /**
     * 批量插入任务记录
     */
    void batchInsertTaskRecords(@Param("records") List<DecryptingTaskRecord> records);

    /**
     * 更新任务记录状态
     */
    void updateTaskRecord(DecryptingTaskRecord record);

    /**
     * 根据ID查询任务记录
     */
    DecryptingTaskRecord selectTaskRecordById(@Param("id") String id);

    /**
     * 查询失败和待处理的任务记录（可重试的）
     */
    List<DecryptingTaskRecord> selectFailedTaskRecords(@Param("batchNo") String batchNo);

    /**
     * 查询指定批次的任务记录
     */
    List<DecryptingTaskRecord> selectTaskRecordsByBatch(@Param("batchNo") String batchNo);

    /**
     * 查询指定表和数据code的任务记录
     */
    DecryptingTaskRecord selectTaskRecordByTableAndCode(@Param("tableName") String tableName, @Param("dataCode") String dataCode);

    /**
     * 统计批次任务状态
     */
    Map<String, Object> countTaskStatusByBatch(@Param("batchNo") String batchNo);

    /**
     * 批量查询成功的任务记录
     */
    List<DecryptingTaskRecord> selectSuccessTaskRecordsByTableAndCodes(@Param("tableName") String tableName, @Param("dataCodes") List<String> dataCodes);

    /**
     * 批量查询任务记录（所有状态）
     */
    List<DecryptingTaskRecord> selectTaskRecordsByTableAndCodes(@Param("tableName") String tableName, @Param("dataCodes") List<String> dataCodes);

    void deleteInfo();
}
