<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zenith.front.dao.mapper.develop.DevelopStepLogMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.zenith.front.model.bean.DevelopStepLog">
        <id column="id" property="id"/>
        <result column="code" property="code"/>
        <result column="es_id" property="esId"/>
        <result column="name" property="name"
                typeHandler="com.zenith.front.common.encrypt.typehandler.EncryptTypeHandler"/>
        <result column="org_name" property="orgName"/>
        <result column="org_zb_code" property="orgZbCode"/>
        <result column="org_code" property="orgCode"/>
        <result column="log_org_code" property="logOrgCode"/>
        <result column="mem_code" property="memCode"/>
        <result column="topre_introduction_mem" property="topreIntroductionMem"/>
        <result column="topre_file_url" property="topreFileUrl"/>
        <result column="topre_join_book_url" property="topreJoinBookUrl"/>
        <result column="topre_committee_date" property="topreCommitteeDate"/>
        <result column="topre_join_book_num" property="topreJoinBookNum"/>
        <result column="topre_join_org_date" property="topreJoinOrgDate"/>
        <result column="topre_committee_file_url" property="topreCommitteeFileUrl"/>
        <result column="d08_name" property="d08Name"/>
        <result column="d08_code" property="d08Code"/>
        <result column="join_org_code" property="joinOrgCode"/>
        <result column="join_org_name" property="joinOrgName"/>
        <result column="d11_code" property="d11Code"/>
        <result column="d11_name" property="d11Name"/>
        <result column="topart_introduction_mem" property="topartIntroductionMem"/>
        <result column="topart_file_url" property="topartFileUrl"/>
        <result column="topart_turn_party_date" property="topartTurnPartyDate"/>
        <result column="d28_name" property="d28Name"/>
        <result column="d28_code" property="d28Code"/>
        <result column="topart_committee_file_url" property="topartCommitteeFileUrl"/>
        <result column="topart_committee_date" property="topartCommitteeDate"/>
        <result column="topart_oath_date" property="topartOathDate"/>
        <result column="extend_prepar_date" property="extendPreparDate"/>
        <result column="toactive_apply_scan_file" property="toactiveApplyScanFile"/>
        <result column="toactive_context_person" property="toactiveContextPerson"/>
        <result column="active_date" property="activeDate"/>
        <result column="toobj_file_url" property="toobjFileUrl"/>
        <result column="toobj_cultivate_date" property="toobjCultivateDate"/>
        <result column="toobj_check_file_url" property="toobjCheckFileUrl"/>
        <result column="toobj_context_mem" property="toobjContextMem"/>
        <result column="object_date" property="objectDate"/>
        <result column="canncel_date" property="canncelDate"/>
        <result column="canncel_code" property="canncelCode"/>
        <result column="canncel_name" property="canncelName"/>
        <result column="create_time" property="createTime"/>
        <result column="update_time" property="updateTime"/>
        <result column="delete_time" property="deleteTime"/>
        <result column="topart_oath_date_url" property="topartOathDateUrl"/>
        <result column="is_history" property="isHistory"/>
        <result column="update_account" property="updateAccount"/>
        <result column="d09_code" property="d09Code"/>
        <result column="d09_name" property="d09Name"/>
        <result column="is_out_system" property="isOutSystem"/>
        <result column="out_branch_org_name" property="outBranchOrgName"/>
        <result column="legacy_data" property="legacyData"/>
        <result column="idcard" property="idcard"
                typeHandler="com.zenith.front.common.encrypt.typehandler.EncryptTypeHandler"/>
        <result column="d06_code" property="d06Code"/>
        <result column="d06_name" property="d06Name"/>
        <result column="d48_code" property="d48Code"/>
        <result column="d48_name" property="d48Name"/>
        <result column="sex_code" property="sexCode"/>
        <result column="sex_name" property="sexName"/>
        <result column="birthday" property="birthday"/>
        <result column="phone" property="phone"
                typeHandler="com.zenith.front.common.encrypt.typehandler.EncryptTypeHandler"/>
        <result column="d07_code" property="d07Code"/>
        <result column="d07_name" property="d07Name"/>
        <result column="d21_code" property="d21Code"/>
        <result column="d21_name" property="d21Name"/>
        <result column="is_farmer" property="isFarmer"/>
        <result column="age" property="age"/>
        <result column="is_dispatch" property="isDispatch"/>
        <result column="advanced_model_code" property="advancedModelCode"/>
        <result column="d19_name" property="d19Name"/>
        <result column="d19_code" property="d19Code"/>
        <result column="d89_code" property="d89Code"/>
        <result column="d89_name" property="d89Name"/>
        <result column="d20_name" property="d20Name"/>
        <result column="d20_code" property="d20Code"/>
        <result column="d04_code" property="d04Code"/>
        <result column="d04_name" property="d04Name"/>
        <result column="job_nature_code" property="jobNatureCode"/>
        <result column="byyx" property="byyx"/>
        <result column="d88_code" property="d88Code"/>
        <result column="d88_name" property="d88Name"/>
        <result column="home_address" property="homeAddress"
                typeHandler="com.zenith.front.common.encrypt.typehandler.EncryptTypeHandler"/>
        <result column="apply_date" property="applyDate"/>
        <result column="unit_information" property="unitInformation"/>
        <result column="has_unit_province" property="hasUnitProvince"/>
        <result column="statistical_unit" property="statisticalUnit"/>
        <result column="has_unit_statistics" property="hasUnitStatistics"/>
        <result column="enter_school_date" property="enterSchoolDate"/>
        <result column="is_transfer" property="isTransfer"/>
        <result column="has_worker" property="hasWorker"/>
        <result column="reading_college" property="readingCollege"/>
        <result column="reading_professional_code" property="readingProfessionalCode"/>
        <result column="reading_professional_name" property="readingProfessionalName"/>
        <result column="educational_system" property="educationalSystem"/>
        <result column="leave_org_date" property="leaveOrgDate"/>
        <result column="has_high_knowledge" property="hasHighKnowledge"/>
        <result column="has_high_level_talents" property="hasHighLevelTalents"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id
        , code, es_id, name, org_name, org_zb_code, org_code, log_org_code, mem_code, topre_introduction_mem, topre_file_url, topre_join_book_url, topre_committee_date, topre_join_book_num, topre_join_org_date, topre_committee_file_url, d08_name, d08_code, join_org_code, join_org_name, d11_code, d11_name, topart_introduction_mem, topart_file_url, topart_turn_party_date, d28_name, d28_code, topart_committee_file_url, topart_committee_date, topart_oath_date, extend_prepar_date, toactive_apply_scan_file, toactive_context_person, active_date, toobj_file_url, toobj_cultivate_date, toobj_check_file_url, toobj_context_mem, object_date, canncel_date, canncel_code, canncel_name, create_time, update_time, delete_time, topart_oath_date_url, is_history, update_account, d09_code, d09_name, is_out_system, out_branch_org_name, legacy_data, idcard, d06_code, d06_name, d48_code, d48_name, sex_code, sex_name, birthday, phone, d07_code, d07_name, d21_code, d21_name, is_farmer, age, is_dispatch, advanced_model_code, d19_name, d19_code, d89_code, d89_name, d20_name, d20_code, d04_code, d04_name, job_nature_code, byyx, d88_code, d88_name, home_address, apply_date, unit_information, has_unit_province, statistical_unit, has_unit_statistics, enter_school_date, is_transfer, has_worker, reading_college, reading_professional_code, reading_professional_name, educational_system, leave_org_date, has_high_knowledge, has_high_level_talents
    </sql>

    <select id="getReadyList" resultType="com.zenith.front.model.vo.MemDevelopVo">
        SELECT t1.code,t1.name,t1.sex_code "sexCode",t1.sex_name "sexName",t1.phone,"t1"."d07_code" "d07Code",
        t1.org_name as "orgName", t1."idcard" as "idcard", t1.mem_code "memCode", t1.legacy_data "legacyData",
        t1.d06_name as d06Name, t1.d07_name as d07Name, t1.d09_name as d09Name, t1.d20_name as d20Name,
        t1.d21_name as d21Name, t1.d19_name as d19Name,t1.topre_join_org_date as topreJoinOrgDate,
        t1.log_org_code as memOrgCode,t1.has_calculation_grade as hasCalculationGrade,t1.has_unit_province as hasUnitProvince,
        t1.d04_code as d04Code, t1.has_unit_statistics as hasUnitStatistics,t1.unit_information as unitInformation,
        t1.d194_name,t1.d195_name,
        t1.educational_system AS educationalSystem, t1.enter_school_date AS enterSchoolDate
        FROM ccp_develop_step_log_all t1
        WHERE t1.d08_code = '3'
        AND t1.delete_time IS NULL
        AND EXTRACT ( YEAR FROM "topre_join_org_date" ) = #{year}
        <choose>
            <when test="dto.subordinate==0">
                and t1.log_org_code = #{dto.memOrgCode}
            </when>
            <otherwise>
                and t1.log_org_code like concat(#{dto.memOrgCode},'%')
            </otherwise>
        </choose>
        <if test="dto.memName!=null and dto.memName!=''">
            AND ("t1"."name" like concat('%',#{dto.memName},'%'))
        </if>

        <if test="dto.d09CodeList!=null and dto.d09CodeList.size>0">
            AND "t1"."d09_code" IN
            <foreach collection="dto.d09CodeList" item="id" open="(" close=")" separator=",">
                #{id}
            </foreach>
        </if>
        <if test="dto.d07CodeList!=null and dto.d07CodeList.size>0">
            AND "t1"."d07_code" IN
            <foreach collection="dto.d07CodeList" item="id" open="(" close=")" separator=",">
                #{id}
            </foreach>
        </if>
        <if test="dto.sexCodeList!=null and dto.sexCodeList.size>0">
            AND "t1"."sex_code" IN
            <foreach collection="dto.sexCodeList" item="id" open="(" close=")" separator=",">
                #{id}
            </foreach>
        </if>
        <if test="dto.d194CodeList!=null and dto.d194CodeList.size>0">
            AND "t1"."d194_code" IN
            <foreach collection="dto.d194CodeList" item="id" open="(" close=")" separator=",">
                #{id}
            </foreach>
        </if>
        <if test="dto.d195CodeList!=null and dto.d195CodeList.size>0">
            AND "t1"."d195_code" IN
            <foreach collection="dto.d195CodeList" item="id" open="(" close=")" separator=",">
                #{id}
            </foreach>
        </if>
        ORDER BY topre_join_org_date desc,create_time desc
    </select>

    <select id="getYearReadyCount" resultType="java.util.Map">
        SELECT count(1)
        FROM ccp_develop_step_log t1
                 LEFT JOIN ccp_mem_develop t2 ON t2.code = t1.mem_code
        WHERE t1.d08_code = '3'
          AND t1.delete_time IS NULL
          AND EXTRACT(YEAR FROM "topre_join_org_date") = #{year}
          AND t1.log_org_code like concat(#{orgCode}, '%')
        GROUP BY t1.id
    </select>

    <select id="selectListByOrgCode" resultType="com.zenith.front.model.bean.DevelopStepLog">
        SELECT ccp_develop_step_log.code,
               ccp_develop_step_log.log_org_code        as logOrgCode,
               ccp_develop_step_log.d08_code            as d08Code,
               ccp_develop_step_log.topre_join_org_date as topreJoinOrgDate
        FROM ccp_develop_step_log
                 LEFT JOIN ccp_org ON ccp_org.code = ccp_develop_step_log.org_code
                 LEFT JOIN ccp_unit ON ccp_org.main_unit_code = ccp_unit.code
        WHERE ccp_develop_step_log.delete_time IS NULL
          AND ccp_develop_step_log.d08_code IN ('3', '4', '5')
          AND ccp_org.delete_time IS NULL
          AND (is_dissolve IS NULL OR is_dissolve &lt;&gt; 1)
          AND ccp_unit.d04_code in ('922', '923')
          AND ccp_develop_step_log.log_org_code like concat(#{orgCode}, '%')
    </select>


    <select id="findDevelopStepLogAllNotIn" resultMap="BaseResultMap">
        SELECT code
        FROM ccp_develop_step_log
        WHERE code NOT IN (SELECT code FROM ccp_develop_step_log_all WHERE delete_time IS NULL)
          AND delete_time IS NULL;
    </select>

    <select id="stat" resultType="java.util.Map">
        SELECT ccp_develop_step_log.code,
               ccp_develop_step_log.log_org_code,
               ccp_develop_step_log.birthday,
               ccp_develop_step_log.sex_code,
               ccp_develop_step_log.d06_code,
               ccp_develop_step_log.d07_code,
               u.main_unit_code,
               u.main_unit_name
        FROM ccp_develop_step_log
                 INNER JOIN ccp_org_all u ON u.code = ccp_develop_step_log.org_code
        WHERE d08_code = '3'
          AND ccp_develop_step_log.delete_time IS NULL
            AND topre_join_org_date BETWEEN #{begin} AND #{end}
            AND u.d04_code IN
            <foreach collection="d04CodeList" item="id" open="(" close=")" separator=",">
                #{id}
            </foreach>
            <if test="name!=null and name!=''">
                AND (u.main_unit_name like concat('%',#{name},'%'))
            </if>
            <choose>
                <when test="includeChild == true">
                    AND log_org_code like concat(#{orgLevelCode},'%')
                </when>
                <otherwise>
                    AND log_org_code = #{orgLevelCode}
                </otherwise>
            </choose>
            <if test="d09CodeList!=null and d09CodeList.size>0">
                AND "d09_code" IN
                <foreach collection="d09CodeList" item="id" open="(" close=")" separator=",">
                    #{id}
                </foreach>
            </if>
            <if test="d07CodeList!=null and d07CodeList.size>0">
                AND "d07_code" IN
                <foreach collection="d07CodeList" item="id" open="(" close=")" separator=",">
                    #{id}
                </foreach>
            </if>
            <if test="sexCodeList!=null and sexCodeList.size>0">
                AND "sex_code" IN
                <foreach collection="sexCodeList" item="id" open="(" close=")" separator=",">
                    #{id}
                </foreach>
            </if>
        ORDER BY "char_length"(u.org_code),
                 u.sort,
                 u.create_time DESC,
                 u.ID DESC
    </select>

    <select id="getReadyListLikeOrgCode" resultType="com.zenith.front.model.vo.MemDevelopVo">
        SELECT
               <choose>
                   <when test="selectSql != null and selectSql != ''">
                       ${selectSql}
                   </when>
                   <otherwise>
                       code,
                       NAME,
                       org_name AS orgName,
                       idcard AS idcard,
                       mem_code as memCode,
                       topre_join_org_date AS topreJoinOrgDate,
                       log_org_code AS memOrgCode
                   </otherwise>
               </choose>
        FROM
            ccp_develop_step_log_all
        WHERE
            d08_code = '3'
          AND delete_time IS NULL
          AND log_org_code like concat(#{orgCode},'%')
          AND topre_join_org_date between #{startDate} and #{endDate}
    </select>


    <sql id="developStepSql">
        SELECT
            NAME,
            org_code as orgCode,
            sex_name AS sexName,
            idcard AS idcard,
            to_char(birthday, 'yyyy.MM.dd') AS birthday,
            has_worker AS hasWorker,
            d04_name AS d04Name,
            d07_name AS d07Name,
            age AS age,
            d09_name AS d09Name,
            d19_name AS d19Name,
            d06_name AS d06Name,
            d06_code AS d06Code,
            to_char(apply_date, 'yyyy.MM.dd') AS applyDate,
            to_char(active_date, 'yyyy.MM.dd') AS activeDate,
            to_char(object_date, 'yyyy.MM.dd') AS objectDate,
            to_char(topre_join_org_date, 'yyyy.MM.dd') AS topreJoinOrgDate,
            to_char(topart_turn_party_date, 'yyyy.MM.dd') AS topartTurnPartyDate,
            org_name AS outBranchOrgName,
            to_char(short_training_begin_time, 'yyyy.MM.dd') as shortTrainingBeginTime,
            to_char(short_training_end_time, 'yyyy.MM.dd') as shortTrainingEndTime,
            to_char(review_conclusion_time, 'yyyy.MM.dd') as reviewConclusionTime,
            d28_name as d28Name
        FROM
            ccp_develop_step_log_all
    </sql>

    <select id="getReadyPageLikeOrgCode" resultType="com.zenith.front.model.vo.BaseExcelReport">
        <include refid="developStepSql"/>
        WHERE
            delete_time IS NULL
          AND d08_code = '3'
          AND ccp_develop_step_log_all.topre_join_org_date BETWEEN #{startDate} AND #{endDate}
          AND log_org_code LIKE CONCAT(#{orgCode}, '%')
    </select>
    <select id="selectPageYBQPull" resultType="com.zenith.front.model.vo.BaseExcelReport">
        <include refid="developStepSql"/>
        WHERE delete_time IS NULL
        AND d08_code in ('1','2')
        AND extend_prepar_date BETWEEN #{startDate} AND #{endDate}
    </select>
    <select id="selectPageUNYBQPull" resultType="com.zenith.front.model.vo.BaseExcelReport">
        <include refid="developStepSql"/>
        WHERE delete_time IS NULL
        AND d08_code = '2'
        AND extend_prepar_date &lt; now()
    </select>

    <select id="getYearDevelopmentParty" resultMap="BaseResultMap">
        SELECT
            *
        FROM ccp_develop_step_log t1
                 LEFT JOIN ccp_mem_develop t2 ON t2.code = t1.mem_code
        WHERE t1.d08_code = '3'
          AND t1.delete_time IS NULL
          AND EXTRACT(YEAR FROM "topre_join_org_date") = #{year}
          AND t1.log_org_code like concat(#{orgCode}, '%')
    </select>

    <select id="exportYearDevelopmentParty" resultMap="BaseResultMap">
        SELECT
            *
        FROM ccp_develop_step_log t1
                 LEFT JOIN ccp_mem_develop t2 ON t2.code = t1.mem_code
        WHERE t1.d08_code = '3'
          AND t1.delete_time IS NULL
          AND EXTRACT(YEAR FROM "topre_join_org_date") = #{year}
          AND t1.log_org_code like concat(#{orgCode}, '%')
    </select>

</mapper>
