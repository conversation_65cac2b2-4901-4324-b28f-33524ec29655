<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zenith.front.dao.mapper.dict.DictMapper">

    <select id="selectD151Map" resultType="java.util.Map">
        SELECT key, parent, name, city
        FROM "public"."dict_d151"
        ORDER BY "key"
    </select>

    <select id="selectD151Key" resultType="java.lang.String">
        SELECT
        key
        FROM
        dict_d151
        <where>
            <if test="keyList != null and keyList.size >0">
                KEY IN
                <foreach collection="keyList" item="item" index="index" open="(" separator="," close=")">
                    #{item}
                </foreach>
                OR parent IN
                <foreach collection="keyList" item="item" index="index" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
        </where>
        ORDER BY
        ID
    </select>

    <select id="selectD151KeyCity" resultType="java.util.Map">
        SELECT key,city FROM "dict_d151" WHERE city IS NOT NULL ORDER BY "key"
    </select>

    <select id="selectD212Key" resultType="java.lang.String">
        SELECT key FROM "dict_d212" WHERE key like CONCAT(#{key}, '%')
    </select>
</mapper>
