<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zenith.front.dao.mapper.org.OrgExampleSiteMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.zenith.front.model.bean.OrgExampleSite">
        <id column="code" property="code"/>
        <result column="org_code" property="orgCode"/>
        <result column="org_level_code" property="orgLevelCode"/>
        <result column="d157_code" property="d157Code"/>
        <result column="d157_name" property="d157Name"/>
        <result column="example_site_date" property="exampleSiteDate"/>
        <result column="identify_unit" property="identifyUnit"/>
        <result column="has_standard_up" property="hasStandardUp"/>
        <result column="accept_time" property="acceptTime"/>
        <result column="accept_unit" property="acceptUnit"/>
        <result column="create_time" property="createTime"/>
        <result column="update_time" property="updateTime"/>
        <result column="delete_time" property="deleteTime"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        code
        , org_code, org_level_code, d157_code, d157_name, example_site_date, identify_unit, has_standard_up, accept_time, accept_unit, create_time, update_time, delete_time
    </sql>

</mapper>
