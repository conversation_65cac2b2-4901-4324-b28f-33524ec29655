<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zenith.front.dao.mapper.problemfeedback.ProblemFeedbackMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.zenith.front.model.bean.ProblemFeedback">
        <id column="code" property="code" />
        <result column="title" property="title" />
        <result column="questions_briefly" property="questionsBriefly" />
        <result column="problem_details" property="problemDetails" />
        <result column="user_id" property="userId" />
        <result column="reply_situation" property="replySituation" />
        <result column="release_time" property="releaseTime" />
        <result column="look_number" property="lookNumber" />
        <result column="member_reply" property="memberReply" />
        <result column="state_repair" property="stateRepair" />
        <result column="contact" property="contact" />
        <result column="create_time" property="createTime" />
        <result column="update_time" property="updateTime" />
        <result column="delete_time" property="deleteTime" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        code, title, questions_briefly, problem_details, user_id, reply_situation, release_time, look_number, member_reply, state_repair, contact, create_time, update_time, delete_time
    </sql>

</mapper>
