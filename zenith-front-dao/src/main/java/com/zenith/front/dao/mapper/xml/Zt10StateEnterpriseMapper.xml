<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zenith.front.mapper.Zt10StateEnterpriseMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.zenith.front.model.bean.Zt10StateEnterprise">
        <id column="code" property="code" />
        <result column="unit_code" property="unitCode" />
        <result column="is_build_board" property="isBuildBoard" />
        <result column="is_secretary_chairman_by_one" property="isSecretaryChairmanByOne" />
        <result column="is_intake_business_overhead" property="isIntakeBusinessOverhead" />
        <result column="is_personnel_party_by_department" property="isPersonnelPartyByDepartment" />
        <result column="is_personnel_party_by_leader" property="isPersonnelPartyByLeader" />
        <result column="is_party_managerial_same_rank" property="isPartyManagerialSameRank" />
        <result column="is_chairman_by_superior_principal" property="isChairmanBySuperiorPrincipal" />
        <result column="listed_company_num" property="listedCompanyNum" />
        <result column="is_party_required_write_rule" property="isPartyRequiredWriteRule" />
        <result column="is_organ_study_as_per_processors" property="isOrganStudyAsPerProcessors" />
        <result column="branch_offices_num" property="branchOfficesNum" />
        <result column="is_build_organ" property="isBuildOrgan" />
        <result column="primary_party_organ_num" property="primaryPartyOrganNum" />
        <result column="branch_party_num" property="branchPartyNum" />
        <result column="create_time" property="createTime" />
        <result column="update_time" property="updateTime" />
        <result column="delete_time" property="deleteTime" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        code, unit_code, is_build_board, is_secretary_chairman_by_one, is_intake_business_overhead, is_personnel_party_by_department, is_personnel_party_by_leader, is_party_managerial_same_rank, is_chairman_by_superior_principal, listed_company_num, is_party_required_write_rule, is_organ_study_as_per_processors, branch_offices_num, is_build_organ, primary_party_organ_num, branch_party_num, create_time, update_time, delete_time
    </sql>

</mapper>
