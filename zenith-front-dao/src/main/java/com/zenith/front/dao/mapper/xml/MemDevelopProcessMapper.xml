<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zenith.front.dao.mapper.mem.MemDevelopProcessMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.zenith.front.model.bean.MemDevelopProcess">
        <id column="code" property="code"/>
        <result column="process_node" property="processNode"/>
        <result column="digital_lot_no" property="digitalLotNo"/>
        <result column="d08_code" property="d08Code"/>
        <result column="approve_time" property="approveTime"/>
        <result column="approve_user" property="approveUser"/>
        <result column="extend_star_time" property="extendStarTime"/>
        <result column="extend_end_time" property="extendEndTime"/>
        <result column="previous_code" property="previousCode"/>
        <result column="create_time" property="createTime"/>
        <result column="create_user" property="createUser"/>
        <result column="update_time" property="updateTime"/>
        <result column="update_user" property="updateUser"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        code
        , process_node, digital_lot_no, d08_code, approve_time, approve_user, extend_star_time, extend_end_time, previous_code, create_time, create_user, update_time, update_user
    </sql>

    <select id="getMemDevelopAuditList" resultType="com.zenith.front.model.vo.MemAuditListVo">
        SELECT
        t1.code,t1.name,t1.sex_name, t1.org_code,t1.develop_org_code,
        p.process_node,p.digital_lot_no,p.d08_code,p.approve_time,p.create_time,p.update_time,p.reason,p.audit_status
        FROM ccp_mem_develop_process p
        LEFT JOIN ccp_mem_develop t1 on t1.digital_lot_no = p.digital_lot_no
        WHERE p.process_node ='FZ_8' AND p.audit_status IN ( 1, 2, 3 )
        AND p.delete_time IS NULL and (t1.is_transfer is null or t1.is_transfer != 1)
          and t1.delete_time is null
        <choose>
            <when test="dto.subordinate==0">
                and t1.develop_org_code = #{dto.memOrgCode}
            </when>
            <otherwise>
                and t1.develop_org_code like concat(#{dto.memOrgCode},'%')
            </otherwise>
        </choose>
        <if test="dto.memName!=null and dto.memName!=''">
            AND ("t1"."name" like concat('%',#{dto.memName},'%'))
        </if>
        ORDER BY p.audit_status,p.update_time desc,p.code
    </select>

    <select id="getMemAuditList" resultType="com.zenith.front.model.vo.MemAuditListVo">
        SELECT
        t1.code,t1.name,t1.sex_name,t1.idcard,t1.phone,t1.org_code,t1.mem_org_code,
        p.process_node,p.digital_lot_no,p.d08_code,p.approve_time,p.create_time,p.update_time,p.reason,p.audit_status
        FROM ccp_mem_develop_process p
        LEFT JOIN ccp_mem t1 on t1.digital_lot_no = p.digital_lot_no
        WHERE p.process_node ='YBQ_6' AND p.audit_status IN ( 1, 2, 3 )
        AND p.delete_time IS NULL and (t1.is_transfer is null or t1.is_transfer != 1)
        and t1.delete_time is null
        <choose>
            <when test="dto.subordinate==0">
                and t1.mem_org_code = #{dto.memOrgCode}
            </when>
            <otherwise>
                and t1.mem_org_code like concat(#{dto.memOrgCode},'%')
            </otherwise>
        </choose>
        <if test="dto.memName!=null and dto.memName!=''">
            AND ("t1"."name" like concat('%',#{dto.memName},'%'))
        </if>
        ORDER BY p.audit_status,p.update_time desc,p.code
    </select>
    <select id="parentYbqProcess" resultType="java.lang.Integer">
        select count(1) from ccp_mem_develop_process p1
                                 left join ccp_mem_develop_process p2 on p1.previous_code = p2.code and p2.process_node like 'YBQ%'
        where p1.process_node = 'YBQ_1_1' and p1.approve_time is null and p2.code is not null and p1.delete_time is null
          and p1.digital_lot_no = #{digitalLotNo}
    </select>
    <select id="parentJjProcess" resultType="java.lang.Integer">
        select count(1) from ccp_mem_develop_process p1
                                 left join ccp_mem_develop_process p2 on p1.previous_code = p2.code and p2.process_node like 'JJ%'
        where (p1.process_node = 'JJ_1' or  p1.process_node = 'JJ_4' and p2.process_node in ('JJ_3', 'JJ_5')) and p1.approve_time is null and p2.code is not null and p1.delete_time is null
          and p1.digital_lot_no = #{digitalLotNo}
    </select>
    <select id="previousProcessNode" resultType="java.lang.String">
        select p2.process_node  from
            ccp_mem_develop_process p1
                left join ccp_mem_develop_process p2 on p1.previous_code = p2.code
        where   p1.approve_time is null and p1.delete_time is null
          and p1.digital_lot_no = #{digitalLotNo}
    </select>

</mapper>
