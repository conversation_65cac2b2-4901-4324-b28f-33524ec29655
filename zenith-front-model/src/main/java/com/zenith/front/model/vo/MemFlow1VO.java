package com.zenith.front.model.vo;


import com.zenith.front.model.bean.MemFlow1;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;

@EqualsAndHashCode(callSuper = true)
@Data
public class MemFlow1VO extends MemFlow1 {

    private Integer id;

    private String code;

    /**
     * 流动党员唯一码
     */
    private String flowUqCode;

    /**
     * 党员主键
     */
    private String memCode;

    /**
     * 姓名
     */
    private String memName;

    /**
     * 性别
     */
    private String memSexCode;

    private String memSexName;

    /**
     * 身份证号码
     */
    private String memIdcard;

    /**
     * 联系电话
     */
    private String memPhone;

    /**
     * 党组织所在行政区划
     */
    private String outAdministrativeDivisionCode;

    private String outAdministrativeDivisionName;

    /**
     * 流动类型
     */
    private String flowTypeCode;

    private String flowTypeName;

    /**
     * 流动原因 跨省（区、市）流动 省(区、市)内跨市（地、州、盟)流动 市(地、州、盟)内跨县（(市、区、旗)流动
     */
    private String flowReasonCode;

    private String flowReasonName;

    /**
     * 外出日期
     */
    private Date outTime;

    /**
     * 外出时长
     */
    private String outDuraion;

    /**
     * 党委
     */
    private String outOrgCode;

    private String outOrgName;

    /**
     * 流入地党支部
     */
    private String inOrgCode;

    /**
     * 流入地党支部名称
     */
    private String inOrgName;

    /**
     * 退回原因
     */
    private String rejectReasonCode;

    private String rejectReasonName;

    /**
     * 退回时间
     */
    private Date rejectTime;

    /**
     * 退回操作党组织
     */
    private String rejectOrgCode;

    /**
     * 退回操作党组织
     */
    private String rejectOrgName;

    /**
     * 退回操作党组织联系电话
     */
    private String rejectOrgPhone;
    /**
     * 流出地党支部
     */
    private String memOrgCode;

    private String memOrgName;
    /**
     * 流出地党支部联系电话
     */
    private String memOrgPhone;
    /**
     * 工作岗位
     */
    private String memD09Code;

    private String memD09Name;
    /**
     * 外出地点
     */
    private String outPlaceCode;

    private String outPlaceName;
    /**
     * 流入地党组织单位类型
     */
    private String outOrgD04Code;

    /**
     * 流入地党组织单位类型
     */
    private String outOrgD04Name;
    /**
     * 流动党员活动证
     */
    private String isHold;

    /**
     * 流入地党支部联系方式
     */
    private String inOrgPhone;
    /**
     * 参与组织生活情况
     */
    private String inOrgLife;
    /**
     * 表现反馈
     */
    private String inFeedback;
    /**
     * 外出地点（党组织）补充说明
     */
    private String outOrgRemarks;
    /**
     * 流回时间
     */
    private Date flowBackTime;
    /**
     * 超时未纳入
     */
    private String outOverTime;
    /**
     * 超时未转接组织关系
     */
    private String notTransFerRed;

    /**
     * 流动状态名称
     */
    private String flowStepName;
    /**
     * 结对联系人
     */
    private String pairedContact;
    /**
     * 结对联系方式
     */
    private String pairedContactPhone;

    /**
     * 登记时间
     */
    private Date registerTime;
    private String rejectOtherReasonText;

    private String outOrgContact;

    private String outOrgContactPhone;

}
