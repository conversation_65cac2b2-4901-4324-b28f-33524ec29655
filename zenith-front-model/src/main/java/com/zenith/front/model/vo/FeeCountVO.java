package com.zenith.front.model.vo;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @Description: 党费标准列表
 * @date 2019/6/3 17:36
 */
@Data
@ToString
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
public class FeeCountVO {
    /**
     * 层级码
     */
    private String memOrgOrgCode;
    /**
     * 组织名称
     */
    private String orgName;
    /**
     * 组织简称
     */
    private String orgShortName;

    /**
     * 12月数据
     */
    private List<FeeVO> feeVOList;

}
