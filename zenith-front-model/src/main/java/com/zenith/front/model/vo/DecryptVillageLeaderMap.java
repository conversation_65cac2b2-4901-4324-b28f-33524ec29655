package com.zenith.front.model.vo;

import com.zenith.front.common.encrypt.annotation.EncryptEnabled;
import com.zenith.front.common.encrypt.annotation.EncryptField;
import lombok.Getter;
import lombok.Setter;

import java.util.LinkedHashMap;

/**
 * 兼容返回类型为map时数据解密,村两委班子信息
 *
 * <AUTHOR>
 * @version V1.0
 * @date 2022/10/21 16:10
 */
@EncryptEnabled
@Setter
@Getter
public class DecryptVillageLeaderMap<K, V> extends LinkedHashMap<K, V> {

    @EncryptField(order = 1)
    private String a0101;
    @EncryptField(order = 2)
    private String a0192a;

}
