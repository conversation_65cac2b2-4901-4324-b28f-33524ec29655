package com.zenith.front.model.bean;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import java.util.Date;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableField;
import java.io.Serializable;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2021-07-11
 */
@TableName("table_ruler")
public class TableRuler extends Model<TableRuler> {

    private static final long serialVersionUID=1L;

    /**
     * 唯一自增长主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 校验规则所属表id
     */
    @TableField("table")
    private String table;

    /**
     * 规则表名称
     */
    @TableField("table_name")
    private String tableName;

    /**
     * 表内还是标间关系（1是表内，2是表间）
     */
    @TableField("ruler_type")
    private Integer rulerType;

    /**
     * 规则描述
     */
    @TableField("remark")
    private String remark;

    /**
     * 校验规则
     */
    @TableField("ruler")
    private String ruler;

    /**
     * 是否删除（0否1是2是表间，3是除法暂存）
     */
    @TableField("is_del")
    private Integer isDel;

    /**
     * 创建时间
     */
    @TableField("createTime")
    private Date createTime;

    /**
     * 类型（1=公务员,2=参公事业,3=参公群团,4=地方班子成员,5=事业单位）
     */
    @TableField("type")
    private String type;

    /**
     * 过滤条件
     */
    @TableField("filter")
    private String filter;

    /**
     * 前端请求数据,接受前端拆分的原数据
     */
    @TableField("front_data")
    private String frontData;

    /**
     * 拆分的前端的数据统一次数标识符
     */
    @TableField("uuid")
    private String uuid;

    /**
     * 是否拉通行
     */
    @TableField("isRowAll")
    private Integer isRowAll;

    /**
     * 是否拉通列
     */
    @TableField("isColumnAll")
    private Integer isColumnAll;

    /**
     * 规则描述
     */
    @TableField("table_ruler")
    private String tableRuler;

    /**
     * 连接表
     */
    @TableField("connection_table")
    private String connectionTable;

    /**
     * 拆分表
     */
    @TableField("spit_row")
    private Integer spitRow;

    /**
     * 拆分列数字
     */
    @TableField("spit_column")
    private Integer spitColumn;

    /**
     * 1=行列配置2=特殊配置（单个空）
     */
    @TableField("table_type")
    private Integer tableType;

    /**
     * 是否强制校核规则
     */
    @TableField("is_forcibly")
    private Integer isForcibly;


    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getTable() {
        return table;
    }

    public void setTable(String table) {
        this.table = table;
    }

    public String getTableName() {
        return tableName;
    }

    public void setTableName(String tableName) {
        this.tableName = tableName;
    }

    public Integer getRulerType() {
        return rulerType;
    }

    public void setRulerType(Integer rulerType) {
        this.rulerType = rulerType;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public String getRuler() {
        return ruler;
    }

    public void setRuler(String ruler) {
        this.ruler = ruler;
    }

    public Integer getIsDel() {
        return isDel;
    }

    public void setIsDel(Integer isDel) {
        this.isDel = isDel;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public String getFilter() {
        return filter;
    }

    public void setFilter(String filter) {
        this.filter = filter;
    }

    public String getFrontData() {
        return frontData;
    }

    public void setFrontData(String frontData) {
        this.frontData = frontData;
    }

    public String getUuid() {
        return uuid;
    }

    public void setUuid(String uuid) {
        this.uuid = uuid;
    }

    public Integer getIsRowAll() {
        return isRowAll;
    }

    public void setIsRowAll(Integer isRowAll) {
        this.isRowAll = isRowAll;
    }

    public Integer getIsColumnAll() {
        return isColumnAll;
    }

    public void setIsColumnAll(Integer isColumnAll) {
        this.isColumnAll = isColumnAll;
    }

    public String getTableRuler() {
        return tableRuler;
    }

    public void setTableRuler(String tableRuler) {
        this.tableRuler = tableRuler;
    }

    public String getConnectionTable() {
        return connectionTable;
    }

    public void setConnectionTable(String connectionTable) {
        this.connectionTable = connectionTable;
    }

    public Integer getSpitRow() {
        return spitRow;
    }

    public void setSpitRow(Integer spitRow) {
        this.spitRow = spitRow;
    }

    public Integer getSpitColumn() {
        return spitColumn;
    }

    public void setSpitColumn(Integer spitColumn) {
        this.spitColumn = spitColumn;
    }

    public Integer getTableType() {
        return tableType;
    }

    public void setTableType(Integer tableType) {
        this.tableType = tableType;
    }

    public Integer getIsForcibly() {
        return isForcibly;
    }

    public void setIsForcibly(Integer isForcibly) {
        this.isForcibly = isForcibly;
    }

    @Override
    public Serializable pkVal() {
        return this.id;
    }

    @Override
    public String toString() {
        return "TableRuler{" +
                "id=" + id +
                ", table='" + table + '\'' +
                ", tableName='" + tableName + '\'' +
                ", rulerType=" + rulerType +
                ", remark='" + remark + '\'' +
                ", ruler='" + ruler + '\'' +
                ", isDel=" + isDel +
                ", createTime=" + createTime +
                ", type='" + type + '\'' +
                ", filter='" + filter + '\'' +
                ", frontData='" + frontData + '\'' +
                ", uuid='" + uuid + '\'' +
                ", isRowAll=" + isRowAll +
                ", isColumnAll=" + isColumnAll +
                ", tableRuler='" + tableRuler + '\'' +
                ", connectionTable='" + connectionTable + '\'' +
                ", spitRow=" + spitRow +
                ", spitColumn=" + spitColumn +
                ", tableType=" + tableType +
                ", isForcibly=" + isForcibly +
                '}';
    }
}
