package com.zenith.front.model.bean;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import java.util.Date;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableField;
import java.io.Serializable;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2021-07-11
 */
@TableName("check_table_event")
public class CheckTableEvent extends Model<CheckTableEvent> {

    private static final long serialVersionUID=1L;

    /**
     * 自增长id
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 规则表id
     */
    @TableField("ruler_id")
    private Integer rulerId;

    /**
     * 校核表名称
     */
    @TableField("table")
    private String table;

    /**
     * 校核规则表描述
     */
    @TableField("remark")
    private String remark;

    /**
     * 涉及的表行
     */
    @TableField("table_row")
    private Integer tableRow;

    /**
     * 涉及的表列
     */
    @TableField("table_cloum")
    private Integer tableCloum;

    /**
     * 展示错误信息语句
     */
    @TableField("all_remark")
    private String allRemark;

    /**
     * 校核时间
     */
    @TableField("check_time")
    private Date checkTime;

    /**
     * 创建时间
     */
    @TableField("create_time")
    private Date createTime;

    /**
     * 校核单位id
     */
    @TableField("unit_id")
    private String unitId;

    /**
     * 套表type
     */
    @TableField("type")
    private String type;

    /**
     * 计算公式左边值
     */
    @TableField("left_value")
    private String leftValue;

    /**
     * 计算公式右边值
     */
    @TableField("right_value")
    private String rightValue;

    /**
     * 匹配方式
     */
    @TableField("check_type")
    private String checkType;


    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Integer getRulerId() {
        return rulerId;
    }

    public void setRulerId(Integer rulerId) {
        this.rulerId = rulerId;
    }

    public String getTable() {
        return table;
    }

    public void setTable(String table) {
        this.table = table;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public Integer getTableRow() {
        return tableRow;
    }

    public void setTableRow(Integer tableRow) {
        this.tableRow = tableRow;
    }

    public Integer getTableCloum() {
        return tableCloum;
    }

    public void setTableCloum(Integer tableCloum) {
        this.tableCloum = tableCloum;
    }

    public String getAllRemark() {
        return allRemark;
    }

    public void setAllRemark(String allRemark) {
        this.allRemark = allRemark;
    }

    public Date getCheckTime() {
        return checkTime;
    }

    public void setCheckTime(Date checkTime) {
        this.checkTime = checkTime;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public String getUnitId() {
        return unitId;
    }

    public void setUnitId(String unitId) {
        this.unitId = unitId;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public String getLeftValue() {
        return leftValue;
    }

    public void setLeftValue(String leftValue) {
        this.leftValue = leftValue;
    }

    public String getRightValue() {
        return rightValue;
    }

    public void setRightValue(String rightValue) {
        this.rightValue = rightValue;
    }

    public String getCheckType() {
        return checkType;
    }

    public void setCheckType(String checkType) {
        this.checkType = checkType;
    }

    @Override
    public Serializable pkVal() {
        return this.id;
    }

    @Override
    public String toString() {
        return "CheckTableEvent{" +
        "id=" + id +
        ", rulerId=" + rulerId +
        ", table=" + table +
        ", remark=" + remark +
        ", tableRow=" + tableRow +
        ", tableCloum=" + tableCloum +
        ", allRemark=" + allRemark +
        ", checkTime=" + checkTime +
        ", createTime=" + createTime +
        ", unitId=" + unitId +
        ", type=" + type +
        ", leftValue=" + leftValue +
        ", rightValue=" + rightValue +
        ", checkType=" + checkType +
        "}";
    }
}
