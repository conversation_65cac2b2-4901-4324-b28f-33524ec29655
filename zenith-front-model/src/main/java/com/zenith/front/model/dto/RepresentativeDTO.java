package com.zenith.front.model.dto;

import com.zenith.front.model.bean.Representative;
import com.zenith.front.model.validate.group.Common1Group;
import com.zenith.front.model.validate.group.UpdateGroup;
import lombok.ToString;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

@ToString
public class RepresentativeDTO {

    private Long id;
    @NotBlank(groups = UpdateGroup.class, message = "code 不能为空")
    private String code;
    private String esId;
    /**
     * 党员名称
     */
    @NotBlank(groups = Common1Group.class, message = "memName 不能为空")
    private String memName;
    @NotBlank(groups = Common1Group.class, message = "memCode 不能为空")
    private String memCode;
    /**
     * 党员出生日期
     */
    private java.util.Date memBirthday;
    /**
     * 党员学历代码
     */
    private String d07Code;
    /**
     * 党员性别代码
     */
    private String sexCode;
    /**
     * 党员民族代码
     */
    private String d06Code;
    /**
     * 党员一线情况代码
     */
    private String d21Code;
    /**
     * 任职时所在党支部code
     */
    @NotBlank(groups = Common1Group.class, message = "orgCode 不能为空")
    private String orgCode;
    /**
     * 任职时所在党支部层级码
     */
    @NotBlank(groups = Common1Group.class, message = "representativeOrgCode 不能为空")
    private String representativeOrgCode;
    @NotBlank(groups = Common1Group.class, message = "orgName 不能为空")
    private String orgName;
    /**
     * 所在选举单位code
     */
    @NotBlank(groups = Common1Group.class, message = "unitCode 不能为空")
    private String unitCode;
    @NotBlank(groups = Common1Group.class, message = "unitName 不能为空")
    private String unitName;
    /**
     * 任职届次开始时间
     */
    private java.util.Date electStartDate;
    /**
     * 任职届次code
     */
    @NotBlank(groups = Common1Group.class, message = "electCode 不能为空")
    private String electCode;
    /**
     * 任职届次名字
     */
    @NotBlank(groups = Common1Group.class, message = "electName 不能为空")
    private String electName;
    /**
     * 任职届次类型名称(1:全国,2:市,3:区,4:县,5:乡,6:镇)
     */
    @NotBlank(groups = Common1Group.class, message = "d61Code 不能为空")
    private String d61Code;
    @NotBlank(groups = Common1Group.class, message = "d61Name 不能为空")
    private String d61Name;
    /**
     * 当选类型
     */
    @NotBlank(groups = Common1Group.class, message = "d44Code 不能为空")
    private String d44Code;
    @NotBlank(groups = Common1Group.class, message = "d44Name 不能为空")
    private String d44Name;
    /**
     * 当选时身份
     */
    @NotBlank(groups = Common1Group.class, message = "d46Code 不能为空")
    private String d46Code;
    @NotBlank(groups = Common1Group.class, message = "d46Name 不能为空")
    private String d46Name;
    /**
     * 当选是职务
     */
    @NotBlank(groups = Common1Group.class, message = "positionName 不能为空")
    private String positionName;
    /**
     * 是否是委员
     */
    @NotNull(groups = Common1Group.class, message = "isMember 不能为空")
    private Integer isMember;
    /**
     * 是否先进模范人
     */
    @NotNull(groups = Common1Group.class, message = "isAdvanced 不能为空")
    private Integer isAdvanced;
    /**
     * 是否参与各级党组织培训
     */
    @NotNull(groups = Common1Group.class, message = "isTeach 不能为空")
    private Integer isTeach;
    /**
     * 代表资格状态 1:正常 2:终止代表资格 3:停止执行党代表大会代表职务
     */
    @NotNull(groups = Common1Group.class, message = "isTeach 不能为空")
    private String d45Code;
    @NotNull(groups = Common1Group.class, message = "isTeach 不能为空")
    private String d45Name;
    /**
     * 终止资格类型
     */
    private String d24Code;
    private String d24Name;
    /**
     * 终止资格备注
     */
    private String zbStopRemark;
    /**
     * 终止资格日期
     */
    private java.util.Date zbStopDate;
    /**
     * 干部管理单位(领导干部下必填)
     */
    private String gbManageUnit;
    /**
     * 当选日期
     */
    @NotNull(groups = Common1Group.class, message = "startDate 不能为空")
    private java.util.Date startDate;
    private java.util.Date createTime;
    private java.util.Date updateTime;
    private java.util.Date timestamp;
    private java.util.Date deleteTime;
    private String zbCode;
    private Integer isHistory;
    private String updateAccount;

    public RepresentativeDTO setId(Long id) {
        this.id = id;
        return this;
    }

    public Long getId() {
        return this.id;
    }

    public RepresentativeDTO setCode(String code) {
        this.code = code;
        return this;
    }

    public String getCode() {
        return this.code;
    }

    public RepresentativeDTO setEsId(String esId) {
        this.esId = esId;
        return this;
    }

    public String getEsId() {
        return this.esId;
    }

    public RepresentativeDTO setMemName(String memName) {
        this.memName = memName;
        return this;
    }

    public String getMemName() {
        return this.memName;
    }

    public RepresentativeDTO setMemCode(String memCode) {
        this.memCode = memCode;
        return this;
    }

    public String getMemCode() {
        return this.memCode;
    }

    public RepresentativeDTO setMemBirthday(java.util.Date memBirthday) {
        this.memBirthday = memBirthday;
        return this;
    }

    public java.util.Date getMemBirthday() {
        return this.memBirthday;
    }

    public RepresentativeDTO setD07Code(String d07Code) {
        this.d07Code = d07Code;
        return this;
    }

    public String getD07Code() {
        return this.d07Code;
    }

    public RepresentativeDTO setSexCode(String sexCode) {
        this.sexCode = sexCode;
        return this;
    }

    public String getSexCode() {
        return this.sexCode;
    }

    public RepresentativeDTO setD06Code(String d06Code) {
        this.d06Code = d06Code;
        return this;
    }

    public String getD06Code() {
        return this.d06Code;
    }

    public RepresentativeDTO setD21Code(String d21Code) {
        this.d21Code = d21Code;
        return this;
    }

    public String getD21Code() {
        return this.d21Code;
    }

    public RepresentativeDTO setOrgCode(String orgCode) {
        this.orgCode = orgCode;
        return this;
    }

    public String getOrgCode() {
        return this.orgCode;
    }

    public RepresentativeDTO setRepresentativeOrgCode(String representativeOrgCode) {
        this.representativeOrgCode = representativeOrgCode;
        return this;
    }

    public String getRepresentativeOrgCode() {
        return this.representativeOrgCode;
    }

    public RepresentativeDTO setOrgName(String orgName) {
        this.orgName = orgName;
        return this;
    }

    public String getOrgName() {
        return this.orgName;
    }

    public RepresentativeDTO setUnitCode(String unitCode) {
        this.unitCode = unitCode;
        return this;
    }

    public String getUnitCode() {
        return this.unitCode;
    }

    public RepresentativeDTO setUnitName(String unitName) {
        this.unitName = unitName;
        return this;
    }

    public String getUnitName() {
        return this.unitName;
    }

    public RepresentativeDTO setElectStartDate(java.util.Date electStartDate) {
        this.electStartDate = electStartDate;
        return this;
    }

    public java.util.Date getElectStartDate() {
        return this.electStartDate;
    }

    public RepresentativeDTO setElectCode(String electCode) {
        this.electCode = electCode;
        return this;
    }

    public String getElectCode() {
        return this.electCode;
    }

    public RepresentativeDTO setElectName(String electName) {
        this.electName = electName;
        return this;
    }

    public String getElectName() {
        return this.electName;
    }

    public RepresentativeDTO setD61Code(String d61Code) {
        this.d61Code = d61Code;
        return this;
    }

    public String getD61Code() {
        return this.d61Code;
    }

    public RepresentativeDTO setD61Name(String d61Name) {
        this.d61Name = d61Name;
        return this;
    }

    public String getD61Name() {
        return this.d61Name;
    }

    public RepresentativeDTO setD44Code(String d44Code) {
        this.d44Code = d44Code;
        return this;
    }

    public String getD44Code() {
        return this.d44Code;
    }

    public RepresentativeDTO setD44Name(String d44Name) {
        this.d44Name = d44Name;
        return this;
    }

    public String getD44Name() {
        return this.d44Name;
    }

    public RepresentativeDTO setD46Code(String d46Code) {
        this.d46Code = d46Code;
        return this;
    }

    public String getD46Code() {
        return this.d46Code;
    }

    public RepresentativeDTO setD46Name(String d46Name) {
        this.d46Name = d46Name;
        return this;
    }

    public String getD46Name() {
        return this.d46Name;
    }

    public RepresentativeDTO setPositionName(String positionName) {
        this.positionName = positionName;
        return this;
    }

    public String getPositionName() {
        return this.positionName;
    }

    public RepresentativeDTO setIsMember(Integer isMember) {
        this.isMember = isMember;
        return this;
    }

    public Integer getIsMember() {
        return this.isMember;
    }

    public RepresentativeDTO setIsAdvanced(Integer isAdvanced) {
        this.isAdvanced = isAdvanced;
        return this;
    }

    public Integer getIsAdvanced() {
        return this.isAdvanced;
    }

    public RepresentativeDTO setIsTeach(Integer isTeach) {
        this.isTeach = isTeach;
        return this;
    }

    public Integer getIsTeach() {
        return this.isTeach;
    }

    public RepresentativeDTO setD45Code(String d45Code) {
        this.d45Code = d45Code;
        return this;
    }

    public String getD45Code() {
        return this.d45Code;
    }

    public RepresentativeDTO setD45Name(String d45Name) {
        this.d45Name = d45Name;
        return this;
    }

    public String getD45Name() {
        return this.d45Name;
    }

    public RepresentativeDTO setD24Code(String d24Code) {
        this.d24Code = d24Code;
        return this;
    }

    public String getD24Code() {
        return this.d24Code;
    }

    public RepresentativeDTO setD24Name(String d24Name) {
        this.d24Name = d24Name;
        return this;
    }

    public String getD24Name() {
        return this.d24Name;
    }

    public RepresentativeDTO setZbStopRemark(String zbStopRemark) {
        this.zbStopRemark = zbStopRemark;
        return this;
    }

    public String getZbStopRemark() {
        return this.zbStopRemark;
    }

    public RepresentativeDTO setZbStopDate(java.util.Date zbStopDate) {
        this.zbStopDate = zbStopDate;
        return this;
    }

    public java.util.Date getZbStopDate() {
        return this.zbStopDate;
    }

    public RepresentativeDTO setGbManageUnit(String gbManageUnit) {
        this.gbManageUnit = gbManageUnit;
        return this;
    }

    public String getGbManageUnit() {
        return this.gbManageUnit;
    }

    public RepresentativeDTO setStartDate(java.util.Date startDate) {
        this.startDate = startDate;
        return this;
    }

    public java.util.Date getStartDate() {
        return this.startDate;
    }

    public RepresentativeDTO setCreateTime(java.util.Date createTime) {
        this.createTime = createTime;
        return this;
    }

    public java.util.Date getCreateTime() {
        return this.createTime;
    }

    public RepresentativeDTO setUpdateTime(java.util.Date updateTime) {
        this.updateTime = updateTime;
        return this;
    }

    public java.util.Date getUpdateTime() {
        return this.updateTime;
    }

    public RepresentativeDTO setTimestamp(java.util.Date timestamp) {
        this.timestamp = timestamp;
        return this;
    }

    public java.util.Date getTimestamp() {
        return this.timestamp;
    }

    public RepresentativeDTO setDeleteTime(java.util.Date deleteTime) {
        this.deleteTime = deleteTime;
        return this;
    }

    public java.util.Date getDeleteTime() {
        return this.deleteTime;
    }

    public RepresentativeDTO setZbCode(String zbCode) {
        this.zbCode = zbCode;
        return this;
    }

    public String getZbCode() {
        return this.zbCode;
    }

    public RepresentativeDTO setIsHistory(Integer isHistory) {
        this.isHistory = isHistory;
        return this;
    }

    public Integer getIsHistory() {
        return this.isHistory;
    }

    public RepresentativeDTO setUpdateAccount(String updateAccount) {
        this.updateAccount = updateAccount;
        return this;
    }

    public String getUpdateAccount() {
        return this.updateAccount;
    }

    public Representative toModel() {
        Representative model = new Representative();
        model.setId(this.id);
        model.setCode(this.code);
        model.setEsId(this.esId);
        model.setMemName(this.memName);
        model.setMemCode(this.memCode);
        model.setMemBirthday(this.memBirthday);
        model.setD07Code(this.d07Code);
        model.setSexCode(this.sexCode);
        model.setD06Code(this.d06Code);
        model.setD21Code(this.d21Code);
        model.setOrgCode(this.orgCode);
        model.setRepresentativeOrgCode(this.representativeOrgCode);
        model.setOrgName(this.orgName);
        model.setUnitCode(this.unitCode);
        model.setUnitName(this.unitName);
        model.setElectStartDate(this.electStartDate);
        model.setElectCode(this.electCode);
        model.setElectName(this.electName);
        model.setD61Code(this.d61Code);
        model.setD61Name(this.d61Name);
        model.setD44Code(this.d44Code);
        model.setD44Name(this.d44Name);
        model.setD46Code(this.d46Code);
        model.setD46Name(this.d46Name);
        model.setPositionName(this.positionName);
        model.setIsMember(this.isMember);
        model.setIsAdvanced(this.isAdvanced);
        model.setIsTeach(this.isTeach);
        model.setD45Code(this.d45Code);
        model.setD45Name(this.d45Name);
        model.setD24Code(this.d24Code);
        model.setD24Name(this.d24Name);
        model.setZbStopRemark(this.zbStopRemark);
        model.setZbStopDate(this.zbStopDate);
        model.setGbManageUnit(this.gbManageUnit);
        model.setStartDate(this.startDate);
        model.setCreateTime(this.createTime);
        model.setUpdateTime(this.updateTime);
        model.setTimestamp(this.timestamp);
        model.setDeleteTime(this.deleteTime);
        model.setZbCode(this.zbCode);
        model.setIsHistory(this.isHistory);
        model.setUpdateAccount(this.updateAccount);
        return model;
    }
}