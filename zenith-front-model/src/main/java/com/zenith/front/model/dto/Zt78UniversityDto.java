package com.zenith.front.model.dto;

import com.zenith.front.model.validate.group.AddGroup;

import javax.validation.constraints.NotBlank;

/**
 * 专题调查表七八
 *
 * <AUTHOR>
 * @date 2021/08/13
 */
public class Zt78UniversityDto {

    /**
     * 单位code
     */
    @NotBlank(groups = AddGroup.class, message = "unitCode 不能为空")
    private String unitCode;

    /**
     * 设常委会的(0：否  1：是)
     */
    private Integer hasStandCommittee;

    /**
     * 向地方党委和主管部委专题报告党委领导下的校长负责制执行情况的
     */
    private Integer specialReport;

    /**
     * 修订党委全委会、常委会和校长办公会议事规则的
     */
    private Integer revisedRules;

    /**
     * 学校党委书记向地方党委述职的
     */
    private Integer secretaryToDistrict;

    /**
     * 组织开展二级院（系）党组织书记向学校党委述职的
     */
    private Integer secretaryToSchool;

    /**
     * 校长系中共党员的
     */
    private Integer partyMember;

    /**
     * 校长担任党委副书记的
     */
    private Integer deputySecretary;

    /**
     * 纪委书记任职情况担任党委常委的 (0：否  1：是)
     */
    private Integer hasSecretaryOffice;

    /**
     * 纪委书记任职情况担任不设常委会的党委委员的(0：否  1：是)
     */
    private Integer hasSecretaryNoOffice;

    /**
     * 组织部长任职情况担任党委常委的(0：否  1：是)
     */
    private Integer hasMinisterOffice;

    /**
     * 组织部长任职情况担任不设常委会的党委委员的(0：否  1：是)
     */
    private Integer hasMinisterNoOffice;

    /**
     * 宣传部长任职情况担任党委常委的(0：否  1：是)
     */
    private Integer hasPropagandaOffice;

    /**
     * 宣传部长任职情况担任不设常委会的党委委员的 (0：否  1：是)
     */
    private Integer hasPropagandaNoOffice;

    /**
     * 统战部长任职情况担任党委常委的 (0：否  1：是)
     */
    private Integer hasUniteOffice;

    /**
     * 统战部长任职情况担任不设常委会的党委委员的 (0：否  1：是)
     */
    private Integer hasUniteNoOffice;

    /**
     * 二级院（系）（0否，1是）
     */
    private Integer isAffiliatedCollege;

    /**
     * 二级院系配备专职组织员
     */
    private String d82Code;

    /**
     * 二级院系配备专职组织员名称
     */
    private String d82Name;

    /**
     * 本年度院系党委书记参加培训人次
     */
    private Integer yearYxdwsjJoinTrainNum;

    /**
     * 本年度党支部书记参加培训人次
     */
    private Integer yearDzbsjJoinTrainNum;

    /**
     * 本年度任届期满的院系党支部
     */
    private Integer yearRqjmdyxdzbsjNum;

    /**
     * 本年换届（0否，1是）
     */
    private Integer isYearChangeSecret;

    /**
     * 本年度发展党员
     */
    private Integer yearDevelopParty;

    /**
     * 本年度发展教师党员
     */
    private Integer yearDevelopTeacherParty;

    /**
     * 本年度发展学生党员
     */
    private Integer yearDevelopStudentParty;

    /**
     * 本年度毕业生党员
     */
    private Integer yearDevelopGraduateParty;

    /**
     * 尚未转出组织关系的（0否，1是）
     */
    private Integer isNotTurnedRelation;

    /**
     * 累积未转出组织关系的毕业生党员
     */
    private Integer notTurnedRelationBysdyNum;

    public String getUnitCode() {
        return unitCode;
    }

    public void setUnitCode(String unitCode) {
        this.unitCode = unitCode;
    }

    public Integer getHasStandCommittee() {
        return hasStandCommittee;
    }

    public void setHasStandCommittee(Integer hasStandCommittee) {
        this.hasStandCommittee = hasStandCommittee;
    }

    public Integer getSpecialReport() {
        return specialReport;
    }

    public void setSpecialReport(Integer specialReport) {
        this.specialReport = specialReport;
    }

    public Integer getRevisedRules() {
        return revisedRules;
    }

    public void setRevisedRules(Integer revisedRules) {
        this.revisedRules = revisedRules;
    }

    public Integer getSecretaryToDistrict() {
        return secretaryToDistrict;
    }

    public void setSecretaryToDistrict(Integer secretaryToDistrict) {
        this.secretaryToDistrict = secretaryToDistrict;
    }

    public Integer getSecretaryToSchool() {
        return secretaryToSchool;
    }

    public void setSecretaryToSchool(Integer secretaryToSchool) {
        this.secretaryToSchool = secretaryToSchool;
    }

    public Integer getPartyMember() {
        return partyMember;
    }

    public void setPartyMember(Integer partyMember) {
        this.partyMember = partyMember;
    }

    public Integer getDeputySecretary() {
        return deputySecretary;
    }

    public void setDeputySecretary(Integer deputySecretary) {
        this.deputySecretary = deputySecretary;
    }

    public Integer getHasSecretaryOffice() {
        return hasSecretaryOffice;
    }

    public void setHasSecretaryOffice(Integer hasSecretaryOffice) {
        this.hasSecretaryOffice = hasSecretaryOffice;
    }

    public Integer getHasSecretaryNoOffice() {
        return hasSecretaryNoOffice;
    }

    public void setHasSecretaryNoOffice(Integer hasSecretaryNoOffice) {
        this.hasSecretaryNoOffice = hasSecretaryNoOffice;
    }

    public Integer getHasMinisterOffice() {
        return hasMinisterOffice;
    }

    public void setHasMinisterOffice(Integer hasMinisterOffice) {
        this.hasMinisterOffice = hasMinisterOffice;
    }

    public Integer getHasMinisterNoOffice() {
        return hasMinisterNoOffice;
    }

    public void setHasMinisterNoOffice(Integer hasMinisterNoOffice) {
        this.hasMinisterNoOffice = hasMinisterNoOffice;
    }

    public Integer getHasPropagandaOffice() {
        return hasPropagandaOffice;
    }

    public void setHasPropagandaOffice(Integer hasPropagandaOffice) {
        this.hasPropagandaOffice = hasPropagandaOffice;
    }

    public Integer getHasPropagandaNoOffice() {
        return hasPropagandaNoOffice;
    }

    public void setHasPropagandaNoOffice(Integer hasPropagandaNoOffice) {
        this.hasPropagandaNoOffice = hasPropagandaNoOffice;
    }

    public Integer getHasUniteOffice() {
        return hasUniteOffice;
    }

    public void setHasUniteOffice(Integer hasUniteOffice) {
        this.hasUniteOffice = hasUniteOffice;
    }

    public Integer getHasUniteNoOffice() {
        return hasUniteNoOffice;
    }

    public void setHasUniteNoOffice(Integer hasUniteNoOffice) {
        this.hasUniteNoOffice = hasUniteNoOffice;
    }

    public Integer getIsAffiliatedCollege() {
        return isAffiliatedCollege;
    }

    public void setIsAffiliatedCollege(Integer isAffiliatedCollege) {
        this.isAffiliatedCollege = isAffiliatedCollege;
    }

    public String getd82Code() {
        return d82Code;
    }

    public void setd82Code(String d82Code) {
        this.d82Code = d82Code;
    }

    public String getd82Name() {
        return d82Name;
    }

    public void setd82Name(String d82Name) {
        this.d82Name = d82Name;
    }

    public Integer getYearYxdwsjJoinTrainNum() {
        return yearYxdwsjJoinTrainNum;
    }

    public void setYearYxdwsjJoinTrainNum(Integer yearYxdwsjJoinTrainNum) {
        this.yearYxdwsjJoinTrainNum = yearYxdwsjJoinTrainNum;
    }

    public Integer getYearDzbsjJoinTrainNum() {
        return yearDzbsjJoinTrainNum;
    }

    public void setYearDzbsjJoinTrainNum(Integer yearDzbsjJoinTrainNum) {
        this.yearDzbsjJoinTrainNum = yearDzbsjJoinTrainNum;
    }

    public Integer getYearRqjmdyxdzbsjNum() {
        return yearRqjmdyxdzbsjNum;
    }

    public void setYearRqjmdyxdzbsjNum(Integer yearRqjmdyxdzbsjNum) {
        this.yearRqjmdyxdzbsjNum = yearRqjmdyxdzbsjNum;
    }

    public Integer getIsYearChangeSecret() {
        return isYearChangeSecret;
    }

    public void setIsYearChangeSecret(Integer isYearChangeSecret) {
        this.isYearChangeSecret = isYearChangeSecret;
    }

    public Integer getYearDevelopParty() {
        return yearDevelopParty;
    }

    public void setYearDevelopParty(Integer yearDevelopParty) {
        this.yearDevelopParty = yearDevelopParty;
    }

    public Integer getYearDevelopTeacherParty() {
        return yearDevelopTeacherParty;
    }

    public void setYearDevelopTeacherParty(Integer yearDevelopTeacherParty) {
        this.yearDevelopTeacherParty = yearDevelopTeacherParty;
    }

    public Integer getYearDevelopStudentParty() {
        return yearDevelopStudentParty;
    }

    public void setYearDevelopStudentParty(Integer yearDevelopStudentParty) {
        this.yearDevelopStudentParty = yearDevelopStudentParty;
    }

    public Integer getYearDevelopGraduateParty() {
        return yearDevelopGraduateParty;
    }

    public void setYearDevelopGraduateParty(Integer yearDevelopGraduateParty) {
        this.yearDevelopGraduateParty = yearDevelopGraduateParty;
    }

    public Integer getIsNotTurnedRelation() {
        return isNotTurnedRelation;
    }

    public void setIsNotTurnedRelation(Integer isNotTurnedRelation) {
        this.isNotTurnedRelation = isNotTurnedRelation;
    }

    public Integer getNotTurnedRelationBysdyNum() {
        return notTurnedRelationBysdyNum;
    }

    public void setNotTurnedRelationBysdyNum(Integer notTurnedRelationBysdyNum) {
        this.notTurnedRelationBysdyNum = notTurnedRelationBysdyNum;
    }

}
