package com.zenith.front.model.bean;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import java.util.Date;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableField;

import java.io.Serializable;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2021-07-11
 */
@TableName("ccp_representative_contact")
public class RepresentativeContact extends Model<RepresentativeContact> {

    private static final long serialVersionUID=1L;

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    @TableField("code")
    private String code;

    @TableField("es_id")
    private String esId;

    /**
     * 机构名称
     */
    @TableField("contact_name")
    private String contactName;

    /**
     * 党组织code
     */
    @TableField("contact_org_code")
    private String contactOrgCode;

    /**
     * 党组织层级码
     */
    @TableField("org_code")
    private String orgCode;

    /**
     * 党组织名称

     */
    @TableField("org_name")
    private String orgName;

    /**
     * 党组织类型
     */
    @TableField("d01_code")
    private String d01Code;

    /**
     * 党组织隶属关系
     */
    @TableField("d03_code")
    private String d03Code;

    /**
     * 机构级别
     */
    @TableField("d62_name")
    private String d62Name;

    @TableField("d62_code")
    private String d62Code;

    /**
     * 编制数
     */
    @TableField("bz_num")
    private Long bzNum;

    @TableField("timestamp")
    private Date timestamp;

    @TableField("delete_time")
    private Date deleteTime;

    @TableField("update_time")
    private Date updateTime;

    @TableField("create_time")
    private Date createTime;

    @TableField("zb_code")
    private String zbCode;

    @TableField("is_history")
    private Integer isHistory;

    @TableField("update_account")
    private String updateAccount;


    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getEsId() {
        return esId;
    }

    public void setEsId(String esId) {
        this.esId = esId;
    }

    public String getContactName() {
        return contactName;
    }

    public void setContactName(String contactName) {
        this.contactName = contactName;
    }

    public String getContactOrgCode() {
        return contactOrgCode;
    }

    public void setContactOrgCode(String contactOrgCode) {
        this.contactOrgCode = contactOrgCode;
    }

    public String getOrgCode() {
        return orgCode;
    }

    public void setOrgCode(String orgCode) {
        this.orgCode = orgCode;
    }

    public String getOrgName() {
        return orgName;
    }

    public void setOrgName(String orgName) {
        this.orgName = orgName;
    }

    public String getD01Code() {
        return d01Code;
    }

    public void setD01Code(String d01Code) {
        this.d01Code = d01Code;
    }

    public String getD03Code() {
        return d03Code;
    }

    public void setD03Code(String d03Code) {
        this.d03Code = d03Code;
    }

    public String getD62Name() {
        return d62Name;
    }

    public void setD62Name(String d62Name) {
        this.d62Name = d62Name;
    }

    public String getD62Code() {
        return d62Code;
    }

    public void setD62Code(String d62Code) {
        this.d62Code = d62Code;
    }

    public Long getBzNum() {
        return bzNum;
    }

    public void setBzNum(Long bzNum) {
        this.bzNum = bzNum;
    }

    public Date getTimestamp() {
        return timestamp;
    }

    public void setTimestamp(Date timestamp) {
        this.timestamp = timestamp;
    }

    public Date getDeleteTime() {
        return deleteTime;
    }

    public void setDeleteTime(Date deleteTime) {
        this.deleteTime = deleteTime;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public String getZbCode() {
        return zbCode;
    }

    public void setZbCode(String zbCode) {
        this.zbCode = zbCode;
    }

    public Integer getIsHistory() {
        return isHistory;
    }

    public void setIsHistory(Integer isHistory) {
        this.isHistory = isHistory;
    }

    public String getUpdateAccount() {
        return updateAccount;
    }

    public void setUpdateAccount(String updateAccount) {
        this.updateAccount = updateAccount;
    }

    @Override
    public Serializable pkVal() {
        return this.id;
    }

    @Override
    public String toString() {
        return "RepresentativeContact{" +
        "id=" + id +
        ", code=" + code +
        ", esId=" + esId +
        ", contactName=" + contactName +
        ", contactOrgCode=" + contactOrgCode +
        ", orgCode=" + orgCode +
        ", orgName=" + orgName +
        ", d01Code=" + d01Code +
        ", d03Code=" + d03Code +
        ", d62Name=" + d62Name +
        ", d62Code=" + d62Code +
        ", bzNum=" + bzNum +
        ", timestamp=" + timestamp +
        ", deleteTime=" + deleteTime +
        ", updateTime=" + updateTime +
        ", createTime=" + createTime +
        ", zbCode=" + zbCode +
        ", isHistory=" + isHistory +
        ", updateAccount=" + updateAccount +
        "}";
    }
}
