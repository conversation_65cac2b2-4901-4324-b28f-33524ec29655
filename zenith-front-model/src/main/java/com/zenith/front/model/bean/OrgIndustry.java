package com.zenith.front.model.bean;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableField;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 * 行业党组织
 * </p>
 *
 * <AUTHOR>
 * @since 2021-11-16
 */
@TableName("ccp_org_industry")
public class OrgIndustry extends Model<OrgIndustry> {

    private static final long serialVersionUID = 1L;

    /**
     * 唯一标识符code
     */
      @TableId(value = "code", type = IdType.ASSIGN_UUID)
    private String code;

    /**
     * 党组织唯一组织code
     */
    @TableField("org_code")
    private String orgCode;

    /**
     * 党组织组织层级码
     */
    @TableField("industry_org_code")
    private String industryOrgCode;

    /**
     * 组织名称
     */
    @TableField("industry_org_name")
    private String industryOrgName;

    /**
     * 组织类别
     */
    @TableField("industry_org_type")
    private String industryOrgType;

    /**
     * 行业分类
     */
    @TableField("industry_classification")
    private String industryClassification;

    /**
     * 所属层级
     */
    @TableField("subordinate_level")
    private String subordinateLevel;

    /**
     * 隶属关系
     */
    @TableField("membership_function")
    private String membershipFunction;

    /**
     * 书记是否由行业主管部门党员负责同志担任
     */
    @TableField("has_secretary_industry")
    private Integer hasSecretaryIndustry;

    /**
     * 专职工作人员数
     */
    @TableField("worker_number")
    private Integer workerNumber;

    /**
     * 是否有所属党组织
     */
    @TableField("has_party_organizations")
    private Integer hasPartyOrganizations;

    /**
     * 关联组织（多选）
     */
    @TableField("associated_organization")
    private String associatedOrganization;

    /**
     * 删除时间
     */
    @TableField("delete_time")
    private Date deleteTime;

    /**
     * 创建时间
     */
    @TableField("create_time")
    private Date createTime;

    /**
     * 更新时间
     */
    @TableField("update_time")
    private Date updateTime;


    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getOrgCode() {
        return orgCode;
    }

    public void setOrgCode(String orgCode) {
        this.orgCode = orgCode;
    }

    public String getIndustryOrgCode() {
        return industryOrgCode;
    }

    public void setIndustryOrgCode(String industryOrgCode) {
        this.industryOrgCode = industryOrgCode;
    }

    public String getIndustryOrgName() {
        return industryOrgName;
    }

    public void setIndustryOrgName(String industryOrgName) {
        this.industryOrgName = industryOrgName;
    }

    public String getIndustryOrgType() {
        return industryOrgType;
    }

    public void setIndustryOrgType(String industryOrgType) {
        this.industryOrgType = industryOrgType;
    }

    public String getIndustryClassification() {
        return industryClassification;
    }

    public void setIndustryClassification(String industryClassification) {
        this.industryClassification = industryClassification;
    }

    public String getSubordinateLevel() {
        return subordinateLevel;
    }

    public void setSubordinateLevel(String subordinateLevel) {
        this.subordinateLevel = subordinateLevel;
    }

    public String getMembershipFunction() {
        return membershipFunction;
    }

    public void setMembershipFunction(String membershipFunction) {
        this.membershipFunction = membershipFunction;
    }

    public Integer getHasSecretaryIndustry() {
        return hasSecretaryIndustry;
    }

    public void setHasSecretaryIndustry(Integer hasSecretaryIndustry) {
        this.hasSecretaryIndustry = hasSecretaryIndustry;
    }

    public Integer getWorkerNumber() {
        return workerNumber;
    }

    public void setWorkerNumber(Integer workerNumber) {
        this.workerNumber = workerNumber;
    }

    public Integer getHasPartyOrganizations() {
        return hasPartyOrganizations;
    }

    public void setHasPartyOrganizations(Integer hasPartyOrganizations) {
        this.hasPartyOrganizations = hasPartyOrganizations;
    }

    public String getAssociatedOrganization() {
        return associatedOrganization;
    }

    public void setAssociatedOrganization(String associatedOrganization) {
        this.associatedOrganization = associatedOrganization;
    }

    public Date getDeleteTime() {
        return deleteTime;
    }

    public void setDeleteTime(Date deleteTime) {
        this.deleteTime = deleteTime;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    @Override
    protected Serializable pkVal() {
        return this.code;
    }

    @Override
    public String toString() {
        return "OrgIndustry{" +
        "code=" + code +
        ", orgCode=" + orgCode +
        ", industryOrgCode=" + industryOrgCode +
        ", industryOrgName=" + industryOrgName +
        ", industryOrgType=" + industryOrgType +
        ", industryClassification=" + industryClassification +
        ", subordinateLevel=" + subordinateLevel +
        ", membershipFunction=" + membershipFunction +
        ", hasSecretaryIndustry=" + hasSecretaryIndustry +
        ", workerNunber=" + workerNumber +
        ", hasPartyOrganizations=" + hasPartyOrganizations +
        ", associatedOrganization=" + associatedOrganization +
        ", deleteTime=" + deleteTime +
        ", createTime=" + createTime +
        ", updateTime=" + updateTime +
        "}";
    }
}
