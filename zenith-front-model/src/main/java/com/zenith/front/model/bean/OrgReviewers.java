package com.zenith.front.model.bean;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.zenith.front.common.encrypt.typehandler.EncryptTypeHandler;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2021-08-16
 */
@TableName(value = "ccp_org_reviewers", autoResultMap = true)
public class OrgReviewers extends Model<OrgReviewers> {

    private static final long serialVersionUID = 1L;

    /**
     * 唯一标识符code
     */
    @TableId(value = "code", type = IdType.ASSIGN_UUID)
    private String code;

    /**
     * 民主评议code
     */
    @TableField("org_code")
    private String orgCode;

    /**
     * 民主评议code
     */
    @TableField("appraisal_code")
    private String appraisalCode;

    /**
     * 党员
     */
    @TableField("mem_code")
    private String memCode;

    /**
     * 党员
     */
    @TableField(value = "mem_name", typeHandler = EncryptTypeHandler.class)
    private String memName;

    /**
     * 评议结果(字典表选项) 必填
     */
    @TableField("result")
    private String result;

    /**
     * 评议原因(文本框填写) 非必填
     */
    @TableField("reason")
    private String reason;

    /**
     * 上级党组织意见(文本框填写)非必填
     */
    @TableField("opinion")
    private String opinion;

    /**
     * 处理情况(当评议结果为基本不合格，不合格出现此选项且为必填)
     */
    @TableField("situation")
    private String situation;

    /**
     * 处理原因(当评议结果为基本不合格，不合格出现此选项且为必填)
     */
    @TableField("handling_reasons")
    private String handlingReasons;

    /**
     * 创建时间
     */
    @TableField("create_time")
    private Date createTime;

    /**
     * 更新时间
     */
    @TableField("update_time")
    private Date updateTime;

    /**
     * 删除时间
     */
    @TableField("delete_time")
    private Date deleteTime;

    /**
     * 停止党籍时间
     */
    @TableField("stop_party_date")
    private Date stopPartyDate;

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getOrgCode() {
        return orgCode;
    }

    public void setOrgCode(String orgCode) {
        this.orgCode = orgCode;
    }

    public String getAppraisalCode() {
        return appraisalCode;
    }

    public void setAppraisalCode(String appraisalCode) {
        this.appraisalCode = appraisalCode;
    }

    public String getMemCode() {
        return memCode;
    }

    public void setMemCode(String memCode) {
        this.memCode = memCode;
    }

    public String getMemName() {
        return memName;
    }

    public void setMemName(String memName) {
        this.memName = memName;
    }

    public String getResult() {
        return result;
    }

    public void setResult(String result) {
        this.result = result;
    }

    public String getReason() {
        return reason;
    }

    public void setReason(String reason) {
        this.reason = reason;
    }

    public String getOpinion() {
        return opinion;
    }

    public void setOpinion(String opinion) {
        this.opinion = opinion;
    }

    public String getSituation() {
        return situation;
    }

    public void setSituation(String situation) {
        this.situation = situation;
    }

    public String getHandlingReasons() {
        return handlingReasons;
    }

    public void setHandlingReasons(String handlingReasons) {
        this.handlingReasons = handlingReasons;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    public Date getDeleteTime() {
        return deleteTime;
    }

    public void setDeleteTime(Date deleteTime) {
        this.deleteTime = deleteTime;
    }

    public Date getStopPartyDate() {
        return stopPartyDate;
    }

    public void setStopPartyDate(Date stopPartyDate) {
        this.stopPartyDate = stopPartyDate;
    }

    @Override
    protected Serializable pkVal() {
        return this.code;
    }

    @Override
    public String toString() {
        return "OrgReviewers{" +
                "code='" + code + '\'' +
                ", orgCode='" + orgCode + '\'' +
                ", appraisalCode='" + appraisalCode + '\'' +
                ", memCode='" + memCode + '\'' +
                ", memName='" + memName + '\'' +
                ", result='" + result + '\'' +
                ", reason='" + reason + '\'' +
                ", opinion='" + opinion + '\'' +
                ", situation='" + situation + '\'' +
                ", handlingReasons='" + handlingReasons + '\'' +
                ", createTime=" + createTime +
                ", updateTime=" + updateTime +
                ", deleteTime=" + deleteTime +
                ", stopPartyDate=" + stopPartyDate +
                '}';
    }
}
