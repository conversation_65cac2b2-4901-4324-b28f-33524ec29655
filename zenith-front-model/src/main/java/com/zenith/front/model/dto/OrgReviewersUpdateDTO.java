package com.zenith.front.model.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 */
@Data
@ToString
@NoArgsConstructor
@AllArgsConstructor
public class OrgReviewersUpdateDTO implements Serializable {

    private static final long serialVersionUID = 5926545381032599414L;

    private List<String> code;

    /**
     * 评议结果(字典表选项) 必填
     */
    @NotNull
    private String result;

    /**
     * 评议原因(文本框填写) 非必填
     */
    private String reason;

    /**
     * 上级党组织意见(文本框填写)非必填
     */
    private String opinion;

    /**
     * 处理情况(当评议结果为基本不合格，不合格出现此选项且为必填)
     */
    private String situation;

    /**
     * 处理原因(当评议结果为基本不合格，不合格出现此选项且为必填)
     */
    private String handlingReasons;
    /**
     * 停止党籍时间
     */
    private Date stopPartyDate;

}
