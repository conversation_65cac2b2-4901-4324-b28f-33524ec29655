package com.zenith.front.model.dto;

import com.zenith.front.common.annotation.ExcelAttribute;
import lombok.Data;

/**
 * 导入入党申请人
 *
 * <AUTHOR>
 * @date 20211104
 */
@Data
public class ImportDevelopDTO {

    /**
     * 姓名
     */
    @ExcelAttribute(sort = 0)
    private String name;
    /**
     * 性别
     */
    @ExcelAttribute(sort = 1)
    private String sexName;
    /**
     * 发展党员身份证
     */
    @ExcelAttribute(sort = 2)
    private String idcard;
    /**
     * 出生日期
     */
    @ExcelAttribute(sort = 3)
    private String birthday;
    /**
     * 籍贯名称
     */
    @ExcelAttribute(sort = 4)
    private String d48Name;
    /**
     * 民族名称
     */
    @ExcelAttribute(sort = 5)
    private String d06Name;
    /**
     * 工作岗位
     */
    @ExcelAttribute(sort = 6)
    private String d09Name;
    /**
     * 申请时学历情况
     */
    @ExcelAttribute(sort = 7)
    private String d07Name;
    /**
     * 毕业院校（专科及以上填写）
     */
    @ExcelAttribute(sort = 8)
    private String byyx;
    /**
     * 专业
     */
    @ExcelAttribute(sort = 9)
    private String d88Name;
    /**
     * 政治面貌
     */
    @ExcelAttribute(sort = 10)
    private String politicsName;
    /**
     * 手机号码
     */
    @ExcelAttribute(sort = 11)
    private String phone;
    /**
     * 工作性质
     */
    @ExcelAttribute(sort = 12)
    private String jobNatureName;
    /**
     * 管理党组织
     */
    @ExcelAttribute(sort = 13)
    private String orgName;
    /**
     * 申请入党时间
     */
    @ExcelAttribute(sort = 14)
    private String applyDate;
    /**
     * 一线情况
     */
    @ExcelAttribute(sort = 15)
    private String d21Name;
    /**
     * 申请时新社会阶层
     */
    @ExcelAttribute(sort = 16)
    private String d20Name;
    /**
     * 专业技术职务
     */
    @ExcelAttribute(sort = 17)
    private String d19Name;
    /**
     * 是否高知识群体
     */
    @ExcelAttribute(sort = 18)
    private String isHighKnowledge;
    /**
     * 先进模范人物
     */
    @ExcelAttribute(sort = 19)
    private String advancedModelName;
    /**
     * 申请人档案管理单位
     */
    @ExcelAttribute(sort = 20)
    private String archiveUnit;
    /**
     * 现居地
     */
    @ExcelAttribute(sort = 21)
    private String homeAddress;

    /**
     * 在读院校
     */
    @ExcelAttribute(sort = 22)
    private String readingCollege;
    /**
     * 在读专业
     */
    @ExcelAttribute(sort = 23)
    private String readingProfessionalName;
    /**
     * 学制
     */
    @ExcelAttribute(sort = 24)
    private String educationalSystem;
    /**
     * 入学时间
     */
    @ExcelAttribute(sort = 25)
    private String enterSchoolDate;

}
