package com.zenith.front.model.vo;

import com.zenith.front.common.encrypt.annotation.EncryptEnabled;
import com.zenith.front.common.encrypt.annotation.EncryptField;

import java.util.Date;

/**
 * <AUTHOR>
 * @version V1.0
 * @date 2022/8/1 0001 17:13
 */
@EncryptEnabled
public class OrgPartyCongressCommitteeMessageVO {

    @EncryptField(order = 1)
    private String memName;
    private String orgCode;
    private String orgName;
    private Date deleteTime;

    public String getMemName() {
        return memName;
    }

    public void setMemName(String memName) {
        this.memName = memName;
    }

    public String getOrgCode() {
        return orgCode;
    }

    public void setOrgCode(String orgCode) {
        this.orgCode = orgCode;
    }

    public String getOrgName() {
        return orgName;
    }

    public void setOrgName(String orgName) {
        this.orgName = orgName;
    }

    public Date getDeleteTime() {
        return deleteTime;
    }

    public void setDeleteTime(Date deleteTime) {
        this.deleteTime = deleteTime;
    }

    @Override
    public String toString() {
        return "OrgPartyCongressCommitteeMessageVO{" +
                "memName='" + memName + '\'' +
                ", orgCode='" + orgCode + '\'' +
                ", orgName='" + orgName + '\'' +
                ", deleteTime='" + deleteTime + '\'' +
                '}';
    }
}
