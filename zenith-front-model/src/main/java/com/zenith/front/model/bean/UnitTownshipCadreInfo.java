package com.zenith.front.model.bean;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2022-04-11
 */
@TableName("ccp_unit_township_cadre_info")
public class UnitTownshipCadreInfo extends Model<UnitTownshipCadreInfo> {

    private static final long serialVersionUID = 1L;

    @TableId(value = "code", type = IdType.ASSIGN_UUID)
    private String code;

    /**
     * 单位code
     */
    @TableField("unit_code")
    private String unitCode;

    /**
     * 组织code
     */
    @TableField("org_code")
    private String orgCode;

    /**
     * 组织层级码
     */
    @TableField("org_level_code")
    private String orgLevelCode;

    /**
     * 行政编制数（数字、必填）
     */
    @TableField("xzbz_num")
    private Integer xzbzNum;

    /**
     * 事业编制数（数字、必填）
     */
    @TableField("sybz_num")
    private Integer sybzNum;

    /**
     * 工勤编制数（数字、必填）
     */
    @TableField("gqbz_num")
    private Integer gqbzNum;

    /**
     * 空缺行政编制数（数字、必填、不能大于行政编制数字）
     */
    @TableField("kqxzbz_num")
    private Integer kqxzbzNum;

    /**
     * 空缺事业编制数（数字、必填、不能大于行事业编制数字）
     */
    @TableField("kqsybz_num")
    private Integer kqsybzNum;

    /**
     * 被借调工作人员总数（数字、必填）
     */
    @TableField("seconded_staff_num")
    private Integer secondedStaffNum;

    /**
     * 省级以上借调人数（数字、必填、不能大于被借调工作人员总数）
     */
    @TableField("above_provincial_seconded_staff_num")
    private Integer aboveProvincialSecondedStaffNum;

    /**
     * 省级借调人数（数字、必填、不能大于被借调工作人员总数）
     */
    @TableField("provincial_seconded_staff_num")
    private Integer provincialSecondedStaffNum;

    /**
     * 市级借调人数（数字、必填、不能大于被借调工作人员总数）
     */
    @TableField("city_seconded_staff_num")
    private Integer citySecondedStaffNum;

    /**
     * 县级借调人数（数字、必填、不能大于被借调工作人员总数）
     */
    @TableField("county_seconded_staff_num")
    private Integer countySecondedStaffNum;

    @TableField("create_time")
    private Date createTime;

    @TableField("update_time")
    private Date updateTime;

    @TableField("delete_time")
    private Date deleteTime;

    /**
     * 统计时间
     */
    @TableField("year")
    private String year;

    @TableField("count_date")
    private Date countDate;

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getUnitCode() {
        return unitCode;
    }

    public void setUnitCode(String unitCode) {
        this.unitCode = unitCode;
    }

    public String getOrgCode() {
        return orgCode;
    }

    public void setOrgCode(String orgCode) {
        this.orgCode = orgCode;
    }

    public String getOrgLevelCode() {
        return orgLevelCode;
    }

    public void setOrgLevelCode(String orgLevelCode) {
        this.orgLevelCode = orgLevelCode;
    }

    public Integer getXzbzNum() {
        return xzbzNum;
    }

    public void setXzbzNum(Integer xzbzNum) {
        this.xzbzNum = xzbzNum;
    }

    public Integer getSybzNum() {
        return sybzNum;
    }

    public void setSybzNum(Integer sybzNum) {
        this.sybzNum = sybzNum;
    }

    public Integer getGqbzNum() {
        return gqbzNum;
    }

    public void setGqbzNum(Integer gqbzNum) {
        this.gqbzNum = gqbzNum;
    }

    public Integer getKqxzbzNum() {
        return kqxzbzNum;
    }

    public void setKqxzbzNum(Integer kqxzbzNum) {
        this.kqxzbzNum = kqxzbzNum;
    }

    public Integer getKqsybzNum() {
        return kqsybzNum;
    }

    public void setKqsybzNum(Integer kqsybzNum) {
        this.kqsybzNum = kqsybzNum;
    }

    public Integer getSecondedStaffNum() {
        return secondedStaffNum;
    }

    public void setSecondedStaffNum(Integer secondedStaffNum) {
        this.secondedStaffNum = secondedStaffNum;
    }

    public Integer getAboveProvincialSecondedStaffNum() {
        return aboveProvincialSecondedStaffNum;
    }

    public void setAboveProvincialSecondedStaffNum(Integer aboveProvincialSecondedStaffNum) {
        this.aboveProvincialSecondedStaffNum = aboveProvincialSecondedStaffNum;
    }

    public Integer getProvincialSecondedStaffNum() {
        return provincialSecondedStaffNum;
    }

    public void setProvincialSecondedStaffNum(Integer provincialSecondedStaffNum) {
        this.provincialSecondedStaffNum = provincialSecondedStaffNum;
    }

    public Integer getCitySecondedStaffNum() {
        return citySecondedStaffNum;
    }

    public void setCitySecondedStaffNum(Integer citySecondedStaffNum) {
        this.citySecondedStaffNum = citySecondedStaffNum;
    }

    public Integer getCountySecondedStaffNum() {
        return countySecondedStaffNum;
    }

    public void setCountySecondedStaffNum(Integer countySecondedStaffNum) {
        this.countySecondedStaffNum = countySecondedStaffNum;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    public Date getDeleteTime() {
        return deleteTime;
    }

    public void setDeleteTime(Date deleteTime) {
        this.deleteTime = deleteTime;
    }

    public String getYear() {
        return year;
    }

    public void setYear(String year) {
        this.year = year;
    }

    public Date getCountDate() {
        return countDate;
    }

    public void setCountDate(Date countDate) {
        this.countDate = countDate;
    }

    @Override
    protected Serializable pkVal() {
        return this.code;
    }

    @Override
    public String toString() {
        return "UnitTownshipCadreInfo{" +
                "code=" + code +
                ", unitCode=" + unitCode +
                ", orgCode=" + orgCode +
                ", orgLevelCode=" + orgLevelCode +
                ", xzbzNum=" + xzbzNum +
                ", sybzNum=" + sybzNum +
                ", gqbzNum=" + gqbzNum +
                ", kqxzbzNum=" + kqxzbzNum +
                ", kqsybzNum=" + kqsybzNum +
                ", secondedStaffNum=" + secondedStaffNum +
                ", aboveProvincialSecondedStaffNum=" + aboveProvincialSecondedStaffNum +
                ", provincialSecondedStaffNum=" + provincialSecondedStaffNum +
                ", citySecondedStaffNum=" + citySecondedStaffNum +
                ", countySecondedStaffNum=" + countySecondedStaffNum +
                ", createTime=" + createTime +
                ", updateTime=" + updateTime +
                ", deleteTime=" + deleteTime +
                ", year=" + year +
                ", countDate=" + countDate +
                "}";
    }
}
