package com.zenith.front.model.dto;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.util.Date;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2022-11-14
 */
@Setter
@Getter
@ToString
public class MemFlowReRegisterDTO {

    /**
     * 组织主键
     */
    private String code;

    /**
     * 外出地点
     */
    private String outPlaceCode;
    /**
     * 流入党委
     */
    private String outOrgCode;
    private String outOrgName;
    private String outOrgContact;
    private String outOrgContactPhone;
    /**
     * 流入党支部
     */
    private String outOrgBranchCode;
    private String outOrgBranchOrgCode;
    private String outOrgBranchName;
    /**
     * 流入行政区
     */
    private String outAdministrativeDivisionCode;
    private String outOrgRemarks;
    private String isProvince;

    /**
     * 流动原因
     */
    private String flowReasonCode;

    private String flowReasonName;

    /**
     * 外出日期
     */
    private Date outTime;

    /**
     * 流入地党组织单位类型
     */
    private String inOrgD04Code;

    /**
     * 流入地党组织单位类型选择企业，需要经济类型用于统计
     */
    private String inUnitD16Code;

    /**
     * 党费交纳情况（交到流出地至）
     */
    private Date partyExpensesOutTime;

    /**
     * 流动党员活动证
     */
    private String isHold;

    /**
     * 结对联系人
     */
    private String pairedContact;
    /**
     * 结对联系方式
     */
    private String pairedContactPhone;

    /**
     * 登记时间
     */
    private Date registerTime;

}
