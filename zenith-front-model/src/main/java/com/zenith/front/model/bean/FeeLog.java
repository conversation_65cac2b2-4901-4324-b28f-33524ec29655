package com.zenith.front.model.bean;

import java.math.BigDecimal;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import java.util.Date;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableField;
import java.io.Serializable;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2021-07-11
 */
@TableName("ccp_fee_log")
public class FeeLog extends Model<FeeLog> {

    private static final long serialVersionUID=1L;

    /**
     * 党费标准自增长ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 党费标准唯一标识符
     */
    @TableField("code")
    private String code;

    /**
     * 备注
     */
    @TableField("remark")
    private String remark;

    /**
     * 党费标准code
     */
    @TableField("fee_code")
    private String feeCode;

    /**
     * 创建者账号
     */
    @TableField("creator_account")
    private String creatorAccount;

    /**
     * 详情
     */
    @TableField("detail")
    private String detail;

    /**
     * 人员code
     */
    @TableField("mem_code")
    private String memCode;

    /**
     * 设置时人员所在组织唯一标识符
     */
    @TableField("mem_org_code")
    private String memOrgCode;

    /**
     * 设置时人员所在组织层级码
     */
    @TableField("mem_org_org_code")
    private String memOrgOrgCode;

    /**
     * 缴纳金额
     */
    @TableField("money")
    private BigDecimal money;

    /**
     * 党费基数
     */
    @TableField("base")
    private BigDecimal base;

    /**
     * 党费标准
     */
    @TableField("stand")
    private BigDecimal stand;

    /**
     * 备注
     */
    @TableField("reason")
    private String reason;

    /**
     * 计算类型code
     */
    @TableField("d49_code")
    private String d49Code;

    /**
     * 计算类型名称
     */
    @TableField("d49_name")
    private String d49Name;

    /**
     * 创建时间
     */
    @TableField("create_time")
    private Date createTime;

    /**
     * 更新时间
     */
    @TableField("update_time")
    private Date updateTime;

    /**
     * 删除时间
     */
    @TableField("delete_time")
    private Date deleteTime;


    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public String getFeeCode() {
        return feeCode;
    }

    public void setFeeCode(String feeCode) {
        this.feeCode = feeCode;
    }

    public String getCreatorAccount() {
        return creatorAccount;
    }

    public void setCreatorAccount(String creatorAccount) {
        this.creatorAccount = creatorAccount;
    }

    public String getDetail() {
        return detail;
    }

    public void setDetail(String detail) {
        this.detail = detail;
    }

    public String getMemCode() {
        return memCode;
    }

    public void setMemCode(String memCode) {
        this.memCode = memCode;
    }

    public String getMemOrgCode() {
        return memOrgCode;
    }

    public void setMemOrgCode(String memOrgCode) {
        this.memOrgCode = memOrgCode;
    }

    public String getMemOrgOrgCode() {
        return memOrgOrgCode;
    }

    public void setMemOrgOrgCode(String memOrgOrgCode) {
        this.memOrgOrgCode = memOrgOrgCode;
    }

    public BigDecimal getMoney() {
        return money;
    }

    public void setMoney(BigDecimal money) {
        this.money = money;
    }

    public BigDecimal getBase() {
        return base;
    }

    public void setBase(BigDecimal base) {
        this.base = base;
    }

    public BigDecimal getStand() {
        return stand;
    }

    public void setStand(BigDecimal stand) {
        this.stand = stand;
    }

    public String getReason() {
        return reason;
    }

    public void setReason(String reason) {
        this.reason = reason;
    }

    public String getD49Code() {
        return d49Code;
    }

    public void setD49Code(String d49Code) {
        this.d49Code = d49Code;
    }

    public String getD49Name() {
        return d49Name;
    }

    public void setD49Name(String d49Name) {
        this.d49Name = d49Name;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    public Date getDeleteTime() {
        return deleteTime;
    }

    public void setDeleteTime(Date deleteTime) {
        this.deleteTime = deleteTime;
    }

    @Override
    public Serializable pkVal() {
        return this.id;
    }

    @Override
    public String toString() {
        return "FeeLog{" +
        "id=" + id +
        ", code=" + code +
        ", remark=" + remark +
        ", feeCode=" + feeCode +
        ", creatorAccount=" + creatorAccount +
        ", detail=" + detail +
        ", memCode=" + memCode +
        ", memOrgCode=" + memOrgCode +
        ", memOrgOrgCode=" + memOrgOrgCode +
        ", money=" + money +
        ", base=" + base +
        ", stand=" + stand +
        ", reason=" + reason +
        ", d49Code=" + d49Code +
        ", d49Name=" + d49Name +
        ", createTime=" + createTime +
        ", updateTime=" + updateTime +
        ", deleteTime=" + deleteTime +
        "}";
    }
}
