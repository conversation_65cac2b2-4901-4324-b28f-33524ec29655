package com.zenith.front.model.dto;

import com.zenith.front.model.bean.Activity;
import com.zenith.front.model.validate.group.Common1Group;
import lombok.*;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.List;

@ToString
public class ActivityDTO{


    /**
     * 主持人
     * */
    @NotNull(groups = {Common1Group.class},message = "主持人不允许为空!")
    private List<ActivityMemDTO> hostorList;

    /**
     * 讲课人
     * */
    private List<ActivityMemDTO> lecturerList;


    /**
     * 领导人
     * */
    private List<ActivityMemDTO> leaderList;


    /**
     * 应到人员
     * */
    @NotNull(groups = {Common1Group.class},message = "应到人员不允许为空!")
    private List<ActivityMemDTO> memList;


    /**
     * 列席人员
     * */
    private List<ActivityMemDTO> attendList;

    /**
     * 缺席人员
     * */
    private List<ActivityMemDTO> absenceList;

    public List<ActivityMemDTO> getHostorList() {
        return hostorList;
    }

    public void setHostorList(List<ActivityMemDTO> hostorList) {
        this.hostorList = hostorList;
    }

    public List<ActivityMemDTO> getLecturerList() {
        return lecturerList;
    }

    public void setLecturerList(List<ActivityMemDTO> lecturerList) {
        this.lecturerList = lecturerList;
    }

    public List<ActivityMemDTO> getLeaderList() {
        return leaderList;
    }

    public void setLeaderList(List<ActivityMemDTO> leaderList) {
        this.leaderList = leaderList;
    }

    public List<ActivityMemDTO> getMemList() {
        return memList;
    }

    public void setMemList(List<ActivityMemDTO> memList) {
        this.memList = memList;
    }

    public List<ActivityMemDTO> getAttendList() {
        return attendList;
    }

    public void setAttendList(List<ActivityMemDTO> attendList) {
        this.attendList = attendList;
    }

    public List<ActivityMemDTO> getAbsenceList() {
        return absenceList;
    }

    public void setAbsenceList(List<ActivityMemDTO> absenceList) {
        this.absenceList = absenceList;
    }

    /**
   	 *  活动自增长主键
   	 */
    private Long id;
   	/**
   	 * 活动唯一主键
   	 */
    private String code;
   	/**
   	 * 活动名称
   	 */
    @NotBlank(groups = {Common1Group.class},message = "活动名称不允许为空!")
    private String name;
   	/**
   	 * 录入活动组织唯一标识符
   	 */
    @NotBlank(groups = {Common1Group.class},message = "组织唯一标识符不允许为空!")
    private String acOrgCode;
   	/**
   	 * 录入活动组织层级码
   	 */
    @NotBlank(groups = {Common1Group.class},message = "组织层级码不允许为空!")
    private String acOrgOrgCode;
    private String groupKey;
    private String groupName;
   	/**
   	 * 党小组会集合
   	 */
    private String groupCode;
   	/**
   	 * 举办时间
   	 */
    @NotNull(groups = {Common1Group.class},message = "举办时间不允许为空!")
    private java.util.Date holdTime;
   	/**
   	 * 活动类型集合
   	 */
    @NotNull(groups = {Common1Group.class},message = "活动类型code不允许为空!")
    private Object typeCodes;
   	/**
   	 * 活动类型集合名称
   	 */
    @NotNull(groups = {Common1Group.class},message = "活动类型名称不允许为空!")
    private Object typeNames;
   	/**
   	 * 活动地点经纬度
   	 */
    @NotBlank(groups = {Common1Group.class},message = "活动地点维度不允许为空!")
    private String location;
   	/**
   	 * 活动地点
   	 */
    @NotBlank(groups = {Common1Group.class},message = "活动地点不允许为空!")
    private String address;
   	/**
   	 * 主持人总数
   	 */
    private Integer hostorCount;
   	/**
   	 * 讲课人总数
   	 */
    private Integer lecturerCount;
   	/**
   	 * 领导人员总数
   	 */
    private Integer leaderCount;
   	/**
   	 * 应到人员总数
   	 */
    private Integer memCount;
   	/**
   	 * 列席人员总数
   	 */
    private Integer attendCount;
   	/**
   	 * 缺席人员总数
   	 */
    private Integer absenceCount;
   	/**
   	 * 活动内容
   	 */
    private String content;
   	/**
   	 * 活动状态{1.未开始，2进行中，3已结束}
   	 */
    @NotNull(groups = {Common1Group.class},message = "活动状态不允许为空!")
    private Integer status;
   	/**
   	 * 是否公开
   	 */
    private Integer isOpen;
   	/**
   	 * 查看次数
   	 */
    private Integer viewCount;
   	/**
   	 * 点赞次数
   	 */
    private Integer goodCount;
   	/**
   	 * 是否包含附件 1有 0无
   	 */
    private Integer hasFile;
   	/**
   	 * pc:电脑，phone:手机
   	 */
    private String createType;
   	/**
   	 * 时间乐观锁
   	 */
    private java.util.Date timestamp;
   	/**
   	 * 是否有图片 1有 0无
   	 */
    private Integer hasImage;
   	/**
   	 * 是否有视频 1有 0无
   	 */
    private Integer hasVideo;
   	/**
   	 * 是否有语音 1有 0无
   	 */
    private Integer hasVoice;
   	/**
   	 * 是否有文档 1有 0无
   	 */
    private Integer hasDocument;
   	/**
   	 * 人员类型集合
   	 */
    private String memFilter;
   	/**
   	 * 取消原因
   	 */
    private String cancelReason;
   	/**
   	 * 创建人
   	 */
    private String creatorAccount;
   	/**
   	 * 活动计划（活动议程）
   	 */
    @NotBlank(groups = {Common1Group.class},message = "活动议程不允许为空!")
    private String acPlan;
   	/**
   	 * 活动计划文件（活动议程附件)
   	 */
    private String acPlanFile;
   	/**
   	 * 签到方式(1扫码签到，2打卡签到，3定位签到)
   	 */
    private String canReview;
   	/**
   	 * 通知方式(1微信通知，2短信通知，3平台通知)
   	 */
    private String noticePlan;
   	/**
   	 * 创建时间
   	 */
    private java.util.Date createTime;
   	/**
   	 * 更新时间
   	 */
    private java.util.Date updateTime;
   	/**
   	 * 删除时间
   	 */
    private java.util.Date deleteTime;
   	/**
   	 * 活动结束时间
   	 */
    @NotNull(groups = {Common1Group.class},message = "活动结束时间不允许为空!")
    private java.util.Date endTime;
   	/**
   	 * 录入时活动所在组织标识符集合
   	 */
    private Object entryOrgCodeSet;

    public ActivityDTO setId(Long id){
        this.id = id;
        return this;
    }
    public Long getId() {
    	return this.id;
    }
    public ActivityDTO setCode(String code){
        this.code = code;
        return this;
    }
    public String getCode() {
    	return this.code;
    }
    public ActivityDTO setName(String name){
        this.name = name;
        return this;
    }
    public String getName() {
    	return this.name;
    }
    public ActivityDTO setAcOrgCode(String acOrgCode){
        this.acOrgCode = acOrgCode;
        return this;
    }
    public String getAcOrgCode() {
    	return this.acOrgCode;
    }
    public ActivityDTO setAcOrgOrgCode(String acOrgOrgCode){
        this.acOrgOrgCode = acOrgOrgCode;
        return this;
    }
    public String getAcOrgOrgCode() {
    	return this.acOrgOrgCode;
    }
    public ActivityDTO setGroupKey(String groupKey){
        this.groupKey = groupKey;
        return this;
    }
    public String getGroupKey() {
    	return this.groupKey;
    }
    public ActivityDTO setGroupName(String groupName){
        this.groupName = groupName;
        return this;
    }
    public String getGroupName() {
    	return this.groupName;
    }
    public ActivityDTO setGroupCode(String groupCode){
        this.groupCode = groupCode;
        return this;
    }
    public String getGroupCode() {
    	return this.groupCode;
    }
    public ActivityDTO setHoldTime(java.util.Date holdTime){
        this.holdTime = holdTime;
        return this;
    }
    public java.util.Date getHoldTime() {
    	return this.holdTime;
    }
    public ActivityDTO setTypeCodes(Object typeCodes){
        this.typeCodes = typeCodes;
        return this;
    }
    public Object getTypeCodes() {
    	return this.typeCodes;
    }
    public ActivityDTO setTypeNames(Object typeNames){
        this.typeNames = typeNames;
        return this;
    }
    public Object getTypeNames() {
    	return this.typeNames;
    }
    public ActivityDTO setLocation(String location){
        this.location = location;
        return this;
    }
    public String getLocation() {
    	return this.location;
    }
    public ActivityDTO setAddress(String address){
        this.address = address;
        return this;
    }
    public String getAddress() {
    	return this.address;
    }
    public ActivityDTO setHostorCount(Integer hostorCount){
        this.hostorCount = hostorCount;
        return this;
    }
    public Integer getHostorCount() {
    	return this.hostorCount;
    }
    public ActivityDTO setLecturerCount(Integer lecturerCount){
        this.lecturerCount = lecturerCount;
        return this;
    }
    public Integer getLecturerCount() {
    	return this.lecturerCount;
    }
    public ActivityDTO setLeaderCount(Integer leaderCount){
        this.leaderCount = leaderCount;
        return this;
    }
    public Integer getLeaderCount() {
    	return this.leaderCount;
    }
    public ActivityDTO setMemCount(Integer memCount){
        this.memCount = memCount;
        return this;
    }
    public Integer getMemCount() {
    	return this.memCount;
    }
    public ActivityDTO setAttendCount(Integer attendCount){
        this.attendCount = attendCount;
        return this;
    }
    public Integer getAttendCount() {
    	return this.attendCount;
    }
    public ActivityDTO setAbsenceCount(Integer absenceCount){
        this.absenceCount = absenceCount;
        return this;
    }
    public Integer getAbsenceCount() {
    	return this.absenceCount;
    }
    public ActivityDTO setContent(String content){
        this.content = content;
        return this;
    }
    public String getContent() {
    	return this.content;
    }
    public ActivityDTO setStatus(Integer status){
        this.status = status;
        return this;
    }
    public Integer getStatus() {
    	return this.status;
    }
    public ActivityDTO setIsOpen(Integer isOpen){
        this.isOpen = isOpen;
        return this;
    }
    public Integer getIsOpen() {
    	return this.isOpen;
    }
    public ActivityDTO setViewCount(Integer viewCount){
        this.viewCount = viewCount;
        return this;
    }
    public Integer getViewCount() {
    	return this.viewCount;
    }
    public ActivityDTO setGoodCount(Integer goodCount){
        this.goodCount = goodCount;
        return this;
    }
    public Integer getGoodCount() {
    	return this.goodCount;
    }
    public ActivityDTO setHasFile(Integer hasFile){
        this.hasFile = hasFile;
        return this;
    }
    public Integer getHasFile() {
    	return this.hasFile;
    }
    public ActivityDTO setCreateType(String createType){
        this.createType = createType;
        return this;
    }
    public String getCreateType() {
    	return this.createType;
    }
    public ActivityDTO setTimestamp(java.util.Date timestamp){
        this.timestamp = timestamp;
        return this;
    }
    public java.util.Date getTimestamp() {
    	return this.timestamp;
    }
    public ActivityDTO setHasImage(Integer hasImage){
        this.hasImage = hasImage;
        return this;
    }
    public Integer getHasImage() {
    	return this.hasImage;
    }
    public ActivityDTO setHasVideo(Integer hasVideo){
        this.hasVideo = hasVideo;
        return this;
    }
    public Integer getHasVideo() {
    	return this.hasVideo;
    }
    public ActivityDTO setHasVoice(Integer hasVoice){
        this.hasVoice = hasVoice;
        return this;
    }
    public Integer getHasVoice() {
    	return this.hasVoice;
    }
    public ActivityDTO setHasDocument(Integer hasDocument){
        this.hasDocument = hasDocument;
        return this;
    }
    public Integer getHasDocument() {
    	return this.hasDocument;
    }
    public ActivityDTO setMemFilter(String memFilter){
        this.memFilter = memFilter;
        return this;
    }
    public String getMemFilter() {
    	return this.memFilter;
    }
    public ActivityDTO setCancelReason(String cancelReason){
        this.cancelReason = cancelReason;
        return this;
    }
    public String getCancelReason() {
    	return this.cancelReason;
    }
    public ActivityDTO setCreatorAccount(String creatorAccount){
        this.creatorAccount = creatorAccount;
        return this;
    }
    public String getCreatorAccount() {
    	return this.creatorAccount;
    }
    public ActivityDTO setAcPlan(String acPlan){
        this.acPlan = acPlan;
        return this;
    }
    public String getAcPlan() {
    	return this.acPlan;
    }
    public ActivityDTO setAcPlanFile(String acPlanFile){
        this.acPlanFile = acPlanFile;
        return this;
    }
    public String getAcPlanFile() {
    	return this.acPlanFile;
    }
    public ActivityDTO setCanReview(String canReview){
        this.canReview = canReview;
        return this;
    }
    public String getCanReview() {
    	return this.canReview;
    }
    public ActivityDTO setNoticePlan(String noticePlan){
        this.noticePlan = noticePlan;
        return this;
    }
    public String getNoticePlan() {
    	return this.noticePlan;
    }
    public ActivityDTO setCreateTime(java.util.Date createTime){
        this.createTime = createTime;
        return this;
    }
    public java.util.Date getCreateTime() {
    	return this.createTime;
    }
    public ActivityDTO setUpdateTime(java.util.Date updateTime){
        this.updateTime = updateTime;
        return this;
    }
    public java.util.Date getUpdateTime() {
    	return this.updateTime;
    }
    public ActivityDTO setDeleteTime(java.util.Date deleteTime){
        this.deleteTime = deleteTime;
        return this;
    }
    public java.util.Date getDeleteTime() {
    	return this.deleteTime;
    }
    public ActivityDTO setEndTime(java.util.Date endTime){
        this.endTime = endTime;
        return this;
    }
    public java.util.Date getEndTime() {
    	return this.endTime;
    }
    public ActivityDTO setEntryOrgCodeSet(Object entryOrgCodeSet){
        this.entryOrgCodeSet = entryOrgCodeSet;
        return this;
    }
    public Object getEntryOrgCodeSet() {
    	return this.entryOrgCodeSet;
    }


    public Activity toModel(){
        Activity model = new Activity();
        model.setId(this.id);
        model.setCode(this.code);
        model.setName(this.name);
        model.setAcOrgCode(this.acOrgCode);
        model.setAcOrgOrgCode(this.acOrgOrgCode);
        model.setGroupKey(this.groupKey);
        model.setGroupName(this.groupName);
        model.setGroupCode(this.groupCode);
        model.setHoldTime(this.holdTime);
        model.setTypeCodes(this.typeCodes);
        model.setTypeNames(this.typeNames);
        model.setLocation(this.location);
        model.setAddress(this.address);
        model.setHostorCount(this.hostorCount);
        model.setLecturerCount(this.lecturerCount);
        model.setLeaderCount(this.leaderCount);
        model.setMemCount(this.memCount);
        model.setAttendCount(this.attendCount);
        model.setAbsenceCount(this.absenceCount);
        model.setContent(this.content);
        model.setStatus(this.status);
        model.setIsOpen(this.isOpen);
        model.setViewCount(this.viewCount);
        model.setGoodCount(this.goodCount);
        model.setHasFile(this.hasFile);
        model.setCreateType(this.createType);
        model.setTimestamp(this.timestamp);
        model.setHasImage(this.hasImage);
        model.setHasVideo(this.hasVideo);
        model.setHasVoice(this.hasVoice);
        model.setHasDocument(this.hasDocument);
        model.setMemFilter(this.memFilter);
        model.setCancelReason(this.cancelReason);
        model.setCreatorAccount(this.creatorAccount);
        model.setAcPlan(this.acPlan);
        model.setAcPlanFile(this.acPlanFile);
        model.setCanReview(this.canReview);
        model.setNoticePlan(this.noticePlan);
        model.setCreateTime(this.createTime);
        model.setUpdateTime(this.updateTime);
        model.setDeleteTime(this.deleteTime);
        model.setEndTime(this.endTime);
        model.setEntryOrgCodeSet(this.entryOrgCodeSet);
        return model;
    }
}