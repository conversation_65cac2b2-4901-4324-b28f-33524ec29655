package com.zenith.front.model.dto;

import com.zenith.front.model.bean.Trend;
import com.zenith.front.model.validate.group.AddGroup;
import com.zenith.front.model.validate.group.Common1Group;
import com.zenith.front.model.validate.group.UpdateGroup;
import lombok.*;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.List;

@ToString
public class TrendDTO{

    /**
     * 自增长主键
     */
    private Integer id;
    /**
     * 唯一标识符
     */
    @NotBlank(groups = {Common1Group.class, UpdateGroup.class},message = "标识符不允许为空!")
    private String code;
    /**
     * 动态标题
     */
    @NotBlank(groups = { AddGroup.class,UpdateGroup.class},message = "动态标题不允许为空!")
    private String tittle;
    /**
     * 动态内容
     */
    @NotBlank(groups = { AddGroup.class,UpdateGroup.class},message = "动态内容不允许为空!")
    private String context;
    /**
     * 动态类型
     */
    @NotNull(groups = { AddGroup.class,UpdateGroup.class},message = "类型不允许为空!")
    private Integer type;
    /**
     * 动态可见状态（1.公开，0.不公开）
     */
    private Integer selectType;
    /**
     * 推送状态(1.预发布，2.已发布)
     */
    private Integer pushStatus;
    /**
     * 查看次数
     */
    private Integer selectCount;
    /**
     * 点赞次数
     */
    private Integer praiseCount;
    /**
     * 收藏次数
     */
    private Integer collectCount;
    /**
     * 是否通知（是否发送消息通知）
     */
    @NotNull(groups = { AddGroup.class,UpdateGroup.class},message = "请确认是否通知!")
    private Integer isNotice;
    /**
     * 是否撤销(0.否，1.是)
     */
    private Integer isCancel;
    /**
     * 文件集合
     */
    private String files;
    /**
     *  创建时间
     */
    private java.util.Date createTime;
    /**
     * 更新时间
     */
    private java.util.Date updateTime;
    /**
     * 删除时间
     */
    private java.util.Date deleteTime;
    /**
     * 创建动态的组织code
     */
    @NotBlank(groups = { AddGroup.class},message = "创建组织标识符不允许为空!")
    private String createOrgCode;
    /**
     * 创建动态的组织code层级码
     */
    @NotBlank(groups = { AddGroup.class},message = "组织层级码不允许为空!")
    private String createOrgOrgCode;
    /**
     * 创建动态的组织的唯一标识符集合
     */
    private String createOrgSet;
    /**
     * 是否建议推送到门户
     */
    private Integer isPortal;
    /**
     * 创建账号
     */
    private String createAccount;
   	/**
   	 * 审核人
   	 */
    @NotBlank(groups = {AddGroup.class,UpdateGroup.class},message = "审核人不允许为空!")
    private String checkPerson;

    /**
     * 标签图片
     * **/
    private List<FileDto> titlePhotoList;

    public List<FileDto> getTitlePhotoList() {
        return titlePhotoList;
    }

    public void setTitlePhotoList(List<FileDto> title_photo) {
        this.titlePhotoList = title_photo;
    }

    public TrendDTO setId(Integer id){
        this.id = id;
        return this;
    }
    public Integer getId() {
    	return this.id;
    }
    public TrendDTO setCode(String code){
        this.code = code;
        return this;
    }
    public String getCode() {
    	return this.code;
    }
    public TrendDTO setTittle(String tittle){
        this.tittle = tittle;
        return this;
    }
    public String getTittle() {
    	return this.tittle;
    }
    public TrendDTO setContext(String context){
        this.context = context;
        return this;
    }
    public String getContext() {
    	return this.context;
    }
    public TrendDTO setType(Integer type){
        this.type = type;
        return this;
    }
    public Integer getType() {
    	return this.type;
    }
    public TrendDTO setSelectType(Integer selectType){
        this.selectType = selectType;
        return this;
    }
    public Integer getSelectType() {
    	return this.selectType;
    }
    public TrendDTO setPushStatus(Integer pushStatus){
        this.pushStatus = pushStatus;
        return this;
    }
    public Integer getPushStatus() {
    	return this.pushStatus;
    }
    public TrendDTO setSelectCount(Integer selectCount){
        this.selectCount = selectCount;
        return this;
    }
    public Integer getSelectCount() {
    	return this.selectCount;
    }
    public TrendDTO setPraiseCount(Integer praiseCount){
        this.praiseCount = praiseCount;
        return this;
    }
    public Integer getPraiseCount() {
    	return this.praiseCount;
    }
    public TrendDTO setCollectCount(Integer collectCount){
        this.collectCount = collectCount;
        return this;
    }
    public Integer getCollectCount() {
    	return this.collectCount;
    }
    public TrendDTO setIsNotice(Integer isNotice){
        this.isNotice = isNotice;
        return this;
    }
    public Integer getIsNotice() {
    	return this.isNotice;
    }
    public TrendDTO setIsCancel(Integer isCancel){
        this.isCancel = isCancel;
        return this;
    }
    public Integer getIsCancel() {
    	return this.isCancel;
    }
    public TrendDTO setFiles(String files){
        this.files = files;
        return this;
    }
    public String getFiles() {
    	return this.files;
    }
    public TrendDTO setCreateTime(java.util.Date createTime){
        this.createTime = createTime;
        return this;
    }
    public java.util.Date getCreateTime() {
    	return this.createTime;
    }
    public TrendDTO setUpdateTime(java.util.Date updateTime){
        this.updateTime = updateTime;
        return this;
    }
    public java.util.Date getUpdateTime() {
    	return this.updateTime;
    }
    public TrendDTO setDeleteTime(java.util.Date deleteTime){
        this.deleteTime = deleteTime;
        return this;
    }
    public java.util.Date getDeleteTime() {
    	return this.deleteTime;
    }
    public TrendDTO setCreateOrgCode(String createOrgCode){
        this.createOrgCode = createOrgCode;
        return this;
    }
    public String getCreateOrgCode() {
    	return this.createOrgCode;
    }
    public TrendDTO setCreateOrgOrgCode(String createOrgOrgCode){
        this.createOrgOrgCode = createOrgOrgCode;
        return this;
    }
    public String getCreateOrgOrgCode() {
    	return this.createOrgOrgCode;
    }
    public TrendDTO setCreateOrgSet(String createOrgSet){
        this.createOrgSet = createOrgSet;
        return this;
    }
    public String getCreateOrgSet() {
    	return this.createOrgSet;
    }
    public TrendDTO setIsPortal(Integer isPortal){
        this.isPortal = isPortal;
        return this;
    }
    public Integer getIsPortal() {
    	return this.isPortal;
    }
    public TrendDTO setCreateAccount(String createAccount){
        this.createAccount = createAccount;
        return this;
    }
    public String getCreateAccount() {
    	return this.createAccount;
    }
    public TrendDTO setCheckPerson(String checkPerson){
        this.checkPerson = checkPerson;
        return this;
    }
    public String getCheckPerson() {
    	return this.checkPerson;
    }


    public Trend toModel(){
        Trend model = new Trend();
        model.setId(this.id);
        model.setCode(this.code);
        model.setTittle(this.tittle);
        model.setContext(this.context);
        model.setType(this.type);
        model.setSelectType(this.selectType);
        model.setPushStatus(this.pushStatus);
        model.setSelectCount(this.selectCount);
        model.setPraiseCount(this.praiseCount);
        model.setCollectCount(this.collectCount);
        model.setIsNotice(this.isNotice);
        model.setIsCancel(this.isCancel);
        model.setFiles(this.files);
        model.setCreateTime(this.createTime);
        model.setUpdateTime(this.updateTime);
        model.setDeleteTime(this.deleteTime);
        model.setCreateOrgCode(this.createOrgCode);
        model.setCreateOrgOrgCode(this.createOrgOrgCode);
        model.setCreateOrgSet(this.createOrgSet);
        model.setIsPortal(this.isPortal);
        model.setCreateAccount(this.createAccount);
        model.setCheckPerson(this.checkPerson);
        return model;
    }
}