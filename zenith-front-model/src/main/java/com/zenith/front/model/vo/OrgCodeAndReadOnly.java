package com.zenith.front.model.vo;

import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

import java.io.Serializable;

/**
 *
 * 登录返回的用户管理机构的数据数据
 * @date 2019/03/25
 * <AUTHOR>
 */
@ToString
@Data
@NoArgsConstructor
public class OrgCodeAndReadOnly implements Serializable {
    private static final long serialVersionUID = 5837280532780071146L;
    private String  orgCode;
    private Integer isReadOnly;
}
