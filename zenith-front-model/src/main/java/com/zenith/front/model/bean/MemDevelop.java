package com.zenith.front.model.bean;

import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.zenith.front.common.encrypt.typehandler.EncryptTypeHandler;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2021-07-11
 */
@TableName(value = "ccp_mem_develop", autoResultMap = true)
public class MemDevelop extends Model<MemDevelop> {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    @TableField("code")
    private String code;

    @TableField("es_id")
    private String esId;

    /**
     * 发展党员姓名
     */
    @TableField(value = "name", typeHandler = EncryptTypeHandler.class)
    private String name;

    @TableField("pinyin")
    private String pinyin;

    /**
     * 发展党员身份证
     */
    @TableField(value = "idcard", typeHandler = EncryptTypeHandler.class)
    private String idcard;

    /**
     * 民族code
     */
    @TableField("d06_code")
    private String d06Code;

    /**
     * 民族名称
     */
    @TableField("d06_name")
    private String d06Name;

    /**
     * 籍贯
     */
    @TableField("d48_code")
    private String d48Code;

    /**
     * 籍贯名称
     */
    @TableField("d48_name")
    private String d48Name;

    /**
     * 性别
     */
    @TableField("sex_code")
    private String sexCode;

    /**
     * 性别name
     */
    @TableField("sex_name")
    private String sexName;

    /**
     * 出生日期
     */
    @TableField("birthday")
    private Date birthday;

    /**
     * 电话
     */
    @TableField(value = "phone", typeHandler = EncryptTypeHandler.class)
    private String phone;

    /**
     * 学历code
     */
    @TableField("d07_code")
    private String d07Code;

    /**
     * 学历名称
     */
    @TableField("d07_name")
    private String d07Name;

    /**
     * 工作岗位
     */
    @TableField("d09_code")
    private String d09Code;

    /**
     * 工作岗位名称
     */
    @TableField("d09_name")
    private String d09Name;

    /**
     * 婚姻状况code
     */
    @TableField("d60_code")
    private String d60Code;

    /**
     * 婚姻状况名称
     */
    @TableField("d60_name")
    private String d60Name;

    /**
     * 参加工作日期
     */
    @TableField("join_work_date")
    private Date joinWorkDate;

    /**
     * 申请入党日期
     */
    @TableField("apply_date")
    private Date applyDate;

    /**
     * 档案管理单位名称
     */
    @TableField("archive_unit")
    private String archiveUnit;

    /**
     * 家庭住址
     */
    @TableField(value = "home_address", typeHandler = EncryptTypeHandler.class)
    private String homeAddress;

    /**
     * 聘任专业技术职务名称
     */
    @TableField("d19_name")
    private String d19Name;

    /**
     * 聘任专业技术职务code
     */
    @TableField("d19_code")
    private String d19Code;

    /**
     * 新社会阶层类型名称
     */
    @TableField("d20_name")
    private String d20Name;

    /**
     * 新社会阶层类型code
     */
    @TableField("d20_code")
    private String d20Code;

    /**
     * 一线情况code
     */
    @TableField("d21_code")
    private String d21Code;

    /**
     * 一线情况名称
     */
    @TableField("d21_name")
    private String d21Name;

    /**
     * 发展党员类型code
     */
    @TableField("d08_code")
    private String d08Code;

    /**
     * 发展党员类型名称
     */
    @TableField("d08_name")
    private String d08Name;

    /**
     * 是否农民工
     */
    @TableField("is_farmer")
    private Integer isFarmer;

    /**
     * 入党申请书扫描件
     */
    @TableField("file_url")
    private String fileUrl;

    /**
     * 聘任日期
     */
    @TableField("appointment_date")
    private Date appointmentDate;

    /**
     * 入党递交党组织code
     */
    @TableField("applied_org_code")
    private String appliedOrgCode;

    @TableField("applied_org_zb_code")
    private String appliedOrgZbCode;

    @TableField("applied_org_name")
    private String appliedOrgName;

    /**
     * 入党递交党组织层级码
     */
    @TableField("develop_applied_org_code")
    private String developAppliedOrgCode;

    /**
     * 失联日期
     */
    @TableField("loss_date")
    private Date lossDate;

    /**
     * 终止日期
     */
    @TableField("appointment_end_date")
    private Date appointmentEndDate;

    /**
     * 是否高知识群体
     */
    @TableField("is_high_knowledge")
    private Integer isHighKnowledge;

    /**
     * 失联情况
     */
    @TableField("d18_name")
    private String d18Name;

    /**
     * 失联情况code
     */
    @TableField("d18_code")
    private String d18Code;

    /**
     * 管理党组织code
     */
    @TableField("org_code")
    private String orgCode;

    /**
     * 管理党组织层级码
     */
    @TableField("develop_org_code")
    private String developOrgCode;

    @TableField("org_name")
    private String orgName;

    @TableField("org_zb_code")
    private String orgZbCode;

    /**
     * 是否是先进模范人物
     */
    @TableField("advanced_model_code")
    private String advancedModelCode;

    @TableField("create_time")
    private Date createTime;

    @TableField(value = "update_time", fill = FieldFill.UPDATE)
    private Date updateTime;

    /**
     * 召开支委会日期(成为积极分子时间
     */
    @TableField(value = "active_date")
    private Date activeDate;

    /**
     * 召开支委会日期(成为发展对象时间
     */
    @TableField(value = "object_date")
    private Date objectDate;

    /**
     * 加入中共组织的类别code
     */
    @TableField("join_org_type")
    private String joinOrgType;

    /**
     * 进入支部类型
     */
    @TableField("d11_name")
    private String d11Name;

    /**
     * 进入支部类型code
     */
    @TableField("d11_code")
    private String d11Code;

    /**
     * 加入中共组织的类别名称
     */
    @TableField("join_org_name")
    private String joinOrgName;

    /**
     * 是否劳务派遣
     */
    @TableField("is_dispatch")
    private Integer isDispatch;

    @TableField("delete_time")
    private Date deleteTime;

    @TableField("timestamp")
    private Date timestamp;

    @TableField("zb_code")
    private String zbCode;

    @TableField("is_history")
    private Integer isHistory;

    @TableField("update_account")
    private String updateAccount;

    /**
     * 是否系统外,1-系统外,0--系统内
     */
    @TableField("is_out_system")
    private Integer isOutSystem;

    /**
     * 系统外发展时所在党支部name
     */
    @TableField("out_branch_org_name")
    private String outBranchOrgName;

    @TableField(exist = false)
    private String memOrgCode;

    /**
     * 说明理由
     */
    @TableField("instructions")
    private String instructions;
    /**
     * 毕业院校（专科及以上填写）
     */
    @TableField("byyx")
    private String byyx;
    /**
     * 毕业专业（专科及以上填写）
     */
    @TableField("d88_code")
    private String d88Code;

    @TableField("d88_name")
    private String d88Name;

    @TableField("has_young_farmers")
    private Integer hasYoungFarmers;

    @TableField("has_worker")
    private Integer hasWorker;

    @TableField("politics_code")
    private String politicsCode;

    @TableField("politics_name")
    private String politicsName;

    /**
     * 在读院校
     */
    @TableField("reading_college")
    private String readingCollege;

    /**
     * 在读专业
     */
    @TableField("reading_professional_code")
    private String readingProfessionalCode;

    /**
     * 在读专业名称
     */
    @TableField("reading_professional_name")
    private String readingProfessionalName;

    /**
     * 学制
     */
    @TableField("educational_system")
    private String educationalSystem;


    /**
     * 是否死亡
     */
    @TableField("has_dead")
    private Integer hasDead;


    /**
     * 死亡时间
     */
    @TableField("dead_time")
    private Date deadTime;


    /**
     * 死亡时间
     */
    @TableField("ratification_time")
    private Date ratificationTime;

    /**
     * 工作性质
     */
    @TableField("job_nature_code")
    private String jobNatureCode;

    /**
     * 专业技术职称
     */
    @TableField("d126_code")
    private String d126Code;

    /**
     * 专业技术职称名称
     */
    @TableField("d126_name")
    private String d126Name;

    /**
     * 统计单位
     */
    @TableField("statistical_unit")
    private String statisticalUnit;
    /**
     * 中间交换区单独传的单位标识
     */
    @TableField(exist = false)
    private String middleUnitCode;
    /**
     * 中间交换区单独传的单位名称
     */
    @TableField(exist = false)
    private String middleUnitName;
    /**
     * 自定义单位名称
     */
    @TableField(exist = false)
    private String selfUnitName;
    /**
     * 类别
     */
    @TableField(exist = false)
    private String d01Code;

    @TableField("unit_information")
    private String unitInformation;

    @TableField("d04_code")
    private String d04Code;

    @TableField("has_unit_province")
    private Integer hasUnitProvince;

    @TableField("\"has_unit_statistics\"")
    private Integer hasUnitStatistics;
    /**
     * 取消发展原因
     */
    @TableField("cancel_develop_reason")
    private String cancelDevelopReason;

    /**
     * 入学时间
     */
    @TableField("enter_school_date")
    private Date enterSchoolDate;
    /**
     * 是否 在关系转接中
     */
    @TableField("is_transfer")
    private Integer isTransfer;

    /**
     * 知识分子情况
     */
    @TableField("d154_code")
    private String d154Code;
    @TableField("d154_name")
    private String d154Name;

    /**
     * 入党申请人消息提示
     */
    @TableField(exist = false)
    private String joinMessage;

    /**
     * 积极分子消息提示
     */
    @TableField(exist = false)
    private String activistMessage;

    /**
     * 扩展参数
     */
    @TableField(exist = false)
    private String lastCode;

    /**
     * 扩展参数
     */
    @TableField(exist = false)
    private String lastName;
    /**
     * 是否超时自动锁定 1是
     */
    @TableField(exist = false)
    private String isAutoLock;
    /**
     * 是否需要自动计算年级（0 否 ，1 是）
     */
    @TableField("has_calculation_grade")
    private Integer hasCalculationGrade;

    /**
     * 国民经济行业CODE
     */
    @TableField("d194_code")
    private String d194Code;

    /**
     * 与国民经济行业name
     */
    @TableField("d194_name")
    private String d194Name;

    /**
     * 生产性服务行业CODE
     */
    @TableField("d195_code")
    private String d195Code;

    /**
     * 生产性服务行业name
     */
    @TableField("d195_name")
    private String d195Name;

    /**
     * 档案批次唯一码
     */
    @TableField("digital_lot_no")
    private String digitalLotNo;

    /**
     * 流程节点:RD_1(新增入党申请)；RD_2_1(一月内需要谈话)；RD_2_2(谈话时间少于10天)；RD_2_3(一月内未进行谈话)；RD_3(已谈话)；RD_4(满足积极份子)；	 JJ_1(待考察人员)；JJ_2(待第一次考察)；JJ_3(待第二次考察)；JJ_4(超半月未按时考察)；JJ_5(持续考察人员)； JJ_6（上级党委备案）；JJ_7(发展对象阶段)；	 FZ_1(支委会审查)；FZ_2(党总支审查)；FZ_3_1(一月内进行预审)；FZ_3_2（预审时间少于十天）；FZ_3_3(一月内未进行预审)；FZ_4(县级党委预审)；FZ_5_1(一月内进行讨论人员)；FZ_5_2(讨论时间少于五天)；FZ_5_3(一月内未进行讨论)；FZ_6_1(三个月内审批人员)；FZ_6_2(审批时间少于十天)；FZ_6_3(审批超时)；FZ_6_4(特殊原因延长审批)；FZ_7(接收预备党员)；
     */
    @TableField("process_node")
    private String processNode;

    /**
     * 档案完整度：1-完整；0-不完整
     */
    @TableField("is_integrality")
    private Integer isIntegrality;
    /**
     * 县委审核状态：1未审核，2审核通过，3审核未通过，默认为0
     */
    @TableField("audit_status")
    private Integer auditStatus;
    /**
     * 遵义流程是否有审批权限
     */
    @TableField(exist = false)
    private Boolean approve;
    /**
     * 党员身份证和姓名编辑次数
     */
    @TableField("edit_identity_count")
    private Integer editIdentityCount;

    public Integer getEditIdentityCount() {
        return editIdentityCount;
    }

    public void setEditIdentityCount(Integer editIdentityCount) {
        this.editIdentityCount = editIdentityCount;
    }

    public Boolean getApprove() {
        return approve;
    }

    public void setApprove(Boolean approve) {
        this.approve = approve;
    }

    public String getDigitalLotNo() {
        return digitalLotNo;
    }

    public void setDigitalLotNo(String digitalLotNo) {
        this.digitalLotNo = digitalLotNo;
    }

    public String getProcessNode() {
        return processNode;
    }

    public void setProcessNode(String processNode) {
        this.processNode = processNode;
    }

    public Integer getIsIntegrality() {
        return isIntegrality;
    }

    public void setIsIntegrality(Integer isIntegrality) {
        this.isIntegrality = isIntegrality;
    }

    public Integer getAuditStatus() {
        return auditStatus;
    }

    public void setAuditStatus(Integer auditStatus) {
        this.auditStatus = auditStatus;
    }

    public String getD194Code() {
        return d194Code;
    }

    public void setD194Code(String d194Code) {
        this.d194Code = d194Code;
    }

    public String getD194Name() {
        return d194Name;
    }

    public void setD194Name(String d194Name) {
        this.d194Name = d194Name;
    }

    public String getD195Code() {
        return d195Code;
    }

    public void setD195Code(String d195Code) {
        this.d195Code = d195Code;
    }

    public String getD195Name() {
        return d195Name;
    }

    public void setD195Name(String d195Name) {
        this.d195Name = d195Name;
    }

    public Date getEnterSchoolDate() {
        return enterSchoolDate;
    }

    public void setEnterSchoolDate(Date enterSchoolDate) {
        this.enterSchoolDate = enterSchoolDate;
    }

    public String getCancelDevelopReason() {
        return cancelDevelopReason;
    }

    public void setCancelDevelopReason(String cancelDevelopReason) {
        this.cancelDevelopReason = cancelDevelopReason;
    }

    public Integer getHasUnitStatistics() {
        return hasUnitStatistics;
    }

    public void setHasUnitStatistics(Integer hasUnitStatistics) {
        this.hasUnitStatistics = hasUnitStatistics;
    }

    public String getStatisticalUnit() {
        return statisticalUnit;
    }

    public void setStatisticalUnit(String statisticalUnit) {
        this.statisticalUnit = statisticalUnit;
    }

    public String getMiddleUnitCode() {
        return middleUnitCode;
    }

    public void setMiddleUnitCode(String middleUnitCode) {
        this.middleUnitCode = middleUnitCode;
    }

    public String getMiddleUnitName() {
        return middleUnitName;
    }

    public void setMiddleUnitName(String middleUnitName) {
        this.middleUnitName = middleUnitName;
    }

    public String getSelfUnitName() {
        return selfUnitName;
    }

    public void setSelfUnitName(String selfUnitName) {
        this.selfUnitName = selfUnitName;
    }

    public String getD01Code() {
        return d01Code;
    }

    public void setD01Code(String d01Code) {
        this.d01Code = d01Code;
    }

    public String getUnitInformation() {
        return unitInformation;
    }

    public void setUnitInformation(String unitInformation) {
        this.unitInformation = unitInformation;
    }

    public String getD04Code() {
        return d04Code;
    }

    public void setD04Code(String d04Code) {
        this.d04Code = d04Code;
    }

    public Integer getHasUnitProvince() {
        return hasUnitProvince;
    }

    public void setHasUnitProvince(Integer hasUnitProvince) {
        this.hasUnitProvince = hasUnitProvince;
    }

    public String getD126Code() {
        return d126Code;
    }

    public void setD126Code(String d126Code) {
        this.d126Code = d126Code;
    }

    public String getD126Name() {
        return d126Name;
    }

    public void setD126Name(String d126Name) {
        this.d126Name = d126Name;
    }

    public String getJobNatureCode() {
        return jobNatureCode;
    }

    public void setJobNatureCode(String jobNatureCode) {
        this.jobNatureCode = jobNatureCode;
    }

    public Integer getHasDead() {
        return hasDead;
    }

    public void setHasDead(Integer hasDead) {
        this.hasDead = hasDead;
    }

    public Date getDeadTime() {
        return deadTime;
    }

    public void setDeadTime(Date deadTime) {
        this.deadTime = deadTime;
    }

    public Date getRatificationTime() {
        return ratificationTime;
    }

    public void setRatificationTime(Date ratificationTime) {
        this.ratificationTime = ratificationTime;
    }

    public String getReadingCollege() {
        return readingCollege;
    }

    public void setReadingCollege(String readingCollege) {
        this.readingCollege = readingCollege;
    }

    public String getReadingProfessionalCode() {
        return readingProfessionalCode;
    }

    public void setReadingProfessionalCode(String readingProfessionalCode) {
        this.readingProfessionalCode = readingProfessionalCode;
    }

    public String getReadingProfessionalName() {
        return readingProfessionalName;
    }

    public void setReadingProfessionalName(String readingProfessionalName) {
        this.readingProfessionalName = readingProfessionalName;
    }

    public String getEducationalSystem() {
        return educationalSystem;
    }

    public void setEducationalSystem(String educationalSystem) {
        this.educationalSystem = educationalSystem;
    }

    public String getPoliticsCode() {
        return politicsCode;
    }

    public void setPoliticsCode(String politicsCode) {
        this.politicsCode = politicsCode;
    }

    public String getPoliticsName() {
        return politicsName;
    }

    public void setPoliticsName(String politicsName) {
        this.politicsName = politicsName;
    }

    public Integer getHasYoungFarmers() {
        return hasYoungFarmers;
    }

    public void setHasYoungFarmers(Integer hasYoungFarmers) {
        this.hasYoungFarmers = hasYoungFarmers;
    }

    public Integer getHasWorker() {
        return hasWorker;
    }

    public void setHasWorker(Integer hasWorker) {
        this.hasWorker = hasWorker;
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getEsId() {
        return esId;
    }

    public void setEsId(String esId) {
        this.esId = esId;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getPinyin() {
        return pinyin;
    }

    public void setPinyin(String pinyin) {
        this.pinyin = pinyin;
    }

    public String getIdcard() {
        return idcard;
    }

    public void setIdcard(String idcard) {
        this.idcard = idcard;
    }

    public String getD06Code() {
        return d06Code;
    }

    public void setD06Code(String d06Code) {
        this.d06Code = d06Code;
    }

    public String getD06Name() {
        return d06Name;
    }

    public void setD06Name(String d06Name) {
        this.d06Name = d06Name;
    }

    public String getD48Code() {
        return d48Code;
    }

    public void setD48Code(String d48Code) {
        this.d48Code = d48Code;
    }

    public String getD48Name() {
        return d48Name;
    }

    public void setD48Name(String d48Name) {
        this.d48Name = d48Name;
    }

    public String getSexCode() {
        return sexCode;
    }

    public void setSexCode(String sexCode) {
        this.sexCode = sexCode;
    }

    public String getSexName() {
        return sexName;
    }

    public void setSexName(String sexName) {
        this.sexName = sexName;
    }

    public Date getBirthday() {
        return birthday;
    }

    public void setBirthday(Date birthday) {
        this.birthday = birthday;
    }

    public String getPhone() {
        return phone;
    }

    public void setPhone(String phone) {
        this.phone = phone;
    }

    public String getD07Code() {
        return d07Code;
    }

    public void setD07Code(String d07Code) {
        this.d07Code = d07Code;
    }

    public String getD07Name() {
        return d07Name;
    }

    public void setD07Name(String d07Name) {
        this.d07Name = d07Name;
    }

    public String getD09Code() {
        return d09Code;
    }

    public void setD09Code(String d09Code) {
        this.d09Code = d09Code;
    }

    public String getD09Name() {
        return d09Name;
    }

    public void setD09Name(String d09Name) {
        this.d09Name = d09Name;
    }

    public String getD60Code() {
        return d60Code;
    }

    public void setD60Code(String d60Code) {
        this.d60Code = d60Code;
    }

    public String getD60Name() {
        return d60Name;
    }

    public void setD60Name(String d60Name) {
        this.d60Name = d60Name;
    }

    public Date getJoinWorkDate() {
        return joinWorkDate;
    }

    public void setJoinWorkDate(Date joinWorkDate) {
        this.joinWorkDate = joinWorkDate;
    }

    public Date getApplyDate() {
        return applyDate;
    }

    public void setApplyDate(Date applyDate) {
        this.applyDate = applyDate;
    }

    public String getArchiveUnit() {
        return archiveUnit;
    }

    public void setArchiveUnit(String archiveUnit) {
        this.archiveUnit = archiveUnit;
    }

    public String getHomeAddress() {
        return homeAddress;
    }

    public void setHomeAddress(String homeAddress) {
        this.homeAddress = homeAddress;
    }

    public String getD19Name() {
        return d19Name;
    }

    public void setD19Name(String d19Name) {
        this.d19Name = d19Name;
    }

    public String getD19Code() {
        return d19Code;
    }

    public void setD19Code(String d19Code) {
        this.d19Code = d19Code;
    }

    public String getD20Name() {
        return d20Name;
    }

    public void setD20Name(String d20Name) {
        this.d20Name = d20Name;
    }

    public String getD20Code() {
        return d20Code;
    }

    public void setD20Code(String d20Code) {
        this.d20Code = d20Code;
    }

    public String getD21Code() {
        return d21Code;
    }

    public void setD21Code(String d21Code) {
        this.d21Code = d21Code;
    }

    public String getD21Name() {
        return d21Name;
    }

    public void setD21Name(String d21Name) {
        this.d21Name = d21Name;
    }

    public String getD08Code() {
        return d08Code;
    }

    public void setD08Code(String d08Code) {
        this.d08Code = d08Code;
    }

    public String getD08Name() {
        return d08Name;
    }

    public void setD08Name(String d08Name) {
        this.d08Name = d08Name;
    }

    public Integer getIsFarmer() {
        return isFarmer;
    }

    public void setIsFarmer(Integer isFarmer) {
        this.isFarmer = isFarmer;
    }

    public String getFileUrl() {
        return fileUrl;
    }

    public void setFileUrl(String fileUrl) {
        this.fileUrl = fileUrl;
    }

    public Date getAppointmentDate() {
        return appointmentDate;
    }

    public void setAppointmentDate(Date appointmentDate) {
        this.appointmentDate = appointmentDate;
    }

    public String getAppliedOrgCode() {
        return appliedOrgCode;
    }

    public void setAppliedOrgCode(String appliedOrgCode) {
        this.appliedOrgCode = appliedOrgCode;
    }

    public String getAppliedOrgZbCode() {
        return appliedOrgZbCode;
    }

    public void setAppliedOrgZbCode(String appliedOrgZbCode) {
        this.appliedOrgZbCode = appliedOrgZbCode;
    }

    public String getAppliedOrgName() {
        return appliedOrgName;
    }

    public void setAppliedOrgName(String appliedOrgName) {
        this.appliedOrgName = appliedOrgName;
    }

    public String getDevelopAppliedOrgCode() {
        return developAppliedOrgCode;
    }

    public void setDevelopAppliedOrgCode(String developAppliedOrgCode) {
        this.developAppliedOrgCode = developAppliedOrgCode;
    }

    public Date getLossDate() {
        return lossDate;
    }

    public void setLossDate(Date lossDate) {
        this.lossDate = lossDate;
    }

    public Date getAppointmentEndDate() {
        return appointmentEndDate;
    }

    public void setAppointmentEndDate(Date appointmentEndDate) {
        this.appointmentEndDate = appointmentEndDate;
    }

    public Integer getIsHighKnowledge() {
        return isHighKnowledge;
    }

    public void setIsHighKnowledge(Integer isHighKnowledge) {
        this.isHighKnowledge = isHighKnowledge;
    }

    public String getD18Name() {
        return d18Name;
    }

    public void setD18Name(String d18Name) {
        this.d18Name = d18Name;
    }

    public String getD18Code() {
        return d18Code;
    }

    public void setD18Code(String d18Code) {
        this.d18Code = d18Code;
    }

    public String getOrgCode() {
        return orgCode;
    }

    public void setOrgCode(String orgCode) {
        this.orgCode = orgCode;
    }

    public String getDevelopOrgCode() {
        return developOrgCode;
    }

    public void setDevelopOrgCode(String developOrgCode) {
        this.developOrgCode = developOrgCode;
    }

    public String getOrgName() {
        return orgName;
    }

    public void setOrgName(String orgName) {
        this.orgName = orgName;
    }

    public String getOrgZbCode() {
        return orgZbCode;
    }

    public void setOrgZbCode(String orgZbCode) {
        this.orgZbCode = orgZbCode;
    }

    public String getAdvancedModelCode() {
        return advancedModelCode;
    }

    public void setAdvancedModelCode(String advancedModelCode) {
        this.advancedModelCode = advancedModelCode;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    public Date getActiveDate() {
        return activeDate;
    }

    public void setActiveDate(Date activeDate) {
        this.activeDate = activeDate;
    }

    public Date getObjectDate() {
        return objectDate;
    }

    public void setObjectDate(Date objectDate) {
        this.objectDate = objectDate;
    }

    public String getJoinOrgType() {
        return joinOrgType;
    }

    public void setJoinOrgType(String joinOrgType) {
        this.joinOrgType = joinOrgType;
    }

    public String getD11Name() {
        return d11Name;
    }

    public void setD11Name(String d11Name) {
        this.d11Name = d11Name;
    }

    public String getD11Code() {
        return d11Code;
    }

    public void setD11Code(String d11Code) {
        this.d11Code = d11Code;
    }

    public String getJoinOrgName() {
        return joinOrgName;
    }

    public void setJoinOrgName(String joinOrgName) {
        this.joinOrgName = joinOrgName;
    }

    public Integer getIsDispatch() {
        return isDispatch;
    }

    public void setIsDispatch(Integer isDispatch) {
        this.isDispatch = isDispatch;
    }

    public Date getDeleteTime() {
        return deleteTime;
    }

    public void setDeleteTime(Date deleteTime) {
        this.deleteTime = deleteTime;
    }

    public Date getTimestamp() {
        return timestamp;
    }

    public void setTimestamp(Date timestamp) {
        this.timestamp = timestamp;
    }

    public String getZbCode() {
        return zbCode;
    }

    public void setZbCode(String zbCode) {
        this.zbCode = zbCode;
    }

    public Integer getIsHistory() {
        return isHistory;
    }

    public void setIsHistory(Integer isHistory) {
        this.isHistory = isHistory;
    }

    public String getUpdateAccount() {
        return updateAccount;
    }

    public void setUpdateAccount(String updateAccount) {
        this.updateAccount = updateAccount;
    }

    public Integer getIsOutSystem() {
        return isOutSystem;
    }

    public void setIsOutSystem(Integer isOutSystem) {
        this.isOutSystem = isOutSystem;
    }

    public String getOutBranchOrgName() {
        return outBranchOrgName;
    }

    public void setOutBranchOrgName(String outBranchOrgName) {
        this.outBranchOrgName = outBranchOrgName;
    }

    public String getMemOrgCode() {
        return memOrgCode;
    }

    public void setMemOrgCode(String memOrgCode) {
        this.memOrgCode = memOrgCode;
    }

    public String getInstructions() {
        return instructions;
    }

    public void setInstructions(String instructions) {
        this.instructions = instructions;
    }

    public String getByyx() {
        return byyx;
    }

    public void setByyx(String byyx) {
        this.byyx = byyx;
    }

    public String getD88Code() {
        return d88Code;
    }

    public void setD88Code(String d88Code) {
        this.d88Code = d88Code;
    }

    public String getD88Name() {
        return d88Name;
    }

    public void setD88Name(String d88Name) {
        this.d88Name = d88Name;
    }

    public Integer getIsTransfer() {
        return isTransfer;
    }

    public void setIsTransfer(Integer isTransfer) {
        this.isTransfer = isTransfer;
    }

    public String getD154Code() {
        return d154Code;
    }

    public void setD154Code(String d154Code) {
        this.d154Code = d154Code;
    }

    public String getD154Name() {
        return d154Name;
    }

    public void setD154Name(String d154Name) {
        this.d154Name = d154Name;
    }

    public String getJoinMessage() {
        return joinMessage;
    }

    public void setJoinMessage(String joinMessage) {
        this.joinMessage = joinMessage;
    }

    public String getActivistMessage() {
        return activistMessage;
    }

    public void setActivistMessage(String activistMessage) {
        this.activistMessage = activistMessage;
    }

    public String getLastCode() {
        return lastCode;
    }

    public void setLastCode(String lastCode) {
        this.lastCode = lastCode;
    }

    public String getLastName() {
        return lastName;
    }

    public void setLastName(String lastName) {
        this.lastName = lastName;
    }

    public String getIsAutoLock() {
        return isAutoLock;
    }

    public void setIsAutoLock(String isAutoLock) {
        this.isAutoLock = isAutoLock;
    }

    public Integer getHasCalculationGrade() {
        return hasCalculationGrade;
    }

    public void setHasCalculationGrade(Integer hasCalculationGrade) {
        this.hasCalculationGrade = hasCalculationGrade;
    }

    @Override
    public Serializable pkVal() {
        return this.id;
    }

    @Override
    public String toString() {
        return "MemDevelop{" +
                "id=" + id +
                ", code=" + code +
                ", esId=" + esId +
                ", name=" + name +
                ", pinyin=" + pinyin +
                ", idcard=" + idcard +
                ", d06Code=" + d06Code +
                ", d06Name=" + d06Name +
                ", d48Code=" + d48Code +
                ", d48Name=" + d48Name +
                ", sexCode=" + sexCode +
                ", sexName=" + sexName +
                ", birthday=" + birthday +
                ", phone=" + phone +
                ", d07Code=" + d07Code +
                ", d07Name=" + d07Name +
                ", d09Code=" + d09Code +
                ", d09Name=" + d09Name +
                ", d60Code=" + d60Code +
                ", d60Name=" + d60Name +
                ", joinWorkDate=" + joinWorkDate +
                ", applyDate=" + applyDate +
                ", archiveUnit=" + archiveUnit +
                ", homeAddress=" + homeAddress +
                ", d19Name=" + d19Name +
                ", d19Code=" + d19Code +
                ", d20Name=" + d20Name +
                ", d20Code=" + d20Code +
                ", d21Code=" + d21Code +
                ", d21Name=" + d21Name +
                ", d08Code=" + d08Code +
                ", d08Name=" + d08Name +
                ", isFarmer=" + isFarmer +
                ", fileUrl=" + fileUrl +
                ", appointmentDate=" + appointmentDate +
                ", appliedOrgCode=" + appliedOrgCode +
                ", appliedOrgZbCode=" + appliedOrgZbCode +
                ", appliedOrgName=" + appliedOrgName +
                ", developAppliedOrgCode=" + developAppliedOrgCode +
                ", lossDate=" + lossDate +
                ", appointmentEndDate=" + appointmentEndDate +
                ", isHighKnowledge=" + isHighKnowledge +
                ", d18Name=" + d18Name +
                ", d18Code=" + d18Code +
                ", orgCode=" + orgCode +
                ", developOrgCode=" + developOrgCode +
                ", orgName=" + orgName +
                ", orgZbCode=" + orgZbCode +
                ", createTime=" + createTime +
                ", updateTime=" + updateTime +
                ", activeDate=" + activeDate +
                ", objectDate=" + objectDate +
                ", joinOrgType=" + joinOrgType +
                ", d11Name=" + d11Name +
                ", d11Code=" + d11Code +
                ", joinOrgName=" + joinOrgName +
                ", isDispatch=" + isDispatch +
                ", deleteTime=" + deleteTime +
                ", timestamp=" + timestamp +
                ", zbCode=" + zbCode +
                ", isHistory=" + isHistory +
                ", updateAccount=" + updateAccount +
                ", isOutSystem=" + isOutSystem +
                ", outBranchOrgName=" + outBranchOrgName +
                ", enterSchoolDate=" + enterSchoolDate +
                ", hasCalculationGrade=" + hasCalculationGrade +
                ", d194Code=" + d194Code +
                ", d195Code=" + d195Code +
                ", d195Name=" + d195Name +
                ", d194Name=" + d194Name +
                "}";
    }
}
