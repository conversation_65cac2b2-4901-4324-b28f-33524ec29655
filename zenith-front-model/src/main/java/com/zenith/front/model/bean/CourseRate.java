package com.zenith.front.model.bean;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import java.util.Date;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableField;
import java.io.Serializable;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2021-07-11
 */
@TableName("ccp_course_rate")
public class CourseRate extends Model<CourseRate> {

    private static final long serialVersionUID=1L;

    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.ASSIGN_UUID)
    private String id;

    /**
     * 课程主键
     */
    @TableField("course_id")
    private String courseId;

    /**
     * 用户编号
     */
    @TableField("user_id")
    private String userId;

    /**
     * 1 已读 2 未读 3 点赞 4 评论 5 已完成 6 进行中 7 未开始 8 待阅卷
     */
    @TableField("type")
    private String type;

    /**
     * 评论内容
     */
    @TableField("comment_content")
    private String commentContent;

    /**
     * 观看时长
     */
    @TableField("watch_time")
    private Integer watchTime;

    /**
     * 点赞时间
     */
    @TableField("like_time")
    private Date likeTime;

    /**
     * 评论时间
     */
    @TableField("comment_time")
    private Date commentTime;

    /**
     * 完成时间
     */
    @TableField("completion_time")
    private Date completionTime;

    /**
     * 创建时间
     */
    @TableField("gmt_create")
    private Date gmtCreate;

    /**
     * 更新时间
     */
    @TableField("gmt_modified")
    private Date gmtModified;

    /**
     * 1 true 0 false
     */
    @TableField("delete")
    private Integer delete;


    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getCourseId() {
        return courseId;
    }

    public void setCourseId(String courseId) {
        this.courseId = courseId;
    }

    public String getUserId() {
        return userId;
    }

    public void setUserId(String userId) {
        this.userId = userId;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public String getCommentContent() {
        return commentContent;
    }

    public void setCommentContent(String commentContent) {
        this.commentContent = commentContent;
    }

    public Integer getWatchTime() {
        return watchTime;
    }

    public void setWatchTime(Integer watchTime) {
        this.watchTime = watchTime;
    }

    public Date getLikeTime() {
        return likeTime;
    }

    public void setLikeTime(Date likeTime) {
        this.likeTime = likeTime;
    }

    public Date getCommentTime() {
        return commentTime;
    }

    public void setCommentTime(Date commentTime) {
        this.commentTime = commentTime;
    }

    public Date getCompletionTime() {
        return completionTime;
    }

    public void setCompletionTime(Date completionTime) {
        this.completionTime = completionTime;
    }

    public Date getGmtCreate() {
        return gmtCreate;
    }

    public void setGmtCreate(Date gmtCreate) {
        this.gmtCreate = gmtCreate;
    }

    public Date getGmtModified() {
        return gmtModified;
    }

    public void setGmtModified(Date gmtModified) {
        this.gmtModified = gmtModified;
    }

    public Integer getDelete() {
        return delete;
    }

    public void setDelete(Integer delete) {
        this.delete = delete;
    }

    @Override
    public Serializable pkVal() {
        return this.id;
    }

    @Override
    public String toString() {
        return "CourseRate{" +
        "id=" + id +
        ", courseId=" + courseId +
        ", userId=" + userId +
        ", type=" + type +
        ", commentContent=" + commentContent +
        ", watchTime=" + watchTime +
        ", likeTime=" + likeTime +
        ", commentTime=" + commentTime +
        ", completionTime=" + completionTime +
        ", gmtCreate=" + gmtCreate +
        ", gmtModified=" + gmtModified +
        ", delete=" + delete +
        "}";
    }
}
