package com.zenith.front.model.dto;

import com.zenith.front.model.custom.HasSubordinateField;
import com.zenith.front.model.validate.group.Common1Group;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2025/6/30
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class DigitalDataDTO extends HasSubordinateField {

    /**
     * 组织层级码
     */
    @NotBlank(groups = Common1Group.class, message = "memOrgCode 不能为空")
    private String memOrgCode;
    /**
     * 档案批次唯一码集合
     */
    @NotNull(groups = Common1Group.class, message = "档案批次唯一码 不能为空")
    private List<String> digitalLotNoList;
    /**
     * 是否导出所有：0否，1是
     */
    @NotBlank(groups = Common1Group.class, message = "isExportAll 不能为空")
    private String isExportAll;
    /**
     * 1党员信息，2发展党员信息
     */
    @NotBlank(groups = Common1Group.class, message = "type 不能为空")
    private String type;

}
