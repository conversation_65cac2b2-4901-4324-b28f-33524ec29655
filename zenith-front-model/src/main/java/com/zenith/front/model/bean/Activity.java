package com.zenith.front.model.bean;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.extension.activerecord.Model;

import java.util.Date;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableField;
import com.zenith.front.common.jsonb.JsonbTypeHandler;

import java.io.Serializable;

/**
 * <p>
 * 活动表
 * </p>
 *
 * <AUTHOR>
 * @since 2021-07-11
 */
@TableName(value = "ccp_activity", autoResultMap = true)
public class Activity extends Model<Activity> {

    private static final long serialVersionUID = 1L;

    /**
     * 活动自增长主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 活动唯一主键
     */
    @TableField("code")
    private String code;

    /**
     * 活动名称
     */
    @TableField("name")
    private String name;

    /**
     * 录入活动组织唯一标识符
     */
    @TableField("ac_org_code")
    private String acOrgCode;

    /**
     * 录入活动组织层级码
     */
    @TableField("ac_org_org_code")
    private String acOrgOrgCode;

    @TableField("group_key")
    private String groupKey;

    @TableField("group_name")
    private String groupName;

    /**
     * 党小组会集合
     */
    @TableField("group_code")
    private String groupCode;

    /**
     * 举办时间
     */
    @TableField("hold_time")
    private Date holdTime;

    /**
     * 活动类型集合
     */
    @TableField(value = "type_codes", typeHandler = JsonbTypeHandler.class)
    private Object typeCodes;

    /**
     * 活动类型集合名称
     */
    @TableField(value = "type_names", typeHandler = JsonbTypeHandler.class)
    private Object typeNames;

    /**
     * 活动地点经纬度
     */
    @TableField("location")
    private String location;

    /**
     * 活动地点
     */
    @TableField("address")
    private String address;

    /**
     * 主持人总数
     */
    @TableField("hostor_count")
    private Integer hostorCount;

    /**
     * 讲课人总数
     */
    @TableField("lecturer_count")
    private Integer lecturerCount;

    /**
     * 领导人员总数
     */
    @TableField("leader_count")
    private Integer leaderCount;

    /**
     * 应到人员总数
     */
    @TableField("mem_count")
    private Integer memCount;

    /**
     * 列席人员总数
     */
    @TableField("attend_count")
    private Integer attendCount;

    /**
     * 缺席人员总数
     */
    @TableField("absence_count")
    private Integer absenceCount;

    /**
     * 活动内容
     */
    @TableField("content")
    private String content;

    /**
     * 活动状态{1.未开始，2进行中，3已结束}
     */
    @TableField("status")
    private Integer status;

    /**
     * 是否公开{0.否，1是}
     */
    @TableField("is_open")
    private Integer isOpen;

    /**
     * 查看次数
     */
    @TableField("view_count")
    private Integer viewCount;

    /**
     * 点赞次数
     */
    @TableField("good_count")
    private Integer goodCount;

    /**
     * 是否包含附件 1有 0无
     */
    @TableField("has_file")
    private Integer hasFile;

    /**
     * pc:电脑，phone:手机
     */
    @TableField("create_type")
    private String createType;

    /**
     * 时间乐观锁
     */
    @TableField("timestamp")
    private Date timestamp;

    /**
     * 是否有图片 1有 0无
     */
    @TableField("has_image")
    private Integer hasImage;

    /**
     * 是否有视频 1有 0无
     */
    @TableField("has_video")
    private Integer hasVideo;

    /**
     * 是否有语音 1有 0无
     */
    @TableField("has_voice")
    private Integer hasVoice;

    /**
     * 是否有文档 1有 0无
     */
    @TableField("has_document")
    private Integer hasDocument;

    /**
     * 人员类型集合
     */
    @TableField("mem_filter")
    private String memFilter;

    /**
     * 取消原因
     */
    @TableField("cancel_reason")
    private String cancelReason;

    /**
     * 创建人
     */
    @TableField("creator_account")
    private String creatorAccount;

    /**
     * 活动计划（活动议程）
     */
    @TableField("ac_plan")
    private String acPlan;

    /**
     * 活动计划文件（活动议程附件)
     */
    @TableField("ac_plan_file")
    private String acPlanFile;

    /**
     * 签到方式(0无 , 1扫码签到，2打卡签到，3定位签到)
     */
    @TableField("can_review")
    private String canReview;

    /**
     * 通知方式(0无，1微信通知，2短信通知，3平台通知)
     */
    @TableField("notice_plan")
    private String noticePlan;

    /**
     * 创建时间
     */
    @TableField("create_time")
    private Date createTime;

    /**
     * 更新时间
     */
    @TableField("update_time")
    private Date updateTime;

    /**
     * 删除时间
     */
    @TableField("delete_time")
    private Date deleteTime;

    /**
     * 活动结束时间
     */
    @TableField("end_time")
    private Date endTime;

    /**
     * 录入时活动所在组织标识符集合
     */
    @TableField(value = "entry_org_code_set", typeHandler = JsonbTypeHandler.class)
    private Object entryOrgCodeSet;


    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getAcOrgCode() {
        return acOrgCode;
    }

    public void setAcOrgCode(String acOrgCode) {
        this.acOrgCode = acOrgCode;
    }

    public String getAcOrgOrgCode() {
        return acOrgOrgCode;
    }

    public void setAcOrgOrgCode(String acOrgOrgCode) {
        this.acOrgOrgCode = acOrgOrgCode;
    }

    public String getGroupKey() {
        return groupKey;
    }

    public void setGroupKey(String groupKey) {
        this.groupKey = groupKey;
    }

    public String getGroupName() {
        return groupName;
    }

    public void setGroupName(String groupName) {
        this.groupName = groupName;
    }

    public String getGroupCode() {
        return groupCode;
    }

    public void setGroupCode(String groupCode) {
        this.groupCode = groupCode;
    }

    public Date getHoldTime() {
        return holdTime;
    }

    public void setHoldTime(Date holdTime) {
        this.holdTime = holdTime;
    }

    public Object getTypeCodes() {
        return typeCodes;
    }

    public void setTypeCodes(Object typeCodes) {
        this.typeCodes = typeCodes;
    }

    public Object getTypeNames() {
        return typeNames;
    }

    public void setTypeNames(Object typeNames) {
        this.typeNames = typeNames;
    }

    public String getLocation() {
        return location;
    }

    public void setLocation(String location) {
        this.location = location;
    }

    public String getAddress() {
        return address;
    }

    public void setAddress(String address) {
        this.address = address;
    }

    public Integer getHostorCount() {
        return hostorCount;
    }

    public void setHostorCount(Integer hostorCount) {
        this.hostorCount = hostorCount;
    }

    public Integer getLecturerCount() {
        return lecturerCount;
    }

    public void setLecturerCount(Integer lecturerCount) {
        this.lecturerCount = lecturerCount;
    }

    public Integer getLeaderCount() {
        return leaderCount;
    }

    public void setLeaderCount(Integer leaderCount) {
        this.leaderCount = leaderCount;
    }

    public Integer getMemCount() {
        return memCount;
    }

    public void setMemCount(Integer memCount) {
        this.memCount = memCount;
    }

    public Integer getAttendCount() {
        return attendCount;
    }

    public void setAttendCount(Integer attendCount) {
        this.attendCount = attendCount;
    }

    public Integer getAbsenceCount() {
        return absenceCount;
    }

    public void setAbsenceCount(Integer absenceCount) {
        this.absenceCount = absenceCount;
    }

    public String getContent() {
        return content;
    }

    public void setContent(String content) {
        this.content = content;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public Integer getIsOpen() {
        return isOpen;
    }

    public void setIsOpen(Integer isOpen) {
        this.isOpen = isOpen;
    }

    public Integer getViewCount() {
        return viewCount;
    }

    public void setViewCount(Integer viewCount) {
        this.viewCount = viewCount;
    }

    public Integer getGoodCount() {
        return goodCount;
    }

    public void setGoodCount(Integer goodCount) {
        this.goodCount = goodCount;
    }

    public Integer getHasFile() {
        return hasFile;
    }

    public void setHasFile(Integer hasFile) {
        this.hasFile = hasFile;
    }

    public String getCreateType() {
        return createType;
    }

    public void setCreateType(String createType) {
        this.createType = createType;
    }

    public Date getTimestamp() {
        return timestamp;
    }

    public void setTimestamp(Date timestamp) {
        this.timestamp = timestamp;
    }

    public Integer getHasImage() {
        return hasImage;
    }

    public void setHasImage(Integer hasImage) {
        this.hasImage = hasImage;
    }

    public Integer getHasVideo() {
        return hasVideo;
    }

    public void setHasVideo(Integer hasVideo) {
        this.hasVideo = hasVideo;
    }

    public Integer getHasVoice() {
        return hasVoice;
    }

    public void setHasVoice(Integer hasVoice) {
        this.hasVoice = hasVoice;
    }

    public Integer getHasDocument() {
        return hasDocument;
    }

    public void setHasDocument(Integer hasDocument) {
        this.hasDocument = hasDocument;
    }

    public String getMemFilter() {
        return memFilter;
    }

    public void setMemFilter(String memFilter) {
        this.memFilter = memFilter;
    }

    public String getCancelReason() {
        return cancelReason;
    }

    public void setCancelReason(String cancelReason) {
        this.cancelReason = cancelReason;
    }

    public String getCreatorAccount() {
        return creatorAccount;
    }

    public void setCreatorAccount(String creatorAccount) {
        this.creatorAccount = creatorAccount;
    }

    public String getAcPlan() {
        return acPlan;
    }

    public void setAcPlan(String acPlan) {
        this.acPlan = acPlan;
    }

    public String getAcPlanFile() {
        return acPlanFile;
    }

    public void setAcPlanFile(String acPlanFile) {
        this.acPlanFile = acPlanFile;
    }

    public String getCanReview() {
        return canReview;
    }

    public void setCanReview(String canReview) {
        this.canReview = canReview;
    }

    public String getNoticePlan() {
        return noticePlan;
    }

    public void setNoticePlan(String noticePlan) {
        this.noticePlan = noticePlan;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    public Date getDeleteTime() {
        return deleteTime;
    }

    public void setDeleteTime(Date deleteTime) {
        this.deleteTime = deleteTime;
    }

    public Date getEndTime() {
        return endTime;
    }

    public void setEndTime(Date endTime) {
        this.endTime = endTime;
    }

    public Object getEntryOrgCodeSet() {
        return entryOrgCodeSet;
    }

    public void setEntryOrgCodeSet(Object entryOrgCodeSet) {
        this.entryOrgCodeSet = entryOrgCodeSet;
    }

    @Override
    public Serializable pkVal() {
        return this.id;
    }

    @Override
    public String toString() {
        return "Activity{" +
                "id=" + id +
                ", code=" + code +
                ", name=" + name +
                ", acOrgCode=" + acOrgCode +
                ", acOrgOrgCode=" + acOrgOrgCode +
                ", groupKey=" + groupKey +
                ", groupName=" + groupName +
                ", groupCode=" + groupCode +
                ", holdTime=" + holdTime +
                ", typeCodes=" + typeCodes +
                ", typeNames=" + typeNames +
                ", location=" + location +
                ", address=" + address +
                ", hostorCount=" + hostorCount +
                ", lecturerCount=" + lecturerCount +
                ", leaderCount=" + leaderCount +
                ", memCount=" + memCount +
                ", attendCount=" + attendCount +
                ", absenceCount=" + absenceCount +
                ", content=" + content +
                ", status=" + status +
                ", isOpen=" + isOpen +
                ", viewCount=" + viewCount +
                ", goodCount=" + goodCount +
                ", hasFile=" + hasFile +
                ", createType=" + createType +
                ", timestamp=" + timestamp +
                ", hasImage=" + hasImage +
                ", hasVideo=" + hasVideo +
                ", hasVoice=" + hasVoice +
                ", hasDocument=" + hasDocument +
                ", memFilter=" + memFilter +
                ", cancelReason=" + cancelReason +
                ", creatorAccount=" + creatorAccount +
                ", acPlan=" + acPlan +
                ", acPlanFile=" + acPlanFile +
                ", canReview=" + canReview +
                ", noticePlan=" + noticePlan +
                ", createTime=" + createTime +
                ", updateTime=" + updateTime +
                ", deleteTime=" + deleteTime +
                ", endTime=" + endTime +
                ", entryOrgCodeSet=" + entryOrgCodeSet +
                "}";
    }
}
