package com.zenith.front.model.dto;

import lombok.Data;
import lombok.ToString;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2021/9/10 14:13
 * @Version 1.0
 */
@Data
@ToString
public class UnitIncomeDTO {
    /**
     * 唯一标识符code
     */
    private String code;

    /**
     * 集体经济code
     */
    private String economicCode;

    /**
     * 收入金额（万元）
     */
    private BigDecimal incomeAmount;

    /**
     * 负债金额（万元）
     */
    private BigDecimal incurDebtsAmount;

    /**
     * 收入情况 [{id:主键，type:收入类别，typeName:类别名称，amount：收入金额（万元），remark：备注}]，字典表dict_d130
     */
    private List<UnitIncomeSubDto> income;

    /**
     * 支出情况（同上） dict_d1301
     */
    private List<UnitIncomeSubDto> outlay;

    /**
     * 资产情况（同上） dict_d1302
     */
    private List<UnitIncomeSubDto> property;

    /**
     * 所有者权益情况（同上） dict_d1303
     */
    private List<UnitIncomeSubDto> ownership;
    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 单位code
     */
    private String unitCode;

    /**
     * 是否由村党组织书记担任村集体经济负责人
     */
    private Integer hasSecretaryEconomy;

    /**
     * 集体经济负债（万）
     */
    private BigDecimal collectiveEconomicLiabilities;
//
//    /**
//     * 是否获中央和省级财政扶持资金（1是 0否）
//     */
//    private Integer hasFinancialSupport;
//
//    /**
//     * 中央和省级财政扶持资金
//     */
//    private BigDecimal financialSupportEnforced;
//
//    /**
//     * 中央和省级财政扶持资金执行率
//     */
//    private String enforced;
//
//    /**
//     * 已完工验收项目数字
//     */
//    private Integer completedAcceptanceProjects;
//
//    /**
//     * 已获得收益
//     */
//    private BigDecimal incomeObtained;

}
