package com.zenith.front.model.dto;

import lombok.Data;

import javax.validation.constraints.Max;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotBlank;

/**
 * <AUTHOR>
 * @create_date 2025-05-07 16:30
 * @description
 */
@Data
public class UnitMemSelectListDTO {
    /**
     * 单位code 不能为空
     */
    @NotBlank(message = "单位code 不能为空")
    private String unitCode;
    /**
     * 分页页数
     */
    @Min(value = 1, message = "页码最小为1")
    private Integer pageNum;
    /**
     * 分页每页数
     */
    @Max(value = 100, message = "每页条数大小范围在1-100")
    private Integer pageSize;
}
