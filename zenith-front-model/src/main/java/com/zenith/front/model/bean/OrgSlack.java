package com.zenith.front.model.bean;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2021-08-05
 */
@TableName("ccp_org_slack")
public class OrgSlack extends Model<OrgSlack> {

    private static final long serialVersionUID = 1L;

    /**
     * 自增长主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 唯一标识符code
     */
    @TableField("code")
    private String code;

    /**
     * 组织唯一标识符，中组部code
     */
    @TableField("zb_code")
    private String zbCode;

    /**
     * 组织code
     */
    @TableField("org_code")
    private String orgCode;
    /**
     * 组织层级码
     */
    @TableField("slack_org_code")
    private String slackOrgCode;

    /**
     * 涣散组织名称
     */
    @TableField(exist = false)
    private String name;

    /**
     * 涣散类型（多选逗号分隔）
     */
    @TableField("d74_code")
    private String d74Code;

    /**
     * 涣散类型名称
     */
    @TableField("d74_name")
    private String d74Name;

    /**
     * 整顿开始时间
     */
    @TableField("neaten_time")
    private Date neatenTime;

    /**
     * 是否建档立卡贫困村
     */
    @TableField("is_filing_poverty_village")
    private Integer isFilingPovertyVillage;

    /**
     * 年初缺配的基层党组织书记数
     */
    @TableField("early_lack_secretary_num")
    private Integer earlyLackSecretaryNum;

    /**
     * 本年度已选配
     */
    @TableField("current_year_selected")
    private Integer currentYearSelected;

    /**
     * 年初需调整的党组织书记数
     */
    @TableField("early_adjust_secretary_num")
    private Integer earlyAdjustSecretaryNum;

    /**
     * 本年度已调整
     */
    @TableField("current_year_adjusted")
    private Integer currentYearAdjusted;

    /**
     * 培训党组织书记
     */
    @TableField("train_secretary_num")
    private Integer trainSecretaryNum;

    /**
     * 联村的县级领导班子成员（人）
     */
    @TableField("lian_village_leader_num")
    private Integer lianVillageLeaderNum;

    /**
     * 包村的乡镇领导班子成员（人）
     */
    @TableField("bao_village_leader_num")
    private Integer baoVillageLeaderNum;

    /**
     * 选派第一书记（人）
     */
    @TableField("select_secretary_num")
    private Integer selectSecretaryNum;

    /**
     * 结对帮扶的县级及以上机关单位（个）
     */
    @TableField("twinning_unit_num")
    private Integer twinningUnitNum;

    /**
     * 省市两级挂牌督办的村（个）
     */
    @TableField("two_level_listing_village_num")
    private Integer twoLevelListingVillageNum;

    /**
     * 开展专项整治（项）
     */
    @TableField("wage_special_num")
    private Integer wageSpecialNum;

    /**
     * 解决各类问题（个）
     */
    @TableField("solve_problem_num")
    private Integer solveProblemNum;

    /**
     * 查处违纪违法行为（例）
     */
    @TableField("treat_violate_num")
    private Integer treatViolateNum;

    /**
     * 创建时间
     */
    @TableField("create_time")
    private Date createTime;

    /**
     * 更新时间
     */
    @TableField("update_time")
    private Date updateTime;

    /**
     * 删除时间
     */
    @TableField("delete_time")
    private Date deleteTime;

    /**
     * 是否整顿（0否，1是）
     */
    @TableField("has_neaten")
    private String hasNeaten;

    /**
     *
     */
    @TableField("d04_code")
    private String d04Code;

    /**
     * 整顿开始时间
     */
    @TableField("neaten_endtime")
    private Date neatenEndTime;

    /**
     * 整顿
     */
    @TableField("reason")
    private Date reason;

    /**
     * 整顿
     */
    @TableField("township_assessment")
    private Date townshipAssessment;

    /**
     * 整顿
     */
    @TableField("county_level_assessment")
    private Date countyLevelAssessment;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getZbCode() {
        return zbCode;
    }

    public void setZbCode(String zbCode) {
        this.zbCode = zbCode;
    }

    public String getOrgCode() {
        return orgCode;
    }

    public void setOrgCode(String orgCode) {
        this.orgCode = orgCode;
    }

    public String getSlackOrgCode() {
        return slackOrgCode;
    }

    public void setSlackOrgCode(String slackOrgCode) {
        this.slackOrgCode = slackOrgCode;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getD74Code() {
        return d74Code;
    }

    public void setD74Code(String d74Code) {
        this.d74Code = d74Code;
    }

    public String getD74Name() {
        return d74Name;
    }

    public void setD74Name(String d74Name) {
        this.d74Name = d74Name;
    }

    public Date getNeatenTime() {
        return neatenTime;
    }

    public void setNeatenTime(Date neatenTime) {
        this.neatenTime = neatenTime;
    }

    public Integer getIsFilingPovertyVillage() {
        return isFilingPovertyVillage;
    }

    public void setIsFilingPovertyVillage(Integer isFilingPovertyVillage) {
        this.isFilingPovertyVillage = isFilingPovertyVillage;
    }

    public Integer getEarlyLackSecretaryNum() {
        return earlyLackSecretaryNum;
    }

    public void setEarlyLackSecretaryNum(Integer earlyLackSecretaryNum) {
        this.earlyLackSecretaryNum = earlyLackSecretaryNum;
    }

    public Integer getCurrentYearSelected() {
        return currentYearSelected;
    }

    public void setCurrentYearSelected(Integer currentYearSelected) {
        this.currentYearSelected = currentYearSelected;
    }

    public Integer getEarlyAdjustSecretaryNum() {
        return earlyAdjustSecretaryNum;
    }

    public void setEarlyAdjustSecretaryNum(Integer earlyAdjustSecretaryNum) {
        this.earlyAdjustSecretaryNum = earlyAdjustSecretaryNum;
    }

    public Integer getCurrentYearAdjusted() {
        return currentYearAdjusted;
    }

    public void setCurrentYearAdjusted(Integer currentYearAdjusted) {
        this.currentYearAdjusted = currentYearAdjusted;
    }

    public Integer getTrainSecretaryNum() {
        return trainSecretaryNum;
    }

    public void setTrainSecretaryNum(Integer trainSecretaryNum) {
        this.trainSecretaryNum = trainSecretaryNum;
    }

    public Integer getLianVillageLeaderNum() {
        return lianVillageLeaderNum;
    }

    public void setLianVillageLeaderNum(Integer lianVillageLeaderNum) {
        this.lianVillageLeaderNum = lianVillageLeaderNum;
    }

    public Integer getBaoVillageLeaderNum() {
        return baoVillageLeaderNum;
    }

    public void setBaoVillageLeaderNum(Integer baoVillageLeaderNum) {
        this.baoVillageLeaderNum = baoVillageLeaderNum;
    }

    public Integer getSelectSecretaryNum() {
        return selectSecretaryNum;
    }

    public void setSelectSecretaryNum(Integer selectSecretaryNum) {
        this.selectSecretaryNum = selectSecretaryNum;
    }

    public Integer getTwinningUnitNum() {
        return twinningUnitNum;
    }

    public void setTwinningUnitNum(Integer twinningUnitNum) {
        this.twinningUnitNum = twinningUnitNum;
    }

    public Integer getTwoLevelListingVillageNum() {
        return twoLevelListingVillageNum;
    }

    public void setTwoLevelListingVillageNum(Integer twoLevelListingVillageNum) {
        this.twoLevelListingVillageNum = twoLevelListingVillageNum;
    }

    public Integer getWageSpecialNum() {
        return wageSpecialNum;
    }

    public void setWageSpecialNum(Integer wageSpecialNum) {
        this.wageSpecialNum = wageSpecialNum;
    }

    public Integer getSolveProblemNum() {
        return solveProblemNum;
    }

    public void setSolveProblemNum(Integer solveProblemNum) {
        this.solveProblemNum = solveProblemNum;
    }

    public Integer getTreatViolateNum() {
        return treatViolateNum;
    }

    public void setTreatViolateNum(Integer treatViolateNum) {
        this.treatViolateNum = treatViolateNum;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    public Date getDeleteTime() {
        return deleteTime;
    }

    public void setDeleteTime(Date deleteTime) {
        this.deleteTime = deleteTime;
    }

    public String getHasNeaten() {
        return hasNeaten;
    }

    public void setHasNeaten(String hasNeaten) {
        this.hasNeaten = hasNeaten;
    }

    public String getD04Code() {
        return d04Code;
    }

    public void setD04Code(String d04Code) {
        this.d04Code = d04Code;
    }

    public Date getNeatenEndTime() {
        return neatenEndTime;
    }

    public void setNeatenEndTime(Date neatenEndTime) {
        this.neatenEndTime = neatenEndTime;
    }

    public Date getReason() {
        return reason;
    }

    public void setReason(Date reason) {
        this.reason = reason;
    }

    public Date getTownshipAssessment() {
        return townshipAssessment;
    }

    public void setTownshipAssessment(Date townshipAssessment) {
        this.townshipAssessment = townshipAssessment;
    }

    public Date getCountyLevelAssessment() {
        return countyLevelAssessment;
    }

    public void setCountyLevelAssessment(Date countyLevelAssessment) {
        this.countyLevelAssessment = countyLevelAssessment;
    }

    @Override
    protected Serializable pkVal() {
        return this.id;
    }

    @Override
    public String toString() {
        return "OrgSlack{" +
                "id=" + id +
                ", code=" + code +
                ", zbCode=" + zbCode +
                ", orgCode=" + orgCode +
                ", d74Code=" + d74Code +
                ", d74Name=" + d74Name +
                ", neatenTime=" + neatenTime +
                ", isFilingPovertyVillage=" + isFilingPovertyVillage +
                ", earlyLackSecretaryNum=" + earlyLackSecretaryNum +
                ", currentYearSelected=" + currentYearSelected +
                ", earlyAdjustSecretaryNum=" + earlyAdjustSecretaryNum +
                ", currentYearAdjusted=" + currentYearAdjusted +
                ", trainSecretaryNum=" + trainSecretaryNum +
                ", lianVillageLeaderNum=" + lianVillageLeaderNum +
                ", baoVillageLeaderNum=" + baoVillageLeaderNum +
                ", selectSecretaryNum=" + selectSecretaryNum +
                ", twinningUnitNum=" + twinningUnitNum +
                ", twoLevelListingVillageNum=" + twoLevelListingVillageNum +
                ", wageSpecialNum=" + wageSpecialNum +
                ", solveProblemNum=" + solveProblemNum +
                ", treatViolateNum=" + treatViolateNum +
                ", createTime=" + createTime +
                ", updateTime=" + updateTime +
                ", deleteTime=" + deleteTime +
                "}";
    }
}
