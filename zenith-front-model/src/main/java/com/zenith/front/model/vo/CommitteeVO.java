package com.zenith.front.model.vo;

import com.zenith.front.common.encrypt.annotation.EncryptEnabled;
import com.zenith.front.common.encrypt.annotation.EncryptField;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

import java.util.Date;

/**
 * <AUTHOR>
 * @Date 2021/7/14 16:48
 * @Version 1.0
 */
@Data
@ToString
@NoArgsConstructor
@AllArgsConstructor
@EncryptEnabled
public class CommitteeVO {
    @EncryptField(order = 1)
    private String name;
    @EncryptField(order = 2)
    private String d022_name;
    private Date start_date;
    private Date end_date;
    private String code;
}
