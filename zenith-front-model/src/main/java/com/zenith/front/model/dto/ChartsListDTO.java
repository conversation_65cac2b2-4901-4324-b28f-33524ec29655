package com.zenith.front.model.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @Description: 概况统计图表请求模型
 * @date 2019/4/28 10:40
 */
@Data
@ToString
@NoArgsConstructor
@AllArgsConstructor

public class ChartsListDTO {

    /**
     * 组织概况
     */
    private List<DisplayDTO> orgOverview;

    /**
     * 单位概况
     */
    private List<DisplayDTO> unitOverview;

    /**
     * 党员概况
     */
    private List<DisplayDTO> memOverview;

    /**
     * 发展党员概况
     */
    private List<DisplayDTO> developOverview;

    /**
     * 流动党员概况
     */
    private List<DisplayDTO> flowOverview;

    /**
     * 关系转接概况
     */
    private List<DisplayDTO> transferOverview;

    /**
     * 党代表概况
     */
    private List<DisplayDTO> representativeOverview;

    /**
     * 党代表概况
     */
    private List<DisplayDTO> partyOverview;


}
