package com.zenith.front.model.dto;

import com.zenith.front.model.bean.UnitSecondary;
import lombok.ToString;

import java.util.Date;

@ToString
public class UnitSecondaryDTO {

    /**
     * 唯一标识符code
     */
    private String code;

    /**
     * 唯一自增长id
     */
    private Long id;

    /**
     * es唯一主键
     */
    private String esId;

    /**
     * 院系名称
     */
    private String facultyName;

    /**
     * 党组类别代码
     */
    private String d110Code;


    /**
     * 党组类别名称
     */
    private String d110Name;


    /**
     * 关联单位_代码
     */
    private String unitCode;


    /**
     * 关联组织名称
     */
    private String orgCode;

    /**
     * 关联组织代码
     */
    private String orgName;

    /**
     * 备注
     */
    private String remark;

    /**
     * 建立时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 删除时间
     */
    private Date deleteTime;

    /**
     * 时间戳
     */
    private Date timestamp;

    /**
     * 最近更更新账号
     */
    private String updateAccount;

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getEsId() {
        return esId;
    }

    public void setEsId(String esId) {
        this.esId = esId;
    }

    public String getFacultyName() {
        return facultyName;
    }

    public void setFacultyName(String facultyName) {
        this.facultyName = facultyName;
    }

    public String getD110Code() {
        return d110Code;
    }

    public void setD110Code(String d110Code) {
        this.d110Code = d110Code;
    }

    public String getD110Name() {
        return d110Name;
    }

    public void setD110Name(String d110Name) {
        this.d110Name = d110Name;
    }

    public String getUnitCode() {
        return unitCode;
    }

    public void setUnitCode(String unitCode) {
        this.unitCode = unitCode;
    }

    public String getOrgCode() {
        return orgCode;
    }

    public void setOrgCode(String orgCode) {
        this.orgCode = orgCode;
    }

    public String getOrgName() {
        return orgName;
    }

    public void setOrgName(String orgName) {
        this.orgName = orgName;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    public Date getDeleteTime() {
        return deleteTime;
    }

    public void setDeleteTime(Date deleteTime) {
        this.deleteTime = deleteTime;
    }

    public Date getTimestamp() {
        return timestamp;
    }

    public void setTimestamp(Date timestamp) {
        this.timestamp = timestamp;
    }

    public String getUpdateAccount() {
        return updateAccount;
    }

    public void setUpdateAccount(String updateAccount) {
        this.updateAccount = updateAccount;
    }

    public UnitSecondary toModel() {
        UnitSecondary model = new UnitSecondary();
        model.setCode(this.code);
        model.setId(this.id);
        model.setEsId(this.esId);
        model.setFacultyName(this.facultyName);
        model.setD110Code(this.d110Code);
        model.setD110Name(this.d110Name);
        model.setUnitCode(this.unitCode);
        model.setOrgCode(this.orgCode);
        model.setOrgName(this.orgName);
        model.setRemark(this.remark);
        model.setCreateTime(this.createTime);
        model.setUpdateTime(this.updateTime);
        model.setDeleteTime(this.deleteTime);
        model.setTimestamp(this.timestamp);
        model.setUpdateAccount(this.updateAccount);
        return model;
    }
}