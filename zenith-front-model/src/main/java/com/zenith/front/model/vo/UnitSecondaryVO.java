package com.zenith.front.model.vo;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

import java.util.Date;

/**
 * <AUTHOR>
 * @version 1.0
 * @Description: 党组
 * @date 2021-10-05
 */
@Data
@ToString
@NoArgsConstructor
@AllArgsConstructor
public class UnitSecondaryVO {
    /**
     * 唯一标识符code
     */
    private String code;

    /**
     * 唯一自增长id
     */
    private Long id;

    /**
     * es唯一主键
     */
    private String esId;

    /**
     * 院系名称
     */
    private String facultyName;

    /**
     * 党组类别代码
     */
    private String d110Code;


    /**
     * 党组类别名称
     */
    private String d110Name;


    /**
     * 关联单位_代码
     */
    private String unitCode;


    /**
     * 关联组织名称
     */
    private String orgCode;

    /**
     * 关联组织代码
     */
    private String orgName;

    /**
     * 备注
     */
    private String remark;

    /**
     * 建立时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 删除时间
     */
    private Date deleteTime;

    /**
     * 时间戳
     */
    private Date timestamp;

    /**
     * 最近更更新账号
     */
    private String updateAccount;
}
