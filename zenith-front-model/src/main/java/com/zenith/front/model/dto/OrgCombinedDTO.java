package com.zenith.front.model.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

import java.util.List;

/**
 * <AUTHOR>
 */
@Data
@ToString
@NoArgsConstructor
@AllArgsConstructor
public class OrgCombinedDTO {

    /**
     * 需要合并的党支部或联合党支部
     */
    private List<String> codes;

    /**
     * 党员需要合并到的党组织
     */
    private String code;
}
