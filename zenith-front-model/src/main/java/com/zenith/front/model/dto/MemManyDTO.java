package com.zenith.front.model.dto;

import com.zenith.front.model.bean.MemMany;
import com.zenith.front.model.validate.group.Common5Group;
import com.zenith.front.model.validate.group.Common6Group;
import lombok.ToString;

import javax.validation.constraints.NotBlank;

@ToString
public class MemManyDTO {

    private Long id;
    @NotBlank(groups = Common6Group.class, message = "code 不能为空")
    private String code;
    @NotBlank(groups = Common5Group.class, message = "memCode 不能为空")
    private String memCode;
    private String memName;
    @NotBlank(groups = Common5Group.class, message = "idcard 不能为空")
    private String idcard;
    /**
     * 党员民族code
     */
    @NotBlank(groups = Common5Group.class, message = "d06Code 不能为空")
    private String d06Code;
    /**
     * 党员民族code
     */
    @NotBlank(groups = Common5Group.class, message = "d06Name 不能为空")
    private String d06Name;
    /**
     * 性别
     */
    @NotBlank(groups = Common5Group.class, message = "sexCode 不能为空")
    private String sexCode;
    @NotBlank(groups = Common5Group.class, message = "sexName 不能为空")
    private String sexName;
    /**
     * 人员类型
     */
    @NotBlank(groups = Common5Group.class, message = "d08Code 不能为空")
    private String d08Code;
    @NotBlank(groups = Common5Group.class, message = "d08Name 不能为空")
    private String d08Name;
    @NotBlank(groups = Common5Group.class, message = "d48Name 不能为空")
    private String d48Name;
    /**
     * 党员自身所在组织code
     */
    @NotBlank(groups = Common5Group.class, message = "memCurrOrgCode 不能为空")
    private String memCurrOrgCode;
    /**
     * 党员自身所在组织名称
     */
    private String memCurrOrgName;
    /**
     * 党员自身所在组织层级码
     */
    @NotBlank(groups = Common5Group.class, message = "memCurrManyOrgCode 不能为空")
    private String memCurrManyOrgCode;
    /**
     * 党员进入的组织code
     */
    @NotBlank(groups = Common5Group.class, message = "joinOrgCode 不能为空")
    private String joinOrgCode;
    /**
     * 党员进入的组织名称
     */
    @NotBlank(groups = Common5Group.class, message = "joinOrgName 不能为空")
    private String joinOrgName;
    /**
     * 党员进入的组织层级码
     */
    @NotBlank(groups = Common5Group.class, message = "joinManyOrgCode 不能为空")
    private String joinManyOrgCode;
    /**
     * 进入管理类型code
     */
    @NotBlank(groups = Common5Group.class, message = "joinOrgTypeCode 不能为空")
    private String joinOrgTypeCode;
    /**
     * 进入管理类型名称
     */
    @NotBlank(groups = Common5Group.class, message = "joinOrgTypeName 不能为空")
    private String joinOrgTypeName;
    /**
     * 党员进入的组织日期
     */
    private java.util.Date joinOrgDate;
    private java.util.Date createTime;
    private java.util.Date updateTime;
    private java.util.Date timestamp;
    private java.util.Date deleteTime;
    private String zbCode;
    /**
     * 籍贯
     */
    @NotBlank(groups = Common5Group.class, message = "d48Code 不能为空")
    private String d48Code;
    private Integer isHistory;
    private String updateAccount;

    public MemManyDTO setId(Long id) {
        this.id = id;
        return this;
    }

    public Long getId() {
        return this.id;
    }

    public MemManyDTO setCode(String code) {
        this.code = code;
        return this;
    }

    public String getCode() {
        return this.code;
    }

    public MemManyDTO setMemCode(String memCode) {
        this.memCode = memCode;
        return this;
    }

    public String getMemCode() {
        return this.memCode;
    }

    public MemManyDTO setMemName(String memName) {
        this.memName = memName;
        return this;
    }

    public String getMemName() {
        return this.memName;
    }

    public MemManyDTO setIdcard(String idcard) {
        this.idcard = idcard;
        return this;
    }

    public String getIdcard() {
        return this.idcard;
    }

    public MemManyDTO setD06Code(String d06Code) {
        this.d06Code = d06Code;
        return this;
    }

    public String getD06Code() {
        return this.d06Code;
    }

    public MemManyDTO setD06Name(String d06Name) {
        this.d06Name = d06Name;
        return this;
    }

    public String getD06Name() {
        return this.d06Name;
    }

    public MemManyDTO setSexCode(String sexCode) {
        this.sexCode = sexCode;
        return this;
    }

    public String getSexCode() {
        return this.sexCode;
    }

    public MemManyDTO setSexName(String sexName) {
        this.sexName = sexName;
        return this;
    }

    public String getSexName() {
        return this.sexName;
    }

    public MemManyDTO setD08Code(String d08Code) {
        this.d08Code = d08Code;
        return this;
    }

    public String getD08Code() {
        return this.d08Code;
    }

    public MemManyDTO setD08Name(String d08Name) {
        this.d08Name = d08Name;
        return this;
    }

    public String getD08Name() {
        return this.d08Name;
    }

    public MemManyDTO setD48Name(String d48Name) {
        this.d48Name = d48Name;
        return this;
    }

    public String getD48Name() {
        return this.d48Name;
    }

    public MemManyDTO setMemCurrOrgCode(String memCurrOrgCode) {
        this.memCurrOrgCode = memCurrOrgCode;
        return this;
    }

    public String getMemCurrOrgCode() {
        return this.memCurrOrgCode;
    }

    public MemManyDTO setMemCurrOrgName(String memCurrOrgName) {
        this.memCurrOrgName = memCurrOrgName;
        return this;
    }

    public String getMemCurrOrgName() {
        return this.memCurrOrgName;
    }

    public MemManyDTO setMemCurrManyOrgCode(String memCurrManyOrgCode) {
        this.memCurrManyOrgCode = memCurrManyOrgCode;
        return this;
    }

    public String getMemCurrManyOrgCode() {
        return this.memCurrManyOrgCode;
    }

    public MemManyDTO setJoinOrgCode(String joinOrgCode) {
        this.joinOrgCode = joinOrgCode;
        return this;
    }

    public String getJoinOrgCode() {
        return this.joinOrgCode;
    }

    public MemManyDTO setJoinOrgName(String joinOrgName) {
        this.joinOrgName = joinOrgName;
        return this;
    }

    public String getJoinOrgName() {
        return this.joinOrgName;
    }

    public MemManyDTO setJoinManyOrgCode(String joinManyOrgCode) {
        this.joinManyOrgCode = joinManyOrgCode;
        return this;
    }

    public String getJoinManyOrgCode() {
        return this.joinManyOrgCode;
    }

    public MemManyDTO setJoinOrgTypeCode(String joinOrgTypeCode) {
        this.joinOrgTypeCode = joinOrgTypeCode;
        return this;
    }

    public String getJoinOrgTypeCode() {
        return this.joinOrgTypeCode;
    }

    public MemManyDTO setJoinOrgTypeName(String joinOrgTypeName) {
        this.joinOrgTypeName = joinOrgTypeName;
        return this;
    }

    public String getJoinOrgTypeName() {
        return this.joinOrgTypeName;
    }

    public MemManyDTO setJoinOrgDate(java.util.Date joinOrgDate) {
        this.joinOrgDate = joinOrgDate;
        return this;
    }

    public java.util.Date getJoinOrgDate() {
        return this.joinOrgDate;
    }

    public MemManyDTO setCreateTime(java.util.Date createTime) {
        this.createTime = createTime;
        return this;
    }

    public java.util.Date getCreateTime() {
        return this.createTime;
    }

    public MemManyDTO setUpdateTime(java.util.Date updateTime) {
        this.updateTime = updateTime;
        return this;
    }

    public java.util.Date getUpdateTime() {
        return this.updateTime;
    }

    public MemManyDTO setTimestamp(java.util.Date timestamp) {
        this.timestamp = timestamp;
        return this;
    }

    public java.util.Date getTimestamp() {
        return this.timestamp;
    }

    public MemManyDTO setDeleteTime(java.util.Date deleteTime) {
        this.deleteTime = deleteTime;
        return this;
    }

    public java.util.Date getDeleteTime() {
        return this.deleteTime;
    }

    public MemManyDTO setZbCode(String zbCode) {
        this.zbCode = zbCode;
        return this;
    }

    public String getZbCode() {
        return this.zbCode;
    }

    public MemManyDTO setD48Code(String d48Code) {
        this.d48Code = d48Code;
        return this;
    }

    public String getD48Code() {
        return this.d48Code;
    }

    public MemManyDTO setIsHistory(Integer isHistory) {
        this.isHistory = isHistory;
        return this;
    }

    public Integer getIsHistory() {
        return this.isHistory;
    }

    public MemManyDTO setUpdateAccount(String updateAccount) {
        this.updateAccount = updateAccount;
        return this;
    }

    public String getUpdateAccount() {
        return this.updateAccount;
    }

    public MemMany toModel() {
        MemMany model = new MemMany();
        model.setId(this.id);
        model.setCode(this.code);
        model.setMemCode(this.memCode);
        model.setMemName(this.memName);
        model.setIdcard(this.idcard);
        model.setD06Code(this.d06Code);
        model.setD06Name(this.d06Name);
        model.setSexCode(this.sexCode);
        model.setSexName(this.sexName);
        model.setD08Code(this.d08Code);
        model.setD08Name(this.d08Name);
        model.setD48Name(this.d48Name);
        model.setMemCurrOrgCode(this.memCurrOrgCode);
        model.setMemCurrOrgName(this.memCurrOrgName);
        model.setMemCurrManyOrgCode(this.memCurrManyOrgCode);
        model.setJoinOrgCode(this.joinOrgCode);
        model.setJoinOrgName(this.joinOrgName);
        model.setJoinManyOrgCode(this.joinManyOrgCode);
        model.setJoinOrgTypeCode(this.joinOrgTypeCode);
        model.setJoinOrgTypeName(this.joinOrgTypeName);
        model.setJoinOrgDate(this.joinOrgDate);
        model.setCreateTime(this.createTime);
        model.setUpdateTime(this.updateTime);
        model.setTimestamp(this.timestamp);
        model.setDeleteTime(this.deleteTime);
        model.setZbCode(this.zbCode);
        model.setD48Code(this.d48Code);
        model.setIsHistory(this.isHistory);
        model.setUpdateAccount(this.updateAccount);
        return model;
    }
}