package com.zenith.front.model.dto;

import com.zenith.front.model.bean.TaskActivity;
import lombok.*;

@ToString
public class TaskActivityDTO{

   	/**
   	 * 自增长id
   	 */
    private Integer id;
   	/**
   	 * 唯一标识符code
   	 */
    private String code;
   	/**
   	 * 任务code
   	 */
    private String taskCode;
   	/**
   	 * 活动code
   	 */
    private String activityCode;
   	/**
   	 * 完成详情code
   	 */
    private String taskObjectCode;
   	/**
   	 * 创建时间
   	 */
    private java.util.Date createTime;
   	/**
   	 * 更新时间
   	 */
    private java.util.Date updateTime;
   	/**
   	 * 删除时间
   	 */
    private java.util.Date deleteTime;

    public TaskActivityDTO setId(Integer id){
        this.id = id;
        return this;
    }
    public Integer getId() {
    	return this.id;
    }
    public TaskActivityDTO setCode(String code){
        this.code = code;
        return this;
    }
    public String getCode() {
    	return this.code;
    }
    public TaskActivityDTO setTaskCode(String taskCode){
        this.taskCode = taskCode;
        return this;
    }
    public String getTaskCode() {
    	return this.taskCode;
    }
    public TaskActivityDTO setActivityCode(String activityCode){
        this.activityCode = activityCode;
        return this;
    }
    public String getActivityCode() {
    	return this.activityCode;
    }
    public TaskActivityDTO setTaskObjectCode(String taskObjectCode){
        this.taskObjectCode = taskObjectCode;
        return this;
    }
    public String getTaskObjectCode() {
    	return this.taskObjectCode;
    }
    public TaskActivityDTO setCreateTime(java.util.Date createTime){
        this.createTime = createTime;
        return this;
    }
    public java.util.Date getCreateTime() {
    	return this.createTime;
    }
    public TaskActivityDTO setUpdateTime(java.util.Date updateTime){
        this.updateTime = updateTime;
        return this;
    }
    public java.util.Date getUpdateTime() {
    	return this.updateTime;
    }
    public TaskActivityDTO setDeleteTime(java.util.Date deleteTime){
        this.deleteTime = deleteTime;
        return this;
    }
    public java.util.Date getDeleteTime() {
    	return this.deleteTime;
    }


    public TaskActivity toModel(){
        TaskActivity model = new TaskActivity();
        model.setId(this.id);
        model.setCode(this.code);
        model.setTaskCode(this.taskCode);
        model.setActivityCode(this.activityCode);
        model.setTaskObjectCode(this.taskObjectCode);
        model.setCreateTime(this.createTime);
        model.setUpdateTime(this.updateTime);
        model.setDeleteTime(this.deleteTime);
        return model;
    }
}