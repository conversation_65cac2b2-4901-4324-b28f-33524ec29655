package com.zenith.front.model.vo;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2019/6/242:20 PM
 */
public class DemocraticAnaVO {
    /**
     * 组织id
     * */
    private String orgId;
    /***
     * 组织code
     * */
    private String orgCode;
    /**
     * 组织名称
     * */
    private String orgName;
    /**
     * 组织类型
     * **/
    private String type;

    /**
     * 优秀总人数
     * */
    private Integer yxTotal = 0;
    /***
     * 合格总人数
     * */
    private Integer hgTotal = 0;
    /**
     * 不合格总人数
     * **/
    private Integer bhgTotal = 0;
    /***
     * 已评议总数
     * */
    private Integer ypyTotal = 0;
    /**
     * 总人数
     * **/
    private Integer total = 0;
    /**
     * 直属下级
     * */
    private List<DemocraticAnaVO> subOrgArr;


    public Integer getYxTotal() {
        return yxTotal;
    }

    public DemocraticAnaVO setYxTotal(Integer yxTotal) {
        this.yxTotal = yxTotal;
        return this;
    }

    public Integer getHgTotal() {
        return hgTotal;
    }

    public DemocraticAnaVO setHgTotal(Integer hgTotal) {
        this.hgTotal = hgTotal;
        return this;
    }

    public Integer getBhgTotal() {
        return bhgTotal;
    }

    public DemocraticAnaVO setBhgTotal(Integer bhgTotal) {
        this.bhgTotal = bhgTotal;
        return this;
    }

    public Integer getTotal() {
        return total;
    }

    public DemocraticAnaVO setTotal(Integer total) {
        this.total = total;
        return this;
    }

    public String getOrgId() {
        return orgId;
    }

    public DemocraticAnaVO setOrgId(String orgId) {
        this.orgId = orgId;
        return this;
    }

    public String getOrgCode() {
        return orgCode;
    }

    public DemocraticAnaVO setOrgCode(String orgCode) {
        this.orgCode = orgCode;
        return this;
    }

    public String getOrgName() {
        return orgName;
    }

    public DemocraticAnaVO setOrgName(String orgName) {
        this.orgName = orgName;
        return this;
    }

    public String getType() {
        return type;
    }

    public DemocraticAnaVO setType(String type) {
        this.type = type;
        return this;
    }

    public Integer getYpyTotal() {
        return ypyTotal;
    }

    public DemocraticAnaVO setYpyTotal(Integer ypyTotal) {
        this.ypyTotal = ypyTotal;
        return this;
    }

    public List<DemocraticAnaVO> getSubOrgArr() {
        return subOrgArr;
    }

    public DemocraticAnaVO setSubOrgArr(List<DemocraticAnaVO> subOrgArr) {
        this.subOrgArr = subOrgArr;
        return this;
    }

    public void incYxTotal(Integer total){
        this.yxTotal = this.yxTotal + total;
    }

    public void incHgTotal(Integer total){
        this.hgTotal = this.hgTotal + total;
    }

    public void incBhgTotal(Integer total){
        this.bhgTotal = this.bhgTotal + total;
    }

    public void incYpyTotal(Integer total){
        this.ypyTotal = this.ypyTotal + total;
    }

    public void incTotal(Integer total){
        this.total = this.total + total;
    }

    public void addSubOrg(DemocraticAnaVO sub){
        if(subOrgArr == null){
            subOrgArr = new ArrayList<>();
        }
        subOrgArr.add(sub);
    }
}
