package com.zenith.front.model.bean;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;

import java.util.Date;


/**
 * <AUTHOR>
 * @date 2021/8/27
 */
@TableName("ccp_org_slack_rectification")
public class OrgSlackRectification extends Model<OrgSlackRectification> {

    private static final long serialVersionUID = 1L;

    /**
     * 自增长主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 唯一标识符code
     */
    @TableField("code")
    private String code;

    /**
     * 组织唯一标识符，中组部code
     */
    @TableField("zb_code")
    private String zbCode;

    /**
     * 组织code
     */
    @TableField("org_code")
    private String orgCode;
    /**
     * 组织层级码
     */
    @TableField("slack_org_code")
    private String slackOrgCode;
    /**
     * 涣散组织记录id
     */
    @TableField("slack_code")
    private String slackCode;
    /**
     * 整顿形式
     */
    @TableField("d90_code")
    private String d90Code;

    /**
     * 整顿形式名称
     */
    @TableField("d90_name")
    private String d90Name;

    /**
     * 形式详情
     */
    @TableField("d91_code")
    private String d91Code;

    public String getD90Code() {
        return d90Code;
    }

    public void setD90Code(String d90Code) {
        this.d90Code = d90Code;
    }

    public String getD90Name() {
        return d90Name;
    }

    public void setD90Name(String d90Name) {
        this.d90Name = d90Name;
    }

    public String getD91Code() {
        return d91Code;
    }

    public void setD91Code(String d91Code) {
        this.d91Code = d91Code;
    }

    public String getD91Name() {
        return d91Name;
    }

    public void setD91Name(String d91Name) {
        this.d91Name = d91Name;
    }

    /**

     * 形式详情名称
     */
    @TableField("d91_name")
    private String d91Name;

    /**
     * 本年度已选配
     */
    @TableField("current_year_selected")
    private Integer currentYearSelected;


    /**
     * 本年度已调整
     */
    @TableField("current_year_adjusted")
    private Integer currentYearAdjusted;

    /**
     *
     * 创建时间
     * @return
     */
    @TableField("create_time")
    private Date createTime;

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public static long getSerialVersionUID() {
        return serialVersionUID;
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getZbCode() {
        return zbCode;
    }

    public void setZbCode(String zbCode) {
        this.zbCode = zbCode;
    }

    public String getOrgCode() {
        return orgCode;
    }

    public void setOrgCode(String orgCode) {
        this.orgCode = orgCode;
    }

    public String getSlackOrgCode() {
        return slackOrgCode;
    }

    public void setSlackOrgCode(String slackOrgCode) {
        this.slackOrgCode = slackOrgCode;
    }

    public String getSlackCode() {
        return slackCode;
    }

    public void setSlackCode(String slackCode) {
        this.slackCode = slackCode;
    }

    public Integer getCurrentYearSelected() {
        return currentYearSelected;
    }

    public void setCurrentYearSelected(Integer currentYearSelected) {
        this.currentYearSelected = currentYearSelected;
    }

    public Integer getCurrentYearAdjusted() {
        return currentYearAdjusted;
    }

    public void setCurrentYearAdjusted(Integer currentYearAdjusted) {
        this.currentYearAdjusted = currentYearAdjusted;
    }
}
