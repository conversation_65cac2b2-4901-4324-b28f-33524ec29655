package com.zenith.front.model.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

import javax.validation.constraints.NotBlank;
import java.util.Date;

/**
 * <AUTHOR>
 */
@Data
@ToString
@NoArgsConstructor
@AllArgsConstructor
public class OrgDissolveDTO {

    /**
     * 撤销的党组织
     */
    @NotBlank
    private String code;

    /**
     * 撤销日期
     */
    private Date date;

    /**
     * 撤销文号
     */
    private String documentNum;

    /**
     * 撤销原因
     */
    private String reason;

    /**
     * 党组织类型
     */
    private String d01Code;

}
