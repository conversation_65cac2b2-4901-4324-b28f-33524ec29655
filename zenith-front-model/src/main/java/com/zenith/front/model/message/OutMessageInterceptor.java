//package com.zenith.front.message;
//
//import org.springframework.core.MethodParameter;
//import org.springframework.http.converter.HttpMessageConverter;
//import org.springframework.http.converter.HttpMessageNotWritableException;
//import org.springframework.stereotype.Component;
//import org.springframework.web.HttpMediaTypeNotAcceptableException;
//import org.springframework.web.context.request.NativeWebRequest;
//import org.springframework.web.method.support.ModelAndViewContainer;
//import org.springframework.web.servlet.mvc.method.annotation.RequestResponseBodyMethodProcessor;
//
//import java.io.IOException;
//import java.util.List;
//
///***
// * 如果action 返回值为outMessage 则render 为json
// * <AUTHOR>
// * */
//@Component
//public class OutMessageInterceptor extends RequestResponseBodyMethodProcessor {
//
//    public OutMessageInterceptor(List<HttpMessageConverter<?>> converters) {
//        super(converters);
//    }
//
//    @Override
//    public void handleReturnValue(Object returnValue, MethodParameter returnType, ModelAndViewContainer mavContainer, NativeWebRequest webRequest) throws HttpMessageNotWritableException {
//        if (returnValue instanceof OutMessage) {
//            try {
//                super.handleReturnValue(returnValue, returnType, mavContainer, webRequest);
//            } catch (IOException | HttpMediaTypeNotAcceptableException | HttpMessageNotWritableException e) {
//                e.printStackTrace();
//            }
//        }
//    }
//}
