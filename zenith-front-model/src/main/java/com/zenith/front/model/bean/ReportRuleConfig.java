package com.zenith.front.model.bean;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableField;
import java.io.Serializable;

/**
 * <p>
 * excel 系统配置表
 * </p>
 *
 * <AUTHOR>
 * @since 2021-07-11
 */
@TableName("report_rule_config")
public class ReportRuleConfig extends Model<ReportRuleConfig> {

    private static final long serialVersionUID=1L;

    /**
     * 自增长 主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 年份
     */
    @TableField("year")
    private Integer year;

    /**
     * 字典code
     */
    @TableField("report_code")
    private String reportCode;

    /**
     * 名称
     */
    @TableField("name")
    private String name;

    /**
     * 顶级节点默认值为 -1
     */
    @TableField("parent_key")
    private String parentKey;

    /**
     * type 1:公务员 2:班子 3:是事业单位
     */
    @TableField("type")
    private String type;

    /**
     * excel配置
     */
    @TableField("json_config")
    private String jsonConfig;

    /**
     * 原来的数据
     */
    @TableField("real_data")
    private String realData;

    /**
     * 补充资料配置
     */
    @TableField("replenish_json_config")
    private String replenishJsonConfig;


    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public Integer getYear() {
        return year;
    }

    public void setYear(Integer year) {
        this.year = year;
    }

    public String getReportCode() {
        return reportCode;
    }

    public void setReportCode(String reportCode) {
        this.reportCode = reportCode;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getParentKey() {
        return parentKey;
    }

    public void setParentKey(String parentKey) {
        this.parentKey = parentKey;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public String getJsonConfig() {
        return jsonConfig;
    }

    public void setJsonConfig(String jsonConfig) {
        this.jsonConfig = jsonConfig;
    }

    public String getRealData() {
        return realData;
    }

    public void setRealData(String realData) {
        this.realData = realData;
    }

    public String getReplenishJsonConfig() {
        return replenishJsonConfig;
    }

    public void setReplenishJsonConfig(String replenishJsonConfig) {
        this.replenishJsonConfig = replenishJsonConfig;
    }

    @Override
    public Serializable pkVal() {
        return this.id;
    }

    @Override
    public String toString() {
        return "ReportRuleConfig{" +
                "id=" + id +
                ", year=" + year +
                ", reportCode='" + reportCode + '\'' +
                ", name='" + name + '\'' +
                ", parentKey='" + parentKey + '\'' +
                ", type='" + type + '\'' +
                ", jsonConfig='" + jsonConfig + '\'' +
                ", realData='" + realData + '\'' +
                ", replenishJsonConfig='" + replenishJsonConfig + '\'' +
                '}';
    }
}
