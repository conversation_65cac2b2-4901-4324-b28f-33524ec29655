package com.zenith.front.model.vo;

import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2021/11/16 19:37
 * @Version 1.0
 */
@Data
public class OrgIndustryVO {

    /**
     * 唯一标识符code
     */
    private String code;

    /**
     * 党组织唯一组织code
     */
    private String orgCode;

    /**
     * 党组织组织层级码
     */
    private String industryOrgCode;

    /**
     * 组织名称
     */
    private String industryOrgName;

    /**
     * 组织类别
     */
    private String industryOrgType;

    /**
     * 行业分类
     */
    private String industryClassification;

    /**
     * 行业分类名称
     */
    private String industryClassificationName;

    /**
     * 所属层级
     */
    private String subordinateLevel;

    /**
     * 隶属关系
     */
    private String membershipFunction;

    /**
     * 隶属关系名称
     */
    private String membershipFunctionName;

    /**
     * 书记是否由行业主管部门党员负责同志担任
     */
    private Integer hasSecretaryIndustry;

    /**
     * 专职工作人员数
     */
    private Integer workerNumber;

    /**
     * 是否有所属党组织
     */
    private Integer hasPartyOrganizations;

    /**
     * 关联组织（多选）
     */
    private List<AssociatedOrganizationVO> associatedOrganization;
}
