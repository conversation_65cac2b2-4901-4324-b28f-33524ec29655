package com.zenith.front.model.bean;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import java.util.Date;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableField;
import java.io.Serializable;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2021-07-11
 */
@TableName("ccp_trend_check")
public class TrendCheck extends Model<TrendCheck> {

    private static final long serialVersionUID=1L;

    /**
     *   自增长ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 唯一主键
     */
    @TableField("code")
    private String code;

    /**
     * 动态ID
     */
    @TableField("tered_code")
    private String teredCode;

    /**
     * 审核组织code
     */
    @TableField("check_org_code")
    private String checkOrgCode;

    /**
     * 审核组织层级码
     */
    @TableField("check_org_org_code")
    private String checkOrgOrgCode;

    /**
     * 审核组织层级码所在集合
     */
    @TableField("check_org_set")
    private String checkOrgSet;

    /**
     * 发起审核组织code
     */
    @TableField("sponsor_org_code")
    private String sponsorOrgCode;

    /**
     * 审核组织层级码
     */
    @TableField("sponsor_org_org_code")
    private String sponsorOrgOrgCode;

    /**
     * 发起所在组织所在集合
     */
    @TableField("sponsor_org_set")
    private String sponsorOrgSet;

    /**
     * 审核状态（0=待审核，1=审核通过，2=审核未通过）
     */
    @TableField("status")
    private Integer status;

    /**
     * 未通过理由
     */
    @TableField("check_remark")
    private String checkRemark;

    /**
     * 创建时间
     */
    @TableField("creat_time")
    private Date creatTime;

    /**
     * 更新时间
     */
    @TableField("update_time")
    private Date updateTime;

    /**
     * 删除时间
     */
    @TableField("delete_time")
    private Date deleteTime;


    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getTeredCode() {
        return teredCode;
    }

    public void setTeredCode(String teredCode) {
        this.teredCode = teredCode;
    }

    public String getCheckOrgCode() {
        return checkOrgCode;
    }

    public void setCheckOrgCode(String checkOrgCode) {
        this.checkOrgCode = checkOrgCode;
    }

    public String getCheckOrgOrgCode() {
        return checkOrgOrgCode;
    }

    public void setCheckOrgOrgCode(String checkOrgOrgCode) {
        this.checkOrgOrgCode = checkOrgOrgCode;
    }

    public String getCheckOrgSet() {
        return checkOrgSet;
    }

    public void setCheckOrgSet(String checkOrgSet) {
        this.checkOrgSet = checkOrgSet;
    }

    public String getSponsorOrgCode() {
        return sponsorOrgCode;
    }

    public void setSponsorOrgCode(String sponsorOrgCode) {
        this.sponsorOrgCode = sponsorOrgCode;
    }

    public String getSponsorOrgOrgCode() {
        return sponsorOrgOrgCode;
    }

    public void setSponsorOrgOrgCode(String sponsorOrgOrgCode) {
        this.sponsorOrgOrgCode = sponsorOrgOrgCode;
    }

    public String getSponsorOrgSet() {
        return sponsorOrgSet;
    }

    public void setSponsorOrgSet(String sponsorOrgSet) {
        this.sponsorOrgSet = sponsorOrgSet;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public String getCheckRemark() {
        return checkRemark;
    }

    public void setCheckRemark(String checkRemark) {
        this.checkRemark = checkRemark;
    }

    public Date getCreatTime() {
        return creatTime;
    }

    public void setCreatTime(Date creatTime) {
        this.creatTime = creatTime;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    public Date getDeleteTime() {
        return deleteTime;
    }

    public void setDeleteTime(Date deleteTime) {
        this.deleteTime = deleteTime;
    }

    @Override
    public Serializable pkVal() {
        return this.id;
    }

    @Override
    public String toString() {
        return "TrendCheck{" +
        "id=" + id +
        ", code=" + code +
        ", teredCode=" + teredCode +
        ", checkOrgCode=" + checkOrgCode +
        ", checkOrgOrgCode=" + checkOrgOrgCode +
        ", checkOrgSet=" + checkOrgSet +
        ", sponsorOrgCode=" + sponsorOrgCode +
        ", sponsorOrgOrgCode=" + sponsorOrgOrgCode +
        ", sponsorOrgSet=" + sponsorOrgSet +
        ", status=" + status +
        ", checkRemark=" + checkRemark +
        ", creatTime=" + creatTime +
        ", updateTime=" + updateTime +
        ", deleteTime=" + deleteTime +
        "}";
    }
}
