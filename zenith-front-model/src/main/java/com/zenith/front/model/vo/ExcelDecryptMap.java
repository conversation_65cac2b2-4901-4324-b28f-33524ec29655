package com.zenith.front.model.vo;

import com.zenith.front.common.encrypt.annotation.EncryptEnabled;
import com.zenith.front.common.encrypt.annotation.EncryptField;
import lombok.Getter;
import lombok.Setter;

import java.util.LinkedHashMap;

/**
 * 兼容返回类型为map时数据解密
 *
 * <AUTHOR>
 * @version V1.0
 * @date 2022/10/21 16:10
 */
@EncryptEnabled
@Setter
@Getter
public class ExcelDecryptMap<K, V> extends LinkedHashMap<K, V> {

    /**
     * 兼容数据库别名导出
     */
    @EncryptField(order = 5)
    private String 姓名;
    @EncryptField(order = 6)
    private String 身份证;
    @EncryptField(order = 7)
    private String 联系电话;
    @EncryptField(order = 8)
    private String 家庭住址;
    @EncryptField(order = 9)
    private String 人员姓名;
    @EncryptField(order = 10)
    private String 身份证号;
    @EncryptField(order = 11)
    private String 手机号码;
    @EncryptField(order = 12)
    private String 现居住地;
    @EncryptField(order = 13)
    private String 组织书记;
    @EncryptField(order = 14)
    private String 组织联系人;

}
