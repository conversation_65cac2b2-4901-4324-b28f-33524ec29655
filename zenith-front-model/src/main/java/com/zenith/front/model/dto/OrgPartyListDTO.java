package com.zenith.front.model.dto;

import com.zenith.front.model.custom.HasSubordinateField;
import com.zenith.front.model.validate.group.Common1Group;
import lombok.*;

import javax.validation.constraints.Max;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotBlank;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @Description: 党组查询模板
 * @date 2021-10-05
 */
@EqualsAndHashCode(callSuper = true)
@Data
@ToString
@NoArgsConstructor
@AllArgsConstructor
public class OrgPartyListDTO extends HasSubordinateField {


    /**
     * 组织层级码
     */
    @NotBlank(groups = Common1Group.class, message = "orgCode 不能为空")
    private String orgCode;
    /**
     * 党组类别
     */
    private List<String> d108CodeList;
    /**
     * 搜索框,按名称搜索
     */
    private String partyName;
    /**
     * 分页页数
     */
    @Min(value = 1, groups = {Common1Group.class}, message = "页码最小为1")
    private Integer pageNum;
    /**
     * 分页每页数
     */
    @Max(value = 100, groups = {Common1Group.class}, message = "每页条数大小范围在1-100")
    private Integer pageSize;

}
