package com.zenith.front.model.bean;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.zenith.front.common.encrypt.typehandler.EncryptTypeHandler;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * <p>
 * 选调生信息
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-07
 */
@Data
@TableName(value = "ccp_unit_mem_select", autoResultMap = true)
public class UnitMemSelect extends Model<UnitMemSelect> {

    private static final long serialVersionUID = 1L;

    @TableId(value = "code", type = IdType.ASSIGN_UUID)
    private String code;

    /**
     * 是否党员
     */
    @TableField("mem_type_code")
    private String memTypeCode;

    @TableField("mem_type_name")
    private String memTypeName;

    /**
     * 党员code
     */
    @TableField("mem_code")
    private String memCode;

    /**
     * 姓名
     */
    @TableField("mem_name")
    private String memName;

    /**
     * 性别code
     */
    @TableField("sex_code")
    private String sexCode;

    /**
     * 性别
     */
    @TableField("sex_name")
    private String sexName;

    /**
     * 身份证
     */
    @TableField("mem_idcard")
    private String memIdcard;

    /**
     * 出生日期
     */
    @TableField("birthday")
    private Date birthday;

    /**
     * 政治面貌code
     */
    @TableField("d89_code")
    private String d89Code;

    /**
     * 政治面貌
     */
    @TableField("d89_name")
    private String d89Name;

    /**
     * 学历情况
     */
    @TableField("d07_code")
    private String d07Code;

    /**
     * 学历情况
     */
    @TableField("d07_name")
    private String d07Name;

    /**
     * 电话
     */
    @TableField("phone")
    private String phone;

    /**
     * 选调开始时间
     */
    @TableField("start_time")
    private Date startTime;

    /**
     * 选调结束时间
     */
    @TableField("end_time")
    private Date endTime;

    /**
     * 选调单位层级code
     */
    @TableField("d144_code")
    private String d144Code;

    /**
     * 选调单位层级name
     */
    @TableField("d144_name")
    private String d144Name;

    /**
     * 是否双一流大学生
     */
    @TableField("is_double_first")
    private Integer isDoubleFirst;

    /**
     * 双一流大学名称
     */
    @TableField("double_first_name")
    private String doubleFirstName;

    /**
     * 离开时间
     */
    @TableField("leave_time")
    private Date leaveTime;

    /**
     * 离开说明
     */
    @TableField("leave_remark")
    private String leaveRemark;

    /**
     * 单位code
     */
    @TableField("unit_code")
    private String unitCode;

    /**
     * 单位名称
     */
    @TableField("unit_name")
    private String unitName;

    /**
     * 是否本单位班子成员（unit_mem_开头）：1-是；0-否
     */
    @TableField("is_unit_mem")
    private Integer isUnitMem;

    /**
     * 是否党组织班子成员（org_mem_开头）：1-是；0-否
     */
    @TableField("is_org_mem")
    private Integer isOrgMem;

    /**
     * 是否驻村干部（cadre_mem_开头）：1-是；0-否
     */
    @TableField("is_cadre_mem")
    private Integer isCadreMem;

    /**
     * 是否社区工作者（work_mem_开头）：1-是；0-否
     */
    @TableField("is_work_mem")
    private Integer isWorkMem;

    /**
     * 是否后备干部（reserve_mem_开头）：1-是；0-否
     */
    @TableField("is_reserve_mem")
    private Integer isReserveMem;

    /**
     * 班子成员职务代码dict_d25
     */
    @TableField("unit_mem_d25_code")
    private String unitMemD25Code;

    /**
     * 班子成员职务代码dict_d25
     */
    @TableField("unit_mem_d25_name")
    private String unitMemD25Name;

    /**
     * 批准的文号或文件
     */
    @TableField("unit_mem_file_number")
    private String unitMemFileNumber;

    /**
     * 任职开始时间
     */
    @TableField("unit_mem_start_date")
    private Date unitMemStartDate;

    /**
     * 报酬（万元/年）
     */
    @TableField("unit_mem_reward")
    private BigDecimal unitMemReward;

    /**
     * 是否参加城镇职工养老保险
     */
    @TableField("unit_mem_endowment_insurance_for_urban_employees")
    private Integer unitMemEndowmentInsuranceForUrbanEmployees;

    /**
     * 人员来源代码
     */
    @TableField("unit_mem_d0121_code")
    private String unitMemD0121Code;

    /**
     * 人员来源说明
     */
    @TableField("unit_mem_d0121_name")
    private String unitMemD0121Name;

    /**
     * 行政职务说明
     */
    @TableField("unit_mem_remark")
    private String unitMemRemark;

    /**
     * 是否在任
     */
    @TableField("unit_mem_is_incumbent")
    private Integer unitMemIsIncumbent;

    /**
     * 兼任情况代码 dict_d26
     */
    @TableField("unit_mem_d26_code")
    private String unitMemD26Code;

    /**
     * 兼任情况代码 dict_d26
     */
    @TableField("unit_mem_d26_name")
    private String unitMemD26Name;

    /**
     * 最新届次
     */
    @TableField("unit_mem_elect_code")
    private String unitMemElectCode;

    /**
     * 党内职务代码
     */
    @TableField("org_mem_d022_code")
    private String orgMemD022Code;

    /**
     * 党内职务名称
     */
    @TableField(value = "org_mem_d022_name", typeHandler = EncryptTypeHandler.class)
    private String orgMemD022Name;

    /**
     * 党内职务说明
     */
    @TableField("org_mem_duty_explain")
    private String orgMemDutyExplain;

    /**
     * 任职开始时间
     */
    @TableField("org_mem_start_date")
    private Date orgMemStartDate;

    /**
     * 决定或批准任职的文号
     */
    @TableField("org_mem_file_number")
    private String orgMemFileNumber;

    /**
     * 届次code
     */
    @TableField("org_mem_elect_code")
    private String orgMemElectCode;

    /**
     * 是否中层管理人员 1是 0否
     */
    @TableField("org_mem_has_middle_management")
    private Integer orgMemHasMiddleManagement;

    /**
     * 人员来源code
     */
    @TableField("org_mem_d121_code")
    private String orgMemD121Code;

    /**
     * 人员来源name
     */
    @TableField("org_mem_d121_name")
    private String orgMemD121Name;

    /**
     * 是否参加县级集中轮训
     */
    @TableField("org_mem_has_part_training")
    private Integer orgMemHasPartTraining;

    /**
     * 报酬（万元/年）
     */
    @TableField("org_mem_reward")
    private BigDecimal orgMemReward;

    /**
     * 是否参加城镇职工养老保险
     */
    @TableField("org_mem_endowment_insurance_for_urban_employees")
    private Integer orgMemEndowmentInsuranceForUrbanEmployees;

    /**
     * 1.驻村第一书记、2.驻村干部
     */
    @TableField("cadre_mem_d140_code")
    private String cadreMemD140Code;

    @TableField("cadre_mem_d140_name")
    private String cadreMemD140Name;

    /**
     * 1.中直、2.省直、3.市直、4.县直、5.乡镇
     */
    @TableField("cadre_mem_d141_code")
    private String cadreMemD141Code;

    @TableField("cadre_mem_d141_name")
    private String cadreMemD141Name;

    /**
     * 驻村开始时间
     */
    @TableField("cadre_mem_start_date")
    private Date cadreMemStartDate;

    /**
     * 预计驻村结束时间
     */
    @TableField("cadre_mem_resident_date")
    private Date cadreMemResidentDate;

    /**
     * 派出单位名称及职务
     */
    @TableField("cadre_mem_dispatch_position")
    private String cadreMemDispatchPosition;

    /**
     * 选派层级code
     */
    @TableField("cadre_mem_d197_code")
    private String cadreMemD197Code;

    @TableField("cadre_mem_d197_name")
    private String cadreMemD197Name;

    /**
     * 是否县乡领导班子成员帮带人（1是）
     */
    @TableField("work_mem_has_leaders_help_people")
    private Integer workMemHasLeadersHelpPeople;

    /**
     * 到村任职补助经费
     */
    @TableField("work_mem_subsidies")
    private BigDecimal workMemSubsidies;

    /**
     * 是否专职党务工作者（1是）
     */
    @TableField("work_mem_has_party_work")
    private Integer workMemHasPartyWork;

    /**
     * 是否推荐为两代表一委员（1是）
     */
    @TableField("work_mem_has_two_one_member")
    private Integer workMemHasTwoOneMember;

    @TableField("work_mem_d116_name")
    private String workMemD116Name;

    /**
     * 录用来源
     */
    @TableField("work_mem_d116_code")
    private String workMemD116Code;

    /**
     * 岗位
     */
    @TableField("work_mem_d143_code")
    private String workMemD143Code;

    /**
     * 岗位
     */
    @TableField("work_mem_d143_name")
    private String workMemD143Name;

    /**
     * 行政职务说明
     */
    @TableField("work_mem_remark")
    private String workMemRemark;

    /**
     * 行政职务说明
     */
    @TableField("reserve_mem_remark")
    private String reserveMemRemark;

    /**
     * 是否县乡领导班子成员帮带人（1是）
     */
    @TableField("reserve_mem_has_leaders_help_people")
    private Integer reserveMemHasLeadersHelpPeople;

    /**
     * 到村任职补助经费
     */
    @TableField("reserve_mem_subsidies")
    private BigDecimal reserveMemSubsidies;

    /**
     * 岗位
     */
    @TableField("reserve_mem_d143_code")
    private String reserveMemD143Code;

    /**
     * 岗位
     */
    @TableField("reserve_mem_d143_name")
    private String reserveMemD143Name;

    /**
     * 是否在村工作，1-是，0-否
     */
    @TableField("reserve_mem_is_work_village")
    private Integer reserveMemIsWorkVillage;

    /**
     * 帮带人单位
     */
    @TableField("reserve_mem_help_unit")
    private String reserveMemHelpUnit;

    /**
     * 帮带人姓名
     */
    @TableField("reserve_mem_help_mem")
    private String reserveMemHelpMem;

    /**
     * 目前就业岗位
     */
    @TableField("reserve_mem_now_job")
    private String reserveMemNowJob;

    @TableField("create_time")
    private Date createTime;

    @TableField("create_user")
    private String createUser;

    @TableField("update_time")
    private Date updateTime;

    @TableField("update_user")
    private String updateUser;

    @TableField("delete_time")
    private Date deleteTime;

    /**
     * 单位班子关联成员code
     */
    @TableField("unit_mem_code")
    private String unitMemCode;

    /**
     * 组织班子关联成员code
     */
    @TableField("org_mem_code")
    private String orgMemCode;

    /**
     * 驻村班子关联成员code
     */
    @TableField("cadre_mem_code")
    private String cadreMemCode;

    /**
     * 工作者关联成员code
     */
    @TableField("work_mem_code")
    private String workMemCode;

    /**
     * 后备干部关联成员code
     */
    @TableField("reserve_mem_code")
    private String reserveMemCode;
}
