package com.zenith.front.model.bean;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2022-12-17
 */
@TableName("ccp_org_life_part")
public class OrgLifePart extends Model<OrgLifePart> {

    private static final long serialVersionUID = 1L;

    @TableId(value = "code", type = IdType.ASSIGN_UUID)
    private String code;
    /**
     * 组织生活code
     */
    @TableField("org_life_code")
    private String orgLifeCode;
    /**
     * 参与组织code
     */
    @TableField("org_code")
    private String orgCode;

    @TableField("org_level_code")
    private String orgLevelCode;

    @TableField("name")
    private String name;

    @TableField("create_time")
    private Date createTime;

    @TableField("update_time")
    private Date updateTime;

    @TableField("delete_time")
    private Date deleteTime;


    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getOrgLifeCode() {
        return orgLifeCode;
    }

    public void setOrgLifeCode(String orgLifeCode) {
        this.orgLifeCode = orgLifeCode;
    }

    public String getOrgCode() {
        return orgCode;
    }

    public void setOrgCode(String orgCode) {
        this.orgCode = orgCode;
    }

    public String getOrgLevelCode() {
        return orgLevelCode;
    }

    public void setOrgLevelCode(String orgLevelCode) {
        this.orgLevelCode = orgLevelCode;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    public Date getDeleteTime() {
        return deleteTime;
    }

    public void setDeleteTime(Date deleteTime) {
        this.deleteTime = deleteTime;
    }

    @Override
    protected Serializable pkVal() {
        return this.code;
    }

    @Override
    public String toString() {
        return "OrgLifePart{" +
                "code=" + code +
                ", orgLifeCode=" + orgLifeCode +
                ", orgCode=" + orgCode +
                ", orgLevelCode=" + orgLevelCode +
                ", createTime=" + createTime +
                ", updateTime=" + updateTime +
                ", deleteTime=" + deleteTime +
                "}";
    }
}
