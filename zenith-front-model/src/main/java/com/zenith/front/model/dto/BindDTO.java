package com.zenith.front.model.dto;

import javax.validation.constraints.NotBlank;

/**
 * <AUTHOR>
 * @date 2019/5/175:07 PM
 */
public class BindDTO {
    /***
     * appID
     * */
    @NotBlank(message = "appId不能为空")
    private String appId;
    /***
     * openId
     * */
    @NotBlank(message = "openID不能为空")
    private String openId;
    /**
     * 人员名称
     * */
    @NotBlank(message = "用户名称不能为空")
    private String name;
    /***
     * 人员身份证
     * */
    @NotBlank(message = "人员身份证号码不能为空")
    private String idCard;

    private String ip;

    private String uaHead;

    public String getAppId() {
        return appId;
    }

    public BindDTO setAppId(String appId) {
        this.appId = appId;
        return this;
    }

    public String getOpenId() {
        return openId;
    }

    public BindDTO setOpenId(String openId) {
        this.openId = openId;
        return this;
    }

    public String getName() {
        return name;
    }

    public BindDTO setName(String name) {
        this.name = name;
        return this;
    }

    public String getIdCard() {
        return idCard;
    }

    public BindDTO setIdCard(String idCard) {
        this.idCard = idCard;
        return this;
    }

    public String getIp() {
        return ip;
    }

    public BindDTO setIp(String ip) {
        this.ip = ip;
        return this;
    }

    public String getUaHead() {
        return uaHead;
    }

    public BindDTO setUaHead(String uaHead) {
        this.uaHead = uaHead;
        return this;
    }

    @Override
    public String toString() {
        return "BindDTO{" +
                "appId='" + appId + '\'' +
                ", openId='" + openId + '\'' +
                ", name='" + name + '\'' +
                ", idCard='" + idCard + '\'' +
                ", ip='" + ip + '\'' +
                ", uaHead='" + uaHead + '\'' +
                '}';
    }
}
