package com.zenith.front.model.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

/**
 * <AUTHOR>
 * @Date 2021/8/4 17:55
 * @Version 1.0
 */
@Data
@ToString
@NoArgsConstructor
@AllArgsConstructor
public class OrgExtendDTO {

    /**
     * 唯一标识符code
     */
    private String code;

    /**
     * 组织唯一标识符，中组部code
     */
    private String zbCode;

    /**
     * 组织层级码
     */
    private String orgCode;

    /**
     * 本年开展评议情况---民主评议党员
     */
    private Integer appraisalSituation;

    /**
     * 参加评议党员数
     */
    private Integer joinReviewMems;

    /**
     * 结束评议党员数
     */
    private Integer endReviewMems;

    /**
     * 表彰党员数
     */
    private Integer recognitionMems;

    /**
     * 评定为不合格党员数
     */
    private Integer evaluateUnqualifiedMems;

    /**
     * 期限改正党员数
     */
    private Integer deadlineCorrectMems;

    /**
     * 劝退党员数
     */
    private Integer stopMems;

    /**
     * 劝而不退除名党员数
     */
    private Integer persuadeDismissMems;

    /**
     * 自行脱党除名党员数
     */
    private Integer removeYourselfMems;

    /**
     * 党员违纪被开除党籍党员数
     */
    private Integer violationDisciplineMems;

    /**
     * 党员违纪给予除出党以外纪律处分党员数
     */
    private Integer disciplinaryOutsideMems;

    /**
     * 限期改正期满仍无转变予以劝退以及劝而不退除名党员数
     */
    private Integer persuadePersuadeMems;

    /**
     * 党员培训总人次---党员培训情况
     */
    private Integer trainMemTotal;

    /**
     * 基层党组织书记培训人次
     */
    private Integer basicSecretaryTrain;

    /**
     * 非公有制经济组织和社会组织党员培训人次
     */
    private Integer nonPublicSocietyOrganTrainMem;

    /**
     * 新党员培训人次
     */
    private Integer trainNewMem;

    /**
     * 流动党员培训人次
     */
    private Integer trainMemFlow;

    /**
     * 边疆民族地区基层党员教育培训人次
     */
    private Integer trainFrontierBasicMem;

    /**
     * 党员创业就业技能培训人次
     */
    private Integer trainSkillMem;

    /**
     * 农村党组织书记培训人次
     */
    private Integer countrySecretaryTrain;

    /**
     * 社区党务工作者培训人次
     */
    private Integer workerTrain;

    /**
     * 国有企业二级及以下单位（部门）党组织书记培训人次
     */
    private Integer stateOwnedSecretaryTrain;

    /**
     * 非公有制经济控制企业党组织书记培训人次
     */
    private Integer nonPublicCompanySecretaryTrain;

    /**
     * 党员年度集中学习培训达标的基层党组织个
     */
    private Integer trainMemStandardBasicOrgan;

    /**
     * 基层党组织书记和班子成员年度集中学习培训达标的基层党组织个
     */
    private Integer trainSecretaryStandardBasicOrgan;

    /**
     * 直接组织开展农村党员集中培训的乡镇个数
     */
    private Integer trainMemTowns;

    /**
     * 党员干部现代远程教育终端站点共个数
     */
    private Integer memEduSiteNum;

    /**
     * 乡镇（街道）远程教育终端站点个数
     */
    private Integer townEduSiteNum;

    /**
     * 社区（居委会）远程教育终端站点个数
     */
    private Integer communityEduSiteNum;

    /**
     * 建制村远程教育终端站点个数
     */
    private Integer villageEduSiteNum;

    /**
     * 互联网传播远程教育终端站点个数
     */
    private Integer internetEduSiteNum;

    /**
     * 有线远程教育终端站点个数
     */
    private Integer wiredEduSiteNum;

    /**
     * 卫星远程教育终端站点个数
     */
    private Integer satelliteEduSiteNum;

    /**
     * 远程教育终端站点管理员个数
     */
    private Integer eduSiteManageNum;

    /**
     * 乡镇（街道）远程教育终端站点干部个数
     */
    private Integer townEduSiteManageNum;

    /**
     * 村、社区远程教育终端站点干部名
     */
    private Integer villageEduSiteManageNum;

    /**
     * 远程教育终端站点志愿者名
     */
    private Integer eduSiteVolunteerNum;

    /**
     * 农村党员远程教育培训人次
     */
    private Integer villageLongEduMemNum;

    /**
     * 学校支部类型---普通高等学校
     */
    private String d73Code;

    /**
     * 学校支部类型名称
     */
    private String d73Name;

    /**
     * 本年度追授优秀共产党员数---党内表彰情况
     */
    private Integer currentYearAwardMemNum;

    /**
     * 其中抗震救灾追授党员数
     */
    private Integer earthquakeReliefAwardMemNum;

    /**
     * 是否接收了流动党员---流动党员
     */
    private Integer hasAcceptFlowMem;

    /**
     * 是否未转入组织关系的党员单独建立的---多重党员
     */
    private Integer notRelationMemBuild;

    /**
     * 在国（境）外期间已作出党处理的人数---出国
     */
    private Integer outPartyAbroadMem;

    /**
     * 回国后已作出党处理的人数
     */
    private Integer outPartyBackHomeMem;

    /**
     * 是否本年届满---基础扩展信息
     */
    private Integer currentYearEndTenure;

    /**
     * 是否本年换届
     */
    private Integer currentYearChangeTerm;

    /**
     * 上年底党员总数
     */
    private Integer lastYearMemTotal;

    /**
     * 本年重新入党人数
     */
    private Integer currentYearJoinNum;

    /**
     * 本年恢复党籍人数
     */
    private Integer currentYearReconvertNum;

    /**
     * 停止党籍后本年恢复党籍人数
     */
    private Integer stopCurrentYearReconvertNum;

    /**
     * 本年转入组织关系数
     */
    private Integer currentYearIntoRelationNum;

    /**
     * 本年整建制转入数
     */
    private Integer currentYearFixedIntoNum;

    /**
     * 本年整建制转出数
     */
    private Integer currentYearFixedOutNum;

    /**
     * 本年转出组织关系数
     */
    private Integer currentYearOutRelationNum;

    /**
     * 本年死亡党员数
     */
    private Integer currentYearDieMemNum;

    /**
     * 本年转出组织关系介绍信回执数
     */
    private Integer currentYearOutRelationLetterNum;

    /**
     * 省级党委审批追认的共产党员数
     */
    private Integer provincialApprovalMemNum;

    /**
     * 开展公推直选标识
     */
    private Integer electedByDirect;

    /**
     * 书记副书记选举方式
     */
    private String deputySecretaryElect;

    /**
     * 开展党员定期评议基层党组织领导班子标识
     */
    private Integer appraiseBasicLeaderTeam;

    /**
     * 开展党员旁听会议的基层党委数
     */
    private Integer attendMeetBasicOrganNum;

    /**
     * 参加旁听的党员数
     */
    private Integer joinAuditMemNum;
}
