package com.zenith.front.model.dto;

import com.zenith.front.model.bean.Task;
import com.zenith.front.model.validate.group.AddGroup;
import com.zenith.front.model.validate.group.DeleteGroup;
import com.zenith.front.model.validate.group.QueryGroup;
import com.zenith.front.model.validate.group.UpdateGroup;
import lombok.*;

import javax.validation.constraints.NotNull;
import java.util.List;

@ToString
public class TaskDTO{


    /**
     * 增加属性-接受对象集合
     * */
    @NotNull(groups = {AddGroup.class, UpdateGroup.class},message = "接受对象不允许为空!")
    private List<String> objectList;

    public List<String> getObjectList() {
        return objectList;
    }

    public void setObjectList(List<String> objectList) {
        this.objectList = objectList;
    }

    /**
     * 自增长id
     */
    //@NotNull(groups = {UpdateGroup.class},message = "任务标识符不允许为空!")
    private Integer id;
    /**
     * 唯一主键
     */
    @NotNull(groups = {QueryGroup.class,UpdateGroup.class, DeleteGroup.class},message = "任务标识符不允许为空!")
    private String code;
    /**
     * 任务名称
     */
    @NotNull(groups = {AddGroup.class,UpdateGroup.class},message = "任务名称不允许为空!")
    private String taskName;
    /**
     * 备注
     */
    @NotNull(groups = {AddGroup.class,UpdateGroup.class},message = "备注不允许为空!")
    private String taskRemark;
    /**
     * 任务对象类别(1.党员，2组织)
     */
    @NotNull(groups = {AddGroup.class,UpdateGroup.class},message = "对象类别不允许为空!")
    private Integer taskObjectType;
    /**
     * 任务对象
     */
    private String taskObject;
    /**
     * 开始时间
     */
    @NotNull(groups = {AddGroup.class,UpdateGroup.class},message = "开始时间不允许为空!")
    private java.util.Date startDate;
    /**
     * 结束时间
     */
    @NotNull(groups = {AddGroup.class,UpdateGroup.class},message = "结束时间不允许为空!")
    private java.util.Date endDate;
    /**
     * 任务分值
     */
    private Integer taskFraction;
    /**
     * 任务周期
     */
    @NotNull(groups = {AddGroup.class,UpdateGroup.class},message = "周期不允许为空!")
    private Integer taskCycle;
    /**
     * 创建时间
     */
    private java.util.Date createTime;
    /**
     * 更新时间
     */
    private java.util.Date updateTime;
    /**
     * 删除时间
     */
    private java.util.Date deleteTime;
   	/**
   	 * 任务状态
   	 */
    private Integer taskStatus;
   	/**
   	 * 创建组织唯一标识符
   	 */
    @NotNull(groups = {AddGroup.class},message = "创建组织标识符不允许为空!")
    private String createOrgCode;
   	/**
   	 * 创建组织层级码
   	 */
    @NotNull(groups = {AddGroup.class},message = "创建组织层级码不允许为空!")
    private String createOrgOrgCode;



    private String taskFile;

    public String getTaskFile() {
        return taskFile;
    }

    public void setTaskFile(String taskFile) {
        this.taskFile = taskFile;
    }

    public TaskDTO setId(Integer id){
        this.id = id;
        return this;
    }
    public Integer getId() {
    	return this.id;
    }
    public TaskDTO setCode(String code){
        this.code = code;
        return this;
    }
    public String getCode() {
    	return this.code;
    }
    public TaskDTO setTaskName(String taskName){
        this.taskName = taskName;
        return this;
    }
    public String getTaskName() {
    	return this.taskName;
    }
    public TaskDTO setTaskRemark(String taskRemark){
        this.taskRemark = taskRemark;
        return this;
    }
    public String getTaskRemark() {
    	return this.taskRemark;
    }
    public TaskDTO setTaskObjectType(Integer taskObjectType){
        this.taskObjectType = taskObjectType;
        return this;
    }
    public Integer getTaskObjectType() {
    	return this.taskObjectType;
    }
    public TaskDTO setTaskObject(String taskObject){
        this.taskObject = taskObject;
        return this;
    }
    public String getTaskObject() {
    	return this.taskObject;
    }
    public TaskDTO setStartDate(java.util.Date startDate){
        this.startDate = startDate;
        return this;
    }
    public java.util.Date getStartDate() {
    	return this.startDate;
    }
    public TaskDTO setEndDate(java.util.Date endDate){
        this.endDate = endDate;
        return this;
    }
    public java.util.Date getEndDate() {
    	return this.endDate;
    }
    public TaskDTO setTaskFraction(Integer taskFraction){
        this.taskFraction = taskFraction;
        return this;
    }
    public Integer getTaskFraction() {
    	return this.taskFraction;
    }
    public TaskDTO setTaskCycle(Integer taskCycle){
        this.taskCycle = taskCycle;
        return this;
    }
    public Integer getTaskCycle() {
    	return this.taskCycle;
    }
    public TaskDTO setCreateTime(java.util.Date createTime){
        this.createTime = createTime;
        return this;
    }
    public java.util.Date getCreateTime() {
    	return this.createTime;
    }
    public TaskDTO setUpdateTime(java.util.Date updateTime){
        this.updateTime = updateTime;
        return this;
    }
    public java.util.Date getUpdateTime() {
    	return this.updateTime;
    }
    public TaskDTO setDeleteTime(java.util.Date deleteTime){
        this.deleteTime = deleteTime;
        return this;
    }
    public java.util.Date getDeleteTime() {
    	return this.deleteTime;
    }
    public TaskDTO setTaskStatus(Integer taskStatus){
        this.taskStatus = taskStatus;
        return this;
    }
    public Integer getTaskStatus() {
    	return this.taskStatus;
    }
    public TaskDTO setCreateOrgCode(String createOrgCode){
        this.createOrgCode = createOrgCode;
        return this;
    }
    public String getCreateOrgCode() {
    	return this.createOrgCode;
    }
    public TaskDTO setCreateOrgOrgCode(String createOrgOrgCode){
        this.createOrgOrgCode = createOrgOrgCode;
        return this;
    }
    public String getCreateOrgOrgCode() {
    	return this.createOrgOrgCode;
    }


    public Task toModel(){
        Task model = new Task();
        model.setId(this.id);
        model.setCode(this.code);
        model.setTaskName(this.taskName);
        model.setTaskRemark(this.taskRemark);
        model.setTaskObjectType(this.taskObjectType);
        model.setTaskObject(this.taskObject);
        model.setStartDate(this.startDate);
        model.setEndDate(this.endDate);
        model.setTaskFraction(this.taskFraction);
        model.setTaskCycle(this.taskCycle);
        model.setCreateTime(this.createTime);
        model.setUpdateTime(this.updateTime);
        model.setDeleteTime(this.deleteTime);
        model.setTaskStatus(this.taskStatus);
        model.setCreateOrgCode(this.createOrgCode);
        model.setCreateOrgOrgCode(this.createOrgOrgCode);
        model.setTaskFile(this.taskFile);
        return model;
    }


}