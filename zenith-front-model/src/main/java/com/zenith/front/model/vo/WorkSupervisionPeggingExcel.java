package com.zenith.front.model.vo;

import com.zenith.front.common.annotation.ExcelAttribute;
import lombok.Data;

/**
 * <AUTHOR>
 * @Date 2021/11/5 18:34
 * @Version 1.0
 */
@Data
public class WorkSupervisionPeggingExcel {

    @ExcelAttribute(sort = 0)
    private String name;

    @ExcelAttribute(sort = 1)
    private String d01Name;

    @ExcelAttribute(sort = 2)
    private String contacter;

    @ExcelAttribute(sort = 3)
    private String contactPhone;

    @ExcelAttribute(sort = 4)
    private String secretary;

    @ExcelAttribute(sort = 5)
    private String v6;
    @ExcelAttribute(sort = 6)
    private String v7;
    @ExcelAttribute(sort = 7)
    private String v8;
    @ExcelAttribute(sort = 8)
    private String v9;
    @ExcelAttribute(sort = 9)
    private String v10;
    @ExcelAttribute(sort = 10)
    private String v11;
    @ExcelAttribute(sort = 11)
    private String v12;
    @ExcelAttribute(sort = 12)
    private String v13;
    @ExcelAttribute(sort = 13)
    private String v14;
    @ExcelAttribute(sort = 14)
    private String v15;
    @ExcelAttribute(sort = 15)
    private String v16;
}
