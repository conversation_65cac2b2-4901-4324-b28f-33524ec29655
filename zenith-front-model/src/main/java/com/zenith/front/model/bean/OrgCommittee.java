package com.zenith.front.model.bean;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.zenith.front.common.encrypt.typehandler.EncryptTypeHandler;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2021-07-11
 */
@TableName(value = "ccp_org_committee", autoResultMap = true)
public class OrgCommittee extends Model<OrgCommittee> {

    private static final long serialVersionUID = 1L;

    /**
     * 唯一自增长主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 唯一标识符code
     */
    @TableField("code")
    private String code;

    /**
     * es唯一主键
     */
    @TableField("es_id")
    private String esId;

    /**
     * 人员code
     */
    @TableField("mem_code")
    private String memCode;

    /**
     * 党内职务代码
     */
    @TableField("d022_code")
    private String d022Code;

    /**
     * 党内职务名称
     */
    @TableField(value = "d022_name", typeHandler = EncryptTypeHandler.class)
    private String d022Name;

    /**
     * 党内职务说明
     */
    @TableField("duty_explain")
    private String dutyExplain;

    /**
     * 任职开始时间
     */
    @TableField("start_date")
    private Date startDate;

    /**
     * 任职结束时间
     */
    @TableField(value = "end_date")
    private Date endDate;

    /**
     * 任职结束时间  组织换届自动将时间设置成新届次创建时间
     */
    @TableField("emp_end_date")
    private Date empEndDate;
    /**
     * 是否在任
     */
    @TableField("is_incumbent")
    private Integer isIncumbent;

    /**
     * 职务级别code
     */
    @TableField("d51_code")
    private String d51Code;

    /**
     * 职务级别name
     */
    @TableField("d51_name")
    private String d51Name;

    /**
     * 决定或批准任职的文号
     */
    @TableField("file_number")
    private String fileNumber;

    /**
     * 排序
     */
    @TableField("sort")
    private Integer sort;

    /**
     * 届次code
     */
    @TableField("elect_code")
    private String electCode;

    /**
     * 时间戳
     */
    @TableField("timestamp")
    private Date timestamp;

    /**
     * 删除时间
     */
    @TableField("delete_time")
    private Date deleteTime;

    /**
     * 更新时间
     */
    @TableField("update_time")
    private Date updateTime;

    /**
     * 创建时间
     */
    @TableField("create_time")
    private Date createTime;

    /**
     * 组织zb_code
     */
    @TableField("zb_code")
    private String zbCode;

    /**
     * 任职时所在组织唯一标识code
     */
    @TableField("org_code")
    private String orgCode;

    /**
     * 任职时所在组织层级码
     */
    @TableField("position_org_code")
    private String positionOrgCode;

    /**
     * 任职时所在组织名称
     */
    @TableField("position_org_name")
    private String positionOrgName;

    /**
     * 是否历史数据
     */
    @TableField("is_history")
    private Integer isHistory;

    /**
     * 最近更更新账号
     */
    @TableField("update_account")
    private String updateAccount;

    @TableField("mem_type_code")
    private String memTypeCode;

    @TableField("mem_type_name")
    private String memTypeName;

    @TableField(value = "mem_name", typeHandler = EncryptTypeHandler.class)
    private String memName;

    @TableField("sex_code")
    private String sexCode;

    @TableField("sex_name")
    private String sexName;

    @TableField("birthday")
    private Date birthday;

    @TableField("d07_code")
    private String d07Code;

    @TableField("d07_name")
    private String d07Name;

    @TableField(value = "mem_idcard", typeHandler = EncryptTypeHandler.class)
    private String memIdcard;

    /**
     * 是否中层管理人员 1是 0否
     */
    @TableField("has_middle_management")
    private Integer hasMiddleManagement;

    /**
     * 离任原因code
     */
    @TableField("d120_code")
    private String d120Code;

    /**
     * 离任原因名称
     */
    @TableField("d120_name")
    private String d120Name;
    /**
     * 人员来源code
     */
    @TableField("d121_code")
    private String d121Code;

    /**
     * 人员来源name
     */
    @TableField("d121_name")
    private String d121Name;

    /**
     * 是否参加县级集中轮训
     */
    @TableField("has_part_training")
    private Integer hasPartTraining;

    /**
     * 报酬（万元/年）
     */
    @TableField("reward")
    private BigDecimal reward;

    /**
     * 是否村任职选调生（选择框、必填）(1 是 0 否)
     */
    @TableField("has_village_transfer_student")
    private Integer hasVillageTransferStudent;
    /**
     * 选调单位层级 dict_d144
     */
    @TableField("d144_code")
    private String d144Code;

    @TableField("d144_name")
    private String d144Name;
    /**
     * 主单位类别为乡镇
     * 班子成员来源code（整合是否为五方面人员）
     */
    @TableField("d138_code")
    private String d138Code;

    /**
     * 班子成员来源name
     */
    @TableField("d138_name")
    private String d138Name;

    /**
     * 是否参加城镇职工养老保险
     */
    @TableField("endowment_insurance_for_urban_employees")
    private Integer endowmentInsuranceForUrbanEmployees;

    /**
     * 是否为五方面人员
     */
    @TableField("whether_it_is_from_five_aspects")
    private Integer whetherItIsFromFiveAspects;
    /**
     * 政治面貌 dict_d89
     */
    @TableField("d89_code")
    private String d89Code;

    @TableField("d89_name")
    private String d89Name;
    /**
     * 职务（手填）
     */
    @TableField("current_position_job")
    private String currentPositionJob;
    /**
     * 照片路径
     */
    @TableField("photo_path")
    private String photoPath;

    @TableField("file_name")
    private String fileName;


    /**
     * 是否双一流大学生
     */
    @TableField("is_double_first")
    private Integer isDoubleFirst;

    public Date getEmpEndDate() {
        return empEndDate;
    }

    public void setEmpEndDate(Date empEndDate) {
        this.empEndDate = empEndDate;
    }

    public Integer getIsDoubleFirst() {
        return isDoubleFirst;
    }

    public void setIsDoubleFirst(Integer isDoubleFirst) {
        this.isDoubleFirst = isDoubleFirst;
    }


    public String getD120Code() {
        return d120Code;
    }

    public void setD120Code(String d120Code) {
        this.d120Code = d120Code;
    }

    public String getD120Name() {
        return d120Name;
    }

    public void setD120Name(String d120Name) {
        this.d120Name = d120Name;
    }

    public String getD121Code() {
        return d121Code;
    }

    public void setD121Code(String d121Code) {
        this.d121Code = d121Code;
    }

    public String getD121Name() {
        return d121Name;
    }

    public void setD121Name(String d121Name) {
        this.d121Name = d121Name;
    }

    public Integer getHasPartTraining() {
        return hasPartTraining;
    }

    public void setHasPartTraining(Integer hasPartTraining) {
        this.hasPartTraining = hasPartTraining;
    }

    public Integer getHasMiddleManagement() {
        return hasMiddleManagement;
    }

    public void setHasMiddleManagement(Integer hasMiddleManagement) {
        this.hasMiddleManagement = hasMiddleManagement;
    }

    public String getMemTypeCode() {
        return memTypeCode;
    }

    public void setMemTypeCode(String memTypeCode) {
        this.memTypeCode = memTypeCode;
    }

    public String getMemTypeName() {
        return memTypeName;
    }

    public void setMemTypeName(String memTypeName) {
        this.memTypeName = memTypeName;
    }

    public String getMemName() {
        return memName;
    }

    public void setMemName(String memName) {
        this.memName = memName;
    }

    public String getSexCode() {
        return sexCode;
    }

    public void setSexCode(String sexCode) {
        this.sexCode = sexCode;
    }

    public String getSexName() {
        return sexName;
    }

    public void setSexName(String sexName) {
        this.sexName = sexName;
    }

    public Date getBirthday() {
        return birthday;
    }

    public void setBirthday(Date birthday) {
        this.birthday = birthday;
    }

    public String getD07Code() {
        return d07Code;
    }

    public void setD07Code(String d07Code) {
        this.d07Code = d07Code;
    }

    public String getD07Name() {
        return d07Name;
    }

    public void setD07Name(String d07Name) {
        this.d07Name = d07Name;
    }

    public String getMemIdcard() {
        return memIdcard;
    }

    public void setMemIdcard(String memIdcard) {
        this.memIdcard = memIdcard;
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getEsId() {
        return esId;
    }

    public void setEsId(String esId) {
        this.esId = esId;
    }

    public String getMemCode() {
        return memCode;
    }

    public void setMemCode(String memCode) {
        this.memCode = memCode;
    }

    public String getD022Code() {
        return d022Code;
    }

    public void setD022Code(String d022Code) {
        this.d022Code = d022Code;
    }

    public String getD022Name() {
        return d022Name;
    }

    public void setD022Name(String d022Name) {
        this.d022Name = d022Name;
    }

    public String getDutyExplain() {
        return dutyExplain;
    }

    public void setDutyExplain(String dutyExplain) {
        this.dutyExplain = dutyExplain;
    }

    public Date getStartDate() {
        return startDate;
    }

    public void setStartDate(Date startDate) {
        this.startDate = startDate;
    }

    public Date getEndDate() {
        return endDate;
    }

    public void setEndDate(Date endDate) {
        this.endDate = endDate;
    }

    public Integer getIsIncumbent() {
        return isIncumbent;
    }

    public void setIsIncumbent(Integer isIncumbent) {
        this.isIncumbent = isIncumbent;
    }

    public String getD51Code() {
        return d51Code;
    }

    public void setD51Code(String d51Code) {
        this.d51Code = d51Code;
    }

    public String getD51Name() {
        return d51Name;
    }

    public void setD51Name(String d51Name) {
        this.d51Name = d51Name;
    }

    public String getFileNumber() {
        return fileNumber;
    }

    public void setFileNumber(String fileNumber) {
        this.fileNumber = fileNumber;
    }

    public Integer getSort() {
        return sort;
    }

    public void setSort(Integer sort) {
        this.sort = sort;
    }

    public String getElectCode() {
        return electCode;
    }

    public void setElectCode(String electCode) {
        this.electCode = electCode;
    }

    public Date getTimestamp() {
        return timestamp;
    }

    public void setTimestamp(Date timestamp) {
        this.timestamp = timestamp;
    }

    public Date getDeleteTime() {
        return deleteTime;
    }

    public void setDeleteTime(Date deleteTime) {
        this.deleteTime = deleteTime;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public String getZbCode() {
        return zbCode;
    }

    public void setZbCode(String zbCode) {
        this.zbCode = zbCode;
    }

    public String getOrgCode() {
        return orgCode;
    }

    public void setOrgCode(String orgCode) {
        this.orgCode = orgCode;
    }

    public String getPositionOrgCode() {
        return positionOrgCode;
    }

    public void setPositionOrgCode(String positionOrgCode) {
        this.positionOrgCode = positionOrgCode;
    }

    public String getPositionOrgName() {
        return positionOrgName;
    }

    public void setPositionOrgName(String positionOrgName) {
        this.positionOrgName = positionOrgName;
    }

    public Integer getIsHistory() {
        return isHistory;
    }

    public void setIsHistory(Integer isHistory) {
        this.isHistory = isHistory;
    }

    public String getUpdateAccount() {
        return updateAccount;
    }

    public void setUpdateAccount(String updateAccount) {
        this.updateAccount = updateAccount;
    }

    public BigDecimal getReward() {
        return reward;
    }

    public void setReward(BigDecimal reward) {
        this.reward = reward;
    }

    public Integer getHasVillageTransferStudent() {
        return hasVillageTransferStudent;
    }

    public void setHasVillageTransferStudent(Integer hasVillageTransferStudent) {
        this.hasVillageTransferStudent = hasVillageTransferStudent;
    }

    public String getD144Code() {
        return d144Code;
    }

    public void setD144Code(String d144Code) {
        this.d144Code = d144Code;
    }

    public String getD144Name() {
        return d144Name;
    }

    public void setD144Name(String d144Name) {
        this.d144Name = d144Name;
    }

    public String getD138Code() {
        return d138Code;
    }

    public void setD138Code(String d138Code) {
        this.d138Code = d138Code;
    }

    public String getD138Name() {
        return d138Name;
    }

    public void setD138Name(String d138Name) {
        this.d138Name = d138Name;
    }

    public Integer getEndowmentInsuranceForUrbanEmployees() {
        return endowmentInsuranceForUrbanEmployees;
    }

    public void setEndowmentInsuranceForUrbanEmployees(Integer endowmentInsuranceForUrbanEmployees) {
        this.endowmentInsuranceForUrbanEmployees = endowmentInsuranceForUrbanEmployees;
    }

    public Integer getWhetherItIsFromFiveAspects() {
        return whetherItIsFromFiveAspects;
    }

    public void setWhetherItIsFromFiveAspects(Integer whetherItIsFromFiveAspects) {
        this.whetherItIsFromFiveAspects = whetherItIsFromFiveAspects;
    }

    public String getD89Code() {
        return d89Code;
    }

    public void setD89Code(String d89Code) {
        this.d89Code = d89Code;
    }

    public String getD89Name() {
        return d89Name;
    }

    public void setD89Name(String d89Name) {
        this.d89Name = d89Name;
    }

    public String getCurrentPositionJob() {
        return currentPositionJob;
    }

    public void setCurrentPositionJob(String currentPositionJob) {
        this.currentPositionJob = currentPositionJob;
    }

    public String getPhotoPath() {
        return photoPath;
    }

    public void setPhotoPath(String photoPath) {
        this.photoPath = photoPath;
    }

    public String getFileName() {
        return fileName;
    }

    public void setFileName(String fileName) {
        this.fileName = fileName;
    }

    @Override
    public Serializable pkVal() {
        return this.id;
    }

    @Override
    public String toString() {
        return "OrgCommittee{" +
                "id=" + id +
                ", code='" + code + '\'' +
                ", esId='" + esId + '\'' +
                ", memCode='" + memCode + '\'' +
                ", d022Code='" + d022Code + '\'' +
                ", d022Name='" + d022Name + '\'' +
                ", dutyExplain='" + dutyExplain + '\'' +
                ", startDate=" + startDate +
                ", endDate=" + endDate +
                ", isIncumbent=" + isIncumbent +
                ", d51Code='" + d51Code + '\'' +
                ", d51Name='" + d51Name + '\'' +
                ", fileNumber='" + fileNumber + '\'' +
                ", sort=" + sort +
                ", electCode='" + electCode + '\'' +
                ", timestamp=" + timestamp +
                ", deleteTime=" + deleteTime +
                ", updateTime=" + updateTime +
                ", createTime=" + createTime +
                ", zbCode='" + zbCode + '\'' +
                ", orgCode='" + orgCode + '\'' +
                ", positionOrgCode='" + positionOrgCode + '\'' +
                ", positionOrgName='" + positionOrgName + '\'' +
                ", isHistory=" + isHistory +
                ", updateAccount='" + updateAccount + '\'' +
                ", memTypeCode='" + memTypeCode + '\'' +
                ", memTypeName='" + memTypeName + '\'' +
                ", memName='" + memName + '\'' +
                ", sexCode='" + sexCode + '\'' +
                ", sexName='" + sexName + '\'' +
                ", birthday=" + birthday +
                ", d07Code='" + d07Code + '\'' +
                ", d07Name='" + d07Name + '\'' +
                ", memIdcard='" + memIdcard + '\'' +
                ", hasMiddleManagement='" + hasMiddleManagement + '\'' +
                ", d120Code='" + d120Code + '\'' +
                ", d120Name='" + d120Name + '\'' +
                ", d121Code='" + d121Code + '\'' +
                ", d121Name='" + d121Name + '\'' +
                ", hasPartTraining='" + hasPartTraining + '\'' +
                ", hasVillageTransferStudent='" + hasVillageTransferStudent + '\'' +
                ", d138Code='" + d138Code + '\'' +
                ", d138Name='" + d138Name + '\'' +
                ", isDoubleFirst=" + isDoubleFirst +
                ", endowmentInsuranceForUrbanEmployees='" + endowmentInsuranceForUrbanEmployees + '\'' +
                ", whetherItIsFromFiveAspects='" + whetherItIsFromFiveAspects + '\'' +
                '}';
    }
}
