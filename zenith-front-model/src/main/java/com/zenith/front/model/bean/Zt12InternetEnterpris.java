package com.zenith.front.model.bean;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2021-08-13
 */
@TableName("ccp_zt12_internet_enterpris")
public class Zt12InternetEnterpris extends Model<Zt12InternetEnterpris> {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(value = "code", type = IdType.ASSIGN_UUID)
    private String code;

    /**
     * 单位code
     */
    @TableField("unit_code")
    private String unitCode;

    /**
     * 建立联合党支部数
     */
    @TableField("build_unite_branch_num")
    private Integer buildUniteBranchNum;

    /**
     * 本科以上学历（在岗职工）
     */
    @TableField("above_bachelor_num")
    private Integer aboveBachelorNum;

    /**
     * 研究生以上学历（在岗职工）
     */
    @TableField("above_graduate_num")
    private Integer aboveGraduateNum;

    /**
     * 党员数
     */
    @TableField("party_num")
    private Integer partyNum;

    /**
     * 本年度发展的
     */
    @TableField("current_year_develop_num")
    private Integer currentYearDevelopNum;

    /**
     * 未转组织关系党员人数
     */
    @TableField("not_turned_relation_num")
    private Integer notTurnedRelationNum;

    /**
     * 党组织书记
     */
    @TableField("secretary_num")
    private Integer secretaryNum;

    /**
     * 由企业中高层管理人员担任的
     */
    @TableField("secretary_hold_middle_manager_num")
    private Integer secretaryHoldMiddleManagerNum;

    /**
     * 企业主要负责人是党员的（0否，1是）
     */
    @TableField("is_principal_party")
    private Integer isPrincipalParty;

    /**
     * 创建时间
     */
    @TableField("create_time")
    private Date createTime;

    /**
     * 更新时间
     */
    @TableField("update_time")
    private Date updateTime;

    /**
     * 删除时间
     */
    @TableField("delete_time")
    private Date deleteTime;


    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getUnitCode() {
        return unitCode;
    }

    public void setUnitCode(String unitCode) {
        this.unitCode = unitCode;
    }

    public Integer getBuildUniteBranchNum() {
        return buildUniteBranchNum;
    }

    public void setBuildUniteBranchNum(Integer buildUniteBranchNum) {
        this.buildUniteBranchNum = buildUniteBranchNum;
    }

    public Integer getAboveBachelorNum() {
        return aboveBachelorNum;
    }

    public void setAboveBachelorNum(Integer aboveBachelorNum) {
        this.aboveBachelorNum = aboveBachelorNum;
    }

    public Integer getAboveGraduateNum() {
        return aboveGraduateNum;
    }

    public void setAboveGraduateNum(Integer aboveGraduateNum) {
        this.aboveGraduateNum = aboveGraduateNum;
    }

    public Integer getPartyNum() {
        return partyNum;
    }

    public void setPartyNum(Integer partyNum) {
        this.partyNum = partyNum;
    }

    public Integer getCurrentYearDevelopNum() {
        return currentYearDevelopNum;
    }

    public void setCurrentYearDevelopNum(Integer currentYearDevelopNum) {
        this.currentYearDevelopNum = currentYearDevelopNum;
    }

    public Integer getNotTurnedRelationNum() {
        return notTurnedRelationNum;
    }

    public void setNotTurnedRelationNum(Integer notTurnedRelationNum) {
        this.notTurnedRelationNum = notTurnedRelationNum;
    }

    public Integer getSecretaryNum() {
        return secretaryNum;
    }

    public void setSecretaryNum(Integer secretaryNum) {
        this.secretaryNum = secretaryNum;
    }

    public Integer getSecretaryHoldMiddleManagerNum() {
        return secretaryHoldMiddleManagerNum;
    }

    public void setSecretaryHoldMiddleManagerNum(Integer secretaryHoldMiddleManagerNum) {
        this.secretaryHoldMiddleManagerNum = secretaryHoldMiddleManagerNum;
    }

    public Integer getIsPrincipalParty() {
        return isPrincipalParty;
    }

    public void setIsPrincipalParty(Integer isPrincipalParty) {
        this.isPrincipalParty = isPrincipalParty;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    public Date getDeleteTime() {
        return deleteTime;
    }

    public void setDeleteTime(Date deleteTime) {
        this.deleteTime = deleteTime;
    }

    @Override
    protected Serializable pkVal() {
        return this.code;
    }

    @Override
    public String toString() {
        return "Zt12InternetEnterpris{" +
                "code=" + code +
                ", unitCode=" + unitCode +
                ", buildUniteBranchNum=" + buildUniteBranchNum +
                ", aboveBachelorNum=" + aboveBachelorNum +
                ", aboveGraduateNum=" + aboveGraduateNum +
                ", partyNum=" + partyNum +
                ", currentYearDevelopNum=" + currentYearDevelopNum +
                ", notTurnedRelationNum=" + notTurnedRelationNum +
                ", secretaryNum=" + secretaryNum +
                ", secretaryHoldMiddleManagerNum=" + secretaryHoldMiddleManagerNum +
                ", isPrincipalParty=" + isPrincipalParty +
                ", createTime=" + createTime +
                ", updateTime=" + updateTime +
                ", deleteTime=" + deleteTime +
                "}";
    }
}
