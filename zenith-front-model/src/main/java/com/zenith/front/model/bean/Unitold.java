package com.zenith.front.model.bean;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import java.util.Date;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableField;
import java.io.Serializable;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2021-07-11
 */
@TableName("ccp_unitold")
public class Unitold extends Model<Unitold> {

    private static final long serialVersionUID=1L;

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    @TableField("code")
    private String code;

    @TableField("es_id")
    private String esId;

    @TableField("credit_code")
    private String creditCode;

    @TableField("create_unit_org_code")
    private String createUnitOrgCode;

    @TableField("create_org_zb_code")
    private String createOrgZbCode;

    @TableField("create_org_code")
    private String createOrgCode;

    @TableField("is_create_org")
    private Integer isCreateOrg;

    @TableField("name")
    private String name;

    @TableField("pinyin")
    private String pinyin;

    @TableField("d48_code")
    private String d48Code;

    @TableField("d48_name")
    private String d48Name;

    @TableField("d04_code")
    private String d04Code;

    @TableField("d04_name")
    private String d04Name;

    @TableField("has_major_worker")
    private Integer hasMajorWorker;

    @TableField("has_major_deputy_secretary")
    private Integer hasMajorDeputySecretary;

    @TableField("manage_org_code")
    private String manageOrgCode;

    @TableField("manage_org_name")
    private String manageOrgName;

    @TableField("d35_code")
    private String d35Code;

    @TableField("d35_name")
    private String d35Name;

    @TableField("legal_is_member")
    private Integer legalIsMember;

    @TableField("legal_is_secretary")
    private Integer legalIsSecretary;

    @TableField("d05_code")
    private String d05Code;

    @TableField("d05_name")
    private String d05Name;

    @TableField("is_branch")
    private Integer isBranch;

    @TableField("address")
    private String address;

    @TableField("telephone")
    private String telephone;

    @TableField("is_legal")
    private Integer isLegal;

    @TableField("create_time")
    private Date createTime;

    @TableField("update_time")
    private Date updateTime;

    @TableField("delete_time")
    private Date deleteTime;

    @TableField("ratio")
    private Double ratio;

    @TableField("keywords")
    private String keywords;

    @TableField("unit_code")
    private String unitCode;

    @TableField("orgs")
    private String orgs;

    @TableField("timestamp")
    private Date timestamp;

    @TableField("main_org_code")
    private String mainOrgCode;

    @TableField("main_org_name")
    private String mainOrgName;

    @TableField("main_org_type")
    private String mainOrgType;

    @TableField("main_org_type_code")
    private String mainOrgTypeCode;

    @TableField("main_unit_org_code")
    private String mainUnitOrgCode;

    /**
     * 是否是历史数据
     */
    @TableField("is_history")
    private String isHistory;

    /**
     * 最近更新账号
     */
    @TableField("update_account")
    private String updateAccount;

    /**
     * 党建指导组织_层级码
     */
    @TableField("manage_unit_org_code")
    private String manageUnitOrgCode;


    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getEsId() {
        return esId;
    }

    public void setEsId(String esId) {
        this.esId = esId;
    }

    public String getCreditCode() {
        return creditCode;
    }

    public void setCreditCode(String creditCode) {
        this.creditCode = creditCode;
    }

    public String getCreateUnitOrgCode() {
        return createUnitOrgCode;
    }

    public void setCreateUnitOrgCode(String createUnitOrgCode) {
        this.createUnitOrgCode = createUnitOrgCode;
    }

    public String getCreateOrgZbCode() {
        return createOrgZbCode;
    }

    public void setCreateOrgZbCode(String createOrgZbCode) {
        this.createOrgZbCode = createOrgZbCode;
    }

    public String getCreateOrgCode() {
        return createOrgCode;
    }

    public void setCreateOrgCode(String createOrgCode) {
        this.createOrgCode = createOrgCode;
    }

    public Integer getIsCreateOrg() {
        return isCreateOrg;
    }

    public void setIsCreateOrg(Integer isCreateOrg) {
        this.isCreateOrg = isCreateOrg;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getPinyin() {
        return pinyin;
    }

    public void setPinyin(String pinyin) {
        this.pinyin = pinyin;
    }

    public String getD48Code() {
        return d48Code;
    }

    public void setD48Code(String d48Code) {
        this.d48Code = d48Code;
    }

    public String getD48Name() {
        return d48Name;
    }

    public void setD48Name(String d48Name) {
        this.d48Name = d48Name;
    }

    public String getD04Code() {
        return d04Code;
    }

    public void setD04Code(String d04Code) {
        this.d04Code = d04Code;
    }

    public String getD04Name() {
        return d04Name;
    }

    public void setD04Name(String d04Name) {
        this.d04Name = d04Name;
    }

    public Integer getHasMajorWorker() {
        return hasMajorWorker;
    }

    public void setHasMajorWorker(Integer hasMajorWorker) {
        this.hasMajorWorker = hasMajorWorker;
    }

    public Integer getHasMajorDeputySecretary() {
        return hasMajorDeputySecretary;
    }

    public void setHasMajorDeputySecretary(Integer hasMajorDeputySecretary) {
        this.hasMajorDeputySecretary = hasMajorDeputySecretary;
    }

    public String getManageOrgCode() {
        return manageOrgCode;
    }

    public void setManageOrgCode(String manageOrgCode) {
        this.manageOrgCode = manageOrgCode;
    }

    public String getManageOrgName() {
        return manageOrgName;
    }

    public void setManageOrgName(String manageOrgName) {
        this.manageOrgName = manageOrgName;
    }

    public String getD35Code() {
        return d35Code;
    }

    public void setD35Code(String d35Code) {
        this.d35Code = d35Code;
    }

    public String getD35Name() {
        return d35Name;
    }

    public void setD35Name(String d35Name) {
        this.d35Name = d35Name;
    }

    public Integer getLegalIsMember() {
        return legalIsMember;
    }

    public void setLegalIsMember(Integer legalIsMember) {
        this.legalIsMember = legalIsMember;
    }

    public Integer getLegalIsSecretary() {
        return legalIsSecretary;
    }

    public void setLegalIsSecretary(Integer legalIsSecretary) {
        this.legalIsSecretary = legalIsSecretary;
    }

    public String getD05Code() {
        return d05Code;
    }

    public void setD05Code(String d05Code) {
        this.d05Code = d05Code;
    }

    public String getD05Name() {
        return d05Name;
    }

    public void setD05Name(String d05Name) {
        this.d05Name = d05Name;
    }

    public Integer getIsBranch() {
        return isBranch;
    }

    public void setIsBranch(Integer isBranch) {
        this.isBranch = isBranch;
    }

    public String getAddress() {
        return address;
    }

    public void setAddress(String address) {
        this.address = address;
    }

    public String getTelephone() {
        return telephone;
    }

    public void setTelephone(String telephone) {
        this.telephone = telephone;
    }

    public Integer getIsLegal() {
        return isLegal;
    }

    public void setIsLegal(Integer isLegal) {
        this.isLegal = isLegal;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    public Date getDeleteTime() {
        return deleteTime;
    }

    public void setDeleteTime(Date deleteTime) {
        this.deleteTime = deleteTime;
    }

    public Double getRatio() {
        return ratio;
    }

    public void setRatio(Double ratio) {
        this.ratio = ratio;
    }

    public String getKeywords() {
        return keywords;
    }

    public void setKeywords(String keywords) {
        this.keywords = keywords;
    }

    public String getUnitCode() {
        return unitCode;
    }

    public void setUnitCode(String unitCode) {
        this.unitCode = unitCode;
    }

    public String getOrgs() {
        return orgs;
    }

    public void setOrgs(String orgs) {
        this.orgs = orgs;
    }

    public Date getTimestamp() {
        return timestamp;
    }

    public void setTimestamp(Date timestamp) {
        this.timestamp = timestamp;
    }

    public String getMainOrgCode() {
        return mainOrgCode;
    }

    public void setMainOrgCode(String mainOrgCode) {
        this.mainOrgCode = mainOrgCode;
    }

    public String getMainOrgName() {
        return mainOrgName;
    }

    public void setMainOrgName(String mainOrgName) {
        this.mainOrgName = mainOrgName;
    }

    public String getMainOrgType() {
        return mainOrgType;
    }

    public void setMainOrgType(String mainOrgType) {
        this.mainOrgType = mainOrgType;
    }

    public String getMainOrgTypeCode() {
        return mainOrgTypeCode;
    }

    public void setMainOrgTypeCode(String mainOrgTypeCode) {
        this.mainOrgTypeCode = mainOrgTypeCode;
    }

    public String getMainUnitOrgCode() {
        return mainUnitOrgCode;
    }

    public void setMainUnitOrgCode(String mainUnitOrgCode) {
        this.mainUnitOrgCode = mainUnitOrgCode;
    }

    public String getIsHistory() {
        return isHistory;
    }

    public void setIsHistory(String isHistory) {
        this.isHistory = isHistory;
    }

    public String getUpdateAccount() {
        return updateAccount;
    }

    public void setUpdateAccount(String updateAccount) {
        this.updateAccount = updateAccount;
    }

    public String getManageUnitOrgCode() {
        return manageUnitOrgCode;
    }

    public void setManageUnitOrgCode(String manageUnitOrgCode) {
        this.manageUnitOrgCode = manageUnitOrgCode;
    }

    @Override
    public Serializable pkVal() {
        return this.id;
    }

    @Override
    public String toString() {
        return "Unitold{" +
        "id=" + id +
        ", code=" + code +
        ", esId=" + esId +
        ", creditCode=" + creditCode +
        ", createUnitOrgCode=" + createUnitOrgCode +
        ", createOrgZbCode=" + createOrgZbCode +
        ", createOrgCode=" + createOrgCode +
        ", isCreateOrg=" + isCreateOrg +
        ", name=" + name +
        ", pinyin=" + pinyin +
        ", d48Code=" + d48Code +
        ", d48Name=" + d48Name +
        ", d04Code=" + d04Code +
        ", d04Name=" + d04Name +
        ", hasMajorWorker=" + hasMajorWorker +
        ", hasMajorDeputySecretary=" + hasMajorDeputySecretary +
        ", manageOrgCode=" + manageOrgCode +
        ", manageOrgName=" + manageOrgName +
        ", d35Code=" + d35Code +
        ", d35Name=" + d35Name +
        ", legalIsMember=" + legalIsMember +
        ", legalIsSecretary=" + legalIsSecretary +
        ", d05Code=" + d05Code +
        ", d05Name=" + d05Name +
        ", isBranch=" + isBranch +
        ", address=" + address +
        ", telephone=" + telephone +
        ", isLegal=" + isLegal +
        ", createTime=" + createTime +
        ", updateTime=" + updateTime +
        ", deleteTime=" + deleteTime +
        ", ratio=" + ratio +
        ", keywords=" + keywords +
        ", unitCode=" + unitCode +
        ", orgs=" + orgs +
        ", timestamp=" + timestamp +
        ", mainOrgCode=" + mainOrgCode +
        ", mainOrgName=" + mainOrgName +
        ", mainOrgType=" + mainOrgType +
        ", mainOrgTypeCode=" + mainOrgTypeCode +
        ", mainUnitOrgCode=" + mainUnitOrgCode +
        ", isHistory=" + isHistory +
        ", updateAccount=" + updateAccount +
        ", manageUnitOrgCode=" + manageUnitOrgCode +
        "}";
    }
}
