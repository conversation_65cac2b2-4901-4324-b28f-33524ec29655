package com.zenith.front.model.bean;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableField;

import java.io.Serializable;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2021-09-15
 */
@TableName("dict_d01")
public class DictD01 extends Model<DictD01> {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    @TableField("key")
    private String key;

    @TableField("name")
    private String name;

    @TableField("pinyin")
    private String pinyin;

    @TableField("parent")
    private String parent;

    @TableField("type")
    private String type;

    @TableField("is_leaf")
    private Integer isLeaf;

    @TableField("sort")
    private Long sort;

    @TableField("map_new_key")
    private String mapNewKey;

    @TableField("map_type")
    private String mapType;

    @TableField("map_new_key_name")
    private String mapNewKeyName;

    @TableField("old_key")
    private String oldKey;

    @TableField("old_name")
    private String oldName;

    @TableField("remark")
    private String remark;

    @TableField("disabled")
    private Integer disabled;

    @TableField("enabled")
    private Integer enabled;


    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getKey() {
        return key;
    }

    public void setKey(String key) {
        this.key = key;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getPinyin() {
        return pinyin;
    }

    public void setPinyin(String pinyin) {
        this.pinyin = pinyin;
    }

    public String getParent() {
        return parent;
    }

    public void setParent(String parent) {
        this.parent = parent;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public Integer getIsLeaf() {
        return isLeaf;
    }

    public void setIsLeaf(Integer isLeaf) {
        this.isLeaf = isLeaf;
    }

    public Long getSort() {
        return sort;
    }

    public void setSort(Long sort) {
        this.sort = sort;
    }

    public String getMapNewKey() {
        return mapNewKey;
    }

    public void setMapNewKey(String mapNewKey) {
        this.mapNewKey = mapNewKey;
    }

    public String getMapType() {
        return mapType;
    }

    public void setMapType(String mapType) {
        this.mapType = mapType;
    }

    public String getMapNewKeyName() {
        return mapNewKeyName;
    }

    public void setMapNewKeyName(String mapNewKeyName) {
        this.mapNewKeyName = mapNewKeyName;
    }

    public String getOldKey() {
        return oldKey;
    }

    public void setOldKey(String oldKey) {
        this.oldKey = oldKey;
    }

    public String getOldName() {
        return oldName;
    }

    public void setOldName(String oldName) {
        this.oldName = oldName;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public Integer getDisabled() {
        return disabled;
    }

    public void setDisabled(Integer disabled) {
        this.disabled = disabled;
    }

    public Integer getEnabled() {
        return enabled;
    }

    public void setEnabled(Integer enabled) {
        this.enabled = enabled;
    }

    @Override
    protected Serializable pkVal() {
        return this.id;
    }

    @Override
    public String toString() {
        return "DictD01{" +
                "id=" + id +
                ", key=" + key +
                ", name=" + name +
                ", pinyin=" + pinyin +
                ", parent=" + parent +
                ", type=" + type +
                ", isLeaf=" + isLeaf +
                ", sort=" + sort +
                ", mapNewKey=" + mapNewKey +
                ", mapType=" + mapType +
                ", mapNewKeyName=" + mapNewKeyName +
                ", oldKey=" + oldKey +
                ", oldName=" + oldName +
                ", remark=" + remark +
                ", disabled=" + disabled +
                ", enabled=" + enabled +
                "}";
    }
}
