package com.zenith.front.model.dto;

import com.zenith.front.model.validate.group.AddGroup;
import com.zenith.front.model.validate.group.UpdateGroup;
import lombok.Data;
import org.hibernate.validator.constraints.Range;

import javax.validation.constraints.NotNull;
import java.util.Date;

/**
 * <AUTHOR>
 * @since 2022/04/11
 */
@Data
public class UnitResidentDTO extends BaseDTO {

    private static final long serialVersionUID = -2068744608717436124L;

    /**
     * 届次code
     */
//    @NotNull(groups = {AddGroup.class, UpdateGroup.class}, message = "届次不允许为空!")
//    private String electCode;

    /**
     * 党员唯一主键
     */
    private String memCode;

    /**
     * 党员姓名
     */
    private String memName;

    /**
     * 1.本单位内党员；2.非本单位党员；3.非党员
     */
    @NotNull(message = "1.本单位内党员；2.非本单位党员；3.非党员")
    @Range(min = 1, max = 3, message = "1.本单位内党员；2.非本单位党员；3.非党员")
    private String d139Code;

    private String d139Name;

    /**
     * 1.驻村第一书记、2.驻村干部
     */
    @NotNull(message = "1.驻村第一书记、2.驻村干部")
    @Range(min = 1, max = 2, message = "1.驻村第一书记、2.驻村干部")
    private String d140Code;

    private String d140Name;

    /**
     * 1.中直、2.省直、3.市直、4.县直、5.乡镇
     */
    @NotNull(message = "1.中直、2.省直、3.市直、4.县直、5.乡镇")
    @Range(min = 1, max = 5, message = "1.中直、2.省直、3.市直、4.县直、5.乡镇")
    private String d141Code;

    private String d141Name;

    /**
     * 性别
     */
    private String sexCode;

    private String sexName;

    /**
     * 党员身份证
     */
    private String memIdcard;

    /**
     * 党员生日
     */
    private Date memBirthday;

    /**
     * 党员学历
     */
    private String d07Code;


    private String d07Name;

    /**
     * 任职开始时间
     */
    @NotNull(groups = {AddGroup.class, UpdateGroup.class}, message = "任职开始时间不允许为空!")
    private Date startDate;
    /**
     * 任职结束时间
     */
    private Date endDate;

    /**
     * 是否村任职选调生（是否选择框）
     */
    private Integer hasVillageTransferStudent;
    /**
     * 选调单位层级
     */
    private String d144Code;
    private String d144Name;
    /**
     * 单位code
     */
    @NotNull(groups = {AddGroup.class, UpdateGroup.class}, message = "unitCode不允许为空!")
    private String unitCode;
    /**
     * 驻村时间
     */
    private Date residentDate;
    /**
     * 派出单位名称及职务
     */
    private String dispatchPosition;

    /**
     * 选派层级code
     */
    private String d197Code;

    /**
     * 选派层级name
     */
    private String d197Name;


    @Override
    public String toString() {
        return "UnitResidentDTO{" +
                "unitCode='" + unitCode + '\'' +
                ", memCode='" + memCode + '\'' +
                ", memName='" + memName + '\'' +
                ", d139Code='" + d139Code + '\'' +
                ", d139Name='" + d139Name + '\'' +
                ", d140Code='" + d140Code + '\'' +
                ", d140Name='" + d140Name + '\'' +
                ", d141Code='" + d141Code + '\'' +
                ", d141Name='" + d141Name + '\'' +
                ", sexCode='" + sexCode + '\'' +
                ", sexName='" + sexName + '\'' +
                ", memIdcard='" + memIdcard + '\'' +
                ", memBirthday=" + memBirthday +
                ", d07Code='" + d07Code + '\'' +
                ", d07Name='" + d07Name + '\'' +
                ", startDate=" + startDate +
                ", endDate=" + endDate +
                ", hasVillageTransferStudent='" + hasVillageTransferStudent + '\'' +
                '}';
    }
}