package com.zenith.front.model.vo;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

/**
 * <AUTHOR>
 * @version 1.0
 * @Description: 概况统计图表
 * @date 2019/4/28 10:36
 */
@Data
@ToString
@NoArgsConstructor
@AllArgsConstructor
public class OrgChartVO {
    /**
     * 党组织总数
     */
    private Long orgTotal;
    /**
     * 七天内即将届满
     */
    private Long orgElectTotal;
    /**
     * 正式党员比例
     */
    private String fullMemPercent;
    /**
     * 预备党员
     */
    private Long readyMemTotal;
    /**
     * 预备党员比例
     */
    private String readyMemPercent;
    /**
     * 少数民族
     */
    private Long minorityTotal;
    /**
     * 大专及以上的党员数
     */
    private Long dzTotal;
    /**
     * 女性总数
     */
    private Long womanTotal;
    /**
     * 女性占比
     */
    private String womanPercent;
    /**
     * 男性总数
     */
    private Long manTotal;
    /**
     * 男性占比
     */
    private String manPercent;

}
