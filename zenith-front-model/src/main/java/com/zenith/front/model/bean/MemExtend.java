package com.zenith.front.model.bean;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableField;

import java.io.Serializable;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2021-07-11
 */
@TableName("ccp_mem_extend")
public class MemExtend extends Model<MemExtend> {

    private static final long serialVersionUID=1L;

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    @TableField("code")
    private String code;

    @TableField("mem_code")
    private String memCode;

    @TableField("extend_org_code")
    private String extendOrgCode;

    @TableField("org_zb_code")
    private String orgZbCode;

    @TableField("org_code")
    private String orgCode;

    @TableField("village_zbwy")
    private Integer villageZbwy;

    @TableField("village_zbsj")
    private Integer villageZbsj;

    @TableField("village_cwwy")
    private Integer villageCwwy;

    @TableField("village_cwzr")
    private Integer villageCwzr;

    @TableField("community_zbwy")
    private Integer communityZbwy;

    @TableField("community_zbsj")
    private Integer communityZbsj;

    @TableField("community_cwwy")
    private Integer communityCwwy;

    @TableField("community_cwzr")
    private Integer communityCwzr;


    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getMemCode() {
        return memCode;
    }

    public void setMemCode(String memCode) {
        this.memCode = memCode;
    }

    public String getExtendOrgCode() {
        return extendOrgCode;
    }

    public void setExtendOrgCode(String extendOrgCode) {
        this.extendOrgCode = extendOrgCode;
    }

    public String getOrgZbCode() {
        return orgZbCode;
    }

    public void setOrgZbCode(String orgZbCode) {
        this.orgZbCode = orgZbCode;
    }

    public String getOrgCode() {
        return orgCode;
    }

    public void setOrgCode(String orgCode) {
        this.orgCode = orgCode;
    }

    public Integer getVillageZbwy() {
        return villageZbwy;
    }

    public void setVillageZbwy(Integer villageZbwy) {
        this.villageZbwy = villageZbwy;
    }

    public Integer getVillageZbsj() {
        return villageZbsj;
    }

    public void setVillageZbsj(Integer villageZbsj) {
        this.villageZbsj = villageZbsj;
    }

    public Integer getVillageCwwy() {
        return villageCwwy;
    }

    public void setVillageCwwy(Integer villageCwwy) {
        this.villageCwwy = villageCwwy;
    }

    public Integer getVillageCwzr() {
        return villageCwzr;
    }

    public void setVillageCwzr(Integer villageCwzr) {
        this.villageCwzr = villageCwzr;
    }

    public Integer getCommunityZbwy() {
        return communityZbwy;
    }

    public void setCommunityZbwy(Integer communityZbwy) {
        this.communityZbwy = communityZbwy;
    }

    public Integer getCommunityZbsj() {
        return communityZbsj;
    }

    public void setCommunityZbsj(Integer communityZbsj) {
        this.communityZbsj = communityZbsj;
    }

    public Integer getCommunityCwwy() {
        return communityCwwy;
    }

    public void setCommunityCwwy(Integer communityCwwy) {
        this.communityCwwy = communityCwwy;
    }

    public Integer getCommunityCwzr() {
        return communityCwzr;
    }

    public void setCommunityCwzr(Integer communityCwzr) {
        this.communityCwzr = communityCwzr;
    }

    @Override
    public Serializable pkVal() {
        return this.id;
    }

    @Override
    public String toString() {
        return "MemExtend{" +
        "id=" + id +
        ", code=" + code +
        ", memCode=" + memCode +
        ", extendOrgCode=" + extendOrgCode +
        ", orgZbCode=" + orgZbCode +
        ", orgCode=" + orgCode +
        ", villageZbwy=" + villageZbwy +
        ", villageZbsj=" + villageZbsj +
        ", villageCwwy=" + villageCwwy +
        ", villageCwzr=" + villageCwzr +
        ", communityZbwy=" + communityZbwy +
        ", communityZbsj=" + communityZbsj +
        ", communityCwwy=" + communityCwwy +
        ", communityCwzr=" + communityCwzr +
        "}";
    }
}
