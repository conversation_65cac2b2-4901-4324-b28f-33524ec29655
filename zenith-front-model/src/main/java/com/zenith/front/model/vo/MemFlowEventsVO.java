package com.zenith.front.model.vo;

import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 * @since 2025-02-11 10:19
 */
@Data
public class MemFlowEventsVO {

    /**
     * 主键
     */
    private String id;

    /**
     * 流动党员登记code
     */
    private String memFlowCode;

    /**
     * 流动党员code
     */
    private String memCode;

    /**
     * 活动类型（0组织活动 1志愿服务）
     */
    private String eventType;

    /**
     * 活动名称或者志愿服务名称
     */
    private String eventName;

    /**
     * 活动情况说明
     */
    private String eventExplain;

    /**
     * 活动类型详情
     */
    private String eventDetails;

    /**
     * 活动日期
     */
    private Date eventDate;

    /**
     * 维护日期
     */
    private Date maintainDate;

    /**
     * 填写人ID
     */
    private String fillerUserId;

    /**
     * 填写人名称
     */
    private String fillerUserName;

    /**
     * 填写党组织ID
     */
    private String fillerOrgId;

    /**
     * 填写党组织code
     */
    private String fillerOrgCode;

    /**
     * 填写党组织名称
     */
    private String fillerOrgName;

    /**
     * 填写党组织名称
     */
    private Date fillerDate;

    /**
     * 流出走向 （消息走向：1流出方发送 2流入方发送）
     */
    private String flowDirection;

    /**
     * 流出方根节点代码
     */
    private String outflowNodeCode;

    /**
     * 流入方根节点代码
     */
    private String inflowNodeCode;

    /**
     * 1-本地创建  2-交换区数据
     */
    private Integer sourceType;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 删除时间
     */
    private Date deleteTime;
}
