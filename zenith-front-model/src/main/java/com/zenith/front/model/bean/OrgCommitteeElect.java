package com.zenith.front.model.bean;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2021-07-11
 */
@TableName("ccp_org_committee_elect")
public class OrgCommitteeElect extends Model<OrgCommitteeElect> {

    private static final long serialVersionUID = 1L;

    /**
     * 唯一自增长主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 唯一标识符code
     */
    @TableField("code")
    private String code;

    /**
     * es同步唯一主键
     */
    @TableField("es_id")
    private String esId;

    /**
     * 届次组织层级码
     */
    @TableField("elect_org_code")
    private String electOrgCode;

    /**
     * 组织唯一标识符code
     */
    @TableField("org_code")
    private String orgCode;

    /**
     * 届次开始时间
     */
    @TableField("tenure_start_date")
    private Date tenureStartDate;

    /**
     * 届次结束时间
     */
    @TableField("tenure_end_date")
    private Date tenureEndDate;

    /**
     * 选举方式code
     */
    @TableField("elect_code")
    private Integer electCode;

    /**
     * 选举方式名称
     */
    @TableField("elect_name")
    private String electName;

    /**
     * 是否在任
     */
    @TableField("is_live")
    private Integer isLive;

    /**
     * 特殊情况说明
     */
    @TableField("special_explain")
    private String specialExplain;

    /**
     * 选举应到人数
     */
    @TableField("shoud_number")
    private Long shoudNumber;

    /**
     * 选举实到人数
     */
    @TableField("real_number")
    private Long realNumber;

    /**
     * 批复职数
     */
    @TableField("post_number")
    private Long postNumber;

    /**
     * 实际选出职数
     */
    @TableField("real_post_number")
    private Long realPostNumber;

    /**
     * 是否首任届次
     */
    @TableField("is_frist_code")
    private Integer isFristCode;

    /**
     * 换届材料地址
     */
    @TableField("material")
    private String material;

    /**
     * 时间戳
     */
    @TableField("timestamp")
    private Date timestamp;

    /**
     * 删除时间
     */
    @TableField("delete_time")
    private Date deleteTime;

    /**
     * 创建时间
     */
    @TableField("create_time")
    private Date createTime;

    /**
     * 更新时间
     */
    @TableField("update_time")
    private Date updateTime;

    /**
     * 届次组织zb_code
     */
    @TableField("zb_code")
    private String zbCode;

    /**
     * 是否历史数据
     */
    @TableField("is_history")
    private Integer isHistory;

    /**
     * 最近更更新账号
     */
    @TableField("update_account")
    private String updateAccount;


    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getEsId() {
        return esId;
    }

    public void setEsId(String esId) {
        this.esId = esId;
    }

    public String getElectOrgCode() {
        return electOrgCode;
    }

    public void setElectOrgCode(String electOrgCode) {
        this.electOrgCode = electOrgCode;
    }

    public String getOrgCode() {
        return orgCode;
    }

    public void setOrgCode(String orgCode) {
        this.orgCode = orgCode;
    }

    public Date getTenureStartDate() {
        return tenureStartDate;
    }

    public void setTenureStartDate(Date tenureStartDate) {
        this.tenureStartDate = tenureStartDate;
    }

    public Date getTenureEndDate() {
        return tenureEndDate;
    }

    public void setTenureEndDate(Date tenureEndDate) {
        this.tenureEndDate = tenureEndDate;
    }

    public Integer getElectCode() {
        return electCode;
    }

    public void setElectCode(Integer electCode) {
        this.electCode = electCode;
    }

    public String getElectName() {
        return electName;
    }

    public void setElectName(String electName) {
        this.electName = electName;
    }

    public Integer getIsLive() {
        return isLive;
    }

    public void setIsLive(Integer isLive) {
        this.isLive = isLive;
    }

    public String getSpecialExplain() {
        return specialExplain;
    }

    public void setSpecialExplain(String specialExplain) {
        this.specialExplain = specialExplain;
    }

    public Long getShoudNumber() {
        return shoudNumber;
    }

    public void setShoudNumber(Long shoudNumber) {
        this.shoudNumber = shoudNumber;
    }

    public Long getRealNumber() {
        return realNumber;
    }

    public void setRealNumber(Long realNumber) {
        this.realNumber = realNumber;
    }

    public Long getPostNumber() {
        return postNumber;
    }

    public void setPostNumber(Long postNumber) {
        this.postNumber = postNumber;
    }

    public Long getRealPostNumber() {
        return realPostNumber;
    }

    public void setRealPostNumber(Long realPostNumber) {
        this.realPostNumber = realPostNumber;
    }

    public Integer getIsFristCode() {
        return isFristCode;
    }

    public void setIsFristCode(Integer isFristCode) {
        this.isFristCode = isFristCode;
    }

    public String getMaterial() {
        return material;
    }

    public void setMaterial(String material) {
        this.material = material;
    }

    public Date getTimestamp() {
        return timestamp;
    }

    public void setTimestamp(Date timestamp) {
        this.timestamp = timestamp;
    }

    public Date getDeleteTime() {
        return deleteTime;
    }

    public void setDeleteTime(Date deleteTime) {
        this.deleteTime = deleteTime;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    public String getZbCode() {
        return zbCode;
    }

    public void setZbCode(String zbCode) {
        this.zbCode = zbCode;
    }

    public Integer getIsHistory() {
        return isHistory;
    }

    public void setIsHistory(Integer isHistory) {
        this.isHistory = isHistory;
    }

    public String getUpdateAccount() {
        return updateAccount;
    }

    public void setUpdateAccount(String updateAccount) {
        this.updateAccount = updateAccount;
    }

    @Override
    public Serializable pkVal() {
        return this.id;
    }

    @Override
    public String toString() {
        return "OrgCommitteeElect{" +
                "id=" + id +
                ", code=" + code +
                ", esId=" + esId +
                ", electOrgCode=" + electOrgCode +
                ", orgCode=" + orgCode +
                ", tenureStartDate=" + tenureStartDate +
                ", tenureEndDate=" + tenureEndDate +
                ", electCode=" + electCode +
                ", electName=" + electName +
                ", isLive=" + isLive +
                ", specialExplain=" + specialExplain +
                ", shoudNumber=" + shoudNumber +
                ", realNumber=" + realNumber +
                ", postNumber=" + postNumber +
                ", realPostNumber=" + realPostNumber +
                ", isFristCode=" + isFristCode +
                ", material=" + material +
                ", timestamp=" + timestamp +
                ", deleteTime=" + deleteTime +
                ", createTime=" + createTime +
                ", updateTime=" + updateTime +
                ", zbCode=" + zbCode +
                ", isHistory=" + isHistory +
                ", updateAccount=" + updateAccount +
                "}";
    }
}
