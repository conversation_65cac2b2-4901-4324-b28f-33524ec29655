package com.zenith.front.model.dto;

import com.zenith.front.model.validate.group.AddGroup;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.util.Date;

/**
 * 乡镇干部队伍补充信息
 *
 * <AUTHOR>
 * @date 2022/4/11
 */
@Data
public class UnitTownshipCadreInfoDto {
    private String code;

    /**
     * 单位code
     */
    @NotBlank(groups = AddGroup.class, message = "unitCode 不能为空")
    private String unitCode;

    /**
     * 组织code
     */
    private String orgCode;

    /**
     * 组织层级码
     */
    private String orgLevelCode;

    /**
     * 行政编制数（数字、必填）
     */
    private Integer xzbzNum;

    /**
     * 事业编制数（数字、必填）
     */
    private Integer sybzNum;

    /**
     * 工勤编制数（数字、必填）
     */
    private Integer gqbzNum;

    /**
     * 空缺行政编制数（数字、必填、不能大于行政编制数字）
     */
    private Integer kqxzbzNum;

    /**
     * 空缺事业编制数（数字、必填、不能大于行事业编制数字）
     */
    private Integer kqsybzNum;

    /**
     * 被借调工作人员总数（数字、必填）
     */
    private Integer secondedStaffNum;

    /**
     * 省级以上借调人数（数字、必填、不能大于被借调工作人员总数）
     */
    private Integer aboveProvincialSecondedStaffNum;

    /**
     * 省级借调人数（数字、必填、不能大于被借调工作人员总数）
     */
    private Integer provincialSecondedStaffNum;

    /**
     * 市级借调人数（数字、必填、不能大于被借调工作人员总数）
     */
    private Integer citySecondedStaffNum;

    /**
     * 县级借调人数（数字、必填、不能大于被借调工作人员总数）
     */
    private Integer countySecondedStaffNum;
    /**
     * 统计时间
     */
    @NotBlank(groups = AddGroup.class, message = "countDate 不能为空")
    private Date countDate;
}
