package com.zenith.front.model.bean;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import java.util.Date;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableField;
import com.zenith.front.model.dto.ActivityMemDTO;

import java.io.Serializable;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2021-07-11
 */
@TableName("ccp_activity_mem")
public class ActivityMem extends Model<ActivityMem> {

    private static final long serialVersionUID=1L;

    /**
     * 唯一自增长主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 活动code
     */
    @TableField("activity_code")
    private String activityCode;

    /**
     * 是否自定义人员[1是0否]
     */
    @TableField("is_customize")
    private String isCustomize;

    /**
     * 人员code[如果非本系统的人员需要与头像上传文件一致做为人员code]
     */
    @TableField("mem_code")
    private String memCode;

    /**
     * 系统内人员参加活动时所在的组织唯一标识符
     */
    @TableField("mem_org_code")
    private String memOrgCode;

    /**
     * 系统内人员参加活动时所在的组织层级码
     */
    @TableField("mem_org_org_code")
    private String memOrgOrgCode;

    /**
     * 人员头像
     */
    @TableField("avator")
    private String avator;

    /**
     * 人员名称
     */
    @TableField("name")
    private String name;

    /**
     * 人员身份证
     */
    @TableField("idcard")
    private String idcard;

    /**
     * 人员电话
     */
    @TableField("phone")
    private String phone;

    /**
     * 缺席原因
     */
    @TableField("reason")
    private String reason;

    /**
     * 创建时间
     */
    @TableField("create_time")
    private Date createTime;

    /**
     * 更新时间
     */
    @TableField("update_time")
    private Date updateTime;

    /**
     * 删除时间
     */
    @TableField("delete_time")
    private Date deleteTime;

    /**
     * 备注
     */
    @TableField("remark")
    private String remark;

    /**
     * 是否主持人员{0否1是}
     */
    @TableField("is_hostor")
    private Integer isHostor;

    /**
     * 是否讲课人员{0否1是}
     */
    @TableField("is_lecturer")
    private Integer isLecturer;

    /**
     * 是否领导人员{0否1是}
     */
    @TableField("is_leader")
    private Integer isLeader;

    /**
     * 是否应到人员{0否1是}
     */
    @TableField("is_mem")
    private Integer isMem;

    /**
     * 是否列席人员{0否1是}
     */
    @TableField("is_attend")
    private Integer isAttend;

    /**
     * 是否缺席人员{0否1是}
     */
    @TableField("is_absence")
    private Integer isAbsence;

    /**
     * 是否签到{0否1是}
     */
    @TableField("is_review")
    private Integer isReview;

    /**
     * 参加时人员所在组织标识符集合
     */
    @TableField("join_org_code_set")
    private String joinOrgCodeSet;


    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getActivityCode() {
        return activityCode;
    }

    public void setActivityCode(String activityCode) {
        this.activityCode = activityCode;
    }

    public String getIsCustomize() {
        return isCustomize;
    }

    public void setIsCustomize(String isCustomize) {
        this.isCustomize = isCustomize;
    }

    public String getMemCode() {
        return memCode;
    }

    public void setMemCode(String memCode) {
        this.memCode = memCode;
    }

    public String getMemOrgCode() {
        return memOrgCode;
    }

    public void setMemOrgCode(String memOrgCode) {
        this.memOrgCode = memOrgCode;
    }

    public String getMemOrgOrgCode() {
        return memOrgOrgCode;
    }

    public void setMemOrgOrgCode(String memOrgOrgCode) {
        this.memOrgOrgCode = memOrgOrgCode;
    }

    public String getAvator() {
        return avator;
    }

    public void setAvator(String avator) {
        this.avator = avator;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getIdcard() {
        return idcard;
    }

    public void setIdcard(String idcard) {
        this.idcard = idcard;
    }

    public String getPhone() {
        return phone;
    }

    public void setPhone(String phone) {
        this.phone = phone;
    }

    public String getReason() {
        return reason;
    }

    public void setReason(String reason) {
        this.reason = reason;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    public Date getDeleteTime() {
        return deleteTime;
    }

    public void setDeleteTime(Date deleteTime) {
        this.deleteTime = deleteTime;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public Integer getIsHostor() {
        return isHostor;
    }

    public void setIsHostor(Integer isHostor) {
        this.isHostor = isHostor;
    }

    public Integer getIsLecturer() {
        return isLecturer;
    }

    public void setIsLecturer(Integer isLecturer) {
        this.isLecturer = isLecturer;
    }

    public Integer getIsLeader() {
        return isLeader;
    }

    public void setIsLeader(Integer isLeader) {
        this.isLeader = isLeader;
    }

    public Integer getIsMem() {
        return isMem;
    }

    public void setIsMem(Integer isMem) {
        this.isMem = isMem;
    }

    public Integer getIsAttend() {
        return isAttend;
    }

    public void setIsAttend(Integer isAttend) {
        this.isAttend = isAttend;
    }

    public Integer getIsAbsence() {
        return isAbsence;
    }

    public void setIsAbsence(Integer isAbsence) {
        this.isAbsence = isAbsence;
    }

    public Integer getIsReview() {
        return isReview;
    }

    public void setIsReview(Integer isReview) {
        this.isReview = isReview;
    }

    public String getJoinOrgCodeSet() {
        return joinOrgCodeSet;
    }

    public void setJoinOrgCodeSet(String joinOrgCodeSet) {
        this.joinOrgCodeSet = joinOrgCodeSet;
    }

    @Override
    public Serializable pkVal() {
        return this.id;
    }

    public ActivityMemDTO toDTO(){
        ActivityMemDTO dto = new ActivityMemDTO();
        dto.setId(this.getId());
        dto.setActivityCode(this.getActivityCode());
        dto.setIsCustomize(this.getIsCustomize());
        dto.setMemCode(this.getMemCode());
        dto.setMemOrgCode(this.getMemOrgCode());
        dto.setMemOrgOrgCode(this.getMemOrgOrgCode());
        dto.setAvator(this.getAvator());
        dto.setName(this.getName());
        dto.setIdcard(this.getIdcard());
        dto.setPhone(this.getPhone());
        dto.setReason(this.getReason());
        dto.setCreateTime(this.getCreateTime());
        dto.setUpdateTime(this.getUpdateTime());
        dto.setDeleteTime(this.getDeleteTime());
        dto.setRemark(this.getRemark());
        dto.setIsHostor(this.getIsHostor());
        dto.setIsLecturer(this.getIsLecturer());
        dto.setIsLeader(this.getIsLeader());
        dto.setIsMem(this.getIsMem());
        dto.setIsAttend(this.getIsAttend());
        dto.setIsAbsence(this.getIsAbsence());
        dto.setIsReview(this.getIsReview());
        dto.setJoinOrgCodeSet(this.getJoinOrgCodeSet());
        return dto;
    }

    @Override
    public String toString() {
        return "ActivityMem{" +
        "id=" + id +
        ", activityCode=" + activityCode +
        ", isCustomize=" + isCustomize +
        ", memCode=" + memCode +
        ", memOrgCode=" + memOrgCode +
        ", memOrgOrgCode=" + memOrgOrgCode +
        ", avator=" + avator +
        ", name=" + name +
        ", idcard=" + idcard +
        ", phone=" + phone +
        ", reason=" + reason +
        ", createTime=" + createTime +
        ", updateTime=" + updateTime +
        ", deleteTime=" + deleteTime +
        ", remark=" + remark +
        ", isHostor=" + isHostor +
        ", isLecturer=" + isLecturer +
        ", isLeader=" + isLeader +
        ", isMem=" + isMem +
        ", isAttend=" + isAttend +
        ", isAbsence=" + isAbsence +
        ", isReview=" + isReview +
        ", joinOrgCodeSet=" + joinOrgCodeSet +
        "}";
    }
}
