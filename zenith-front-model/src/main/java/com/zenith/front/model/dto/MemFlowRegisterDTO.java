package com.zenith.front.model.dto;

import com.baomidou.mybatisplus.annotation.TableField;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.util.Date;
import java.util.List;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2022-11-14
 */
@Setter
@Getter
@ToString
public class MemFlowRegisterDTO {


    /**
     * 组织主键
     */
    private String code;
    /**
     * 重新流出功能，传之前数据code进行终止操作
     */
    private String oldCode;
    /**
     * 组织主键
     */
    private String orgCode;

    /**
     * 多个流动党员主键
     */
    private List<String> codeList;

    /**
     * 多个流动党员所在组织主键
     */
    private List<String> orgCodeList;

    /**
     * 是否省内外
     */
    private String isProvince;

    /**
     * 外出地点
     */
    private String outPlaceCode;

    private String outPlaceName;

    /**
     * 党委
     */
    private String outOrgCode;

    private String outOrgName;

    /**
     * 流入地党组织联系人
     */
    private String outOrgContact;

    /**
     * 流入地党组织联系方式
     */
    private String outOrgContactPhone;

    /**
     * 党支部
     */
    private String outOrgBranchCode;

    private String outOrgBranchName;

    /**
     * 党支部层级码
     */
    private String outOrgBranchOrgCode;

    /**
     * 党组织所在行政区划
     */
    private String outAdministrativeDivisionCode;

    private String outAdministrativeDivisionName;

    /**
     * 失去联系情形
     */
    private String lostContactCode;

    private String lostContactName;

    /**
     * 失去联系日期
     */
    private Date lostContactTime;

    /**
     * 流动类型
     */
    private String flowTypeCode;

    private String flowTypeName;

    /**
     * 流动原因
     */
    private String flowReasonCode;

    private String flowReasonName;

    /**
     * 外出日期
     */
    private Date outTime;

    /**
     * 流入地党组织单位类型
     */
    private String outOrgD04Code;

    /**
     * 外出日期
     */
    private String outOrgD04Name;

    /**
     * 外出地点（党组织）补充说明
     */
    private String outOrgRemarks;

    /**
     * 党费交纳情况（交到流出地至）
     */
    private Date partyExpensesOutTime;

    /**
     * 党费交纳情况（交到流入地至）
     */
    private Date partyExpensesInTime;

    /**
     * 参与组织生活情况
     */
    private String inOrgLife;

    /**
     * 表现反馈
     */
    private String inFeedback;

    /**
     * 流动党员活动证
     */
    private String isHold;

    /**
     * 流回时间
     */
    private Date flowBackTime;

    /**
     * 撤销时间
     */
    private Date cancelTime;

    /**
     * 是否流出
     */
    private String flowOut;

    /**
     * 是否流入
     */
    private String flowIn;

    /**
     * 流动状态 1已流出（未纳入管理） 2已纳入支部管理 3流出被退回 4流出终止 5流动完成 6撤销流出
     */
    private String flowStep;

    /**
     * 流入地党支部
     */
    private String inOrgCode;

    /**
     * 流入地党支部名称
     */
    private String inOrgName;

    /**
     * 党支部联系方式
     */
    private String inOrgPhone;

    /**
     * 党支部单位性质类别
     */
    private String inOrgD04Code;

    private String inOrgD04Name;

    /**
     * 流入地党组织单位类型选择企业，需要经济类型用于统计
     */
    private String inUnitD16Code;

    /**
     * 工作单位类型
     */
    private String inOrgD09Code;

    private String inOrgD09Name;

    /**
     * 党员工作岗位
     */
    private String inMemD09Code;

    private String inMemD09Name;

    /**
     * 党员联系方式
     */
    private String inMemPhone;

    /**
     * 接收时间
     */
    private Date inReceivingTime;

    /**
     * 移至县级库时间
     */
    private Date moveToCountyTime;

    /**
     * 退回原因
     */
    private String rejectReasonCode;

    private String rejectReasonName;

    /**
     * 退回时间
     */
    private Date rejectTime;

    /**
     * 退回操作党组织
     */
    private String rejectOrgCode;

    /**
     * 退回操作党组织
     */
    private String rejectOrgName;

    /**
     * 退回操作党组织联系电话
     */
    private String rejectOrgPhone;

    /**
     * 结对联系人Code
     */
    private String pairedContactCode;
    /**
     * 结对联系人
     */
    private String pairedContact;
    /**
     * 结对联系方式
     */
    private String pairedContactPhone;

    /**
     * 是否为本节点流出地成立状态  0/null-否 1-是
     */
    private String flowOutStatus;

    /**
     * 流出登记类型  1-省内主动流出登记 2-省外主动流出登记 3-省外提醒流出登记 4-省内提醒流出登记  5-流出地组织流出登记
     */
    private String flowSignType;

    /**
     * 登记时间
     */
    private Date registerTime;

    /**
     * 流动党员人员类型
     */
    private String flowMemTypeCode;
    /**
     * 流动党员人员类型
     */
    private String flowMemTypeName;
    /**
     * 人员类型备注
     */
    private String flowMemTypeRemark;
    /**
     * 人员类型新就业备注
     */
    private String flowMemTypeNewRemark;
    /**
     * 流入地是否为农民
     */
    private String lrdIsFarmer;

    /**
     * 是否为农民
     */
    private String isFarmer;

}
