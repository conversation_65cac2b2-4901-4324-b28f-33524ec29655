//package com.zenith.front.dto;
//
//import com.zenith.front.model.OrgAll;
//import lombok.*;
//
//@ToString
//public class OrgAllDTO {
//
//    /**
//     * 自增长主键
//     */
//    private Long id;
//    /**
//     * 唯一标识符code
//     */
//    private String code;
//    /**
//     * es同步唯一主键
//     */
//    private String esId;
//    /**
//     * 组织唯一标识符，中组部code
//     */
//    private String zbCode;
//    /**
//     * 组织层级码
//     */
//    private String orgCode;
//    /**
//     * 组织全称
//     */
//    private String name;
//    /**
//     * 组织简称
//     */
//    private String shortName;
//    /**
//     * 组织全称拼音
//     */
//    private String pinyin;
//    /**
//     * 上级党组织code
//     */
//    private String parentOrgCode;
//    /**
//     * 是否叶子节点
//     */
//    private Integer isLeaf;
//    /**
//     * 上级党组织名称
//     */
//    private String parentName;
//    /**
//     * 上级党组织唯一标识符
//     */
//    private String parentCode;
//    /**
//     * 党组织类型名称
//     */
//    private String d01Name;
//    /**
//     * 党组织类型code
//     */
//    private String d01Code;
//    /**
//     * 党组织大分类
//     */
//    private String orgType;
//    /**
//     * 党组织隶属关系名称
//     */
//    private String d03Name;
//    /**
//     * 党组织隶属关系代码
//     */
//    private String d03Code;
//    /**
//     * 党组织所在单位情况名称
//     */
//    private String d02Name;
//    /**
//     * 党组织所在单位情况代码
//     */
//    private String d02Code;
//    /**
//     * 是否离退休：1是，2否
//     */
//    private Integer isRetire;
//    /**
//     * 是否流动党员党组织：1是，2否
//     */
//    private Integer isFlow;
//    /**
//     * 党组织书记
//     */
//    private String secretary;
//    /**
//     * 党组织书记key
//     */
//    private String secretaryCode;
//    /**
//     * 联系人
//     */
//    private String contacter;
//    /**
//     * 联系方式
//     */
//    private String contactPhone;
//    /**
//     * 传真号
//     */
//    private String faxNumber;
//    /**
//     * 通讯地址
//     */
//    private String postAddress;
//    /**
//     * 邮政编码
//     */
//    private String postCode;
//    /**
//     * 关联单位集合
//     */
//    private String units;
//    /**
//     * 党组织建立日期
//     */
//    private java.util.Date createDate;
//    /**
//     * 排序
//     */
//    private Integer sort;
//    /**
//     * 创建时间
//     */
//    private java.util.Date createTime;
//    /**
//     * 更新时间
//     */
//    private java.util.Date updateTime;
//    /**
//     * 删除时间
//     */
//    private java.util.Date deleteTime;
//    /**
//     * 组合关键字
//     */
//    private String keywords;
//    /**
//     * 备注
//     */
//    private String remark;
//    /**
//     * 坐标
//     */
//    private String addressPosition;
//    /**
//     * 时间戳
//     */
//    private java.util.Date timestamp;
//    /**
//     * 信息完整度
//     */
//    private Double ratio;
//    /**
//     * 主要单位主键
//     */
//    private String mainUnitCode;
//    /**
//     * 主要单位类型
//     */
//    private String mainUnitType;
//    /**
//     * 主要单位名称
//     */
//    private String mainUnitName;
//    /**
//     * 是否历史数据
//     */
//    private Integer isHistory;
//    /**
//     * 最近届次人员集合
//     */
//    private Object electPople;
//    /**
//     * 最近届次书记个人基本信息
//     */
//    private Object electSecretary;
//
//    public OrgAllDTO setId(Long id) {
//        this.id = id;
//        return this;
//    }
//
//    public Long getId() {
//        return this.id;
//    }
//
//    public OrgAllDTO setCode(String code) {
//        this.code = code;
//        return this;
//    }
//
//    public String getCode() {
//        return this.code;
//    }
//
//    public OrgAllDTO setEsId(String esId) {
//        this.esId = esId;
//        return this;
//    }
//
//    public String getEsId() {
//        return this.esId;
//    }
//
//    public OrgAllDTO setZbCode(String zbCode) {
//        this.zbCode = zbCode;
//        return this;
//    }
//
//    public String getZbCode() {
//        return this.zbCode;
//    }
//
//    public OrgAllDTO setOrgCode(String orgCode) {
//        this.orgCode = orgCode;
//        return this;
//    }
//
//    public String getOrgCode() {
//        return this.orgCode;
//    }
//
//    public OrgAllDTO setName(String name) {
//        this.name = name;
//        return this;
//    }
//
//    public String getName() {
//        return this.name;
//    }
//
//    public OrgAllDTO setShortName(String shortName) {
//        this.shortName = shortName;
//        return this;
//    }
//
//    public String getShortName() {
//        return this.shortName;
//    }
//
//    public OrgAllDTO setPinyin(String pinyin) {
//        this.pinyin = pinyin;
//        return this;
//    }
//
//    public String getPinyin() {
//        return this.pinyin;
//    }
//
//    public OrgAllDTO setParentOrgCode(String parentOrgCode) {
//        this.parentOrgCode = parentOrgCode;
//        return this;
//    }
//
//    public String getParentOrgCode() {
//        return this.parentOrgCode;
//    }
//
//    public OrgAllDTO setIsLeaf(Integer isLeaf) {
//        this.isLeaf = isLeaf;
//        return this;
//    }
//
//    public Integer getIsLeaf() {
//        return this.isLeaf;
//    }
//
//    public OrgAllDTO setParentName(String parentName) {
//        this.parentName = parentName;
//        return this;
//    }
//
//    public String getParentName() {
//        return this.parentName;
//    }
//
//    public OrgAllDTO setParentCode(String parentCode) {
//        this.parentCode = parentCode;
//        return this;
//    }
//
//    public String getParentCode() {
//        return this.parentCode;
//    }
//
//    public OrgAllDTO setD01Name(String d01Name) {
//        this.d01Name = d01Name;
//        return this;
//    }
//
//    public String getD01Name() {
//        return this.d01Name;
//    }
//
//    public OrgAllDTO setD01Code(String d01Code) {
//        this.d01Code = d01Code;
//        return this;
//    }
//
//    public String getD01Code() {
//        return this.d01Code;
//    }
//
//    public OrgAllDTO setOrgType(String orgType) {
//        this.orgType = orgType;
//        return this;
//    }
//
//    public String getOrgType() {
//        return this.orgType;
//    }
//
//    public OrgAllDTO setD03Name(String d03Name) {
//        this.d03Name = d03Name;
//        return this;
//    }
//
//    public String getD03Name() {
//        return this.d03Name;
//    }
//
//    public OrgAllDTO setD03Code(String d03Code) {
//        this.d03Code = d03Code;
//        return this;
//    }
//
//    public String getD03Code() {
//        return this.d03Code;
//    }
//
//    public OrgAllDTO setD02Name(String d02Name) {
//        this.d02Name = d02Name;
//        return this;
//    }
//
//    public String getD02Name() {
//        return this.d02Name;
//    }
//
//    public OrgAllDTO setD02Code(String d02Code) {
//        this.d02Code = d02Code;
//        return this;
//    }
//
//    public String getD02Code() {
//        return this.d02Code;
//    }
//
//    public OrgAllDTO setIsRetire(Integer isRetire) {
//        this.isRetire = isRetire;
//        return this;
//    }
//
//    public Integer getIsRetire() {
//        return this.isRetire;
//    }
//
//    public OrgAllDTO setIsFlow(Integer isFlow) {
//        this.isFlow = isFlow;
//        return this;
//    }
//
//    public Integer getIsFlow() {
//        return this.isFlow;
//    }
//
//    public OrgAllDTO setSecretary(String secretary) {
//        this.secretary = secretary;
//        return this;
//    }
//
//    public String getSecretary() {
//        return this.secretary;
//    }
//
//    public OrgAllDTO setSecretaryCode(String secretaryCode) {
//        this.secretaryCode = secretaryCode;
//        return this;
//    }
//
//    public String getSecretaryCode() {
//        return this.secretaryCode;
//    }
//
//    public OrgAllDTO setContacter(String contacter) {
//        this.contacter = contacter;
//        return this;
//    }
//
//    public String getContacter() {
//        return this.contacter;
//    }
//
//    public OrgAllDTO setContactPhone(String contactPhone) {
//        this.contactPhone = contactPhone;
//        return this;
//    }
//
//    public String getContactPhone() {
//        return this.contactPhone;
//    }
//
//    public OrgAllDTO setFaxNumber(String faxNumber) {
//        this.faxNumber = faxNumber;
//        return this;
//    }
//
//    public String getFaxNumber() {
//        return this.faxNumber;
//    }
//
//    public OrgAllDTO setPostAddress(String postAddress) {
//        this.postAddress = postAddress;
//        return this;
//    }
//
//    public String getPostAddress() {
//        return this.postAddress;
//    }
//
//    public OrgAllDTO setPostCode(String postCode) {
//        this.postCode = postCode;
//        return this;
//    }
//
//    public String getPostCode() {
//        return this.postCode;
//    }
//
//    public OrgAllDTO setUnits(String units) {
//        this.units = units;
//        return this;
//    }
//
//    public String getUnits() {
//        return this.units;
//    }
//
//    public OrgAllDTO setCreateDate(java.util.Date createDate) {
//        this.createDate = createDate;
//        return this;
//    }
//
//    public java.util.Date getCreateDate() {
//        return this.createDate;
//    }
//
//    public OrgAllDTO setSort(Integer sort) {
//        this.sort = sort;
//        return this;
//    }
//
//    public Integer getSort() {
//        return this.sort;
//    }
//
//    public OrgAllDTO setCreateTime(java.util.Date createTime) {
//        this.createTime = createTime;
//        return this;
//    }
//
//    public java.util.Date getCreateTime() {
//        return this.createTime;
//    }
//
//    public OrgAllDTO setUpdateTime(java.util.Date updateTime) {
//        this.updateTime = updateTime;
//        return this;
//    }
//
//    public java.util.Date getUpdateTime() {
//        return this.updateTime;
//    }
//
//    public OrgAllDTO setDeleteTime(java.util.Date deleteTime) {
//        this.deleteTime = deleteTime;
//        return this;
//    }
//
//    public java.util.Date getDeleteTime() {
//        return this.deleteTime;
//    }
//
//    public OrgAllDTO setKeywords(String keywords) {
//        this.keywords = keywords;
//        return this;
//    }
//
//    public String getKeywords() {
//        return this.keywords;
//    }
//
//    public OrgAllDTO setRemark(String remark) {
//        this.remark = remark;
//        return this;
//    }
//
//    public String getRemark() {
//        return this.remark;
//    }
//
//    public OrgAllDTO setAddressPosition(String addressPosition) {
//        this.addressPosition = addressPosition;
//        return this;
//    }
//
//    public String getAddressPosition() {
//        return this.addressPosition;
//    }
//
//    public OrgAllDTO setTimestamp(java.util.Date timestamp) {
//        this.timestamp = timestamp;
//        return this;
//    }
//
//    public java.util.Date getTimestamp() {
//        return this.timestamp;
//    }
//
//    public OrgAllDTO setRatio(Double ratio) {
//        this.ratio = ratio;
//        return this;
//    }
//
//    public Double getRatio() {
//        return this.ratio;
//    }
//
//    public OrgAllDTO setMainUnitCode(String mainUnitCode) {
//        this.mainUnitCode = mainUnitCode;
//        return this;
//    }
//
//    public String getMainUnitCode() {
//        return this.mainUnitCode;
//    }
//
//    public OrgAllDTO setMainUnitType(String mainUnitType) {
//        this.mainUnitType = mainUnitType;
//        return this;
//    }
//
//    public String getMainUnitType() {
//        return this.mainUnitType;
//    }
//
//    public OrgAllDTO setMainUnitName(String mainUnitName) {
//        this.mainUnitName = mainUnitName;
//        return this;
//    }
//
//    public String getMainUnitName() {
//        return this.mainUnitName;
//    }
//
//    public OrgAllDTO setIsHistory(Integer isHistory) {
//        this.isHistory = isHistory;
//        return this;
//    }
//
//    public Integer getIsHistory() {
//        return this.isHistory;
//    }
//
//    public OrgAllDTO setElectPople(Object electPople) {
//        this.electPople = electPople;
//        return this;
//    }
//
//    public Object getElectPople() {
//        return this.electPople;
//    }
//
//    public OrgAllDTO setElectSecretary(Object electSecretary) {
//        this.electSecretary = electSecretary;
//        return this;
//    }
//
//    public Object getElectSecretary() {
//        return this.electSecretary;
//    }
//
//    public OrgAll toModel() {
//        OrgAll model = new OrgAll();
//        model.setId(this.id);
//        model.setCode(this.code);
//        model.setEsId(this.esId);
//        model.setZbCode(this.zbCode);
//        model.setOrgCode(this.orgCode);
//        model.setName(this.name);
//        model.setShortName(this.shortName);
//        model.setPinyin(this.pinyin);
//        model.setParentOrgCode(this.parentOrgCode);
//        model.setIsLeaf(this.isLeaf);
//        model.setParentName(this.parentName);
//        model.setParentCode(this.parentCode);
//        model.setD01Name(this.d01Name);
//        model.setD01Code(this.d01Code);
//        model.setOrgType(this.orgType);
//        model.setD03Name(this.d03Name);
//        model.setD03Code(this.d03Code);
//        model.setD02Name(this.d02Name);
//        model.setD02Code(this.d02Code);
//        model.setIsRetire(this.isRetire);
//        model.setIsFlow(this.isFlow);
//        model.setSecretary(this.secretary);
//        model.setSecretaryCode(this.secretaryCode);
//        model.setContacter(this.contacter);
//        model.setContactPhone(this.contactPhone);
//        model.setFaxNumber(this.faxNumber);
//        model.setPostAddress(this.postAddress);
//        model.setPostCode(this.postCode);
//        model.setUnits(this.units);
//        model.setCreateDate(this.createDate);
//        model.setSort(this.sort);
//        model.setCreateTime(this.createTime);
//        model.setUpdateTime(this.updateTime);
//        model.setDeleteTime(this.deleteTime);
//        model.setKeywords(this.keywords);
//        model.setRemark(this.remark);
//        model.setAddressPosition(this.addressPosition);
//        model.setTimestamp(this.timestamp);
//        model.setRatio(this.ratio);
//        model.setMainUnitCode(this.mainUnitCode);
//        model.setMainUnitType(this.mainUnitType);
//        model.setMainUnitName(this.mainUnitName);
//        model.setIsHistory(this.isHistory);
//        model.setElectPople(this.electPople);
//        model.setElectSecretary(this.electSecretary);
//        return model;
//    }
//}