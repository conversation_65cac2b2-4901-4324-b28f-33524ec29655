package com.zenith.front.model.vo;

import java.io.Serializable;

/**
 * <AUTHOR>
 */
public class LogicVO implements Serializable {

    private static final long serialVersionUID = 5003130022669247160L;

    /**
     * 主键
     */
    private String id;

    /**
     * 序号
     */
    private Integer num;

    /**
     * 表名
     */
    private String tableName;

    /**
     * 表名 作用和tableName一样
     */
    private String table;

    /**
     * 逻辑校验条件
     */
    private String condition;

    /**
     * 是否通过
     */
    private Boolean hasPass;

    /**
     * 未通过数量
     */
    private Integer fail;

    /**
     * 是否被忽略
     */
    private Boolean ignored;

    public String getId() {
        return id;
    }

    public LogicVO setId(String id) {
        this.id = id;
        return this;
    }

    public Integer getNum() {
        return num;
    }

    public LogicVO setNum(Integer num) {
        this.num = num;
        return this;
    }

    public String getTableName() {
        return tableName;
    }

    public LogicVO setTableName(String tableName) {
        this.tableName = tableName;
        return this;
    }

    public String getTable() {
        return table;
    }

    public LogicVO setTable(String table) {
        this.table = table;
        return this;
    }

    public String getCondition() {
        return condition;
    }

    public LogicVO setCondition(String condition) {
        this.condition = condition;
        return this;
    }

    public Boolean getHasPass() {
        return hasPass;
    }

    public LogicVO setHasPass(Boolean hasPass) {
        this.hasPass = hasPass;
        return this;
    }

    public Integer getFail() {
        return fail;
    }

    public LogicVO setFail(Integer fail) {
        this.fail = fail;
        return this;
    }

    public Boolean getIgnored() {
        return ignored;
    }

    public LogicVO setIgnored(Boolean ignored) {
        this.ignored = ignored;
        return this;
    }

    @Override
    public String toString() {
        return "LogicVO{" +
                "id='" + id + '\'' +
                ", num=" + num +
                ", tableName='" + tableName + '\'' +
                ", table='" + table + '\'' +
                ", condition='" + condition + '\'' +
                ", hasPass=" + hasPass +
                ", fail=" + fail +
                ", ignored=" + ignored +
                '}';
    }
}
