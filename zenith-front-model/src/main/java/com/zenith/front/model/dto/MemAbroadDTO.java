package com.zenith.front.model.dto;

import com.zenith.front.model.bean.MemAbroad;
import com.zenith.front.model.validate.group.AddGroup;
import com.zenith.front.model.validate.group.BackGroup;
import com.zenith.front.model.validate.group.Common1Group;
import lombok.ToString;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.Date;

@ToString
public class MemAbroadDTO {

    private Long id;
    private String code;
    private String esId;
    /**
     * 人员所属组织层级码
     */
    @NotBlank(groups = AddGroup.class, message = "abroadOrgCode 不能为空")
    private String abroadOrgCode;
    /**
     * 人员所属组织code
     */
    @NotBlank(groups = AddGroup.class, message = "orgCode 不能为空")
    private String orgCode;
    /**
     * 所至国家（地区）1：港澳台，2：国外
     */
    @NotBlank(groups = AddGroup.class, message = "countryCode 不能为空")
    private String countryCode;
    /**
     * 所至国家（地区）1：港澳台，2：国外
     */
    @NotBlank(groups = AddGroup.class, message = "countryName 不能为空")
    private String countryName;
    /**
     * 出国境原因代码
     */
    @NotBlank(groups = AddGroup.class, message = "d033Code 不能为空")
    private String d033Code;
    /**
     * 出国境原因代码
     */
    @NotBlank(groups = AddGroup.class, message = "d033Name 不能为空")
    private String d033Name;
    /**
     * 出国（境）日期
     */
    @NotNull(groups = AddGroup.class, message = "abroadDate 不能为空")
    private java.util.Date abroadDate;
    /**
     * 回国时间
     */
    @NotBlank(groups = BackGroup.class, message = "backHomeDate 不能为空")
    private java.util.Date backHomeDate;
    /**
     * 出国境党员与党组织联系情况代码说明
     */
    @NotBlank(groups = BackGroup.class, message = "d037Name 不能为空")
    private String d037Name;
    /**
     * 出国境党员与党组织联系情况代码
     */
    @NotBlank(groups = BackGroup.class, message = "d037Code 不能为空")
    private String d037Code;
    /**
     * 党籍处理方式说明
     */
    private String d038Name;
    /**
     * 党籍处理方式代码
     */
    private String d038Code;
    /**
     * 该党员因出国（境）特申请保留党籍的时间，以月为单位
     */
    private Integer partyKeepMonth;
    /**
     * 回国情况说明
     */
    private String d039Name;
    /**
     * 回国情况代码
     */
    private String d039Code;
    /**
     * 恢复组织生活情况说明
     */
    @NotBlank(groups = BackGroup.class, message = "恢复组织生活情况 不能为空")
    private String d040Name;
    /**
     * 恢复组织生活情况代码
     */
    @NotBlank(groups = BackGroup.class, message = "恢复组织生活情况 不能为空")
    private String d040Code;
    /**
     * 批准恢复组织生活日期
     */
    private java.util.Date approveDate;
    /**
     * 出国（境）事项说明
     */
    private String remark;
    /**
     * 申请恢复组织生活日期
     */
    @NotBlank(groups = BackGroup.class, message = "申请恢复组织生活日期 不能为空")
    private java.util.Date renewOrglifeDate;
    /**
     * 人员唯一code
     */
    private String memCode;
    /**
     * 人员名称
     */
    private String memName;
    /**
     * 出国时所在组织code
     */
    private String issuedOrgCode;
    /**
     * 出国时所在组织名称
     */
    private String issuedOrgName;
    /**
     * 是否组织关系转接
     */
    @NotNull(groups = AddGroup.class, message = "isTransfer 不能为空")
    private Integer isTransfer;
    /**
     * 申请恢复组织生活日期
     */
    private java.util.Date repeatPartyDate;
    private java.util.Date createTime;
    private java.util.Date updateTime;
    /**
     * 录入回出国境情况组织
     */
    private String orgEntryCode;
    /**
     * 录入回出国境情况组织
     */
    private String orgEntryName;
    private java.util.Date deleteTime;
    private java.util.Date timestamp;
    private String zbCode;
    /**
     * 是否是历史数据
     */
    private Integer isHistory;
    /**
     * 修改人
     */
    private String updateAccount;

    /**
     * 国家
     */
    private String d127Code;

    /**
     * 国家名称
     */
    private String d127Name;
    /**
     * 停止党籍
     */
    @NotNull(groups = Common1Group.class, message = "停止党籍 不能为空")
    private Date stopPartyDate;
    /**
     * 工作单位及职务
     */
    private String workPost;
    /**
     * 停止党籍依据（文件上传）
     */
    private String stopBasis;

    /**
     * 预计回国时间
     */
    @NotNull(groups = AddGroup.class, message = "预计回国时间 不能为空")
    private java.util.Date expectedBackHomeDate;

    /**
     * 停止党籍原因code, dict_d33下23开头的
     */
    @NotNull(groups = Common1Group.class, message = "停止党籍原因 不能为空")
    private String stopMembershipCode;

    /**
     * 停止党籍原因code, dict_d33下23开头的
     */
    @NotNull(groups = Common1Group.class, message = "停止党籍原因 不能为空")
    private String stopMembershipName;


    public String getStopMembershipCode() {
        return stopMembershipCode;
    }

    public void setStopMembershipCode(String stopMembershipCode) {
        this.stopMembershipCode = stopMembershipCode;
    }

    public String getStopMembershipName() {
        return stopMembershipName;
    }

    public void setStopMembershipName(String stopMembershipName) {
        this.stopMembershipName = stopMembershipName;
    }

    public Date getExpectedBackHomeDate() {
        return expectedBackHomeDate;
    }

    public void setExpectedBackHomeDate(Date expectedBackHomeDate) {
        this.expectedBackHomeDate = expectedBackHomeDate;
    }

    public Date getStopPartyDate() {
        return stopPartyDate;
    }

    public void setStopPartyDate(Date stopPartyDate) {
        this.stopPartyDate = stopPartyDate;
    }

    public String getD127Code() {
        return d127Code;
    }

    public void setD127Code(String d127Code) {
        this.d127Code = d127Code;
    }

    public String getD127Name() {
        return d127Name;
    }

    public void setD127Name(String d127Name) {
        this.d127Name = d127Name;
    }

    public MemAbroadDTO setId(Long id) {
        this.id = id;
        return this;
    }

    public Long getId() {
        return this.id;
    }

    public MemAbroadDTO setCode(String code) {
        this.code = code;
        return this;
    }

    public String getCode() {
        return this.code;
    }

    public MemAbroadDTO setEsId(String esId) {
        this.esId = esId;
        return this;
    }

    public String getEsId() {
        return this.esId;
    }

    public MemAbroadDTO setAbroadOrgCode(String abroadOrgCode) {
        this.abroadOrgCode = abroadOrgCode;
        return this;
    }

    public String getAbroadOrgCode() {
        return this.abroadOrgCode;
    }

    public MemAbroadDTO setOrgCode(String orgCode) {
        this.orgCode = orgCode;
        return this;
    }

    public String getOrgCode() {
        return this.orgCode;
    }

    public MemAbroadDTO setCountryCode(String countryCode) {
        this.countryCode = countryCode;
        return this;
    }

    public String getCountryCode() {
        return this.countryCode;
    }

    public MemAbroadDTO setCountryName(String countryName) {
        this.countryName = countryName;
        return this;
    }

    public String getCountryName() {
        return this.countryName;
    }

    public MemAbroadDTO setD033Code(String d033Code) {
        this.d033Code = d033Code;
        return this;
    }

    public String getD033Code() {
        return this.d033Code;
    }

    public MemAbroadDTO setD033Name(String d033Name) {
        this.d033Name = d033Name;
        return this;
    }

    public String getD033Name() {
        return this.d033Name;
    }

    public MemAbroadDTO setAbroadDate(java.util.Date abroadDate) {
        this.abroadDate = abroadDate;
        return this;
    }

    public java.util.Date getAbroadDate() {
        return this.abroadDate;
    }

    public MemAbroadDTO setBackHomeDate(java.util.Date backHomeDate) {
        this.backHomeDate = backHomeDate;
        return this;
    }

    public java.util.Date getBackHomeDate() {
        return this.backHomeDate;
    }

    public MemAbroadDTO setD037Name(String d037Name) {
        this.d037Name = d037Name;
        return this;
    }

    public String getD037Name() {
        return this.d037Name;
    }

    public MemAbroadDTO setD037Code(String d037Code) {
        this.d037Code = d037Code;
        return this;
    }

    public String getD037Code() {
        return this.d037Code;
    }

    public MemAbroadDTO setD038Name(String d038Name) {
        this.d038Name = d038Name;
        return this;
    }

    public String getD038Name() {
        return this.d038Name;
    }

    public MemAbroadDTO setD038Code(String d038Code) {
        this.d038Code = d038Code;
        return this;
    }

    public String getD038Code() {
        return this.d038Code;
    }

    public MemAbroadDTO setPartyKeepMonth(Integer partyKeepMonth) {
        this.partyKeepMonth = partyKeepMonth;
        return this;
    }

    public Integer getPartyKeepMonth() {
        return this.partyKeepMonth;
    }

    public MemAbroadDTO setD039Name(String d039Name) {
        this.d039Name = d039Name;
        return this;
    }

    public String getD039Name() {
        return this.d039Name;
    }

    public MemAbroadDTO setD039Code(String d039Code) {
        this.d039Code = d039Code;
        return this;
    }

    public String getD039Code() {
        return this.d039Code;
    }

    public MemAbroadDTO setD040Name(String d040Name) {
        this.d040Name = d040Name;
        return this;
    }

    public String getD040Name() {
        return this.d040Name;
    }

    public MemAbroadDTO setD040Code(String d040Code) {
        this.d040Code = d040Code;
        return this;
    }

    public String getD040Code() {
        return this.d040Code;
    }

    public MemAbroadDTO setApproveDate(java.util.Date approveDate) {
        this.approveDate = approveDate;
        return this;
    }

    public java.util.Date getApproveDate() {
        return this.approveDate;
    }

    public MemAbroadDTO setRemark(String remark) {
        this.remark = remark;
        return this;
    }

    public String getRemark() {
        return this.remark;
    }

    public MemAbroadDTO setRenewOrglifeDate(java.util.Date renewOrglifeDate) {
        this.renewOrglifeDate = renewOrglifeDate;
        return this;
    }

    public java.util.Date getRenewOrglifeDate() {
        return this.renewOrglifeDate;
    }

    public MemAbroadDTO setMemCode(String memCode) {
        this.memCode = memCode;
        return this;
    }

    public String getMemCode() {
        return this.memCode;
    }

    public MemAbroadDTO setMemName(String memName) {
        this.memName = memName;
        return this;
    }

    public String getMemName() {
        return this.memName;
    }

    public MemAbroadDTO setIssuedOrgCode(String issuedOrgCode) {
        this.issuedOrgCode = issuedOrgCode;
        return this;
    }

    public String getIssuedOrgCode() {
        return this.issuedOrgCode;
    }

    public MemAbroadDTO setIssuedOrgName(String issuedOrgName) {
        this.issuedOrgName = issuedOrgName;
        return this;
    }

    public String getIssuedOrgName() {
        return this.issuedOrgName;
    }

    public MemAbroadDTO setIsTransfer(Integer isTransfer) {
        this.isTransfer = isTransfer;
        return this;
    }

    public Integer getIsTransfer() {
        return this.isTransfer;
    }

    public MemAbroadDTO setRepeatPartyDate(java.util.Date repeatPartyDate) {
        this.repeatPartyDate = repeatPartyDate;
        return this;
    }

    public java.util.Date getRepeatPartyDate() {
        return this.repeatPartyDate;
    }

    public MemAbroadDTO setCreateTime(java.util.Date createTime) {
        this.createTime = createTime;
        return this;
    }

    public java.util.Date getCreateTime() {
        return this.createTime;
    }

    public MemAbroadDTO setUpdateTime(java.util.Date updateTime) {
        this.updateTime = updateTime;
        return this;
    }

    public java.util.Date getUpdateTime() {
        return this.updateTime;
    }

    public MemAbroadDTO setOrgEntryCode(String orgEntryCode) {
        this.orgEntryCode = orgEntryCode;
        return this;
    }

    public String getOrgEntryCode() {
        return this.orgEntryCode;
    }

    public MemAbroadDTO setOrgEntryName(String orgEntryName) {
        this.orgEntryName = orgEntryName;
        return this;
    }

    public String getOrgEntryName() {
        return this.orgEntryName;
    }

    public MemAbroadDTO setDeleteTime(java.util.Date deleteTime) {
        this.deleteTime = deleteTime;
        return this;
    }

    public java.util.Date getDeleteTime() {
        return this.deleteTime;
    }

    public MemAbroadDTO setTimestamp(java.util.Date timestamp) {
        this.timestamp = timestamp;
        return this;
    }

    public java.util.Date getTimestamp() {
        return this.timestamp;
    }

    public MemAbroadDTO setZbCode(String zbCode) {
        this.zbCode = zbCode;
        return this;
    }

    public String getZbCode() {
        return this.zbCode;
    }

    public MemAbroadDTO setIsHistory(Integer isHistory) {
        this.isHistory = isHistory;
        return this;
    }

    public Integer getIsHistory() {
        return this.isHistory;
    }

    public MemAbroadDTO setUpdateAccount(String updateAccount) {
        this.updateAccount = updateAccount;
        return this;
    }

    public String getUpdateAccount() {
        return this.updateAccount;
    }

    public String getWorkPost() {
        return workPost;
    }

    public void setWorkPost(String workPost) {
        this.workPost = workPost;
    }

    public String getStopBasis() {
        return stopBasis;
    }

    public void setStopBasis(String stopBasis) {
        this.stopBasis = stopBasis;
    }

    public MemAbroad toModel() {
        MemAbroad model = new MemAbroad();
        model.setId(this.id);
        model.setCode(this.code);
        model.setEsId(this.esId);
        model.setAbroadOrgCode(this.abroadOrgCode);
        model.setOrgCode(this.orgCode);
        model.setCountryCode(this.countryCode);
        model.setCountryName(this.countryName);
        model.setD033Code(this.d033Code);
        model.setD033Name(this.d033Name);
        model.setAbroadDate(this.abroadDate);
        model.setBackHomeDate(this.backHomeDate);
        model.setD037Name(this.d037Name);
        model.setD037Code(this.d037Code);
        model.setD038Name(this.d038Name);
        model.setD038Code(this.d038Code);
        model.setPartyKeepMonth(this.partyKeepMonth);
        model.setD039Name(this.d039Name);
        model.setD039Code(this.d039Code);
        model.setD040Name(this.d040Name);
        model.setD040Code(this.d040Code);
        model.setApproveDate(this.approveDate);
        model.setRemark(this.remark);
        model.setRenewOrglifeDate(this.renewOrglifeDate);
        model.setMemCode(this.memCode);
        model.setMemName(this.memName);
        model.setIssuedOrgCode(this.issuedOrgCode);
        model.setIssuedOrgName(this.issuedOrgName);
        model.setIsTransfer(this.isTransfer);
        model.setRepeatPartyDate(this.repeatPartyDate);
        model.setCreateTime(this.createTime);
        model.setUpdateTime(this.updateTime);
        model.setOrgEntryCode(this.orgEntryCode);
        model.setOrgEntryName(this.orgEntryName);
        model.setDeleteTime(this.deleteTime);
        model.setTimestamp(this.timestamp);
        model.setZbCode(this.zbCode);
        model.setIsHistory(this.isHistory);
        model.setUpdateAccount(this.updateAccount);
        model.setD127Code(this.getD127Code());
        model.setD127Name(this.getD127Name());
        model.setStopPartyDate(this.stopPartyDate);
        model.setWorkPost(this.workPost);
        model.setStopBasis(this.stopBasis);
        model.setExpectedBackHomeDate(this.expectedBackHomeDate);
        return model;
    }
}