package com.zenith.front.model.dto;

import com.zenith.front.model.bean.TransferLog;
import lombok.*;

@ToString
public class TransferLogDTO{

   	/**
   	 * 主键
   	 */
    private String id;
   	/**
   	 * 处理审批的id
   	 */
    private String handleApprovalId;
   	/**
   	 * 受影响的审批id
   	 */
    private String effectApprovalId;
   	/**
   	 * 操作类型,0 审批通过 1发起转接 2 驳回 3更改目标组织
   	 */
    private Integer type;
   	/**
   	 * 操作原因
   	 */
    private String reason;
   	/**
   	 * 创建时间
   	 */
    private java.util.Date createTime;
   	/**
   	 * 更新时间
   	 */
    private java.util.Date updateTime;

    public TransferLogDTO setId(String id){
        this.id = id;
        return this;
    }
    public String getId() {
    	return this.id;
    }
    public TransferLogDTO setHandleApprovalId(String handleApprovalId){
        this.handleApprovalId = handleApprovalId;
        return this;
    }
    public String getHandleApprovalId() {
    	return this.handleApprovalId;
    }
    public TransferLogDTO setEffectApprovalId(String effectApprovalId){
        this.effectApprovalId = effectApprovalId;
        return this;
    }
    public String getEffectApprovalId() {
    	return this.effectApprovalId;
    }
    public TransferLogDTO setType(Integer type){
        this.type = type;
        return this;
    }
    public Integer getType() {
    	return this.type;
    }
    public TransferLogDTO setReason(String reason){
        this.reason = reason;
        return this;
    }
    public String getReason() {
    	return this.reason;
    }
    public TransferLogDTO setCreateTime(java.util.Date createTime){
        this.createTime = createTime;
        return this;
    }
    public java.util.Date getCreateTime() {
    	return this.createTime;
    }
    public TransferLogDTO setUpdateTime(java.util.Date updateTime){
        this.updateTime = updateTime;
        return this;
    }
    public java.util.Date getUpdateTime() {
    	return this.updateTime;
    }


    public TransferLog toModel(){
        TransferLog model = new TransferLog();
        model.setId(this.id);
        model.setHandleApprovalId(this.handleApprovalId);
        model.setEffectApprovalId(this.effectApprovalId);
        model.setType(this.type);
        model.setReason(this.reason);
        model.setCreateTime(this.createTime);
        model.setUpdateTime(this.updateTime);
        return model;
    }
}