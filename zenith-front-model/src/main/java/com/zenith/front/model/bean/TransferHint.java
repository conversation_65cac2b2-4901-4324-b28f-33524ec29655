package com.zenith.front.model.bean;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import lombok.Data;

import java.util.Date;

/**
 * 关系转接提醒表
 * <AUTHOR>
 * @create_date 2025-05-20 11:39
 * @description
 */
@Data
@TableName("ccp_transfer_hint")
public class TransferHint extends Model<TransferHint> {

    /**
     * 主键
     */
    @TableId(value = "code", type = IdType.ASSIGN_UUID)
    private String code;

    /**
     * 转接记录id
     */
    @TableField("transfer_id")
    private String transferId;

    /**
     * 提醒记录类型：1-上传交换区失败的， 2-主动撤销的，4-超期自动退回
     */
    @TableField("type")
    private Integer type;

    /**
     * 提示时间
     */
    @TableField("hint_date")
    private Date hintDate;

    /**
     * 提示接收人
     */
    @TableField("hint_account")
    private String hintAccount;

}
