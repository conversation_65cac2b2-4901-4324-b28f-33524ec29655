package com.zenith.front.model.bean;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import java.util.Date;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableField;
import java.io.Serializable;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2021-07-11
 */
@TableName("ccp_task")
public class Task extends Model<Task> {

    private static final long serialVersionUID=1L;

    /**
     * 自增长id
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 唯一主键
     */
    @TableField("code")
    private String code;

    /**
     * 任务名称
     */
    @TableField("task_name")
    private String taskName;

    /**
     * 备注
     */
    @TableField("task_remark")
    private String taskRemark;

    /**
     * 任务对象类别(1.党员，2组织)
     */
    @TableField("task_object_type")
    private Integer taskObjectType;

    /**
     * 任务对象
     */
    @TableField("task_object")
    private String taskObject;

    /**
     * 开始时间
     */
    @TableField("start_date")
    private Date startDate;

    /**
     * 结束时间
     */
    @TableField("end_date")
    private Date endDate;

    /**
     * 任务分值
     */
    @TableField("task_fraction")
    private Integer taskFraction;

    /**
     * 任务周期
     */
    @TableField("task_cycle")
    private Integer taskCycle;

    /**
     * 创建时间
     */
    @TableField("create_time")
    private Date createTime;

    /**
     * 更新时间
     */
    @TableField("update_time")
    private Date updateTime;

    /**
     * 删除时间
     */
    @TableField("delete_time")
    private Date deleteTime;

    /**
     * 任务状态(1.未开始，2进行中，3已结束)
     */
    @TableField("task_status")
    private Integer taskStatus;

    /**
     * 创建组织唯一标识符
     */
    @TableField("create_org_code")
    private String createOrgCode;

    /**
     * 创建组织层级码
     */
    @TableField("create_org_org_code")
    private String createOrgOrgCode;

    /**
     * 任务资料
     */
    @TableField("task_file")
    private String taskFile;


    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getTaskName() {
        return taskName;
    }

    public void setTaskName(String taskName) {
        this.taskName = taskName;
    }

    public String getTaskRemark() {
        return taskRemark;
    }

    public void setTaskRemark(String taskRemark) {
        this.taskRemark = taskRemark;
    }

    public Integer getTaskObjectType() {
        return taskObjectType;
    }

    public void setTaskObjectType(Integer taskObjectType) {
        this.taskObjectType = taskObjectType;
    }

    public String getTaskObject() {
        return taskObject;
    }

    public void setTaskObject(String taskObject) {
        this.taskObject = taskObject;
    }

    public Date getStartDate() {
        return startDate;
    }

    public void setStartDate(Date startDate) {
        this.startDate = startDate;
    }

    public Date getEndDate() {
        return endDate;
    }

    public void setEndDate(Date endDate) {
        this.endDate = endDate;
    }

    public Integer getTaskFraction() {
        return taskFraction;
    }

    public void setTaskFraction(Integer taskFraction) {
        this.taskFraction = taskFraction;
    }

    public Integer getTaskCycle() {
        return taskCycle;
    }

    public void setTaskCycle(Integer taskCycle) {
        this.taskCycle = taskCycle;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    public Date getDeleteTime() {
        return deleteTime;
    }

    public void setDeleteTime(Date deleteTime) {
        this.deleteTime = deleteTime;
    }

    public Integer getTaskStatus() {
        return taskStatus;
    }

    public void setTaskStatus(Integer taskStatus) {
        this.taskStatus = taskStatus;
    }

    public String getCreateOrgCode() {
        return createOrgCode;
    }

    public void setCreateOrgCode(String createOrgCode) {
        this.createOrgCode = createOrgCode;
    }

    public String getCreateOrgOrgCode() {
        return createOrgOrgCode;
    }

    public void setCreateOrgOrgCode(String createOrgOrgCode) {
        this.createOrgOrgCode = createOrgOrgCode;
    }

    public String getTaskFile() {
        return taskFile;
    }

    public void setTaskFile(String taskFile) {
        this.taskFile = taskFile;
    }

    @Override
    public Serializable pkVal() {
        return this.id;
    }

    @Override
    public String toString() {
        return "Task{" +
        "id=" + id +
        ", code=" + code +
        ", taskName=" + taskName +
        ", taskRemark=" + taskRemark +
        ", taskObjectType=" + taskObjectType +
        ", taskObject=" + taskObject +
        ", startDate=" + startDate +
        ", endDate=" + endDate +
        ", taskFraction=" + taskFraction +
        ", taskCycle=" + taskCycle +
        ", createTime=" + createTime +
        ", updateTime=" + updateTime +
        ", deleteTime=" + deleteTime +
        ", taskStatus=" + taskStatus +
        ", createOrgCode=" + createOrgCode +
        ", createOrgOrgCode=" + createOrgOrgCode +
        ", taskFile=" + taskFile +
        "}";
    }
}
