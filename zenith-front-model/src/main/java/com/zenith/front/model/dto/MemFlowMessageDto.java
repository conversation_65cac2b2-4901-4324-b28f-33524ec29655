package com.zenith.front.model.dto;

import com.baomidou.mybatisplus.annotation.TableField;
import com.zenith.front.model.custom.HasSubordinateField;
import com.zenith.front.model.validate.group.Common1Group;
import com.zenith.front.model.validate.group.Common2Group;
import lombok.Data;

import javax.validation.constraints.Max;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotBlank;
import java.io.Serializable;

@Data
public class MemFlowMessageDto extends HasSubordinateField {

    /**
     * 分页页数
     */
    private Integer pageNum;
    /**
     * 分页每页数
     */
    private Integer pageSize;

    /**
     * 消息
     */
    @NotBlank(groups = {Common1Group.class},message = "message不能为空")
    private String message;

    /**
     *流动记录code
     */
    @NotBlank(groups = {Common1Group.class, Common2Group.class},message = "memFlowCode不能为空")
    private String memFlowCode;

    /**
     * 1-流出管理页面 2-流入管理页面
     */
    private String type;








}
