package com.zenith.front.model.bean;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;

import java.util.Date;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2021-07-11
 */
@TableName("ccp_unit_committee_elect")
public class UnitCommitteeElect extends Model<UnitCommitteeElect> {

    private static final long serialVersionUID = 1L;

    /**
     * 唯一自增长主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 唯一标识符code
     */
    @TableField("code")
    private String code;

    /**
     * es同步唯一主键
     */
    @TableField("es_id")
    private String esId;

    /**
     * 单位唯一标识符code
     */
    @TableField("unit_code")
    private String unitCode;

    /**
     * 届次开始时间
     */
    @TableField("tenure_start_date")
    private Date tenureStartDate;

    /**
     * 届次结束时间
     */
    @TableField("tenure_end_date")
    private Date tenureEndDate;

    /**
     * 时间戳
     */
    @TableField("timestamp")
    private Date timestamp;

    /**
     * 删除时间
     */
    @TableField("delete_time")
    private Date deleteTime;

    /**
     * 创建时间
     */
    @TableField("create_time")
    private Date createTime;

    /**
     * 更新时间
     */
    @TableField("update_time")
    private Date updateTime;

    /**
     * 最近更更新账号
     */
    @TableField("update_account")
    private String updateAccount;

    /**
     * 是否历史数据
     */
    @TableField(exist = false)
    private Integer isHistory;

    public Integer getIsHistory() {
        return isHistory;
    }

    public void setIsHistory(Integer isHistory) {
        this.isHistory = isHistory;
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getEsId() {
        return esId;
    }

    public void setEsId(String esId) {
        this.esId = esId;
    }

    public String getUnitCode() {
        return unitCode;
    }

    public void setUnitCode(String unitCode) {
        this.unitCode = unitCode;
    }

    public Date getTenureStartDate() {
        return tenureStartDate;
    }

    public void setTenureStartDate(Date tenureStartDate) {
        this.tenureStartDate = tenureStartDate;
    }

    public Date getTenureEndDate() {
        return tenureEndDate;
    }

    public void setTenureEndDate(Date tenureEndDate) {
        this.tenureEndDate = tenureEndDate;
    }

    public Date getTimestamp() {
        return timestamp;
    }

    public void setTimestamp(Date timestamp) {
        this.timestamp = timestamp;
    }

    public Date getDeleteTime() {
        return deleteTime;
    }

    public void setDeleteTime(Date deleteTime) {
        this.deleteTime = deleteTime;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    public String getUpdateAccount() {
        return updateAccount;
    }

    public void setUpdateAccount(String updateAccount) {
        this.updateAccount = updateAccount;
    }

    @Override
    public String toString() {
        return "UnitCommitteeElect{" +
                "id=" + id +
                ", code='" + code + '\'' +
                ", esId='" + esId + '\'' +
                ", unitCode='" + unitCode + '\'' +
                ", tenureStartDate=" + tenureStartDate +
                ", tenureEndDate=" + tenureEndDate +
                ", timestamp=" + timestamp +
                ", deleteTime=" + deleteTime +
                ", createTime=" + createTime +
                ", updateTime=" + updateTime +
                ", updateAccount='" + updateAccount + '\'' +
                '}';
    }
}
