package com.zenith.front.model.vo;

import com.zenith.front.model.bean.MemFlow1;

/**
 * <AUTHOR>
 * @date 2022/11/14
 */
public class MemFlowNewVo extends MemFlow1 {

    /**
     * 外出时长
     */
    private String outLength;
    /**
     * 流动状态名称
     */
    private String flowStepName;
    /**
     * 流动党员提示
     */
    private String flowMessage;

    public String getOutLength() {
        return outLength;
    }

    public void setOutLength(String outLength) {
        this.outLength = outLength;
    }

    public String getFlowStepName() {
        return flowStepName;
    }

    public void setFlowStepName(String flowStepName) {
        this.flowStepName = flowStepName;
    }

    public String getFlowMessage() {
        return flowMessage;
    }

    public void setFlowMessage(String flowMessage) {
        this.flowMessage = flowMessage;
    }
}
