package com.zenith.front.model.dto;

import com.zenith.front.model.bean.TransferApproval;
import lombok.*;

@ToString
public class TransferApprovalDTO {

    /**
     * 主键
     */
    private String id;
    /**
     * 转接记录id
     */
    private String recordId;
    /**
     * 审核用户id
     */
    private String userId;
    /**
     * 当前组织id
     */
    private String orgId;
    /**
     * 下一个审核组织id
     */
    private String nextOrgId;
    /**
     * 是否代审,0 非代审
     */
    private Integer isInstead;
    /**
     * 审批状态,0 待审核 1 审核通过 2退回
     */
    private Integer status;
    /**
     * 父id
     */
    private String parentId;
    /**
     * 创建时间
     */
    private java.util.Date createTime;
    /**
     * 更新时间
     */
    private java.util.Date updateTime;
    private Integer direction;

    public Integer getDirection() {
        return direction;
    }

    public void setDirection(Integer direction) {
        this.direction = direction;
    }

    public TransferApprovalDTO setId(String id) {
        this.id = id;
        return this;
    }

    public String getId() {
        return this.id;
    }

    public TransferApprovalDTO setRecordId(String recordId) {
        this.recordId = recordId;
        return this;
    }

    public String getRecordId() {
        return this.recordId;
    }

    public TransferApprovalDTO setUserId(String userId) {
        this.userId = userId;
        return this;
    }

    public String getUserId() {
        return this.userId;
    }

    public TransferApprovalDTO setOrgId(String orgId) {
        this.orgId = orgId;
        return this;
    }

    public String getOrgId() {
        return this.orgId;
    }

    public TransferApprovalDTO setNextOrgId(String nextOrgId) {
        this.nextOrgId = nextOrgId;
        return this;
    }

    public String getNextOrgId() {
        return this.nextOrgId;
    }

    public TransferApprovalDTO setIsInstead(Integer isInstead) {
        this.isInstead = isInstead;
        return this;
    }

    public Integer getIsInstead() {
        return this.isInstead;
    }

    public TransferApprovalDTO setStatus(Integer status) {
        this.status = status;
        return this;
    }

    public Integer getStatus() {
        return this.status;
    }

    public TransferApprovalDTO setParentId(String parentId) {
        this.parentId = parentId;
        return this;
    }

    public String getParentId() {
        return this.parentId;
    }

    public TransferApprovalDTO setCreateTime(java.util.Date createTime) {
        this.createTime = createTime;
        return this;
    }

    public java.util.Date getCreateTime() {
        return this.createTime;
    }

    public TransferApprovalDTO setUpdateTime(java.util.Date updateTime) {
        this.updateTime = updateTime;
        return this;
    }

    public java.util.Date getUpdateTime() {
        return this.updateTime;
    }

    public TransferApproval toModel() {
        TransferApproval model = new TransferApproval();
        model.setId(this.id);
        model.setRecordId(this.recordId);
        model.setUserId(this.userId);
        model.setOrgId(this.orgId);
        model.setNextOrgId(this.nextOrgId);
        model.setIsInstead(this.isInstead);
        model.setStatus(this.status);
        model.setParentId(this.parentId);
        model.setCreateTime(this.createTime);
        model.setUpdateTime(this.updateTime);
        return model;
    }
}