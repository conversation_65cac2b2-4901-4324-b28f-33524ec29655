package com.zenith.front.model.vo;

import cn.afterturn.easypoi.excel.annotation.Excel;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2021/12/13
 */
@Data
public class MemListVoExcel {

    private static final long serialVersionUID = 6284731337680940509L;
    @Excel(name = "序号", orderNum = "1", height = 8, width = 10, isImportField = "true_st")
    private Integer xh;
    @Excel(name = "姓名", orderNum = "2", height = 8, width = 10, isImportField = "true_st")
    private String name;
    @Excel(name = "性别", orderNum = "3", height = 8, width = 10, isImportField = "true_st")
    private String sexName;
    @Excel(name = "公民身份证", orderNum = "4", height = 8, width = 20, isImportField = "true_st")
    private String idcard;
    @Excel(name = "电话", orderNum = "5", height = 8, width = 15, isImportField = "true_st")
    private String phone;
    @Excel(name = "民族", orderNum = "7", height = 8, width = 10, isImportField = "true_st")
    private String d06Name;
    @Excel(name = "学历", orderNum = "8", height = 8, width = 10, isImportField = "true_st")
    private String d07Name;
    @Excel(name = "党员类型", orderNum = "9", height = 8, width = 10, isImportField = "true_st")
    private String d08Name;
    @Excel(name = "入党时间", orderNum = "10", height = 8, width = 15, isImportField = "true_st")
    private String topreJoinOrgDate;
    @Excel(name = "专业技术职务", orderNum = "11", height = 8, width = 15, isImportField = "true_st")
    private String d19Name;
    @Excel(name = "工作岗位", orderNum = "12", height = 8, width = 30, isImportField = "true_st")
    private String d09Name;
    @Excel(name = "新社会阶层", orderNum = "13", height = 8, width = 30, isImportField = "true_st")
    private String d20Name;
    @Excel(name = "一线情况", orderNum = "14", height = 8, width = 30, isImportField = "true_st")
    private String d21Name;
    @Excel(name = "所在组织", orderNum = "15", height = 8, width = 50, isImportField = "true_st")
    private String orgName;
    @Excel(name = "人事关系是否在党组织关联单位内", orderNum = "16", height = 8, width = 50, isImportField = "true_st")
    private String hasUnitStatistics;
    @Excel(name = "人事关系所在单位是否省内单位", orderNum = "17", height = 8, width = 50, isImportField = "true_st")
    private String hasUnitProvince;
    @Excel(name = "人事关系所在单位名称", orderNum = "18", height = 8, width = 50, isImportField = "true_st")
    private String unitInformation;
    @Excel(name = "人事关系所在单位类别", orderNum = "19", height = 8, width = 50, isImportField = "true_st")
    private String d04Code;
    @Excel(name = "是否需要自动计算年级", orderNum = "20", height = 8, width = 50, isImportField = "true_st")
    private String hasCalculationGrade;
    @Excel(name = "学制", orderNum = "21", height = 8, width = 30, isImportField = "true_st")
    private String educationalSystem;
    @Excel(name = "入学时间", orderNum = "22", height = 8, width = 15, isImportField = "true_st")
    private String enterSchoolDate;

    @Excel(name = "国民经济行业", orderNum = "23", height = 8, width = 30, isImportField = "true_st")
    private String d194Name;
    @Excel(name = "生产性服务行业", orderNum = "24", height = 8, width = 30, isImportField = "true_st")
    private String d195Name;

}
