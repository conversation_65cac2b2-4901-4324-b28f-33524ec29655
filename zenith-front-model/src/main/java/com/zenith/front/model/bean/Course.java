package com.zenith.front.model.bean;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import java.util.Date;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableField;
import java.io.Serializable;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2021-07-11
 */
@TableName("ccp_course")
public class Course extends Model<Course> {

    private static final long serialVersionUID=1L;

    /**
     * 唯一标识符
     */
    @TableId(value = "id", type = IdType.ASSIGN_UUID)
    private String id;

    /**
     * 课程名称
     */
    @TableField("name")
    private String name;

    /**
     * 所属分类
     */
    @TableField("category")
    private String category;

    /**
     * 课程类型
     */
    @TableField("type")
    private String type;

    /**
     * 有效性
     */
    @TableField("is_valid")
    private Boolean isValid;

    /**
     * 是否可下载
     */
    @TableField("is_download")
    private Boolean isDownload;

    /**
     * 附件名称
     */
    @TableField("file_name")
    private String fileName;

    /**
     * 简介
     */
    @TableField("file_introduction")
    private String fileIntroduction;

    /**
     * 附件地址
     */
    @TableField("file_url")
    private String fileUrl;

    /**
     * 封面图地址
     */
    @TableField("cover_url")
    private String coverUrl;

    /**
     * 时长(时分秒)
     */
    @TableField("duration")
    private String duration;

    /**
     * 时长
     */
    @TableField("duration_long")
    private Long durationLong;

    /**
     * 上传者主键
     */
    @TableField("uploader_id")
    private String uploaderId;

    /**
     * 上传者
     */
    @TableField("uploader")
    private String uploader;

    /**
     * 创建时间
     */
    @TableField("gmt_create")
    private Date gmtCreate;

    /**
     * 更新时间
     */
    @TableField("gmt_modified")
    private Date gmtModified;

    /**
     * 1 true 0 false
     */
    @TableField("delete")
    private Integer delete;

    /**
     * 机构
     */
    @TableField("org_code")
    private String orgCode;


    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getCategory() {
        return category;
    }

    public void setCategory(String category) {
        this.category = category;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public Boolean getValid() {
        return isValid;
    }

    public void setValid(Boolean isValid) {
        this.isValid = isValid;
    }

    public Boolean getDownload() {
        return isDownload;
    }

    public void setDownload(Boolean isDownload) {
        this.isDownload = isDownload;
    }

    public String getFileName() {
        return fileName;
    }

    public void setFileName(String fileName) {
        this.fileName = fileName;
    }

    public String getFileIntroduction() {
        return fileIntroduction;
    }

    public void setFileIntroduction(String fileIntroduction) {
        this.fileIntroduction = fileIntroduction;
    }

    public String getFileUrl() {
        return fileUrl;
    }

    public void setFileUrl(String fileUrl) {
        this.fileUrl = fileUrl;
    }

    public String getCoverUrl() {
        return coverUrl;
    }

    public void setCoverUrl(String coverUrl) {
        this.coverUrl = coverUrl;
    }

    public String getDuration() {
        return duration;
    }

    public void setDuration(String duration) {
        this.duration = duration;
    }

    public Long getDurationLong() {
        return durationLong;
    }

    public void setDurationLong(Long durationLong) {
        this.durationLong = durationLong;
    }

    public String getUploaderId() {
        return uploaderId;
    }

    public void setUploaderId(String uploaderId) {
        this.uploaderId = uploaderId;
    }

    public String getUploader() {
        return uploader;
    }

    public void setUploader(String uploader) {
        this.uploader = uploader;
    }

    public Date getGmtCreate() {
        return gmtCreate;
    }

    public void setGmtCreate(Date gmtCreate) {
        this.gmtCreate = gmtCreate;
    }

    public Date getGmtModified() {
        return gmtModified;
    }

    public void setGmtModified(Date gmtModified) {
        this.gmtModified = gmtModified;
    }

    public Integer getDelete() {
        return delete;
    }

    public void setDelete(Integer delete) {
        this.delete = delete;
    }

    public String getOrgCode() {
        return orgCode;
    }

    public void setOrgCode(String orgCode) {
        this.orgCode = orgCode;
    }

    @Override
    public Serializable pkVal() {
        return this.id;
    }

    @Override
    public String toString() {
        return "Course{" +
        "id=" + id +
        ", name=" + name +
        ", category=" + category +
        ", type=" + type +
        ", isValid=" + isValid +
        ", isDownload=" + isDownload +
        ", fileName=" + fileName +
        ", fileIntroduction=" + fileIntroduction +
        ", fileUrl=" + fileUrl +
        ", coverUrl=" + coverUrl +
        ", duration=" + duration +
        ", durationLong=" + durationLong +
        ", uploaderId=" + uploaderId +
        ", uploader=" + uploader +
        ", gmtCreate=" + gmtCreate +
        ", gmtModified=" + gmtModified +
        ", delete=" + delete +
        ", orgCode=" + orgCode +
        "}";
    }
}
