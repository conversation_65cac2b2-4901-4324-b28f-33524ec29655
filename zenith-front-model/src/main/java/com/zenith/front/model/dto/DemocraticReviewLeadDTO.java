package com.zenith.front.model.dto;

import com.zenith.front.model.bean.DemocraticReviewLead;
import com.zenith.front.model.validate.group.AddGroup;
import lombok.*;

import javax.validation.constraints.NotBlank;

@Data
@ToString
@NoArgsConstructor
@AllArgsConstructor
public class DemocraticReviewLeadDTO {

    /**
     * id
     */
    private Long id;
    @NotBlank(groups = AddGroup.class, message = "code 不能为空")
    private String code;
    /**
     * 组织唯一标示
     */
    private String orgCode;
    /**
     * 组织层级码
     */
    private String leadOrgCode;
    /**
     * 年度
     */
    private String year;
    /**
     * 民主评议code
     */
    private String reviewCode;
    /**
     * 党员code
     */
    private String memCode;
    /**
     * 评议等级
     */
    @NotBlank(groups = AddGroup.class, message = "d69Code 不能为空")
    private String d69Code;
    /**
     * 评议名称
     */
    @NotBlank(groups = AddGroup.class, message = "d69Name 不能为空")
    private String d69Name;
    /**
     * 原因
     */
    private String reason;
    private java.util.Date createTime;
    private java.util.Date updateTime;
    private java.util.Date deleteTime;
    /**
     * 修改记录的党员code
     */
    private String updateMemCode;
    /**
     * 优秀数量
     */
    private Integer excellentCount;
    /**
     * 合格数量
     */
    private Integer qualifiedCount;
    /**
     * 基本合格数量
     */
    private Integer passMusterCount;
    /**
     * 不定等级数量
     */
    private Integer notLevelCount;
    /**
     * 不合格数量
     */
    private Integer disqualificationCount;
    /**
     * 待评定数量
     */
    private Integer evaluatingCount;

    private String sexCode;
    private String d08Code;

    public DemocraticReviewLead toModel() {
        DemocraticReviewLead model = new DemocraticReviewLead();
        model.setId(this.id);
        model.setCode(this.code);
        model.setOrgCode(this.orgCode);
        model.setLeadOrgCode(this.leadOrgCode);
        model.setYear(this.year);
        model.setReviewCode(this.reviewCode);
        model.setMemCode(this.memCode);
        model.setD69Code(this.d69Code);
        model.setD69Name(this.d69Name);
        model.setReason(this.reason);
        model.setCreateTime(this.createTime);
        model.setUpdateTime(this.updateTime);
        model.setDeleteTime(this.deleteTime);
        model.setUpdateMemCode(this.updateMemCode);
        model.setExcellentCount(this.excellentCount);
        model.setQualifiedCount(this.qualifiedCount);
        model.setPassMusterCount(this.passMusterCount);
        model.setNotLevelCount(this.notLevelCount);
        model.setDisqualificationCount(this.disqualificationCount);
        model.setEvaluatingCount(this.evaluatingCount);
        model.setSexCode(this.sexCode);
        model.setD08Code(this.d08Code);
        return model;
    }
}