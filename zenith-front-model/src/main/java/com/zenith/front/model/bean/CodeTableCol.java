package com.zenith.front.model.bean;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableField;
import java.io.Serializable;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2021-07-11
 */
@TableName("code_table_col")
public class CodeTableCol extends Model<CodeTableCol> {

    private static final long serialVersionUID=1L;

    /**
     * 唯一标识符
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 表名称
     */
    @TableField("table_code")
    private String tableCode;

    /**
     * 字段key
     */
    @TableField("col_code")
    private String colCode;

    /**
     * 字段名称
     */
    @TableField("col_name")
    private String colName;

    /**
     * 字段类型(lable)
     */
    @TableField("col_type")
    private String colType;

    /**
     * 字段指标项
     */
    @TableField("col_lection_code")
    private String colLectionCode;

    /**
     * 是否启用(1是0否)
     */
    @TableField("is_use")
    private Integer isUse;

    /**
     * 字段备注
     */
    @TableField("remark")
    private String remark;


    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getTableCode() {
        return tableCode;
    }

    public void setTableCode(String tableCode) {
        this.tableCode = tableCode;
    }

    public String getColCode() {
        return colCode;
    }

    public void setColCode(String colCode) {
        this.colCode = colCode;
    }

    public String getColName() {
        return colName;
    }

    public void setColName(String colName) {
        this.colName = colName;
    }

    public String getColType() {
        return colType;
    }

    public void setColType(String colType) {
        this.colType = colType;
    }

    public String getColLectionCode() {
        return colLectionCode;
    }

    public void setColLectionCode(String colLectionCode) {
        this.colLectionCode = colLectionCode;
    }

    public Integer getIsUse() {
        return isUse;
    }

    public void setIsUse(Integer isUse) {
        this.isUse = isUse;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    @Override
    public Serializable pkVal() {
        return this.id;
    }

    @Override
    public String toString() {
        return "CodeTableCol{" +
        "id=" + id +
        ", tableCode=" + tableCode +
        ", colCode=" + colCode +
        ", colName=" + colName +
        ", colType=" + colType +
        ", colLectionCode=" + colLectionCode +
        ", isUse=" + isUse +
        ", remark=" + remark +
        "}";
    }
}
