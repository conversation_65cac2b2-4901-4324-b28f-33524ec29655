package com.zenith.front.model.dto;

import lombok.ToString;

import java.util.Date;

/**
 * <AUTHOR>
 * @Date 2021/9/23 15:31
 * @Version 1.0
 */
@ToString
public class ProblemFeedbackDTO {
    /**
     * 唯一标识符
     */
    private String code;

    /**
     * 标题
     */
    private String title;

    /**
     * 问题简述
     */
    private String questionsBriefly;

    /**
     * 问题详情
     */
    private String problemDetails;

    /**
     * 发布人
     */
    private String userId;

    /**
     * 回复情况
     */
    private String replySituation;

    /**
     * 发布时间
     */
    private Date releaseTime;

    /**
     * 查看人数
     */
    private Long lookNumber;

    /**
     * 是否回复
     */
    private Integer memberReply;

    /**
     * 修复状态
     */
    private String stateRepair;

    /**
     * 联系方式
     */
    private String contact;

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public String getQuestionsBriefly() {
        return questionsBriefly;
    }

    public void setQuestionsBriefly(String questionsBriefly) {
        this.questionsBriefly = questionsBriefly;
    }

    public String getProblemDetails() {
        return problemDetails;
    }

    public void setProblemDetails(String problemDetails) {
        this.problemDetails = problemDetails;
    }

    public String getUserId() {
        return userId;
    }

    public void setUserId(String userId) {
        this.userId = userId;
    }

    public String getReplySituation() {
        return replySituation;
    }

    public void setReplySituation(String replySituation) {
        this.replySituation = replySituation;
    }

    public Date getReleaseTime() {
        return releaseTime;
    }

    public void setReleaseTime(Date releaseTime) {
        this.releaseTime = releaseTime;
    }

    public Long getLookNumber() {
        return lookNumber;
    }

    public void setLookNumber(Long lookNumber) {
        this.lookNumber = lookNumber;
    }

    public Integer getMemberReply() {
        return memberReply;
    }

    public void setMemberReply(Integer memberReply) {
        this.memberReply = memberReply;
    }

    public String getStateRepair() {
        return stateRepair;
    }

    public void setStateRepair(String stateRepair) {
        this.stateRepair = stateRepair;
    }

    public String getContact() {
        return contact;
    }

    public void setContact(String contact) {
        this.contact = contact;
    }
}
