package com.zenith.front.model.vo;

import lombok.Data;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @Description:
 * @date 2019/6/21 11:08
 */
@Data
public class FeeAnaVO {

    /**
     * 组织id
     */
    private String orgId;
    /***
     * 组织层级码
     * */
    private String orgCode;
    /**
     * 组织名称
     **/
    private String orgName;
    /***
     * 组织类型
     * */
    private String type;
    /***
     * 已缴纳金额
     * */
    private BigDecimal yjnCount = new BigDecimal(0);
    /**
     * 应交金额
     */
    private BigDecimal yjCount = new BigDecimal(0);
    /***
     * 今日缴纳总额
     * **/
    private BigDecimal jrjnzeCount = new BigDecimal(0);
    /**
     * 未缴纳/所有应该缴纳的百分比
     **/
    private String percent;
    /***
     * 子组织统计信息
     * */
    private List<FeeAnaVO> subOrgAna;

    public void addSubOrg(FeeAnaVO vo) {
        if (subOrgAna == null) {
            subOrgAna = new ArrayList<>();
        }
        subOrgAna.add(vo);
    }
}
