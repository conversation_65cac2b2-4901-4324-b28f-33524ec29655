package com.zenith.front.model.bean;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;

/**
 * <p>
 * excel 系统配置表
 * </p>
 *
 * <AUTHOR>
 * @since 2021-07-11
 */
@TableName("report_2020Data")
public class Report2020Data extends Model<Report2020Data> {

    private static final long serialVersionUID=1L;

    /**
     * 自增长 主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 唯一标识
     */
    @TableField("code")
    private String code;

    /**
     * 机构code
     */
    @TableField("org_code")
    private String orgCode;

    /**
     * 报表数据
     */
    @TableField("report_data")
    private String reportData;

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getOrgCode() {
        return orgCode;
    }

    public void setOrgCode(String orgCode) {
        this.orgCode = orgCode;
    }

    public String getReportData() {
        return reportData;
    }

    public void setReportData(String reportData) {
        this.reportData = reportData;
    }

    @Override
    public String toString() {
        return "Report2020Data{" +
                "id=" + id +
                ", code='" + code + '\'' +
                ", orgCode='" + orgCode + '\'' +
                ", reportData='" + reportData + '\'' +
                '}';
    }
}
