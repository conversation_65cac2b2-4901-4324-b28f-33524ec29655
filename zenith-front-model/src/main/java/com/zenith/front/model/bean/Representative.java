package com.zenith.front.model.bean;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import java.util.Date;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableField;

import java.io.Serializable;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2021-07-11
 */
@TableName("ccp_representative")
public class Representative extends Model<Representative> {

    private static final long serialVersionUID=1L;

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    @TableField("code")
    private String code;

    @TableField("es_id")
    private String esId;

    /**
     * 党员名称
     */
    @TableField("mem_name")
    private String memName;

    @TableField("mem_code")
    private String memCode;

    /**
     * 党员出生日期
     */
    @TableField("mem_birthday")
    private Date memBirthday;

    /**
     * 党员学历代码
     */
    @TableField("d07_code")
    private String d07Code;

    /**
     * 党员性别代码
     */
    @TableField("sex_code")
    private String sexCode;

    /**
     * 党员民族代码
     */
    @TableField("d06_code")
    private String d06Code;

    /**
     * 党员一线情况代码
     */
    @TableField("d21_code")
    private String d21Code;

    /**
     * 任职时所在党支部code
     */
    @TableField("representative_org_code")
    private String representativeOrgCode;

    /**
     * 任职时所在党支部层级码
     */
    @TableField("org_code")
    private String orgCode;

    @TableField("org_name")
    private String orgName;

    /**
     * 所在选举单位code
     */
    @TableField("unit_code")
    private String unitCode;

    @TableField("unit_name")
    private String unitName;

    /**
     * 任职届次开始时间
     */
    @TableField("elect_start_date")
    private Date electStartDate;

    /**
     * 任职届次code
     */
    @TableField("elect_code")
    private String electCode;

    /**
     * 任职届次名字
     */
    @TableField("elect_name")
    private String electName;

    /**
     * 任职届次类型名称(1:全国,2:市,3:区,4:县,5:乡,6:镇)
     */
    @TableField("d61_code")
    private String d61Code;

    @TableField("d61_name")
    private String d61Name;

    /**
     * 当选类型
     */
    @TableField("d44_name")
    private String d44Name;

    @TableField("d44_code")
    private String d44Code;

    /**
     * 当选时身份
     */
    @TableField("d46_name")
    private String d46Name;

    @TableField("d46_code")
    private String d46Code;

    /**
     * 当选是职务
     */
    @TableField("position_name")
    private String positionName;

    /**
     * 是否是委员
     */
    @TableField("is_member")
    private Integer isMember;

    /**
     * 是否先进模范人
     */
    @TableField("is_advanced")
    private Integer isAdvanced;

    /**
     * 是否参与各级党组织培训
     */
    @TableField("is_teach")
    private Integer isTeach;

    /**
     * 代表资格状态 1:正常 2:终止代表资格 3:停止执行党代表大会代表职务
     */
    @TableField("d45_name")
    private String d45Name;

    @TableField("d45_code")
    private String d45Code;

    /**
     * 终止资格类型
     */
    @TableField("d24_code")
    private String d24Code;

    @TableField("d24_name")
    private String d24Name;

    /**
     * 终止资格备注
     */
    @TableField("zb_stop_remark")
    private String zbStopRemark;

    /**
     * 终止资格日期
     */
    @TableField("zb_stop_date")
    private Date zbStopDate;

    /**
     * 干部管理单位(领导干部下必填)
     */
    @TableField("gb_manage_unit")
    private String gbManageUnit;

    /**
     * 当选日期
     */
    @TableField("start_date")
    private Date startDate;

    @TableField("create_time")
    private Date createTime;

    @TableField("update_time")
    private Date updateTime;

    @TableField("timestamp")
    private Date timestamp;

    @TableField("delete_time")
    private Date deleteTime;

    @TableField("zb_code")
    private String zbCode;

    @TableField("is_history")
    private Integer isHistory;

    @TableField("update_account")
    private String updateAccount;


    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getEsId() {
        return esId;
    }

    public void setEsId(String esId) {
        this.esId = esId;
    }

    public String getMemName() {
        return memName;
    }

    public void setMemName(String memName) {
        this.memName = memName;
    }

    public String getMemCode() {
        return memCode;
    }

    public void setMemCode(String memCode) {
        this.memCode = memCode;
    }

    public Date getMemBirthday() {
        return memBirthday;
    }

    public void setMemBirthday(Date memBirthday) {
        this.memBirthday = memBirthday;
    }

    public String getD07Code() {
        return d07Code;
    }

    public void setD07Code(String d07Code) {
        this.d07Code = d07Code;
    }

    public String getSexCode() {
        return sexCode;
    }

    public void setSexCode(String sexCode) {
        this.sexCode = sexCode;
    }

    public String getD06Code() {
        return d06Code;
    }

    public void setD06Code(String d06Code) {
        this.d06Code = d06Code;
    }

    public String getD21Code() {
        return d21Code;
    }

    public void setD21Code(String d21Code) {
        this.d21Code = d21Code;
    }

    public String getRepresentativeOrgCode() {
        return representativeOrgCode;
    }

    public void setRepresentativeOrgCode(String representativeOrgCode) {
        this.representativeOrgCode = representativeOrgCode;
    }

    public String getOrgCode() {
        return orgCode;
    }

    public void setOrgCode(String orgCode) {
        this.orgCode = orgCode;
    }

    public String getOrgName() {
        return orgName;
    }

    public void setOrgName(String orgName) {
        this.orgName = orgName;
    }

    public String getUnitCode() {
        return unitCode;
    }

    public void setUnitCode(String unitCode) {
        this.unitCode = unitCode;
    }

    public String getUnitName() {
        return unitName;
    }

    public void setUnitName(String unitName) {
        this.unitName = unitName;
    }

    public Date getElectStartDate() {
        return electStartDate;
    }

    public void setElectStartDate(Date electStartDate) {
        this.electStartDate = electStartDate;
    }

    public String getElectCode() {
        return electCode;
    }

    public void setElectCode(String electCode) {
        this.electCode = electCode;
    }

    public String getElectName() {
        return electName;
    }

    public void setElectName(String electName) {
        this.electName = electName;
    }

    public String getD61Code() {
        return d61Code;
    }

    public void setD61Code(String d61Code) {
        this.d61Code = d61Code;
    }

    public String getD61Name() {
        return d61Name;
    }

    public void setD61Name(String d61Name) {
        this.d61Name = d61Name;
    }

    public String getD44Name() {
        return d44Name;
    }

    public void setD44Name(String d44Name) {
        this.d44Name = d44Name;
    }

    public String getD44Code() {
        return d44Code;
    }

    public void setD44Code(String d44Code) {
        this.d44Code = d44Code;
    }

    public String getD46Name() {
        return d46Name;
    }

    public void setD46Name(String d46Name) {
        this.d46Name = d46Name;
    }

    public String getD46Code() {
        return d46Code;
    }

    public void setD46Code(String d46Code) {
        this.d46Code = d46Code;
    }

    public String getPositionName() {
        return positionName;
    }

    public void setPositionName(String positionName) {
        this.positionName = positionName;
    }

    public Integer getIsMember() {
        return isMember;
    }

    public void setIsMember(Integer isMember) {
        this.isMember = isMember;
    }

    public Integer getIsAdvanced() {
        return isAdvanced;
    }

    public void setIsAdvanced(Integer isAdvanced) {
        this.isAdvanced = isAdvanced;
    }

    public Integer getIsTeach() {
        return isTeach;
    }

    public void setIsTeach(Integer isTeach) {
        this.isTeach = isTeach;
    }

    public String getD45Name() {
        return d45Name;
    }

    public void setD45Name(String d45Name) {
        this.d45Name = d45Name;
    }

    public String getD45Code() {
        return d45Code;
    }

    public void setD45Code(String d45Code) {
        this.d45Code = d45Code;
    }

    public String getD24Code() {
        return d24Code;
    }

    public void setD24Code(String d24Code) {
        this.d24Code = d24Code;
    }

    public String getD24Name() {
        return d24Name;
    }

    public void setD24Name(String d24Name) {
        this.d24Name = d24Name;
    }

    public String getZbStopRemark() {
        return zbStopRemark;
    }

    public void setZbStopRemark(String zbStopRemark) {
        this.zbStopRemark = zbStopRemark;
    }

    public Date getZbStopDate() {
        return zbStopDate;
    }

    public void setZbStopDate(Date zbStopDate) {
        this.zbStopDate = zbStopDate;
    }

    public String getGbManageUnit() {
        return gbManageUnit;
    }

    public void setGbManageUnit(String gbManageUnit) {
        this.gbManageUnit = gbManageUnit;
    }

    public Date getStartDate() {
        return startDate;
    }

    public void setStartDate(Date startDate) {
        this.startDate = startDate;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    public Date getTimestamp() {
        return timestamp;
    }

    public void setTimestamp(Date timestamp) {
        this.timestamp = timestamp;
    }

    public Date getDeleteTime() {
        return deleteTime;
    }

    public void setDeleteTime(Date deleteTime) {
        this.deleteTime = deleteTime;
    }

    public String getZbCode() {
        return zbCode;
    }

    public void setZbCode(String zbCode) {
        this.zbCode = zbCode;
    }

    public Integer getIsHistory() {
        return isHistory;
    }

    public void setIsHistory(Integer isHistory) {
        this.isHistory = isHistory;
    }

    public String getUpdateAccount() {
        return updateAccount;
    }

    public void setUpdateAccount(String updateAccount) {
        this.updateAccount = updateAccount;
    }

    @Override
    public Serializable pkVal() {
        return this.id;
    }

    @Override
    public String toString() {
        return "Representative{" +
        "id=" + id +
        ", code=" + code +
        ", esId=" + esId +
        ", memName=" + memName +
        ", memCode=" + memCode +
        ", memBirthday=" + memBirthday +
        ", d07Code=" + d07Code +
        ", sexCode=" + sexCode +
        ", d06Code=" + d06Code +
        ", d21Code=" + d21Code +
        ", representativeOrgCode=" + representativeOrgCode +
        ", orgCode=" + orgCode +
        ", orgName=" + orgName +
        ", unitCode=" + unitCode +
        ", unitName=" + unitName +
        ", electStartDate=" + electStartDate +
        ", electCode=" + electCode +
        ", electName=" + electName +
        ", d61Code=" + d61Code +
        ", d61Name=" + d61Name +
        ", d44Name=" + d44Name +
        ", d44Code=" + d44Code +
        ", d46Name=" + d46Name +
        ", d46Code=" + d46Code +
        ", positionName=" + positionName +
        ", isMember=" + isMember +
        ", isAdvanced=" + isAdvanced +
        ", isTeach=" + isTeach +
        ", d45Name=" + d45Name +
        ", d45Code=" + d45Code +
        ", d24Code=" + d24Code +
        ", d24Name=" + d24Name +
        ", zbStopRemark=" + zbStopRemark +
        ", zbStopDate=" + zbStopDate +
        ", gbManageUnit=" + gbManageUnit +
        ", startDate=" + startDate +
        ", createTime=" + createTime +
        ", updateTime=" + updateTime +
        ", timestamp=" + timestamp +
        ", deleteTime=" + deleteTime +
        ", zbCode=" + zbCode +
        ", isHistory=" + isHistory +
        ", updateAccount=" + updateAccount +
        "}";
    }
}
