package com.zenith.front.model.dto;

import com.zenith.front.model.bean.MemExtend;
import lombok.*;

@ToString
public class MemExtendDTO{

    private Long id;
    private String code;
    private String memCode;
    private String extendOrgCode;
    private String orgZbCode;
    private String orgCode;
    private Integer villageZbwy;
    private Integer villageZbsj;
    private Integer villageCwwy;
    private Integer villageCwzr;
    private Integer communityZbwy;
    private Integer communityZbsj;
    private Integer communityCwwy;
    private Integer communityCwzr;

    public MemExtendDTO setId(Long id){
        this.id = id;
        return this;
    }
    public Long getId() {
    	return this.id;
    }
    public MemExtendDTO setCode(String code){
        this.code = code;
        return this;
    }
    public String getCode() {
    	return this.code;
    }
    public MemExtendDTO setMemCode(String memCode){
        this.memCode = memCode;
        return this;
    }
    public String getMemCode() {
    	return this.memCode;
    }
    public MemExtendDTO setExtendOrgCode(String extendOrgCode){
        this.extendOrgCode = extendOrgCode;
        return this;
    }
    public String getExtendOrgCode() {
    	return this.extendOrgCode;
    }
    public MemExtendDTO setOrgZbCode(String orgZbCode){
        this.orgZbCode = orgZbCode;
        return this;
    }
    public String getOrgZbCode() {
    	return this.orgZbCode;
    }
    public MemExtendDTO setOrgCode(String orgCode){
        this.orgCode = orgCode;
        return this;
    }
    public String getOrgCode() {
    	return this.orgCode;
    }
    public MemExtendDTO setVillageZbwy(Integer villageZbwy){
        this.villageZbwy = villageZbwy;
        return this;
    }
    public Integer getVillageZbwy() {
    	return this.villageZbwy;
    }
    public MemExtendDTO setVillageZbsj(Integer villageZbsj){
        this.villageZbsj = villageZbsj;
        return this;
    }
    public Integer getVillageZbsj() {
    	return this.villageZbsj;
    }
    public MemExtendDTO setVillageCwwy(Integer villageCwwy){
        this.villageCwwy = villageCwwy;
        return this;
    }
    public Integer getVillageCwwy() {
    	return this.villageCwwy;
    }
    public MemExtendDTO setVillageCwzr(Integer villageCwzr){
        this.villageCwzr = villageCwzr;
        return this;
    }
    public Integer getVillageCwzr() {
    	return this.villageCwzr;
    }
    public MemExtendDTO setCommunityZbwy(Integer communityZbwy){
        this.communityZbwy = communityZbwy;
        return this;
    }
    public Integer getCommunityZbwy() {
    	return this.communityZbwy;
    }
    public MemExtendDTO setCommunityZbsj(Integer communityZbsj){
        this.communityZbsj = communityZbsj;
        return this;
    }
    public Integer getCommunityZbsj() {
    	return this.communityZbsj;
    }
    public MemExtendDTO setCommunityCwwy(Integer communityCwwy){
        this.communityCwwy = communityCwwy;
        return this;
    }
    public Integer getCommunityCwwy() {
    	return this.communityCwwy;
    }
    public MemExtendDTO setCommunityCwzr(Integer communityCwzr){
        this.communityCwzr = communityCwzr;
        return this;
    }
    public Integer getCommunityCwzr() {
    	return this.communityCwzr;
    }


    public MemExtend toModel(){
        MemExtend model = new MemExtend();
        model.setId(this.id);
        model.setCode(this.code);
        model.setMemCode(this.memCode);
        model.setExtendOrgCode(this.extendOrgCode);
        model.setOrgZbCode(this.orgZbCode);
        model.setOrgCode(this.orgCode);
        model.setVillageZbwy(this.villageZbwy);
        model.setVillageZbsj(this.villageZbsj);
        model.setVillageCwwy(this.villageCwwy);
        model.setVillageCwzr(this.villageCwzr);
        model.setCommunityZbwy(this.communityZbwy);
        model.setCommunityZbsj(this.communityZbsj);
        model.setCommunityCwwy(this.communityCwwy);
        model.setCommunityCwzr(this.communityCwzr);
        return model;
    }
}