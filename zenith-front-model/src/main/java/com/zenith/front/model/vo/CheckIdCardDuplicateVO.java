package com.zenith.front.model.vo;

import com.zenith.front.common.encrypt.annotation.EncryptEnabled;
import com.zenith.front.common.encrypt.annotation.EncryptField;

import java.io.Serializable;

/**
 * <AUTHOR>
 */
@EncryptEnabled
public class CheckIdCardDuplicateVO implements Serializable {

    private static final long serialVersionUID = -3544875813320809070L;

    private String code;

    @EncryptField(order = 1)
    private String name;

    private String orgName;

    @EncryptField(order = 2)
    private String idcard;

    @EncryptField(order = 3)
    private String contactPhone;

    private String type;

    public String getContactPhone() {
        return contactPhone;
    }

    public void setContactPhone(String contactPhone) {
        this.contactPhone = contactPhone;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getOrgName() {
        return orgName;
    }

    public void setOrgName(String orgName) {
        this.orgName = orgName;
    }

    public String getIdcard() {
        return idcard;
    }

    public void setIdcard(String idcard) {
        this.idcard = idcard;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    @Override
    public String toString() {
        return "CheckIdCardDuplicateVO{" +
                "code='" + code + '\'' +
                ", name='" + name + '\'' +
                ", orgName='" + orgName + '\'' +
                ", idcard='" + idcard + '\'' +
                ", contactPhone='" + contactPhone + '\'' +
                ", type='" + type + '\'' +
                '}';
    }
}
