package com.zenith.front.model.bean;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableField;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2021-11-16
 */
@TableName("ccp_mem_logic")
public class MemLogic extends Model<MemLogic> {

    private static final long serialVersionUID = 1L;

    /**
     * 唯一标识符
     */
    @TableId(value = "id", type = IdType.ASSIGN_UUID)
    private String id;

    /**
     * 唯一主键
     */
    @TableField("code")
    private String code;

    /**
     * 机构主键
     */
    @TableField("org_code")
    private String orgCode;

    /**
     * 机构层级码
     */
    @TableField("org_org_code")
    private String orgOrgCode;

    /**
     * 表名
     */
    @TableField("table_code")
    private String tableCode;

    /**
     * 字段名
     */
    @TableField("col_code")
    private String colCode;

    /**
     * 字段值
     */
    @TableField("col_value")
    private String colValue;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getOrgCode() {
        return orgCode;
    }

    public void setOrgCode(String orgCode) {
        this.orgCode = orgCode;
    }

    public String getOrgOrgCode() {
        return orgOrgCode;
    }

    public void setOrgOrgCode(String orgOrgCode) {
        this.orgOrgCode = orgOrgCode;
    }

    public String getTableCode() {
        return tableCode;
    }

    public void setTableCode(String tableCode) {
        this.tableCode = tableCode;
    }

    public String getColCode() {
        return colCode;
    }

    public void setColCode(String colCode) {
        this.colCode = colCode;
    }

    public String getColValue() {
        return colValue;
    }

    public void setColValue(String colValue) {
        this.colValue = colValue;
    }

    @Override
    public String toString() {
        return "MemLogic{" +
                "id='" + id + '\'' +
                ", code='" + code + '\'' +
                ", orgCode='" + orgCode + '\'' +
                ", orgOrgCode='" + orgOrgCode + '\'' +
                ", tableCode='" + tableCode + '\'' +
                ", colCode='" + colCode + '\'' +
                ", colValue='" + colValue + '\'' +
                '}';
    }
}
