package com.zenith.front.model.bean;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import lombok.Data;

import java.util.Date;

/**
 * 报表单元格解释表
 * <AUTHOR>
 * @create_date 2025-02-07 14:18
 * @description
 */
@TableName(value = "report_rule_config_explain")
@Data
public class ReportRuleConfigExplain extends Model<ReportRuleConfigExplain> {

    @TableId(value = "id", type = IdType.ASSIGN_UUID)
    private String id;

    /**
     * 报表CODE
     */
    @TableField("report_code")
    private String reportCode;

    /**
     * table0（统计项）、replenish（补充资料）
     */
    @TableField("table_type")
    private String tableType;

    /**
     * 报表单元格坐标
     */
    @TableField("cell")
    private String cell;

    /**
     * 单元格内容解释
     */
    @TableField("explain")
    private String explain;

    /**
     * 修改时间
     */
    @TableField("update_time")
    private Date updateTime;

    /**
     * 创建时间
     */
    @TableField("create_time")
    private Date createTime;

    /**
     * 反查显示列配置
     */
    @TableField("include_field_list")
    private String includeFieldList;


    /**
     * 表名  类似于ccp_mem_all
     */
    @TableField("report_type")
    private String reportType;
}
