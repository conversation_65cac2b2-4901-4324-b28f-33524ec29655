package com.zenith.front.model.dto;

import com.zenith.front.model.bean.RoleType;
import lombok.*;

@ToString
public class RoleTypeDTO{

    private Integer id;
   	/**
   	 * 角色类型
   	 */
    private Integer type;
   	/**
   	 * 描述信息
   	 */
    private String des;

    public RoleTypeDTO setId(Integer id){
        this.id = id;
        return this;
    }
    public Integer getId() {
    	return this.id;
    }
    public RoleTypeDTO setType(Integer type){
        this.type = type;
        return this;
    }
    public Integer getType() {
    	return this.type;
    }
    public RoleTypeDTO setDes(String des){
        this.des = des;
        return this;
    }
    public String getDes() {
    	return this.des;
    }


    public RoleType toModel(){
        RoleType model = new RoleType();
        model.setId(this.id);
        model.setType(this.type);
        model.setDes(this.des);
        return model;
    }
}