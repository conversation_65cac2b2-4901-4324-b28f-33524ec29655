package com.zenith.front.model.dto;
import com.zenith.front.model.bean.Dues;
import lombok.Data;
import lombok.ToString;

import java.util.Date;
import java.util.List;

/**
 * @author: D.watermelon
 * @date: 2023/2/10 16:08
 * @description: 入则恳恳以尽忠 出则谦谦以自悔
 */

@ToString
@Data
public class DuesData {

    /**
     * 人员code
     */
    private String memCode;
    /**
     * 人员所在党组织名称
     */
    private String orgName;

    /**
     * 人员名称
     */
    private String memName;

    /**
     * 人员数据情况
     */
    private List<Dues> data;

    /**
     * 人员身份证
     * */
    private String idCard;

    /**
     * 党员起交
     * */
    private Date startPayDate;

    /**
     * 党员最后缴费
     * */
    private Date lastPayDate;

    /**
     *
     * 少交原因/免交原因
     */
    private String remark;



}
