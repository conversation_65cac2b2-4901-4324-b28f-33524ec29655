package com.zenith.front.model.bean;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import java.util.Date;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableField;
import java.io.Serializable;

/**
 * <p>
 * 通知表，会通过消息表ccp_message进行单个拆分到次表
 * </p>
 *
 * <AUTHOR>
 * @since 2021-07-11
 */
@TableName("ccp_message_notice")
public class MessageNotice extends Model<MessageNotice> {

    private static final long serialVersionUID=1L;

    /**
     *  自增长主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 消息通知主键
     */
    @TableField("code")
    private String code;

    /**
     * 回复内容
     */
    @TableField("reply_context")
    private String replyContext;

    /**
     * 是否查看
     */
    @TableField("is_select")
    private String isSelect;

    /**
     * 是否再次发送通知
     */
    @TableField("notice_plan")
    private Integer noticePlan;

    /**
     * 回复文件
     */
    @TableField("file")
    private String file;

    /**
     * 通知时间
     */
    @TableField("time")
    private Date time;

    /**
     * 消息code
     */
    @TableField("message_code")
    private String messageCode;

    /**
     * 再次发送通知的消息code
     */
    @TableField("notice_message_code")
    private String noticeMessageCode;

    /**
     * 消息类别
     */
    @TableField("message_type")
    private Integer messageType;

    /**
     * 接收人code[如果是组织，则是组织的层级码，如果是人员则是人员的唯一标识符]
     */
    @TableField("receive_code")
    private String receiveCode;

    /**
     * 接收人组织唯一标识符
     */
    @TableField("receive_org_code")
    private String receiveOrgCode;

    /**
     * 创建时间
     */
    @TableField("create_time")
    private Date createTime;

    /**
     * 更新时间
     */
    @TableField("update_time")
    private Date updateTime;

    /**
     * 删除时间
     */
    @TableField("delete_time")
    private Date deleteTime;


    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getReplyContext() {
        return replyContext;
    }

    public void setReplyContext(String replyContext) {
        this.replyContext = replyContext;
    }

    public String getIsSelect() {
        return isSelect;
    }

    public void setIsSelect(String isSelect) {
        this.isSelect = isSelect;
    }

    public Integer getNoticePlan() {
        return noticePlan;
    }

    public void setNoticePlan(Integer noticePlan) {
        this.noticePlan = noticePlan;
    }

    public String getFile() {
        return file;
    }

    public void setFile(String file) {
        this.file = file;
    }

    public Date getTime() {
        return time;
    }

    public void setTime(Date time) {
        this.time = time;
    }

    public String getMessageCode() {
        return messageCode;
    }

    public void setMessageCode(String messageCode) {
        this.messageCode = messageCode;
    }

    public String getNoticeMessageCode() {
        return noticeMessageCode;
    }

    public void setNoticeMessageCode(String noticeMessageCode) {
        this.noticeMessageCode = noticeMessageCode;
    }

    public Integer getMessageType() {
        return messageType;
    }

    public void setMessageType(Integer messageType) {
        this.messageType = messageType;
    }

    public String getReceiveCode() {
        return receiveCode;
    }

    public void setReceiveCode(String receiveCode) {
        this.receiveCode = receiveCode;
    }

    public String getReceiveOrgCode() {
        return receiveOrgCode;
    }

    public void setReceiveOrgCode(String receiveOrgCode) {
        this.receiveOrgCode = receiveOrgCode;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    public Date getDeleteTime() {
        return deleteTime;
    }

    public void setDeleteTime(Date deleteTime) {
        this.deleteTime = deleteTime;
    }

    @Override
    public Serializable pkVal() {
        return this.id;
    }

    @Override
    public String toString() {
        return "MessageNotice{" +
        "id=" + id +
        ", code=" + code +
        ", replyContext=" + replyContext +
        ", isSelect=" + isSelect +
        ", noticePlan=" + noticePlan +
        ", file=" + file +
        ", time=" + time +
        ", messageCode=" + messageCode +
        ", noticeMessageCode=" + noticeMessageCode +
        ", messageType=" + messageType +
        ", receiveCode=" + receiveCode +
        ", receiveOrgCode=" + receiveOrgCode +
        ", createTime=" + createTime +
        ", updateTime=" + updateTime +
        ", deleteTime=" + deleteTime +
        "}";
    }
}
