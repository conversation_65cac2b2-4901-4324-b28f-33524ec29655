package com.zenith.front.model.dto;

import com.zenith.front.model.validate.group.Common1Group;
import com.zenith.front.model.validate.group.Common4Group;
import lombok.ToString;

import javax.validation.constraints.Max;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotBlank;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2021/10/11 15:15
 * @Version 1.0
 */
@ToString
public class MemFlowListDTO {
    /**
     * 组织层级码
     */
    @NotBlank(groups = Common1Group.class, message = "memOrgCode 不能为空,1--根据组织树搜索,0--全市搜索")
    private String orgCode;
    /**
     * 工作岗位
     */
    private List<String> d09CodeList;
    /**
     * 原因类型
     */
    private List<String> d41CodeList;
    /**
     * 人员学历
     */
    private List<String> d07CodeList;
    /**
     * 性别,1--男,0--女
     */
    private List<String> sexCodeList;
    /**
     * 搜索框,按名称搜索
     */
    private String memName;

    private String subordinate;

    /**
     * 分页页数
     */
    @Min(value = 1, groups = {Common4Group.class, Common1Group.class}, message = "页码最小为1")
    private Integer pageNum;
    /**
     * 分页每页数
     */
    @Max(value = 100, groups = {Common4Group.class, Common1Group.class}, message = "每页条数大小范围在1-100")
    private Integer pageSize;

    private Integer status;

    public String getSubordinate() {
        return subordinate;
    }

    public void setSubordinate(String subordinate) {
        this.subordinate = subordinate;
    }

    public String getOrgCode() {
        return orgCode;
    }

    public void setOrgCode(String orgCode) {
        this.orgCode = orgCode;
    }

    public List<String> getD09CodeList() {
        return d09CodeList;
    }

    public void setD09CodeList(List<String> d09CodeList) {
        this.d09CodeList = d09CodeList;
    }

    public List<String> getD41CodeList() {
        return d41CodeList;
    }

    public void setD41CodeList(List<String> d41CodeList) {
        this.d41CodeList = d41CodeList;
    }

    public List<String> getD07CodeList() {
        return d07CodeList;
    }

    public void setD07CodeList(List<String> d07CodeList) {
        this.d07CodeList = d07CodeList;
    }

    public List<String> getSexCodeList() {
        return sexCodeList;
    }

    public void setSexCodeList(List<String> sexCodeList) {
        this.sexCodeList = sexCodeList;
    }

    public String getMemName() {
        return memName;
    }

    public void setMemName(String memName) {
        this.memName = memName;
    }

    public Integer getPageNum() {
        return pageNum;
    }

    public void setPageNum(Integer pageNum) {
        this.pageNum = pageNum;
    }

    public Integer getPageSize() {
        return pageSize;
    }

    public void setPageSize(Integer pageSize) {
        this.pageSize = pageSize;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }
}
