package com.zenith.front.model.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @Date 2021/8/13 18:24
 * @Version 1.0
 */
@Data
@ToString
@NoArgsConstructor
@AllArgsConstructor
public class Zt1RuralPartyDTO {
    /**
     * 主键
     */
    private String code;

    /**
     * 单位code
     */
    private String unitCode;

    /**
     * 建档立卡贫困村数(0：否  1：是)
     */
    private Integer hasFiling;

    /**
     * 累计选派第一书记(0：否  1：是)
     */
    private Integer hasAppointment;

    /**
     * 现任第一书记(0：否  1：是)
     */
    private Integer hasCurrent;

    /**
     * 本年各级培训第一书记(人次）
     */
    private Long trainFirstSecretary;

    /**
     * 为第一书记安排不低于1万元工作经费的村 (0：否  1：是)
     */
    private Integer hasLowOneWan;

    /**
     * 派出单位落实责任、项目、资金捆绑的村(0：否  1：是)
     */
    private Integer hasWorkable;

    /**
     * 提拔使用或晋级的第一书记(0：否  1：是)
     */
    private Integer hasPromotePromotion;

    /**
     * 因工作不胜任召回调整的第一书记(0：否  1：是)
     */
    private Integer hasRecallAdjust;

    /**
     * 平均每村运转经费（万元∕年）
     */
    private BigDecimal operatingFunds;

    /**
     * 平均每村办公经费（万元∕年）
     */
    private BigDecimal officeExpenses;

    /**
     * 村党组织书记平均报酬（万元∕年）
     */
    private BigDecimal villageReward;

    /**
     * 村干部基本报酬和村级组织办公经费合计低于11万元的县（市、区、旗）(0：否  1：是)
     */
    private Integer hasRemunerationOrganization;

    /**
     * 落实正常离任村干部生活补贴的县（市、区、旗）(0：否  1：是)
     */
    private Integer hasPracticableSubsidies;

    /**
     * 落实农村公共服务运行维护支出或服务群众专项经费的县（市、区、旗）(0：否  1：是)
     */
    private Integer hasPracticableMaintain;

    /**
     * 落实村民小组长误工补贴的县（市、区、旗）(0：否  1：是)
     */
    private Integer hasPracticableDelaysSubsidies;

    /**
     * 暂无活动场所的行政村(0：否  1：是)
     */
    private Integer hasActivities;

    /**
     * 活动场所面积200㎡以上的行政村(0：否  1：是)
     */
    private Integer hasTowBaiAbove;

    /**
     * 本年新建或改扩建活动场所数量
     */
    private Long buildSites;

    /**
     * 未完成“五小”建设的乡镇(0：否  1：是)
     */
    private Integer hasCompleteFiveMin;
}
