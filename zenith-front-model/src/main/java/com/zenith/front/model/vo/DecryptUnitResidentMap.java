package com.zenith.front.model.vo;

import com.zenith.front.common.encrypt.annotation.EncryptEnabled;
import com.zenith.front.common.encrypt.annotation.EncryptField;
import lombok.Getter;
import lombok.Setter;

import java.util.LinkedHashMap;

/**
 * 兼容返回类型为map时数据解密,驻村干部
 *
 * <AUTHOR>
 * @version V1.0
 * @date 2022/10/21 16:10
 */
@EncryptEnabled
@Setter
@Getter
public class DecryptUnitResidentMap<K, V> extends LinkedHashMap<K, V> {
    @EncryptField(order = 1)
    private String mem_name;
    @EncryptField(order = 2)
    private String mem_idcard;
    @EncryptField(order = 2)
    private String idcard;

}
