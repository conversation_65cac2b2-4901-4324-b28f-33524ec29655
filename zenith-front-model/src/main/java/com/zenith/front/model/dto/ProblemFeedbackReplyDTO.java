package com.zenith.front.model.dto;

import lombok.ToString;

/**
 * <AUTHOR>
 * @Date 2021/10/20 16:41
 * @Version 1.0
 */
@ToString
public class ProblemFeedbackReplyDTO {


    private ProblemFeedbackReplyDTO message;

    private String code;

    /**
     * 问题反馈主键
     */
    private String problemId;

    /**
     * 留言人id
     */
    private String userId;

    /**
     * 留言人姓名
     */
    private String userName;

    /**
     * 内容
     */
    private String content;

    /**
     * 父id
     */
    private String itemId;

    /**
     * 上级回复id
     */
    private String replySupId;

    /**
     * 回复人id
     */
    private String replyUserId;

    /**
     * 回复人姓名
     */
    private String replyUserName;

    private String rootCode;

    public String getRootCode() {
        return rootCode;
    }

    public void setRootCode(String rootCode) {
        this.rootCode = rootCode;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getProblemId() {
        return problemId;
    }

    public void setProblemId(String problemId) {
        this.problemId = problemId;
    }

    public String getUserId() {
        return userId;
    }

    public void setUserId(String userId) {
        this.userId = userId;
    }

    public String getUserName() {
        return userName;
    }

    public void setUserName(String userName) {
        this.userName = userName;
    }

    public String getContent() {
        return content;
    }

    public void setContent(String content) {
        this.content = content;
    }

    public String getItemId() {
        return itemId;
    }

    public void setItemId(String itemId) {
        this.itemId = itemId;
    }

    public String getReplySupId() {
        return replySupId;
    }

    public void setReplySupId(String replySupId) {
        this.replySupId = replySupId;
    }

    public String getReplyUserId() {
        return replyUserId;
    }

    public void setReplyUserId(String replyUserId) {
        this.replyUserId = replyUserId;
    }

    public String getReplyUserName() {
        return replyUserName;
    }

    public void setReplyUserName(String replyUserName) {
        this.replyUserName = replyUserName;
    }

    public ProblemFeedbackReplyDTO getMessage() {
        return message;
    }

    public void setMessage(ProblemFeedbackReplyDTO message) {
        this.message = message;
    }
}
