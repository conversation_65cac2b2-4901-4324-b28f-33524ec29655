package com.zenith.front.model.dto;

import com.zenith.front.model.validate.group.AddGroup;

import javax.validation.constraints.NotBlank;

/**
 * 专题调查表十二
 *
 * <AUTHOR>
 * @date 2021/08/13
 */
public class Zt12InternetEnterprisDto {

    /**
     * 单位code
     */
    @NotBlank(groups = AddGroup.class, message = "unitCode 不能为空")
    private String unitCode;

    /**
     * 建立联合党支部数
     */
    private Integer buildUniteBranchNum;

    /**
     * 本科以上学历（在岗职工）
     */
    private Integer aboveBachelorNum;

    /**
     * 研究生以上学历（在岗职工）
     */
    private Integer aboveGraduateNum;

    /**
     * 党员数
     */
    private Integer partyNum;

    /**
     * 本年度发展的
     */
    private Integer currentYearDevelopNum;

    /**
     * 未转组织关系党员人数
     */
    private Integer notTurnedRelationNum;

    /**
     * 党组织书记
     */
    private Integer secretaryNum;

    /**
     * 由企业中高层管理人员担任的
     */
    private Integer secretaryHoldMiddleManagerNum;

    /**
     * 企业主要负责人是党员的（0否，1是）
     */
    private Integer isPrincipalParty;


    public String getUnitCode() {
        return unitCode;
    }

    public void setUnitCode(String unitCode) {
        this.unitCode = unitCode;
    }

    public Integer getBuildUniteBranchNum() {
        return buildUniteBranchNum;
    }

    public void setBuildUniteBranchNum(Integer buildUniteBranchNum) {
        this.buildUniteBranchNum = buildUniteBranchNum;
    }

    public Integer getAboveBachelorNum() {
        return aboveBachelorNum;
    }

    public void setAboveBachelorNum(Integer aboveBachelorNum) {
        this.aboveBachelorNum = aboveBachelorNum;
    }

    public Integer getAboveGraduateNum() {
        return aboveGraduateNum;
    }

    public void setAboveGraduateNum(Integer aboveGraduateNum) {
        this.aboveGraduateNum = aboveGraduateNum;
    }

    public Integer getPartyNum() {
        return partyNum;
    }

    public void setPartyNum(Integer partyNum) {
        this.partyNum = partyNum;
    }

    public Integer getCurrentYearDevelopNum() {
        return currentYearDevelopNum;
    }

    public void setCurrentYearDevelopNum(Integer currentYearDevelopNum) {
        this.currentYearDevelopNum = currentYearDevelopNum;
    }

    public Integer getNotTurnedRelationNum() {
        return notTurnedRelationNum;
    }

    public void setNotTurnedRelationNum(Integer notTurnedRelationNum) {
        this.notTurnedRelationNum = notTurnedRelationNum;
    }

    public Integer getSecretaryNum() {
        return secretaryNum;
    }

    public void setSecretaryNum(Integer secretaryNum) {
        this.secretaryNum = secretaryNum;
    }

    public Integer getSecretaryHoldMiddleManagerNum() {
        return secretaryHoldMiddleManagerNum;
    }

    public void setSecretaryHoldMiddleManagerNum(Integer secretaryHoldMiddleManagerNum) {
        this.secretaryHoldMiddleManagerNum = secretaryHoldMiddleManagerNum;
    }

    public Integer getIsPrincipalParty() {
        return isPrincipalParty;
    }

    public void setIsPrincipalParty(Integer isPrincipalParty) {
        this.isPrincipalParty = isPrincipalParty;
    }


}
