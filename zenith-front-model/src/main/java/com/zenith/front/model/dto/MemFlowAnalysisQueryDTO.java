package com.zenith.front.model.dto;

import com.zenith.front.model.custom.HasSubordinateField;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.util.Date;
import java.util.List;
import java.util.Set;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2022-11-14
 */
@Setter
@Getter
@ToString
public class MemFlowAnalysisQueryDTO extends HasSubordinateField {

    /**
     * 组织code
     */
    private String orgCode;
    /**
     * 组织名称
     */
    private String orgName;

    /**
     * 是否包含下级
     */
    private Boolean below;

    /**
     * 分页页数
     */
    private Integer pageNum;
    /**
     * 分页每页数
     */
    private Integer pageSize;

    /**
     * 流出或流入 默认流出 1 流入 2
     */
    private String flow;

    /**
     * 姓名(多个用逗号,分割)
     */
    private String memName;

    /**
     * 姓名(多个用逗号,分割)
     */
    private Set<String> memNameList;

    /**
     * 失去联系情形
     */
    private String lostContactCode;

    /**
     * 失去联系情形(多个用逗号,分割)
     */
    private Set<String> lostContactCodeList;

    /**
     * 党员性别
     */
    private String memSexCode;

    /**
     * 党员性别(多个用逗号,分割)
     */
    private Set<String> memSexCodeList;

    /**
     * 参与组织生活情况
     */
    private String inOrgLifeCode;

    /**
     * 参与组织生活情况(多个用逗号,分割)
     */
    private Set<String> inOrgLifeCodeList;

    /**
     * 外出日期
     */
    private String outTimeStart;

    /**
     * 外出日期
     */
    private Date outTimeStarts;

    /**
     * 外出日期
     */
    private String outTimeEnd;

    /**
     * 外出日期
     */
    private Date outTimeEnds;

    /**
     * 人员工作岗位
     */
    private String memD09Code;

    /**
     * 人员工作岗位(多个用逗号,分割)
     */
    private Set<String> memD09CodeList;

    /**
     * 流回时间
     */
    private String flowBackTimeStart;

    /**
     * 流回时间
     */
    private String flowBackTimeEnd;

    /**
     * 流回时间
     */
    private Date flowBackTimeStarts;

    /**
     * 流回时间
     */
    private Date flowBackTimeEnds;

    /**
     * 流出地党组织单位类别
     */
    private String outOrgD04Code;

    /**
     * 流出地党组织单位类别(多个用逗号,分割)
     */
    private Set<String> outOrgD04CodeList;

    /**
     * 流入地党组织单位类别
     */
    private String inOrgD04Code;

    /**
     * 流入地党组织单位类别(多个用逗号,分割)
     */
    private Set<String> inOrgD04CodeList;

    /**
     * 流动原因
     */
    private String flowReasonCode;

    /**
     * 流动原因(多个用逗号,分割)
     */
    private Set<String> flowReasonCodeList;

    /**
     * 流动类型
     */
    private String flowTypeCode;

    /**
     * 流动类型(多个用逗号,分割)
     */
    private Set<String> flowTypeCodeList;

    /**
     * 管理情况 1 未纳入支部 2 已纳入支部
     */
    private String flowStep;

    /**
     * 管理情况(多个用逗号,分割)
     */
    private Set<String> flowStepList;

    /**
     * 导出
     */
    private List<ExportDTO> exportList;
}
