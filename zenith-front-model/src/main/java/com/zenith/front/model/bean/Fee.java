package com.zenith.front.model.bean;

import java.math.BigDecimal;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import java.util.Date;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableField;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.postgresql.util.PGobject;

import java.io.Serializable;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2021-07-11
 */
@TableName("ccp_fee")
public class Fee extends Model<Fee> {

    private static final long serialVersionUID=1L;

    /**
     * 党费标准自增长ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 党费标准唯一标识符
     */
    @TableField("code")
    private String code;

    /**
     * 党员code
     */
    @TableField("mem_code")
    private String memCode;

    /**
     * 人员当前所在组织唯一标识符
     */
    @TableField("mem_org_code")
    private String memOrgCode;

    /**
     * 人员当前所在组织层级码
     */
    @TableField("mem_org_org_code")
    private String memOrgOrgCode;

    /**
     * 标准所属年份
     */
    @TableField("year")
    private String year;

    /**
     * 标准所属月份
     */
    @TableField("month")
    private String month;

    /**
     * 缴纳标准
     */
    @TableField("standard")
    private BigDecimal standard;

    /**
     * 缴纳类型(支付类型 1:手机支付  2: PC支付,3-其他)
     */
    @TableField("pay_type")
    private String payType;

    /**
     * 缴纳时所在组织唯一标识符
     */
    @TableField("pay_org_code")
    private String payOrgCode;

    /**
     * 缴纳时所在组织层级码
     */
    @TableField("pay_org_org_code")
    private String payOrgOrgCode;

    /**
     * 支付时所在组织标识符集合
     */
    @TableField("pay_org_code_set")
    private String payOrgCodeSet;

    /**
     * 设置时所在组织层级码
     */
    @TableField("setting_org_org_code")
    private String settingOrgOrgCode;

    /**
     * 设置时所在组织唯一标识符
     */
    @TableField("setting_org_code")
    private String settingOrgCode;

    /**
     * 设置时所在组织标识符集合
     */
    @TableField("setting_org_code_set")
    private Object settingOrgCodeSet;

    /**
     * 缴纳金额
     */
    @TableField("pay_money")
    private BigDecimal payMoney;

    /**
     * 更新账号
     */
    @TableField("update_account")
    private String updateAccount;

    /**
     * 标准所属季度
     */
    @TableField("season")
    private String season;

    /**
     * 备注
     */
    @TableField("remark")
    private String remark;

    /**
     * 是否忽略（统计类别使用，例如离退休党员要交但是可能不纳入统计等,1--忽略,0--不忽略）
     */
    @TableField("is_ignore")
    private Integer isIgnore;

    /**
     * 党费基数
     */
    @TableField("base")
    private BigDecimal base;

    /**
     * 计算类型code
     */
    @TableField("d49_code")
    private String d49Code;

    /**
     * 计算类型名称
     */
    @TableField("d49_name")
    private String d49Name;

    /**
     * 批准少，或者免交原因
     */
    @TableField("reason")
    private String reason;

    /**
     * 标准缴纳订单号
     */
    @TableField("pay_order")
    private String payOrder;

    /**
     * 创建时间
     */
    @TableField("create_time")
    private Date createTime;

    /**
     * 更新时间
     */
    @TableField("update_time")
    private Date updateTime;

    /**
     * 删除时间
     */
    @TableField("delete_time")
    private Date deleteTime;

    /**
     * 应交时间
     */
    @TableField("should_pay_date")
    private Date shouldPayDate;

    /**
     * 缴费时间
     */
    @TableField("pay_date")
    private Date payDate;


    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getMemCode() {
        return memCode;
    }

    public void setMemCode(String memCode) {
        this.memCode = memCode;
    }

    public String getMemOrgCode() {
        return memOrgCode;
    }

    public void setMemOrgCode(String memOrgCode) {
        this.memOrgCode = memOrgCode;
    }

    public String getMemOrgOrgCode() {
        return memOrgOrgCode;
    }

    public void setMemOrgOrgCode(String memOrgOrgCode) {
        this.memOrgOrgCode = memOrgOrgCode;
    }

    public String getYear() {
        return year;
    }

    public void setYear(String year) {
        this.year = year;
    }

    public String getMonth() {
        return month;
    }

    public void setMonth(String month) {
        this.month = month;
    }

    public BigDecimal getStandard() {
        return standard;
    }

    public void setStandard(BigDecimal standard) {
        this.standard = standard;
    }

    public String getPayType() {
        return payType;
    }

    public void setPayType(String payType) {
        this.payType = payType;
    }

    public String getPayOrgCode() {
        return payOrgCode;
    }

    public void setPayOrgCode(String payOrgCode) {
        this.payOrgCode = payOrgCode;
    }

    public String getPayOrgOrgCode() {
        return payOrgOrgCode;
    }

    public void setPayOrgOrgCode(String payOrgOrgCode) {
        this.payOrgOrgCode = payOrgOrgCode;
    }

    public String getPayOrgCodeSet() {
        return payOrgCodeSet;
    }

    public void setPayOrgCodeSet(String payOrgCodeSet) {
        this.payOrgCodeSet = payOrgCodeSet;
    }

    public String getSettingOrgOrgCode() {
        return settingOrgOrgCode;
    }

    public void setSettingOrgOrgCode(String settingOrgOrgCode) {
        this.settingOrgOrgCode = settingOrgOrgCode;
    }

    public String getSettingOrgCode() {
        return settingOrgCode;
    }

    public void setSettingOrgCode(String settingOrgCode) {
        this.settingOrgCode = settingOrgCode;
    }

    public Object getSettingOrgCodeSet() {
        return settingOrgCodeSet;
    }

    public void setSettingOrgCodeSet(Object settingOrgCodeSet) {
        ObjectMapper mapper = new ObjectMapper();
        try {
            String value = mapper.writeValueAsString(settingOrgCodeSet);
            PGobject pGobject = new PGobject();
            pGobject.setType("json");
            pGobject.setValue(value);
            this.settingOrgCodeSet = pGobject;
        } catch (Exception e) {
            throw new RuntimeException("PgObject转化失败");
        }
    }

    public BigDecimal getPayMoney() {
        return payMoney;
    }

    public void setPayMoney(BigDecimal payMoney) {
        this.payMoney = payMoney;
    }

    public String getUpdateAccount() {
        return updateAccount;
    }

    public void setUpdateAccount(String updateAccount) {
        this.updateAccount = updateAccount;
    }

    public String getSeason() {
        return season;
    }

    public void setSeason(String season) {
        this.season = season;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public Integer getIsIgnore() {
        return isIgnore;
    }

    public void setIsIgnore(Integer isIgnore) {
        this.isIgnore = isIgnore;
    }

    public BigDecimal getBase() {
        return base;
    }

    public void setBase(BigDecimal base) {
        this.base = base;
    }

    public String getD49Code() {
        return d49Code;
    }

    public void setD49Code(String d49Code) {
        this.d49Code = d49Code;
    }

    public String getD49Name() {
        return d49Name;
    }

    public void setD49Name(String d49Name) {
        this.d49Name = d49Name;
    }

    public String getReason() {
        return reason;
    }

    public void setReason(String reason) {
        this.reason = reason;
    }

    public String getPayOrder() {
        return payOrder;
    }

    public void setPayOrder(String payOrder) {
        this.payOrder = payOrder;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    public Date getDeleteTime() {
        return deleteTime;
    }

    public void setDeleteTime(Date deleteTime) {
        this.deleteTime = deleteTime;
    }

    public Date getShouldPayDate() {
        return shouldPayDate;
    }

    public void setShouldPayDate(Date shouldPayDate) {
        this.shouldPayDate = shouldPayDate;
    }

    public Date getPayDate() {
        return payDate;
    }

    public void setPayDate(Date payDate) {
        this.payDate = payDate;
    }

    @Override
    public Serializable pkVal() {
        return this.id;
    }

    @Override
    public String toString() {
        return "Fee{" +
        "id=" + id +
        ", code=" + code +
        ", memCode=" + memCode +
        ", memOrgCode=" + memOrgCode +
        ", memOrgOrgCode=" + memOrgOrgCode +
        ", year=" + year +
        ", month=" + month +
        ", standard=" + standard +
        ", payType=" + payType +
        ", payOrgCode=" + payOrgCode +
        ", payOrgOrgCode=" + payOrgOrgCode +
        ", payOrgCodeSet=" + payOrgCodeSet +
        ", settingOrgOrgCode=" + settingOrgOrgCode +
        ", settingOrgCode=" + settingOrgCode +
        ", settingOrgCodeSet=" + settingOrgCodeSet +
        ", payMoney=" + payMoney +
        ", updateAccount=" + updateAccount +
        ", season=" + season +
        ", remark=" + remark +
        ", isIgnore=" + isIgnore +
        ", base=" + base +
        ", d49Code=" + d49Code +
        ", d49Name=" + d49Name +
        ", reason=" + reason +
        ", payOrder=" + payOrder +
        ", createTime=" + createTime +
        ", updateTime=" + updateTime +
        ", deleteTime=" + deleteTime +
        ", shouldPayDate=" + shouldPayDate +
        ", payDate=" + payDate +
        "}";
    }
}
