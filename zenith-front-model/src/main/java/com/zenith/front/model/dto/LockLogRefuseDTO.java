package com.zenith.front.model.dto;

import java.io.Serializable;

/**
 * <AUTHOR>
 */
public class LockLogRefuseDTO implements Serializable {

    private static final long serialVersionUID = 201198635068256968L;

    /**
     * 申请记录主键
     */
    private String code;

    /**
     * 拒绝理由
     */
    private String reason;

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getReason() {
        return reason;
    }

    public void setReason(String reason) {
        this.reason = reason;
    }

    @Override
    public String toString() {
        return "LockLogRefuseDTO{" +
                "code='" + code + '\'' +
                ", reason='" + reason + '\'' +
                '}';
    }
}