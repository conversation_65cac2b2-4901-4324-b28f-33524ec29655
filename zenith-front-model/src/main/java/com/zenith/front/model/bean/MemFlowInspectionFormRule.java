package com.zenith.front.model.bean;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import java.util.Date;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableField;
import java.io.Serializable;

/**
 * <p>
 * 流动党员工作督察表规则
 * </p>
 *
 * <AUTHOR>
 * @since 2025-02-20
 */
@TableName("ccp_mem_flow_inspection_form_rule")
public class MemFlowInspectionFormRule extends Model<MemFlowInspectionFormRule> {

    private static final long serialVersionUID = 1L;

      @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 1-多列； 2-单列
     */
    @TableField("type")
    private Integer type;

    /**
     * 列名
     */
    @TableField("col_name")
    private String colName;

    /**
     * 执行sql
     */
    @TableField("with_sql")
    private String withSql;

    /**
     * 删除时间
     */
    @TableField("delete_time")
    private Date deleteTime;


    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public Integer getType() {
        return type;
    }

    public void setType(Integer type) {
        this.type = type;
    }

    public String getColName() {
        return colName;
    }

    public void setColName(String colName) {
        this.colName = colName;
    }

    public String getWithSql() {
        return withSql;
    }

    public void setWithSql(String withSql) {
        this.withSql = withSql;
    }

    public Date getDeleteTime() {
        return deleteTime;
    }

    public void setDeleteTime(Date deleteTime) {
        this.deleteTime = deleteTime;
    }

    @Override
    protected Serializable pkVal() {
        return this.id;
    }

    @Override
    public String toString() {
        return "MemFlowInspectionFormRule{" +
        "id=" + id +
        ", type=" + type +
        ", colName=" + colName +
        ", withSql=" + withSql +
        ", deleteTime=" + deleteTime +
        "}";
    }
}
