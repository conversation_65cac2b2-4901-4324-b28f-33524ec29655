package com.zenith.front.model.dto;

import lombok.Data;
import org.jooq.SortField;

import java.util.List;

/**
 * 年统反查列配置、单元格解释  DTO
 */
@Data
public class PeggingParaDTO {
    private String year;
    /**
     * 反查类型，1--普通表，2--补充资料
     */
    private String type;
    private Integer pageNum;
    private Integer pageSize;

    private String reportCode;
    private String orgCode;
    private String orgLevelCode;

    private String rowIndex;
    private String colIndex;
    /**
     * 包含的字段
     */
    private List<String> includeFieldList;
    /**
     * 排序字段
     */
    private List<SortField<Object>> orderByFieldList;

    /**
     * 反查类型，1--正常反查，2--直属单位统计
     */
    private String peggingType;


    /**
     * 反查类型，1--正常反查，2--直属单位统计
     */
    private String reportType;

    /**
     * 反查类型，1--正常反查，2--直属单位统计
     */
    private List<Object> fieldList;
}
