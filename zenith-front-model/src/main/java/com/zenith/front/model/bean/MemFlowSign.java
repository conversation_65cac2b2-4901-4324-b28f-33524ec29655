package com.zenith.front.model.bean;

import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import lombok.Data;
import lombok.EqualsAndHashCode;
import java.util.Date;

/**
 * <p>
 * ccp_mem_flow_sign 实体类
 * </p>
 *
 * <AUTHOR>
 * @date 2025-01-22 09:43:28
 */
@Data
@TableName("ccp_mem_flow_sign")
public class MemFlowSign extends Model<MemFlowSign> {

    private static final long serialVersionUID = -4274687842715591852L;
    /**
     * 唯一标识
     */
    @TableId(value = "id", type = IdType.ASSIGN_UUID)
    private String id;
    /**
     * 唯一标识
     */
    @TableField("code")
    private String code;
    /**
     * 党员code
     */
    @TableField("mem_code")
    private String memCode;
    /**
     * 姓名
     */
    @TableField("name")
    private String name;
    /**
     * 性别code
     */
    @TableField("sex_code")
    private String sexCode;
    /**
     * 性别名称
     */
    @TableField("sex_name")
    private String sexName;
    /**
     * 身份证
     */
    @TableField("idcard")
    private String idcard;
    /**
     * 出生日期
     */
    @TableField("birthday")
    private Date birthday;
    /**
     * 民族code
     */
    @TableField("d06_code")
    private String d06Code;
    /**
     * 民族名称
     */
    @TableField("d06_name")
    private String d06Name;
    /**
     * 学历code
     */
    @TableField("d07_code")
    private String d07Code;
    /**
     * 学历名称
     */
    @TableField("d07_name")
    private String d07Name;
    /**
     * 学位code
     */
    @TableField("d145_code")
    private String d145Code;
    /**
     * 学位名称
     */
    @TableField("d145_name")
    private String d145Name;
    /**
     * 入党日期
     */
    @TableField("join_date")
    private Date joinDate;
    /**
     * 转正日期
     */
    @TableField("formal_date")
    private Date formalDate;
    /**
     * 工作岗位
     */
    @TableField("d09_code")
    private String d09Code;
    /**
     * 工作岗位名称
     */
    @TableField("d09_name")
    private String d09Name;
    /**
     * 新社会阶层类型code
     */
    @TableField("d20_code")
    private String d20Code;
    /**
     * 新社会阶层类型名称
     */
    @TableField("d20_name")
    private String d20Name;
    /**
     * 从事专业技术职务
     */
    @TableField("work_post_code")
    private String workPostCode;
    /**
     * 从事专业技术职务
     */
    @TableField("work_post_name")
    private String workPostName;
    /**
     * 人员类别
     */
    @TableField("d08_code")
    private String d08Code;
    /**
     * 人员类别名称
     */
    @TableField("d08_name")
    private String d08Name;
    /**
     * 是否为农民
     */
    @TableField("is_farmer")
    private String isFarmer;
    /**
     * 手机号码
     */
    @TableField("phone")
    private String phone;
    /**
     * 户籍所在地
     */
    @TableField("house_hold_register")
    private String houseHoldRegister;
    /**
     * 家庭住址
     */
    @TableField("home_address")
    private String homeAddress;
    /**
     * 党龄校正值
     */
    @TableField("age_correction")
    private String ageCorrection;
    /**
     * 党员组织关系所在党组织code-改
     */
    @TableField("org_flow_code")
    private String orgFlowCode;

    /**
     * 党员组织关系所在党组织层级码-改
     */
    @TableField("org_flow_level_code")
    private String orgFlowLevelCode;
    /**
     * 党员组织关系所在党组织名称-改
     */
    @TableField("org_flow_name")
    private String orgFlowName;

    /**
     * 流入地党支部code-改
     */
    @TableField("apply_org_flow_code")
    private String applyOrgFlowCode;

    /**
     * 流入地党支部层级码-改
     */
    @TableField("apply_org_flow_level_code")
    private String applyOrgFlowLevelCode;

    /**
     * 流入地党支部名称-改
     */
    @TableField("apply_org_flow_name")
    private String applyOrgFlowName;

    /**
     * 联系电话
     */
    @TableField("connection")
    private String connection;
    /**
     * 流入地联系人
     */
    @TableField("flow_connection_name")
    private String flowConnectionName;
    /**
     * 流入地联系方式
     */
    @TableField("flow_connection")
    private String flowConnection;
    /**
     * 党员在流入地常用联系方式-改
     */
    @TableField("frequently_phone")
    private String frequentlyPhone;
    /**
     * 流动原因code
     */
    @TableField("d146_code")
    private String d146Code;
    /**
     * 流动原因
     */
    @TableField("d146_name")
    private String d146Name;
    /**
     * 流动原因详情
     */
    @TableField("flow_reason_detail")
    private String flowReasonDetail;
    /**
     * 外出日期
     */
    @TableField("out_date")
    private Date outDate;
    /**
     * 外出地点补充说明-改
     */
    @TableField("out_instructions")
    private String outInstructions;
    /**
     * 党费缴至日期流出地
     */
    @TableField("ghana_date")
    private Date ghanaDate;

    /**
     * 接收支部代码
     */
    @TableField("receive_code")
    private String receiveCode;
    /**
     * 接收支部
     */
    @TableField("receive_name")
    private String receiveName;

    /**
     * 0-本节点  1-省内非本节点 2-中组部获取的省外数据
     */
    @TableField("data_type")
    private String dataType;

    /**
     * create_time
     */
    @TableField(value = "create_time", fill = FieldFill.INSERT)
    private Date createTime;
    /**
     * update_time
     */
    @TableField(value = "update_time", fill = FieldFill.UPDATE)
    private Date updateTime;
    /**
     * delete_time
     */
    @TableField("delete_time")
    private Date deleteTime;
    /**
     * remark
     */
    @TableField("remark")
    private String remark;
    /**
     * timestamp
     */
    @TableField("timestamp")
    private Date timestamp;

    /**
     * 党组织联系方式
     */
    @TableField("org_connection")
    private String orgConnection;

    /**
     * 党组织联系人
     */
    @TableField("org_connection_name")
    private String orgConnectionName;

    @TableField("flow_up_code")
    private String flowUpCode;

    /**
     * 接收提醒根节点代码
     */
    @TableField("receive_node_code")
    private String receiveNodeCode;

    /**
     * 发送提醒根节点代码
     */
    @TableField("send_node_code")
    private String sendNodeCode;

    /**
     * 流动提醒处理状态 1.办理中 2.已登记  3.已退回
     */
    @TableField("flow_warn_status")
    private String flowWarnStatus;

    /**
     * 流出地是否查看提醒 0-否 1-是
     */
    @TableField("flow_view_warn")
    private String flowViewWarn;

    /**
     * 党员组织关系所在党组织区划码
     */
    @TableField("org_flow_division")
    private String orgFLowDivision;


    /**
     * 流动党员人员类型
     */
    @TableField("flow_mem_type_code")
    private String flowMemTypeCode;
    /**
     * 流动党员人员类型
     */
    @TableField("flow_mem_type_name")
    private String flowMemTypeName;
    /**
     * 人员类型备注
     */
    @TableField("flow_mem_type_remark")
    private String flowMemTypeRemark;
    /**
     * 人员类型新就业备注
     */
    @TableField("flow_mem_type_new_remark")
    private String flowMemTypeNewRemark;
    /**
     * 流入地是否为农民
     */
    @TableField("lrd_is_farmer")
    private String lrdIsFarmer;
}
