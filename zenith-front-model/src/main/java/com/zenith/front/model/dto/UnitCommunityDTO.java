package com.zenith.front.model.dto;


import cn.hutool.core.bean.BeanUtil;
import com.zenith.front.model.bean.UnitExtend;
import lombok.Data;
import lombok.ToString;

import java.math.BigDecimal;
import java.util.Date;

/**
 *
 * UnitCommunity字段 合并到 UnitExtend
 */
@Data
@ToString
public class UnitCommunityDTO {

    private Long id;

    private String code;

    private String orgCode;

    /**
     * 年份
     */
    private Integer year;

    /**
     * 今年选派第一书记
     */
    private String firstSecretarySelect;

    /**
     * 现任第一书记代码
     */
    private String firstSecretaryCode;

    /**
     * 现任第一书记姓名
     */
    private String firstSecretaryName;

    /**
     * 本年各级培训第一书记
     */
    private Integer secretaryTrainingNum;

    /**
     * 是否为第一书记安排不低于1万元工作经费
     */
    private Integer hasThousand;

    /**
     * 是否派出单位落实责任、项目、资金捆绑的
     */
    private Integer hasBundled;

    /**
     * 提拔使用或晋级的第一书记数
     */
    private Integer promotedNum;

    /**
     * 因工作不胜任召回调整的第一书记数
     */
    private Integer adjustedNum;

    /**
     * 运转经费
     */
    private BigDecimal operatingExpenses;

    /**
     * 每村办公经费
     */
    private BigDecimal villagePer;

    /**
     * 党组织书记平均报酬
     */
    private BigDecimal secretarySalary;

    /**
     * 活动场所面积
     */
    private Integer spaceArea;

    /**
     * 本年新建或改扩建活动场所数量
     */
    private Integer newExpandArea;


    /**
     * 村党组织书记中录用公务员数
     */
    private Integer secretaryPartyNum;
    /**
     * 村党组织书记中录用事业编制工作人员数
     */
    private Integer secretaryEmploySybzNum;

    /**
     * 从村党组织书记中选拔乡镇领导干部人员数
     */
    private Integer secretaryPromotedNum;

    /**
     * 社区纳入财政预算的工作经费总额
     */
    private BigDecimal communityMoneyNum;

    /**
     * 社区全年服务群众专项经费总额
     */
    private BigDecimal communityServingPeople;

    /**
     * 是否开展在职党员到社区报到为群众服务
     */
    private Integer communityMasses;


    private Date createTime;

    private Date updateTime;

    private Date deleteTime;

    private Date timestamp;

    private String updateAccount;

    private Integer hasFirstSecretary;

    /**
     * 是否垂直管理部门 1是，0否 dict_d147
     */
    private String isCzglbm;

    /**
     * 吸收未转入组织关系的党员 1是，0否
     */
    private Integer b301;

    /**
     * 配备专职副书记 1是，0否
     */
    private Integer hasMajorDeputySecretary;

    /**
     * 政府工作部门建立党组（党委）情况,是否建立党组
     */
    private String d80Code;

    /**
     * 在岗职工数（人）
     */
    private Integer onPostNum;

    /**
     * 党政机关工作人员
     */
    private Integer b30A12;

    /**
     * 在职党员数（人）
     */
    private Integer memberNum;

    /**
     * 是否政府工作部门 1是，0否
     */
    private Integer isDfzgbm;

    /**
     * 35岁以下在岗职工数（人）
     */
    private Integer underThirtyFiveNum;

    /**
     * 单位负责人
     */
    private String principal;

    /**
     * 单位负责人是否是党员 1是，0否
     */
    private Integer isUnitMem;

    /**
     * 是否有“两代表一委员”   1是，0否
     */
    private Integer isCommitteeRepresent;

    /**
     * 为上级党建工作联系点 1是，0否
     */
    private Integer isOrgContact;

    /**
     * 建立党员志愿者队伍 1是，0否
     */
    private Integer isVolTeam;

    /**
     * 企业规模
     */
    private String d17Code;

    /**
     * 经济类型
     */
    private String d16Code;

    /**
     * 企业隶属关系
     */
    private String d79Code;

    /**
     * 在岗专业技术人员数（人）
     */
    private Integer tecNum;

    /**
     * 在岗职工工人（工勤技能）数
     */
    private Integer workerNum;

    /**
     * 是否建有工会 1是，0否
     */
    private Integer isWorkUnion;

    /**
     * 工会负责人是否是党员 1是，0否
     */
    private Integer isWorkUnionMember;

    /**
     * 是否建有共青团 1是，0否
     */
    private Integer isTeenager;

    /**
     * 共青团负责人是否是党员 1是，0否
     */
    private Integer isTeenagerMember;

    /**
     * 是否建有妇联 1是，0否
     */
    private Integer isWomenFederation;

    /**
     * 妇联负责人是否是党员 1是，0否
     */
    private Integer isWomenFederationMember;

    /**
     * 党建工作指导员数
     */
    private Integer bzt610;

    /**
     * 所属层级
     */
    private String d78Code;

    /**
     * 建立党员服务机构 1是，0否
     */
    private Integer isOrgService;

    /**
     * 是否选派党建工作指导员或联络员 1是，0否
     */
    private Integer isOrgInstructor;

    /**
     * 是否脱钩行业协会商会 1是，0否
     */
    private Integer isDecouplIndustry;

    /**
     * 隶属于两新工委的（人）
     */
    private Integer lslxgw;

    /**
     * 隶属于机关工委的（人）
     */
    private Integer lsjggw;

    /**
     * 隶属于行业主管部门的（人）
     */
    private Integer lshyzg;

    /**
     * 书记由行业主管部门党员负责同志担任（人）
     */
    private Integer sjyhyzg;

    /**
     * 专职工作人员数（人）
     */
    private Integer zzgzry;

    /**
     * 医疗机构设立级别
     */
    private String d77Code;

    /**
     * 在岗专业技术人员（高级职称）（人）
     */
    private Integer zaigangGaoji;

    /**
     * 户数（户）
     */
    private Integer houseNum;

    /**
     * 户籍人口（人）
     */
    private Integer housePersonNum;

    /**
     * 常住人口（人）
     */
    private Integer permanentPopulation;

    /**
     * 上年度当地农村居民人均可支配收入（元）
     */
    private BigDecimal lastYearIncome;

    /**
     * 党组织书记情况
     */
    private String d76Code;

    /**
     * 有党组织书记后备人选
     */
    private Integer bzt44;

    /**
     * 参加县级以上培训
     */
    private Integer bzt45;

    /**
     * 实行“四议两公开”工作法
     */
    private Integer bzt46;

    /**
     * 成立村务监督委员会或其他村务监督机构
     */
    private Integer bzt47;

    /**
     * 建档立卡贫困村 1是，0否
     */
    private Integer isPoorVillage;

    /**
     * 建立村务监督委员会 1是，0否
     */
    private Integer isVillageCommittee;

    /**
     * 自然村数
     */
    private Integer naturalVillageNum;

    /**
     * 集体经济年收入（元）
     */
    private BigDecimal collectiveEconomyGdp;

    /**
     * 第一书记姓名
     */
    private String firstSecName;

    /**
     * 第一书记公民身份证号
     */
    private String firstSecId;

    /**
     * 第一书记任期（年）
     */
    private Integer totalYear;

    /**
     * 本年召开党委全委会数
     */
    private Integer b3212;

    /**
     * 为第一书记安排不低于1万元工作经费的村
     */
    private Integer bzt412;

    /**
     * 未完成“五小”建设的乡镇
     */
    private Integer bzt429;

    /**
     * 向建制村选派第一书记数
     */
    private Integer bzt410;

    /**
     * 暂无活动场所的建制村
     */
    private Integer bzt426;

    /**
     * 纪委常委数
     */
    private Integer b3210;

    /**
     * 本年各级培训第一书记（人次）
     */
    private Integer bzt411;

    /**
     * 本年领导班子民主生活会参加人员数
     */
    private Integer b3215;

    /**
     * 是否已召开年会 1是，0否
     */
    private Integer b325;

    /**
     * 提拔使用或晋级的第一书记数
     */
    private Integer bzt414;

    /**
     * 是否本年已换届
     */
    private Integer b323;

    /**
     * 党委候补委员数
     */
    private Integer b3211;

    /**
     * 建制村运转经费合计（万元/年）
     */
    private Integer bzt416;

    /**
     * 活动场所面积200㎡以上的建制村
     */
    private Integer bzt427;

    /**
     * 是否本年任届期满 1是，0否
     */
    private Integer b322;

    /**
     * 村党组织书记报酬合计（万元/年）
     */
    private Integer bzt418;

    /**
     * 村党组织书记报酬低于农村居民人均可支配收入两倍标准的县 1是，0否
     */
    private Integer bzt420;

    /**
     * 纪委委员数
     */
    private Integer b329;

    /**
     * 因工作不胜任召回调整的第一书记数
     */
    private Integer bzt415;

    /**
     * 本届换届选举代表数
     */
    private Integer b326;

    /**
     * 从村党组织书记中选拔乡镇领导干部数
     */
    private Integer bzt49;

    /**
     * 本年新建或改扩建活动场所
     */
    private Integer bzt428;

    /**
     * 是否试行党代会常任制 1是，0否
     */
    private Integer b324;

    /**
     * 建制村办公经费合计（万元/年）
     */
    private Integer bzt417;

    /**
     * 党委委员数
     */
    private Integer b327;

    /**
     * 从村党组织书记中录用公务员和事业编制工作人员
     */
    private Integer bzt48;

    /**
     * 落实正常离任村党组织书记生活补贴的县 1是，0否
     */
    private Integer bzt422;

    /**
     * 派出单位落实责任、项目、资金捆绑的村
     */
    private Integer bzt413;

    /**
     * 村干部基本报酬和村级组织办公经费合计低于9万元的县 1是，0否
     */
    private Integer bzt419;

    /**
     * 本年召开领导班子民主生活会
     */
    private Integer b3214;

    /**
     * 无集体经济收入的建制村数
     */
    private Integer bzt425;

    /**
     * 为村党组织书记办理养老保险的县 1是，0否
     */
    private Integer bzt421;

    /**
     * 落实村民小组长误工补贴的县 1是，0否
     */
    private Integer bzt424;

    /**
     * 落实农村公共服务运行维护支出或服务群众专项经费的县 1是，0否
     */
    private Integer bzt423;

    /**
     * 党委常委数
     */
    private Integer b328;

    /**
     * 本年内参加党委全委会委员数
     */
    private Integer b3213;

    /**
     * 街道干部数
     */
    private Integer b61;

    /**
     * 街道干部35岁及以下
     */
    private Integer b62;

    /**
     * 街道干部36至54岁
     */
    private Integer b63;

    /**
     * 街道干部55岁及以上
     */
    private Integer b64;

    /**
     * 街道干部大专及以上
     */
    private Integer b65;

    /**
     * 街道干部高中中专及以下
     */
    private Integer b66;

    /**
     * 街道干部公务员人数
     */
    private Integer b67;

    /**
     * 街道干部事业人员
     */
    private Integer b68;

    /**
     * 街道干部其他身份
     */
    private Integer b69;

    /**
     * 是否实行兼职委员制 1是，0否
     */
    private Integer b610;

    /**
     * 是否建立工委 1是，0否
     */
    private Integer isWorkeCommittee;

    /**
     * 第一书记任职开始日期
     */
    private Date startDate;

    /**
     * 主要负责人担任党组织书记 1是，0否
     */
    private Integer mainPartySecretary;

    /**
     * 是否实行党组织领导的院（所）长负责制 1是，0否
     */
    private Integer orgLeaderCharge;

    /**
     * 公益分类
     */
    private String d81Code;

    /**
     * 2023-add-单位类别为【科研院所】时
     * 2023-add-单位类别为【教育】时
     * 领导体制已写入医院章程的 1是，0否
     */
    private Integer ldtzyyzc;

    /**
     * 是否党建工作要求写入医院章程 1是，0否
     */
    private Integer isPartyWorkWrite;

    /**
     * 是否开展基层党建述职评议考核	 1是，0否
     */
    private Integer isOpenOrgAssess;

    /**
     * 2023-add-单位类别为【科研院所】时
     * 2023-add-单位类别为【教育】时
     * 是否党委书记、院长分设 1是，0否
     */
    private Integer isLeaderSeparate;

    /**
     * 2023-add-单位类别为【科研院所】时
     * 2023-add-单位类别为【教育】时
     * 是否院长系中共党员 1是，0否
     */
    private Integer leaderIsGcdy;

    /**
     * 2023-add-单位类别为【科研院所】时
     * 2023-add-单位类别为【教育】时
     * 是否院长担任党委副书记 1是，0否
     */
    private Integer isLeaderDeputySecretary;

    /**
     * 2023-add-单位类别为【科研院所】时
     * 医院内设机构党支部（个）
     */
    private Integer isSetOrgParty;

    /**
     * 2023-add-单位类别为【科研院所】时
     * 党支部书记是内设机构负责人（人）
     */
    private Integer secretaryIsInsideLeader;

    /**
     * 2023-add-单位类别为【科研院所】时
     * 党支部书记是“双带头人”的（人）
     */
    private Integer sjsdtr;

    /**
     * 本年度党支部书记参加培训人次（人）
     */
    private Integer yearTrainOrgMem;

    /**
     * 本年度任届期满的内设机构党支部（个）
     */
    private Integer isYearExpirationOrg;

    /**
     * 本年换届（个）
     */
    private Integer isYearOrgChange;

    /**
     * 本年度发展党员（人）
     */
    private Integer yearDevelopMem;

    /**
     * 本年度发展卫生技术人员党员（人）
     */
    private Integer yearDevelopMemMedicine;

    /**
     * 本年度列为入党积极分子（人）
     */
    private Integer rdjjfz;

    /**
     * 在校中专生数
     */
    private Integer b73;

    /**
     * 在校高中、中技生数
     */
    private Integer b74;

    /**
     * 在校研究生数
     */
    private Integer b71;

    /**
     * 在校本科生数
     */
    private Integer b72;

    /**
     * 在校专科生数
     */
    private Integer b78;

    /**
     * 高等学校教师数
     */
    private Integer b75;

    /**
     * 高等学校女教师数
     */
    private Integer b76;

    /**
     * 高等学校35岁及以下教师
     */
    private Integer b77;

    /**
     * 是否设立常委会 1是，0否
     */
    private Integer hasStandingCommittee;


    /**
     * 是否向地方党委和主管部委专题报告党委领导下的校长负责制执行 1是 0否
     */
    private Integer hasReportImplementation;

    /**
     * 是否修订党委全委会、常委会和校长办公会议事规则 1是 0否
     */
    private Integer hasOfficeProcedure;


    /**
     * 学校党委书记是否向地方党委述职 1是，0否
     */
    private Integer schoolHasReportsLocal;

    /**
     * 是否组织开展二级院（系）党组织书记向学校党委述职
     */
    private Integer hasSecretaryUniversityCommittee;


    /**
     * 校长是否中共党员
     */
    private Integer hasPresidentPartyMember;


    /**
     * 校长是否担任党委副书记
     */
    private Integer hasDeputyPartySecretary;

    /**
     * 医院等级--代码
     */
    private String d95Code;

    /**
     * 医院等级--名称
     */
    private String d95Name;


    /**
     * 办校类别代码
     */
    private String d109Code;

    /**
     * 办校类别中文
     */
    private String d109Name;


    /**
     * 办院级别代码
     */
    private String d110Code;

    /**
     * 办院级别中文
     */
    private String d110Name;


    /**
     * 办院类型代码
     */
    private String d111Code;

    /**
     * 办院类型中文
     */
    private String d111Name;


    /**
     * 2023-add-单位类别为【教育】时
     * 是否已实行党委领导下的院长负责制 1是，0否
     */
    private Integer hasResponsibilitySystem;

    /**
     * 院长是否中共党员  1是，0否
     */
    private Integer hasDeanPartyMember;


    /**
     * 院长是否担任党委副书记  1是，0否
     */
    private Integer hasDeanPartySecretary;


    /**
     * 党支部书记是否“双带头人”  1是，0否
     */
    private Integer hasSjsdtr;

    /**
     * 党支部书记是否内设机构负责人 1是，0否
     */
    private Integer hasSecretaryisinsideleader;

    /**
     * 是否实行院（所）长负责制 1是，0否
     */
    private Integer hasDeanResponsibilitySystem;


    /**
     * 所长负责制情况代码
     */
    private String d112Code;

    /**
     * 所长负责制情况代码
     */
    private String d112Name;


    /**
     * 行业分类
     */
    private String d114Code;

    /**
     * 是否配备专职党务工作人员
     */
    private Integer hasPartyWork;

    /**
     * 是否企业本级
     */
    private Integer hasFirmLevel;

    /**
     * 企业本级名称
     */
    private String firmLevelName;


    /**
     * 企业级别
     */
    private String d115Code;

    /**
     * 是否建立董事会
     */
    private Integer hasDirectors;

    /**
     * 董事长是否担任书记
     */
    private Integer hasChairmanSecretary;

    /**
     * 党建工作经费是否按上年度工资总额一定比例纳入企业管理费用
     */
    private Integer hasProportionateFunding;

    /**
     * 人事管理和基层党建是否由一个部门抓
     */
    private Integer hasBranchToCatch;


    /**
     * 人事管理和基层党建是否由一个领导管
     */
    private Integer hasByLeader;


    /**
     * 党务工作人员和经营管理人员是否同职级同待遇
     */
    private Integer hasSameTreatment;


    /**
     * 是否上市公司
     */
    private Integer hasPublicCompany;


    /**
     * 党建工作是否写入公司章程
     */
    private Integer hasArticlesIncorporation;

    /**
     * 是否党组织研究讨论作为董事会、经理层决策重大问题前置程序
     */
    private Integer hasPrepositionalProcedure;


    /**
     * 董事长是否由上级企业有关负责人兼任
     */
    private Integer hasResponsiblePerson;


    /**
     * 分支机构树
     */
    private Integer branches;


    /**
     * 基层党组织数量
     */
    private Integer partyOrganizationNum;


    /**
     * 已建立党组织
     */
    private Integer haveBeenEstablished;


    /**
     * 党员数
     */
    private Integer partyMembers;


    /**
     * 是否依托组织部门成立的非公党工委
     */
    private Integer hasNonPublicParty;


    /**
     * 是否设立专门办事机构的非公党工委 1是，0否
     */
    private Integer hasSpecialAgencies;


    /**
     * 办事机构工作人员编制数
     */
    private Integer staffOfficeNumbers;


    /**
     * 组织部门（非公党工委）直接联系的非公企业党组织数
     */
    private Integer nonPublicEnterprises;

    private Integer hasCommunityAccess;

    private Integer hasJointUnits;
    /**
     * 是否按不低于上年度当地全口径城镇单位就业人员平均工资水平确定报酬
     */
    private Integer hasLowerSocial;

    private BigDecimal communityWorkersSalary;

    private BigDecimal communitySecretarySalary;

    private Integer communityBuildingNumber;

    private Integer hasCommunityPositions;

    private Integer communityOfficeSpace;

    private Integer hasParttimeSystem;
    /**
     * 是否建档立卡贫困村 0否 1是
     */
    private Integer hasPoorVillage;
    private Integer hasFourTwoOpenWork;
    private Integer hasCommunitySupervisory;

    /**
     * 专业技术人员数
     */
    private Integer technicalPersonnel;

    /**
     * 党员中高级职称人员数
     */
    private Integer partySeniorTitle;


    /**
     * 是否建立志愿者服务机构 1是，0否
     */
    private Integer hasVolunteerOrganization;


    /**
     * 是否赋予区域综合管理权、对上级部门派驻机构负责人的人事考核权等权力 1是，0否
     */
    private Integer hasExaminationPower;

    /**
     * 是否取消招商引资等职能 1是，0否
     */
    private Integer hasCancelInvestmentPromotion;

    /**
     * 是否整合职能统筹设置党政内设工作机构 1是，0否
     */
    private Integer hasWorkMechanism;

    /**
     * 是否组织委员是否纳入上一级党委管理 1是，0否
     */
    private Integer hasIncludedCommittee;


    /**
     * 是否建立党群服务中心 1是，0否
     */
    private Integer hasGroupServiceCenter;

    /**
     * 是否实行与驻区单位党建联建共建 1是，0否
     */
    private Integer hasPartyBuildEndeavor;


    /**
     * 是否党建工作指导员联系 1是，0否
     */
    private Integer hasInstructorContact;

    /**
     * 是否建立工会或共青团组织  1是，0否
     */
    private Integer hasUnionOrganization;

    /**
     * 未转组织关系党员数
     */
    private Integer notTurnedParty;

    /**
     * 是否主要负责人担任党组织书记 1是，0否
     */
    private Integer hasOrganizationSecretary;


    /**
     * 从业人员数
     */
    private Integer employeesNumber;

    /**
     * 法定代表人是否党员 1是，0否
     */
    private Integer hasRepresentative;

    /**
     * 是否兼任企业党组书记 1是，0否
     */
    private Integer hasProperSecretary;

    /**
     * 吸收未转入组织关系的党员建立党组织数
     */
    private Integer absorbedTissueNumber;

    /**
     * 主要负责人是否党员 1是，0否
     */
    private Integer hasHeadParty;

    /**
     * 社区纳入财政预算的工作经费总额（万元）
     */
    private BigDecimal includedFinancial;

    /**
     * 社区全年服务群众专项经费总额（万元）
     */
    private BigDecimal specialFundsMasses;

    /**
     * 是否开展在职党员到社区报到为群众服务 1是，0否
     */
    private Integer hasCommunityReport;

    /**
     * 纪委书记是否担任学校党委常委 1是，0否
     */
    private Integer hasSecretaryCommittee;

    /**
     * 组织部长是否担任学校党委常委 1是，0否
     */
    private Integer hasTissueCommittee;

    /**
     * 宣传部长是否担任学校党委常委 1是，0否
     */
    private Integer hasPropagandaCommittee;


    /**
     * 宣传部长是否担任学校党委常委 1是，0否
     */
    private Integer hasFrontCommittee;

    /**
     * 本科以上学历人数（数字）
     */
    private Integer aboveBkEducation;

    /**
     * 研究生以上学历人数（数字）
     */
    private Integer aboveYjsEducation;

    /**
     * 是否由企业中高层管理人员担任党组织书记的 1是，0否
     */
    private Integer hasSecretaryHighLevel;

    /**
     * 是否党组织书记（是1 否0）
     */
    private Integer hasLevelSecretary;

    /**
     * 户籍数
     */
    private Integer householdRegistration;

    /**
     * 户籍数
     */
    private Integer registeredPopulation;

    /**
     * 本级企业是否省内企业
     */
    private Integer hasIndustryProvince;

    /**
     * 全体在校学生中研究生人数
     */
    private Integer graduateStudent;

    /**
     * 全体在校学生中大学本科生人数
     */
    private Integer undergraduateStudent;

    /**
     * 全体在校学生中大学专科生人数
     */
    private Integer juniorCollegeStudent;

    /**
     * 全体在校学生中高中、中技人数
     */
    private Integer middleTechnicalStudents;

    /**
     * 高等学校教师人数
     */
    private Integer teachersInstitutionsHigher;

    /**
     * 高等学校教师中女性人数
     */
    private Integer teachersHigherWomen;

    /**
     * 高等学校教师中35岁及以下人数
     */
    private Integer teachersAgeThirtyFiveBelow;


    /**
     * 到社区报到的在职党员
     */
    private Integer reportCommunityMember;

    /**
     * 2023-add-单位类别为【科研院所】时
     * 2023-add-单位类别为【教育】时
     * 党委书记是否兼任行政职务
     */
    private Integer hasClerkPosition;

    /**
     * 党委书记是否兼任行政职务
     */
    private Integer hasSecretaryEconomy;

    /**
     * 全体在校学生中中专生人数
     */
    private Integer technicalSecondaryStudent;
    /**
     * 是否配备院长（0否，1是）
     */
    private Integer isAllocateDean;
    /**
     * 是否配备书记（0否，1是）
     */
    private Integer isAllocateSecretary;

    /**
     * 是否建立工会  1是，0否
     */
    private Integer hasLabourUnion;

    /**
     * 是否建立共青团组织  1是，0否
     */
    private Integer hasYouthLeague;

    /**
     * 是否建立妇联组织  1是，0否
     */
    private Integer hasWomensFederation;

    /**
     * 是否已统一整合设置网格 0否，1是
     */
    private Integer hasSetGrid;
    /**
     * 是否有专职网格员纳入社区工作者管理 0否，1是
     */
    private Integer hasIncludedGridWorker;

    /**
     * 2023年统修改增加国名经济行业
     */
    private String d194Code;

    public String getD194Code() {
        return d194Code;
    }

    public void setD194Code(String d194Code) {
        this.d194Code = d194Code;
    }


    /**
     * 2023年统修改增加生产服务行行业
     */
    private String d195Code;
    public String getD195Code() {
        return d195Code;
    }

    public void setD195Code(String d195Code) {
        this.d195Code = d195Code;
    }

    // -----------专题十一 科研院所 相关统计项--------
    // -----------专题十二 中小学校 相关统计项--------
    /**
     * 2023-add-单位类别为【科研院所】时
     * 2023-add-单位类别为【教育】时
     *  已修订党组织会议、院（所）长办公会议议事规则的（1是0否）
     */
    private Integer ysgzIs;
    /**
     * 2023-add-单位类别为【科研院所】时
     * 2023-add-单位类别为【教育】时
     * 已建立学校党组织书记和校长定期沟通制度的 （1是0否）
     */
    private Integer yjldqgtIs;
    /**
     * 2023-add-单位类别为【科研院所】时
     * 2023-add-单位类别为【教育】时
     * 已设立党务工作机构的 （1是0否）
     */
    private Integer ysldwgzjgIs;
    /**
     * 2023-add-单位类别为【科研院所】时
     * 2023-add-单位类别为【教育】时
     *已配备专职党务工作人员的	（1是0否）
     */
    private Integer ypbzzdwgzryIs;

    /**
     * 单位类别，用来逻辑校验
     */
    private String d04Code;

    //
    // ==========
    // ==========
    // unit 中的部分信息
    /**
     * 参加县级及以上集中培训人数（数字、必填）
     */
    private Integer joinAboveCountyTrainNum;
    /**
     * 村干部参加城镇职工养老保险人数（数字、必填）
     */
    private Integer villageJoinUrbanWorkerNum;
    /**
     * 应到村任职选调生人数（数字、必填）
     */
    private Integer numberOfStudentsToBeTransferredToTheVillage;
    /**
     * 是否有大学毕业生在村工作（选择框、必填）1 是 0 否
     */
    private Integer whetherThereAreCollegeGraduatesWorkingInTheVillage;

    /**
     * 不是党委委员的政府领导班子成员人数（必填、数字）
     */
    private Integer numberOfNonGovernmentalMembers;

    /**
     * 是否民族院校 供发展党员工作情况调度表 民族院校（人） 使用
     */
    private Integer hasNationalColleges;

    /**
     * 农村专业技术协会数量
     */
    private Integer ruralProfessionalTechnicalAssociationNum;

    /**
     * 农民专业合作社数量
     */
    private Integer farmerSpecializedCooperativesNum;

    /**
     * 家庭农场数量
     */
    private Integer familyFarmNum;

    /**
     * 专调表八 本年度高校党支部书记参加培训人次
     */
    private Integer yearBranchTraining;

    /**
     * 专调表八 本年度院系本级党组织书记参加培训人次
     */
    private Integer yearTraining;

    /**
     * 专调表八 本年度毕业生党员
     */
    private Integer graduatePartyMember;

    /**
     * 专调表八 尚未转出组织关系的
     */
    private Integer orgRelationshipNotTransferred;

    /**
     * 是否将党建工作经费纳入管理费列支、税前扣除(当单位类型为社会组织)
     */
    private Integer hasWorkingExpenses;

    /**
     * 登记级别
     */
    private String d118Code;

    /**
     * 登记级别
     */
    private String d118Name;



    //===========================
    // unitAll
    /**
     * 街道干部人数
     */
    private Integer streetCadres;
    /**
     * 街道干部35岁及以下人数
     */
    private Integer age35Below;
    /**
     * 街道干部36至55岁人数
     */
    private Integer age36ToAge55;
    /**
     * 街道干部56岁及以上人数
     */
    private Integer age56Above;
    /**
     * 街道干部大专及以上学历人数
     */
    private Integer collegeDegreeAbove;
    /**
     * 街道干部高中中专及以下人数
     */
    private Integer secondarySchoolBelow;
    /**
     * 街道干部公务员人数
     */
    private Integer streetCadresCivil;
    /**
     * 街道干部事业单位人数
     */
    private Integer streetCadresInstitutions;
    /**
     * 街道干部其他身份人数
     */
    private Integer cadreOther;

    /**
     * 与业务主管单位关系(当单位类型为社会组织)
     */
    private String d159Code;

    /**
     * 与业务主管单位关系(当单位类型为社会组织)
     */
    private String d159Name;

    /**
     * 是否建立履职事项清单 1-是 0-否
     */
    private Integer hasPerformedDetail;

    /**
     * 国有经济控制的企业党建工作情况：境外分支机构有党员的机构
     */
    private Integer existMemberBranches;

    /**
     * 国有经济控制的企业党建工作情况：境外分支机构有党员3人以上、未建立党组织的
     */
    private Integer threeMemberNoOrgBranches;

    /**
     * 有经济控制的企业境外分支共有员工数
     */
    private Integer branchEmployee;
    /**
     * 有经济控制的企业境外分支其中国内派出员工
     */
    private Integer branchEmployeeHome;
    /**
     * 境外分支机构已建立的党委数
     */
    private Integer branchCommittee;
    /**
     * 境外分支机构已建立的总支部数
     */
    private Integer branchGeneral;
    /**
     * 境外分支机构已建立的支部数
     */
    private Integer branchNode;

    public UnitExtend toModel() {
        UnitExtend model = new UnitExtend();
        BeanUtil.copyProperties(this, model);
        return model;
    }
}
