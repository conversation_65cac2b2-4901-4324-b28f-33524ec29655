package com.zenith.front.model.dto;

import com.zenith.front.model.bean.MessageNotice;
import com.zenith.front.model.validate.group.Common1Group;
import lombok.*;

import javax.validation.constraints.NotBlank;

@ToString
public class MessageNoticeDTO{

   	/**
   	 *  自增长主键
   	 */
    private Integer id;
   	/**
   	 * 消息通知主键
   	 */
    @NotBlank(groups = {Common1Group.class},message = "标识符不允许为空!")
    private String code;
   	/**
   	 * 回复内容
   	 */
    private String replyContext;
   	/**
   	 * 是否查看
   	 */
    private String isSelect;
   	/**
   	 * 是否再次发送通知
   	 */
    private Integer noticePlan;
   	/**
   	 * 回复文件
   	 */
    private String file;
   	/**
   	 * 通知时间
   	 */
    private java.util.Date time;
   	/**
   	 * 消息code
   	 */
    private String messageCode;
   	/**
   	 * 再次发送通知的消息code
   	 */
    private String noticeMessageCode;
   	/**
   	 * 消息类别
   	 */
    private Integer messageType;
   	/**
   	 * 接收人code[如果是组织，则是组织的层级码，如果是人员则是人员的唯一标识符]
   	 */
    private String receiveCode;
   	/**
   	 * 接收人组织唯一标识符
   	 */
    private String receiveOrgCode;
   	/**
   	 * 创建时间
   	 */
    private java.util.Date createTime;
   	/**
   	 * 更新时间
   	 */
    private java.util.Date updateTime;
   	/**
   	 * 删除时间
   	 */
    private java.util.Date deleteTime;

    public MessageNoticeDTO setId(Integer id){
        this.id = id;
        return this;
    }
    public Integer getId() {
    	return this.id;
    }
    public MessageNoticeDTO setCode(String code){
        this.code = code;
        return this;
    }
    public String getCode() {
    	return this.code;
    }
    public MessageNoticeDTO setReplyContext(String replyContext){
        this.replyContext = replyContext;
        return this;
    }
    public String getReplyContext() {
    	return this.replyContext;
    }
    public MessageNoticeDTO setIsSelect(String isSelect){
        this.isSelect = isSelect;
        return this;
    }
    public String getIsSelect() {
    	return this.isSelect;
    }
    public MessageNoticeDTO setNoticePlan(Integer noticePlan){
        this.noticePlan = noticePlan;
        return this;
    }
    public Integer getNoticePlan() {
    	return this.noticePlan;
    }
    public MessageNoticeDTO setFile(String file){
        this.file = file;
        return this;
    }
    public String getFile() {
    	return this.file;
    }
    public MessageNoticeDTO setTime(java.util.Date time){
        this.time = time;
        return this;
    }
    public java.util.Date getTime() {
    	return this.time;
    }
    public MessageNoticeDTO setMessageCode(String messageCode){
        this.messageCode = messageCode;
        return this;
    }
    public String getMessageCode() {
    	return this.messageCode;
    }
    public MessageNoticeDTO setNoticeMessageCode(String noticeMessageCode){
        this.noticeMessageCode = noticeMessageCode;
        return this;
    }
    public String getNoticeMessageCode() {
    	return this.noticeMessageCode;
    }
    public MessageNoticeDTO setMessageType(Integer messageType){
        this.messageType = messageType;
        return this;
    }
    public Integer getMessageType() {
    	return this.messageType;
    }
    public MessageNoticeDTO setReceiveCode(String receiveCode){
        this.receiveCode = receiveCode;
        return this;
    }
    public String getReceiveCode() {
    	return this.receiveCode;
    }
    public MessageNoticeDTO setReceiveOrgCode(String receiveOrgCode){
        this.receiveOrgCode = receiveOrgCode;
        return this;
    }
    public String getReceiveOrgCode() {
    	return this.receiveOrgCode;
    }
    public MessageNoticeDTO setCreateTime(java.util.Date createTime){
        this.createTime = createTime;
        return this;
    }
    public java.util.Date getCreateTime() {
    	return this.createTime;
    }
    public MessageNoticeDTO setUpdateTime(java.util.Date updateTime){
        this.updateTime = updateTime;
        return this;
    }
    public java.util.Date getUpdateTime() {
    	return this.updateTime;
    }
    public MessageNoticeDTO setDeleteTime(java.util.Date deleteTime){
        this.deleteTime = deleteTime;
        return this;
    }
    public java.util.Date getDeleteTime() {
    	return this.deleteTime;
    }


    public MessageNotice toModel(){
        MessageNotice model = new MessageNotice();
        model.setId(this.id);
        model.setCode(this.code);
        model.setReplyContext(this.replyContext);
        model.setIsSelect(this.isSelect);
        model.setNoticePlan(this.noticePlan);
        model.setFile(this.file);
        model.setTime(this.time);
        model.setMessageCode(this.messageCode);
        model.setNoticeMessageCode(this.noticeMessageCode);
        model.setMessageType(this.messageType);
        model.setReceiveCode(this.receiveCode);
        model.setReceiveOrgCode(this.receiveOrgCode);
        model.setCreateTime(this.createTime);
        model.setUpdateTime(this.updateTime);
        model.setDeleteTime(this.deleteTime);
        return model;
    }
}