package com.zenith.front.model.dto;

import com.zenith.front.model.custom.HasSubordinateField;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.Max;
import javax.validation.constraints.Min;
import java.util.List;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2021-11-14
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class MemFlowSignAuditDto extends HasSubordinateField {

    private static final long serialVersionUID = 6967586543498251384L;

    /**
     * 分页页数
     */
    @Min(value = 1, message = "页码最小为1")
    private Integer pageNum;

    /**
     * 分页每页数
     */
    @Max(value = 100, message = "每页条数大小范围在1-100")
    private Integer pageSize;

    private String id;

    private String orgCode;

    private String name;

    private Long signAuditId;

    /**
     * 审批状态，0-待审批，1-审批通过，2-审批未通过
     */
    private String status;

    /**
     * 审批状态，0-待审批，1-审批通过，2-审批未通过
     */
    private List<String> statusList;

    /**
     * 拒绝原因，1-党员不存在，2-党员组织关系转接中，3-党员信息流转中
     */
    private String refuse;

    /**
     * 拒绝原因，1-党员不存在，2-党员组织关系转接中，3-党员信息流转中
     */
    private List<String> refuseList;

    /**
     * 说明理由
     */
    private String reason;

    private String idCard;
}
