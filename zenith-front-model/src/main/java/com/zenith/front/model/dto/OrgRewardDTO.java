package com.zenith.front.model.dto;

import com.zenith.front.model.validate.group.Common8Group;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.Date;

/**
 * <AUTHOR>
 * @version 1.0
 * @Description: 组织奖惩接受模型
 * @date 2019/4/4 20:32
 */
@Data
@ToString
@NoArgsConstructor
@AllArgsConstructor
public class OrgRewardDTO implements IDto {

    private Long id;
    private String code;
    private String esId;
    /**
     * 奖惩代码
     */
    @NotBlank(groups = Common8Group.class, message = "d42Code 不能为空")
    private String d42Code;
    /**
     * 奖惩名称
     */
    @NotBlank(groups = Common8Group.class, message = "d42Name 不能为空")
    private String d42Name;
    /**
     * 奖惩党组织层级码
     */
    @NotBlank(groups = Common8Group.class, message = "rewardOrgCode 不能为空")
    private String rewardOrgCode;
    /**
     * 奖惩党组织唯一组织code
     */
    @NotBlank(groups = Common8Group.class, message = "orgCode 不能为空")
    private String orgCode;
    private String d47Code;
    private String d47Name;
    /**
     * 批准时间
     */
    @NotNull(groups = Common8Group.class, message = "startDate 不能为空")
    private Date startDate;
    private String remark;
    private Integer type;
    private String commendation;
    private String commendationName;
    private Date timestamp;
    private Date deleteTime;
    private Date createTime;
    private Date updateTime;
    /**
     * 中组部唯一标示
     */
    @NotBlank(groups = Common8Group.class, message = "zbCode 不能为空")
    private String zbCode;
    private Integer isHistory;
    private String updateAccount;
    /**
     * 奖励批准单位名称
     */
    private String approveOrgan;
}
