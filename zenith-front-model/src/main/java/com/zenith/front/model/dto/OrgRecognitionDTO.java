package com.zenith.front.model.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2021/9/9 10:14
 * @Version 1.0
 */
@Data
@ToString
@NoArgsConstructor
@AllArgsConstructor
public class OrgRecognitionDTO {

    private String code;

    /**
     * 党组织表彰唯一组织code
     */
    private String orgCode;

    /**
     * 党组织表彰组织层级码
     */
    private String recognitionOrgCode;

    /**
     * 表彰文件号
     */
    private String fileNo;

    /**
     * 表彰对象
     */
    private String recognitionObject;

    /**
     * 表彰级别
     */
    private String recognitionLevel;

    /**
     * 党委(总支部、支部)书记优秀共产党员名数
     */
    private Integer committeeParty;

    /**
     * 党委(总支部、支部)书记优秀党务工作者名数
     */
    private Integer committeeWorker;

    /**
     * 生活困难优秀共产党员名数
     */
    private Integer difficultParty;

    /**
     * 生活困难优秀党务工作者名数
     */
    private Integer difficultWorker;

    private List<OrgRecognitionDataDTO> information;

    private Date annual;

    /**
     * 表彰类型
     */
    private String recognitionType;
    /**
     * 周年情况
     */
    private Integer anniversarySituation;
}
