package com.zenith.front.model.dto;

import cn.hutool.core.util.StrUtil;
import com.zenith.front.common.encrypt.properties.EncryptProperties;
import com.zenith.front.common.untils.SM4Untils;
import com.zenith.front.model.vo.DecryptMap;
import lombok.Data;

import java.lang.reflect.Field;
import java.util.Arrays;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/3/7
 */
@Data
public class CombinedSearchSubDto {
    /**
     * 信息集
     */
    private String infoSetValue;
    /**
     * 左括号
     */
    private String leftBrackets;
    /**
     * 信息项
     */
    private String infoItem;
    /**
     * 运算符
     */
    private String operator;
    /**
     * 值
     */
    private String value;
    /**
     * 右括号
     */
    private String rightBrackets;
    /**
     * 关系 and or
     */
    private String relation;
    /**
     * 唯一标识
     */
    private String cTCI;
    /**
     * Col_Name
     */
    private String colName;
    /**
     * valueList
     */
    private List<Object> valueList;

    /**
     * 兼容处理加密字段相关的搜索问题
     *
     * @return java.lang.String
     * <AUTHOR>
     * @creed Talk is cheap,show me the code
     * @date 2022年10月24日 11时25分
     */
    public String getValueOld() {
        String nginxKey = EncryptProperties.nginxKey;
        Field[] fields = DecryptMap.class.getDeclaredFields();
        if (Arrays.stream(fields).map(Field::getName).anyMatch(simpleName -> StrUtil.equalsIgnoreCase(simpleName, infoItem))) {
            return SM4Untils.encryptContent(nginxKey, value);
        }
        return value;
    }


    public String getValue() {
        String nginxKey = EncryptProperties.nginxKey;
        switch (infoSetValue) {
            case "ccp_mem":
            case "ccp_mem_develop":
                return getValueOld();
            case "ccp_org":
                if (StrUtil.equals(infoItem, "name")) {
                    return value;
                } else {
                    return getValueOld();
                }
            case "ccp_org_committee":
            case "ccp_org_reviewers":
            case "mem_flow":
                if (StrUtil.equalsAny(infoItem, "memName", "memIdcard")) {
                    return SM4Untils.encryptContent(nginxKey, value);
                } else {
                    return value;
                }
            default:
                return value;
        }
    }

}
