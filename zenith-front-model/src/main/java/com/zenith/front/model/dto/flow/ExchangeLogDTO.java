package com.zenith.front.model.dto.flow;

import lombok.Data;

import java.util.Date;

/**
* <p>
* 数据交换步骤 DTO
* </p>
*
* <AUTHOR>
* @date 2025-01-17 14:48:28
*/
@Data
public class ExchangeLogDTO {
    private static final long serialVersionUID = 5820804010557588829L;

    /**
    *本表中唯一标识符
    */
    private String id;
    /**
    *交换区业务类型，直接写表名或者其他
    */
    private String typeName;
    /**
    *操作名称，增删改查之类的
    */
    private String operationName;
    /**
    *业务主键
    */
    private String bizId;
    /**
    *操作时间戳
    */
    private Date operationTimestamp;
    /**
    *是否成功
    */
    private Integer isSuccess;
    /**
    *请求数据
    */
    private String requestData;
    /**
    *请求地址
    */
    private String requestUrl;
    /**
    *请求方法，1-get,2-post
    */
    private String requestMethod;
    /**
    *结果数据
    */
    private String resultData;
    /**
    *如果数据加密，存放解析后的数据
    */
    private String parseData;
    /**
    *创建时间
    */
    private Date createTime;
    /**
    *修改时间
    */
    private Date updateTime;
    /**
    *删除时间
    */
    private Date deleteTime;
    /**
    *备注
    */
    private String remark;
    /**
    *时间戳
    */
    private Date timestamp;

}

