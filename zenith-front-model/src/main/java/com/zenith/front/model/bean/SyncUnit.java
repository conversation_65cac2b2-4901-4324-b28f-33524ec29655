package com.zenith.front.model.bean;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import java.util.Date;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableField;
import java.io.Serializable;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2021-07-11
 */
@TableName("sync_unit")
public class SyncUnit extends Model<SyncUnit> {

    private static final long serialVersionUID=1L;

    /**
     * 组织机构ID,uuid,不能为null
     */
    @TableId(value = "id", type = IdType.ASSIGN_UUID)
    private String id;

    /**
     * 机构实体标识 1：实体机构 2：虚拟机构
     */
    @TableField("actual")
    private String actual;

    /**
     * 组织机构简称
     */
    @TableField("name")
    private String name;

    /**
     * 组织机构全称
     */
    @TableField("fullName")
    private String fullName;

    /**
     * 组织机构编码
     */
    @TableField("code")
    private String code;

    /**
     * 父级组织机构
     */
    @TableField("parentId")
    private Integer parentId;

    /**
     * 层次编码：四位一级
     */
    @TableField("levelCode")
    private String levelCode;

    /**
     * 组织机构ID
     */
    @TableField("openId")
    private Integer openId;

    /**
     * 机构类别 1：分类 2：单位 3：内部机构
     */
    @TableField("type")
    private String type;

    /**
     * 联系电话
     */
    @TableField("tel")
    private String tel;

    /**
     * 排序号
     */
    @TableField("orderNo")
    private Integer orderNo;

    /**
     * 机构状态 1：启用 2：停用 3：删除
     */
    @TableField("state")
    private String state;

    /**
     * 备注
     */
    @TableField("unitnote")
    private String unitnote;

    /**
     * 政务钉钉ID
     */
    @TableField("zwddId")
    private Integer zwddId;

    /**
     * 机构地址
     */
    @TableField("unitAddress")
    private String unitAddress;

    /**
     * 联系人
     */
    @TableField("contact")
    private String contact;

    /**
     * 创建时间,不允许为null
     */
    @TableField("create_time")
    private Date createTime;

    /**
     * 更新时间,不允许为null,默认值是createTime
     */
    @TableField("update_time")
    private Date updateTime;

    /**
     * 0 未删除 1已删除 默认为0
     */
    @TableField("is_delete")
    private Integer isDelete;


    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getActual() {
        return actual;
    }

    public void setActual(String actual) {
        this.actual = actual;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getFullName() {
        return fullName;
    }

    public void setFullName(String fullName) {
        this.fullName = fullName;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public Integer getParentId() {
        return parentId;
    }

    public void setParentId(Integer parentId) {
        this.parentId = parentId;
    }

    public String getLevelCode() {
        return levelCode;
    }

    public void setLevelCode(String levelCode) {
        this.levelCode = levelCode;
    }

    public Integer getOpenId() {
        return openId;
    }

    public void setOpenId(Integer openId) {
        this.openId = openId;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public String getTel() {
        return tel;
    }

    public void setTel(String tel) {
        this.tel = tel;
    }

    public Integer getOrderNo() {
        return orderNo;
    }

    public void setOrderNo(Integer orderNo) {
        this.orderNo = orderNo;
    }

    public String getState() {
        return state;
    }

    public void setState(String state) {
        this.state = state;
    }

    public String getUnitnote() {
        return unitnote;
    }

    public void setUnitnote(String unitnote) {
        this.unitnote = unitnote;
    }

    public Integer getZwddId() {
        return zwddId;
    }

    public void setZwddId(Integer zwddId) {
        this.zwddId = zwddId;
    }

    public String getUnitAddress() {
        return unitAddress;
    }

    public void setUnitAddress(String unitAddress) {
        this.unitAddress = unitAddress;
    }

    public String getContact() {
        return contact;
    }

    public void setContact(String contact) {
        this.contact = contact;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    public Integer getIsDelete() {
        return isDelete;
    }

    public void setIsDelete(Integer isDelete) {
        this.isDelete = isDelete;
    }

    @Override
    public Serializable pkVal() {
        return this.id;
    }

    @Override
    public String toString() {
        return "SyncUnit{" +
        "id=" + id +
        ", actual=" + actual +
        ", name=" + name +
        ", fullName=" + fullName +
        ", code=" + code +
        ", parentId=" + parentId +
        ", levelCode=" + levelCode +
        ", openId=" + openId +
        ", type=" + type +
        ", tel=" + tel +
        ", orderNo=" + orderNo +
        ", state=" + state +
        ", unitnote=" + unitnote +
        ", zwddId=" + zwddId +
        ", unitAddress=" + unitAddress +
        ", contact=" + contact +
        ", createTime=" + createTime +
        ", updateTime=" + updateTime +
        ", isDelete=" + isDelete +
        "}";
    }
}
