package com.zenith.front.model.bean;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableField;
import java.io.Serializable;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2022-04-14
 */
@TableName("report_custom_rule_config")
public class ReportCustomRuleConfig extends Model<ReportCustomRuleConfig> {

    private static final long serialVersionUID = 1L;

    /**
     * 自增长 主键
     */
      @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 年份
     */
    @TableField("year")
    private Integer year;

    /**
     * 字典code
     */
    @TableField("report_code")
    private String reportCode;

    /**
     * 名称
     */
    @TableField("name")
    private String name;


    /**
     * excel名称
     */
    @TableField("excel_name")
    private String excelName;

    /**
     * type 1:公务员 2:班子 3:是事业单位
     */
    @TableField("type")
    private String type;

    /**
     * excel配置
     */
    @TableField("json_config")
    private String jsonConfig;

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public Integer getYear() {
        return year;
    }

    public void setYear(Integer year) {
        this.year = year;
    }

    public String getReportCode() {
        return reportCode;
    }

    public void setReportCode(String reportCode) {
        this.reportCode = reportCode;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getExcelName() {
        return excelName;
    }

    public void setExcelName(String excelName) {
        this.excelName = excelName;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }


    public String getJsonConfig() {
        return jsonConfig;
    }

    public void setJsonConfig(String jsonConfig) {
        this.jsonConfig = jsonConfig;
    }

    @Override
    protected Serializable pkVal() {
        return this.id;
    }

    @Override
    public String toString() {
        return "ReportCustomRuleConfig{" +
                "id=" + id +
                ", year=" + year +
                ", reportCode='" + reportCode + '\'' +
                ", name='" + name + '\'' +
                ", excelName='" + excelName + '\'' +
                ", type='" + type + '\'' +
                ", jsonConfig='" + jsonConfig + '\'' +
                '}';
    }
}
