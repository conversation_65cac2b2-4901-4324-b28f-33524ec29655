package com.zenith.front.model.dto;

/**
 * <AUTHOR>
 * @date 2019/5/174:06 PM
 */
public class AuthDTO {
    /***
     * 用于换取access token的code
     * */
    private String code;
    /***
     * 微信公众号appID
     * */
    private String appId;
    /***
     * 请求的IP地址
     * */
    private String ip;
    /***
     * 请求的浏览器UA头
     * */
    private String uaHead;

    public String getCode() {
        return code;
    }

    public AuthDTO setCode(String code) {
        this.code = code;
        return this;
    }

    public String getAppId() {
        return appId;
    }

    public AuthDTO setAppId(String appId) {
        this.appId = appId;
        return this;
    }

    public String getIp() {
        return ip;
    }

    public AuthDTO setIp(String ip) {
        this.ip = ip;
        return this;
    }

    public String getUaHead() {
        return uaHead;
    }

    public AuthDTO setUaHead(String uaHead) {
        this.uaHead = uaHead;
        return this;
    }

    @Override
    public String toString() {
        return "AuthDTO{" +
                "code='" + code + '\'' +
                ", appId='" + appId + '\'' +
                ", ip='" + ip + '\'' +
                ", uaHead='" + uaHead + '\'' +
                '}';
    }
}
