package com.zenith.front.model.vo;

import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 * @date 2022/5/27
 */
@Data
public class ActivistTransferListVo {
    /**
     * 主键
     */
    private String id;
    /**
     * 用户id
     */
    private String userId;
    /**
     * 名称
     */
    private String name;
    /**
     * 人员id
     */
    private String memId;
    /**
     * 源组织id
     */
    private String srcOrgId;
    /**
     * 源组织名称
     */
    private String srcOrgName;
    /**
     * 目标组织id
     */
    private String targetOrgId;
    /**
     * 目标组织名称
     */
    private String targetOrgName;
    /**
     * 公共节点id
     */
    private String commonOrgId;
    /**
     * 公共节点名称
     */
    private String commonOrgName;
//    /**
//     * 源组织关系数组
//     */
//    private Object srcOrgRelation;
//    /**
//     * 目标组织关系数组
//     */
//    private Object targetOrgRelation;
    /**
     * 转出类型
     */
    private String outType;
    /**
     * 转入类型
     */
    private String inType;
    /**
     * 类型
     */
    private String type;
    /***
     * 类型名称
     * */
    private String typeName;
    /**
     * 审批记录id
     */
    private String currentApprovalId;
    /**
     * 转接状态,0转接中 1 转接完成  2已撤销
     */
    private Integer status;
    /**
     * 审批理由
     */
    private String reason;
    /**
     * 创建时间
     */
    private java.util.Date createTime;
    /**
     * 更新时间
     */
    private java.util.Date updateTime;
    /**
     * 附加信息
     */
    private Object extraData;

    private Integer isOrg;

    private String remark;
    /**
     * 转出时间
     **/
    private Date transferOutTime;
    /***
     * 转接原因代码
     * */
    private String d146Code;
    /***
     * 转接原因名称
     * */
    private String d146Name;

}
