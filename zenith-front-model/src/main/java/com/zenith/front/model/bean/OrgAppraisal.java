package com.zenith.front.model.bean;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableField;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2021-08-16
 */
@TableName("ccp_org_appraisal")
public class OrgAppraisal extends Model<OrgAppraisal> {

    private static final long serialVersionUID = 1L;

    /**
     * 唯一标识符code
     */
    @TableId(value = "code", type = IdType.ASSIGN_UUID)
    private String code;

    /**
     * 组织层级码
     */
    @TableField("org_code")
    private String orgCode;

    /**
     * 评议年度(年份选择框可以选择当前时间倒退5年)
     */
    @TableField("year")
    private Date year;

    /**
     * 应评议人数（数值填写框）
     */
    @TableField("people_to_be_reviewed")
    private Integer peopleToBeReviewed;

    /**
     * 已评议人数
     */
    @TableField("people_reviewed")
    private Integer peopleReviewed;

    /**
     * 评议情况（1开始评议，2结束评议）
     */
    @TableField("situation")
    private Integer situation;

    /**
     * 地点（文本框填写，不超过100字）非必填
     */
    @TableField("place")
    private String place;

    /**
     * 主题（文本框填写，不超过100字）非必填
     */
    @TableField("theme")
    private String theme;

    /**
     * 主要内容（文本框填写，不超过300字）非必填
     */
    @TableField("content")
    private String content;

    /**
     * 创建时间
     */
    @TableField("create_time")
    private Date createTime;

    /**
     * 更新时间
     */
    @TableField("update_time")
    private Date updateTime;

    /**
     * 删除时间
     */
    @TableField("delete_time")
    private Date deleteTime;

    @TableField("start_time")
    private Date startTime;

    @TableField("end_time")
    private Date endTime;

    public Date getStartTime() {
        return startTime;
    }

    public void setStartTime(Date startTime) {
        this.startTime = startTime;
    }

    public Date getEndTime() {
        return endTime;
    }

    public void setEndTime(Date endTime) {
        this.endTime = endTime;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getOrgCode() {
        return orgCode;
    }

    public void setOrgCode(String orgCode) {
        this.orgCode = orgCode;
    }

    public Date getYear() {
        return year;
    }

    public void setYear(Date year) {
        this.year = year;
    }

    public Integer getPeopleToBeReviewed() {
        return peopleToBeReviewed;
    }

    public void setPeopleToBeReviewed(Integer peopleToBeReviewed) {
        this.peopleToBeReviewed = peopleToBeReviewed;
    }

    public Integer getPeopleReviewed() {
        return peopleReviewed;
    }

    public void setPeopleReviewed(Integer peopleReviewed) {
        this.peopleReviewed = peopleReviewed;
    }

    public Integer getSituation() {
        return situation;
    }

    public void setSituation(Integer situation) {
        this.situation = situation;
    }

    public String getPlace() {
        return place;
    }

    public void setPlace(String place) {
        this.place = place;
    }

    public String getTheme() {
        return theme;
    }

    public void setTheme(String theme) {
        this.theme = theme;
    }

    public String getContent() {
        return content;
    }

    public void setContent(String content) {
        this.content = content;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    public Date getDeleteTime() {
        return deleteTime;
    }

    public void setDeleteTime(Date deleteTime) {
        this.deleteTime = deleteTime;
    }

    @Override
    protected Serializable pkVal() {
        return this.code;
    }

    @Override
    public String toString() {
        return "OrgAppraisal{" +
                ", code=" + code +
                ", orgCode=" + orgCode +
                ", year=" + year +
                ", peopleToBeReviewed=" + peopleToBeReviewed +
                ", peopleReviewed=" + peopleReviewed +
                ", situation=" + situation +
                ", place=" + place +
                ", theme=" + theme +
                ", content=" + content +
                ", createTime=" + createTime +
                ", updateTime=" + updateTime +
                ", deleteTime=" + deleteTime +
                "}";
    }
}
