package com.zenith.front.model.vo;

import com.zenith.front.common.encrypt.annotation.EncryptEnabled;
import com.zenith.front.common.encrypt.annotation.EncryptField;
import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 * @since 2025/7/1
 */
@Data
@EncryptEnabled
public class MemDigitalOperationLogVO {


    private String code;

    /**
     * 没得参考意义，数字档案中无法确定是哪个节点处理上传
     * 流程节点:RD_1(新增入党申请)；RD_2_1(一月内需要谈话)；RD_2_2(谈话时间少于10天)；RD_2_3(一月内未进行谈话)；RD_3(已谈话)；RD_4(满足积极份子)；	 JJ_1(待考察人员)；JJ_2(待第一次考察)；JJ_3(待第二次考察)；JJ_4(超半月未按时考察)；JJ_5(持续考察人员)； JJ_6（上级党委备案）；JJ_7(发展对象阶段)；	 FZ_1(支委会审查)；FZ_2(党总支审查)；FZ_3_1(一月内进行预审)；FZ_3_2（预审时间少于十天）；FZ_3_3(一月内未进行预审)；FZ_4(县级党委预审)；FZ_5_1(一月内进行讨论人员)；FZ_5_2(讨论时间少于五天)；FZ_5_3(一月内未进行讨论)；FZ_6_1(三个月内审批人员)；FZ_6_2(审批时间少于十天)；FZ_6_3(审批超时)；FZ_6_4(特殊原因延长审批)；FZ_7(接收预备党员)；
     */
    @Deprecated
    private String processNode;

    /**
     * 档案批次唯一码
     */
    private String digitalLotNo;

    /**
     * 所属档案目录阶段编码:dict_222
     */
    private String d222Code;

    /**
     * 所属档案目录阶段编码:dict_222
     */
    private String d222Name;

    /**
     * 档案名，多个逗号分隔
     */
    private String digitalNames;

    /**
     * 档案code，多个逗号分隔
     */
    private String digitalCodes;

    /**
     * 操作时间
     */
    private Date oprationTime;

    /**
     * 前端录入,操作人（上传人、删除人）
     */
    private String oprationUser;

    /**
     * 操作党组织
     */
    private String oprationOrgName;

    /**
     * 操作类型：1-上传； 2-删除
     */
    private Integer oprationType;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 创建人
     */
    private String createUser;

    /**
     * 修改时间
     */
    private Date updateTime;

    /**
     * 修改人
     */
    private String updateUser;

    /**
     * 删除时间
     */
    private Date deleteTime;

    /**
     * 党员姓名
     */
    @EncryptField(order = 1)
    private String memName;

}
