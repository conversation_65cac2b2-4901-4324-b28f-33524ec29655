package com.zenith.front.model.vo;

import cn.afterturn.easypoi.excel.annotation.Excel;
import lombok.Data;

@Data
public class MemFlow1HistoryVoExcel {
    @Excel(name = "序号", orderNum = "1", height = 8, width = 10, isImportField = "true_st")
    private Integer index;
    @Excel(name = "姓名", orderNum = "2", height = 8, width = 10, isImportField = "true_st")
    private String memName;
    @Excel(name = "性别", orderNum = "3", height = 8, width = 10, isImportField = "true_st")
    private String memSexName;
    @Excel(name = "联系电话", orderNum = "4", height = 8, width = 15, isImportField = "true_st")
    private String memPhone;
    @Excel(name = "流动类型", orderNum = "5", height = 8, width = 50, isImportField = "true_st")
    private String flowTypeName;
    @Excel(name = "流出地党支部", orderNum = "6", height = 8, width = 50, isImportField = "true_st")
    private String memOrgName;
    @Excel(name = "流入地党支部", orderNum = "7", height = 8, width = 50, isImportField = "true_st")
    private String inOrgName;
    @Excel(name = "流出日期", orderNum = "8", height = 8, width = 25, isImportField = "true_st")
    private String outTime;
    @Excel(name = "流回日期", orderNum = "9", height = 8, width = 25, isImportField = "true_st")
    private String flowBackTime;
}
