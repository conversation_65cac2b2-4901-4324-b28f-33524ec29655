package com.zenith.front.model.bean;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2021-11-04
 */
@TableName("ccp_org_recognition_all")
public class OrgRecognitionAll extends Model<OrgRecognitionAll> {

    private static final Long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.ASSIGN_UUID)
    private String id;

    @TableField("code")
    private String code;

    /**
     * 组织code
     */
    @TableField("org_code")
    private String orgCode;
    /**
     * 组织类别
     */
    @TableField("d01_code")
    private String d01Code;

    /**
     * 组织层级码
     */
    @TableField("org_level_code")
    private String orgLevelCode;

    /**
     * 人数
     */
    @TableField("number")
    private Integer number;

    /**
     * 表彰对象 dict_d93
     */
    @TableField("recognition_object")
    private String recognitionObject;

    /**
     * 表彰级别 dict_d94
     */
    @TableField("recognition_level")
    private String recognitionLevel;

    /**
     * 是否删除
     */
    @TableField("delete_time")
    private Date deleteTime;

    /**
     * 表彰类型 dict_d125
     */
    @TableField("recognition_type")
    private String recognitionType;

    /**
     * 党委(总支部、支部)书记优秀共产党员名数
     */
    @TableField("committee_party")
    private Integer committeeParty;

    /**
     * 党委(总支部、支部)书记优秀党务工作者名数
     */
    @TableField("committee_worker")
    private Integer committeeWorker;

    /**
     * 生活困难优秀共产党员名数
     */
    @TableField("difficult_party")
    private Integer difficultParty;

    /**
     * 生活困难优秀党务工作者名数
     */
    @TableField("difficult_worker")
    private Integer difficultWorker;

    /**
     * 追授优秀共产党员名数
     */
    @TableField("add_good_party")
    private Integer addGoodParty;
    /**
     * 建党50周年情况（数）
     */
    @TableField("anniversary_situation")
    private Integer anniversarySituation;
    /**
     * 表彰年度
     */
    @TableField("year")
    private Integer year;

    /**
     * 表彰文件号
     */
    @TableField("file_no")
    private String fileNo;

    /**
     * 年度
     */
    @TableField("annual")
    private Date annual;

    public String getFileNo() {
        return fileNo;
    }

    public void setFileNo(String fileNo) {
        this.fileNo = fileNo;
    }

    public Date getAnnual() {
        return annual;
    }

    public void setAnnual(Date annual) {
        this.annual = annual;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getOrgCode() {
        return orgCode;
    }

    public void setOrgCode(String orgCode) {
        this.orgCode = orgCode;
    }

    public String getD01Code() {
        return d01Code;
    }

    public void setD01Code(String d01Code) {
        this.d01Code = d01Code;
    }

    public String getOrgLevelCode() {
        return orgLevelCode;
    }

    public void setOrgLevelCode(String orgLevelCode) {
        this.orgLevelCode = orgLevelCode;
    }

    public Integer getNumber() {
        return number;
    }

    public void setNumber(Integer number) {
        this.number = number;
    }

    public String getRecognitionObject() {
        return recognitionObject;
    }

    public void setRecognitionObject(String recognitionObject) {
        this.recognitionObject = recognitionObject;
    }

    public String getRecognitionLevel() {
        return recognitionLevel;
    }

    public void setRecognitionLevel(String recognitionLevel) {
        this.recognitionLevel = recognitionLevel;
    }

    public Date getDeleteTime() {
        return deleteTime;
    }

    public void setDeleteTime(Date deleteTime) {
        this.deleteTime = deleteTime;
    }

    public String getRecognitionType() {
        return recognitionType;
    }

    public void setRecognitionType(String recognitionType) {
        this.recognitionType = recognitionType;
    }

    public Integer getCommitteeParty() {
        return committeeParty;
    }

    public void setCommitteeParty(Integer committeeParty) {
        this.committeeParty = committeeParty;
    }

    public Integer getCommitteeWorker() {
        return committeeWorker;
    }

    public void setCommitteeWorker(Integer committeeWorker) {
        this.committeeWorker = committeeWorker;
    }

    public Integer getDifficultParty() {
        return difficultParty;
    }

    public void setDifficultParty(Integer difficultParty) {
        this.difficultParty = difficultParty;
    }

    public Integer getDifficultWorker() {
        return difficultWorker;
    }

    public void setDifficultWorker(Integer difficultWorker) {
        this.difficultWorker = difficultWorker;
    }

    public Integer getAddGoodParty() {
        return addGoodParty;
    }

    public void setAddGoodParty(Integer addGoodParty) {
        this.addGoodParty = addGoodParty;
    }

    public Integer getAnniversarySituation() {
        return anniversarySituation;
    }

    public void setAnniversarySituation(Integer anniversarySituation) {
        this.anniversarySituation = anniversarySituation;
    }

    public Integer getYear() {
        return year;
    }

    public void setYear(Integer year) {
        this.year = year;
    }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    @Override
    protected Serializable pkVal() {
        return this.code;
    }

    @Override
    public String toString() {
        return "OrgRecognitionAll{" +
                "code=" + code +
                ", orgCode=" + orgCode +
                ", orgLevelCode=" + orgLevelCode +
                ", number=" + number +
                ", recognitionObject=" + recognitionObject +
                ", recognitionLevel=" + recognitionLevel +
                ", deleteTime=" + deleteTime +
                ", recognitionType=" + recognitionType +
                ", committeeParty=" + committeeParty +
                ", committeeWorker=" + committeeWorker +
                ", difficultParty=" + difficultParty +
                ", difficultWorker=" + difficultWorker +
                ", addGoodParty=" + addGoodParty +
                ", id=" + id +
                "}";
    }
}
