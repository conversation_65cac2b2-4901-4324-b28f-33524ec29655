package com.zenith.front.model.bean;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import java.util.Date;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableField;

/**
 * <p>
 * 身份证无效表
 * </p>
 *
 * <AUTHOR>
 * @since 2021-12-25
 */
@TableName("ccp_mem_invalid_card")
public class MemInvalidCard extends Model<MemInvalidCard> {

    private static final long serialVersionUID = 1L;

    /**
     * 自增ID
     */
      @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 唯一主键
     */
    @TableField("code")
    private String code;

    /**
     * 姓名
     */
    @TableField("name")
    private String name;


    /**
     * 身份证
     */
    @TableField("idcard")
    private String idcard;

    /**
     * 组织层级code
     */
    @TableField("mem_org_code")
    private String memOrgCode;

    /**
     * 组织唯一标识符
     */
    @TableField("org_code")
    private String orgCode;

    /**
     * 联系电话
     */
    @TableField("phone")
    private String phone;


    @TableField("mem_type")
    private String memType;

    @TableField("create_time")
    private Date createTime;

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public String getMemType() {
        return memType;
    }

    public void setMemType(String memType) {
        this.memType = memType;
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getIdcard() {
        return idcard;
    }

    public void setIdcard(String idcard) {
        this.idcard = idcard;
    }

    public String getMemOrgCode() {
        return memOrgCode;
    }

    public void setMemOrgCode(String memOrgCode) {
        this.memOrgCode = memOrgCode;
    }

    public String getOrgCode() {
        return orgCode;
    }

    public void setOrgCode(String orgCode) {
        this.orgCode = orgCode;
    }

    public String getPhone() {
        return phone;
    }

    public void setPhone(String phone) {
        this.phone = phone;
    }
}
