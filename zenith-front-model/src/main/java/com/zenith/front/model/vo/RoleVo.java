package com.zenith.front.model.vo;

import lombok.AllArgsConstructor;
import lombok.NoArgsConstructor;
import lombok.ToString;

import java.util.Date;

/**
 * <AUTHOR>
 * @version 1.0
 * @Description: 角色用于返回前端的模型
 * @date 2019/3/13 15:21
 */
@ToString
@NoArgsConstructor
@AllArgsConstructor
public class RoleVo {
    /**
     * 角色id,uuid,不能为null
     */
    private String id;
    /**
     * 角色名称,不能为null
     */
    private String name;
    /**
     * 权限码1001011,不能为null,默认权限
     */
    private String permission;
    /**
     * 有效时间,内置角色有效时间为永久值为null
     */
    private Date valid_time;
    /**
     * 创建时间,默认不为null
     */
    private Date create_time;
    /**
     * 更新时间,默认为createTime,不能为null
     */
    private Date update_time;
    /**
     * 创建者,角色account
     */
    private String create_account;
    /**
     * 角色父id
     */
    private String parent_id;
    /**
     * 角色类型
     */
    private long role_type_code;
    /**
     * 修改者,角色account
     */
    private String update_account;
    /**
     * 使用角色的用户名称字符串
     */
    private String userNames;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getPermission() {
        return permission;
    }

    public void setPermission(String permission) {
        this.permission = permission;
    }

    public Date getValid_time() {
        return valid_time;
    }

    public void setValid_time(Date valid_time) {
        this.valid_time = valid_time;
    }

    public Date getCreate_time() {
        return create_time;
    }

    public void setCreate_time(Date create_time) {
        this.create_time = create_time;
    }

    public Date getUpdate_time() {
        return update_time;
    }

    public void setUpdate_time(Date update_time) {
        this.update_time = update_time;
    }

    public String getCreate_account() {
        return create_account;
    }

    public void setCreate_account(String create_account) {
        this.create_account = create_account;
    }

    public String getParent_id() {
        return parent_id;
    }

    public void setParent_id(String parent_id) {
        this.parent_id = parent_id;
    }

    public long getRole_type_code() {
        return role_type_code;
    }

    public void setRole_type_code(long role_type_code) {
        this.role_type_code = role_type_code;
    }

    public String getUpdate_account() {
        return update_account;
    }

    public void setUpdate_account(String update_account) {
        this.update_account = update_account;
    }

    public String getUserNames() {
        return userNames;
    }

    public void setUserNames(String userNames) {
        this.userNames = userNames;
    }
}
