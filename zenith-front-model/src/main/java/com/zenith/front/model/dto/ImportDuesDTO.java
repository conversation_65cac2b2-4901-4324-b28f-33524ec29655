package com.zenith.front.model.dto;

import com.zenith.front.common.annotation.ExcelAttribute;
import lombok.Data;

/**
 * 导入党费缴纳
 *
 * <AUTHOR>
 * @date 20211104
 */
@Data
public class ImportDuesDTO {

    /**
     * 姓名
     */
    @ExcelAttribute(sort = 0)
    private String name;
    /**
     * 出生日期
     */
    @ExcelAttribute(sort = 1)
    private String birthday;
    /**
     * 所在党支部
     */
    @ExcelAttribute(sort = 2)
    private String orgName;
    /**
     * 一月交纳党费
     */
    @ExcelAttribute(sort = 3)
    private String oneDues;
    /**
     * 二月交纳党费
     */
    @ExcelAttribute(sort = 4)
    private String twoDues;
    /**
     * 三月交纳党费
     */
    @ExcelAttribute(sort = 5)
    private String threeDues;
    /**
     * 四月交纳党费
     */
    @ExcelAttribute(sort = 6)
    private String fourDues;
    /**
     * 五月交纳党费
     */
    @ExcelAttribute(sort = 7)
    private String fiveDues;
    /**
     * 六月交纳党费
     */
    @ExcelAttribute(sort = 8)
    private String sixDues;
    /**
     * 七月交纳党费
     */
    @ExcelAttribute(sort = 9)
    private String sevenDues;
    /**
     * 八月交纳党费
     */
    @ExcelAttribute(sort = 10)
    private String eightDues;
    /**
     * 九月交纳党费
     */
    @ExcelAttribute(sort = 11)
    private String nineDues;
    /**
     * 十月交纳党费
     */
    @ExcelAttribute(sort = 12)
    private String tenDues;
    /**
     * 十一月交纳党费
     */
    @ExcelAttribute(sort = 13)
    private String elevenDues;
    /**
     * 十二月交纳党费
     */
    @ExcelAttribute(sort = 14)
    private String twelveDues;
}
