package com.zenith.front.model.dto;

import com.alibaba.fastjson.JSONArray;

import java.io.Serializable;
import java.util.List;

/**
 * 组织报表类
 *
 * <AUTHOR>
 */
public class OrgReportDTO implements Serializable {

    private static final long serialVersionUID = 6316233800638809487L;

    /**
     * code
     */
    private String code;

    /**
     * 发布到组织code
     */
    private List<String> codeList;

    /**
     * 发布内容
     */
    private JSONArray excelContent;

    /**
     * 行
     */
    private Integer r;

    /**
     * 列
     */
    private Integer c;

    /**
     * 分页：当前页
     */
    private Integer pageNum;

    /**
     * 分页：每页显示条数
     */
    private Integer pageSize;

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public List<String> getCodeList() {
        return codeList;
    }

    public void setCodeList(List<String> codeList) {
        this.codeList = codeList;
    }

    public JSONArray getExcelContent() {
        return excelContent;
    }

    public void setExcelContent(JSONArray excelContent) {
        this.excelContent = excelContent;
    }

    public Integer getR() {
        return r;
    }

    public void setR(Integer r) {
        this.r = r;
    }

    public Integer getC() {
        return c;
    }

    public void setC(Integer c) {
        this.c = c;
    }

    public Integer getPageNum() {
        return pageNum;
    }

    public void setPageNum(Integer pageNum) {
        this.pageNum = pageNum;
    }

    public Integer getPageSize() {
        return pageSize;
    }

    public void setPageSize(Integer pageSize) {
        this.pageSize = pageSize;
    }

    @Override
    public String toString() {
        return "OrgReportDTO{" +
                "code='" + code + '\'' +
                ", codeList=" + codeList +
                ", excelContent=" + excelContent +
                ", r=" + r +
                ", c=" + c +
                ", pageNum=" + pageNum +
                ", pageSize=" + pageSize +
                '}';
    }
}
