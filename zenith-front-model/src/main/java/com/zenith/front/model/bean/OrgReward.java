package com.zenith.front.model.bean;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2021-07-11
 */
@TableName("ccp_org_reward")
public class OrgReward extends Model<OrgReward> {

    private static final long serialVersionUID = 1L;

    /**
     * 唯一自增长主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 唯一标识符code
     */
    @TableField("code")
    private String code;

    /**
     * es主键_code
     */
    @TableField("es_id")
    private String esId;

    /**
     * 组织奖惩code
     */
    @TableField("d42_code")
    private String d42Code;

    /**
     * 组织奖惩名称
     */
    @TableField("d42_name")
    private String d42Name;

    /**
     * 奖惩党组织唯一组织code
     */
    @TableField("org_code")
    private String orgCode;

    /**
     * 奖惩党组织层级码
     */
    @TableField("reward_org_code")
    private String rewardOrgCode;

    /**
     * 奖惩原因code
     */
    @TableField("d47_code")
    private String d47Code;

    /**
     * 奖惩原因name
     */
    @TableField("d47_name")
    private String d47Name;

    /**
     * 批准时间
     */
    @TableField("start_date")
    private Date startDate;

    /**
     * 备注
     */
    @TableField("remark")
    private String remark;

    /**
     * 1是奖0是惩戒
     */
    @TableField("type")
    private Integer type;

    /**
     * 表彰类型
     */
    @TableField("commendation")
    private String commendation;

    /**
     * 表彰类型名称
     */
    @TableField("commendation_name")
    private String commendationName;

    /**
     * 时间戳
     */
    @TableField("timestamp")
    private Date timestamp;

    /**
     * 删除时间
     */
    @TableField("delete_time")
    private Date deleteTime;

    /**
     * 创建时间
     */
    @TableField("create_time")
    private Date createTime;

    /**
     * 更新时间
     */
    @TableField("update_time")
    private Date updateTime;

    /**
     * 组织唯一中组部标识
     */
    @TableField("zb_code")
    private String zbCode;

    /**
     * 是否历史数据
     */
    @TableField("is_history")
    private Integer isHistory;

    /**
     * 最近更更新账号
     */
    @TableField("update_account")
    private String updateAccount;

    /**
     * 奖励批准单位名称
     */
    @TableField("approve_organ")
    private String approveOrgan;

    /**
     * 组织名称
     */
    @TableField(exist = false)
    private String orgName;


    public String getOrgName() {
        return orgName;
    }

    public void setOrgName(String orgName) {
        this.orgName = orgName;
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getEsId() {
        return esId;
    }

    public void setEsId(String esId) {
        this.esId = esId;
    }

    public String getD42Code() {
        return d42Code;
    }

    public void setD42Code(String d42Code) {
        this.d42Code = d42Code;
    }

    public String getD42Name() {
        return d42Name;
    }

    public void setD42Name(String d42Name) {
        this.d42Name = d42Name;
    }

    public String getOrgCode() {
        return orgCode;
    }

    public void setOrgCode(String orgCode) {
        this.orgCode = orgCode;
    }

    public String getRewardOrgCode() {
        return rewardOrgCode;
    }

    public void setRewardOrgCode(String rewardOrgCode) {
        this.rewardOrgCode = rewardOrgCode;
    }

    public String getD47Code() {
        return d47Code;
    }

    public void setD47Code(String d47Code) {
        this.d47Code = d47Code;
    }

    public String getD47Name() {
        return d47Name;
    }

    public void setD47Name(String d47Name) {
        this.d47Name = d47Name;
    }

    public Date getStartDate() {
        return startDate;
    }

    public void setStartDate(Date startDate) {
        this.startDate = startDate;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public Integer getType() {
        return type;
    }

    public void setType(Integer type) {
        this.type = type;
    }

    public String getCommendation() {
        return commendation;
    }

    public void setCommendation(String commendation) {
        this.commendation = commendation;
    }

    public String getCommendationName() {
        return commendationName;
    }

    public void setCommendationName(String commendationName) {
        this.commendationName = commendationName;
    }

    public Date getTimestamp() {
        return timestamp;
    }

    public void setTimestamp(Date timestamp) {
        this.timestamp = timestamp;
    }

    public Date getDeleteTime() {
        return deleteTime;
    }

    public void setDeleteTime(Date deleteTime) {
        this.deleteTime = deleteTime;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    public String getZbCode() {
        return zbCode;
    }

    public void setZbCode(String zbCode) {
        this.zbCode = zbCode;
    }

    public Integer getIsHistory() {
        return isHistory;
    }

    public void setIsHistory(Integer isHistory) {
        this.isHistory = isHistory;
    }

    public String getUpdateAccount() {
        return updateAccount;
    }

    public void setUpdateAccount(String updateAccount) {
        this.updateAccount = updateAccount;
    }

    public String getApproveOrgan() {
        return approveOrgan;
    }

    public void setApproveOrgan(String approveOrgan) {
        this.approveOrgan = approveOrgan;
    }

    @Override
    public Serializable pkVal() {
        return this.id;
    }

    @Override
    public String toString() {
        return "OrgReward{" +
                "id=" + id +
                ", code=" + code +
                ", esId=" + esId +
                ", d42Code=" + d42Code +
                ", d42Name=" + d42Name +
                ", orgCode=" + orgCode +
                ", rewardOrgCode=" + rewardOrgCode +
                ", d47Code=" + d47Code +
                ", d47Name=" + d47Name +
                ", startDate=" + startDate +
                ", remark=" + remark +
                ", type=" + type +
                ", commendation=" + commendation +
                ", commendationName=" + commendationName +
                ", timestamp=" + timestamp +
                ", deleteTime=" + deleteTime +
                ", createTime=" + createTime +
                ", updateTime=" + updateTime +
                ", zbCode=" + zbCode +
                ", isHistory=" + isHistory +
                ", updateAccount=" + updateAccount +
                "}";
    }
}
