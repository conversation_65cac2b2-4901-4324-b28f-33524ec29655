package com.zenith.front.model.dto;

import com.zenith.front.model.validate.group.Common1Group;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

import javax.validation.constraints.Max;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @Date 2021/10/8 17:34
 * @Version 1.0
 */
@NoArgsConstructor
@Data
@ToString
public class UnitStreetsCadresListDTO {
    /**
     * 分页页数
     */
    @Min(value = 1, groups = Common1Group.class, message = "页码最小为1")
    @NotNull(groups = Common1Group.class, message = "pageNum 不能为空")
    private Integer pageNum;
    /**
     * 分页每页数
     */
    @Max(value = 100, groups = Common1Group.class, message = "每页条数大小范围在1-100")
    @NotNull(groups = Common1Group.class, message = "pageSize 不能为空")
    private Integer pageSize;
    /**
     * 单位code
     */
    @NotBlank(groups = Common1Group.class, message = "unitCode 不能为空")
    private String unitCode;
}
