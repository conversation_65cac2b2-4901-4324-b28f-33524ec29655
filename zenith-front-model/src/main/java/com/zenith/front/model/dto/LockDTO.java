package com.zenith.front.model.dto;

import com.zenith.front.model.custom.HasSubordinateField;
import org.hibernate.validator.constraints.Range;

import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 */
public class LockDTO extends HasSubordinateField {

    private static final long serialVersionUID = -2280625249646856953L;

    /**
     * 分页：当前页
     */
    private Integer pageNum;

    /**
     * 分页：每页显示条数
     */
    private Integer pageSize;

    /**
     * 组织code
     */
    private String orgCode;

    /**
     * 党员/组织/单位code
     */
    private String code;

    /**
     * 层级码
     */
    private String memCode;

    /**
     * 锁定对象 1 党员 2 单位 3 组织
     */
    @NotNull(message = "锁定对象 1 党员 2 单位 3 组织")
    @Range(min = 1, max = 3, message = "锁定对象 1 党员 2 单位 3 组织")
    private Integer lockObject;

    /**
     * 是否被锁定 1 true 0 false
     */
    private Integer locked;

    /**
     * 搜索关键词（党员《名称，身份证》）
     */
    private String keyword;

    /**
     * 解锁理由/操作原由
     */
    private String unlockReason;

    /**
     * 当前数据code
     */
    private String uqCode;

    public Integer getPageNum() {
        return pageNum;
    }

    public void setPageNum(Integer pageNum) {
        this.pageNum = pageNum;
    }

    public Integer getPageSize() {
        return pageSize;
    }

    public void setPageSize(Integer pageSize) {
        this.pageSize = pageSize;
    }

    public String getOrgCode() {
        return orgCode;
    }

    public void setOrgCode(String orgCode) {
        this.orgCode = orgCode;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getMemCode() {
        return memCode;
    }

    public void setMemCode(String memCode) {
        this.memCode = memCode;
    }

    public Integer getLockObject() {
        return lockObject;
    }

    public void setLockObject(Integer lockObject) {
        this.lockObject = lockObject;
    }

    public Integer getLocked() {
        return locked;
    }

    public void setLocked(Integer locked) {
        this.locked = locked;
    }

    public String getKeyword() {
        return keyword;
    }

    public void setKeyword(String keyword) {
        this.keyword = keyword;
    }

    public String getUnlockReason() {
        return unlockReason;
    }

    public void setUnlockReason(String unlockReason) {
        this.unlockReason = unlockReason;
    }

    public String getUqCode() {
        return uqCode;
    }

    public void setUqCode(String uqCode) {
        this.uqCode = uqCode;
    }

    @Override
    public String toString() {
        return "LockDTO{" +
                "pageNum=" + pageNum +
                ", pageSize=" + pageSize +
                ", orgCode='" + orgCode + '\'' +
                ", code='" + code + '\'' +
                ", memCode='" + memCode + '\'' +
                ", lockObject=" + lockObject +
                ", locked=" + locked +
                ", keyword='" + keyword + '\'' +
                ", unlockReason='" + unlockReason + '\'' +
                ", uqCode='" + uqCode + '\'' +
                '}';
    }
}
