package com.zenith.front.model.vo;

import com.zenith.front.common.annotation.JsonMinioBasePath;
import com.zenith.front.model.validate.group.AddGroup;
import com.zenith.front.model.validate.group.DeleteGroup;
import com.zenith.front.model.validate.group.UpdateGroup;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.util.Date;

/**
 * @author: D.watermelon
 * @date: 2019/4/12 16:33
 * @description: 入则恳恳以尽忠 出则谦谦以自悔
 */
@Data
@ToString
@NoArgsConstructor
@AllArgsConstructor
public class CommitVO {
    private String memName;

    /**
     * 唯一自增长主键
     */
    private Long id;
    /**
     * 唯一标识符code
     */
    @NotBlank(groups = {UpdateGroup.class, DeleteGroup.class}, message = "届次主键不允许为空!")
    private String code;
    /**
     * es唯一主键
     */
    private String esId;
    /**
     * 人员code
     */
    @NotBlank(groups = {AddGroup.class, UpdateGroup.class}, message = "人员code不允许为空!")
    private String memCode;
    /**
     * 党内职务代码
     */
    @NotBlank(groups = {AddGroup.class, UpdateGroup.class}, message = "党内职务代码不允许为空!")
    private String d022Code;
    /**
     * 党内职务名称
     */
    @NotBlank(groups = {AddGroup.class, UpdateGroup.class}, message = "党内职务名称不允许为空!")
    private String d022Name;
    /**
     * 党内职务说明
     */
    private String dutyExplain;
    /**
     * 任职开始时间
     */
    @NotNull(groups = {AddGroup.class, UpdateGroup.class}, message = "任职开始时间不允许为空!")
    private java.util.Date startDate;
    /**
     * 任职结束时间
     */
    @NotNull(groups = {AddGroup.class, UpdateGroup.class}, message = "任职结束时间不允许为空!")
    private java.util.Date endDate;
    /**
     * 是否在任
     */
    private Integer isIncumbent;
    /**
     * 职务级别code
     */
    private String d51Code;
    /**
     * 职务级别name
     */
    private String d51Name;
    /**
     * 决定或批准任职的文号
     */
    private String fileNumber;
    /**
     * 排序
     */
    private Integer sort;
    /**
     * 届次code
     */
    @NotBlank(groups = {AddGroup.class, UpdateGroup.class, DeleteGroup.class}, message = "届次code不允许为空!")
    private String electCode;
    /**
     * 时间戳
     */
    private java.util.Date timestamp;
    /**
     * 删除时间
     */
    private java.util.Date deleteTime;
    /**
     * 更新时间
     */
    private java.util.Date updateTime;
    /**
     * 创建时间
     */
    private java.util.Date createTime;
    /**
     * 组织zb_code
     */
    private String zbCode;
    /**
     * 任职时所在组织唯一标识code
     */
    private String orgCode;
    /**
     * 任职时组织code
     */
    private String positionOrgCode;
    /**
     * 任职时所在组织名称
     */
    private String positionOrgName;
    /**
     * 是否历史数据
     */
    private Integer isHistory;
    /**
     * 最近更更新账号
     */
    private String updateAccount;

    private String memTypeCode;

    private String memTypeName;


    private String sexCode;

    private String sexName;

    private Date birthday;

    private String d07Code;

    private String d07Name;

    private String memIdcard;
    /**
     * 是否中层管理人员 1是 0否
     */
    private Integer hasMiddleManagement;


    /**
     * 离任原因code
     */
    private String d120Code;

    /**
     * 离任原因名称
     */
    private String d120Name;
    /**
     * 人员来源code
     */
    private String d121Code;

    /**
     * 人员来源name
     */
    private String d121Name;

    /**
     * 是否参加县级集中轮训
     */
    private Integer hasPartTraining;


    /**
     * 是否村任职选调生（选择框、必填）(1 是 0 否)
     */
    private Integer hasVillageTransferStudent;

    /**
     * 班子成员来源code
     */
    private String d138Code;

    /**
     * 班子成员来源name
     */
    private String d138Name;

    private BigDecimal reward;

    /**
     * 是否参加城镇职工养老保险
     */
    private Integer endowmentInsuranceForUrbanEmployees;

    /**
     * 是否为五方面人员
     */
    private Integer whetherItIsFromFiveAspects;

    /**
     * 选调单位层级 dict_d144
     */
    private String d144Code;

    private String d144Name;
    /**
     * 政治面貌 dict_d89
     */
    private String d89Code;
    private String d89Name;
    /**
     * 职务
     */
    private String currentPositionJob;
    /**
     * 照片路径
     */
    @JsonMinioBasePath
    private String photoPath;

    private String fileName;
    /**
     * 是否双一流大学生
     */
    private Integer isDoubleFirst;
    /**
     * 任职结束时间  组织换届自动将时间设置成新届次创建时间
     */
    private Date empEndDate;
}




