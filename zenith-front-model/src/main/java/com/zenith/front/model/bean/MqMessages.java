package com.zenith.front.model.bean;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import java.util.Date;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableField;
import java.io.Serializable;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2022-07-26
 */
@TableName("ccp_mq_messages")
public class MqMessages extends Model<MqMessages> {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
      @TableId(value = "code", type = IdType.ASSIGN_UUID)
    private String code;

    /**
     * 消息主键
     */
    @TableField("message_code")
    private String messageCode;

    /**
     * 是否被消费
     */
    @TableField("has_consumed")
    private Integer hasConsumed;

    /**
     * 消费时间
     */
    @TableField("consumed_time")
    private Date consumedTime;

    /**
     * 消费体
     */
    @TableField("consumer")
    private String consumer;

    /**
     * 是否忽略
     */
    @TableField("ignore")
    private Integer ignore;

    /**
     * 忽略者
     */
    @TableField("ignored_by")
    private String ignoredBy;

    /**
     * 消费时间
     */
    @TableField("delete_time")
    private Date deleteTime;

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getMessageCode() {
        return messageCode;
    }

    public void setMessageCode(String messageCode) {
        this.messageCode = messageCode;
    }

    public Integer getHasConsumed() {
        return hasConsumed;
    }

    public void setHasConsumed(Integer hasConsumed) {
        this.hasConsumed = hasConsumed;
    }

    public Date getConsumedTime() {
        return consumedTime;
    }

    public void setConsumedTime(Date consumedTime) {
        this.consumedTime = consumedTime;
    }

    public String getConsumer() {
        return consumer;
    }

    public void setConsumer(String consumer) {
        this.consumer = consumer;
    }

    public Integer getIgnore() {
        return ignore;
    }

    public void setIgnore(Integer ignore) {
        this.ignore = ignore;
    }

    public String getIgnoredBy() {
        return ignoredBy;
    }

    public void setIgnoredBy(String ignoredBy) {
        this.ignoredBy = ignoredBy;
    }

    public Date getDeleteTime() {
        return deleteTime;
    }

    public void setDeleteTime(Date deleteTime) {
        this.deleteTime = deleteTime;
    }

    @Override
    protected Serializable pkVal() {
        return this.code;
    }

    @Override
    public String toString() {
        return "MqMessages{" +
        "code=" + code +
        ", messageCode=" + messageCode +
        ", hasConsumed=" + hasConsumed +
        ", consumedTime=" + consumedTime +
        ", consumer=" + consumer +
        ", ignore=" + ignore +
        ", ignoredBy=" + ignoredBy +
        ", deleteTime=" + deleteTime +
        "}";
    }
}
