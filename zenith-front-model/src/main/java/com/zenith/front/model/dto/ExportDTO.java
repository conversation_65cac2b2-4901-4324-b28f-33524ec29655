package com.zenith.front.model.dto;

//import org.jooq.Condition;

/**
 * <AUTHOR>
 * @version 1.0
 * @Description: 导出对象
 * @date 2019/5/5 18:03
 */
public class ExportDTO {
    /**
     * 字段
     */
    private String field;
    /**
     * 注释/别名
     */
    private String annotation;
    /**
     * 条件
     */
//    private Condition condition;
    /**
     * when值
     */
    private Object result;
    /**
     * else值
     */
    private Object otherwise;

    public String getField() {
        return field;
    }

    public ExportDTO setField(String field) {
        this.field = field;
        return this;
    }

    public String getAnnotation() {
        return annotation;
    }

    public ExportDTO setAnnotation(String annotation) {
        this.annotation = annotation;
        return this;
    }

//    public Condition getCondition() {
//        return condition;
//    }
//
//    public ExportDTO setCondition(Condition condition) {
//        this.condition = condition;
//        return this;
//    }

    public Object getResult() {
        return result;
    }

    public ExportDTO setResult(Object result) {
        this.result = result;
        return this;
    }

    public Object getOtherwise() {
        return otherwise;
    }

    public ExportDTO setOtherwise(Object otherwise) {
        this.otherwise = otherwise;
        return this;
    }

    @Override
    public String toString() {
        return "ExportDTO{" +
                "field='" + field + '\'' +
                ", annotation='" + annotation + '\'' +
//                ", condition=" + condition +
                ", result=" + result +
                ", otherwise=" + otherwise +
                '}';
    }
}
