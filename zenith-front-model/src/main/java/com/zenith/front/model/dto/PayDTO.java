package com.zenith.front.model.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @Description: 党费支付数据模型
 * @date 2019/5/20 12:13
 */
@Data
@ToString
@NoArgsConstructor
@AllArgsConstructor
public class PayDTO {

    private List<FeeOrderDTO> feeOrderDTOList;
    /**
     * 支付金额  小写
     */
    private java.math.BigDecimal money;
    /**
     * 支付金额  大写
     */
    private String chineseMoney;
}
