package com.zenith.front.model.bean;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2022-11-12
 */
@TableName("ccp_mem_flow_sync_log")
public class MemFlowSyncLog extends Model<MemFlowSyncLog> {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(value = "code", type = IdType.ASSIGN_ID)
    private String code;

    /**
     * 是否省内外 省内 1 省外 0
     */
    @TableField("is_province")
    private String isProvince;

    /**
     * 同步JSON数据
     */
    @TableField("sync_json")
    private String syncJson;

    /**
     * 数据类型
     */
    @TableField("data_type")
    private String dataType;

    /**
     * 1 成功 0 失败
     */
    @TableField("state")
    private String state;

    /**
     * 异常消息
     */
    @TableField("err_msg")
    private String errMsg;

    /**
     * 创建时间
     */
    @TableField("create_time")
    private Date createTime;

    /**
     * 操作用户
     */
    @TableField("update_account")
    private String updateAccount;

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getIsProvince() {
        return isProvince;
    }

    public void setIsProvince(String isProvince) {
        this.isProvince = isProvince;
    }

    public String getSyncJson() {
        return syncJson;
    }

    public void setSyncJson(String syncJson) {
        this.syncJson = syncJson;
    }

    public String getDataType() {
        return dataType;
    }

    public void setDataType(String dataType) {
        this.dataType = dataType;
    }

    public String getState() {
        return state;
    }

    public void setState(String state) {
        this.state = state;
    }

    public String getErrMsg() {
        return errMsg;
    }

    public void setErrMsg(String errMsg) {
        this.errMsg = errMsg;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public String getUpdateAccount() {
        return updateAccount;
    }

    public void setUpdateAccount(String updateAccount) {
        this.updateAccount = updateAccount;
    }

    @Override
    protected Serializable pkVal() {
        return this.code;
    }

    @Override
    public String toString() {
        return "MemFlowSyncLog{" +
                "code='" + code + '\'' +
                ", isProvince=" + isProvince +
                ", syncJson=" + syncJson +
                ", dataType=" + dataType +
                ", state=" + state +
                ", errMsg=" + errMsg +
                ", createTime=" + createTime +
                ", updateAccount='" + updateAccount + '\'' +
                '}';
    }
}
