package com.zenith.front.model.vo;

import com.zenith.front.common.encrypt.annotation.EncryptEnabled;
import com.zenith.front.common.encrypt.annotation.EncryptField;
import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 * @since 2025/3/27
 */
@Data
@EncryptEnabled
public class MemAuditListVo {

    private String code;
    /**
     * 姓名
     */
    @EncryptField(order = 1)
    private String name;

    /**
     * 身份证号码
     */
    @EncryptField(order = 2)
    private String idcard;
    /**
     * 性别
     */
    private String sexCode;
    /**
     * 性别name
     */
    private String sexName;
    /**
     * 电话
     */
    @EncryptField(order = 3)
    private String phone;
    /**
     * 人员类型code
     */
    private String d08Code;

    /**
     * 人员类型名称
     */
    private String d08Name;

    /**
     * 管理党组织code
     */
    private String orgCode;

    /**
     * 管理党组织层级码
     */
    private String developOrgCode;
    private String memOrgCode;
    private String orgName;
    private Date createTime;
    private Date updateTime;
    /**
     * 人员code唯一标识
     */
    private String memCode;

    /**
     * 档案批次唯一码
     */
    private String digitalLotNo;
    /**
     * 流程节点
     */
    private String processNode;
    /**
     * 审批时间
     */
    private Date approveTime;
    /**
     * 审核状态，1-审核通过，0-审核未通过
     */
    private String status;
    /**
     * 不通过原因
     */
    private String reason;
    /**
     * 县委审核状态：1未审核，2审核通过，3审核未通过，默认为0
     */
    private Integer auditStatus;
    /**
     * 遵义流程是否有审批权限
     */
    private Boolean approve;

}
