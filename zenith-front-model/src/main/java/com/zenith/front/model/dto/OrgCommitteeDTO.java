package com.zenith.front.model.dto;

import com.baomidou.mybatisplus.annotation.TableField;
import com.zenith.front.common.annotation.FilterMinioBasePath;
import com.zenith.front.model.bean.OrgCommittee;
import com.zenith.front.model.validate.group.AddGroup;
import com.zenith.front.model.validate.group.DeleteGroup;
import com.zenith.front.model.validate.group.UpdateGroup;
import lombok.ToString;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.util.Date;

@ToString
public class OrgCommitteeDTO {

    /**
     * 唯一自增长主键
     */
    private Long id;
    /**
     * 唯一标识符code
     */
    @NotBlank(groups = {UpdateGroup.class, DeleteGroup.class}, message = "届次主键不允许为空!")
    private String code;
    /**
     * es唯一主键
     */
    private String esId;
    /**
     * 人员code
     */
    @NotBlank(groups = {AddGroup.class, UpdateGroup.class}, message = "人员code不允许为空!")
    private String memCode;
    /**
     * 党内职务代码
     */
    @NotBlank(groups = {AddGroup.class, UpdateGroup.class}, message = "党内职务代码不允许为空!")
    private String d022Code;
    /**
     * 党内职务名称
     */
    @NotBlank(groups = {AddGroup.class, UpdateGroup.class}, message = "党内职务名称不允许为空!")
    private String d022Name;
    /**
     * 党内职务说明
     */
    private String dutyExplain;
    /**
     * 任职开始时间
     */
    @NotNull(groups = {AddGroup.class, UpdateGroup.class}, message = "任职开始时间不允许为空!")
    private java.util.Date startDate;
    /**
     * 任职结束时间
     */
    @NotNull(groups = {AddGroup.class, UpdateGroup.class}, message = "任职结束时间不允许为空!")
    private java.util.Date endDate;
    /**
     * 是否在任
     */
    private Integer isIncumbent;
    /**
     * 职务级别code
     */
    private String d51Code;
    /**
     * 职务级别name
     */
    private String d51Name;
    /**
     * 决定或批准任职的文号
     */
    private String fileNumber;
    /**
     * 排序
     */
    private Integer sort;
    /**
     * 届次code
     */
    @NotBlank(groups = {AddGroup.class, UpdateGroup.class, DeleteGroup.class}, message = "届次code不允许为空!")
    private String electCode;
    /**
     * 时间戳
     */
    private java.util.Date timestamp;
    /**
     * 删除时间
     */
    private java.util.Date deleteTime;
    /**
     * 更新时间
     */
    private java.util.Date updateTime;
    /**
     * 创建时间
     */
    private java.util.Date createTime;
    /**
     * 组织zb_code
     */
    private String zbCode;
    /**
     * 任职时所在组织唯一标识code
     */
    private String orgCode;
    /**
     * 任职时组织code
     */
    private String positionOrgCode;
    /**
     * 任职时所在组织名称
     */
    private String positionOrgName;
    /**
     * 是否历史数据
     */
    private Integer isHistory;
    /**
     * 最近更更新账号
     */
    private String updateAccount;

    private String memTypeCode;

    private String memTypeName;

    private String memName;

    private String sexCode;

    private String sexName;

    private Date birthday;

    private String d07Code;

    private String d07Name;

    private String memIdcard;

    /**
     * 是否中层管理人员 1是 0否
     */
    private Integer hasMiddleManagement;


    /**
     * 离任原因code
     */
    private String d120Code;

    /**
     * 离任原因名称
     */
    private String d120Name;
    /**
     * 人员来源code
     */
    private String d121Code;

    /**
     * 人员来源name
     */
    private String d121Name;

    /**
     * 是否参加县级集中轮训
     */
    private Integer hasPartTraining;

    /**
     * 报酬（万元/年）
     */
    private BigDecimal reward;

    /**
     * 是否村任职选调生（选择框、必填）(1 是 0 否)
     */
    private Integer hasVillageTransferStudent;
    /**
     * 选调单位层级
     */
    private String d144Code;
    private String d144Name;
    /**
     * 班子成员来源code
     */
    private String d138Code;

    /**
     * 班子成员来源name
     */
    private String d138Name;

    /**
     * 是否参加城镇职工养老保险
     */
    private Integer endowmentInsuranceForUrbanEmployees;

    /**
     * 是否为五方面人员
     */
    private Integer whetherItIsFromFiveAspects;
    /**
     * 政治面貌 dict_d89
     */
    private String d89Code;
    private String d89Name;
    /**
     * 职务（手填）
     */
    private String currentPositionJob;
    /**
     * 照片路径
     */
    @FilterMinioBasePath
    private String photoPath;
    private String fileName;

    /**
     * 是否双一流大学生
     */
    @TableField("is_double_first")
    private int isDoubleFirst;


    public int getIsDoubleFirst() {
        return isDoubleFirst;
    }

    public void setIsDoubleFirst(int isDoubleFirst) {
        this.isDoubleFirst = isDoubleFirst;
    }


    public String getD120Code() {
        return d120Code;
    }

    public void setD120Code(String d120Code) {
        this.d120Code = d120Code;
    }

    public String getD120Name() {
        return d120Name;
    }

    public void setD120Name(String d120Name) {
        this.d120Name = d120Name;
    }

    public String getD121Code() {
        return d121Code;
    }

    public void setD121Code(String d121Code) {
        this.d121Code = d121Code;
    }

    public String getD121Name() {
        return d121Name;
    }

    public void setD121Name(String d121Name) {
        this.d121Name = d121Name;
    }

    public Integer getHasPartTraining() {
        return hasPartTraining;
    }

    public void setHasPartTraining(Integer hasPartTraining) {
        this.hasPartTraining = hasPartTraining;
    }

    public Integer getHasMiddleManagement() {
        return hasMiddleManagement;
    }

    public void setHasMiddleManagement(Integer hasMiddleManagement) {
        this.hasMiddleManagement = hasMiddleManagement;
    }

    public OrgCommitteeDTO setId(Long id) {
        this.id = id;
        return this;
    }

    public Long getId() {
        return this.id;
    }

    public OrgCommitteeDTO setCode(String code) {
        this.code = code;
        return this;
    }

    public String getCode() {
        return this.code;
    }

    public OrgCommitteeDTO setEsId(String esId) {
        this.esId = esId;
        return this;
    }

    public String getEsId() {
        return this.esId;
    }

    public OrgCommitteeDTO setMemCode(String memCode) {
        this.memCode = memCode;
        return this;
    }

    public String getMemCode() {
        return this.memCode;
    }

    public OrgCommitteeDTO setD022Code(String d022Code) {
        this.d022Code = d022Code;
        return this;
    }

    public String getD022Code() {
        return this.d022Code;
    }

    public OrgCommitteeDTO setD022Name(String d022Name) {
        this.d022Name = d022Name;
        return this;
    }

    public String getD022Name() {
        return this.d022Name;
    }

    public OrgCommitteeDTO setDutyExplain(String dutyExplain) {
        this.dutyExplain = dutyExplain;
        return this;
    }

    public String getDutyExplain() {
        return this.dutyExplain;
    }

    public OrgCommitteeDTO setStartDate(java.util.Date startDate) {
        this.startDate = startDate;
        return this;
    }

    public java.util.Date getStartDate() {
        return this.startDate;
    }

    public OrgCommitteeDTO setEndDate(java.util.Date endDate) {
        this.endDate = endDate;
        return this;
    }

    public java.util.Date getEndDate() {
        return this.endDate;
    }

    public OrgCommitteeDTO setIsIncumbent(Integer isIncumbent) {
        this.isIncumbent = isIncumbent;
        return this;
    }

    public Integer getIsIncumbent() {
        return this.isIncumbent;
    }

    public OrgCommitteeDTO setFileNumber(String fileNumber) {
        this.fileNumber = fileNumber;
        return this;
    }

    public String getFileNumber() {
        return this.fileNumber;
    }

    public OrgCommitteeDTO setSort(Integer sort) {
        this.sort = sort;
        return this;
    }

    public Integer getSort() {
        return this.sort;
    }

    public OrgCommitteeDTO setElectCode(String electCode) {
        this.electCode = electCode;
        return this;
    }

    public String getElectCode() {
        return this.electCode;
    }

    public OrgCommitteeDTO setTimestamp(java.util.Date timestamp) {
        this.timestamp = timestamp;
        return this;
    }

    public java.util.Date getTimestamp() {
        return this.timestamp;
    }

    public OrgCommitteeDTO setDeleteTime(java.util.Date deleteTime) {
        this.deleteTime = deleteTime;
        return this;
    }

    public java.util.Date getDeleteTime() {
        return this.deleteTime;
    }

    public OrgCommitteeDTO setUpdateTime(java.util.Date updateTime) {
        this.updateTime = updateTime;
        return this;
    }

    public java.util.Date getUpdateTime() {
        return this.updateTime;
    }

    public OrgCommitteeDTO setCreateTime(java.util.Date createTime) {
        this.createTime = createTime;
        return this;
    }

    public java.util.Date getCreateTime() {
        return this.createTime;
    }

    public OrgCommitteeDTO setZbCode(String zbCode) {
        this.zbCode = zbCode;
        return this;
    }

    public String getZbCode() {
        return this.zbCode;
    }

    public OrgCommitteeDTO setOrgCode(String orgCode) {
        this.orgCode = orgCode;
        return this;
    }

    public String getOrgCode() {
        return this.orgCode;
    }

    public OrgCommitteeDTO setPositionOrgCode(String positionOrgCode) {
        this.positionOrgCode = positionOrgCode;
        return this;
    }

    public String getPositionOrgCode() {
        return this.positionOrgCode;
    }

    public OrgCommitteeDTO setPositionOrgName(String positionOrgName) {
        this.positionOrgName = positionOrgName;
        return this;
    }

    public String getPositionOrgName() {
        return this.positionOrgName;
    }

    public OrgCommitteeDTO setIsHistory(Integer isHistory) {
        this.isHistory = isHistory;
        return this;
    }

    public Integer getIsHistory() {
        return this.isHistory;
    }

    public OrgCommitteeDTO setUpdateAccount(String updateAccount) {
        this.updateAccount = updateAccount;
        return this;
    }

    public String getUpdateAccount() {
        return this.updateAccount;
    }


    public String getMemTypeCode() {
        return memTypeCode;
    }

    public void setMemTypeCode(String memTypeCode) {
        this.memTypeCode = memTypeCode;
    }

    public String getMemTypeName() {
        return memTypeName;
    }

    public void setMemTypeName(String memTypeName) {
        this.memTypeName = memTypeName;
    }

    public String getMemName() {
        return memName;
    }

    public void setMemName(String memName) {
        this.memName = memName;
    }

    public String getSexCode() {
        return sexCode;
    }

    public void setSexCode(String sexCode) {
        this.sexCode = sexCode;
    }

    public String getSexName() {
        return sexName;
    }

    public void setSexName(String sexName) {
        this.sexName = sexName;
    }

    public Date getBirthday() {
        return birthday;
    }

    public void setBirthday(Date birthday) {
        this.birthday = birthday;
    }

    public String getD07Code() {
        return d07Code;
    }

    public void setD07Code(String d07Code) {
        this.d07Code = d07Code;
    }

    public String getD07Name() {
        return d07Name;
    }

    public void setD07Name(String d07Name) {
        this.d07Name = d07Name;
    }

    public String getMemIdcard() {
        return memIdcard;
    }

    public void setMemIdcard(String memIdcard) {
        this.memIdcard = memIdcard;
    }

    public BigDecimal getReward() {
        return reward;
    }

    public void setReward(BigDecimal reward) {
        this.reward = reward;
    }

    public Integer getHasVillageTransferStudent() {
        return hasVillageTransferStudent;
    }

    public void setHasVillageTransferStudent(Integer hasVillageTransferStudent) {
        this.hasVillageTransferStudent = hasVillageTransferStudent;
    }

    public String getD144Code() {
        return d144Code;
    }

    public void setD144Code(String d144Code) {
        this.d144Code = d144Code;
    }

    public String getD144Name() {
        return d144Name;
    }

    public void setD144Name(String d144Name) {
        this.d144Name = d144Name;
    }

    public String getD138Code() {
        return d138Code;
    }

    public void setD138Code(String d138Code) {
        this.d138Code = d138Code;
    }

    public String getD138Name() {
        return d138Name;
    }

    public void setD138Name(String d138Name) {
        this.d138Name = d138Name;
    }

    public Integer getEndowmentInsuranceForUrbanEmployees() {
        return endowmentInsuranceForUrbanEmployees;
    }

    public void setEndowmentInsuranceForUrbanEmployees(Integer endowmentInsuranceForUrbanEmployees) {
        this.endowmentInsuranceForUrbanEmployees = endowmentInsuranceForUrbanEmployees;
    }

    public Integer getWhetherItIsFromFiveAspects() {
        return whetherItIsFromFiveAspects;
    }

    public void setWhetherItIsFromFiveAspects(Integer whetherItIsFromFiveAspects) {
        this.whetherItIsFromFiveAspects = whetherItIsFromFiveAspects;
    }

    public String getD89Code() {
        return d89Code;
    }

    public void setD89Code(String d89Code) {
        this.d89Code = d89Code;
    }

    public String getD89Name() {
        return d89Name;
    }

    public void setD89Name(String d89Name) {
        this.d89Name = d89Name;
    }

    public String getCurrentPositionJob() {
        return currentPositionJob;
    }

    public void setCurrentPositionJob(String currentPositionJob) {
        this.currentPositionJob = currentPositionJob;
    }

    public String getPhotoPath() {
        return photoPath;
    }

    public void setPhotoPath(String photoPath) {
        this.photoPath = photoPath;
    }

    public String getFileName() {
        return fileName;
    }

    public void setFileName(String fileName) {
        this.fileName = fileName;
    }

    public OrgCommittee toModel() {
        OrgCommittee model = new OrgCommittee();
        model.setId(this.id);
        model.setCode(this.code);
        model.setEsId(this.esId);
        model.setMemCode(this.memCode);
        model.setD022Code(this.d022Code);
        model.setD022Name(this.d022Name);
        model.setDutyExplain(this.dutyExplain);
        model.setStartDate(this.startDate);
        model.setEndDate(this.endDate);
        model.setIsIncumbent(this.isIncumbent);
        model.setD51Code(this.d51Code);
        model.setD51Name(this.d51Name);
        model.setFileNumber(this.fileNumber);
        model.setSort(this.sort);
        model.setElectCode(this.electCode);
        model.setTimestamp(this.timestamp);
        model.setDeleteTime(this.deleteTime);
        model.setUpdateTime(this.updateTime);
        model.setCreateTime(this.createTime);
        model.setZbCode(this.zbCode);
        model.setOrgCode(this.orgCode);
        model.setPositionOrgCode(this.positionOrgCode);
        model.setPositionOrgName(this.positionOrgName);
        model.setIsHistory(this.isHistory);
        model.setUpdateAccount(this.updateAccount);
        model.setMemTypeCode(this.memTypeCode);
        model.setMemTypeName(this.memTypeName);
        model.setBirthday(this.birthday);
        model.setMemName(this.memName);
        model.setSexCode(this.sexCode);
        model.setSexName(this.sexName);
        model.setD07Code(this.d07Code);
        model.setD07Name(this.d07Name);
        model.setMemIdcard(this.memIdcard);
        model.setHasMiddleManagement(this.hasMiddleManagement);
        model.setD120Code(this.d120Code);
        model.setD120Name(this.d120Name);
        model.setD121Code(this.d121Code);
        model.setD121Name(this.d121Name);
        model.setHasPartTraining(this.hasPartTraining);
        model.setReward(this.getReward());
        model.setHasVillageTransferStudent(this.hasVillageTransferStudent);
        model.setD144Code(this.d144Code);
        model.setD144Name(this.d144Name);
        model.setD138Code(this.getD138Code());
        model.setD138Name(this.getD138Name());
        model.setEndowmentInsuranceForUrbanEmployees(this.endowmentInsuranceForUrbanEmployees);
        model.setWhetherItIsFromFiveAspects(this.getWhetherItIsFromFiveAspects());
        model.setD89Code(this.d89Code);
        model.setD89Name(this.d89Name);
        model.setCurrentPositionJob(this.currentPositionJob);
        model.setPhotoPath(this.photoPath);
        model.setIsDoubleFirst(this.isDoubleFirst);
        model.setFileName(this.fileName);
        return model;
    }

    public String getD51Code() {
        return this.d51Code;
    }

    public OrgCommitteeDTO setD51Code(String d51Code) {
        this.d51Code = d51Code;
        return this;
    }

    public String getD51Name() {
        return this.d51Name;
    }

    public OrgCommitteeDTO setD51Name(String d51Name) {
        this.d51Name = d51Name;
        return this;
    }
}