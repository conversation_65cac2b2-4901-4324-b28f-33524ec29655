package com.zenith.front.model.dto;

import com.zenith.front.model.bean.TransferEffectMems;
import lombok.*;

@ToString
public class TransferEffectMemsDTO{

   	/**
   	 * 主键
   	 */
    private String id;
   	/**
   	 * 转接记录id
   	 */
    private String recordId;
   	/**
   	 * 人员id
   	 */
    private String memId;
   	/**
   	 * 人员名称
   	 */
    private String memName;
   	/**
   	 * 组织id
   	 */
    private String orgId;
   	/**
   	 * 组织code
   	 */
    private String orgCode;
   	/**
   	 * 组织名称
   	 */
    private String orgName;
   	/**
   	 * 创建时间
   	 */
    private java.util.Date createTime;

    public TransferEffectMemsDTO setId(String id){
        this.id = id;
        return this;
    }
    public String getId() {
    	return this.id;
    }
    public TransferEffectMemsDTO setRecordId(String recordId){
        this.recordId = recordId;
        return this;
    }
    public String getRecordId() {
    	return this.recordId;
    }
    public TransferEffectMemsDTO setMemId(String memId){
        this.memId = memId;
        return this;
    }
    public String getMemId() {
    	return this.memId;
    }
    public TransferEffectMemsDTO setMemName(String memName){
        this.memName = memName;
        return this;
    }
    public String getMemName() {
    	return this.memName;
    }
    public TransferEffectMemsDTO setOrgId(String orgId){
        this.orgId = orgId;
        return this;
    }
    public String getOrgId() {
    	return this.orgId;
    }
    public TransferEffectMemsDTO setOrgCode(String orgCode){
        this.orgCode = orgCode;
        return this;
    }
    public String getOrgCode() {
    	return this.orgCode;
    }
    public TransferEffectMemsDTO setOrgName(String orgName){
        this.orgName = orgName;
        return this;
    }
    public String getOrgName() {
    	return this.orgName;
    }
    public TransferEffectMemsDTO setCreateTime(java.util.Date createTime){
        this.createTime = createTime;
        return this;
    }
    public java.util.Date getCreateTime() {
    	return this.createTime;
    }


    public TransferEffectMems toModel(){
        TransferEffectMems model = new TransferEffectMems();
        model.setId(this.id);
        model.setRecordId(this.recordId);
        model.setMemId(this.memId);
        model.setMemName(this.memName);
        model.setOrgId(this.orgId);
        model.setOrgCode(this.orgCode);
        model.setOrgName(this.orgName);
        model.setCreateTime(this.createTime);
        return model;
    }
}