package com.zenith.front.model.dto;

import lombok.Data;

import javax.validation.constraints.Max;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2025-02-11 11:17
 */
@Data
public class MemFlowEventsListDTO {
    /**
     * 分页页数
     */
    @Min(value = 1, message = "页码最小为1")
    @NotNull(message = "pageNum不能为空")
    private Integer pageNum;

    /**
     * 分页每页数
     */
    @Max(value = 100, message = "每页条数大小范围在1-100")
    @NotNull(message = "pageSize不能为空")
    private Integer pageSize;

    /**
     * 流动党员登记code
     */
    @NotBlank(message = "memFlowCode不能为空")
    private String memFlowCode;

    /**
     * 流动党员登记code
     */
    private List<String> memFlowCodes;
}
