package com.zenith.front.model.dto;

import com.zenith.front.model.validate.group.Common1Group;
import lombok.ToString;

import javax.validation.constraints.Max;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @Date 2021/10/21 14:11
 * @Version 1.0
 */
@ToString
public class ProblemFeedbackReplyListDTO {
    /**
     * 分页页数
     */
    @Min(value = 1, groups = Common1Group.class, message = "页码最小为1")
    @NotNull(groups = Common1Group.class, message = "pageNum 不能为空")
    private Integer pageNum;
    /**
     * 分页每页数
     */
    @Max(value = 100, groups = Common1Group.class, message = "每页条数大小范围在1-100")
    @NotNull(groups = Common1Group.class, message = "pageSize 不能为空")
    private Integer pageSize;

    @NotNull(groups = Common1Group.class, message = "code 不能为空")
    private String code;

    public Integer getPageNum() {
        return pageNum;
    }

    public void setPageNum(Integer pageNum) {
        this.pageNum = pageNum;
    }

    public Integer getPageSize() {
        return pageSize;
    }

    public void setPageSize(Integer pageSize) {
        this.pageSize = pageSize;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }
}
