package com.zenith.front.model.dto.flow;

import com.baomidou.mybatisplus.annotation.TableField;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

@Data
public class OrgFlowSync implements Serializable {

    private static final long serialVersionUID = 6502880601222450988L;

    /**
     * 自增长主键
     */
    private Long id;

    /**
     * 唯一标识符code
     */
    private String code;

    /**
     * es唯一主键
     */
    private String esId;

    /**
     * 党组织代码
     */
    private String zbCode;

    /**
     * 组织层级码
     */
    private String orgCode;

    /**
     * 名称
     */
    private String name;

    /**
     * 组织简称
     */
    private String shortName;

    /**
     * 组织全称拼音
     */
    private String pinyin;

    /**
     * 是否有效，审批通过才是有效的组织
     */
    private Integer isEnable;

    /**
     * 上级党组织唯一标识符
     */
    private String parentCode;

    /**
     * 上级党组织唯一标识符
     */
    private String parentName;

    /**
     * 父级的党组织类型  1-普通党组织  2-流动党组织
     */
    private Integer parentFlowType;

    /**
     * 党组织类型code
     */
    private String d01Code;

    /**
     * 成立日期
     */
    private Date createDate;

    /**
     * 联系人
     */
    private String contacter;

    /**
     * 联系方式
     */
    private String contactPhone;

    /**
     * 党组织成立类型code
     */
    private String d200Code;

    /**
     * 批准成立的党组织（县级以上党委）
     */
    private String approveCode;

    /**
     * 行政区划代码
     */
    private String administrativeDivision;

    /**
     * 流动的行政区划代码
     */
    private String flowAdministrativeDivision;
    /**
     * 排序
     */
    private Integer sort;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 删除时间
     */
    private Date deleteTime;

    /**
     * 备注
     */
    private String remark;
    /**
     * 依托单位code
     */
    private String d201Code;
    /**
     * 其他依托单位详情
     */
    private String supportUnitDetails;

    /**
     * 1-本地创建  2-交换区数据
     */
    private Integer sourceType;


}
