package com.zenith.front.model.dto;

import com.zenith.front.model.validate.group.Common1Group;
import com.zenith.front.model.validate.group.Common2Group;
import com.zenith.front.model.validate.group.QueryGroup;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

import javax.validation.constraints.Max;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @Description: 党费支出
 * @date 2019/6/4 14:24
 */
@Data
@ToString
@NoArgsConstructor
@AllArgsConstructor
public class FeeDisburseListDTO {

    /**
     * 组织唯一标识
     */
    @NotBlank(groups = Common1Group.class, message = "orgCode 不能为空")
    private String orgCode;
    /**
     * 组织层级码
     */
    @NotBlank(groups = Common1Group.class, message = "disburseOrgCode 不能为空")
    private String disburseOrgCode;
    /**
     * 记录人
     */
    private String recorder;
    /**
     * 时间月份
     */
    @NotNull(groups = Common1Group.class, message = "recordTime 不能为空")
    private Date recordTime;
    /**
     * 收支项目
     */
    private List<String> d68CodeList;
    /**
     * 录入类型
     */
    private List<String> recordTypeList;

    /**
     * 分页页数
     */
    @Min(value = 1, groups = QueryGroup.class, message = "页码最小为1")
    private Integer pageNum;
    /**
     * 分页每页数
     */
    @Max(value = 100, groups = QueryGroup.class, message = "每页条数大小范围在1-100")
    private Integer pageSize;

    /**
     * 查询时间
     */
    @NotNull(groups = Common2Group.class, message = "queryDate 不能为空")
    private Date queryDate;
    /**
     * 是否是第一次
     */
    @NotBlank(groups = Common2Group.class, message = "isFirst 不能为空")
    private String isFirst;
}
