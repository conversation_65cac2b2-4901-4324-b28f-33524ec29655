package com.zenith.front.model.vo;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.zenith.front.common.encrypt.annotation.EncryptEnabled;
import com.zenith.front.common.encrypt.annotation.EncryptField;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @date 2021/12/13
 */
@EncryptEnabled
public class MemDevelopVo implements Serializable {
    private Long id;

    private String code;

    private String esId;

    /**
     * 发展党员姓名
     */
    @EncryptField(order = 1)
    private String name;

    private String pinyin;

    /**
     * 发展党员身份证
     */
    @EncryptField(order = 2)
    private String idcard;

    /**
     * 民族code
     */
    private String d06Code;

    /**
     * 民族名称
     */
    private String d06Name;

    /**
     * 籍贯
     */
    private String d48Code;

    /**
     * 籍贯名称
     */
    private String d48Name;

    /**
     * 性别
     */
    private String sexCode;

    /**
     * 性别name
     */
    private String sexName;

    /**
     * 出生日期
     */
    private Date birthday;

    /**
     * 电话
     */
    @EncryptField(order = 3)
    private String phone;

    /**
     * 学历code
     */
    private String d07Code;

    /**
     * 学历名称
     */
    private String d07Name;

    /**
     * 工作岗位
     */
    private String d09Code;

    /**
     * 工作岗位名称
     */
    private String d09Name;

    /**
     * 婚姻状况code
     */
    private String d60Code;

    /**
     * 婚姻状况名称
     */
    private String d60Name;

    /**
     * 参加工作日期
     */
    private Date joinWorkDate;

    /**
     * 申请入党日期
     */
    private Date applyDate;

    /**
     * 档案管理单位名称
     */
    private String archiveUnit;

    /**
     * 家庭住址
     */
    @EncryptField(order = 4)
    private String homeAddress;

    /**
     * 聘任专业技术职务名称
     */
    private String d19Name;

    /**
     * 聘任专业技术职务code
     */
    private String d19Code;

    /**
     * 新社会阶层类型名称
     */
    private String d20Name;

    /**
     * 新社会阶层类型code
     */
    private String d20Code;

    /**
     * 一线情况code
     */
    private String d21Code;

    /**
     * 一线情况名称
     */
    private String d21Name;

    /**
     * 发展党员类型code
     */
    private String d08Code;

    /**
     * 发展党员类型名称
     */
    private String d08Name;

    /**
     * 是否农民工
     */
    private Integer isFarmer;

    /**
     * 入党申请书扫描件
     */
    private String fileUrl;

    /**
     * 聘任日期
     */
    private Date appointmentDate;

    /**
     * 入党递交党组织code
     */
    private String appliedOrgCode;

    private String appliedOrgZbCode;

    private String appliedOrgName;

    /**
     * 入党递交党组织层级码
     */
    private String developAppliedOrgCode;

    /**
     * 失联日期
     */
    private Date lossDate;

    /**
     * 终止日期
     */
    private Date appointmentEndDate;

    /**
     * 是否高知识群体
     */
    private Integer isHighKnowledge;

    /**
     * 失联情况
     */
    private String d18Name;

    /**
     * 失联情况code
     */
    private String d18Code;

    /**
     * 管理党组织code
     */
    private String orgCode;

    /**
     * 管理党组织层级码
     */
    private String developOrgCode;

    private String orgName;

    private String orgZbCode;

    /**
     * 是否是先进模范人物
     */
    private String advancedModelCode;

    private Date createTime;

    private Date updateTime;

    /**
     * 召开支委会日期(成为积极分子时间
     */
    private Date activeDate;

    /**
     * 召开支委会日期(成为发展对象时间
     */
    private Date objectDate;

    /**
     * 加入中共组织的类别code
     */
    private String joinOrgType;

    /**
     * 进入支部类型
     */
    private String d11Name;

    /**
     * 进入支部类型code
     */
    private String d11Code;

    /**
     * 加入中共组织的类别名称
     */
    private String joinOrgName;

    /**
     * 是否劳务派遣
     */
    private Integer isDispatch;

    private Date deleteTime;

    private Date timestamp;

    private String zbCode;

    private Integer isHistory;

    private String updateAccount;

    /**
     * 是否系统外,1-系统外,0--系统内
     */
    private Integer isOutSystem;

    /**
     * 系统外发展时所在党支部name
     */
    private String outBranchOrgName;

    private String memOrgCode;

    /**
     * 说明理由
     */
    private String instructions;
    /**
     * 毕业院校（专科及以上填写）
     */
    private String byyx;
    /**
     * 毕业专业（专科及以上填写）
     */
    private String d88Code;

    private String d88Name;

    private Integer hasYoungFarmers;

    private Integer hasWorker;

    private String politicsCode;

    private String politicsName;

    /**
     * 在读院校
     */
    private String readingCollege;

    /**
     * 在读专业
     */
    private String readingProfessionalCode;

    /**
     * 在读专业名称
     */
    private String readingProfessionalName;

    /**
     * 学制
     */
    private String educationalSystem;


    /**
     * 是否死亡
     */
    private Integer hasDead;


    /**
     * 死亡时间
     */
    private Date deadTime;


    /**
     * 批准时间
     */
    private Date ratificationTime;

    /**
     * 工作性质
     */
    private String jobNatureCode;
    private String jobNatureCodeName;
    /**
     * 专业技术职称
     */
    private String d126Code;

    /**
     * 专业技术职称名称
     */
    private String d126Name;

    /**
     * 统计单位
     */
    private String statisticalUnit;
    /**
     * 中间交换区单独传的单位标识
     */
    private String middleUnitCode;
    /**
     * 中间交换区单独传的单位名称
     */
    private String middleUnitName;
    /**
     * 自定义单位名称
     */
    private String selfUnitName;
    /**
     * 类别
     */
    private String d01Code;

    private String unitInformation;

    private String d04Code;

    private Integer hasUnitProvince;
    @JsonInclude
    private Integer hasUnitStatistics;
    /**
     * 取消发展原因
     */
    private String cancelDevelopReason;

    /**
     * 入党介绍人是否为本组织人员
     */
    private Integer hasStaffOrganization;
    /**
     * 入党介绍人（发展对象=》预备党员）
     */
    private String topreIntroductionMem;
    /**
     * 召开支委会日期(成为预备党员日期)
     */
    private Date topreJoinOrgDate;
    /**
     * 加入共产党类型code（发展对象=》预备党员）
     */
    private String joinOrgCode;
    private String joinOrgCodeName;
    /**
     * 入党志愿书编号（发展对象=》预备党员）
     */
    private String topreJoinBookNum;
    /**
     * 人员code唯一标识
     */
    private String memCode;
    private Integer legacyData;
    /**
     * 入学时间
     */
    private Date enterSchoolDate;
    /**
     * 知识分子情况
     */
    private String d154Code;

    private String d154Name;

    /**
     * 国民经济行业CODE
     */
    private String d194Code;

    /**
     * 与国民经济行业name
     */
    private String d194Name;

    /**
     * 生产性服务行业CODE
     */
    private String d195Code;

    /**
     * 生产性服务行业name
     */
    private String d195Name;

    /**
     * 档案批次唯一码
     */
    private String digitalLotNo;

    /**
     * 流程节点
     */
    private String processNode;
    /**
     * 党员身份证和姓名编辑次数
     */
    private Integer editIdentityCount;


    public Integer getEditIdentityCount() {
        return editIdentityCount;
    }

    public void setEditIdentityCount(Integer editIdentityCount) {
        this.editIdentityCount = editIdentityCount;
    }

    public String getProcessNode() {
        return processNode;
    }

    public void setProcessNode(String processNode) {
        this.processNode = processNode;
    }

    public String getDigitalLotNo() {
        return digitalLotNo;
    }

    public void setDigitalLotNo(String digitalLotNo) {
        this.digitalLotNo = digitalLotNo;
    }

    public String getD194Code() {
        return d194Code;
    }

    public void setD194Code(String d194Code) {
        this.d194Code = d194Code;
    }

    public String getD194Name() {
        return d194Name;
    }

    public void setD194Name(String d194Name) {
        this.d194Name = d194Name;
    }

    public String getD195Code() {
        return d195Code;
    }

    public void setD195Code(String d195Code) {
        this.d195Code = d195Code;
    }

    public String getD195Name() {
        return d195Name;
    }

    public void setD195Name(String d195Name) {
        this.d195Name = d195Name;
    }

    /**
     * 是否需要自动计算年级（0 否 ，1 是）
     */
    private Integer hasCalculationGrade;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getEsId() {
        return esId;
    }

    public void setEsId(String esId) {
        this.esId = esId;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getPinyin() {
        return pinyin;
    }

    public void setPinyin(String pinyin) {
        this.pinyin = pinyin;
    }

    public String getIdcard() {
        return idcard;
    }

    public void setIdcard(String idcard) {
        this.idcard = idcard;
    }

    public String getD06Code() {
        return d06Code;
    }

    public void setD06Code(String d06Code) {
        this.d06Code = d06Code;
    }

    public String getD06Name() {
        return d06Name;
    }

    public void setD06Name(String d06Name) {
        this.d06Name = d06Name;
    }

    public String getD48Code() {
        return d48Code;
    }

    public void setD48Code(String d48Code) {
        this.d48Code = d48Code;
    }

    public String getD48Name() {
        return d48Name;
    }

    public void setD48Name(String d48Name) {
        this.d48Name = d48Name;
    }

    public String getSexCode() {
        return sexCode;
    }

    public void setSexCode(String sexCode) {
        this.sexCode = sexCode;
    }

    public String getSexName() {
        return sexName;
    }

    public void setSexName(String sexName) {
        this.sexName = sexName;
    }

    public Date getBirthday() {
        return birthday;
    }

    public void setBirthday(Date birthday) {
        this.birthday = birthday;
    }

    public String getPhone() {
        return phone;
    }

    public void setPhone(String phone) {
        this.phone = phone;
    }

    public String getD07Code() {
        return d07Code;
    }

    public void setD07Code(String d07Code) {
        this.d07Code = d07Code;
    }

    public String getD07Name() {
        return d07Name;
    }

    public void setD07Name(String d07Name) {
        this.d07Name = d07Name;
    }

    public String getD09Code() {
        return d09Code;
    }

    public void setD09Code(String d09Code) {
        this.d09Code = d09Code;
    }

    public String getD09Name() {
        return d09Name;
    }

    public void setD09Name(String d09Name) {
        this.d09Name = d09Name;
    }

    public String getD60Code() {
        return d60Code;
    }

    public void setD60Code(String d60Code) {
        this.d60Code = d60Code;
    }

    public String getD60Name() {
        return d60Name;
    }

    public void setD60Name(String d60Name) {
        this.d60Name = d60Name;
    }

    public Date getJoinWorkDate() {
        return joinWorkDate;
    }

    public void setJoinWorkDate(Date joinWorkDate) {
        this.joinWorkDate = joinWorkDate;
    }

    public Date getApplyDate() {
        return applyDate;
    }

    public void setApplyDate(Date applyDate) {
        this.applyDate = applyDate;
    }

    public String getArchiveUnit() {
        return archiveUnit;
    }

    public void setArchiveUnit(String archiveUnit) {
        this.archiveUnit = archiveUnit;
    }

    public String getHomeAddress() {
        return homeAddress;
    }

    public void setHomeAddress(String homeAddress) {
        this.homeAddress = homeAddress;
    }

    public String getD19Name() {
        return d19Name;
    }

    public void setD19Name(String d19Name) {
        this.d19Name = d19Name;
    }

    public String getD19Code() {
        return d19Code;
    }

    public void setD19Code(String d19Code) {
        this.d19Code = d19Code;
    }

    public String getD20Name() {
        return d20Name;
    }

    public void setD20Name(String d20Name) {
        this.d20Name = d20Name;
    }

    public String getD20Code() {
        return d20Code;
    }

    public void setD20Code(String d20Code) {
        this.d20Code = d20Code;
    }

    public String getD21Code() {
        return d21Code;
    }

    public void setD21Code(String d21Code) {
        this.d21Code = d21Code;
    }

    public String getD21Name() {
        return d21Name;
    }

    public void setD21Name(String d21Name) {
        this.d21Name = d21Name;
    }

    public String getD08Code() {
        return d08Code;
    }

    public void setD08Code(String d08Code) {
        this.d08Code = d08Code;
    }

    public String getD08Name() {
        return d08Name;
    }

    public void setD08Name(String d08Name) {
        this.d08Name = d08Name;
    }

    public Integer getIsFarmer() {
        return isFarmer;
    }

    public void setIsFarmer(Integer isFarmer) {
        this.isFarmer = isFarmer;
    }

    public String getFileUrl() {
        return fileUrl;
    }

    public void setFileUrl(String fileUrl) {
        this.fileUrl = fileUrl;
    }

    public Date getAppointmentDate() {
        return appointmentDate;
    }

    public void setAppointmentDate(Date appointmentDate) {
        this.appointmentDate = appointmentDate;
    }

    public String getAppliedOrgCode() {
        return appliedOrgCode;
    }

    public void setAppliedOrgCode(String appliedOrgCode) {
        this.appliedOrgCode = appliedOrgCode;
    }

    public String getAppliedOrgZbCode() {
        return appliedOrgZbCode;
    }

    public void setAppliedOrgZbCode(String appliedOrgZbCode) {
        this.appliedOrgZbCode = appliedOrgZbCode;
    }

    public String getAppliedOrgName() {
        return appliedOrgName;
    }

    public void setAppliedOrgName(String appliedOrgName) {
        this.appliedOrgName = appliedOrgName;
    }

    public String getDevelopAppliedOrgCode() {
        return developAppliedOrgCode;
    }

    public void setDevelopAppliedOrgCode(String developAppliedOrgCode) {
        this.developAppliedOrgCode = developAppliedOrgCode;
    }

    public Date getLossDate() {
        return lossDate;
    }

    public void setLossDate(Date lossDate) {
        this.lossDate = lossDate;
    }

    public Date getAppointmentEndDate() {
        return appointmentEndDate;
    }

    public void setAppointmentEndDate(Date appointmentEndDate) {
        this.appointmentEndDate = appointmentEndDate;
    }

    public Integer getIsHighKnowledge() {
        return isHighKnowledge;
    }

    public void setIsHighKnowledge(Integer isHighKnowledge) {
        this.isHighKnowledge = isHighKnowledge;
    }

    public String getD18Name() {
        return d18Name;
    }

    public void setD18Name(String d18Name) {
        this.d18Name = d18Name;
    }

    public String getD18Code() {
        return d18Code;
    }

    public void setD18Code(String d18Code) {
        this.d18Code = d18Code;
    }

    public String getOrgCode() {
        return orgCode;
    }

    public void setOrgCode(String orgCode) {
        this.orgCode = orgCode;
    }

    public String getDevelopOrgCode() {
        return developOrgCode;
    }

    public void setDevelopOrgCode(String developOrgCode) {
        this.developOrgCode = developOrgCode;
    }

    public String getOrgName() {
        return orgName;
    }

    public void setOrgName(String orgName) {
        this.orgName = orgName;
    }

    public String getOrgZbCode() {
        return orgZbCode;
    }

    public void setOrgZbCode(String orgZbCode) {
        this.orgZbCode = orgZbCode;
    }

    public String getAdvancedModelCode() {
        return advancedModelCode;
    }

    public void setAdvancedModelCode(String advancedModelCode) {
        this.advancedModelCode = advancedModelCode;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    public Date getActiveDate() {
        return activeDate;
    }

    public void setActiveDate(Date activeDate) {
        this.activeDate = activeDate;
    }

    public Date getObjectDate() {
        return objectDate;
    }

    public void setObjectDate(Date objectDate) {
        this.objectDate = objectDate;
    }

    public String getJoinOrgType() {
        return joinOrgType;
    }

    public void setJoinOrgType(String joinOrgType) {
        this.joinOrgType = joinOrgType;
    }

    public String getD11Name() {
        return d11Name;
    }

    public void setD11Name(String d11Name) {
        this.d11Name = d11Name;
    }

    public String getD11Code() {
        return d11Code;
    }

    public void setD11Code(String d11Code) {
        this.d11Code = d11Code;
    }

    public String getJoinOrgName() {
        return joinOrgName;
    }

    public void setJoinOrgName(String joinOrgName) {
        this.joinOrgName = joinOrgName;
    }

    public Integer getIsDispatch() {
        return isDispatch;
    }

    public void setIsDispatch(Integer isDispatch) {
        this.isDispatch = isDispatch;
    }

    public Date getDeleteTime() {
        return deleteTime;
    }

    public void setDeleteTime(Date deleteTime) {
        this.deleteTime = deleteTime;
    }

    public Date getTimestamp() {
        return timestamp;
    }

    public void setTimestamp(Date timestamp) {
        this.timestamp = timestamp;
    }

    public String getZbCode() {
        return zbCode;
    }

    public void setZbCode(String zbCode) {
        this.zbCode = zbCode;
    }

    public Integer getIsHistory() {
        return isHistory;
    }

    public void setIsHistory(Integer isHistory) {
        this.isHistory = isHistory;
    }

    public String getUpdateAccount() {
        return updateAccount;
    }

    public void setUpdateAccount(String updateAccount) {
        this.updateAccount = updateAccount;
    }

    public Integer getIsOutSystem() {
        return isOutSystem;
    }

    public void setIsOutSystem(Integer isOutSystem) {
        this.isOutSystem = isOutSystem;
    }

    public String getOutBranchOrgName() {
        return outBranchOrgName;
    }

    public void setOutBranchOrgName(String outBranchOrgName) {
        this.outBranchOrgName = outBranchOrgName;
    }

    public String getMemOrgCode() {
        return memOrgCode;
    }

    public void setMemOrgCode(String memOrgCode) {
        this.memOrgCode = memOrgCode;
    }

    public String getInstructions() {
        return instructions;
    }

    public void setInstructions(String instructions) {
        this.instructions = instructions;
    }

    public String getByyx() {
        return byyx;
    }

    public void setByyx(String byyx) {
        this.byyx = byyx;
    }

    public String getD88Code() {
        return d88Code;
    }

    public void setD88Code(String d88Code) {
        this.d88Code = d88Code;
    }

    public String getD88Name() {
        return d88Name;
    }

    public void setD88Name(String d88Name) {
        this.d88Name = d88Name;
    }

    public Integer getHasYoungFarmers() {
        return hasYoungFarmers;
    }

    public void setHasYoungFarmers(Integer hasYoungFarmers) {
        this.hasYoungFarmers = hasYoungFarmers;
    }

    public Integer getHasWorker() {
        return hasWorker;
    }

    public void setHasWorker(Integer hasWorker) {
        this.hasWorker = hasWorker;
    }

    public String getPoliticsCode() {
        return politicsCode;
    }

    public void setPoliticsCode(String politicsCode) {
        this.politicsCode = politicsCode;
    }

    public String getPoliticsName() {
        return politicsName;
    }

    public void setPoliticsName(String politicsName) {
        this.politicsName = politicsName;
    }

    public String getReadingCollege() {
        return readingCollege;
    }

    public void setReadingCollege(String readingCollege) {
        this.readingCollege = readingCollege;
    }

    public String getReadingProfessionalCode() {
        return readingProfessionalCode;
    }

    public void setReadingProfessionalCode(String readingProfessionalCode) {
        this.readingProfessionalCode = readingProfessionalCode;
    }

    public String getReadingProfessionalName() {
        return readingProfessionalName;
    }

    public void setReadingProfessionalName(String readingProfessionalName) {
        this.readingProfessionalName = readingProfessionalName;
    }

    public String getEducationalSystem() {
        return educationalSystem;
    }

    public void setEducationalSystem(String educationalSystem) {
        this.educationalSystem = educationalSystem;
    }

    public Integer getHasDead() {
        return hasDead;
    }

    public void setHasDead(Integer hasDead) {
        this.hasDead = hasDead;
    }

    public Date getDeadTime() {
        return deadTime;
    }

    public void setDeadTime(Date deadTime) {
        this.deadTime = deadTime;
    }

    public Date getRatificationTime() {
        return ratificationTime;
    }

    public void setRatificationTime(Date ratificationTime) {
        this.ratificationTime = ratificationTime;
    }

    public String getJobNatureCode() {
        return jobNatureCode;
    }

    public void setJobNatureCode(String jobNatureCode) {
        this.jobNatureCode = jobNatureCode;
    }

    public String getJobNatureCodeName() {
        return jobNatureCodeName;
    }

    public void setJobNatureCodeName(String jobNatureCodeName) {
        this.jobNatureCodeName = jobNatureCodeName;
    }

    public String getD126Code() {
        return d126Code;
    }

    public void setD126Code(String d126Code) {
        this.d126Code = d126Code;
    }

    public String getD126Name() {
        return d126Name;
    }

    public void setD126Name(String d126Name) {
        this.d126Name = d126Name;
    }

    public String getStatisticalUnit() {
        return statisticalUnit;
    }

    public void setStatisticalUnit(String statisticalUnit) {
        this.statisticalUnit = statisticalUnit;
    }

    public String getMiddleUnitCode() {
        return middleUnitCode;
    }

    public void setMiddleUnitCode(String middleUnitCode) {
        this.middleUnitCode = middleUnitCode;
    }

    public String getMiddleUnitName() {
        return middleUnitName;
    }

    public void setMiddleUnitName(String middleUnitName) {
        this.middleUnitName = middleUnitName;
    }

    public String getSelfUnitName() {
        return selfUnitName;
    }

    public void setSelfUnitName(String selfUnitName) {
        this.selfUnitName = selfUnitName;
    }

    public String getD01Code() {
        return d01Code;
    }

    public void setD01Code(String d01Code) {
        this.d01Code = d01Code;
    }

    public String getUnitInformation() {
        return unitInformation;
    }

    public void setUnitInformation(String unitInformation) {
        this.unitInformation = unitInformation;
    }

    public String getD04Code() {
        return d04Code;
    }

    public void setD04Code(String d04Code) {
        this.d04Code = d04Code;
    }

    public Integer getHasUnitProvince() {
        return hasUnitProvince;
    }

    public void setHasUnitProvince(Integer hasUnitProvince) {
        this.hasUnitProvince = hasUnitProvince;
    }

    public Integer getHasUnitStatistics() {
        return hasUnitStatistics;
    }

    public void setHasUnitStatistics(Integer hasUnitStatistics) {
        this.hasUnitStatistics = hasUnitStatistics;
    }

    public String getCancelDevelopReason() {
        return cancelDevelopReason;
    }

    public void setCancelDevelopReason(String cancelDevelopReason) {
        this.cancelDevelopReason = cancelDevelopReason;
    }

    public Integer getHasStaffOrganization() {
        return hasStaffOrganization;
    }

    public void setHasStaffOrganization(Integer hasStaffOrganization) {
        this.hasStaffOrganization = hasStaffOrganization;
    }

    public String getTopreIntroductionMem() {
        return topreIntroductionMem;
    }

    public void setTopreIntroductionMem(String topreIntroductionMem) {
        this.topreIntroductionMem = topreIntroductionMem;
    }

    public Date getTopreJoinOrgDate() {
        return topreJoinOrgDate;
    }

    public void setTopreJoinOrgDate(Date topreJoinOrgDate) {
        this.topreJoinOrgDate = topreJoinOrgDate;
    }

    public String getJoinOrgCode() {
        return joinOrgCode;
    }

    public void setJoinOrgCode(String joinOrgCode) {
        this.joinOrgCode = joinOrgCode;
    }

    public String getJoinOrgCodeName() {
        return joinOrgCodeName;
    }

    public void setJoinOrgCodeName(String joinOrgCodeName) {
        this.joinOrgCodeName = joinOrgCodeName;
    }

    public String getTopreJoinBookNum() {
        return topreJoinBookNum;
    }

    public void setTopreJoinBookNum(String topreJoinBookNum) {
        this.topreJoinBookNum = topreJoinBookNum;
    }

    public String getMemCode() {
        return memCode;
    }

    public void setMemCode(String memCode) {
        this.memCode = memCode;
    }

    public Integer getLegacyData() {
        return legacyData;
    }

    public void setLegacyData(Integer legacyData) {
        this.legacyData = legacyData;
    }

    public Date getEnterSchoolDate() {
        return enterSchoolDate;
    }

    public void setEnterSchoolDate(Date enterSchoolDate) {
        this.enterSchoolDate = enterSchoolDate;
    }

    public String getD154Code() {
        return d154Code;
    }

    public void setD154Code(String d154Code) {
        this.d154Code = d154Code;
    }

    public String getD154Name() {
        return d154Name;
    }

    public void setD154Name(String d154Name) {
        this.d154Name = d154Name;
    }

    public Integer getHasCalculationGrade() {
        return hasCalculationGrade;
    }

    public void setHasCalculationGrade(Integer hasCalculationGrade) {
        this.hasCalculationGrade = hasCalculationGrade;
    }
}
