package com.zenith.front.model.bean;

import java.math.BigDecimal;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import java.util.Date;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableField;
import java.io.Serializable;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2021-07-11
 */
@TableName("ccp_fee_disburse")
public class FeeDisburse extends Model<FeeDisburse> {

    private static final long serialVersionUID=1L;

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    @TableField("code")
    private String code;

    /**
     * 组织标识
     */
    @TableField("org_code")
    private String orgCode;

    /**
     * 组织层级码
     */
    @TableField("disburse_org_code")
    private String disburseOrgCode;

    /**
     * 初始数据时所在的组织code
     */
    @TableField("init_org_code_set")
    private String initOrgCodeSet;

    /**
     * 收支项目
     */
    @TableField("d68_code")
    private String d68Code;

    @TableField("d68_name")
    private String d68Name;

    /**
     * 金额
     */
    @TableField("money")
    private BigDecimal money;

    /**
     * 记录人
     */
    @TableField("recorder")
    private String recorder;

    /**
     * 修改账户
     */
    @TableField("update_account")
    private String updateAccount;

    /**
     * 时间月份
     */
    @TableField("record_time")
    private Date recordTime;

    /**
     * 记录时间,创建时间
     */
    @TableField("create_time")
    private Date createTime;

    @TableField("update_time")
    private Date updateTime;

    @TableField("delete_time")
    private Date deleteTime;

    /**
     * 录入类型(0--手动录入,1--账单录入)
     */
    @TableField("record_type")
    private String recordType;

    /**
     * 账号
     */
    @TableField("bank_num")
    private String bankNum;

    /**
     * 订单来源(0--微信流水,1--工商流水)
     */
    @TableField("order_source_type")
    private String orderSourceType;


    @TableField(exist = false)
    private String orgName;

    public String getOrgName() {
        return orgName;
    }

    public void setOrgName(String orgName) {
        this.orgName = orgName;
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getOrgCode() {
        return orgCode;
    }

    public void setOrgCode(String orgCode) {
        this.orgCode = orgCode;
    }

    public String getDisburseOrgCode() {
        return disburseOrgCode;
    }

    public void setDisburseOrgCode(String disburseOrgCode) {
        this.disburseOrgCode = disburseOrgCode;
    }

    public String getInitOrgCodeSet() {
        return initOrgCodeSet;
    }

    public void setInitOrgCodeSet(String initOrgCodeSet) {
        this.initOrgCodeSet = initOrgCodeSet;
    }

    public String getD68Code() {
        return d68Code;
    }

    public void setD68Code(String d68Code) {
        this.d68Code = d68Code;
    }

    public String getD68Name() {
        return d68Name;
    }

    public void setD68Name(String d68Name) {
        this.d68Name = d68Name;
    }

    public BigDecimal getMoney() {
        return money;
    }

    public void setMoney(BigDecimal money) {
        this.money = money;
    }

    public String getRecorder() {
        return recorder;
    }

    public void setRecorder(String recorder) {
        this.recorder = recorder;
    }

    public String getUpdateAccount() {
        return updateAccount;
    }

    public void setUpdateAccount(String updateAccount) {
        this.updateAccount = updateAccount;
    }

    public Date getRecordTime() {
        return recordTime;
    }

    public void setRecordTime(Date recordTime) {
        this.recordTime = recordTime;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    public Date getDeleteTime() {
        return deleteTime;
    }

    public void setDeleteTime(Date deleteTime) {
        this.deleteTime = deleteTime;
    }

    public String getRecordType() {
        return recordType;
    }

    public void setRecordType(String recordType) {
        this.recordType = recordType;
    }

    public String getBankNum() {
        return bankNum;
    }

    public void setBankNum(String bankNum) {
        this.bankNum = bankNum;
    }

    public String getOrderSourceType() {
        return orderSourceType;
    }

    public void setOrderSourceType(String orderSourceType) {
        this.orderSourceType = orderSourceType;
    }

    @Override
    public Serializable pkVal() {
        return this.id;
    }

    @Override
    public String toString() {
        return "FeeDisburse{" +
        "id=" + id +
        ", code=" + code +
        ", orgCode=" + orgCode +
        ", disburseOrgCode=" + disburseOrgCode +
        ", initOrgCodeSet=" + initOrgCodeSet +
        ", d68Code=" + d68Code +
        ", d68Name=" + d68Name +
        ", money=" + money +
        ", recorder=" + recorder +
        ", updateAccount=" + updateAccount +
        ", recordTime=" + recordTime +
        ", createTime=" + createTime +
        ", updateTime=" + updateTime +
        ", deleteTime=" + deleteTime +
        ", recordType=" + recordType +
        ", bankNum=" + bankNum +
        ", orderSourceType=" + orderSourceType +
        "}";
    }
}
