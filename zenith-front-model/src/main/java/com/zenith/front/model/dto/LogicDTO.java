package com.zenith.front.model.dto;

import java.io.Serializable;

/**
 * <AUTHOR>
 */
public class LogicDTO implements Serializable {

    private static final long serialVersionUID = 5627126387172642077L;

    /**
     * 机构码
     */
    private String orgCode;

    /**
     * 表名
     */
    private String tableName;

    /**
     * 字典表配置id
     */
    private String id;

    /**
     * 当前页
     */
    public Integer pageNum;

    /**
     * 每页显示条数
     */
    public Integer pageSize;

    public String getOrgCode() {
        return orgCode;
    }

    public void setOrgCode(String orgCode) {
        this.orgCode = orgCode;
    }

    public String getTableName() {
        return tableName;
    }

    public void setTableName(String tableName) {
        this.tableName = tableName;
    }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public Integer getPageNum() {
        return pageNum;
    }

    public void setPageNum(Integer pageNum) {
        this.pageNum = pageNum;
    }

    public Integer getPageSize() {
        return pageSize;
    }

    public void setPageSize(Integer pageSize) {
        this.pageSize = pageSize;
    }

    @Override
    public String toString() {
        return "LogicDTO{" +
                "orgCode='" + orgCode + '\'' +
                ", tableName='" + tableName + '\'' +
                ", id='" + id + '\'' +
                ", pageNum=" + pageNum +
                ", pageSize=" + pageSize +
                '}';
    }
}
