package com.zenith.front.model.bean;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2022-05-09
 */
@TableName("ccp_activist_transfer_log")
public class ActivistTransferLog extends Model<ActivistTransferLog> {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.ASSIGN_UUID)
    private String id;

    /**
     * 处理审批的id
     */
    @TableField("handle_approval_id")
    private String handleApprovalId;

    /**
     * 受影响的审批id
     */
    @TableField("effect_approval_id")
    private String effectApprovalId;

    /**
     * 操作类型,0 审批通过 1发起转接 2 驳回 3更改目标组织
     */
    @TableField("type")
    private Integer type;

    /**
     * 操作原因
     */
    @TableField("reason")
    private String reason;

    @TableField("create_time")
    private Date createTime;

    @TableField("update_time")
    private Date updateTime;


    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getHandleApprovalId() {
        return handleApprovalId;
    }

    public void setHandleApprovalId(String handleApprovalId) {
        this.handleApprovalId = handleApprovalId;
    }

    public String getEffectApprovalId() {
        return effectApprovalId;
    }

    public void setEffectApprovalId(String effectApprovalId) {
        this.effectApprovalId = effectApprovalId;
    }

    public Integer getType() {
        return type;
    }

    public void setType(Integer type) {
        this.type = type;
    }

    public String getReason() {
        return reason;
    }

    public void setReason(String reason) {
        this.reason = reason;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    @Override
    protected Serializable pkVal() {
        return this.id;
    }

    @Override
    public String toString() {
        return "ActivistTransferLog{" +
                "id=" + id +
                ", handleApprovalId=" + handleApprovalId +
                ", effectApprovalId=" + effectApprovalId +
                ", type=" + type +
                ", reason=" + reason +
                ", createTime=" + createTime +
                ", updateTime=" + updateTime +
                "}";
    }
}
