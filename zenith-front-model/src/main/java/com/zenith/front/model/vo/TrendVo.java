package com.zenith.front.model.vo;

import com.zenith.front.model.dto.FileDto;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

import java.util.List;

/**
 * @author: D.watermelon
 * @date: 2019/6/14 15:03
 * @description: 入则恳恳以尽忠 出则谦谦以自悔
 */
@Data
@ToString
@NoArgsConstructor
@AllArgsConstructor
public class TrendVo {
    /**
     * 增加反馈实体属性
     */
    private String createOrgName;

    /**
     * 自增长主键
     */
    private Integer id;
    /**
     * 唯一标识符
     */

    private String code;
    /**
     * 动态标题
     */

    private String tittle;
    /**
     * 动态内容
     */

    private String context;
    /**
     * 动态类型
     */

    private Integer type;
    /**
     * 动态可见状态（1.公开，0.不公开）
     */
    private Integer selectType;
    /**
     * 推送状态(1.预发布，2.已发布)
     */
    private Integer pushStatus;
    /**
     * 查看次数
     */
    private Integer selectCount;
    /**
     * 点赞次数
     */
    private Integer praiseCount;
    /**
     * 收藏次数
     */
    private Integer collectCount;
    /**
     * 是否通知（是否发送消息通知）
     */

    private Integer isNotice;
    /**
     * 是否撤销(0.否，1.是)
     */
    private Integer isCancel;
    /**
     * 文件集合
     */
    private String files;
    /**
     * 创建时间
     */
    private java.util.Date createTime;
    /**
     * 更新时间
     */
    private java.util.Date updateTime;
    /**
     * 删除时间
     */
    private java.util.Date deleteTime;
    /**
     * 创建动态的组织code
     */

    private String createOrgCode;
    /**
     * 创建动态的组织code层级码
     */

    private String createOrgOrgCode;
    /**
     * 创建动态的组织的唯一标识符集合
     */
    private String createOrgSet;
    /**
     * 是否建议推送到门户
     */
    private Integer isPortal;
    /**
     * 创建账号
     */
    private String createAccount;
    /**
     * 审核人
     */

    private String checkPerson;

    /**
     * 照片文件
     */

    private List<FileDto> titlePhotoList;

}
