package com.zenith.front.model.bean;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import java.util.Date;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableField;
import java.io.Serializable;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2021-07-11
 */
@TableName("ccp_memold")
public class Memold extends Model<Memold> {

    private static final long serialVersionUID=1L;

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    @TableField("code")
    private String code;

    @TableField("es_id")
    private String esId;

    @TableField("name")
    private String name;

    @TableField("pinyin")
    private String pinyin;

    @TableField("idcard")
    private String idcard;

    @TableField("mem_org_code")
    private String memOrgCode;

    @TableField("org_zb_code")
    private String orgZbCode;

    @TableField("org_code")
    private String orgCode;

    @TableField("phone")
    private String phone;

    @TableField("other_tel")
    private String otherTel;

    @TableField("photo")
    private String photo;

    @TableField("join_org_date")
    private Date joinOrgDate;

    @TableField("full_member_date")
    private Date fullMemberDate;

    @TableField("d49_name")
    private String d49Name;

    @TableField("d49_code")
    private String d49Code;

    @TableField("dues_price")
    private Double duesPrice;

    @TableField("last_pay_date")
    private String lastPayDate;

    @TableField("dues_paid")
    private Double duesPaid;

    @TableField("mem_zb_key")
    private String memZbKey;

    @TableField("mem_extend_zb_key")
    private String memExtendZbKey;

    @TableField("create_time")
    private Date createTime;

    @TableField("remark")
    private String remark;

    @TableField("timestamp")
    private Date timestamp;

    @TableField("update_time")
    private Date updateTime;

    @TableField("delete_time")
    private Date deleteTime;

    @TableField("marry_code")
    private String marryCode;

    @TableField("join_work_date")
    private Date joinWorkDate;

    @TableField("archive_unit")
    private String archiveUnit;

    @TableField("home_address")
    private String homeAddress;

    @TableField("d12_name")
    private String d12Name;

    @TableField("d12_code")
    private String d12Code;

    @TableField("leave_org_date")
    private Date leaveOrgDate;

    @TableField("d29_name")
    private String d29Name;

    @TableField("d29_code")
    private String d29Code;

    @TableField("d50_name")
    private String d50Name;

    @TableField("d50_code")
    private String d50Code;

    @TableField("lost_contact_date")
    private Date lostContactDate;

    @TableField("d18_code")
    private String d18Code;

    @TableField("d18_name")
    private String d18Name;

    @TableField("org_group_id")
    private String orgGroupId;

    @TableField("org_group_name")
    private String orgGroupName;

    @TableField("d19_name")
    private String d19Name;

    @TableField("d19_code")
    private String d19Code;

    @TableField("d20_name")
    private String d20Name;

    @TableField("d20_code")
    private String d20Code;

    @TableField("d21_code")
    private String d21Code;

    @TableField("d21_name")
    private String d21Name;

    @TableField("birthday")
    private Date birthday;

    @TableField("age")
    private Integer age;

    @TableField("sex_code")
    private String sexCode;

    @TableField("sex_name")
    private String sexName;

    @TableField("d06_code")
    private String d06Code;

    @TableField("d06_name")
    private String d06Name;

    @TableField("d48_code")
    private String d48Code;

    @TableField("d48_name")
    private String d48Name;

    @TableField("d07_code")
    private String d07Code;

    @TableField("d07_name")
    private String d07Name;

    @TableField("d08_code")
    private String d08Code;

    @TableField("d08_name")
    private String d08Name;

    @TableField("d09_code")
    private String d09Code;

    @TableField("d09_name")
    private String d09Name;

    @TableField("is_farmer")
    private Integer isFarmer;

    @TableField("apply_date")
    private Date applyDate;

    @TableField("active_date")
    private Date activeDate;

    @TableField("join_org_party_date")
    private Date joinOrgPartyDate;

    @TableField("object_date")
    private Date objectDate;

    @TableField("d11_name")
    private String d11Name;

    @TableField("d11_code")
    private String d11Code;

    @TableField("d27_name")
    private String d27Name;

    @TableField("d27_code")
    private String d27Code;

    @TableField("d28_code")
    private String d28Code;

    @TableField("d28_name")
    private String d28Name;

    @TableField("is_lost")
    private Integer isLost;

    @TableField("large_pay_state")
    private Integer largePayState;

    @TableField("branch_org_zb_code")
    private String branchOrgZbCode;

    @TableField("branch_org_name")
    private String branchOrgName;

    @TableField("branch_org_key")
    private String branchOrgKey;

    @TableField("branch_org_code")
    private String branchOrgCode;

    @TableField("out_branch_org_name")
    private String outBranchOrgName;

    @TableField("is_dispatch")
    private Integer isDispatch;

    @TableField("is_idcard_repeat")
    private Integer isIdcardRepeat;

    @TableField("is_idcard_legal")
    private Integer isIdcardLegal;

    @TableField("ratio")
    private Double ratio;

    @TableField("extend_prepar_date")
    private Date extendPreparDate;

    @TableField("cancel_extend_date_reason")
    private String cancelExtendDateReason;

    @TableField("flow_status")
    private Integer flowStatus;

    @TableField("recover_party_reason")
    private String recoverPartyReason;

    @TableField("stop_party_reason")
    private String stopPartyReason;

    @TableField("work_post")
    private String workPost;

    @TableField("settle_area")
    private String settleArea;

    @TableField("stop_party_date")
    private Date stopPartyDate;

    @TableField("recover_party_date")
    private Date recoverPartyDate;

    @TableField("mem_code")
    private String memCode;

    @TableField("open_id")
    private String openId;

    /**
     * 是否是历史数据
     */
    @TableField("is_history")
    private Integer isHistory;

    /**
     * 修改人
     */
    @TableField("update_account")
    private String updateAccount;


    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getEsId() {
        return esId;
    }

    public void setEsId(String esId) {
        this.esId = esId;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getPinyin() {
        return pinyin;
    }

    public void setPinyin(String pinyin) {
        this.pinyin = pinyin;
    }

    public String getIdcard() {
        return idcard;
    }

    public void setIdcard(String idcard) {
        this.idcard = idcard;
    }

    public String getMemOrgCode() {
        return memOrgCode;
    }

    public void setMemOrgCode(String memOrgCode) {
        this.memOrgCode = memOrgCode;
    }

    public String getOrgZbCode() {
        return orgZbCode;
    }

    public void setOrgZbCode(String orgZbCode) {
        this.orgZbCode = orgZbCode;
    }

    public String getOrgCode() {
        return orgCode;
    }

    public void setOrgCode(String orgCode) {
        this.orgCode = orgCode;
    }

    public String getPhone() {
        return phone;
    }

    public void setPhone(String phone) {
        this.phone = phone;
    }

    public String getOtherTel() {
        return otherTel;
    }

    public void setOtherTel(String otherTel) {
        this.otherTel = otherTel;
    }

    public String getPhoto() {
        return photo;
    }

    public void setPhoto(String photo) {
        this.photo = photo;
    }

    public Date getJoinOrgDate() {
        return joinOrgDate;
    }

    public void setJoinOrgDate(Date joinOrgDate) {
        this.joinOrgDate = joinOrgDate;
    }

    public Date getFullMemberDate() {
        return fullMemberDate;
    }

    public void setFullMemberDate(Date fullMemberDate) {
        this.fullMemberDate = fullMemberDate;
    }

    public String getD49Name() {
        return d49Name;
    }

    public void setD49Name(String d49Name) {
        this.d49Name = d49Name;
    }

    public String getD49Code() {
        return d49Code;
    }

    public void setD49Code(String d49Code) {
        this.d49Code = d49Code;
    }

    public Double getDuesPrice() {
        return duesPrice;
    }

    public void setDuesPrice(Double duesPrice) {
        this.duesPrice = duesPrice;
    }

    public String getLastPayDate() {
        return lastPayDate;
    }

    public void setLastPayDate(String lastPayDate) {
        this.lastPayDate = lastPayDate;
    }

    public Double getDuesPaid() {
        return duesPaid;
    }

    public void setDuesPaid(Double duesPaid) {
        this.duesPaid = duesPaid;
    }

    public String getMemZbKey() {
        return memZbKey;
    }

    public void setMemZbKey(String memZbKey) {
        this.memZbKey = memZbKey;
    }

    public String getMemExtendZbKey() {
        return memExtendZbKey;
    }

    public void setMemExtendZbKey(String memExtendZbKey) {
        this.memExtendZbKey = memExtendZbKey;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public Date getTimestamp() {
        return timestamp;
    }

    public void setTimestamp(Date timestamp) {
        this.timestamp = timestamp;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    public Date getDeleteTime() {
        return deleteTime;
    }

    public void setDeleteTime(Date deleteTime) {
        this.deleteTime = deleteTime;
    }

    public String getMarryCode() {
        return marryCode;
    }

    public void setMarryCode(String marryCode) {
        this.marryCode = marryCode;
    }

    public Date getJoinWorkDate() {
        return joinWorkDate;
    }

    public void setJoinWorkDate(Date joinWorkDate) {
        this.joinWorkDate = joinWorkDate;
    }

    public String getArchiveUnit() {
        return archiveUnit;
    }

    public void setArchiveUnit(String archiveUnit) {
        this.archiveUnit = archiveUnit;
    }

    public String getHomeAddress() {
        return homeAddress;
    }

    public void setHomeAddress(String homeAddress) {
        this.homeAddress = homeAddress;
    }

    public String getD12Name() {
        return d12Name;
    }

    public void setD12Name(String d12Name) {
        this.d12Name = d12Name;
    }

    public String getD12Code() {
        return d12Code;
    }

    public void setD12Code(String d12Code) {
        this.d12Code = d12Code;
    }

    public Date getLeaveOrgDate() {
        return leaveOrgDate;
    }

    public void setLeaveOrgDate(Date leaveOrgDate) {
        this.leaveOrgDate = leaveOrgDate;
    }

    public String getD29Name() {
        return d29Name;
    }

    public void setD29Name(String d29Name) {
        this.d29Name = d29Name;
    }

    public String getD29Code() {
        return d29Code;
    }

    public void setD29Code(String d29Code) {
        this.d29Code = d29Code;
    }

    public String getD50Name() {
        return d50Name;
    }

    public void setD50Name(String d50Name) {
        this.d50Name = d50Name;
    }

    public String getD50Code() {
        return d50Code;
    }

    public void setD50Code(String d50Code) {
        this.d50Code = d50Code;
    }

    public Date getLostContactDate() {
        return lostContactDate;
    }

    public void setLostContactDate(Date lostContactDate) {
        this.lostContactDate = lostContactDate;
    }

    public String getD18Code() {
        return d18Code;
    }

    public void setD18Code(String d18Code) {
        this.d18Code = d18Code;
    }

    public String getD18Name() {
        return d18Name;
    }

    public void setD18Name(String d18Name) {
        this.d18Name = d18Name;
    }

    public String getOrgGroupId() {
        return orgGroupId;
    }

    public void setOrgGroupId(String orgGroupId) {
        this.orgGroupId = orgGroupId;
    }

    public String getOrgGroupName() {
        return orgGroupName;
    }

    public void setOrgGroupName(String orgGroupName) {
        this.orgGroupName = orgGroupName;
    }

    public String getD19Name() {
        return d19Name;
    }

    public void setD19Name(String d19Name) {
        this.d19Name = d19Name;
    }

    public String getD19Code() {
        return d19Code;
    }

    public void setD19Code(String d19Code) {
        this.d19Code = d19Code;
    }

    public String getD20Name() {
        return d20Name;
    }

    public void setD20Name(String d20Name) {
        this.d20Name = d20Name;
    }

    public String getD20Code() {
        return d20Code;
    }

    public void setD20Code(String d20Code) {
        this.d20Code = d20Code;
    }

    public String getD21Code() {
        return d21Code;
    }

    public void setD21Code(String d21Code) {
        this.d21Code = d21Code;
    }

    public String getD21Name() {
        return d21Name;
    }

    public void setD21Name(String d21Name) {
        this.d21Name = d21Name;
    }

    public Date getBirthday() {
        return birthday;
    }

    public void setBirthday(Date birthday) {
        this.birthday = birthday;
    }

    public Integer getAge() {
        return age;
    }

    public void setAge(Integer age) {
        this.age = age;
    }

    public String getSexCode() {
        return sexCode;
    }

    public void setSexCode(String sexCode) {
        this.sexCode = sexCode;
    }

    public String getSexName() {
        return sexName;
    }

    public void setSexName(String sexName) {
        this.sexName = sexName;
    }

    public String getD06Code() {
        return d06Code;
    }

    public void setD06Code(String d06Code) {
        this.d06Code = d06Code;
    }

    public String getD06Name() {
        return d06Name;
    }

    public void setD06Name(String d06Name) {
        this.d06Name = d06Name;
    }

    public String getD48Code() {
        return d48Code;
    }

    public void setD48Code(String d48Code) {
        this.d48Code = d48Code;
    }

    public String getD48Name() {
        return d48Name;
    }

    public void setD48Name(String d48Name) {
        this.d48Name = d48Name;
    }

    public String getD07Code() {
        return d07Code;
    }

    public void setD07Code(String d07Code) {
        this.d07Code = d07Code;
    }

    public String getD07Name() {
        return d07Name;
    }

    public void setD07Name(String d07Name) {
        this.d07Name = d07Name;
    }

    public String getD08Code() {
        return d08Code;
    }

    public void setD08Code(String d08Code) {
        this.d08Code = d08Code;
    }

    public String getD08Name() {
        return d08Name;
    }

    public void setD08Name(String d08Name) {
        this.d08Name = d08Name;
    }

    public String getD09Code() {
        return d09Code;
    }

    public void setD09Code(String d09Code) {
        this.d09Code = d09Code;
    }

    public String getD09Name() {
        return d09Name;
    }

    public void setD09Name(String d09Name) {
        this.d09Name = d09Name;
    }

    public Integer getIsFarmer() {
        return isFarmer;
    }

    public void setIsFarmer(Integer isFarmer) {
        this.isFarmer = isFarmer;
    }

    public Date getApplyDate() {
        return applyDate;
    }

    public void setApplyDate(Date applyDate) {
        this.applyDate = applyDate;
    }

    public Date getActiveDate() {
        return activeDate;
    }

    public void setActiveDate(Date activeDate) {
        this.activeDate = activeDate;
    }

    public Date getJoinOrgPartyDate() {
        return joinOrgPartyDate;
    }

    public void setJoinOrgPartyDate(Date joinOrgPartyDate) {
        this.joinOrgPartyDate = joinOrgPartyDate;
    }

    public Date getObjectDate() {
        return objectDate;
    }

    public void setObjectDate(Date objectDate) {
        this.objectDate = objectDate;
    }

    public String getD11Name() {
        return d11Name;
    }

    public void setD11Name(String d11Name) {
        this.d11Name = d11Name;
    }

    public String getD11Code() {
        return d11Code;
    }

    public void setD11Code(String d11Code) {
        this.d11Code = d11Code;
    }

    public String getD27Name() {
        return d27Name;
    }

    public void setD27Name(String d27Name) {
        this.d27Name = d27Name;
    }

    public String getD27Code() {
        return d27Code;
    }

    public void setD27Code(String d27Code) {
        this.d27Code = d27Code;
    }

    public String getD28Code() {
        return d28Code;
    }

    public void setD28Code(String d28Code) {
        this.d28Code = d28Code;
    }

    public String getD28Name() {
        return d28Name;
    }

    public void setD28Name(String d28Name) {
        this.d28Name = d28Name;
    }

    public Integer getIsLost() {
        return isLost;
    }

    public void setIsLost(Integer isLost) {
        this.isLost = isLost;
    }

    public Integer getLargePayState() {
        return largePayState;
    }

    public void setLargePayState(Integer largePayState) {
        this.largePayState = largePayState;
    }

    public String getBranchOrgZbCode() {
        return branchOrgZbCode;
    }

    public void setBranchOrgZbCode(String branchOrgZbCode) {
        this.branchOrgZbCode = branchOrgZbCode;
    }

    public String getBranchOrgName() {
        return branchOrgName;
    }

    public void setBranchOrgName(String branchOrgName) {
        this.branchOrgName = branchOrgName;
    }

    public String getBranchOrgKey() {
        return branchOrgKey;
    }

    public void setBranchOrgKey(String branchOrgKey) {
        this.branchOrgKey = branchOrgKey;
    }

    public String getBranchOrgCode() {
        return branchOrgCode;
    }

    public void setBranchOrgCode(String branchOrgCode) {
        this.branchOrgCode = branchOrgCode;
    }

    public String getOutBranchOrgName() {
        return outBranchOrgName;
    }

    public void setOutBranchOrgName(String outBranchOrgName) {
        this.outBranchOrgName = outBranchOrgName;
    }

    public Integer getIsDispatch() {
        return isDispatch;
    }

    public void setIsDispatch(Integer isDispatch) {
        this.isDispatch = isDispatch;
    }

    public Integer getIsIdcardRepeat() {
        return isIdcardRepeat;
    }

    public void setIsIdcardRepeat(Integer isIdcardRepeat) {
        this.isIdcardRepeat = isIdcardRepeat;
    }

    public Integer getIsIdcardLegal() {
        return isIdcardLegal;
    }

    public void setIsIdcardLegal(Integer isIdcardLegal) {
        this.isIdcardLegal = isIdcardLegal;
    }

    public Double getRatio() {
        return ratio;
    }

    public void setRatio(Double ratio) {
        this.ratio = ratio;
    }

    public Date getExtendPreparDate() {
        return extendPreparDate;
    }

    public void setExtendPreparDate(Date extendPreparDate) {
        this.extendPreparDate = extendPreparDate;
    }

    public String getCancelExtendDateReason() {
        return cancelExtendDateReason;
    }

    public void setCancelExtendDateReason(String cancelExtendDateReason) {
        this.cancelExtendDateReason = cancelExtendDateReason;
    }

    public Integer getFlowStatus() {
        return flowStatus;
    }

    public void setFlowStatus(Integer flowStatus) {
        this.flowStatus = flowStatus;
    }

    public String getRecoverPartyReason() {
        return recoverPartyReason;
    }

    public void setRecoverPartyReason(String recoverPartyReason) {
        this.recoverPartyReason = recoverPartyReason;
    }

    public String getStopPartyReason() {
        return stopPartyReason;
    }

    public void setStopPartyReason(String stopPartyReason) {
        this.stopPartyReason = stopPartyReason;
    }

    public String getWorkPost() {
        return workPost;
    }

    public void setWorkPost(String workPost) {
        this.workPost = workPost;
    }

    public String getSettleArea() {
        return settleArea;
    }

    public void setSettleArea(String settleArea) {
        this.settleArea = settleArea;
    }

    public Date getStopPartyDate() {
        return stopPartyDate;
    }

    public void setStopPartyDate(Date stopPartyDate) {
        this.stopPartyDate = stopPartyDate;
    }

    public Date getRecoverPartyDate() {
        return recoverPartyDate;
    }

    public void setRecoverPartyDate(Date recoverPartyDate) {
        this.recoverPartyDate = recoverPartyDate;
    }

    public String getMemCode() {
        return memCode;
    }

    public void setMemCode(String memCode) {
        this.memCode = memCode;
    }

    public String getOpenId() {
        return openId;
    }

    public void setOpenId(String openId) {
        this.openId = openId;
    }

    public Integer getIsHistory() {
        return isHistory;
    }

    public void setIsHistory(Integer isHistory) {
        this.isHistory = isHistory;
    }

    public String getUpdateAccount() {
        return updateAccount;
    }

    public void setUpdateAccount(String updateAccount) {
        this.updateAccount = updateAccount;
    }

    @Override
    public Serializable pkVal() {
        return this.id;
    }

    @Override
    public String toString() {
        return "Memold{" +
        "id=" + id +
        ", code=" + code +
        ", esId=" + esId +
        ", name=" + name +
        ", pinyin=" + pinyin +
        ", idcard=" + idcard +
        ", memOrgCode=" + memOrgCode +
        ", orgZbCode=" + orgZbCode +
        ", orgCode=" + orgCode +
        ", phone=" + phone +
        ", otherTel=" + otherTel +
        ", photo=" + photo +
        ", joinOrgDate=" + joinOrgDate +
        ", fullMemberDate=" + fullMemberDate +
        ", d49Name=" + d49Name +
        ", d49Code=" + d49Code +
        ", duesPrice=" + duesPrice +
        ", lastPayDate=" + lastPayDate +
        ", duesPaid=" + duesPaid +
        ", memZbKey=" + memZbKey +
        ", memExtendZbKey=" + memExtendZbKey +
        ", createTime=" + createTime +
        ", remark=" + remark +
        ", timestamp=" + timestamp +
        ", updateTime=" + updateTime +
        ", deleteTime=" + deleteTime +
        ", marryCode=" + marryCode +
        ", joinWorkDate=" + joinWorkDate +
        ", archiveUnit=" + archiveUnit +
        ", homeAddress=" + homeAddress +
        ", d12Name=" + d12Name +
        ", d12Code=" + d12Code +
        ", leaveOrgDate=" + leaveOrgDate +
        ", d29Name=" + d29Name +
        ", d29Code=" + d29Code +
        ", d50Name=" + d50Name +
        ", d50Code=" + d50Code +
        ", lostContactDate=" + lostContactDate +
        ", d18Code=" + d18Code +
        ", d18Name=" + d18Name +
        ", orgGroupId=" + orgGroupId +
        ", orgGroupName=" + orgGroupName +
        ", d19Name=" + d19Name +
        ", d19Code=" + d19Code +
        ", d20Name=" + d20Name +
        ", d20Code=" + d20Code +
        ", d21Code=" + d21Code +
        ", d21Name=" + d21Name +
        ", birthday=" + birthday +
        ", age=" + age +
        ", sexCode=" + sexCode +
        ", sexName=" + sexName +
        ", d06Code=" + d06Code +
        ", d06Name=" + d06Name +
        ", d48Code=" + d48Code +
        ", d48Name=" + d48Name +
        ", d07Code=" + d07Code +
        ", d07Name=" + d07Name +
        ", d08Code=" + d08Code +
        ", d08Name=" + d08Name +
        ", d09Code=" + d09Code +
        ", d09Name=" + d09Name +
        ", isFarmer=" + isFarmer +
        ", applyDate=" + applyDate +
        ", activeDate=" + activeDate +
        ", joinOrgPartyDate=" + joinOrgPartyDate +
        ", objectDate=" + objectDate +
        ", d11Name=" + d11Name +
        ", d11Code=" + d11Code +
        ", d27Name=" + d27Name +
        ", d27Code=" + d27Code +
        ", d28Code=" + d28Code +
        ", d28Name=" + d28Name +
        ", isLost=" + isLost +
        ", largePayState=" + largePayState +
        ", branchOrgZbCode=" + branchOrgZbCode +
        ", branchOrgName=" + branchOrgName +
        ", branchOrgKey=" + branchOrgKey +
        ", branchOrgCode=" + branchOrgCode +
        ", outBranchOrgName=" + outBranchOrgName +
        ", isDispatch=" + isDispatch +
        ", isIdcardRepeat=" + isIdcardRepeat +
        ", isIdcardLegal=" + isIdcardLegal +
        ", ratio=" + ratio +
        ", extendPreparDate=" + extendPreparDate +
        ", cancelExtendDateReason=" + cancelExtendDateReason +
        ", flowStatus=" + flowStatus +
        ", recoverPartyReason=" + recoverPartyReason +
        ", stopPartyReason=" + stopPartyReason +
        ", workPost=" + workPost +
        ", settleArea=" + settleArea +
        ", stopPartyDate=" + stopPartyDate +
        ", recoverPartyDate=" + recoverPartyDate +
        ", memCode=" + memCode +
        ", openId=" + openId +
        ", isHistory=" + isHistory +
        ", updateAccount=" + updateAccount +
        "}";
    }
}
