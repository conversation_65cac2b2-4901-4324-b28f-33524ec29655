package com.zenith.front.model.dto;

import com.zenith.front.model.bean.MemAll;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

@ToString
@Getter
@Setter
public class MemAllDTO {

    /**
     * 自增ID
     */
    private Long id;
    /**
     * 唯一主键
     */
    private String code;
    /**
     * es_ides唯一主键
     */
    private String esId;
    /**
     * 姓名
     */
    private String name;
    /**
     * 拼音
     */
    private String pinyin;
    /**
     * 身份证
     */
    private String idcard;
    /**
     * 组织层级code
     */
    private String memOrgCode;
    /**
     * 组织名称
     */
    private String orgName;
    /**
     * 组织zb_code
     */
    private String orgZbCode;
    /**
     * 组织唯一标识符
     */
    private String orgCode;
    /**
     * 联系电话
     */
    private String phone;
    /**
     * 其他电话
     */
    private String otherTel;
    /**
     * 党员照片
     */
    private String photo;
    /**
     * 预备党员日期(入党时间)
     */
    private java.util.Date joinOrgDate;
    /**
     * 转为正式党员日期
     */
    private java.util.Date fullMemberDate;
    /**
     * 党费交纳情况name
     */
    private String duesStandardName;
    /**
     * 党费交纳情况code
     */
    private String duesStandardCode;
    /**
     * 党费参考标准
     */
    private Double duesPrice;
    /**
     * 最后缴费时间
     */
    private java.util.Date lastPayDate;
    /**
     * 党费缴纳金额
     */
    private Double duesPaid;
    /**
     * 中组部主键
     */
    private String memZbKey;
    /**
     * 中组部人员扩展信息主键
     */
    private String memExtendZbKey;
    /**
     * 创建时间
     */
    private String createTime;
    /**
     * 备注
     */
    private String remark;
    private java.util.Date timestamp;
    /**
     * 更新时间
     */
    private String updateTime;
    /**
     * 删除时间
     */
    private java.math.BigDecimal deleteTime;
    /**
     * 婚姻状况
     */
    private String marryCode;
    /**
     * 参加工作日期
     */
    private String joinWorkDate;
    /**
     * 档案管理单位名称
     */
    private String archiveUnit;
    /**
     * 家庭住址
     */
    private String homeAddress;
    /**
     * 离开党组织的类别name
     */
    private String leaveOrgTypeName;
    /**
     * 离开党组织的类别
     */
    private String leaveOrgTypeCode;
    /**
     * 离开党组织日期[LeaveDate]
     */
    private String leaveOrgDate;
    /**
     * 离开党组织时职务级别name
     */
    private String leaveOrgDutyLevelName;
    /**
     * 离开党组织时职务级别代码 dict_d29
     */
    private String leaveOrgDutyLevelCode;
    /**
     * 出党原因name
     */
    private String leaveReasonName;
    /**
     * 出党原因  dict_leave_reason
     */
    private String leaveReasonCode;
    /**
     * 失去联系日期[LostContactDate]
     */
    private String lostContactDate;
    /**
     * 失去联系类型 dict_d18
     */
    private String lostContactTypeCode;
    /**
     * dict_d18
     */
    private String lostContactTypeName;
    /**
     * 党小组
     */
    private String orgGroupId;
    /**
     * 党小组名称
     */
    private String orgGroupName;
    /**
     * 聘任专业技术职务名称
     */
    private String technicalName;
    /**
     * 聘任专业技术职务code
     */
    private String technicalCode;
    /**
     * 新社会阶层类型名称  (dict_d20)
     */
    private String stratumTypeName;
    /**
     * 新社会阶层类型code (dict_d20)
     */
    private String stratumTypeCode;
    /**
     * 一线情况[OneLineCode]  (dict_d21)
     */
    private String oneLineCode;
    /**
     * 一线情况名称
     */
    private String oneLineName;
    /**
     * 出生日期
     */
    private java.util.Date birthday;
    /**
     * 年龄
     */
    private Integer age;
    /**
     * 性别code
     */
    private String sexCode;
    /**
     * 性别名称
     */
    private String sexName;
    /**
     * 民族code  dict_d06
     */
    private String nationCode;
    /**
     * 民族名称 dict_d06
     */
    private String nationName;
    /**
     * 籍贯  dict_area
     */
    private String nativePlaceCode;
    /**
     * 籍贯名称 dict_area
     */
    private String nativePlaceName;
    /**
     * 学历code  (dict_d07)
     */
    private String educationCode;
    /**
     * 学历名称
     */
    private String educationName;
    /**
     * 人员类别 dict_d08
     */
    private String typeCode;
    /**
     * 人员类别名称
     */
    private String typeName;
    /**
     * 工作岗位  (dict_d09)
     */
    private String jobCode;
    /**
     * 工作岗位名称
     */
    private String jobName;
    /**
     * 是否农民工
     */
    private Boolean isFarmer;
    /**
     * 申请入党日期
     */
    private String applyDate;
    /**
     * 积极分子日期
     */
    private String activeDate;
    /**
     * 进入支部日期
     */
    private String joinOrgPartyDate;
    /**
     * 发展对象日期
     */
    private String objectDate;
    /**
     * 进入支部类型name  dict_d11
     */
    private String joinOrgModeName;
    /**
     * 进入支部类型code dict_d11
     */
    private String joinOrgModeCode;
    /**
     * 加入党组织类别name
     */
    private String addOrgTypeName;
    /**
     * 加入党组织类别code  (dict_d11)
     */
    private String addOrgTypeCode;
    /**
     * 预备党员转正类型code  (dict_d28)
     */
    private String prepareTurnPartyCode;
    /**
     * 预备党员转正类型name  (dict_d28)
     */
    private String prepareTurnPartyName;
    /**
     * 是否失去联系
     */
    private Boolean isLost;
    /**
     * 是否大额党费交纳
     */
    private Boolean largePayState;
    /**
     * 发展时所在党支部code
     */
    private String branchOrgZbCode;
    /**
     * 发展时所在党支部name
     */
    private String branchOrgName;
    /**
     * 发展时所在党支部key
     */
    private String branchOrgKey;
    /**
     * 发展时所在党支部层级码code
     */
    private String branchOrgCode;
    /**
     * 系统外发展时所在党支部name
     */
    private String outBranchOrgName;
    /**
     * 是否劳务派遣
     */
    private Boolean isDispatch;
    private Boolean isIdcardRepeat;
    private Boolean isIdcardLegal;
    private Double ratio;
    /**
     * 预备期到的时间(或延长到的时间)
     */
    private String extendPreparDate;
    /**
     * 撤销延长预备期原因
     */
    private String cancelExtendDateReason;
    /**
     * 是否已流动(1已流出，2流回)
     */
    private Integer flowStatus;
    /**
     * 恢复党籍原因
     */
    private String recoverPartyReason;
    /**
     * 停止党籍原因
     */
    private String stopPartyReason;
    /**
     * 工作单位及职务
     */
    private String workPost;
    /**
     * 定居的国家（地区）
     */
    private String settleArea;
    private String stopPartyDate;
    private String recoverPartyDate;
    /**
     * 旧人员code
     */
    private String memCode;
    private String openId;

    /**
     * 在系统内开始的缴费时间(一旦设置不能更改)
     */
    private java.util.Date startPayDate;

    public MemAll toModel() {
        MemAll model = new MemAll();
        model.setId(this.id);
        model.setCode(this.code);
        model.setEsId(this.esId);
        model.setName(this.name);
        model.setPinyin(this.pinyin);
        model.setIdcard(this.idcard);
        model.setMemOrgCode(this.memOrgCode);
        model.setOrgName(this.orgName);
        model.setOrgZbCode(this.orgZbCode);
        model.setOrgCode(this.orgCode);
        model.setPhone(this.phone);
        model.setOtherTel(this.otherTel);
        model.setPhoto(this.photo);
        model.setJoinOrgDate(this.joinOrgDate);
        model.setFullMemberDate(this.fullMemberDate);
        model.setDuesStandardName(this.duesStandardName);
        model.setDuesStandardCode(this.duesStandardCode);
        model.setDuesPrice(this.duesPrice);
        model.setLastPayDate(this.lastPayDate);
        model.setDuesPaid(this.duesPaid);
        model.setMemZbKey(this.memZbKey);
        model.setMemExtendZbKey(this.memExtendZbKey);
        model.setCreateTime(this.createTime);
        model.setRemark(this.remark);
        model.setTimestamp(this.timestamp);
        model.setUpdateTime(this.updateTime);
        model.setDeleteTime(this.deleteTime);
        model.setMarryCode(this.marryCode);
        model.setJoinWorkDate(this.joinWorkDate);
        model.setArchiveUnit(this.archiveUnit);
        model.setHomeAddress(this.homeAddress);
        model.setLeaveOrgTypeName(this.leaveOrgTypeName);
        model.setLeaveOrgTypeCode(this.leaveOrgTypeCode);
        model.setLeaveOrgDate(this.leaveOrgDate);
        model.setLeaveOrgDutyLevelName(this.leaveOrgDutyLevelName);
        model.setLeaveOrgDutyLevelCode(this.leaveOrgDutyLevelCode);
        model.setLeaveReasonName(this.leaveReasonName);
        model.setLeaveReasonCode(this.leaveReasonCode);
        model.setLostContactDate(this.lostContactDate);
        model.setLostContactTypeCode(this.lostContactTypeCode);
        model.setLostContactTypeName(this.lostContactTypeName);
        model.setOrgGroupId(this.orgGroupId);
        model.setOrgGroupName(this.orgGroupName);
        model.setTechnicalName(this.technicalName);
        model.setTechnicalCode(this.technicalCode);
        model.setStratumTypeName(this.stratumTypeName);
        model.setStratumTypeCode(this.stratumTypeCode);
        model.setOneLineCode(this.oneLineCode);
        model.setOneLineName(this.oneLineName);
        model.setBirthday(this.birthday);
        model.setAge(this.age);
        model.setSexCode(this.sexCode);
        model.setSexName(this.sexName);
        model.setNationCode(this.nationCode);
        model.setNationName(this.nationName);
        model.setNativePlaceCode(this.nativePlaceCode);
        model.setNativePlaceName(this.nativePlaceName);
        model.setEducationCode(this.educationCode);
        model.setEducationName(this.educationName);
        model.setTypeCode(this.typeCode);
        model.setTypeName(this.typeName);
        model.setJobCode(this.jobCode);
        model.setJobName(this.jobName);
        model.setFarmer(this.isFarmer);
        model.setApplyDate(this.applyDate);
        model.setActiveDate(this.activeDate);
        model.setJoinOrgPartyDate(this.joinOrgPartyDate);
        model.setObjectDate(this.objectDate);
        model.setJoinOrgModeName(this.joinOrgModeName);
        model.setJoinOrgModeCode(this.joinOrgModeCode);
        model.setAddOrgTypeName(this.addOrgTypeName);
        model.setAddOrgTypeCode(this.addOrgTypeCode);
        model.setPrepareTurnPartyCode(this.prepareTurnPartyCode);
        model.setPrepareTurnPartyName(this.prepareTurnPartyName);
        model.setLost(this.isLost);
        model.setLargePayState(this.largePayState);
        model.setBranchOrgZbCode(this.branchOrgZbCode);
        model.setBranchOrgName(this.branchOrgName);
        model.setBranchOrgKey(this.branchOrgKey);
        model.setBranchOrgCode(this.branchOrgCode);
        model.setOutBranchOrgName(this.outBranchOrgName);
        model.setDispatch(this.isDispatch);
        model.setIdcardRepeat(this.isIdcardRepeat);
        model.setIdcardLegal(this.isIdcardLegal);
        model.setRatio(this.ratio);
        model.setExtendPreparDate(this.extendPreparDate);
        model.setCancelExtendDateReason(this.cancelExtendDateReason);
        model.setFlowStatus(this.flowStatus);
        model.setRecoverPartyReason(this.recoverPartyReason);
        model.setStopPartyReason(this.stopPartyReason);
        model.setWorkPost(this.workPost);
        model.setSettleArea(this.settleArea);
        model.setStopPartyDate(this.stopPartyDate);
        model.setRecoverPartyDate(this.recoverPartyDate);
        model.setMemCode(this.memCode);
        model.setOpenId(this.openId);
        model.setStartPayDate(this.startPayDate);
        return model;
    }
}