package com.zenith.front.model.dto;

import com.zenith.front.model.bean.MemFlowLog;
import lombok.*;

@ToString
public class MemFlowLogDTO{

    private Long id;
    private String key;
    private String code;
    private String title;
    private String content;
    private String createtime;
    private String updatetime;
    private Boolean deleted;
    private java.util.Date timestamp;

    public MemFlowLogDTO setId(Long id){
        this.id = id;
        return this;
    }
    public Long getId() {
    	return this.id;
    }
    public MemFlowLogDTO setKey(String key){
        this.key = key;
        return this;
    }
    public String getKey() {
    	return this.key;
    }
    public MemFlowLogDTO setCode(String code){
        this.code = code;
        return this;
    }
    public String getCode() {
    	return this.code;
    }
    public MemFlowLogDTO setTitle(String title){
        this.title = title;
        return this;
    }
    public String getTitle() {
    	return this.title;
    }
    public MemFlowLogDTO setContent(String content){
        this.content = content;
        return this;
    }
    public String getContent() {
    	return this.content;
    }
    public MemFlowLogDTO setCreatetime(String createtime){
        this.createtime = createtime;
        return this;
    }
    public String getCreatetime() {
    	return this.createtime;
    }
    public MemFlowLogDTO setUpdatetime(String updatetime){
        this.updatetime = updatetime;
        return this;
    }
    public String getUpdatetime() {
    	return this.updatetime;
    }
    public MemFlowLogDTO setDeleted(Boolean deleted){
        this.deleted = deleted;
        return this;
    }
    public Boolean getDeleted() {
    	return this.deleted;
    }
    public MemFlowLogDTO setTimestamp(java.util.Date timestamp){
        this.timestamp = timestamp;
        return this;
    }
    public java.util.Date getTimestamp() {
    	return this.timestamp;
    }


    public MemFlowLog toModel(){
        MemFlowLog model = new MemFlowLog();
        model.setId(this.id);
        model.setKey(this.key);
        model.setCode(this.code);
        model.setTitle(this.title);
        model.setContent(this.content);
        model.setCreatetime(this.createtime);
        model.setUpdatetime(this.updatetime);
        model.setDeleted(this.deleted);
        model.setTimestamp(this.timestamp);
        return model;
    }
}