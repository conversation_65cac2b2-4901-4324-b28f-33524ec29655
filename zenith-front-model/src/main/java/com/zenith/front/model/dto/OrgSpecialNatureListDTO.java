package com.zenith.front.model.dto;

import com.zenith.front.model.validate.group.Common1Group;
import lombok.Data;
import lombok.ToString;

import javax.validation.constraints.Max;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2021/9/9 17:10
 * @Version 1.0
 */
@Data
@ToString
public class OrgSpecialNatureListDTO {

    /**
     * 分页页数
     */
    @Min(value = 1, groups = Common1Group.class, message = "页码最小为1")
    @NotNull(groups = Common1Group.class, message = "pageNum 不能为空")
    private Integer pageNum;
    /**
     * 分页每页数
     */
    @Max(value = 100, groups = Common1Group.class, message = "每页条数大小范围在1-100")
    @NotNull(groups = Common1Group.class, message = "pageSize 不能为空")
    private Integer pageSize;
    /**
     * 组织层级码
     */
    @NotBlank(groups = Common1Group.class, message = "orgCode 不能为空")
    private String orgCode;

    private String orgName;

    private Integer subordinate;

    /**
     * 组织类别
     */
    private List<String> d122CodeList;
    /**
     * 组织性质
     */
    private List<String> d96CodeList;

    /**
     * 党组织类别
     */
    private List<String> d01CodeList;

}
