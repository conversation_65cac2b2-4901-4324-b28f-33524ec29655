package com.zenith.front.model.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;
import org.hibernate.validator.constraints.Range;

import javax.validation.constraints.Max;
import javax.validation.constraints.Min;
import java.util.ArrayList;

/**
 * @author: D.watermelon
 * @date: 2019/5/23 17:33
 * @description: 入则恳恳以尽忠 出则谦谦以自悔
 */

@Data
@ToString
@NoArgsConstructor
@AllArgsConstructor
public class ActivitiFilterDto {
    /**
     * 分页页数
     */
    @Min(value = 1, message = "页码最小为1")
    private Integer pageNum;
    /**
     * 分页每页数
     */
    @Max(value = 100, message = "每页条数大小范围在1-100")
    private Integer pageSize;

    /**
     * 活动名称关键字搜索
     * */
    private String activityName;

    /**
     * 活动状态
     * */
    private ArrayList<Integer> status;

    /**
     * 请求终端
     * */
    @Range(min = 1,max = 2,message ="状态值1-2")
    private Integer type;

    /**
     * 请求筛选的活动类型
     * <p>
     * */
    private ArrayList<String> activityType;

    /**
     * 请求组织的code
     *
     */
    private String org_org_code;


}
