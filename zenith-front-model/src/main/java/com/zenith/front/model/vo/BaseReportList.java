package com.zenith.front.model.vo;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 */
public class BaseReportList implements Serializable {

    private static final long serialVersionUID = -8037682899525771395L;

    /**
     * 农村党建调度表
     */
    private List<BaseReport> memReport;
    private List<BaseReport> unitReport;
    private List<BaseReport> orgReport;
    private List<BaseReport> developStepLog;
    private List<BaseReport> memFlow;
    /**
     * 发展党员工作情况调度表
     */
    private List<BaseReport> developStepLogAll;
    /**
     * 发展党员工作情况统计表
     */
    private List<BaseReport> developStatistics;

    public List<BaseReport> getMemReport() {
        return memReport;
    }

    public void setMemReport(List<BaseReport> memReport) {
        this.memReport = memReport;
    }

    public List<BaseReport> getUnitReport() {
        return unitReport;
    }

    public void setUnitReport(List<BaseReport> unitReport) {
        this.unitReport = unitReport;
    }

    public List<BaseReport> getOrgReport() {
        return orgReport;
    }

    public void setOrgReport(List<BaseReport> orgReport) {
        this.orgReport = orgReport;
    }

    public List<BaseReport> getDevelopStepLog() {
        return developStepLog;
    }

    public void setDevelopStepLog(List<BaseReport> developStepLog) {
        this.developStepLog = developStepLog;
    }

    public List<BaseReport> getMemFlow() {
        return memFlow;
    }

    public void setMemFlow(List<BaseReport> memFlow) {
        this.memFlow = memFlow;
    }

    public List<BaseReport> getDevelopStepLogAll() {
        return developStepLogAll;
    }

    public void setDevelopStepLogAll(List<BaseReport> developStepLogAll) {
        this.developStepLogAll = developStepLogAll;
    }

    public List<BaseReport> getDevelopStatistics() {
        return developStatistics;
    }

    public void setDevelopStatistics(List<BaseReport> developStatistics) {
        this.developStatistics = developStatistics;
    }
}
