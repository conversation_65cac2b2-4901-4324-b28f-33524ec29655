package com.zenith.front.model.bean;

import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.zenith.front.common.encrypt.annotation.EncryptEnabled;
import com.zenith.front.common.encrypt.annotation.EncryptField;
import com.zenith.front.common.encrypt.typehandler.EncryptTypeHandler;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2021-08-11
 */
@Data
@EncryptEnabled
@TableName(value = "ccp_unit_extend", autoResultMap = true)
public class UnitExtend extends Model<UnitExtend> {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    @TableField("code")
    private String code;

    /**
     * 是否垂直管理部门 1是，0否 dict_d147
     */
    @Deprecated
    @TableField("is_czglbm")
    private String isCzglbm;

    /**
     * 吸收未转入组织关系的党员 1是，0否
     */
    @TableField("b30_1")
    private Integer b301;

    /**
     * 配备专职副书记 1是，0否
     */
    @TableField("has_major_deputy_secretary")
    private Integer hasMajorDeputySecretary;

    /**
     * 政府工作部门建立党组（党委）情况,是否建立党组
     */
    @TableField("d80_code")
    private String d80Code;

    /**
     * 在岗职工数（人）
     */
    @TableField("on_post_num")
    private Integer onPostNum;

    /**
     * 党政机关工作人员
     */
    @TableField("b30_a12")
    private Integer b30A12;

    /**
     * 在职党员数（人）
     */
    @TableField("member_num")
    private Integer memberNum;

    /**
     * 是否政府工作部门 1是，0否
     */
    @TableField("is_dfzgbm")
    private Integer isDfzgbm;

    /**
     * 35岁以下在岗职工数（人）
     */
    @TableField("under_thirty_five_num")
    private Integer underThirtyFiveNum;

    /**
     * 单位负责人
     */
    @TableField("principal")
    private String principal;

    /**
     * 单位负责人是否是党员 1是，0否
     */
    @TableField("is_unit_mem")
    private Integer isUnitMem;

    /**
     * 是否有“两代表一委员”   1是，0否
     */
    @TableField("is_committee_represent")
    private Integer isCommitteeRepresent;

    /**
     * 为上级党建工作联系点 1是，0否
     */
    @TableField("is_org_contact")
    private Integer isOrgContact;

    /**
     * 建立党员志愿者队伍 1是，0否
     */
    @TableField("is_vol_team")
    private Integer isVolTeam;

    /**
     * 企业规模
     */
    @TableField("d17_code")
    private String d17Code;

    /**
     * 经济类型
     */
    @TableField("d16_code")
    private String d16Code;

    /**
     * 企业隶属关系
     */
    @TableField("d79_code")
    private String d79Code;

    /**
     * 在岗专业技术人员数（人）
     */
    @TableField("tec_num")
    private Integer tecNum;

    /**
     * 在岗职工工人（工勤技能）数
     */
    @TableField("worker_num")
    private Integer workerNum;

    /**
     * 是否建有工会 1是，0否
     */
    @TableField("is_work_union")
    private Integer isWorkUnion;

    /**
     * 工会负责人是否是党员 1是，0否
     */
    @TableField("is_work_union_member")
    private Integer isWorkUnionMember;

    /**
     * 是否建有共青团 1是，0否
     */
    @TableField("is_teenager")
    private Integer isTeenager;

    /**
     * 共青团负责人是否是党员 1是，0否
     */
    @TableField("is_teenager_member")
    private Integer isTeenagerMember;

    /**
     * 是否建有妇联 1是，0否
     */
    @TableField("is_women_federation")
    private Integer isWomenFederation;

    /**
     * 妇联负责人是否是党员 1是，0否
     */
    @TableField("is_women_federation_member")
    private Integer isWomenFederationMember;

    /**
     * 党建工作指导员数
     */
    @TableField("\"bZT6_10\"")
    private Integer bzt610;

    /**
     * 所属层级
     */
    @TableField("d78_code")
    private String d78Code;

    /**
     * 建立党员服务机构 1是，0否
     */
    @TableField("is_org_service")
    private Integer isOrgService;

    /**
     * 是否选派党建工作指导员或联络员 1是，0否
     */
    @TableField("is_org_instructor")
    private Integer isOrgInstructor;

    /**
     * 是否脱钩行业协会商会 1是，0否
     */
    @TableField("is_decoupl_industry")
    private Integer isDecouplIndustry;

    /**
     * 隶属于两新工委的（人）
     */
    @TableField("lslxgw")
    private Integer lslxgw;

    /**
     * 隶属于机关工委的（人）
     */
    @TableField("lsjggw")
    private Integer lsjggw;

    /**
     * 隶属于行业主管部门的（人）
     */
    @TableField("lshyzg")
    private Integer lshyzg;

    /**
     * 书记由行业主管部门党员负责同志担任（人）
     */
    @TableField("sjyhyzg")
    private Integer sjyhyzg;

    /**
     * 专职工作人员数（人）
     */
    @TableField("zzgzry")
    private Integer zzgzry;

    /**
     * 医疗机构设立级别
     */
    @TableField("d77_code")
    private String d77Code;

    /**
     * 在岗专业技术人员（高级职称）（人）
     */
    @TableField("zaigang_gaoji")
    private Integer zaigangGaoji;

    /**
     * 户数（户）
     */
    @TableField("house_num")
    private Integer houseNum;

    /**
     * 户籍人口（人）
     */
    @TableField("house_person_num")
    private Integer housePersonNum;

    /**
     * 常住人口（人）
     */
    @TableField("permanent_population")
    private Integer permanentPopulation;

    /**
     * 上年度当地农村居民人均可支配收入（元）
     */
    @TableField("last_year_income")
    private BigDecimal lastYearIncome;

    /**
     * 党组织书记情况
     */
    @TableField("d76_code")
    private String d76Code;

    /**
     * 有党组织书记后备人选
     */
    @TableField("\"bZT4_4\"")
    private Integer bzt44;

    /**
     * 参加县级以上培训
     */
    @TableField("\"bZT4_5\"")
    private Integer bzt45;

    /**
     * 实行“四议两公开”工作法
     */
    @TableField("\"bZT4_6\"")
    private Integer bzt46;

    /**
     * 成立村务监督委员会或其他村务监督机构
     */
    @TableField("\"bZT4_7\"")
    private Integer bzt47;

    /**
     * 建档立卡贫困村 1是，0否
     */
    @TableField("is_poor_village")
    private Integer isPoorVillage;

    /**
     * 建立村务监督委员会 1是，0否
     */
    @TableField("is_village_committee")
    private Integer isVillageCommittee;

    /**
     * 自然村数
     */
    @TableField("natural_village_num")
    private Integer naturalVillageNum;

    /**
     * 集体经济年收入（元）
     */
    @TableField("collective_economy_gdp")
    private BigDecimal collectiveEconomyGdp;

    /**
     * 第一书记姓名
     */
    @TableField("first_sec_name")
    private String firstSecName;

    /**
     * 第一书记公民身份证号
     */
    @TableField("first_sec_id")
    private String firstSecId;

    /**
     * 第一书记任期（年）
     */
    @TableField("total_year")
    private Integer totalYear;

    /**
     * 本年召开党委全委会数
     */
    @TableField("b32_12")
    private Integer b3212;

    /**
     * 为第一书记安排不低于1万元工作经费的村
     */
    @TableField("\"bZT4_12\"")
    private Integer bzt412;

    /**
     * 未完成“五小”建设的乡镇
     */
    @TableField("\"bZT4_29\"")
    private Integer bzt429;

    /**
     * 向建制村选派第一书记数
     */
    @TableField("\"bZT4_10\"")
    private Integer bzt410;

    /**
     * 暂无活动场所的建制村
     */
    @TableField("\"bZT4_26\"")
    private Integer bzt426;

    /**
     * 纪委常委数
     */
    @TableField("\"b32_10\"")
    private Integer b3210;

    /**
     * 本年各级培训第一书记（人次）
     */
    @TableField("\"bZT4_11\"")
    private Integer bzt411;

    /**
     * 本年领导班子民主生活会参加人员数
     */
    @TableField("\"b32_15\"")
    private Integer b3215;

    /**
     * 是否已召开年会 1是，0否
     */
    @TableField("\"b32_5\"")
    private Integer b325;

    /**
     * 提拔使用或晋级的第一书记数
     */
    @TableField("\"bZT4_14\"")
    private Integer bzt414;

    /**
     * 是否本年已换届
     */
    @TableField("\"b32_3\"")
    private Integer b323;

    /**
     * 党委候补委员数
     */
    @TableField("\"b32_11\"")
    private Integer b3211;

    /**
     * 建制村运转经费合计（万元/年）
     */
    @TableField("\"bZT4_16\"")
    private Integer bzt416;

    /**
     * 活动场所面积200㎡以上的建制村
     */
    @TableField("\"bZT4_27\"")
    private Integer bzt427;

    /**
     * 是否本年任届期满 1是，0否
     */
    @TableField("\"b32_2\"")
    private Integer b322;

    /**
     * 村党组织书记报酬合计（万元/年）
     */
    @TableField("\"bZT4_18\"")
    private Integer bzt418;

    /**
     * 村党组织书记报酬低于农村居民人均可支配收入两倍标准的县 1是，0否
     */
    @TableField("\"bZT4_20\"")
    private Integer bzt420;

    /**
     * 纪委委员数
     */
    @TableField("\"b32_9\"")
    private Integer b329;

    /**
     * 因工作不胜任召回调整的第一书记数
     */
    @TableField("\"bZT4_15\"")
    private Integer bzt415;

    /**
     * 本届换届选举代表数
     */
    @TableField("\"b32_6\"")
    private Integer b326;

    /**
     * 从村党组织书记中选拔乡镇领导干部数
     */
    @TableField("\"bZT4_9\"")
    private Integer bzt49;

    /**
     * 本年新建或改扩建活动场所
     */
    @TableField("\"bZT4_28\"")
    private Integer bzt428;

    /**
     * 是否试行党代会常任制 1是，0否
     */
    @TableField("\"b32_4\"")
    private Integer b324;

    /**
     * 建制村办公经费合计（万元/年）
     */
    @TableField("\"bZT4_17\"")
    private Integer bzt417;

    /**
     * 党委委员数
     */
    @TableField("\"b32_7\"")
    private Integer b327;

    /**
     * 从村党组织书记中录用公务员和事业编制工作人员
     */
    @TableField("\"bZT4_8\"")
    private Integer bzt48;

    /**
     * 落实正常离任村党组织书记生活补贴的县 1是，0否
     */
    @TableField("\"bZT4_22\"")
    private Integer bzt422;

    /**
     * 派出单位落实责任、项目、资金捆绑的村
     */
    @TableField("\"bZT4_13\"")
    private Integer bzt413;

    /**
     * 村干部基本报酬和村级组织办公经费合计低于9万元的县 1是，0否
     */
    @TableField("\"bZT4_19\"")
    private Integer bzt419;

    /**
     * 本年召开领导班子民主生活会
     */
    @TableField("\"b32_14\"")
    private Integer b3214;

    /**
     * 无集体经济收入的建制村数
     */
    @TableField("\"bZT4_25\"")
    private Integer bzt425;

    /**
     * 为村党组织书记办理养老保险的县 1是，0否
     */
    @TableField("\"bZT4_21\"")
    private Integer bzt421;

    /**
     * 落实村民小组长误工补贴的县 1是，0否
     */
    @TableField("\"bZT4_24\"")
    private Integer bzt424;

    /**
     * 落实农村公共服务运行维护支出或服务群众专项经费的县 1是，0否
     */
    @TableField("\"bZT4_23\"")
    private Integer bzt423;

    /**
     * 党委常委数
     */
    @TableField("\"b32_8\"")
    private Integer b328;

    /**
     * 本年内参加党委全委会委员数
     */
    @TableField("\"b32_13\"")
    private Integer b3213;

    /**
     * 街道干部数
     */
    @TableField("\"b6_1\"")
    private Integer b61;

    /**
     * 街道干部35岁及以下
     */
    @TableField("b6_2")
    private Integer b62;

    /**
     * 街道干部36至54岁
     */
    @TableField("b6_3")
    private Integer b63;

    /**
     * 街道干部55岁及以上
     */
    @TableField("b6_4")
    private Integer b64;

    /**
     * 街道干部大专及以上
     */
    @TableField("b6_5")
    private Integer b65;

    /**
     * 街道干部高中中专及以下
     */
    @TableField("b6_6")
    private Integer b66;

    /**
     * 街道干部公务员人数
     */
    @TableField("b6_7")
    private Integer b67;

    /**
     * 街道干部事业人员
     */
    @TableField("b6_8")
    private Integer b68;

    /**
     * 街道干部其他身份
     */
    @TableField("b6_9")
    private Integer b69;

    /**
     * 是否实行兼职委员制 1是，0否
     */
    @TableField("b6_10")
    private Integer b610;

    /**
     * 是否建立工委 1是，0否
     */
    @TableField("is_worke_committee")
    private Integer isWorkeCommittee;

    /**
     * 第一书记任职开始日期
     */
    @TableField("start_date")
    private Date startDate;

    /**
     * 主要负责人担任党组织书记 1是，0否
     */
    @TableField("main_party_secretary")
    private Integer mainPartySecretary;

    /**
     * 是否实行党组织领导的院（所）长负责制 1是，0否
     */
    @TableField("org_leader_charge")
    private Integer orgLeaderCharge;

    /**
     * 公益分类
     */
    @TableField("d81_code")
    private String d81Code;

    /**
     * 2023-add-单位类别为【科研院所】时
     * 2023-add-单位类别为【教育】时
     * 领导体制已写入医院章程的 1是，0否
     */
    @TableField("ldtzyyzc")
    private Integer ldtzyyzc;

    /**
     * 是否党建工作要求写入医院章程 1是，0否
     */
    @TableField("is_party_work_write")
    private Integer isPartyWorkWrite;

    /**
     * 是否开展基层党建述职评议考核	 1是，0否
     */
    @TableField("is_open_org_assess")
    private Integer isOpenOrgAssess;

    /**
     * 2023-add-单位类别为【科研院所】时
     * 2023-add-单位类别为【教育】时
     * 是否党委书记、院长分设 1是，0否
     */
    @TableField("is_leader_separate")
    private Integer isLeaderSeparate;

    /**
     * 2023-add-单位类别为【科研院所】时
     * 2023-add-单位类别为【教育】时
     * 是否院长系中共党员 1是，0否
     */
    @TableField("leader_is_gcdy")
    private Integer leaderIsGcdy;

    /**
     * 2023-add-单位类别为【科研院所】时
     * 2023-add-单位类别为【教育】时
     * 是否院长担任党委副书记 1是，0否
     */
    @TableField("is_leader_deputy_secretary")
    private Integer isLeaderDeputySecretary;

    /**
     * 2023-add-单位类别为【科研院所】时
     * 医院内设机构党支部（个）
     */
    @TableField("is_set_org_party")
    private Integer isSetOrgParty;

    /**
     * 2023-add-单位类别为【科研院所】时
     * 党支部书记是内设机构负责人（人）
     */
    @TableField("secretary_is_inside_leader")
    private Integer secretaryIsInsideLeader;

    /**
     * 2023-add-单位类别为【科研院所】时
     * 党支部书记是“双带头人”的（人）
     */
    @TableField("sjsdtr")
    private Integer sjsdtr;

    /**
     * 本年度党支部书记参加培训人次（人）
     */
    @TableField("year_train_org_mem")
    private Integer yearTrainOrgMem;

    /**
     * 本年度任届期满的内设机构党支部（个）
     */
    @TableField("is_year_expiration_org")
    private Integer isYearExpirationOrg;

    /**
     * 本年换届（个）
     */
    @TableField("is_year_org_change")
    private Integer isYearOrgChange;

    /**
     * 本年度发展党员（人）
     */
    @TableField("year_develop_mem")
    private Integer yearDevelopMem;

    /**
     * 本年度发展卫生技术人员党员（人）
     */
    @TableField("year_develop_mem_medicine")
    private Integer yearDevelopMemMedicine;

    /**
     * 本年度列为入党积极分子（人）
     */
    @TableField("rdjjfz")
    private Integer rdjjfz;

    /**
     * 在校中专生数
     */
    @TableField("b7_3")
    private Integer b73;

    /**
     * 在校高中、中技生数
     */
    @TableField("b7_4")
    private Integer b74;

    /**
     * 在校研究生数
     */
    @TableField("b7_1")
    private Integer b71;

    /**
     * 在校本科生数
     */
    @TableField("b7_2")
    private Integer b72;

    /**
     * 在校专科生数
     */
    @TableField("b7_8")
    private Integer b78;

    /**
     * 高等学校教师数
     */
    @TableField("b7_5")
    private Integer b75;

    /**
     * 高等学校女教师数
     */
    @TableField("b7_6")
    private Integer b76;

    /**
     * 高等学校35岁及以下教师
     */
    @TableField("b7_7")
    private Integer b77;

    @TableField("create_time")
    private Date createTime;

    @TableField("update_time")
    private Date updateTime;

    @TableField("delete_time")
    private Date deleteTime;

    /**
     * 是否设立常委会 1是，0否
     */
    @TableField("has_standing_committee")
    private Integer hasStandingCommittee;


    /**
     * 是否向地方党委和主管部委专题报告党委领导下的校长负责制执行 1是 0否
     */
    @TableField("has_report_implementation")
    private Integer hasReportImplementation;

    /**
     * 是否修订党委全委会、常委会和校长办公会议事规则 1是 0否
     */
    @TableField("has_office_procedure")
    private Integer hasOfficeProcedure;


    /**
     * 学校党委书记是否向地方党委述职 1是，0否
     */
    @TableField("school_has_reports_local")
    private Integer schoolHasReportsLocal;

    /**
     * 是否组织开展二级院（系）党组织书记向学校党委述职
     */
    @TableField("has_secretary_university_committee")
    private Integer hasSecretaryUniversityCommittee;


    /**
     * 校长是否中共党员
     */
    @TableField("has_president_party_member")
    private Integer hasPresidentPartyMember;


    /**
     * 校长是否担任党委副书记
     */
    @TableField("has_deputy_party_secretary")
    private Integer hasDeputyPartySecretary;

    /**
     * 医院等级--代码
     */
    @TableField("d95_code")
    private String d95Code;

    /**
     * 医院等级--名称
     */
    @TableField("d95_name")
    private String d95Name;


    /**
     * 办校类别代码
     */
    @TableField("d109_code")
    private String d109Code;

    /**
     * 办校类别中文
     */
    @TableField("d109_name")
    private String d109Name;


    /**
     * 办院级别代码
     */
    @TableField("d110_code")
    private String d110Code;

    /**
     * 办院级别中文
     */
    @TableField("d110_name")
    private String d110Name;


    /**
     * 办院类型代码
     */
    @TableField("d111_code")
    private String d111Code;

    /**
     * 办院类型中文
     */
    @TableField("d111_name")
    private String d111Name;


    /**
     * 2023-add-单位类别为【教育】时
     * 是否已实行党委领导下的院长负责制 1是，0否
     */
    @TableField("has_responsibility_system")
    private Integer hasResponsibilitySystem;

    /**
     * 院长是否中共党员  1是，0否
     */
    @TableField("has_dean_party_member")
    private Integer hasDeanPartyMember;


    /**
     * 院长是否担任党委副书记  1是，0否
     */
    @TableField("has_dean_party_secretary")
    private Integer hasDeanPartySecretary;


    /**
     * 党支部书记是否“双带头人”  1是，0否
     */
    @TableField("has_sjsdtr")
    private Integer hasSjsdtr;

    /**
     * 党支部书记是否内设机构负责人 1是，0否
     */
    @TableField("has_secretaryisinsideleader")
    private Integer hasSecretaryisinsideleader;

    /**
     * 是否实行院（所）长负责制 1是，0否
     */
    @TableField("has_dean_responsibility_system")
    private Integer hasDeanResponsibilitySystem;


    /**
     * 所长负责制情况代码
     */
    @TableField("d112_code")
    private String d112Code;

    /**
     * 所长负责制情况代码
     */
    @TableField("d112_name")
    private String d112Name;


    /**
     * 行业分类
     */
    @TableField("d114_code")
    private String d114Code;

    /**
     * 是否配备专职党务工作人员
     */
    @TableField("has_party_work")
    private Integer hasPartyWork;

    /**
     * 是否企业本级
     */
    @TableField("has_firm_level")
    private Integer hasFirmLevel;

    /**
     * 企业本级名称
     */
    @TableField("firm_level_name")
    private String firmLevelName;


    /**
     * 企业级别
     */
    @TableField("d115_code")
    private String d115Code;

    /**
     * 是否建立董事会
     */
    @TableField("has_directors")
    private Integer hasDirectors;

    /**
     * 董事长是否担任书记
     */
    @TableField("has_chairman_secretary")
    private Integer hasChairmanSecretary;

    /**
     * 党建工作经费是否按上年度工资总额一定比例纳入企业管理费用
     */
    @TableField("has_proportionate_funding")
    private Integer hasProportionateFunding;

    /**
     * 人事管理和基层党建是否由一个部门抓
     */
    @TableField("has_branch_to_catch")
    private Integer hasBranchToCatch;


    /**
     * 人事管理和基层党建是否由一个领导管
     */
    @TableField("has_by_leader")
    private Integer hasByLeader;


    /**
     * 党务工作人员和经营管理人员是否同职级同待遇
     */
    @TableField("has_same_treatment")
    private Integer hasSameTreatment;


    /**
     * 是否上市公司
     */
    @TableField("has_public_company")
    private Integer hasPublicCompany;


    /**
     * 党建工作是否写入公司章程
     */
    @TableField("has_articles_incorporation")
    private Integer hasArticlesIncorporation;

    /**
     * 是否党组织研究讨论作为董事会、经理层决策重大问题前置程序
     */
    @TableField("has_prepositional_procedure")
    private Integer hasPrepositionalProcedure;


    /**
     * 董事长是否由上级企业有关负责人兼任
     */
    @TableField("has_responsible_person")
    private Integer hasResponsiblePerson;


    /**
     * 分支机构树
     */
    @TableField("branches")
    private Integer branches;


    /**
     * 基层党组织数量
     */
    @TableField("party_organization_num")
    private Integer partyOrganizationNum;


    /**
     * 已建立党组织
     */
    @TableField("have_been_established")
    private Integer haveBeenEstablished;


    /**
     * 党员数
     */
    @TableField("party_members")
    private Integer partyMembers;


    /**
     * 是否依托组织部门成立的非公党工委
     */
    @TableField("has_non_public_party")
    private Integer hasNonPublicParty;


    /**
     * 是否设立专门办事机构的非公党工委 1是，0否
     */
    @TableField("has_special_agencies")
    private Integer hasSpecialAgencies;


    /**
     * 办事机构工作人员编制数
     */
    @TableField("staff_office_numbers")
    private Integer staffOfficeNumbers;


    /**
     * 组织部门（非公党工委）直接联系的非公企业党组织数
     */
    @TableField("non_public_enterprises")
    private Integer nonPublicEnterprises;

    @TableField("has_community_access")
    private Integer hasCommunityAccess;

    @TableField("has_joint_units")
    private Integer hasJointUnits;
    /**
     * 是否按不低于上年度当地全口径城镇单位就业人员平均工资水平确定报酬
     */
    @TableField("has_lower_social")
    private Integer hasLowerSocial;

    @TableField("community_workers_salary")
    private BigDecimal communityWorkersSalary;

    @TableField("community_secretary_salary")
    private BigDecimal communitySecretarySalary;

    @TableField("community_building_number")
    private Integer communityBuildingNumber;

    @TableField("has_community_positions")
    private Integer hasCommunityPositions;

    @TableField("community_office_space")
    private Integer communityOfficeSpace;

    @TableField("has_parttime_system")
    private Integer hasParttimeSystem;
    /**
     * 是否建档立卡贫困村 0否 1是
     */
    @TableField("has_poor_village")
    private Integer hasPoorVillage;
    @TableField("has_four_two_open_work")
    private Integer hasFourTwoOpenWork;
    @TableField("has_community_supervisory")
    private Integer hasCommunitySupervisory;

    /**
     * 专业技术人员数
     */
    @TableField("technical_personnel")
    private Integer technicalPersonnel;

    /**
     * 党员中高级职称人员数
     */
    @TableField("party_senior_title")
    private Integer partySeniorTitle;


    /**
     * 是否建立志愿者服务机构 1是，0否
     */
    @TableField("has_volunteer_organization")
    private Integer hasVolunteerOrganization;


    /**
     * 是否赋予区域综合管理权、对上级部门派驻机构负责人的人事考核权等权力 1是，0否
     */
    @TableField("has_examination_power")
    private Integer hasExaminationPower;

    /**
     * 是否取消招商引资等职能 1是，0否
     */
    @TableField("has_cancel_investment_promotion")
    private Integer hasCancelInvestmentPromotion;

    /**
     * 是否整合职能统筹设置党政内设工作机构 1是，0否
     */
    @TableField("has_work_mechanism")
    private Integer hasWorkMechanism;

    /**
     * 是否组织委员是否纳入上一级党委管理 1是，0否
     */
    @TableField("has_included_committee")
    private Integer hasIncludedCommittee;


    /**
     * 是否建立党群服务中心 1是，0否
     */
    @TableField("has_group_service_center")
    private Integer hasGroupServiceCenter;

    /**
     * 是否实行与驻区单位党建联建共建 1是，0否
     */
    @TableField("has_party_build_endeavor")
    private Integer hasPartyBuildEndeavor;


    /**
     * 是否党建工作指导员联系 1是，0否
     */
    @TableField("has_instructor_contact")
    private Integer hasInstructorContact;

    /**
     * 是否建立工会或共青团组织  1是，0否
     */
    @TableField("has_union_organization")
    private Integer hasUnionOrganization;

    /**
     * 未转组织关系党员数
     */
    @TableField("not_turned_party")
    private Integer notTurnedParty;

    /**
     * 是否主要负责人担任党组织书记 1是，0否
     */
    @TableField("has_organization_secretary")
    private Integer hasOrganizationSecretary;


    /**
     * 从业人员数
     */
    @TableField("employees_number")
    private Integer employeesNumber;

    /**
     * 法定代表人是否党员 1是，0否
     */
    @TableField("has_representative")
    private Integer hasRepresentative;

    /**
     * 是否兼任企业党组书记 1是，0否
     */
    @TableField("has_proper_secretary")
    private Integer hasProperSecretary;

    /**
     * 吸收未转入组织关系的党员建立党组织数
     */
    @TableField("absorbed_tissue_number")
    private Integer absorbedTissueNumber;

    /**
     * 主要负责人是否党员 1是，0否
     */
    @TableField("has_head_party")
    private Integer hasHeadParty;

    /**
     * 社区纳入财政预算的工作经费总额（万元）
     */
    @TableField("included_financial")
    private BigDecimal includedFinancial;

    /**
     * 社区全年服务群众专项经费总额（万元）
     */
    @TableField("special_funds_masses")
    private BigDecimal specialFundsMasses;

    /**
     * 是否开展在职党员到社区报到为群众服务 1是，0否
     */
    @TableField("has_community_report")
    private Integer hasCommunityReport;

    /**
     * 纪委书记是否担任学校党委常委 1是，0否
     */
    @TableField("has_secretary_committee")
    private Integer hasSecretaryCommittee;

    /**
     * 组织部长是否担任学校党委常委 1是，0否
     */
    @TableField("has_tissue_committee")
    private Integer hasTissueCommittee;

    /**
     * 宣传部长是否担任学校党委常委 1是，0否
     */
    @TableField("has_propaganda_committee")
    private Integer hasPropagandaCommittee;


    /**
     * 宣传部长是否担任学校党委常委 1是，0否
     */
    @TableField("has_front_committee")
    private Integer hasFrontCommittee;

    /**
     * 本科以上学历人数（数字）
     */
    @TableField("above_bk_education")
    private Integer aboveBkEducation;

    /**
     * 研究生以上学历人数（数字）
     */
    @TableField("above_yjs_education")
    private Integer aboveYjsEducation;

    /**
     * 是否由企业中高层管理人员担任党组织书记的 1是，0否
     */
    @TableField("has_secretary_high_level")
    private Integer hasSecretaryHighLevel;

    /**
     * 是否党组织书记（是1 否0）
     */
    @TableField("has_level_secretary")
    private Integer hasLevelSecretary;

    /**
     * 户籍数
     */
    @TableField("household_registration")
    private Integer householdRegistration;

    /**
     * 户籍数
     */
    @TableField("registered_population")
    private Integer registeredPopulation;

    /**
     * 本级企业是否省内企业
     */
    @TableField("has_industry_province")
    private Integer hasIndustryProvince;

    /**
     * 全体在校学生中研究生人数
     */
    @TableField("graduate_student")
    private Integer graduateStudent;

    /**
     * 全体在校学生中大学本科生人数
     */
    @TableField("undergraduate_student")
    private Integer undergraduateStudent;

    /**
     * 全体在校学生中大学专科生人数
     */
    @TableField("junior_college_student")
    private Integer juniorCollegeStudent;

    /**
     * 全体在校学生中高中、中技人数
     */
    @TableField("middle_technical_students")
    private Integer middleTechnicalStudents;

    /**
     * 高等学校教师人数
     */
    @TableField("teachers_institutions_higher")
    private Integer teachersInstitutionsHigher;

    /**
     * 高等学校教师中女性人数
     */
    @TableField("teachers_higher_women")
    private Integer teachersHigherWomen;

    /**
     * 高等学校教师中35岁及以下人数
     */
    @TableField("teachers_age_thirty_five_below")
    private Integer teachersAgeThirtyFiveBelow;


    /**
     * 到社区报到的在职党员
     */
    @TableField("report_community_member")
    private Integer reportCommunityMember;

    /**
     * 2023-add-单位类别为【科研院所】时
     * 2023-add-单位类别为【教育】时
     * 党委书记是否兼任行政职务
     */
    @TableField("has_clerk_position")
    private Integer hasClerkPosition;

    /**
     * 党委书记是否兼任行政职务
     */
    @TableField("has_secretary_economy")
    private Integer hasSecretaryEconomy;

    /**
     * 全体在校学生中中专生人数
     */
    @TableField("technical_secondary_student")
    private Integer technicalSecondaryStudent;
    /**
     * 是否配备院长（0否，1是）
     */
    @TableField("is_allocate_dean")
    private Integer isAllocateDean;
    /**
     * 是否配备书记（0否，1是）
     */
    @TableField("is_allocate_secretary")
    private Integer isAllocateSecretary;

    /**
     * 是否建立工会  1是，0否
     */
    @TableField("has_labour_union")
    private Integer hasLabourUnion;

    /**
     * 是否建立共青团组织  1是，0否
     */
    @TableField("has_youth_league")
    private Integer hasYouthLeague;

    /**
     * 是否建立妇联组织  1是，0否
     */
    @TableField("has_womens_federation")
    private Integer hasWomensFederation;

    /**
     * 是否已统一整合设置网格 0否，1是
     */
    @TableField("has_set_grid")
    private Integer hasSetGrid;
    /**
     * 是否有专职网格员纳入社区工作者管理 0否，1是
     */
    @TableField("has_included_grid_worker")
    private Integer hasIncludedGridWorker;

//    /**
//     * 2023年统修改增加国名经济行业
//     */
//    @TableField("d194_code")
//    private String d194Code;
//
//    public String getD194Code() {
//        return d194Code;
//    }
//
//    public void setD194Code(String d194Code) {
//        this.d194Code = d194Code;
//    }


//    /**
//     * 2023年统修改增加生产服务行行业
//     */
//    @TableField("d195_code")
//    private String d195Code;
//    public String getD195Code() {
//        return d195Code;
//    }
//
//    public void setD195Code(String d195Code) {
//        this.d195Code = d195Code;
//    }

    // -----------专题十一 科研院所 相关统计项--------
    // -----------专题十二 中小学校 相关统计项--------
    /**
     * 2023-add-单位类别为【科研院所】时
     * 2023-add-单位类别为【教育】时
     *  已修订党组织会议、院（所）长办公会议议事规则的（1是0否）
     */
    @TableField("ysgz_is")
    private Integer ysgzIs;
    /**
     * 2023-add-单位类别为【科研院所】时
     * 2023-add-单位类别为【教育】时
     * 已建立学校党组织书记和校长定期沟通制度的 （1是0否）
     */
    @TableField("yjldqgt_is")
    private Integer yjldqgtIs;
    /**
     * 2023-add-单位类别为【科研院所】时
     * 2023-add-单位类别为【教育】时
     * 已设立党务工作机构的 （1是0否）
     */
    @TableField("ysldwgzjg_is")
    private Integer ysldwgzjgIs;
    /**
     * 2023-add-单位类别为【科研院所】时
     * 2023-add-单位类别为【教育】时
     *已配备专职党务工作人员的	（1是0否）
     */
    @TableField("ypbzzdwgzry_is")
    private Integer ypbzzdwgzryIs;

    // unitCommunity 合并过来的字段

    /**
     * 默认当前年份
     * 年份
     */
    @TableField("year")
    //@TableLogic(value = "cast(to_char(now(), 'yyyy') as int)")
    private Integer year;

    /**
     * 今年选派第一书记
     */
    @TableField("first_secretary_select")
    private String firstSecretarySelect;

    /**
     * 现任第一书记代码
     */
    @TableField("first_secretary_code")
    private String firstSecretaryCode;

    /**
     * 现任第一书记姓名
     */
    @EncryptField(order = 1)
    @TableField(value = "first_secretary_name", typeHandler = EncryptTypeHandler.class)
    private String firstSecretaryName;

    /**
     * 本年各级培训第一书记
     */
    @TableField("secretary_training_num")
    private Integer secretaryTrainingNum;

    /**
     * 是否为第一书记安排不低于1万元工作经费
     */
    @TableField("has_thousand")
    private Integer hasThousand;

    /**
     * 是否派出单位落实责任、项目、资金捆绑的
     */
    @TableField("has_bundled")
    private Integer hasBundled;

    /**
     * 提拔使用或晋级的第一书记数
     */
    @TableField("promoted_num")
    private Integer promotedNum;

    /**
     * 因工作不胜任召回调整的第一书记数
     */
    @TableField("adjusted_num")
    private Integer adjustedNum;

    /**
     * 运转经费
     */
    @TableField("operating_expenses")
    private BigDecimal operatingExpenses;

    /**
     * 每村办公经费
     */
    @TableField("village_per")
    private BigDecimal villagePer;

    /**
     * 党组织书记平均报酬
     */
    @TableField("secretary_salary")
    private BigDecimal secretarySalary;

    /**
     * 活动场所面积
     */
    @TableField("space_area")
    private Integer spaceArea;

    /**
     * 本年新建或改扩建活动场所数量
     */
    @TableField("new_expand_area")
    private Integer newExpandArea;


    /**
     * 专调2 村党组织书记中录用公务员数
     */
    @TableField("secretary_party_num")
    private Integer secretaryPartyNum;

    /**
     * 专调2 村党组织书记中录用事业编制工作人员数
     */
    @TableField("secretary_employ_sybz_num")
    private Integer secretaryEmploySybzNum;

    /**
     * 从村党组织书记中选拔乡镇领导干部人员数
     */
    @TableField("secretary_promoted_num")
    private Integer secretaryPromotedNum;

    /**
     * 社区纳入财政预算的工作经费总额
     */
    @TableField("community_money_num")
    private BigDecimal communityMoneyNum;

    /**
     * 社区全年服务群众专项经费总额
     */
    @TableField("community_serving_people")
    private BigDecimal communityServingPeople;

    /**
     * 是否开展在职党员到社区报到为群众服务
     */
    @TableField("community_masses")
    private Integer communityMasses;

    /**
     * 是否配备第一书记 1是，0否
     */
    @TableField("has_first_secretary")
    private Integer hasFirstSecretary;

    @TableField("update_account")
    private String updateAccount;

    /**
     * 该字段只是证明用户在扩展信息处点了保存，详情不用查询历史数据
     */
    @TableField("unit_community_code")
    private String unitCommunityCode;

    /**
     * 单位类别，用来逻辑校验
     */
    @TableField(exist = false)
    private String d04Code;


    // ==========
    // unit 中的部分信息
    /**
     * 参加县级及以上集中培训人数（数字、必填）
     */
    @TableField("join_above_county_train_num")
    private Integer joinAboveCountyTrainNum;
    /**
     * 村干部参加城镇职工养老保险人数（数字、必填）
     */
    @TableField("village_join_urban_worker_num")
    private Integer villageJoinUrbanWorkerNum;
    /**
     * 应到村任职选调生人数（数字、必填）
     */
    @TableField("number_of_students_to_be_transferred_to_the_village")
    private Integer numberOfStudentsToBeTransferredToTheVillage;
    /**
     * 是否有大学毕业生在村工作（选择框、必填）1 是 0 否
     */
    @TableField("whether_there_are_college_graduates_working_in_the_village")
    private Integer whetherThereAreCollegeGraduatesWorkingInTheVillage;

    /**
     * 不是党委委员的政府领导班子成员人数（必填、数字）
     */
    @TableField("number_of_non_governmental_members")
    private Integer numberOfNonGovernmentalMembers;

    /**
     * 是否民族院校 供发展党员工作情况调度表 民族院校（人） 使用
     */
    @TableField("has_national_colleges")
    private Integer hasNationalColleges;

    /**
     * 农村专业技术协会数量
     */
    @TableField("rural_professional_technical_association_num")
    private Integer ruralProfessionalTechnicalAssociationNum;

    /**
     * 农民专业合作社数量
     */
    @TableField("farmer_specialized_cooperatives_num")
    private Integer farmerSpecializedCooperativesNum;

    /**
     * 家庭农场数量
     */
    @TableField("family_farm_num")
    private Integer familyFarmNum;

    /**
     * 专调表八 本年度高校党支部书记参加培训人次
     */
    @TableField("year_branch_training")
    private Integer yearBranchTraining;

    /**
     * 专调表八 本年度院系本级党组织书记参加培训人次
     */
    @TableField("year_training")
    private Integer yearTraining;

    /**
     * 专调表八 本年度毕业生党员
     */
    @TableField("graduate_party_member")
    private Integer graduatePartyMember;

    /**
     * 专调表八 尚未转出组织关系的
     */
    @TableField("org_relationship_not_transferred")
    private Integer orgRelationshipNotTransferred;

    /**
     * 是否将党建工作经费纳入管理费列支、税前扣除(当单位类型为社会组织)
     */
    @TableField("has_working_expenses")
    private Integer hasWorkingExpenses;

    /**
     * 登记级别
     */
    @TableField("d118_code")
    private String d118Code;

    /**
     * 登记级别
     */
    @TableField("d118_name")
    private String d118Name;



    //===========================
    // unitAll

    /**
     * 街道干部人数
     */
    @TableField("street_cadres")
    private Integer streetCadres;

    /**
     * 街道干部35岁及以下人数
     */
    @TableField("age35_below")
    private Integer age35Below;
    /**
     * 街道干部36至55岁人数
     */
    @TableField("age36_to_age55")
    private Integer age36ToAge55;
    /**
     * 街道干部56岁及以上人数
     */
    @TableField("age_56_above")
    private Integer age56Above;
    /**
     * 街道干部大专及以上学历人数
     */
    @TableField("college_degree_above")
    private Integer collegeDegreeAbove;
    /**
     * 街道干部高中中专及以下人数
     */
    @TableField("secondary_school_below")
    private Integer secondarySchoolBelow;
    /**
     * 街道干部公务员人数
     */
    @TableField("street_cadres_civil")
    private Integer streetCadresCivil;
    /**
     * 街道干部事业单位人数
     */
    @TableField("street_cadres_institutions")
    private Integer streetCadresInstitutions;
    /**
     * 街道干部其他身份人数
     */
    @TableField("cadre_other")
    private Integer cadreOther;

    /**
     * 与业务主管单位关系(当单位类型为社会组织)
     */
    @TableField("d159_code")
    private String d159Code;

    /**
     * 与业务主管单位关系(当单位类型为社会组织)
     */
    @TableField("d159_name")
    private String d159Name;

    /**
     * 是否建立履职事项清单 1-是 0-否
     */
    @TableField("has_performed_detail")
    private Integer hasPerformedDetail;

    /**
     * 国有经济控制的企业党建工作情况：境外分支机构有党员的机构
     */
    @TableField("exist_member_branches")
    private Integer existMemberBranches;

    /**
     * 国有经济控制的企业党建工作情况：境外分支机构有党员3人以上、未建立党组织的
     */
    @TableField("three_member_no_org_branches")
    private Integer threeMemberNoOrgBranches;

    /**
     * 有经济控制的企业境外分支共有员工数
     */
    @TableField("branch_employee")
    private Integer branchEmployee;
    /**
     * 有经济控制的企业境外分支其中国内派出员工
     */
    @TableField("branch_employee_home")
    private Integer branchEmployeeHome;
    /**
     * 境外分支机构已建立的党委数
     */
    @TableField("branch_committee")
    private Integer branchCommittee;
    /**
     * 境外分支机构已建立的总支部数
     */
    @TableField("branch_general")
    private Integer branchGeneral;
    /**
     * 境外分支机构已建立的支部数
     */
    @TableField("branch_node")
    private Integer branchNode;

    @Override
    public String toString() {
        return "UnitExtend{" +
                "id=" + id +
                ", code='" + code + '\'' +
                ", isCzglbm=" + isCzglbm +
                ", b301=" + b301 +
                ", hasMajorDeputySecretary=" + hasMajorDeputySecretary +
                ", d80Code='" + d80Code + '\'' +
                ", onPostNum=" + onPostNum +
                ", b30A12=" + b30A12 +
                ", memberNum=" + memberNum +
                ", isDfzgbm=" + isDfzgbm +
                ", underThirtyFiveNum=" + underThirtyFiveNum +
                ", principal='" + principal + '\'' +
                ", isUnitMem=" + isUnitMem +
                ", isCommitteeRepresent=" + isCommitteeRepresent +
                ", isOrgContact=" + isOrgContact +
                ", isVolTeam=" + isVolTeam +
                ", d17Code='" + d17Code + '\'' +
                ", d16Code='" + d16Code + '\'' +
                ", d79Code='" + d79Code + '\'' +
                ", tecNum=" + tecNum +
                ", workerNum=" + workerNum +
                ", isWorkUnion=" + isWorkUnion +
                ", isWorkUnionMember=" + isWorkUnionMember +
                ", isTeenager=" + isTeenager +
                ", isTeenagerMember=" + isTeenagerMember +
                ", isWomenFederation=" + isWomenFederation +
                ", isWomenFederationMember=" + isWomenFederationMember +
                ", bzt610=" + bzt610 +
                ", d78Code='" + d78Code + '\'' +
                ", isOrgService=" + isOrgService +
                ", isOrgInstructor=" + isOrgInstructor +
                ", isDecouplIndustry=" + isDecouplIndustry +
                ", lslxgw=" + lslxgw +
                ", lsjggw=" + lsjggw +
                ", lshyzg=" + lshyzg +
                ", sjyhyzg=" + sjyhyzg +
                ", zzgzry=" + zzgzry +
                ", d77Code='" + d77Code + '\'' +
                ", zaigangGaoji=" + zaigangGaoji +
                ", houseNum=" + houseNum +
                ", housePersonNum=" + housePersonNum +
                ", permanentPopulation=" + permanentPopulation +
                ", lastYearIncome=" + lastYearIncome +
                ", d76Code='" + d76Code + '\'' +
                ", bzt44=" + bzt44 +
                ", bzt45=" + bzt45 +
                ", bzt46=" + bzt46 +
                ", bzt47=" + bzt47 +
                ", isPoorVillage=" + isPoorVillage +
                ", isVillageCommittee=" + isVillageCommittee +
                ", naturalVillageNum=" + naturalVillageNum +
                ", collectiveEconomyGdp=" + collectiveEconomyGdp +
                ", firstSecName='" + firstSecName + '\'' +
                ", firstSecId='" + firstSecId + '\'' +
                ", totalYear=" + totalYear +
                ", b3212=" + b3212 +
                ", bzt412=" + bzt412 +
                ", bzt429=" + bzt429 +
                ", bzt410=" + bzt410 +
                ", bzt426=" + bzt426 +
                ", b3210=" + b3210 +
                ", bzt411=" + bzt411 +
                ", b3215=" + b3215 +
                ", b325=" + b325 +
                ", bzt414=" + bzt414 +
                ", b323=" + b323 +
                ", b3211=" + b3211 +
                ", bzt416=" + bzt416 +
                ", bzt427=" + bzt427 +
                ", b322=" + b322 +
                ", bzt418=" + bzt418 +
                ", bzt420=" + bzt420 +
                ", b329=" + b329 +
                ", bzt415=" + bzt415 +
                ", b326=" + b326 +
                ", bzt49=" + bzt49 +
                ", bzt428=" + bzt428 +
                ", b324=" + b324 +
                ", bzt417=" + bzt417 +
                ", b327=" + b327 +
                ", bzt48=" + bzt48 +
                ", bzt422=" + bzt422 +
                ", bzt413=" + bzt413 +
                ", bzt419=" + bzt419 +
                ", b3214=" + b3214 +
                ", bzt425=" + bzt425 +
                ", bzt421=" + bzt421 +
                ", bzt424=" + bzt424 +
                ", bzt423=" + bzt423 +
                ", b328=" + b328 +
                ", b3213=" + b3213 +
                ", b61=" + b61 +
                ", b62=" + b62 +
                ", b63=" + b63 +
                ", b64=" + b64 +
                ", b65=" + b65 +
                ", b66=" + b66 +
                ", b67=" + b67 +
                ", b68=" + b68 +
                ", b69=" + b69 +
                ", b610=" + b610 +
                ", isWorkeCommittee=" + isWorkeCommittee +
                ", startDate=" + startDate +
                ", mainPartySecretary=" + mainPartySecretary +
                ", orgLeaderCharge=" + orgLeaderCharge +
                ", d81Code='" + d81Code + '\'' +
                ", ldtzyyzc=" + ldtzyyzc +
                ", isPartyWorkWrite=" + isPartyWorkWrite +
                ", isOpenOrgAssess=" + isOpenOrgAssess +
                ", isLeaderSeparate=" + isLeaderSeparate +
                ", leaderIsGcdy=" + leaderIsGcdy +
                ", isLeaderDeputySecretary=" + isLeaderDeputySecretary +
                ", isSetOrgParty=" + isSetOrgParty +
                ", secretaryIsInsideLeader=" + secretaryIsInsideLeader +
                ", sjsdtr=" + sjsdtr +
                ", yearTrainOrgMem=" + yearTrainOrgMem +
                ", isYearExpirationOrg=" + isYearExpirationOrg +
                ", isYearOrgChange=" + isYearOrgChange +
                ", yearDevelopMem=" + yearDevelopMem +
                ", yearDevelopMemMedicine=" + yearDevelopMemMedicine +
                ", rdjjfz=" + rdjjfz +
                ", b73=" + b73 +
                ", b74=" + b74 +
                ", b71=" + b71 +
                ", b72=" + b72 +
                ", b78=" + b78 +
                ", b75=" + b75 +
                ", b76=" + b76 +
                ", b77=" + b77 +
                ", createTime=" + createTime +
                ", updateTime=" + updateTime +
                ", deleteTime=" + deleteTime +
                ", hasStandingCommittee=" + hasStandingCommittee +
                ", hasReportImplementation=" + hasReportImplementation +
                ", hasOfficeProcedure=" + hasOfficeProcedure +
                ", schoolHasReportsLocal=" + schoolHasReportsLocal +
                ", hasSecretaryUniversityCommittee=" + hasSecretaryUniversityCommittee +
                ", hasPresidentPartyMember=" + hasPresidentPartyMember +
                ", hasDeputyPartySecretary=" + hasDeputyPartySecretary +
                ", d95Code='" + d95Code + '\'' +
                ", d95Name='" + d95Name + '\'' +
                ", d109Code='" + d109Code + '\'' +
                ", d109Name='" + d109Name + '\'' +
                ", d110Code='" + d110Code + '\'' +
                ", d110Name='" + d110Name + '\'' +
                ", d111Code='" + d111Code + '\'' +
                ", d111Name='" + d111Name + '\'' +
                ", hasResponsibilitySystem=" + hasResponsibilitySystem +
                ", hasDeanPartyMember=" + hasDeanPartyMember +
                ", hasDeanPartySecretary=" + hasDeanPartySecretary +
                ", hasSjsdtr=" + hasSjsdtr +
                ", hasSecretaryisinsideleader=" + hasSecretaryisinsideleader +
                ", hasDeanResponsibilitySystem=" + hasDeanResponsibilitySystem +
                ", d112Code='" + d112Code + '\'' +
                ", d112Name='" + d112Name + '\'' +
                ", d114Code='" + d114Code + '\'' +
                ", hasPartyWork=" + hasPartyWork +
                ", hasFirmLevel=" + hasFirmLevel +
                ", firmLevelName='" + firmLevelName + '\'' +
                ", d115Code='" + d115Code + '\'' +
                ", hasDirectors=" + hasDirectors +
                ", hasChairmanSecretary=" + hasChairmanSecretary +
                ", hasProportionateFunding=" + hasProportionateFunding +
                ", hasBranchToCatch=" + hasBranchToCatch +
                ", hasByLeader=" + hasByLeader +
                ", hasSameTreatment=" + hasSameTreatment +
                ", hasPublicCompany=" + hasPublicCompany +
                ", hasArticlesIncorporation=" + hasArticlesIncorporation +
                ", hasPrepositionalProcedure=" + hasPrepositionalProcedure +
                ", hasResponsiblePerson=" + hasResponsiblePerson +
                ", branches=" + branches +
                ", partyOrganizationNum=" + partyOrganizationNum +
                ", haveBeenEstablished=" + haveBeenEstablished +
                ", partyMembers=" + partyMembers +
                ", hasNonPublicParty=" + hasNonPublicParty +
                ", hasSpecialAgencies=" + hasSpecialAgencies +
                ", staffOfficeNumbers=" + staffOfficeNumbers +
                ", nonPublicEnterprises=" + nonPublicEnterprises +
                ", hasCommunityAccess=" + hasCommunityAccess +
                ", hasJointUnits=" + hasJointUnits +
                ", hasLowerSocial=" + hasLowerSocial +
                ", communityWorkersSalary=" + communityWorkersSalary +
                ", communitySecretarySalary=" + communitySecretarySalary +
                ", communityBuildingNumber=" + communityBuildingNumber +
                ", hasCommunityPositions=" + hasCommunityPositions +
                ", communityOfficeSpace=" + communityOfficeSpace +
                ", hasParttimeSystem=" + hasParttimeSystem +
                ", hasPoorVillage=" + hasPoorVillage +
                ", hasFourTwoOpenWork=" + hasFourTwoOpenWork +
                ", hasCommunitySupervisory=" + hasCommunitySupervisory +
                ", technicalPersonnel=" + technicalPersonnel +
                ", partySeniorTitle=" + partySeniorTitle +
                ", hasVolunteerOrganization=" + hasVolunteerOrganization +
                ", hasExaminationPower=" + hasExaminationPower +
                ", hasCancelInvestmentPromotion=" + hasCancelInvestmentPromotion +
                ", hasWorkMechanism=" + hasWorkMechanism +
                ", hasIncludedCommittee=" + hasIncludedCommittee +
                ", hasGroupServiceCenter=" + hasGroupServiceCenter +
                ", hasPartyBuildEndeavor=" + hasPartyBuildEndeavor +
                ", hasInstructorContact=" + hasInstructorContact +
                ", hasUnionOrganization=" + hasUnionOrganization +
                ", notTurnedParty=" + notTurnedParty +
                ", hasOrganizationSecretary=" + hasOrganizationSecretary +
                ", hasLabourUnion=" + hasLabourUnion +
                ", hasYouthLeague=" + hasYouthLeague +
                ", hasWomensFederation =" + hasWomensFederation +
//                ", d194Code='" + d194Code + '\'' +
//                ", d195Code='" + d195Code + '\'' +
                '}';
    }

}
