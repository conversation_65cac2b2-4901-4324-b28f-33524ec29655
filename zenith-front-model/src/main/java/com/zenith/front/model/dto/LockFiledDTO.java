package com.zenith.front.model.dto;

import org.hibernate.validator.constraints.Range;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 */
public class LockFiledDTO implements Serializable {

    private static final long serialVersionUID = 7808918642543572580L;

    /**
     * 锁定对象 1 党员 2 组织 3 单位
     */
    @NotNull(message = "锁定对象 1 党员 2 单位 3 组织")
    @Range(min = 1L, max = 3, message = "锁定对象 1 党员 2 单位 3 组织")
    private Integer lockObject;

    /**
     * 锁定字段
     */
    private List<String> filedList;

    public Integer getLockObject() {
        return lockObject;
    }

    public void setLockObject(Integer lockObject) {
        this.lockObject = lockObject;
    }

    public List<String> getFiledList() {
        return filedList;
    }

    public void setFiledList(List<String> filedList) {
        this.filedList = filedList;
    }

    @Override
    public String toString() {
        return "LockFiledDTO{" +
                "lockObject=" + lockObject +
                ", filedList=" + filedList +
                '}';
    }
}
