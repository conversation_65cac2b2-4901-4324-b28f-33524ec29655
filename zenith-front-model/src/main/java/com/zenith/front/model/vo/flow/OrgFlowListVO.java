package com.zenith.front.model.vo.flow;


import com.baomidou.mybatisplus.annotation.TableField;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;

@Data
public class OrgFlowListVO implements Serializable {

    private static final long serialVersionUID = 1559478396231009131L;
    /**
     * 自增长主键
     */
    private Long id;

    /**
     * 唯一标识符code
     */
    private String code;

    /**
     * es唯一主键
     */
    private String esId;

    /**
     * 党组织代码
     */
    private String zbCode;

    /**
     * 组织层级码
     */
    private String orgCode;

    /**
     * 名称
     */
    private String name;

    /**
     * 组织简称
     */
    private String shortName;

    /**
     * 组织全称拼音
     */
    private String pinyin;

    /**
     * 是否有效，审批通过才是有效的组织 0-无效 1-有效
     */
    private Integer isEnable;

    /**
     * 上级党组织唯一标识符
     */
    private String parentCode;

    /**
     * 父级的党组织类型  1-普通党组织  2-流动党组织
     */
    private Integer parentFlowType;

    /**
     * 党组织类型code
     */
    private String d01Code;

    /**
     *  党组织类型名称
     */
    private String d01Name;

    /**
     * 成立日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "Asia/Shanghai")
    private Date createDate;

    /**
     * 联系人
     */
    private String contacter;

    /**
     * 联系方式
     */
    private String contactPhone;

    /**
     * 党组织成立类型code
     */
    private String d200Code;

    /**
     * 党组织成立类型名称
     */
    private String d200Name;

    /**
     * 批准成立的党组织（县级以上党委）
     */
    private String approveCode;

    /**
     * 批准成立的党组织名称（县级以上党委）
     */
    private String approveName;

    /**
     * 行政区划代码
     */
    private String administrativeDivision;

    /**
     * 行政区划名称
     */
    private String administrativeDivisionName;

    /**
     * 流动地行政区划代码
     */
    private String flowAdministrativeDivision;
    /**
     * 流动地行政区划名称
     */
    private String flowAdministrativeDivisionName;

    /**
     * 依托单位code
     */
    private String d201Code;

    /**
     * 依托单位名称
     */
    private String d201Name;

    /**
     * 其他依托单位详情
     */
    private String supportUnitDetails;

    /**
     * 1-本地创建  2-交换区数据
     */
    private Integer sourceType;

    /**
     * 排序
     */
    private Integer sort;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    /**
     * 更新时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date updateTime;

    /**
     * 备注
     */
    private String remark;

    /**
     * 审批状态，0-待审批，1-审批通过，2-审批未通过
     */
    private String status;



}
