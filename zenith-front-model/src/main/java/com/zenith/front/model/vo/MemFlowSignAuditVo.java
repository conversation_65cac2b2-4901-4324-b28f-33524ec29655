package com.zenith.front.model.vo;

import lombok.Data;

import java.util.Date;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2021-11-14
 */
@Data
public class MemFlowSignAuditVo {

    /**
     * 唯一标识符
     */
    private Long id;

    /**
     * 唯一标识符code
     */
    private String code;

    /**
     * 流动党员code
     */
    private String signCode;

    /**
     * 入党申请人code
     */
    private String flowSignCode;

    /**
     * 党组织记录code
     */
    private String flowSignRecordsCode;

    /**
     * 流动组织Code
     */
    private String flowOrgCode;

    /**
     * 流动组织名称
     */
    private String flowOrgName;

    /**
     * 审批时间
     */
    private Date auditTime;

    /**
     * 审批人唯一标识
     */
    private String auditUserId;

    /**
     * 审批人名称
     */
    private String auditUserName;

    /**
     * 审批人组织ID
     */
    private String auditOrgId;

    /**
     * 审批人组织层级码
     */
    private String auditOrgCode;

    /**
     * 审批人组织名称
     */
    private String auditOrgName;

    /**
     * 审批状态，0-待审批，1-审批通过，2-审批未通过
     */
    private String status;

    /**
     * 拒绝原因，1-党员不存在，2-党员组织关系转接中，3-党员信息流转中
     */
    private String refuse;

    /**
     * 说明理由
     */
    private String reason;

    /**
     *
     */
    private Date createTime;

    /**
     *
     */
    private Date updateTime;

    /**
     *
     */
    private Date deleteTime;

    /**
     *
     */
    private String remark;

    /**
     *
     */
    private Date timestamp;
}
