package com.zenith.front.model.dto;

import com.zenith.front.model.validate.group.*;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

import javax.validation.constraints.Max;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Positive;

/**
 * <AUTHOR>
 * @Description: 民主评议
 * @date 2019/6/18 14:27
 */
@Data
@ToString
@NoArgsConstructor
@AllArgsConstructor
public class ReviewListDTO {
    /**
     * 组织层级码
     */
    @NotBlank(groups = {Common1Group.class, Common2Group.class}, message = "orgCodeLevel 不能为空")
    private String orgCodeLevel;

    /**
     * 组织code
     */
    @NotBlank(groups = {Common2Group.class}, message = "orgCode 不能为空")
    private String orgCode;

    /**
     * 搜索框,按名称搜索
     */
    private String name;

    /**
     * 状态,评议状态(1--党员互评中,2--领导评议中,3--评议结束)
     */
    private Integer status;

    /**
     * 民主评议code
     */
    @NotBlank(groups = {Common2Group.class, Common3Group.class}, message = "reviewCode 不能为空")
    private String reviewCode;

    /**
     * 人员code
     */
    @NotBlank(groups = {Common2Group.class, Common4Group.class}, message = "memCode 不能为空")
    private String memCode;

    /**
     * 年份
     */
    @NotBlank(groups = Common2Group.class, message = "year 不能为空")
    private String year;

    /**
     * 分页页数
     */
    @Positive(groups = QueryGroup.class, message = "pageNum 最小为1")
    @NotNull(groups = QueryGroup.class, message = "pageNum 不能为空")
    private Integer pageNum;
    /**
     * 分页每页数
     */
    @Max(value = 100, groups = QueryGroup.class, message = "pageSize 大小范围在1-100")
    @Positive(groups = QueryGroup.class, message = "pageSize 最小为1")
    @NotNull(groups = QueryGroup.class, message = "pageSize 不能为空")
    private Integer pageSize;

    /**
     * 是否是个人端,1--是,0或空--否
     */
    private String isPerson;

}
