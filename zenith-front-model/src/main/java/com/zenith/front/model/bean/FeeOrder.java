package com.zenith.front.model.bean;

import java.math.BigDecimal;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.extension.activerecord.Model;

import java.util.Date;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableField;

import java.io.Serializable;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2021-07-11
 */
@TableName("ccp_fee_order")
public class FeeOrder extends Model<FeeOrder> {

    private static final long serialVersionUID = 1L;

    /**
     * 唯一自增长主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 订单自己生成code
     */
    @TableField("code")
    private String code;

    /**
     * 订单code[传送给微信的订单code,因为可能出现补缴，一笔订单是否拆分]
     */
    @TableField("fee_order")
    private String feeOrder;

    /**
     * 缴纳人员code
     */
    @TableField("mem_code")
    private String memCode;

    /**
     * 缴纳人员所在组织唯一标识符
     */
    @TableField("mem_org_code")
    private String memOrgCode;

    /**
     * 缴纳时所在的人员组织层级码
     */
    @TableField("mem_org_org_code")
    private String memOrgOrgCode;

    /**
     * 金额
     */
    @TableField("money")
    private BigDecimal money;

    /**
     * 支付时间
     */
    @TableField("pay_date")
    private Date payDate;

    /**
     * 支付月份[缴纳月份]
     */
    @TableField("pay_month")
    private String payMonth;

    /**
     * 状态[SUCCESS_支付成功],[REFUND_转入退款][NOTPAY_未支付][CLOSED_已关闭][REVOKED_已撤销（刷卡支付）][USERPAYING_用户支付中][PAYERROR_支付失败(其他原因，如银行返回失败)]
     */
    @TableField("state")
    private String state;

    /**
     * 支付类型 1:手机支付  2: PC支付
     */
    @TableField("pay_type")
    private String payType;

    /**
     * 商户号
     */
    @TableField("merchantid")
    private String merchantid;

    /**
     * 微信订单号
     */
    @TableField("merchant_order")
    private String merchantOrder;

    /**
     * 备注
     */
    @TableField("remark")
    private String remark;

    /**
     * 缴纳类型[1是个人缴纳,2是代缴]
     */
    @TableField("order_type")
    private String orderType;

    /**
     * 是否被回调消费【1是2否】
     */
    @TableField("is_consumption")
    private Integer isConsumption;

    /**
     * 创建时间
     */
    @TableField("create_time")
    private Date createTime;

    /**
     * 更新时间
     */
    @TableField("update_time")
    private Date updateTime;

    /**
     * 是否删除
     */
    @TableField("delete_time")
    private Date deleteTime;

    /**
     * 支付年份[缴费年份]
     */
    @TableField("pay_year")
    private String payYear;

    /**
     * 支付时人员所在组织标识符集合
     */
    @TableField("pay_org_code_set")
    private String payOrgCodeSet;

    /**
     * 最后缴费时间
     */
    @TableField("last_pay_date")
    private Date lastPayDate;

    /**
     * 缴费到的时间
     */
    @TableField("end_pay_date")
    private Date endPayDate;

    /**
     * 创建者人员
     */
    @TableField("creator_mem_code")
    private String creatorMemCode;

    /**
     * 创建者账号
     */
    @TableField("creator_account")
    private String creatorAccount;


    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getFeeOrder() {
        return feeOrder;
    }

    public void setFeeOrder(String feeOrder) {
        this.feeOrder = feeOrder;
    }

    public String getMemCode() {
        return memCode;
    }

    public void setMemCode(String memCode) {
        this.memCode = memCode;
    }

    public String getMemOrgCode() {
        return memOrgCode;
    }

    public void setMemOrgCode(String memOrgCode) {
        this.memOrgCode = memOrgCode;
    }

    public String getMemOrgOrgCode() {
        return memOrgOrgCode;
    }

    public void setMemOrgOrgCode(String memOrgOrgCode) {
        this.memOrgOrgCode = memOrgOrgCode;
    }

    public BigDecimal getMoney() {
        return money;
    }

    public void setMoney(BigDecimal money) {
        this.money = money;
    }

    public Date getPayDate() {
        return payDate;
    }

    public void setPayDate(Date payDate) {
        this.payDate = payDate;
    }

    public String getPayMonth() {
        return payMonth;
    }

    public void setPayMonth(String payMonth) {
        this.payMonth = payMonth;
    }

    public String getState() {
        return state;
    }

    public void setState(String state) {
        this.state = state;
    }

    public String getPayType() {
        return payType;
    }

    public void setPayType(String payType) {
        this.payType = payType;
    }

    public String getMerchantid() {
        return merchantid;
    }

    public void setMerchantid(String merchantid) {
        this.merchantid = merchantid;
    }

    public String getMerchantOrder() {
        return merchantOrder;
    }

    public void setMerchantOrder(String merchantOrder) {
        this.merchantOrder = merchantOrder;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public String getOrderType() {
        return orderType;
    }

    public void setOrderType(String orderType) {
        this.orderType = orderType;
    }

    public Integer getIsConsumption() {
        return isConsumption;
    }

    public void setIsConsumption(Integer isConsumption) {
        this.isConsumption = isConsumption;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    public Date getDeleteTime() {
        return deleteTime;
    }

    public void setDeleteTime(Date deleteTime) {
        this.deleteTime = deleteTime;
    }

    public String getPayYear() {
        return payYear;
    }

    public void setPayYear(String payYear) {
        this.payYear = payYear;
    }

    public String getPayOrgCodeSet() {
        return payOrgCodeSet;
    }

    public void setPayOrgCodeSet(String payOrgCodeSet) {
        this.payOrgCodeSet = payOrgCodeSet;
    }

    public Date getLastPayDate() {
        return lastPayDate;
    }

    public void setLastPayDate(Date lastPayDate) {
        this.lastPayDate = lastPayDate;
    }

    public Date getEndPayDate() {
        return endPayDate;
    }

    public void setEndPayDate(Date endPayDate) {
        this.endPayDate = endPayDate;
    }

    public String getCreatorMemCode() {
        return creatorMemCode;
    }

    public void setCreatorMemCode(String creatorMemCode) {
        this.creatorMemCode = creatorMemCode;
    }

    public String getCreatorAccount() {
        return creatorAccount;
    }

    public void setCreatorAccount(String creatorAccount) {
        this.creatorAccount = creatorAccount;
    }

    @Override
    public Serializable pkVal() {
        return this.id;
    }

    @Override
    public String toString() {
        return "FeeOrder{" +
                "id=" + id +
                ", code=" + code +
                ", feeOrder=" + feeOrder +
                ", memCode=" + memCode +
                ", memOrgCode=" + memOrgCode +
                ", memOrgOrgCode=" + memOrgOrgCode +
                ", money=" + money +
                ", payDate=" + payDate +
                ", payMonth=" + payMonth +
                ", state=" + state +
                ", payType=" + payType +
                ", merchantid=" + merchantid +
                ", merchantOrder=" + merchantOrder +
                ", remark=" + remark +
                ", orderType=" + orderType +
                ", isConsumption=" + isConsumption +
                ", createTime=" + createTime +
                ", updateTime=" + updateTime +
                ", deleteTime=" + deleteTime +
                ", payYear=" + payYear +
                ", payOrgCodeSet=" + payOrgCodeSet +
                ", lastPayDate=" + lastPayDate +
                ", endPayDate=" + endPayDate +
                ", creatorMemCode=" + creatorMemCode +
                ", creatorAccount=" + creatorAccount +
                "}";
    }


    /**************额外字段****************/
    /**
     * 被缴纳人员名称
     */
    @TableField(exist = false)
    private String memName;
    /**
     * 缴纳人员名称
     */
    @TableField(exist = false)
    private String creatorName;
    /**
     * 组织名称
     */
    @TableField(exist = false)
    private String orgName;

    public String getMemName() {
        return memName;
    }

    public void setMemName(String memName) {
        this.memName = memName;
    }

    public String getCreatorName() {
        return creatorName;
    }

    public void setCreatorName(String creatorName) {
        this.creatorName = creatorName;
    }

    public String getOrgName() {
        return orgName;
    }

    public void setOrgName(String orgName) {
        this.orgName = orgName;
    }
}
