package com.zenith.front.model.bean;

import java.math.BigDecimal;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.extension.activerecord.Model;

import java.util.Date;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableField;

import java.io.Serializable;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2022-04-18
 */
@TableName("ccp_unit_expand_collective_economy")
public class UnitExpandCollectiveEconomy extends Model<UnitExpandCollectiveEconomy> {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(value = "code", type = IdType.ASSIGN_UUID)
    private String code;

    /**
     * 单位code
     */
    @TableField("unit_code")
    private String unitCode;

    /**
     * 获扶持资金年度
     */
    @TableField("year")
    private Integer year;

    /**
     * 是否获中央和省级财政扶持资金（1是 0否）
     */
    @TableField("has_financial_support")
    private Integer hasFinancialSupport;

    /**
     * 中央和省级财政扶持资金
     */
    @TableField("financial_support_enforced")
    private BigDecimal financialSupportEnforced;

    /**
     * 中央和省级财政扶持资金执行率
     */
    @TableField("enforced")
    private BigDecimal enforced;

    /**
     * 已完工验收项目数字
     */
    @TableField("completed_acceptance_projects")
    private Integer completedAcceptanceProjects;

    /**
     * 已获得收益
     */
    @TableField("income_obtained")
    private BigDecimal incomeObtained;

    /**
     * 操作用户
     */
    @TableField("update_account")
    private String updateAccount;

    /**
     * 创建时间
     */
    @TableField("create_time")
    private Date createTime;

    /**
     * 修改时间
     */
    @TableField("update_time")
    private Date updateTime;

    /**
     * 删除时间
     */
    @TableField("delete_time")
    private Date deleteTime;


    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getUnitCode() {
        return unitCode;
    }

    public void setUnitCode(String unitCode) {
        this.unitCode = unitCode;
    }

    public Integer getYear() {
        return year;
    }

    public void setYear(Integer year) {
        this.year = year;
    }

    public Integer getHasFinancialSupport() {
        return hasFinancialSupport;
    }

    public void setHasFinancialSupport(Integer hasFinancialSupport) {
        this.hasFinancialSupport = hasFinancialSupport;
    }

    public BigDecimal getFinancialSupportEnforced() {
        return financialSupportEnforced;
    }

    public void setFinancialSupportEnforced(BigDecimal financialSupportEnforced) {
        this.financialSupportEnforced = financialSupportEnforced;
    }

    public BigDecimal getEnforced() {
        return enforced;
    }

    public void setEnforced(BigDecimal enforced) {
        this.enforced = enforced;
    }

    public Integer getCompletedAcceptanceProjects() {
        return completedAcceptanceProjects;
    }

    public void setCompletedAcceptanceProjects(Integer completedAcceptanceProjects) {
        this.completedAcceptanceProjects = completedAcceptanceProjects;
    }

    public BigDecimal getIncomeObtained() {
        return incomeObtained;
    }

    public void setIncomeObtained(BigDecimal incomeObtained) {
        this.incomeObtained = incomeObtained;
    }

    public String getUpdateAccount() {
        return updateAccount;
    }

    public void setUpdateAccount(String updateAccount) {
        this.updateAccount = updateAccount;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    public Date getDeleteTime() {
        return deleteTime;
    }

    public void setDeleteTime(Date deleteTime) {
        this.deleteTime = deleteTime;
    }

    @Override
    protected Serializable pkVal() {
        return this.code;
    }

    @Override
    public String toString() {
        return "UnitExpandCollectiveEconomy{" +
                "code=" + code +
                ", unitCode=" + unitCode +
                ", year=" + year +
                ", hasFinancialSupport=" + hasFinancialSupport +
                ", financialSupportEnforced=" + financialSupportEnforced +
                ", enforced=" + enforced +
                ", completedAcceptanceProjects=" + completedAcceptanceProjects +
                ", incomeObtained=" + incomeObtained +
                ", updateAccount=" + updateAccount +
                ", createTime=" + createTime +
                ", updateTime=" + updateTime +
                ", deleteTime=" + deleteTime +
                "}";
    }
}
