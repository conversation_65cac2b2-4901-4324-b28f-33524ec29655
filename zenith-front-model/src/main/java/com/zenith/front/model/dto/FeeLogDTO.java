package com.zenith.front.model.dto;

import com.zenith.front.model.bean.FeeLog;
import lombok.*;

@ToString
public class FeeLogDTO{

   	/**
   	 * 党费标准自增长ID
   	 */
    private Long id;
   	/**
   	 * 党费标准唯一标识符
   	 */
    private String code;
   	/**
   	 * 备注
   	 */
    private String remark;
   	/**
   	 * 党费标准code
   	 */
    private String feeCode;
   	/**
   	 * 创建者账号
   	 */
    private String creatorAccount;
   	/**
   	 * 详情
   	 */
    private String detail;
   	/**
   	 * 人员code
   	 */
    private String memCode;
   	/**
   	 * 设置时人员所在组织唯一标识符
   	 */
    private String memOrgCode;
   	/**
   	 * 设置时人员所在组织层级码
   	 */
    private String memOrgOrgCode;
   	/**
   	 * 缴纳金额
   	 */
    private java.math.BigDecimal money;
   	/**
   	 * 党费基数
   	 */
    private java.math.BigDecimal base;
   	/**
   	 * 党费标准
   	 */
    private java.math.BigDecimal stand;
   	/**
   	 * 备注
   	 */
    private String reason;
   	/**
   	 * 计算类型code
   	 */
    private String d49Code;
   	/**
   	 * 计算类型名称
   	 */
    private String d49Name;
   	/**
   	 * 创建时间
   	 */
    private java.util.Date createTime;
   	/**
   	 * 更新时间
   	 */
    private java.util.Date updateTime;
   	/**
   	 * 删除时间
   	 */
    private java.util.Date deleteTime;

    public FeeLogDTO setId(Long id){
        this.id = id;
        return this;
    }
    public Long getId() {
    	return this.id;
    }
    public FeeLogDTO setCode(String code){
        this.code = code;
        return this;
    }
    public String getCode() {
    	return this.code;
    }
    public FeeLogDTO setRemark(String remark){
        this.remark = remark;
        return this;
    }
    public String getRemark() {
    	return this.remark;
    }
    public FeeLogDTO setFeeCode(String feeCode){
        this.feeCode = feeCode;
        return this;
    }
    public String getFeeCode() {
    	return this.feeCode;
    }
    public FeeLogDTO setCreatorAccount(String creatorAccount){
        this.creatorAccount = creatorAccount;
        return this;
    }
    public String getCreatorAccount() {
    	return this.creatorAccount;
    }
    public FeeLogDTO setDetail(String detail){
        this.detail = detail;
        return this;
    }
    public String getDetail() {
    	return this.detail;
    }
    public FeeLogDTO setMemCode(String memCode){
        this.memCode = memCode;
        return this;
    }
    public String getMemCode() {
    	return this.memCode;
    }
    public FeeLogDTO setMemOrgCode(String memOrgCode){
        this.memOrgCode = memOrgCode;
        return this;
    }
    public String getMemOrgCode() {
    	return this.memOrgCode;
    }
    public FeeLogDTO setMemOrgOrgCode(String memOrgOrgCode){
        this.memOrgOrgCode = memOrgOrgCode;
        return this;
    }
    public String getMemOrgOrgCode() {
    	return this.memOrgOrgCode;
    }
    public FeeLogDTO setMoney(java.math.BigDecimal money){
        this.money = money;
        return this;
    }
    public java.math.BigDecimal getMoney() {
    	return this.money;
    }
    public FeeLogDTO setBase(java.math.BigDecimal base){
        this.base = base;
        return this;
    }
    public java.math.BigDecimal getBase() {
    	return this.base;
    }
    public FeeLogDTO setStand(java.math.BigDecimal stand){
        this.stand = stand;
        return this;
    }
    public java.math.BigDecimal getStand() {
    	return this.stand;
    }
    public FeeLogDTO setReason(String reason){
        this.reason = reason;
        return this;
    }
    public String getReason() {
    	return this.reason;
    }
    public FeeLogDTO setD49Code(String d49Code){
        this.d49Code = d49Code;
        return this;
    }
    public String getD49Code() {
    	return this.d49Code;
    }
    public FeeLogDTO setD49Name(String d49Name){
        this.d49Name = d49Name;
        return this;
    }
    public String getD49Name() {
    	return this.d49Name;
    }
    public FeeLogDTO setCreateTime(java.util.Date createTime){
        this.createTime = createTime;
        return this;
    }
    public java.util.Date getCreateTime() {
    	return this.createTime;
    }
    public FeeLogDTO setUpdateTime(java.util.Date updateTime){
        this.updateTime = updateTime;
        return this;
    }
    public java.util.Date getUpdateTime() {
    	return this.updateTime;
    }
    public FeeLogDTO setDeleteTime(java.util.Date deleteTime){
        this.deleteTime = deleteTime;
        return this;
    }
    public java.util.Date getDeleteTime() {
    	return this.deleteTime;
    }


    public FeeLog toModel(){
        FeeLog model = new FeeLog();
        model.setId(this.id);
        model.setCode(this.code);
        model.setRemark(this.remark);
        model.setFeeCode(this.feeCode);
        model.setCreatorAccount(this.creatorAccount);
        model.setDetail(this.detail);
        model.setMemCode(this.memCode);
        model.setMemOrgCode(this.memOrgCode);
        model.setMemOrgOrgCode(this.memOrgOrgCode);
        model.setMoney(this.money);
        model.setBase(this.base);
        model.setStand(this.stand);
        model.setReason(this.reason);
        model.setD49Code(this.d49Code);
        model.setD49Name(this.d49Name);
        model.setCreateTime(this.createTime);
        model.setUpdateTime(this.updateTime);
        model.setDeleteTime(this.deleteTime);
        return model;
    }
}