package com.zenith.front.model.bean;

import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.extension.activerecord.Model;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2021-12-15
 */
@TableName("ccp_org_develop_rights")
public class OrgDevelopRights extends Model<OrgDevelopRights> {

    private static final long serialVersionUID = 1L;

    /**
     * 唯一标识符code
     */
    @TableId(value = "code", type = IdType.ASSIGN_UUID)
    private String code;

    /**
     * 组织唯一标识符，中组部code
     */
    @TableField("zb_code")
    private String zbCode;

    /**
     * 组织code
     */
    @TableField("org_code")
    private String orgCode;

    /**
     * 组织层级码
     */
    @TableField("org_level_code")
    private String orgLevelCode;

    /**
     * 组织名称
     */
    @TableField("name")
    private String name;

    /**
     * 创建时间
     */
    @TableField("create_time")
    private Date createTime;

    /**
     * 更新时间
     */
    @TableField(value = "update_time", fill = FieldFill.UPDATE)
    private Date updateTime;

    /**
     * 删除时间
     */
    @TableField("delete_time")
    private Date deleteTime;


    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getZbCode() {
        return zbCode;
    }

    public void setZbCode(String zbCode) {
        this.zbCode = zbCode;
    }

    public String getOrgCode() {
        return orgCode;
    }

    public void setOrgCode(String orgCode) {
        this.orgCode = orgCode;
    }

    public String getOrgLevelCode() {
        return orgLevelCode;
    }

    public void setOrgLevelCode(String orgLevelCode) {
        this.orgLevelCode = orgLevelCode;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    public Date getDeleteTime() {
        return deleteTime;
    }

    public void setDeleteTime(Date deleteTime) {
        this.deleteTime = deleteTime;
    }

    @Override
    protected Serializable pkVal() {
        return this.code;
    }

    @Override
    public String toString() {
        return "OrgDevelopRights{" +
                "code=" + code +
                ", zbCode=" + zbCode +
                ", orgCode=" + orgCode +
                ", orgLevelCode=" + orgLevelCode +
                ", name=" + name +
                ", createTime=" + createTime +
                ", updateTime=" + updateTime +
                ", deleteTime=" + deleteTime +
                "}";
    }
}
