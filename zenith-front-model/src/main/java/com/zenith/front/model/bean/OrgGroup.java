package com.zenith.front.model.bean;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2021-07-11
 */
@TableName("ccp_org_group")
public class OrgGroup extends Model<OrgGroup> {

    private static final long serialVersionUID = 1L;

    /**
     * 唯一标识符code
     */
    @TableField("code")
    private String code;

    /**
     * 唯一自增长id
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 党小组组织层级码
     */
    @TableField("group_org_code")
    private String groupOrgCode;

    /**
     * 党小组组织zb_code
     */
    @TableField("zb_code")
    private String zbCode;

    /**
     * 党小组名称
     */
    @TableField("name")
    private String name;

    /**
     * 党小组创建时间
     */
    @TableField("create_date")
    private Date createDate;

    /**
     * 建立时间
     */
    @TableField("create_time")
    private Date createTime;

    /**
     * 更新时间
     */
    @TableField("update_time")
    private Date updateTime;

    /**
     * 是否退休党小组，暂时不做操作保留字段
     */
    @TableField("is_retire")
    private Boolean isRetire;

    /**
     * 党小组组织code
     */
    @TableField("org_code")
    private String orgCode;

    /**
     * 删除时间
     */
    @TableField("delete_time")
    private Date deleteTime;

    /**
     * 时间戳
     */
    @TableField("timestamp")
    private Date timestamp;

    /**
     * 是否历史数据
     */
    @TableField("is_history")
    private Integer isHistory;

    /**
     * 最近更更新账号
     */
    @TableField("update_account")
    private String updateAccount;


    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getGroupOrgCode() {
        return groupOrgCode;
    }

    public void setGroupOrgCode(String groupOrgCode) {
        this.groupOrgCode = groupOrgCode;
    }

    public String getZbCode() {
        return zbCode;
    }

    public void setZbCode(String zbCode) {
        this.zbCode = zbCode;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public Date getCreateDate() {
        return createDate;
    }

    public void setCreateDate(Date createDate) {
        this.createDate = createDate;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    public Boolean getIsRetire() {
        return isRetire;
    }

    public void setIsRetire(Boolean isRetire) {
        this.isRetire = isRetire;
    }

    public String getOrgCode() {
        return orgCode;
    }

    public void setOrgCode(String orgCode) {
        this.orgCode = orgCode;
    }

    public Date getDeleteTime() {
        return deleteTime;
    }

    public void setDeleteTime(Date deleteTime) {
        this.deleteTime = deleteTime;
    }

    public Date getTimestamp() {
        return timestamp;
    }

    public void setTimestamp(Date timestamp) {
        this.timestamp = timestamp;
    }

    public Integer getIsHistory() {
        return isHistory;
    }

    public void setIsHistory(Integer isHistory) {
        this.isHistory = isHistory;
    }

    public String getUpdateAccount() {
        return updateAccount;
    }

    public void setUpdateAccount(String updateAccount) {
        this.updateAccount = updateAccount;
    }

    @Override
    public Serializable pkVal() {
        return this.code;
    }

    @Override
    public String toString() {
        return "OrgGroup{" +
                "code=" + code +
                ", id=" + id +
                ", groupOrgCode=" + groupOrgCode +
                ", zbCode=" + zbCode +
                ", name=" + name +
                ", createDate=" + createDate +
                ", createTime=" + createTime +
                ", updateTime=" + updateTime +
                ", isRetire=" + isRetire +
                ", orgCode=" + orgCode +
                ", deleteTime=" + deleteTime +
                ", timestamp=" + timestamp +
                ", isHistory=" + isHistory +
                ", updateAccount=" + updateAccount +
                "}";
    }
}
