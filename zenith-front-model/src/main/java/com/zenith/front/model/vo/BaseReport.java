package com.zenith.front.model.vo;

import com.zenith.front.common.encrypt.annotation.EncryptEnabled;
import com.zenith.front.common.encrypt.annotation.EncryptField;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 */
@EncryptEnabled
public class BaseReport implements Serializable {

    private static final long serialVersionUID = -8037682899525771395L;

    /**
     * 人员code
     */
    private String code;

    /**
     * 党员code
     */
    private String memCode;

    /**
     * 姓名
     */
    @EncryptField(order = 1)
    private String name;

    /**
     * 身份证
     */
    @EncryptField(order = 2)
    private String idcard;

    /**
     * 组织层级码
     */
    private String memOrgCode;

    /**
     * 组织code
     */
    private String orgOrgCode;

    /**
     * 联系电话
     */
    @EncryptField(order = 3)
    private String phone;

    /**
     * 出生日期
     */
    private Date birthday;

    /**
     * 年龄
     */
    private Integer age;

    /**
     * 性别
     */
    private String sexCode;

    private String sexName;

    /**
     * 学历code  (dict_d07)
     */
    private String d07Code;

    /**
     * 学历名称
     */
    private String d07Name;

    /**
     * 岗位
     */
    private String d09Code;

    /**
     * 岗位名称
     */
    private String d09Name;

    /**
     * 聘任专业技术职务code
     */
    private String d19Code;

    /**
     * 聘任专业技术职务名称
     */
    private String d19Name;

    /**
     * 任职时所在组织层级码
     */
    private String positionOrgCode;

    /**
     * 任职时所在组织唯一标识code
     */
    private String orgCode;

    /**
     * 人员组织班子党内职务代码
     */
    private String d022Code;

    /**
     * 人员组织班子党内职务名称
     */
    @EncryptField(order = 4)
    private String d022Name;

    /**
     * 班子成员来源code
     */
    private String d138Code;

    /**
     * 班子成员来源名称
     */
    private String d138Name;

    /**
     * 人员单位班子成员在任职务代码  dict_d25
     */
    private String d25Code;

    /**
     * 人员单位班子成员在任职务代码  dict_d25
     */
    private String d25Name;

    /**
     * 单位类别_code
     */
    private String d04Code;

    /**
     * 人员单位类别_name
     */
    private String d04Name;

    /**
     * 人员单位code
     */
    private String unitCode;

    /**
     * 人员单位名称
     */
    private String unitName;

    /**
     * 党员集中培训情况_code
     */
    private String d142Code;

    /**
     * 党员集中培训情况_name
     */
    private String d142Name;

    /**
     * 报酬（万元/年）（数字[保留两位小数]、必填）
     */
    private BigDecimal rewardForOrg;

    /**
     * 报酬（万元/年）（数字[保留两位小数]、必填）
     */
    private BigDecimal rewardForVillage;

    /**
     * 1.驻村第一书记、2.驻村干部
     */
    private String d140Code;

    private String d140Name;

    /**
     * 1.中直、2.省直、3.市直、4.县直、5.乡镇
     */
    private String d141Code;

    private String d141Name;

    /**
     * 党内任职起始日期（驻村书记干部）
     */
    private Date startDateZc;

    /**
     * 党内任职结束日期（驻村书记干部）
     */
    private Date endDateZc;

    /**
     * 是否村任职选调生（是否选择框）
     */
    private String hasVillageTransferStudent;

    /**
     * 单位隶属关系
     */
    private String d35Code;

    private String d35Name;

    /**
     * 是否县乡领导班子成员帮带(1是 0否)
     */
    private String hasLeadersHelpPeople;

    /**
     * 到村任职补助经费
     */
    private BigDecimal subsidies;

    /**
     * 村社区干部岗位_code
     */
    private String d143Code;

    private String d143Name;
    /**
     * 1 村两委班子，2 驻村干部，3 村社区后备干部
     */
    private String moduleType;

    private Date createTime;

    private Date updateTime;

    private Date deleteTime;


    /**
     * 关联组织code
     */
    private String mainOrgCode;

    /**
     * 关联组织名称
     */
    private String mainOrgName;

    /**
     * 在编人数
     */
    private Integer atNumber;

    /**
     * 行政编制
     */
    private Integer atAdministrative;

    /**
     * 事业编制
     */
    private Integer atCareer;

    /**
     * 空缺编制
     */
    private Integer vacancy;

    /**
     * 行政编制
     */
    private Integer vacancyAdministrative;

    /**
     * 事业编制
     */
    private Integer vacancyCareer;

    /**
     * 被被	借调工作人员总数
     */
    private Integer secondedNum;

    /**
     * 占在编人数比例
     */
    private String atProportion;

    /**
     * 省级以上
     */
    private Integer provincialAbove;

    /**
     * 省级
     */
    private Integer provincial;

    /**
     * 市级
     */
    private Integer city;

    /**
     * 县级
     */
    private Integer county;

    /**
     * 参加县级及以上集中培训人数
     */
    private Integer joinAboveCountyTrainNum;

    /**
     * 村干部参加城镇职工养老保险人数
     */
    private Integer villageJoinUrbanWorkerNum;

    /**
     * 参加比例
     */
    private String joinProportion;

    /**
     * 组织层级码
     */
    private String createUnitOrgCode;

    /**
     * 组织code
     */
    private String createOrgCode;

    /**
     * 中央和省级财政扶持资金
     */
    private BigDecimal financialSupportEnforced;

    /**
     * 中央和省级财政扶持资金执行率
     */
    private BigDecimal enforced;

    /**
     * 已完工验收项目数字
     */
    private Integer completedAcceptanceProjects;

    /**
     * 已获得收益
     */
    private BigDecimal incomeObtained;

    /**
     * 应到村任职选调生人数（数字、必填）
     */
    private Integer numberOfStudentsToBeTransferredToTheVillage;


    /**
     * 不是党委委员的政府领导班子成员人数
     */
    private Integer numberOfNonGovernmentalMembers;

    /**
     * 是否有大学毕业生在村工作（选择框、必填）1 是 0 否
     */
    private Integer whetherThereAreCollegeGraduatesWorkingInTheVillage;

    /**
     * 到村任职补助经费使用率（数字、不能大于100，必填）
     */
    private String toTheVillageOfficeSubsidyFundsUtilizationRate;

    /**
     * 党员类型
     */
    private String d08Code;

    /**
     * 发展党员层级码
     */
    private String logOrgCode;

    /**
     * 流动党员层级码
     */
    private String memOrgOrgCode;

    /**
     * 组织类型
     */
    private String d01Code;

    /**
     * 村两委报酬（万元）
     */
    private BigDecimal rewardSum;

    /**
     * 组织类型
     */
    private String flowStatus;

    /**
     * 选调单位层级
     */
    private String d144Code;

    /**
     * 是否后备干部兼村委干部（默认为空）
     */
    private String hasReserveCommittee;
    /**
     * 是否任期未满调整第一书记（驻村干部）
     */
    private String hasTenureNotFull;

    /**
     * 召开支委会日期(成为预备党员日期)
     */
    private Date topreJoinOrgDate;
    /**
     * 1社区工作者，2村后备干部
     */
    private String sideType;

    /**
     * 1 产业工人
     */
    private Integer hasWorker;

    /**
     * 1 高知识群体
     */
    private Integer hasHighKnowledge;

    /**
     * 1 是否高层次人才
     */
    private Integer hasHighLevelTalents;

    /**
     * 1 是否民族院校
     */
    private Integer hasNationalColleges;

    /**
     * 知识分子情况
     */
    private String d154Code;


    private String d154Name;

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getMemCode() {
        return memCode;
    }

    public void setMemCode(String memCode) {
        this.memCode = memCode;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getIdcard() {
        return idcard;
    }

    public void setIdcard(String idcard) {
        this.idcard = idcard;
    }

    public String getMemOrgCode() {
        return memOrgCode;
    }

    public void setMemOrgCode(String memOrgCode) {
        this.memOrgCode = memOrgCode;
    }

    public String getOrgOrgCode() {
        return orgOrgCode;
    }

    public void setOrgOrgCode(String orgOrgCode) {
        this.orgOrgCode = orgOrgCode;
    }

    public String getPhone() {
        return phone;
    }

    public void setPhone(String phone) {
        this.phone = phone;
    }

    public Date getBirthday() {
        return birthday;
    }

    public void setBirthday(Date birthday) {
        this.birthday = birthday;
    }

    public Integer getAge() {
        return age;
    }

    public void setAge(Integer age) {
        this.age = age;
    }

    public String getSexCode() {
        return sexCode;
    }

    public void setSexCode(String sexCode) {
        this.sexCode = sexCode;
    }

    public String getSexName() {
        return sexName;
    }

    public void setSexName(String sexName) {
        this.sexName = sexName;
    }

    public String getD07Code() {
        return d07Code;
    }

    public void setD07Code(String d07Code) {
        this.d07Code = d07Code;
    }

    public String getD07Name() {
        return d07Name;
    }

    public String getD09Code() {
        return d09Code;
    }

    public void setD09Code(String d09Code) {
        this.d09Code = d09Code;
    }

    public String getD09Name() {
        return d09Name;
    }

    public void setD09Name(String d09Name) {
        this.d09Name = d09Name;
    }

    public String getD19Code() {
        return d19Code;
    }

    public void setD19Code(String d19Code) {
        this.d19Code = d19Code;
    }

    public String getD19Name() {
        return d19Name;
    }

    public void setD19Name(String d19Name) {
        this.d19Name = d19Name;
    }

    public void setD07Name(String d07Name) {
        this.d07Name = d07Name;
    }

    public String getPositionOrgCode() {
        return positionOrgCode;
    }

    public void setPositionOrgCode(String positionOrgCode) {
        this.positionOrgCode = positionOrgCode;
    }

    public String getOrgCode() {
        return orgCode;
    }

    public void setOrgCode(String orgCode) {
        this.orgCode = orgCode;
    }

    public String getD022Code() {
        return d022Code;
    }

    public void setD022Code(String d022Code) {
        this.d022Code = d022Code;
    }

    public String getD022Name() {
        return d022Name;
    }

    public void setD022Name(String d022Name) {
        this.d022Name = d022Name;
    }

    public String getD138Code() {
        return d138Code;
    }

    public void setD138Code(String d138Code) {
        this.d138Code = d138Code;
    }

    public String getD138Name() {
        return d138Name;
    }

    public void setD138Name(String d138Name) {
        this.d138Name = d138Name;
    }

    public String getD25Code() {
        return d25Code;
    }

    public void setD25Code(String d25Code) {
        this.d25Code = d25Code;
    }

    public String getD25Name() {
        return d25Name;
    }

    public void setD25Name(String d25Name) {
        this.d25Name = d25Name;
    }

    public String getD04Code() {
        return d04Code;
    }

    public void setD04Code(String d04Code) {
        this.d04Code = d04Code;
    }

    public String getD04Name() {
        return d04Name;
    }

    public void setD04Name(String d04Name) {
        this.d04Name = d04Name;
    }

    public String getUnitCode() {
        return unitCode;
    }

    public void setUnitCode(String unitCode) {
        this.unitCode = unitCode;
    }

    public String getUnitName() {
        return unitName;
    }

    public void setUnitName(String unitName) {
        this.unitName = unitName;
    }

    public String getD142Code() {
        return d142Code;
    }

    public void setD142Code(String d142Code) {
        this.d142Code = d142Code;
    }

    public String getD142Name() {
        return d142Name;
    }

    public void setD142Name(String d142Name) {
        this.d142Name = d142Name;
    }

    public BigDecimal getRewardForOrg() {
        return rewardForOrg;
    }

    public void setRewardForOrg(BigDecimal rewardForOrg) {
        this.rewardForOrg = rewardForOrg;
    }

    public BigDecimal getRewardForVillage() {
        return rewardForVillage;
    }

    public void setRewardForVillage(BigDecimal rewardForVillage) {
        this.rewardForVillage = rewardForVillage;
    }

    public String getD140Code() {
        return d140Code;
    }

    public void setD140Code(String d140Code) {
        this.d140Code = d140Code;
    }

    public String getD140Name() {
        return d140Name;
    }

    public void setD140Name(String d140Name) {
        this.d140Name = d140Name;
    }

    public String getD141Code() {
        return d141Code;
    }

    public void setD141Code(String d141Code) {
        this.d141Code = d141Code;
    }

    public String getD141Name() {
        return d141Name;
    }

    public void setD141Name(String d141Name) {
        this.d141Name = d141Name;
    }

    public Date getStartDateZc() {
        return startDateZc;
    }

    public void setStartDateZc(Date startDateZc) {
        this.startDateZc = startDateZc;
    }

    public Date getEndDateZc() {
        return endDateZc;
    }

    public void setEndDateZc(Date endDateZc) {
        this.endDateZc = endDateZc;
    }

    public String getHasVillageTransferStudent() {
        return hasVillageTransferStudent;
    }

    public void setHasVillageTransferStudent(String hasVillageTransferStudent) {
        this.hasVillageTransferStudent = hasVillageTransferStudent;
    }

    public String getD35Code() {
        return d35Code;
    }

    public void setD35Code(String d35Code) {
        this.d35Code = d35Code;
    }

    public String getD35Name() {
        return d35Name;
    }

    public void setD35Name(String d35Name) {
        this.d35Name = d35Name;
    }

    public String getHasLeadersHelpPeople() {
        return hasLeadersHelpPeople;
    }

    public void setHasLeadersHelpPeople(String hasLeadersHelpPeople) {
        this.hasLeadersHelpPeople = hasLeadersHelpPeople;
    }

    public BigDecimal getSubsidies() {
        return subsidies;
    }

    public void setSubsidies(BigDecimal subsidies) {
        this.subsidies = subsidies;
    }

    public String getD143Code() {
        return d143Code;
    }

    public void setD143Code(String d143Code) {
        this.d143Code = d143Code;
    }

    public String getD143Name() {
        return d143Name;
    }

    public void setD143Name(String d143Name) {
        this.d143Name = d143Name;
    }

    public String getModuleType() {
        return moduleType;
    }

    public void setModuleType(String moduleType) {
        this.moduleType = moduleType;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    public Date getDeleteTime() {
        return deleteTime;
    }

    public void setDeleteTime(Date deleteTime) {
        this.deleteTime = deleteTime;
    }

    public String getMainOrgCode() {
        return mainOrgCode;
    }

    public void setMainOrgCode(String mainOrgCode) {
        this.mainOrgCode = mainOrgCode;
    }

    public String getMainOrgName() {
        return mainOrgName;
    }

    public void setMainOrgName(String mainOrgName) {
        this.mainOrgName = mainOrgName;
    }

    public Integer getAtNumber() {
        return atNumber;
    }

    public void setAtNumber(Integer atNumber) {
        this.atNumber = atNumber;
    }

    public Integer getAtAdministrative() {
        return atAdministrative;
    }

    public void setAtAdministrative(Integer atAdministrative) {
        this.atAdministrative = atAdministrative;
    }

    public Integer getAtCareer() {
        return atCareer;
    }

    public void setAtCareer(Integer atCareer) {
        this.atCareer = atCareer;
    }

    public Integer getVacancy() {
        return vacancy;
    }

    public void setVacancy(Integer vacancy) {
        this.vacancy = vacancy;
    }

    public Integer getVacancyAdministrative() {
        return vacancyAdministrative;
    }

    public void setVacancyAdministrative(Integer vacancyAdministrative) {
        this.vacancyAdministrative = vacancyAdministrative;
    }

    public Integer getVacancyCareer() {
        return vacancyCareer;
    }

    public void setVacancyCareer(Integer vacancyCareer) {
        this.vacancyCareer = vacancyCareer;
    }

    public Integer getSecondedNum() {
        return secondedNum;
    }

    public void setSecondedNum(Integer secondedNum) {
        this.secondedNum = secondedNum;
    }

    public String getAtProportion() {
        return atProportion;
    }

    public void setAtProportion(String atProportion) {
        this.atProportion = atProportion;
    }

    public Integer getProvincialAbove() {
        return provincialAbove;
    }

    public void setProvincialAbove(Integer provincialAbove) {
        this.provincialAbove = provincialAbove;
    }

    public Integer getProvincial() {
        return provincial;
    }

    public void setProvincial(Integer provincial) {
        this.provincial = provincial;
    }

    public Integer getCity() {
        return city;
    }

    public void setCity(Integer city) {
        this.city = city;
    }

    public Integer getCounty() {
        return county;
    }

    public void setCounty(Integer county) {
        this.county = county;
    }

    public Integer getJoinAboveCountyTrainNum() {
        return joinAboveCountyTrainNum;
    }

    public void setJoinAboveCountyTrainNum(Integer joinAboveCountyTrainNum) {
        this.joinAboveCountyTrainNum = joinAboveCountyTrainNum;
    }

    public Integer getVillageJoinUrbanWorkerNum() {
        return villageJoinUrbanWorkerNum;
    }

    public void setVillageJoinUrbanWorkerNum(Integer villageJoinUrbanWorkerNum) {
        this.villageJoinUrbanWorkerNum = villageJoinUrbanWorkerNum;
    }

    public String getJoinProportion() {
        return joinProportion;
    }

    public void setJoinProportion(String joinProportion) {
        this.joinProportion = joinProportion;
    }

    public String getCreateUnitOrgCode() {
        return createUnitOrgCode;
    }

    public void setCreateUnitOrgCode(String createUnitOrgCode) {
        this.createUnitOrgCode = createUnitOrgCode;
    }

    public String getCreateOrgCode() {
        return createOrgCode;
    }

    public void setCreateOrgCode(String createOrgCode) {
        this.createOrgCode = createOrgCode;
    }

    public BigDecimal getFinancialSupportEnforced() {
        return financialSupportEnforced;
    }

    public void setFinancialSupportEnforced(BigDecimal financialSupportEnforced) {
        this.financialSupportEnforced = financialSupportEnforced;
    }

    public BigDecimal getEnforced() {
        return enforced;
    }

    public void setEnforced(BigDecimal enforced) {
        this.enforced = enforced;
    }

    public Integer getCompletedAcceptanceProjects() {
        return completedAcceptanceProjects;
    }

    public void setCompletedAcceptanceProjects(Integer completedAcceptanceProjects) {
        this.completedAcceptanceProjects = completedAcceptanceProjects;
    }

    public BigDecimal getIncomeObtained() {
        return incomeObtained;
    }

    public void setIncomeObtained(BigDecimal incomeObtained) {
        this.incomeObtained = incomeObtained;
    }

    public Integer getNumberOfStudentsToBeTransferredToTheVillage() {
        return numberOfStudentsToBeTransferredToTheVillage;
    }

    public void setNumberOfStudentsToBeTransferredToTheVillage(Integer numberOfStudentsToBeTransferredToTheVillage) {
        this.numberOfStudentsToBeTransferredToTheVillage = numberOfStudentsToBeTransferredToTheVillage;
    }

    public Integer getNumberOfNonGovernmentalMembers() {
        return numberOfNonGovernmentalMembers;
    }

    public void setNumberOfNonGovernmentalMembers(Integer numberOfNonGovernmentalMembers) {
        this.numberOfNonGovernmentalMembers = numberOfNonGovernmentalMembers;
    }

    public Integer getWhetherThereAreCollegeGraduatesWorkingInTheVillage() {
        return whetherThereAreCollegeGraduatesWorkingInTheVillage;
    }

    public void setWhetherThereAreCollegeGraduatesWorkingInTheVillage(Integer whetherThereAreCollegeGraduatesWorkingInTheVillage) {
        this.whetherThereAreCollegeGraduatesWorkingInTheVillage = whetherThereAreCollegeGraduatesWorkingInTheVillage;
    }

    public String getToTheVillageOfficeSubsidyFundsUtilizationRate() {
        return toTheVillageOfficeSubsidyFundsUtilizationRate;
    }

    public void setToTheVillageOfficeSubsidyFundsUtilizationRate(String toTheVillageOfficeSubsidyFundsUtilizationRate) {
        this.toTheVillageOfficeSubsidyFundsUtilizationRate = toTheVillageOfficeSubsidyFundsUtilizationRate;
    }

    public String getD08Code() {
        return d08Code;
    }

    public void setD08Code(String d08Code) {
        this.d08Code = d08Code;
    }

    public String getLogOrgCode() {
        return logOrgCode;
    }

    public void setLogOrgCode(String logOrgCode) {
        this.logOrgCode = logOrgCode;
    }

    public String getMemOrgOrgCode() {
        return memOrgOrgCode;
    }

    public void setMemOrgOrgCode(String memOrgOrgCode) {
        this.memOrgOrgCode = memOrgOrgCode;
    }

    public String getD01Code() {
        return d01Code;
    }

    public void setD01Code(String d01Code) {
        this.d01Code = d01Code;
    }

    public BigDecimal getRewardSum() {
        return rewardSum;
    }

    public void setRewardSum(BigDecimal rewardSum) {
        this.rewardSum = rewardSum;
    }

    public String getFlowStatus() {
        return flowStatus;
    }

    public void setFlowStatus(String flowStatus) {
        this.flowStatus = flowStatus;
    }

    public String getD144Code() {
        return d144Code;
    }

    public void setD144Code(String d144Code) {
        this.d144Code = d144Code;
    }

    public String getHasReserveCommittee() {
        return hasReserveCommittee;
    }

    public void setHasReserveCommittee(String hasReserveCommittee) {
        this.hasReserveCommittee = hasReserveCommittee;
    }

    public String getHasTenureNotFull() {
        return hasTenureNotFull;
    }

    public void setHasTenureNotFull(String hasTenureNotFull) {
        this.hasTenureNotFull = hasTenureNotFull;
    }

    public Date getTopreJoinOrgDate() {
        return topreJoinOrgDate;
    }

    public void setTopreJoinOrgDate(Date topreJoinOrgDate) {
        this.topreJoinOrgDate = topreJoinOrgDate;
    }

    public String getSideType() {
        return sideType;
    }

    public void setSideType(String sideType) {
        this.sideType = sideType;
    }

    public Integer getHasWorker() {
        return hasWorker;
    }

    public void setHasWorker(Integer hasWorker) {
        this.hasWorker = hasWorker;
    }

    public Integer getHasHighKnowledge() {
        return hasHighKnowledge;
    }

    public void setHasHighKnowledge(Integer hasHighKnowledge) {
        this.hasHighKnowledge = hasHighKnowledge;
    }

    public Integer getHasHighLevelTalents() {
        return hasHighLevelTalents;
    }

    public void setHasHighLevelTalents(Integer hasHighLevelTalents) {
        this.hasHighLevelTalents = hasHighLevelTalents;
    }

    public Integer getHasNationalColleges() {
        return hasNationalColleges;
    }

    public void setHasNationalColleges(Integer hasNationalColleges) {
        this.hasNationalColleges = hasNationalColleges;
    }

    public String getD154Code() {
        return d154Code;
    }

    public void setD154Code(String d154Code) {
        this.d154Code = d154Code;
    }

    public String getD154Name() {
        return d154Name;
    }

    public void setD154Name(String d154Name) {
        this.d154Name = d154Name;
    }
}
