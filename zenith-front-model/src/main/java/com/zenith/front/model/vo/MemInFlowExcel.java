package com.zenith.front.model.vo;

import com.zenith.front.common.annotation.ExcelAttribute;
import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 * @Date 2021/11/5 19:18
 * @Version 1.0
 */
@Data
public class MemInFlowExcel {

    @ExcelAttribute(sort = 0)
    private String memName;

    @ExcelAttribute(sort = 1)
    private String memOrgName;

    @ExcelAttribute(sort = 2)
    private String outflowOrgName;

    @ExcelAttribute(sort = 3)
    private String isProvOutName;

    @ExcelAttribute(sort = 4)
    private String matchSituation;

    @ExcelAttribute(sort = 5)
    private Date outflowDate;
}
