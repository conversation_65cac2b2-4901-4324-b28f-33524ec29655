package com.zenith.front.model.bean;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import java.io.Serializable;

/**
 * <AUTHOR>
 */
@TableName(value = "ccp_logic_check")
public class LogicCheck implements Serializable {

    private static final long serialVersionUID = -5294832954798481739L;

    /**
     *
     */
    @TableId(value = "id")
    private Integer id;

    /**
     * 序号
     */
    @TableField(value = "no")
    private Integer no;

    /**
     * 条件
     */
    @TableField(value = "condition")
    private String condition;

    /**
     * 查询总数sql
     */
    @TableField(value = "check_count_sql")
    private String checkCountSql;

    /**
     * 查询sql
     */
    @TableField(value = "check_sql")
    private String checkSql;

    /**
     * 查询条件
     */
    @TableField(value = "check_seat")
    private String checkSeat;

    /**
     * 查询表名
     */
    @TableField(value = "check_table_name")
    private String checkTableName;

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public Integer getNo() {
        return no;
    }

    public void setNo(Integer no) {
        this.no = no;
    }

    public String getCondition() {
        return condition;
    }

    public void setCondition(String condition) {
        this.condition = condition;
    }

    public String getCheckCountSql() {
        return checkCountSql;
    }

    public void setCheckCountSql(String checkCountSql) {
        this.checkCountSql = checkCountSql;
    }

    public String getCheckSql() {
        return checkSql;
    }

    public void setCheckSql(String checkSql) {
        this.checkSql = checkSql;
    }

    public String getCheckSeat() {
        return checkSeat;
    }

    public void setCheckSeat(String checkSeat) {
        this.checkSeat = checkSeat;
    }

    public String getCheckTableName() {
        return checkTableName;
    }

    public void setCheckTableName(String checkTableName) {
        this.checkTableName = checkTableName;
    }
}