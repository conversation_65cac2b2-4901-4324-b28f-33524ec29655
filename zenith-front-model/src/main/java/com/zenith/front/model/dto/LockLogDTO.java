package com.zenith.front.model.dto;

import com.zenith.front.model.custom.HasSubordinateField;

import java.util.List;

/**
 * <AUTHOR>
 */
public class LockLogDTO extends HasSubordinateField {

    private static final long serialVersionUID = -2280625249646856953L;

    /**
     * 分页：当前页
     */
    private Integer pageNum;

    /**
     * 分页：每页显示条数
     */
    private Integer pageSize;

    /**
     * 组织code
     */
    private String code;

    /**
     * 申请记录1 解锁记录2 锁定记录3
     */
    private String type;

    /**
     * 解锁对象 1 党员 2 单位 3 组织
     */
    private String unlockObject;

    /**
     * 解锁对象 1 党员 2 单位 3 组织
     */
    private String lockObject;

    /**
     * 搜索关键词
     */
    private String keyword;

    /**
     * 操作状态
     */
    private List<String> state;

    public Integer getPageNum() {
        return pageNum;
    }

    public void setPageNum(Integer pageNum) {
        this.pageNum = pageNum;
    }

    public Integer getPageSize() {
        return pageSize;
    }

    public void setPageSize(Integer pageSize) {
        this.pageSize = pageSize;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public String getUnlockObject() {
        return unlockObject;
    }

    public void setUnlockObject(String unlockObject) {
        this.unlockObject = unlockObject;
    }

    public String getLockObject() {
        return lockObject;
    }

    public void setLockObject(String lockObject) {
        this.lockObject = lockObject;
    }

    public String getKeyword() {
        return keyword;
    }

    public void setKeyword(String keyword) {
        this.keyword = keyword;
    }

    public List<String> getState() {
        return state;
    }

    public void setState(List<String> state) {
        this.state = state;
    }

    @Override
    public String toString() {
        return "LockLogDTO{" +
                "pageNum=" + pageNum +
                ", pageSize=" + pageSize +
                ", code='" + code + '\'' +
                ", type='" + type + '\'' +
                ", unlockObject='" + unlockObject + '\'' +
                ", lockObject='" + lockObject + '\'' +
                ", keyword='" + keyword + '\'' +
                ", state='" + state + '\'' +
                '}';
    }
}