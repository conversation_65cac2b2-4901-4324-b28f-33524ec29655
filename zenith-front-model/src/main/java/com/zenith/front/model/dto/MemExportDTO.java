package com.zenith.front.model.dto;

import com.zenith.front.common.annotation.ExcelAttribute;

/**
 * <AUTHOR>
 * @date 2021/9/29
 */
public class MemExportDTO {
    /**
     * 姓名
     */
    @ExcelAttribute(sort = 0)
    private String name;
    @ExcelAttribute(sort = 1)
    private String idcard;
    @ExcelAttribute(sort = 2)
    private String orgZbCode;
    @ExcelAttribute(sort = 3)
    private String sexName;
    @ExcelAttribute(sort = 4)
    private String birthday;
    @ExcelAttribute(sort = 5)
    private String d48Name;
    @ExcelAttribute(sort = 6)
    private String d06Name;
    @ExcelAttribute(sort = 7)
    private String d07Name;
    @ExcelAttribute(sort = 8)
    private String isDispatch;
    @ExcelAttribute(sort = 9)
    private String d09Name;
    @ExcelAttribute(sort = 10)
    private String isFarmer;
    @ExcelAttribute(sort = 11)
    private String phone;
    @ExcelAttribute(sort = 12)
    private String otherTel;
    @ExcelAttribute(sort = 13)
    private String orgCode;
    @ExcelAttribute(sort = 14)
    private String d08Name;
    @ExcelAttribute(sort = 15)
    private String applyDate;
    @ExcelAttribute(sort = 16)
    private String activeDate;
    @ExcelAttribute(sort = 17)
    private String objectDate;
    @ExcelAttribute(sort = 18)
    private String d21Name;
    @ExcelAttribute(sort = 19)
    private String d19Name;
    @ExcelAttribute(sort = 20)
    private String d20Name;
    @ExcelAttribute(sort = 21)
    private String joinOrgDate;
    @ExcelAttribute(sort = 22)
    private String d27Name;
    @ExcelAttribute(sort = 23)
    private String d11Name;
    @ExcelAttribute(sort = 24)
    private String joinOrgPartyDate;
    @ExcelAttribute(sort = 25)
    private String branchOrgName;
    @ExcelAttribute(sort = 26)
    private String fullMemberDate;
    @ExcelAttribute(sort = 27)
    private String d28Name;
    @ExcelAttribute(sort = 28)
    private String d49Name;
    @ExcelAttribute(sort = 29)
    private String d18Name;
    @ExcelAttribute(sort = 30)
    private String lostContactDate;
    @ExcelAttribute(sort = 31)
    private String homeAddress;


    public String getBranchOrgName() {
        return branchOrgName;
    }

    public void setBranchOrgName(String branchOrgName) {
        this.branchOrgName = branchOrgName;
    }

    public String getActiveDate() {
        return activeDate;
    }

    public void setActiveDate(String activeDate) {
        this.activeDate = activeDate;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getIdcard() {
        return idcard;
    }

    public void setIdcard(String idcard) {
        this.idcard = idcard;
    }

    public String getOrgZbCode() {
        return orgZbCode;
    }

    public void setOrgZbCode(String orgZbCode) {
        this.orgZbCode = orgZbCode;
    }

    public String getSexName() {
        return sexName;
    }

    public void setSexName(String sexName) {
        this.sexName = sexName;
    }

    public String getBirthday() {
        return birthday;
    }

    public void setBirthday(String birthday) {
        this.birthday = birthday;
    }

    public String getD48Name() {
        return d48Name;
    }

    public void setD48Name(String d48Name) {
        this.d48Name = d48Name;
    }

    public String getD06Name() {
        return d06Name;
    }

    public void setD06Name(String d06Name) {
        this.d06Name = d06Name;
    }

    public String getD07Name() {
        return d07Name;
    }

    public void setD07Name(String d07Name) {
        this.d07Name = d07Name;
    }

    public String getIsDispatch() {
        return isDispatch;
    }

    public void setIsDispatch(String isDispatch) {
        this.isDispatch = isDispatch;
    }

    public String getD09Name() {
        return d09Name;
    }

    public void setD09Name(String d09Name) {
        this.d09Name = d09Name;
    }

    public String getIsFarmer() {
        return isFarmer;
    }

    public void setIsFarmer(String isFarmer) {
        this.isFarmer = isFarmer;
    }

    public String getPhone() {
        return phone;
    }

    public void setPhone(String phone) {
        this.phone = phone;
    }

    public String getOtherTel() {
        return otherTel;
    }

    public void setOtherTel(String otherTel) {
        this.otherTel = otherTel;
    }

    public String getOrgCode() {
        return orgCode;
    }

    public void setOrgCode(String orgCode) {
        this.orgCode = orgCode;
    }

    public String getD08Name() {
        return d08Name;
    }

    public void setD08Name(String d08Name) {
        this.d08Name = d08Name;
    }

    public String getApplyDate() {
        return applyDate;
    }

    public void setApplyDate(String applyDate) {
        this.applyDate = applyDate;
    }

    public String getObjectDate() {
        return objectDate;
    }

    public void setObjectDate(String objectDate) {
        this.objectDate = objectDate;
    }

    public String getD21Name() {
        return d21Name;
    }

    public void setD21Name(String d21Name) {
        this.d21Name = d21Name;
    }

    public String getD19Name() {
        return d19Name;
    }

    public void setD19Name(String d19Name) {
        this.d19Name = d19Name;
    }

    public String getD20Name() {
        return d20Name;
    }

    public void setD20Name(String d20Name) {
        this.d20Name = d20Name;
    }

    public String getJoinOrgDate() {
        return joinOrgDate;
    }

    public void setJoinOrgDate(String joinOrgDate) {
        this.joinOrgDate = joinOrgDate;
    }

    public String getD27Name() {
        return d27Name;
    }

    public void setD27Name(String d27Name) {
        this.d27Name = d27Name;
    }

    public String getD11Name() {
        return d11Name;
    }

    public void setD11Name(String d11Name) {
        this.d11Name = d11Name;
    }

    public String getJoinOrgPartyDate() {
        return joinOrgPartyDate;
    }

    public void setJoinOrgPartyDate(String joinOrgPartyDate) {
        this.joinOrgPartyDate = joinOrgPartyDate;
    }

    public String getFullMemberDate() {
        return fullMemberDate;
    }

    public void setFullMemberDate(String fullMemberDate) {
        this.fullMemberDate = fullMemberDate;
    }

    public String getD28Name() {
        return d28Name;
    }

    public void setD28Name(String d28Name) {
        this.d28Name = d28Name;
    }

    public String getD49Name() {
        return d49Name;
    }

    public void setD49Name(String d49Name) {
        this.d49Name = d49Name;
    }

    public String getD18Name() {
        return d18Name;
    }

    public void setD18Name(String d18Name) {
        this.d18Name = d18Name;
    }

    public String getLostContactDate() {
        return lostContactDate;
    }

    public void setLostContactDate(String lostContactDate) {
        this.lostContactDate = lostContactDate;
    }

    public String getHomeAddress() {
        return homeAddress;
    }

    public void setHomeAddress(String homeAddress) {
        this.homeAddress = homeAddress;
    }
}
