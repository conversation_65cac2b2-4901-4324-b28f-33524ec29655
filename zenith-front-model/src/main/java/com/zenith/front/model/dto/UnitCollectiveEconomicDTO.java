package com.zenith.front.model.dto;

import lombok.ToString;

import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 * @Date 2021/9/10 11:50
 * @Version 1.0
 */
@ToString
public class UnitCollectiveEconomicDTO {
    /**
     * 唯一标识符code
     */
    private String code;

    /**
     * 党组织唯一组织code
     */
    private String orgCode;

    /**
     * 党组织组织层级码
     */
    private String economicOrgCode;

    /**
     * 产业名称
     */
    private String industryName;

    /**
     * 收入渠道
     */
    private String incomeChannel;

    /**
     * 收入产业
     */
    private String incomeIndustry;

    private String unitCode;

    private String unitName;
    /**
     * 发展经济类型code
     */
    private String d128Code;

    /**
     * 发展经济类型name
     */
    private String d128Name;

    /**
     * 发展经济类型金额（万元）
     */
    private BigDecimal developMoney;

    /**
     * 采取组织形式code
     */
    private String d129Code;

    /**
     * 采取组织形式name
     */
    private String d129Name;

    /**
     * 组织形式金额（万元）
     */
    private BigDecimal organAmount;

    /**
     * 受益农村人口数
     */
    private Integer benefitPopulation;
    private Date createTime;

    /**
     * 村党组织的书记是否担任村级集体经济组织负责人的村（1是 0否）
     */
    private Integer hasEconomicVillage;

    public Integer getHasEconomicVillage() {
        return hasEconomicVillage;
    }

    public void setHasEconomicVillage(Integer hasEconomicVillage) {
        this.hasEconomicVillage = hasEconomicVillage;
    }

    public UnitCollectiveEconomicDTO() {
    }

    public String getUnitCode() {
        return unitCode;
    }

    public void setUnitCode(String unitCode) {
        this.unitCode = unitCode;
    }

    public String getUnitName() {
        return unitName;
    }

    public void setUnitName(String unitName) {
        this.unitName = unitName;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getOrgCode() {
        return orgCode;
    }

    public void setOrgCode(String orgCode) {
        this.orgCode = orgCode;
    }

    public String getEconomicOrgCode() {
        return economicOrgCode;
    }

    public void setEconomicOrgCode(String economicOrgCode) {
        this.economicOrgCode = economicOrgCode;
    }

    public String getIndustryName() {
        return industryName;
    }

    public void setIndustryName(String industryName) {
        this.industryName = industryName;
    }

    public String getIncomeChannel() {
        return incomeChannel;
    }

    public void setIncomeChannel(String incomeChannel) {
        this.incomeChannel = incomeChannel;
    }

    public String getIncomeIndustry() {
        return incomeIndustry;
    }

    public void setIncomeIndustry(String incomeIndustry) {
        this.incomeIndustry = incomeIndustry;
    }

    public String getD128Code() {
        return d128Code;
    }

    public void setD128Code(String d128Code) {
        this.d128Code = d128Code;
    }

    public String getD128Name() {
        return d128Name;
    }

    public void setD128Name(String d128Name) {
        this.d128Name = d128Name;
    }

    public BigDecimal getDevelopMoney() {
        return developMoney;
    }

    public void setDevelopMoney(BigDecimal developMoney) {
        this.developMoney = developMoney;
    }

    public String getD129Code() {
        return d129Code;
    }

    public void setD129Code(String d129Code) {
        this.d129Code = d129Code;
    }

    public String getD129Name() {
        return d129Name;
    }

    public void setD129Name(String d129Name) {
        this.d129Name = d129Name;
    }

    public BigDecimal getOrganAmount() {
        return organAmount;
    }

    public void setOrganAmount(BigDecimal organAmount) {
        this.organAmount = organAmount;
    }

    public Integer getBenefitPopulation() {
        return benefitPopulation;
    }

    public void setBenefitPopulation(Integer benefitPopulation) {
        this.benefitPopulation = benefitPopulation;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }
}
