package com.zenith.front.model.vo;

import cn.hutool.core.date.DateUtil;

import java.io.Serializable;
import java.util.Date;
import java.util.Objects;

/**
 * <AUTHOR>
 * @version V1.0
 * @Package com.zenith.front.vo
 * @date 2021/8/17 0017 19:19
 */
public class OrgAppraisalVO implements Serializable {

    private static final long serialVersionUID = 2852348756296492411L;
    /**
     * 唯一标识符code
     */
    private String code;

    /**
     * 组织层级码
     */
    private String orgCode;

    /**
     * 评议年度(年份选择框可以选择当前时间倒退5年)
     */
    private Date year;

    /**
     * 应评议人数（数值填写框）
     */
    private Integer peopleToBeReviewed;

    /**
     * 已评议人数
     */
    private Integer peopleReviewed;

    /**
     * 评议情况（1开始评议，2结束评议）
     */
    private Integer situation;

    /**
     * 地点（文本框填写，不超过100字）非必填
     */
    private String place;

    /**
     * 主题（文本框填写，不超过100字）非必填
     */
    private String theme;

    /**
     * 主要内容（文本框填写，不超过300字）非必填
     */
    private String content;

    private Date startTime;

    private Date endTime;

    public Date getStartTime() {
        return startTime;
    }

    public void setStartTime(Date startTime) {
        this.startTime = startTime;
    }

    public Date getEndTime() {
        return endTime;
    }

    public void setEndTime(Date endTime) {
        this.endTime = endTime;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getOrgCode() {
        return orgCode;
    }

    public void setOrgCode(String orgCode) {
        this.orgCode = orgCode;
    }

    public Date getYear() {
        if (Objects.nonNull(year)) {
            return DateUtil.beginOfYear(year);
        }
        return null;
    }

    public void setYear(Date year) {
        this.year = year;
    }

    public Integer getPeopleToBeReviewed() {
        return peopleToBeReviewed;
    }

    public void setPeopleToBeReviewed(Integer peopleToBeReviewed) {
        this.peopleToBeReviewed = peopleToBeReviewed;
    }

    public Integer getPeopleReviewed() {
        return peopleReviewed;
    }

    public void setPeopleReviewed(Integer peopleReviewed) {
        this.peopleReviewed = peopleReviewed;
    }

    public Integer getSituation() {
        return situation;
    }

    public void setSituation(Integer situation) {
        this.situation = situation;
    }

    public String getPlace() {
        return place;
    }

    public void setPlace(String place) {
        this.place = place;
    }

    public String getTheme() {
        return theme;
    }

    public void setTheme(String theme) {
        this.theme = theme;
    }

    public String getContent() {
        return content;
    }

    public void setContent(String content) {
        this.content = content;
    }

    @Override
    public String toString() {
        return "OrgAppraisalVO{" +
                "code='" + code + '\'' +
                ", orgCode='" + orgCode + '\'' +
                ", year=" + year +
                ", peopleToBeReviewed=" + peopleToBeReviewed +
                ", peopleReviewed=" + peopleReviewed +
                ", situation=" + situation +
                ", place='" + place + '\'' +
                ", theme='" + theme + '\'' +
                ", content='" + content + '\'' +
                '}';
    }
}
