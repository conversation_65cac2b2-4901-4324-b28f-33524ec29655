package com.zenith.front.model.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @Date 2021/8/13 18:31
 * @Version 1.0
 */
@Data
@ToString
@NoArgsConstructor
@AllArgsConstructor
public class Zt5CommunityPartyDTO {

    /**
     * 主键
     */
    private String code;

    /**
     * 单位code
     */
    private String unitCode;

    /**
     * 专职党务工作者
     */
    private Long partyWorker;

    /**
     * 大专及以上学历
     */
    private Long collegeOrAbove;

    /**
     * 从机关和街道选派的
     */
    private Long governmentStreetsSelect;

    /**
     * 从退役军人中选聘的
     */
    private Long selectFromVeterans;

    /**
     * 录用为公务员的
     */
    private Long civilServiceRecruitment;

    /**
     * 选拔进入事业编制的
     */
    private Long selectIntoEstablishment;

    /**
     * 推荐为两代表一委员的
     */
    private Long representativeMember;

    /**
     * 按不低于上年度当地社会平均工资水平确定报酬的社区
     */
    private Long lessThanPastYear;

    /**
     * 全部社区工作者年工资总额（万元）
     */
    private BigDecimal communityWorkWage;

    /**
     * 全部社区党组织书记年工资总额（万元）
     */
    private BigDecimal communitySecretaryWage;

    /**
     * 党建工作指导员
     */
    private Long jobInstructor;

    /**
     * 建立社区工作者岗位等级序列的社区
     */
    private Long jobLevelWorkersCommunity;

    /**
     * 社区党组织书记实行县级党委备案管理的县（市、区、旗）
     */
    private Long practiceFilingManagement;

    /**
     * 纳入财政预算的社区
     */
    private Long intoBudget;

    /**
     * 社区纳入财政预算的工作经费总额（万元）
     */
    private BigDecimal intoBudgetMoney;

    /**
     * 落实服务群众专项经费的社区
     */
    private Long serviceFunds;

    /**
     * 社区全年服务群众专项经费总额（万元）
     */
    private BigDecimal annualServiceFunds;

    /**
     * 实行兼职委员制的社区
     */
    private Integer hasCommitteeSystem;

    /**
     * 开展在职党员到社区报到为群众服务的社区
     */
    private Long membersReportAnnualService;

    /**
     * 到社区报到的在职党员
     */
    private Long membersReportCommunity;
}
