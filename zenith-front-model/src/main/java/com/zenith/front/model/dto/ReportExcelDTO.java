package com.zenith.front.model.dto;

import cn.hutool.core.date.DateUtil;
import com.zenith.front.model.validate.group.Common1Group;
import lombok.Data;
import lombok.ToString;

import javax.validation.constraints.NotBlank;
import java.util.Date;
import java.util.List;
import java.util.Objects;

/**
 * 报表excel
 *
 * <AUTHOR>
 */
@Data
@ToString
public class ReportExcelDTO {

    /**
     * 调度表类型
     */
    private String reportCode;

    /**
     * 调度表名称
     */
    private String name;

    /**
     * 层级码
     */
    @NotBlank(groups = Common1Group.class, message = "orgCode 不能为空")
    private String orgCode;

    /**
     * 第几列
     */
    private String c;

    /**
     * 反查类型
     */
    private String t;

    /**
     * 页码
     */
    private Integer pageNum;

    /**
     * 每页显示条数
     */
    private Integer pageSize;

    /**
     * 开始时间
     */
    private Date startDate;

    /**
     * 结束时间
     */
    private Date endDate;

    /**
     * 民主评议年度
     */
    private Integer year;

    public String getTypeStatus() {
        return typeStatus;
    }

    public void setTypeStatus(String typeStatus) {
        this.typeStatus = typeStatus;
    }

    /**
     * 调度表类型
     */
    private String typeStatus;

    /**
     * 兼容发展党员工作情况调度表,自定义时间查询
     */
    public Date getStartDate() {
        if (Objects.isNull(startDate)) {
            return DateUtil.beginOfYear(new Date());
        }
        return startDate;
    }

    public Date getEndDate() {
        if (Objects.isNull(endDate)) {
            return DateUtil.endOfDay(new Date());
        }
        return endDate;
    }
    public Date getStartDate1() {
        return startDate;
    }

    public Date getEndDate1() {
        return endDate;
    }

    /**
     * 驻村系统两委班子调度表参数
     */
    private List<String> d04CodeList;
}
