package com.zenith.front.model.bean;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.extension.activerecord.Model;

import java.util.Date;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableField;

import java.io.Serializable;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2022-03-11
 */
@TableName("ccp_org_report")
public class OrgReport extends Model<OrgReport> {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 组织报表唯一编号
     */
    @TableField("code")
    private String code;

    /**
     * 发布报表组织code
     */
    @TableField("report_org_code")
    private String reportOrgCode;

    /**
     * 报表code
     */
    @TableField("report_code")
    private String reportCode;

    /**
     * 发布到组织code
     */
    @TableField("org_code")
    private String orgCode;

    /**
     * 发布内容
     */
    @TableField("excel_content")
    private Object excelContent;

    /**
     * 已发布 1 未发布 0
     */
    @TableField("publish")
    private String publish;

    /**
     * 操作账号
     */
    @TableField("update_account")
    private String updateAccount;

    /**
     * 创建时间
     */
    @TableField("create_time")
    private Date createTime;

    /**
     * 修改时间
     */
    @TableField("update_time")
    private Date updateTime;

    /**
     * 删除时间
     */
    @TableField("delete_time")
    private Date deleteTime;


    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getReportOrgCode() {
        return reportOrgCode;
    }

    public void setReportOrgCode(String reportOrgCode) {
        this.reportOrgCode = reportOrgCode;
    }

    public String getReportCode() {
        return reportCode;
    }

    public void setReportCode(String reportCode) {
        this.reportCode = reportCode;
    }

    public String getOrgCode() {
        return orgCode;
    }

    public void setOrgCode(String orgCode) {
        this.orgCode = orgCode;
    }

    public Object getExcelContent() {
        return excelContent;
    }

    public void setExcelContent(Object excelContent) {
        this.excelContent = excelContent;
    }

    public String getPublish() {
        return publish;
    }

    public void setPublish(String publish) {
        this.publish = publish;
    }

    public String getUpdateAccount() {
        return updateAccount;
    }

    public void setUpdateAccount(String updateAccount) {
        this.updateAccount = updateAccount;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    public Date getDeleteTime() {
        return deleteTime;
    }

    public void setDeleteTime(Date deleteTime) {
        this.deleteTime = deleteTime;
    }

    @Override
    protected Serializable pkVal() {
        return this.id;
    }

    @Override
    public String toString() {
        return "OrgReport{" +
                "id=" + id +
                ", code='" + code + '\'' +
                ", reportOrgCode='" + reportOrgCode + '\'' +
                ", reportCode='" + reportCode + '\'' +
                ", orgCode='" + orgCode + '\'' +
                ", excelContent='" + excelContent + '\'' +
                ", publish='" + publish + '\'' +
                ", updateAccount='" + updateAccount + '\'' +
                ", createTime=" + createTime +
                ", updateTime=" + updateTime +
                ", deleteTime=" + deleteTime +
                '}';
    }
}
