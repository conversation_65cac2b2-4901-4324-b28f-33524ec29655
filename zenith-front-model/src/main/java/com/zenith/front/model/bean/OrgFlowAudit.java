package com.zenith.front.model.bean;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2021-11-14
 */
@EqualsAndHashCode(callSuper = true)
@Data
@TableName(value = "ccp_org_flow_audit", autoResultMap = true)
public class OrgFlowAudit extends Model<OrgFlowAudit> {

    private static final long serialVersionUID = 1L;

    /**
     * 唯一标识符
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 唯一标识符code
     */
    @TableField("code")
    private String code;

    /**
     * 流动组织层级码
     */
    @TableField("flow_org_code")
    private String flowOrgCode;

    /**
     * 流动组织Code
     */
    @TableField("flow_org_level_code")
    private String flowOrgLevelCode;

    /**
     * 流动组织名称
     */
    @TableField("name")
    private String name;

    /**
     * 是否需要中央交换区流转，1-是，0-否
     */
    @TableField("is_exchange")
    private Integer isExchange;

    /**
     * 审批状态，0-待审批，1-审批通过，2-审批未通过
     */
    @TableField("status")
    private String status;

    /**
     * 审批时间
     */
    @TableField("audit_time")
    private Date auditTime;

    /**
     * 审批人唯一标识
     */
    @TableField("audit_user_id")
    private String auditUserId;

    /**
     * 审批人名称
     */
    @TableField("audit_user_name")
    private String auditUserName;

    /**
     * 审批单位的组织code
     */
    @TableField("approve_code")
    private String approveCode;

    /**
     * 审批理由
     */
    @TableField("reason")
    private String reason;

    /**
     *
     */
    @TableField("create_time")
    private Date createTime;

    /**
     *
     */
    @TableField("update_time")
    private Date updateTime;

    /**
     *
     */
    @TableField("delete_time")
    private Date deleteTime;

    /**
     *
     */
    @TableField("remark")
    private String remark;

    /**
     *
     */
    @TableField("timestamp")
    private Date timestamp;

    @Override
    public Serializable pkVal() {
        return this.id;
    }
}
