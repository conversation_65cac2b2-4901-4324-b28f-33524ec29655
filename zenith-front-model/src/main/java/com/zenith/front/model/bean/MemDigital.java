package com.zenith.front.model.bean;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 * 党员（发展党员）数字档案表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-02-14
 */
@TableName("ccp_mem_digital")
public class MemDigital extends Model<MemDigital> {

    private static final long serialVersionUID = 1L;

    /**
     * 唯一码
     */
    @TableId(value = "code", type = IdType.ASSIGN_UUID)
    private String code;

    /**
     * 流程节点:RD_1(新增入党申请)；RD_2_1(一月内需要谈话)；RD_2_2(谈话时间少于10天)；RD_2_3(一月内未进行谈话)；RD_3(已谈话)；RD_4(满足积极份子)；	 JJ_1(待考察人员)；JJ_2(待第一次考察)；JJ_3(待第二次考察)；JJ_4(超半月未按时考察)；JJ_5(持续考察人员)； JJ_6（上级党委备案）；JJ_7(发展对象阶段)；	 FZ_1(支委会审查)；FZ_2(党总支审查)；FZ_3_1(一月内进行预审)；FZ_3_2（预审时间少于十天）；FZ_3_3(一月内未进行预审)；FZ_4(县级党委预审)；FZ_5_1(一月内进行讨论人员)；FZ_5_2(讨论时间少于五天)；FZ_5_3(一月内未进行讨论)；FZ_6_1(三个月内审批人员)；FZ_6_2(审批时间少于十天)；FZ_6_3(审批超时)；FZ_6_4(特殊原因延长审批)；FZ_7(接收预备党员)；
     */
    @TableField("process_node")
    private String processNode;

    /**
     * 档案批次唯一码
     */
    @TableField("digital_lot_no")
    private String digitalLotNo;

    /**
     * 文件名
     */
    @TableField("name")
    private String name;

    /**
     * 文件地址
     */
    @TableField("path")
    private String path;

    /**
     * 所属档案目录阶段编码:dict_222
     */
    @TableField("d222_code")
    private String d222Code;

    /**
     * 所属档案目录阶段编码:dict_222
     */
    @TableField("d222_name")
    private String d222Name;

    /**
     * 党员类型
     */
    @TableField("d08_code")
    private String d08Code;

    /**
     * 排序
     */
    @TableField("sort")
    private Integer sort;

    /**
     * 前端录入,操作人（上传人、删除人）
     */
    @TableField("opration_user")
    private String oprationUser;

    /**
     * 创建时间
     */
    @TableField("create_time")
    private Date createTime;

    /**
     * 创建人
     */
    @TableField("create_user")
    private String createUser;

    /**
     * 修改时间
     */
    @TableField("update_time")
    private Date updateTime;

    /**
     * 修改人
     */
    @TableField("update_user")
    private String updateUser;

    /**
     * 删除时间
     */
    @TableField("delete_time")
    private Date deleteTime;

    /**
     * 关系转出时间
     */
    @TableField("transfer_time")
    private Date transferTime;

    /**
     * 文件大小，kb单位
     */
    @TableField("file_size")
    private Long fileSize;

    /**
     * 操作人code
     */
    @TableField("opration_code")
    private String oprationCode;

    /**
     * 补充档案申请到期时间
     */
    @TableField("applicant_end_date")
    private Date applicantEndDate;

    /**
     * 是否历史档案
     */
    @TableField(exist = false)
    private Boolean history;

    public Boolean getHistory() {
        return history;
    }

    public void setHistory(Boolean history) {
        this.history = history;
    }

    public String getOprationCode() {
        return oprationCode;
    }

    public void setOprationCode(String oprationCode) {
        this.oprationCode = oprationCode;
    }

    public Date getApplicantEndDate() {
        return applicantEndDate;
    }

    public void setApplicantEndDate(Date applicantEndDate) {
        this.applicantEndDate = applicantEndDate;
    }

    public Long getFileSize() {
        return fileSize;
    }

    public void setFileSize(Long fileSize) {
        this.fileSize = fileSize;
    }

    public Date getTransferTime() {
        return transferTime;
    }

    public void setTransferTime(Date transferTime) {
        this.transferTime = transferTime;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getProcessNode() {
        return processNode;
    }

    public void setProcessNode(String processNode) {
        this.processNode = processNode;
    }

    public String getDigitalLotNo() {
        return digitalLotNo;
    }

    public void setDigitalLotNo(String digitalLotNo) {
        this.digitalLotNo = digitalLotNo;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getPath() {
        return path;
    }

    public void setPath(String path) {
        this.path = path;
    }

    public String getd222Code() {
        return d222Code;
    }

    public void setd222Code(String d222Code) {
        this.d222Code = d222Code;
    }

    public String getd222Name() {
        return d222Name;
    }

    public void setd222Name(String d222Name) {
        this.d222Name = d222Name;
    }

    public String getd08Code() {
        return d08Code;
    }

    public void setd08Code(String d08Code) {
        this.d08Code = d08Code;
    }

    public Integer getSort() {
        return sort;
    }

    public void setSort(Integer sort) {
        this.sort = sort;
    }

    public String getOprationUser() {
        return oprationUser;
    }

    public void setOprationUser(String oprationUser) {
        this.oprationUser = oprationUser;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public String getCreateUser() {
        return createUser;
    }

    public void setCreateUser(String createUser) {
        this.createUser = createUser;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    public String getUpdateUser() {
        return updateUser;
    }

    public void setUpdateUser(String updateUser) {
        this.updateUser = updateUser;
    }

    public Date getDeleteTime() {
        return deleteTime;
    }

    public void setDeleteTime(Date deleteTime) {
        this.deleteTime = deleteTime;
    }

    @Override
    protected Serializable pkVal() {
        return this.code;
    }

    @Override
    public String toString() {
        return "CcpMemDigital{" +
                "code=" + code +
                ", digitalLotNo=" + digitalLotNo +
                ", name=" + name +
                ", path=" + path +
                ", d222Code=" + d222Code +
                ", d222Name=" + d222Name +
                ", d08Code=" + d08Code +
                ", sort=" + sort +
                ", oprationUser=" + oprationUser +
                ", createTime=" + createTime +
                ", createUser=" + createUser +
                ", updateTime=" + updateTime +
                ", updateUser=" + updateUser +
                ", deleteTime=" + deleteTime +
                "}";
    }
}
