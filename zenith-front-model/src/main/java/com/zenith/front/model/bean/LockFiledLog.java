package com.zenith.front.model.bean;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.extension.activerecord.Model;

import java.util.Date;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableField;
import com.zenith.front.common.encrypt.typehandler.EncryptTypeHandler;

import java.io.Serializable;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2022-03-26
 */
@TableName(value = "ccp_lock_filed_log", autoResultMap = true)
public class LockFiledLog extends Model<LockFiledLog> {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 唯一编号
     */
    @TableField("code")
    private String code;

    /**
     * 申请记录1 解锁记录2 锁定记录3
     */
    @TableField("type")
    private String type;

    /**
     * 解锁对象 1 党员 2 单位 3 组织
     */
    @TableField("unlock_object")
    private String unlockObject;

    /**
     * 解锁对象code
     */
    @TableField("unlock_code")
    private String unlockCode;

    /**
     * 锁定党员/单位/组织名称
     */
    @TableField(value = "unlock_name", typeHandler = EncryptTypeHandler.class)
    private String unlockName;

    /**
     * 层级码
     */
    @TableField("level_code")
    private String levelCode;

    /**
     * 操作用户所在组织code
     */
    @TableField("mem_org_code")
    private String memOrgCode;

    /**
     * 解锁理由/操作原由
     */
    @TableField("unlock_reason")
    private String unlockReason;

    /**
     * 拒绝理由
     */
    @TableField("reason")
    private String reason;

    /**
     * 拒绝用户
     */
    @TableField("reason_person")
    private String reasonPerson;

    /**
     * 操作状态 1 已同意 0 已拒绝 默认null
     */
    @TableField("state")
    private String state;

    /**
     * 操作账号
     */
    @TableField("mem_account")
    private String memAccount;

    /**
     * 账号相关人员
     */
    @TableField("account_number_related_person")
    private String accountNumberRelatedPerson;

    /**
     * 修改时间
     */
    @TableField("update_time")
    private Date updateTime;

    /**
     * 申请时间/解锁时间
     */
    @TableField("create_time")
    private Date createTime;

    /**
     * 操作类型
     */
    @TableField(exist = false)
    private String typeName;


    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public String getUnlockObject() {
        return unlockObject;
    }

    public void setUnlockObject(String unlockObject) {
        this.unlockObject = unlockObject;
    }

    public String getUnlockCode() {
        return unlockCode;
    }

    public void setUnlockCode(String unlockCode) {
        this.unlockCode = unlockCode;
    }

    public String getUnlockName() {
        return unlockName;
    }

    public void setUnlockName(String unlockName) {
        this.unlockName = unlockName;
    }

    public String getLevelCode() {
        return levelCode;
    }

    public void setLevelCode(String levelCode) {
        this.levelCode = levelCode;
    }

    public String getMemOrgCode() {
        return memOrgCode;
    }

    public void setMemOrgCode(String memOrgCode) {
        this.memOrgCode = memOrgCode;
    }

    public String getUnlockReason() {
        return unlockReason;
    }

    public void setUnlockReason(String unlockReason) {
        this.unlockReason = unlockReason;
    }

    public String getReason() {
        return reason;
    }

    public void setReason(String reason) {
        this.reason = reason;
    }

    public String getReasonPerson() {
        return reasonPerson;
    }

    public void setReasonPerson(String reasonPerson) {
        this.reasonPerson = reasonPerson;
    }

    public String getState() {
        return state;
    }

    public void setState(String state) {
        this.state = state;
    }

    public String getMemAccount() {
        return memAccount;
    }

    public void setMemAccount(String memAccount) {
        this.memAccount = memAccount;
    }

    public String getAccountNumberRelatedPerson() {
        return accountNumberRelatedPerson;
    }

    public void setAccountNumberRelatedPerson(String accountNumberRelatedPerson) {
        this.accountNumberRelatedPerson = accountNumberRelatedPerson;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public String getTypeName() {
        return typeName;
    }

    public void setTypeName(String typeName) {
        this.typeName = typeName;
    }

    @Override
    protected Serializable pkVal() {
        return this.id;
    }

    @Override
    public String toString() {
        return "LockFiledLog{" +
                "id=" + id +
                ", code=" + code +
                ", type=" + type +
                ", typeName=" + typeName +
                ", unlockObject=" + unlockObject +
                ", unlockCode=" + unlockCode +
                ", unlockName=" + unlockName +
                ", levelCode=" + levelCode +
                ", memOrgCode=" + memOrgCode +
                ", unlockReason=" + unlockReason +
                ", reason=" + reason +
                ", reasonPerson=" + reasonPerson +
                ", state=" + state +
                ", memAccount=" + memAccount +
                ", accountNumberRelatedPerson=" + accountNumberRelatedPerson +
                ", updateTime=" + updateTime +
                ", createTime=" + createTime +
                "}";
    }
}
