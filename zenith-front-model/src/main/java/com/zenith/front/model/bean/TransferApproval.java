package com.zenith.front.model.bean;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2021-07-11
 */
@TableName("ccp_transfer_approval")
public class TransferApproval extends Model<TransferApproval> {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.ASSIGN_UUID)
    private String id;

    /**
     * 转接记录id
     */
    @TableField("record_id")
    private String recordId;

    /**
     * 审核用户id
     */
    @TableField("user_id")
    private String userId;

    /**
     * 当前组织id
     */
    @TableField("org_id")
    private String orgId;

    /**
     * 下一个审核组织id
     */
    @TableField("next_org_id")
    private String nextOrgId;

    /**
     * 是否代审,0 非代审
     */
    @TableField("is_instead")
    private Integer isInstead;

    /**
     * 审批状态,0 待审核 1 审核通过 2退回
     */
    @TableField("status")
    private Integer status;

    /**
     * 父id
     */
    @TableField("parent_id")
    private String parentId;

    /**
     * 创建时间
     */
    @TableField("create_time")
    private Date createTime;

    /**
     * 更新时间
     */
    @TableField("update_time")
    private Date updateTime;

    /**
     * 0 源组织审批记录 1目标组织审批记录
     */
    @TableField("direction")
    private Integer direction;

    /**
     * 经办人
     */
    @TableField("handler_man")
    private String handlerMan;


    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getRecordId() {
        return recordId;
    }

    public void setRecordId(String recordId) {
        this.recordId = recordId;
    }

    public String getUserId() {
        return userId;
    }

    public void setUserId(String userId) {
        this.userId = userId;
    }

    public String getOrgId() {
        return orgId;
    }

    public void setOrgId(String orgId) {
        this.orgId = orgId;
    }

    public String getNextOrgId() {
        return nextOrgId;
    }

    public void setNextOrgId(String nextOrgId) {
        this.nextOrgId = nextOrgId;
    }

    public Integer getIsInstead() {
        return isInstead;
    }

    public void setIsInstead(Integer isInstead) {
        this.isInstead = isInstead;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public String getParentId() {
        return parentId;
    }

    public void setParentId(String parentId) {
        this.parentId = parentId;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    public Integer getDirection() {
        return direction;
    }

    public void setDirection(Integer direction) {
        this.direction = direction;
    }

    public String getHandlerMan() {
        return handlerMan;
    }

    public void setHandlerMan(String handlerMan) {
        this.handlerMan = handlerMan;
    }

    @Override
    public Serializable pkVal() {
        return this.id;
    }

    @Override
    public String toString() {
        return "TransferApproval{" +
                "id='" + id + '\'' +
                ", recordId='" + recordId + '\'' +
                ", userId='" + userId + '\'' +
                ", orgId='" + orgId + '\'' +
                ", nextOrgId='" + nextOrgId + '\'' +
                ", isInstead=" + isInstead +
                ", status=" + status +
                ", parentId='" + parentId + '\'' +
                ", createTime=" + createTime +
                ", updateTime=" + updateTime +
                ", direction=" + direction +
                ", handlerMan='" + handlerMan + '\'' +
                '}';
    }
}
