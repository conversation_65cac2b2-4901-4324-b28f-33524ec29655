package com.zenith.front.model.bean;

import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.extension.activerecord.Model;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 * 党支部标准化规范化建设达标尿范点情况
 * </p>
 *
 * <AUTHOR>
 * @since 2022-12-14
 */
@TableName("ccp_org_example_site")
public class OrgExampleSite extends Model<OrgExampleSite> {

    private static final long serialVersionUID = 1L;

    @TableId(value = "code", type = IdType.ASSIGN_UUID)
    private String code;

    /**
     * 组织code
     */
    @TableField("org_code")
    private String orgCode;

    /**
     * 组织层级码
     */
    @TableField("org_level_code")
    private String orgLevelCode;

    /**
     * 示范点情况（非必填）
     */
    @TableField(value = "d157_code", updateStrategy = FieldStrategy.IGNORED)
    private String d157Code;

    @TableField(value = "d157_name", updateStrategy = FieldStrategy.IGNORED)
    private String d157Name;

    /**
     * 认定为示范点时间（示范点有选项后必填）
     */
    @TableField(value = "example_site_date", updateStrategy = FieldStrategy.IGNORED)
    private Date exampleSiteDate;

    /**
     * 认定单位（示范点有选项后必填）
     */
    @TableField(value = "identify_unit", updateStrategy = FieldStrategy.IGNORED)
    private String identifyUnit;

    /**
     * 是否达标
     */
    @TableField("has_standard_up")
    private String hasStandardUp;

    /**
     * 验收时间（是否达标勾选后显示）
     */
    @TableField(value = "accept_time", updateStrategy = FieldStrategy.IGNORED)
    private Date acceptTime;

    /**
     * 验收单位（是否达标勾选后显示）
     */
    @TableField(value = "accept_unit", updateStrategy = FieldStrategy.IGNORED)
    private String acceptUnit;

    @TableField("create_time")
    private Date createTime;

    @TableField("update_time")
    private Date updateTime;

    @TableField("delete_time")
    private Date deleteTime;


    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getOrgCode() {
        return orgCode;
    }

    public void setOrgCode(String orgCode) {
        this.orgCode = orgCode;
    }

    public String getOrgLevelCode() {
        return orgLevelCode;
    }

    public void setOrgLevelCode(String orgLevelCode) {
        this.orgLevelCode = orgLevelCode;
    }

    public String getd157Code() {
        return d157Code;
    }

    public void setd157Code(String d157Code) {
        this.d157Code = d157Code;
    }

    public String getd157Name() {
        return d157Name;
    }

    public void setd157Name(String d157Name) {
        this.d157Name = d157Name;
    }

    public Date getExampleSiteDate() {
        return exampleSiteDate;
    }

    public void setExampleSiteDate(Date exampleSiteDate) {
        this.exampleSiteDate = exampleSiteDate;
    }

    public String getIdentifyUnit() {
        return identifyUnit;
    }

    public void setIdentifyUnit(String identifyUnit) {
        this.identifyUnit = identifyUnit;
    }

    public String getHasStandardUp() {
        return hasStandardUp;
    }

    public void setHasStandardUp(String hasStandardUp) {
        this.hasStandardUp = hasStandardUp;
    }

    public Date getAcceptTime() {
        return acceptTime;
    }

    public void setAcceptTime(Date acceptTime) {
        this.acceptTime = acceptTime;
    }

    public String getAcceptUnit() {
        return acceptUnit;
    }

    public void setAcceptUnit(String acceptUnit) {
        this.acceptUnit = acceptUnit;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    public Date getDeleteTime() {
        return deleteTime;
    }

    public void setDeleteTime(Date deleteTime) {
        this.deleteTime = deleteTime;
    }

    @Override
    protected Serializable pkVal() {
        return this.code;
    }

    @Override
    public String toString() {
        return "OrgExampleSite{" +
                "code=" + code +
                ", orgCode=" + orgCode +
                ", orgLevelCode=" + orgLevelCode +
                ", d157Code=" + d157Code +
                ", d157Name=" + d157Name +
                ", exampleSiteDate=" + exampleSiteDate +
                ", identifyUnit=" + identifyUnit +
                ", hasStandardUp=" + hasStandardUp +
                ", acceptTime=" + acceptTime +
                ", acceptUnit=" + acceptUnit +
                ", createTime=" + createTime +
                ", updateTime=" + updateTime +
                ", deleteTime=" + deleteTime +
                "}";
    }
}
