package com.zenith.front.model.dto;

import com.zenith.front.model.bean.MemReward;
import com.zenith.front.model.validate.group.AddGroup;
import lombok.ToString;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

@ToString
public class MemRewardDTO {

    private Long id;
    private String code;
    private String esId;
    /**
     * 党员受奖/惩名称代码 对应d029代码表
     */
    @NotBlank(groups = AddGroup.class, message = "d029Code 不能为空")
    private String d029Code;
    /**
     * 党员受奖/惩名称对应d029代码表
     */
    @NotBlank(groups = AddGroup.class, message = "d029Name 不能为空")
    private String d029Name;
    /**
     * 党员code
     */
    @NotBlank(groups = AddGroup.class, message = "memCode 不能为空")
    private String memCode;
    /**
     * 党组织层级码
     */
    @NotBlank(groups = AddGroup.class, message = "rewardOrgCode 不能为空")
    private String rewardOrgCode;
    /**
     * 党组织识别码
     */
    @NotBlank(groups = AddGroup.class, message = "orgCode 不能为空")
    private String orgCode;
    /**
     * 奖/惩批准日期
     */
    @NotNull(groups = AddGroup.class, message = "startDate 不能为空")
    private java.util.Date startDate;
    /**
     * 奖/惩撤销日期
     */
    private java.util.Date endDate;
    /**
     * 受奖/惩原因说明
     */
    private String remark;
    /**
     * 决定对该党员进行奖惩的原始文件号
     */
    private String fileNumber;
    /**
     * 奖/惩原因代码
     */
    private String d030Code;
    /**
     * 奖/惩原因名称
     */
    private String d030Name;
    /**
     * 奖/惩批准机关名称
     */
    @NotBlank(groups = AddGroup.class, message = "orgName 不能为空")
    private String orgName;
    /**
     * 受奖批准机关级别代码名称
     */
    private String d52Name;
    /**
     * 批准机关级别代码（表dict_d52）
     */
    private String d52Code;
    /**
     * 离开时的行政职务级别代码（表dict_duty_level）
     */
    private String d51Code;
    private String d51Name;
    /**
     * 1是奖励0是惩戒
     */
    @NotNull(groups = AddGroup.class, message = "type 不能为空")
    private Integer type;
    private java.util.Date deleteTime;
    private java.util.Date createTime;
    private java.util.Date updateTime;
    /**
     * 录入奖惩情况组织
     */
    private String orgEntryCode;
    private String orgEntryName;
    private java.util.Date timestamp;
    /**
     * 表彰类型0--及时性,1--定期
     */
    private Integer commendation;
    private String zbCode;
    /**
     * 是否历史数据
     */
    private Integer isHistory;
    private String updateAccount;
    /**
     * 奖惩原因
     */
    private String d47Name;
    /**
     * 奖惩原因
     */
    private String d47Code;

    public MemRewardDTO setId(Long id) {
        this.id = id;
        return this;
    }

    public Long getId() {
        return this.id;
    }

    public MemRewardDTO setCode(String code) {
        this.code = code;
        return this;
    }

    public String getCode() {
        return this.code;
    }

    public MemRewardDTO setEsId(String esId) {
        this.esId = esId;
        return this;
    }

    public String getEsId() {
        return this.esId;
    }

    public MemRewardDTO setD029Code(String d029Code) {
        this.d029Code = d029Code;
        return this;
    }

    public String getD029Code() {
        return this.d029Code;
    }

    public MemRewardDTO setD029Name(String d029Name) {
        this.d029Name = d029Name;
        return this;
    }

    public String getD029Name() {
        return this.d029Name;
    }

    public MemRewardDTO setMemCode(String memCode) {
        this.memCode = memCode;
        return this;
    }

    public String getMemCode() {
        return this.memCode;
    }

    public MemRewardDTO setRewardOrgCode(String rewardOrgCode) {
        this.rewardOrgCode = rewardOrgCode;
        return this;
    }

    public String getRewardOrgCode() {
        return this.rewardOrgCode;
    }

    public MemRewardDTO setOrgCode(String orgCode) {
        this.orgCode = orgCode;
        return this;
    }

    public String getOrgCode() {
        return this.orgCode;
    }

    public MemRewardDTO setStartDate(java.util.Date startDate) {
        this.startDate = startDate;
        return this;
    }

    public java.util.Date getStartDate() {
        return this.startDate;
    }

    public MemRewardDTO setEndDate(java.util.Date endDate) {
        this.endDate = endDate;
        return this;
    }

    public java.util.Date getEndDate() {
        return this.endDate;
    }

    public MemRewardDTO setRemark(String remark) {
        this.remark = remark;
        return this;
    }

    public String getRemark() {
        return this.remark;
    }

    public MemRewardDTO setFileNumber(String fileNumber) {
        this.fileNumber = fileNumber;
        return this;
    }

    public String getFileNumber() {
        return this.fileNumber;
    }

    public MemRewardDTO setD030Code(String d030Code) {
        this.d030Code = d030Code;
        return this;
    }

    public String getD030Code() {
        return this.d030Code;
    }

    public MemRewardDTO setD030Name(String d030Name) {
        this.d030Name = d030Name;
        return this;
    }

    public String getD030Name() {
        return this.d030Name;
    }

    public MemRewardDTO setOrgName(String orgName) {
        this.orgName = orgName;
        return this;
    }

    public String getOrgName() {
        return this.orgName;
    }

    public MemRewardDTO setD52Name(String d52Name) {
        this.d52Name = d52Name;
        return this;
    }

    public String getD52Name() {
        return this.d52Name;
    }

    public MemRewardDTO setD52Code(String d52Code) {
        this.d52Code = d52Code;
        return this;
    }

    public String getD52Code() {
        return this.d52Code;
    }

    public MemRewardDTO setD51Code(String d51Code) {
        this.d51Code = d51Code;
        return this;
    }

    public String getD51Code() {
        return this.d51Code;
    }

    public MemRewardDTO setD51Name(String d51Name) {
        this.d51Name = d51Name;
        return this;
    }

    public String getD51Name() {
        return this.d51Name;
    }

    public MemRewardDTO setType(Integer type) {
        this.type = type;
        return this;
    }

    public Integer getType() {
        return this.type;
    }

    public MemRewardDTO setDeleteTime(java.util.Date deleteTime) {
        this.deleteTime = deleteTime;
        return this;
    }

    public java.util.Date getDeleteTime() {
        return this.deleteTime;
    }

    public MemRewardDTO setCreateTime(java.util.Date createTime) {
        this.createTime = createTime;
        return this;
    }

    public java.util.Date getCreateTime() {
        return this.createTime;
    }

    public MemRewardDTO setUpdateTime(java.util.Date updateTime) {
        this.updateTime = updateTime;
        return this;
    }

    public java.util.Date getUpdateTime() {
        return this.updateTime;
    }

    public MemRewardDTO setOrgEntryCode(String orgEntryCode) {
        this.orgEntryCode = orgEntryCode;
        return this;
    }

    public String getOrgEntryCode() {
        return this.orgEntryCode;
    }

    public MemRewardDTO setOrgEntryName(String orgEntryName) {
        this.orgEntryName = orgEntryName;
        return this;
    }

    public String getOrgEntryName() {
        return this.orgEntryName;
    }

    public MemRewardDTO setTimestamp(java.util.Date timestamp) {
        this.timestamp = timestamp;
        return this;
    }

    public java.util.Date getTimestamp() {
        return this.timestamp;
    }

    public MemRewardDTO setCommendation(Integer commendation) {
        this.commendation = commendation;
        return this;
    }

    public Integer getCommendation() {
        return this.commendation;
    }

    public MemRewardDTO setZbCode(String zbCode) {
        this.zbCode = zbCode;
        return this;
    }

    public String getZbCode() {
        return this.zbCode;
    }

    public MemRewardDTO setIsHistory(Integer isHistory) {
        this.isHistory = isHistory;
        return this;
    }

    public Integer getIsHistory() {
        return this.isHistory;
    }

    public MemRewardDTO setUpdateAccount(String updateAccount) {
        this.updateAccount = updateAccount;
        return this;
    }

    public String getUpdateAccount() {
        return this.updateAccount;
    }

    public MemRewardDTO setD47Name(String d47Name) {
        this.d47Name = d47Name;
        return this;
    }

    public String getD47Name() {
        return this.d47Name;
    }

    public MemRewardDTO setD47Code(String d47Code) {
        this.d47Code = d47Code;
        return this;
    }

    public String getD47Code() {
        return this.d47Code;
    }

    public MemReward toModel() {
        MemReward model = new MemReward();
        model.setId(this.id);
        model.setCode(this.code);
        model.setEsId(this.esId);
        model.setD029Code(this.d029Code);
        model.setD029Name(this.d029Name);
        model.setMemCode(this.memCode);
        model.setRewardOrgCode(this.rewardOrgCode);
        model.setOrgCode(this.orgCode);
        model.setStartDate(this.startDate);
        model.setEndDate(this.endDate);
        model.setRemark(this.remark);
        model.setFileNumber(this.fileNumber);
        model.setD030Code(this.d030Code);
        model.setD030Name(this.d030Name);
        model.setOrgName(this.orgName);
        model.setD52Name(this.d52Name);
        model.setD52Code(this.d52Code);
        model.setD51Code(this.d51Code);
        model.setD51Name(this.d51Name);
        model.setType(this.type);
        model.setDeleteTime(this.deleteTime);
        model.setCreateTime(this.createTime);
        model.setUpdateTime(this.updateTime);
        model.setOrgEntryCode(this.orgEntryCode);
        model.setOrgEntryName(this.orgEntryName);
        model.setTimestamp(this.timestamp);
        model.setCommendation(this.commendation);
        model.setZbCode(this.zbCode);
        model.setIsHistory(this.isHistory);
        model.setUpdateAccount(this.updateAccount);
        model.setD47Name(this.d47Name);
        model.setD47Code(this.d47Code);
        return model;
    }
}