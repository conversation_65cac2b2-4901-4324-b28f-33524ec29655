package com.zenith.front.model.bean;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.zenith.front.common.encrypt.typehandler.EncryptTypeHandler;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2021-09-29
 */
@TableName(value = "ccp_org_party_congress_committee", autoResultMap = true)
public class OrgPartyCongressCommittee extends Model<OrgPartyCongressCommittee> {

    private static final long serialVersionUID = 1L;

    /**
     * 唯一自增长主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 唯一标识符code
     */
    @TableField("code")
    private String code;

    /**
     * es唯一主键
     */
    @TableField("es_id")
    private String esId;

    /**
     * 人员code
     */
    @TableField("mem_code")
    private String memCode;

    /**
     * 人员姓名
     */
    @TableField(value = "mem_name", typeHandler = EncryptTypeHandler.class)
    private String memName;

    /**
     * 身份证
     */
    @TableField(value = "mem_idcard", typeHandler = EncryptTypeHandler.class)
    private String memIdcard;

    /**
     * 性别代码
     */
    @TableField("sex_code")
    private String sexCode;

    /**
     * 性别名称
     */
    @TableField("sex_name")
    private String sexName;

    /**
     * 出生日期
     */
    @TableField("birthday")
    private Date birthday;

    /**
     * 学历代码
     */
    @TableField("d07_code")
    private String d07Code;

    /**
     * 学历名称
     */
    @TableField("d07_name")
    private String d07Name;

    /**
     * 是否本组织党员代码
     */
    @TableField("mem_type_code")
    private String memTypeCode;

    /**
     * 是否本组织党员中文
     */
    @TableField("mem_type_name")
    private String memTypeName;

    /**
     * 任职开始时间
     */
    @TableField("start_date")
    private Date startDate;

    /**
     * 任职结束时间
     */
    @TableField(value = "end_date")
    private Date endDate;

    /**
     * 终止类型code
     */
    @TableField(value = "d105_code")
    private Integer d105Code;

    /**
     * 终止类型name
     */
    @TableField("d105_name")
    private String d105Name;

    @TableField("d106_code")
    private String d106Code;

    @TableField("d106_name")
    private String d106Name;

    /**
     * 届次code
     */
    @TableField("elect_code")
    private String electCode;

    /**
     * 组织zb_code
     */
    @TableField("zb_code")
    private String zbCode;

    /**
     * 任职时所在组织唯一标识code
     */
    @TableField("org_code")
    private String orgCode;

    @TableField(exist = false)
    private String orgName;

    /**
     * 任职时所在组织层级码
     */
    @TableField("position_org_code")
    private String positionOrgCode;

    /**
     * 任职时所在组织名称
     */
    @TableField("position_org_name")
    private String positionOrgName;

    /**
     * 是否历史数据
     */
    @TableField("is_history")
    private Integer isHistory;

    /**
     * 创建时间
     */
    @TableField("create_time")
    private Date createTime;

    /**
     * 更新时间
     */
    @TableField("update_time")
    private Date updateTime;

    /**
     * 删除时间
     */
    @TableField("delete_time")
    private Date deleteTime;

    /**
     * 时间戳
     */
    @TableField("timestamp")
    private Date timestamp;

    /**
     * 最近更更新账号
     */
    @TableField("update_account")
    private String updateAccount;
    /**
     * 职业代码
     */
    @TableField("d124_code")
    private String d124Code;
    /**
     * 职业名称
     */
    @TableField("d124_name")
    private String d124Name;

    @TableField(exist = false)
    private String party;

    public String getParty() {
        return party;
    }

    public void setParty(String party) {
        this.party = party;
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getEsId() {
        return esId;
    }

    public void setEsId(String esId) {
        this.esId = esId;
    }

    public String getMemCode() {
        return memCode;
    }

    public void setMemCode(String memCode) {
        this.memCode = memCode;
    }

    public String getMemName() {
        return memName;
    }

    public void setMemName(String memName) {
        this.memName = memName;
    }

    public String getMemIdcard() {
        return memIdcard;
    }

    public void setMemIdcard(String memIdcard) {
        this.memIdcard = memIdcard;
    }

    public String getSexCode() {
        return sexCode;
    }

    public void setSexCode(String sexCode) {
        this.sexCode = sexCode;
    }

    public String getSexName() {
        return sexName;
    }

    public void setSexName(String sexName) {
        this.sexName = sexName;
    }

    public Date getBirthday() {
        return birthday;
    }

    public void setBirthday(Date birthday) {
        this.birthday = birthday;
    }

    public String getd07Code() {
        return d07Code;
    }

    public void setd07Code(String d07Code) {
        this.d07Code = d07Code;
    }

    public String getd07Name() {
        return d07Name;
    }

    public void setd07Name(String d07Name) {
        this.d07Name = d07Name;
    }

    public String getMemTypeCode() {
        return memTypeCode;
    }

    public void setMemTypeCode(String memTypeCode) {
        this.memTypeCode = memTypeCode;
    }

    public String getMemTypeName() {
        return memTypeName;
    }

    public void setMemTypeName(String memTypeName) {
        this.memTypeName = memTypeName;
    }

    public Date getStartDate() {
        return startDate;
    }

    public void setStartDate(Date startDate) {
        this.startDate = startDate;
    }

    public Date getEndDate() {
        return endDate;
    }

    public void setEndDate(Date endDate) {
        this.endDate = endDate;
    }

    public Integer getd105Code() {
        return d105Code;
    }

    public void setd105Code(Integer d105Code) {
        this.d105Code = d105Code;
    }

    public String getd105Name() {
        return d105Name;
    }

    public void setd105Name(String d105Name) {
        this.d105Name = d105Name;
    }

    public String getd106Code() {
        return d106Code;
    }

    public void setd106Code(String d106Code) {
        this.d106Code = d106Code;
    }

    public String getd106Name() {
        return d106Name;
    }

    public void setd106Name(String d106Name) {
        this.d106Name = d106Name;
    }

    public String getElectCode() {
        return electCode;
    }

    public void setElectCode(String electCode) {
        this.electCode = electCode;
    }

    public String getZbCode() {
        return zbCode;
    }

    public void setZbCode(String zbCode) {
        this.zbCode = zbCode;
    }

    public String getOrgCode() {
        return orgCode;
    }

    public void setOrgCode(String orgCode) {
        this.orgCode = orgCode;
    }

    public String getPositionOrgCode() {
        return positionOrgCode;
    }

    public void setPositionOrgCode(String positionOrgCode) {
        this.positionOrgCode = positionOrgCode;
    }

    public String getPositionOrgName() {
        return positionOrgName;
    }

    public void setPositionOrgName(String positionOrgName) {
        this.positionOrgName = positionOrgName;
    }

    public Integer getIsHistory() {
        return isHistory;
    }

    public void setIsHistory(Integer isHistory) {
        this.isHistory = isHistory;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    public Date getDeleteTime() {
        return deleteTime;
    }

    public void setDeleteTime(Date deleteTime) {
        this.deleteTime = deleteTime;
    }

    public Date getTimestamp() {
        return timestamp;
    }

    public void setTimestamp(Date timestamp) {
        this.timestamp = timestamp;
    }

    public String getUpdateAccount() {
        return updateAccount;
    }

    public void setUpdateAccount(String updateAccount) {
        this.updateAccount = updateAccount;
    }

    public String getD124Code() {
        return d124Code;
    }

    public void setD124Code(String d124Code) {
        this.d124Code = d124Code;
    }

    public String getD124Name() {
        return d124Name;
    }

    public void setD124Name(String d124Name) {
        this.d124Name = d124Name;
    }

    public String getOrgName() {
        return orgName;
    }

    public void setOrgName(String orgName) {
        this.orgName = orgName;
    }

    @Override
    protected Serializable pkVal() {
        return this.id;
    }

    @Override
    public String toString() {
        return "OrgPartyCongressCommittee{" +
                "id=" + id +
                ", code=" + code +
                ", esId=" + esId +
                ", memCode=" + memCode +
                ", memName=" + memName +
                ", memIdcard=" + memIdcard +
                ", sexCode=" + sexCode +
                ", sexName=" + sexName +
                ", birthday=" + birthday +
                ", d07Code=" + d07Code +
                ", d07Name=" + d07Name +
                ", memTypeCode=" + memTypeCode +
                ", memTypeName=" + memTypeName +
                ", startDate=" + startDate +
                ", endDate=" + endDate +
                ", d105Code=" + d105Code +
                ", d105Name=" + d105Name +
                ", d106Code=" + d106Code +
                ", d106Name=" + d106Name +
                ", electCode=" + electCode +
                ", zbCode=" + zbCode +
                ", orgCode=" + orgCode +
                ", positionOrgCode=" + positionOrgCode +
                ", positionOrgName=" + positionOrgName +
                ", isHistory=" + isHistory +
                ", createTime=" + createTime +
                ", updateTime=" + updateTime +
                ", deleteTime=" + deleteTime +
                ", timestamp=" + timestamp +
                ", updateAccount=" + updateAccount +
                "}";
    }
}
