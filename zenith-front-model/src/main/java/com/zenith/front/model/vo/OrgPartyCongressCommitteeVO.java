package com.zenith.front.model.vo;

import com.zenith.front.common.encrypt.annotation.EncryptEnabled;
import com.zenith.front.common.encrypt.annotation.EncryptField;
import com.zenith.front.model.bean.OrgPartyCongressCommittee;
import lombok.Data;

import java.util.Date;

@Data
@EncryptEnabled
public class OrgPartyCongressCommitteeVO {
    private static final long serialVersionUID = 1L;

    /**
     * 唯一自增长主键
     */
    private Long id;

    /**
     * 唯一标识符code
     */
    private String code;

    /**
     * es唯一主键
     */
    private String esId;

    /**
     * 人员code
     */
    private String memCode;

    /**
     * 人员姓名
     */
    @EncryptField(order = 1)
    private String memName;

    /**
     * 身份证
     */
    @EncryptField(order = 2)
    private String memIdcard;

    /**
     * 性别代码
     */
    private String sexCode;

    /**
     * 性别名称
     */
    private String sexName;

    /**
     * 出生日期
     */
    private Date birthday;

    /**
     * 学历代码
     */
    private String d07Code;

    /**
     * 学历名称
     */
    private String d07Name;

    /**
     * 是否本组织党员代码
     */
    private String memTypeCode;

    /**
     * 是否本组织党员中文
     */
    private String memTypeName;

    /**
     * 任职开始时间
     */
    private Date startDate;

    /**
     * 任职结束时间
     */
    private Date endDate;

    /**
     * 终止类型code
     */
    private Integer d105Code;

    /**
     * 终止类型name
     */
    private String d105Name;

    private String d106Code;

    private String d106Name;

    /**
     * 届次code
     */
    private String electCode;

    /**
     * 组织zb_code
     */
    private String zbCode;

    /**
     * 任职时所在组织唯一标识code
     */
    private String orgCode;

    private String orgName;

    /**
     * 任职时所在组织层级码
     */
    private String positionOrgCode;

    /**
     * 任职时所在组织名称
     */
    private String positionOrgName;

    /**
     * 是否历史数据
     */
    private Integer isHistory;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 删除时间
     */
    private Date deleteTime;

    /**
     * 时间戳
     */
    private Date timestamp;

    /**
     * 最近更更新账号
     */
    private String updateAccount;
    /**
     * 职业代码
     */
    private String d124Code;
    /**
     * 职业名称
     */
    private String d124Name;

    private String party;
}
