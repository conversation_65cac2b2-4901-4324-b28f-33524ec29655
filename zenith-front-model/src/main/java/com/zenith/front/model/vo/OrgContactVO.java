package com.zenith.front.model.vo;

import com.zenith.front.model.dto.FileDto;
import lombok.Data;

import java.util.Date;

@Data
public class OrgContactVO {

    /**
     * 主键code
     */
    private String code;
    /**
     * 组织code
     */
    private String orgCode;
    /**
     * 组织层级码
     */
    private String orgLevelCode;

    /**
     * 支部联系人code
     */
    private String contactCode;

    /**
     * 联系人姓名
     */
    private String name;

    /**
     * 身份证号码
     */
    private String idcard;

    /**
     * 工作类型
     */
    private String d156Code;

    private String d156Name;

    /**
     * 工作开展时间
     */
    private Date startDate;

    /**
     * 工作记录
     */
    private String workRecord;

    /**
     * 文件路径
     */
    private FileDto filePath;

    /**
     * 备注说明
     */
    private String remark;

    /**
     * 示范点情况（非必填）
     */
    private String d157Code;

    private String d157Name;

    /**
     * 认定为示范点时间（示范点有选项后必填）
     */
    private Date exampleSiteDate;

    /**
     * 认定单位（示范点有选项后必填）
     */
    private String identifyUnit;

    /**
     * 是否达标(0：是 1：否)
     */
    private String hasStandardUp;

    /**
     * 验收时间（是否达标勾选后显示）
     */
    private Date acceptTime;

    /**
     * 验收单位（是否达标勾选后显示）
     */
    private String acceptUnit;

    /**
     * 联系人类型
     */
    private String d188Code;
    private String d188Name;

    /**
     * 联系人层级
     */
    private String d189Code;
    private String d189Name;

    /**
     * 起始时间
     */
    private Date startTime;
}
