package com.zenith.front.model.dto;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @since 2022/04/18
 */
public class UnitExpandCollectiveEconomyDTO extends BaseDTO {

    private static final long serialVersionUID = 1043171993719737172L;

    /**
     * 单位code
     */
    private String unitCode;

    /**
     * 获扶持资金年度
     */
    private Integer year;

    /**
     * 是否获中央和省级财政扶持资金（1是 0否）
     */
    private Integer hasFinancialSupport;

    /**
     * 中央和省级财政扶持资金
     */
    private BigDecimal financialSupportEnforced;

    /**
     * 中央和省级财政扶持资金执行率
     */
    private BigDecimal enforced;

    /**
     * 已完工验收项目数字
     */
    private Integer completedAcceptanceProjects;

    /**
     * 已获得收益
     */
    private BigDecimal incomeObtained;

    public String getUnitCode() {
        return unitCode;
    }

    public void setUnitCode(String unitCode) {
        this.unitCode = unitCode;
    }

    public Integer getYear() {
        return year;
    }

    public void setYear(Integer year) {
        this.year = year;
    }

    public Integer getHasFinancialSupport() {
        return hasFinancialSupport;
    }

    public void setHasFinancialSupport(Integer hasFinancialSupport) {
        this.hasFinancialSupport = hasFinancialSupport;
    }

    public BigDecimal getFinancialSupportEnforced() {
        return financialSupportEnforced;
    }

    public void setFinancialSupportEnforced(BigDecimal financialSupportEnforced) {
        this.financialSupportEnforced = financialSupportEnforced;
    }

    public BigDecimal getEnforced() {
        return enforced;
    }

    public void setEnforced(BigDecimal enforced) {
        this.enforced = enforced;
    }

    public Integer getCompletedAcceptanceProjects() {
        return completedAcceptanceProjects;
    }

    public void setCompletedAcceptanceProjects(Integer completedAcceptanceProjects) {
        this.completedAcceptanceProjects = completedAcceptanceProjects;
    }

    public BigDecimal getIncomeObtained() {
        return incomeObtained;
    }

    public void setIncomeObtained(BigDecimal incomeObtained) {
        this.incomeObtained = incomeObtained;
    }

    @Override
    public String toString() {
        return "UnitExpandCollectiveEconomyDTO{" +
                "unitCode='" + unitCode + '\'' +
                ", year='" + year + '\'' +
                ", hasFinancialSupport=" + hasFinancialSupport +
                ", financialSupportEnforced=" + financialSupportEnforced +
                ", enforced='" + enforced + '\'' +
                ", completedAcceptanceProjects=" + completedAcceptanceProjects +
                ", incomeObtained=" + incomeObtained +
                '}';
    }
}