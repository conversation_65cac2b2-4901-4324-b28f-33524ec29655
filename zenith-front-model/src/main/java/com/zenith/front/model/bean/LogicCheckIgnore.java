package com.zenith.front.model.bean;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.extension.activerecord.Model;

import java.util.Date;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableField;

import java.io.Serializable;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2022-03-24
 */
@TableName("ccp_logic_check_ignore")
public class LogicCheckIgnore extends Model<LogicCheckIgnore> {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.ASSIGN_UUID)
    private String id;

    /**
     * 被忽略的校核条件code
     */
    @TableField("logic_check_code")
    private String logicCheckCode;

    /**
     * 被忽略的校核条件
     */
    @TableField("logic_check_name")
    private String logicCheckName;

    /**
     * 被忽略的校核表名
     */
    @TableField("type")
    private String type;

    /**
     * 被忽略的校核类型code 党员主键 单位主键 组织主键
     */
    @TableField("code")
    private String code;

    /**
     * 所属组织层级码
     */
    @TableField("org_code")
    private String orgCode;

    /**
     * 特殊情况说明
     */
    @TableField("reason")
    private String reason;

    /**
     * 操作账户
     */
    @TableField("update_account")
    private String updateAccount;

    /**
     * 创建时间
     */
    @TableField("create_time")
    private Date createTime;

    /**
     * 删除账户
     */
    @TableField("delete_account")
    private String deleteAccount;

    /**
     * 删除时间
     */
    @TableField("delete_time")
    private Date deleteTime;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getLogicCheckCode() {
        return logicCheckCode;
    }

    public void setLogicCheckCode(String logicCheckCode) {
        this.logicCheckCode = logicCheckCode;
    }

    public String getLogicCheckName() {
        return logicCheckName;
    }

    public void setLogicCheckName(String logicCheckName) {
        this.logicCheckName = logicCheckName;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getOrgCode() {
        return orgCode;
    }

    public void setOrgCode(String orgCode) {
        this.orgCode = orgCode;
    }

    public String getReason() {
        return reason;
    }

    public void setReason(String reason) {
        this.reason = reason;
    }

    public String getUpdateAccount() {
        return updateAccount;
    }

    public void setUpdateAccount(String updateAccount) {
        this.updateAccount = updateAccount;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public String getDeleteAccount() {
        return deleteAccount;
    }

    public void setDeleteAccount(String deleteAccount) {
        this.deleteAccount = deleteAccount;
    }

    public Date getDeleteTime() {
        return deleteTime;
    }

    public void setDeleteTime(Date deleteTime) {
        this.deleteTime = deleteTime;
    }

    @Override
    protected Serializable pkVal() {
        return this.id;
    }

    @Override
    public String toString() {
        return "LogicCheckIgnore{" +
                "id=" + id +
                ", logicCheckCode=" + logicCheckCode +
                ", logicCheckName=" + logicCheckName +
                ", type=" + type +
                ", code=" + code +
                ", orgCode=" + orgCode +
                ", reason=" + reason +
                ", updateAccount=" + updateAccount +
                ", createTime=" + createTime +
                ", deleteAccount=" + deleteAccount +
                ", deleteTime=" + deleteTime +
                "}";
    }
}
