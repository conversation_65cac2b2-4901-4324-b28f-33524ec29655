package com.zenith.front.model.bean;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableField;
import java.io.Serializable;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2021-07-11
 */
@TableName("dict_chart")
public class DictChart extends Model<DictChart> {

    private static final long serialVersionUID=1L;

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    @TableField("key")
    private String key;

    @TableField("name")
    private String name;

    @TableField("pinyin")
    private String pinyin;

    @TableField("method_name")
    private String methodName;

    @TableField("parent")
    private String parent;

    @TableField("is_leaf")
    private Integer isLeaf;

    @TableField("sort")
    private Long sort;

    @TableField("type")
    private String type;

    @TableField("para_name")
    private String paraName;

//    @TableField("default")
//    private String default;

    /**
     * 是否启用
     */
    @TableField("is_enable")
    private String isEnable;

    @TableField("remark")
    private String remark;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getKey() {
        return key;
    }

    public void setKey(String key) {
        this.key = key;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getPinyin() {
        return pinyin;
    }

    public void setPinyin(String pinyin) {
        this.pinyin = pinyin;
    }

    public String getMethodName() {
        return methodName;
    }

    public void setMethodName(String methodName) {
        this.methodName = methodName;
    }

    public String getParent() {
        return parent;
    }

    public void setParent(String parent) {
        this.parent = parent;
    }

    public Integer getIsLeaf() {
        return isLeaf;
    }

    public void setIsLeaf(Integer isLeaf) {
        this.isLeaf = isLeaf;
    }

    public Long getSort() {
        return sort;
    }

    public void setSort(Long sort) {
        this.sort = sort;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public String getParaName() {
        return paraName;
    }

    public void setParaName(String paraName) {
        this.paraName = paraName;
    }

//    public String getDefault() {
//        return default;
//    }
//
//    public void setDefault(String default) {
//        this.default = default;
//    }

    public String getIsEnable() {
        return isEnable;
    }

    public void setIsEnable(String isEnable) {
        this.isEnable = isEnable;
    }

    @Override
    public Serializable pkVal() {
        return this.id;
    }

    @Override
    public String toString() {
        return "DictChart{" +
        "id=" + id +
        ", key=" + key +
        ", name=" + name +
        ", pinyin=" + pinyin +
        ", methodName=" + methodName +
        ", parent=" + parent +
        ", isLeaf=" + isLeaf +
        ", sort=" + sort +
        ", type=" + type +
        ", paraName=" + paraName +
//        ", default=" + default +
        ", isEnable=" + isEnable +
        "}";
    }
}
