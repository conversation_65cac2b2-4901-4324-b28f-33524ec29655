package com.zenith.front.model.vo;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2021/9/9 17:32
 * @Version 1.0
 */
public class OrgSpecialNatureVO {

    /**
     * 唯一标识符code
     */
    private String code;

    /**
     * 党组织唯一组织code
     */
    private String orgCode;

    /**
     * 党组织组织层级码
     */
    private String specialOrgCode;

    /**
     * 组织名称
     */
    private String specialOrgName;

    /**
     * 组织性质
     */
    private String specialOrgNature;

    /**
     * 组织性质名称
     */
    private String specialOrgNatureName;

    /**
     * 隶属关系
     */
    private String membershipFunction;

    /**
     * 隶属关系名称
     */
    private String membershipFunctionName;

    /**
     * 建立日期
     */
    private Date setUpDate;

    /**
     * 关联组织（多选）
     */
    private List<AssociatedOrganizationVO> associatedOrganization;

    /**
     * 关联组织（多选）
     */
    private String associatedOrganizationName;

    /**
     * 是否关联党组织
     */
    private Integer hasAssociatedOrg;

    /**
     * 关联集体经济
     */
    private List<AssociatedOrganizationVO> collectiveEconomy;

    /**
     * 组织类别
     * */
    private  String specialOrgType;

    public String getSpecialOrgType() {
        return specialOrgType;
    }

    public void setSpecialOrgType(String specialOrgType) {
        this.specialOrgType = specialOrgType;
    }

    public List<AssociatedOrganizationVO> getCollectiveEconomy() {
        return collectiveEconomy;
    }

    public void setCollectiveEconomy(List<AssociatedOrganizationVO> collectiveEconomy) {
        this.collectiveEconomy = collectiveEconomy;
    }

    public OrgSpecialNatureVO() {
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getOrgCode() {
        return orgCode;
    }

    public void setOrgCode(String orgCode) {
        this.orgCode = orgCode;
    }

    public String getSpecialOrgCode() {
        return specialOrgCode;
    }

    public void setSpecialOrgCode(String specialOrgCode) {
        this.specialOrgCode = specialOrgCode;
    }

    public String getSpecialOrgName() {
        return specialOrgName;
    }

    public void setSpecialOrgName(String specialOrgName) {
        this.specialOrgName = specialOrgName;
    }

    public String getSpecialOrgNature() {
        return specialOrgNature;
    }

    public void setSpecialOrgNature(String specialOrgNature) {
        this.specialOrgNature = specialOrgNature;
    }

    public String getSpecialOrgNatureName() {
        return specialOrgNatureName;
    }

    public void setSpecialOrgNatureName(String specialOrgNatureName) {
        this.specialOrgNatureName = specialOrgNatureName;
    }

    public String getMembershipFunction() {
        return membershipFunction;
    }

    public void setMembershipFunction(String membershipFunction) {
        this.membershipFunction = membershipFunction;
    }

    public String getMembershipFunctionName() {
        return membershipFunctionName;
    }

    public void setMembershipFunctionName(String membershipFunctionName) {
        this.membershipFunctionName = membershipFunctionName;
    }

    public Date getSetUpDate() {
        return setUpDate;
    }

    public void setSetUpDate(Date setUpDate) {
        this.setUpDate = setUpDate;
    }

    public List<AssociatedOrganizationVO> getAssociatedOrganization() {
        return associatedOrganization;
    }

    public void setAssociatedOrganization(List<AssociatedOrganizationVO> associatedOrganization) {
        this.associatedOrganization = associatedOrganization;
    }

    public String getAssociatedOrganizationName() {
        return associatedOrganizationName;
    }

    public void setAssociatedOrganizationName(String associatedOrganizationName) {
        this.associatedOrganizationName = associatedOrganizationName;
    }

    public Integer getHasAssociatedOrg() {
        return hasAssociatedOrg;
    }

    public void setHasAssociatedOrg(Integer hasAssociatedOrg) {
        this.hasAssociatedOrg = hasAssociatedOrg;
    }

    @Override
    public String toString() {
        return "OrgSpecialNatureVO{" +
                "code='" + code + '\'' +
                ", orgCode='" + orgCode + '\'' +
                ", specialOrgCode='" + specialOrgCode + '\'' +
                ", specialOrgName='" + specialOrgName + '\'' +
                ", specialOrgNature='" + specialOrgNature + '\'' +
                ", specialOrgNatureName='" + specialOrgNatureName + '\'' +
                ", membershipFunction='" + membershipFunction + '\'' +
                ", membershipFunctionName='" + membershipFunctionName + '\'' +
                ", setUpDate=" + setUpDate +
                ", associatedOrganization='" + associatedOrganization + '\'' +
                ", associatedOrganizationName='" + associatedOrganizationName + '\'' +
                ", hasAssociatedOrg=" + hasAssociatedOrg +
                '}';
    }
}
