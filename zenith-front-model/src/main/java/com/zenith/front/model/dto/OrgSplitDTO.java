package com.zenith.front.model.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

import javax.validation.constraints.NotBlank;
import java.util.List;

/**
 * <AUTHOR>
 */
@Data
@ToString
@NoArgsConstructor
@AllArgsConstructor
public class OrgSplitDTO {

    /**
     * 需要拆分的党组织
     */
    private String code;

    /**
     * 党组织名称
     */
    @NotBlank
    private String name;

    /**
     * 拆分党组织的党员
     */
    private List<Long> memIds;
}
