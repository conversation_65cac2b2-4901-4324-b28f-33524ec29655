package com.zenith.front.model.bean;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableField;

import java.io.Serializable;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2021-07-11
 */
@TableName("code_table_config")
public class CodeTableConfig extends Model<CodeTableConfig> {

    private static final long serialVersionUID = 1L;

    /**
     * 唯一标识符
     */
    @TableId(value = "id", type = IdType.ASSIGN_UUID)
    private String id;

    /**
     * 行唯一标识符
     */
    @TableField("col_id")
    private String colId;

    /**
     * 行字段名称
     */
    @TableField("col_name")
    private String colName;

    /**
     * 行字段值
     */
    @TableField("col_value")
    private String colValue;

    /**
     * 比较类型(大于，大于等于，等于，包含，不包含，小于，小于等于)
     */
    @TableField("compare_type")
    private String compareType;

    /**
     * 比较字段
     */
    @TableField("compare_col")
    private String compareCol;

    /**
     * 是否启用
     */
    @TableField("is_use")
    private Integer isUse;

    /**
     * 比较字段值
     */
    @TableField("compare_value")
    private String compareValue;

    /**
     * 前端反显数据(勿用)
     */
    @TableField("data")
    private String data;

    /**
     * 比较字段Id
     */
    @TableField("\"compareId\"")
    private String compareId;


    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getColId() {
        return colId;
    }

    public void setColId(String colId) {
        this.colId = colId;
    }

    public String getColName() {
        return colName;
    }

    public void setColName(String colName) {
        this.colName = colName;
    }

    public String getColValue() {
        return colValue;
    }

    public void setColValue(String colValue) {
        this.colValue = colValue;
    }

    public String getCompareType() {
        return compareType;
    }

    public void setCompareType(String compareType) {
        this.compareType = compareType;
    }

    public String getCompareCol() {
        return compareCol;
    }

    public void setCompareCol(String compareCol) {
        this.compareCol = compareCol;
    }

    public Integer getIsUse() {
        return isUse;
    }

    public void setIsUse(Integer isUse) {
        this.isUse = isUse;
    }

    public String getCompareValue() {
        return compareValue;
    }

    public void setCompareValue(String compareValue) {
        this.compareValue = compareValue;
    }

    public String getData() {
        return data;
    }

    public void setData(String data) {
        this.data = data;
    }

    public String getCompareId() {
        return compareId;
    }

    public void setCompareId(String compareId) {
        this.compareId = compareId;
    }

    @Override
    public Serializable pkVal() {
        return this.id;
    }

    @Override
    public String toString() {
        return "CodeTableConfig{" +
                "id=" + id +
                ", colId=" + colId +
                ", colName=" + colName +
                ", colValue=" + colValue +
                ", compareType=" + compareType +
                ", compareCol=" + compareCol +
                ", isUse=" + isUse +
                ", compareValue=" + compareValue +
                ", data=" + data +
                ", compareId=" + compareId +
                "}";
    }
}
