package com.zenith.front.model.bean;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.extension.activerecord.Model;

import java.util.Date;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableField;

import java.io.Serializable;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2021-07-11
 */
@TableName("ccp_fee_wxbill")
public class FeeWxbill extends Model<FeeWxbill> {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    @TableField("code")
    private String code;

    /**
     * ﻿交易时间
     */
    @TableField("trade_time")
    private Date tradeTime;

    /**
     * 公众账号ID
     */
    @TableField("appid")
    private String appid;

    /**
     * 商户号
     */
    @TableField("mchid")
    private String mchid;

    /**
     * 设备号
     */
    @TableField("device_info")
    private String deviceInfo;

    /**
     * 微信订单号
     */
    @TableField("transaction_id")
    private String transactionId;

    /**
     * 商户订单号
     */
    @TableField("out_trade_no")
    private String outTradeNo;

    /**
     * 特约商户号
     */
    @TableField("sub_mch_id")
    private String subMchId;

    /**
     * 用户标识
     */
    @TableField("openid")
    private String openid;

    /**
     * 交易类型
     */
    @TableField("trade_type")
    private String tradeType;

    /**
     * 交易状态
     */
    @TableField("trade_state")
    private String tradeState;

    /**
     * 付款银行
     */
    @TableField("bank_type")
    private String bankType;

    /**
     * 货币种类
     */
    @TableField("fee_type")
    private String feeType;

    /**
     * 应结订单金额
     */
    @TableField("total_fee")
    private String totalFee;

    /**
     * 代金券金额
     */
    @TableField("coupon_amount")
    private String couponAmount;

    /**
     * 微信退款单号
     */
    @TableField("refund_id")
    private String refundId;

    /**
     * 商户退款单号
     */
    @TableField("out_refund_no")
    private String outRefundNo;

    /**
     * 退款金额
     */
    @TableField("refund_fee")
    private String refundFee;

    /**
     * 充值券退款金额
     */
    @TableField("coupon_refund_amount")
    private String couponRefundAmount;

    /**
     * 退款类型
     */
    @TableField("refund_type")
    private String refundType;

    /**
     * 退款状态
     */
    @TableField("refund_status")
    private String refundStatus;

    /**
     * 商品名称
     */
    @TableField("product_name")
    private String productName;

    /**
     * 商户数据包
     */
    @TableField("data_package")
    private String dataPackage;

    /**
     * 手续费
     */
    @TableField("procedure_fee")
    private String procedureFee;

    /**
     * 费率
     */
    @TableField("rate")
    private String rate;

    /**
     * 订单金额
     */
    @TableField("order_amount")
    private String orderAmount;

    /**
     * 申请退款金额
     */
    @TableField("refund_settlement_amount")
    private String refundSettlementAmount;

    /**
     * 费率备注
     */
    @TableField("rate_note")
    private String rateNote;

    /**
     * 删除时间
     */
    @TableField("delete_time")
    private Date deleteTime;

    /**
     * 创建时间
     */
    @TableField("create_time")
    private Date createTime;


    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public Date getTradeTime() {
        return tradeTime;
    }

    public void setTradeTime(Date tradeTime) {
        this.tradeTime = tradeTime;
    }

    public String getAppid() {
        return appid;
    }

    public void setAppid(String appid) {
        this.appid = appid;
    }

    public String getMchid() {
        return mchid;
    }

    public void setMchid(String mchid) {
        this.mchid = mchid;
    }

    public String getDeviceInfo() {
        return deviceInfo;
    }

    public void setDeviceInfo(String deviceInfo) {
        this.deviceInfo = deviceInfo;
    }

    public String getTransactionId() {
        return transactionId;
    }

    public void setTransactionId(String transactionId) {
        this.transactionId = transactionId;
    }

    public String getOutTradeNo() {
        return outTradeNo;
    }

    public void setOutTradeNo(String outTradeNo) {
        this.outTradeNo = outTradeNo;
    }

    public String getSubMchId() {
        return subMchId;
    }

    public void setSubMchId(String subMchId) {
        this.subMchId = subMchId;
    }

    public String getOpenid() {
        return openid;
    }

    public void setOpenid(String openid) {
        this.openid = openid;
    }

    public String getTradeType() {
        return tradeType;
    }

    public void setTradeType(String tradeType) {
        this.tradeType = tradeType;
    }

    public String getTradeState() {
        return tradeState;
    }

    public void setTradeState(String tradeState) {
        this.tradeState = tradeState;
    }

    public String getBankType() {
        return bankType;
    }

    public void setBankType(String bankType) {
        this.bankType = bankType;
    }

    public String getFeeType() {
        return feeType;
    }

    public void setFeeType(String feeType) {
        this.feeType = feeType;
    }

    public String getTotalFee() {
        return totalFee;
    }

    public void setTotalFee(String totalFee) {
        this.totalFee = totalFee;
    }

    public String getCouponAmount() {
        return couponAmount;
    }

    public void setCouponAmount(String couponAmount) {
        this.couponAmount = couponAmount;
    }

    public String getRefundId() {
        return refundId;
    }

    public void setRefundId(String refundId) {
        this.refundId = refundId;
    }

    public String getOutRefundNo() {
        return outRefundNo;
    }

    public void setOutRefundNo(String outRefundNo) {
        this.outRefundNo = outRefundNo;
    }

    public String getRefundFee() {
        return refundFee;
    }

    public void setRefundFee(String refundFee) {
        this.refundFee = refundFee;
    }

    public String getCouponRefundAmount() {
        return couponRefundAmount;
    }

    public void setCouponRefundAmount(String couponRefundAmount) {
        this.couponRefundAmount = couponRefundAmount;
    }

    public String getRefundType() {
        return refundType;
    }

    public void setRefundType(String refundType) {
        this.refundType = refundType;
    }

    public String getRefundStatus() {
        return refundStatus;
    }

    public void setRefundStatus(String refundStatus) {
        this.refundStatus = refundStatus;
    }

    public String getProductName() {
        return productName;
    }

    public void setProductName(String productName) {
        this.productName = productName;
    }

    public String getDataPackage() {
        return dataPackage;
    }

    public void setDataPackage(String dataPackage) {
        this.dataPackage = dataPackage;
    }

    public String getProcedureFee() {
        return procedureFee;
    }

    public void setProcedureFee(String procedureFee) {
        this.procedureFee = procedureFee;
    }

    public String getRate() {
        return rate;
    }

    public void setRate(String rate) {
        this.rate = rate;
    }

    public String getOrderAmount() {
        return orderAmount;
    }

    public void setOrderAmount(String orderAmount) {
        this.orderAmount = orderAmount;
    }

    public String getRefundSettlementAmount() {
        return refundSettlementAmount;
    }

    public void setRefundSettlementAmount(String refundSettlementAmount) {
        this.refundSettlementAmount = refundSettlementAmount;
    }

    public String getRateNote() {
        return rateNote;
    }

    public void setRateNote(String rateNote) {
        this.rateNote = rateNote;
    }

    public Date getDeleteTime() {
        return deleteTime;
    }

    public void setDeleteTime(Date deleteTime) {
        this.deleteTime = deleteTime;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    @Override
    public Serializable pkVal() {
        return this.id;
    }

    @Override
    public String toString() {
        return "FeeWxbill{" +
                "id=" + id +
                ", code=" + code +
                ", tradeTime=" + tradeTime +
                ", appid=" + appid +
                ", mchid=" + mchid +
                ", deviceInfo=" + deviceInfo +
                ", transactionId=" + transactionId +
                ", outTradeNo=" + outTradeNo +
                ", subMchId=" + subMchId +
                ", openid=" + openid +
                ", tradeType=" + tradeType +
                ", tradeState=" + tradeState +
                ", bankType=" + bankType +
                ", feeType=" + feeType +
                ", totalFee=" + totalFee +
                ", couponAmount=" + couponAmount +
                ", refundId=" + refundId +
                ", outRefundNo=" + outRefundNo +
                ", refundFee=" + refundFee +
                ", couponRefundAmount=" + couponRefundAmount +
                ", refundType=" + refundType +
                ", refundStatus=" + refundStatus +
                ", productName=" + productName +
                ", dataPackage=" + dataPackage +
                ", procedureFee=" + procedureFee +
                ", rate=" + rate +
                ", orderAmount =" + orderAmount +
                ", refundSettlementAmount=" + refundSettlementAmount +
                ", rateNote=" + rateNote +
                ", deleteTime=" + deleteTime +
                ", createTime=" + createTime +
                "}";
    }
}
