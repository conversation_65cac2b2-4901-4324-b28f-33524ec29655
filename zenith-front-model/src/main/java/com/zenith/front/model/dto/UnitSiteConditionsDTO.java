package com.zenith.front.model.dto;

import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @Date 2021/10/7 16:06
 * @Version 1.0
 */
@NoArgsConstructor
@Data
@ToString
public class UnitSiteConditionsDTO {
    /**
     * 唯一key
     */
    private String code;

    /**
     * es_id
     */
    private String esId;

    /**
     * 时间
     */
    private Integer time;

    /**
     * 财政专项列支非公企业党建工作经费（万元）
     */
    private BigDecimal fiscalFunds;

    /**
     * 党费拨补非公企业党建工作经费 （万元）
     */
    private BigDecimal partyExpenses;

    /**
     * 非公企业集聚区综合性党群活动服务中心
     */
    private Integer activityServiceCenter;

    /**
     * 新建立非公企业集聚区综合性党群活动服务中心
     */
    private Integer newActivityServiceCenter;

    /**
     * 关联单位_代码
     */
    private String unitCode;

    /**
     * 关联组织_代码
     */
    private String orgCode;

    /**
     * 关联组织_名称
     */
    private String orgName;
}
