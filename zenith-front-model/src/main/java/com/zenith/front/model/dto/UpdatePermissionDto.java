package com.zenith.front.model.dto;

import com.zenith.front.model.bean.Permission;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

import java.io.Serializable;
import java.util.List;

/**
 * 修改权限实体类
 *
 * <AUTHOR>
 * @date 2019/03/18
 */
@Data
@ToString
@NoArgsConstructor
public class UpdatePermissionDto implements IDto, Serializable {

    private static final long serialVersionUID = -5755961310528457356L;
    /**
     * 用户id
     */
    private String userId;
    /**
     * 角色id
     */
    private String roleId;
    /**
     * 待修改权限集合
     */
    private List<Permission> permissionList;

}
