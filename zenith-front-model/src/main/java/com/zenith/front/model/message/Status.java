package com.zenith.front.model.message;

/***
 * 定义全局状态码:
 * 0  代表操作成功代码
 * 10 开始代表通用的错误代码
 * 11 开始代表用户相关错误代码
 * 12 开始代表角色相关错误代码
 * 13 开始代表权限相关错误代码
 * 17 开始代表届次相关错误代码
 * 16 组织相关代码
 * 18 党员
 * 19 单位
 * 20 组织关系转接
 * 21 党代表
 * 22 党费
 * 23 微信
 * 24 活动
 * 25 通知相关代码
 * 26 工作动态相关代码
 * 27 民主评议相关代码
 * 28 任务相关状态代码
 * <AUTHOR>
 * */
public enum Status {

    /**
     * 操作成功
     */
    SUCCESS(0, "操作成功"),
    /**
     * 操作失败
     */
    FAIL(1000, "操作失败"),
    /**
     * 参数错误
     */
    PARA_ERROR(1001, "参数错误"),

    /**
     * 请检查参数是否正确
     */
    NOT_NULL_ERROR(1002, "请检查参数是否正确"),
    /***
     * 参数错误,用于AOP验证参数
     * */
    PARA_VALIDATE_ERROR(1003, "参数错误:%s"),
    /**
     * 参数缺失
     */
    PARA_VALIDATE_MISS(10031, "参数缺失:%s"),
    /***
     * 参数错误data不能为空
     * */
    IN_MESSAGE_DATA_ERROR(1004, "data不能为空"),
    /**
     * 对象不存在
     */
    OBJEC_NOT_EXIST(1005, "对象不存在"),
    /**
     * 数据异常
     */
    DATA_EXCEPTION(1006, "数据异常"),

    NOT_DATA(1007, "暂无数据"),

    HAS_STAFF_ORGANIZATION(1008, "%s 为留党查看人员"),

    /**
     * 用户相关
     */
    NOT_LOGIN(1100, "用户未登录"),

    USERNAME_ISEMPTY(1101, "用户名不能为空且不能包含空格"),

    PASSWORD_ISEMPTY(1102, "密码不能为空"),

    LOGIN_FAILED(1103, "登录失败,账号不存在或密码错误"),

    CODE_ERROR(1104, "验证码错误"),

    USER_NAME_OR_PASSWORD_ERROR(1105, "用户名或密码错误"),

    USER_IS_LOCK(1106, "该用户已经被冻结"),

    USER_ISRESETPWD(1107, "重置密码或者修改密码的标示为空"),

    USER_OLDPASSWORD_ISEMPTY(1108, "用户的原始密码不能为空"),

    USER_OLDPASSWORD_ISNOTTRUE(1109, "原密码不正确"),

    USER_NOT_EXIST(1110, "用户不存在或密码错误"),

    USER_CURORGCODE_ISNOTEXSIT(1111, "用户所在单位为空"),

    USER_SYSLEVEL_ISEMPTY(1112, "用户管理层级为空"),

    PASSWORD_PASSWORD_NOT_EQ(1113, "重置密码与确认密码不匹配"),

    USER_CHOOSE_ROLE_ISEMPTY(1114, "用户所选角色为空"),

    USER_UPDATEPERMISSION_NOTENOUGH(1115, "用户权限长度与修改的权限长度不对等,请联系管理员"),

    USER_ORGCODE_ISEMPTY(1116, "用户所在的层级码为空"),

    CURRENT_USER_ROLEID(1117, "用户所选的当前的角色ID为空"),

    ORGCODE_NULL(1118, "组织层级码为空"),

    USER_HAVE_LIVE(1119, "用户已存在"),

    USERNAME_LIEN(1120, "用户名已存在"),

    USERNAME_CAN_USER(1121, "用户名可以使用"),

    ACCOUNT_ZIMU_DATA(1122, "账号只能为数字或字母"),

    PASSWORD_ERROR_LENGTH(1123, "密码的长度错误"),

    PASSWORD_REASONABLE(1124, "密码长度合理"),

    PASSWORD_NOT_NULL(1125, "密码不能为空"),

    USER_SYSLEVELNAME_ISEMPTY(1125, "用户管理层级为空"),

    USER_ORGID_ISEMPTY(1126, "用户自身所在单位id为空"),

    USER_MANAGERORG_ISEMPTY(1127, "用户修改管理单位的集合为空"),

    USER_ACCOUNT_LENGTH(1128, "账号长度不能小于3"),

    NO_ACCESS_SYSTEM(1129, "无权访问村社区系统"),

    NOT_ACTIVATED_NOT_EDIT(1130, "该党组织未审核通过，无法修改"),

    UKEY_HAS_BIND_USER(1131, "ukey已绑定用户:%s"),
    USER_IS_NOT_EXIST(1132, "用户在交换区不存在"),

    UKEY_NEEDED_LOGIN(1133, "用户已绑定KEY，请插入KEY登录"),

    UKEY_USER_NO_MATCH(1134, "用户绑定KEY与插入KEY不一致，请联系管理员处理！"),


    /**
     * 角色--权限
     */
    BUILT_IN_UNEDIT(1200, "系统管理员不可编辑"),

    PARENTID_ISNULL(1201, "角色父ID参数错误"),

    ROLE_CODE_MAX(1202, "角色code已经达到最大"),

    ROLE_BEHIND_TIME(1203, "角色已过期"),

    ROLE_NOT_EXIST(1204, "角色不存在"),

    BUILT_IN_FIELD_UNEDIT(1205, "内建字段不可编辑"),

    ROLE_ID_ISEMPTY(1206, "角色Id为空"),

    PARENTID_ERROR(1207, "父角色不能是自身及其自身下级!"),

    USER_ROLE_ERROR(1208, "其他用户关联了该角色!"),

    ROLE_HAS_SUB(1208, "角色还有下级!"),

    TOKEN_AUTH_FAILD(1300, "会话认证失败"),

    PERMISSION_DENIED(1301, "您暂无权限处理该数据信息"),

    PERMISSION_NOT_NULL(1302, "权限不能为空"),

    PERMISSION_LENGTH(1303, "权限数据错误"),

    ROLE_PAST(1304, "角色已过期"),

    PERMISSION_INDEX_OUT(1305, "权限越界"),

    PERMISSION_length(1306, "权限数据错误"),

    USER_ROLE_PAST(1307, "该用户当前角色已过期,拒绝操作"),

    PARENT_ROLE_PAST(1308, "父角色已过期,拒绝操作"),

    USER_READ_ONLY_ERROR(1309, "用户只读,拒绝操作"),

    SIG_ERROR(1310, "数据签名不合法"),
    NOT_IS_ADMIN(1311, "暂无管理员权限"),
    PERMISSION_NODE_ERR0(1312, "党员所在党支部上级具有预备党员审批权限的党（工）委未设置预备党员审批权限，请上级具有预备党员审批权限的党（工）委前往党组织管理—审批预备党员权限管理中新增预备党员审批权限，权限设置后支部再发起转接"),

    ACTIVIST_PERMISSION_NODE_ERR0(1313, "积极分子所在党支部上级具有预备党员审批权限的党（工）委未设置预备党员审批权限，请上级具有预备党员审批权限的党（工）委前往党组织管理—审批预备党员权限管理中新增预备党员审批权限，权限设置后支部再发起转接"),

    TARGET_ORG_PERMISSION_NODE_ERR0(1314, "目的党组织上级具有预备党员审批权限的党（工）委未设置预备党员审批权限，请上级具有预备党员审批权限的党（工）委前往党组织管理—审批预备党员权限管理中新增预备党员审批权限，权限设置后支部再发起转接"),
    NOT_IS_ADMINORG(1315, "暂未找到分节点顶级党组织，请联系客服"),
    /**
     * 组织
     */
    PARTY_BRANCH_ERROR(1400, "党支部不能新增下级"),

    ORG_NAME_EROOR(1401, "组织名称不符合规范,需以中共开头，以委员会结尾"),

    D01_CODE_ERROR(1402, "所选组织类型有误"),

    ORG_NOT_EXIST(1403, "组织不存在"),

    JOBINFO_NOT_NULL(1404, "领导岗位不能为空"),

    ORGID_NOT_NULL(1405, "组织id不能为空"),

    NEED_DEL_GROUP_MEM(1406, "删除党小组必须先移除党小组中的成员"),

    ORG_GROUP_MEM_NULL(1407, "党小组成员不存在"),

    ORG_GROUP_MEM_EXIST(1408, "该党员已存在其他党小组中"),

    ORG_GROUP_NEED_DZB(1409, "只有党支部才能添加党小组"),

    ELECT_COMMIT_DATE_EARLY(1410, "任职开始时间不能早于结束时间"),

    ELECT_NOT_EXIST(1411, "当前届次信息不存在"),

    ELECT_COMMIT_NOTNULL(1412, "请移除当前届次班子成员"),

    START_DATE_EARLY(1413, "届次开始时间不能晚于结束时间"),

    START_DATE_LAST(1414, "届次开始时间不能晚于最近届次结束时间"),

    COMMIT_IS_NULL(1415, "暂未找到该班子成员信息"),

    COMMIT_ELECT_NOTEQ(1416, "届次与班子成员届次信息不一致"),

    MEM_D28_ERROR(1417, "党员转正类型错误!"),

    UNION_DZB_MUST_TWO(1418, "联合党支部必须关联2个或2个以上的单位"),

    NOT_UNION_DZB_MUST_ONE(1419, "独立单位只能关联一个单位"),

    ORG_MUST_UNION_UNIT(1420, "请选择关联单位"),

    LINKED_IS_MAIN_UNIT_ONE(1421, "关联的单位有且只能有一个主单位"),

    NEED_DEL_SUB_ORG(1422, "请先删除下级组织"),

    NEED_CANCEL_UNIT(1423, "请先取消关联或删除关联单位"),

    NEED_DEL_ORG_MEM(1424, "请先转接或删除组织下的党员"),

    NEED_DEL_ORG_DEVELOP_MEM(1425, "请先转接或删除组织下的发展党员"),

    NEED_DEL_ORG_REPRESENTATIVE(1426, "请先删除组织下的党代表相关信息"),

    NEED_DEL_ORG_MANY_MEM(1427, "请先取消关联组织下的多重党员"),

    NEED_DEL_ORG_UNIT(1428, "请先删除组织下的单位"),

    NEED_DEL_ORG_TRANSFER(1429, "请先完成或撤销组织下的关系转接"),

    NEED_DEL_ORG_FLOW(1430, "请先完成组织下的党员流动"),

    NEED_DEL_ORG_ACTIVITY(1431, "请先完成或撤销组织下的活动"),

    DO_NOT_REPEAT_ADD(1432, "该组织软弱涣散信息已存在，请勿重复添加"),

    NOT_DISSOLVE(1433, "该组织(单位)有%s名党员基本信息！撤销党组织，其信息应该先转走！"),

    ONLY_BE_NEARLY_FIVE_YEARS(1434, "评议年度只能是近五年"),

    ONLY_ONCE_A_YEAR(1435, "民主评议一年只能新增一条记录"),

    DEMOCRATIC_APPRAISAL_DOES_NOT_EXIST(1436, "民主评议不存在"),

    ORGANIZATION_CANNOT_BE_CHANGED(1437, "不能更改组织机构"),

    DEMOCRATIC_REVIEWERS_DOES_NOT_EXIST(1438, "评议人员不存在"),

    A_PERSON_CAN_ONLY_COMMENT_ONCE_A_YEAR(1439, "%s，一年，只能评议一次"),

    THE_NUMBER_OF_REVIEWERS_CANNOT_BE_GREATER_THAN_THE_NUMBER_OF_REVIEWERS(1440, "评议人员数不能大于应评议人数"),

    PLEASE_SELECT_A_PARTY_MEMBER(1441, "请选择分拆党组织的党员和入党申请员！"),

    LEAGUE_MEMBERS_IN_DIRECTING(1442, "团员推优党员年龄应再28岁以下"),

    PUBLIC_OWNERSHIP_NO_MORE_THAN_SIXTY(1443, "公有制岗位申请入党时间不允许超过60岁"),

    START_GT_END(1444, "结束时间不能小于开始时间"),

    DISQUALIFICATION_OF_APPLICANT(1445, "只能取消入党申请人资格"),

    THE_PERSONNEL_OF_THE_CURRENT_SESSION_HAVE_BEEN_DELETED(1446, "当前届次人员已被移除"),

    RECOGNITION_SITUATION_CANNOT_BE_EMPTY(1447, "表彰情况不能为空"),

    THERE_PARTY_MEMBERS_IN_THE_CURRENT_ORGANIZATION(1448, "当前组织下存在党员"),

    THERE_JOIN_THE_PARTY_IN_THE_CURRENT_ORGANIZATION(1448, "当前组织下存在入党申请人"),

    THERE_FLOW_PARTY_MEMBERS_IN_THE_CURRENT_ORGANIZATION(1448, "当前组织下存在流动党员"),

    THERE_TRANSFER_OUT_IN_THE_CURRENT_ORGANIZATION(1448, "当前组织下存在关系转出"),

    THERE_TRANSFER_IN_IN_THE_CURRENT_ORGANIZATION(1448, "当前组织下存在关系转入"),


    THERE_ORG_IN_THE_CURRENT_ORGANIZATION(1449, "当前组织下存在下级组织"),
    CURRENT_ORG_IS_ZIJ(1450, "不允许调整到自己及其下属组织"),
    CURRENT_ORG_NOT_EXIST(1450, "目标组织不存在"),
    UNION_DZB_MUST_THREE(1451, "联合党支部关联单位属性须选择联合党支部"),
    UNION_DZB_MUST_SIX(1452, "非党支部关联单位属性须选择独立单位或与上级党组织相同"),
    UNION_DZB_MUST_Five(1453, "非基层党委、党支部、联合党支部不允许选择关联单位情况"),
    UNION_MOVE_MUST(1453, "无法调整组织架构到党支部或者联合党支部下"),
    UNION_DZB_MUST_TEST(1455, "发起失败，请先处理好发起组织下的党员以及组织数据"),


    /**
     * 同一个届次下只能选择一个书记
     */
    THEREIS_ONLY_ONE_SECRETARY_IN_THE_SAME_SESSION(1456, "同一届次下只能选择一个书记"),

    /**
     * 请先配置信息项锁定
     */
    LOCK_INFO_ITEM_NOT_CONFIGURED(1457, "请先配置信息项锁定"),

    /**
     * 党员已被锁定
     */
    MEM_HAVE_BEEN_LOCKED(1458, "党员已被锁定"),

    /**
     * 组织已被锁定
     */
    ORG_HAVE_BEEN_LOCKED(1459, "组织已被锁定"),

    /**
     * 组织已被锁定
     */
    UNIT_HAVE_BEEN_LOCKED(1460, "单位已被锁定"),

    /**
     * 组织不允许锁定
     */
    ORG_DOES_NOT_ALLOW_LOCKING(1461, "组织不允许锁定"),

    /**
     * 上级组织才能解锁
     */
    PARENT_ORG_CAN_UNLOCK(1462, "上级党组织才能解锁"),

    /**
     * 已向上级发起信息解锁申请
     */
    INFO_UNLOCKING_APPLICATION_HAS_BEEN_INITIATED_TO_THE_SUPERIOR(1463, "已向上级发起信息解锁申请"),

    /**
     * 报表信息不存在
     */
    ORG_REPORT_INFO_DOES_NOT_EXIST(1464, "报表信息不存在"),

    /**
     * 申请记录不存在
     */
    APPLICATION_RECORD_DOES_NOT_EXIST(1465, "申请记录不存在"),
    USER_DEL_ORG_ACCOUNT(1426, "当前党组织或下组织存在使用账号，请先修改账号管理组织或禁用、删除相关账号"),

    ORG_NATIONAL_ECONOMY_SAME_UNIT(1466, "下级党组织不能与本级组织同步过来的国民经济行业相同"),

    PERMISSION_CREATE_FLOW(1467, "您无权限创建流动党组织"),

    PASS_EXAMINE_NOT_CANCEL(1468, "该流动党组织不是待审核状态无法撤销"),

    PARTY_BRANCH_NOT(1469, "党支部下不能创建组织"),

    /**
     * 单位
     */
    HAS_UNIT_COMMITTEE(1500, "请先删除单位下面的班子成员!"),

    UNIT_IS_NULL(1501, "单位不存在"),

    UNIT_COMM_IS_REPETITION(1502, "请不要重复添加班子成员"),

    START_DATE_BEFORE_END(1503, "任职开始时间不能晚于结束时间"),

    SOCIAL_CREDIT_CODE_ALREADY_EXISTS(1504, "社会信用代码已被:%s使用"),

    YEAR_ALREADY_EXISTS(1505, "年份已存在！"),

    HAS_UNIT_MAIN_ORGAN(1506, "当前单位为组织的主要单位，请先在组织管理中取消主要单位关联"),

    UNIT_EXPAND_COLLECTIVE_ECONOMY_IS_NULL(1507, "财政扶持壮大村级集体经济情况不存在"),
    ORG_IS_NULL(1508, "导入党组织信息不存在，请核实后导入"),


    /**
     * 单位组织关联
     */
    LINKED_IS_MAIN_ORG_ONE(1600, "关联的组织有且只能有一个主组织"),

    LINKED_SIZE_ERROR(1601, "请选择需要关联的组织"),

    LINKED_NOTORG_ONE(1602, "非机关单位只能关联一个单位"),

    /**
     * 流动党员
     */
    FLOW_OUT_MEM_NO_PERMISSION(1700, "非当前账号管理的党员无法流动!"),

    /**
     * 党员
     */
    TIME_NULL_YBDY(1890, "短期培训时间,开始时间必须大于结束时间,不能为空,开始时间结束时间必须大于等于3天"),
    TIME_ERROR_YBDY(1891, "短期集中培训开始结束时间和政治审查结论性意见落款时间小于发展对象时间"),
    TIME_ERROR__TWO_YBDY(1891, "短期集中培训开始结束时间和政治审查结论性意见落款时间大于支部党员大会通过时间"),
    AGE_NEED_OVER_18(1892, "入党申请人年龄必须大于等于18岁"),
    BIRTH_NEED_LT_NEW(1800, "出生日期不能晚于当前日期"),

    APPLY_NEED_GT_BIRTH(1801, "申请入党日期必须晚于出生日期"),

    ACTIVE_NEED_GT_BIRTH(1802, "积极分子日期必须晚于申请入党日期"),

    OBJECT_NEED_GT_ACTIVE(1803, "发展对象日期必须晚于积极分子日期"),

    JOIN_ORG_NEED_GT_ACTIVE(1804, "预备党员日期必须晚于发展对象日期"),

    FULL_MEM_NEED_GT_ACTIVE(1805, "正式党员日期必须晚于预备党员日期"),

    LOST_CONTACT_DATE_NULL(1806, "失去联系日期不能为空"),

    IDCARD_IS_ERROR(1807, "身份证格式不对"),

    MEM_IS_ERROR(1808, "该党员信息不存在"),

    BACK_DATE_BEFORE_ABROAD(1809, "出国时间不能晚于回国时间"),

    NOT_FIND_MEM_INFO(1811, "未查询到外支部人员信息"),

    FLOW_NOTNULL_ORG_CODE(1811, "明确组织code不允许为空"),

    FLOW_NOTNULL_ORG_ORG_CODE(1812, "明确组织层级码不允许为空"),

    FLOW_NOTNULL_ORG_ORG_NAME(1813, "明确组织名称不允许为空"),

    MUST_PROBATIONARY_PARTY_MEM(1814, "只有预备党员才能进行预备党员转正"),

    EXTENDPREPAR_DATE_ERROR(1815, "延长预备期时间错误"),

    JOIN_CURR_EQUAL(1816, "请不要关联本支部的人员"),

    CURR_MEM_EXTEND_NOT(1817, "当前人员未被延长预备期"),

    FLOW_BACK_ORG_CODE(1818, "流回组织码不允许为空"),

    FLOW_BACK_ORG_ORG_CODE(1819, "明确组织层级码不允许为空"),

    BIRTHDAY_GT_WORK(1820, "参加工作时间必须晚于出生日期"),

    BIRTHDAY_GT_ACTIVE(1821, "成为积极分子时间必须晚于出生日期"),

    BIRTHDAY_GT_OBJECT(1822, "成为发展对象时间必须晚于出生日期"),

    BIRTHDAY_GT_LOSS(1823, "失联时间必须晚于出生日期"),

    BIRTHDAY_GT_appointment(1824, "聘任时间必须晚于出生日期"),

    BIRTHDAY_GT_APPOINTMENTEND(1825, "聘任结束时间必须晚于出生日期"),

    APPOINTMENT_GT_APPOINTMENTEND(1826, "聘任结束时间必须晚于聘任时间"),

    D08_CODE_ERROR(1826, "人员类型参数错误"),

    D11_CODE_ERROR(1827, "进入支部类型错误"),

    JOIN_ORG_CODE_ERROR(1828, "进入支部类型错误"),

    REPEAT_ASSOCIATED_MEM(1829, "请不要重复关联同一名党员"),

    REPEAT_IDCARD(1830, "与系统中的身份证号码相同，请核对。"),

    UNIT_REPEAT_ORG(1831, "请不要重复关联同一组织"),

    ORG_REPEAT_UNIT(1832, "请不要重复关联同一单位"),

    DZB_ADD_MEM(1833, "只有党支部和联合党支部才能新增人员"),

    UPDATE_D08_CODE(1834, "编辑时不能更改人员类型"),

    FULL_MEMBER_DATE_LESS_ONE_YEAR(1835, "转正时间必须大于入党时间一年以上"),

    LEAVE_ORG_DEL_MEM_DIFFICULT(1836, "请先取消该党员的困难党员资格"),

    LEAVE_ORG_DEL_MEM_MANY(1837, "该党员属于关联党员，请先取消关联情况"),

    THE_ENTRY_BRANCH_TYPE_CANNOT_BE_MODIFIED(1838, "进入支部类型不允许修改"),

    DEVELOP_UPLOAD_EXCEL_ERROR(1839, "请使用正确的模板进行导入"),

    PERSONNEL_ALREADY_EXIST(1840, "该党员员已存在"),

    NOT_IN_BETWEEN_REPORT_DATE(1850, "%s需填写统计报告期以内的时间"),
    NOT_IN_BETWEEN_DEFAULT_DATE(1851, "%s需填写预设时间以内时间【%s】"),
    MEM_IN_HAVE(1852, "党员 %s 已在党组织 %s 存在，请与该党组织联系确认具体情况"),
    MEM_NUM_MAX(1853, "导出信息已经超过3500条，请选择层级党组织进行导出"),

    /**
     * 组织关系转接
     */
    ORG_TRANSFER_SRC_AND_TARGET_ERROR(1901, "组织关系转接源组织和目标组织错误"),

    ORG_TRANSFER_TYPE_ERROR(1902, "组织关系转接类型错误"),

    TARGET_TRANSFER_ERROR(1903, "转接失败,目标组织或者目标组织上级正在转接中"),

    SRC_TRANSFER_ERROR(1904, "转接失败,上级组织或下级组织以及人员正在转接中"),

    TRANSFER_UNDO_PERMISSION_ERROR(1905, "撤销失败,不能撤销非本人发起的转接申请"),

    TRANSFER_CHANGE_TARGET_FOR_SRC_ERROR(1906, "更改目标组织失败,目标组织不能是当前组织及其下级"),

    TRANSFER_CHANGE_TARGET_FOR_TARGET_ERROR(1907, "更改目标组织失败,目标组织只能是当前组织下级"),

    TRANSFER_CHANGE_MEM_FEE_ERROR(1908, "修改党费失败,改人员未在转接中"),

    TRANSFER_MEM_SYS_OUT_IN_ID_CARD_ERROR(1909, "人员身份证号码不正确"),

    TRANSFER_ALREADY_APPLY_ERROR(1910, "该组织已被审批,请刷新页面重试!"),

    TRANSFER_MEM_ADJUST_DETAIL(1911, "支部间人员调整暂无详情"),

    TRANSFER_CHANGE_TARGET_EMPTY_TARGET_ERROR(1912, "转出市外无法更改目标组织"),

    TRANSFER_SRC_OR_TARGET_NULL_ERROR(1913, "目标组织和源组织不能为空"),

    TRANSFER_NOT_SYSTEM_OUT_IN_SRC_NULL_ERROR(1914, "非系统外转入,源组织不能为空"),

    TRANSFER_NOT_SYSTEM_OUT_OUT_SRC_NULL_ERROR(1914, "非系统外转出,目标组织不能为空"),

    TRANSFER_TARGET_ORG_IS_NOT_EXIST(1915, "目标组织不存在"),

    TRANSFER_SRC_ORG_IS_NOT_EXIST(1916, "目标组织不存在"),

    TRANSFER_FOR_ORG_TARGET_ORG_TYPE_ERROR(1917, "目标组织只能是党委"),

    TRANSFER_FOR_MEM_TARGET_ORG_TYPE_ERROR(1918, "目标组织只能是支部"),

    TRANSFER_TARGET_ORG_ERROR_MESSAGE(1919, "目标组织:%s正在转接中"),

    TRANSFER_SRC_ORG_ERROR_MESSAGE(1920, "源组织:%s正在转接中"),

    TRANSFER_SRC_MEM_ERROR_MESSAGE(1920, "源组织人员:%s正在转接中"),
    TRANSFER_TARGET_ORG_IS_NOT_EXIST_APPROVE(1921, "目标组织不存在具有预备党员审批权限党委"),
    TRANSFER_MME_IS_TRANSFER(1922, "该党员已经存在转接中，请耐心等待审核完成后再转入"),

    /**
     * 党代表
     */
    ORG_OR_UNIT_TYPE_ERROR(2100, "所选组织不符合规定的六种类型，请仔细阅读温馨提示！"),

    ELECT_UNIT__NUM_EXIST(2101, "该单位已录入当前届次"),

    GB_MANAGE_UNIT_NOT_NULL(2102, "干部管理单位不能为空"),

    DDB_D24CODE_NOT_NULL(2103, "资格终止类型不能为空"),

    DDB_zbStopDate_NOT_NULL(2104, "资格终止日期不能为空"),

    DDB_STOP_GT_START(2105, "终止时间不能早于当选时间"),

    DDB_ELECT_EXIST(2106, "当前届次中已经包含该党代表,请不要重复添加"),

    /**
     * 党费
     */
    MONTH_PAY_MONEY(2200, "当前月份已经缴纳党费,无法进行此操作"),

    MONTH_NEED_AFTER_LAST_PAY_DAY(2201, "设置月份不能早于党员最晚缴费时间"),

    LAST_PAY_DAY_IS_NULL(2202, "党员暂无最后缴费时间"),

    PAY_MONEY_ERROR(2203, "缴费金额计算有误"),

    NOT_CHOOSE_MEM(2204, "请先选择需要缴费党员"),

    OUTTRADENO_NOT_DATA(2205, "未查询到该订单的相关记录"),

    PAY_MONTH_ERROR(2206, "交费月数错误"),
    STANDARD_MUST_GT_ZERO(2207, "党费标准必须大于零"),
    PAY_DUES_ERROR(2208, "未查询到该月份记录"),
    PAY_STANDARD_ERROR(2209, "当前党员存在月份未设置党费标准，请设置后再应用缴费至全年"),
    PAY_NUMBER_ERROR(2210, "导入数据最大支持3500行,已经超过最大行数"),

    /***
     * 微信
     * */
    WX_ROLE_ERROR(2310, "服务端微信配置异常!"),

    WX_CONFIG_ERROR(2311, "服务端未配置相应的APPID"),

    WX_USER_BIND_ERROR(2312, "用户未绑定"),

    WX_APP_ID_NULL_ERROR(2313, "微信appid不能为空"),

    NOT_BILL_MSG(2313, "暂未查询到流水信息"),

    RESET_BILL_FAIL(2314, "重置流水信息失败"),

    /**
     * 活动
     */
    ACTIVITY_NOT_AUTH_EDIT(2401, "暂无修改活动的权限"),

    ACTIVITY_IS_CONDUCT(2402, "该活动已经开始，无法修改"),

    ACTIVITY_NOT_PRESENCE(2403, "暂未找到该活动信息"),

    ACTIVITY_NOT_JOIN(2404, "您暂未参与该活动"),

    ACTIVITY_NOT_REPEAT(2406, "请勿重复签到该活动"),

    ACTIVITY_NOT_START(2407, "该活动暂未开始"),

    ACTIVITY_IS_END(2407, "该活动已结束"),

    ACTIVITY_IS_CHECK(2407, "您已签到,无法请假"),

    ACTIVITY_SATUS_GET(2408, "未知请求来源,已经上报管理员"),

    ACTIVITY_SATUS_ORG_ORG_CODE(2409, "请传入组织标识符"),

    ACTIVITY_NOT_PC(2410, "该请求仅适合电脑端"),

    ACTIVITY_NOT_MEM(2411, "未找到该党员信息"),

    ACTIVITY_NOT_AUTH_SELECT(2412, "暂无查看活动的权限"),

    /**
     * 通知相关
     */
    NOTICE_IS_EMPTY(2501, "该消息已不存在"),

    NOTICE_REPLY_EMPTY(2502, "请确认回复类型"),

    NOTICE_NOT_OUTH(2503, "暂无管理权无法查看"),

    /**
     * 工作动态相关
     */
    TREND_IS_EMPTY(2601, "暂未找到该动态"),

    TREND_NOT_PUSH(2602, "该动态暂未发布"),

    TREND_NOT_AUTH(2603, "暂无该动态权限"),

    TREND_IS_PUSH(2604, "该动态已经发布"),

    TREND_IS_CANCEL(2605, "该动态已经撤销"),

    TREND_IS_DELETE(2606, "该动态已经删除"),

    CHECK_IS_EMPTY(2607, "暂未找到该审核"),

    TREND_IS_CHECK(2608, "该动态已被审核"),

    TREND_CHECK_AUTH(2609, "无该动态审核权限"),

    ORG_CHECK_STATUS(2610, "审核状态不允许为空"),

    /**
     * 民主评议
     */
    NEED_BRANCH_ORG(2701, "党支部才能进行相关操作"),

    START_NEED_GT_NOW(2702, "开始时间不能早于当前时间"),

    START_NEED_LT_DEADLINETIME(2703, "开始时间需早于互评截止时间"),

    DEADLINE_NEED_LT_ENDTIME(2704, "互评截止时间需早于评议截止时间"),

    DEMOCRATIC_YEAR_EXIST(2705, "所选年度已进行过民主评议"),

    DEMOCRATIC_YEAR_END(2706, "该年度民主评议已结束"),

    CANNOT_CHANGE_THETIME(2707, "不可更改当前状态之前的时间"),

    CANNOT_CHANGE_DEMOCRATIC_YEAR(2708, "不可更改民主评议年度"),

    /**
     * 任务相关
     */
    TASK_NO_AUTH(2801, "暂无发布任务权限"),

    TASK_NO_SELECT_AUTH(2802, "暂无查看权限"),

    TASK_IS_NULL(2803, "暂为找到该任务"),

    TASK_NO_CHECK_AUTH(2804, "暂无该任务审核权限"),

    TASK_IS_CHECK(2805, "该任务已被审核"),

    TASK_CHECK_IS_NULL(2806, "暂未找到审核记录"),

    TASK_CHECK_NOT_REMOVE(2807, "该任务已经提交,无法移除"),

    FILE_NO_EXIST(2809, "文件号已存在"),

    SELECT_OBJECT_CAN_NOT_EMPTY(2810, "操作对象不能为空"),

    SELECT_CAN_NOT_EMPTY(2811, "发展党员管理党组织层级码不允许为空"),

    ORG_PROPERTY_ERRO(2813, "根据《中国共产党发展党员工作细则》规定：乡镇（街道）党委所属的基层党委%s ，不能审批预备党员"),
    ORG_PROPERTY_TYPE_ERRO(2814, "根据《中国共产党发展党员工作细则》规定：中共各级委员会党委%s ，不能审批预备党员"),

    /**
     * 左括号和右括号数量不一致
     */
    BRACKETS_ERROR(2812, "左括号和右括号数量不一致"),


    NO_DATA_FOUND_FLOW_INFO_DOES_NOT_EXIST(3000, "未查询到数据，流动信息不存在"),
    FLOW_DATA_STATUS_CHANGE(3001, "此次操作无效，该流动党员状态已变更，请稍后再试！"),

    MEM_FLOW_RECORDS_NOT_EXIST(3002, "未查询到数据，流动记录不存在"),

    /**
     * 请选择文件
     */
    FILE_NULL(9990, "请选择文件"),

    /**
     * 数据有问题
     */
    ORG_CODE_ERROR(9993, "组织层级码错误"),

    DATA_ERROR(9994, "该数据存在问题,请联系管理员"),

    /**
     * 发生异常
     */
    EXCEPTION_ERROR(9995, "发生异常"),

    /**
     * 该对象在数据库存不存在,不能修改
     */
    OBJECT_DBISNOTALIVE(9996, "该对象在数据库存不存在,不能修改"),

    /**
     * 该对象已在数据库存在不能保存
     */
    OBJECT_DBISALIVE(9997, "该对象已经在数据库存在"),

    /**
     * 每页显示个数大于100,太大
     */
    PAGE_MAX_PAGESIZE_ISLARGER(9998, "每页显示个数大于100"),

    /**
     * 系统错误
     */
    DATABASE_ERROR(9998, "错误的 Sql 语法异常"),

    /**
     * 加密机异常
     */
    JMJ_ENCRYPT_ERROR(9001, "加密机加密失败，请稍后重试!"),

    /**
     * 加密机解密异常
     */
    JMJ_DECRYPT_ERROR(9002, "加密机解密失败，请稍后重试!"),

    /**
     * 加密机通用异常
     */
    JMJ_ERROR(9003, "加密机服务异常，请稍后重试!"),

    /**
     * 系统错误
     */
    SYSTEM_ERROR(9999, "系统错误");

    /**
     * 标识码
     */
    private final Integer code;

    /**
     * 消息
     */
    private final String message;

    Status(Integer code, String message) {
        this.code = code;
        this.message = message;
    }

    public Integer getCode() {
        return code;
    }

    public String getMessage() {
        return message;
    }
}
