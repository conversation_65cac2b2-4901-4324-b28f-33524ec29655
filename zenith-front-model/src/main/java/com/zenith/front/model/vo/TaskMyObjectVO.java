package com.zenith.front.model.vo;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

import java.util.Date;

/**
 * @author: <PERSON><PERSON>watermelon
 * @date: 2019/6/27 14:34
 * @description: 入则恳恳以尽忠 出则谦谦以自悔
 */

@Data
@ToString
@NoArgsConstructor
@AllArgsConstructor
public class TaskMyObjectVO {

    private Integer taskStatus;
    private String taskName;
    private Date startDate;
    private Date endDate;

    private Integer taskObject;
    private Date deleteTime;
    private Integer status;
    private Date createTime;

    private String objectOrgCode;
    private String taskCode;
    private String code;
    private String createOrgCode;
}
