package com.zenith.front.model.dto;

import com.zenith.front.common.annotation.PrimaryKey;
import com.zenith.front.model.bean.DevelopStepLog;
import com.zenith.front.model.validate.group.*;
import lombok.ToString;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.Date;

/**
 * <AUTHOR>
 */
@ToString
public class DevelopStepLogDTO {

    private Long id;
    private String code;
    private String esId;


    /**
     * 短期集中培训开始时间(发展对象=》预备党员）
     */
    @NotNull(groups = Common1Group.class, message = "topartCommitteeDate 不能为空")
    private java.util.Date shortTrainingBeginTime;

    /**
     * 短期集中培训结束时间(发展对象=》预备党员）
     */
    @NotNull(groups = Common1Group.class, message = "topartCommitteeDate 不能为空")
    private java.util.Date shortTrainingEndTime;

    /**
     * 政治审查结论性意见落款时间(发展对象=》预备党员）
     */
    @NotNull(groups = Common1Group.class, message = "topartCommitteeDate 不能为空")
    private java.util.Date reviewConclusionTime;






    /**
     * 姓名
     */
    @NotBlank(groups = {Common1Group.class, Common5Group.class}, message = "name 不能为空")
    private String name;
    /**
     * 组织名称
     */
    @NotBlank(groups = {Common1Group.class, Common5Group.class}, message = "orgName 不能为空")
    private String orgName;
    /**
     * 组织zb_code
     */
    @NotBlank(groups = {Common5Group.class}, message = "orgZbCode 不能为空")
    private String orgZbCode;
    /**
     * 组织org_code
     */
    @NotBlank(groups = {Common1Group.class, Common5Group.class}, message = "orgCode 不能为空")
    private String orgCode;
    /**
     * 组织层级码
     */
    @NotBlank(groups = {Common1Group.class, Common5Group.class}, message = "logOrgCode 不能为空")
    private String logOrgCode;
    /**
     * 人员code唯一标识
     */
    @NotBlank(groups = {Common1Group.class, Common5Group.class}, message = "memCode 不能为空")
    @PrimaryKey
    private String memCode;
    /**
     * 入党介绍人（发展对象=》预备党员）
     */
    private String topreIntroductionMem;
    /**
     * 支委会会议记录扫描件（发展对象=》预备党员）
     */
    private String topreFileUrl;
    /**
     * 入党志愿书扫描件（发展对象=》预备党员）
     */
    private String topreJoinBookUrl;
    /**
     * 上级党委审批日期（发展对象=》预备党员）
     */
    @NotNull(groups = Common8Group.class, message = "topreCommitteeDate 不能为空")
    private java.util.Date topreCommitteeDate;
    /**
     * 入党志愿书编号（发展对象=》预备党员）
     */
    private String topreJoinBookNum;
    /**
     * 召开支委会日期(成为预备党员日期)
     */
    @NotNull(groups = Common8Group.class, message = "topreJoinOrgDate 不能为空")
    private java.util.Date topreJoinOrgDate;
    /**
     * 上级党委审批通知扫描件（发展对象=》预备党员）
     */
    private String topreCommitteeFileUrl;
    /**
     * 人员类别
     */
    @NotBlank(groups = {Common1Group.class, Common5Group.class}, message = "d08Name 不能为空")
    private String d08Name;
    /**
     * 人员类别
     */
    @NotBlank(groups = {Common1Group.class, Common5Group.class}, message = "d08Code 不能为空")
    private String d08Code;
    /**
     * 加入共产党类型code（发展对象=》预备党员）
     */
    private String joinOrgCode;
    /**
     * 加入共产党类型（发展对象=》预备党员）
     */
    private String joinOrgName;
    /**
     * 进入支部类型code（发展对象=》预备党员）
     */
    private String d11Code;
    /**
     * 进入支部类型（发展对象=》预备党员）
     */
    private String d11Name;
    /**
     * 入党介绍人(预备党员=》正式党员）
     */
    private String topartIntroductionMem;
    /**
     * 支委会会议记录扫描件(预备党员=》正式党员）
     */
    private String topartFileUrl;
    /**
     * 召开支委会日期(成为正式党员日期)
     */
    @NotNull(groups = Common1Group.class, message = "topartTurnPartyDate 不能为空")
    private java.util.Date topartTurnPartyDate;
    /**
     * 审批结果(预备党员=》正式党员）
     */
    @NotBlank(groups = Common1Group.class, message = "d28Name 不能为空")
    private String d28Name;
    /**
     * 审批结果code(预备党员=》正式党员）
     */
    @NotBlank(groups = Common1Group.class, message = "d28Code 不能为空")
    private String d28Code;
    /**
     * 上级党委审批通知扫描件(预备党员=》正式党员）
     */
    private String topartCommitteeFileUrl;
    /**
     * 上级党委审批日期(预备党员=》正式党员）
     */
    @NotNull(groups = Common1Group.class, message = "topartCommitteeDate 不能为空")
    private java.util.Date topartCommitteeDate;
    /**
     * 入党宣誓日期(预备党员=》正式党员）
     */
    @NotNull(groups = Common1Group.class, message = "topartOathDate 不能为空")
    private java.util.Date topartOathDate;
    /**
     * 延长预备期到的时间
     */
    private java.util.Date extendPreparDate;
    /**
     * 支委会会议记录扫描件(申请人=》积极分子）
     */
    private String toactiveApplyScanFile;
    /**
     * 入党申请人联系人(申请人=》积极分子）
     */
    private String toactiveContextPerson;
    /**
     * 召开支委会日期(成为积极分子日期)
     */
    @NotNull(groups = Common2Group.class, message = "activeDate 不能为空")
    private java.util.Date activeDate;
    /**
     * 支委会会议记录扫描件(积极分子=》发展对象）
     */
    private String toobjFileUrl;
    /**
     * 培养教育考察时间(积极分子=》发展对象）
     */
    @NotNull(groups = Common7Group.class, message = "toobjCultivateDate 不能为空")
    private java.util.Date toobjCultivateDate;
    /**
     * 考察材料扫描件(积极分子=》发展对象）
     */
    private String toobjCheckFileUrl;
    /**
     * 培养联系人(积极分子=》发展对象）
     */
    private String toobjContextMem;
    /**
     * 召开支委会日期(成为发展对象时间
     */
    @NotNull(groups = Common7Group.class, message = "objectDate  不能为空")
    private java.util.Date objectDate;
    /**
     * 取消资格方式(1=退回入党申请人阶段,2=退回积极分子阶段)
     */
    @NotNull(groups = Common6Group.class, message = "canncelDate 不能为空")
    private java.util.Date canncelDate;
    @NotBlank(groups = Common6Group.class, message = "canncelCode 不能为空")
    private String canncelCode;
    @NotBlank(groups = Common6Group.class, message = "canncelName 不能为空")
    private String canncelName;
    private java.util.Date createTime;
    private java.util.Date updateTime;
    private java.util.Date deleteTime;
    /**
     * 入党宣誓照片（预备党员=》正式党员）
     */
    private String topartOathDateUrl;
    private Integer isHistory;
    private String updateAccount;
    /**
     * 工作岗位
     */
    private String d09Code;

    private String d09Name;

    /**
     * 0--半年,1--一年
     */
    private Integer isMark;

    /**
     * 是否系统外,1-系统外,0--系统内
     */
    private Integer isOutSystem;
    private String outBranchOrgName;

    private String instructions;
    /**
     * 其他政治面貌
     */
    private String d89Code;
    private String d89Name;


    /**
     * 发展党员code
     */
    private String developCode;

    /**
     * 学历code
     */
    private String d07Code;

    /**
     * 学历名称
     */
    private String d07Name;


    /**
     * 聘任专业技术职务名称
     */
    private String d19Name;

    /**
     * 聘任专业技术职务code
     */
    private String d19Code;

    /**
     * 新社会阶层类型名称
     */
    private String d20Name;

    /**
     * 新社会阶层类型code
     */
    private String d20Code;

    /**
     * 一线情况code
     */
    private String d21Code;

    /**
     * 一线情况名称
     */
    private String d21Name;

    /**
     * 是否农民工
     */
    private Integer isFarmer;

    /**
     * 是否高知识群体
     */
    private Integer isHighKnowledge;

    /**
     * 是否是先进模范人物
     */
    private String advancedModelCode;

    /**
     * 是否劳务派遣
     */
    private Integer isDispatch;

    /**
     * 毕业院校（专科及以上填写）
     */
    private String byyx;

    /**
     * 毕业专业（专科及以上填写）
     */
    private String d88Code;

    private String d88Name;

    /**
     * 是否青年农民
     */
    private Integer hasYoungFarmers;

    /**
     * 是否产业工人
     */
    private Integer hasWorker;

    /**
     * 是否本组织人员
     */
    private Integer hasStaffOrganization;
    /**
     * 取消发展原因
     */
    private String cancelDevelopReason;

    private Boolean hasSpecialDevelopment;

    /**
     * 组织类别
     */
    private String d01Code;

    private String statisticalUnit;
    /**
     * 在读院校
     */
    private String readingCollege;
    /**
     * 在读专业
     */
    private String readingProfessionalCode;
    /**
     * 在读专业名称
     */
    private String readingProfessionalName;
    /**
     * 学制
     */
    private String educationalSystem;

    /**
     * 入学时间
     */
    private Date enterSchoolDate;

    /**
     * 申请入党日期
     */
    private Date applyDate;
    /**
     * 发展党员身份证
     */
    private String idcard;
    /**
     * 民族code
     */
    private String d06Code;
    /**
     * 民族名称
     */
    private String d06Name;
    /**
     * 籍贯
     */
    private String d48Code;
    /**
     * 籍贯名称
     */
    private String d48Name;
    /**
     * 性别
     */
    private String sexCode;
    /**
     * 性别name
     */
    private String sexName;
    /**
     * 出生日期
     */
    private Date birthday;
    /**
     * 电话
     */
    private String phone;
    /**
     * 发展党员年龄
     */
    private Integer age;
    /**
     * 工作性质
     */
    private String jobNatureCode;
    private String homeAddress;
    private String unitInformation;
    private String d04Code;
    private Integer hasUnitProvince;
    private Integer hasUnitStatistics;
    /**
     * 离开党组织日期
     */
    private Date leaveOrgDate;
    /**
     * 知识分子情况
     */
    private String d154Code;

    private String d154Name;

    /**
     * 是否需要自动计算年级（0 否 ，1 是）
     */
    private Integer hasCalculationGrade;

    public Boolean getHasSpecialDevelopment() {
        return hasSpecialDevelopment;
    }

    public void setHasSpecialDevelopment(Boolean hasSpecialDevelopment) {
        this.hasSpecialDevelopment = hasSpecialDevelopment;
    }

    public Integer getHasStaffOrganization() {
        return hasStaffOrganization;
    }

    public void setHasStaffOrganization(Integer hasStaffOrganization) {
        this.hasStaffOrganization = hasStaffOrganization;
    }

    public String getCancelDevelopReason() {
        return cancelDevelopReason;
    }

    public void setCancelDevelopReason(String cancelDevelopReason) {
        this.cancelDevelopReason = cancelDevelopReason;
    }

    public String getDevelopCode() {
        return developCode;
    }

    public void setDevelopCode(String developCode) {
        this.developCode = developCode;
    }

    public String getD07Code() {
        return d07Code;
    }

    public void setD07Code(String d07Code) {
        this.d07Code = d07Code;
    }

    public String getD07Name() {
        return d07Name;
    }

    public void setD07Name(String d07Name) {
        this.d07Name = d07Name;
    }

    public String getD19Name() {
        return d19Name;
    }

    public void setD19Name(String d19Name) {
        this.d19Name = d19Name;
    }

    public String getD19Code() {
        return d19Code;
    }

    public void setD19Code(String d19Code) {
        this.d19Code = d19Code;
    }

    public String getD20Name() {
        return d20Name;
    }

    public void setD20Name(String d20Name) {
        this.d20Name = d20Name;
    }

    public String getD20Code() {
        return d20Code;
    }

    public void setD20Code(String d20Code) {
        this.d20Code = d20Code;
    }

    public String getD21Code() {
        return d21Code;
    }

    public void setD21Code(String d21Code) {
        this.d21Code = d21Code;
    }

    public String getD21Name() {
        return d21Name;
    }

    public void setD21Name(String d21Name) {
        this.d21Name = d21Name;
    }

    public Integer getIsFarmer() {
        return isFarmer;
    }

    public void setIsFarmer(Integer isFarmer) {
        this.isFarmer = isFarmer;
    }

    public Integer getIsHighKnowledge() {
        return isHighKnowledge;
    }

    public void setIsHighKnowledge(Integer isHighKnowledge) {
        this.isHighKnowledge = isHighKnowledge;
    }

    public String getAdvancedModelCode() {
        return advancedModelCode;
    }

    public void setAdvancedModelCode(String advancedModelCode) {
        this.advancedModelCode = advancedModelCode;
    }

    public Integer getIsDispatch() {
        return isDispatch;
    }

    public void setIsDispatch(Integer isDispatch) {
        this.isDispatch = isDispatch;
    }

    public String getByyx() {
        return byyx;
    }

    public void setByyx(String byyx) {
        this.byyx = byyx;
    }

    public String getD88Code() {
        return d88Code;
    }

    public void setD88Code(String d88Code) {
        this.d88Code = d88Code;
    }

    public String getD88Name() {
        return d88Name;
    }

    public void setD88Name(String d88Name) {
        this.d88Name = d88Name;
    }

    public Integer getHasYoungFarmers() {
        return hasYoungFarmers;
    }

    public void setHasYoungFarmers(Integer hasYoungFarmers) {
        this.hasYoungFarmers = hasYoungFarmers;
    }

    public Integer getHasWorker() {
        return hasWorker;
    }

    public void setHasWorker(Integer hasWorker) {
        this.hasWorker = hasWorker;
    }

    public String getInstructions() {
        return instructions;
    }

    public void setInstructions(String instructions) {
        this.instructions = instructions;
    }

    public String getOutBranchOrgName() {
        return outBranchOrgName;
    }

    public void setOutBranchOrgName(String outBranchOrgName) {
        this.outBranchOrgName = outBranchOrgName;
    }

    public Integer getIsOutSystem() {
        return isOutSystem;
    }

    public void setIsOutSystem(Integer isOutSystem) {
        this.isOutSystem = isOutSystem;
    }

    public String getD09Code() {
        return d09Code;
    }

    public void setD09Code(String d09Code) {
        this.d09Code = d09Code;
    }

    public String getD09Name() {
        return d09Name;
    }

    public void setD09Name(String d09Name) {
        this.d09Name = d09Name;
    }

    public Integer getIsMark() {
        return isMark;
    }

    public void setIsMark(Integer isMark) {
        this.isMark = isMark;
    }

    public DevelopStepLogDTO setId(Long id) {
        this.id = id;
        return this;
    }

    public Long getId() {
        return this.id;
    }

    public DevelopStepLogDTO setCode(String code) {
        this.code = code;
        return this;
    }

    public String getCode() {
        return this.code;
    }

    public DevelopStepLogDTO setEsId(String esId) {
        this.esId = esId;
        return this;
    }

    public String getEsId() {
        return this.esId;
    }

    public DevelopStepLogDTO setName(String name) {
        this.name = name;
        return this;
    }

    public String getName() {
        return this.name;
    }

    public DevelopStepLogDTO setOrgName(String orgName) {
        this.orgName = orgName;
        return this;
    }

    public String getOrgName() {
        return this.orgName;
    }

    public DevelopStepLogDTO setOrgZbCode(String orgZbCode) {
        this.orgZbCode = orgZbCode;
        return this;
    }

    public String getOrgZbCode() {
        return this.orgZbCode;
    }

    public DevelopStepLogDTO setOrgCode(String orgCode) {
        this.orgCode = orgCode;
        return this;
    }

    public String getOrgCode() {
        return this.orgCode;
    }

    public DevelopStepLogDTO setLogOrgCode(String logOrgCode) {
        this.logOrgCode = logOrgCode;
        return this;
    }

    public String getLogOrgCode() {
        return this.logOrgCode;
    }

    public DevelopStepLogDTO setMemCode(String memCode) {
        this.memCode = memCode;
        return this;
    }

    public String getMemCode() {
        return this.memCode;
    }

    public DevelopStepLogDTO setTopreIntroductionMem(String topreIntroductionMem) {
        this.topreIntroductionMem = topreIntroductionMem;
        return this;
    }

    public String getTopreIntroductionMem() {
        return this.topreIntroductionMem;
    }

    public DevelopStepLogDTO setTopreFileUrl(String topreFileUrl) {
        this.topreFileUrl = topreFileUrl;
        return this;
    }

    public String getTopreFileUrl() {
        return this.topreFileUrl;
    }

    public DevelopStepLogDTO setTopreJoinBookUrl(String topreJoinBookUrl) {
        this.topreJoinBookUrl = topreJoinBookUrl;
        return this;
    }

    public String getTopreJoinBookUrl() {
        return this.topreJoinBookUrl;
    }

    public DevelopStepLogDTO setTopreCommitteeDate(java.util.Date topreCommitteeDate) {
        this.topreCommitteeDate = topreCommitteeDate;
        return this;
    }

    public java.util.Date getTopreCommitteeDate() {
        return this.topreCommitteeDate;
    }

    public DevelopStepLogDTO setTopreJoinBookNum(String topreJoinBookNum) {
        this.topreJoinBookNum = topreJoinBookNum;
        return this;
    }

    public String getTopreJoinBookNum() {
        return this.topreJoinBookNum;
    }

    public DevelopStepLogDTO setTopreJoinOrgDate(java.util.Date topreJoinOrgDate) {
        this.topreJoinOrgDate = topreJoinOrgDate;
        return this;
    }

    public java.util.Date getTopreJoinOrgDate() {
        return this.topreJoinOrgDate;
    }

    public DevelopStepLogDTO setTopreCommitteeFileUrl(String topreCommitteeFileUrl) {
        this.topreCommitteeFileUrl = topreCommitteeFileUrl;
        return this;
    }

    public String getTopreCommitteeFileUrl() {
        return this.topreCommitteeFileUrl;
    }

    public DevelopStepLogDTO setD08Name(String d08Name) {
        this.d08Name = d08Name;
        return this;
    }

    public String getD08Name() {
        return this.d08Name;
    }

    public DevelopStepLogDTO setD08Code(String d08Code) {
        this.d08Code = d08Code;
        return this;
    }

    public String getD08Code() {
        return this.d08Code;
    }

    public DevelopStepLogDTO setJoinOrgCode(String joinOrgCode) {
        this.joinOrgCode = joinOrgCode;
        return this;
    }

    public String getJoinOrgCode() {
        return this.joinOrgCode;
    }

    public DevelopStepLogDTO setJoinOrgName(String joinOrgName) {
        this.joinOrgName = joinOrgName;
        return this;
    }

    public String getJoinOrgName() {
        return this.joinOrgName;
    }

    public DevelopStepLogDTO setD11Code(String d11Code) {
        this.d11Code = d11Code;
        return this;
    }

    public String getD11Code() {
        return this.d11Code;
    }

    public DevelopStepLogDTO setD11Name(String d11Name) {
        this.d11Name = d11Name;
        return this;
    }

    public String getD11Name() {
        return this.d11Name;
    }

    public DevelopStepLogDTO setTopartIntroductionMem(String topartIntroductionMem) {
        this.topartIntroductionMem = topartIntroductionMem;
        return this;
    }

    public String getTopartIntroductionMem() {
        return this.topartIntroductionMem;
    }

    public DevelopStepLogDTO setTopartFileUrl(String topartFileUrl) {
        this.topartFileUrl = topartFileUrl;
        return this;
    }

    public String getTopartFileUrl() {
        return this.topartFileUrl;
    }

    public DevelopStepLogDTO setTopartTurnPartyDate(java.util.Date topartTurnPartyDate) {
        this.topartTurnPartyDate = topartTurnPartyDate;
        return this;
    }

    public java.util.Date getTopartTurnPartyDate() {
        return this.topartTurnPartyDate;
    }

    public DevelopStepLogDTO setD28Name(String d28Name) {
        this.d28Name = d28Name;
        return this;
    }

    public String getD28Name() {
        return this.d28Name;
    }

    public DevelopStepLogDTO setD28Code(String d28Code) {
        this.d28Code = d28Code;
        return this;
    }

    public String getD28Code() {
        return this.d28Code;
    }

    public DevelopStepLogDTO setTopartCommitteeFileUrl(String topartCommitteeFileUrl) {
        this.topartCommitteeFileUrl = topartCommitteeFileUrl;
        return this;
    }

    public String getTopartCommitteeFileUrl() {
        return this.topartCommitteeFileUrl;
    }

    public DevelopStepLogDTO setTopartCommitteeDate(java.util.Date topartCommitteeDate) {
        this.topartCommitteeDate = topartCommitteeDate;
        return this;
    }

    public java.util.Date getTopartCommitteeDate() {
        return this.topartCommitteeDate;
    }

    public DevelopStepLogDTO setTopartOathDate(java.util.Date topartOathDate) {
        this.topartOathDate = topartOathDate;
        return this;
    }

    public java.util.Date getTopartOathDate() {
        return this.topartOathDate;
    }

    public DevelopStepLogDTO setExtendPreparDate(java.util.Date extendPreparDate) {
        this.extendPreparDate = extendPreparDate;
        return this;
    }

    public java.util.Date getExtendPreparDate() {
        return this.extendPreparDate;
    }

    public DevelopStepLogDTO setToactiveApplyScanFile(String toactiveApplyScanFile) {
        this.toactiveApplyScanFile = toactiveApplyScanFile;
        return this;
    }

    public String getToactiveApplyScanFile() {
        return this.toactiveApplyScanFile;
    }

    public DevelopStepLogDTO setToactiveContextPerson(String toactiveContextPerson) {
        this.toactiveContextPerson = toactiveContextPerson;
        return this;
    }

    public String getToactiveContextPerson() {
        return this.toactiveContextPerson;
    }

    public DevelopStepLogDTO setActiveDate(java.util.Date activeDate) {
        this.activeDate = activeDate;
        return this;
    }

    public java.util.Date getActiveDate() {
        return this.activeDate;
    }

    public DevelopStepLogDTO setToobjFileUrl(String toobjFileUrl) {
        this.toobjFileUrl = toobjFileUrl;
        return this;
    }

    public String getToobjFileUrl() {
        return this.toobjFileUrl;
    }

    public DevelopStepLogDTO setToobjCultivateDate(java.util.Date toobjCultivateDate) {
        this.toobjCultivateDate = toobjCultivateDate;
        return this;
    }

    public java.util.Date getToobjCultivateDate() {
        return this.toobjCultivateDate;
    }

    public DevelopStepLogDTO setToobjCheckFileUrl(String toobjCheckFileUrl) {
        this.toobjCheckFileUrl = toobjCheckFileUrl;
        return this;
    }

    public String getToobjCheckFileUrl() {
        return this.toobjCheckFileUrl;
    }

    public DevelopStepLogDTO setToobjContextMem(String toobjContextMem) {
        this.toobjContextMem = toobjContextMem;
        return this;
    }

    public String getToobjContextMem() {
        return this.toobjContextMem;
    }

    public DevelopStepLogDTO setObjectDate(java.util.Date objectDate) {
        this.objectDate = objectDate;
        return this;
    }

    public java.util.Date getObjectDate() {
        return this.objectDate;
    }

    public DevelopStepLogDTO setCanncelDate(java.util.Date canncelDate) {
        this.canncelDate = canncelDate;
        return this;
    }

    public java.util.Date getCanncelDate() {
        return this.canncelDate;
    }

    public DevelopStepLogDTO setCanncelCode(String canncelCode) {
        this.canncelCode = canncelCode;
        return this;
    }

    public String getCanncelCode() {
        return this.canncelCode;
    }

    public DevelopStepLogDTO setCanncelName(String canncelName) {
        this.canncelName = canncelName;
        return this;
    }

    public String getCanncelName() {
        return this.canncelName;
    }

    public DevelopStepLogDTO setCreateTime(java.util.Date createTime) {
        this.createTime = createTime;
        return this;
    }

    public java.util.Date getCreateTime() {
        return this.createTime;
    }

    public DevelopStepLogDTO setUpdateTime(java.util.Date updateTime) {
        this.updateTime = updateTime;
        return this;
    }

    public java.util.Date getUpdateTime() {
        return this.updateTime;
    }

    public DevelopStepLogDTO setDeleteTime(java.util.Date deleteTime) {
        this.deleteTime = deleteTime;
        return this;
    }

    public java.util.Date getDeleteTime() {
        return this.deleteTime;
    }

    public DevelopStepLogDTO setTopartOathDateUrl(String topartOathDateUrl) {
        this.topartOathDateUrl = topartOathDateUrl;
        return this;
    }

    public String getTopartOathDateUrl() {
        return this.topartOathDateUrl;
    }

    public DevelopStepLogDTO setIsHistory(Integer isHistory) {
        this.isHistory = isHistory;
        return this;
    }

    public Integer getIsHistory() {
        return this.isHistory;
    }

    public DevelopStepLogDTO setUpdateAccount(String updateAccount) {
        this.updateAccount = updateAccount;
        return this;
    }

    public String getUpdateAccount() {
        return this.updateAccount;
    }

    public String getD89Code() {
        return d89Code;
    }

    public void setD89Code(String d89Code) {
        this.d89Code = d89Code;
    }

    public String getD89Name() {
        return d89Name;
    }

    public void setD89Name(String d89Name) {
        this.d89Name = d89Name;
    }

    public String getD01Code() {
        return d01Code;
    }

    public void setD01Code(String d01Code) {
        this.d01Code = d01Code;
    }

    public String getStatisticalUnit() {
        return statisticalUnit;
    }

    public void setStatisticalUnit(String statisticalUnit) {
        this.statisticalUnit = statisticalUnit;
    }

    public String getReadingCollege() {
        return readingCollege;
    }

    public void setReadingCollege(String readingCollege) {
        this.readingCollege = readingCollege;
    }

    public String getReadingProfessionalCode() {
        return readingProfessionalCode;
    }

    public void setReadingProfessionalCode(String readingProfessionalCode) {
        this.readingProfessionalCode = readingProfessionalCode;
    }

    public String getReadingProfessionalName() {
        return readingProfessionalName;
    }

    public void setReadingProfessionalName(String readingProfessionalName) {
        this.readingProfessionalName = readingProfessionalName;
    }

    public String getEducationalSystem() {
        return educationalSystem;
    }

    public void setEducationalSystem(String educationalSystem) {
        this.educationalSystem = educationalSystem;
    }

    public Date getEnterSchoolDate() {
        return enterSchoolDate;
    }

    public void setEnterSchoolDate(Date enterSchoolDate) {
        this.enterSchoolDate = enterSchoolDate;
    }

    public Date getApplyDate() {
        return applyDate;
    }

    public void setApplyDate(Date applyDate) {
        this.applyDate = applyDate;
    }

    public String getIdcard() {
        return idcard;
    }

    public void setIdcard(String idcard) {
        this.idcard = idcard;
    }

    public String getD06Code() {
        return d06Code;
    }

    public void setD06Code(String d06Code) {
        this.d06Code = d06Code;
    }

    public String getD06Name() {
        return d06Name;
    }

    public void setD06Name(String d06Name) {
        this.d06Name = d06Name;
    }

    public String getD48Code() {
        return d48Code;
    }

    public void setD48Code(String d48Code) {
        this.d48Code = d48Code;
    }

    public String getD48Name() {
        return d48Name;
    }

    public void setD48Name(String d48Name) {
        this.d48Name = d48Name;
    }

    public String getSexCode() {
        return sexCode;
    }

    public void setSexCode(String sexCode) {
        this.sexCode = sexCode;
    }

    public String getSexName() {
        return sexName;
    }

    public void setSexName(String sexName) {
        this.sexName = sexName;
    }

    public Date getBirthday() {
        return birthday;
    }

    public void setBirthday(Date birthday) {
        this.birthday = birthday;
    }

    public String getPhone() {
        return phone;
    }

    public void setPhone(String phone) {
        this.phone = phone;
    }

    public Integer getAge() {
        return age;
    }

    public void setAge(Integer age) {
        this.age = age;
    }

    public String getJobNatureCode() {
        return jobNatureCode;
    }

    public void setJobNatureCode(String jobNatureCode) {
        this.jobNatureCode = jobNatureCode;
    }

    public String getHomeAddress() {
        return homeAddress;
    }

    public void setHomeAddress(String homeAddress) {
        this.homeAddress = homeAddress;
    }

    public String getUnitInformation() {
        return unitInformation;
    }

    public void setUnitInformation(String unitInformation) {
        this.unitInformation = unitInformation;
    }

    public String getD04Code() {
        return d04Code;
    }

    public void setD04Code(String d04Code) {
        this.d04Code = d04Code;
    }

    public Integer getHasUnitProvince() {
        return hasUnitProvince;
    }

    public void setHasUnitProvince(Integer hasUnitProvince) {
        this.hasUnitProvince = hasUnitProvince;
    }

    public Integer getHasUnitStatistics() {
        return hasUnitStatistics;
    }

    public void setHasUnitStatistics(Integer hasUnitStatistics) {
        this.hasUnitStatistics = hasUnitStatistics;
    }

    public Date getLeaveOrgDate() {
        return leaveOrgDate;
    }

    public void setLeaveOrgDate(Date leaveOrgDate) {
        this.leaveOrgDate = leaveOrgDate;
    }

    public String getD154Code() {
        return d154Code;
    }

    public void setD154Code(String d154Code) {
        this.d154Code = d154Code;
    }

    public String getD154Name() {
        return d154Name;
    }

    public void setD154Name(String d154Name) {
        this.d154Name = d154Name;
    }

    public Integer getHasCalculationGrade() {
        return hasCalculationGrade;
    }

    public void setHasCalculationGrade(Integer hasCalculationGrade) {
        this.hasCalculationGrade = hasCalculationGrade;
    }

    public DevelopStepLog toModel() {
        DevelopStepLog model = new DevelopStepLog();
        model.setId(this.id);
        model.setCode(this.code);
        model.setEsId(this.esId);
        model.setName(this.name);
        model.setOrgName(this.orgName);
        model.setOrgZbCode(this.orgZbCode);
        model.setOrgCode(this.orgCode);
        model.setLogOrgCode(this.logOrgCode);
        model.setMemCode(this.memCode);
        model.setTopreIntroductionMem(this.topreIntroductionMem);
        model.setTopreFileUrl(this.topreFileUrl);
        model.setTopreJoinBookUrl(this.topreJoinBookUrl);
        model.setTopreCommitteeDate(this.topreCommitteeDate);
        model.setTopreJoinBookNum(this.topreJoinBookNum);
        model.setTopreJoinOrgDate(this.topreJoinOrgDate);
        model.setTopreCommitteeFileUrl(this.topreCommitteeFileUrl);
        model.setD08Name(this.d08Name);
        model.setD08Code(this.d08Code);
        model.setJoinOrgCode(this.joinOrgCode);
        model.setJoinOrgName(this.joinOrgName);
        model.setD11Code(this.d11Code);
        model.setD11Name(this.d11Name);
        model.setTopartIntroductionMem(this.topartIntroductionMem);
        model.setTopartFileUrl(this.topartFileUrl);
        model.setTopartTurnPartyDate(this.topartTurnPartyDate);
        model.setD28Name(this.d28Name);
        model.setD28Code(this.d28Code);
        model.setTopartCommitteeFileUrl(this.topartCommitteeFileUrl);
        model.setTopartCommitteeDate(this.topartCommitteeDate);
        model.setTopartOathDate(this.topartOathDate);
        model.setExtendPreparDate(this.extendPreparDate);
        model.setToactiveApplyScanFile(this.toactiveApplyScanFile);
        model.setToactiveContextPerson(this.toactiveContextPerson);
        model.setActiveDate(this.activeDate);
        model.setToobjFileUrl(this.toobjFileUrl);
        model.setToobjCultivateDate(this.toobjCultivateDate);
        model.setToobjCheckFileUrl(this.toobjCheckFileUrl);
        model.setToobjContextMem(this.toobjContextMem);
        model.setObjectDate(this.objectDate);
        model.setCanncelDate(this.canncelDate);
        model.setCanncelCode(this.canncelCode);
        model.setCanncelName(this.canncelName);
        model.setCreateTime(this.createTime);
        model.setUpdateTime(this.updateTime);
        model.setDeleteTime(this.deleteTime);
        model.setTopartOathDateUrl(this.topartOathDateUrl);
        model.setIsHistory(this.isHistory);
        model.setUpdateAccount(this.updateAccount);
        model.setD09Code(this.d09Code);
        model.setD09Name(this.d09Name);
        model.setOutBranchOrgName(this.getOutBranchOrgName());
        model.setD154Code(this.getD154Code());
        model.setD154Name(this.getD154Name());
        model.setHasCalculationGrade(this.getHasCalculationGrade());
        return model;
    }

    public Date getShortTrainingBeginTime() {
        return shortTrainingBeginTime;
    }

    public void setShortTrainingBeginTime(Date shortTrainingBeginTime) {
        this.shortTrainingBeginTime = shortTrainingBeginTime;
    }

    public Date getShortTrainingEndTime() {
        return shortTrainingEndTime;
    }

    public void setShortTrainingEndTime(Date shortTrainingEndTime) {
        this.shortTrainingEndTime = shortTrainingEndTime;
    }

    public Date getReviewConclusionTime() {
        return reviewConclusionTime;
    }

    public void setReviewConclusionTime(Date reviewConclusionTime) {
        this.reviewConclusionTime = reviewConclusionTime;
    }
}