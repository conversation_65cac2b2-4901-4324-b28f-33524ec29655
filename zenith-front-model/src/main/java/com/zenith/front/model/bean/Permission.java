package com.zenith.front.model.bean;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import java.util.Date;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableField;
import java.io.Serializable;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2021-07-11
 */
@TableName("sys_permission")
public class Permission extends Model<Permission> {

    private static final long serialVersionUID=1L;

    /**
     * 权限id,默认为自增长
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 权限描述不能为null
     */
    @TableField("des")
    private String des;

    /**
     * 创建时间,默认不能为null
     */
    @TableField("create_time")
    private Date createTime;

    /**
     * 1:界面权限 2:接口权限
     */
    @TableField("type")
    private Integer type;

    /**
     * 父级id
     */
    @TableField("parent_id")
    private Long parentId;


    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getDes() {
        return des;
    }

    public void setDes(String des) {
        this.des = des;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Integer getType() {
        return type;
    }

    public void setType(Integer type) {
        this.type = type;
    }

    public Long getParentId() {
        return parentId;
    }

    public void setParentId(Long parentId) {
        this.parentId = parentId;
    }

    @Override
    public Serializable pkVal() {
        return this.id;
    }

    @Override
    public String toString() {
        return "Permission{" +
        "id=" + id +
        ", des=" + des +
        ", createTime=" + createTime +
        ", type=" + type +
        ", parentId=" + parentId +
        "}";
    }
}
