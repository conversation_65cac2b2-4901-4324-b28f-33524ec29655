package com.zenith.front.model.bean;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import java.util.Date;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableField;
import java.io.Serializable;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2021-07-11
 */
@TableName("sys_operate_log")
public class OperateLog extends Model<OperateLog> {

    private static final long serialVersionUID=1L;

    /**
     * 主键UUID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 操作人员账户
     */
    @TableField("account")
    private String account;

    /**
     * 0是增加 1是修改 2删除 不能为null
     */
    @TableField("operate")
    private Integer operate;

    /**
     * 表名不能为null
     */
    @TableField("table_name")
    private String tableName;

    /**
     * sql语句,不能为null
     */
    @TableField("sql")
    private String sql;

    /**
     * 参数,默认是 json数组
     */
    @TableField("args")
    private String args;

    /**
     * 创建时间不能为null
     */
    @TableField("create_time")
    private Date createTime;


    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getAccount() {
        return account;
    }

    public void setAccount(String account) {
        this.account = account;
    }

    public Integer getOperate() {
        return operate;
    }

    public void setOperate(Integer operate) {
        this.operate = operate;
    }

    public String getTableName() {
        return tableName;
    }

    public void setTableName(String tableName) {
        this.tableName = tableName;
    }

    public String getSql() {
        return sql;
    }

    public void setSql(String sql) {
        this.sql = sql;
    }

    public String getArgs() {
        return args;
    }

    public void setArgs(String args) {
        this.args = args;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    @Override
    public Serializable pkVal() {
        return this.id;
    }

    @Override
    public String toString() {
        return "OperateLog{" +
        "id=" + id +
        ", account=" + account +
        ", operate=" + operate +
        ", tableName=" + tableName +
        ", sql=" + sql +
        ", args=" + args +
        ", createTime=" + createTime +
        "}";
    }
}
