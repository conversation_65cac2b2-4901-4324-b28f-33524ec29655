package com.zenith.front.model.dto;

import com.zenith.front.model.custom.HasSubordinateField;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.util.Date;
import java.util.List;

@Setter
@Getter
@ToString
public class MemFlow1DTO  extends HasSubordinateField {

    private Integer pageNum;
    private Integer pageSize;

    private String code;
    /**
     * 党员主键
     */
    private String memCode;
    /**
     * 姓名
     */
    private String memName;

    /**
     * 流出类型
     */
    private List<String> outPlaceCode;

    /**
     * 所在组织
     */
    private String memOrgCode;

    /**
     * 党支部层级码
     */
    private String outOrgBranchOrgCode;

    /**
     * 党组织所在行政区划
     */
    private List<String> outAdministrativeDivisionCode;

    /**
     * 流动类型:跨省（区、市）流动 省(区、市)内跨市（地、州、盟)流动 市(地、州、盟)内跨县（(市、区、旗)流动
     */
    private List<String> flowTypeCode;

    /**
     * 流回日期
     */
    private Date flowOutDate;

    /**
     *  1已流出（未纳入管理） 2已纳入支部管理 3流出被退回 4流出历史
     */
    private String flowType;
    /**
     * 1已完成流动  2已撤销流动  3已终止流动
     */
    private List<String> flowStep;
    /**
     * 外出日期开始
     */
    private Date startOutTime;
    /**
     * 外出日期结束
     */
    private Date endOutTime;
    /**
     * 数据创建日期-开始
     */
    private Date startCreateTime;
    /**
     * 数据创建日期-结束
     */
    private Date endCreateTime;
    /**
     * 登记日期-开始
     */
    private Date startRegisterTime;
    /**
     * 登记日期-结束
     */
    private Date endRegisterTime;
}
