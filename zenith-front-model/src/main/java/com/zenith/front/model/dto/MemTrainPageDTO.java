package com.zenith.front.model.dto;

import com.zenith.front.model.validate.group.Common1Group;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

import javax.validation.constraints.Max;
import javax.validation.constraints.Min;

/**
 * <AUTHOR>
 * @Date 2021/8/6 9:41
 * @Version 1.0
 */
@Data
@ToString
@NoArgsConstructor
@AllArgsConstructor
public class MemTrainPageDTO {
    private String orgCode;

    /**
     * 分页页数
     */
    @Min(value = 1, groups = {Common1Group.class}, message = "页码最小为1")
    private Integer pageNum;
    /**
     * 分页每页数
     */
    @Max(value = 100, groups = {Common1Group.class}, message = "每页条数大小范围在1-100")
    private Integer pageSize;
}
