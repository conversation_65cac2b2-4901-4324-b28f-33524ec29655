package com.zenith.front.model.vo;

import lombok.Data;

import java.util.Date;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2021-11-14
 */
@Data
public class OrgFlowAuditVo {

    /**
     * 唯一标识符
     */
    private String id;

    /**
     * 唯一标识符code
     */
    private String code;

    /**
     * 流动组织层级码
     */
    private String flowOrgCode;

    /**
     * 流动组织Code
     */
    private String flowOrgLevelCode;

    /**
     * 流动组织名称
     */
    private String name;

    /**
     * 是否需要中央交换区流转，1-是，0-否
     */
    private Integer isExchange;

    /**
     * 审批状态，0-待审批，1-审批通过，2-审批未通过
     */
    private String status;

    /**
     * 审批时间
     */
    private Date auditTime;

    /**
     * 审批人唯一标识
     */
    private String auditUserId;

    /**
     * 审批人名称
     */
    private String auditUserName;

    /**
     * 审批单位的组织code
     */
    private String approveCode;

    /**
     * 审批理由
     */
    private String reason;

    /**
     *
     */
    private Date createTime;

    /**
     *
     */
    private Date updateTime;

    /**
     *
     */
    private Date deleteTime;

    /**
     *
     */
    private String remark;

    /**
     *
     */
    private Long timestamp;
}
