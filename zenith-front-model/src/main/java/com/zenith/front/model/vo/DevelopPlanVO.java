package com.zenith.front.model.vo;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

/**
 * <AUTHOR>
 * @version 1.0
 * @Description: 发展指标
 * @date 2019/4/21 14:29
 */
@Data
@ToString
@NoArgsConstructor
@AllArgsConstructor
public class DevelopPlanVO {
    /**
     * 组织code
     */
    private String code;
    /**
     * 组织层级码
     */
    private String orgCode;
    /**
     * 组织名称
     */
    private String orgName;
    private Long totalNumber;
    private Long usedNumber;
    private Long undoneNumber;
    private String year;
    /**
     * 完成占比
     */
    private String percent;
    /**
     * 未分配指标(大于等于零)
     */
    private Long unfinishedNumber;
    /**
     * 实际未分配指标
     */
    private Long realityUnfinishedNumber;
}
