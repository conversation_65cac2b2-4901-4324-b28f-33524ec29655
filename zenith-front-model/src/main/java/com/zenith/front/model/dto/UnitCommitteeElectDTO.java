package com.zenith.front.model.dto;

import com.zenith.front.model.validate.group.AddGroup;
import com.zenith.front.model.validate.group.UpdateGroup;
import lombok.ToString;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.Date;

@ToString
public class UnitCommitteeElectDTO {

    /**
     *
     */
    @NotBlank(groups = UpdateGroup.class, message = "code 不能为空")
    private String code;
    /**
     * 单位标识
     */
    @NotBlank(groups = {AddGroup.class, UpdateGroup.class}, message = "unitCode 不能为空")
    private String unitCode;
    /**
     * 开始时间
     */
    @NotNull(groups = {AddGroup.class, UpdateGroup.class}, message = "tenureStartDate 不能为空")
    private java.util.Date tenureStartDate;
    /**
     * 结束时间
     */
    @NotNull(groups = {AddGroup.class, UpdateGroup.class}, message = "tenureEndDate 不能为空")
    private java.util.Date tenureEndDate;

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getUnitCode() {
        return unitCode;
    }

    public void setUnitCode(String unitCode) {
        this.unitCode = unitCode;
    }

    public Date getTenureStartDate() {
        return tenureStartDate;
    }

    public void setTenureStartDate(Date tenureStartDate) {
        this.tenureStartDate = tenureStartDate;
    }

    public Date getTenureEndDate() {
        return tenureEndDate;
    }

    public void setTenureEndDate(Date tenureEndDate) {
        this.tenureEndDate = tenureEndDate;
    }
}