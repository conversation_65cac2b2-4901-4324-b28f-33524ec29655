package com.zenith.front.model.dto;

import cn.afterturn.easypoi.excel.annotation.Excel;
import lombok.Data;

@Data
public class OrgLifeExcelDTO {
    @Excel(name = "序号", orderNum = "1", height = 8, width = 10, isImportField = "true_st")
    private Integer index;

    @Excel(name = "活动名称", orderNum = "2", height = 8, width = 20, isImportField = "true_st")
    private String activityName;

    @Excel(name = "创建党组织名称", orderNum = "3", height = 8, width = 30, isImportField = "true_st")
    private String orgName;

    @Excel(name = "参与党组织名称", orderNum = "4", height = 8, width = 30, isImportField = "true_st")
    private String joinOrgCodeListName;

    @Excel(name = "活动类型", orderNum = "5", height = 8, width = 30, isImportField = "true_st")
    private String d158Name;

    @Excel(name = "活动时间", orderNum = "6", height = 8, width = 25, isImportField = "true_st")
    private String activityTime;

    @Excel(name = "活动记录", orderNum = "7", height = 8, width = 50, isImportField = "true_st")
    private String activityRecord;
}
