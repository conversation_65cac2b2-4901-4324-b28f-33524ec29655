package com.zenith.front.model.bean;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.zenith.front.common.encrypt.typehandler.EncryptTypeHandler;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2021-10-26
 */
@TableName(value = "ccp_mem_develop_all", autoResultMap = true)
public class MemDevelopAll extends Model<MemDevelopAll> {

    private static final long serialVersionUID = 1L;

    /**
     * 唯一标识符
     */
    @TableId(value = "id", type = IdType.ASSIGN_UUID)
    private String id;

    /**
     * 发展党员唯一标识符
     */
    @TableField("code")
    private String code;

    /**
     * 发展党员es同步id
     */
    @TableField("es_id")
    private String esId;

    /**
     * 发展党员姓名
     */
    @TableField(value = "name", typeHandler = EncryptTypeHandler.class)
    private String name;

    /**
     * 发展党员姓名拼音
     */
    @TableField("pinyin")
    private String pinyin;

    /**
     * 发展党员身份证
     */
    @TableField(value = "idcard", typeHandler = EncryptTypeHandler.class)
    private String idcard;

    /**
     * 民族code
     */
    @TableField("d06_code")
    private String d06Code;

    /**
     * 民族名称
     */
    @TableField("d06_name")
    private String d06Name;

    /**
     * 籍贯
     */
    @TableField("d48_code")
    private String d48Code;

    /**
     * 籍贯名称
     */
    @TableField("d48_name")
    private String d48Name;

    /**
     * 性别
     */
    @TableField("sex_code")
    private String sexCode;

    /**
     * 性别name
     */
    @TableField("sex_name")
    private String sexName;

    /**
     * 出生日期
     */
    @TableField("birthday")
    private Date birthday;

    /**
     * 电话
     */
    @TableField(value = "phone", typeHandler = EncryptTypeHandler.class)
    private String phone;

    /**
     * 学历code
     */
    @TableField("d07_code")
    private String d07Code;

    /**
     * 学历名称
     */
    @TableField("d07_name")
    private String d07Name;

    /**
     * 工作岗位
     */
    @TableField("d09_code")
    private String d09Code;

    /**
     * 工作岗位名称
     */
    @TableField("d09_name")
    private String d09Name;

    /**
     * 发展党员年龄
     */
    @TableField("age")
    private Integer age;

    /**
     * 是否劳务派遣
     */
    @TableField("is_dispatch")
    private Integer isDispatch;

    /**
     * 一线情况code
     */
    @TableField("d21_code")
    private String d21Code;

    /**
     * 一线情况名称
     */
    @TableField("d21_name")
    private String d21Name;

    /**
     * 聘任专业技术职务名称
     */
    @TableField("d19_name")
    private String d19Name;

    /**
     * 聘任专业技术职务code
     */
    @TableField("d19_code")
    private String d19Code;

    /**
     * 新社会阶层类型名称
     */
    @TableField("d20_name")
    private String d20Name;

    /**
     * 新社会阶层类型code
     */
    @TableField("d20_code")
    private String d20Code;

    /**
     * 先进模范人物情况
     */
    @TableField("advanced_model_code")
    private String advancedModelCode;

    /**
     * 政治面貌
     */
    @TableField("politics_code")
    private String politicsCode;

    /**
     * 政治面貌名称
     */
    @TableField("politics_name")
    private String politicsName;

    /**
     * 是否死亡
     */
    @TableField("has_dead")
    private Integer hasDead;

    /**
     * 死亡时间
     */
    @TableField("dead_time")
    private Date deadTime;

    /**
     * 追认时间
     */
    @TableField("ratification_time")
    private Date ratificationTime;

    /**
     * 发展党员类型code
     */
    @TableField("d08_code")
    private String d08Code;

    /**
     * 发展党员类型名称
     */
    @TableField("d08_name")
    private String d08Name;

    /**
     * 发展党员单位类别_code
     */
    @TableField("d04_code")
    private String d04Code;

    /**
     * 发展党员单位类别_name
     */
    @TableField("d04_name")
    private String d04Name;

    /**
     * 发展党员单位code
     */
    @TableField("unit_code")
    private String unitCode;

    /**
     * 发展党员单位名称
     */
    @TableField("unit_name")
    private String unitName;

    /**
     * 创建时间
     */
    @TableField("create_time")
    private Date createTime;

    /**
     * 更新时间
     */
    @TableField("update_time")
    private Date updateTime;

    /**
     * 删除时间
     */
    @TableField("delete_time")
    private Date deleteTime;

    /**
     * 加入党组织类别name ?
     */
    @TableField("d27_name")
    private String d27Name;

    /**
     * 加入党组织类别code  (dict_d27) ?
     */
    @TableField("d27_code")
    private String d27Code;
    /**
     * 是否本年申请
     */
    @TableField("apply_date_curr_year")
    private Integer applyDateCurrYear;
    /**
     * 机构层级码
     */
    @TableField("develop_org_code")
    private String developOrgCode;
    /**
     * 管理党组织code
     */
    @TableField("org_code")
    private String orgCode;
    /**
     * 人事关系所在单位
     */
    @TableField("personnel_relations_unit")
    private String personnelRelationsUnit;

    /**
     * 人事关系所在单位类别
     */
    @TableField("personnel_d04_code")
    private String personnelD04Code;

    /**
     * 人事关系所在单位类别名称
     */
    @TableField("personnel_d04_name")
    private String personnelD04Name;

    /**
     * 统计单位
     */
    @TableField("statistical_unit")
    private String statisticalUnit;
    /**
     * 人事关系是否在党组织关联单位内(1是，0否)
     */
    @TableField("\"has_unit_statistics\"")
    private Integer hasUnitStatistics;
    /**
     * 人事关系所在单位名称
     */
    @TableField("unit_information")
    private String unitInformation;
    /**
     * 人事关系所在单位是否省内单位(1是，0否)
     */
    @TableField("has_unit_province")
    private Integer hasUnitProvince;

    /**
     * 乡镇，街道情况（1是乡，2是镇，3是街道）
     */
    @TableField("is_towns")
    private Integer isTowns;
    /***
     * 行政村，城市社区，乡镇社区情况（1行政村，2城市社区，3是乡镇社区）
     * */
    @TableField("is_community")
    private Integer isCommunity;

    /**
     * 是否农民工
     */
    @TableField("is_farmer")
    private Integer isFarmer;
    /**
     * 发展对象年度
     */
    @TableField("topre_join_org_date_year")
    private Integer topreJoinOrgDateYear;
    /**
     * 是否 在关系转接中
     */
    @TableField("is_transfer")
    private Integer isTransfer;

    /**
     * 国民经济行业CODE
     */
    @TableField("d194_code")
    private String d194Code;

    /**
     * 与国民经济行业name
     */
    @TableField("d194_name")
    private String d194Name;

    /**
     * 生产性服务行业CODE
     */
    @TableField("d195_code")
    private String d195Code;

    /**
     * 生产性服务行业name
     */
    @TableField("d195_name")
    private String d195Name;

    public String getD194Code() {
        return d194Code;
    }

    public void setD194Code(String d194Code) {
        this.d194Code = d194Code;
    }

    public String getD194Name() {
        return d194Name;
    }

    public void setD194Name(String d194Name) {
        this.d194Name = d194Name;
    }

    public String getD195Code() {
        return d195Code;
    }

    public void setD195Code(String d195Code) {
        this.d195Code = d195Code;
    }

    public String getD195Name() {
        return d195Name;
    }

    public void setD195Name(String d195Name) {
        this.d195Name = d195Name;
    }

    public Integer getIsTowns() {
        return isTowns;
    }

    public void setIsTowns(Integer isTowns) {
        this.isTowns = isTowns;
    }

    public Integer getIsCommunity() {
        return isCommunity;
    }

    public void setIsCommunity(Integer isCommunity) {
        this.isCommunity = isCommunity;
    }

    public Integer getIsFarmer() {
        return isFarmer;
    }

    public void setIsFarmer(Integer isFarmer) {
        this.isFarmer = isFarmer;
    }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getEsId() {
        return esId;
    }

    public void setEsId(String esId) {
        this.esId = esId;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getPinyin() {
        return pinyin;
    }

    public void setPinyin(String pinyin) {
        this.pinyin = pinyin;
    }

    public String getIdcard() {
        return idcard;
    }

    public void setIdcard(String idcard) {
        this.idcard = idcard;
    }


    public String getD06Code() {
        return d06Code;
    }

    public void setD06Code(String d06Code) {
        this.d06Code = d06Code;
    }

    public String getD06Name() {
        return d06Name;
    }

    public void setD06Name(String d06Name) {
        this.d06Name = d06Name;
    }

    public String getD48Code() {
        return d48Code;
    }

    public void setD48Code(String d48Code) {
        this.d48Code = d48Code;
    }

    public String getD48Name() {
        return d48Name;
    }

    public void setD48Name(String d48Name) {
        this.d48Name = d48Name;
    }

    public String getSexCode() {
        return sexCode;
    }

    public void setSexCode(String sexCode) {
        this.sexCode = sexCode;
    }

    public String getSexName() {
        return sexName;
    }

    public void setSexName(String sexName) {
        this.sexName = sexName;
    }

    public Date getBirthday() {
        return birthday;
    }

    public void setBirthday(Date birthday) {
        this.birthday = birthday;
    }

    public String getPhone() {
        return phone;
    }

    public void setPhone(String phone) {
        this.phone = phone;
    }


    public String getD07Code() {
        return d07Code;
    }

    public void setD07Code(String d07Code) {
        this.d07Code = d07Code;
    }

    public String getD07Name() {
        return d07Name;
    }

    public void setD07Name(String d07Name) {
        this.d07Name = d07Name;
    }

    public String getD09Code() {
        return d09Code;
    }

    public void setD09Code(String d09Code) {
        this.d09Code = d09Code;
    }

    public String getD09Name() {
        return d09Name;
    }

    public void setD09Name(String d09Name) {
        this.d09Name = d09Name;
    }

    public Integer getAge() {
        return age;
    }

    public void setAge(Integer age) {
        this.age = age;
    }

    public Integer getIsDispatch() {
        return isDispatch;
    }

    public void setIsDispatch(Integer isDispatch) {
        this.isDispatch = isDispatch;
    }


    public String getD21Code() {
        return d21Code;
    }

    public void setD21Code(String d21Code) {
        this.d21Code = d21Code;
    }

    public String getD21Name() {
        return d21Name;
    }

    public void setD21Name(String d21Name) {
        this.d21Name = d21Name;
    }

    public String getD19Name() {
        return d19Name;
    }

    public void setD19Name(String d19Name) {
        this.d19Name = d19Name;
    }

    public String getD19Code() {
        return d19Code;
    }

    public void setD19Code(String d19Code) {
        this.d19Code = d19Code;
    }

    public String getD20Name() {
        return d20Name;
    }

    public void setD20Name(String d20Name) {
        this.d20Name = d20Name;
    }

    public String getD20Code() {
        return d20Code;
    }

    public void setD20Code(String d20Code) {
        this.d20Code = d20Code;
    }

    public String getAdvancedModelCode() {
        return advancedModelCode;
    }

    public void setAdvancedModelCode(String advancedModelCode) {
        this.advancedModelCode = advancedModelCode;
    }

    public String getPoliticsCode() {
        return politicsCode;
    }

    public void setPoliticsCode(String politicsCode) {
        this.politicsCode = politicsCode;
    }

    public String getPoliticsName() {
        return politicsName;
    }

    public void setPoliticsName(String politicsName) {
        this.politicsName = politicsName;
    }

    public Integer getHasDead() {
        return hasDead;
    }

    public void setHasDead(Integer hasDead) {
        this.hasDead = hasDead;
    }

    public Date getDeadTime() {
        return deadTime;
    }

    public void setDeadTime(Date deadTime) {
        this.deadTime = deadTime;
    }

    public Date getRatificationTime() {
        return ratificationTime;
    }

    public void setRatificationTime(Date ratificationTime) {
        this.ratificationTime = ratificationTime;
    }


    public String getD08Code() {
        return d08Code;
    }

    public void setD08Code(String d08Code) {
        this.d08Code = d08Code;
    }

    public String getD08Name() {
        return d08Name;
    }

    public void setD08Name(String d08Name) {
        this.d08Name = d08Name;
    }

    public String getD04Code() {
        return d04Code;
    }

    public void setD04Code(String d04Code) {
        this.d04Code = d04Code;
    }

    public String getD04Name() {
        return d04Name;
    }

    public void setD04Name(String d04Name) {
        this.d04Name = d04Name;
    }

    public String getUnitCode() {
        return unitCode;
    }

    public void setUnitCode(String unitCode) {
        this.unitCode = unitCode;
    }

    public String getUnitName() {
        return unitName;
    }

    public void setUnitName(String unitName) {
        this.unitName = unitName;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    public Date getDeleteTime() {
        return deleteTime;
    }

    public void setDeleteTime(Date deleteTime) {
        this.deleteTime = deleteTime;
    }

    public String getD27Name() {
        return d27Name;
    }

    public void setD27Name(String d27Name) {
        this.d27Name = d27Name;
    }

    public String getD27Code() {
        return d27Code;
    }

    public void setD27Code(String d27Code) {
        this.d27Code = d27Code;
    }

    public Integer getApplyDateCurrYear() {
        return applyDateCurrYear;
    }

    public void setApplyDateCurrYear(Integer applyDateCurrYear) {
        this.applyDateCurrYear = applyDateCurrYear;
    }

    public String getDevelopOrgCode() {
        return developOrgCode;
    }

    public void setDevelopOrgCode(String developOrgCode) {
        this.developOrgCode = developOrgCode;
    }

    public String getOrgCode() {
        return orgCode;
    }

    public void setOrgCode(String orgCode) {
        this.orgCode = orgCode;
    }

    public String getPersonnelRelationsUnit() {
        return personnelRelationsUnit;
    }

    public void setPersonnelRelationsUnit(String personnelRelationsUnit) {
        this.personnelRelationsUnit = personnelRelationsUnit;
    }

    public String getPersonnelD04Code() {
        return personnelD04Code;
    }

    public void setPersonnelD04Code(String personnelD04Code) {
        this.personnelD04Code = personnelD04Code;
    }

    public String getPersonnelD04Name() {
        return personnelD04Name;
    }

    public void setPersonnelD04Name(String personnelD04Name) {
        this.personnelD04Name = personnelD04Name;
    }

    public String getStatisticalUnit() {
        return statisticalUnit;
    }

    public void setStatisticalUnit(String statisticalUnit) {
        this.statisticalUnit = statisticalUnit;
    }

    public Integer getHasUnitStatistics() {
        return hasUnitStatistics;
    }

    public void setHasUnitStatistics(Integer hasUnitStatistics) {
        this.hasUnitStatistics = hasUnitStatistics;
    }

    public String getUnitInformation() {
        return unitInformation;
    }

    public void setUnitInformation(String unitInformation) {
        this.unitInformation = unitInformation;
    }

    public Integer getHasUnitProvince() {
        return hasUnitProvince;
    }

    public void setHasUnitProvince(Integer hasUnitProvince) {
        this.hasUnitProvince = hasUnitProvince;
    }

    public Integer getTopreJoinOrgDateYear() {
        return topreJoinOrgDateYear;
    }

    public void setTopreJoinOrgDateYear(Integer topreJoinOrgDateYear) {
        this.topreJoinOrgDateYear = topreJoinOrgDateYear;
    }

    public Integer getIsTransfer() {
        return isTransfer;
    }

    public void setIsTransfer(Integer isTransfer) {
        this.isTransfer = isTransfer;
    }

    @Override
    protected Serializable pkVal() {
        return this.id;
    }

    @Override
    public String toString() {
        return "MemDevelopAll{" +
                "id=" + id +
                ", code=" + code +
                ", esId=" + esId +
                ", name=" + name +
                ", pinyin=" + pinyin +
                ", idcard=" + idcard +
                ", d06Code=" + d06Code +
                ", d06Name=" + d06Name +
                ", d48Code=" + d48Code +
                ", d48Name=" + d48Name +
                ", sexCode=" + sexCode +
                ", sexName=" + sexName +
                ", birthday=" + birthday +
                ", phone=" + phone +
                ", d07Code=" + d07Code +
                ", d07Name=" + d07Name +
                ", d09Code=" + d09Code +
                ", d09Name=" + d09Name +
                ", age=" + age +
                ", isDispatch=" + isDispatch +
                ", d21Code=" + d21Code +
                ", d21Name=" + d21Name +
                ", d19Name=" + d19Name +
                ", d19Code=" + d19Code +
                ", d20Name=" + d20Name +
                ", d20Code=" + d20Code +
                ", advancedModelCode=" + advancedModelCode +
                ", politicsCode=" + politicsCode +
                ", politicsName=" + politicsName +
                ", hasDead=" + hasDead +
                ", deadTime=" + deadTime +
                ", ratificationTime=" + ratificationTime +
                ", d08Code=" + d08Code +
                ", d08Name=" + d08Name +
                ", d04Code=" + d04Code +
                ", d04Name=" + d04Name +
                ", unitCode=" + unitCode +
                ", unitName=" + unitName +
                ", createTime=" + createTime +
                ", updateTime=" + updateTime +
                ", deleteTime=" + deleteTime +
                "}";
    }
}
