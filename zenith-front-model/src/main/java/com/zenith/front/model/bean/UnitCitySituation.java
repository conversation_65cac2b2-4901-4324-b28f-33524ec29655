package com.zenith.front.model.bean;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableField;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 * 城市基层党建情况
 * </p>
 *
 * <AUTHOR>
 * @since 2021-11-12
 */
@TableName("ccp_unit_city_situation")
public class UnitCitySituation extends Model<UnitCitySituation> {

    private static final long serialVersionUID = 1L;

    /**
     * 唯一key
     */
      @TableId(value = "code", type = IdType.ASSIGN_UUID)
    private String code;

    /**
     * es_id
     */
    @TableField("es_id")
    private String esId;

    /**
     * 关联单位_代码
     */
    @TableField("unit_code")
    private String unitCode;

    /**
     * 关联组织层级码
     */
    @TableField("org_code")
    private String orgCode;

    /**
     * 是否完成内设机构整合(1是 0否)
     */
    @TableField("has_complete_internal_institutions")
    private Integer hasCompleteInternalInstitutions;

    /**
     * 是否实现一支队伍执法(1是 0否)
     */
    @TableField("has_achieve_team_law")
    private Integer hasAchieveTeamLaw;

    /**
     * 是否与区级职能部门划清职权边界(1是 0否)
     */
    @TableField("has_draw_clear_power")
    private Integer hasDrawClearPower;

    /**
     * 是否依法赋予街道相应人事考核、征得同意、规划参与、综合管理、行政执法、应急管理、决策建议(1是 0否)
     */
    @TableField("has_legally_endowed")
    private Integer hasLegallyEndowed;

    /**
     * 是否取消协税护税、招商引资任务(1是 0否)
     */
    @TableField("has_cancel_task")
    private Integer hasCancelTask;

    /**
     * 是否建立“大工委”(1是 0否)
     */
    @TableField("has_build_committee")
    private Integer hasBuildCommittee;

    /**
     * 是否建立联席会议制度(1是 0否)
     */
    @TableField("has_establish_system")
    private Integer hasEstablishSystem;

    /**
     * 是否制定工作清单、建立准入制度的(1是 0否)
     */
    @TableField("has_establish_work_system")
    private Integer hasEstablishWorkSystem;

    /**
     * 是否依法赋予社区对辖区单位和个人评价权、建议权(1是 0否)
     */
    @TableField("has_grant_power")
    private Integer hasGrantPower;

    /**
     * 社区类型（1.老旧型，2.新建型，3.工矿型，4.商圈型，5.易扶型，6.其他型）
     */
    @TableField("d131_code")
    private String d131Code;

    /**
     * 社区类型名称
     */
    @TableField("d131_name")
    private String d131Name;

    /**
     * 社区志愿服务类社会组织数
     */
    @TableField("community_service_organizations")
    private Integer communityServiceOrganizations;

    /**
     * 社区志愿者人数
     */
    @TableField("community_volunteers")
    private Integer communityVolunteers;

    /**
     * 社区居民人口总数
     */
    @TableField("resident_population_community")
    private Integer residentPopulationCommunity;

    /**
     * 是否建立党群服务中心(1是 0否)
     */
    @TableField("has_establish_service_center")
    private Integer hasEstablishServiceCenter;

    /**
     * 居民小区建立党群服务驿站数
     */
    @TableField("community_service_stations")
    private Integer communityServiceStations;

    /**
     * 是否实行“一站式服务”“一门式办理”(1是 0否)
     */
    @TableField("has_implement_service_management")
    private Integer hasImplementServiceManagement;

    /**
     * 是否建设“智慧社区”(1是 0否)
     */
    @TableField("has_build_smart_community")
    private Integer hasBuildSmartCommunity;

    /**
     * 驻社区的机关企事业单位数
     */
    @TableField("community_institutions_enterprises")
    private Integer communityInstitutionsEnterprises;

    /**
     * 驻社区机关企事业单位到社区报到服务数
     */
    @TableField("community_reporting_services")
    private Integer communityReportingServices;

    /**
     * 驻社区的非公和社会组织数
     */
    @TableField("non_public_community_organizations")
    private Integer nonPublicCommunityOrganizations;

    /**
     * 驻社区非公和社会组织到社区报到服务数
     */
    @TableField("non_public_organizations_services")
    private Integer nonPublicOrganizationsServices;

    /**
     * 驻社区的机关企事业单位、非公和社会组织党员人数
     */
    @TableField("members_on_public_organizations")
    private Integer membersOnPublicOrganizations;

    /**
     * 商务楼宇数
     */
    @TableField("commercial_buildings")
    private Integer commercialBuildings;

    /**
     * 建立楼宇党群服务中心的商务楼宇数
     */
    @TableField("organizations_commercial_buildings")
    private Integer organizationsCommercialBuildings;

    /**
     * 商务楼宇配备党务工作者数
     */
    @TableField("commercial_buildings_workers")
    private Integer commercialBuildingsWorkers;

    /**
     * 商务楼宇选派党建指导员数
     */
    @TableField("commercial_buildings_instructor")
    private Integer commercialBuildingsInstructor;

    /**
     * 纳税500万元至1000万元商务楼宇数
     */
    @TableField("five_to_one_buildings")
    private Integer fiveToOneBuildings;

    /**
     * 纳税1000万元以上商务楼宇数:
     */
    @TableField("one_more_than_buildings")
    private Integer oneMoreThanBuildings;

    /**
     * 划分网格数
     */
    @TableField("grids")
    private Integer grids;

    /**
     * 配备网格员数
     */
    @TableField("grid_members")
    private Integer gridMembers;

    /**
     * 建立网格党组织数
     */
    @TableField("organization_party_grid")
    private Integer organizationPartyGrid;

    /**
     * 是否完成将各类网格整合成为综合网格，形成基层治理“一张网”(1是 0否)
     */
    @TableField("has_complete_net")
    private Integer hasCompleteNet;

    /**
     * 是否建立“大党委”的社区(1是 0否)
     */
    @TableField("has_large_party_community")
    private Integer hasLargePartyCommunity;

    /**
     * 是否建立联席会议制度的社区(1是 0否)
     */
    @TableField("has_meet_system_community")
    private Integer hasMeetSystemCommunity;

    /**
     * 居民小区数
     */
    @TableField("residential_areas")
    private Integer residentialAreas;

    /**
     * 物业管理的小区数
     */
    @TableField("tube_plots")
    private Integer tubePlots;

    /**
     * 成立党组织的物业企业数
     */
    @TableField("organization_companies")
    private Integer organizationCompanies;

    /**
     * 成立业委会的小区数
     */
    @TableField("industry_authority_community")
    private Integer industryAuthorityCommunity;

    /**
     * 成立业委会党组织数
     */
    @TableField("industry_authority_organization")
    private Integer industryAuthorityOrganization;

    /**
     * 建立社区党组织领导下，居委会、物业企业、业委会“三方共议”“双向进入”的小区数:
     */
    @TableField("three_parties_communities")
    private Integer threePartiesCommunities;

    /**
     * 创建时间
     */
    @TableField("create_time")
    private Date createTime;

    @TableField("update_time")
    private Date updateTime;

    @TableField("delete_time")
    private Date deleteTime;

    /**
     * 建立党组织的商务楼宇数
     */
    @TableField("commercial_buildings_party_organizations")
    private Integer commercialBuildingsPartyOrganizations;

    /**
     * 在居民住宅小区、楼院等划分的网格数
     */
    @TableField("jmzzlyWgs")
    private Integer jmzzlyWgs;

    /**
     * 在居民住宅小区、楼院等划分已建立党组织的网格数
     */
    @TableField("jmzzlyDzWgs")
    private Integer jmzzlyDzWgs;

    /**
     * 在商务楼宇、商圈市场等单独划定的网格数
     */
    @TableField("swsqWgs")
    private Integer swsqWgs;

    /**
     * 在商务楼宇、商圈市场等单独划定已建立党组织的网格数
     */
    @TableField("swsqDzWgs")
    private Integer swsqDzWgs;

    /**
     * 街道（乡镇）领导班子成员直接联系的网格数
     */
    @TableField("jdldWgs")
    private Integer jdldWgs;

    /**
     * 由社区工作者担任的专职网格员数
     */
    @TableField("zzWgys")
    private Integer zzWgys;

    /**
     * 全部专职网格员年工资总额（万元）
     */
    @TableField("zzNgzze")
    private Integer zzNgzze;

    /**
     * 配备兼职网格员数
     */
    @TableField("jzWgys")
    private Integer jzWgys;

    public Integer getJmzzlyWgs() {
        return jmzzlyWgs;
    }

    public void setJmzzlyWgs(Integer jmzzlyWgs) {
        this.jmzzlyWgs = jmzzlyWgs;
    }

    public Integer getJmzzlyDzWgs() {
        return jmzzlyDzWgs;
    }

    public void setJmzzlyDzWgs(Integer jmzzlyDzWgs) {
        this.jmzzlyDzWgs = jmzzlyDzWgs;
    }

    public Integer getSwsqWgs() {
        return swsqWgs;
    }

    public void setSwsqWgs(Integer swsqWgs) {
        this.swsqWgs = swsqWgs;
    }

    public Integer getSwsqDzWgs() {
        return swsqDzWgs;
    }

    public void setSwsqDzWgs(Integer swsqDzWgs) {
        this.swsqDzWgs = swsqDzWgs;
    }

    public Integer getJdldWgs() {
        return jdldWgs;
    }

    public void setJdldWgs(Integer jdldWgs) {
        this.jdldWgs = jdldWgs;
    }

    public Integer getZzWgys() {
        return zzWgys;
    }

    public void setZzWgys(Integer zzWgys) {
        this.zzWgys = zzWgys;
    }

    public Integer getZzNgzze() {
        return zzNgzze;
    }

    public void setZzNgzze(Integer zzNgzze) {
        this.zzNgzze = zzNgzze;
    }

    public Integer getJzWgys() {
        return jzWgys;
    }

    public void setJzWgys(Integer jzWgys) {
        this.jzWgys = jzWgys;
    }

    public Integer getCommercialBuildingsPartyOrganizations() {
        return commercialBuildingsPartyOrganizations;
    }

    public void setCommercialBuildingsPartyOrganizations(Integer commercialBuildingsPartyOrganizations) {
        this.commercialBuildingsPartyOrganizations = commercialBuildingsPartyOrganizations;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getEsId() {
        return esId;
    }

    public void setEsId(String esId) {
        this.esId = esId;
    }

    public String getUnitCode() {
        return unitCode;
    }

    public void setUnitCode(String unitCode) {
        this.unitCode = unitCode;
    }

    public String getOrgCode() {
        return orgCode;
    }

    public void setOrgCode(String orgCode) {
        this.orgCode = orgCode;
    }

    public Integer getHasCompleteInternalInstitutions() {
        return hasCompleteInternalInstitutions;
    }

    public void setHasCompleteInternalInstitutions(Integer hasCompleteInternalInstitutions) {
        this.hasCompleteInternalInstitutions = hasCompleteInternalInstitutions;
    }

    public Integer getHasAchieveTeamLaw() {
        return hasAchieveTeamLaw;
    }

    public void setHasAchieveTeamLaw(Integer hasAchieveTeamLaw) {
        this.hasAchieveTeamLaw = hasAchieveTeamLaw;
    }

    public Integer getHasDrawClearPower() {
        return hasDrawClearPower;
    }

    public void setHasDrawClearPower(Integer hasDrawClearPower) {
        this.hasDrawClearPower = hasDrawClearPower;
    }

    public Integer getHasLegallyEndowed() {
        return hasLegallyEndowed;
    }

    public void setHasLegallyEndowed(Integer hasLegallyEndowed) {
        this.hasLegallyEndowed = hasLegallyEndowed;
    }

    public Integer getHasCancelTask() {
        return hasCancelTask;
    }

    public void setHasCancelTask(Integer hasCancelTask) {
        this.hasCancelTask = hasCancelTask;
    }

    public Integer getHasBuildCommittee() {
        return hasBuildCommittee;
    }

    public void setHasBuildCommittee(Integer hasBuildCommittee) {
        this.hasBuildCommittee = hasBuildCommittee;
    }

    public Integer getHasEstablishSystem() {
        return hasEstablishSystem;
    }

    public void setHasEstablishSystem(Integer hasEstablishSystem) {
        this.hasEstablishSystem = hasEstablishSystem;
    }

    public Integer getHasEstablishWorkSystem() {
        return hasEstablishWorkSystem;
    }

    public void setHasEstablishWorkSystem(Integer hasEstablishWorkSystem) {
        this.hasEstablishWorkSystem = hasEstablishWorkSystem;
    }

    public Integer getHasGrantPower() {
        return hasGrantPower;
    }

    public void setHasGrantPower(Integer hasGrantPower) {
        this.hasGrantPower = hasGrantPower;
    }

    public String getd131Code() {
        return d131Code;
    }

    public void setd131Code(String d131Code) {
        this.d131Code = d131Code;
    }

    public String getd131Name() {
        return d131Name;
    }

    public void setd131Name(String d131Name) {
        this.d131Name = d131Name;
    }

    public Integer getCommunityServiceOrganizations() {
        return communityServiceOrganizations;
    }

    public void setCommunityServiceOrganizations(Integer communityServiceOrganizations) {
        this.communityServiceOrganizations = communityServiceOrganizations;
    }

    public Integer getCommunityVolunteers() {
        return communityVolunteers;
    }

    public void setCommunityVolunteers(Integer communityVolunteers) {
        this.communityVolunteers = communityVolunteers;
    }

    public Integer getResidentPopulationCommunity() {
        return residentPopulationCommunity;
    }

    public void setResidentPopulationCommunity(Integer residentPopulationCommunity) {
        this.residentPopulationCommunity = residentPopulationCommunity;
    }

    public Integer getHasEstablishServiceCenter() {
        return hasEstablishServiceCenter;
    }

    public void setHasEstablishServiceCenter(Integer hasEstablishServiceCenter) {
        this.hasEstablishServiceCenter = hasEstablishServiceCenter;
    }

    public Integer getCommunityServiceStations() {
        return communityServiceStations;
    }

    public void setCommunityServiceStations(Integer communityServiceStations) {
        this.communityServiceStations = communityServiceStations;
    }

    public Integer getHasImplementServiceManagement() {
        return hasImplementServiceManagement;
    }

    public void setHasImplementServiceManagement(Integer hasImplementServiceManagement) {
        this.hasImplementServiceManagement = hasImplementServiceManagement;
    }

    public Integer getHasBuildSmartCommunity() {
        return hasBuildSmartCommunity;
    }

    public void setHasBuildSmartCommunity(Integer hasBuildSmartCommunity) {
        this.hasBuildSmartCommunity = hasBuildSmartCommunity;
    }

    public Integer getCommunityInstitutionsEnterprises() {
        return communityInstitutionsEnterprises;
    }

    public void setCommunityInstitutionsEnterprises(Integer communityInstitutionsEnterprises) {
        this.communityInstitutionsEnterprises = communityInstitutionsEnterprises;
    }

    public Integer getCommunityReportingServices() {
        return communityReportingServices;
    }

    public void setCommunityReportingServices(Integer communityReportingServices) {
        this.communityReportingServices = communityReportingServices;
    }

    public Integer getNonPublicCommunityOrganizations() {
        return nonPublicCommunityOrganizations;
    }

    public void setNonPublicCommunityOrganizations(Integer nonPublicCommunityOrganizations) {
        this.nonPublicCommunityOrganizations = nonPublicCommunityOrganizations;
    }

    public Integer getNonPublicOrganizationsServices() {
        return nonPublicOrganizationsServices;
    }

    public void setNonPublicOrganizationsServices(Integer nonPublicOrganizationsServices) {
        this.nonPublicOrganizationsServices = nonPublicOrganizationsServices;
    }

    public Integer getMembersOnPublicOrganizations() {
        return membersOnPublicOrganizations;
    }

    public void setMembersOnPublicOrganizations(Integer membersOnPublicOrganizations) {
        this.membersOnPublicOrganizations = membersOnPublicOrganizations;
    }

    public Integer getCommercialBuildings() {
        return commercialBuildings;
    }

    public void setCommercialBuildings(Integer commercialBuildings) {
        this.commercialBuildings = commercialBuildings;
    }

    public Integer getOrganizationsCommercialBuildings() {
        return organizationsCommercialBuildings;
    }

    public void setOrganizationsCommercialBuildings(Integer organizationsCommercialBuildings) {
        this.organizationsCommercialBuildings = organizationsCommercialBuildings;
    }

    public Integer getCommercialBuildingsWorkers() {
        return commercialBuildingsWorkers;
    }

    public void setCommercialBuildingsWorkers(Integer commercialBuildingsWorkers) {
        this.commercialBuildingsWorkers = commercialBuildingsWorkers;
    }

    public Integer getCommercialBuildingsInstructor() {
        return commercialBuildingsInstructor;
    }

    public void setCommercialBuildingsInstructor(Integer commercialBuildingsInstructor) {
        this.commercialBuildingsInstructor = commercialBuildingsInstructor;
    }

    public Integer getFiveToOneBuildings() {
        return fiveToOneBuildings;
    }

    public void setFiveToOneBuildings(Integer fiveToOneBuildings) {
        this.fiveToOneBuildings = fiveToOneBuildings;
    }

    public Integer getOneMoreThanBuildings() {
        return oneMoreThanBuildings;
    }

    public void setOneMoreThanBuildings(Integer oneMoreThanBuildings) {
        this.oneMoreThanBuildings = oneMoreThanBuildings;
    }

    public Integer getGrids() {
        return grids;
    }

    public void setGrids(Integer grids) {
        this.grids = grids;
    }

    public Integer getGridMembers() {
        return gridMembers;
    }

    public void setGridMembers(Integer gridMembers) {
        this.gridMembers = gridMembers;
    }

    public Integer getOrganizationPartyGrid() {
        return organizationPartyGrid;
    }

    public void setOrganizationPartyGrid(Integer organizationPartyGrid) {
        this.organizationPartyGrid = organizationPartyGrid;
    }

    public Integer getHasCompleteNet() {
        return hasCompleteNet;
    }

    public void setHasCompleteNet(Integer hasCompleteNet) {
        this.hasCompleteNet = hasCompleteNet;
    }

    public Integer getHasLargePartyCommunity() {
        return hasLargePartyCommunity;
    }

    public void setHasLargePartyCommunity(Integer hasLargePartyCommunity) {
        this.hasLargePartyCommunity = hasLargePartyCommunity;
    }

    public Integer getHasMeetSystemCommunity() {
        return hasMeetSystemCommunity;
    }

    public void setHasMeetSystemCommunity(Integer hasMeetSystemCommunity) {
        this.hasMeetSystemCommunity = hasMeetSystemCommunity;
    }

    public Integer getResidentialAreas() {
        return residentialAreas;
    }

    public void setResidentialAreas(Integer residentialAreas) {
        this.residentialAreas = residentialAreas;
    }

    public Integer getTubePlots() {
        return tubePlots;
    }

    public void setTubePlots(Integer tubePlots) {
        this.tubePlots = tubePlots;
    }

    public Integer getOrganizationCompanies() {
        return organizationCompanies;
    }

    public void setOrganizationCompanies(Integer organizationCompanies) {
        this.organizationCompanies = organizationCompanies;
    }

    public Integer getIndustryAuthorityCommunity() {
        return industryAuthorityCommunity;
    }

    public void setIndustryAuthorityCommunity(Integer industryAuthorityCommunity) {
        this.industryAuthorityCommunity = industryAuthorityCommunity;
    }

    public Integer getIndustryAuthorityOrganization() {
        return industryAuthorityOrganization;
    }

    public void setIndustryAuthorityOrganization(Integer industryAuthorityOrganization) {
        this.industryAuthorityOrganization = industryAuthorityOrganization;
    }

    public Integer getThreePartiesCommunities() {
        return threePartiesCommunities;
    }

    public void setThreePartiesCommunities(Integer threePartiesCommunities) {
        this.threePartiesCommunities = threePartiesCommunities;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    public Date getDeleteTime() {
        return deleteTime;
    }

    public void setDeleteTime(Date deleteTime) {
        this.deleteTime = deleteTime;
    }

    @Override
    protected Serializable pkVal() {
        return this.code;
    }

    @Override
    public String toString() {
        return "UnitCitySituation{" +
        "code=" + code +
        ", esId=" + esId +
        ", unitCode=" + unitCode +
        ", orgCode=" + orgCode +
        ", hasCompleteInternalInstitutions=" + hasCompleteInternalInstitutions +
        ", hasAchieveTeamLaw=" + hasAchieveTeamLaw +
        ", hasDrawClearPower=" + hasDrawClearPower +
        ", hasLegallyEndowed=" + hasLegallyEndowed +
        ", hasCancelTask=" + hasCancelTask +
        ", hasBuildCommittee=" + hasBuildCommittee +
        ", hasEstablishSystem=" + hasEstablishSystem +
        ", hasEstablishWorkSystem=" + hasEstablishWorkSystem +
        ", hasGrantPower=" + hasGrantPower +
        ", d131Code=" + d131Code +
        ", d131Name=" + d131Name +
        ", communityServiceOrganizations=" + communityServiceOrganizations +
        ", communityVolunteers=" + communityVolunteers +
        ", residentPopulationCommunity=" + residentPopulationCommunity +
        ", hasEstablishServiceCenter=" + hasEstablishServiceCenter +
        ", communityServiceStations=" + communityServiceStations +
        ", hasImplementServiceManagement=" + hasImplementServiceManagement +
        ", hasBuildSmartCommunity=" + hasBuildSmartCommunity +
        ", communityInstitutionsEnterprises=" + communityInstitutionsEnterprises +
        ", communityReportingServices=" + communityReportingServices +
        ", nonPublicCommunityOrganizations=" + nonPublicCommunityOrganizations +
        ", nonPublicOrganizationsServices=" + nonPublicOrganizationsServices +
        ", membersOnPublicOrganizations=" + membersOnPublicOrganizations +
        ", commercialBuildings=" + commercialBuildings +
        ", organizationsCommercialBuildings=" + organizationsCommercialBuildings +
        ", commercialBuildingsWorkers=" + commercialBuildingsWorkers +
        ", commercialBuildingsInstructor=" + commercialBuildingsInstructor +
        ", fiveToOneBuildings=" + fiveToOneBuildings +
        ", oneMoreThanBuildings=" + oneMoreThanBuildings +
        ", grids=" + grids +
        ", gridMembers=" + gridMembers +
        ", organizationPartyGrid=" + organizationPartyGrid +
        ", hasCompleteNet=" + hasCompleteNet +
        ", hasLargePartyCommunity=" + hasLargePartyCommunity +
        ", hasMeetSystemCommunity=" + hasMeetSystemCommunity +
        ", residentialAreas=" + residentialAreas +
        ", tubePlots=" + tubePlots +
        ", organizationCompanies=" + organizationCompanies +
        ", industryAuthorityCommunity=" + industryAuthorityCommunity +
        ", industryAuthorityOrganization=" + industryAuthorityOrganization +
        ", threePartiesCommunities=" + threePartiesCommunities +
        ", createTime=" + createTime +
        ", updateTime=" + updateTime +
        ", deleteTime=" + deleteTime +
        "}";
    }
}
