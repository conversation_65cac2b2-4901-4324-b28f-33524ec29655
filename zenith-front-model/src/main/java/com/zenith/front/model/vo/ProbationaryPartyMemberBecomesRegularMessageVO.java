package com.zenith.front.model.vo;

import com.zenith.front.common.encrypt.annotation.EncryptEnabled;
import com.zenith.front.common.encrypt.annotation.EncryptField;

import java.util.Date;

/**
 * 预备党员转正提醒 实体类
 *
 * <AUTHOR>
 * @version V1.0
 * @date 2022/7/28 15:27
 */
@EncryptEnabled
public class ProbationaryPartyMemberBecomesRegularMessageVO {

    @EncryptField(order = 1)
    private String name;

    private Date extendPreparDate;

    private String memOrgCode;

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public Date getExtendPreparDate() {
        return extendPreparDate;
    }

    public void setExtendPreparDate(Date extendPreparDate) {
        this.extendPreparDate = extendPreparDate;
    }

    public String getMemOrgCode() {
        return memOrgCode;
    }

    public void setMemOrgCode(String memOrgCode) {
        this.memOrgCode = memOrgCode;
    }

    @Override
    public String toString() {
        return "probationaryPartyMemberBecomesRegularVO{" +
                "name='" + name + '\'' +
                ", extendPreparDate=" + extendPreparDate +
                ", memOrgCode='" + memOrgCode + '\'' +
                '}';
    }
}
