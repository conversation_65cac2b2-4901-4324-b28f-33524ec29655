package com.zenith.front.model.bean;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2021-11-14
 */
@EqualsAndHashCode(callSuper = true)
@Data
@TableName(value = "ccp_mem_flow_sign_audit", autoResultMap = true)
public class MemFlowSignAudit extends Model<MemFlowSignAudit> {

    private static final long serialVersionUID = 1L;

    /**
     * 唯一标识符
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 唯一标识符code
     */
    @TableField("code")
    private String code;

    /**
     * 流动党员code
     */
    @TableField("sign_code")
    private String signCode;

    /**
     * 入党申请人code
     */
    @TableField("flow_sign_code")
    private String flowSignCode;

    /**
     * 党组织记录code
     */
    @TableField("flow_sign_records_code")
    private String flowSignRecordsCode;

    /**
     * 流动组织Code
     */
    @TableField("flow_org_code")
    private String flowOrgCode;

    /**
     * 流动组织名称
     */
    @TableField("flow_org_name")
    private String flowOrgName;

    /**
     * 审批时间
     */
    @TableField("audit_time")
    private Date auditTime;

    /**
     * 审批人唯一标识
     */
    @TableField("audit_user_id")
    private String auditUserId;

    /**
     * 审批人名称
     */
    @TableField("audit_user_name")
    private String auditUserName;

    /**
     * 审批人组织ID
     */
    @TableField("audit_org_id")
    private String auditOrgId;

    /**
     * 审批人组织层级码
     */
    @TableField("audit_org_code")
    private String auditOrgCode;

    /**
     * 审批人组织名称
     */
    @TableField("audit_org_name")
    private String auditOrgName;

    /**
     * 审批状态，0-待审批，1-审批通过，2-审批未通过
     */
    @TableField("status")
    private String status;

    /**
     * 拒绝原因，1-党员不存在，2-党员组织关系转接中，3-党员信息流转中
     */
    @TableField("refuse")
    private String refuse;

    /**
     * 说明理由
     */
    @TableField("reason")
    private String reason;

    /**
     *
     */
    @TableField("create_time")
    private Date createTime;

    /**
     *
     */
    @TableField("update_time")
    private Date updateTime;

    /**
     *
     */
    @TableField("delete_time")
    private Date deleteTime;

    /**
     *
     */
    @TableField("remark")
    private String remark;

    /**
     *
     */
    @TableField("timestamp")
    private Date timestamp;

    @Override
    public Serializable pkVal() {
        return this.id;
    }
}
