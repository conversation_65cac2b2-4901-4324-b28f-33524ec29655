package com.zenith.front.model.vo;

import com.zenith.front.model.custom.Record;
import com.zenith.front.model.validate.group.DeleteGroup;
import com.zenith.front.model.validate.group.MemGroup;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.Date;
import java.util.List;

@Data
@ToString
@NoArgsConstructor
@AllArgsConstructor
public class MemVO {

    /**
     * 自增ID
     */
    private Long id;
    /**
     * 唯一主键
     */
    @NotBlank(groups = DeleteGroup.class, message = "code 不能为空")
    private String code;
    /**
     * es_ides唯一主键
     */
    private String esId;
    /**
     * 姓名
     */
    @NotBlank(groups = MemGroup.class, message = "name 不能为空")
    private String name;
    /**
     * 拼音
     */
    private String pinyin;
    /**
     * 身份证
     */
    @NotBlank(groups = MemGroup.class, message = "idcard 不能为空")
    private String idcard;
    /**
     * 组织层级code
     */
    @NotBlank(groups = DeleteGroup.class, message = "code 不能为空")
    private String memOrgCode;
    /**
     * 组织zb_code
     */
    private String orgZbCode;
    /**
     * 组织唯一标识符
     */
    @NotBlank(groups = MemGroup.class, message = "orgCode 不能为空")
    private String orgCode;
    /**
     * 组织名
     */
    private String orgName;
    /**
     * 联系电话
     */
    @NotBlank(groups = MemGroup.class, message = "phone 不能为空")
    private String phone;
    /**
     * 其他电话
     */
    private String otherTel;
    /**
     * 党员照片
     */
    private String photo;
    /**
     * 预备党员日期(入党时间)
     */
    @NotNull(groups = MemGroup.class, message = "joinOrgDate 不能为空")
    private Date joinOrgDate;
    /**
     * 转为正式党员日期
     */
    private Date fullMemberDate;
    /**
     * 党费交纳情况name
     */
    @NotBlank(groups = MemGroup.class, message = "d49Name 不能为空")
    private String d49Name;
    /**
     * 党费交纳情况code
     */
    @NotBlank(groups = MemGroup.class, message = "d49Code 不能为空")
    private String d49Code;
    /**
     * 党费参考标准
     */
    private Double duesPrice;
    /**
     * 最后缴费时间
     */
    private Date lastPayDate;
    /**
     * 党费缴纳金额
     */
    private Double duesPaid;
    /**
     * 中组部主键
     */
    private String memZbKey;
    /**
     * 中组部人员扩展信息主键
     */
    private String memExtendZbKey;
    /**
     * 创建时间
     */
    private Date createTime;
    /**
     * 备注
     */
    private String remark;
    private Date timestamp;
    /**
     * 更新时间
     */
    private Date updateTime;
    /**
     * 删除时间
     */
    private Date deleteTime;
    /**
     * 婚姻状况
     */
    private String marryCode;
    /**
     * 参加工作日期
     */
    private Date joinWorkDate;
    /**
     * 档案管理单位名称
     */
    private String archiveUnit;
    /**
     * 家庭住址
     */
    @NotBlank(groups = MemGroup.class, message = "homeAddress 不能为空")
    private String homeAddress;
    /**
     * 离开党组织的类别name
     */
    @NotBlank(groups = DeleteGroup.class, message = "d12Name 不能为空")
    private String d12Name;
    /**
     * 离开党组织的类别
     */
    @NotBlank(groups = DeleteGroup.class, message = "d12Code 不能为空")
    private String d12Code;
    /**
     * 离开党组织日期[LeaveDate]
     */
    @NotNull(groups = DeleteGroup.class, message = "leaveOrgDate 不能为空")
    private Date leaveOrgDate;
    /**
     * 离开党组织时职务级别name
     */
    private String d51Name;
    /**
     * 离开党组织时职务级别代码 dict_d29
     */
    private String d51Code;
    /**
     * 出党原因name
     */
    private String d50Name;
    /**
     * 出党原因  dict_leave_reason
     */
    private String d50Code;
    /**
     * 失去联系日期[LostContactDate]
     */
    private Date lostContactDate;
    /**
     * 失去联系类型 dict_d18
     */
    private String d18Code;
    /**
     * dict_d18
     */
    private String d18Name;
    /**
     * 党小组
     */
    private String orgGroupId;
    /**
     * 党小组名称
     */
    private String orgGroupName;
    /**
     * 聘任专业技术职务名称
     */
    @NotBlank(groups = MemGroup.class, message = "d19Name 不能为空")
    private String d19Name;
    /**
     * 聘任专业技术职务code
     */
    @NotBlank(groups = MemGroup.class, message = "d19Code 不能为空")
    private String d19Code;
    /**
     * 新社会阶层类型名称  (dict_d20)
     */
    @NotBlank(groups = MemGroup.class, message = "d20Name 不能为空")
    private String d20Name;
    /**
     * 新社会阶层类型code (dict_d20)
     */
    @NotBlank(groups = MemGroup.class, message = "d20Code 不能为空")
    private String d20Code;
    /**
     * 一线情况[OneLineCode]  (dict_d21)
     */
    @NotBlank(groups = MemGroup.class, message = "d21Code 不能为空")
    private String d21Code;
    /**
     * 一线情况名称
     */
    @NotBlank(groups = MemGroup.class, message = "d21Name 不能为空")
    private String d21Name;
    /**
     * 出生日期
     */
    @NotNull(groups = MemGroup.class, message = "birthday 不能为空")
    private Date birthday;
    /**
     * 年龄
     */
    private Integer age;
    /**
     * 性别code
     */
    @NotBlank(groups = MemGroup.class, message = "sexCode 不能为空")
    private String sexCode;
    /**
     * 性别名称
     */
    private String sexName;
    /**
     * 民族code  dict_d06
     */
    @NotBlank(groups = MemGroup.class, message = "d06Code 不能为空")
    private String d06Code;
    /**
     * 民族名称 dict_d06
     */
    @NotBlank(groups = MemGroup.class, message = "d06Name 不能为空")
    private String d06Name;
    /**
     * 籍贯  dict_area
     */
    @NotBlank(groups = MemGroup.class, message = "d48Code 不能为空")
    private String d48Code;
    /**
     * 籍贯名称 dict_area
     */
    // @NotBlank(groups = MemGroup.class, message = "d48Name 不能为空")
    private String d48Name;
    /**
     * 学历code  (dict_d07)
     */
    @NotBlank(groups = MemGroup.class, message = "d07Code 不能为空")
    private String d07Code;
    /**
     * 学历名称
     */
    @NotBlank(groups = MemGroup.class, message = "d07Name 不能为空")
    private String d07Name;
    /**
     * 人员类别 dict_d08
     */
    @NotBlank(groups = MemGroup.class, message = "d08Code 不能为空")
    private String d08Code;
    /**
     * 人员类别名称
     */
    @NotBlank(groups = MemGroup.class, message = "d08Name 不能为空")
    private String d08Name;
    /**
     * 工作岗位  (dict_d09)
     */
    @NotBlank(groups = MemGroup.class, message = "d09Code 不能为空")
    private String d09Code;
    /**
     * 工作岗位名称
     */
    @NotBlank(groups = MemGroup.class, message = "d09Name 不能为空")
    private String d09Name;
    /**
     * 是否农民工
     */
    @NotNull(groups = MemGroup.class, message = "isFarmer 不能为空")
    private Integer isFarmer;
    /**
     * 申请入党日期
     */
    @NotNull(groups = MemGroup.class, message = "applyDate 不能为空")
    private Date applyDate;
    /**
     * 积极分子日期
     */
    @NotNull(groups = MemGroup.class, message = "activeDate 不能为空")
    private Date activeDate;
    /**
     * 进入支部日期
     */
    @NotNull(groups = MemGroup.class, message = "joinOrgPartyDate 不能为空")
    private Date joinOrgPartyDate;
    /**
     * 发展对象日期
     */
    @NotNull(groups = MemGroup.class, message = "objectDate 不能为空")
    private Date objectDate;
    /**
     * 进入支部类型name  dict_d11 ?
     */
    @NotBlank(groups = MemGroup.class, message = "d11Name 不能为空")
    private String d11Name;
    /**
     * 进入支部类型code dict_d11 ?
     */
    @NotBlank(groups = MemGroup.class, message = "d11Code 不能为空")
    private String d11Code;
    /**
     * 加入党组织类别name ?
     */
    private String d27Name;
    /**
     * 加入党组织类别code  (dict_d27) ?
     */
    private String d27Code;
    /**
     * 预备党员转正类型code  (dict_d28)
     */
    private String d28Code;
    /**
     * 预备党员转正类型name  (dict_d28)
     */
    private String d28Name;
    /**
     * 是否失去联系
     */
    private Integer isLost;
    /**
     * 是否大额党费交纳
     */
    private Integer largePayState;
    /**
     * 发展时所在党支部code
     */
    private String branchOrgZbCode;
    /**
     * 发展时所在党支部name
     */
    private String branchOrgName;
    /**
     * 发展时所在党支部key
     */
    private String branchOrgKey;
    /**
     * 发展时所在党支部层级码code
     */
    private String branchOrgCode;
    /**
     * 系统外发展时所在党支部name
     */
    private String outBranchOrgName;
    /**
     * 是否劳务派遣
     */
    @NotNull(groups = MemGroup.class, message = "isDispatch 不能为空")
    private Integer isDispatch;
    private Integer isIdcardRepeat;
    private Integer isIdcardLegal;
    private Double ratio;
    /**
     * 预备期到的时间(或延长到的时间)
     */
    private Date extendPreparDate;
    /**
     * 撤销延长预备期原因
     */
    private String cancelExtendDateReason;
    /**
     * 是否已流动(1已流出，2流回)
     */
    private Integer flowStatus;
    /**
     * 恢复党籍原因
     */
    private String recoverPartyReason;
    /**
     * 停止党籍原因
     */
    private String stopPartyReason;
    /**
     * 工作单位及职务
     */
    private String workPost;
    /**
     * 定居的国家（地区）
     */
    private String settleArea;
    private Date stopPartyDate;
    private Date recoverPartyDate;
    /**
     * 旧人员code
     */
    private String memCode;
    private String openId;
    /**
     * 是否是历史数据
     */
    private Integer isHistory;
    /**
     * 修改人
     */
    private String updateAccount;
    /**
     * 是否恢复党籍
     */
    private Integer isReconvert;
    /**
     * 是否延长
     */
    private Integer isExtend;
    /**
     * 在系统内开始的缴费时间(一旦设置不能更改)
     */
    private Date startPayDate;
    /**
     * 管理组织
     */
    private List<Record> manageOrg;
    /**
     * 是否延长预备期
     */
    private Integer isExtendPrepare;

    private String d50CodeText;

    private String reasonsNote;

    private Integer hasRatification;

    /**
     * 专业技术职称代码
     */
    private String d126Code;

    /**
     * 专业技术职称名称
     */
    private String d126Name;

    /**
     * 统计单位
     */
    private String statisticalUnit;
    /**
     * 是否停止党籍（停止党籍1，未停止党籍0）
     */
    private Integer hasStopParty;

    /**
     * 是否取得联系（1是，0否）
     */
    private Integer hasGetTouch;

    /**
     * 取得联系时间
     */
    private Date getTouchDate;

    /**
     * 批准机构名称
     */
    private String approvalOrganName;

    /**
     * 批准机构级别
     */
    private String d52Code;

    /**
     * 批准机构级别名称
     */
    private String d52Name;

    /**
     * 批准时间
     */
    private Date approvalTime;

    /**
     * 离开党组织错误录入原因
     */
    private String errorReason;

    /**
     * 补录类型代码
     */
    private String d135Code;

    /**
     * 补录类型名称
     */
    private String d135Name;

    /**
     * 补充录入原因
     */
    private String replenishInputReason;

    /**
     * 省内转入组织code
     */
    private String provinceOrgCode;

    /**
     * 省内转入组织name
     */
    private String provinceOrgName;

    /**
     * 省外转入的转出组织名称
     */
    private String outsideProvinceName;


    /**
     * 是否省外转入
     */
    private Integer isOutlandTransferIn;

    /***
     * 省外转入年份
     * */
    private Date outlandTransferInYear;


    /**
     * 转出党组织名称
     */
    private String outlandTransferOrg;

    /**
     * 转出的地区代码
     **/
    private String d136Code;
    /***
     * 转出地区名称
     * */
    private String d136Name;

    /**
     * 锁定字段
     */
    private List<String> lockFields;

    /**
     * 在党员查询列表里面加一个图标，鼠标悬停后显示，该预备党员预备期已满，请及时维护。
     */
    private String message;
    /**
     * 其他政治面貌
     */
    private String d89Code;
    private String d89Name;
    /**
     * 知识分子情况
     */
    private String d154Code;
    private String d154Name;
    /**
     * 人事关系是否在党组织关联单位内(1是，0否)
     */
    private Integer hasUnitStatistics;

    /**
     * 是否需要自动计算年级（0 否 ，1 是）
     */
    private Integer hasCalculationGrade;

    /**
     * 是否入党宣誓 1-是；0否
     */
    private Integer isOath;

    /**
     * 档案类别：1-绿色档案； 2-蓝色档案； 3-红色档案； 99-其他
     */
    private Integer digitalType;

    /**
     * 党员身份认定：1-已认定；0-未认定
     */
    private Integer isSure;

    /**
     * 入党志愿书：1-已上传；0-未上传
     */
    private Integer isVolunteer;

    /**
     * 档案完整度：1-完整；0-不完整
     */
    private Integer isIntegrality;

    /**
     * 流程节点
     */
    private String processNode;

    /**
     * 召开支委会日期(成为正式党员日期)
     */
    private Date topartTurnPartyDate;

    /**
     * 档案批次唯一码
     */
    private String digitalLotNo;
    /**
     * 档案目录区分：0-只有固定的7个档案， 1-所有档案目录
     */
    private Integer isCatalogue;
    /**
     * 县委审核状态：1未审核，2审核通过，3审核未通过，默认为0
     */
    private Integer auditStatus;
    /**
     * 遵义流程是否有审批权限
     */
    private Boolean approve;
    /**
     * 是否县级党委
     */
    private Boolean isXJDW;

    /**
     * 档案完整度详情
     */
    private List<DigitalCompletenessVO> digitalCompletenessVOList;

    /**
     * 导出状态： 0-待申请  1-申请中 2-申请通过且在有效期内可导出
     */
    private Integer exportStatus;

    /**
     * 补充状态： 0-待申请  1-申请中 2-申请通过且在有效期内可补充
     */
    private Integer fillStatus;
    /**
     * 档案是否已归档：1已归档，0未归档
     */
    private Integer isArchived;
}