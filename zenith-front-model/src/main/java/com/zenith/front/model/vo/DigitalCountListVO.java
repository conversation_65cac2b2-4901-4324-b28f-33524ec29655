package com.zenith.front.model.vo;

import lombok.Data;

/**
 * <AUTHOR>
 * @create_date 2025-06-27 11:14
 * @description
 */
@Data
public class DigitalCountListVO {
    /**
     * 组织层级码
     */
    private String orgLevelCode;
    /**
     * 党员名称
     */
    private String name;
    /**
     * 党员code
     */
    private String code;
    /**
     * 最小上传数
     */
    private Long minNum;
    /**
     * 实际上传数
     */
    private Long num;
    /**
     * 档案目录
     */
    private String d222Code;
    /**
     * 党员岗位
     */
    private String d08Code;

    /**
     * 档案目录区分：0-只有固定的7个档案
     */
    private Integer isCatalogue;
}
