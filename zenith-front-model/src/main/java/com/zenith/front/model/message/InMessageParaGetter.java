//package com.zenith.front.message;
//
//import com.b1809.base.utils.JackSonUtil;
//import com.fasterxml.jackson.core.type.TypeReference;
//import com.jfinal.core.Action;
//import com.jfinal.core.Controller;
//import com.jfinal.core.paragetter.ParaGetter;
//import com.jfinal.kit.LogKit;
//
//import java.lang.reflect.Method;
//import java.lang.reflect.ParameterizedType;
//import java.lang.reflect.Type;
//
///***
// * 用户转换payload 中的数据为InMessage对象
// * <AUTHOR>
// * */
//public class InMessageParaGetter extends ParaGetter<InMessage> {
//
//    public InMessageParaGetter(String parameterName, String defaultValue) {
//        super(parameterName, defaultValue);
//    }
//
//    @Override
//    protected InMessage to(String v) {
//        return null;
//    }
//
//    @Override
//    public InMessage get(Action action, Controller c) {
//        Method method = action.getMethod();
//        Type[] genericParameterTypes = method.getGenericParameterTypes();
//        for (Type genericParameterType : genericParameterTypes) {
//            ParameterizedType type = (ParameterizedType) genericParameterType;
//            Class rawType = (Class) type.getRawType();
//            if (InMessage.class.isAssignableFrom(rawType)) {
//                String rawData = c.getRawData();
//                try {
//                    return JackSonUtil.JSON.readValue(rawData, new TypeReference<InMessage>() {
//                        @Override
//                        public Type getType() {
//                            return genericParameterType;
//                        }
//                    });
//                } catch (Exception e) {
//                    e.printStackTrace();
//                    LogKit.error("参数错误", e);
//                    return new InMessage();
//                }
//            }
//        }
//        return null;
//    }
//}
