package com.zenith.front.model.modelview;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.zenith.front.common.jsonb.JsonbTypeHandler;
import com.zenith.front.common.kit.JackSonUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.*;

/**
 * @author: D.watermelon
 * @date: 2021/12/29 23:45
 * @description: 入则恳恳以尽忠 出则谦谦以自悔
 */
@TableName(value = "ccp_transfer_tar_org_view", autoResultMap = true)
public class TransFerTarView implements Serializable {
    private static final long serialVersionUID = 1L;

    public static final Logger LOGGER = LoggerFactory.getLogger(TransFerTarView.class);

    /**
     * 组织层级码
     */
    @TableField("org_code")
    private String orgCode;


    /**
     * 主键
     */
    @TableId("id")
    private String id;

    /**
     * 用户id
     */
    @TableField("user_id")
    private String userId;

    /**
     * 名称
     */
    @TableField("name")
    private String name;

    /**
     * 人员id
     */
    @TableField("mem_id")
    private String memId;

    /**
     * 源组织id
     */
    @TableField("src_org_id")
    private String srcOrgId;

    /**
     * 源组织名称
     */
    @TableField("src_org_name")
    private String srcOrgName;

    /**
     * 目标组织id
     */
    @TableField("target_org_id")
    private String targetOrgId;

    /**
     * 目标组织名称
     */
    @TableField("target_org_name")
    private String targetOrgName;

    /**
     * 公共节点id
     */
    @TableField("common_org_id")
    private String commonOrgId;

    /**
     * 公共节点名称
     */
    @TableField("common_org_name")
    private String commonOrgName;

    /**
     * 源组织关系数组
     */
    @TableField(value = "src_org_relation", typeHandler = JsonbTypeHandler.class)
    private Object srcOrgRelation;

    /**
     * 目标组织关系数组
     */
    @TableField(value = "target_org_relation", typeHandler = JsonbTypeHandler.class)
    private Object targetOrgRelation;

    /**
     * 党费截止日期
     */
    @TableField("mem_fee_end_time")
    private Date memFeeEndTime;

    /**
     * 党费标准
     */
    @TableField("mem_fee_standard")
    private BigDecimal memFeeStandard;

    /**
     * 转出类型
     */
    @TableField("out_type")
    private String outType;

    /**
     * 转入类型
     */
    @TableField("in_type")
    private String inType;

    /**
     * 类型
     */
    @TableField("type")
    private String type;

    /**
     * 审批记录id
     */
    @TableField("current_approval_id")
    private String currentApprovalId;

    /**
     * 转接状态,0转接中 1 转接完成  2已撤销
     */
    @TableField("status")
    private Integer status;

    /**
     * 审批理由
     */
    @TableField("reason")
    private String reason;

    /**
     * 创建时间
     */
    @TableField("create_time")
    private Date createTime;

    /**
     * 更新时间
     */
    @TableField("update_time")
    private Date updateTime;

    /**
     * 附加信息
     */
    @TableField(value = "extra_data", typeHandler = JsonbTypeHandler.class)
    private Object extraData;

    /**
     * 介绍信url
     */
    @TableField("letter_url")
    private String letterUrl;

    /**
     * 源组织关系数组 用于机构树查询
     */
    @TableField(value = "src_org_relation_rel", typeHandler = JsonbTypeHandler.class)
    private Object srcOrgRelationRel;

    /**
     * 目标组织关系数组
     */
    @TableField(value = "target_org_relation_rel", typeHandler = JsonbTypeHandler.class)
    private Object targetOrgRelationRel;

    @TableField(value = "whether_extend_prep_period")
    private String whetherExtendPrepPeriod;


    /**
     * 影响人数
     */
    @TableField("effect_mems")
    private Integer effectMems;

    @TableField(exist = false)
    private String next_org_id;

    @TableField("d92_code")
    private String d92Code;

    @TableField("d92_name")
    private String d92Name;

    @TableField("remark")
    private String remark;

    /**
     * 省外转出时间
     */
    @TableField("transfer_out_time")
    private Date transferOutTime;

    @TableField("data_text")
    private String dataText;

    public String getOrgCode() {
        return orgCode;
    }

    public void setOrgCode(String orgCode) {
        this.orgCode = orgCode;
    }

    public String getDataText() {
        return dataText;
    }

    public void setDataText(String dataText) {
        this.dataText = dataText;
    }

    public Date getTransferOutTime() {
        return transferOutTime;
    }

    public void setTransferOutTime(Date transferOutTime) {
        this.transferOutTime = transferOutTime;
    }

    public String getWhetherExtendPrepPeriod() {
        return whetherExtendPrepPeriod;
    }

    public void setWhetherExtendPrepPeriod(String whetherExtendPrepPeriod) {
        this.whetherExtendPrepPeriod = whetherExtendPrepPeriod;
    }

    public String getD92Code() {
        return d92Code;
    }


    public Object getSrcOrgRelationRel() {
        return srcOrgRelationRel;
    }

    public void setSrcOrgRelationRel(Object srcOrgRelationRel) {
        this.srcOrgRelationRel = srcOrgRelationRel;
    }

    public Object getTargetOrgRelationRel() {
        return targetOrgRelationRel;
    }

    public void setTargetOrgRelationRel(Object targetOrgRelationRel) {
        this.targetOrgRelationRel = targetOrgRelationRel;
    }

    public void setD92Code(String d92Code) {
        this.d92Code = d92Code;
    }

    public String getD92Name() {
        return d92Name;
    }

    public void setD92Name(String d92Name) {
        this.d92Name = d92Name;
    }

    public String getNext_org_id() {
        return next_org_id;
    }

    public void setNext_org_id(String next_org_id) {
        this.next_org_id = next_org_id;
    }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getUserId() {
        return userId;
    }

    public void setUserId(String userId) {
        this.userId = userId;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getMemId() {
        return memId;
    }

    public void setMemId(String memId) {
        this.memId = memId;
    }

    public String getSrcOrgId() {
        return srcOrgId;
    }

    public void setSrcOrgId(String srcOrgId) {
        this.srcOrgId = srcOrgId;
    }

    public String getSrcOrgName() {
        return srcOrgName;
    }

    public void setSrcOrgName(String srcOrgName) {
        this.srcOrgName = srcOrgName;
    }

    public String getTargetOrgId() {
        return targetOrgId;
    }

    public void setTargetOrgId(String targetOrgId) {
        this.targetOrgId = targetOrgId;
    }

    public String getTargetOrgName() {
        return targetOrgName;
    }

    public void setTargetOrgName(String targetOrgName) {
        this.targetOrgName = targetOrgName;
    }

    public String getCommonOrgId() {
        return commonOrgId;
    }

    public void setCommonOrgId(String commonOrgId) {
        this.commonOrgId = commonOrgId;
    }

    public String getCommonOrgName() {
        return commonOrgName;
    }

    public void setCommonOrgName(String commonOrgName) {
        this.commonOrgName = commonOrgName;
    }

    public Object getSrcOrgRelation() {
        return srcOrgRelation;
    }

    public void setSrcOrgRelation(Object srcOrgRelation) {
        this.srcOrgRelation = srcOrgRelation;
    }

    public Object getTargetOrgRelation() {
        return targetOrgRelation;
    }

    public void setTargetOrgRelation(Object targetOrgRelation) {
        this.targetOrgRelation = targetOrgRelation;
    }

    public Date getMemFeeEndTime() {
        return memFeeEndTime;
    }

    public void setMemFeeEndTime(Date memFeeEndTime) {
        this.memFeeEndTime = memFeeEndTime;
    }

    public BigDecimal getMemFeeStandard() {
        return memFeeStandard;
    }

    public void setMemFeeStandard(BigDecimal memFeeStandard) {
        this.memFeeStandard = memFeeStandard;
    }

    public String getOutType() {
        return outType;
    }

    public void setOutType(String outType) {
        this.outType = outType;
    }

    public String getInType() {
        return inType;
    }

    public void setInType(String inType) {
        this.inType = inType;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public String getCurrentApprovalId() {
        return currentApprovalId;
    }

    public void setCurrentApprovalId(String currentApprovalId) {
        this.currentApprovalId = currentApprovalId;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public String getReason() {
        return reason;
    }

    public void setReason(String reason) {
        this.reason = reason;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    public Object getExtraData() {
        return extraData;
    }

    public void setExtraData(Object extraData) {
        this.extraData = extraData;
    }

    public String getLetterUrl() {
        return letterUrl;
    }

    public void setLetterUrl(String letterUrl) {
        this.letterUrl = letterUrl;
    }

    public Integer getEffectMems() {
        return effectMems;
    }

    public void setEffectMems(Integer effectMems) {
        this.effectMems = effectMems;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    @Override
    public String toString() {
        return "TransFerTarView{" +
                "id=" + id +
                ", userId=" + userId +
                ", name=" + name +
                ", memId=" + memId +
                ", srcOrgId=" + srcOrgId +
                ", srcOrgName=" + srcOrgName +
                ", targetOrgId=" + targetOrgId +
                ", targetOrgName=" + targetOrgName +
                ", commonOrgId=" + commonOrgId +
                ", commonOrgName=" + commonOrgName +
                ", srcOrgRelation=" + srcOrgRelation +
                ", targetOrgRelation=" + targetOrgRelation +
                ", memFeeEndTime=" + memFeeEndTime +
                ", memFeeStandard=" + memFeeStandard +
                ", outType=" + outType +
                ", inType=" + inType +
                ", type=" + type +
                ", currentApprovalId=" + currentApprovalId +
                ", status=" + status +
                ", reason=" + reason +
                ", createTime=" + createTime +
                ", updateTime=" + updateTime +
                ", extraData=" + extraData +
                ", letterUrl=" + letterUrl +
                ", effectMems=" + effectMems +
                "}";
    }

    /***
     * 设置目标组织关系
     * */
    public void setTargetOrgRelationAsList(List<String> list) {
        setTargetOrgRelation(list);
    }

    /**
     * 设置源组织关系
     **/
    public void setSrcOrgRelationAsList(List<String> list) {
        setSrcOrgRelation(list);
    }

    /**
     * 获取目标组织关系
     */
    public List<String> getTargetOrgRelationAsList() {
        return getStrings(targetOrgRelation);
    }

    /***
     * 获取源组织关系
     * */
    public List<String> getSrcOrgRelationAsList() {
        return getStrings(srcOrgRelation);
    }


    /***
     * 设置目标组织关系
     * */
    public void setTargetOrgRelationRelAsList(List<String> list) {
        setTargetOrgRelationRel(list);
    }

    /**
     * 设置源组织关系
     **/
    public void setSrcOrgRelationRelAsList(List<String> list) {
        setSrcOrgRelationRel(list);
    }

    /**
     * 获取目标组织关系
     */
    public List<String> getTargetOrgRelationRelAsList() {
        return getStrings(targetOrgRelationRel);
    }

    /***
     * 获取源组织关系
     * */
    public List<String> getSrcOrgRelationRelAsList() {
        return getStrings(srcOrgRelationRel);
    }


    private List<String> getStrings(Object srcOrgRelation) {
        List<String> result = new ArrayList<>();
        if (Objects.isNull(srcOrgRelation)) {
            return result;
        }
        return JackSonUtil.jsonToList(JackSonUtil.toJson(srcOrgRelation), String.class);
//        try {
//            JsonNode jsonNode = JackSonUtil.JSON.readTree(srcOrgRelation.toString());
//            for (JsonNode node : jsonNode) {
//                result.add(node.asText());
//            }
//        } catch (IOException e) {
//            e.printStackTrace();
//            LOGGER.error("target_org_relation json error", e);
//        }
//        return result;
    }

    /**
     * 转hashSet
     **/
    public Set<String> getTargetOrgRelationAsSet() {
        List<String> targetOrgRelationAsList = getTargetOrgRelationAsList();
        return new HashSet<>(targetOrgRelationAsList);
    }

    /**
     * 转hashSet
     **/
    public Set<String> getSrcOrgRelationAsSet() {
        List<String> srcOrgRelationAsList = getSrcOrgRelationAsList();
        return new HashSet<>(srcOrgRelationAsList);
    }

    /**
     * 人员附加信息类
     **/
    public static class MemInfoExtraData {
        /**
         * 人员姓名
         */
        private String memName;
        /***
         * 人员民族
         * */
        private String memNation;
        /***
         * 人员类型
         * */
        private String memType;
        /***
         * 手机号码
         * */
        private String phone;
        /***
         * 性别
         * */
        private String sex;
        /***
         * 身份号码
         * */
        private String idCard;
        /***
         * 入党时间
         * */
        private Date inOrgTime;
        /***
         * 转正时间
         * */
        private Date turnTime;
        /***
         * 地址
         * */
        private String address;

        public String getMemName() {
            return memName;
        }

        public TransFerTarView.MemInfoExtraData setMemName(String memName) {
            this.memName = memName;
            return this;
        }

        public String getMemNation() {
            return memNation;
        }

        public TransFerTarView.MemInfoExtraData setMemNation(String memNation) {
            this.memNation = memNation;
            return this;
        }

        public String getMemType() {
            return memType;
        }

        public TransFerTarView.MemInfoExtraData setMemType(String memType) {
            this.memType = memType;
            return this;
        }

        public String getPhone() {
            return phone;
        }

        public TransFerTarView.MemInfoExtraData setPhone(String phone) {
            this.phone = phone;
            return this;
        }

        public String getSex() {
            return sex;
        }

        public TransFerTarView.MemInfoExtraData setSex(String sex) {
            this.sex = sex;
            return this;
        }

        public String getIdCard() {
            return idCard;
        }

        public TransFerTarView.MemInfoExtraData setIdCard(String idCard) {
            this.idCard = idCard;
            return this;
        }

        public Date getInOrgTime() {
            return inOrgTime;
        }

        public TransFerTarView.MemInfoExtraData setInOrgTime(Date inOrgTime) {
            this.inOrgTime = inOrgTime;
            return this;
        }

        public Date getTurnTime() {
            return turnTime;
        }

        public TransFerTarView.MemInfoExtraData setTurnTime(Date turnTime) {
            this.turnTime = turnTime;
            return this;
        }

        public String getAddress() {
            return address;
        }

        public TransFerTarView.MemInfoExtraData setAddress(String address) {
            this.address = address;
            return this;
        }
    }

    /***
     * 设置人员附加信息
     * */
    public void setMemInfoExtraData(List<TransFerTarView.MemInfoExtraData> extraData) {
        setExtraData(extraData);
    }

    /***
     * 获取人员附加信息
     * */
    public TransFerTarView.MemInfoExtraData getMemInfoExtraData() {
        if (Objects.isNull(extraData)) {
            return null;
        }
        String jsonStr = extraData.toString();
        TransFerTarView.MemInfoExtraData memInfoExtraData = null;
        try {
            memInfoExtraData = JackSonUtil.JSON.readValue(jsonStr, TransFerTarView.MemInfoExtraData.class);
        } catch (Exception e) {
            LOGGER.error("转换extra_data失败!", e);
        }
        return memInfoExtraData;
    }
}
