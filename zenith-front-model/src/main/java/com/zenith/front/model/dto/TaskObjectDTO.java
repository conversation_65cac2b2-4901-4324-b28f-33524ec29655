package com.zenith.front.model.dto;

import com.zenith.front.model.bean.TaskObject;
import com.zenith.front.model.validate.group.Common1Group;
import com.zenith.front.model.validate.group.Common2Group;
import lombok.*;

import javax.validation.constraints.NotNull;

@ToString
public class TaskObjectDTO{

   	/**
   	 * 自增长id
   	 */
    private Integer id;
   	/**
   	 * 唯一标识符code
   	 */
    @NotNull(groups = {Common1Group.class, Common2Group.class},message = "标识符不允许为空!")
    private String code;
   	/**
   	 * 任务code
   	 */
    private String taskCode;
   	/**
   	 * 组织&人员唯一标识符
   	 */
    private String objectCode;
   	/**
   	 * 组织&人员层级码
   	 */
    private String objectOrgCode;
   	/**
   	 * 提交内容
   	 */
    private String commitContext;
   	/**
   	 * 提交文件
   	 */
    private String commitFile;

    /**
     * 驳回原因
     */
    private String remark;

   	/**
   	 * 进行状态
   	 */
    @NotNull(groups = {Common1Group.class},message = "审核状态不允许为空!")
    private Integer status;
   	/**
   	 * 创建时间
   	 */
    private java.util.Date createTime;
   	/**
   	 * 更新时间
   	 */
    private java.util.Date updateTime;
   	/**
   	 * 删除时间
   	 */
    private java.util.Date deleteTime;


    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public TaskObjectDTO setId(Integer id){
        this.id = id;
        return this;
    }
    public Integer getId() {
    	return this.id;
    }
    public TaskObjectDTO setCode(String code){
        this.code = code;
        return this;
    }
    public String getCode() {
    	return this.code;
    }
    public TaskObjectDTO setTaskCode(String taskCode){
        this.taskCode = taskCode;
        return this;
    }
    public String getTaskCode() {
    	return this.taskCode;
    }
    public TaskObjectDTO setObjectCode(String objectCode){
        this.objectCode = objectCode;
        return this;
    }
    public String getObjectCode() {
    	return this.objectCode;
    }
    public TaskObjectDTO setObjectOrgCode(String objectOrgCode){
        this.objectOrgCode = objectOrgCode;
        return this;
    }
    public String getObjectOrgCode() {
    	return this.objectOrgCode;
    }
    public TaskObjectDTO setCommitContext(String commitContext){
        this.commitContext = commitContext;
        return this;
    }
    public String getCommitContext() {
    	return this.commitContext;
    }
    public TaskObjectDTO setCommitFile(String commitFile){
        this.commitFile = commitFile;
        return this;
    }
    public String getCommitFile() {
    	return this.commitFile;
    }
    public TaskObjectDTO setStatus(Integer status){
        this.status = status;
        return this;
    }
    public Integer getStatus() {
    	return this.status;
    }
    public TaskObjectDTO setCreateTime(java.util.Date createTime){
        this.createTime = createTime;
        return this;
    }
    public java.util.Date getCreateTime() {
    	return this.createTime;
    }
    public TaskObjectDTO setUpdateTime(java.util.Date updateTime){
        this.updateTime = updateTime;
        return this;
    }
    public java.util.Date getUpdateTime() {
    	return this.updateTime;
    }
    public TaskObjectDTO setDeleteTime(java.util.Date deleteTime){
        this.deleteTime = deleteTime;
        return this;
    }
    public java.util.Date getDeleteTime() {
    	return this.deleteTime;
    }


    public TaskObject toModel(){
        TaskObject model = new TaskObject();
        model.setId(this.id);
        model.setCode(this.code);
        model.setTaskCode(this.taskCode);
        model.setObjectCode(this.objectCode);
        model.setObjectOrgCode(this.objectOrgCode);
        model.setCommitContext(this.commitContext);
        model.setCommitFile(this.commitFile);
        model.setStatus(this.status);
        model.setCreateTime(this.createTime);
        model.setUpdateTime(this.updateTime);
        model.setDeleteTime(this.deleteTime);
        model.setRemark(this.remark);
        return model;
    }
}