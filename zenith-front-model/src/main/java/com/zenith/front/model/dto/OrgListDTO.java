package com.zenith.front.model.dto;

import com.zenith.front.model.custom.HasSubordinateField;
import com.zenith.front.model.validate.group.Common1Group;
import com.zenith.front.model.validate.group.Common2Group;
import lombok.*;

import javax.validation.constraints.Max;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @Description: 组织列表请求模型
 * @date 2019/4/3 14:45
 */
@EqualsAndHashCode(callSuper = true)
@Data
@ToString
@NoArgsConstructor
@AllArgsConstructor
public class OrgListDTO extends HasSubordinateField {

    private static final long serialVersionUID = -366500898717384101L;

    /**
     * 组织层级码
     */
    @NotBlank(groups = Common1Group.class, message = "orgCode 不能为空")
    private String orgCode;
    /**
     * 排除的组织层级码
     */
    private List<String> excludeOrgCodeList;
    /**
     * 组织类别
     */
    private List<String> d01CodeList;
    /**
     * 隶属关系
     */
    private List<String> d03CodeList;
    /**
     * 组织大类
     */
    private List<String> orgTypeList;
    /**
     * 党组织所在单位情况代码
     */
    private List<String> d02CodeList;
    /**
     * 单位类别
     */
    private List<String> d04CodeList;
    /**
     * 搜索框,按名称搜索
     */
    private String orgName;
    /**
     * 分页页数
     */
    @Min(value = 1, groups = {Common1Group.class}, message = "页码最小为1")
    private Integer pageNum;
    /**
     * 分页每页数
     */
    @Max(value = 100, groups = {Common1Group.class}, message = "每页条数大小范围在1-100")
    private Integer pageSize;

    /**
     * 是否权限检验,0--不进行校验,1--进行权限校验
     */
    private String isPermissionCheck;

    /**
     * 导出
     */
    @NotNull(groups = Common2Group.class, message = "exportList 不能为空")
    private List<ExportDTO> exportList;
    /**
     * 表名
     */
    @NotBlank(groups = Common2Group.class, message = "tableName 不能为空")
    private String tableName;

    /**
     * 0--导出自己及直属下级,1--导出自己所有下级
     */
    @NotBlank(groups = Common2Group.class, message = "isExportAll 不能为空")
    private String isExportAll;

    /**
     * 导出类别：1-党组织基本信息  2-应换 3-未换 4-已换
     */
    private Integer type;

    /**
     * 国民经济行业CODE
     */
    private List<String> d194CodeList;

    /**
     * 生产服务性行业CODE
     */
    private List<String> d195CodeList;

    /**
     * 是否只展示流动党组织 0/null-否 1-是
     */
    private String mustFlowStatus;


}
