package com.zenith.front.model.vo;

import cn.afterturn.easypoi.excel.annotation.Excel;
import lombok.Data;

@Data
public class MemFlow1VOExcel {
    @Excel(name = "序号", orderNum = "1", height = 8, width = 10, isImportField = "true_st")
    private Integer index;
    @Excel(name = "姓名", orderNum = "2", height = 8, width = 10, isImportField = "true_st")
    private String memName;
    @Excel(name = "性别", orderNum = "3", height = 8, width = 10, isImportField = "true_st")
    private String memSexName;
    @Excel(name = "联系电话", orderNum = "4", height = 8, width = 15, isImportField = "true_st")
    private String memPhone;
    @Excel(name = "流出类型", orderNum = "4", height = 8, width = 30, isImportField = "true_st")
    private String outPlaceName;
    @Excel(name = "流入地行政区划", orderNum = "6", height = 8, width = 30, isImportField = "true_st")
    private String outAdministrativeDivisionName;
    @Excel(name = "流动类型", orderNum = "7", height = 8, width = 50, isImportField = "true_st")
    private String flowTypeName;
    @Excel(name = "外出日期", orderNum = "8", height = 8, width = 25, isImportField = "true_st")
    private String outTime;
    @Excel(name = "外出时长", orderNum = "9", height = 8, width = 10, isImportField = "true_st")
    private String outDuraion;
    @Excel(name = "流入地党支部", orderNum = "10", height = 8, width = 50, isImportField = "true_st")
    private String inOrgName;
    @Excel(name = "流出地党支部", orderNum = "11", height = 8, width = 50, isImportField = "true_st")
    private String memOrgName;
    @Excel(name = "流入地党组织联系人", orderNum = "12", height = 8, width = 50, isImportField = "true_st")
    private String outOrgContact;
    @Excel(name = "流入地党组织联系方式", orderNum = "13", height = 8, width = 50, isImportField = "true_st")
    private String outOrgContactPhone;

}
