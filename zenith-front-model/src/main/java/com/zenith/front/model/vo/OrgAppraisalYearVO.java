package com.zenith.front.model.vo;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @version V1.0
 * @Package com.zenith.front.vo
 * @date 2021/8/17 0017 19:19
 */
public class OrgAppraisalYearVO implements Serializable {

    private static final long serialVersionUID = -3871115757990555326L;
    /**
     * 唯一标识符code
     */
    private String code;

    /**
     * 评议年度(年份选择框可以选择当前时间倒退5年)
     */
    private Integer year;

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public Integer getYear() {
        return year;
    }

    public void setYear(Integer year) {
        this.year = year;
    }

    @Override
    public String toString() {
        return "OrgAppraisalYearVO{" +
                "code='" + code + '\'' +
                ", year=" + year +
                '}';
    }
}
