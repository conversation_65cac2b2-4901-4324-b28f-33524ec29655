package com.zenith.front.model.vo;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

import java.util.Date;

/**
 * <AUTHOR>
 */
@Data
@ToString
@NoArgsConstructor
@AllArgsConstructor
public class OrgDemocraticAppraisalVO {

    /**
     * 主键
     */
    private String code;

    /**
     * 评议年度(年份选择框可以选择当前时间倒退5年)
     */
    private Date year;

    /**
     * 应评议人数（数值填写框）
     */
    private Integer peopleToBeReviewed;

    /**
     * 已评议人数
     */
    private Integer peopleReviewed;

    /**
     * 评议情况（开始评议，结束评议）
     */
    private String situation;

}
