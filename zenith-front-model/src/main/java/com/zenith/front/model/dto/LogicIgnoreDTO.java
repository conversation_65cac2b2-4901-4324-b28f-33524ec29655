package com.zenith.front.model.dto;

import java.io.Serializable;

/**
 * <AUTHOR>
 */
public class LogicIgnoreDTO implements Serializable {

    private static final long serialVersionUID = 5627126387172642077L;

    /**
     * 逻辑校核主键
     */
    private String logicCheckCode;

    /**
     * 逻辑校核名称
     */
    private String logicCheckName;

    /**
     * 类型
     */
    private String type;

    /**
     * 忽略主键
     */
    private String code;

    /**
     * 原因
     */
    private String reason;

    public String getLogicCheckCode() {
        return logicCheckCode;
    }

    public void setLogicCheckCode(String logicCheckCode) {
        this.logicCheckCode = logicCheckCode;
    }

    public String getLogicCheckName() {
        return logicCheckName;
    }

    public void setLogicCheckName(String logicCheckName) {
        this.logicCheckName = logicCheckName;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getReason() {
        return reason;
    }

    public void setReason(String reason) {
        this.reason = reason;
    }

    @Override
    public String toString() {
        return "LogicIgnoreDTO{" +
                "logicCheckCode='" + logicCheckCode + '\'' +
                ", logicCheckName='" + logicCheckName + '\'' +
                ", type='" + type + '\'' +
                ", code='" + code + '\'' +
                ", reason='" + reason + '\'' +
                '}';
    }
}
