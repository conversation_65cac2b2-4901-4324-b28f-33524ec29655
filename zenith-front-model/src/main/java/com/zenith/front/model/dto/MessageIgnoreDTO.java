package com.zenith.front.model.dto;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @version V1.0
 * @date 2022/7/27 16:42
 */
public class MessageIgnoreDTO implements Serializable {

    /**
     * 消息主键
     */
    private List<String> code;

    /**
     * 是否查看
     */
    private Boolean cat;

    public List<String> getCode() {
        return code;
    }

    public void setCode(List<String> code) {
        this.code = code;
    }

    public boolean isCat() {
        return cat;
    }

    public void setCat(boolean cat) {
        this.cat = cat;
    }

    @Override
    public String toString() {
        return "MessageIgnoreDTO{" +
                "code='" + code + '\'' +
                "cat='" + cat + '\'' +
                '}';
    }
}
