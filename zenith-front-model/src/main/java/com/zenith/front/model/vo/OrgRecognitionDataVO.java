package com.zenith.front.model.vo;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

/**
 * <AUTHOR>
 * @Date 2021/9/14 16:03
 * @Version 1.0
 */
@Data
@ToString
@NoArgsConstructor
@AllArgsConstructor
public class OrgRecognitionDataVO {
    /**
     * 唯一标识符code
     */
    private String code;

    /**
     * 表彰唯一code
     */
    private String recognitionCode;

    /**
     * 表彰对象
     */
    private String recognitionObject;

    /**
     * 表彰级别
     */
    private String recognitionLevel;

    /**
     * 人数
     */
    private Integer number;
}
