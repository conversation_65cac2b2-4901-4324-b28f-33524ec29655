package com.zenith.front.model.vo;

import com.zenith.front.model.custom.Record;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

import java.util.List;

/**
 * @author: D.watermelon
 * @date: 2019/6/3 23:27
 * @description: 入则恳恳以尽忠 出则谦谦以自悔
 */
@Data
@ToString
@NoArgsConstructor
@AllArgsConstructor
public class MessageNoticeVO {

    /**
     * 增加消息回复实体属性
     * */
    private List<Record> replyList;

    /**
     *
     * 增加消息实体属性--消息来源
     * */
    private String fromType;


    /**
     * 增加消息实体属性--消息名称
     * */
    private String messageNme;

    /**
     * 增加消息实体属性--消息内容
     * */
    private String messageContext;


    /**
     * 增加消息实体属性--是否需要回复
     * */

    private Integer messageIsReply;


    /**
     * 增加消息实体属性--消息类别名称
     * */
    private String messageTypeNmae;

    /**
     *增加消息实体属性--发布人
     * */
    private  String messageCreateName;


    /**
     *
     * 增加消息实体--接收人
     * */
    private String receiveName;

    /**
     *  自增长主键
     */
    private Integer id;
    /**
     * 消息通知主键
     */
    private String code;
    /**
     * 回复内容
     */
    private String replyContext;
    /**
     * 是否查看
     */
    private String isSelect;
    /**
     * 是否再次发送通知
     */
    private Integer noticePlan;
    /**
     * 回复文件
     */
    private String file;
    /**
     * 通知时间
     */
    private java.util.Date time;
    /**
     * 消息code
     */
    private String messageCode;
    /**
     * 再次发送通知的消息code
     */
    private String noticeMessageCode;
    /**
     * 消息类别
     */
    private Integer messageType;
    /**
     * 接收人code[如果是组织，则是组织的层级码，如果是人员则是人员的唯一标识符]
     */
    private String receiveCode;
    /**
     * 接收人组织唯一标识符
     */
    private String receiveOrgCode;
    /**
     * 创建时间
     */
    private java.util.Date createTime;
    /**
     * 更新时间
     */
    private java.util.Date updateTime;
    /**
     * 删除时间
     */

}
