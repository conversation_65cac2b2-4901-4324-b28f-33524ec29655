package com.zenith.front.model.bean;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableField;
import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 * 问题反馈
 * </p>
 *
 * <AUTHOR>
 * @since 2021-09-23
 */
@TableName("ccp_problem_feedback")
public class ProblemFeedback extends Model<ProblemFeedback> {

    private static final long serialVersionUID = 1L;

    /**
     * 唯一标识符
     */
    @TableId(value = "code", type = IdType.ASSIGN_UUID)
    private String code;

    /**
     * 标题
     */
    @TableField("title")
    private String title;

    /**
     * 问题简述
     */
    @TableField("questions_briefly")
    private String questionsBriefly;

    /**
     * 问题详情
     */
    @TableField("problem_details")
    private String problemDetails;

    /**
     * 发布人
     */
    @TableField("user_id")
    private String userId;

    /**
     * 回复情况
     */
    @TableField("reply_situation")
    private String replySituation;

    /**
     * 发布时间
     */
    @TableField("release_time")
    private Date releaseTime;

    /**
     * 查看人数
     */
    @TableField("look_number")
    private Long lookNumber;

    /**
     * 是否回复
     */
    @TableField("member_reply")
    private Integer memberReply;

    /**
     * 修复状态
     */
    @TableField("state_repair")
    private String stateRepair;

    /**
     * 联系方式
     */
    @TableField("contact")
    private String contact;

    @TableField("create_time")
    private Date createTime;

    @TableField("update_time")
    private Date updateTime;

    @TableField("delete_time")
    private Date deleteTime;


    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public String getQuestionsBriefly() {
        return questionsBriefly;
    }

    public void setQuestionsBriefly(String questionsBriefly) {
        this.questionsBriefly = questionsBriefly;
    }

    public String getProblemDetails() {
        return problemDetails;
    }

    public void setProblemDetails(String problemDetails) {
        this.problemDetails = problemDetails;
    }

    public String getUserId() {
        return userId;
    }

    public void setUserId(String userId) {
        this.userId = userId;
    }

    public String getReplySituation() {
        return replySituation;
    }

    public void setReplySituation(String replySituation) {
        this.replySituation = replySituation;
    }

    public Date getReleaseTime() {
        return releaseTime;
    }

    public void setReleaseTime(Date releaseTime) {
        this.releaseTime = releaseTime;
    }

    public Long getLookNumber() {
        return lookNumber;
    }

    public void setLookNumber(Long lookNumber) {
        this.lookNumber = lookNumber;
    }

    public Integer getMemberReply() {
        return memberReply;
    }

    public void setMemberReply(Integer memberReply) {
        this.memberReply = memberReply;
    }

    public String getStateRepair() {
        return stateRepair;
    }

    public void setStateRepair(String stateRepair) {
        this.stateRepair = stateRepair;
    }

    public String getContact() {
        return contact;
    }

    public void setContact(String contact) {
        this.contact = contact;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    public Date getDeleteTime() {
        return deleteTime;
    }

    public void setDeleteTime(Date deleteTime) {
        this.deleteTime = deleteTime;
    }

    @Override
    protected Serializable pkVal() {
        return this.code;
    }

    @Override
    public String toString() {
        return "ProblemFeedback{" +
        "code=" + code +
        ", title=" + title +
        ", questionsBriefly=" + questionsBriefly +
        ", problemDetails=" + problemDetails +
        ", userId=" + userId +
        ", replySituation=" + replySituation +
        ", releaseTime=" + releaseTime +
        ", lookNumber=" + lookNumber +
        ", memberReply=" + memberReply +
        ", stateRepair=" + stateRepair +
        ", contact=" + contact +
        ", createTime=" + createTime +
        ", updateTime=" + updateTime +
        ", deleteTime=" + deleteTime +
        "}";
    }
}
