/*******************************************************************************
 Copyright 2018 Disney Streaming Services

 Licensed under the Apache License, Version 2.0 (the "Apache License")
 with the following modification; you may not use this file except in
 compliance with the Apache License and the following modification to it:
 Section 6. Trademarks. is deleted and replaced with:

 6. Trademarks. This License does not grant permission to use the trade
 names, trademarks, service marks, or product names of the Licensor
 and its affiliates, except as required to comply with Section 4(c) of
 the License and to reproduce the content of the NOTICE file.

 You may obtain a copy of the Apache License at

 http://www.apache.org/licenses/LICENSE-2.0

 Unless required by applicable law or agreed to in writing, software
 distributed under the Apache License with the above modification is
 distributed on an "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
 KIND, either express or implied. See the Apache License for the specific
 language governing permissions and limitations under the Apache License.

 ******************************************************************************/

package com.zenith.front.model.pgwatch;

import com.fasterxml.jackson.annotation.*;

import java.util.List;

@JsonTypeInfo(use = JsonTypeInfo.Id.NAME,
        include = JsonTypeInfo.As.EXISTING_PROPERTY,
        property = "kind", visible = true)
@JsonSubTypes({
        @JsonSubTypes.Type(value = InsertAbstractChange.class, name = "insert"),
        @JsonSubTypes.Type(value = UpdateAbstractChange.class, name = "update"),
        @JsonSubTypes.Type(value = DeleteAbstractChange.class, name = "delete")
})
@JsonAutoDetect(fieldVisibility = JsonAutoDetect.Visibility.ANY)
public abstract class AbstractChange {
    private final String kind;
    private final String table;
    private final String schema;

    @JsonCreator
    public AbstractChange(
            @JsonProperty(value = "kind", required = true)
            final String kindInput,
            @JsonProperty(value = "table", required = true)
            final String tableInput,
            @JsonProperty(value = "schema", required = true)
            final String schemaInput
    ) {
        this.kind = kindInput;
        this.table = tableInput;
        this.schema = schemaInput;
    }

    public String getKind() {
        return kind;
    }

    public String getTable() {
        return table;
    }

    public String getSchema() {
        return schema;
    }

    /***
     * 获取列
     * @return 列名集合
     * */
    public abstract List<String> getColumnNames();
    /***
     * 或者值
     * @return 获取列值
     * */
    public abstract List<Object> getColumnValues();

    public Object getValueForColumn(final String columnName)
            throws UnknownColumnNameException {
        int columnIndex = getColumnNames().indexOf(columnName);
        if (columnIndex != -1) {
            return getColumnValues().get(columnIndex);
        } else {
            throw new UnknownColumnNameException(columnName);
        }
    }
}
