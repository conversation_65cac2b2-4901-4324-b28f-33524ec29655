package com.zenith.front.model.dto;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;

import java.util.Date;

/**
* <p>
* ccp_mem_flow_sign 实体类
* </p>
*
* <AUTHOR>
* @date 2025-01-22 09:43:28
*/
@Data
public class MemFlowSignSyncDTO {
    /**
    *唯一标识
    */
    private String id;
    /**
    *唯一标识
    */
    private String code;
    /**
    *党员code
    */
    private String memCode;
    /**
    *姓名
    */
    private String name;
    /**
    *性别code
    */
    private String sexCode;
    /**
    *性别名称
    */
    private String sexName;
    /**
    *身份证
    */
    private String idcard;
    /**
    *出生日期
    */
    private Date birthday;
    /**
    *民族code
    */
    private String d06Code;
    /**
    *民族名称
    */
    private String d06Name;
    /**
    *学历code
    */
    @TableField("d07_code")
    private String d07Code;
    /**
    *学历名称
    */
    private String d07Name;
    /**
    *学位code
    */
    private String d145Code;
    /**
    *学位名称
    */
    private String d145Name;
    /**
    *入党日期
    */
    private Date joinDate;
    /**
    *转正日期
    */
    private Date formalDate;
    /**
    *工作岗位
    */
    private String d09Code;
    /**
    *工作岗位名称
    */
    private String d09Name;
    /**
    *新社会阶层类型code
    */
    private String d20Code;
    /**
    *新社会阶层类型名称
    */
    private String d20Name;
    /**
    *从事专业技术职务
    */
    private String workPostCode;
    /**
     *从事专业技术职务
     */
    private String workPostName;
    /**
    *人员类别
    */
    private String d08Code;
    /**
    *人员类别名称
    */
    private String d08Name;
    /**
    *是否为农民
    */
    private String isFarmer;
    /**
    *手机号码
    */
    private String phone;
    /**
    *户籍所在地
    */
    private String houseHoldRegister;
    /**
    *家庭住址
    */
    private String homeAddress;
    /**
    *党龄校正值
    */
    private String ageCorrection;
    /**
     * 党员组织关系所在党组织code-改
     */
    private String orgFlowCode;

    /**
     * 党员组织关系所在党组织层级码-改
     */
    private String orgFlowLevelCode;
    /**
    *党员组织关系所在党组织名称-改
    */
    private String orgFlowName;

    /**
     * 流入地党支部code-改
     */
    private String applyOrgFlowCode;

    /**
     * 流入地党支部层级码-改
     */
    private String applyOrgFlowLevelCode;

    /**
     * 流入地党支部名称-改
     */
    private String applyOrgFlowName;

    /**
    *联系电话
    */
    private String connection;
    /**
    *流入地联系人
    */
    private String flowConnectionName;
    /**
    *流入地联系方式
    */
    private String flowConnection;
    /**
     * 党员在流入地常用联系方式-改
     */
    private String frequentlyPhone;
    /**
    *流动原因code
    */
    private String d146Code;
    /**
    *流动原因
    */
    private String d146Name;
    /**
    *流动原因详情
    */
    private String flowReasonDetail;
    /**
    *外出日期
    */
    private Date outDate;
    /**
    *外出地点补充说明-改
    */
    private String outInstructions;
    /**
    *党费缴至日期流出地
    */
    private Date ghanaDate;

    /**
     * 接收支部代码
     */
    private String receiveCode;
    /**
     *接收支部
     */
    private String receiveName;

    /**
    * 0-本节点  1-省内非本节点 2-中组部获取的省外数据
    */
    private String dataType;

    /**
     * 党组织联系方式
     */
    private String orgConnection;
    /**
     * 党组织联系人
     */
    private String orgConnectionName;
    /**
     *flow_up_code
     */
    private String flowUpCode;
    /**
     *接收提醒根节点代码
     */
    private String receiveNodeCode;
    /**
     *发送提醒根节代码
     */
    private String sendNodeCode;
    /**
     *流动提醒处理状态 1.办理中 2.已登记  3.已退回
     */
    private String flowWarnStatus;
    /**
     *流出地是否查看提醒 0-否 1-是
     */
    private String flowViewWarn;
    /**
     *nginx_key
     */
    private String nginxKey;
    /**
     *审批时间
     */
    private Date auditTime;
    /**
     *审批人ID
     */
    private String auditUserId;
    /**
     *审批人名称
     */
    private String auditUserName;
    /**
     *审批人组织ID
     */
    private String auditOrgId;
    /**
     *审批人组织层级码
     */
    private String auditOrgCode;
    /**
     *审批人组织名称
     */
    private String auditOrgName;
    /**
     *审批状态，0-待审批，1-审批通过，2-审批未通过
     */
    private String status;
    /**
     *拒绝原因，1-党员不存在，2-党员组织关系转接中，3-党员信息流转中
     */
    private String refuse;
    /**
     *说明理由
     */
    private String reason;

    /**
    *create_time
    */
    private Date createTime;
    /**
    *update_time
    */
    private Date updateTime;
    /**
    *delete_time
    */
    private Date deleteTime;
    /**
    *remark
    */
    private String remark;
    /**
    *timestamp
    */
    private Date timestamp;

    /**
     * 党员组织关系所在党组织区划码
     */
    private String orgFLowDivision;
}
