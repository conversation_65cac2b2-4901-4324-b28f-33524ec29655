package com.zenith.front.model.message;

import lombok.ToString;

/**
 * @param <T>
 * <AUTHOR>
 */
@ToString
public class OutMessage<T> {
    /***
     * 状态码
     * */
    private int code;
    /***
     * 状态消息
     * */
    private String message;
    /***
     * 返回数据
     * */
    private T data;

    public OutMessage() {

    }

    public OutMessage(Status status) {

        this.code = status.getCode();
        this.message = status.getMessage();
    }

    public OutMessage(Status status, T data) {

        this.code = status.getCode();
        this.message = status.getMessage();
        this.data = data;
    }

    public OutMessage(int code, String message, T data) {

        this.code = code;
        this.message = message;
        this.data = data;
    }

    public int getCode() {
        return code;
    }

    public String getMessage() {
        return message;
    }

    public T getData() {
        return data;
    }

    public void setCode(int code) {
        this.code = code;
    }

    public void setMessage(String message) {
        this.message = message;
    }

    public void setData(T data) {
        this.data = data;
    }

    public OutMessage<T> format(Object data) {
        this.message = String.format(this.message, data);
        return this;
    }

    public OutMessage<T> format(Object data, Object data1) {
        this.message = String.format(this.message, data, data1);
        return this;
    }

}
