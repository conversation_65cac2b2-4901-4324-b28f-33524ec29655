package com.zenith.front.model.dto;

/**
 * <AUTHOR>
 * @date 2019/4/23:07 PM
 */
public class LoginDTO {
    /**
     * 账号
     * */
    //@NotNull(groups = Common3Group.class,message = "账号不能为空")
    private String account;
    /***
     * 密码
     * */
    //@NotNull(groups = Common3Group.class,message = "密码不能为空")
    private String password;
    /***
     * 验证码
     * */
    //@NotNull(groups = Common3Group.class,message = "验证码不能为空")
    private String captchaCode;

    /**
     * 渝快政接入方式（appcode）
     * **/
    private String appcode;

    /**
     * 渝快政接入方式（token）
     * **/
    private String token;


    /**
     * 渝快政接入方式（targetUrl）

     * **/
    private String targetUrl;

    /**
     * 渝快政接入方式（openId）
     * **/
    private  String openId;

    private String uuid;

    private String ip;

    private String ua;

    /**
     * 登录key
     */
    private String ukey;

    public String getUkey() {
        return ukey;
    }

    public void setUkey(String ukey) {
        this.ukey = ukey;
    }

    public String getOpenId() {
        return openId;
    }

    public void setOpenId(String openId) {
        this.openId = openId;
    }

    public String getUuid() {
        return uuid;
    }

    public void setUuid(String uuid) {
        this.uuid = uuid;
    }

    public String getAccount() {
        return account;
    }

    public void setAccount(String account) {
        this.account = account;
    }

    public String getPassword() {
        return password;
    }

    public void setPassword(String password) {
        this.password = password;
    }

    public String getCaptchaCode() {
        return captchaCode;
    }

    public void setCaptchaCode(String captchaCode) {
        this.captchaCode = captchaCode;
    }

    public String getIp() {
        return ip;
    }

    public LoginDTO setIp(String ip) {
        this.ip = ip;
        return this;
    }

    public String getUa() {
        return ua;
    }

    public LoginDTO setUa(String ua) {
        this.ua = ua;
        return this;
    }

    public String getAppcode() {
        return appcode;
    }

    public void setAppcode(String appcode) {
        this.appcode = appcode;
    }

    public String getToken() {
        return token;
    }

    public void setToken(String token) {
        this.token = token;
    }

    public String getTargetUrl() {
        return targetUrl;
    }

    public void setTargetUrl(String targetUrl) {
        this.targetUrl = targetUrl;
    }

    @Override
    public String toString() {
        return "LoginDTO{" +
                "account='" + account + '\'' +
                ", password='" + password + '\'' +
                ", captchaCode='" + captchaCode + '\'' +
                '}';
    }
}
