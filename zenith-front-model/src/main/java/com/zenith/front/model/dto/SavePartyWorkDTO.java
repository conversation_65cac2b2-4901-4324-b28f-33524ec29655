package com.zenith.front.model.dto;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2021/09/27
 */
public class SavePartyWorkDTO implements Serializable {

    private static final long serialVersionUID = 8662323545297865532L;

    /**
     * 组织机构
     */
    @NotBlank(message = "组织机构不能为空")
    private String orgCode;

    /**
     * 人员code
     */
    @NotEmpty(message = "人员code不能为空")
    private List<String> memCode;

    public String getOrgCode() {
        return orgCode;
    }

    public void setOrgCode(String orgCode) {
        this.orgCode = orgCode;
    }

    public List<String> getMemCode() {
        return memCode;
    }

    public void setMemCode(List<String> memCode) {
        this.memCode = memCode;
    }

    @Override
    public String toString() {
        return "SavePartyWorkDTO{" +
                "orgCode='" + orgCode + '\'' +
                ", memCode=" + memCode +
                '}';
    }
}
