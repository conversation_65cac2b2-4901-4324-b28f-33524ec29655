package com.zenith.front.model.dto;

import cn.hutool.core.util.StrUtil;
import com.zenith.front.common.constant.UserConstant;
import com.zenith.front.model.bean.User;
import com.zenith.front.model.validate.group.*;
import lombok.ToString;
import org.hibernate.validator.constraints.Length;
import org.hibernate.validator.constraints.Range;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Null;
import javax.validation.constraints.Pattern;
import java.util.List;

/***
 * 用户相关DTO
 * <AUTHOR>
 * */
@ToString
public class UserDTO {

    /**
     * 用户ID,uuid,不能为null
     */
    @NotBlank(groups = {QueryGroup.class, DeleteGroup.class, UpdateGroup.class, BatchUpdateGroup.class, IDGroup.class}, message = "用户id不能为空")
    private String id;
    /**
     * 用户账号,不能为null
     */
    @NotBlank(groups = {AddGroup.class, Common1Group.class}, message = "账号不能为空")
    @Pattern(groups = {AddGroup.class, Common1Group.class}, regexp = com.zenith.front.common.constant.UserConstant.ACCOUNT_MATCH, message = "用户名只能是数字字母")
    private String account;
    /**
     * 用户密码,不能为null
     */
    @Length(groups = {AddGroup.class, Common2Group.class, UpdateGroup.class, BatchUpdateGroup.class}, min = 6, max = 16, message = "密码长度必须为6-12")
    @NotBlank(groups = {AddGroup.class, Common2Group.class}, message = "密码不能为空")
    private String password;
    /***
     * 用户旧密码 用于修改自己的密码 时 需要的属性
     * */
    private String oldPassword;
    /**
     * 用户名称,不能为null
     */
    @NotBlank(groups = UpdateGroup.class, message = "用户名称不能为空")
    @Null(groups = BatchUpdateGroup.class, message = "不能批量修改用户名称")
    private String name;
    /**
     * 用户手机号,允许为null
     */
    @Null(groups = BatchUpdateGroup.class, message = "不能批量修改用户手机号")
    private String phone;

    /**
     * 0 未删除 1已删除 默认为0
     */
    private Integer isDelete;
    /**
     * 0 为锁定 1已锁定 默认为0
     */
    @Range(groups = {UpdateGroup.class, BatchUpdateGroup.class}, min = UserConstant.UNLOCK, max = UserConstant.LOCK)
    private Integer isLock;
    /**
     * 创建时间,不允许为null
     */
    private java.util.Date createTime;
    /**
     * 更新时间,不允许为null,默认值是createTime
     */
    private java.util.Date updateTime;
    /**
     * 创建者,只能是account
     */
    private String createAccount;
    /**
     * 组织id
     */
    @NotBlank(groups = AddGroup.class, message = "用户所在组织id不能为空")
    @Null(groups = BatchUpdateGroup.class, message = "不能批量修改用户所在组织")
    private String orgId;
    /**
     * 组织code
     */
    @NotBlank(groups = AddGroup.class, message = "用户所在组织code不能为空")
    @Null(groups = BatchUpdateGroup.class, message = "不能批量修改用户所在组织")
    private String orgCode;
    /**
     * 组织名称
     */
    private String orgName;
    /**
     * 0 不只读 1是只读
     */
    @NotNull(groups = AddGroup.class, message = "用户是否只读不能为空")
    @Range(groups = {AddGroup.class, UpdateGroup.class, BatchUpdateGroup.class}, min = UserConstant.NOT_IS_READ_ONLY, max = UserConstant.IS_READ_ONLY, message = "只读状态码错误应是0或1")
    private Integer readOnly;
    /**
     * 关联人员code
     */
    private String memCode;
    /**
     * 关联党员名称
     */
    private String memName;
    /***
     * 人员所在组织id
     * */
    private String memOrgId;
    /****
     * 人员所在组织code
     * */
    private String memOrgCode;
    /**
     * 人员所在组织名称
     */
    private String memOrgName;

    /**
     * 修改者账号
     */
    private String updateAccount;
    /**
     * 最后登录时间
     */
    private java.util.Date lastLoginTime;

    private String ip;

    private Long loginCount;
    /***
     * 是否是微信管理员
     * */
    private boolean isWxAdmin;

    /***
     * 用户相关角色权限信息
     * */
    @Valid
    private List<UserRolePermissionInfo> manages;

    /**
     * 管理系统 默认综合党务管理系统
     */
    @NotBlank(groups = AddGroup.class, message = "用户管理系统不能为空")
    private String managementSystem;

    /**
     * 密评序列号
     */
    private String ukey;

    public String getUkey() {
        return ukey;
    }

    public void setUkey(String ukey) {
        this.ukey = ukey;
    }
    /**
     * 节点用户标记: 1（遵义用户）
     */
    private Integer isNodeManage;
    /**
     * 是否登录时判断需要修改密码
     */
    private String isLoginUpdatePassword;

    public Integer getIsNodeManage() {
        // 遵义用户标记
        if(StrUtil.isNotBlank(orgCode) && StrUtil.startWithAny(orgCode, UserConstant.ZUN_YI_ORG_LEVEL_CODE)){
            return 1;
        }
        return 0;
    }

    public void setIsNodeManage(Integer isNodeManage) {
        this.isNodeManage = isNodeManage;
    }

    public String getMemOrgId() {
        return memOrgId;
    }

    public UserDTO setMemOrgId(String memOrgId) {
        this.memOrgId = memOrgId;
        return this;
    }

    public String getMemOrgCode() {
        return memOrgCode;
    }

    public UserDTO setMemOrgCode(String memOrgCode) {
        this.memOrgCode = memOrgCode;
        return this;
    }

    public String getMemOrgName() {
        return memOrgName;
    }

    public UserDTO setMemOrgName(String memOrgName) {
        this.memOrgName = memOrgName;
        return this;
    }

    public UserDTO setId(String id) {
        this.id = id;
        return this;
    }

    public String getId() {
        return this.id;
    }

    public UserDTO setAccount(String account) {
        this.account = account;
        return this;
    }

    public String getAccount() {
        return this.account;
    }

    public UserDTO setPassword(String password) {
        this.password = password;
        return this;
    }

    public String getPassword() {
        return this.password;
    }

    public UserDTO setName(String name) {
        this.name = name;
        return this;
    }

    public String getName() {
        return this.name;
    }

    public UserDTO setPhone(String phone) {
        this.phone = phone;
        return this;
    }

    public String getPhone() {
        return this.phone;
    }

    public UserDTO setIsDelete(Integer isDelete) {
        this.isDelete = isDelete;
        return this;
    }

    public Integer getIsDelete() {
        return this.isDelete;
    }

    public UserDTO setIsLock(Integer isLock) {
        this.isLock = isLock;
        return this;
    }

    public Integer getIsLock() {
        return this.isLock;
    }

    public UserDTO setCreateTime(java.util.Date createTime) {
        this.createTime = createTime;
        return this;
    }

    public java.util.Date getCreateTime() {
        return this.createTime;
    }

    public UserDTO setUpdateTime(java.util.Date updateTime) {
        this.updateTime = updateTime;
        return this;
    }

    public java.util.Date getUpdateTime() {
        return this.updateTime;
    }

    public UserDTO setCreateAccount(String createAccount) {
        this.createAccount = createAccount;
        return this;
    }

    public String getCreateAccount() {
        return this.createAccount;
    }

    public UserDTO setOrgId(String orgId) {
        this.orgId = orgId;
        return this;
    }

    public String getOrgId() {
        return this.orgId;
    }

    public UserDTO setReadOnly(Integer readOnly) {
        this.readOnly = readOnly;
        return this;
    }

    public Integer getReadOnly() {
        return this.readOnly;
    }

    public UserDTO setMemCode(String memCode) {
        this.memCode = memCode;
        return this;
    }

    public String getMemCode() {
        return this.memCode;
    }

    public UserDTO setOrgCode(String orgCode) {
        this.orgCode = orgCode;
        return this;
    }

    public String getOrgCode() {
        return this.orgCode;
    }

    public UserDTO setUpdateAccount(String updateAccount) {
        this.updateAccount = updateAccount;
        return this;
    }

    public String getUpdateAccount() {
        return this.updateAccount;
    }

    public UserDTO setLastLoginTime(java.util.Date lastLoginTime) {
        this.lastLoginTime = lastLoginTime;
        return this;
    }

    public java.util.Date getLastLoginTime() {
        return this.lastLoginTime;
    }

    public String getOrgName() {
        return orgName;
    }

    public void setOrgName(String orgName) {
        this.orgName = orgName;
    }

    public List<UserRolePermissionInfo> getManages() {
        return manages;
    }

    public void setManages(List<UserRolePermissionInfo> manages) {
        this.manages = manages;
    }

    public String getOldPassword() {
        return oldPassword;
    }

    public void setOldPassword(String oldPassword) {
        this.oldPassword = oldPassword;
    }

    public String getIp() {
        return ip;
    }

    public UserDTO setIp(String ip) {
        this.ip = ip;
        return this;
    }

    public Long getLoginCount() {
        return loginCount;
    }

    public UserDTO setLoginCount(Long loginCount) {
        this.loginCount = loginCount;
        return this;
    }

    public String getMemName() {
        return memName;
    }

    public UserDTO setMemName(String memName) {
        this.memName = memName;
        return this;
    }

    public boolean isWxAdmin() {
        return isWxAdmin;
    }

    public UserDTO setWxAdmin(boolean wxAdmin) {
        isWxAdmin = wxAdmin;
        return this;
    }

    public String getManagementSystem() {
        return managementSystem;
    }

    public void setManagementSystem(String managementSystem) {
        this.managementSystem = managementSystem;
    }

    public User toModel() {
        User model = new User();
        model.setId(this.id);
        model.setAccount(this.account);
        model.setPassword(this.password);
        model.setName(this.name);
        model.setPhone(this.phone);
        model.setIsDelete(this.isDelete);
        model.setIsLock(this.isLock);
        model.setCreateTime(this.createTime);
        model.setUpdateTime(this.updateTime);
        model.setCreateAccount(this.createAccount);
        model.setOrgId(this.orgId);
        model.setReadOnly(this.readOnly);
        model.setMemCode(this.memCode);
        model.setOrgCode(this.orgCode);
        model.setUpdateAccount(this.updateAccount);
        model.setLastLoginTime(this.lastLoginTime);
        return model;
    }

    public static class UserRolePermissionInfo {

        /**
         * 用户管理组织code
         */
        @NotNull(groups = UpdateGroup.class, message = "用户管理组织code不能为空")
        private String managerOrgCode;
        /**
         * 用户管理组织id
         */
        @NotNull(groups = UpdateGroup.class, message = "用户管理组织id不能为空")
        private String managerOrgId;
        /**
         * 用户管理组织名称
         */
        private String managerOrgName;
        /**
         * 用户管理组织类型
         **/
        private String managerOrgType;

        /***
         * 用户角色id
         * */
        @NotNull(groups = {UpdateGroup.class, BatchUpdateGroup.class}, message = "用户角色id不能为空")
        private String roleId;
        /**
         * 用户角色类型
         */
        private Integer roleType;
        /***
         * 用户角色名称
         * */
        private String roleName;
        /***
         * 用户权限码
         * */
        private String permissionCode;
        /***
         * 用户权限id
         * */
        private String userPermissionId;
        /***
         * 是否是默认角色
         * */
        @NotNull(groups = UpdateGroup.class, message = "是否是默认角色不能为空")
        @Range(groups = UpdateGroup.class, min = 0, max = 1, message = "是否默认只能在0-1之前两个状态")
        private Integer isDefault;
        /**
         * 当前角色是否过期
         */
        private Integer isExpired;

        public String getManagerOrgCode() {
            return managerOrgCode;
        }

        public void setManagerOrgCode(String managerOrgCode) {
            this.managerOrgCode = managerOrgCode;
        }

        public String getManagerOrgId() {
            return managerOrgId;
        }

        public void setManagerOrgId(String managerOrgId) {
            this.managerOrgId = managerOrgId;
        }

        public String getManagerOrgName() {
            return managerOrgName;
        }

        public void setManagerOrgName(String managerOrgName) {
            this.managerOrgName = managerOrgName;
        }

        public String getRoleId() {
            return roleId;
        }

        public void setRoleId(String roleId) {
            this.roleId = roleId;
        }

        public Integer getRoleType() {
            return roleType;
        }

        public void setRoleType(Integer roleType) {
            this.roleType = roleType;
        }

        public String getRoleName() {
            return roleName;
        }

        public void setRoleName(String roleName) {
            this.roleName = roleName;
        }

        public String getPermissionCode() {
            return permissionCode;
        }

        public void setPermissionCode(String permissionCode) {
            this.permissionCode = permissionCode;
        }

        public Integer getIsDefault() {
            return isDefault;
        }

        public void setIsDefault(Integer isDefault) {
            this.isDefault = isDefault;
        }

        public String getUserPermissionId() {
            return userPermissionId;
        }

        public void setUserPermissionId(String userPermissionId) {
            this.userPermissionId = userPermissionId;
        }

        public Integer getIsExpired() {
            return isExpired;
        }

        public void setIsExpired(Integer isExpired) {
            this.isExpired = isExpired;
        }

        public String getManagerOrgType() {
            return managerOrgType;
        }

        public UserRolePermissionInfo setManagerOrgType(String managerOrgType) {
            this.managerOrgType = managerOrgType;
            return this;
        }
    }

    public String getIsLoginUpdatePassword() {
        return isLoginUpdatePassword;
    }

    public void setIsLoginUpdatePassword(String isLoginUpdatePassword) {
        this.isLoginUpdatePassword = isLoginUpdatePassword;
    }
}