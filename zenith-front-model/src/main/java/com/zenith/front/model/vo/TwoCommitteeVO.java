package com.zenith.front.model.vo;

import com.zenith.front.common.encrypt.annotation.EncryptEnabled;
import com.zenith.front.common.encrypt.annotation.EncryptField;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

import java.util.Date;

/**
 * <AUTHOR>
 * @Date 2021/7/16 19:37
 * @Version 1.0
 */
@Data
@ToString
@NoArgsConstructor
@AllArgsConstructor
@EncryptEnabled
public class TwoCommitteeVO {
    private Integer isCreateOrg;
    private String manageOrgCode;
    private String mainUnitOrgCode;
    private String memCode;
    @EncryptField(order = 1)
    private String memName;
    private String d25Name;
    private String d26Name;
    @EncryptField(order = 2)
    private String memIdcard;
    private String isIncumbent;
    private String remark;
    private String name;
    private String sexName;
    private String joinOrgDate;
    private String fullMemberDate;
    private String isFarmer;
    private String d07Name;

    private String d19Name;
    @EncryptField(order = 3)
    private String idcard;
    @EncryptField(order = 4)
    private String phone;
    private Date birthday;
    @EncryptField(order = 5)
    private String d022Name;
    private String orgCommitteeMemCode;
    private String orgName;
    private String parentOrgName;
    private String politicsCode;
    private String d22Name;
    private Integer age;
    private String d144Code;
}
