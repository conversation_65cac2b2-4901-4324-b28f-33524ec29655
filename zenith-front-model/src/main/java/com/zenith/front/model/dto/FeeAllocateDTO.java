package com.zenith.front.model.dto;

import com.zenith.front.model.bean.FeeAllocate;
import com.zenith.front.model.validate.group.Common1Group;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

@Data
public class FeeAllocateDTO {

    private Long id;
    @NotBlank(groups = Common1Group.class, message = "code 不能为空")
    private String code;
    /**
     * 组织唯一标示
     */
    private String orgCode;
    /**
     * 组织层级码
     */
    private String allocateOrgCode;
    /**
     * 正式党员数量
     */
    @NotNull(groups = Common1Group.class, message = "fullMemNum 不能为空")
    private Integer fullMemNum;
    /**
     * 预备党员数量
     */
    @NotNull(groups = Common1Group.class, message = "proMemNum 不能为空")
    private Integer proMemNum;
    /**
     * 离退休党员数量
     */
    @NotNull(groups = Common1Group.class, message = "retirementMemNum 不能为空")
    private Integer retirementMemNum;
    /**
     * 初始时所在组织code集合
     */
    private String initOrgCodeSet;
    /**
     * 下拨比例
     */
    @NotNull(groups = Common1Group.class, message = "allocateRatio 不能为空")
    private java.math.BigDecimal allocateRatio;
    /**
     * 下拨金额
     */
    @NotNull(groups = Common1Group.class, message = "allocateMoney 不能为空")
    private java.math.BigDecimal allocateMoney;
    /**
     * 下拨类型(0--手动输入,1--自动计算)
     */
    @NotBlank(groups = Common1Group.class, message = "allocateType 不能为空")
    private String allocateType;
    /**
     * 是否下拨(0--否,1--是)
     */
    @NotBlank(groups = Common1Group.class, message = "isAllocate 不能为空")
    private String isAllocate;
    private String updateAccount;
    private java.util.Date createTime;
    private java.util.Date updateTime;
    private java.util.Date deleteTime;
    /**
     * 下拨时间(月份)
     */
    private java.util.Date allocateTime;
    /**
     * 金额
     */
    private java.math.BigDecimal money;

    /**
     * 应收金额
     */
    private java.math.BigDecimal shouldPayMoney;

    public FeeAllocate toModel() {
        FeeAllocate model = new FeeAllocate();
        model.setId(this.id);
        model.setCode(this.code);
        model.setOrgCode(this.orgCode);
        model.setAllocateOrgCode(this.allocateOrgCode);
        model.setFullMemNum(this.fullMemNum);
        model.setProMemNum(this.proMemNum);
        model.setRetirementMemNum(this.retirementMemNum);
        model.setInitOrgCodeSet(this.initOrgCodeSet);
        model.setAllocateRatio(this.allocateRatio);
        model.setAllocateMoney(this.allocateMoney);
        model.setAllocateType(this.allocateType);
        model.setIsAllocate(this.isAllocate);
        model.setUpdateAccount(this.updateAccount);
        model.setCreateTime(this.createTime);
        model.setUpdateTime(this.updateTime);
        model.setDeleteTime(this.deleteTime);
        model.setAllocateTime(this.allocateTime);
        model.setMoney(this.money);
        model.setShouldPayMoney(this.shouldPayMoney);
        return model;
    }
}