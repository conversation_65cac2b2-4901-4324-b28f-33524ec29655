package com.zenith.front.model.bean;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableField;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 * 单位街道干部表
 * </p>
 *
 * <AUTHOR>
 * @since 2021-10-08
 */
@TableName("ccp_unit_streets_cadres")
public class UnitStreetsCadres extends Model<UnitStreetsCadres> {

    private static final long serialVersionUID = 1L;

    /**
     * 唯一key
     */
      @TableId(value = "code", type = IdType.ASSIGN_UUID)
    private String code;

    /**
     * es_id
     */
    @TableField("es_id")
    private String esId;

    /**
     * 关联单位_代码
     */
    @TableField("unit_code")
    private String unitCode;

    /**
     * 关联组织_代码
     */
    @TableField("org_code")
    private String orgCode;

    /**
     * 关联组织_名称
     */
    @TableField("org_name")
    private String orgName;

    /**
     * 年份
     */
    @TableField("year")
    private Integer year;

    /**
     * 街道干部人数
     */
    @TableField("street_cadres")
    private Integer streetCadres;

    /**
     * 街道干部35岁及以下人数
     */
    @TableField("age35_below")
    private Integer age35Below;

    /**
     * 街道干部36至55岁人数
     */
    @TableField("age36_to_age55")
    private Integer age36ToAge55;

    /**
     * 街道干部56岁及以上人数
     */
    @TableField("age_56_above")
    private Integer age56Above;

    /**
     * 街道干部大专及以上学历人数
     */
    @TableField("college_degree_above")
    private Integer collegeDegreeAbove;

    /**
     * 街道干部高中中专及以下人数
     */
    @TableField("secondary_school_below")
    private Integer secondarySchoolBelow;

    /**
     * 街道干部公务员人数
     */
    @TableField("street_cadres_civil")
    private Integer streetCadresCivil;

    /**
     * 街道干部事业单位人数
     */
    @TableField("street_cadres_institutions")
    private Integer streetCadresInstitutions;

    /**
     * 街道干部其他身份人数
     */
    @TableField("cadre_other")
    private Integer cadreOther;

    @TableField("create_time")
    private Date createTime;

    @TableField("update_time")
    private Date updateTime;

    @TableField("delete_time")
    private Date deleteTime;


    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getEsId() {
        return esId;
    }

    public void setEsId(String esId) {
        this.esId = esId;
    }

    public String getUnitCode() {
        return unitCode;
    }

    public void setUnitCode(String unitCode) {
        this.unitCode = unitCode;
    }

    public String getOrgCode() {
        return orgCode;
    }

    public void setOrgCode(String orgCode) {
        this.orgCode = orgCode;
    }

    public String getOrgName() {
        return orgName;
    }

    public void setOrgName(String orgName) {
        this.orgName = orgName;
    }

    public Integer getYear() {
        return year;
    }

    public void setYear(Integer year) {
        this.year = year;
    }

    public Integer getStreetCadres() {
        return streetCadres;
    }

    public void setStreetCadres(Integer streetCadres) {
        this.streetCadres = streetCadres;
    }

    public Integer getAge35Below() {
        return age35Below;
    }

    public void setAge35Below(Integer age35Below) {
        this.age35Below = age35Below;
    }

    public Integer getAge36ToAge55() {
        return age36ToAge55;
    }

    public void setAge36ToAge55(Integer age36ToAge55) {
        this.age36ToAge55 = age36ToAge55;
    }

    public Integer getAge56Above() {
        return age56Above;
    }

    public void setAge56Above(Integer age56Above) {
        this.age56Above = age56Above;
    }

    public Integer getCollegeDegreeAbove() {
        return collegeDegreeAbove;
    }

    public void setCollegeDegreeAbove(Integer collegeDegreeAbove) {
        this.collegeDegreeAbove = collegeDegreeAbove;
    }

    public Integer getSecondarySchoolBelow() {
        return secondarySchoolBelow;
    }

    public void setSecondarySchoolBelow(Integer secondarySchoolBelow) {
        this.secondarySchoolBelow = secondarySchoolBelow;
    }

    public Integer getStreetCadresCivil() {
        return streetCadresCivil;
    }

    public void setStreetCadresCivil(Integer streetCadresCivil) {
        this.streetCadresCivil = streetCadresCivil;
    }

    public Integer getStreetCadresInstitutions() {
        return streetCadresInstitutions;
    }

    public void setStreetCadresInstitutions(Integer streetCadresInstitutions) {
        this.streetCadresInstitutions = streetCadresInstitutions;
    }

    public Integer getCadreOther() {
        return cadreOther;
    }

    public void setCadreOther(Integer cadreOther) {
        this.cadreOther = cadreOther;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    public Date getDeleteTime() {
        return deleteTime;
    }

    public void setDeleteTime(Date deleteTime) {
        this.deleteTime = deleteTime;
    }

    @Override
    protected Serializable pkVal() {
        return this.code;
    }

    @Override
    public String toString() {
        return "UnitStreetsCadres{" +
        "code=" + code +
        ", esId=" + esId +
        ", unitCode=" + unitCode +
        ", orgCode=" + orgCode +
        ", orgName=" + orgName +
        ", year=" + year +
        ", streetCadres=" + streetCadres +
        ", age35Below=" + age35Below +
        ", age36ToAge55=" + age36ToAge55 +
        ", age56Above=" + age56Above +
        ", collegeDegreeAbove=" + collegeDegreeAbove +
        ", secondarySchoolBelow=" + secondarySchoolBelow +
        ", streetCadresCivil=" + streetCadresCivil +
        ", streetCadresInstitutions=" + streetCadresInstitutions +
        ", cadreOther=" + cadreOther +
        ", createTime=" + createTime +
        ", updateTime=" + updateTime +
        ", deleteTime=" + deleteTime +
        "}";
    }
}
