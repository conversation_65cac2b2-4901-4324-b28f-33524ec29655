package com.zenith.front.model.bean;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;

import java.util.Date;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2021-10-05
 */
@TableName("ccp_unit_secondary")
public class UnitSecondary extends Model<UnitSecondary> {

    private static final long serialVersionUID = 1L;

    /**
     * 唯一标识符code
     */
    @TableField("code")
    private String code;

    /**
     * 唯一自增长id
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * es唯一主键
     */
    @TableField("es_id")
    private String esId;

    /**
     * 院系名称
     */
    @TableField("faculty_name")
    private String facultyName;

    /**
     * 党组类别代码
     */
    @TableField("d110_code")
    private String d110Code;


    /**
     * 党组类别名称
     */
    @TableField("d110_name")
    private String d110Name;


    /**
     * 关联单位_代码
     */
    @TableField("unit_code")
    private String unitCode;


    /**
     * 关联组织名称
     */
    @TableField("org_code")
    private String orgCode;

    /**
     * 关联组织代码
     */
    @TableField("org_name")
    private String orgName;

    /**
     * 备注
     */
    @TableField("remark")
    private String remark;

    /**
     * 建立时间
     */
    @TableField("create_time")
    private Date createTime;

    /**
     * 更新时间
     */
    @TableField("update_time")
    private Date updateTime;

    /**
     * 删除时间
     */
    @TableField("delete_time")
    private Date deleteTime;

    /**
     * 时间戳
     */
    @TableField("timestamp")
    private Date timestamp;

    /**
     * 最近更更新账号
     */
    @TableField("update_account")
    private String updateAccount;


    @Override
    public String toString() {
        return "UnitSecondary{" +
                "code='" + code + '\'' +
                ", id=" + id +
                ", esId='" + esId + '\'' +
                ", facultyName='" + facultyName + '\'' +
                ", d110Code='" + d110Code + '\'' +
                ", d110Name='" + d110Name + '\'' +
                ", unitCode='" + unitCode + '\'' +
                ", orgCode='" + orgCode + '\'' +
                ", orgName='" + orgName + '\'' +
                ", remark='" + remark + '\'' +
                ", createTime=" + createTime +
                ", updateTime=" + updateTime +
                ", deleteTime=" + deleteTime +
                ", timestamp=" + timestamp +
                ", updateAccount='" + updateAccount + '\'' +
                '}';
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getEsId() {
        return esId;
    }

    public void setEsId(String esId) {
        this.esId = esId;
    }

    public String getFacultyName() {
        return facultyName;
    }

    public void setFacultyName(String facultyName) {
        this.facultyName = facultyName;
    }

    public String getD110Code() {
        return d110Code;
    }

    public void setD110Code(String d110Code) {
        this.d110Code = d110Code;
    }

    public String getD110Name() {
        return d110Name;
    }

    public void setD110Name(String d110Name) {
        this.d110Name = d110Name;
    }

    public String getUnitCode() {
        return unitCode;
    }

    public void setUnitCode(String unitCode) {
        this.unitCode = unitCode;
    }

    public String getOrgCode() {
        return orgCode;
    }

    public void setOrgCode(String orgCode) {
        this.orgCode = orgCode;
    }

    public String getOrgName() {
        return orgName;
    }

    public void setOrgName(String orgName) {
        this.orgName = orgName;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    public Date getDeleteTime() {
        return deleteTime;
    }

    public void setDeleteTime(Date deleteTime) {
        this.deleteTime = deleteTime;
    }

    public Date getTimestamp() {
        return timestamp;
    }

    public void setTimestamp(Date timestamp) {
        this.timestamp = timestamp;
    }

    public String getUpdateAccount() {
        return updateAccount;
    }

    public void setUpdateAccount(String updateAccount) {
        this.updateAccount = updateAccount;
    }
}
