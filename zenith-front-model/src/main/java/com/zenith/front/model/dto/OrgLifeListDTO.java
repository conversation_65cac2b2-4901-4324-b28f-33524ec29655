package com.zenith.front.model.dto;

import java.util.Date;
import java.util.List;

public class OrgLifeListDTO {
    /**
     * 分页
     */
    private Integer pageNum;
    private Integer pageSize;

    /**
     * 主键code
     */
    private String code;

    public String getOrgGropCode() {
        return orgGropCode;
    }

    public void setOrgGropCode(String orgGropCode) {
        this.orgGropCode = orgGropCode;
    }

    /**
     * 党小组会参会党小组code
     */
    private String orgGropCode;

    /**
     * 组织code
     */
    private String orgCode;

    /**
     * 活动名称
     */
    private String activityName;

    /**
     * 开始时间
     */
    private Date startTime;

    /**
     * 结束时间
     */
    private Date endTime;

    /**
     * 组织生活类型code
     */
    private List<String> d158Code;

    /**
     * 参与党支部层级码
     */
    private String joinOrgLevelCodeList;

    /**
     * 党组织类型
     */
    private String d01Code;

    public Integer getPageNum() {
        return pageNum;
    }

    public void setPageNum(Integer pageNum) {
        this.pageNum = pageNum;
    }

    public Integer getPageSize() {
        return pageSize;
    }

    public void setPageSize(Integer pageSize) {
        this.pageSize = pageSize;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getOrgCode() {
        return orgCode;
    }

    public void setOrgCode(String orgCode) {
        this.orgCode = orgCode;
    }

    public String getActivityName() {
        return activityName;
    }

    public void setActivityName(String activityName) {
        this.activityName = activityName;
    }

    public Date getStartTime() {
        return startTime;
    }

    public void setStartTime(Date startTime) {
        this.startTime = startTime;
    }

    public Date getEndTime() {
        return endTime;
    }

    public void setEndTime(Date endTime) {
        this.endTime = endTime;
    }

    public List<String> getD158Code() {
        return d158Code;
    }

    public void setD158Code(List<String> d158Code) {
        this.d158Code = d158Code;
    }

    public String getJoinOrgLevelCodeList() {
        return joinOrgLevelCodeList;
    }

    public void setJoinOrgLevelCodeList(String joinOrgLevelCodeList) {
        this.joinOrgLevelCodeList = joinOrgLevelCodeList;
    }

    public String getD01Code() {
        return d01Code;
    }

    public void setD01Code(String d01Code) {
        this.d01Code = d01Code;
    }
}
