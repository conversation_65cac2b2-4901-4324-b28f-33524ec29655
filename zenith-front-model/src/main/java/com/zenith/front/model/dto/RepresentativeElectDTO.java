package com.zenith.front.model.dto;

import com.zenith.front.model.bean.RepresentativeElect;
import com.zenith.front.model.validate.group.Common1Group;
import com.zenith.front.model.validate.group.Common5Group;
import com.zenith.front.model.validate.group.UpdateGroup;
import lombok.ToString;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

@ToString
public class RepresentativeElectDTO {

    private Long id;
    @NotBlank(groups = UpdateGroup.class, message = "code 不能为空")
    private String code;
    private String esId;
    /**
     * 组织层级码
     */
    private String electOrgCode;

    private String levelCode;
    /**
     * 届次所属单位code
     */
    @NotBlank(groups = {Common1Group.class, Common5Group.class}, message = "unitCode 不能为空")
    private String unitCode;
    /**
     * 届次所属单位的name
     */
    @NotBlank(groups = Common1Group.class, message = "unitName 不能为空")
    private String unitName;
    /**
     * 届次类别
     */
    @NotBlank(groups = Common1Group.class, message = "d61Code 不能为空")
    private String d61Code;
    @NotBlank(groups = Common1Group.class, message = "d61Name 不能为空")
    private String d61Name;
    /**
     * 届次数
     */
    @Min(groups = Common1Group.class, value = 1, message = "最小值不能小于1")
    private Long electNum;
    /**
     * 届次名称
     */
    @NotBlank(groups = Common1Group.class, message = "electName 不能为空")
    private String electName;
    /**
     * 起始日期
     */
    @NotNull(groups = {Common1Group.class, Common5Group.class}, message = "startDate 不能为空")
    private java.util.Date startDate;
    /**
     * 届满日期
     */
    @NotNull(groups = {Common1Group.class, Common5Group.class}, message = "endDate 不能为空")
    private java.util.Date endDate;
    /**
     * 是否当前届次
     */
    @NotNull(groups = Common1Group.class, message = "isNowElect 不能为空")
    private Integer isNowElect;
    /**
     * 是否试行乡试
     */
    @NotNull(groups = Common1Group.class, message = "isCountryTest 不能为空")
    private Integer isCountryTest;
    private java.util.Date createTime;
    private java.util.Date updateTime;
    private java.util.Date timestamp;
    private java.util.Date deleteTime;
    private String zbCode;
    /**
     * 组织code
     */
    private String orgCode;
    private Integer isHistory;
    private String updateAccount;

    public String getLevelCode() {
        return levelCode;
    }

    public void setLevelCode(String levelCode) {
        this.levelCode = levelCode;
    }

    public RepresentativeElectDTO setId(Long id) {
        this.id = id;
        return this;
    }

    public Long getId() {
        return this.id;
    }

    public RepresentativeElectDTO setCode(String code) {
        this.code = code;
        return this;
    }

    public String getCode() {
        return this.code;
    }

    public RepresentativeElectDTO setEsId(String esId) {
        this.esId = esId;
        return this;
    }

    public String getEsId() {
        return this.esId;
    }

    public RepresentativeElectDTO setElectOrgCode(String electOrgCode) {
        this.electOrgCode = electOrgCode;
        return this;
    }

    public String getElectOrgCode() {
        return this.electOrgCode;
    }

    public RepresentativeElectDTO setUnitCode(String unitCode) {
        this.unitCode = unitCode;
        return this;
    }

    public String getUnitCode() {
        return this.unitCode;
    }

    public RepresentativeElectDTO setUnitName(String unitName) {
        this.unitName = unitName;
        return this;
    }

    public String getUnitName() {
        return this.unitName;
    }

    public RepresentativeElectDTO setD61Code(String d61Code) {
        this.d61Code = d61Code;
        return this;
    }

    public String getD61Code() {
        return this.d61Code;
    }

    public RepresentativeElectDTO setD61Name(String d61Name) {
        this.d61Name = d61Name;
        return this;
    }

    public String getD61Name() {
        return this.d61Name;
    }

    public RepresentativeElectDTO setElectNum(Long electNum) {
        this.electNum = electNum;
        return this;
    }

    public Long getElectNum() {
        return this.electNum;
    }

    public RepresentativeElectDTO setElectName(String electName) {
        this.electName = electName;
        return this;
    }

    public String getElectName() {
        return this.electName;
    }

    public RepresentativeElectDTO setStartDate(java.util.Date startDate) {
        this.startDate = startDate;
        return this;
    }

    public java.util.Date getStartDate() {
        return this.startDate;
    }

    public RepresentativeElectDTO setEndDate(java.util.Date endDate) {
        this.endDate = endDate;
        return this;
    }

    public java.util.Date getEndDate() {
        return this.endDate;
    }

    public RepresentativeElectDTO setIsNowElect(Integer isNowElect) {
        this.isNowElect = isNowElect;
        return this;
    }

    public Integer getIsNowElect() {
        return this.isNowElect;
    }

    public RepresentativeElectDTO setIsCountryTest(Integer isCountryTest) {
        this.isCountryTest = isCountryTest;
        return this;
    }

    public Integer getIsCountryTest() {
        return this.isCountryTest;
    }

    public RepresentativeElectDTO setCreateTime(java.util.Date createTime) {
        this.createTime = createTime;
        return this;
    }

    public java.util.Date getCreateTime() {
        return this.createTime;
    }

    public RepresentativeElectDTO setUpdateTime(java.util.Date updateTime) {
        this.updateTime = updateTime;
        return this;
    }

    public java.util.Date getUpdateTime() {
        return this.updateTime;
    }

    public RepresentativeElectDTO setTimestamp(java.util.Date timestamp) {
        this.timestamp = timestamp;
        return this;
    }

    public java.util.Date getTimestamp() {
        return this.timestamp;
    }

    public RepresentativeElectDTO setDeleteTime(java.util.Date deleteTime) {
        this.deleteTime = deleteTime;
        return this;
    }

    public java.util.Date getDeleteTime() {
        return this.deleteTime;
    }

    public RepresentativeElectDTO setZbCode(String zbCode) {
        this.zbCode = zbCode;
        return this;
    }

    public String getZbCode() {
        return this.zbCode;
    }

    public RepresentativeElectDTO setOrgCode(String orgCode) {
        this.orgCode = orgCode;
        return this;
    }

    public String getOrgCode() {
        return this.orgCode;
    }

    public RepresentativeElectDTO setIsHistory(Integer isHistory) {
        this.isHistory = isHistory;
        return this;
    }

    public Integer getIsHistory() {
        return this.isHistory;
    }

    public RepresentativeElectDTO setUpdateAccount(String updateAccount) {
        this.updateAccount = updateAccount;
        return this;
    }

    public String getUpdateAccount() {
        return this.updateAccount;
    }

    public RepresentativeElect toModel() {
        RepresentativeElect model = new RepresentativeElect();
        model.setId(this.id);
        model.setCode(this.code);
        model.setEsId(this.esId);
        model.setElectOrgCode(this.electOrgCode);
        model.setUnitCode(this.unitCode);
        model.setUnitName(this.unitName);
        model.setD61Code(this.d61Code);
        model.setD61Name(this.d61Name);
        model.setElectNum(this.electNum);
        model.setElectName(this.electName);
        model.setStartDate(this.startDate);
        model.setEndDate(this.endDate);
        model.setIsNowElect(this.isNowElect);
        model.setIsCountryTest(this.isCountryTest);
        model.setCreateTime(this.createTime);
        model.setUpdateTime(this.updateTime);
        model.setTimestamp(this.timestamp);
        model.setDeleteTime(this.deleteTime);
        model.setZbCode(this.zbCode);
        model.setOrgCode(this.orgCode);
        model.setIsHistory(this.isHistory);
        model.setUpdateAccount(this.updateAccount);
        return model;
    }
}