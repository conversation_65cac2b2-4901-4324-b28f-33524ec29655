package com.zenith.front.model.dto;

import com.baomidou.mybatisplus.annotation.TableField;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.util.Date;

/**
 * <AUTHOR>
 * @date 2022/11/15
 */
@Data
public class MemFlowReceiveDto {
    /**
     * 主键
     */
    @NotNull(message = "code不能为空")
    private String code;
    /**
     * 流入地党支部
     */
    private String inOrgCode;
    /**
     * 流入地党支部名称
     */
    private String inOrgName;
    /**
     * 党支部联系方式
     */
    private String inOrgPhone;
    /**
     * 流入地联系人
     */
    private String inOrgUser;
    /**
     * 党支部单位性质类别
     */
    private String inOrgD04Code;
    private String inOrgD04Name;
    /**
     * 工作单位类型（已删除）
     */
    private String inUnitD04Code;
    private String inUnitD04Name;
    /**
     * 党员工作岗位（流入地时的工作岗位）
     */
    private String inMemD09Code;
    private String inMemD09Name;
    /**
     * 党员联系方式
     */
    private String inMemPhone;

    /**
     * 退回原因
     */
    private String rejectReasonCode;
    private String rejectReasonName;
    /**
     * 退回原因为其他的是填写详情
     */
    private String rejectOtherReasonText;
    /**
     * 流回时间
     */
    private Date flowBackTime;

    /**
     * 党费交纳情况（交到流入地至）
     */
    private Date partyExpensesInTime;
    /**
     * 民主评议情况
     */
    private String mzAppraisal;
    /**
     * 参与组织生活情况
     */
    private String inOrgLifeCode;
    private String inOrgLifeName;
    private String inOrgLife;
    /**
     * 表现反馈
     */
    private String inFeedback;

    /**
     * 接收时间
     */
    private Date inReceivingTime;

    /**
     * 经济类型 dict_d16
     */
    private String inUnitD16Code;
    private String inUnitD16Name;
    /**
     * 新社会阶层 dict_d20
     */
    private String inMemD20Code;
    private String inMemD20Name;
    /**
     * 退回-与流动党员本人联系记录
     */
    private String rejectContactMemRecord;
    /**
     * 退回-与流出地党组织联系记录
     */
    private String rejectContactOrgRecord;

    /**
     * 流动党员人员类型
     */
    private String flowMemTypeCode;
    /**
     * 流动党员人员类型
     */
    private String flowMemTypeName;
    /**
     * 人员类型备注
     */
    private String flowMemTypeRemark;
    /**
     * 人员类型新就业备注
     */
    private String flowMemTypeNewRemark;
    /**
     * 是否为农民
     */
    private String isFarmer;
    /**
     * 流入地是否为农民
     */
    private String lrdIsFarmer;

}
