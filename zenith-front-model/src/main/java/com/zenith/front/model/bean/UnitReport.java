package com.zenith.front.model.bean;

import java.math.BigDecimal;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.extension.activerecord.Model;

import java.util.Date;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableField;

import java.io.Serializable;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2022-04-13
 */
@TableName("ccp_unit_report")
public class UnitReport extends Model<UnitReport> {

    private static final long serialVersionUID = 1L;

    /**
     * 单位code
     */
    @TableId(value = "code", type = IdType.UUID)
    private String code;

    /**
     * 单位名称
     */
    @TableField("name")
    private String name;

    /**
     * 单位类别
     */
    @TableField("d04_code")
    private String d04Code;

    /**
     * 单位类别名称
     */
    @TableField("d04_name")
    private String d04Name;

    /**
     * 隶属关系
     */
    @TableField("d35_code")
    private String d35Code;

    /**
     * 隶属关系名称
     */
    @TableField("d35_name")
    private String d35Name;

    /**
     * 关联组织code
     */
    @TableField("main_org_code")
    private String mainOrgCode;

    /**
     * 关联组织名称
     */
    @TableField("main_org_name")
    private String mainOrgName;

    /**
     * 在编人数
     */
    @TableField("at_number")
    private Integer atNumber;

    /**
     * 行政编制
     */
    @TableField("at_administrative")
    private Integer atAdministrative;

    /**
     * 事业编制
     */
    @TableField("at_career")
    private Integer atCareer;

    /**
     * 空缺编制
     */
    @TableField("vacancy")
    private Integer vacancy;

    /**
     * 行政编制
     */
    @TableField("vacancy_administrative")
    private Integer vacancyAdministrative;

    /**
     * 事业编制
     */
    @TableField("vacancy_career")
    private Integer vacancyCareer;

    /**
     * 被借调工作人员总数
     */
    @TableField("seconded_num")
    private Integer secondedNum;

    /**
     * 占在编人数比例
     */
    @TableField("at_proportion")
    private String atProportion;

    /**
     * 省级以上
     */
    @TableField("provincial_above")
    private Integer provincialAbove;

    /**
     * 省级
     */
    @TableField("provincial")
    private Integer provincial;

    /**
     * 市级
     */
    @TableField("city")
    private Integer city;

    /**
     * 县级
     */
    @TableField("county")
    private Integer county;

    /**
     * 参加县级及以上集中培训人数
     */
    @TableField("join_above_county_train_num")
    private Integer joinAboveCountyTrainNum;

    /**
     * 村干部参加城镇职工养老保险人数
     */
    @TableField("village_join_urban_worker_num")
    private Integer villageJoinUrbanWorkerNum;

    /**
     * 参加比例
     */
    @TableField("join_proportion")
    private String joinProportion;

    /**
     * 组织层级码
     */
    @TableField("create_unit_org_code")
    private String createUnitOrgCode;

    /**
     * 组织code
     */
    @TableField("create_org_code")
    private String createOrgCode;

    /**
     * 中央和省级财政扶持资金
     */
    @TableField("financial_support_enforced")
    private BigDecimal financialSupportEnforced;

    /**
     * 中央和省级财政扶持资金执行率
     */
    @TableField("enforced")
    private BigDecimal enforced;

    /**
     * 已完工验收项目数字
     */
    @TableField("completed_acceptance_projects")
    private Integer completedAcceptanceProjects;

    /**
     * 已获得收益
     */
    @TableField("income_obtained")
    private BigDecimal incomeObtained;

    /**
     * 应到村任职选调生人数（数字、必填）
     */
    @TableField("number_of_students_to_be_transferred_to_the_village")
    private Integer numberOfStudentsToBeTransferredToTheVillage;

    @TableField("create_time")
    private Date createTime;

    @TableField("update_time")
    private Date updateTime;

    @TableField("delete_time")
    private Date deleteTime;

    /**
     * 不是党委委员的政府领导班子成员人数（必填、数字）
     */
    @TableField("number_of_non_governmental_members")
    private Integer numberOfNonGovernmentalMembers;

    /**
     * 是否有大学毕业生在村工作（选择框、必填）1 是 0 否
     */
    @TableField("whether_there_are_college_graduates_working_in_the_village")
    private Integer whetherThereAreCollegeGraduatesWorkingInTheVillage;


    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getD04Code() {
        return d04Code;
    }

    public void setD04Code(String d04Code) {
        this.d04Code = d04Code;
    }

    public String getD04Name() {
        return d04Name;
    }

    public void setD04Name(String d04Name) {
        this.d04Name = d04Name;
    }

    public String getD35Code() {
        return d35Code;
    }

    public void setD35Code(String d35Code) {
        this.d35Code = d35Code;
    }

    public String getD35Name() {
        return d35Name;
    }

    public void setD35Name(String d35Name) {
        this.d35Name = d35Name;
    }

    public String getMainOrgCode() {
        return mainOrgCode;
    }

    public void setMainOrgCode(String mainOrgCode) {
        this.mainOrgCode = mainOrgCode;
    }

    public String getMainOrgName() {
        return mainOrgName;
    }

    public void setMainOrgName(String mainOrgName) {
        this.mainOrgName = mainOrgName;
    }

    public Integer getAtNumber() {
        return atNumber;
    }

    public void setAtNumber(Integer atNumber) {
        this.atNumber = atNumber;
    }

    public Integer getAtAdministrative() {
        return atAdministrative;
    }

    public void setAtAdministrative(Integer atAdministrative) {
        this.atAdministrative = atAdministrative;
    }

    public Integer getAtCareer() {
        return atCareer;
    }

    public void setAtCareer(Integer atCareer) {
        this.atCareer = atCareer;
    }

    public Integer getVacancy() {
        return vacancy;
    }

    public void setVacancy(Integer vacancy) {
        this.vacancy = vacancy;
    }

    public Integer getVacancyAdministrative() {
        return vacancyAdministrative;
    }

    public void setVacancyAdministrative(Integer vacancyAdministrative) {
        this.vacancyAdministrative = vacancyAdministrative;
    }

    public Integer getVacancyCareer() {
        return vacancyCareer;
    }

    public void setVacancyCareer(Integer vacancyCareer) {
        this.vacancyCareer = vacancyCareer;
    }

    public Integer getSecondedNum() {
        return secondedNum;
    }

    public void setSecondedNum(Integer secondedNum) {
        this.secondedNum = secondedNum;
    }

    public String getAtProportion() {
        return atProportion;
    }

    public void setAtProportion(String atProportion) {
        this.atProportion = atProportion;
    }

    public Integer getProvincialAbove() {
        return provincialAbove;
    }

    public void setProvincialAbove(Integer provincialAbove) {
        this.provincialAbove = provincialAbove;
    }

    public Integer getProvincial() {
        return provincial;
    }

    public void setProvincial(Integer provincial) {
        this.provincial = provincial;
    }

    public Integer getCity() {
        return city;
    }

    public void setCity(Integer city) {
        this.city = city;
    }

    public Integer getCounty() {
        return county;
    }

    public void setCounty(Integer county) {
        this.county = county;
    }

    public Integer getJoinAboveCountyTrainNum() {
        return joinAboveCountyTrainNum;
    }

    public void setJoinAboveCountyTrainNum(Integer joinAboveCountyTrainNum) {
        this.joinAboveCountyTrainNum = joinAboveCountyTrainNum;
    }

    public Integer getVillageJoinUrbanWorkerNum() {
        return villageJoinUrbanWorkerNum;
    }

    public void setVillageJoinUrbanWorkerNum(Integer villageJoinUrbanWorkerNum) {
        this.villageJoinUrbanWorkerNum = villageJoinUrbanWorkerNum;
    }

    public String getJoinProportion() {
        return joinProportion;
    }

    public void setJoinProportion(String joinProportion) {
        this.joinProportion = joinProportion;
    }

    public String getCreateUnitOrgCode() {
        return createUnitOrgCode;
    }

    public void setCreateUnitOrgCode(String createUnitOrgCode) {
        this.createUnitOrgCode = createUnitOrgCode;
    }

    public String getCreateOrgCode() {
        return createOrgCode;
    }

    public void setCreateOrgCode(String createOrgCode) {
        this.createOrgCode = createOrgCode;
    }

    public BigDecimal getFinancialSupportEnforced() {
        return financialSupportEnforced;
    }

    public void setFinancialSupportEnforced(BigDecimal financialSupportEnforced) {
        this.financialSupportEnforced = financialSupportEnforced;
    }

    public BigDecimal getEnforced() {
        return enforced;
    }

    public void setEnforced(BigDecimal enforced) {
        this.enforced = enforced;
    }

    public Integer getCompletedAcceptanceProjects() {
        return completedAcceptanceProjects;
    }

    public void setCompletedAcceptanceProjects(Integer completedAcceptanceProjects) {
        this.completedAcceptanceProjects = completedAcceptanceProjects;
    }

    public BigDecimal getIncomeObtained() {
        return incomeObtained;
    }

    public void setIncomeObtained(BigDecimal incomeObtained) {
        this.incomeObtained = incomeObtained;
    }

    public Integer getNumberOfStudentsToBeTransferredToTheVillage() {
        return numberOfStudentsToBeTransferredToTheVillage;
    }

    public void setNumberOfStudentsToBeTransferredToTheVillage(Integer numberOfStudentsToBeTransferredToTheVillage) {
        this.numberOfStudentsToBeTransferredToTheVillage = numberOfStudentsToBeTransferredToTheVillage;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    public Date getDeleteTime() {
        return deleteTime;
    }

    public void setDeleteTime(Date deleteTime) {
        this.deleteTime = deleteTime;
    }

    public Integer getNumberOfNonGovernmentalMembers() {
        return numberOfNonGovernmentalMembers;
    }

    public void setNumberOfNonGovernmentalMembers(Integer numberOfNonGovernmentalMembers) {
        this.numberOfNonGovernmentalMembers = numberOfNonGovernmentalMembers;
    }

    public Integer getWhetherThereAreCollegeGraduatesWorkingInTheVillage() {
        return whetherThereAreCollegeGraduatesWorkingInTheVillage;
    }

    public void setWhetherThereAreCollegeGraduatesWorkingInTheVillage(Integer whetherThereAreCollegeGraduatesWorkingInTheVillage) {
        this.whetherThereAreCollegeGraduatesWorkingInTheVillage = whetherThereAreCollegeGraduatesWorkingInTheVillage;
    }

    @Override
    protected Serializable pkVal() {
        return this.code;
    }

    @Override
    public String toString() {
        return "CcpUnitReport{" +
                "code=" + code +
                ", name=" + name +
                ", d04Code=" + d04Code +
                ", d04Name=" + d04Name +
                ", d35Code=" + d35Code +
                ", d35Name=" + d35Name +
                ", mainOrgCode=" + mainOrgCode +
                ", mainOrgName=" + mainOrgName +
                ", atNumber=" + atNumber +
                ", atAdministrative=" + atAdministrative +
                ", atCareer=" + atCareer +
                ", vacancy=" + vacancy +
                ", vacancyAdministrative=" + vacancyAdministrative +
                ", vacancyCareer=" + vacancyCareer +
                ", secondedNum=" + secondedNum +
                ", atProportion=" + atProportion +
                ", provincialAbove=" + provincialAbove +
                ", provincial=" + provincial +
                ", city=" + city +
                ", county=" + county +
                ", joinAboveCountyTrainNum=" + joinAboveCountyTrainNum +
                ", villageJoinUrbanWorkerNum=" + villageJoinUrbanWorkerNum +
                ", joinProportion=" + joinProportion +
                ", createUnitOrgCode=" + createUnitOrgCode +
                ", createOrgCode=" + createOrgCode +
                ", financialSupportEnforced=" + financialSupportEnforced +
                ", enforced=" + enforced +
                ", completedAcceptanceProjects=" + completedAcceptanceProjects +
                ", incomeObtained=" + incomeObtained +
                ", numberOfStudentsToBeTransferredToTheVillage=" + numberOfStudentsToBeTransferredToTheVillage +
                ", createTime=" + createTime +
                ", updateTime=" + updateTime +
                ", deleteTime=" + deleteTime +
                "}";
    }
}
