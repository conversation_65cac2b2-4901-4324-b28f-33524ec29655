package com.zenith.front.model.bean;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.extension.activerecord.Model;

import java.io.IOException;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableField;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.zenith.front.model.dto.ChartsListDTO;
import com.zenith.front.common.jsonb.JsonbTypeHandler;

import java.io.Serializable;
import java.util.Date;
import java.util.Objects;

/**
 * <p>
 * 用户表
 * </p>
 *
 * <AUTHOR>
 * @since 2021-07-11
 */
@TableName("sys_user")
public class User extends Model<User> {

    private static final long serialVersionUID = 1L;

    /**
     * 用户ID,uuid,不能为null
     */
    @TableId(value = "id", type = IdType.ASSIGN_UUID)
    private String id;

    /**
     * 用户账号,不能为null
     */
    @TableField("account")
    private String account;

    /**
     * 用户密码,不能为null
     */
    @TableField("password")
    private String password;

    /**
     * 用户名称,不能为null
     */
    @TableField("name")
    private String name;

    /**
     * 用户手机号,允许为null
     */
    @TableField("phone")
    private String phone;

    /**
     * 角色ID,用户当前角色,不允许为null
     */
    @TableField("\"current_user_roleId\"")
    private String currentUserRoleid;

    /**
     * 0 未删除 1已删除 默认为0
     */
    @TableField("is_delete")
    private Integer isDelete;

    /**
     * 0 为锁定 1已锁定 默认为0
     */
    @TableField("is_lock")
    private Integer isLock;

    /**
     * 创建时间,不允许为null
     */
    @TableField("create_time")
    private Date createTime;

    /**
     * 更新时间,不允许为null,默认值是createTime
     */
    @TableField("update_time")
    private Date updateTime;

    /**
     * 创建者,只能是account
     */
    @TableField("create_account")
    private String createAccount;

    /**
     * 组织id
     */
    @TableField("org_id")
    private String orgId;

    /**
     * 0 不只读 1是只读
     */
    @TableField("read_only")
    private Integer readOnly;

    /**
     * 用户管理人员id
     */
    @TableField("mem_code")
    private String memCode;

    /**
     * 组织code
     */
    @TableField("org_code")
    private String orgCode;

    /**
     * 修改者账号
     */
    @TableField("update_account")
    private String updateAccount;

    /**
     * 最后登录时间
     */
    @TableField("last_login_time")
    private Date lastLoginTime;

    /**
     * 模块概况数据
     */
    @TableField(value = "chart", typeHandler = JsonbTypeHandler.class)
    private Object chart;

    /**
     * 用户openId(渝快政)
     */
    @TableField("open_id")
    private String openId;

    /**
     * 默认综合党务管理系统 1 村社区管理系统 2 todo 兼容村社区管理系统
     */
    @TableField("management_system")
    private String managementSystem;
    /**
     * 上次修改密码时间
     */
    @TableField("last_update_password_time")
    private Date lastUpdatePasswordTime;


    /**
     * 密评序列号
     */
    @TableField("ukey")
    private String ukey;

    public String getUkey() {
        return ukey;
    }

    public void setUkey(String ukey) {
        this.ukey = ukey;
    }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getAccount() {
        return account;
    }

    public void setAccount(String account) {
        this.account = account;
    }

    public String getPassword() {
        return password;
    }

    public void setPassword(String password) {
        this.password = password;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getPhone() {
        return phone;
    }

    public void setPhone(String phone) {
        this.phone = phone;
    }

    public String getCurrentUserRoleid() {
        return currentUserRoleid;
    }

    public void setCurrentUserRoleid(String currentUserRoleid) {
        this.currentUserRoleid = currentUserRoleid;
    }

    public Integer getIsDelete() {
        return isDelete;
    }

    public void setIsDelete(Integer isDelete) {
        this.isDelete = isDelete;
    }

    public Integer getIsLock() {
        return isLock;
    }

    public void setIsLock(Integer isLock) {
        this.isLock = isLock;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    public String getCreateAccount() {
        return createAccount;
    }

    public void setCreateAccount(String createAccount) {
        this.createAccount = createAccount;
    }

    public String getOrgId() {
        return orgId;
    }

    public void setOrgId(String orgId) {
        this.orgId = orgId;
    }

    public Integer getReadOnly() {
        return readOnly;
    }

    public void setReadOnly(Integer readOnly) {
        this.readOnly = readOnly;
    }

    public String getMemCode() {
        return memCode;
    }

    public void setMemCode(String memCode) {
        this.memCode = memCode;
    }

    public String getOrgCode() {
        return orgCode;
    }

    public void setOrgCode(String orgCode) {
        this.orgCode = orgCode;
    }

    public String getUpdateAccount() {
        return updateAccount;
    }

    public void setUpdateAccount(String updateAccount) {
        this.updateAccount = updateAccount;
    }

    public Date getLastLoginTime() {
        return lastLoginTime;
    }

    public void setLastLoginTime(Date lastLoginTime) {
        this.lastLoginTime = lastLoginTime;
    }

    public Object getChart() {
        return chart;
    }

    public void setChart(Object chart) {
        this.chart = chart;
    }

    public String getOpenId() {
        return openId;
    }

    public void setOpenId(String openId) {
        this.openId = openId;
    }

    public String getManagementSystem() {
        return managementSystem;
    }

    public void setManagementSystem(String managementSystem) {
        this.managementSystem = managementSystem;
    }

    public void setLastUpdatePasswordTime(Date lastUpdatePasswordTime) {
        this.lastUpdatePasswordTime = lastUpdatePasswordTime;
    }

    public Date getLastUpdatePasswordTime() {
        return lastUpdatePasswordTime;
    }

    @Override
    public Serializable pkVal() {
        return this.id;
    }

    @Override
    public String toString() {
        return "User{" +
                "id=" + id +
                ", account=" + account +
                ", password=" + password +
                ", name=" + name +
                ", phone=" + phone +
                ", currentUserRoleid=" + currentUserRoleid +
                ", isDelete=" + isDelete +
                ", isLock=" + isLock +
                ", createTime=" + createTime +
                ", updateTime=" + updateTime +
                ", createAccount=" + createAccount +
                ", orgId=" + orgId +
                ", readOnly=" + readOnly +
                ", memCode=" + memCode +
                ", orgCode=" + orgCode +
                ", updateAccount=" + updateAccount +
                ", lastLoginTime=" + lastLoginTime +
                ", chart=" + chart +
                ", openId=" + openId +
                ", managementSystem=" + managementSystem +
                "}";
    }

    /**
     * 模块概况数据
     */
    public ChartsListDTO chart() {
        Object object = getChart();
        if (Objects.isNull(object)) {
            return null;
        }
        String value = object.toString();
        ObjectMapper mapper = new ObjectMapper();
        try {
            return mapper.readValue(value, ChartsListDTO.class);
        } catch (IOException e) {
            throw new RuntimeException("PgObject转化失败");
        }
    }
}
