package com.zenith.front.service;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.zenith.front.core.encrypt.DecryptingInformation;
import com.zenith.front.dao.mapper.decrypting.DecryptingInformationMapper;
import com.zenith.front.model.entity.DecryptingTaskRecord;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 解密任务服务类
 * 提供高级的任务管理功能
 * 
 * <AUTHOR>
 * @date 2025-06-30
 */
@Service
@Slf4j
public class DecryptingTaskService {

    @Resource
    private DecryptingInformation decryptingInformation;
    
    @Resource
    private DecryptingInformationMapper mapper;

    /**
     * 获取任务详细报告
     */
    public Map<String, Object> getTaskReport(String batchNo) {
        Map<String, Object> report = new HashMap<>();
        
        // 基础统计信息
        Map<String, Object> statistics = decryptingInformation.getBatchStatistics(batchNo);
        report.put("statistics", statistics);
        
        // 失败任务详情
        List<DecryptingTaskRecord> failedTasks = decryptingInformation.getFailedTasks(batchNo);
        report.put("failedTasks", failedTasks);
        
        // 按表名分组的统计
        List<DecryptingTaskRecord> allTasks = decryptingInformation.getBatchTasks(batchNo);
        Map<String, Map<String, Integer>> tableStatistics = new HashMap<>();
        
        for (DecryptingTaskRecord task : allTasks) {
            String tableName = task.getTableName();
            tableStatistics.computeIfAbsent(tableName, k -> new HashMap<>());
            Map<String, Integer> tableStats = tableStatistics.get(tableName);
            
            String statusKey = getStatusName(task.getStatus());
            tableStats.put(statusKey, tableStats.getOrDefault(statusKey, 0) + 1);
            tableStats.put("total", tableStats.getOrDefault("total", 0) + 1);
        }
        
        report.put("tableStatistics", tableStatistics);
        
        return report;
    }

    /**
     * 获取状态名称
     */
    private String getStatusName(Integer status) {
        if (status == null) return "unknown";
        switch (status) {
            case DecryptingTaskRecord.STATUS_PENDING: return "pending";
            case DecryptingTaskRecord.STATUS_PROCESSING: return "processing";
            case DecryptingTaskRecord.STATUS_SUCCESS: return "success";
            case DecryptingTaskRecord.STATUS_FAILED: return "failed";
            default: return "unknown";
        }
    }

    /**
     * 检查批次是否完成
     */
    public boolean isBatchCompleted(String batchNo) {
        Map<String, Object> statistics = decryptingInformation.getBatchStatistics(batchNo);
        if (statistics == null) {
            return false;
        }
        
        Long pendingCount = (Long) statistics.get("pending_count");
        Long processingCount = (Long) statistics.get("processing_count");
        
        return (pendingCount == null || pendingCount == 0) && 
               (processingCount == null || processingCount == 0);
    }

    /**
     * 获取批次成功率
     */
    public double getBatchSuccessRate(String batchNo) {
        Map<String, Object> statistics = decryptingInformation.getBatchStatistics(batchNo);
        if (statistics == null) {
            return 0.0;
        }
        
        Long totalCount = (Long) statistics.get("total_count");
        Long successCount = (Long) statistics.get("success_count");
        
        if (totalCount == null || totalCount == 0) {
            return 0.0;
        }
        
        return (successCount == null ? 0.0 : successCount.doubleValue()) / totalCount.doubleValue() * 100;
    }

    /**
     * 清理已完成的任务记录（可选功能）
     */
    public int cleanupCompletedTasks(String batchNo, boolean onlySuccess) {
        // 这里可以添加清理逻辑，删除已完成的任务记录
        // 注意：实际使用时需要谨慎，建议只清理很久以前的记录
        log.info("清理任务记录功能暂未实现，批次：{}，仅成功：{}", batchNo, onlySuccess);
        return 0;
    }

    /**
     * 重置失败任务的重试次数
     */
    public int resetFailedTaskRetryCount(String batchNo) {
        List<DecryptingTaskRecord> failedTasks = decryptingInformation.getFailedTasks(batchNo);
        if (CollUtil.isEmpty(failedTasks)) {
            return 0;
        }
        
        int resetCount = 0;
        for (DecryptingTaskRecord task : failedTasks) {
            if (task.getRetryCount() != null && task.getRetryCount() > 0) {
                task.setRetryCount(0);
                task.setStatus(DecryptingTaskRecord.STATUS_PENDING);
                mapper.updateTaskRecord(task);
                resetCount++;
            }
        }
        
        log.info("已重置{}个失败任务的重试次数，批次：{}", resetCount, batchNo);
        return resetCount;
    }

    /**
     * 获取任务执行摘要
     */
    public String getTaskSummary(String batchNo) {
        Map<String, Object> statistics = decryptingInformation.getBatchStatistics(batchNo);
        if (statistics == null) {
            return "批次" + batchNo + "不存在";
        }
        
        Long totalCount = (Long) statistics.get("total_count");
        Long successCount = (Long) statistics.get("success_count");
        Long failedCount = (Long) statistics.get("failed_count");
        Long pendingCount = (Long) statistics.get("pending_count");
        Long processingCount = (Long) statistics.get("processing_count");
        
        double successRate = getBatchSuccessRate(batchNo);
        boolean completed = isBatchCompleted(batchNo);
        
        StringBuilder summary = new StringBuilder();
        summary.append("批次：").append(batchNo).append("\n");
        summary.append("总任务数：").append(totalCount).append("\n");
        summary.append("成功：").append(successCount).append("\n");
        summary.append("失败：").append(failedCount).append("\n");
        summary.append("待处理：").append(pendingCount).append("\n");
        summary.append("处理中：").append(processingCount).append("\n");
        summary.append("成功率：").append(String.format("%.2f%%", successRate)).append("\n");
        summary.append("状态：").append(completed ? "已完成" : "进行中");
        
        return summary.toString();
    }

    /**
     * 验证批次数据完整性
     */
    public Map<String, Object> validateBatchData(String batchNo) {
        Map<String, Object> result = new HashMap<>();
        List<DecryptingTaskRecord> allTasks = decryptingInformation.getBatchTasks(batchNo);
        
        if (CollUtil.isEmpty(allTasks)) {
            result.put("valid", false);
            result.put("message", "批次不存在或无任务记录");
            return result;
        }
        
        int invalidTasks = 0;
        StringBuilder issues = new StringBuilder();
        
        for (DecryptingTaskRecord task : allTasks) {
            // 检查必要字段
            if (StrUtil.isBlank(task.getTableName()) || StrUtil.isBlank(task.getDataCode())) {
                invalidTasks++;
                issues.append("任务").append(task.getId()).append("缺少必要字段; ");
            }
            
            // 检查状态合理性
            if (task.getStatus() == null || task.getStatus() < 0 || task.getStatus() > 3) {
                invalidTasks++;
                issues.append("任务").append(task.getId()).append("状态异常; ");
            }
            
            // 检查字段计数
            if (task.getTotalFieldCount() != null && task.getSuccessFieldCount() != null) {
                if (task.getSuccessFieldCount() > task.getTotalFieldCount()) {
                    invalidTasks++;
                    issues.append("任务").append(task.getId()).append("成功字段数超过总数; ");
                }
            }
        }
        
        result.put("valid", invalidTasks == 0);
        result.put("totalTasks", allTasks.size());
        result.put("invalidTasks", invalidTasks);
        result.put("issues", issues.toString());
        
        return result;
    }
}
