package com.zenith.front.core.service.transfer;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.zenith.front.api.transfer.ITransferLogService;
import com.zenith.front.common.constant.TransferLogConstant;
import com.zenith.front.dao.mapper.transfer.TransferLogMapper;
import com.zenith.front.model.bean.TransferLog;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;

/**
 * <p>
 * 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-07-10
 */
@Service
public class TransferLogServiceImpl extends ServiceImpl<TransferLogMapper, TransferLog> implements ITransferLogService {

    @Override
    public TransferLog createOrgTransferLog() {
        TransferLog orgTransferLog = new TransferLog();
        orgTransferLog.setType(TransferLogConstant.PUSH_TRANSFER_STATUS);
        orgTransferLog.setCreateTime(new Date());
        orgTransferLog.setUpdateTime(new Date());
        return orgTransferLog;
    }

    @Override
    public boolean rewriteSave(TransferLog transferLog) {
        return save(transferLog);
    }

    @Override
    public List<TransferLog> findByHandleApprovalId(String id) {
        return list(new LambdaQueryWrapper<TransferLog>().eq(TransferLog::getHandleApprovalId, id));
    }
}
