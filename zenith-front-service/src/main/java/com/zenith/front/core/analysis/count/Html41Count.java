package com.zenith.front.core.analysis.count;

import cn.hutool.core.util.StrUtil;
import com.b1809.analysis.entity.PeggingPara;
import com.b1809.analysis.extend.ITableCount;
import com.b1809.analysis.plugin.elasticsearch.EsKit;
import com.jfinal.plugin.activerecord.Record;
import com.zenith.front.core.analysis.ext.condition.MemAllCondition;
import com.zenith.front.core.analysis.ext.condition.MemFlowStatisticsCondition;
import com.zenith.front.core.analysis.ext.condition.OrgAllCondition;
import com.zenith.front.core.analysis.ext.condition.UnitAllCondition;
import org.jooq.Condition;
import org.jooq.Record1;
import org.jooq.SelectConditionStep;
import org.jooq.SelectHavingStep;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

import static com.zenith.front.common.constant.DBConstant.DSL_CONTEXT;
import static org.jooq.impl.DSL.*;

/**
 * 流动党员情况（一）
 *
 * <AUTHOR>
 * @date 2022/11/25
 */
@Component
public class Html41Count implements ITableCount {


    @Override
    public String getReportCode() {
        return "41.html";
    }

    @Override
    public Map<String, Map<String, Number>> doCount(String reportCode, String orgCode, String orgLevelCode) throws Exception {
        return getCount(orgCode, orgLevelCode, null);
    }

    public Map<String, Map<String, Number>> getCount(String orgCode, String orgLevelCode, String tableYear) {
        String memFlowStatistics = StrUtil.isEmpty(tableYear) ? "mem_flow_all" : "mem_flow_all_" + tableYear;
        Map<String, Number> result = new LinkedHashMap<>();
        Html53Count.initTableCol(1, 21, 1, 10, result);
        MemFlowStatisticsCondition memFlowCond = new MemFlowStatisticsCondition();
        SelectHavingStep<Record1<Object>> records1 = DSL_CONTEXT.select(field("d09_code,d20_code,d04_code,d16_code,out_place_code,flow_type_code,is_hold,has_inter,count(1) as total"))
                .from(table(name(memFlowStatistics)).as("mem_flow_all")).where(memFlowCond.create(orgCode, orgLevelCode).and("flow_out_in = '1'"))
                .groupBy(field("d09_code,d20_code,d04_code,d16_code,out_place_code,flow_type_code,is_hold,has_inter"));
        List<com.jfinal.plugin.activerecord.Record> records = EsKit.findBySql(records1.toString()).toRecord();
        for (Record record : records) {
            setStatisticsValue(record, result);

        }
        Map<String, Map<String, Number>> mapMap = new HashMap<>(10);
        mapMap.put("table0", result);
        return mapMap;
    }

    public static void setStatisticsValue(Record record, Map<String, Number> result) {
        Integer total = record.getInt("total");
        String d04Code = record.getStr("d04_code");
        String d16Code = record.getStr("d16_code");
        //外出地点 	1 流向基层党（工）委	2 流向县（市、区、旗)	3 无固定地点	4 不掌握流向
        String outPlaceCode = record.getStr("out_place_code");
        //流动类型	1 跨省（区、市）流动	2 省(区、市)内跨市（地、州、盟)流动	3 市(地、州、盟)内跨县（(市、区、旗)流动
        String flowTypeCode = record.getStr("flow_type_code");
        setCellValue(record, "1", result, total);
        //跨 省 流 动
        if (StrUtil.equals(flowTypeCode, "1")) {
            setCellValue(record, "2", result, total);
        }
        //省  内  跨  市 （地）  流  动
        if (StrUtil.equals(flowTypeCode, "2")) {
            setCellValue(record, "3", result, total);
        }
        //2024年统新增  市  内  跨  县（区）  流  动
        if (StrUtil.equals(flowTypeCode, "3")) {
            setCellValue(record, "4", result, total);
        }
        //持《中国共产党流动党员活动证》
        if (StrUtil.equals(record.getStr("is_hold"), "1")) {
            setCellValue(record, "5", result, total);
        }

        //公有经济控制单位 d04_code 以1，2，3，4 开头（d16_code 以1 开头,2 开头）,96
        boolean flag = StrUtil.startWith(d04Code, "4") && StrUtil.startWithAny(d16Code, "1", "2");
        if (StrUtil.startWithAny(d04Code, "1", "2", "3") || StrUtil.equals(d04Code, "96") || flag) {
            setCellValue(record, "6", result, total);
        }
        //   机关
        if (StrUtil.startWithAny(d04Code, "1", "2")) {
            setCellValue(record, "7", result, total);
        }
        //   事业单位
        if (StrUtil.startWith(d04Code, "3")) {
            setCellValue(record, "8", result, total);
        }
        //   港澳台商投资企业   4 + d16_code等于121，22
        if (StrUtil.startWith(d04Code, "4") && StrUtil.equalsAny(d16Code, "121", "22")) {
            setCellValue(record, "9", result, total);
        }
        //   外商投资企业 4 + d16_code等于122，23
        if (StrUtil.startWith(d04Code, "4") && StrUtil.equalsAny(d16Code, "122", "23")) {
            setCellValue(record, "10", result, total);
        }

        //非公有经济控制单位 d04_code 以4 开头（d16_code 3，4，5 开头），5 开头，94,95
        boolean flag2 = StrUtil.startWith(d04Code, "4") && StrUtil.startWithAny(d16Code, "3", "4", "5");
        if (flag2 || StrUtil.startWithAny(d04Code, "5") || StrUtil.equalsAny(d04Code, "94", "95")) {
            setCellValue(record, "11", result, total);
        }
        //  民办非企业单位 52
        if (StrUtil.startWith(d04Code, "52")) {
            setCellValue(record, "12", result, total);
        }
        //  私营企业    4 + d16_code 以3 开头,排除32,33
        if (StrUtil.startWith(d04Code, "4") && StrUtil.startWith(d16Code, "3") && !StrUtil.equalsAny(d16Code, "32", "33")) {
            setCellValue(record, "13", result, total);
        }
        //  港澳台商投资企业    4 + d16_code等于32，以4 开头
        if (StrUtil.startWith(d04Code, "4") && (StrUtil.startWith(d16Code, "4") || StrUtil.equals(d16Code, "32"))) {
            setCellValue(record, "14", result, total);
        }
        //  外商投资企业  4 + d16_code 等于33，以5 开头
        if (StrUtil.startWith(d04Code, "4") && (StrUtil.startWith(d16Code, "5") || StrUtil.equals(d16Code, "33"))) {
            setCellValue(record, "15", result, total);
        }

        //个体工商户 93
        if (StrUtil.startWith(d04Code, "93")) {
            setCellValue(record, "16", result, total);
        }
        //城市社区（居委会）
        if (StrUtil.startWith(d04Code, "921")) {
            setCellValue(record, "17", result, total);
        }
        //乡镇社区（居委会）
        if (StrUtil.startWith(d04Code, "922")) {
            setCellValue(record, "18", result, total);
        }
        //行政村
        if (StrUtil.startWith(d04Code, "923")) {
            setCellValue(record, "19", result, total);
        }
        //其他
        if (StrUtil.startWithAny(d04Code, "6", "91", "97")) {
            setCellValue(record, "20", result, total);
            if (StrUtil.equals(outPlaceCode, "4")) {
                setCellValue(record, "21", result, total);
            }
        }
    }

    /**
     * 设置每一个单元格
     */
    public static void setCellValue(Record record, String row, Map<String, Number> result, Integer addNum) {
        String d09Code = record.getStr("d09_code");
        String d20Code = record.getStr("d20_code");
        String hasInter = record.getStr("has_inter");
        //总计
        Html53Count.setTableMapValue(row, "1", addNum, result);
        if (StrUtil.equalsAny(d09Code, "014", "015")) {
            //公有经济企事业单位人员
            Html53Count.setTableMapValue(row, "2", addNum, result);
        } else if (StrUtil.startWithAny(d09Code, "021", "022")) {
            //非公有制经济组织从业人员
            Html53Count.setTableMapValue(row, "3", addNum, result);
        } else if (StrUtil.startWithAny(d09Code, "03")) {
            //社会组织从业人员
            Html53Count.setTableMapValue(row, "4", addNum, result);
        } else if (StrUtil.startWithAny(d20Code, "5") && StrUtil.equals(hasInter, "1")) {
            //中介组织从业人员
            Html53Count.setTableMapValue(row, "5", addNum, result);
        } else if (StrUtil.startWithAny(d09Code, "025")) {
            //新就业群体
            Html53Count.setTableMapValue(row, "6", addNum, result);
        } else if (StrUtil.startWithAny(d09Code, "1")) {
            //农牧渔民
            Html53Count.setTableMapValue(row, "7", addNum, result);
        }else if (StrUtil.startWithAny(d09Code, "4")){
            //离退休人员
            Html53Count.setTableMapValue(row, "8", addNum, result);
        } else if (StrUtil.equals(d09Code, "515")) {
            //未就业的高校毕业生
            Html53Count.setTableMapValue(row, "9", addNum, result);
        } else {
            Html53Count.setTableMapValue(row, "10", addNum, result);
        }
    }

    @Override
    public Map<String, Object> doPegging(PeggingPara peggingPara) throws Exception {
        MemFlowStatisticsCondition memFlowCond = new MemFlowStatisticsCondition();
        Condition condition = memFlowCond.create(peggingPara.getOrgCode(), peggingPara.getOrgLevelCode()).and("flow_out_in = '1'")
                .and(getColCondition(peggingPara.getColIndex()))
                .and(getRowCondition(peggingPara.getRowIndex()));
        return Html53Count.getReportPageResult(peggingPara, memFlowCond.getTableName(), condition, memFlowCond.getLevelCodeField());
    }

    /**
     * 列条件
     */
    public static Condition getColCondition(String colIndex) {
        Condition condition = noCondition();
        if (StrUtil.equals(colIndex, "1")) {
        } else if (StrUtil.equals(colIndex, "2")) {
            //公有经济企事业单位人员
            condition = condition.and("d09_code in ('014','015')");
        } else if (StrUtil.equals(colIndex, "3")) {
            //非公有制经济组织从业人员
            condition = condition.and("d09_code in ('021','022')");
        } else if (StrUtil.equals(colIndex, "4")) {
            //社会组织从业人员
            condition = condition.and("d09_code like '03%'");
        } else if (StrUtil.equals(colIndex, "5")) {
            //中介组织从业人员
            condition = condition.and("has_inter = '1' and d20_code like '5%'");
        } else if (StrUtil.equals(colIndex, "6")) {
            //新就业群体
            condition = condition.and("d09_code like '025%'");
        } else if (StrUtil.equals(colIndex, "7")) {
            //农牧渔民
            condition = condition.and("d09_code like '1%'");
        }else if (StrUtil.equals(colIndex, "8")){
            //离退休人员
            condition = condition.and("d09_code like '4%'");
        }
        else if (StrUtil.equals(colIndex, "9")) {
            //未就业的高校毕业生
            condition = condition.and("d09_code = '515'");
        }
        else {
            //其他
            condition = condition.and("d09_code not in ('014','015','021','022')")
                    .and("d09_code not like '03%' and d09_code not like '025%' and d09_code not like '1%' and d09_code not like '4%'")
                    .and("d09_code != '515'")
                    .and("has_inter != '1' and d20_code not like '5%'");
        }
        return condition;
    }

    /**
     * 行条件
     */
    public static Condition getRowCondition(String rowIndex) {
        Condition condition = noCondition();
        if (StrUtil.equalsAny(rowIndex, "1")) {
        } else if (StrUtil.equalsAny(rowIndex, "2")) {
            condition = condition.and("flow_type_code = '1'");
        } else if (StrUtil.equalsAny(rowIndex, "3")) {
            condition = condition.and("flow_type_code = '2'");
        } else if (StrUtil.equalsAny(rowIndex, "4")) {
            condition = condition.and("flow_type_code = '3'");
        } else if (StrUtil.equalsAny(rowIndex, "5")) {
            condition = condition.and("is_hold = '1'");
        } else if (StrUtil.equalsAny(rowIndex, "6")) {
            //公有经济控制单位
            condition = condition.and("d04_code like '1%' or d04_code like '2%' or d04_code like '3%' or d04_code='96' " +
                    "or (d04_code like '4%' and (d16_code like '1%' or d16_code like '2%'))");
        } else if (StrUtil.equalsAny(rowIndex, "7")) {
            condition = condition.and("d04_code like '1%' or d04_code like '2%'");
        } else if (StrUtil.equalsAny(rowIndex, "8")) {
            condition = condition.and("d04_code like '3%'");
        } else if (StrUtil.equalsAny(rowIndex, "9")) {
            condition = condition.and("d04_code like '4%' and d16_code in ('121','22')");
        } else if (StrUtil.equalsAny(rowIndex, "10")) {
            condition = condition.and("d04_code like '4%' and d16_code in ('122','23')");
        } else if (StrUtil.equalsAny(rowIndex, "11")) {
            //非公有经济控制单位
            condition = condition.and("(d04_code like '4%' and (d16_code like '3%' or d16_code like '4%' or d16_code like '5%')) " +
                    "or d04_code like '5%' or d04_code in('94','95')");
        } else if (StrUtil.equalsAny(rowIndex, "12")) {
            condition = condition.and("d04_code like '52%'");
        } else if (StrUtil.equalsAny(rowIndex, "13")) {
            condition = condition.and("d04_code like '4%' and d16_code like '3%' and d16_code not in ('32','33')");
        } else if (StrUtil.equalsAny(rowIndex, "14")) {
            condition = condition.and("d04_code like '4%' and (d16_code like '4%' or d16_code in ('32'))");
        } else if (StrUtil.equalsAny(rowIndex, "15")) {
            condition = condition.and("d04_code like '4%' and (d16_code like '5%' or d16_code in ('33'))");
        } else if (StrUtil.equalsAny(rowIndex, "16")) {
            //个体工商户
            condition = condition.and("d04_code like '93%'");
        } else if (StrUtil.equalsAny(rowIndex, "17")) {
            condition = condition.and("d04_code like '921%'");
        } else if (StrUtil.equalsAny(rowIndex, "18")) {
            condition = condition.and("d04_code like '922%'");
        } else if (StrUtil.equalsAny(rowIndex, "19")) {
            condition = condition.and("d04_code like '923%'");
        } else if (StrUtil.equalsAny(rowIndex, "20", "21")) {
            condition = condition.and("d04_code like '6%' or d04_code like '91%' or d04_code like '97%' ");
            if (StrUtil.equals(rowIndex, "21")) {
                condition = condition.and("out_place_code = '4'");
            }
        }

        return condition;
    }

    /**
     * 补充资料
     */
    @Override
    public Map<String, Number> doReplenishCount(String reportCode, String orgCode, String orgLevelCode) throws Exception {
        return getReplenishCount(orgCode, orgLevelCode, null);
    }

    public Map<String, Number> getReplenishCount(String orgCode, String orgLevelCode, String tableYear) {
        String ccpOrgAll = StrUtil.isEmpty(tableYear) ? "ccp_org_all" : "ccp_org_all_" + tableYear;
        String ccpUnitAll = StrUtil.isEmpty(tableYear) ? "ccp_unit_all" : "ccp_unit_all_" + tableYear;
        String ccpMemAll = StrUtil.isEmpty(tableYear) ? "ccp_mem_all" : "ccp_mem_all_" + tableYear;
        Map<String, Number> result = new LinkedHashMap<>();
        Html53Count.initReplenishCol(1, 12, result);

        SelectConditionStep<Record1<Object>> records1 = DSL_CONTEXT.select(field("d01_code")).from(table(name(ccpOrgAll)).as("ccp_org_all"))
                .where(new Html7Count().getOrgListCondition(orgCode, orgLevelCode).and("d01_code like '12%' or d01_code like '13%' or d01_code like '14%'"));
        List<Record> records = EsKit.findBySql(records1.toString()).toRecord();
        //1．建立党员服务中心（站、点）      个，其中省（区、市）一级      个，市（地、州、盟）一级       个，县（市、区、旗）一级      个，乡（镇）一级      个，街道      个，社区      个，村      个。
        records.stream().map(record -> record.getStr("d01_code")).forEach(d01Code -> {
            Html53Count.setReplenishMapValue("1", 1, result);
            if (StrUtil.startWith(d01Code, "12")) {
                Html53Count.setReplenishMapValue("2", 1, result);
            } else if (StrUtil.startWith(d01Code, "13")) {
                Html53Count.setReplenishMapValue("3", 1, result);
            } else if (StrUtil.startWith(d01Code, "14")) {
                Html53Count.setReplenishMapValue("4", 1, result);
            }
        });
        SelectConditionStep<Record1<Object>> records2 = DSL_CONTEXT.select(field("d04_code,is_legal")).from(table(name(ccpUnitAll)).as("ccp_unit_all"))
                .where(new Html7Count().getUnitListCondition(orgCode, orgLevelCode).and("is_legal = 1 and (d04_code like '91%' or d04_code like '92%') "));
        List<Record> unitRecords = EsKit.findBySql(records2.toString()).toRecord();
        unitRecords.stream().map(unitRecord -> unitRecord.getStr("d04_code")).forEach(d04Code -> {
            Html53Count.setReplenishMapValue("1", 1, result);
            if (StrUtil.startWith(d04Code, "912")) {
                Html53Count.setReplenishMapValue("5", 1, result);
            } else if (StrUtil.startWith(d04Code, "911")) {
                Html53Count.setReplenishMapValue("6", 1, result);
            } else if (StrUtil.equalsAny(d04Code, "921", "922")) {
                Html53Count.setReplenishMapValue("7", 1, result);
            } else if (StrUtil.startWith(d04Code, "923")) {
                Html53Count.setReplenishMapValue("8", 1, result);
            }
        });

        //2．由人才（劳动）服务中心统一管理的流动党员   名，其中由省级人才（劳动）服务中心统一管理的流动党员   名，由市级人才（劳动）服务中心统一管理的流动党员   名，由县级人才（劳动）服务中心统一管理的流动党员   名。
        SelectConditionStep<Record1<Object>> records3 = DSL_CONTEXT.select(field("d09_code,d35_code,is_transfer")).from(table(name(ccpMemAll)).as("ccp_mem_all"))
                .where(new Html7Count().getMemListCondition(orgCode, orgLevelCode).and("d09_code ='511'"));
        List<Record> memRecords = EsKit.findBySql(records3.toString()).toRecord();
        memRecords.forEach(record -> {
            String d35Code = record.getStr("d35_code");
            String isTransfer = record.getStr("is_transfer");
            Html53Count.setReplenishMapValue("9", 1, result);
            if (StrUtil.startWithAny(d35Code, "2", "3")) {
                Html53Count.setReplenishMapValue("10", 1, result);
            }
            if (StrUtil.startWithAny(d35Code, "4", "5", "6")) {
                Html53Count.setReplenishMapValue("11", 1, result);
            }
            if (StrUtil.startWithAny(d35Code, "7")) {
                Html53Count.setReplenishMapValue("12", 1, result);
            }
        });
        return result;
    }

    /**
     * 补充资料反查
     */
    @Override
    public Map<String, Object> doReplenishPegging(PeggingPara peggingPara) throws Exception {
        String colIndex = peggingPara.getRowIndex();
        if (StrUtil.equalsAny(colIndex, "1", "2", "3", "4")) {
            OrgAllCondition orgAllCondition = new OrgAllCondition();
            Condition condition = this.getBczlOrgCondition(colIndex, peggingPara.getOrgCode(), peggingPara.getOrgLevelCode());
            return Html53Count.getReportPageResult(peggingPara, orgAllCondition.getTableName(), condition, orgAllCondition.getLevelCodeField());
        }
        if (StrUtil.equalsAny(colIndex, "5", "6", "7", "8")) {
            UnitAllCondition unitAllCondition = new UnitAllCondition();
            Condition condition = this.getBczlUnitCondition(colIndex, peggingPara.getOrgCode(), peggingPara.getOrgLevelCode());
            return Html53Count.getReportPageResult(peggingPara, unitAllCondition.getTableName(), condition, unitAllCondition.getLevelCodeField());
        }
        MemAllCondition memAllCondition = new MemAllCondition();
        Condition condition = this.getBczlMemCondition(colIndex, peggingPara.getOrgCode(), peggingPara.getOrgLevelCode());
        return Html53Count.getReportPageResult(peggingPara, memAllCondition.getTableName(), condition, memAllCondition.getLevelCodeField());
    }

    Condition getBczlOrgCondition(String rowIndex, String orgCode, String orgLevelCode) {
        Condition condition = new Html7Count().getOrgListCondition(orgCode, orgLevelCode).and("d01_code is not null");
        if (StrUtil.equals(rowIndex, "1")) {
            return condition.and("1=0");
        } else if (StrUtil.equals(rowIndex, "2")) {
            condition = condition.and("d01_code like '12%'");
        } else if (StrUtil.equals(rowIndex, "3")) {
            condition = condition.and("d01_code like '13%'");
        } else if (StrUtil.equals(rowIndex, "4")) {
            condition = condition.and("d01_code like '14%'");
        }
        return condition;
    }

    Condition getBczlUnitCondition(String rowIndex, String orgCode, String orgLevelCode) {
        Condition condition = new Html7Count().getUnitListCondition(orgCode, orgLevelCode).and("is_legal = 1");
        if (StrUtil.equals(rowIndex, "5")) {
            return condition.and("d04_code like '912%'");
        } else if (StrUtil.equals(rowIndex, "6")) {
            condition = condition.and("d04_code like '911%'");
        } else if (StrUtil.equals(rowIndex, "7")) {
            condition = condition.and("d04_code in ('921','922')");
        } else if (StrUtil.equals(rowIndex, "8")) {
            condition = condition.and("d04_code like '923%'");
        }
        return condition;
    }

    Condition getBczlMemCondition(String rowIndex, String orgCode, String orgLevelCode) {
        Condition condition = new Html7Count().getMemListCondition(orgCode, orgLevelCode).and("d09_code ='511'");
        if (StrUtil.equals(rowIndex, "9")) {
        } else if (StrUtil.equals(rowIndex, "10")) {
            condition = condition.and("d35_code like '2%' or d35_code like '3%'");
        } else if (StrUtil.equals(rowIndex, "11")) {
            condition = condition.and("d35_code like '4%' or d35_code like '5%' or d35_code like '6%'");
        } else if (StrUtil.equals(rowIndex, "12")) {
            condition = condition.and("d35_code like '7%'");
        }
        return condition;
    }


}
