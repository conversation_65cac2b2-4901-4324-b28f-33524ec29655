package com.zenith.front.core.service.mem;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.zenith.front.api.mem.IMemReportService;
import com.zenith.front.api.mem.IMemService;
import com.zenith.front.api.mem.IMemTrainInfoService;
import com.zenith.front.api.org.IOrgCommitteeService;
import com.zenith.front.api.org.IOrgService;
import com.zenith.front.api.transfer.ITransferApprovalService;
import com.zenith.front.api.unit.*;
import com.zenith.front.api.user.IUserService;
import com.zenith.front.common.mybatisplus.WrapperUtil;
import com.zenith.front.dao.mapper.mem.MemReportMapper;
import com.zenith.front.dao.mapper.org.OrgCommitteeElectMapper;
import com.zenith.front.dao.mapper.unit.UnitCommitteeElectMapper;
import com.zenith.front.dao.mapper.unit.UnitCountrysideMapper;
import com.zenith.front.model.bean.*;
import com.zenith.front.model.vo.BaseReport;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <p>
 * 乡镇行政村等 领导班子统计表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-04-13
 */
@Service
public class MemReportServiceImpl extends ServiceImpl<MemReportMapper, MemReport> implements IMemReportService {
    @Resource
    private IMemReportService iMemReportService;
    @Resource
    private IMemService iMemService;
    @Resource
    private IMemTrainInfoService iMemTrainInfoService;
    @Resource
    private IOrgService iOrgService;
    @Resource
    private IUnitService iUnitService;
    @Resource
    private IUnitAllService iUnitAllService;
    @Resource
    private IOrgCommitteeService iOrgCommitteeService;
    @Resource
    private OrgCommitteeElectMapper orgCommitteeElectMapper;
    @Resource
    private IUnitCommitteeService iUnitCommitteeService;
    @Resource
    private UnitCommitteeElectMapper unitCommitteeElectMapper;
    @Resource
    private IUnitResidentService iUnitResidentService;
    @Resource
    private UnitCountrysideMapper unitCountrysideMapper;
    @Resource
    private MemReportMapper memReportMapper;
    @Resource
    private IUnitOrgLinkedService iUnitOrgLinkedService;
    @Resource
    private ITransferApprovalService iTransferApprovalService;
    @Resource
    protected IUserService iUserService;

    /**
     * 同步组织班子成员信息
     */
    @Override
    public void syncOrgCommittee(String orgCode) {
        //清空当前组织下的组织班子任职情况
        List<MemReport> reports = list(new LambdaQueryWrapper<MemReport>().eq(MemReport::getOrgOrgCode, orgCode).eq(MemReport::getModuleType, "1"));
        List<String> delList = reports.stream().filter(e1 -> StrUtil.isEmpty(e1.getD25Code())).map(MemReport::getId).collect(Collectors.toList());
        if (CollUtil.isNotEmpty(delList)) {
            iMemReportService.remove(new LambdaQueryWrapper<MemReport>().in(MemReport::getId, delList));
        }
        List<String> list = reports.stream().filter(e1 -> StrUtil.isNotEmpty(e1.getD25Code())).map(MemReport::getId).collect(Collectors.toList());
        if (CollUtil.isNotEmpty(list)) {
            iMemReportService.update(new LambdaUpdateWrapper<MemReport>()
                    .set(MemReport::getD022Code, null).set(MemReport::getD022Name, null).set(MemReport::getD138Code, null).set(MemReport::getD138Name, null)
                    .set(MemReport::getRewardForOrg, BigDecimal.ZERO).set(MemReport::getRewardSum, null).in(MemReport::getId, list));
        }
        Org org = iOrgService.findOrgByCode(orgCode);
        //TODO 20220511 与上级党组织相同的乡镇班子，村两委班子 不出数
        if (Objects.isNull(org) || StrUtil.startWithAny(org.getD02Code(), "2")) {
            return;
        }
        UnitOrgLinked unitOrgLinked = iUnitOrgLinkedService.findByOrgCodeAndIsMainUnit(orgCode);
        //TODO 20230104 这里排除单位的主组织并非当前组织
        if (Objects.isNull(unitOrgLinked) || !Objects.equals(unitOrgLinked.getIsOrgMain(), 1)) {
            return;
        }

        Unit unit = iUnitService.findByCode(unitOrgLinked.getUnitCode());
        // 乡镇、行政村、社区领导班子（非法人不统计、城市社区不统计）
        if (Objects.isNull(unit) || !Objects.equals(unit.getIsLegal(), 1) || !StrUtil.equalsAny(unit.getD04Code(), "9121", "9122", "922", "923", "921")) {
            return;
        }
        OrgCommitteeElect elect = orgCommitteeElectMapper.findNewestElect(orgCode, DateUtil.format(new Date(), "yyyy-MM-dd"));
        if (Objects.isNull(elect)) {
            return;
        }

        List<OrgCommittee> committeeList = iOrgCommitteeService.findAllCommitByElect(elect.getCode());
        for (OrgCommittee committee : committeeList) {
            MemReport memReport = new MemReport();
            BeanUtils.copyProperties(committee, memReport);
            memReport.setName(committee.getMemName());
            memReport.setIdcard(committee.getMemIdcard());
            if (StrUtil.equals("1", committee.getD022Code())) {
                memReport.setIsPrincipal("1");
            } else {
                memReport.setIsPrincipal("0");
            }
            if (StrUtil.equals(committee.getMemTypeCode(), "1") && StrUtil.isNotEmpty(memReport.getMemCode())) {
                Mem mem = iMemService.findByCode(memReport.getMemCode());
                if (Objects.nonNull(mem)) {
                    memReport.setName(mem.getName());
                    memReport.setIdcard(mem.getIdcard());
                    memReport.setBirthday(mem.getBirthday());
                    memReport.setSexCode(mem.getSexCode());
                    memReport.setSexName(mem.getSexName());
                    memReport.setD07Code(mem.getD07Code());
                    memReport.setD07Name(mem.getD07Name());
                    memReport.setD08Code(mem.getD08Code());
                    memReport.setD08Name(mem.getD08Name());
                }
                MemTrainInfo trainInfo = iMemTrainInfoService.findByMemCode(memReport.getMemCode());
                memReport.setD142Code(Objects.nonNull(trainInfo) ? trainInfo.getD142Code() : "");
                memReport.setD142Name(Objects.nonNull(trainInfo) ? trainInfo.getD142Name() : "");
            } else {
                memReport.setMemCode(null);
            }
            memReport.setAge(Objects.nonNull(memReport.getBirthday()) ? DateUtil.ageOfNow(memReport.getBirthday()) : null);
            //根据姓名身份证相同，更新村委班子兼任数据
            MemReport report = this.findMemReportOnly(memReport.getMemCode(), memReport.getName(), memReport.getIdcard(), "1");
            // TODO 转为历史任职或删除时，班子兼任数据同步更新
            if (Objects.nonNull(committee.getEndDate()) || Objects.nonNull(committee.getDeleteTime())) {
                if (Objects.nonNull(report) && StrUtil.isNotEmpty(report.getD25Code())) {
                    this.updateHasReserveCommittee(memReport, true);
                    iMemReportService.update(new LambdaUpdateWrapper<MemReport>()
                            .set(MemReport::getD022Code, null).set(MemReport::getD022Name, null).set(MemReport::getD138Code, null).set(MemReport::getD138Name, null)
                            .set(MemReport::getRewardForOrg, BigDecimal.ZERO).set(MemReport::getRewardSum, report.getRewardForVillage())
                            .eq(MemReport::getId, report.getId()));
                }
                continue;
            }
            if (Objects.nonNull(report)) {
                // 获取下级党支部层级码
                if (StrUtil.startWith(report.getMemOrgCode(), org.getOrgCode())) {
                    org.setOrgCode(report.getMemOrgCode());
                    org.setCode(report.getOrgCode());
                }
            }
            memReport.setMemOrgCode(org.getOrgCode());
            memReport.setOrgOrgCode(org.getCode());
            memReport.setD04Code(unit.getD04Code());
            memReport.setD04Name(unit.getD04Name());
            memReport.setUnitCode(unit.getCode());
            memReport.setUnitName(unit.getName());
            memReport.setD35Code(unit.getD35Code());
            memReport.setD35Name(unit.getD35Name());
            memReport.setRewardForOrg(Objects.nonNull(committee.getReward()) ? committee.getReward() : BigDecimal.ZERO);
            memReport.setRewardSum(memReport.getRewardForOrg());
            memReport.setD155Code(unit.getD155Code());
            memReport.setModuleType("1");
            this.updateHasReserveCommittee(memReport, false);

            if (Objects.isNull(report)) {
                iMemReportService.save(memReport);
            } else {
                BeanUtils.copyProperties(memReport, report, "id", "d25Code", "d25Name", "rewardForVillage");
                BigDecimal rewardForVillage = Objects.nonNull(report.getRewardForVillage()) ? report.getRewardForVillage() : BigDecimal.ZERO;
                BigDecimal avg = this.computeAvg(memReport.getRewardForOrg(), rewardForVillage);
                report.setRewardSum(avg);
                iMemReportService.updateById(report);
            }
        }

    }

    /**
     * 同步单位班子成员信息
     */
    @Override
    public void syncUnitCommittee(String unitCode) {
        //清空当前单位下的村委班子任职情况
        List<MemReport> reports = list(new LambdaQueryWrapper<MemReport>().eq(MemReport::getUnitCode, unitCode).eq(MemReport::getModuleType, "1"));
        List<String> delList = reports.stream().filter(e -> StrUtil.isEmpty(e.getD022Code())).map(MemReport::getId).collect(Collectors.toList());
        if (CollUtil.isNotEmpty(delList)) {
            iMemReportService.remove(new LambdaQueryWrapper<MemReport>().in(MemReport::getId, delList));
        }
        List<String> list = reports.stream().filter(e1 -> StrUtil.isNotEmpty(e1.getD022Code())).map(MemReport::getId).collect(Collectors.toList());
        if (CollUtil.isNotEmpty(list)) {
            iMemReportService.update(new LambdaUpdateWrapper<MemReport>().set(MemReport::getD25Code, null).set(MemReport::getD25Name, null)
                    .set(MemReport::getRewardForVillage, BigDecimal.ZERO).set(MemReport::getRewardSum, null).in(MemReport::getId, list));
        }
        UnitOrgLinked unitOrgLinked = iUnitOrgLinkedService.findByUnitCodeAndIsMainAll(unitCode);
        if (Objects.isNull(unitOrgLinked)) {
            return;
        }
        UnitAll unit = iUnitAllService.findByCode(unitCode);
        //社区、村（非法人不统计、城市社区不统计）
        if (Objects.isNull(unit) || !Objects.equals(unit.getIsLegal(), 1) || !StrUtil.equalsAny(unit.getD04Code(), "9121", "9122", "922", "923", "921")) {
            return;
        }
        UnitCommitteeElect elect = unitCommitteeElectMapper.findNewestElect(unitCode, DateUtil.format(new Date(), "yyyy-MM-dd"));
        if (Objects.isNull(elect)) {
            return;
        }
        // 获取单位届次下所有的班子成员
        List<UnitCommittee> committeeList = iUnitCommitteeService.findAllByElect(elect.getCode());
        for (UnitCommittee committee : committeeList) {
            MemReport memReport = new MemReport();
            BeanUtils.copyProperties(committee, memReport);
            memReport.setName(committee.getMemName());
            memReport.setIdcard(committee.getMemIdcard());
            if (StrUtil.equalsAny("41", "51", committee.getD25Code())) {
                memReport.setIsPrincipal("1");
            } else {
                memReport.setIsPrincipal("0");
            }
            if (StrUtil.isNotEmpty(memReport.getMemCode()) && StrUtil.equals(committee.getMemTypeCode(), "1")) {
                Mem mem = iMemService.findByCode(memReport.getMemCode());
                if (Objects.nonNull(mem)) {
                    memReport.setIdcard(mem.getIdcard());
                    memReport.setBirthday(mem.getBirthday());
                    memReport.setSexCode(mem.getSexCode());
                    memReport.setSexName(mem.getSexName());
                    memReport.setD07Code(mem.getD07Code());
                    memReport.setD07Name(mem.getD07Name());
                    memReport.setD08Code(mem.getD08Code());
                    memReport.setD08Name(mem.getD08Name());
                }
                MemTrainInfo trainInfo = iMemTrainInfoService.findByMemCode(memReport.getMemCode());
                memReport.setD142Code(Objects.nonNull(trainInfo) ? trainInfo.getD142Code() : "");
                memReport.setD142Name(Objects.nonNull(trainInfo) ? trainInfo.getD142Name() : "");
            } else {
                memReport.setMemCode(null);
            }
            memReport.setAge(Objects.nonNull(memReport.getBirthday()) ? DateUtil.ageOfNow(memReport.getBirthday()) : null);
            //根据姓名身份证相同，更新村委班子兼任数据
            MemReport report = this.findMemReportOnly(memReport.getMemCode(), memReport.getName(), memReport.getIdcard(), "1");
            // TODO 转为历史任职或删除时，班子兼任数据同步更新
            if (Objects.nonNull(committee.getEndDate()) || Objects.nonNull(committee.getDeleteTime())) {
                if (Objects.nonNull(report) && StrUtil.isNotEmpty(report.getD022Code())) {
                    this.updateHasReserveCommittee(memReport, true);
                    iMemReportService.update(new LambdaUpdateWrapper<MemReport>().set(MemReport::getD25Code, null).set(MemReport::getD25Name, null)
                            .set(MemReport::getRewardForVillage, BigDecimal.ZERO).set(MemReport::getRewardSum, report.getRewardForOrg())
                            .eq(MemReport::getId, report.getId()));
                }
                continue;
            }
            if (Objects.nonNull(report)) {
                // 获取下级党支部层级码
                if (StrUtil.startWith(report.getMemOrgCode(), unit.getCreateUnitOrgCode())) {
                    unit.setCreateUnitOrgCode(report.getMemOrgCode());
                    unit.setCreateOrgCode(report.getOrgCode());
                }
            }
            memReport.setMemOrgCode(unit.getCreateUnitOrgCode());
            memReport.setOrgOrgCode(unit.getCreateOrgCode());
            memReport.setD04Code(unit.getD04Code());
            memReport.setD04Name(unit.getD04Name());
            memReport.setUnitCode(unit.getCode());
            memReport.setUnitName(unit.getName());
            memReport.setD35Code(unit.getD35Code());
            memReport.setD35Name(unit.getD35Name());
            memReport.setHasVillageTransferStudent(StrUtil.isNotEmpty(committee.getHasVillageTransferStudent()) ? Integer.parseInt(committee.getHasVillageTransferStudent()) : null);
            memReport.setRewardForVillage(Objects.nonNull(committee.getReward()) ? committee.getReward() : BigDecimal.ZERO);
            memReport.setRewardSum(memReport.getRewardForVillage());
            memReport.setD155Code(unit.getD155Code());
            memReport.setModuleType("1");
            this.updateHasReserveCommittee(memReport, false);

            if (Objects.isNull(report)) {
                iMemReportService.save(memReport);
            } else {
                BeanUtils.copyProperties(memReport, report, "id", "d022Code", "d022Name", "d138Code", "d138Name", "rewardForOrg");
                BigDecimal rewardForOrg = Objects.nonNull(report.getRewardForOrg()) ? report.getRewardForOrg() : BigDecimal.ZERO;
                BigDecimal avg = this.computeAvg(memReport.getRewardForVillage(), rewardForOrg);
                report.setRewardSum(avg);
                iMemReportService.updateById(report);
            }
        }

    }

    /**
     * 同步驻村第一书记和驻村干部
     */
    @Override
    public void syncUnitResident(String unitCode) {
        if (StrUtil.isEmpty(unitCode)) {
            return;
        }
        iMemReportService.remove(new LambdaQueryWrapper<MemReport>().eq(MemReport::getUnitCode, unitCode).eq(MemReport::getModuleType, "2"));
        UnitAll unit = iUnitAllService.findByCode(unitCode);
        //社区、村（非法人不统计、城市社区不统计）
        if (Objects.isNull(unit) || !Objects.equals(unit.getIsLegal(), 1) || !StrUtil.equalsAny(unit.getD04Code(), "922", "923")) {
            return;
        }
//        UnitResidentElect elect = unitResidentElectMapper.findLatestElect(unitCode, DateUtil.format(new Date(), "yyyy-MM-dd"));
//        if (Objects.isNull(elect)) {
//            return;
//        }

        List<UnitResident> unitResidentList = iUnitResidentService.findByUnitCode(unit.getCode());
        unitResidentList.forEach(resident -> {
            MemReport memReport = new MemReport();
            BeanUtils.copyProperties(resident, memReport);
            memReport.setName(resident.getMemName());
            memReport.setIdcard(resident.getMemIdcard());
            memReport.setBirthday(resident.getMemBirthday());
            if (StrUtil.isNotEmpty(memReport.getMemCode()) && StrUtil.equalsAny(resident.getd139Code(), "1", "2")) {
                Mem mem = iMemService.findByCode(memReport.getMemCode());
                if (Objects.nonNull(mem)) {
                    memReport.setIdcard(mem.getIdcard());
                    memReport.setBirthday(mem.getBirthday());
                    memReport.setSexCode(mem.getSexCode());
                    memReport.setSexName(mem.getSexName());
                    memReport.setD07Code(mem.getD07Code());
                    memReport.setD07Name(mem.getD07Name());
                }
            } else {
                memReport.setMemCode(null);
            }
            memReport.setStartDateZc(resident.getStartDate());
            memReport.setEndDateZc(resident.getEndDate());
            memReport.setAge(Objects.nonNull(memReport.getBirthday()) ? DateUtil.ageOfNow(memReport.getBirthday()) : null);
            memReport.setMemOrgCode(unit.getCreateUnitOrgCode());
            memReport.setOrgOrgCode(unit.getCreateOrgCode());
            memReport.setD04Code(unit.getD04Code());
            memReport.setD04Name(unit.getD04Name());
            memReport.setUnitCode(unit.getCode());
            memReport.setUnitName(unit.getName());
            memReport.setD35Code(unit.getD35Code());
            memReport.setD35Name(unit.getD35Name());
            //是否任期未满调整第一书记：驻村结束时间 < 预计驻村结束时间
            if (StrUtil.equals(resident.getd140Code(), "1") && Objects.nonNull(resident.getEndDate()) && Objects.nonNull(resident.getResidentDate())
                    && resident.getEndDate().before(resident.getResidentDate())) {
                memReport.setHasTenureNotFull("1");
            }
            memReport.setD155Code(unit.getD155Code());
            memReport.setModuleType("2");
            iMemReportService.save(memReport);
        });

    }

    /**
     * 同步村后备干部
     */
    @Override
    public void syncUnitCountryside(String unitCode) {
        if (StrUtil.isEmpty(unitCode)) {
            return;
        }
        iMemReportService.remove(new LambdaQueryWrapper<MemReport>().eq(MemReport::getUnitCode, unitCode).eq(MemReport::getModuleType, "3"));
        UnitAll unit = iUnitAllService.findByCode(unitCode);
        //社区、村（非法人不统计、城市社区不统计）
        if (Objects.isNull(unit) || !Objects.equals(unit.getIsLegal(), 1) || !StrUtil.equalsAny(unit.getD04Code(), "922", "923", "921")) {
            return;
        }
        //TODO 2025.01.11 统计城市社区921，需满足村社区类别选1或2
        if (StrUtil.equals(unit.getD04Code(), "921") && !StrUtil.containsAny(unit.getD155Code(), "1", "2")) {
            return;
        }

        //处理村委班子兼任情况
        List<MemReport> allReports = list(new LambdaQueryWrapper<MemReport>().isNotNull(MemReport::getName).isNotNull(MemReport::getIdcard).eq(MemReport::getModuleType, "1"));
        Set<String> xmIdCardSet = allReports.stream().map(e -> (e.getName() + e.getIdcard()).replaceAll(" ", "")).collect(Collectors.toSet());
        //获取村社区工作者，村社区后备干部数据
        List<UnitCountryside> countrysides = unitCountrysideMapper.getUnitCountrysideList(unitCode, null);
        countrysides.forEach(countryside -> {
            MemReport memReport = new MemReport();
            BeanUtils.copyProperties(countryside, memReport);
            memReport.setName(countryside.getMemName());
            memReport.setIdcard(countryside.getMemIdcard());
            memReport.setAge(Objects.nonNull(memReport.getBirthday()) ? DateUtil.ageOfNow(memReport.getBirthday()) : null);
            memReport.setMemOrgCode(unit.getCreateUnitOrgCode());
            memReport.setOrgOrgCode(unit.getCreateOrgCode());
            memReport.setD04Code(unit.getD04Code());
            memReport.setD04Name(unit.getD04Name());
            memReport.setD35Code(unit.getD35Code());
            memReport.setD35Name(unit.getD35Name());
            memReport.setSideType(countryside.getType());
            memReport.setD155Code(unit.getD155Code());
            memReport.setModuleType("3");
            if (StrUtil.equals(countryside.getMemTypeCode(), "1")) {
                Mem mem = iMemService.getOne(new LambdaQueryWrapper<Mem>().eq(Mem::getName, memReport.getName()).eq(Mem::getIdcard, memReport.getIdcard()).isNull(Mem::getDeleteTime).last("limit 1"));
                memReport.setMemCode(Objects.nonNull(mem) ? mem.getCode() : "1");
            }
            boolean contains = xmIdCardSet.contains((memReport.getName() + memReport.getIdcard()).replaceAll(" ", ""));
            if (contains) {
                memReport.setHasReserveCommittee("1");
            }
            iMemReportService.save(memReport);
        });

    }

    /**
     * 更新是否后备干部兼村委干部标识
     */
    private void updateHasReserveCommittee(MemReport memReport, boolean isHistory) {
        MemReport report = this.findMemReportOnly(null, memReport.getName(), memReport.getIdcard(), "3");
        if (Objects.nonNull(report)) {
            report.setHasReserveCommittee(isHistory ? null : "1");
            iMemReportService.updateById(report);
        }
    }

    /**
     * 同步党员培训情况
     */
    @Override
    public void syncMemTrainInfo(String memCode) {
        Mem mem = iMemService.findByCode(memCode);
        if (Objects.isNull(mem)) {
            return;
        }
        MemReport memReport = iMemReportService.getOne(new LambdaQueryWrapper<MemReport>().eq(MemReport::getName, mem.getName()).eq(MemReport::getIdcard, mem.getIdcard()).last("limit 1"));
        if (Objects.nonNull(memReport)) {
            MemTrainInfo trainInfo = iMemTrainInfoService.findByMemCode(memReport.getMemCode());
            memReport.setD142Code(Objects.nonNull(trainInfo) ? trainInfo.getD142Code() : "");
            memReport.setD142Name(Objects.nonNull(trainInfo) ? trainInfo.getD142Name() : "");
            iMemReportService.updateById(memReport);
        }
    }

    public MemReport findMemReportOnly(String memCode, String name, String idCard, String moduleType) {
        MemReport report = this.getOne(new LambdaQueryWrapper<MemReport>().eq(MemReport::getModuleType, moduleType).eq(MemReport::getMemCode, memCode).last("limit 1"));
        if (Objects.isNull(report)) {
            report = this.getOne(new LambdaQueryWrapper<MemReport>().eq(MemReport::getModuleType, moduleType).eq(MemReport::getName, name).eq(MemReport::getIdcard, idCard).last("limit 1"));
        }
        return report;
    }

    /**
     * 计算两个数的平均数
     */
    private BigDecimal computeAvg(BigDecimal b1, BigDecimal b2) {
        b1 = Objects.isNull(b1) ? BigDecimal.ZERO : b1;
        b2 = Objects.isNull(b2) ? BigDecimal.ZERO : b2;
        BigDecimal add = b1.add(b2);
        if (b1.compareTo(BigDecimal.ZERO) > 0 && b2.compareTo(BigDecimal.ZERO) > 0) {
            return add.divide(new BigDecimal(2), 2, RoundingMode.HALF_UP);
        } else {
            return add;
        }
    }

    /**
     * 初始化统计表数据
     */
    @Override
    public void initMemReport() {
        this.remove(Wrappers.emptyWrapper());
//        this.remove(new LambdaQueryWrapper<MemReport>().eq(MemReport::getModuleType, "1"));

        List<OrgCommitteeElect> orgCommitteeElectList = orgCommitteeElectMapper.selectList(new LambdaQueryWrapper<OrgCommitteeElect>().isNull(OrgCommitteeElect::getDeleteTime).orderByAsc(OrgCommitteeElect::getElectOrgCode));
        orgCommitteeElectList.stream().map(OrgCommitteeElect::getOrgCode).collect(Collectors.toCollection(HashSet::new)).parallelStream().forEach(this::syncOrgCommittee);
        System.out.println("-->>>同步组织班子成员信息...");

        List<UnitCommitteeElect> unitCommitteeElectList = unitCommitteeElectMapper.selectList(new LambdaQueryWrapper<UnitCommitteeElect>().isNull(UnitCommitteeElect::getDeleteTime));
        unitCommitteeElectList.stream().map(UnitCommitteeElect::getUnitCode).collect(Collectors.toCollection(HashSet::new)).parallelStream().forEach(this::syncUnitCommittee);
        System.out.println("-->>>同步单位班子成员信息...");

        List<UnitResident> unitResidentList = iUnitResidentService.list(new LambdaQueryWrapper<UnitResident>().isNull(UnitResident::getDeleteTime).isNull(UnitResident::getEndDate));
        unitResidentList.stream().map(UnitResident::getUnitCode).collect(Collectors.toCollection(HashSet::new)).parallelStream().forEach(this::syncUnitResident);
        System.out.println("-->>>同步驻村第一书记和驻村干部...");

        List<UnitCountryside> countrysideList = unitCountrysideMapper.getUnitCountrysideList(null, null);
        countrysideList.stream().map(UnitCountryside::getUnitCode).collect(Collectors.toCollection(HashSet::new)).parallelStream().forEach(this::syncUnitCountryside);
        System.out.println("-->>>同步村后备干部...");
    }

    @Override
    public List<BaseReport> selectListByOrgCode(String orgCode) {
        List<MemReport> memReportList = memReportMapper.selectListByOrgCode(orgCode);
        // TODO: 2022/10/26 数据转换，兼容加解密
        return memReportList.stream().map(pojo -> {
            BaseReport baseReport = new BaseReport();
            BeanUtils.copyProperties(pojo, baseReport);
            return baseReport;
        }).collect(Collectors.toList());
    }

    @Override
    public List<BaseReport> selectListByOrgCodeEntry(String orgCode) {
//        List<MemReport> memReportList = memReportMapper.selectListByOrgCode(orgCode);
        LambdaQueryWrapper<MemReport> wrapper = new LambdaQueryWrapper<>();
        wrapper.isNull(MemReport::getDeleteTime);
        wrapper.likeRight(MemReport::getMemOrgCode, orgCode);
        wrapper.ne(MemReport::getD04Code, "921");
        WrapperUtil.excludeEncrypt(wrapper, MemReport.class);
        List<MemReport> memReportList = memReportMapper.selectList(wrapper);
        // TODO: 2022/10/26 数据转换，兼容加解密
        return memReportList.stream().map(pojo -> {
            BaseReport baseReport = new BaseReport();
            BeanUtils.copyProperties(pojo, baseReport);
            return baseReport;
        }).collect(Collectors.toList());
    }

    @Override
    public void updateMemInfo(String code, String name, String idcard, String memOrgCode, String phone, Date birthday, Integer age, String sexCode
            , String sexName, String d07Code, String d07Name) {
        //根据code查询党员
        List<MemReport> memReportList = findByMemCode(code);
        if (CollUtil.isEmpty(memReportList)) {
            //根据姓名和身份证查询党员
            List<MemReport> reportList = findByNameAndIdcard(name, idcard);
            if (CollUtil.isEmpty(reportList)) {
                return;
            } else {
                memReportList.addAll(reportList);
            }
        }
        for (MemReport memReport : memReportList) {
            memReport.setName(name);
            memReport.setIdcard(idcard);
            memReport.setMemOrgCode(memOrgCode);
            memReport.setPhone(phone);
            memReport.setBirthday(birthday);
            memReport.setAge(age);
            memReport.setSexCode(sexCode);
            memReport.setSexName(sexName);
            memReport.setD07Code(d07Code);
            memReport.setD07Name(d07Name);
        }
        updateBatchById(memReportList);
    }

    @Override
    public List<MemReport> findByMemCode(String memCode) {
        return list(new LambdaQueryWrapper<MemReport>().eq(MemReport::getMemCode, memCode));
    }

    @Override
    public List<MemReport> findByNameAndIdcard(String name, String idcard) {
        return list(new LambdaQueryWrapper<MemReport>().eq(MemReport::getName, name).eq(MemReport::getIdcard, idcard));
    }

//    /**
//     * 初始化经办人
//     */
//    public void initTransferApprovalHandlerMan() {
//        List<TransferApproval> list = iTransferApprovalService.list(new LambdaQueryWrapper<TransferApproval>().isNull(TransferApproval::getHandlerMan));
//        list.parallelStream().forEach(approval -> {
//            String str = iUserService.findMemNameByUserId(approval.getUserId());
//            if (StrUtil.isNotEmpty(str)) {
//                approval.setHandlerMan(str);
//                iTransferApprovalService.updateById(approval);
//            }
//        });
//        syncMemService.syncMemAbroadCountryName();
//        System.out.println("初始化经办人...");
//    }

}
