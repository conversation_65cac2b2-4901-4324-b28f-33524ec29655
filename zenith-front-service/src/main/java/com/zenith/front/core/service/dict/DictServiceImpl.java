package com.zenith.front.core.service.dict;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.zenith.front.api.dict.IDictService;
import com.zenith.front.common.constant.DictConstant;
import com.zenith.front.common.constant.LogicCompareType;
import com.zenith.front.common.kit.JackSonUtil;
import com.zenith.front.common.mybatisplus.MybatisPlusConfig;
import com.zenith.front.core.cache.CacheUtils;
import com.zenith.front.dao.mapper.codetable.CodeTableConfigMapper;
import com.zenith.front.dao.mapper.dict.DictMapper;
import com.zenith.front.model.custom.Dict;
import com.zenith.front.model.custom.Record;
import com.zenith.front.model.dto.DicDTO;
import com.zenith.front.model.dto.DicNormalDTO;
import com.zenith.front.model.message.OutMessage;
import com.zenith.front.model.message.Status;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <p>
 * 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-07-10
 */
@Service
public class DictServiceImpl implements IDictService {

    @Resource
    private DictMapper dictMapper;
    @Resource
    private CodeTableConfigMapper codeTableConfigMapper;

    @Override
    public List<Record> getAll(String dicName) {
        //将表名存进ThreadLocal，用以动态表名设置
        MybatisPlusConfig.MY_TABLE_NAME_LOCAL.set(dicName);
        QueryWrapper<Dict> tQueryWrapper = new QueryWrapper<>();
        tQueryWrapper.select("*");
        tQueryWrapper.orderByAsc("key");
        List<Dict> dictList = dictMapper.selectList(tQueryWrapper);
        List<Record> result = new ArrayList<>();
        dictList.forEach(val -> {
            Map<String, Object> map = JackSonUtil.beanToMap(val);
            Record record = new Record();
            record.put(map);
            result.add(record);
        });
        return result;
    }

    @Override
    public List<Record> getDic(String dicName) {
        return getAll(dicName);
    }

    @Override
    public OutMessage tableUpDic(DicDTO dicDTO) {

        String dicName = dicDTO.getDicName();
        Integer id = dicDTO.getId();
        String remark = dicDTO.getRemark();
        boolean disabled = dicDTO.getDisabled();
        boolean enabled = dicDTO.getEnabled();

        //获取相关得Dict 相关信息
        Record dictByIdAndName = getDictByIdAndName(id, dicName);
        dictByIdAndName.set("remark", remark);
        dictByIdAndName.set("disabled", disabled);
        dictByIdAndName.set("enabled", enabled);

        boolean b = updateDict(dicName, dictByIdAndName);

        return new OutMessage(b ? Status.SUCCESS : Status.FAIL);
    }

    private Record getDictByIdAndName(Integer id, String dicName) {
        //将表名存进ThreadLocal，用以动态表名设置
        MybatisPlusConfig.MY_TABLE_NAME_LOCAL.set(dicName);
        QueryWrapper<Dict> tQueryWrapper = new QueryWrapper<>();
        tQueryWrapper.eq("id", id);
        tQueryWrapper.select("*");
        Dict dict = dictMapper.selectOne(tQueryWrapper);
        Map<String, Object> map = JackSonUtil.beanToMap(dict);
        Record record = new Record();
        record.put(map);
        return record;
    }

    @Override
    public List<Record> getDictByKeys(Set<String> keys,String dicName) {
        //将表名存进ThreadLocal，用以动态表名设置
        MybatisPlusConfig.MY_TABLE_NAME_LOCAL.set(dicName);
        QueryWrapper<Dict> tQueryWrapper = new QueryWrapper<>();
        tQueryWrapper.in("key", keys);
        tQueryWrapper.select("*");
        tQueryWrapper.orderByAsc("key");
        List<Dict> dictList = dictMapper.selectList(tQueryWrapper);
        List<Record> result = new ArrayList<>();
        dictList.forEach(val -> {
            Map<String, Object> map = JackSonUtil.beanToMapNonHump(val);
            Record record = new Record();
            record.put(map);
            result.add(record);
        });
        return result;
    }

    private boolean updateDict(String dicName, Record dictByIdAndName) {
        //将表名存进ThreadLocal，用以动态表名设置
        MybatisPlusConfig.MY_TABLE_NAME_LOCAL.set(dicName);
        // 将 Map 转换为 实体类
        Dict dict = JSON.parseObject(JSON.toJSONString(dictByIdAndName.getColumns()), Dict.class);
        return dictMapper.updateById(dict) > 0;
    }

    @Override
    public List<Record> getDictD48(String parent) {
        //将表名存进ThreadLocal，用以动态表名设置
        MybatisPlusConfig.MY_TABLE_NAME_LOCAL.set("dict_d48");
        QueryWrapper<Dict> tQueryWrapper = new QueryWrapper<>();
        tQueryWrapper.eq(Objects.nonNull(parent), "parent", parent);
        tQueryWrapper.orderByAsc("id");
        tQueryWrapper.select("*");
        List<Map<String, Object>> maps = dictMapper.selectMaps(tQueryWrapper);
        List<Record> result = new ArrayList<>();
        maps.forEach(val -> {
            Record record = new Record();
            record.put(val);
            result.add(record);
        });
        return result;
    }

    @Override
    public Map<String, String> getKeyNameMapByType(String dicName) {
        List<Record> recordList = getDic(dicName);
        return recordList.stream().filter(e -> Objects.nonNull(e.get("key")) && Objects.nonNull(e.get("name")))
                .collect(Collectors.toMap(record -> record.getStr("key"), record -> record.getStr("name")));
    }

    @Override
    public OutMessage<List<Record>> getExportList(String tableName) {
        List<Record> recordList = CacheUtils.getExportFieldList(tableName);
        return new OutMessage<>(Status.SUCCESS, recordList);
    }

    /**
     * 根据逻辑配置获取匹配项
     *
     * @param dto
     * @return
     */
    @Override
    public Map<String, String> normalList(DicNormalDTO dto) {
        Map<String, String> result = new HashMap<>(1);

        List<LinkedHashMap<String, Object>> codeTableColList = CacheUtils.getLogicalConfiguration();
        if(CollUtil.isEmpty(codeTableColList)){
            return result;
        }
        //按表名分组
        Map<String, List<LinkedHashMap<String, Object>>> tableCodeMap = codeTableColList.stream().collect(Collectors.groupingBy(ob -> ob.get("table_code").toString()));
        if(Objects.isNull(tableCodeMap)){
            return result;
        }
        //按字段key分组
        Map<String, List<LinkedHashMap<String, Object>>> colCodeMap = tableCodeMap.get(dto.getTableCode()).stream().collect(Collectors.groupingBy(ob -> ob.get("col_code").toString()));
        // 按 colCode
        List<LinkedHashMap<String, Object>> list = colCodeMap.get(dto.getColCode());
        if (CollUtil.isEmpty(list)) {
            return result;
        }
        List<LinkedHashMap<String, Object>> stringObjectLinkedHashMap = list.stream()
                .filter(e -> Objects.equals(e.get("col_value"), dto.getColValue()) && Objects.equals(e.get("relation_col_code"), dto.getCompareColCode()))
                .collect(Collectors.toList());
        if (CollUtil.isEmpty(stringObjectLinkedHashMap)) {
            return result;
        }
        LinkedHashMap<String, Object> comParMap = stringObjectLinkedHashMap.get(0);

        // 类型
        Object lable = comParMap.get("col_type");
        // 非字典表不查询
        if(!Objects.equals(lable, "lable")){
            return result;
        }

        // 比较类型
        Object compareType = comParMap.get("compare_type");
        // 只匹配eq相等的
        if(Objects.isNull(compareType) || !Objects.equals(LogicCompareType.EQ.keyword, compareType)){
            return result;
        }
        Object value = comParMap.get("compare_value");
        JSONArray array = JSONUtil.parseArray(String.valueOf(value));
        List<String> valList = array.toList(String.class);
        // 需要匹配的字典表
        Object dictTableName = comParMap.get("relation_col_lection_code");
        if(Objects.nonNull(dictTableName)){
            List<Record> records = CacheUtils.getDic(String.valueOf(dictTableName));
            if(CollUtil.isEmpty(records)){
                return result;
            }
            // 叶子节点
            records = records.stream().filter(e -> Objects.equals(e.get("leaf"), true)).collect(Collectors.toList());
            if(CollUtil.isEmpty(records)){
                return result;
            }
            Record record = records.stream().filter(e -> valList.contains(e.getStr("key"))).findFirst().orElse(new Record());
            result.put(record.getStr("key"), record.getStr("name"));
            return result;
        }
        return result;
    }

    /**
     * 获取籍贯字典表当前层级及下级
     *
     * @param parent
     * @return
     */
    @Override
    public List<Record> getDictD48JuniorList(String parent, String keyWord) {
        List<Record> data = CacheUtils.getDic(DictConstant.DICT_D48);
        if(StrUtil.isNotEmpty(keyWord)){
            return data.stream()
                    .filter(e -> e.getStr("name").contains(keyWord) || e.getStr("key").contains(keyWord))
                    .collect(Collectors.toList());
        }
        if(StrUtil.isNotEmpty(parent)){
            return data.stream().filter(e -> e.getStr("key").startsWith(parent)).collect(Collectors.toList());
        }
        return new ArrayList<>();
    }

    /**
     * 非驼峰下划线
     *
     * @param dictName
     * @return
     */
    @Override
    public List<Record> getAllNonHump(String dictName) {
        //将表名存进ThreadLocal，用以动态表名设置
        MybatisPlusConfig.MY_TABLE_NAME_LOCAL.set(dictName);
        QueryWrapper<Dict> tQueryWrapper = new QueryWrapper<>();
        tQueryWrapper.select("*");
        tQueryWrapper.orderByAsc("key");
        List<Dict> dictList = dictMapper.selectList(tQueryWrapper);
        List<Record> result = new ArrayList<>();
        dictList.forEach(val -> {
            Map<String, Object> map = JackSonUtil.beanToMapNonHump(val);
            Record record = new Record();
            record.put(map);
            result.add(record);
        });
        return result;
    }

}
