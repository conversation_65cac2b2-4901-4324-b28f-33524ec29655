package com.zenith.front.core.service.unit;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.zenith.front.api.unit.IUnitCitySituationService;
import com.zenith.front.common.kit.CodeUtil;
import com.zenith.front.common.kit.StrKit;
import com.zenith.front.core.service.sync.SyncUnitService;
import com.zenith.front.dao.mapper.unit.UnitCitySituationMapper;
import com.zenith.front.model.dto.UnitCitySituationDTO;
import com.zenith.front.model.dto.UnitSiteConditionsListDTO;
import com.zenith.front.model.message.OutMessage;
import com.zenith.front.model.message.Status;
import com.zenith.front.model.bean.UnitCitySituation;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;

/**
 * <p>
 * 城市基层党建情况 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-11-12
 */
@Service
public class UnitCitySituationServiceImpl extends ServiceImpl<UnitCitySituationMapper, UnitCitySituation> implements IUnitCitySituationService {

    @Resource
    private UnitCitySituationMapper situationMapper;
    @Resource
    private SyncUnitService syncUnitService;

    @Override
    public OutMessage addOrUpdate(UnitCitySituationDTO data) {
        if (StrKit.isBlank(data.getCode())) {
            UnitCitySituation conditions = new UnitCitySituation();
            BeanUtils.copyProperties(data,conditions);
            conditions.setEsId(CodeUtil.getEsId());
            conditions.setCreateTime(new Date());
            int insert = situationMapper.insert(conditions);
            if (insert > 0) {
                syncUnitService.syncUnitCitySituation(data.getUnitCode());
            }
        } else {
            UnitCitySituation conditions = situationMapper.selectById(data.getCode());
            if (conditions == null) {
                return new OutMessage<>(Status.OBJECT_DBISNOTALIVE);
            }
            BeanUtils.copyProperties(data,conditions);
            conditions.setUpdateTime(new Date());
            int i = situationMapper.updateById(conditions);
            if (i > 0){
                syncUnitService.syncUnitCitySituation(data.getUnitCode());
            }
        }
        return new OutMessage<>(Status.SUCCESS);
    }

    @Override
    public OutMessage getList(UnitSiteConditionsListDTO data) {
        LambdaQueryWrapper<UnitCitySituation> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(UnitCitySituation::getUnitCode,data.getUnitCode());
        wrapper.isNull(UnitCitySituation::getDeleteTime)
                .orderByAsc(UnitCitySituation::getCreateTime);
        Page<UnitCitySituation> page = new Page<>(data.getPageNum(), data.getPageSize());
        Page<UnitCitySituation> pageList = page(page, wrapper);
        return new OutMessage(Status.SUCCESS,pageList);
    }

    @Override
    public OutMessage findByCode(String code) {
        LambdaQueryWrapper<UnitCitySituation> sql = new LambdaQueryWrapper<>();
        sql.eq(UnitCitySituation::getUnitCode, code).isNull(UnitCitySituation::getDeleteTime).orderByDesc(UnitCitySituation::getCreateTime).last("LIMIT 1");
        return new OutMessage(Status.SUCCESS,getOne(sql));
    }

    @Override
    public OutMessage delByCode(String code) {
        LambdaUpdateWrapper<UnitCitySituation> sql = new LambdaUpdateWrapper<>();
        sql.set(UnitCitySituation::getDeleteTime,new Date()).eq(UnitCitySituation::getCode,code);
        boolean update = update(sql);
        if (update){
            UnitCitySituation one = this.getOne(new LambdaQueryWrapper<UnitCitySituation>().eq(UnitCitySituation::getCode, code));
            syncUnitService.syncUnitCitySituation(one.getUnitCode());
        }
        return new OutMessage(Status.SUCCESS);
    }
}
