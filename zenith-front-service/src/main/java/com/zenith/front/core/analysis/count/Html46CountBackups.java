package com.zenith.front.core.analysis.count;

import cn.hutool.core.util.StrUtil;
import com.b1809.analysis.entity.PeggingPara;
import com.b1809.analysis.extend.ITableCount;
import com.b1809.analysis.plugin.elasticsearch.EsKit;
import com.jfinal.plugin.activerecord.Record;
import com.zenith.front.common.constant.CommonConstant;
import com.zenith.front.core.analysis.ext.condition.MemAllCondition;
import com.zenith.front.core.analysis.ext.condition.OrgAllCondition;
import com.zenith.front.core.analysis.ext.condition.UnitAllCondition;
import org.jooq.Condition;
import org.jooq.Record1;
import org.jooq.SelectConditionStep;
import org.jooq.SelectHavingStep;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.*;

import static com.zenith.front.common.constant.DBConstant.DSL_CONTEXT;
import static org.jooq.impl.DSL.*;

/**
 * 专题调查表一 农村党建相关情况（乡镇社区作为村统计）
 */
@Component
public class Html46CountBackups implements ITableCount {

    @Override
    public String getReportCode() {
        return "46_csq.html";
    }

    @Override
    public Map<String, Map<String, Number>> doCount(String reportCode, String orgCode, String orgLevelCode) throws Exception {
        return getCount(orgCode, orgLevelCode, null);
    }

    public Map<String, Map<String, Number>> getCount(String orgCode, String orgLevelCode, String tableYear) throws Exception {
        String ccpUnitAll = StrUtil.isEmpty(tableYear) ? "ccp_unit_all" : "ccp_unit_all_" + tableYear;
        String ccpOrgAll = StrUtil.isEmpty(tableYear) ? "ccp_org_all" : "ccp_org_all_" + tableYear;
        String ccpMemAll = StrUtil.isEmpty(tableYear) ? "ccp_mem_all" : "ccp_mem_all_" + tableYear;
        Map<String, Number> result = new LinkedHashMap<>();
        Html53Count.initTableCol(1, 1, 1, 23, result);
        // todo：2024.01.15 异地扶贫搬迁安置村的 d04_code可以包含城市社区921
        Condition condition = noCondition().and(field(name("d04_code"), String.class).in("922", "923", "921"));
        SelectConditionStep<Record1<Object>> records1 = DSL_CONTEXT.select(field("d155_code,first_secretary_select,first_secretary_code,secretary_training_num,has_thousand,has_bundled,promoted_num,adjusted_num,operating_expenses,village_per,secretary_salary,space_area,new_expand_area,d04_code,is_legal"))
                .from(table(name(ccpUnitAll)).as("ccp_unit_all")).where(new UnitAllCondition().create(orgCode, orgLevelCode)).and(condition.and("delete_time is null"));
        List<Record> records = EsKit.findBySql(records1.toString()).toRecord();
        BigDecimal operatingExpenses = BigDecimal.ZERO;
        BigDecimal villagePer = BigDecimal.ZERO;
        BigDecimal secretarySalary = BigDecimal.ZERO;
        int index1 = 0, index2 = 0, index3 = 0;
        for (Record record : records) {
            String d04Code = record.getStr("d04_code");
            Integer legal = record.getInt("is_legal");
            String d155Code = record.getStr("d155_code");
            // 单位类别不为城市社区的或者 村类别为 异地扶贫搬迁安置村的 或者 脱贫村的
            if (!"921".equals(d04Code) || StrUtil.containsAny(d155Code, "1", "2")) {
                //16 本年各级培训第一书记（人次）
                Html53Count.setTableMapValue("1", "16", record.getInt("secretary_training_num"), result);
                //17 为第一书记安排不低于1万元工作经费的村
                Html53Count.setTableMapValue("1", "17", record.getInt("has_thousand"), result);
                //18 派出单位落实责任、项目、资金捆绑的村
                Html53Count.setTableMapValue("1", "18", record.getInt("has_bundled"), result);
                //19 提拔使用或晋级的第一书记
                Html53Count.setTableMapValue("1", "19", record.getInt("promoted_num"), result);
                //20 因工作不胜任召回调整的第一书记
                Html53Count.setTableMapValue("1", "20", record.getInt("adjusted_num"), result);
            }

            // 乡镇社区、行政村的统计
            if (StrUtil.equalsAny(d04Code, "922", "923")) {
                //1 行政村总数
                setBackupsMapValue("1", "1", 1, result, d04Code, legal);

                //13
                BigDecimal expenses = record.getBigDecimal("operating_expenses");
                if (Objects.nonNull(expenses) && StrUtil.equalsAny(d04Code, "922", "923") && Objects.equals(legal, 1)) {
                    operatingExpenses = operatingExpenses.add(expenses);
                    index1++;
                }
                BigDecimal per = record.getBigDecimal("village_per");
                if (Objects.nonNull(per) && StrUtil.equalsAny(d04Code, "922", "923") && Objects.equals(legal, 1)) {
                    villagePer = villagePer.add(per);
                    index2++;
                }
                BigDecimal salary = record.getBigDecimal("secretary_salary");
                if (Objects.nonNull(salary) && StrUtil.equalsAny(d04Code, "922", "923") && Objects.equals(legal, 1) && salary.compareTo(BigDecimal.ZERO) > 0) {
                    secretarySalary = secretarySalary.add(salary);
                    index3++;
                }

                // 暂无活动场所的行政村
                if (Objects.nonNull(record.getInt("space_area")) && record.getInt("space_area") == 0) {
                    setBackupsMapValue("1", "28", CommonConstant.ONE_INT, result, d04Code, legal);
                }
                // 活动场所面积200㎡以上的行政村
                if (Objects.nonNull(record.getInt("space_area")) && record.getInt("space_area") > 200) {
                    setBackupsMapValue("1", "29", CommonConstant.ONE_INT, result, d04Code, legal);
                }
                // 本年新建或改扩建活动场所数量
                if (Objects.nonNull(record.getInt("new_expand_area")) && record.getInt("new_expand_area") > CommonConstant.ZERO_INT) {
                    // TODO: 2023/1/12 调整为当村社区场所填写只要有数字，只算1
                    setBackupsMapValue("1", "30", CommonConstant.ONE_INT, result, d04Code, legal);
                }
                // 未完成“五小”建设的乡镇
                Html53Count.setTableMapValue("1", "31", CommonConstant.ZERO_INT, result);
            }
        }
        //21 平均每村运转经费（万元∕年）
        Html53Count.setTableMapValue("1", "21", Html46Count.getAvgCount(operatingExpenses, index1), result);
        //22 平均每村办公经费（万元∕年）
        Html53Count.setTableMapValue("1", "22", Html46Count.getAvgCount(villagePer, index2), result);
        //23 村党组织书记平均报酬（万元∕年）
        Html53Count.setTableMapValue("1", "23", Html46Count.getAvgCount(secretarySalary, index3), result);

        SelectConditionStep<Record1<Object>> orgR = DSL_CONTEXT.select(field("d01_code")).from(table(name(ccpOrgAll)).as("ccp_org_all")).where(new Html7Count().getOrgListCondition(orgCode, orgLevelCode));
        List<Record> records2 = EsKit.findBySql(orgR.toString()).toRecord();
        long count = records2.stream().filter(e -> StrUtil.startWith(e.getStr("d01_code"), "14")).count();
        // 村干部基本报酬和村级组织办公经费合计低于11万元的县（市、区、旗）TODO 专调1  p列不出数
        Html53Count.setTableMapValue("1", "24", 0, result);
        // 落实正常离任村干部生活补贴的县（市、区、旗）
        Html53Count.setTableMapValue("1", "25", Math.toIntExact(count), result);
        // 落实农村公共服务运行维护支出或服务群众专项经费的县（市、区、旗）
        Html53Count.setTableMapValue("1", "26", Math.toIntExact(count), result);
        // 落实村民小组长误工补贴的县（市、区、旗）
        Html53Count.setTableMapValue("1", "27", Math.toIntExact(count), result);

        //现任第一书记 2-7
        SelectHavingStep<Record1<Object>> memStep = DSL_CONTEXT.select(field("end_date,industry,d08_code,d04_code,d155_code,has_org_slack_village,org_commit_code,count(1) total")).from(table(name(ccpMemAll)).as("ccp_mem_all"))
                .where(new MemAllCondition().create(orgCode, orgLevelCode).and(condition.and("d08_code in ('5','50')"))).groupBy(field("end_date,industry,d08_code,d04_code,d155_code,has_org_slack_village,org_commit_code"));
        List<Record> records3 = EsKit.findBySql(memStep.toString()).toRecord();
        for (Record record : records3) {
            // 5：驻村第一书记  50：工作队员
            String d08Code = record.getStr("d08_code");
            Integer total = record.getInt("total");
            String d155Code = record.getStr("d155_code");
            String hasOrgSlackVillage = record.getStr("has_org_slack_village");
            String d04Code = record.getStr("d04_code");
            String d197Code = record.getStr("industry");
            // 任职结束时间
            Object endDate = record.getObject("end_date");

            // 单位类别不为城市社区的或者 村类别为 异地扶贫搬迁安置村的 或者脱贫村的
            if (!"921".equals(d04Code) || StrUtil.containsAny(d155Code, "1", "2")) {
                if (StrUtil.equals(d08Code, "5")) {
                    //2 累计选派第一书记
                    Html53Count.setTableMapValue("1", "2", total, result);
                    // 如果任职时间不为空则下列不需要统计值
                    if (Objects.nonNull(endDate)) {
                        continue;
                    }
                    //3 现任第一书记
                    Html53Count.setTableMapValue("1", "3", total, result);
                    //4 省级选派的
                    if (StrUtil.contains(d197Code, "1")) {
                        Html53Count.setTableMapValue("1", "4", total, result);
                    }
                    //5 市级选派的
                    if (StrUtil.contains(d197Code, "2")) {
                        Html53Count.setTableMapValue("1", "5", total, result);
                    }
                    //6 县级选派的  (2024乡镇选派也统计进去--王察)
                    if (StrUtil.equalsAny(d197Code, "3","4"))  {
                        Html53Count.setTableMapValue("1", "6", total, result);
                    }

                    //7 派驻脱贫村的
                    if (StrUtil.contains(d155Code, "1")) {
                        Html53Count.setTableMapValue("1", "7", total, result);
                    }
                    //8 派驻异地扶贫搬迁安置村的
                    if (StrUtil.contains(d155Code, "2")) {
                        Html53Count.setTableMapValue("1", "8", total, result);
                    }
                    //9 派驻乡村振兴重点村的
                    if (StrUtil.containsAny(d155Code, "3", "4")) {
                        Html53Count.setTableMapValue("1", "9", total, result);
                    }
                    //10 派驻党组织软弱涣散村的
                    if (StrUtil.equals(hasOrgSlackVillage, "1")) {
                        Html53Count.setTableMapValue("1", "10", total, result);
                    }
                } else if (StrUtil.equals(d08Code, "50")) {
                    //11 累计选派工作队员
                    Html53Count.setTableMapValue("1", "11", total, result);
                    // 如果任职时间不为空则下列不需要统计值
                    if (Objects.nonNull(endDate)) {
                        continue;
                    }
                    //12 现任工作队员
                    Html53Count.setTableMapValue("1", "12", total, result);

                    //13 省级选派的
                    if (StrUtil.contains(d197Code, "1")) {
                        Html53Count.setTableMapValue("1", "13", total, result);
                    }
                    //14 市级选派的
                    if (StrUtil.contains(d197Code, "2")) {
                        Html53Count.setTableMapValue("1", "14", total, result);
                    }
                    //6 县级选派的  (2024乡镇选派也统计进去--王察)
                    if (StrUtil.equalsAny(d197Code, "3","4")) {
                        Html53Count.setTableMapValue("1", "15", total, result);
                    }
                }
            }
        }
        Map<String, Map<String, Number>> mapMap = new HashMap<>();
        mapMap.put("table0", result);
        return mapMap;
    }


    /**
     * 乡镇社区作为村统计表
     * 除了bcdefghijkl 需要统计城市社区、乡镇社区和村外，其他的只要村和乡镇社区
     * 只有bcdefghijkl 是统计法人和非法人，其他的还是只统计法人
     */
    public void setBackupsMapValue(String r, String colIndex, Integer addNum, Map<String, Number> result, String d04Code, Integer isLegal) {
        if (StrUtil.equalsAny(colIndex, "1", "28", "29", "30")) {
            if (StrUtil.equalsAny(d04Code, "922", "923") && Objects.equals(isLegal, 1)) {
                Html53Count.setTableMapValue(r, colIndex, addNum, result);
            }
        } else {
            Html53Count.setTableMapValue(r, colIndex, addNum, result);
        }
    }

    @Override
    public Map<String, Object> doPegging(PeggingPara peggingPara) throws Exception {
        String colIndex = peggingPara.getColIndex();
        if (StrUtil.equalsAny(colIndex, "2", "3", "4", "5", "6", "7", "8", "9", "10", "11", "12", "13", "14", "15")) {
            Condition condition = new MemAllCondition().create(peggingPara.getOrgCode(), peggingPara.getOrgLevelCode()).and(new Html46Count().getSecretaryCondition(colIndex, null))
                    .and(field(name("d04_code"), String.class).in("922", "923")
                            .or(field(name("d04_code"), String.class).eq("921").and(field(name("d155_code"), String.class).like("%1%").or(field(name("d155_code"), String.class).like("%2%"))))
                    );
            MemAllCondition memAllCondition = new MemAllCondition();
            return Html53Count.getReportPageResult(peggingPara, memAllCondition.getTableName(), condition, memAllCondition.getLevelCodeField());
        }
        // 村干部基本报酬和村级组织办公经费合计低于11万元的县（市、区、旗）
        if (StrUtil.equalsAny(colIndex, "24", "25", "26", "27")) {
            Condition condition = new Html7Count().getOrgListCondition(peggingPara.getOrgCode(), peggingPara.getOrgLevelCode()).and("d01_code like '14%'");
            if (StrUtil.equals(colIndex, "24")) {
                condition = condition.and("1=0");
            }
            OrgAllCondition cond = new OrgAllCondition();
            return Html53Count.getReportPageResult(peggingPara, cond.getTableName(), condition, cond.getLevelCodeField());
        }
        Condition condition = new UnitAllCondition().create(peggingPara.getOrgCode(), peggingPara.getOrgLevelCode()).and("delete_time is null")
                .and(field(name("d04_code"), String.class).in("922", "923")
                        .or(field(name("d04_code"), String.class).eq("921").and(field(name("d155_code"), String.class).like("%1%").or(field(name("d155_code"), String.class).like("%2%"))))
                ).and(new Html46Count().getTableRowCondition(colIndex, true));
        UnitAllCondition cond = new UnitAllCondition();
        return Html53Count.getReportPageResult(peggingPara, cond.getTableName(), condition, cond.getLevelCodeField());
    }

    @Override
    public Map<String, Number> doReplenishCount(String reportCode, String orgCode, String orgLevelCode) throws Exception {
        return new Html46Count().getReplenishCount(orgCode, orgLevelCode, null, true);
    }

    @Override
    public Map<String, Object> doReplenishPegging(PeggingPara peggingPara) throws Exception {
        if (StrUtil.equalsAny(peggingPara.getRowIndex(), "4", "5")) {
            Condition condition = new Html7Count().getUnitListCondition(peggingPara.getOrgCode(), peggingPara.getOrgLevelCode())
                    .and(field(name("d04_code"), String.class).in("922", "923")).and(new Html46Count().getRowCondition(peggingPara.getRowIndex()));

            UnitAllCondition unitAllCondition = new UnitAllCondition();
            return Html53Count.getReportPageResult(peggingPara, unitAllCondition.getTableName(), condition, unitAllCondition.getLevelCodeField());
        }
        Condition condition = new Html7Count().getOrgListCondition(peggingPara.getOrgCode(), peggingPara.getOrgLevelCode()).and(new Html46Count().getRow2Condition(peggingPara.getRowIndex()));
        OrgAllCondition orgAllCondition = new OrgAllCondition();
        return Html53Count.getReportPageResult(peggingPara, orgAllCondition.getTableName(), condition, orgAllCondition.getLevelCodeField());
    }

}
