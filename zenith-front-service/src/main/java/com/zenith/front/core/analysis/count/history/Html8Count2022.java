package com.zenith.front.core.analysis.count.history;

import cn.hutool.core.util.StrUtil;
import com.b1809.analysis.entity.PeggingPara;
import com.b1809.analysis.extend.ITableCount;
import com.zenith.front.core.analysis.ext.condition.year2022.MemAllCondition2022A;
import com.zenith.front.core.analysis.ext.condition.year2022.OrgAllConditionA;
import org.jooq.Condition;
import org.springframework.stereotype.Component;

import java.util.Map;

/**
 * 民主评议党员情况
 *
 * <AUTHOR>
 * @date 2023/1/7
 */
@Component
public class Html8Count2022 extends Html8CountHistory implements ITableCount {

    @Override
    public String getReportCode() {
        return "2022_8.html";
    }

    @Override
    public Map<String, Map<String, Number>> doCount(String reportCode, String orgCode, String orgLevelCode) throws Exception {
        return getCount(orgCode, orgLevelCode, Html1Count2022.TABLE_ES_YEAR_A);
    }


    @Override
    public Map<String, Object> doPegging(PeggingPara peggingPara) throws Exception {
        Condition condition = getColCondition(peggingPara.getColIndex()).and("appraisal_org_code like '" + peggingPara.getOrgLevelCode() + "%'");
        String rowIndex = peggingPara.getRowIndex();
        if (StrUtil.equalsAny(rowIndex, "1", "2")) {
            condition = condition.and(getOrgCondition()).and(StrUtil.equals(rowIndex, "1") ? "has_join_reviewers=1" : "has_end_reviewers=1");
            OrgAllConditionA cond = new OrgAllConditionA();
            return Html48CountHistory.getReportPageResult(peggingPara, cond.getTableName(), condition, cond.getLevelCodeFieldAppraisal());
        }
        MemAllCondition2022A memAllCond = new MemAllCondition2022A();
        condition = condition.and(getRowCondition(rowIndex)).and("has_join_reviewers=1 and d08_code in('1','2')");
        return Html48CountHistory.getReportPageResult(peggingPara, memAllCond.getTableName(), condition, memAllCond.getLevelCodeFieldAppraisal());
    }

    @Override
    public Map<String, Number> doReplenishCount(String reportCode, String orgCode, String orgLevelCode) throws Exception {
        return null;
    }

    @Override
    public Map<String, Object> doReplenishPegging(PeggingPara peggingPara) throws Exception {
        return null;
    }
}
