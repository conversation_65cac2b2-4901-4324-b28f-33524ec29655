package com.zenith.front.core.job;

import cn.hutool.core.collection.CollUtil;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import com.zenith.front.api.org.IOrgService;
import com.zenith.front.common.kit.StrKit;
import com.zenith.front.dao.mapper.activist.ActivistTransferRecordMapper;
import com.zenith.front.model.bean.MqMessage;
import com.zenith.front.model.bean.Org;
import com.zenith.front.model.vo.ActivistsTransferInMessageVO;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.text.MessageFormat;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 积极分子转入
 *
 * <AUTHOR>
 * @version V1.0
 * @date 2022/8/1 10:16
 */
@Component
public class ActivistsTransferIn extends BaseMqMessageService {

    private static final String NORMAL_MESSAGE = "入党积极分子{0}已拟转入{1}，请及时审批。";
    private static final String TYPE = "10";

    @Resource
    private ActivistTransferRecordMapper activistTransferRecordMapper;
    @Resource
    private IOrgService orgService;

    @XxlJob("activistsTransferIn")
    public void activistsTransferIn() {
        XxlJobHelper.log("积极分子转入,消息提醒.");
        //查询积极分子转入
        List<ActivistsTransferInMessageVO> transferOutMessageVOList = activistTransferRecordMapper.findInMessage();
        if (CollUtil.isNotEmpty(transferOutMessageVOList)) {
            List<MqMessage> mqMessageList = new ArrayList<>();
            List<String> orgCodeList = transferOutMessageVOList.stream().map(ActivistsTransferInMessageVO::getOrgCode).collect(Collectors.toList());
            List<Org> orgList = orgService.findByOrgCodeList(orgCodeList);
            Map<String, Org> orgMap = orgList.stream().collect(Collectors.toMap(Org::getCode, t -> t));

            for (ActivistsTransferInMessageVO activistsTransferInMessageVO : transferOutMessageVOList) {
                String orgCode = activistsTransferInMessageVO.getOrgCode();
                Org org = orgMap.get(orgCode);
                if (Objects.nonNull(org)) {
                    String name = activistsTransferInMessageVO.getName();
                    Date createTime = activistsTransferInMessageVO.getCreateTime();
                    String orgName = activistsTransferInMessageVO.getOrgName();

                    String messageFormat = MessageFormat.format(NORMAL_MESSAGE, name, getOrDefault(orgName, ""));

                    MqMessage message = new MqMessage();
                    message.setCode(StrKit.getRandomUUID());
                    message.setType(TYPE);
                    message.setMessage(messageFormat);
                    message.setOrgCode(org.getOrgCode());
                    message.setCreateTime(new Date());
                    message.setSortTime(createTime);
                    mqMessageList.add(message);
                }
            }
            //按类型删除
            mqMessageService.deleteByType(TYPE);
            if (CollUtil.isNotEmpty(mqMessageList)) {
                //批量保存
                mqMessageService.saveBatch(mqMessageList);
            }
        }
    }
}
