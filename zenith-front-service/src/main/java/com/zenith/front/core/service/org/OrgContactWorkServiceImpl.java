package com.zenith.front.core.service.org;

import cn.afterturn.easypoi.excel.ExcelExportUtil;
import cn.afterturn.easypoi.excel.entity.ExportParams;
import cn.hutool.core.io.FileUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.zenith.front.api.org.IOrgContactWorkService;
import com.zenith.front.common.exception.ServiceException;
import com.zenith.front.core.kit.ExcelUtil;
import com.zenith.front.dao.mapper.org.OrgContactMemMapper;
import com.zenith.front.dao.mapper.org.OrgContactWorkMapper;
import com.zenith.front.model.dto.FileDto;
import com.zenith.front.model.dto.OrgContactDTO;
import com.zenith.front.model.dto.OrgContactListDTO;
import com.zenith.front.model.message.OutMessage;
import com.zenith.front.model.message.Status;
import com.zenith.front.model.bean.OrgContactMem;
import com.zenith.front.model.bean.OrgContactWork;
import com.zenith.front.model.vo.OrgContactVO;
import com.zenith.front.model.vo.OrgContactVOExcel;
import org.apache.poi.ss.usermodel.Workbook;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.io.File;
import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <p>
 * 党支部工作开展情况服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-12-14
 */
@Service
public class OrgContactWorkServiceImpl extends ServiceImpl<OrgContactWorkMapper, OrgContactWork> implements IOrgContactWorkService {

    @Resource
    private OrgContactMemMapper contactMemMapper;

    /**
     * 添加工作开展情况
     */
    @Override
    public OutMessage<?> addContactWork(OrgContactDTO data, String basePath) {
        OrgContactWork orgContactWork = new OrgContactWork();
        BeanUtils.copyProperties(data, orgContactWork);
        FileDto fileDto = data.getFilePath();
        if (ObjectUtil.isNotEmpty(fileDto)) {
            String url = fileDto.getUrl();
            if (StringUtils.hasText(url)) {
                String toUrl = moveFile(basePath, url, "org_contact_work");
                fileDto.setUrl(toUrl);
                orgContactWork.setFilePath(JSON.toJSONString(fileDto));
            }
        }
        orgContactWork.setCreateTime(new Date());
        return new OutMessage<>(this.save(orgContactWork) ? Status.SUCCESS : Status.FAIL);
    }

    public String moveFile(String basePath, String url, String toDir) {
        File src = FileUtil.newFile(basePath + url);
        if (FileUtil.exist(src)) {
            File mkdir = FileUtil.mkdir(basePath + toDir);
            File target = FileUtil.newFile(mkdir + System.getProperty("file.separator") + src.getName());
            FileUtil.move(src, target, true);
            return target.getAbsolutePath().replace(FileUtil.newFile(basePath).getAbsolutePath(), "");
        }
        return "";
    }

    /**
     * 工作开展情况列表
     */
    @Override
    public OutMessage<?> contactWorkList(OrgContactListDTO data) {
        Page<OrgContactVO> orgContactVOPage = new Page<>(data.getPageNum(), data.getPageSize());
        String contactCode = "";
        if (StrUtil.isNotEmpty(data.getName())) {
            OrgContactMem one = contactMemMapper.selectOne(new LambdaQueryWrapper<OrgContactMem>().like(OrgContactMem::getName, data.getName()).eq(OrgContactMem::getOrgCode, data.getOrgCode()).select(OrgContactMem::getCode));
            if (ObjectUtil.isEmpty(one)) {
                throw new ServiceException(500, "当前党支部不存在该联系人");
            }
            contactCode = one.getCode();
        }
        Page<OrgContactWork> orgContactWorkPage = baseMapper.selectPage(new Page<>(data.getPageNum(), data.getPageSize()), new LambdaQueryWrapper<OrgContactWork>()
                .eq(OrgContactWork::getOrgCode, data.getOrgCode())
                .like(ObjectUtil.isNotEmpty(data.getName()), OrgContactWork::getContactCode, contactCode)
                .orderByDesc(OrgContactWork::getStartDate)
        );
        List<OrgContactVO> contactVOS = orgContactWorkPage.getRecords().stream().map(t -> {
            OrgContactVO vo = new OrgContactVO();
            String[] split = t.getContactCode().split(",");
            BeanUtils.copyProperties(t, vo);
            if (ObjectUtil.isNotEmpty(orgContactWorkPage)) {
                List<OrgContactMem> orgContactMems = contactMemMapper.selectList(new LambdaQueryWrapper<OrgContactMem>().in(OrgContactMem::getCode, Arrays.asList(split)));
                vo.setName(orgContactMems.stream().map(OrgContactMem::getName).collect(Collectors.joining(",")));
            }
            if (StrUtil.isNotEmpty(t.getFilePath())) {
                vo.setFilePath(JSON.parseObject(t.getFilePath(), FileDto.class));
            }
            return vo;
        }).collect(Collectors.toList());
        if (ObjectUtil.isNotEmpty(data.getYear())) {
            List<OrgContactVO> contactVOList = new ArrayList<>();
            for (OrgContactVO contactVO : contactVOS) {
                Calendar instance = Calendar.getInstance();
                instance.setTime(contactVO.getStartDate());
                if (ObjectUtil.equal(Integer.parseInt(data.getYear()), instance.get(Calendar.YEAR))) {
                    contactVOList.add(contactVO);
                }
            }
            BeanUtils.copyProperties(orgContactWorkPage,orgContactVOPage);
            orgContactVOPage.setRecords(contactVOList);
            return new OutMessage<>(Status.SUCCESS, orgContactVOPage);
        }
        BeanUtils.copyProperties(orgContactWorkPage,orgContactVOPage);
        orgContactVOPage.setRecords(contactVOS);
        return new OutMessage<>(Status.SUCCESS, orgContactVOPage);
    }

    /**
     * 修改工作开展情况
     */
    @Override
    public OutMessage<?> updateContactWork(OrgContactDTO data, String basePath) {
        OrgContactWork orgContactWork = new OrgContactWork();
        BeanUtils.copyProperties(data, orgContactWork);
        FileDto fileDto = data.getFilePath();
        if (ObjectUtil.isNotEmpty(fileDto)) {
            String url = fileDto.getUrl();
            if (StringUtils.hasText(url)) {
                String toUrl = moveFile(basePath, url, "org_contact_work");
                fileDto.setUrl(toUrl);
            }
            orgContactWork.setFilePath(JSON.toJSONString(fileDto));
        } else {
            orgContactWork.setFilePath("");
        }
        orgContactWork.setUpdateTime(new Date());
        return new OutMessage<>(this.updateById(orgContactWork) ? Status.SUCCESS : Status.FAIL);
    }

    /**
     * 删除工作开展情况
     */
    @Override
    public OutMessage<?> deleteContactWork(String code) {
        return new OutMessage<>(this.removeById(code) ? Status.SUCCESS : Status.FAIL);
    }

    @Override
    public OutMessage<?> findContactWork(String code) {
        OrgContactVO vo = new OrgContactVO();
        OrgContactWork orgContactWork = this.getById(code);
        String filePath = orgContactWork.getFilePath();
        if (StringUtils.hasText(filePath)) {
            vo.setFilePath(JSON.parseObject(filePath, FileDto.class));
        }
        BeanUtils.copyProperties(orgContactWork, vo);
        String[] split = orgContactWork.getContactCode().split(",");
        List<OrgContactMem> orgContactMemList = contactMemMapper.selectList(new LambdaQueryWrapper<OrgContactMem>().in(OrgContactMem::getCode, Arrays.asList(split)).select(OrgContactMem::getName));
        vo.setName(orgContactMemList.stream().map(OrgContactMem::getName).collect(Collectors.joining(",")));
        return new OutMessage<>(Status.SUCCESS, vo);
    }

    @Override
    public OutMessage<?> outExportXlsx(OrgContactListDTO data) throws Exception {
        data.setPageNum(1);
        data.setPageSize(10000000);
        OutMessage<?> outMessage = this.contactWorkList(data);
        Page<OrgContactVO> page = (Page<OrgContactVO>) outMessage.getData();
        int index = 1;
        List<OrgContactVOExcel> result = new ArrayList<>();
        DateFormat dateFormat = new SimpleDateFormat("yyyy/MM/dd");
        for (OrgContactVO orgContactVO : page.getRecords()) {
            OrgContactVOExcel excel = new OrgContactVOExcel();
            BeanUtils.copyProperties(orgContactVO, excel);
            excel.setStartDate(dateFormat.format(orgContactVO.getStartDate()));
            excel.setIndex(index);
            result.add(excel);
            index++;
        }
        Workbook workbook = ExcelExportUtil.exportExcel(new ExportParams(null, ""), OrgContactVOExcel.class, result);
        Map<String, String> map = ExcelUtil.exportFile(workbook, "");
        return new OutMessage<>(Status.SUCCESS, map);
    }

    @Override
    public Map<String, List<OrgContactWork>> getListLikeOrgCode(Date startDate, Date endDate, String orgCode) {
        LambdaQueryWrapper<OrgContactWork> wrapper = new LambdaQueryWrapper<>();
        wrapper.like(OrgContactWork::getOrgLevelCode, orgCode);
        wrapper.isNull(OrgContactWork::getDeleteTime);
        wrapper.between(OrgContactWork::getStartDate, startDate, endDate);
        wrapper.like(OrgContactWork::getd156Code, "4");
        return this.list(wrapper).stream().collect(Collectors.groupingBy(OrgContactWork::getOrgLevelCode));
    }

    @Override
    public List<OrgContactWork> getContactWorkList(String orgCode) {
        return baseMapper.getContactWorkList(orgCode);
    }
}
