package com.zenith.front.core.service.report;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.zenith.front.api.report.IStatisticsYearService;
import com.zenith.front.common.exception.ServiceException;
import com.zenith.front.dao.mapper.report.StatisticsYearMapper;
import com.zenith.front.framework.file.core.MinioTemplate;
import com.zenith.front.model.bean.StatisticsYear;
import com.zenith.front.model.vo.BetweenReportDateVo;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.LocalDate;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Objects;

/**
 * <p>
 * 数据统计配置表服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-02-06
 */
@Service
public class StatisticsYearServiceImpl extends ServiceImpl<StatisticsYearMapper, StatisticsYear> implements IStatisticsYearService {
    @Resource
    private MinioTemplate minioTemplate;


    /**
     * 获取统计年度
     */
    @Override
    public String getStatisticalYear() {
        StatisticsYear statisticsYear = getStatisticsYearById("1");
        if (Objects.isNull(statisticsYear)) {
            throw new ServiceException(500, "请联系管理员配置年度统计报告期时间");
        }
        return StrUtil.toString(DateUtil.year(statisticsYear.getStartDate()));
    }

    /**
     * 是否统计年度
     */
    @Override
    public boolean isStatisticalYear(Date startDate, Date endDate) {
        String year = getStatisticalYear();
        if (Objects.isNull(startDate) || Objects.isNull(endDate) || StrUtil.isBlank(year)) {
            return false;
        }
        final int currYear = Integer.parseInt(year);
        final int startYear = cn.hutool.core.date.DateUtil.year(startDate);
        final int endYear = cn.hutool.core.date.DateUtil.year(endDate);
        return !(startYear > currYear || endYear < currYear);
    }

    /**
     * 获取年度统计周期
     */
    @Override
    public List<Date> annualStatisticsPeriod() {
        List<Date> dateList = new ArrayList<>();
        StatisticsYear statisticsYear = getStatisticsYearById("1");
        if (Objects.isNull(statisticsYear) || Objects.isNull(statisticsYear.getStartDate()) || Objects.isNull(statisticsYear.getEndDate())) {
            throw new ServiceException(500, "请联系管理员配置年度统计报告期时间");
        }
        dateList.add(statisticsYear.getStartDate());
        dateList.add(DateUtil.endOfDay(statisticsYear.getEndDate()));
        return dateList;
    }

    /**
     * 根据主键获取数据
     */
    @Override
    public StatisticsYear getStatisticsYear(String id) {
        StatisticsYear statisticsYear = getStatisticsYearById(id);
        if (Objects.isNull(statisticsYear)) {
            throw new ServiceException(500, "请联系管理员配置年度统计报告期时间");
        }
        return statisticsYear;
    }

    /**
     * 获取报告期内查询条件
     *
     * @param field 字段
     */
    @Override
    public String getReportDateSql(String field) {
        try {
            StatisticsYear statisticsYear = getStatisticsYearById("1");
            Date startDate = statisticsYear.getStartDate();
            Date endDate = statisticsYear.getEndDate();
            if (Objects.nonNull(startDate) && Objects.nonNull(endDate)) {
                return field + " between '" + DateUtil.formatDate(startDate) + "' and '" + DateUtil.formatDate(endDate) + "'";
            }
            if (Objects.nonNull(startDate)) {
                return field + " >= '" + DateUtil.formatDate(startDate) + "'";
            }
            if (Objects.nonNull(endDate)) {
                return field + " <= '" + DateUtil.formatDate(endDate) + "'";
            }
            return " 1=0 ";
        } catch (Exception e) {
            throw new ServiceException(500, "请联系管理员配置年度统计报告期时间");
        }
    }

    /**
     * 获取报告期内查询条件
     *
     * @param field 字段
     */
    @Override
    public String getEsReportDateSql(String field) {
        try {
            StatisticsYear statisticsYear = getStatisticsYearById("1");
            Date startDate = statisticsYear.getStartDate();
            Date endDate = statisticsYear.getEndDate();
            if (Objects.nonNull(startDate) && Objects.nonNull(endDate)) {
                return field + " between " + startDate.getTime() + " and " + endDate.getTime();
            }
            if (Objects.nonNull(startDate)) {
                return field + " >= " + startDate.getTime();
            }
            if (Objects.nonNull(endDate)) {
                return field + " <= " + endDate.getTime();
            }
            return " 1=0 ";
        } catch (Exception e) {
            throw new ServiceException(500, "请联系管理员配置年度统计报告期时间");
        }
    }

    /**
     * 当前时间是否在报告期内
     */
    @Override
    public BetweenReportDateVo isBeforeReportDate(Date date, String id) {
        BetweenReportDateVo vo = new BetweenReportDateVo();
        try {
            if (Objects.isNull(date)) {
                vo.setIsBetween(true);
                return vo;
            }
            StatisticsYear statisticsYear = getStatisticsYearById(id);
            Date startDate = statisticsYear.getStartDate();
            Date endDate = statisticsYear.getEndDate();
            vo.setMessage(this.getBetweenReportMsg(startDate, endDate));
            long dateTime = DateUtil.parse(DateUtil.format(date, "yyyyMMdd")).getTime();
            if (Objects.nonNull(startDate) && Objects.nonNull(endDate)) {
                vo.setIsBetween(dateTime >= startDate.getTime() && dateTime <= endDate.getTime());
                return vo;
            }
            if (Objects.nonNull(startDate)) {
                vo.setIsBetween(dateTime >= startDate.getTime());
                return vo;
            }
            if (Objects.nonNull(endDate)) {
                vo.setIsBetween(dateTime <= endDate.getTime());
                return vo;
            }
        } catch (Exception e) {
            throw new ServiceException(500, "请联系管理员配置年度统计报告期时间");
        }
        vo.setIsBetween(false);
        return vo;
    }

    /**
     * 判断是否在报告期内
     */
    @Override
    public boolean isBetweenReportDate(Date date) {
        if (Objects.isNull(date)) {
            return false;
        }
        BetweenReportDateVo vo = isBeforeReportDate(date, "1");
        return vo.getIsBetween();
    }


    public String getBetweenReportMsg(Date startDate, Date endDate) {
        StringBuilder string = new StringBuilder(Objects.nonNull(startDate) ? DateUtil.formatDate(startDate) : "****");
        StringBuilder builder = new StringBuilder();
        if (Objects.nonNull(endDate)) {
            boolean before = LocalDate.now().isBefore(com.zenith.front.common.kit.DateUtil.dateToLocaleDate(endDate));
            builder.append(before ? LocalDate.now().toString() : DateUtil.formatDate(endDate));
        } else {
            builder.append("****");
        }
        return string.append("至").append(builder).toString();
    }

    /**
     * 根据主键获取配置
     */
    private StatisticsYear getStatisticsYearById(String id) {
        return getById(id);
    }


    /**
     * 更新服务器文件
     */
    public void removeMinioFile(String dtoPath, String dbPath) {
        try {
            if (StrUtil.isEmpty(dbPath)) {
                return;
            }
            if (StrUtil.isEmpty(dtoPath) || !StrUtil.equals(dtoPath, dbPath)) {
                minioTemplate.delete(dbPath);
            }
        } catch (Exception e) {
            log.error("删除文件失败：{}" + dbPath);
        }
    }

}
