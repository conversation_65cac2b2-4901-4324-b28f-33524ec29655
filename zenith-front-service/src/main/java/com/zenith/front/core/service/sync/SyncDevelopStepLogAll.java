package com.zenith.front.core.service.sync;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.zenith.front.api.develop.IDevelopStepLogAllService;
import com.zenith.front.api.develop.IDevelopStepLogService;
import com.zenith.front.api.mem.IMemAllInfoService;
import com.zenith.front.api.mem.IMemDevelopAllService;
import com.zenith.front.api.mem.IMemDevelopService;
import com.zenith.front.api.mem.IMemService;
import com.zenith.front.api.report.IStatisticsYearService;
import com.zenith.front.api.sync.ISyncMemService;
import com.zenith.front.api.unit.IUnitAllService;
import com.zenith.front.common.annualstatisticsconf.AnnualStatisticsConstant;
import com.zenith.front.core.cache.CacheUtils;
import com.zenith.front.core.kit.CollectionUtil;
import com.zenith.front.dao.mapper.develop.DevelopStepLogMapper;
import com.zenith.front.model.message.OutMessage;
import com.zenith.front.model.message.Status;

import com.zenith.front.model.bean.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2022/01/02
 */
@Service
@Slf4j
public class SyncDevelopStepLogAll {

    @Resource
    private IMemDevelopService iMemDevelopService;
    @Resource
    private IMemDevelopAllService iMemDevelopAllService;
    @Resource
    private DevelopStepLogMapper developStepLogMapper;
    @Resource
    private IDevelopStepLogService iDevelopStepLogService;
    @Resource
    private IDevelopStepLogAllService iDevelopStepLogAllService;
    @Resource
    private IMemAllInfoService iMemAllInfoService;
    @Resource
    private ISyncMemService iSyncMemService;
    @Resource
    private IUnitAllService iUnitAllService;
    @Resource
    private IMemService iMemService;
    @Resource
    private IStatisticsYearService iStatisticsYearService;


    /**
     * 同步发展党员相关信息
     *
     * @param code log表code
     */
    public OutMessage<?> syncDevelopStepLog(String code) {
        if (!AnnualStatisticsConstant.SYNC_DB_ENABLE || StrUtil.isEmpty(code)) {
            return new OutMessage<>(Status.SUCCESS);
        }
        DevelopStepLog stepLog = iDevelopStepLogService.findByCode(code);
        if (Objects.isNull(stepLog)) {
            return new OutMessage<>(Status.SUCCESS);
        }
        DevelopStepLogAll stepLogAll = new DevelopStepLogAll();
        BeanUtils.copyProperties(stepLog, stepLogAll);
        //设置其他属性值
        this.setMemLinkedUnit(stepLog, stepLogAll);
        if (Objects.nonNull(stepLogAll.getBirthday())) {
            stepLogAll.setAge(DateUtil.ageOfNow(stepLogAll.getBirthday()));
        }
        if (Objects.nonNull(stepLogAll.getTopreJoinOrgDate())) {
            stepLogAll.setTopreJoinOrgDateYear(DateUtil.year(stepLogAll.getTopreJoinOrgDate()));
        }
        //设置国民经济行业和生产性服务行业
        setMenDevelopD194AndD195(stepLog,stepLogAll);
        DevelopStepLogAll dbDevelopStep = iDevelopStepLogAllService.findByCode(code);
        //设置第几产业
        setIndustry(stepLog,stepLogAll);
        if (Objects.isNull(dbDevelopStep)) {
            iDevelopStepLogAllService.save(stepLogAll);
        } else {
            BeanUtils.copyProperties(stepLogAll, dbDevelopStep, "id");
            iDevelopStepLogAllService.update(dbDevelopStep, Wrappers.<DevelopStepLogAll>lambdaUpdate()
                    .set(Objects.isNull(dbDevelopStep.getDeleteTime()), DevelopStepLogAll::getDeleteTime, null)
                    .set(Objects.isNull(dbDevelopStep.getIsTransfer()), DevelopStepLogAll::getIsTransfer, null)
                    .set(Objects.isNull(dbDevelopStep.getD04Code()), DevelopStepLogAll::getD04Code, null)
                    .set(Objects.isNull(dbDevelopStep.getD04Name()), DevelopStepLogAll::getD04Name, null)
                    .set(Objects.isNull(dbDevelopStep.getUnitCode()), DevelopStepLogAll::getUnitCode, null)
                    .set(Objects.isNull(dbDevelopStep.getUnitName()), DevelopStepLogAll::getUnitName, null)
                    .set(Objects.isNull(dbDevelopStep.getStatisticalUnit()), DevelopStepLogAll::getStatisticalUnit, null)
                    .set(Objects.isNull(dbDevelopStep.getHasUnitStatistics()), DevelopStepLogAll::getHasUnitStatistics, null)
                    .set(Objects.isNull(dbDevelopStep.getUnitInformation()), DevelopStepLogAll::getUnitInformation, null)
                    .set(Objects.isNull(dbDevelopStep.getHasUnitProvince()), DevelopStepLogAll::getHasUnitProvince, null)
                    .set(StrUtil.isEmpty(dbDevelopStep.getJobNatureCode()), DevelopStepLogAll::getJobNatureCode, null)
                    .set(Objects.isNull(dbDevelopStep.getAdvancedModelCode()), DevelopStepLogAll::getAdvancedModelCode, null)
                    .set(Objects.isNull(dbDevelopStep.getHasWorker()), DevelopStepLogAll::getHasWorker, null)
                    .set(Objects.isNull(dbDevelopStep.getD194Code()), DevelopStepLogAll::getD194Code, null)
                    .set(Objects.isNull(dbDevelopStep.getD194Name()), DevelopStepLogAll::getD194Name, null)
                    .set(Objects.isNull(dbDevelopStep.getD195Code()), DevelopStepLogAll::getD195Code, null)
                    .set(Objects.isNull(dbDevelopStep.getD195Name()), DevelopStepLogAll::getD195Name, null)
                    .eq(DevelopStepLogAll::getId, dbDevelopStep.getId()));
        }
        return new OutMessage<>(Status.SUCCESS);
    }

    private void setMenDevelopD194AndD195(DevelopStepLog stepLog, DevelopStepLogAll dbDevelopStep) {
        dbDevelopStep.setD194Code(stepLog.getD194Code());
        dbDevelopStep.setD194Name(stepLog.getD194Name());
        dbDevelopStep.setD195Code(stepLog.getD195Code());
        dbDevelopStep.setD195Name(stepLog.getD195Name());
    }

    private void setIndustry(DevelopStepLog stepLog, DevelopStepLogAll dbDevelopStep) {
        String d194Code = stepLog.getD194Code();
        if (StrUtil.startWith(d194Code,"A")){
            dbDevelopStep.setIndustry("1");
        }else if (StrUtil.startWithAny(d194Code,"B","C","D","E")){
            dbDevelopStep.setIndustry("2");
        }else if(!StrUtil.equals(d194Code,"U")){
            dbDevelopStep.setIndustry("3");
        }
    }
    /**
     * 批量同步发展党员log
     */
    public void syncBatchDevelopStepLogAll() {
        List<DevelopStepLog> developStepLogAllNotIn = developStepLogMapper.findDevelopStepLogAllNotIn();
        if (CollUtil.isNotEmpty(developStepLogAllNotIn)) {
            developStepLogAllNotIn.parallelStream().forEach(developStepLog -> syncDevelopStepLog(developStepLog.getCode()));
        }

//        List<MemDevelopAll> list = iMemDevelopAllService.list(new QueryWrapper<MemDevelopAll>().lambda().select(MemDevelopAll::getId, MemDevelopAll::getCode).apply("delete_time is null and d08_code = '3'"));
//        list.parallelStream().forEach(e -> {
//            MemDevelop develop = iMemDevelopService.findByCode(e.getCode());
//            if (Objects.nonNull(develop) && Objects.nonNull(develop.getObjectDate())) {
//                e.setTopreJoinOrgDateYear(DateUtil.year(develop.getObjectDate()));
//                iMemDevelopAllService.updateById(e);
//            }
//        });
    }


    private void setMemLinkedUnit(DevelopStepLog mem, DevelopStepLogAll memAllInfo) {
        String d04Name = CollectionUtil.listRecordToMap(CacheUtils.getDic("dict_d04"), "key", "name").getOrDefault(mem.getD04Code(), "");
        memAllInfo.setD04Name(StrUtil.isNotEmpty(d04Name) ? d04Name : "");
        //人事关系是否在党组织关联单位内 0，人事关系所在单位是否省内单位 0 1
        if (Objects.equals(mem.getHasUnitStatistics(), 0)) {
            //人事关系所在单位是否省内单位 1
            memAllInfo.setD04Code(mem.getD04Code());
            memAllInfo.setD04Name(d04Name);
            //人事关系所在单位名称
            memAllInfo.setUnitName(mem.getUnitInformation());
            // 中间交换区关联单位主键 0
            memAllInfo.setUnitCode(mem.getStatisticalUnit());
            return;
        }
        //人事关系是否在党组织关联单位内 1
        String statisticalUnit = mem.getStatisticalUnit();
        String unitCode = "";
        if (StrUtil.isNotEmpty(statisticalUnit)) {
            unitCode = statisticalUnit;
        } else {
            //根据组织查询主单位
            UnitOrgLinked unitOrgLinked = iSyncMemService.getUnitOrgLinked(mem.getOrgCode());
            if (Objects.nonNull(unitOrgLinked)) {
                unitCode = unitOrgLinked.getUnitCode();
            }
        }
        if (StrUtil.isNotEmpty(unitCode)) {
            UnitAll unitAll = iUnitAllService.findByCode(unitCode);
            setDevelopStepLogAllValue(memAllInfo, unitAll);
        } else {
            setDevelopStepLogAllValue(memAllInfo, null);
        }
    }


    public void setDevelopStepLogAllValue(DevelopStepLogAll logAll, UnitAll unit) {
        logAll.setD04Name(Objects.isNull(unit) ? "" : unit.getD04Name());
        logAll.setD04Code(Objects.isNull(unit) ? "" : unit.getD04Code());
        logAll.setUnitCode(Objects.isNull(unit) ? "" : unit.getCode());
        logAll.setUnitName(Objects.isNull(unit) ? "" : unit.getName());
    }

    /**
     * 修改党组织同步发展党员信息
     */
    public void syncDevelopStepLogByUpdateOrg(Org org, UnitAll unit, List<String> subOrgList) {
        List<DevelopStepLogAll> list = iDevelopStepLogAllService.list(new LambdaQueryWrapper<DevelopStepLogAll>().in(DevelopStepLogAll::getOrgCode, subOrgList)
                .apply("delete_time is null and d08_code = '3' and EXTRACT ( YEAR FROM \"topre_join_org_date\" ) = " + iStatisticsYearService.getStatisticalYear()));
        if (CollUtil.isNotEmpty(list)) {
            List<DevelopStepLogAll> updateList = new ArrayList<>();
            for (DevelopStepLogAll e : list) {
                boolean flag = false;
                DevelopStepLogAll updateMem = new DevelopStepLogAll();
                //人事关系是否在党组织关联单位内1
                if (Objects.equals(e.getHasUnitStatistics(), 1)) {
                    if (!StrUtil.equalsAny(org.getD01Code(), "632", "634", "932")) {
                        flag = true;
                    }
                } else {
                    //人事关系所在单位是否省内单位1
                    if (Objects.equals(e.getHasUnitProvince(), 1)) {
                        if (Objects.nonNull(unit) && StrUtil.equals(e.getStatisticalUnit(), unit.getCode())) {
                            flag = true;
                        }
                    } else {
                        //关系转接进来的数据
                        if (StrUtil.isEmpty(e.getStatisticalUnit()) && StrUtil.isEmpty(e.getUnitInformation())) {
                            flag = true;
                        }
                    }
                }
                if (flag) {
                    updateMem.setId(e.getId());
                    updateMem.setCode(e.getCode());
                    updateMem.setUpdateTime(e.getUpdateTime());
                    updateMem.setStatisticalUnit(Objects.isNull(unit) ? "" : unit.getCode());
                    updateMem.setUnitInformation(Objects.isNull(unit) ? "" : unit.getName());
                    this.setDevelopStepLogAllValue(updateMem, unit);
                    updateList.add(updateMem);
                }
            }
            if (updateList.size() > 0) {
                iDevelopStepLogAllService.updateBatchById(updateList, updateList.size());
                DevelopStepLogAll all = updateList.stream().findFirst().orElse(new DevelopStepLogAll());
                iDevelopStepLogService.update(new LambdaUpdateWrapper<DevelopStepLog>().set(DevelopStepLog::getUnitInformation, all.getUnitName()).set(DevelopStepLog::getD04Code, all.getD04Code()).set(DevelopStepLog::getStatisticalUnit, all.getUnitCode())
                        .in(DevelopStepLog::getCode, updateList.stream().map(DevelopStepLogAll::getCode).collect(Collectors.toList())));
                System.out.println("------>>>> DevelopStepLogAll同步结束个数：" + updateList.size());
            }
        }
    }

    /**
     * 设置本年度发展党员基本信息
     */
    private void setStepLogNullValue() {
        List<DevelopStepLog> list = iDevelopStepLogService.list(new QueryWrapper<DevelopStepLog>().lambda().select()
                .apply("delete_time IS NULL AND d08_code = '3' AND (d09_code IS NULL OR d07_code IS NULL OR d06_code IS NULL OR idcard IS NULL OR sex_code IS NULL OR age is null or birthday is null)"));
        list.parallelStream().forEach(e -> {
            String memCode = e.getMemCode();
            e.setD09Code(StrUtil.isEmpty(e.getD09Code()) ? this.getD09CodeByMemCode(memCode) : null);
            e.setD07Code(StrUtil.isEmpty(e.getD07Code()) ? this.getD07CodeByMemCode(memCode) : null);
            e.setD06Code(StrUtil.isEmpty(e.getD06Code()) ? this.getD06CodeByMemCode(memCode) : null);
            e.setIdcard(StrUtil.isEmpty(e.getIdcard()) ? this.getIdCardByMemCode(memCode) : null);
            e.setSexCode(StrUtil.isEmpty(e.getSexCode()) ? this.getSexCodeByMemCode(memCode) : null);
            e.setBirthday(Objects.isNull(e.getBirthday()) ? this.getBirthdayByMemCode(memCode) : null);
            e.setAge(Objects.isNull(e.getAge()) ? this.getAgeByMemCode(memCode) : null);
            iDevelopStepLogService.updateById(e);
        });

        List<DevelopStepLogAll> allList = iDevelopStepLogAllService.list(new QueryWrapper<DevelopStepLogAll>().lambda().select()
                .apply("delete_time IS NULL AND d08_code = '3' AND (d09_code IS NULL OR d07_code IS NULL OR d06_code IS NULL OR idcard IS NULL OR sex_code IS NULL OR age is null or birthday is null)"));
        allList.parallelStream().forEach(e -> {
            String memCode = e.getMemCode();
            e.setD09Code(StrUtil.isEmpty(e.getD09Code()) ? this.getD09CodeByMemCode(memCode) : null);
            e.setD07Code(StrUtil.isEmpty(e.getD07Code()) ? this.getD07CodeByMemCode(memCode) : null);
            e.setD06Code(StrUtil.isEmpty(e.getD06Code()) ? this.getD06CodeByMemCode(memCode) : null);
            e.setIdcard(StrUtil.isEmpty(e.getIdcard()) ? this.getIdCardByMemCode(memCode) : null);
            e.setSexCode(StrUtil.isEmpty(e.getSexCode()) ? this.getSexCodeByMemCode(memCode) : null);
            e.setBirthday(Objects.isNull(e.getBirthday()) ? this.getBirthdayByMemCode(memCode) : null);
            e.setAge(Objects.isNull(e.getAge()) ? this.getAgeByMemCode(memCode) : null);
            iDevelopStepLogAllService.updateById(e);
        });
        System.out.println("------>>> " + allList.size());
    }

    private String getD09CodeByMemCode(String memCode) {
        Mem mem = iMemService.getOne(new QueryWrapper<Mem>().lambda().select().eq(Mem::getCode, memCode).isNotNull(Mem::getD09Code).last("limit 1"));
        if (Objects.isNull(mem)) {
            MemDevelop memDevelop = iMemDevelopService.getOne(new QueryWrapper<MemDevelop>().lambda().select().eq(MemDevelop::getCode, memCode).isNotNull(MemDevelop::getD09Code).last("limit 1"));
            return Objects.nonNull(memDevelop) ? memDevelop.getD09Code() : null;
        }
        return mem.getD09Code();
    }

    private String getD07CodeByMemCode(String memCode) {
        Mem mem = iMemService.getOne(new QueryWrapper<Mem>().lambda().select().eq(Mem::getCode, memCode).isNotNull(Mem::getD07Code).last("limit 1"));
        if (Objects.isNull(mem)) {
            MemDevelop memDevelop = iMemDevelopService.getOne(new QueryWrapper<MemDevelop>().lambda().select().eq(MemDevelop::getCode, memCode).isNotNull(MemDevelop::getD07Code).last("limit 1"));
            return Objects.nonNull(memDevelop) ? memDevelop.getD07Code() : null;
        }
        return mem.getD07Code();
    }

    private String getD06CodeByMemCode(String memCode) {
        Mem mem = iMemService.getOne(new QueryWrapper<Mem>().lambda().select().eq(Mem::getCode, memCode).isNotNull(Mem::getD06Code).last("limit 1"));
        if (Objects.isNull(mem)) {
            MemDevelop memDevelop = iMemDevelopService.getOne(new QueryWrapper<MemDevelop>().lambda().select().eq(MemDevelop::getCode, memCode).isNotNull(MemDevelop::getD06Code).last("limit 1"));
            return Objects.nonNull(memDevelop) ? memDevelop.getD06Code() : null;
        }
        return mem.getD06Code();
    }

    private String getIdCardByMemCode(String memCode) {
        Mem mem = iMemService.getOne(new QueryWrapper<Mem>().lambda().select().eq(Mem::getCode, memCode).isNotNull(Mem::getIdcard).last("limit 1"));
        if (Objects.isNull(mem)) {
            MemDevelop memDevelop = iMemDevelopService.getOne(new QueryWrapper<MemDevelop>().lambda().select().eq(MemDevelop::getCode, memCode).isNotNull(MemDevelop::getIdcard).last("limit 1"));
            return Objects.nonNull(memDevelop) ? memDevelop.getIdcard() : null;
        }
        return mem.getIdcard();
    }

    private String getSexCodeByMemCode(String memCode) {
        Mem mem = iMemService.getOne(new QueryWrapper<Mem>().lambda().select().eq(Mem::getCode, memCode).isNotNull(Mem::getSexCode).last("limit 1"));
        if (Objects.isNull(mem)) {
            MemDevelop memDevelop = iMemDevelopService.getOne(new QueryWrapper<MemDevelop>().lambda().select().eq(MemDevelop::getCode, memCode).isNotNull(MemDevelop::getSexCode).last("limit 1"));
            return Objects.nonNull(memDevelop) ? memDevelop.getSexCode() : null;
        }
        return mem.getSexCode();
    }

    private Integer getAgeByMemCode(String memCode) {
        Mem mem = iMemService.getOne(new QueryWrapper<Mem>().lambda().select().eq(Mem::getCode, memCode).isNotNull(Mem::getBirthday).last("limit 1"));
        if (Objects.isNull(mem)) {
            MemDevelop memDevelop = iMemDevelopService.getOne(new QueryWrapper<MemDevelop>().lambda().select().eq(MemDevelop::getCode, memCode).isNotNull(MemDevelop::getBirthday).last("limit 1"));
            return Objects.nonNull(memDevelop) && Objects.nonNull(memDevelop.getBirthday()) ? DateUtil.ageOfNow(memDevelop.getBirthday()) : null;
        }
        return Objects.nonNull(mem.getBirthday()) ? DateUtil.ageOfNow(mem.getBirthday()) : null;
    }

    private Date getBirthdayByMemCode(String memCode) {
        Mem mem = iMemService.getOne(new QueryWrapper<Mem>().lambda().select().eq(Mem::getCode, memCode).isNotNull(Mem::getBirthday).last("limit 1"));
        if (Objects.isNull(mem)) {
            MemDevelop memDevelop = iMemDevelopService.getOne(new QueryWrapper<MemDevelop>().lambda().select().eq(MemDevelop::getCode, memCode).isNotNull(MemDevelop::getBirthday).last("limit 1"));
            return Objects.nonNull(memDevelop) ? memDevelop.getBirthday() : null;
        }
        return mem.getBirthday();
    }

}
