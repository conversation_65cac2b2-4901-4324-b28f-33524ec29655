package com.zenith.front.core.service.transfer;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.zenith.front.api.transfer.ITransferEffectMemsService;
import com.zenith.front.core.kit.ChartUtil;
import com.zenith.front.core.kit.CollectionUtil;
import com.zenith.front.model.custom.Record;
import com.zenith.front.dao.mapper.org.OrgMapper;
import com.zenith.front.dao.mapper.transfer.TransferEffectMemsMapper;
import com.zenith.front.dao.mapper.transfer.TransferRecordMapper;
import com.zenith.front.model.dto.ChartDataDTO;
import com.zenith.front.model.message.OutMessage;
import com.zenith.front.model.message.Status;
import com.zenith.front.model.bean.Org;
import com.zenith.front.model.bean.TransferEffectMems;
import com.zenith.front.model.vo.OptionVO;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * <p>
 *  服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-07-10
 */
@Service
public class TransferEffectMemsServiceImpl extends ServiceImpl<TransferEffectMemsMapper, TransferEffectMems> implements ITransferEffectMemsService {


    @Resource
    private OrgMapper orgMapper;

    @Resource
    private TransferRecordMapper recordMapper;
    @Override
    public OutMessage getTransferTotal(ChartDataDTO data) {
        List<String> list = new ArrayList<String>() {
            private static final long serialVersionUID = 521243073345203798L;

            {
            add("转接总人数");
            add("系统内转接");
            add("整建制转接");
        }};
        Org org = getOrg(data);
        OptionVO vo = recordMapper.getTransfer(org.getCode());
        if (vo == null) {
            return new OutMessage<>(Status.SUCCESS, ChartUtil.getListWithNoData("transferType", list));
        }
        Record record = new Record();
        record.set("转接总人数", vo.getOne());
        record.set("市内转接", vo.getTwo());
        record.set("整建制转接", vo.getThree());
        Map<String, Object> map = record.getColumns();
        List<Record> recordListData = CollectionUtil.mapToListRecord(map, "transferType", "count");
        return new OutMessage<>(Status.SUCCESS, recordListData);
    }

    /**
     * 转出总人数
     * @param data
     * @return
     */
    @Override
    public OutMessage getTransferOutTotal(ChartDataDTO data) {

        List<String> list = new ArrayList<String>() {
            private static final long serialVersionUID = 4487969082652715855L;

            {
            add("转出总人数");
            add("系统内转接");
            add("系统外转接");
        }};

        Org org = getOrg(data);
        OptionVO vo = recordMapper.getTransferOut(org.getCode());
        if (vo == null) {
            return new OutMessage<>(Status.SUCCESS, ChartUtil.getListWithNoData("transferType", list));
        }
        Record record = new Record();
        record.set("系统内转接", vo.getOne());
        record.set("系统外转接", vo.getTwo());
        Map<String, Object> map = record.getColumns();
        List<Record> recordListData = CollectionUtil.mapToListRecord(map, "transferType", "count");
        Record record1 = new Record().set("transferType", "转出总人数").set("count", ChartUtil.getTotalFromList(recordListData, "count"));
        recordListData.add(record1);
        return new OutMessage<>(Status.SUCCESS, recordListData);
    }

    /**
     * 转入总人数
     * @param data
     * @return
     */
    @Override
    public OutMessage getTransferInTotal(ChartDataDTO data) {
        List<String> list = new ArrayList<String>() {
            private static final long serialVersionUID = 1232582366061200072L;

            {
            add("转入总人数");
            add("系统内转接");
            add("系统外转接");
        }};

        Org org = getOrg(data);
        OptionVO vo = recordMapper.getTransferIn(org.getCode());
        if (vo == null) {
            return new OutMessage<>(Status.SUCCESS, ChartUtil.getListWithNoData("transferType", list));
        }
        Record record = new Record();
        record.set("系统内转接", vo.getOne());
        record.set("系统外转接", vo.getTwo());
        Map<String, Object> map = record.getColumns();
        List<Record> recordListData = CollectionUtil.mapToListRecord(map, "transferType", "count");
        Record record1 = new Record().set("transferType", "转入总人数").set("count", ChartUtil.getTotalFromList(recordListData, "count"));
        recordListData.add(record1);
        return new OutMessage<>(Status.SUCCESS, recordListData);
    }

    /**
     * 整建制转接次数
     * @param data
     * @return
     */
    @Override
    public OutMessage getTransferOrgTotal(ChartDataDTO data) {
        List<String> list = new ArrayList<String>() {
            private static final long serialVersionUID = -7064552806285795268L;

            {
            add("整建制转接次数");
            add("系统内整建制次数");
            add("系统外整建制次数");
        }};

        Org org = getOrg(data);
        OptionVO vo = recordMapper.getTransferOrg(org.getCode());
        if (vo == null) {
            return new OutMessage<>(Status.SUCCESS, ChartUtil.getListWithNoData("transferType", list));
        }
        Record record = new Record();
        record.set("系统内整建制次数", vo.getOne());
        record.set("系统外整建制次数", vo.getTwo());
        Map<String, Object> map = record.getColumns();
        List<Record> recordListData = CollectionUtil.mapToListRecord(map, "transferType", "count");
        Record record1 = new Record().set("transferType", "整建制转接次数").set("count", ChartUtil.getTotalFromList(recordListData, "count"));
        recordListData.add(record1);
        return new OutMessage<>(Status.SUCCESS, recordListData);
    }

    /**
     * 整建制转接人数
     * @param data
     * @return
     */
    @Override
    public OutMessage getTransferOrgMemTotal(ChartDataDTO data) {
        List<String> list = new ArrayList<String>() {
            private static final long serialVersionUID = -6704688385048915444L;

            {
            add("整建制转接人数");
            add("系统内整建制人数");
            add("系统外整建制人数");
        }};

        Org org = getOrg(data);
        OptionVO vo = recordMapper.getTransferOrgMem(org.getCode());
        if (vo == null) {
            return new OutMessage<>(Status.SUCCESS, ChartUtil.getListWithNoData("transferType", list));
        }
        Record record = new Record();
        record.set("系统内整建制人数", vo.getOne());
        record.set("系统外整建制人数", vo.getTwo());
        Map<String, Object> map = record.getColumns();
        List<Record> recordListData = CollectionUtil.mapToListRecord(map, "transferType", "count");
        Record record1 = new Record().set("transferType", "整建制转接人数").set("count", ChartUtil.getTotalFromList(recordListData, "count"));
        recordListData.add(record1);
        return new OutMessage<>(Status.SUCCESS, recordListData);
    }

    private Org getOrg(ChartDataDTO data) {
        LambdaQueryWrapper<Org> wrapper = new QueryWrapper<Org>().lambda().select(Org::getCode)
                .eq(Org::getOrgCode, data.getOrgCode()).isNull(Org::getDeleteTime);
        return orgMapper.selectOne(wrapper);
    }
}
