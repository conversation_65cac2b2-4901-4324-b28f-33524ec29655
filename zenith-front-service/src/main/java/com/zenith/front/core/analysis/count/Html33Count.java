package com.zenith.front.core.analysis.count;

import cn.hutool.core.util.StrUtil;
import com.b1809.analysis.entity.PeggingPara;
import com.b1809.analysis.result.ReportResult;
import com.b1809.analysis.table.helper.TableHelper;
import com.b1809.analysis.table.helper.factory.TableHelperFactory;
import com.jfinal.plugin.activerecord.Page;
import com.zenith.front.core.analysis.ext.condition.MemAllCondition;
import com.zenith.front.core.analysis.ext.condition.OrgAllCondition;
import org.jooq.Condition;

import java.util.HashMap;
import java.util.Map;

import static org.jooq.impl.DSL.noCondition;

/**
 * 医疗卫生机构事业法人单位建立党的基层组织情况
 */
public class Html33Count {

    public Map<String, Object> getCheckHtml30(PeggingPara data) {
        MemAllCondition memAllCondition = new MemAllCondition();
        Map<String, Object> resultMap = new HashMap<>();
        TableHelper tableHelper = TableHelperFactory.getHelper();
        String rowIndex = data.getRowIndex();
        if (StrUtil.equals(rowIndex, "7")) {
            Condition condition = this.getOrg30Condition(data.getOrgCode(), data.getOrgLevelCode()).and(this.getOrgCheck30Condition(data.getColIndex()));
            OrgAllCondition orgAllCond = new OrgAllCondition();
            return Html53Count.getReportPageResult(data, orgAllCond.getTableName(), condition, orgAllCond.getLevelCodeField());
        }
        Page<Map<String, Object>> page = tableHelper.findPage(data.getPageNum(), data.getPageSize(), memAllCondition.getTableName(), new Html33Count().getMemCheckListCondition(data.getOrgCode(), data.getOrgLevelCode(), data.getColIndex(), data.getRowIndex()),
                data.getIncludeFieldList(), data.getOrderByFieldList());
        return ReportResult.toResult(resultMap, page, memAllCondition.getTableName());
    }

    public Condition getOrg30Condition(String orgCode, String orgLevelCode) {
        return new OrgAllCondition().create(orgCode, orgLevelCode).and("delete_time is null and (is_dissolve is null or is_dissolve !=1) and d01_code in('632','634','932') and d04_code like '34%' and has_unit_own_level='1'");
    }

    public Condition getOrgCheck30Condition(String colIndex) {
        Condition condition = noCondition();
        if (StrUtil.equals(colIndex, "1")) {
            condition = condition.and("d04_code like '341%'");
        } else if (StrUtil.equals(colIndex, "2")) {
            condition = condition.and("d04_code like '341%' and d81_code ='2'");
        } else if (StrUtil.equals(colIndex, "3")) {
            condition = condition.and("d04_code like '341%' and (d35_code like '2%' or d35_code like '3%')");
        } else if (StrUtil.equals(colIndex, "4")) {
            condition = condition.and("d04_code like '341%' and (d35_code like '4%' or d35_code like '5%')");
        } else if (StrUtil.equals(colIndex, "5")) {
            condition = condition.and("d04_code like '341%' and d35_code in ('7','71','72','73','74','75','76','8','81','82','83') ");
        } else if (StrUtil.equals(colIndex, "6")) {
            condition = condition.and("d04_code like '341%' and d111_code = '1'");
        } else if (StrUtil.equals(colIndex, "7")) {
            condition = condition.and("d04_code like '341%' and d111_code = '2'");
        } else if (StrUtil.equals(colIndex, "8")) {
            condition = condition.and("d04_code like '341%' and d111_code = '3'");
        } else if (StrUtil.equals(colIndex, "9")) {
            condition = condition.and("d04_code like '342%' ");
        } else if (StrUtil.equals(colIndex, "10")) {
            condition = condition.and("d04_code like '3421%' ");
        } else if (StrUtil.equals(colIndex, "11")) {
            condition = condition.and("d04_code like '3421%' and d81_code='1'");
        } else if (StrUtil.equals(colIndex, "12")) {
            condition = condition.and("d04_code like '3422%' ");
        } else if (StrUtil.equals(colIndex, "13")) {
            condition = condition.and("d04_code like '3422%' and d81_code='1'");
        } else if (StrUtil.equals(colIndex, "14")) {
            condition = condition.and(" d04_code like '343%'");
        } else if (StrUtil.equals(colIndex, "15")) {
            condition = condition.and(" d04_code like '3431%'");
        } else if (StrUtil.equals(colIndex, "16")) {
            condition = condition.and(" d04_code like '3432%'");
        }
        return condition;
    }

    public Condition getMemListCondition(String orgCode, String orgLevelCode) {
        return noCondition().and("delete_time is null and d08_code in ('1','2') and (is_transfer is null or is_transfer!=1) and d04_code like '34%' and d09_code like '0%'").and(new MemAllCondition().create(orgCode, orgLevelCode));
    }

    public Condition getMemCheckListCondition(String orgCode, String orgLevelCode, String cell, String rowIndex) {
        if ("12".equals(rowIndex)) {
            if ("1".equals(cell)) {
                return noCondition().and("delete_time is null and d08_code in ('1','2') and (is_transfer is null or is_transfer!=1) and d09_code like '0%' and d04_code like '341%'").and(new MemAllCondition().create(orgCode, orgLevelCode));
            }
            if ("2".equals(cell)) {
                return noCondition().and("delete_time is null and d08_code in ('1','2') and (is_transfer is null or is_transfer!=1) and d09_code like '0%' and d04_code like '341%' and d81_code='2'").and(new MemAllCondition().create(orgCode, orgLevelCode));
            }
            if ("3".equals(cell)) {
                return noCondition().and("delete_time is null and d08_code in ('1','2') and (is_transfer is null or is_transfer!=1) and d09_code like '0%' and d04_code like '341%' and (d35_code like '2%' or d35_code like '3%')").and(new MemAllCondition().create(orgCode, orgLevelCode));
            }
            if ("4".equals(cell)) {
                return noCondition().and("delete_time is null and d08_code in ('1','2') and (is_transfer is null or is_transfer!=1) and d09_code like '0%' and d04_code like '341%' and d35_code like '4%'").and(new MemAllCondition().create(orgCode, orgLevelCode));
            }
            if ("5".equals(cell)) {
                return noCondition().and("delete_time is null and d08_code in ('1','2') and (is_transfer is null or is_transfer!=1) and d09_code like '0%' and d04_code like '341%' and d35_code in ('7','71','72','73','74','75','76','8','81','82','83')").and(new MemAllCondition().create(orgCode, orgLevelCode));
            }
            if ("6".equals(cell)) {
                return noCondition().and("delete_time is null and d08_code in ('1','2') and (is_transfer is null or is_transfer!=1) and d09_code like '0%' and d04_code like '341%' and d111_code='1' and d35_code like '1%'").and(new MemAllCondition().create(orgCode, orgLevelCode));
            }
            if ("7".equals(cell)) {
                return noCondition().and("delete_time is null and d08_code in ('1','2') and (is_transfer is null or is_transfer!=1) and d09_code like '0%' and d04_code like '341%' and d111_code='2' and d35_code like '1%'").and(new MemAllCondition().create(orgCode, orgLevelCode));
            }
            if ("8".equals(cell)) {
                return noCondition().and("delete_time is null and d08_code in ('1','2') and (is_transfer is null or is_transfer!=1) and d09_code like '0%' and d04_code like '341%' and d111_code='3' and d35_code like '1%'").and(new MemAllCondition().create(orgCode, orgLevelCode));
            }
            if ("9".equals(cell)) {
                return noCondition().and("delete_time is null and d08_code in ('1','2') and (is_transfer is null or is_transfer!=1) and d09_code like '0%' and d04_code like '342%'").and(new MemAllCondition().create(orgCode, orgLevelCode));
            }
            if ("10".equals(cell)) {
                return noCondition().and("delete_time is null and d08_code in ('1','2') and (is_transfer is null or is_transfer!=1) and d09_code like '0%' and d04_code='3421'").and(new MemAllCondition().create(orgCode, orgLevelCode));
            }
            if ("11".equals(cell)) {
                return noCondition().and("delete_time is null and d08_code in ('1','2') and (is_transfer is null or is_transfer!=1) and d09_code like '0%' and d04_code='3421' and d81_code='1'").and(new MemAllCondition().create(orgCode, orgLevelCode));
            }
            if ("12".equals(cell)) {
                return noCondition().and("delete_time is null and d08_code in ('1','2') and (is_transfer is null or is_transfer!=1) and d09_code like '0%' and d04_code='3422'").and(new MemAllCondition().create(orgCode, orgLevelCode));
            }
            if ("13".equals(cell)) {
                return noCondition().and("delete_time is null and d08_code in ('1','2') and (is_transfer is null or is_transfer!=1) and d09_code like '0%' and d04_code='3422' and d81_code='1'").and(new MemAllCondition().create(orgCode, orgLevelCode));
            }
            if ("14".equals(cell)) {
                return noCondition().and("delete_time is null and d08_code in ('1','2') and (is_transfer is null or is_transfer!=1) and d09_code like '0%' and d04_code like '343%'").and(new MemAllCondition().create(orgCode, orgLevelCode));
            }
            if ("15".equals(cell)) {
                return noCondition().and("delete_time is null and d08_code in ('1','2') and (is_transfer is null or is_transfer!=1) and d09_code like '0%' and d04_code like '3431%'").and(new MemAllCondition().create(orgCode, orgLevelCode));
            }
            if ("16".equals(cell)) {
                return noCondition().and("delete_time is null and d08_code in ('1','2') and (is_transfer is null or is_transfer!=1) and d09_code like '0%' and d04_code like '3432%'").and(new MemAllCondition().create(orgCode, orgLevelCode));
            }
        }
        if ("14".equals(rowIndex)) {
            if ("1".equals(cell)) {
                return noCondition().and("delete_time is null and d08_code in ('1','2') and (is_transfer is null or is_transfer!=1) and d09_code like '013%' and d04_code like '341%'").and(new MemAllCondition().create(orgCode, orgLevelCode));
            }
            if ("2".equals(cell)) {
                return noCondition().and("delete_time is null and d08_code in ('1','2') and (is_transfer is null or is_transfer!=1) and d09_code like '013%' and d04_code like '341%' and d81_code='2'").and(new MemAllCondition().create(orgCode, orgLevelCode));
            }
            if ("3".equals(cell)) {
                return noCondition().and("delete_time is null and d08_code in ('1','2') and (is_transfer is null or is_transfer!=1) and d09_code like '013%' and d04_code like '341%' and (d35_code like '2%' or d35_code like '3%')").and(new MemAllCondition().create(orgCode, orgLevelCode));
            }
            if ("4".equals(cell)) {
                return noCondition().and("delete_time is null and d08_code in ('1','2') and (is_transfer is null or is_transfer!=1) and d09_code like '013%' and d04_code like '341%' and d35_code like '4%'").and(new MemAllCondition().create(orgCode, orgLevelCode));
            }
            if ("5".equals(cell)) {
                return noCondition().and("delete_time is null and d08_code in ('1','2') and (is_transfer is null or is_transfer!=1) and d09_code like '013%' and d04_code like '341%' and d35_code in ('7','71','72','73','74','75','76','8','81','82','83') ").and(new MemAllCondition().create(orgCode, orgLevelCode));
            }
            if ("6".equals(cell)) {
                return noCondition().and("delete_time is null and d08_code in ('1','2') and (is_transfer is null or is_transfer!=1) and d09_code like '013%' and d04_code like '341%' and d111_code='1' and d35_code like '1%'").and(new MemAllCondition().create(orgCode, orgLevelCode));
            }
            if ("7".equals(cell)) {
                return noCondition().and("delete_time is null and d08_code in ('1','2') and (is_transfer is null or is_transfer!=1) and d09_code like '013%' and d04_code like '341%' and d111_code='2' and d35_code like '1%'").and(new MemAllCondition().create(orgCode, orgLevelCode));
            }
            if ("8".equals(cell)) {
                return noCondition().and("delete_time is null and d08_code in ('1','2') and (is_transfer is null or is_transfer!=1) and d09_code like '013%' and d04_code like '341%' and d111_code='3' and d35_code like '1%'").and(new MemAllCondition().create(orgCode, orgLevelCode));
            }
            if ("9".equals(cell)) {
                return noCondition().and("delete_time is null and d08_code in ('1','2') and (is_transfer is null or is_transfer!=1) and d09_code like '013%' and d04_code like '342%'").and(new MemAllCondition().create(orgCode, orgLevelCode));
            }
            if ("10".equals(cell)) {
                return noCondition().and("delete_time is null and d08_code in ('1','2') and (is_transfer is null or is_transfer!=1) and d09_code like '013%' and d04_code='3421'").and(new MemAllCondition().create(orgCode, orgLevelCode));
            }
            if ("11".equals(cell)) {
                return noCondition().and("delete_time is null and d08_code in ('1','2') and (is_transfer is null or is_transfer!=1) and d09_code like '013%' and d04_code='3421' and d81_code='1'").and(new MemAllCondition().create(orgCode, orgLevelCode));
            }
            if ("12".equals(cell)) {
                return noCondition().and("delete_time is null and d08_code in ('1','2') and (is_transfer is null or is_transfer!=1) and d09_code like '013%' and d04_code='3422'").and(new MemAllCondition().create(orgCode, orgLevelCode));
            }
            if ("13".equals(cell)) {
                return noCondition().and("delete_time is null and d08_code in ('1','2') and (is_transfer is null or is_transfer!=1) and d09_code like '013%' and d04_code='3422' and d81_code='1'").and(new MemAllCondition().create(orgCode, orgLevelCode));
            }
            if ("14".equals(cell)) {
                return noCondition().and("delete_time is null and d08_code in ('1','2') and (is_transfer is null or is_transfer!=1) and d09_code like '013%' and d04_code like '343%'").and(new MemAllCondition().create(orgCode, orgLevelCode));
            }
            if ("15".equals(cell)) {
                return noCondition().and("delete_time is null and d08_code in ('1','2') and (is_transfer is null or is_transfer!=1) and d09_code like '013%' and d04_code like '3431%'").and(new MemAllCondition().create(orgCode, orgLevelCode));
            }
            if ("16".equals(cell)) {
                return noCondition().and("delete_time is null and d08_code in ('1','2') and (is_transfer is null or is_transfer!=1) and d09_code like '013%' and d04_code like '3432%'").and(new MemAllCondition().create(orgCode, orgLevelCode));
            }
        }
        return noCondition();
    }
}
