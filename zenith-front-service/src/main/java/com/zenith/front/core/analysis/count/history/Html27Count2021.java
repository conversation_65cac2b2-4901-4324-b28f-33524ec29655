package com.zenith.front.core.analysis.count.history;

import com.b1809.analysis.entity.PeggingPara;
import com.b1809.analysis.result.ReportResult;
import com.b1809.analysis.table.helper.TableHelper;
import com.b1809.analysis.table.helper.factory.TableHelperFactory;
import com.jfinal.plugin.activerecord.Page;
import com.zenith.front.core.analysis.ext.condition.year2021.MemAllCondition2021;
import org.jooq.Condition;

import java.util.HashMap;
import java.util.Map;

import static org.jooq.impl.DSL.noCondition;

/**
 * <AUTHOR>
 * @Date 2022/1/19 20:39
 * @Version 1.0
 */
public class Html27Count2021 {

    public Map<String, Object> getCheckHtml27(PeggingPara data) {
        Map<String, Object> resultMap = new HashMap<>();
        TableHelper tableHelper = TableHelperFactory.getHelper();
        MemAllCondition2021 memAll2021Condition = new MemAllCondition2021();
        Condition condition = noCondition().and(new Html27CountHistory().getMemListCondition(data.getOrgCode(), data.getOrgLevelCode())
                .and(new Html27CountHistory().getMemCheckListCondition(data.getColIndex())).toString().replace("ccp_mem_all", memAll2021Condition.getTableName()));
        Page<Map<String, Object>> page = tableHelper.findPage(data.getPageNum(), data.getPageSize(), memAll2021Condition.getTableName(), condition,
                data.getIncludeFieldList(), data.getOrderByFieldList());
        return ReportResult.toResult(resultMap, page, memAll2021Condition.getTableName());
    }


}
