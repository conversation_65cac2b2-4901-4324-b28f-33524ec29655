package com.zenith.front.core.constant;

import java.util.Arrays;
import java.util.List;

public enum InquireReportTypeEnum {

    N("N","1","","",""),
    N1("N","9","16","",""),
    O("O","5","","",""),
    O1("O","9","9","",""),
    A("A","1","1,3,5,7,8,9,11,13,15,16,17,18,19,20,21","",""),
    A1("A","2","2,4,6,10,12,14","",""),
    A2("A","3","22,23,24,25,26","",""),
    A3("A","4","","","");





    public String reportCode;

    /**
     * 1 未纳入流入地管理 2 已纳入流入地管理  3流出被退回  4流出历史 5 未纳入支部管理 6 已纳入支部管理 7县级流入库 8流入历史 9流动党组织
     *
     */
    public String type;

    /**
     * 第几列
     */
    public String column;

    /**
     * 方法名
     */
    public String method;

    public String strSql;

    InquireReportTypeEnum(String reportCode, String type, String column, String method,String strSql) {
        this.reportCode = reportCode;
        this.type = type;
        this.column = column;
        this.method = method;
        this.strSql = strSql;
    }


    public static InquireReportTypeEnum getMethodByReportCode(String reportCode,String column) {
        for (InquireReportTypeEnum codeEnum : InquireReportTypeEnum.values()) {
            if (codeEnum.reportCode.equals(reportCode)) {
                List<String> list = Arrays.asList(codeEnum.column.split(","));
                if (list.contains(column)) {
                    return codeEnum;
                }
            }
        }
        return null;
    }
}
