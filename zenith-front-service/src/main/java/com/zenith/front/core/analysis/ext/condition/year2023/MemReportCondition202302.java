package com.zenith.front.core.analysis.ext.condition.year2023;

import com.b1809.analysis.extend.GenSqlConditionFuc;
import org.jooq.Condition;
import org.jooq.Field;

import static org.jooq.impl.DSL.field;
import static org.jooq.impl.DSL.name;

public class MemReportCondition202302 implements GenSqlConditionFuc {
    @Override
    public String getTableName() {
        return "ccp_mem_report_202302";
    }

    @Override
    public Condition create(String orgCode, String orgLevelCode) {
        return field(name( "mem_org_code"), String.class).like(orgLevelCode + "%");
    }

    @Override
    public Field<?> getLevelCodeField() {
        return field(name("mem_org_code"), String.class);
    }
}
