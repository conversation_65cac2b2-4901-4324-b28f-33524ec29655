package com.zenith.front.core.service.org;

import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.zenith.front.api.org.IOrgGroupMemberService;
import com.zenith.front.api.org.IOrgGroupService;
import com.zenith.front.common.constant.CommonConstant;
import com.zenith.front.common.kit.StrKit;
import com.zenith.front.core.cache.CacheUtils;
import com.zenith.front.dao.mapper.org.OrgGroupMemberMapper;
import com.zenith.front.model.dto.OrgGroupMemberDTO;
import com.zenith.front.model.message.OutMessage;
import com.zenith.front.model.message.Status;
import com.zenith.front.model.bean.OrgGroup;
import com.zenith.front.model.bean.OrgGroupMember;
import com.zenith.front.model.vo.OrgGroupMemberCodeVo;
import com.zenith.front.model.vo.OrgGroupMemberVO;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import static com.zenith.front.model.constant.UserConstant.USER_CONTEXT;

/**
 * <p>
 * 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-07-10
 */
@Service
public class OrgGroupMemberServiceImpl extends ServiceImpl<OrgGroupMemberMapper, OrgGroupMember> implements IOrgGroupMemberService {
    @Resource
    private IOrgGroupMemberService orgGroupMemberDao;
    @Resource
    private OrgGroupMemberMapper orgGroupMemberMapper;
    @Resource
    private IOrgGroupService orgGroupDao;

    @Override
    public Page<OrgGroupMember> findeMemByGroup(List<String> groupList, Integer pageNum, Integer pageSize) {
        Page<OrgGroupMember> page = new Page<>(pageNum, pageSize);
        LambdaQueryWrapper<OrgGroupMember> lambdaQueryWrapper = new LambdaQueryWrapper<OrgGroupMember>()
                .in(OrgGroupMember::getGroupCode, groupList)
                .isNull(OrgGroupMember::getDeleteTime);
        page(page, lambdaQueryWrapper);
        return page;
    }

    @Override
    public List<OrgGroupMember> findByGroupCode(String code) {
        return orgGroupMemberMapper.selectList(new LambdaQueryWrapper<OrgGroupMember>().eq(OrgGroupMember::getGroupCode, code).isNull(OrgGroupMember::getDeleteTime));
    }

    @Override
    public OrgGroupMember findByCode(String code) {
        return getOne(new LambdaQueryWrapper<OrgGroupMember>().eq(OrgGroupMember::getCode, code).isNull(OrgGroupMember::getDeleteTime));
    }

    /**
     * 获取党小组成员列表
     *
     * @param pageNum
     * @param pageSize
     * @param groupCode
     * @return
     */
    @Override
    public OutMessage getList(int pageNum, int pageSize, String groupCode) {
        OrgGroup orgGroup = orgGroupDao.findByCode(groupCode);
        if (ObjectUtil.isNull(orgGroup)) {
            return new OutMessage<>(Status.PARA_ERROR);
        }
        String groupOrgCode = orgGroup.getGroupOrgCode();
        if (!StrKit.isBlank(groupOrgCode) && !groupOrgCode.startsWith(USER_CONTEXT.get().getUserRolePermission().getOrgCode())) {
            return new OutMessage<>(Status.PERMISSION_DENIED);
        }

        Page<OrgGroupMember> page = orgGroupMemberDao.getBaseMapper().selectPage(new Page<>(pageNum, pageSize),
                new LambdaQueryWrapper<OrgGroupMember>().eq(OrgGroupMember::getGroupCode, groupCode).isNull(OrgGroupMember::getDeleteTime)
                        .orderByDesc(OrgGroupMember::getCreateTime).orderByDesc(OrgGroupMember::getId));
        // 返回数据
        Page<OrgGroupMemberVO> pageVO = new Page<>();
        List<OrgGroupMemberVO> orgGroupMemberList = new ArrayList<>();

        page.getRecords().forEach(orgGroupMember -> {
            OrgGroupMemberVO orgGroupMemberVO = new OrgGroupMemberVO();
            try {
                // 拷贝
                BeanUtils.copyProperties(orgGroupMember, orgGroupMemberVO);
                // 获取党员名称
                String memName = CacheUtils.getMemName(orgGroupMemberVO.getMemCode());
                orgGroupMemberVO.setMemName(memName);
                orgGroupMemberList.add(orgGroupMemberVO);
            } catch (Exception e) {
                e.printStackTrace();
            }
        });
        BeanUtils.copyProperties(page, pageVO, "list");
        pageVO.setRecords(orgGroupMemberList);
        return new OutMessage<>(Status.SUCCESS, pageVO);
    }

    /**
     * 新增党小组成员
     *
     * @param orgGroupMemberDTO
     * @return
     */
    @Override
    public OutMessage addGroupMem(OrgGroupMemberDTO orgGroupMemberDTO) {
        // 获取党小组标示
        String groupCode = orgGroupMemberDTO.getGroupCode();
        // 获取党小组
        OrgGroup orgGroup = orgGroupDao.findByCode(groupCode);
        if (ObjectUtil.isNull(orgGroup)) {
            return new OutMessage<>(Status.PARA_ERROR);
        }
        // 获取党员标示
        String memCode = orgGroupMemberDTO.getMemCode();
        // 验证党小组成员是否重复
        OrgGroupMemberCodeVo record = orgGroupMemberMapper.findByOrgCodeAndMemCode(orgGroup.getGroupOrgCode(), memCode);
        if (ObjectUtil.isNotNull(record)) {
            return new OutMessage<>(Status.ORG_GROUP_MEM_EXIST);
        }
        orgGroupMemberDTO.setCode(StrKit.getRandomUUID());
        orgGroupMemberDTO.setCreateTime(new Date());
        orgGroupMemberDTO.setUpdateTime(new Date());
        orgGroupMemberDTO.setUpdateAccount(USER_CONTEXT.get().getUser().getAccount());
        orgGroupMemberDTO.setIsHistory(CommonConstant.ZERO_INT);
        orgGroupMemberDTO.setTimestamp(new Date());

        OrgGroupMember orgGroupMember = orgGroupMemberDTO.toModel();
        orgGroupMember.setId(null);
        boolean flag = orgGroupMemberDao.save(orgGroupMember);
        return new OutMessage<>(flag ? Status.SUCCESS : Status.FAIL);
    }

    /**
     * 根据code查找党小组成员
     *
     * @param code
     * @return
     */
    @Override
    public OutMessage findByCodeM(String code) {
        OrgGroupMember orgGroupMember = orgGroupMemberDao.findByCode(code);
        if (ObjectUtil.isNull(orgGroupMember)) {
            return new OutMessage<>(Status.ORG_GROUP_MEM_NULL);
        }
        OrgGroupMemberVO orgGroupMemberVO = new OrgGroupMemberVO();
        BeanUtils.copyProperties(orgGroupMember, orgGroupMemberVO);
        // 获取党员名称
        String memName = CacheUtils.getMemName(orgGroupMemberVO.getMemCode());
        orgGroupMemberVO.setMemName(memName);
        return new OutMessage<>(Status.SUCCESS, orgGroupMemberVO);
    }

    /**
     * 修改党小组成员
     *
     * @param orgGroupMemberDTO
     * @return
     */
    @Override
    public OutMessage updateGroupMem(OrgGroupMemberDTO orgGroupMemberDTO) {
        String code = orgGroupMemberDTO.getCode();
        OrgGroupMember orgGroupMember = orgGroupMemberDao.findByCode(code);
        if (ObjectUtil.isNull(orgGroupMember)) {
            return new OutMessage<>(Status.ORG_GROUP_MEM_NULL);
        }
        orgGroupMemberDTO.setUpdateTime(new Date());
        orgGroupMemberDTO.setUpdateAccount(USER_CONTEXT.get().getUser().getAccount());
        orgGroupMemberDTO.setTimestamp(new Date());
        BeanUtils.copyProperties(orgGroupMemberDTO, orgGroupMember);
        boolean flag = updateById(orgGroupMember);
        return new OutMessage<>(flag ? Status.SUCCESS : Status.FAIL);
    }

    /**
     * 删除党小组成员
     *
     * @param code
     * @return
     */
    @Override
    public OutMessage delGroupMem(String code) {
        OrgGroupMember orgGroupMember = orgGroupMemberDao.findByCode(code);
        if (ObjectUtil.isNull(orgGroupMember)) {
            return new OutMessage<>(Status.ORG_GROUP_MEM_NULL);
        }
        orgGroupMember.setDeleteTime(new Date());
        orgGroupMember.setUpdateAccount(USER_CONTEXT.get().getUser().getAccount());
        orgGroupMember.setIsHistory(CommonConstant.ONE_INT);
        boolean update = updateById(orgGroupMember);
        return new OutMessage<>(update ? Status.SUCCESS : Status.FAIL);
    }


}
