package com.zenith.front.core.sync.filter;

import com.zenith.front.common.kit.StrKit;
import com.zenith.front.core.sync.AbstractCountFilter;

/**
 * 字符串比较过滤器
 *
 * <AUTHOR>
 * @date 2019/6/211:40 PM
 */
public class StrIsFilter extends AbstractCountFilter<String> {

    @Override
    public boolean check(String s) {
        return StrKit.equals(s, getValue());
    }

    @Override
    public String getName() {
        return "strIs";
    }

    @Override
    protected String convert(String value) {
        return value;
    }
}
