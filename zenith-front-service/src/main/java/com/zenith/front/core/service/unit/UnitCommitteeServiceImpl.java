package com.zenith.front.core.service.unit;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUnit;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.thread.ThreadUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.zenith.front.api.mem.IMemReportService;
import com.zenith.front.api.mem.IMemService;
import com.zenith.front.api.org.IOrgAllService;
import com.zenith.front.api.org.IOrgCommitteeElectService;
import com.zenith.front.api.org.IOrgCommitteeService;
import com.zenith.front.api.report.IStatisticsYearService;
import com.zenith.front.api.sync.ISyncMemService;
import com.zenith.front.api.unit.*;
import com.zenith.front.common.constant.CommonConstant;
import com.zenith.front.common.constant.DictConstant;
import com.zenith.front.common.kit.CodeUtil;
import com.zenith.front.common.kit.JackSonUtil;
import com.zenith.front.common.kit.ModelUtils;
import com.zenith.front.common.kit.StrKit;
import com.zenith.front.core.cache.CacheUtils;
import com.zenith.front.core.service.sync.SyncUnitCommittee;
import com.zenith.front.dao.mapper.mem.MemMapper;
import com.zenith.front.dao.mapper.org.OrgCommitteeElectMapper;
import com.zenith.front.dao.mapper.org.OrgCommitteeMapper;
import com.zenith.front.dao.mapper.unit.UnitCommitteeElectMapper;
import com.zenith.front.dao.mapper.unit.UnitCommitteeMapper;
import com.zenith.front.model.bean.*;
import com.zenith.front.model.constant.UserConstant;
import com.zenith.front.model.dto.LedgerDTO;
import com.zenith.front.model.dto.UnitCommitteeDTO;
import com.zenith.front.model.dto.UnitCommitteeElectDTO;
import com.zenith.front.model.message.OutMessage;
import com.zenith.front.model.message.Status;
import com.zenith.front.model.vo.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.Executor;
import java.util.stream.Collectors;

import static com.zenith.front.common.constant.CommonConstant.ONE;

/**
 * <p>
 * 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-07-10
 */
@Service
@Slf4j
public class UnitCommitteeServiceImpl extends ServiceImpl<UnitCommitteeMapper, UnitCommittee> implements IUnitCommitteeService {

    @Resource
    private IUnitService unitService;
    @Resource
    private IUnitAllService unitAllService;
    @Resource
    private UnitCommitteeMapper committeeMapper;
    @Resource
    private OrgCommitteeElectMapper electMapper;
    @Resource
    private IOrgCommitteeElectService iOrgCommitteeElectService;
    @Resource
    private UnitCommitteeElectMapper unitCommitteeElectMapper;
    @Resource
    private IUnitCommitteeService iUnitCommitteeService;
    @Resource
    private IUnitCommitteeElectService iUnitCommitteeElectService;
    @Resource
    private OrgCommitteeMapper orgCommitteeMapper;
    @Resource
    private IOrgCommitteeService iOrgCommitteeService;
    @Resource
    private ISyncMemService iSyncMemService;
    @Resource
    private IMemService iMemService;
    @Resource
    Executor mySimpleAsync;
    @Resource
    private SyncUnitCommittee syncUnitCommittee;
    @Resource
    private IMemReportService iMemReportService;
    @Resource
    private IStatisticsYearService iStatisticsYearService;
    @Resource
    private IUnitOrgLinkedService iUnitOrgLinkedService;
    @Resource
    private IOrgAllService orgAllService;
    @Resource
    private MemMapper memMapper;


    @Override
    public OutMessage getList(int pageNum, int pageSize, String unitCode, String electCode, String leave) {

        Unit unit = unitService.findByCode(unitCode);
        if (ObjectUtil.isNull(unit)) {
            return new OutMessage<>(Status.OBJEC_NOT_EXIST);
        }

        UnitAll unitAll = unitAllService.findByCode(unitCode);
        if (ObjectUtil.isNull(unitAll)) {
            return new OutMessage<>(Status.OBJEC_NOT_EXIST);
        }
        // 权限验证
        String unitOrgCode;
        Integer isCreateOrg = unit.getIsCreateOrg();
        if (CommonConstant.ZERO_INT == isCreateOrg) {
            // 未建立
            unitOrgCode = unitAll.getManageUnitOrgCode();
            if (StrKit.isBlank(unitOrgCode)) {
                return new OutMessage<>(1006, "党建指导组织不能为空", null);
            }
        } else {
            // 已建立
            unitOrgCode = unitAll.getMainUnitOrgCode();
        }

        if (StrKit.isBlank(unitOrgCode)) {
            return new OutMessage<>(Status.DATA_EXCEPTION);
        }

        /*if (!unitOrgCode.startsWith(UserConstant.USER_CONTEXT.get().getUserRolePermission().getOrgCode())) {
            return new OutMessage<>(Status.PERMISSION_DENIED);
        }*/
        Page<UnitCommittee> page = this.getPage(pageNum, pageSize, electCode, leave);

        // 本单位党员：获取党员的身份证、学历、政治面貌信息
        Map<String, Mem> memMap = new HashMap<>();
        if(CollUtil.isNotEmpty(page.getRecords())){
            List<String> memCodeList = page.getRecords().stream().filter(e -> Objects.equals(e.getMemTypeCode(), CommonConstant.ONE) && StrUtil.isNotEmpty(e.getMemCode()))
                    .map(UnitCommittee::getMemCode).collect(Collectors.toList());
            memMap = setUnitCommitteeMem(memCodeList);
        }
        final Map<String, Mem> finalMemMap = memMap;
        page.getRecords().forEach(unitCommittee -> {
            // 单位名称
            unitCommittee.setUnitName(CacheUtils.getUnitName(unitCommittee.getUnitCode()));
            if (ONE.equals(unitCommittee.getMemTypeCode())) {
                // 查找人员名称
                String memCode = unitCommittee.getMemCode();
                String memName = CacheUtils.getMemName(memCode);
                unitCommittee.setMemName(memName);
                // 本单位党员：获取党员的身份证、学历、政治面貌信息
                if(StrUtil.isNotEmpty(memCode) && finalMemMap.containsKey(memCode)){
                    Mem mem = finalMemMap.get(memCode);
                    unitCommittee.setBirthday(mem.getBirthday());
                    unitCommittee.setD89Code(mem.getD89Code());
                    unitCommittee.setD89Name(mem.getD89Name());
                    unitCommittee.setD07Code(mem.getD07Code());
                    unitCommittee.setD07Name(mem.getD07Name());
                    unitCommittee.setSexCode(mem.getSexCode());
                    unitCommittee.setSexName(mem.getSexName());
                }
            }
        });
        return new OutMessage<>(Status.SUCCESS, page);
    }


    @Override
    public List<UnitCommittee> findByElect(String electCode) {
        LambdaQueryWrapper<UnitCommittee> lambdaQueryWrapper = new LambdaQueryWrapper<UnitCommittee>()
                .isNull(UnitCommittee::getDeleteTime)
                .eq(UnitCommittee::getElectCode, electCode)
                .orderByDesc(UnitCommittee::getCreateTime)
                .orderByDesc(UnitCommittee::getId);
//                .select(UnitCommittee::getMemCode, UnitCommittee::getMemName, UnitCommittee::getCode, UnitCommittee::getD25Code, UnitCommittee::getD25Name
//                        , UnitCommittee::getStartDate, UnitCommittee::getEndDate, UnitCommittee::getUnitCode, UnitCommittee::getUnitName, UnitCommittee::getIsIncumbent, UnitCommittee::getMemIdcard, UnitCommittee::getMemTypeCode);
        lambdaQueryWrapper.isNull(UnitCommittee::getEndDate);
        return list(lambdaQueryWrapper);
    }

    @Override
    public List<UnitCommittee> findAllByElect(String electCode) {
        return list(new LambdaQueryWrapper<UnitCommittee>().eq(UnitCommittee::getElectCode, electCode).orderByAsc(UnitCommittee::getDeleteTime).orderByAsc(UnitCommittee::getEndDate));
    }


    /**
     * 获取单位班子成员列表
     *
     * @param pageNum
     * @param pageSize
     * @param electCode
     * @return
     */
    private Page<UnitCommittee> getPage(int pageNum, int pageSize, String electCode, String leave) {
        Page<UnitCommittee> page = new Page<>(pageNum, pageSize);
        LambdaQueryWrapper<UnitCommittee> lambdaQueryWrapper = new LambdaQueryWrapper<UnitCommittee>();
        //todo 列表分页获取，不需要身份证，减少身份证查询
        lambdaQueryWrapper.select(UnitCommittee.class,unitcommit->{
            if (unitcommit.getProperty().equals("memIdcard")){
                return false;
            }
            return true;
        });
        lambdaQueryWrapper.isNull(UnitCommittee::getDeleteTime)
                .eq(UnitCommittee::getElectCode, electCode)
                .orderByDesc(UnitCommittee::getCreateTime)
                .orderByDesc(UnitCommittee::getId);
        // 1 查询本届离任领导成员  0 查询本届在任领导成员
        if (CommonConstant.ONE.equals(leave)) {
            lambdaQueryWrapper.isNotNull(UnitCommittee::getEndDate);
        } else if (CommonConstant.ZERO.equals(leave)) {
            lambdaQueryWrapper.isNull(UnitCommittee::getEndDate);
        }
        page(page, lambdaQueryWrapper);
        return page;
    }


    @Override
    public OutMessage addUnitCommittee(UnitCommitteeDTO unitCommitteeDTO) {
        return this.addUnitCommittee(unitCommitteeDTO, false);
    }

    /**
     * 新增单位班子成员
     * @param unitCommitteeDTO
     * @param isCheck 是否只进行校验, 是的话在保存前返回
     * @return
     */
    @Override
    public OutMessage addUnitCommittee(UnitCommitteeDTO unitCommitteeDTO, Boolean isCheck) {
        OutMessage outMessage = this.checkData(unitCommitteeDTO);
        if (ObjectUtil.isNotNull(outMessage)) {
            return outMessage;
        }

        String unitCode = unitCommitteeDTO.getUnitCode();
        String memCode = unitCommitteeDTO.getMemCode();
        String electCode = unitCommitteeDTO.getElectCode();
        UnitCommittee unitCommittee = this.findByMemCode(unitCode, memCode, electCode);
        if (ObjectUtil.isNotNull(unitCommittee)) {
            // 单位班子成员已经存在
            return new OutMessage<>(Status.UNIT_COMM_IS_REPETITION);
        }

        unitCommitteeDTO.setCode(StrKit.getRandomUUID());
        unitCommitteeDTO.setEsId(CodeUtil.getEsId());
        unitCommitteeDTO.setCreateTime(new Date());

        unitCommittee = unitCommitteeDTO.toModel();
        unitCommittee.setId(null);
        // 处理党组织兼任情况
        this.processD26Code(unitCommittee);

        // 校验，在保存前就返回
        if(Objects.equals(isCheck, true)) {
            return new OutMessage(Status.SUCCESS);
        }

        boolean flag = save(unitCommittee);
        if (flag) {
            ThreadUtil.execAsync(() -> {
                iMemReportService.syncUnitCommittee(unitCode);
                iSyncMemService.syncMem(memCode, "3");
            });
        }
        return new OutMessage<>(flag ? Status.SUCCESS : Status.FAIL, unitCommittee);
    }

    /**
     * 处理兼职问题
     */
    private void processD26Code(UnitCommittee unitCommittee) {
        try {
            final String electCode = unitCommittee.getElectCode();
            final UnitCommitteeElect elect = this.getElect(electCode);
            if (Objects.isNull(elect)) {
                return;
            }
            boolean statisticalYear = iStatisticsYearService.isStatisticalYear(elect.getTenureStartDate(), elect.getTenureEndDate());
            if (!statisticalYear) {
                return;
            }
            // 是否本单位党员
            String memTypeCode = unitCommittee.getMemTypeCode();
            if (StrUtil.equals(memTypeCode, ONE)) {
                // 查询兼任
                String memCode = unitCommittee.getMemCode();
                CurrCommitteeVO currCommitteeVO = orgCommitteeMapper.findLastYearByMemCode(memCode);
                if (Objects.isNull(currCommitteeVO)) {
                    return;
                }
                statisticalYear = iStatisticsYearService.isStatisticalYear(currCommitteeVO.getTenureStartDate(), currCommitteeVO.getTenureEndDate());
                if (!statisticalYear) {
                    return;
                }
                final String d022Code = currCommitteeVO.getD022Code();
                if (StrUtil.equals(d022Code, "1")) {
                    // 党内书记
                    unitCommittee.setD26Code("1");
                    unitCommittee.setD26Name("同级党组织书记兼任");
                } else if (StrUtil.equals(d022Code, "2")) {
                    // 副书记
                    unitCommittee.setD26Code("2");
                    unitCommittee.setD26Name("同级党组织副书记兼任");
                } else if (StrUtil.equals(d022Code, "3")) {
                    // 委员（成员）
                    unitCommittee.setD26Code("3");
                    unitCommittee.setD26Name("同级党组织班子成员兼任");
                }
            }
        } catch (Exception e) {
            log.error("处理单位班子人员兼职情况异常", e);
        }
    }

    /**
     * 参数检验
     *
     * @param unitCommitteeDTO
     * @return
     */
    private OutMessage checkData(UnitCommitteeDTO unitCommitteeDTO) {
        Date startDate = unitCommitteeDTO.getStartDate();
        Date endDate = unitCommitteeDTO.getEndDate();
        if (ObjectUtil.isNotNull(endDate)) {
            long between = DateUtil.between(startDate, endDate, DateUnit.DAY, false);
            if (between <= 0) {
                return new OutMessage<>(Status.START_DATE_BEFORE_END);
            }
        }

        unitCommitteeDTO.setUpdateTime(new Date());
        unitCommitteeDTO.setUpdateAccount(UserConstant.USER_CONTEXT.get().getUser().getAccount());
        unitCommitteeDTO.setTimestamp(new Date());
        return null;
    }

    /**
     * 根据单位及人员查找
     *
     * @param unitCode
     * @param memCode
     * @return
     */
    private UnitCommittee findByMemCode(String unitCode, String memCode, String electCode) {
        LambdaQueryWrapper<UnitCommittee> lambdaQueryWrapper = new LambdaQueryWrapper<UnitCommittee>()
                .isNull(UnitCommittee::getDeleteTime)
                .eq(UnitCommittee::getUnitCode, unitCode)
                .eq(UnitCommittee::getMemCode, memCode)
                .eq(UnitCommittee::getElectCode, electCode)
                //******** 增加班子成员排除历史任职
                .isNull(UnitCommittee::getEndDate)
                .last("limit 1");
        return getOne(lambdaQueryWrapper);
    }

    @Override
    public OutMessage findByCode(String code) {
        UnitCommittee unitCommittee = selectByCode(code);
        String memCode = unitCommittee.getMemCode();
        Mem mem = iMemService.getOne(new LambdaQueryWrapper<Mem>().eq(Mem::getCode, memCode).isNull(Mem::getDeleteTime));
        unitCommittee.setMemIdcard(Objects.nonNull(mem) ? mem.getIdcard() : unitCommittee.getMemIdcard());

        if (ObjectUtil.isNull(unitCommittee)) {
            return new OutMessage<>(Status.OBJEC_NOT_EXIST);
        }
        return new OutMessage<>(Status.SUCCESS, unitCommittee);
    }

    /**
     * 获取单位班子成员
     *
     * @param code
     * @return
     */
    @Override
    public UnitCommittee selectByCode(String code) {
        LambdaQueryWrapper<UnitCommittee> lambdaQueryWrapper = new LambdaQueryWrapper<UnitCommittee>()
                .isNull(UnitCommittee::getDeleteTime)
                .eq(UnitCommittee::getCode, code);
        return getOne(lambdaQueryWrapper);
    }

    @Override
    public OutMessage updateUnitCommittee(UnitCommitteeDTO unitCommitteeDTO, Boolean isBack) throws Exception {
        OutMessage outMessage = this.checkData(unitCommitteeDTO);
        if (ObjectUtil.isNotNull(outMessage)) {
            return outMessage;
        }
        UnitCommittee unitCommittee = selectByCode(unitCommitteeDTO.getCode());
        if (ObjectUtil.isNull(unitCommittee)) {
            return new OutMessage<>(Status.OBJEC_NOT_EXIST);
        }
        String memCode = unitCommitteeDTO.getMemCode();
        if (StringUtils.hasText(memCode)) {
            String unitCode = unitCommitteeDTO.getUnitCode();
            String electCode = unitCommitteeDTO.getElectCode();
            if (isBack || (ObjectUtil.isNull(unitCommittee.getEndDate()) && ObjectUtil.isNull(unitCommitteeDTO.getEndDate()))) {
                UnitCommittee dbUnitCommittee = this.findByMemCode(unitCode, memCode, electCode);
                if (ObjectUtil.isNotNull(dbUnitCommittee) && !StrUtil.equals(dbUnitCommittee.getCode(), unitCommitteeDTO.getCode())) {
                    // 单位班子成员已经存在
                    return new OutMessage<>(Status.UNIT_COMM_IS_REPETITION);
                }
            }
        }

        iStatisticsYearService.removeMinioFile(unitCommitteeDTO.getPhotoPath(), unitCommittee.getPhotoPath());
        ModelUtils.copyPropertiesWithNull(unitCommitteeDTO, unitCommittee, "id", "code", "esId", "createTime", "isHistory");
        // 处理党组织兼任情况
        this.processD26Code(unitCommittee);

        // TODO: 2022/1/21 修复前端bug如果没有的时候， 前端依旧传值给我了
        // 是否本单位党员(1是，0否)
        String memTypeCode = unitCommittee.getMemTypeCode();
        boolean flag = false;
        Long unitCommitteeId = unitCommittee.getId();
        String memName = unitCommittee.getMemName();
        String memIdcard = unitCommittee.getMemIdcard();
        if (memTypeCode.equals(CommonConstant.ONE)) {
            //是本单位党员置空某些信息项
            LambdaUpdateWrapper<UnitCommittee> eq = new UpdateWrapper<UnitCommittee>().lambda()
                    .set(UnitCommittee::getMemCode, unitCommittee.getMemCode())
                    .set(UnitCommittee::getMemName, memName)
                    .set(UnitCommittee::getD25Code, unitCommittee.getD25Code())
                    .set(UnitCommittee::getD25Name, unitCommittee.getD25Name())
                    .set(UnitCommittee::getFileNumber, unitCommittee.getFileNumber())
                    .set(UnitCommittee::getStartDate, unitCommittee.getStartDate())
                    .set(UnitCommittee::getIsIncumbent, unitCommittee.getIsIncumbent())
                    .set(UnitCommittee::getD26Code, unitCommittee.getD26Code())
                    .set(UnitCommittee::getD26Name, unitCommittee.getD26Name())
                    .set(UnitCommittee::getMemTypeCode, unitCommittee.getMemTypeCode())
                    .set(UnitCommittee::getMemTypeName, unitCommittee.getMemTypeName())
                    .set(UnitCommittee::getMemIdcard, null)
                    .set(UnitCommittee::getD07Code, null)
                    .set(UnitCommittee::getD07Name, null)
                    .set(UnitCommittee::getBirthday, null)
                    .set(UnitCommittee::getSexCode, null)
                    .set(UnitCommittee::getSexName, null)
                    .set(UnitCommittee::getUnitCode, unitCommittee.getUnitCode())
                    .set(UnitCommittee::getUnitName, unitCommittee.getUnitName())
                    .set(UnitCommittee::getPersonName, unitCommittee.getPersonName())
                    .set(UnitCommittee::getRemark, unitCommittee.getRemark())
                    .set(UnitCommittee::getCreateTime, unitCommittee.getCreateTime())
                    .set(UnitCommittee::getUpdateTime, unitCommittee.getUpdateTime())
                    .set(UnitCommittee::getDeleteTime, unitCommittee.getDeleteTime())
                    .set(UnitCommittee::getIsHistory, unitCommittee.getIsHistory())
                    .set(UnitCommittee::getUpdateAccount, unitCommittee.getUpdateAccount())
                    .set(UnitCommittee::getElectCode, unitCommittee.getElectCode())
                    .set(UnitCommittee::getHasVillageTransferStudent, unitCommittee.getHasVillageTransferStudent())
                    .set(UnitCommittee::getD144Code, unitCommittee.getD144Code())
                    .set(UnitCommittee::getD144Name, unitCommittee.getD144Name())
                    .set(UnitCommittee::getD0121Code, unitCommittee.getD0121Code())
                    .set(UnitCommittee::getD0121Name, unitCommittee.getD0121Name())
                    .set(UnitCommittee::getReward, unitCommittee.getReward())
                    .set(UnitCommittee::getEndowmentInsuranceForUrbanEmployees, unitCommittee.getEndowmentInsuranceForUrbanEmployees())
                    .set(UnitCommittee::getD89Code, null)
                    .set(UnitCommittee::getD89Name, null)
                    .set(UnitCommittee::getCurrentPositionJob, unitCommittee.getCurrentPositionJob())
                    .set(UnitCommittee::getPhotoPath, unitCommittee.getPhotoPath())
                    .set(UnitCommittee::getFileName, unitCommittee.getFileName())
                    .set(UnitCommittee::getIsDoubleFirst, unitCommittee.getIsDoubleFirst())
                    .eq(UnitCommittee::getId, unitCommitteeId);
            if (isBack) {
                eq.set(UnitCommittee::getEndDate, null);
            } else {
                eq.set(UnitCommittee::getEndDate, unitCommittee.getEndDate());
            }
            flag = update(eq);
            if (flag) {
                // TODO: 2022/10/25 手动处理兼容加密字段
                UnitCommittee committee = new UnitCommittee();
                committee.setId(unitCommitteeId);
                committee.setMemName(memName);
                updateById(committee);
            }
        }
        if (memTypeCode.equals(CommonConstant.ZERO)) {
            //不是本单位党员置空某些信息项
            LambdaUpdateWrapper<UnitCommittee> eq = new UpdateWrapper<UnitCommittee>().lambda()
                    .set(UnitCommittee::getMemCode, null)
                    .set(UnitCommittee::getMemName, memName)
                    .set(UnitCommittee::getD25Code, unitCommittee.getD25Code())
                    .set(UnitCommittee::getD25Name, unitCommittee.getD25Name())
                    .set(UnitCommittee::getFileNumber, unitCommittee.getFileNumber())
                    .set(UnitCommittee::getStartDate, unitCommittee.getStartDate())
                    .set(UnitCommittee::getIsIncumbent, unitCommittee.getIsIncumbent())
                    .set(UnitCommittee::getD26Code, unitCommittee.getD26Code())
                    .set(UnitCommittee::getD26Name, unitCommittee.getD26Name())
                    .set(UnitCommittee::getMemTypeCode, unitCommittee.getMemTypeCode())
                    .set(UnitCommittee::getMemTypeName, unitCommittee.getMemTypeName())
                    .set(UnitCommittee::getMemIdcard, memIdcard)
                    .set(UnitCommittee::getD07Code, unitCommittee.getD07Code())
                    .set(UnitCommittee::getD07Name, unitCommittee.getD07Name())
                    .set(UnitCommittee::getBirthday, unitCommittee.getBirthday())
                    .set(UnitCommittee::getSexCode, unitCommittee.getSexCode())
                    .set(UnitCommittee::getSexName, unitCommittee.getSexName())
                    .set(UnitCommittee::getUnitCode, unitCommittee.getUnitCode())
                    .set(UnitCommittee::getUnitName, unitCommittee.getUnitName())
                    .set(UnitCommittee::getPersonName, unitCommittee.getPersonName())
                    .set(UnitCommittee::getRemark, unitCommittee.getRemark())
                    .set(UnitCommittee::getCreateTime, unitCommittee.getCreateTime())
                    .set(UnitCommittee::getUpdateTime, unitCommittee.getUpdateTime())
                    .set(UnitCommittee::getDeleteTime, unitCommittee.getDeleteTime())
                    .set(UnitCommittee::getIsHistory, unitCommittee.getIsHistory())
                    .set(UnitCommittee::getUpdateAccount, unitCommittee.getUpdateAccount())
                    .set(UnitCommittee::getElectCode, unitCommittee.getElectCode())
                    .set(UnitCommittee::getHasVillageTransferStudent, unitCommittee.getHasVillageTransferStudent())
                    .set(UnitCommittee::getD144Code, unitCommittee.getD144Code())
                    .set(UnitCommittee::getD144Name, unitCommittee.getD144Name())
                    .set(UnitCommittee::getD0121Code, unitCommittee.getD0121Code())
                    .set(UnitCommittee::getD0121Name, unitCommittee.getD0121Name())
                    .set(UnitCommittee::getReward, unitCommittee.getReward())
                    .set(UnitCommittee::getEndowmentInsuranceForUrbanEmployees, unitCommittee.getEndowmentInsuranceForUrbanEmployees())
                    .set(UnitCommittee::getD89Code, unitCommittee.getD89Code())
                    .set(UnitCommittee::getD89Name, unitCommittee.getD89Name())
                    .set(UnitCommittee::getCurrentPositionJob, unitCommittee.getCurrentPositionJob())
                    .set(UnitCommittee::getPhotoPath, unitCommittee.getPhotoPath())
                    .set(UnitCommittee::getFileName, unitCommittee.getFileName())
                    .set(UnitCommittee::getIsDoubleFirst, unitCommittee.getIsDoubleFirst())
                    .eq(UnitCommittee::getId, unitCommitteeId);
            if (isBack) {
                eq.set(UnitCommittee::getEndDate, null);
            } else {
                eq.set(UnitCommittee::getEndDate, unitCommittee.getEndDate());
            }
            flag = update(eq);
            if (flag) {
                // TODO: 2022/10/25 手动处理兼容加密字段
                UnitCommittee committee = new UnitCommittee();
                committee.setId(unitCommitteeId);
                committee.setMemName(memName);
                committee.setMemIdcard(memIdcard);
                committee.setIsDoubleFirst(unitCommittee.getIsDoubleFirst());
                updateById(committee);
            }
        }
        //boolean flag = updateById(unitCommittee);
        this.updateOrgCommitteeSameByUnit(unitCommittee);
        if (flag) {
            ThreadUtil.execAsync(() -> {
                iMemReportService.syncUnitCommittee(unitCommittee.getUnitCode());
                iSyncMemService.syncMem(unitCommittee.getMemCode(), "3");
            });
        }
        return new OutMessage<>(flag ? Status.SUCCESS : Status.FAIL);
    }

    @Override
    public OutMessage delUnitCommittee(String code) {
        UnitCommittee unitCommittee = selectByCode(code);
        if (ObjectUtil.isNull(unitCommittee)) {
            return new OutMessage<>(Status.OBJEC_NOT_EXIST);
        }
        unitCommittee.setDeleteTime(new Date());
        unitCommittee.setUpdateAccount(UserConstant.USER_CONTEXT.get().getUser().getAccount());
        unitCommittee.setTimestamp(new Date());
        unitCommittee.setUpdateTime(new Date());
        boolean flag = updateById(unitCommittee);
        if (flag) {
            ThreadUtil.execAsync(() -> {
                iMemReportService.syncUnitCommittee(unitCommittee.getUnitCode());
                iSyncMemService.syncMem(unitCommittee.getMemCode(), "3");
            });
        }
        return new OutMessage<>(flag ? Status.SUCCESS : Status.FAIL);
    }

    @Override
    public OutMessage getListByMemCode(int pageNum, int pageSize, String memCode) {
        Page<UnitCommittee> page = listByMemCode(pageNum, pageSize, memCode);

        page.getRecords().forEach(unitCommittee -> {
            String unitCode = unitCommittee.getUnitCode();
            unitCommittee.setUnitName(CacheUtils.getUnitName(unitCode));
        });

        return new OutMessage<>(Status.SUCCESS, page);
    }

    private Page<UnitCommittee> listByMemCode(int pageNum, int pageSize, String memCode) {
        Page<UnitCommittee> page = new Page<>(pageNum, pageSize);
        LambdaQueryWrapper<UnitCommittee> lambdaQueryWrapper = new LambdaQueryWrapper<UnitCommittee>()
                .isNull(UnitCommittee::getDeleteTime)
                .eq(UnitCommittee::getMemCode, memCode);
        page(page, lambdaQueryWrapper);
        return page;
    }

    @Override
    public List<UnitCommittee> findByUnitCode(String unitCode) {
        LambdaQueryWrapper<UnitCommittee> lambdaQueryWrapper = new LambdaQueryWrapper<UnitCommittee>()
                .isNull(UnitCommittee::getDeleteTime)
                .eq(UnitCommittee::getUnitCode, unitCode);
        return list(lambdaQueryWrapper);
    }

    @Override
    public boolean batchUpdate(List<UnitCommittee> committeeList) {
        return updateBatchById(committeeList);
    }


    @Override
    public OutMessage getTwoCommittee(LedgerDTO data) {
        Page<TwoCommitteeVO> page = new Page<>(data.getPageNum(), data.getPageSize());
        Page<TwoCommitteeVO> recordPage = committeeMapper.getTwoCommitteeList(page, data.getOrgCode(), data.getStartTime(), data.getEndTime());
        Integer one = 1;
        // 设置名称
        recordPage.getRecords().forEach(record -> {
            Integer isCreateOrg = record.getIsCreateOrg();
            String orgCode;
            if (one.equals(isCreateOrg)) {
                // 建立党组织的
                orgCode = record.getMainUnitOrgCode();
            } else {
                // 未建立党组情况
                orgCode = record.getManageOrgCode();
            }
            record.setOrgName(CacheUtils.getOrgNameByOrgCode(orgCode));
            record.setParentOrgName(CacheUtils.getOrgNameByOrgCode(orgCode.substring(0, orgCode.length() - 3)));
            String memCode = record.getMemCode();
            record.setName(record.getMemName());
            record.setIsIncumbent(ONE.equals(record.getIsIncumbent()) ? "在任" : "不在任");
            record.setRemark(record.getRemark());
            if (StrKit.isBlank(memCode)) {
                // 非党员
                record.setPoliticsCode("群众");
                record.setIdcard(record.getMemIdcard());
            } else {
                record.setName(record.getName());
                record.setSexName(record.getSexName());
                record.setPoliticsCode("党员");
                record.setJoinOrgDate(record.getJoinOrgDate());
                record.setFullMemberDate(record.getFullMemberDate());
                record.setIsFarmer(ONE.equals(record.getIsFarmer()) ? "从事农业生产人员" : "城镇居民");
                record.setD07Name(record.getD07Name());
                record.setD19Name(record.getD19Name());
                record.setIdcard(record.getIdcard());
                record.setPhone(record.getPhone());
                record.setD22Name(record.getD022Name());
                record.setD25Name(record.getD25Name());
                record.setD26Name(record.getD26Name());
                // 年龄
                Date birthday = record.getBirthday();
                if (birthday != null) {
                    record.setAge(DateUtil.ageOfNow(birthday));
                }
            }
        });
        return new OutMessage<>(Status.SUCCESS, recordPage);
    }

    @Override
    public OutMessage getChangeElection(LedgerDTO data) {
        String orgCode = data.getOrgCode();
        int length = orgCode.length();
        Page<ChangeElectionVO> page = new Page<>(data.getPageNum(), data.getPageSize());
        Page<ChangeElectionVO> electPage = electMapper.getChangeElectionList(page, orgCode);
        List<ChangeElectionVO> committeeElects = electPage.getRecords();

        // 填充数据
        committeeElects.forEach(record -> {
            Map<String, Object> linkedHashMap = new LinkedHashMap<>();
            linkedHashMap.put("unitName", record.getUnit_name());
            linkedHashMap.put("orgName", record.getOrg_name());
            linkedHashMap.put("d01Name", record.getD01_name());
            linkedHashMap.put("tenureStartDate", record.getTenure_start_date());
            linkedHashMap.put("secretary", record.getSecretary());
            linkedHashMap.put("count", record.getCount());
            linkedHashMap.put("auditingFileNo", "");
            linkedHashMap.put("tenureEndDate", record.getTenure_end_date());
            linkedHashMap.put("countOrgName", CacheUtils.getOrgShortNameByOrgCode(record.getElect_org_code().substring(0, length)));
            linkedHashMap.put("specialExplain", record.getSpecial_explain());
            //record.clear().setColumns(linkedHashMap);
        });
        return new OutMessage<>(Status.SUCCESS, electPage);
    }

    @Override
    public OutMessage getElectList(int pageNum, int pageSize, String unitCode) {
        final LambdaQueryWrapper<UnitCommitteeElect> queryWrapper = new LambdaQueryWrapper<UnitCommitteeElect>()
                .eq(UnitCommitteeElect::getUnitCode, unitCode)
                .isNull(UnitCommitteeElect::getDeleteTime)
                .orderByDesc(UnitCommitteeElect::getTenureStartDate);
        final Page<UnitCommitteeElect> page = new Page<>(pageNum, pageSize);
        final Page<UnitCommitteeElect> unitCommitteeElectPage = unitCommitteeElectMapper.selectPage(page, queryWrapper);
        int index = 0;
        for (UnitCommitteeElect record : page.getRecords()) {
            //判断是否历史届次 已经按时间排序 最新的是0 其他的是1
            record.setIsHistory(index != 0 ? 1 : 0);
            index++;
        }
        return new OutMessage<>(Status.SUCCESS, unitCommitteeElectPage);
    }

    @Override
    public OutMessage addElect(UnitCommitteeElectDTO electDTO) {
        UnitCommitteeElect elect = new UnitCommitteeElect();
        elect.setCode(CodeUtil.getCode());
        elect.setEsId(CodeUtil.getEsId());
        elect.setUnitCode(electDTO.getUnitCode());
        elect.setTenureStartDate(electDTO.getTenureStartDate());
        elect.setTenureEndDate(electDTO.getTenureEndDate());
        elect.setCreateTime(new Date());
        unitCommitteeElectMapper.insert(elect);
        //新增届次信息  将上一个届次下面所有人的任职结束日期改为新届次开始时间
        updateEndDate(electDTO);
        return new OutMessage<>(Status.SUCCESS, elect.getCode());
    }
    /**
     *  修改任职结束日期
     * @param electDTO
     */
    private void updateEndDate(UnitCommitteeElectDTO electDTO) {
        String unitCode = electDTO.getUnitCode();
        Date tenureStartDate = electDTO.getTenureStartDate();
        // tenureStartDate   的最新届次
        UnitCommitteeElect elect = unitCommitteeElectMapper.selectOne(
                new QueryWrapper<UnitCommitteeElect>().lambda()
                        .eq(UnitCommitteeElect::getUnitCode, unitCode)
                        .lt(UnitCommitteeElect::getTenureStartDate, tenureStartDate)
                        .isNull(UnitCommitteeElect::getDeleteTime)
                        .orderByDesc(UnitCommitteeElect::getTenureStartDate)
                        .last("LIMIT 1")
        );
        // 更新该届次下所有在任人员的任职结束日期
        if (elect != null) {
            List<UnitCommittee> list = iUnitCommitteeService.list(
                    new LambdaQueryWrapper<UnitCommittee>()
                            .eq(UnitCommittee::getElectCode, elect.getCode())
                            .isNull(UnitCommittee::getDeleteTime)
                            .isNull(UnitCommittee::getEndDate)
            );
            if (!list.isEmpty()) {
                list.forEach(unitCommittee -> unitCommittee.setEmpEndDate(tenureStartDate));
                iUnitCommitteeService.saveOrUpdateBatch(list);
            }
        }
    }

    @Override
    public OutMessage updateElect(UnitCommitteeElectDTO electDTO) {
        final LambdaQueryWrapper<UnitCommitteeElect> queryWrapper = new LambdaQueryWrapper<UnitCommitteeElect>()
                .eq(UnitCommitteeElect::getCode, electDTO.getCode())
                .isNull(UnitCommitteeElect::getDeleteTime)
                .last("limit 1");
        final UnitCommitteeElect elect = unitCommitteeElectMapper.selectOne(queryWrapper);
        if (Objects.isNull(elect)) {
            return new OutMessage<>(Status.OBJEC_NOT_EXIST);
        }
        elect.setTenureStartDate(electDTO.getTenureStartDate());
        elect.setTenureEndDate(electDTO.getTenureEndDate());
        elect.setUpdateTime(new Date());
        int update = unitCommitteeElectMapper.updateById(elect);
        if (update > 0) {
            //新增届次信息  将上一个届次下面所有人的任职结束日期改为新届次开始时间
            updateEndDate(electDTO);
            // TODO: 2022/1/27 增加班子成员的同步
            ThreadUtil.execAsync(() -> {
                iMemReportService.syncUnitCommittee(elect.getUnitCode());
                syncUnitCommittee.syncUnitCommitMem(elect.getUnitCode(), null);
            });
        }
        return new OutMessage<>(Status.SUCCESS, elect);
    }

    private UnitCommitteeElect getElect(String electCode) {
        final LambdaQueryWrapper<UnitCommitteeElect> queryWrapper = new LambdaQueryWrapper<UnitCommitteeElect>()
                .eq(UnitCommitteeElect::getCode, electCode)
                .isNull(UnitCommitteeElect::getDeleteTime)
                .last("limit 1");
        return unitCommitteeElectMapper.selectOne(queryWrapper);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public OutMessage delElect(String electCode) {
        final UnitCommitteeElect elect = getElect(electCode);
        if (Objects.isNull(elect)) {
            return new OutMessage<>(Status.OBJEC_NOT_EXIST);
        }
        elect.setDeleteTime(new Date());
        final LambdaQueryWrapper<UnitCommittee> queryWrapper = new LambdaQueryWrapper<UnitCommittee>()
                .eq(UnitCommittee::getElectCode, electCode)
                .isNull(UnitCommittee::getDeleteTime);
        final List<UnitCommittee> unitCommittees = committeeMapper.selectList(queryWrapper);
        if (CollUtil.isNotEmpty(unitCommittees)) {
            return new OutMessage<>(Status.ELECT_COMMIT_NOTNULL);
        }
//        if (CollUtil.isNotEmpty(unitCommittees)) {
//            unitCommittees.forEach(unitCommittee -> unitCommittee.setDeleteTime(new Date()));
//            this.batchUpdate(unitCommittees);
//        }
        unitCommitteeElectMapper.updateById(elect);
        return new OutMessage<>(Status.SUCCESS);
    }

    @Override
    public OutMessage findElect(String electCode) {
        final UnitCommitteeElect elect = getElect(electCode);
        if (Objects.isNull(elect)) {
            return new OutMessage<>(Status.OBJEC_NOT_EXIST);
        }
        return new OutMessage<>(Status.SUCCESS, elect);
    }

    @Override
    public CurrCommitteeVO findCurrYearByMemCode(String memCode) {
        return electMapper.findLastYearByMemCode(memCode);
    }

    @Override
    public OutMessage<?> backOut(UnitCommitteeDTO data) throws Exception {
        return this.updateUnitCommittee(data, true);
    }


    /**
     * 获取班子成员兼任信息
     */
    public OutMessage<?> getUnitCommitteeSrcOrg(String unitCode, String memCode) {
        if (StrUtil.isEmpty(memCode)) {
            return new OutMessage<>(Status.SUCCESS);
        }
        UnitOrgLinked unitOrgLinked = iUnitOrgLinkedService.findByUnitCodeAndIsMainAll(unitCode);
        if (Objects.isNull(unitOrgLinked)) {
            return new OutMessage<>(Status.SUCCESS);
        }
        UnitCommittee committee = new UnitCommittee();
        OrgCommitteeElect elect = iOrgCommitteeElectService.findLastElectByOrgCode(unitOrgLinked.getOrgCode());
        if (Objects.nonNull(elect)) {
            LambdaQueryWrapper<OrgCommittee> wrapper = new LambdaQueryWrapper<OrgCommittee>()
                    .eq(OrgCommittee::getElectCode, elect.getCode()).eq(OrgCommittee::getMemCode, memCode)
                    .isNull(OrgCommittee::getDeleteTime).isNull(OrgCommittee::getEndDate)
                    .orderByDesc(OrgCommittee::getEndDate)
                    .last("limit 1");
            OrgCommittee orgCommittee = iOrgCommitteeService.getOne(wrapper);
            if (Objects.nonNull(orgCommittee)) {
                committee.setFileNumber(orgCommittee.getFileNumber());
                committee.setStartDate(orgCommittee.getStartDate());
                committee.setHasVillageTransferStudent(Objects.nonNull(orgCommittee.getHasVillageTransferStudent()) ? String.valueOf(orgCommittee.getHasVillageTransferStudent()) : null);
                committee.setD144Code(orgCommittee.getD144Code());
                committee.setD144Name(orgCommittee.getD144Name());
                committee.setReward(orgCommittee.getReward());
                committee.setEndowmentInsuranceForUrbanEmployees(orgCommittee.getEndowmentInsuranceForUrbanEmployees());
                committee.setD0121Code(orgCommittee.getD121Code());
                committee.setD0121Name(orgCommittee.getD121Name());
                committee.setRemark(orgCommittee.getDutyExplain());
                committee.setCurrentPositionJob(orgCommittee.getCurrentPositionJob());
                committee.setPhotoPath(orgCommittee.getPhotoPath());
                committee.setFileName(orgCommittee.getFileName());
            }
        }
        return new OutMessage<>(Status.SUCCESS, committee);
    }

    /**
     * 更新兼任班子成员相同信息
     */
    public void updateOrgCommitteeSameByUnit(UnitCommittee committee) {
        if (!StrUtil.equals(committee.getMemTypeCode(), "1")) {
            return;
        }
        UnitCommitteeElect elect = iUnitCommitteeElectService.findLastElectByUnitCode(committee.getUnitCode());
        if (Objects.isNull(elect) || !StrUtil.equals(elect.getCode(), committee.getElectCode())) {
            return;
        }
        UnitOrgLinked unitOrgLinked = iUnitOrgLinkedService.findByUnitCodeAndIsMainAll(committee.getUnitCode());
        if (Objects.isNull(unitOrgLinked)) {
            return;
        }
        OrgCommitteeElect orgCommitteeElect = iOrgCommitteeElectService.findLastElectByOrgCode(unitOrgLinked.getOrgCode());
        if (Objects.nonNull(orgCommitteeElect)) {
            LambdaQueryWrapper<OrgCommittee> wrapper = new LambdaQueryWrapper<OrgCommittee>()
                    .eq(OrgCommittee::getElectCode, orgCommitteeElect.getCode()).eq(OrgCommittee::getMemCode, committee.getMemCode())
                    .isNull(OrgCommittee::getDeleteTime).isNull(OrgCommittee::getEndDate)
                    .orderByDesc(OrgCommittee::getEndDate)
                    .last("limit 1");
            OrgCommittee orgCommittee = iOrgCommitteeService.getOne(wrapper);
            if (Objects.nonNull(orgCommittee)) {
                OrgCommittee updateCote = new OrgCommittee();
                updateCote.setId(orgCommittee.getId());
                updateCote.setFileNumber(committee.getFileNumber());
                updateCote.setCurrentPositionJob(committee.getCurrentPositionJob());
                updateCote.setPhotoPath(committee.getPhotoPath());
                updateCote.setFileName(committee.getFileName());
                if (StrUtil.startWith(unitOrgLinked.getUnitType(), "92")) {
                    updateCote.setHasVillageTransferStudent(Objects.nonNull(committee.getHasVillageTransferStudent()) ? Integer.valueOf(committee.getHasVillageTransferStudent()) : null);
                    updateCote.setD144Code(committee.getD144Code());
                    updateCote.setD144Name(committee.getD144Name());
                    updateCote.setReward(committee.getReward());
                    updateCote.setEndowmentInsuranceForUrbanEmployees(committee.getEndowmentInsuranceForUrbanEmployees());
                    updateCote.setD121Code(committee.getD0121Code());
                    updateCote.setD121Name(committee.getD0121Name());
                }
                iOrgCommitteeService.updateById(updateCote);
            }
        }

    }

    @Override
    public List<UnitCommittee> findAllBySyncVillager() {
        return electMapper.findAllBySyncVillager();
    }

    @Override
    public Page<VillageLeaderListVO> findVillageLeaderList(Integer pageNum, Integer pageSize, String orgLevelCode, Boolean includeChild, String queryStr) {
        Page<VillageLeaderListVO> page = new Page<>(pageNum, pageSize);
        Page<DecryptVillageLeaderMap<String, Object>> mapPage = committeeMapper.findVillageLeaderList(new Page<>(pageNum, pageSize), orgLevelCode, includeChild, queryStr);
        List<DecryptVillageLeaderMap<String, Object>> mapList = mapPage.getRecords();
        if (CollUtil.isEmpty(mapList)) {
            return page;
        }
        //民族
        Map<String, String> d06Map = CacheUtils.getDic(DictConstant.DICT_D06).stream().filter(t -> Objects.nonNull(t.get("key"))).collect(Collectors.toMap(record -> record.get("key"), record1 -> record1.get("name"), (a, b) -> b, LinkedHashMap::new));
        //学历
        Map<String, String> d07Map = CacheUtils.getDic(DictConstant.DICT_D07).stream().filter(t -> Objects.nonNull(t.get("key"))).collect(Collectors.toMap(record -> record.get("key"), record1 -> record1.get("name"), (a, b) -> b, LinkedHashMap::new));
        //籍贯
        Map<String, String> d48Map = CacheUtils.getDic(DictConstant.DICT_D48).stream().filter(t -> Objects.nonNull(t.get("key"))).collect(Collectors.toMap(record -> record.get("key"), record1 -> record1.get("name"), (a, b) -> b, LinkedHashMap::new));
        List<VillageLeaderListVO> records = new ArrayList<>();
        for (Map<String, Object> record : mapList) {
            //1 单位班子 2 组织班子
            String type = record.get("type").toString();
            if (StrUtil.equals(type, ONE)) {
                //现工作单位及职务全称
                record.put("a0192a", record.get("d25_name"));
            }
            //任现职务层次时间
            if (Objects.nonNull(record.get("a0288"))) {
                record.put("a0288", ((Date) record.get("a0288")).getTime());
            }
            Object memTypeCode = record.get("mem_type_code");
            //党员
            boolean flag = StrUtil.equals(memTypeCode.toString(), ONE);
            if (flag) {
                //入党时间
                if (Objects.nonNull(record.get("a0144"))) {
                    record.put("a0144", ((Date) record.get("a0144")).getTime());
                }
                //参加工作时间
                if (Objects.nonNull(record.get("a0134"))) {
                    record.put("a0134", ((Date) record.get("a0134")).getTime());
                }
                //任职时间
                if (Objects.nonNull(record.get("villageTakeTime"))) {
                    record.put("villageTakeTime", ((Date) record.get("villageTakeTime")).getTime());
                }
                //性别 1男 2女
                if (Objects.nonNull(record.get("mem_sex_code"))) {
                    record.put("a0104", record.get("mem_sex_code"));
                    record.put("a0104a", Objects.equals(record.get("mem_sex_code"), "1") ? "男" : (Objects.equals(record.get("mem_sex_code"), "2") ? "女" : "未知"));
                }
                //民族
                if (Objects.nonNull(record.get("mem_d06_code"))) {
                    record.put("a0117", record.get("mem_d06_code"));
                    record.put("a0117a", d06Map.get(record.get("mem_d06_code").toString()));
                    record.put("a0117Name", d06Map.get(record.get("mem_d06_code").toString()));
                }
                //籍贯
                if (Objects.nonNull(record.get("d48_code"))) {
                    record.put("a0111", record.get("d48_code"));
                    record.put("a0111a", d48Map.get(record.get("d48_code").toString()));
                }
                //学历
                if (Objects.nonNull(record.get("mem_d07_code"))) {
                    record.put("a0801a", d07Map.get(record.get("mem_d07_code").toString()));
                }
                //出生日期
                if (Objects.nonNull(record.get("mem_birthday"))) {
                    record.put("a0107", ((Date) record.get("mem_birthday")).getTime());
                }
            } else {
                //性别 1男 2女
                if (Objects.nonNull(record.get("sex_code"))) {
                    record.put("a0104", record.get("sex_code"));
                    record.put("a0104a", Objects.equals(record.get("sex_code"), "1") ? "男" : (Objects.equals(record.get("sex_code"), "2") ? "女" : "未知"));
                }
                //学历
                if (Objects.nonNull(record.get("d07_code"))) {
                    record.put("a0801a", d07Map.get(record.get("d07_code").toString()));
                }
                //出生日期
                if (Objects.nonNull(record.get("birthday"))) {
                    record.put("a0107", ((Date) record.get("birthday")).getTime());
                }
            }
            record.put("d25_name", null);
            record.put("create_time", null);
            record.put("d48_code", null);
            record.put("mem_type_code", null);
            record.put("sex_code", null);
            record.put("mem_sex_code", null);
            record.put("mem_d06_code", null);
            record.put("d07_code", null);
            record.put("mem_d07_code", null);
            record.put("mem_birthday", null);
            records.add(JackSonUtil.mapToBean(record, VillageLeaderListVO.class));
        }
        page.setRecords(records);
        page.setTotal(mapPage.getTotal());
        page.setSize(mapPage.getSize());
        page.setCurrent(mapPage.getCurrent());
        page.setPages(mapPage.getPages());
        return page;
    }

    @Override
    public List<Map<String, String>> getVillageLeaderStat(String orgLevelCode, Boolean includeChild, String queryStr) {
        List<Map<String, Object>> mapList = baseMapper.getVillageLeaderStat(orgLevelCode, includeChild, queryStr);
        if (CollUtil.isEmpty(mapList)) {
            return Collections.emptyList();
        }
        List<String> statList = new ArrayList<>();
        statList.add(orgLevelCode);
        //统计直属下级
        final int statLevel = StrUtil.length(orgLevelCode) + 3;
        for (Map<String, Object> map : mapList) {
            String orgCode = (String) map.get("org_code");
            String statOrgCode = StrUtil.sub(orgCode, 0, statLevel);
            if (!statList.contains(statOrgCode)) {
                statList.add(statOrgCode);
            }
        }
        if (CollUtil.isEmpty(statList)) {
            return Collections.emptyList();
        }
        Map<String, String> mainUnitNameByOrgCodeList = orgAllService.findMainUnitNameByOrgCodeList(statList);
        //村委班子和组织班子都有的情况下
        Map<String, List<Map<String, Object>>> duplicateMemCodeMap = mapList.stream()
                .filter(t -> Objects.nonNull(t.get("mem_code")))
                .collect(Collectors.groupingBy(t -> t.get("mem_code").toString()));
        List<Map<String, String>> result = new ArrayList<>();
        for (String orgCode : mainUnitNameByOrgCodeList.keySet()) {
            //总人数
            int total = 0;
            //村委班子
            int villageCommittee = 0;
            //村支委班子
            int villageBranchCommittee = 0;
            //村支书主任一肩挑人数 d022_code='1' and d25_code in ('41','51')
            int shoulder = 0;
            //村两委兼任人员人数 d022_code is not null and d25_code is not null
            int doubleUp = 0;
            //研究生及以上学历人数
            int postgraduate = 0;
            //本科人数
            int undergraduate = 0;
            //专科人数
            int junior = 0;
            //女性人数
            int woman = 0;
            //35岁及以下人数
            int age35 = 0;
            //36岁及至50岁人数
            int age3650 = 0;
            //51岁至55岁人数
            int age5155 = 0;
            for (Map<String, Object> map : mapList) {
                if (StrUtil.startWith(map.get("org_code").toString(), orgCode)) {
                    total++;
                    //班子类型
                    Object type = map.get("type");
                    //学历
                    Object d07Code = map.get("d07_code");
                    Object memD07Code = map.get("mem_d07_code");
                    //性别
                    Object sexCode = map.get("sex_code");
                    Object memSexCode = map.get("mem_sex_code");
                    //生日
                    Object memBirthday = map.get("mem_birthday");
                    Object birthday = map.get("birthday");
                    //村委班子
                    if (StrUtil.equals(type.toString(), "1")) {
                        villageCommittee++;
                    }
                    //村支委班子
                    if (StrUtil.equals(type.toString(), "2")) {
                        villageBranchCommittee++;
                    }
                    //研究生及以上学历人数
                    if ((Objects.nonNull(d07Code) && StrUtil.startWith(d07Code.toString(), "1")) || (Objects.nonNull(memD07Code) && StrUtil.startWith(memD07Code.toString(), "1"))) {
                        postgraduate++;
                    }
                    //研究生及以上学历人数
                    if ((Objects.nonNull(d07Code) && StrUtil.startWith(d07Code.toString(), "2")) || (Objects.nonNull(memD07Code) && StrUtil.startWith(memD07Code.toString(), "2"))) {
                        undergraduate++;
                    }
                    //研究生及以上学历人数
                    if ((Objects.nonNull(d07Code) && StrUtil.startWith(d07Code.toString(), "3")) || (Objects.nonNull(memD07Code) && StrUtil.startWith(memD07Code.toString(), "3"))) {
                        junior++;
                    }
                    if ((Objects.nonNull(birthday) && birthday instanceof Date)) {
                        Date date = (Date) (birthday);
                        int age = DateUtil.ageOfNow(date);
                        if (age <= 35) {
                            age35++;
                        }
                        if (age >= 36 && age <= 50) {
                            age3650++;
                        }
                        if (age >= 51 && age <= 55) {
                            age5155++;
                        }
                    }
                    if ((Objects.nonNull(memBirthday) && memBirthday instanceof Date)) {
                        Date date = (Date) (memBirthday);
                        int age = DateUtil.ageOfNow(date);
                        if (age <= 35) {
                            age35++;
                        }
                        if (age >= 36 && age <= 50) {
                            age3650++;
                        }
                        if (age >= 51 && age <= 55) {
                            age5155++;
                        }
                    }
                    if ((Objects.nonNull(sexCode) && StrUtil.equals(sexCode.toString(), "0")) || (Objects.nonNull(memSexCode) && StrUtil.equals(memSexCode.toString(), "0"))) {
                        woman++;
                    }
                }
            }
            //村委班子和组织班子都有的人员集合
            List<String> memCodeList = mapList.stream()
                    .filter(t -> Objects.nonNull(t.get("mem_code")) && StrUtil.startWith(t.get("org_code").toString(), orgCode))
                    .collect(Collectors.groupingBy(t -> t.get("mem_code").toString(), Collectors.counting()))
                    .entrySet()
                    .stream()
                    .filter(e -> e.getValue() != 1)
                    .map(Map.Entry::getKey)
                    .collect(Collectors.toList());
            for (String memCodeKey : memCodeList) {
                List<Map<String, Object>> maps = duplicateMemCodeMap.get(memCodeKey);
                if (maps.size() == 2) {
                    Map<String, Object> map1 = maps.get(0);
                    String type1 = (String) map1.get("type");
                    Map<String, Object> map2 = maps.get(1);
                    String type2 = (String) map2.get("type");
                    String d25Code = "";
                    if (StrUtil.equals(type1, "1")) {
                        d25Code = (String) map1.get("d25_code");
                    }
                    String d022Code = "";
                    if (StrUtil.equals(type2, "2")) {
                        d022Code = (String) map2.get("d022_code");
                    }
                    if (StringUtils.hasText(d25Code) && StringUtils.hasText(d022Code)) {
                        doubleUp++;
                        //村支书主任一肩挑人数 d022_code='1' and d25_code in ('41','51')
                        if (StrUtil.equalsAny(d25Code, "41", "51") && StrUtil.equals(d022Code, "1")) {
                            shoulder++;
                        }
                    }
                    Object memBirthday = map1.get("mem_birthday");
                    Object memSexCode = map1.get("mem_sex_code");
                    Object memD07Code = map1.get("mem_d07_code");
                    //研究生及以上学历人数
                    if ((Objects.nonNull(memD07Code) && StrUtil.startWith(memD07Code.toString(), "1"))) {
                        postgraduate--;
                    }
                    //研究生及以上学历人数
                    if ((Objects.nonNull(memD07Code) && StrUtil.startWith(memD07Code.toString(), "2"))) {
                        undergraduate--;
                    }
                    //研究生及以上学历人数
                    if ((Objects.nonNull(memD07Code) && StrUtil.startWith(memD07Code.toString(), "3"))) {
                        junior--;
                    }
                    if ((Objects.nonNull(memBirthday) && memBirthday instanceof Date)) {
                        Date date = (Date) (memBirthday);
                        int age = DateUtil.ageOfNow(date);
                        if (age <= 35) {
                            age35--;
                        }
                        if (age >= 36 && age <= 50) {
                            age3650--;
                        }
                        if (age >= 51 && age <= 55) {
                            age5155--;
                        }
                    }
                    if ((Objects.nonNull(memSexCode) && StrUtil.equals(memSexCode.toString(), "0"))) {
                        woman--;
                    }
                }
            }
            Map<String, String> map = new HashMap<>(10);
            map.put("total", String.valueOf((total - doubleUp)));
            map.put("villageCommittee", String.valueOf(villageCommittee));
            map.put("villageBranchCommittee", String.valueOf(villageBranchCommittee));
            map.put("shoulder", String.valueOf(shoulder));
            map.put("doubleUp", String.valueOf(doubleUp));
            map.put("postgraduate", String.valueOf(postgraduate));
            map.put("undergraduate", String.valueOf(undergraduate));
            map.put("junior", String.valueOf(junior));
            map.put("woman", String.valueOf(woman));
            map.put("35", String.valueOf(age35));
            map.put("36_50", String.valueOf(age3650));
            map.put("51_55", String.valueOf(age5155));

            map.put("unitName", mainUnitNameByOrgCodeList.get(orgCode));
            result.add(map);
        }
        return result;
    }

    /**
     * 根据党员code获取党员是否有当前年份有在职的信息
     * 目前仅仅查询了届次code和当前数据的主键
     *
     * @param memCode
     * @return
     */
    @Override
    public List<UnitCommittee> findUnitCommitByMemCode(String memCode) {
        LambdaQueryWrapper<UnitCommittee> wrapper = new LambdaQueryWrapper<>();
        wrapper.select(UnitCommittee::getElectCode,UnitCommittee::getCode,UnitCommittee::getUnitCode);
        wrapper.eq(UnitCommittee::getMemCode, memCode);
        wrapper.isNull(UnitCommittee::getEndDate)
                .isNull(UnitCommittee::getDeleteTime);
        wrapper.apply("  to_char(start_date, 'yyyy')<='"+ iStatisticsYearService.getStatisticalYear()+"' ");
        return baseMapper.selectList(wrapper);
    }

    @Override
    public List<UnitCommittee> findUnitCommitByMemCodeAll(String memCode) {
        LambdaQueryWrapper<UnitCommittee> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(UnitCommittee::getMemCode, memCode);
        wrapper.isNull(UnitCommittee::getDeleteTime);
        return baseMapper.selectList(wrapper);
    }

    @Override
    public List<UnitCommittee> findUnitCommitByMemIdcard(String memIdcard) {
        LambdaQueryWrapper<UnitCommittee> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(UnitCommittee::getMemIdcard, memIdcard);
        wrapper.isNull(UnitCommittee::getDeleteTime);
        return baseMapper.selectList(wrapper);
    }
    /**
     * 班子成员本单位党员获取身份证、学历、政治面貌信息等信息
     * @param memCodeList
     */
    private Map<String, Mem> setUnitCommitteeMem(List<String> memCodeList){
        Map<String, Mem> memMap = new HashMap<>();
        if(CollUtil.isEmpty(memCodeList)){
            return memMap;
        }
        LambdaQueryWrapper<Mem> memLambdaQueryWrapper = Wrappers.lambdaQuery();
        //todo 处理密评党员不需要字段
        memLambdaQueryWrapper.select(Mem::getCode,Mem::getBirthday,Mem::getD89Code,Mem::getD89Name,Mem::getD07Code,Mem::getD07Name,Mem::getSexCode,Mem::getSexName);
        memLambdaQueryWrapper.isNull(Mem::getDeleteTime)
                .in(Mem::getCode, memCodeList);
        List<Mem> memList = memMapper.selectList(memLambdaQueryWrapper);
        if(CollUtil.isEmpty(memList)){
            return memMap;
        }
        return memList.stream().collect(Collectors.toMap(Mem::getCode, e -> e, (e1, e2) -> e1));
    }
}
