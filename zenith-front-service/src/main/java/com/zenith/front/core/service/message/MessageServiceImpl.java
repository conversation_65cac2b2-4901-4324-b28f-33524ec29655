package com.zenith.front.core.service.message;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.zenith.front.api.message.IMessageService;
import com.zenith.front.dao.mapper.message.MessageMapper;
import com.zenith.front.model.bean.Message;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * <p>
 * 消息单表，消息表会通过消息直接推送到相应的通知表ccp_notice 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-07-10
 */
@Service
public class MessageServiceImpl extends ServiceImpl<MessageMapper, Message> implements IMessageService {

    @Resource
    MessageMapper messageMapper;

    @Override
    public Message findMessageByCode(String code) {
        LambdaQueryWrapper<Message> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(Message::getCode, code)
                .isNull(Message::getDeleteTime);
        return getOne(wrapper);
    }

    @Override
    public List<Message> getMseeageByInList(List<String> messageCodelist) {
        LambdaQueryWrapper<Message> wrapper = new LambdaQueryWrapper<>();
        wrapper.in(CollUtil.isNotEmpty(messageCodelist), Message::getCode, messageCodelist)
                .isNull(Message::getDeleteTime)
                .orderByDesc(Message::getTime);
        return list(wrapper);
    }


    @Override
    public Page<Message> findMessageCreateCode(String code, Integer pageSize, Integer pageNum) {
        LambdaQueryWrapper<Message> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(Message::getCrateOrgCode, code)
                .isNull(Message::getDeleteTime)
                .orderByDesc(Message::getCreateTime);
        return messageMapper.selectPage(new Page<>(pageNum, pageSize), wrapper);
    }


}
