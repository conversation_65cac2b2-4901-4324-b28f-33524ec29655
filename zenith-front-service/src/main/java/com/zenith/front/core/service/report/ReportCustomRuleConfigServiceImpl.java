package com.zenith.front.core.service.report;

import com.zenith.front.api.report.IReportCustomRuleConfigService;
import com.zenith.front.dao.mapper.report.ReportCustomRuleConfigMapper;
import com.zenith.front.model.bean.ReportCustomRuleConfig;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-04-14
 */
@Service
public class ReportCustomRuleConfigServiceImpl extends ServiceImpl<ReportCustomRuleConfigMapper, ReportCustomRuleConfig> implements IReportCustomRuleConfigService {

}
