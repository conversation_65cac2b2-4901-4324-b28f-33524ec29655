package com.zenith.front.core.analysis.ext.condition;

import com.b1809.analysis.extend.GenSqlConditionFuc;
import org.jooq.Condition;
import org.jooq.Field;

import static org.jooq.impl.DSL.field;
import static org.jooq.impl.DSL.name;

/**
 * 行业党组织
 *
 * <AUTHOR>
 * @date 2021/11/23
 */
public class OrgIndustryAllCondition implements GenSqlConditionFuc {


    @Override
    public String getTableName() {
        return "ccp_org_industry_all";
    }

    @Override
    public Condition create(String orgCode, String orgLevelCode) {
        return field(name( "industry_org_code"), String.class).like(orgLevelCode + "%");
    }

    @Override
    public Field<?> getLevelCodeField() {
        return field(name( "industry_org_code"), String.class);
    }
}
