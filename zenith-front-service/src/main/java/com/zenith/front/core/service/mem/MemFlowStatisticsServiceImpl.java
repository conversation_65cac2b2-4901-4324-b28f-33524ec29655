package com.zenith.front.core.service.mem;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.zenith.front.api.mem.IMemFlow1Service;
import com.zenith.front.api.mem.IMemFlowStatisticsService;
import com.zenith.front.api.org.IOrgService;
import com.zenith.front.dao.mapper.mem.MemFlow1Mapper;
import com.zenith.front.dao.mapper.mem.MemFlowStatisticsMapper;
import com.zenith.front.model.bean.MemFlow1;
import com.zenith.front.model.bean.MemFlowStatistics;
import com.zenith.front.model.bean.Org;
import lombok.extern.slf4j.Slf4j;
import org.jooq.Condition;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

import static org.jooq.impl.DSL.*;

/**
 * <p>
 * 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-11-25
 */
@Service
@Slf4j
public class MemFlowStatisticsServiceImpl extends ServiceImpl<MemFlowStatisticsMapper, MemFlowStatistics> implements IMemFlowStatisticsService {

    @Resource
    private IMemFlow1Service iMemFlowService;
    @Resource
    private MemFlow1Mapper memFlow1Mapper;
    @Resource
    private IOrgService iOrgService;

    static final int BATCH_NUM = 5000;


    /**
     * 同步流动党员统计表
     */
    @Override
    public void syncMemFlowStatistics() {
        this.remove(Wrappers.emptyWrapper());
        Org org = iOrgService.getOne(new LambdaQueryWrapper<Org>().isNull(Org::getDeleteTime).orderByAsc(Org::getOrgCode).last("LIMIT 1"));
        if (Objects.isNull(org)) {
            return;
        }
        List<MemFlowStatistics> saveList = new ArrayList<>();
        //流出记录
        List<MemFlow1> outList = iMemFlowService.list(new LambdaQueryWrapper<MemFlow1>().likeRight(MemFlow1::getMemOrgOrgCode, org.getOrgCode()).in(MemFlow1::getFlowStep, "1", "2"));
        for (MemFlow1 memFlow : outList) {
            saveList.add(getMemFlowStatistics(memFlow, "1"));
        }

        //流入记录：只统计已纳入支部管理
        Condition condition = noCondition().and(field(name("flow_step"), String.class).eq("2").and(field(name("has_county_library"), String.class).ne("1")))
                .and(field(name("org_code")).like(org.getOrgCode() + "%").and(field(name("in_org_code")).isNotNull())
                        .or(field(name("out_org_branch_org_code")).like(org.getOrgCode() + "%").and(field(name("in_org_code")).isNull())));
        List<MemFlow1> inList = memFlow1Mapper.findInByList(condition.toString());
        for (MemFlow1 memFlow : inList) {
            MemFlowStatistics statistics = getMemFlowStatistics(memFlow, "2");
            Org orgByCode = iOrgService.findOrgByCode(memFlow.getInOrgCode());
            if (Objects.nonNull(orgByCode)) {
                statistics.setMemOrgCode(orgByCode.getCode());
                statistics.setMemOrgOrgCode(orgByCode.getOrgCode());
                statistics.setInOrgName(orgByCode.getName());
            }
            saveList.add(statistics);
        }

        if (CollUtil.isNotEmpty(saveList)) {

            int nums = (int) Math.ceil((double) saveList.size()/(double) BATCH_NUM);
            for (int i = 0; i < nums; i++){
                List<MemFlowStatistics> tempList;
                if(i == (nums - 1)){
                    tempList = saveList.subList(i * BATCH_NUM, saveList.size());
                }else {
                    tempList = saveList.subList(i * BATCH_NUM, (i + 1) * BATCH_NUM);
                }
                log.info("流动党员总页：{}；当前页：{}; 数量：{}", nums, i+1, tempList.size());
                this.saveBatch(tempList, tempList.size());
            }
            log.info("流动党员数据同步完成：" + saveList.size());
        }
    }

    private MemFlowStatistics getMemFlowStatistics(MemFlow1 memFlow, String flowOutIn) {
        MemFlowStatistics statistics = new MemFlowStatistics();
        BeanUtils.copyProperties(memFlow, statistics);
        statistics.setFlowOutIn(flowOutIn);
        statistics.setD09Code(memFlow.getInMemD09Code());
        statistics.setD09Name(memFlow.getInMemD09Name());
        statistics.setD20Code(memFlow.getInMemD20Code());
        statistics.setD20Name(memFlow.getInMemD20Name());
        statistics.setHasInter(setHasInter(statistics.getD09Code(), statistics.getD20Code()));
        //设置单位关联信息
        statistics.setD16Code(memFlow.getInUnitD16Code());
        if (StrUtil.isNotEmpty(memFlow.getInOrgCode())) {
            statistics.setD04Code(memFlow.getInOrgD04Code());
            statistics.setD04Name(memFlow.getInOrgD04Name());
        } else {
            statistics.setD04Code(memFlow.getOutOrgD04Code());
            statistics.setD04Name(memFlow.getOutOrgD04Name());
        }
        // 流动党员修复经济类型
        if(Objects.nonNull(statistics.getD04Code()) && StrUtil.startWith(statistics.getD04Code(), "4") && StrUtil.isBlank(statistics.getD16Code())){
            statistics.setD16Code("31");
        }
        return statistics;
    }

    /**
     * 是否中介组织从业人员
     */
    private String setHasInter(String d09Code, String d20Code) {
        //新社会阶层 （工作岗位以02、03、505 开头的，等于504的）
        //(d09_code like '02%' and d09_code not like '025%') or (d09_code like '03%' and d09_code not in ('0313','0323','0333')) or d09_code like '505%' or d09_code = '504'
        boolean flag1 = StrUtil.startWith(d09Code, "02") && !StrUtil.startWith(d09Code, "025");
        boolean flag2 = StrUtil.startWith(d09Code, "03") && !StrUtil.equalsAny(d09Code, "0313", "0323", "0333");
        boolean flag3 = StrUtil.startWith(d09Code, "505") || StrUtil.equals(d09Code, "504");
        return StrUtil.startWith(d20Code, "5") && (flag1 || flag2 || flag3) ? "1" : "0";
    }


}
