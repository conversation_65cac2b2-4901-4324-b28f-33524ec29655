package com.zenith.front.core.analysis.count.year2024;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.thread.ThreadUtil;
import cn.hutool.core.util.StrUtil;
import com.b1809.analysis.entity.PeggingPara;
import com.b1809.analysis.extend.ITableCount;
import com.b1809.analysis.plugin.elasticsearch.EsKit;
import com.jfinal.plugin.activerecord.Record;
import com.zenith.front.core.analysis.ext.condition.year2024.MemAllCondition2024;
import com.zenith.front.core.analysis.ext.condition.year2024.OrgAllCondition2024;
import com.zenith.front.core.analysis.ext.condition.year2024.UnitAllCondition2024;
import com.zenith.front.core.analysis.ext.condition.year2024.DevelopStepLogAllCondition2024;
import org.jooq.Condition;
import org.jooq.Record1;
import org.jooq.SelectConditionStep;

import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutorService;

import static com.zenith.front.common.constant.DBConstant.DSL_CONTEXT;
import static org.jooq.impl.DSL.*;

/**
 * 街道、社区（居委会）党员情况
 */
public class Html7Count2024 implements ITableCount {


    @Override
    public String getReportCode() {
        return "2024_7.html";
    }

    @Override
    public Map<String, Map<String, Number>> doCount(String reportCode, String orgCode, String orgLevelCode) throws Exception {
        return null;
    }

    @Override
    public Map<String, Object> doPegging(PeggingPara peggingPara) throws Exception {
        return null;
    }

    @Override
    public Map<String, Number> doReplenishCount(String reportCode, String orgCode, String orgLevelCode) throws Exception {
        return getReplenishCount(orgCode, orgLevelCode, Html1Count2024.TABLE_YEAR, false);
    }

    public Map<String, Number> getReplenishCount(String orgCode, String orgLevelCode, String tableYear, boolean isBackups) {
        String ccpMemAll = StrUtil.isEmpty(tableYear) ? "ccp_mem_all" : "ccp_mem_all_" + tableYear;
        String ccpUnitAll = StrUtil.isEmpty(tableYear) ? "ccp_unit_all" : "ccp_unit_all_" + tableYear;
        Map<String, Number> result = new LinkedHashMap<>();
        Html53Count2024.initReplenishCol(1, 17, result);
        ExecutorService executor = ThreadUtil.newExecutor(10);
        Condition memCond1 = noCondition().and(this.getMem12Condition(orgCode, orgLevelCode));
        Condition memCond = noCondition().and(this.getMem7ListCondition(orgCode, orgLevelCode));
        Map<String, String> stringMap = new HashMap<String, String>() {{
            //1.社区（居委会）委员中党员		名，其中社区（居委会）主任中党员		名。
            if (isBackups) {
                put("1", DSL_CONTEXT.select(field("count(1) as total")).from(table(name(ccpMemAll)).as("ccp_mem_all")).where(memCond1.and("has_unit_own_level='3' and d25_code in ('51','52','53')")).toString());
                put("2", DSL_CONTEXT.select(field("count(1) as total")).from(table(name(ccpMemAll)).as("ccp_mem_all")).where(memCond1.and("has_unit_own_level='3' and d25_code in ('51')")).toString());
            } else {
                put("1", DSL_CONTEXT.select(field("count(1) as total")).from(table(name(ccpMemAll)).as("ccp_mem_all")).where(memCond1.and("has_unit_own_level in ('3','5') and d25_code in ('51','52','53')")).toString());
                put("2", DSL_CONTEXT.select(field("count(1) as total")).from(table(name(ccpMemAll)).as("ccp_mem_all")).where(memCond1.and("has_unit_own_level in ('3','5') and d25_code in ('51')")).toString());
            }
            //3.街道党组织书记		名，其中35岁及以下		名，36至55岁		名，56岁及以上			名；大专及以上名，高中中专及以下		名。
            put("12", DSL_CONTEXT.select(field("count(1) as total")).from(table(name(ccpMemAll)).as("ccp_mem_all")).where(memCond.and("has_unit_own_level_org='1' and d022_code in ('1')")).toString());
            put("13", DSL_CONTEXT.select(field("count(1) as total")).from(table(name(ccpMemAll)).as("ccp_mem_all")).where(memCond.and("has_unit_own_level_org='1' and d022_code in ('1') and age <= 35")).toString());
            put("14", DSL_CONTEXT.select(field("count(1) as total")).from(table(name(ccpMemAll)).as("ccp_mem_all")).where(memCond.and("has_unit_own_level_org='1' and d022_code in ('1') and age > 35 and age<=55")).toString());
            put("15", DSL_CONTEXT.select(field("count(1) as total")).from(table(name(ccpMemAll)).as("ccp_mem_all")).where(memCond.and("has_unit_own_level_org='1' and d022_code in ('1') and age > 55")).toString());
            put("16", DSL_CONTEXT.select(field("count(1) as total")).from(table(name(ccpMemAll)).as("ccp_mem_all")).where(memCond.and("has_unit_own_level_org='1' and d022_code in ('1') and (d07_code like '1%' or d07_code like '2%' or d07_code like '3%')")).toString());
            put("17", DSL_CONTEXT.select(field("count(1) as total")).from(table(name(ccpMemAll)).as("ccp_mem_all")).where(memCond.and("has_unit_own_level_org='1' and d022_code in ('1') and d07_code in ('4', '5', '6', '7', '8', '9')")).toString());
        }};
        Map<String, CompletableFuture<Number>> res = new HashMap<>();
        stringMap.forEach((key, value) -> res.put(key, CompletableFuture.supplyAsync(() -> EsKit.findListBySql(value), executor)));
        res.forEach((key, value) -> {
            try {
                Html53Count2024.setReplenishMapValue(key, Objects.nonNull(value.get()) ? value.get().intValue() : 0, result);
            } catch (Exception e) {
                System.out.println("补充资料统计异常：");
            }
        });
        executor.shutdown();

        //2.街道干部		人，其中35岁及以下		人，36至55岁		人，56岁及以		人；大专及以上		人，高中中专及以下		人；公务员		人，事业人员		人，其他身份		人。
        SelectConditionStep<Record1<Object>> ccp_unit_all = DSL_CONTEXT
                .select(field("sum(street_cadres) street_cadres,sum(age35_below) age35_below,sum(age36_to_age55) age36_to_age55,sum(age_56_above) age_56_above,sum(college_degree_above) college_degree_above," +
                        "sum(secondary_school_below) secondary_school_below,sum(street_cadres_civil) street_cadres_civil,sum(street_cadres_institutions) street_cadres_institutions,sum(cadre_other)cadre_other"))
                .from(table(name(ccpUnitAll)).as("ccp_unit_all")).where(this.getUnitListCondition(orgCode, orgLevelCode))
                .and(field(name("d04_code")).like("911"));
        List<Record> records2 = EsKit.findBySql(ccp_unit_all.toString()).toRecord();
        if (CollUtil.isNotEmpty(records2)) {
            Record record = records2.get(0);
            Html53Count2024.setReplenishMapValue("3", record.getInt("street_cadres"), result);
            Html53Count2024.setReplenishMapValue("4", record.getInt("age35_below"), result);
            Html53Count2024.setReplenishMapValue("5", record.getInt("age36_to_age55"), result);
            Html53Count2024.setReplenishMapValue("6", record.getInt("age_56_above"), result);
            Html53Count2024.setReplenishMapValue("7", record.getInt("college_degree_above"), result);
            Html53Count2024.setReplenishMapValue("8", record.getInt("secondary_school_below"), result);
            Html53Count2024.setReplenishMapValue("9", record.getInt("street_cadres_civil"), result);
            Html53Count2024.setReplenishMapValue("10", record.getInt("street_cadres_institutions"), result);
            Html53Count2024.setReplenishMapValue("11", record.getInt("cadre_other"), result);
        }
        return result;
    }


    public Condition getMemListCondition(String orgCode, String orgLevelCode) {
        return noCondition().and("delete_time is null and d08_code in ('1','2')").and(new MemAllCondition2024().create(orgCode, orgLevelCode));
    }

    /**
     * 党员+非本单位党员(政治面貌为 14中共党员的)
     * @param orgCode
     * @param orgLevelCode
     * @return
     */
    public Condition getMem7ListCondition(String orgCode, String orgLevelCode) {
        return noCondition().and("delete_time is null and (d08_code in ('1','2') OR (d08_code='3' and d89_code='14')) ").and(new MemAllCondition2024().create(orgCode, orgLevelCode));
    }

    /**
     * 党员+非本单位党员(政治面貌为 14中共党员的)
     */
    public Condition getMem12Condition(String orgCode, String orgLevelCode) {
        return noCondition().and("delete_time is null and (d08_code in ('1','2') OR (d08_code='3' and d89_code='14'))").and(new MemAllCondition2024().create(orgCode, orgLevelCode));
    }

    public Condition getMemListCondition() {
        return noCondition().and("delete_time is null and d08_code in ('1','2')");
    }

    public Condition getDevelopLogListCondition(String orgCode, String orgLevelCode, String sqlStr) {
        return noCondition().and("delete_time is null and d08_code ='3'").and(sqlStr).and(new DevelopStepLogAllCondition2024().create(orgCode, orgLevelCode));
    }

    public Condition getDevelopLogListCondition(String sqlStr) {
        return noCondition().and("delete_time is null and d08_code ='3'").and(sqlStr);
    }

    public Condition getUnitListCondition(String orgCode, String orgLevelCode) {
        return noCondition().and("delete_time is null and is_legal=1 ").and(new UnitAllCondition2024().create(orgCode, orgLevelCode));
    }

    public Condition getOrgListCondition(String orgCode, String orgLevelCode) {
        return noCondition().and("delete_time is null and (is_dissolve is null or is_dissolve !=1)").and(new OrgAllCondition2024().create(orgCode, orgLevelCode));
    }

    @Override
    public Map<String, Object> doReplenishPegging(PeggingPara peggingPara) throws Exception {
        if (StrUtil.equalsAny(peggingPara.getRowIndex(), "3", "4", "5", "6", "7", "8", "9", "10", "11")) {
            Condition condition = this.getUnitListCondition(peggingPara.getOrgCode(), peggingPara.getOrgLevelCode())
                    .and(field(name("d04_code"), String.class).eq("911")).and(this.getRowUnitCondition(peggingPara.getRowIndex()));

            UnitAllCondition2024 unitAllCondition = new UnitAllCondition2024();
            return Html53Count2024.getReportPageResult(peggingPara, unitAllCondition.getTableName(), condition, unitAllCondition.getLevelCodeField());
        }
        Condition condition = this.getRowCondition(peggingPara.getOrgCode(), peggingPara.getOrgLevelCode(), peggingPara.getRowIndex(), false);

        MemAllCondition2024 memAllCondition = new MemAllCondition2024();
        return Html53Count2024.getReportPageResult(peggingPara, memAllCondition.getTableName(), condition, memAllCondition.getLevelCodeField());
    }

    public Condition getRowUnitCondition(String rowIndex) {
        Condition condition = noCondition();
        if (StrUtil.equals(rowIndex, "3")) {
            condition = condition.and("street_cadres is not null and street_cadres>0");
        } else if (StrUtil.equals(rowIndex, "4")) {
            condition = condition.and("age35_below is not null and age35_below>0");
        } else if (StrUtil.equals(rowIndex, "5")) {
            condition = condition.and("age36_to_age55 is not null and age36_to_age55>0");
        } else if (StrUtil.equals(rowIndex, "6")) {
            condition = condition.and("age_56_above is not null and age_56_above>0");
        } else if (StrUtil.equals(rowIndex, "7")) {
            condition = condition.and("college_degree_above is not null and college_degree_above>0");
        } else if (StrUtil.equals(rowIndex, "8")) {
            condition = condition.and("secondary_school_below is not null and secondary_school_below>0");
        } else if (StrUtil.equals(rowIndex, "9")) {
            condition = condition.and("street_cadres_civil is not null and street_cadres_civil>0");
        } else if (StrUtil.equals(rowIndex, "10")) {
            condition = condition.and("street_cadres_institutions is not null and street_cadres_institutions>0");
        } else if (StrUtil.equals(rowIndex, "11")) {
            condition = condition.and("cadre_other is not null and cadre_other>0");
        }
        return condition;
    }

    /**
     * 反查条件
     *
     * @param rowIndex 序号
     * @return 查询条件
     */
    public Condition getRowCondition(String orgCode, String orgLevelCode, String rowIndex, boolean isBackups) {
        if (StrUtil.equalsAny(rowIndex, "1", "2")) {
            Condition condition = getMem12Condition(orgCode, orgLevelCode);
            if (isBackups) {
                condition = condition.and("has_unit_own_level='3'");
            } else {
                condition = condition.and("has_unit_own_level in ('3','5')");
            }
            if ("1".equals(rowIndex)) {
                return condition.and(field(name("d25_code"), String.class).in("51", "52", "53"));
            } else if ("2".equals(rowIndex)) {
                return condition.and(field(name("d25_code"), String.class).in("51"));
            }
        } else if (StrUtil.equalsAny(rowIndex, "12", "13", "14", "15", "16", "17")) {
            Condition condition = getMem7ListCondition(orgCode, orgLevelCode).and("has_unit_own_level_org='1' and d022_code in ('1')");
            if ("12".equals(rowIndex)) {
                return condition;
            } else if ("13".equals(rowIndex)) {
                return condition.and(field(name("age")).lessOrEqual(35));
            } else if ("14".equals(rowIndex)) {
                return condition.and(field(name("age")).greaterOrEqual(36).and(field(name("age")).lessOrEqual(55)));
            } else if ("15".equals(rowIndex)) {
                return condition.and(field(name("age")).greaterOrEqual(56));
            } else if ("16".equals(rowIndex)) {
                return condition.and("d07_code like '1%' or d07_code like '2%' or d07_code like '3%'");
            } else if ("17".equals(rowIndex)) {
                return condition.and(field(name("d07_code"), String.class).in("4", "5", "6", "7", "8", "9"));
            }
        } else {
            return noCondition().and("1=0");
        }
        return noCondition().and("1=0");
    }


}
