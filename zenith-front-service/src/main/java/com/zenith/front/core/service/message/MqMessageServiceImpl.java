package com.zenith.front.core.service.message;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.zenith.front.api.message.IMqMessageService;
import com.zenith.front.api.message.IMqMessagesService;
import com.zenith.front.common.kit.StrKit;
import com.zenith.front.core.job.BaseMessageConstant;
import com.zenith.front.dao.mapper.message.MqMessageMapper;
import com.zenith.front.dao.mapper.message.MqMessagesMapper;
import com.zenith.front.model.message.OutMessage;
import com.zenith.front.model.message.Status;
import com.zenith.front.model.bean.MqMessage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.zenith.front.model.bean.MqMessages;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <p>
 * 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-07-26
 */
@Service
public class MqMessageServiceImpl extends ServiceImpl<MqMessageMapper, MqMessage> implements IMqMessageService {

    @Resource
    private MqMessageMapper mqMessageMapper;
    @Resource
    private IMqMessagesService mqMessagesService;
    @Resource
    private MqMessagesMapper mqMessagesMapper;

    private static final List<String> TYPES = Arrays.asList("9", "10", "11", "12");

    @Override
    public OutMessage<Object> findList(Integer pageNum, Integer pageSize, String orgCode, String account) {
        Page<MqMessage> page = new Page<>(pageNum, pageSize);
        LambdaQueryWrapper<MqMessage> lambdaQueryWrapper = new LambdaQueryWrapper<MqMessage>()
                .isNull(MqMessage::getDeleteTime)
                .and(
                        t -> t
                                .and(
                                        q -> q.likeRight(MqMessage::getOrgCode, orgCode).notIn(MqMessage::getType, TYPES)
                                )
                                .or(
                                        q -> q.eq(MqMessage::getOrgCode, orgCode).in(MqMessage::getType, TYPES)
                                )

                )
                .and(
                        t -> t
                                .notLike(MqMessage::getConsumer, account)
                                .or()
                                .isNull(MqMessage::getConsumer)
                )
                .orderByAsc(MqMessage::getSortTime)
                .orderByDesc(MqMessage::getOrgCode)
                .select(MqMessage::getCode, MqMessage::getType, MqMessage::getMessage, MqMessage::getDesignated, MqMessage::getSelf, MqMessage::getConsumer);
        page(page, lambdaQueryWrapper);
        List<MqMessage> records = page.getRecords();
        if (CollUtil.isNotEmpty(records)) {
            for (MqMessage record : records) {
                String type = record.getType();
                if (BaseMessageConstant.MESSAGE_MAP.containsKey(type)) {
                    record.setType(BaseMessageConstant.MESSAGE_MAP.get(type));
                }
            }
        }
        return new OutMessage<>(Status.SUCCESS, page);
    }

    @Override
    public OutMessage<Object> cat(List<String> codeList, boolean ignore, String orgCode, String account) {
        LambdaQueryWrapper<MqMessage> queryWrapper = new LambdaQueryWrapper<MqMessage>()
                .isNull(MqMessage::getDeleteTime)
                .in(MqMessage::getCode, codeList)
                .select(MqMessage::getCode, MqMessage::getConsumer, MqMessage::getOrgCode);
        List<MqMessage> mqMessageList = list(queryWrapper);
        if (CollUtil.isEmpty(mqMessageList)) {
            return new OutMessage<>(Status.SUCCESS);
        }
        List<MqMessage> messageList = new ArrayList<>();
        for (MqMessage message : mqMessageList) {
            String messageCode = message.getCode();

            MqMessage db = new MqMessage();
            db.setCode(messageCode);
            String consumer = message.getConsumer();
            //拼接消费者 逗号分隔 zhangsan,lisi,wangwu
            StringBuilder builder = new StringBuilder(Objects.isNull(consumer) ? "" : consumer);
            if (StringUtils.hasText(consumer)) {
                builder.append(",");
            }
            builder.append(account);
            db.setConsumer(Arrays.stream(builder.toString().split(",")).distinct().collect(Collectors.joining(",")));
            //是否本人查看
            if (StrUtil.equals(message.getOrgCode(), orgCode)) {
                db.setSelf(true);
            }
            messageList.add(db);
        }
        boolean flag = updateBatchById(messageList);
        if (flag) {
            List<MqMessages> mqMessagesList = new ArrayList<>();
            for (MqMessage mqMessages : messageList) {

                MqMessages messages = new MqMessages();
                messages.setCode(StrKit.getRandomUUID());
                messages.setMessageCode(mqMessages.getCode());
                messages.setHasConsumed(1);
                messages.setConsumedTime(new Date());
                messages.setConsumer(account);
                if (ignore) {
                    messages.setIgnore(1);
                    messages.setIgnoredBy(account);
                }
                mqMessagesList.add(messages);
            }
            mqMessagesService.saveBatch(mqMessagesList);
        }
        return new OutMessage<>(flag ? Status.SUCCESS : Status.FAIL);
    }

    @Override
    public void deleteByType(String type) {
        if (BaseMessageConstant.MESSAGE_MAP.containsKey(type)) {
            mqMessageMapper.deleteByType(type);
            mqMessagesMapper.deleteByType(type);
        }
    }
}
