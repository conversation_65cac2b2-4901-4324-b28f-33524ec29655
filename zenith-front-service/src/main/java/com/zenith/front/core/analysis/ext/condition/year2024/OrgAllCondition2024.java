package com.zenith.front.core.analysis.ext.condition.year2024;

import com.b1809.analysis.extend.GenSqlConditionFuc;
import org.jooq.Condition;
import org.jooq.Field;

import static org.jooq.impl.DSL.field;
import static org.jooq.impl.DSL.name;

/**
 * 党组织
 *
 * <AUTHOR>
 * @Date 2021/8/16 15:49
 * @Version 1.0
 */
public class OrgAllCondition2024 implements GenSqlConditionFuc {

    @Override
    public String getTableName() {
        return "ccp_org_all_2024";
    }

    @Override
    public Condition create(String orgCode, String orgLevelCode) {
        return field(name("org_code"), String.class).like(orgLevelCode + "%");
    }

    @Override
    public Field<?> getLevelCodeField() {
        return field(name( "org_code"), String.class);
    }

    public Field<?> getLevelCodeFieldAppraisal() {
        return field(name( "appraisal_org_code"), String.class);
    }

}
