package com.zenith.front.core.service.unit;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.thread.ThreadUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.zenith.front.api.mem.IMemReportService;
import com.zenith.front.api.unit.IUnitResidentService;
import com.zenith.front.common.constant.CommonConstant;
import com.zenith.front.common.constant.DictConstant;
import com.zenith.front.common.kit.CodeUtil;
import com.zenith.front.common.kit.StrKit;
import com.zenith.front.core.cache.CacheUtils;
import com.zenith.front.dao.mapper.unit.UnitResidentMapper;
import com.zenith.front.model.dto.PrimaryKeyDTO;
import com.zenith.front.model.dto.UnitResidentDTO;
import com.zenith.front.model.dto.UnitResidentUpdateDTO;
import com.zenith.front.model.message.OutMessage;
import com.zenith.front.model.message.Status;
import com.zenith.front.model.bean.UnitResident;
import com.zenith.front.model.vo.DecryptUnitResidentMap;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

import static com.zenith.front.common.constant.CommonConstant.ONE;

/**
 * <p>
 * 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-04-11
 */
@Service
public class UnitResidentServiceImpl extends ServiceImpl<UnitResidentMapper, UnitResident> implements IUnitResidentService {

    @Resource
    private IMemReportService iMemReportService;

    @Override
    public OutMessage<Object> doSave(UnitResidentDTO data, String account) {
//        String electCode = data.getElectCode();
//        UnitResidentElect unitResidentElect = unitResidentElectService.findByCode(electCode);
//        if (Objects.isNull(unitResidentElect)) {
//            return new OutMessage<>(Status.ELECT_NOT_EXIST);
//        }
        UnitResident unitResident = new UnitResident();
        unitResident.setCode(StrKit.getRandomUUID());
        unitResident.setEsId(CodeUtil.getEsId());
//        unitResident.setElectCode(unitResidentElect.getCode());
        unitResident.setUnitCode(data.getUnitCode());
        unitResident.setMemCode(data.getMemCode());
        unitResident.setMemName(data.getMemName());
        unitResident.setd139Code(data.getD139Code());
        unitResident.setd139Name(data.getD139Name());
        unitResident.setd140Code(data.getD140Code());
        unitResident.setd140Name(data.getD140Name());
        unitResident.setd141Code(data.getD141Code());
        unitResident.setd141Name(data.getD141Name());
        unitResident.setSexCode(data.getSexCode());
        unitResident.setSexName(data.getSexName());
        unitResident.setMemIdcard(data.getMemIdcard());
        unitResident.setMemBirthday(data.getMemBirthday());
        unitResident.setd07Code(data.getD07Code());
        unitResident.setd07Name(data.getD07Name());
        unitResident.setStartDate(data.getStartDate());
        unitResident.setHasVillageTransferStudent(data.getHasVillageTransferStudent());
        unitResident.setD144Code(data.getD144Code());
        unitResident.setD144Name(data.getD144Name());
        unitResident.setCreateTime(new Date());
        unitResident.setTimestamp(new Date());
        unitResident.setUpdateAccount(account);
        unitResident.setResidentDate(data.getResidentDate());
        unitResident.setDispatchPosition(data.getDispatchPosition());
        unitResident.setD197Code(data.getD197Code());
        unitResident.setD197Name(data.getD197Name());
        boolean flag = save(unitResident);
        if (flag) {
            ThreadUtil.execAsync(() -> iMemReportService.syncUnitResident(data.getUnitCode()));
        }
        return new OutMessage<>(flag ? Status.SUCCESS : Status.FAIL, unitResident);
    }

    @Override
    public OutMessage<Object> doDel(PrimaryKeyDTO data, String account) {
        UnitResident dbResident = this.findByCodeOrId(data.getId(), data.getCode());
        if (Objects.isNull(dbResident)) {
            return new OutMessage<>(Status.SUCCESS);
        }
        UnitResident unitResident = new UnitResident();
        unitResident.setId(dbResident.getId());
        unitResident.setDeleteTime(new Date());
        unitResident.setUpdateAccount(account);
        boolean flag = updateById(unitResident);
        if (flag) {
            ThreadUtil.execAsync(() -> iMemReportService.syncUnitResident(dbResident.getUnitCode()));
        }
        return new OutMessage<>(flag ? Status.SUCCESS : Status.FAIL);
    }

    private UnitResident findByCodeOrId(Long id, String code) {
        LambdaQueryWrapper<UnitResident> wrapper = Wrappers.lambdaQuery();
        if(Objects.nonNull(id)) {
            wrapper.eq(UnitResident::getId, id);
        } else {
            wrapper.eq(UnitResident::getCode, code);
        }
        wrapper.isNull(UnitResident::getDeleteTime);
        return getOne(wrapper,false);
    }

    @Override
    public OutMessage<Object> doUpdate(UnitResidentUpdateDTO data, String account) {
//        String electCode = data.getElectCode();
//        UnitResidentElect unitResidentElect = unitResidentElectService.findByCode(electCode);
//        if (Objects.isNull(unitResidentElect)) {
//            return new OutMessage<>(Status.ELECT_NOT_EXIST);
//        }


        UnitResident resident = this.findByCodeOrId(data.getId(), data.getCode());
        if (Objects.isNull(resident)) {
            return new OutMessage<>(Status.THE_PERSONNEL_OF_THE_CURRENT_SESSION_HAVE_BEEN_DELETED);
        }
        UnitResident unitResident = new UnitResident();
        unitResident.setId(resident.getId());
        // 不是本单位党员的赋值空字符串更新数据
        unitResident.setMemCode(!Objects.equals(data.getD139Code(), "1") ? "" : data.getMemCode());
        unitResident.setMemName(data.getMemName());
        unitResident.setd139Code(data.getD139Code());
        unitResident.setd139Name(data.getD139Name());
        unitResident.setd140Code(data.getD140Code());
        unitResident.setd140Name(data.getD140Name());
        unitResident.setd141Code(data.getD141Code());
        unitResident.setd141Name(data.getD141Name());
        unitResident.setSexCode(data.getSexCode());
        unitResident.setSexName(data.getSexName());
        unitResident.setMemIdcard(data.getMemIdcard());
        unitResident.setMemBirthday(data.getMemBirthday());
        unitResident.setd07Code(data.getD07Code());
        unitResident.setd07Name(data.getD07Name());
        unitResident.setStartDate(data.getStartDate());
        unitResident.setEndDate(data.getEndDate());
        unitResident.setHasVillageTransferStudent(data.getHasVillageTransferStudent());
        unitResident.setD144Code(data.getD144Code());
        unitResident.setD144Name(data.getD144Name());
        unitResident.setUpdateTime(new Date());
        unitResident.setTimestamp(new Date());
        unitResident.setUpdateAccount(account);
        unitResident.setResidentDate(data.getResidentDate());
        unitResident.setDispatchPosition(data.getDispatchPosition());
        unitResident.setD197Name(data.getD197Name());
        unitResident.setD197Code(data.getD197Code());
        boolean flag = updateById(unitResident);
        if (flag) {
            ThreadUtil.execAsync(() -> iMemReportService.syncUnitResident(resident.getUnitCode()));
        }
        return new OutMessage<>(flag ? Status.SUCCESS : Status.FAIL);
    }

    @Override
    public OutMessage<Object> doList(Integer pageNum, Integer pageSize, String unitCode, String leave) {
//        UnitResidentElect unitResidentElect = unitResidentElectService.findByCode(electCode);
//        if (Objects.isNull(unitResidentElect)) {
//            return new OutMessage<>(Status.ELECT_NOT_EXIST);
//        }
        Page<UnitResident> page = this.page(pageNum, pageSize, unitCode, leave);
        return new OutMessage<>(Status.SUCCESS, page);
    }

    /**
     * 根据届次code查询
     *
     * @param pageNum  页码
     * @param pageSize 每页显示条数
     * @param unitCode unitCode
     * @return
     */
    public Page<UnitResident> page(Integer pageNum, Integer pageSize, String unitCode, String leave) {
        Page<UnitResident> page = new Page<>(pageNum, pageSize);
        LambdaQueryWrapper<UnitResident> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(UnitResident::getUnitCode, unitCode);
        queryWrapper.isNull(UnitResident::getDeleteTime);
        // 1 查询本届离任领导成员  0 查询本届在任领导成员
        if (CommonConstant.ONE.equals(leave)) {
            queryWrapper.isNotNull(UnitResident::getEndDate);
        } else if (CommonConstant.ZERO.equals(leave)) {
            queryWrapper.isNull(UnitResident::getEndDate);
        }
        queryWrapper.orderByDesc(UnitResident::getCreateTime);
        page(page, queryWrapper);
        return page;
    }

    @Override
    public OutMessage<Object> backOut(PrimaryKeyDTO data, String account) {
        UnitResident resident = getById(data.getId());
        if (Objects.isNull(resident)) {
            return new OutMessage<>(Status.THE_PERSONNEL_OF_THE_CURRENT_SESSION_HAVE_BEEN_DELETED);
        }
        if (Objects.isNull(resident.getEndDate())) {
            return new OutMessage<>(Status.SUCCESS);
        }
        final LambdaUpdateWrapper<UnitResident> updateWrapper = Wrappers.lambdaUpdate();
        updateWrapper.set(UnitResident::getEndDate, null);
        boolean update = update(null, updateWrapper.eq(UnitResident::getId, resident.getId()));
        if (update) {
            ThreadUtil.execAsync(() -> iMemReportService.syncUnitResident(resident.getUnitCode()));
        }
        return new OutMessage<>(update ? Status.SUCCESS : Status.FAIL);
    }


    @Override
    public List<UnitResident> findByElect(String electCode) {
        LambdaQueryWrapper<UnitResident> wrapper = new LambdaQueryWrapper<UnitResident>().select()
                .eq(UnitResident::getElectCode, electCode)
                .isNull(UnitResident::getDeleteTime).isNull(UnitResident::getEndDate)
                .orderByDesc(UnitResident::getCreateTime).orderByDesc(UnitResident::getId);
        return list(wrapper);
    }

    @Override
    public List<UnitResident> findByUnitCode(String unitCode) {
        LambdaQueryWrapper<UnitResident> wrapper = new LambdaQueryWrapper<UnitResident>().select()
                .eq(UnitResident::getUnitCode, unitCode)
                .isNull(UnitResident::getDeleteTime)
                .orderByDesc(UnitResident::getCreateTime).orderByDesc(UnitResident::getId);
        return list(wrapper);
    }

    @Override
    public List<Map<String, Object>> findVillageCadresList() {
        List<DecryptUnitResidentMap<String, Object>> mapList = baseMapper.findVillageCadresList();
        if (CollUtil.isEmpty(mapList)) {
            return Collections.emptyList();
        }
        //民族
        Map<String, String> d06Map = CacheUtils.getDic(DictConstant.DICT_D06).stream().filter(t -> Objects.nonNull(t.get("key"))).collect(Collectors.toMap(record -> record.get("key"), record1 -> record1.get("name"), (a, b) -> b, LinkedHashMap::new));
        //学历
        Map<String, String> d07Map = CacheUtils.getDic(DictConstant.DICT_D07).stream().filter(t -> Objects.nonNull(t.get("key"))).collect(Collectors.toMap(record -> record.get("key"), record1 -> record1.get("name"), (a, b) -> b, LinkedHashMap::new));
        //籍贯
        Map<String, String> d48Map = CacheUtils.getDic(DictConstant.DICT_D48).stream().filter(t -> Objects.nonNull(t.get("key"))).collect(Collectors.toMap(record -> record.get("key"), record1 -> record1.get("name"), (a, b) -> b, LinkedHashMap::new));

        List<Map<String, Object>> records = new ArrayList<>();
        for (Map<String, Object> record : mapList) {
            Map<String, Object> map = new HashMap<>(50);
            String d139Code = (String) record.get("d139_code");
            boolean flag = StrUtil.equals(d139Code, ONE);
            //人员统一标识符
            map.put("a0000", record.get("code"));
            //姓名
            map.put("a0101", record.get("mem_name"));
            //现工作单位及职务简称
            map.put("a0192", record.get("dispatch_position"));
            //现工作单位及职务全称
            map.put("a0192a", record.get("dispatch_position"));
            //状态0完全删除1正常2历史库3离退人员4临时数据
            map.put("status", "1");
            //驻村开始时间
            map.put("stationStartTime", record.get("start_date"));
            //驻村结束时间
            map.put("stationEndTime", record.get("end_date"));
            //驻村来源
            map.put("stationSource", record.get("d141_code"));
            //创建时间
            map.put("createTime", record.get("create_time"));
            //机构层级码
            map.put("orgLevelCode", record.get("main_unit_org_code"));
            //驻村机构名称
            map.put("orgName", record.get("unit_name"));
            map.put("orgId", record.get("main_org_code"));
            //后备干部
            map.put("isReserve", 0);
            //任职时间
            map.put("villageTakeTime", record.get("start_date"));
            //是否担任第一书记
            map.put("isFirstSecretary", Objects.equals(record.get("d140_code"), "1") ? 1 : 0);
            //所在单位code
            map.put("unitCode", record.get("unit_code"));

            //本党组织内党员
            if (flag) {
                //入党时间
                if (Objects.nonNull(record.get("join_org_date"))) {
                    map.put("a0144", ((Date) record.get("join_org_date")).getTime());
                    map.put("a0140", record.get("join_org_date").toString());
                }
                //参加工作时间
                if (Objects.nonNull(record.get("join_work_date"))) {
                    map.put("a0134", ((Date) record.get("join_work_date")).getTime());
                }
                //籍贯
                if (Objects.nonNull(record.get("d48_code"))) {
                    map.put("a0111", record.get("d48_code"));
                    map.put("a0111a", d48Map.get(record.get("d48_code").toString()));
                }
                if (Objects.nonNull(record.get("d48_code"))) {
                    //民族
                    map.put("a0117", record.get("d06_code"));
                    map.put("a0117Name", d06Map.get(record.get("d06_code").toString()));
                }

            }
            //性别 1男 2女
            if (Objects.nonNull(record.get("mem_sex_code"))) {
                record.put("a0104", flag ? record.get("mem_sex_code") : record.get("sex_code"));
                record.put("a0104a", Objects.equals(record.get("a0104"), "1") ? "男" : (Objects.equals(record.get("a0104"), "2") ? "女" : "未知"));
            }
            //全日制教育学历学位
            //在职教育学历学位
            Object d07Code = flag ? record.get("mem_d07_code") : record.get("d07_code");
            map.put("qrzxl", Objects.nonNull(d07Code) ? d07Map.get(d07Code.toString()) : null);
            //公民身份号码
            map.put("a0184", flag ? record.get("idcard") : record.get("mem_idcard"));
            //出生日期
            map.put("a0107", flag ? ((Date) record.get("birthday")).getTime() : ((Date) record.get("mem_birthday")).getTime());

            records.add(map);
        }
        return records;
    }
}
