package com.zenith.front.core.service.sync;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONArray;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.jfinal.kit.StrKit;
import com.zenith.front.api.dict.IDictService;
import com.zenith.front.api.mem.IMemAllInfoService;
import com.zenith.front.api.mem.IMemService;
import com.zenith.front.api.org.IOrgAllService;
import com.zenith.front.api.org.IOrgCommitteeService;
import com.zenith.front.api.org.IOrgService;
import com.zenith.front.api.org.IOrgSlackService;
import com.zenith.front.api.report.IStatisticsYearService;
import com.zenith.front.api.unit.*;
import com.zenith.front.common.constant.CommonConstant;
import com.zenith.front.core.service.unit.UnitCommunityServiceImpl;
import com.zenith.front.dao.mapper.mem.MemDevelopMapper;
import com.zenith.front.dao.mapper.mem.MemMapper;
import com.zenith.front.dao.mapper.org.OrgCommitteeElectMapper;
import com.zenith.front.dao.mapper.org.OrgPartyMapper;
import com.zenith.front.dao.mapper.unit.*;
import com.zenith.front.model.bean.*;
import com.zenith.front.model.custom.Record;
import com.zenith.front.model.dto.UnitIncomeSubDto;
import com.zenith.front.model.message.OutMessage;
import com.zenith.front.model.message.Status;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Date 2021/10/25 17:14
 * @Version 1.0
 */
@Service
@Slf4j
public class SyncUnitService {

    @Resource
    private IUnitService iUnitService;
    @Resource
    private UnitMapper unitMapper;
    @Resource
    private UnitCommunityMapper communityMapper;
    @Resource
    private UnitExtendMapper unitExtendMapper;
    @Resource
    private IUnitExtendService iUnitExtendService;
    @Resource
    private UnitCountrysideMapper countrysideMapper;
    @Resource
    private UnitSecondaryMapper secondaryMapper;
    @Resource
    private IOrgService orgService;
    @Resource
    private IOrgAllService iOrgAllService;
    @Resource
    private UnitOrgLinkedMapper linkedMapper;
    @Resource
    private MemDevelopMapper memDevelopMapper;
    @Resource
    private MemMapper memMapper;
    @Resource
    private IMemService iMemService;
    @Resource
    private OrgCommitteeElectMapper orgElectMapper;
    @Resource
    private UnitCommitteeElectMapper unitElectMapper;
    @Resource
    private OrgPartyMapper partyMapper;
    @Resource
    private UnitAllMapper unitAllMapper;
    @Resource
    private UnitStreetsCadresMapper unitStreetsCadresMapper;
    @Resource
    private UnitCollectiveEconomicMapper unitCollectiveEconomicMapper;
    @Resource
    private UnitIncomeMapper unitIncomeMapper;
    @Resource
    private IUnitAllService unitAllService;
    @Resource
    private IDictService dictService;
    @Resource
    private IUnitOrgLinkedService unitOrgLinkedService;
    @Resource
    private IOrgSlackService iOrgSlackService;
    @Resource
    private IMemAllInfoService iMemAllInfoService;
    @Resource
    private IUnitOrgLinkedService iUnitOrgLinkedService;
    @Resource
    private IOrgCommitteeService iOrgCommitteeService;
    @Resource
    private IUnitCommitteeService iUnitCommitteeService;
    @Resource
    private SyncMemServiceImpl syncMemService;
    @Resource
    private IUnitResidentService iUnitResidentService;
    @Resource
    private IStatisticsYearService iStatisticsYearService;
    @Resource
    private IUnitCitySituationService iUnitCitySituationService;


    /**
     * 设置单位基本信息到宽表
     *
     * @param code
     */
    public OutMessage setBaseUnit(String code, String type) {
        /*if (!CustomConstant.SYNC_DB_ENABLE || StrUtil.isEmpty(code)) {
            return new OutMessage<>(Status.SUCCESS);
        }*/
        Unit unit = unitMapper.selectOne(new QueryWrapper<Unit>().lambda().eq(Unit::getCode, code));
        if (Objects.isNull(unit)) {
            log.error("同步失败！unit对象不存在，code= " + code);
            return new OutMessage<>(Status.SUCCESS);
        }
        boolean isExist = true;
        UnitAll information = unitAllMapper.selectOne(new QueryWrapper<UnitAll>().eq("code", code).last(" limit 1"));
        if (Objects.isNull(information)) {
            information = new UnitAll();
            BeanUtils.copyProperties(unit, information, "id");
            isExist = false;
        }
        switch (type) {
            //单位基础信息
            case "1":
                BeanUtils.copyProperties(unit, information, "id");
                //关联单位扩展信息 todo 该信息放到扩展信息中
//                this.setUnitExtendSecretary(unit, information);
                //同步在岗职工党员和专业技术党员数
                this.partyMember(unit, information);
                //医院内设机构同步
                this.insideHospital(unit, information);
                // 科研院（所）内设机构统计
                this.researchYsnsjgdzbCount(unit, information);
                // todo 单位扩展信息迁移问题：有些字段在unit中，上面copy方法导致空值覆盖，这里重新赋值给unitAll，防止后面被删除
                this.setUnitExtendSecretary(unit, information);
                break;
            //关联组织当代会信息 x（单位扩展信息同步）
            case "2":
                //关联单位扩展信息
                this.setUnitExtendSecretary(unit, information);
//                this.setUnitExtend(unit.getCode(), information);
                break;
            //社区工作者扩展信息同步
            case "3":
                this.setCommunityWorker(unit.getCode(), information);
                break;
            //二级院校信息同步
            case "4":
                this.setColleges(unit.getCode(), information);
                break;
            //同步党组性质党委信息
            case "5":
                this.committeeNature(unit.getCode(), information);
                break;

            //同步城市街道干部信息
            case "6":
                this.unitStreetsCadres(unit.getCode(), information);
                break;
            //同步集体经济情况
            case "7":
                this.unitCollectiveEconomic(unit.getCode(), information);
                break;
            default:
                return new OutMessage<>(Status.PARA_ERROR);
        }
        if (!isExist) {
            unitAllMapper.insert(information);
        } else {
            if (StrUtil.equals(type, "2")) {
                // todo 2023-12-20 单位扩展信息编辑执行处理字段
                saveOrUpdateUnitCommunity(information);
                this.setHasOrgSlackVillage(code, unit);
            } else {
                saveOrUpdateUnitAllValue(information);
            }
            //更新党组关联信息
            partyMapper.update(null, new LambdaUpdateWrapper<OrgParty>().set(OrgParty::getOrgCode, unit.getCreateOrgCode()).set(OrgParty::getPartyOrgCode, unit.getCreateUnitOrgCode())
                    .set(OrgParty::getUnitName, unit.getName()).set(OrgParty::getD35Code, unit.getD35Code()).eq(OrgParty::getUnitCode, code));
        }
        return new OutMessage<>(Status.SUCCESS);
    }

    /**
     * 调整hhl之前设置单位信息的方法
     */
    private void saveOrUpdateUnitAllValue(UnitAll information) {
        unitAllService.saveOrUpdate(information, Wrappers.<UnitAll>lambdaUpdate()
                .set(Objects.isNull(information.getHasMajorDeputySecretary()), UnitAll::getHasMajorDeputySecretary, null)
                .set(Objects.isNull(information.getOnPostNum()), UnitAll::getOnPostNum, null)
                .set(Objects.isNull(information.getB30A12()), UnitAll::getB30A12, null)
                .set(Objects.isNull(information.getIsVolTeam()), UnitAll::getIsVolTeam, null)
                .set(Objects.isNull(information.getHasVolunteerOrganization()), UnitAll::getHasVolunteerOrganization, null)
                .set(Objects.isNull(information.getD17Code()), UnitAll::getD17Code, null)
                .set(Objects.isNull(information.getD16Code()), UnitAll::getD16Code, null)
                .set(Objects.isNull(information.getD79Code()), UnitAll::getD79Code, null)
                .set(Objects.isNull(information.getTecNum()), UnitAll::getTecNum, null)
                .set(Objects.isNull(information.getBzt610()), UnitAll::getBzt610, null)
                .set(Objects.isNull(information.getD78Code()), UnitAll::getD78Code, null)
                .set(Objects.isNull(information.getIsOrgService()), UnitAll::getIsOrgService, null)
                .set(Objects.isNull(information.getIsDecouplIndustry()), UnitAll::getIsDecouplIndustry, null)
                .set(Objects.isNull(information.getD77Code()), UnitAll::getD77Code, null)
                .set(Objects.isNull(information.getZaigangGaoji()), UnitAll::getZaigangGaoji, null)
                .set(Objects.isNull(information.getTechnicalPersonnel()), UnitAll::getTechnicalPersonnel, null)
                .set(Objects.isNull(information.getPartySeniorTitle()), UnitAll::getPartySeniorTitle, null)
                .set(Objects.isNull(information.getD81Code()), UnitAll::getD81Code, null)
                .set(Objects.isNull(information.getLdtzyyzc()), UnitAll::getLdtzyyzc, null)
                .set(Objects.isNull(information.getIsPartyWorkWrite()), UnitAll::getIsPartyWorkWrite, null)
                .set(Objects.isNull(information.getIsOpenOrgAssess()), UnitAll::getIsOpenOrgAssess, null)
                .set(Objects.isNull(information.getIsLeaderSeparate()), UnitAll::getIsLeaderSeparate, null)
                .set(Objects.isNull(information.getLeaderIsGcdy()), UnitAll::getLeaderIsGcdy, null)
                .set(Objects.isNull(information.getIsLeaderDeputySecretary()), UnitAll::getIsLeaderDeputySecretary, null)
                .set(Objects.isNull(information.getIsSetOrgParty()), UnitAll::getIsSetOrgParty, null)
                .set(Objects.isNull(information.getSecretaryIsInsideLeader()), UnitAll::getSecretaryIsInsideLeader, null)
                .set(Objects.isNull(information.getSjsdtr()), UnitAll::getSjsdtr, null)
                .set(Objects.isNull(information.getIsYearOrgChange()), UnitAll::getIsYearOrgChange, null)
                .set(Objects.isNull(information.getYearDevelopMem()), UnitAll::getYearDevelopMem, null)
                .set(Objects.isNull(information.getHasStandingCommittee()), UnitAll::getHasStandingCommittee, null)
                .set(Objects.isNull(information.getHasReportImplementation()), UnitAll::getHasReportImplementation, null)
                .set(Objects.isNull(information.getHasOfficeProcedure()), UnitAll::getHasOfficeProcedure, null)
                .set(Objects.isNull(information.getSchoolHasReportsLocal()), UnitAll::getSchoolHasReportsLocal, null)
                .set(Objects.isNull(information.getHasSecretaryUniversityCommittee()), UnitAll::getHasSecretaryUniversityCommittee, null)
                .set(Objects.isNull(information.getHasPresidentPartyMember()), UnitAll::getHasPresidentPartyMember, null)
                .set(Objects.isNull(information.getHasDeputyPartySecretary()), UnitAll::getHasDeputyPartySecretary, null)
                .set(Objects.isNull(information.getD95Code()), UnitAll::getD95Code, null)
                .set(Objects.isNull(information.getD95Name()), UnitAll::getD95Name, null)
                .set(Objects.isNull(information.getD109Code()), UnitAll::getD109Code, null)
                .set(Objects.isNull(information.getD109Name()), UnitAll::getD109Name, null)
                .set(Objects.isNull(information.getD111Code()), UnitAll::getD111Code, null)
                .set(Objects.isNull(information.getD111Name()), UnitAll::getD111Name, null)
                .set(Objects.isNull(information.getHasResponsibilitySystem()), UnitAll::getHasResponsibilitySystem, null)
                .set(Objects.isNull(information.getD112Code()), UnitAll::getD112Code, null)
                .set(Objects.isNull(information.getD112Name()), UnitAll::getD112Name, null)
                .set(Objects.isNull(information.getD114Code()), UnitAll::getD114Code, null)
                .set(Objects.isNull(information.getHasPartyWork()), UnitAll::getHasPartyWork, null)
                .set(Objects.isNull(information.getHasFirmLevel()), UnitAll::getHasFirmLevel, null)
                .set(Objects.isNull(information.getD115Code()), UnitAll::getD115Code, null)
                .set(Objects.isNull(information.getHasDirectors()), UnitAll::getHasDirectors, null)
                .set(Objects.isNull(information.getHasChairmanSecretary()), UnitAll::getHasChairmanSecretary, null)
                .set(Objects.isNull(information.getHasProportionateFunding()), UnitAll::getHasProportionateFunding, null)
                .set(Objects.isNull(information.getHasBranchToCatch()), UnitAll::getHasBranchToCatch, null)
                .set(Objects.isNull(information.getHasByLeader()), UnitAll::getHasByLeader, null)
                .set(Objects.isNull(information.getHasSameTreatment()), UnitAll::getHasSameTreatment, null)
                .set(Objects.isNull(information.getHasPublicCompany()), UnitAll::getHasPublicCompany, null)
                .set(Objects.isNull(information.getHasArticlesIncorporation()), UnitAll::getHasArticlesIncorporation, null)
                .set(Objects.isNull(information.getHasPrepositionalProcedure()), UnitAll::getHasPrepositionalProcedure, null)
                .set(Objects.isNull(information.getHasResponsiblePerson()), UnitAll::getHasResponsiblePerson, null)
                .set(Objects.isNull(information.getBranches()), UnitAll::getBranches, null)
                .set(Objects.isNull(information.getPartyOrganizationNum()), UnitAll::getPartyOrganizationNum, null)
                .set(Objects.isNull(information.getHaveBeenEstablished()), UnitAll::getHaveBeenEstablished, null)
                .set(Objects.isNull(information.getPartyMembers()), UnitAll::getPartyMembers, null)
                .set(Objects.isNull(information.getHasNonPublicParty()), UnitAll::getHasNonPublicParty, null)
                .set(Objects.isNull(information.getHasSpecialAgencies()), UnitAll::getHasSpecialAgencies, null)
                .set(Objects.isNull(information.getHasCommunityAccess()), UnitAll::getHasCommunityAccess, null)
                .set(Objects.isNull(information.getHasLowerSocial()), UnitAll::getHasLowerSocial, null)
                .set(Objects.isNull(information.getCommunityBuildingNumber()), UnitAll::getCommunityBuildingNumber, null)
                .set(Objects.isNull(information.getHasCommunityPositions()), UnitAll::getHasCommunityPositions, null)
                .set(Objects.isNull(information.getCommunityOfficeSpace()), UnitAll::getCommunityOfficeSpace, null)
                .set(Objects.isNull(information.getHasParttimeSystem()), UnitAll::getHasParttimeSystem, null)
                .set(Objects.isNull(information.getHasFourTwoOpenWork()), UnitAll::getHasFourTwoOpenWork, null)
                .set(Objects.isNull(information.getHasCommunitySupervisory()), UnitAll::getHasCommunitySupervisory, null)
                .set(Objects.isNull(information.getEmployeesNumber()), UnitAll::getEmployeesNumber, null)
                .set(Objects.isNull(information.getHasInstructorContact()), UnitAll::getHasInstructorContact, null)
                .set(Objects.isNull(information.getHasUnionOrganization()), UnitAll::getHasUnionOrganization, null)
                .set(Objects.isNull(information.getNotTurnedParty()), UnitAll::getNotTurnedParty, null)
                .set(Objects.isNull(information.getHasOrganizationSecretary()), UnitAll::getHasOrganizationSecretary, null)
                .set(Objects.isNull(information.getHasExaminationPower()), UnitAll::getHasExaminationPower, null)
                .set(Objects.isNull(information.getHasCancelInvestmentPromotion()), UnitAll::getHasCancelInvestmentPromotion, null)
                .set(Objects.isNull(information.getHasWorkMechanism()), UnitAll::getHasWorkMechanism, null)
                .set(Objects.isNull(information.getHasIncludedCommittee()), UnitAll::getHasIncludedCommittee, null)
                .set(Objects.isNull(information.getHasGroupServiceCenter()), UnitAll::getHasGroupServiceCenter, null)
                .set(Objects.isNull(information.getHasPartyBuildEndeavor()), UnitAll::getHasPartyBuildEndeavor, null)
                .set(Objects.isNull(information.getHasRepresentative()), UnitAll::getHasRepresentative, null)
                .set(Objects.isNull(information.getHasCountrySecretaryHbgb()), UnitAll::getHasCountrySecretaryHbgb, null)
                .set(Objects.isNull(information.getHasProperSecretary()), UnitAll::getHasProperSecretary, null)
                .set(Objects.isNull(information.getAbsorbedTissueNumber()), UnitAll::getAbsorbedTissueNumber, null)
                .set(Objects.isNull(information.getHasHeadParty()), UnitAll::getHasHeadParty, null)
                .set(Objects.isNull(information.getIncludedFinancial()), UnitAll::getIncludedFinancial, null)
                .set(Objects.isNull(information.getSpecialFundsMasses()), UnitAll::getSpecialFundsMasses, null)
                .set(Objects.isNull(information.getHasCommunityReport()), UnitAll::getHasCommunityReport, null)
                .set(Objects.isNull(information.getHasSecretaryCommittee()), UnitAll::getHasSecretaryCommittee, null)
                .set(Objects.isNull(information.getHasTissueCommittee()), UnitAll::getHasTissueCommittee, null)
                .set(Objects.isNull(information.getHasPropagandaCommittee()), UnitAll::getHasPropagandaCommittee, null)
                .set(Objects.isNull(information.getHasFrontCommittee()), UnitAll::getHasFrontCommittee, null)
                .set(Objects.isNull(information.getCommunityWorkersSalary()), UnitAll::getCommunityWorkersSalary, null)
                .set(Objects.isNull(information.getCommunitySecretarySalary()), UnitAll::getCommunitySecretarySalary, null)
                .set(Objects.isNull(information.getAboveBkEducation()), UnitAll::getAboveBkEducation, null)
                .set(Objects.isNull(information.getAboveYjsEducation()), UnitAll::getAboveYjsEducation, null)
                .set(Objects.isNull(information.getHasSecretaryHighLevel()), UnitAll::getHasSecretaryHighLevel, null)
                .set(Objects.isNull(information.getHasLevelSecretary()), UnitAll::getHasLevelSecretary, null)
                .set(Objects.isNull(information.getGraduateStudent()), UnitAll::getGraduateStudent, null)
                .set(Objects.isNull(information.getUndergraduateStudent()), UnitAll::getUndergraduateStudent, null)
                .set(Objects.isNull(information.getJuniorCollegeStudent()), UnitAll::getJuniorCollegeStudent, null)
                .set(Objects.isNull(information.getMiddleTechnicalStudents()), UnitAll::getMiddleTechnicalStudents, null)
                .set(Objects.isNull(information.getTeachersInstitutionsHigher()), UnitAll::getTeachersInstitutionsHigher, null)
                .set(Objects.isNull(information.getTeachersHigherWomen()), UnitAll::getTeachersHigherWomen, null)
                .set(Objects.isNull(information.getTeachersAgeThirtyFiveBelow()), UnitAll::getTeachersAgeThirtyFiveBelow, null)
                .set(Objects.isNull(information.getReportCommunityMember()), UnitAll::getReportCommunityMember, null)
                .set(Objects.isNull(information.getHasClerkPosition()), UnitAll::getHasClerkPosition, null)
                .set(Objects.isNull(information.getTechnicalSecondaryStudent()), UnitAll::getTechnicalSecondaryStudent, null)
                .set(Objects.isNull(information.getIsAllocateDean()), UnitAll::getIsAllocateDean, null)
                .set(Objects.isNull(information.getIsAllocateSecretary()), UnitAll::getIsAllocateSecretary, null)
                .set(Objects.isNull(information.getHasLabourUnion()), UnitAll::getHasLabourUnion, null)
                .set(Objects.isNull(information.getHasYouthLeague()), UnitAll::getHasYouthLeague, null)
                .set(Objects.isNull(information.getHasWomensFederation()), UnitAll::getHasWomensFederation, null)
                .set(Objects.isNull(information.getHasSetGrid()), UnitAll::getHasSetGrid, null)
                .set(Objects.isNull(information.getHasIncludedGridWorker()), UnitAll::getHasIncludedGridWorker, null)
                .set(Objects.isNull(information.getHasJointUnits()), UnitAll::getHasJointUnits, null)
                .set(Objects.isNull(information.getRuralProfessionalTechnicalAssociationNum()), UnitAll::getRuralProfessionalTechnicalAssociationNum, null)
                .set(Objects.isNull(information.getFarmerSpecializedCooperativesNum()), UnitAll::getFarmerSpecializedCooperativesNum, null)
                .set(Objects.isNull(information.getFamilyFarmNum()), UnitAll::getFamilyFarmNum, null)
                .set(Objects.isNull(information.getYearBranchTraining()), UnitAll::getYearBranchTraining, null)
                .set(Objects.isNull(information.getOrgRelationshipNotTransferred()), UnitAll::getOrgRelationshipNotTransferred, null)
                .set(Objects.isNull(information.getHasWorkingExpenses()), UnitAll::getHasWorkingExpenses, null)
                .set(Objects.isNull(information.getD159Code()), UnitAll::getD159Code, null)
                .set(Objects.isNull(information.getHasIndustryProvince()), UnitAll::getHasIndustryProvince, null)

                // 单位为：中小学\科研院所，相关字段
//                .set(Objects.isNull(information.getYsgzIs()), UnitAll::getYsgzIs, null)
//                .set(Objects.isNull(information.getYjldqgtIs()), UnitAll::getYjldqgtIs, null)
//                .set(Objects.isNull(information.getYsldwgzjgIs()), UnitAll::getYsldwgzjgIs, null)
//                .set(Objects.isNull(information.getYpbzzdwgzryIs()), UnitAll::getYpbzzdwgzryIs, null)
//                // 原UnitCommunity扩展单位信息内容
//                .set(Objects.isNull(information.getFirstSecretaryCode()), UnitAll::getFirstSecretaryCode, null)
//                .set(Objects.isNull(information.getFirstSecretaryName()), UnitAll::getFirstSecretaryName, null)
//                .set(Objects.isNull(information.getYear()), UnitAll::getYear, null)
//                .set(Objects.isNull(information.getFirstSecretarySelect()), UnitAll::getFirstSecretarySelect, null)
//                .set(Objects.isNull(information.getSecretaryTrainingNum()), UnitAll::getSecretaryTrainingNum, null)
//                .set(Objects.isNull(information.getHasThousand()), UnitAll::getHasThousand, null)
//                .set(Objects.isNull(information.getHasBundled()), UnitAll::getHasBundled, null)
//                .set(Objects.isNull(information.getPromotedNum()), UnitAll::getPromotedNum, null)
//                .set(Objects.isNull(information.getAdjustedNum()), UnitAll::getAdjustedNum, null)
//                .set(Objects.isNull(information.getOperatingExpenses()), UnitAll::getOperatingExpenses, null)
//                .set(Objects.isNull(information.getVillagePer()), UnitAll::getVillagePer, null)
//                .set(Objects.isNull(information.getSecretarySalary()), UnitAll::getSecretarySalary, null)
//                .set(Objects.isNull(information.getSpaceArea()), UnitAll::getSpaceArea, null)
//                .set(Objects.isNull(information.getNewExpandArea()), UnitAll::getNewExpandArea, null)
//                .set(Objects.isNull(information.getSecretaryPartyNum()), UnitAll::getSecretaryPartyNum, null)
//                .set(Objects.isNull(information.getSecretaryEmploySybzNum()), UnitAll::getSecretaryEmploySybzNum, null)
//                .set(Objects.isNull(information.getSecretaryPromotedNum()), UnitAll::getSecretaryPromotedNum, null)
//                .set(Objects.isNull(information.getCommunityMoneyNum()), UnitAll::getCommunityMoneyNum, null)
//                .set(Objects.isNull(information.getCommunityServingPeople()), UnitAll::getCommunityServingPeople, null)
//                .set(Objects.isNull(information.getCommunityMasses()), UnitAll::getCommunityMasses, null)

                .eq(UnitAll::getId, information.getId()));
    }

    /**
     * 删除单位信息
     */
    public OutMessage delUnit(String code) {
        UnitAll information = unitAllMapper.selectOne(new QueryWrapper<UnitAll>().lambda().eq(UnitAll::getCode, code)
                .isNull(UnitAll::getDeleteTime));
        if (Objects.nonNull(information)) {
            information.setDeleteTime(new Date());
            unitAllMapper.updateById(information);
        }
        return null;
    }

    /**
     * 单位扩展信息同步
     */
    public void setUnitExtend(String unitCode, UnitAll information) {
        String year = iStatisticsYearService.getStatisticalYear();
        UnitCommunity community = communityMapper.selectOne(new QueryWrapper<UnitCommunity>().lambda()
                .eq(UnitCommunity::getUnitCode, unitCode).isNull(UnitCommunity::getDeleteTime)
                .apply("to_char(year,'yyyy')='" + year + "'")
                .orderByDesc(UnitCommunity::getYear).last("limit 1"));
        UnitCommunity updateUnitCommunity = new UnitCommunity();
        if (Objects.nonNull(community)) {
            BeanUtils.copyProperties(community, updateUnitCommunity);
        }

        information.setFirstSecretaryCode(updateUnitCommunity.getFirstSecretaryCode());
        information.setFirstSecretaryName(updateUnitCommunity.getFirstSecretaryName());
        information.setYear(updateUnitCommunity.getYear());
        information.setFirstSecretarySelect(updateUnitCommunity.getFirstSecretarySelect());
        information.setSecretaryTrainingNum(updateUnitCommunity.getSecretaryTrainingNum());
        information.setHasThousand(updateUnitCommunity.getHasThousand());
        information.setHasBundled(updateUnitCommunity.getHasBundled());
        information.setPromotedNum(updateUnitCommunity.getPromotedNum());
        information.setAdjustedNum(updateUnitCommunity.getAdjustedNum());
        information.setOperatingExpenses(updateUnitCommunity.getOperatingExpenses());
        information.setVillagePer(updateUnitCommunity.getVillagePer());
        information.setSecretarySalary(updateUnitCommunity.getSecretarySalary());
        information.setSpaceArea(updateUnitCommunity.getSpaceArea());
        information.setNewExpandArea(updateUnitCommunity.getNewExpandArea());
        information.setSecretaryPartyNum(updateUnitCommunity.getSecretaryPartyNum());
        information.setSecretaryEmploySybzNum(updateUnitCommunity.getSecretaryEmploySybzNum());
        information.setSecretaryPromotedNum(updateUnitCommunity.getSecretaryPromotedNum());
        information.setCommunityMoneyNum(updateUnitCommunity.getCommunityMoneyNum());
        information.setCommunityServingPeople(updateUnitCommunity.getCommunityServingPeople());
        information.setCommunityMasses(updateUnitCommunity.getCommunityMasses());
    }

    /**
     * 更新单位扩展信息
     */
    private void saveOrUpdateUnitCommunity(UnitAll information) {
        unitAllService.saveOrUpdate(information, Wrappers.<UnitAll>lambdaUpdate()
                //
                .set(Objects.isNull(information.getHasMajorDeputySecretary()), UnitAll::getHasMajorDeputySecretary, null)
                .set(Objects.isNull(information.getOnPostNum()), UnitAll::getOnPostNum, null)
                .set(Objects.isNull(information.getB30A12()), UnitAll::getB30A12, null)
                .set(Objects.isNull(information.getIsVolTeam()), UnitAll::getIsVolTeam, null)
                .set(Objects.isNull(information.getHasVolunteerOrganization()), UnitAll::getHasVolunteerOrganization, null)
                .set(Objects.isNull(information.getD17Code()), UnitAll::getD17Code, null)
                .set(Objects.isNull(information.getD16Code()), UnitAll::getD16Code, null)
                .set(Objects.isNull(information.getD79Code()), UnitAll::getD79Code, null)
                .set(Objects.isNull(information.getTecNum()), UnitAll::getTecNum, null)
                .set(Objects.isNull(information.getBzt610()), UnitAll::getBzt610, null)
                .set(Objects.isNull(information.getD78Code()), UnitAll::getD78Code, null)
                .set(Objects.isNull(information.getIsOrgService()), UnitAll::getIsOrgService, null)
                .set(Objects.isNull(information.getIsDecouplIndustry()), UnitAll::getIsDecouplIndustry, null)
                .set(Objects.isNull(information.getD77Code()), UnitAll::getD77Code, null)
                .set(Objects.isNull(information.getZaigangGaoji()), UnitAll::getZaigangGaoji, null)
                .set(Objects.isNull(information.getD81Code()), UnitAll::getD81Code, null)
                .set(Objects.isNull(information.getLdtzyyzc()), UnitAll::getLdtzyyzc, null)
                .set(Objects.isNull(information.getIsPartyWorkWrite()), UnitAll::getIsPartyWorkWrite, null)
                .set(Objects.isNull(information.getIsOpenOrgAssess()), UnitAll::getIsOpenOrgAssess, null)
                .set(Objects.isNull(information.getIsLeaderSeparate()), UnitAll::getIsLeaderSeparate, null)
                .set(Objects.isNull(information.getLeaderIsGcdy()), UnitAll::getLeaderIsGcdy, null)
                .set(Objects.isNull(information.getIsLeaderDeputySecretary()), UnitAll::getIsLeaderDeputySecretary, null)
                .set(Objects.isNull(information.getIsSetOrgParty()), UnitAll::getIsSetOrgParty, null)
                .set(Objects.isNull(information.getSecretaryIsInsideLeader()), UnitAll::getSecretaryIsInsideLeader, null)
                .set(Objects.isNull(information.getSjsdtr()), UnitAll::getSjsdtr, null)
                .set(Objects.isNull(information.getIsYearOrgChange()), UnitAll::getIsYearOrgChange, null)
                .set(Objects.isNull(information.getYearDevelopMem()), UnitAll::getYearDevelopMem, null)
                .set(Objects.isNull(information.getHasStandingCommittee()), UnitAll::getHasStandingCommittee, null)
                .set(Objects.isNull(information.getHasReportImplementation()), UnitAll::getHasReportImplementation, null)
                .set(Objects.isNull(information.getHasOfficeProcedure()), UnitAll::getHasOfficeProcedure, null)
                .set(Objects.isNull(information.getSchoolHasReportsLocal()), UnitAll::getSchoolHasReportsLocal, null)
                .set(Objects.isNull(information.getHasSecretaryUniversityCommittee()), UnitAll::getHasSecretaryUniversityCommittee, null)
                .set(Objects.isNull(information.getHasPresidentPartyMember()), UnitAll::getHasPresidentPartyMember, null)
                .set(Objects.isNull(information.getHasDeputyPartySecretary()), UnitAll::getHasDeputyPartySecretary, null)
                .set(Objects.isNull(information.getD95Code()), UnitAll::getD95Code, null)
                .set(Objects.isNull(information.getD95Name()), UnitAll::getD95Name, null)
                .set(Objects.isNull(information.getD109Code()), UnitAll::getD109Code, null)
                .set(Objects.isNull(information.getD109Name()), UnitAll::getD109Name, null)
                .set(Objects.isNull(information.getD111Code()), UnitAll::getD111Code, null)
                .set(Objects.isNull(information.getD111Name()), UnitAll::getD111Name, null)
                .set(Objects.isNull(information.getHasResponsibilitySystem()), UnitAll::getHasResponsibilitySystem, null)
                .set(Objects.isNull(information.getD112Code()), UnitAll::getD112Code, null)
                .set(Objects.isNull(information.getD112Name()), UnitAll::getD112Name, null)
                .set(Objects.isNull(information.getD114Code()), UnitAll::getD114Code, null)
                .set(Objects.isNull(information.getHasPartyWork()), UnitAll::getHasPartyWork, null)
                .set(Objects.isNull(information.getHasFirmLevel()), UnitAll::getHasFirmLevel, null)
                .set(Objects.isNull(information.getD115Code()), UnitAll::getD115Code, null)
                .set(Objects.isNull(information.getHasDirectors()), UnitAll::getHasDirectors, null)
                .set(Objects.isNull(information.getHasChairmanSecretary()), UnitAll::getHasChairmanSecretary, null)
                .set(Objects.isNull(information.getHasProportionateFunding()), UnitAll::getHasProportionateFunding, null)
                .set(Objects.isNull(information.getHasBranchToCatch()), UnitAll::getHasBranchToCatch, null)
                .set(Objects.isNull(information.getHasByLeader()), UnitAll::getHasByLeader, null)
                .set(Objects.isNull(information.getHasSameTreatment()), UnitAll::getHasSameTreatment, null)
                .set(Objects.isNull(information.getHasPublicCompany()), UnitAll::getHasPublicCompany, null)
                .set(Objects.isNull(information.getHasArticlesIncorporation()), UnitAll::getHasArticlesIncorporation, null)
                .set(Objects.isNull(information.getHasPrepositionalProcedure()), UnitAll::getHasPrepositionalProcedure, null)
                .set(Objects.isNull(information.getHasResponsiblePerson()), UnitAll::getHasResponsiblePerson, null)
                .set(Objects.isNull(information.getBranches()), UnitAll::getBranches, null)
                .set(Objects.isNull(information.getPartyOrganizationNum()), UnitAll::getPartyOrganizationNum, null)
                .set(Objects.isNull(information.getHaveBeenEstablished()), UnitAll::getHaveBeenEstablished, null)
                .set(Objects.isNull(information.getPartyMembers()), UnitAll::getPartyMembers, null)
                .set(Objects.isNull(information.getHasNonPublicParty()), UnitAll::getHasNonPublicParty, null)
                .set(Objects.isNull(information.getHasSpecialAgencies()), UnitAll::getHasSpecialAgencies, null)
                .set(Objects.isNull(information.getHasCommunityAccess()), UnitAll::getHasCommunityAccess, null)
                .set(Objects.isNull(information.getHasJointUnits()), UnitAll::getHasJointUnits, null)
                .set(Objects.isNull(information.getHasLowerSocial()), UnitAll::getHasLowerSocial, null)
                .set(Objects.isNull(information.getCommunityBuildingNumber()), UnitAll::getCommunityBuildingNumber, null)
                .set(Objects.isNull(information.getHasCommunityPositions()), UnitAll::getHasCommunityPositions, null)
                .set(Objects.isNull(information.getCommunityOfficeSpace()), UnitAll::getCommunityOfficeSpace, null)
                .set(Objects.isNull(information.getHasParttimeSystem()), UnitAll::getHasParttimeSystem, null)
                .set(Objects.isNull(information.getHasFourTwoOpenWork()), UnitAll::getHasFourTwoOpenWork, null)
                .set(Objects.isNull(information.getHasCommunitySupervisory()), UnitAll::getHasCommunitySupervisory, null)
                .set(Objects.isNull(information.getEmployeesNumber()), UnitAll::getEmployeesNumber, null)
                .set(Objects.isNull(information.getHasInstructorContact()), UnitAll::getHasInstructorContact, null)
                .set(Objects.isNull(information.getHasUnionOrganization()), UnitAll::getHasUnionOrganization, null)
                .set(Objects.isNull(information.getNotTurnedParty()), UnitAll::getNotTurnedParty, null)
                .set(Objects.isNull(information.getHasOrganizationSecretary()), UnitAll::getHasOrganizationSecretary, null)
                .set(Objects.isNull(information.getTechnicalPersonnel()), UnitAll::getTechnicalPersonnel, null)
                .set(Objects.isNull(information.getPartySeniorTitle()), UnitAll::getPartySeniorTitle, null)
                .set(Objects.isNull(information.getHasExaminationPower()), UnitAll::getHasExaminationPower, null)
                .set(Objects.isNull(information.getHasCancelInvestmentPromotion()), UnitAll::getHasCancelInvestmentPromotion, null)
                .set(Objects.isNull(information.getHasWorkMechanism()), UnitAll::getHasWorkMechanism, null)
                .set(Objects.isNull(information.getHasIncludedCommittee()), UnitAll::getHasIncludedCommittee, null)
                .set(Objects.isNull(information.getHasGroupServiceCenter()), UnitAll::getHasGroupServiceCenter, null)
                .set(Objects.isNull(information.getHasPartyBuildEndeavor()), UnitAll::getHasPartyBuildEndeavor, null)
                .set(Objects.isNull(information.getHasRepresentative()), UnitAll::getHasRepresentative, null)
                .set(Objects.isNull(information.getHasProperSecretary()), UnitAll::getHasProperSecretary, null)
                .set(Objects.isNull(information.getAbsorbedTissueNumber()), UnitAll::getAbsorbedTissueNumber, null)
                .set(Objects.isNull(information.getHasHeadParty()), UnitAll::getHasHeadParty, null)
                .set(Objects.isNull(information.getIncludedFinancial()), UnitAll::getIncludedFinancial, null)
                .set(Objects.isNull(information.getSpecialFundsMasses()), UnitAll::getSpecialFundsMasses, null)
                .set(Objects.isNull(information.getHasCommunityReport()), UnitAll::getHasCommunityReport, null)
                .set(Objects.isNull(information.getHasSecretaryCommittee()), UnitAll::getHasSecretaryCommittee, null)
                .set(Objects.isNull(information.getHasTissueCommittee()), UnitAll::getHasTissueCommittee, null)
                .set(Objects.isNull(information.getHasPropagandaCommittee()), UnitAll::getHasPropagandaCommittee, null)
                .set(Objects.isNull(information.getHasFrontCommittee()), UnitAll::getHasFrontCommittee, null)
                .set(Objects.isNull(information.getCommunityWorkersSalary()), UnitAll::getCommunityWorkersSalary, null)
                .set(Objects.isNull(information.getAboveBkEducation()), UnitAll::getAboveBkEducation, null)
                .set(Objects.isNull(information.getAboveYjsEducation()), UnitAll::getAboveYjsEducation, null)
                .set(Objects.isNull(information.getHasSecretaryHighLevel()), UnitAll::getHasSecretaryHighLevel, null)
                .set(Objects.isNull(information.getHasLevelSecretary()), UnitAll::getHasLevelSecretary, null)
                .set(Objects.isNull(information.getHasIndustryProvince()), UnitAll::getHasIndustryProvince, null)
                .set(Objects.isNull(information.getGraduateStudent()), UnitAll::getGraduateStudent, null)
                .set(Objects.isNull(information.getUndergraduateStudent()), UnitAll::getUndergraduateStudent, null)
                .set(Objects.isNull(information.getJuniorCollegeStudent()), UnitAll::getJuniorCollegeStudent, null)
                .set(Objects.isNull(information.getMiddleTechnicalStudents()), UnitAll::getMiddleTechnicalStudents, null)
                .set(Objects.isNull(information.getTeachersInstitutionsHigher()), UnitAll::getTeachersInstitutionsHigher, null)
                .set(Objects.isNull(information.getTeachersHigherWomen()), UnitAll::getTeachersHigherWomen, null)
                .set(Objects.isNull(information.getTeachersAgeThirtyFiveBelow()), UnitAll::getTeachersAgeThirtyFiveBelow, null)
                .set(Objects.isNull(information.getReportCommunityMember()), UnitAll::getReportCommunityMember, null)
                .set(Objects.isNull(information.getHasClerkPosition()), UnitAll::getHasClerkPosition, null)
                .set(Objects.isNull(information.getTechnicalSecondaryStudent()), UnitAll::getTechnicalSecondaryStudent, null)
                .set(Objects.isNull(information.getIsAllocateDean()), UnitAll::getIsAllocateDean, null)
                .set(Objects.isNull(information.getIsAllocateSecretary()), UnitAll::getIsAllocateSecretary, null)
                .set(Objects.isNull(information.getHasLabourUnion()), UnitAll::getHasLabourUnion, null)
                .set(Objects.isNull(information.getHasYouthLeague()), UnitAll::getHasYouthLeague, null)
                .set(Objects.isNull(information.getHasWomensFederation()), UnitAll::getHasWomensFederation, null)
                .set(Objects.isNull(information.getHasSetGrid()), UnitAll::getHasSetGrid, null)
                .set(Objects.isNull(information.getHasIncludedGridWorker()), UnitAll::getHasIncludedGridWorker, null)
                // 单位为：中小学、科研院所，，相关字段
                .set(Objects.isNull(information.getYsgzIs()), UnitAll::getYsgzIs, null)
                .set(Objects.isNull(information.getYjldqgtIs()), UnitAll::getYjldqgtIs, null)
                .set(Objects.isNull(information.getYsldwgzjgIs()), UnitAll::getYsldwgzjgIs, null)
                .set(Objects.isNull(information.getYpbzzdwgzryIs()), UnitAll::getYpbzzdwgzryIs, null)

                // UnitCommunity 合并字段
                .set(Objects.isNull(information.getFirstSecretarySelect()), UnitAll::getFirstSecretarySelect, null)
                .set(Objects.isNull(information.getFirstSecretaryCode()), UnitAll::getFirstSecretaryCode, null)
                .set(Objects.isNull(information.getFirstSecretaryName()), UnitAll::getFirstSecretaryName, null)
                .set(Objects.isNull(information.getSecretaryTrainingNum()), UnitAll::getSecretaryTrainingNum, null)
                .set(Objects.isNull(information.getHasThousand()), UnitAll::getHasThousand, null)
                .set(Objects.isNull(information.getHasBundled()), UnitAll::getHasBundled, null)
                .set(Objects.isNull(information.getPromotedNum()), UnitAll::getPromotedNum, null)
                .set(Objects.isNull(information.getAdjustedNum()), UnitAll::getAdjustedNum, null)
                .set(Objects.isNull(information.getOperatingExpenses()), UnitAll::getOperatingExpenses, null)
                .set(Objects.isNull(information.getVillagePer()), UnitAll::getVillagePer, null)
                .set(Objects.isNull(information.getSecretarySalary()), UnitAll::getSecretarySalary, null)
                .set(Objects.isNull(information.getSpaceArea()), UnitAll::getSpaceArea, null)
                .set(Objects.isNull(information.getNewExpandArea()), UnitAll::getNewExpandArea, null)
                .set(Objects.isNull(information.getSecretaryPartyNum()), UnitAll::getSecretaryPartyNum, null)
                .set(Objects.isNull(information.getSecretaryEmploySybzNum()), UnitAll::getSecretaryEmploySybzNum, null)
                .set(Objects.isNull(information.getSecretaryPromotedNum()), UnitAll::getSecretaryPromotedNum, null)
                .set(Objects.isNull(information.getCommunityMoneyNum()), UnitAll::getCommunityMoneyNum, null)
                .set(Objects.isNull(information.getCommunityServingPeople()), UnitAll::getCommunityServingPeople, null)
                .set(Objects.isNull(information.getCommunityMasses()), UnitAll::getCommunityMasses, null)

                // unit 部分
                .set(Objects.isNull(information.getRuralProfessionalTechnicalAssociationNum()), UnitAll::getRuralProfessionalTechnicalAssociationNum, null)
                .set(Objects.isNull(information.getFarmerSpecializedCooperativesNum()), UnitAll::getFarmerSpecializedCooperativesNum, null)
                .set(Objects.isNull(information.getFamilyFarmNum()), UnitAll::getFamilyFarmNum, null)
                .set(Objects.isNull(information.getYearBranchTraining()), UnitAll::getYearBranchTraining, null)
                .set(Objects.isNull(information.getYearTraining()), UnitAll::getYearTraining, null)
                .set(Objects.isNull(information.getGraduatePartyMember()), UnitAll::getGraduatePartyMember, null)
                .set(Objects.isNull(information.getOrgRelationshipNotTransferred()), UnitAll::getOrgRelationshipNotTransferred, null)
                .set(Objects.isNull(information.getHasWorkingExpenses()), UnitAll::getHasWorkingExpenses, null)
                // unitAll部分
                .set(Objects.isNull(information.getAge35Below()), UnitAll::getAge35Below, null)
                .set(Objects.isNull(information.getAge36ToAge55()), UnitAll::getAge36ToAge55, null)
                .set(Objects.isNull(information.getAge56Above()), UnitAll::getAge56Above, null)
                .set(Objects.isNull(information.getCollegeDegreeAbove()), UnitAll::getCollegeDegreeAbove, null)
                .set(Objects.isNull(information.getSecondarySchoolBelow()), UnitAll::getSecondarySchoolBelow, null)
                .set(Objects.isNull(information.getStreetCadresCivil()), UnitAll::getStreetCadresCivil, null)
                .set(Objects.isNull(information.getStreetCadresInstitutions()), UnitAll::getStreetCadresInstitutions, null)
                .set(Objects.isNull(information.getCadreOther()), UnitAll::getCadreOther, null)
                .set(Objects.isNull(information.getD159Code()), UnitAll::getD159Code, null)
                .set(Objects.isNull(information.getD159Name()), UnitAll::getD159Name, null)
                .set(Objects.isNull(information.getHasPerformedDetail()), UnitAll::getHasPerformedDetail, null)
                .set(Objects.isNull(information.getExistMemberBranches()), UnitAll::getExistMemberBranches, null)
                .set(Objects.isNull(information.getThreeMemberNoOrgBranches()), UnitAll::getThreeMemberNoOrgBranches, null)
                .set(Objects.isNull(information.getBranchEmployee()), UnitAll::getBranchEmployee, null)
                .set(Objects.isNull(information.getBranchEmployeeHome()), UnitAll::getBranchEmployeeHome, null)
                .set(Objects.isNull(information.getBranchCommittee()), UnitAll::getBranchCommittee, null)
                .set(Objects.isNull(information.getBranchGeneral()), UnitAll::getBranchGeneral, null)
                .set(Objects.isNull(information.getBranchNode()), UnitAll::getBranchNode, null)


                .eq(UnitAll::getId, information.getId()));
    }

    /**
     * 单位扩展信息书记同步
     */
    public void setUnitExtendSecretary(Unit unit, UnitAll information) {
        String year = iStatisticsYearService.getStatisticalYear();

        LambdaQueryWrapper<UnitExtend> unitExtendLambdaQueryWrapper = Wrappers.lambdaQuery();
        unitExtendLambdaQueryWrapper.isNull(UnitExtend::getDeleteTime)
                .eq(UnitExtend::getCode, unit.getCode())
                .eq(UnitExtend::getYear, Integer.parseInt(year))
                .orderByDesc(UnitExtend::getYear)
                .last(" limit 1 ");
        UnitExtend unitExtend = iUnitExtendService.getOne(unitExtendLambdaQueryWrapper);

        if (Objects.nonNull(unitExtend)) {
            List<Record> dict_d114 = dictService.getDic("dict_d114");
            Map<String, String> d114Map = dict_d114.stream().collect(Collectors.toMap(record -> record.getStr("key"), record -> record.getStr("name")));
//            BeanUtils.copyProperties(unitExtend, information, "id", "code", "esId", "deleteTime", "d04Code");
            // copy指定字段，该属性是从前端哪里得到单位扩展信息传递的所有字段，主要担心其他无关字段覆盖错误
            com.zenith.front.common.kit.BeanUtl.copyProperties(unitExtend, information, UnitCommunityServiceImpl.unitExtendFieldCopyList);
            information.setD114Name(d114Map.get(unitExtend.getD114Code()));
            if (StrUtil.equals(information.getD04Code(), "911")) {
                information.setHasParttimeSystem(unitExtend.getB610());
            }
            //院长是否中共党员  1是，0否
            information.setLeaderIsGcdy(unitExtend.getHasDeanPartyMember());
            //是否院长担任党委副书记 1是，0否
            information.setIsLeaderDeputySecretary(unitExtend.getHasDeanPartySecretary());
        }
    }

    /**
     * 社区工作者扩展信息同步
     */
    public void setCommunityWorker(String unitCode, UnitAll information) {
        List<UnitCountryside> countrysideList = countrysideMapper.selectList(new QueryWrapper<UnitCountryside>()
                .eq("unit_code", unitCode));
        List<UnitCountryside> countrysides = countrysideList.stream().filter(e -> StrUtil.equals(e.getType(), "1") && Objects.isNull(e.getLeaveTime())).collect(Collectors.toList());
        List<UnitCountryside> leaveList = countrysideList.stream().filter(e -> StrUtil.equals(e.getType(), "1") && Objects.nonNull(e.getLeaveTime())).collect(Collectors.toList());

        //社区工作者中专职党务工作者数
        information.setCommunityWorkerCount(countrysides.size());
        long count = countrysides.stream().filter(s -> Objects.nonNull(s.getHasPartyWork()) && CommonConstant.ONE_INT == s.getHasPartyWork()).count();
        information.setPartyWorkerNum(Math.toIntExact(count));
        //社区工作者中大专学历以上人员人数
        long college = countrysides.stream().filter(s -> Objects.nonNull(s.getD07Code()) && (s.getD07Code().startsWith(CommonConstant.ONE) || s.getD07Code().startsWith(CommonConstant.TWO) || "31".equals(s.getD07Code()))).count();
        information.setCollegeDegreeNum(Math.toIntExact(college));
        //社区工作者中从机关和街道选派的人数
        long officesAndStreets = countrysides.stream().filter(s -> Objects.nonNull(s.getD116Code()) && (CommonConstant.ONE.equals(s.getD116Code()) || CommonConstant.TWO.equals(s.getD116Code()))).count();
        information.setOfficesAndStreets(Math.toIntExact(officesAndStreets));
        //社区工作者中从退役军人中选聘的人数
        long veTer = countrysides.stream().filter(s -> Objects.nonNull(s.getD116Code()) && CommonConstant.THREE.equals(s.getD116Code())).count();
        information.setVeterans(Math.toIntExact(veTer));

        //社区工作者中录用为公务员的人数
        long civilServants = leaveList.stream().filter(s -> StrUtil.equals(s.getD117Code(), "1")).count();
        information.setCivilServants(Math.toIntExact(civilServants));
        //社区工作者中选拔进入事业编制的人数
        long establishment = leaveList.stream().filter(s -> StrUtil.equals(s.getD117Code(), "2")).count();
        information.setEstablishment(Math.toIntExact(establishment));
        //社区工作者中推荐为两代表一委人数
        long twoAndOne = countrysides.stream().filter(s -> Objects.nonNull(s.getHasTwoOneMember()) && s.getHasTwoOneMember() == 1).count();
        information.setTwoAndOne(Math.toIntExact(twoAndOne));

        //有村党组织书记后备人选的行政村
        Optional<UnitCountryside> any = countrysideList.stream().filter(e -> StrUtil.equals(e.getType(), "2")).findAny();
        information.setHasCountrySecretaryHbgb(any.isPresent() ? 1 : 0);
    }

    /**
     * 二级院校信息同步
     */
    private void setColleges(String unitCode, UnitAll information) {
        List<UnitSecondary> secondaries = secondaryMapper.selectList(new QueryWrapper<UnitSecondary>()
                .eq("unit_code", unitCode)
                .isNull("delete_time"));
        information.setSecondaryCollege(0);
        information.setSecondaryCollegeCommittee(0);
        information.setSecondaryCollegeAlwaysBranch(0);
        information.setSecondaryCollegeBranch(0);
        information.setSecondaryCollegeWithOne(0);
        information.setSecondaryCollegeGreaterTwo(0);
        information.setSecondaryCollegeWithOneTwo(0);
        if (CollectionUtils.isNotEmpty(secondaries)) {
            //二级院（系）个数
            information.setSecondaryCollege(secondaries.size());
            List<String> orgCodes = secondaries.stream().map(UnitSecondary::getOrgCode).filter(StrKit::notBlank).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(orgCodes)) {
                List<Org> byOrgCodeList = orgService.findByOrgCodeList(orgCodes);
                //二级院（系）中建立党委的
                long collegeCommittee = byOrgCodeList.stream().filter(s -> "61".equals(s.getD01Code())).count();
                information.setSecondaryCollegeCommittee(Math.toIntExact(collegeCommittee));
                //二级院（系）中建立总支部的
                long alwaysBranch = byOrgCodeList.stream().filter(s -> "62".equals(s.getD01Code())).count();
                information.setSecondaryCollegeAlwaysBranch(Math.toIntExact(alwaysBranch));
                //二级院（系）中建立党支部的
                long collegeBranch = byOrgCodeList.stream().filter(s -> s.getD01Code().startsWith("63")).count();
                information.setSecondaryCollegeBranch(Math.toIntExact(collegeBranch));
            }
            //二级院（系）配备1名专职组织员的
            long withOne = secondaries.stream().filter(s -> StrUtil.isNotEmpty(s.getD110Code()) && CommonConstant.ONE.equals(s.getD110Code())).count();
            information.setSecondaryCollegeWithOne(Math.toIntExact(withOne));
            //二级院（系）配备2名及以上专职组织员的
            long greaterTwo = secondaries.stream().filter(s -> StrUtil.isNotEmpty(s.getD110Code()) && CommonConstant.TWO.equals(s.getD110Code())).count();
            information.setSecondaryCollegeGreaterTwo(Math.toIntExact(greaterTwo));
            information.setSecondaryCollegeWithOneTwo(Math.toIntExact(withOne + greaterTwo));
        }
    }

    /**
     * 医院内设机构同步
     */
    private void insideHospital(Unit unit, UnitAll information) {
        String year = iStatisticsYearService.getStatisticalYear();
        if (StrUtil.isNotEmpty(unit.getD04Code()) && unit.getD04Code().startsWith("341")) {
            List<UnitOrgLinked> linkedList = linkedMapper.selectList(new QueryWrapper<UnitOrgLinked>()
                    .eq("unit_code", unit.getCode())
                    .isNull("delete_time"));
            // todo 2024-01-06 create_time 在单位、党组织中保存时都会删除重建这条数据，加时间导致查询不到
//                    .apply("to_char(create_time,'yyyy')='" + year + "'"));
            if (CollectionUtils.isNotEmpty(linkedList)) {
                //医院内设机构党支部（个）
                long count = linkedList.stream().filter(s -> s.getOrgTypeCode().equals(CommonConstant.THREE)).count();
                information.setIsSetOrgParty(Math.toIntExact(count));

                List<String> collect = linkedList.stream().map(UnitOrgLinked::getOrgCode).collect(Collectors.toList());
                List<Org> orgCodeList = orgService.findByOrgCodeList(collect);

                List<OrgCommitteeElect> committeeElects = orgElectMapper.selectList(new QueryWrapper<OrgCommitteeElect>()
                        .in("org_code", collect)
                        .isNull("delete_time")
                        .apply("to_char(create_time,'yyyy')='" + year + "'"));
                if (CollectionUtils.isNotEmpty(committeeElects)) {
                    List<OrgCommitteeElect> electList = new ArrayList<>();
                    Map<String, List<OrgCommitteeElect>> listMap = committeeElects.stream().collect(Collectors.groupingBy(OrgCommitteeElect::getOrgCode));
                    listMap.forEach((k, v) -> {
                        List<OrgCommitteeElect> elects = v.stream().sorted((m1, m2) -> m2.getTenureEndDate().compareTo(m1.getTenureEndDate())).collect(Collectors.toList());
                        OrgCommitteeElect committeeElect = elects.get(0);
                        electList.add(committeeElect);
                    });
                    //医院本年度任届期满的内设机构党支部
                    long full = electList.stream().filter(s -> Objects.nonNull(s.getTenureEndDate()) && s.getTenureEndDate().before(new Date())).count();
                    information.setInternalInstitutions(Math.toIntExact(full));
                    //医院本年度任届期满的内设机构党支部且本年换届
                    long noFull = electList.stream().filter(s -> Objects.nonNull(s.getTenureEndDate()) && s.getTenureEndDate().after(new Date()) && s.getTenureStartDate().getYear() == new Date().getYear()).count();
                    information.setInternalInstitutionsTransition(Math.toIntExact(noFull));
                }
                //党支部书记是内设机构负责人（个）
                long leader = orgCodeList.stream().filter(s -> Objects.nonNull(s.getHasSecretaryisinsideleader()) && CommonConstant.ONE_INT == s.getHasSecretaryisinsideleader()).count();
                information.setSecretaryIsInsideLeader(Math.toIntExact(leader));
                //党支部书记是“双带头人”的（个）
                long sjstr = orgCodeList.stream().filter(s -> Objects.nonNull(s.getHasSjsdtr()) && CommonConstant.ONE_INT == s.getHasSjsdtr()).count();
                information.setSjsdtr(Math.toIntExact(sjstr));
                //本年度所有党员
                List<MemDevelop> memDevelops = memDevelopMapper.selectList(new QueryWrapper<MemDevelop>()
                        .in("develop_org_code", collect).isNull("delete_time")
                        .in("d08_code", "3", "4", "5").apply("to_char(apply_date,'yyyy')='" + year + "'"));
                //医院本年度发展党员
                information.setHospitalsDevelop(memDevelops.size());
                //医院本年度发展卫生技术人员党员[计算]
                List<Org> orgList = orgCodeList.stream().filter(s -> Objects.nonNull(s.getYearDevelopMemMedicine())).collect(Collectors.toList());
                if (CollectionUtils.isNotEmpty(orgList)) {
                    Integer integer = orgList.stream().map(Org::getYearDevelopMemMedicine).reduce(Integer::sum).get();
                    information.setHospitalsDevelopTechnology(integer);
                }
                //医院本年度列为入党积极分子[计算]
                long active = memDevelops.stream().filter(s -> Objects.nonNull(s.getD08Code()) && s.getD08Code().equals(CommonConstant.FOUR)).count();
                information.setHospitalsActive(Math.toIntExact(active));
            }
        }
    }

    /**
     * 科研院（所）内设机构同步
     */
    private void researchYsnsjgdzbCount(Unit unit, UnitAll information) {
        if (StrUtil.isNotEmpty(unit.getD04Code()) && unit.getD04Code().startsWith("32")) {
//            List<UnitOrgLinked> linkedList = linkedMapper.selectList(new QueryWrapper<UnitOrgLinked>()
//                    .eq("unit_code", unit.getCode())
//                    .isNull("delete_time"));
            LambdaQueryWrapper<Org> orgLambdaQueryWrapper = Wrappers.lambdaQuery();
            orgLambdaQueryWrapper.isNull(Org::getDeleteTime)
                    .and(q -> q
                            .isNull(Org::getIsDissolve)
                            .or()
                            .ne(Org::getIsDissolve, CommonConstant.ONE_INT)
                    ).eq(Org::getMainUnitCode, unit.getCode())
                    // TODO: 2025/3/15 添加中组部审核，专题调查表党支部自己建立的，不能是本级的情况，还要考虑联合党支部不与上级或者与上级建立的时候那种支部也要排掉,所以只能是与上级党组织相同的那种
                    .eq(Org::getD02Code,"2")
                    .likeRight(Org::getD01Code, "63");
            List<Org> orgCodeList = orgService.list(orgLambdaQueryWrapper);
            if (CollectionUtils.isNotEmpty(orgCodeList)) {
                //院（所）内设机构党支部（个）
                information.setIsSetOrgParty(Math.toIntExact(orgCodeList.size()));
                //党支部书记是内设机构负责人（个）
                long leader = orgCodeList.stream().filter(s -> Objects.nonNull(s.getHasSecretaryisinsideleader()) && CommonConstant.ONE_INT == s.getHasSecretaryisinsideleader()).count();
                information.setSecretaryIsInsideLeader(Math.toIntExact(leader));
                //党支部书记是“双带头人”的（个）
                long sjstr = orgCodeList.stream().filter(s -> Objects.nonNull(s.getHasSjsdtr()) && CommonConstant.ONE_INT == s.getHasSjsdtr()).count();
                information.setSjsdtr(Math.toIntExact(sjstr));
            }
        }
    }

    /**
     * 同步在岗职工党员和专业技术党员数
     */
    private void partyMember(Unit unit, UnitAll information) {
        String year = iStatisticsYearService.getStatisticalYear();
        List<UnitOrgLinked> linkedList = linkedMapper.selectList(new QueryWrapper<UnitOrgLinked>()
                .eq("unit_code", unit.getCode())
                .isNull("delete_time")
                .apply("to_char(create_time,'yyyy')='" + year + "'"));
        if (CollectionUtils.isNotEmpty(linkedList)) {
            List<String> collect = linkedList.stream().map(UnitOrgLinked::getOrgCode).collect(Collectors.toList());
            List<Mem> memList = memMapper.selectList(new QueryWrapper<Mem>().in("org_code", collect)
                    .in("d08_code", "1", "2").isNull("delete_time"));
            if (CollectionUtils.isNotEmpty(memList)) {
                //在岗职工中的党员数
                information.setInPartyMembers(memList.size());
                long count = memList.stream()
                        .filter(s -> StrUtil.isNotEmpty(s.getD09Code()) && s.getD09Code().startsWith("013")
                                || "015".equals(s.getD09Code())
                                || "022".equals(s.getD09Code())
                                || "0312".equals(s.getD09Code())
                                || "0322".equals(s.getD09Code())
                                || "0332".equals(s.getD09Code())).count();
                //技术人员中的党员数
                information.setTechnologyPartyMembers(Math.toIntExact(count));
            }

        }
    }

    /**
     * 同步党组性质党委信息
     */
    public void committeeNature(String unitCode, UnitAll information) {
        List<OrgParty> parties = partyMapper.selectList(new QueryWrapper<OrgParty>()
                .eq("unit_code", unitCode)
                .isNull("delete_time")
        );
        if (CollectionUtils.isNotEmpty(parties)) {
            long one = parties.stream().filter(s -> StrUtil.isNotEmpty(s.getD108Code()) && s.getD108Code().startsWith(CommonConstant.THREE)).count();
            long two = parties.stream().filter(s -> StrUtil.isNotEmpty(s.getD108Code()) && "37".equals(s.getD108Code())).count();
            long three = parties.stream().filter(s -> StrUtil.isNotEmpty(s.getD108Code()) && s.getD108Code().startsWith(CommonConstant.FOUR)).count();
            //是否机关党组
            long four = parties.stream().filter(s -> StrUtil.isNotEmpty(s.getD108Code()) && !StrUtil.equalsAny(s.getD108Code(), "39")).count();
            //建立党组数字
            if (one > 0) {
                //设置该单位为党组
                information.setHasParty(CommonConstant.ONE_INT);
            } else {
                information.setHasParty(CommonConstant.ZERO_INT);
            }
            if (four > 0) {
                information.setHasAuthority(CommonConstant.ONE_INT);
            } else {
                information.setHasAuthority(CommonConstant.ZERO_INT);
            }
            information.setCreatePartyTeam(Math.toIntExact(one));
            //建 立 分 党 组 数字
            information.setCreatePartyGroup(Math.toIntExact(two));
            //建立党组性质党委数字
            information.setCreatePartyCommittee(Math.toIntExact(three));
        } else {
            information.setCreatePartyTeam(0);
            information.setCreatePartyGroup(0);
            information.setCreatePartyCommittee(0);
            information.setHasParty(0);
            information.setHasAuthority(CommonConstant.ZERO_INT);
        }
    }


    /**
     * 同步城市街道干部信息
     */
    private void unitStreetsCadres(String unitCode, UnitAll unitAll) {
        List<UnitStreetsCadres> cadresList = unitStreetsCadresMapper.selectList(new LambdaQueryWrapper<UnitStreetsCadres>().isNull(UnitStreetsCadres::getDeleteTime).eq(UnitStreetsCadres::getUnitCode, unitCode).eq(UnitStreetsCadres::getYear, Integer.parseInt(iStatisticsYearService.getStatisticalYear())));
        if (CollUtil.isNotEmpty(cadresList)) {
            unitAll.setStreetCadres(Math.toIntExact(cadresList.stream().filter(e -> Objects.nonNull(e.getStreetCadres())).mapToLong(UnitStreetsCadres::getStreetCadres).sum()));
            unitAll.setAge35Below(Math.toIntExact(cadresList.stream().filter(e -> Objects.nonNull(e.getAge35Below())).mapToLong(UnitStreetsCadres::getAge35Below).sum()));
            unitAll.setAge36ToAge55(Math.toIntExact(cadresList.stream().filter(e -> Objects.nonNull(e.getAge36ToAge55())).mapToLong(UnitStreetsCadres::getAge36ToAge55).sum()));
            unitAll.setAge56Above(Math.toIntExact(cadresList.stream().filter(e -> Objects.nonNull(e.getAge56Above())).mapToLong(UnitStreetsCadres::getAge56Above).sum()));
            unitAll.setCollegeDegreeAbove(Math.toIntExact(cadresList.stream().filter(e -> Objects.nonNull(e.getCollegeDegreeAbove())).mapToLong(UnitStreetsCadres::getCollegeDegreeAbove).sum()));
            unitAll.setSecondarySchoolBelow(Math.toIntExact(cadresList.stream().filter(e -> Objects.nonNull(e.getSecondarySchoolBelow())).mapToLong(UnitStreetsCadres::getSecondarySchoolBelow).sum()));
            unitAll.setStreetCadresCivil(Math.toIntExact(cadresList.stream().filter(e -> Objects.nonNull(e.getStreetCadresCivil())).mapToLong(UnitStreetsCadres::getStreetCadresCivil).sum()));
            unitAll.setStreetCadresInstitutions(Math.toIntExact(cadresList.stream().filter(e -> Objects.nonNull(e.getStreetCadresInstitutions())).mapToLong(UnitStreetsCadres::getStreetCadresInstitutions).sum()));
            unitAll.setCadreOther(Math.toIntExact(cadresList.stream().filter(e -> Objects.nonNull(e.getCadreOther())).mapToLong(UnitStreetsCadres::getCadreOther).sum()));
        } else {
            unitAll.setStreetCadres(0);
            unitAll.setAge35Below(0);
            unitAll.setAge36ToAge55(0);
            unitAll.setAge56Above(0);
            unitAll.setCollegeDegreeAbove(0);
            unitAll.setSecondarySchoolBelow(0);
            unitAll.setStreetCadresCivil(0);
            unitAll.setStreetCadresInstitutions(0);
            unitAll.setCadreOther(0);
        }
    }

    /**
     * 同步集体经济情况
     */
    private void unitCollectiveEconomic(String unitCode, UnitAll unitAll) {
        List<UnitCollectiveEconomic> economicList = unitCollectiveEconomicMapper.selectList(new LambdaQueryWrapper<UnitCollectiveEconomic>().eq(UnitCollectiveEconomic::getUnitCode, unitCode).isNull(UnitCollectiveEconomic::getDeleteTime));
        BigDecimal yearAmount = BigDecimal.ZERO;
        BigDecimal yearAmountAll = BigDecimal.ZERO;
        BigDecimal yearOutlayAmount = BigDecimal.ZERO;
        // “当年有经营收益的村”系指村集体经济组织经营收入、发包及上交收入及投资收益之和，减去经营支出和管理费用后，计算结果大于零的村。
        BigDecimal earningsTotalAmount = BigDecimal.ZERO;
        // 经营性收益
        BigDecimal yearEarningsAmount;
        String hasCollectiveEconomy = "0";
        String hasSecretaryPrincipal = "0";
        if (CollUtil.isNotEmpty(economicList)) {
            hasCollectiveEconomy = "1";
            Optional<UnitCollectiveEconomic> optional = economicList.stream().filter(e -> Objects.equals(e.getHasEconomicVillage(), 1)).findAny();
            hasSecretaryPrincipal = optional.isPresent() ? "1" : hasSecretaryPrincipal;
            List<String> ecoList = economicList.stream().map(UnitCollectiveEconomic::getCode).collect(Collectors.toList());
            List<UnitIncome> unitIncomes = unitIncomeMapper.selectList(new LambdaQueryWrapper<UnitIncome>().in(UnitIncome::getEconomicCode, ecoList).isNull(UnitIncome::getDeleteTime).apply("to_char(create_time,'yyyy')='" + iStatisticsYearService.getStatisticalYear() + "'"));
            if (CollUtil.isNotEmpty(unitIncomes)) {
                for (UnitIncome model : unitIncomes) {
                    // 经营性收入
                    String income = model.getIncome();
                    if (StrUtil.isNotEmpty(income)) {
                        List<UnitIncomeSubDto> incomeSubDtos = JSONArray.parseArray(income, UnitIncomeSubDto.class);
                        if (CollUtil.isNotEmpty(incomeSubDtos)) {
                            //2024年统修改 BCDE列的计算逻辑目前只算了经营性收入，需要修改统计口径为跟F列是一样的
                            for (UnitIncomeSubDto subDto : incomeSubDtos.stream().filter(e -> StrUtil.isNotEmpty(e.getType()) && Arrays.asList("1", "2", "3").contains(e.getType()) && StrUtil.isNotEmpty(e.getAmount())).collect(Collectors.toList())) {
                                //经营性收入
                                yearAmount = yearAmount.add(BigDecimal.valueOf(Double.parseDouble(subDto.getAmount())));
                            }
                            for (UnitIncomeSubDto subDto : incomeSubDtos.stream().filter(e -> StrUtil.isNotEmpty(e.getAmount())).collect(Collectors.toList())) {
                                yearAmountAll = yearAmountAll.add(BigDecimal.valueOf(Double.parseDouble(subDto.getAmount())));
                            }
                            // 经营收入、发包及上交收入及投资收益
                            for (UnitIncomeSubDto subDto : incomeSubDtos.stream().filter(e -> StrUtil.isNotEmpty(e.getType()) && Arrays.asList("1", "2", "3").contains(e.getType())
                                    && StrUtil.isNotEmpty(e.getAmount())).collect(Collectors.toList())) {
                                earningsTotalAmount = earningsTotalAmount.add(BigDecimal.valueOf(Double.parseDouble(subDto.getAmount())));
                            }
                        }
                    }
                    // 经营性支出和管理费用
                    String outlay = model.getOutlay();
                    if (StrUtil.isNotEmpty(outlay)) {
                        List<UnitIncomeSubDto> outlaySubDtos = JSONArray.parseArray(outlay, UnitIncomeSubDto.class);
                        if (CollUtil.isNotEmpty(outlaySubDtos)) {
                            for (UnitIncomeSubDto subDto : outlaySubDtos.stream().filter(e -> StrUtil.isNotEmpty(e.getType()) && Arrays.asList("1", "2").contains(e.getType())
                                    && StrUtil.isNotEmpty(e.getAmount())).collect(Collectors.toList())) {
                                //经营性支出
                                yearOutlayAmount = yearOutlayAmount.add(BigDecimal.valueOf(Double.parseDouble(subDto.getAmount())));
                            }

                        }
                    }

                }
            }
        }
        // 经营收入、发包及上交收入及投资收益之和，减去经营支出和管理费用
        yearEarningsAmount = earningsTotalAmount.subtract(yearOutlayAmount);

        //年经营性收入5万元以下薄弱村空壳村（集体经济情况）（1是，0否）
        unitAll.setIncomeLess5w((yearAmount.doubleValue() < 5) ? "1" : "0");
        //年经营性收入50-100万元的村（1是，0否）
        unitAll.setIncome50w100w((yearAmount.doubleValue() >= 50 && yearAmount.doubleValue() <= 100) ? "1" : "0");
        //年经营性收入100万元以上的村（1是，0否）
        unitAll.setIncomeAbove100w((yearAmount.doubleValue() > 100) ? "1" : "0");
        //有集体经济组织的村（1是，0否）
        unitAll.setHasCollectiveEconomy(hasCollectiveEconomy);
        //村党组织书记担任村级集体经济组织负责人的村（1是，0否）
        unitAll.setHasEconomicVillage(hasSecretaryPrincipal);
        //年经营性收入
        unitAll.setYearAmount(yearAmount);
        unitAll.setAllIncomeLess5w(yearAmountAll.doubleValue() < 5 ? "1" : "0");

        //年经营性收入5万元以下薄弱村空壳村（集体经济情况）（1是，0否）
        unitAll.setEarningsLess5w((yearEarningsAmount.doubleValue() < 5) ? "1" : "0");
        //年经营性收入50-100万元的村（1是，0否）
        unitAll.setEarnings50w100w((yearEarningsAmount.doubleValue() >= 50 && yearEarningsAmount.doubleValue() <= 100) ? "1" : "0");
        //年经营性收入100万元以上的村（1是，0否）
        unitAll.setEarningsAbove100w((yearEarningsAmount.doubleValue() > 100) ? "1" : "0");
        // 当年经营收益金额
        unitAll.setEarningsAmount(yearEarningsAmount);

        unitAll.setYearAmountAll(yearAmountAll);
    }

    /**
     * 根据组织code修改单位最新主组织组织类型
     */
    public OutMessage setUnitByUnitOrg(String orgCode) {
        List<UnitAll> orgUnitList = unitAllMapper.selectList(new QueryWrapper<UnitAll>().lambda().eq(UnitAll::getMainOrgCode, orgCode).isNull(UnitAll::getDeleteTime));
        if (CollectionUtil.isNotEmpty(orgUnitList)) {
            Org org = orgService.findOrgByCode(orgCode);
            if (Objects.nonNull(org)) {
                for (UnitAll unitAll : orgUnitList) {
                    unitAll.setMainOrgName(org.getName());
                    unitAll.setMainOrgType(org.getD01Code());
                    unitAll.setMainOrgTypeCode(org.getOrgType());
                    unitAll.setD02Code(org.getD02Code());
                    unitAll.setD01Code(org.getD01Code());
                }
            }
            unitAllService.updateBatchById(orgUnitList);
        }
        return null;
    }

    /**
     * 获取单位联合党支部数
     */
    public OutMessage setPartyBranchesNumber(String unitCode) {
        UnitAll unitAll = unitAllService.findByCode(unitCode);
        Unit unit = unitMapper.selectOne(new QueryWrapper<Unit>().lambda().isNull(Unit::getDeleteTime).eq(Unit::getCode, unitCode));
        if (Objects.isNull(unitAll) || Objects.isNull(unit)) {
            return null;
        }
        List<UnitOrgLinked> unitOrgLinkedList = unitOrgLinkedService.findByUnitCode(unitCode);
        unitAll.setPartyBranchesNumber(CommonConstant.ZERO_INT);
        if (CollectionUtil.isNotEmpty(unitOrgLinkedList)) {
            long count = unitOrgLinkedList.stream().filter(s -> CommonConstant.ONE_INT == s.getIsUnitMain() && StrUtil.equalsAny(s.getOrgType(), "632", "634")).count();
            if (count > 0) {
                unitAll.setPartyBranchesNumber(Math.toIntExact(CommonConstant.ONE_INT));
            }
        }
      /*
        String year = CustomConstant.getStatisticalYear();
        List<UnitCountryside> countrysidesAll = countrysideMapper.selectList(new QueryWrapper<UnitCountryside>()
                .eq("unit_code", unitCode).isNull("delete_time").apply("to_char(create_time,'yyyy')='" + year + "'"));
        //有村党组织书记后备人选的行政村
        Optional<UnitCountryside> any = countrysidesAll.stream().filter(e -> StrUtil.equals(e.getType(), "2")).findAny();
        if (any.isPresent()) {
            unitAll.setHasCountrySecretaryHbgb(1);
        }
        Integer collegeWithOne = Objects.nonNull(unitAll.getSecondaryCollegeWithOne()) ? unitAll.getSecondaryCollegeWithOne() : 0;
        Integer greaterTwo = Objects.nonNull(unitAll.getSecondaryCollegeGreaterTwo()) ? unitAll.getSecondaryCollegeGreaterTwo() : 0;
        unitAll.setSecondaryCollegeWithOneTwo(collegeWithOne + greaterTwo);
        //集体经济情况
        this.unitCollectiveEconomic(unit.getCode(), unitAll);*/
        unitAllService.updateById(unitAll);
        unitMapper.updateById(unit);
        return null;
    }


    /**
     * 国务院和地方政府工作部门法人单位建立党组（党委）情况
     *
     * @param unitCode
     * @return
     */
    public void setUnitLeadingParty(String unitCode) {
        UnitAll unitAll = unitAllService.findByCode(unitCode);
        if (Objects.isNull(unitAll)) {
            return;
        }
        List<OrgParty> parties = partyMapper.selectList(new QueryWrapper<OrgParty>()
                .eq("unit_code", unitCode)
                .isNull("delete_time")
        );
        unitAll.setCreatePartyTeam(0);
        unitAll.setCreatePartyGroup(0);
        unitAll.setCreatePartyCommittee(0);
        unitAll.setHasParty(0);
        unitAll.setHasAuthority(CommonConstant.ZERO_INT);
        if (CollectionUtils.isNotEmpty(parties)) {
            long one = parties.stream().filter(s -> StrUtil.isNotEmpty(s.getD108Code()) && s.getD108Code().startsWith(CommonConstant.THREE)).count();
            long two = parties.stream().filter(s -> StrUtil.isNotEmpty(s.getD108Code()) && "37".equals(s.getD108Code())).count();
            long three = parties.stream().filter(s -> StrUtil.isNotEmpty(s.getD108Code()) && s.getD108Code().startsWith(CommonConstant.FOUR)).count();
            //是否机关党组
            long four = parties.stream().filter(s -> StrUtil.isNotEmpty(s.getD108Code()) && !StrUtil.equalsAny(s.getD108Code(), "39", "34", "37")).count();
            //建立党组数字
            if (one > 0) {
                //设置该单位为党组
                unitAll.setHasParty(CommonConstant.ONE_INT);
            } else {
                unitAll.setHasParty(CommonConstant.ZERO_INT);
            }
            if (four > 0) {
                unitAll.setHasAuthority(Math.toIntExact(four));
            } else {
                unitAll.setHasAuthority(CommonConstant.ZERO_INT);
            }
            unitAll.setCreatePartyTeam(Math.toIntExact(one));
            //建 立 分 党 组 数字
            unitAll.setCreatePartyGroup(Math.toIntExact(two));
            //建立党组性质党委数字
            unitAll.setCreatePartyCommittee(Math.toIntExact(three));
        }
        unitAllService.updateById(unitAll);
    }


    /**
     * 是否党组织软弱涣散村（1是）
     *
     * @param unitCode 单位code
     */
    public void setHasOrgSlackVillage(String unitCode, Unit unitAll) {
        if (Objects.isNull(unitAll)) {
            unitAll = iUnitService.findByCode(unitCode);
        }
        if (Objects.nonNull(unitAll)) {
            unitAllService.update(new LambdaUpdateWrapper<UnitAll>().set(UnitAll::getHasOrgSlackVillage, null).eq(UnitAll::getCode, unitCode));
            if (!StrUtil.equalsAny(unitAll.getD04Code(), "921", "922", "923")) {
                return;
            }
            UnitOrgLinked unitOrgLinked = iUnitOrgLinkedService.findByUnitCodeAndIsMainAll(unitCode);
            if (Objects.isNull(unitOrgLinked)) {
                return;
            }
            Org org = orgService.findOrgByCode(unitOrgLinked.getOrgCode());
            if (Objects.isNull(org)) {
                return;
            }
            List<OrgSlack> slackAllList = iOrgSlackService.findSlackByOrgCode(org.getCode());
            String hasOrgSlackVillage = CollUtil.isNotEmpty(slackAllList) ? "1" : null;
            // 查询第一书记信息：统计在任的
            List<UnitResident> residentList = iUnitResidentService.list(new LambdaQueryWrapper<UnitResident>().eq(UnitResident::getd140Code, "1").isNull(UnitResident::getDeleteTime).isNull(UnitResident::getEndDate)
                    .eq(UnitResident::getUnitCode, unitCode));
            if (CollUtil.isNotEmpty(residentList)) {
                unitAllService.update(new LambdaUpdateWrapper<UnitAll>().set(UnitAll::getHasOrgSlackVillage, hasOrgSlackVillage).isNull(UnitAll::getDeleteTime).eq(UnitAll::getCode, unitCode));
                iMemAllInfoService.update(new LambdaUpdateWrapper<MemAllInfo>().set(MemAllInfo::getHasOrgSlackVillage, hasOrgSlackVillage)
                        .in(MemAllInfo::getCode, residentList.stream().map(UnitResident::getCode).collect(Collectors.toSet())));
            }
        }
    }


    /**
     * 初始化 村集体经济年经营性收入
     */
    public void initYearAmount() {
        unitAllService.update(new LambdaUpdateWrapper<UnitAll>().set(UnitAll::getIncomeLess5w, null).set(UnitAll::getIncome50w100w, null).set(UnitAll::getIncomeAbove100w, null)
                .set(UnitAll::getHasCollectiveEconomy, null).set(UnitAll::getHasEconomicVillage, null).set(UnitAll::getYearAmount, null)
                .isNull(UnitAll::getDeleteTime).apply("year_amount is not null and year_amount !=0.00"));
        unitAllService.update(new LambdaUpdateWrapper<UnitAll>().set(UnitAll::getAllIncomeLess5w, null).set(UnitAll::getYearAmountAll, null)
                .isNull(UnitAll::getDeleteTime).isNotNull(UnitAll::getYearAmountAll));
        List<UnitCollectiveEconomic> economicList = unitCollectiveEconomicMapper.selectList(new LambdaQueryWrapper<UnitCollectiveEconomic>().isNull(UnitCollectiveEconomic::getDeleteTime));
        economicList.stream().map(UnitCollectiveEconomic::getUnitCode).filter(StrUtil::isNotEmpty).collect(Collectors.toCollection(HashSet::new)).forEach(unitCode -> {
            UnitAll unitAll = unitAllService.findByCode(unitCode);
            if (Objects.nonNull(unitAll)) {
                this.unitCollectiveEconomic(unitCode, unitAll);
                unitAllService.updateById(unitAll);
            }
        });

    }

    /**
     * 初始化党组
     */
    public void initOrgParty() {
        unitAllService.update(new LambdaUpdateWrapper<UnitAll>()
                .set(UnitAll::getHasParty, null).set(UnitAll::getHasAuthority, null).set(UnitAll::getCreatePartyTeam, null)
                .set(UnitAll::getCreatePartyGroup, null).set(UnitAll::getCreatePartyCommittee, null)
                .isNull(UnitAll::getDeleteTime));
        List<OrgParty> partyList = partyMapper.selectList(new LambdaQueryWrapper<OrgParty>().select(OrgParty::getUnitCode).isNull(OrgParty::getDeleteTime).isNotNull(OrgParty::getUnitCode));
        partyList.stream().map(OrgParty::getUnitCode).collect(Collectors.toCollection(HashSet::new)).forEach(this::setUnitLeadingParty);
    }


    /**
     * 初始化企业本级党组织书记
     */
    public void initHasQyOwnLevel() {
        // 企业本级党组织书记
        iMemAllInfoService.update(new UpdateWrapper<MemAllInfo>().lambda().set(MemAllInfo::getHasQyOwnLevel, null).isNotNull(MemAllInfo::getHasQyOwnLevel).isNull(MemAllInfo::getDeleteTime));
        List<Unit> unitList = unitMapper.selectList(new LambdaQueryWrapper<Unit>().select(Unit::getCode).likeRight(Unit::getD04Code, "4").isNull(Unit::getDeleteTime));
        unitList.parallelStream().forEach(e -> this.updateQyOwnLevel(e.getCode()));
    }


    /**
     * 初始化是否属于关联单位本级
     */
    public void initHasUnitOwnLevel() {
        // 清除是否属于关联单位本级
        iOrgAllService.update(new UpdateWrapper<OrgAll>().lambda().set(OrgAll::getHasUnitOwnLevel, null).isNotNull(OrgAll::getHasUnitOwnLevel));
        iMemAllInfoService.update(new UpdateWrapper<MemAllInfo>().lambda().set(MemAllInfo::getHasUnitOwnLevel, null).set(MemAllInfo::getD022Code, null).set(MemAllInfo::getD121Code, null)
                .set(MemAllInfo::getHasPartTraining, null).set(MemAllInfo::getD25Code, null).set(MemAllInfo::getUnitD121Code, null)
                .set(MemAllInfo::getHasUnitOwnLevelOrg, null)
                .and(e -> e.isNotNull(MemAllInfo::getHasUnitOwnLevel).or().isNotNull(MemAllInfo::getHasUnitOwnLevelOrg))
                .isNull(MemAllInfo::getDeleteTime));
        List<Unit> list = unitMapper.selectList(new LambdaQueryWrapper<Unit>().select(Unit::getCode).in(Unit::getD04Code, "911", "9121", "9122", "921", "922", "923").isNull(Unit::getDeleteTime)
                .orderByAsc(Unit::getD04Code)); // 排序
        //设置是否属于关联单位本级
        list.forEach(e -> this.updateUnitOwnLevel(e.getCode()));

        // 同步党组织是否本级标识
        List<Unit> unitList = unitMapper.selectList(new LambdaQueryWrapper<Unit>().select(Unit::getCode).isNull(Unit::getDeleteTime));
        unitList.parallelStream().forEach(e -> this.updateOrgOwnLevel(e.getCode()));

    }

    /**
     * 是否属于关联单位本级
     * 1 城市街道，2 乡镇，3 城市社区，4 行政村，5 乡镇社区
     */
    public void updateUnitOwnLevel(String unitCode) {
        Unit unit = iUnitService.findByCode(unitCode);
        //911 城市街道，9121 乡，9122 镇
        //921 城市社区 922 乡镇社区 923 行政村
        if (Objects.isNull(unit) || !StrUtil.startWithAny(unit.getD04Code(), "91", "92")) {
            return;
        }
        String unitOwnLevel = getMemUnitOwnLevelValue(unit.getD04Code());
        String lastDate = com.zenith.front.common.kit.DateUtil.lastYearDay(iStatisticsYearService.getStatisticalYear());
        // 处理表6 街道党组织书记，社区委员书记，处理表5 行政村委员书记
        if (!Objects.equals(unit.getIsLegal(), 1) || !StrUtil.startWithAny(unit.getD04Code(), "911", "92")) {
            return;
        }
        // 党组织班子主单位
        UnitOrgLinked orgLinked = iUnitOrgLinkedService.findByUnitCodeAndIsMainAll(unitCode);
        if (Objects.nonNull(orgLinked)) {
            Org org = orgService.findOrgByCode(orgLinked.getOrgCode());
            if (Objects.nonNull(org)) {
                //获取党组织最新届次
                OrgCommitteeElect nowElect = orgElectMapper.findNewestElect(org.getCode(), lastDate);
                if (Objects.nonNull(nowElect)) {
                    //获取最新届次人员
                    List<OrgCommittee> commitByElect = iOrgCommitteeService.findCommitByElect(nowElect.getCode());
                    if (CollUtil.isNotEmpty(commitByElect)) {
                        Set<String> memCodes = commitByElect.stream().map(OrgCommittee::getMemCode).filter(StrUtil::isNotEmpty).collect(Collectors.toSet());
                        List<OrgCommittee> notPartyList = commitByElect.stream().filter(e -> StrUtil.isEmpty(e.getMemCode()) && StrUtil.isNotEmpty(e.getMemName()) && StrUtil.isNotEmpty(e.getMemIdcard())).collect(Collectors.toList());
                        if (CollUtil.isNotEmpty(memCodes)) {
                            commitByElect.stream().filter(e -> StrUtil.isNotEmpty(e.getMemCode())).forEach(e ->
                                    iMemAllInfoService.update(new UpdateWrapper<MemAllInfo>().lambda().set(MemAllInfo::getHasUnitOwnLevelOrg, unitOwnLevel).set(MemAllInfo::getD022Code, e.getD022Code())
                                            .set(MemAllInfo::getD120Code, e.getD120Code()) // todo 2024-1-1 add:d120_code
                                            .set(MemAllInfo::getD121Code, e.getD121Code()).set(MemAllInfo::getHasPartTraining, e.getHasPartTraining()).eq(MemAllInfo::getCode, e.getMemCode())));
                        }
                        if (CollUtil.isNotEmpty(notPartyList)) {
                            notPartyList.forEach(e ->
                                    iMemAllInfoService.update(new UpdateWrapper<MemAllInfo>().lambda().set(MemAllInfo::getHasUnitOwnLevelOrg, unitOwnLevel).set(MemAllInfo::getD022Code, e.getD022Code())
                                            .set(MemAllInfo::getD120Code, e.getD120Code()) // todo 2024-1-1  add:d120_code
                                            .set(MemAllInfo::getD121Code, e.getD121Code()).set(MemAllInfo::getHasPartTraining, e.getHasPartTraining()).eq(MemAllInfo::getCode, e.getCode())));
                        }
                    }
                }
            }
        }

        // 单位班子村社区主任的规则改为不判断主单位
        UnitOrgLinked unitOrgLinked = iUnitOrgLinkedService.findByUnitCodeAndIsMainOrg(unitCode);
        if (Objects.isNull(unitOrgLinked)) {
            return;
        }
        Org org = orgService.findOrgByCode(unitOrgLinked.getOrgCode());
        if (Objects.isNull(org) || StrUtil.equals(org.getD02Code(), "2")) {
            return;
        }
        if (StrUtil.equals(org.getD02Code(), "4") && !Objects.equals(unitOrgLinked.getIsUnitMain(), 1)) {
            return;
        }

        //获取单位班子最新届次
        UnitCommitteeElect unitElect = unitElectMapper.findNewestElect(unitCode, lastDate);
        if (Objects.nonNull(unitElect)) {
            List<UnitCommittee> commitByElect = iUnitCommitteeService.findByElect(unitElect.getCode());
            if (CollUtil.isNotEmpty(commitByElect)) {
                Set<String> memCodes = commitByElect.stream().map(UnitCommittee::getMemCode).filter(StrUtil::isNotEmpty).collect(Collectors.toSet());
                List<UnitCommittee> notPartyList = commitByElect.stream().filter(e -> StrUtil.isEmpty(e.getMemCode()) && StrUtil.isNotEmpty(e.getMemName()) && StrUtil.isNotEmpty(e.getMemIdcard())).collect(Collectors.toList());
                if (CollUtil.isNotEmpty(memCodes)) {
                    commitByElect.stream().filter(e -> StrUtil.isNotEmpty(e.getMemCode())).forEach(e -> iMemAllInfoService.update(new UpdateWrapper<MemAllInfo>().lambda().set(MemAllInfo::getHasUnitOwnLevel, unitOwnLevel)
                            .set(MemAllInfo::getD25Code, e.getD25Code()).set(MemAllInfo::getUnitD121Code, e.getD0121Code()).eq(MemAllInfo::getCode, e.getMemCode())));
                }
                if (CollUtil.isNotEmpty(notPartyList)) {
                    notPartyList.forEach(e -> iMemAllInfoService.update(new UpdateWrapper<MemAllInfo>().lambda().set(MemAllInfo::getHasUnitOwnLevel, unitOwnLevel)
                            .set(MemAllInfo::getD25Code, e.getD25Code()).set(MemAllInfo::getUnitD121Code, e.getD0121Code()).eq(MemAllInfo::getCode, e.getCode())));
                }
            }
        }
    }

    private String getMemUnitOwnLevelValue(String d04Code) {
        switch (d04Code) {
            case "911":
                return "1";
            case "921":
                return "3";
            case "922":
                return "5";
            case "923":
                return "4";
            default:
                return null;
        }
    }

    /**
     * 企业本级党组织书记
     */
    private void updateQyOwnLevel(String unitCode) {
        Unit unit = iUnitService.findByCode(unitCode);
        if (Objects.isNull(unit) || !StrUtil.startWith(unit.getD04Code(), "4") || !Objects.equals(unit.getIsLegal(), 1)) {
            return;
        }
        UnitOrgLinked unitOrgLinked = iUnitOrgLinkedService.findByUnitCodeAndIsMainAll(unitCode);
        if (Objects.isNull(unitOrgLinked)) {
            return;
        }
        Org org = orgService.findOrgByCode(unitOrgLinked.getOrgCode());
        if (Objects.isNull(org)) {
            return;
        }
        String lastDate = com.zenith.front.common.kit.DateUtil.lastYearDay(iStatisticsYearService.getStatisticalYear());
        OrgCommitteeElect nowElect = orgElectMapper.findNewestElect(org.getCode(), lastDate);
        if (Objects.nonNull(nowElect)) {
            List<OrgCommittee> commitByElect = iOrgCommitteeService.findCommitByElect(nowElect.getCode());
            if (CollUtil.isNotEmpty(commitByElect)) {
                List<OrgCommittee> newList = commitByElect.stream().filter(e -> StrUtil.equals(e.getD022Code(), "1")).collect(Collectors.toList());
                if (CollUtil.isNotEmpty(newList)) {
                    UnitExtend unitExtend = iUnitExtendService.findByCode(unitCode);
                    //1	互联网
                    String d114Code = Objects.nonNull(unitExtend) ? unitExtend.getD114Code() : "";
                    Set<String> memCodes = newList.stream().map(OrgCommittee::getMemCode).filter(StrUtil::isNotEmpty).collect(Collectors.toSet());
                    List<OrgCommittee> notPartyList = newList.stream().filter(e -> StrUtil.isEmpty(e.getMemCode())).collect(Collectors.toList());
                    if (CollUtil.isNotEmpty(memCodes)) {
                        memCodes.forEach(memCode -> iMemAllInfoService.update(new UpdateWrapper<MemAllInfo>().lambda().set(MemAllInfo::getHasQyOwnLevel, "1" + d114Code).eq(MemAllInfo::getCode, memCode)));
                    }
                    if (CollUtil.isNotEmpty(notPartyList)) {
                        notPartyList.forEach(e -> iMemAllInfoService.update(new UpdateWrapper<MemAllInfo>().lambda().set(MemAllInfo::getHasQyOwnLevel, "1" + d114Code).eq(MemAllInfo::getCode, e.getCode())));
                    }
                }
            }
        }
    }


    /**
     * 同步党组织是否本级
     */
    public void updateOrgOwnLevel(String unitCode) {
        Unit unit = iUnitService.findByCode(unitCode);
        if (Objects.isNull(unit)) {
            return;
        }
        UnitOrgLinked unitOrgLinked = iUnitOrgLinkedService.findByUnitCodeAndIsMainAll(unitCode);
        if (Objects.isNull(unitOrgLinked)) {
            return;
        }
        Org org = orgService.findOrgByCode(unitOrgLinked.getOrgCode());
        Integer isLegal = unit.getIsLegal();
        // TODO: 2023/1/20 党组织本级是需要增加是法人单位情况
        if (Objects.nonNull(org) && isLegal.equals(CommonConstant.ONE_INT)) {
            iOrgAllService.update(new UpdateWrapper<OrgAll>().lambda().set(OrgAll::getHasUnitOwnLevel, "1").eq(OrgAll::getCode, org.getCode()));
        }

    }


    /**
     * 初始化驻村干部第一书记
     */
    public void initUnitResident() {
        iMemAllInfoService.remove(new QueryWrapper<MemAllInfo>().lambda().in(MemAllInfo::getD08Code, "5", "50"));
        List<UnitResident> residentList = iUnitResidentService.list(new LambdaQueryWrapper<UnitResident>().in(UnitResident::getd140Code, "1", "2").isNull(UnitResident::getDeleteTime));
        for (UnitResident e : residentList) {
            UnitAll unit = unitAllService.findByCode(e.getUnitCode());
            if (Objects.isNull(unit) || !StrUtil.startWith(unit.getD04Code(), "92")) {
                continue;
            }
            MemAllInfo memAllInfo = new MemAllInfo();
            BeanUtils.copyProperties(e, memAllInfo);
            boolean exist = false;
            if (StrUtil.equals(e.getd139Code(), "1")) {
                Mem mem = iMemService.findAllByCode(e.getMemCode());
                if (Objects.nonNull(mem)) {
                    BeanUtils.copyProperties(mem, memAllInfo);
                    exist = true;
                }
            }
            memAllInfo.setCode(e.getCode());
            String orgCode = StrUtil.isNotEmpty(unit.getMainOrgCode()) ? unit.getMainOrgCode() : unit.getCreateOrgCode();
            String memOrgCode = StrUtil.isNotEmpty(unit.getMainUnitOrgCode()) ? unit.getMainUnitOrgCode() : unit.getCreateUnitOrgCode();
            memAllInfo.setOrgCode(orgCode);
            memAllInfo.setMemOrgCode(memOrgCode);
            memAllInfo.setOrgZbCode(unit.getCreateOrgZbCode());
            syncMemService.setMemAllValue(memAllInfo, unit);
            memAllInfo.setDeleteTime(null);
            memAllInfo.setOrgCommitCode(Objects.nonNull(e.getStartDate()) ? StrUtil.toString(DateUtil.year(e.getStartDate())) : null);
            memAllInfo.setD155Code(unit.getD155Code());
            if (!exist) {
                memAllInfo.setName(e.getMemName());
                memAllInfo.setIdcard(e.getMemIdcard());
                memAllInfo.setAge(Objects.nonNull(e.getMemBirthday()) ? DateUtil.ageOfNow(e.getMemBirthday()) : null);
            }

            if (Objects.equals(e.getd140Code(), "1")) {
                // 第一书记
                memAllInfo.setD08Code("5");
            } else {
                // 确认选派工作队员是否为 驻村干部
                memAllInfo.setD08Code("50");
            }
            // 选派层级，todo：这种数据不会在其他表中统计。所以用这个字段来接收选派层级
            memAllInfo.setIndustry(Objects.isNull(e.getD197Code()) ? "" : e.getD197Code());
            memAllInfo.setEndDate(e.getEndDate());
            iMemAllInfoService.save(memAllInfo);
        }

        // 初始化党组织软弱涣散村的
        unitAllService.update(new LambdaUpdateWrapper<UnitAll>().set(UnitAll::getHasOrgSlackVillage, null).isNotNull(UnitAll::getHasOrgSlackVillage));
        iMemAllInfoService.update(new LambdaUpdateWrapper<MemAllInfo>().set(MemAllInfo::getHasOrgSlackVillage, null).isNotNull(MemAllInfo::getHasOrgSlackVillage));
        List<Unit> list = unitMapper.selectList(new LambdaQueryWrapper<Unit>().select(Unit::getCode, Unit::getD04Code).in(Unit::getD04Code, "921", "922", "923").eq(Unit::getIsLegal, 1).isNull(Unit::getDeleteTime));
        list.parallelStream().forEach(unit -> this.setHasOrgSlackVillage(unit.getCode(), unit));
    }

    /**
     * 单位信息初始化
     */
    public OutMessage<?> initUnitAllInfo() {
        initYearAmount();
        List<Unit> list = unitMapper.selectList(new LambdaQueryWrapper<Unit>().select(Unit::getCode).isNull(Unit::getDeleteTime));
        list.parallelStream().forEach(e -> {
            //关联组织当代会信息 x（单位扩展信息同步）
            setBaseUnit(e.getCode(), "2");
            //同步城市街道干部信息
//            setBaseUnit(e.getCode(), "6");
        });
        // 初始化社区工作者，二级院校信息
        List<UnitAll> unitAllList = unitAllService.list(new LambdaQueryWrapper<UnitAll>().select(UnitAll::getId, UnitAll::getCode).isNull(UnitAll::getDeleteTime));
        unitAllList.parallelStream().forEach(unitAll -> {
            this.setCommunityWorker(unitAll.getCode(), unitAll);
            this.setColleges(unitAll.getCode(), unitAll);
            unitAllService.updateById(unitAll);
        });

        initUnitResident();
        initHasQyOwnLevel();
        initOrgParty();

        return null;
    }

    /**
     * 同步单位基层党建情况
     */
    public void syncUnitCitySituation(String unitCode) {
        UnitAll unitAll = unitAllMapper.selectOne(new QueryWrapper<UnitAll>().lambda().eq(UnitAll::getCode, unitCode));
        if (Objects.isNull(unitAll)) {
            log.error("同步失败！unitAll对象不存在，code= " + unitCode);
            return;
        }
        LambdaQueryWrapper<UnitCitySituation> sql = new LambdaQueryWrapper<>();
        sql.eq(UnitCitySituation::getUnitCode, unitCode).isNull(UnitCitySituation::getDeleteTime).orderByDesc(UnitCitySituation::getCreateTime).last("LIMIT 1");
        UnitCitySituation one = iUnitCitySituationService.getOne(sql);
        UnitCitySituation updateUnitCitySituation = new UnitCitySituation();
        if (Objects.nonNull(one)) {
            BeanUtils.copyProperties(one, updateUnitCitySituation);
        }
        unitAll.setGrids(updateUnitCitySituation.getGrids());
        unitAll.setJmzzlyWgs(updateUnitCitySituation.getJmzzlyWgs());
        unitAll.setJmzzlyDzWgs(updateUnitCitySituation.getJmzzlyDzWgs());
        unitAll.setSwsqWgs(updateUnitCitySituation.getSwsqWgs());
        unitAll.setSwsqDzWgs(updateUnitCitySituation.getSwsqDzWgs());
        unitAll.setJdldWgs(updateUnitCitySituation.getJdldWgs());
        unitAll.setGridMembers(updateUnitCitySituation.getGridMembers());
        unitAll.setZzWgys(updateUnitCitySituation.getZzWgys());
        unitAll.setZzNgzze(updateUnitCitySituation.getZzNgzze());
        unitAll.setJzWgys(updateUnitCitySituation.getJzWgys());
        unitAll.setResidentialAreas(updateUnitCitySituation.getResidentialAreas());
        unitAll.setTubePlots(updateUnitCitySituation.getTubePlots());
        unitAll.setOrganizationCompanies(updateUnitCitySituation.getOrganizationCompanies());
        unitAll.setIndustryAuthorityCommunity(updateUnitCitySituation.getIndustryAuthorityCommunity());
        unitAll.setIndustryAuthorityOrganization(updateUnitCitySituation.getIndustryAuthorityOrganization());
        unitAll.setThreePartiesCommunities(updateUnitCitySituation.getThreePartiesCommunities());
        unitAllService.update(unitAll, Wrappers.<UnitAll>lambdaUpdate()
                .set(Objects.isNull(unitAll.getGrids()), UnitAll::getGrids, null)
                .set(Objects.isNull(unitAll.getJmzzlyWgs()), UnitAll::getJmzzlyWgs, null)
                .set(Objects.isNull(unitAll.getJmzzlyDzWgs()), UnitAll::getJmzzlyDzWgs, null)
                .set(Objects.isNull(unitAll.getSwsqWgs()), UnitAll::getSwsqWgs, null)
                .set(Objects.isNull(unitAll.getSwsqDzWgs()), UnitAll::getSwsqDzWgs, null)
                .set(Objects.isNull(unitAll.getJdldWgs()), UnitAll::getJdldWgs, null)
                .set(Objects.isNull(unitAll.getGridMembers()), UnitAll::getGridMembers, null)
                .set(Objects.isNull(unitAll.getZzWgys()), UnitAll::getZzWgys, null)
                .set(Objects.isNull(unitAll.getZzNgzze()), UnitAll::getZzNgzze, null)
                .set(Objects.isNull(unitAll.getJzWgys()), UnitAll::getJzWgys, null)
                .set(Objects.isNull(unitAll.getResidentialAreas()), UnitAll::getResidentialAreas, null)
                .set(Objects.isNull(unitAll.getTubePlots()), UnitAll::getTubePlots, null)
                .set(Objects.isNull(unitAll.getOrganizationCompanies()), UnitAll::getOrganizationCompanies, null)
                .set(Objects.isNull(unitAll.getIndustryAuthorityCommunity()), UnitAll::getIndustryAuthorityCommunity, null)
                .set(Objects.isNull(unitAll.getIndustryAuthorityOrganization()), UnitAll::getIndustryAuthorityOrganization, null)
                .set(Objects.isNull(unitAll.getThreePartiesCommunities()), UnitAll::getThreePartiesCommunities, null)
                .eq(UnitAll::getId, unitAll.getId()));
    }

    /**
     * 单位类别为行政村的时候，单位班子职务为5开头的社区任职改为4开头的村任职。
     * 如果社区班子成员改为行政村的4开头任职改为5开头的任职。-- 夏云松
     */
    public void processUnitCommitteeD25() {
        List<Unit> list = unitMapper.selectList(new LambdaQueryWrapper<Unit>().select(Unit::getCode, Unit::getD04Code).in(Unit::getD04Code, "921", "922", "923")
                .isNull(Unit::getDeleteTime).orderByAsc(Unit::getD04Code));
        List<UnitCommittee> updateList = new ArrayList<>();
        list.forEach(e -> {
            List<UnitCommittee> committeeList = iUnitCommitteeService.findByUnitCode(e.getCode());
            if (CollUtil.isNotEmpty(committeeList)) {
                // 行政村中，班子成员职务代码为5开头的换成4
                if (StrUtil.equals(e.getD04Code(), "923")) {
                    List<UnitCommittee> collect = committeeList.stream().filter(o -> StrUtil.startWith(o.getD25Code(), "5")).collect(Collectors.toList());
                    if (CollUtil.isNotEmpty(collect)) {
                        collect.forEach(unitCommittee -> {
                            UnitCommittee updateM = new UnitCommittee();
                            updateM.setId(unitCommittee.getId());
                            updateM.setD25Code(unitCommittee.getD25Code().replace("5", "4"));
                            updateM.setD25Name(unitCommittee.getD25Name().replace("社区（居委会）", "村委会"));
                            updateList.add(updateM);
                        });
                    }
                }
                // 城市社区,乡镇社区中，班子成员职务代码为4开头的换成5
                if (StrUtil.equalsAny(e.getD04Code(), "921", "922")) {
                    List<UnitCommittee> collect = committeeList.stream().filter(o -> StrUtil.startWith(o.getD25Code(), "4")).collect(Collectors.toList());
                    if (CollUtil.isNotEmpty(collect)) {
                        collect.forEach(unitCommittee -> {
                            UnitCommittee updateM = new UnitCommittee();
                            updateM.setId(unitCommittee.getId());
                            updateM.setD25Code(unitCommittee.getD25Code().replace("4", "5"));
                            updateM.setD25Name(unitCommittee.getD25Name().replace("村委会", "社区（居委会）"));
                            updateList.add(updateM);
                        });
                    }
                }
            }
        });

        if (CollUtil.isNotEmpty(updateList)) {
            iUnitCommitteeService.updateBatchById(updateList, updateList.size());
            System.out.println("村社区班子成员任职代码更新完成....." + updateList.size());
        }

    }


}
