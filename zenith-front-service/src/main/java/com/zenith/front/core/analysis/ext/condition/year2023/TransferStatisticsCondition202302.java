package com.zenith.front.core.analysis.ext.condition.year2023;

import com.b1809.analysis.extend.GenSqlConditionFuc;
import org.jooq.Condition;
import org.jooq.Field;

import static org.jooq.impl.DSL.field;
import static org.jooq.impl.DSL.name;

/**
 * 关系转接
 *
 * <AUTHOR>
 * @date 2022/6/28
 */
public class TransferStatisticsCondition202302 implements GenSqlConditionFuc {

    @Override
    public String getTableName() {
        return "ccp_transfer_statistics_202302";
    }

    @Override
    public Condition create(String orgCode, String orgLevelCode) {
        return getLevelCodeField().like(orgLevelCode + "%");
    }

    @Override
    public Field<?> getLevelCodeField() {
        return field(name("transfer_org_org_code"), String.class);
    }
}
