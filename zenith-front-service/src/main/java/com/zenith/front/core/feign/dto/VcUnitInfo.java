package com.zenith.front.core.feign.dto;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2022-08-23
 */
public class VcUnitInfo {

    /**
     * 单位主键
     */
    private String code;

    /**
     * 单位名称
     */
    private String unitName;

    /**
     * 单位代码
     */
    private String creditCode;

    /**
     * 单位类别
     */
    private String d04Code;

    /**
     * 单位类别名称
     */
    private String d04Name;

    /**
     * 单位隶属关系
     */
    private String d35Code;

    /**
     * 单位隶属关系名称
     */
    private String d35Name;

    /**
     * 是否法人单位标识：1是，0否
     */
    private Integer isLegal;

    /**
     * 组织主键
     */
    private String orgCode;

    /**
     * 数据类型 -1 新增数据 0现存数据 1删除数据
     */
    private String dataType;

    /**
     * 节点key
     */
    private String nginxKey;

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getUnitName() {
        return unitName;
    }

    public void setUnitName(String unitName) {
        this.unitName = unitName;
    }

    public String getCreditCode() {
        return creditCode;
    }

    public void setCreditCode(String creditCode) {
        this.creditCode = creditCode;
    }

    public String getD04Code() {
        return d04Code;
    }

    public void setD04Code(String d04Code) {
        this.d04Code = d04Code;
    }

    public String getD04Name() {
        return d04Name;
    }

    public void setD04Name(String d04Name) {
        this.d04Name = d04Name;
    }

    public String getD35Code() {
        return d35Code;
    }

    public void setD35Code(String d35Code) {
        this.d35Code = d35Code;
    }

    public String getD35Name() {
        return d35Name;
    }

    public void setD35Name(String d35Name) {
        this.d35Name = d35Name;
    }

    public Integer getIsLegal() {
        return isLegal;
    }

    public void setIsLegal(Integer isLegal) {
        this.isLegal = isLegal;
    }

    public String getOrgCode() {
        return orgCode;
    }

    public void setOrgCode(String orgCode) {
        this.orgCode = orgCode;
    }

    public String getDataType() {
        return dataType;
    }

    public void setDataType(String dataType) {
        this.dataType = dataType;
    }

    public String getNginxKey() {
        return nginxKey;
    }

    public void setNginxKey(String nginxKey) {
        this.nginxKey = nginxKey;
    }
    @Override
    public String toString() {
        return "VcUnitInfo{" +
                "code='" + code + '\'' +
                ", unitName='" + unitName + '\'' +
                ", creditCode='" + creditCode + '\'' +
                ", d04Code='" + d04Code + '\'' +
                ", d04Name='" + d04Name + '\'' +
                ", d35Code='" + d35Code + '\'' +
                ", d35Name='" + d35Name + '\'' +
                ", isLegal=" + isLegal +
                ", orgCode='" + orgCode + '\'' +
                ", dataType='" + dataType + '\'' +
                ", nginxKey='" + nginxKey + '\'' +
                '}';
    }
}
