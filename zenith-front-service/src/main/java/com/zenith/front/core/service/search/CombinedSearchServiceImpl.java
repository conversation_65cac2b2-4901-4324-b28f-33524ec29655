package com.zenith.front.core.service.search;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.jfinal.plugin.activerecord.Record;
import com.zenith.front.api.codetable.ICodeTableColService;
import com.zenith.front.api.dict.IDictService;
import com.zenith.front.api.report.IStatisticsYearService;
import com.zenith.front.api.search.ICombinedSearchService;
import com.zenith.front.common.constant.CommonConstant;
import com.zenith.front.common.constant.DictConstant;
import com.zenith.front.common.constant.SearchConstant;
import com.zenith.front.common.constant.UserConstant;
import com.zenith.front.common.kit.StrKit;
import com.zenith.front.common.mybatisplus.WrapperUtil;
import com.zenith.front.core.cache.CacheUtils;
import com.zenith.front.core.kit.CollectionUtil;
import com.zenith.front.core.kit.ExcelUtil;
import com.zenith.front.core.service.mem.MemServiceImpl;
import com.zenith.front.dao.mapper.mem.MemDevelopMapper;
import com.zenith.front.model.bean.*;
import com.zenith.front.model.dto.CombinedSearchDto;
import com.zenith.front.model.dto.CombinedSearchSubDto;
import com.zenith.front.model.dto.ExportDTO;
import com.zenith.front.model.dto.SearchMemDto;
import com.zenith.front.model.message.OutMessage;
import com.zenith.front.model.message.Status;
import com.zenith.front.model.vo.MemDevelopVo;
import com.zenith.front.model.vo.MemVO;
import org.jooq.Condition;
import org.jooq.Record1;
import org.jooq.SelectConditionStep;
import org.jooq.impl.DSL;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.lang.reflect.Field;
import java.math.BigDecimal;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

import static com.zenith.front.core.kit.DbUtil.DSL_CONTEXT;
import static org.jooq.impl.DSL.*;

/**
 * 组合查询
 *
 * <AUTHOR>
 * @date 2022/3/7
 */
@Service
public class CombinedSearchServiceImpl implements ICombinedSearchService {

    @Resource
    private MemDevelopMapper memDevelopMapper;
    @Resource
    private ICodeTableColService iCodeTableColService;
    @Resource
    private MemServiceImpl memService;
    @Resource
    private IDictService iDictService;
    @Resource
    private IStatisticsYearService iStatisticsYearService;
    @Resource
    HttpServletResponse response;


    @Override
    public OutMessage<?> combined(CombinedSearchDto searchDto, User user) {
        if (StrUtil.isEmpty(searchDto.getOrgLevelCode())) {
            searchDto.setOrgLevelCode(user.getOrgCode());
        }
        List<String> combinedSql = this.getCombinedSql(searchDto);
        if (CollUtil.isEmpty(combinedSql)) {
            return new OutMessage<>(Status.BRACKETS_ERROR);
        }
        String sql = combinedSql.get(0) + combinedSql.get(1) + combinedSql.get(2);
        if (StrUtil.containsAny(sql, "date_error")) {
            return new OutMessage<>(500, "时间格式填写不正确，请以此格式填写例如:2022.01.01", null);
        }
        Long total = this.getPageTotalCountSQL(combinedSql.get(1));
        List<Map<String, Object>> mapList = memDevelopMapper.selectListBySql(sql + " limit " + searchDto.getPageSize() + " offset " + (searchDto.getPageNum() - 1) * searchDto.getPageSize());
        List<MemVO> voList = new ArrayList<>();
        mapList.forEach(e -> {
            MemVO memVO = JSON.parseObject(JSON.toJSONString(e), MemVO.class);
            memVO.setOrgName(CacheUtils.getOrgName(memVO.getOrgCode()));
            memVO.setPhone("*");
            voList.add(memVO);
        });
        Page<MemVO> voPage = new Page<>(searchDto.getPageNum(), searchDto.getPageSize());
        voPage.setRecords(voList);
        voPage.setTotal(total);
        return new OutMessage<>(Status.SUCCESS, voPage);
    }

    @Override
    public OutMessage<?> savePlan(CombinedSearchDto data, User user) {
        return new OutMessage<>(Status.SUCCESS);
    }

    @Override
    public OutMessage<?> searchMem(SearchMemDto searchMemDto, User user) {
        if (StrUtil.isEmpty(searchMemDto.getOrgLevelCode())) {
            searchMemDto.setOrgLevelCode(user.getOrgCode());
        }
        // 查询指定加密字段
        String selectSql = WrapperUtil.existsEncrypt(new LambdaQueryWrapper<>(), Mem.class, "name", "idcard").getSqlSelect();
        List<String> stringList = this.getSearchMemSql(searchMemDto, selectSql);
        if (CollUtil.isEmpty(stringList)) {
            return new OutMessage<>(Status.SUCCESS);
        }
        Page<MemVO> voPage = new Page<>(searchMemDto.getPageNum(), searchMemDto.getPageSize());
        Page<Map<String, Object>> mapPage = memDevelopMapper.selectPageBySql(voPage, stringList.get(0) + stringList.get(1));
        List<MemVO> list = new ArrayList<>();
        mapPage.getRecords().forEach(e -> {
            MemVO memVO = JSON.parseObject(JSON.toJSONString(e), MemVO.class);
            memVO.setOrgName(CacheUtils.getOrgName(memVO.getOrgCode()));
            memVO.setPhone("***");
            list.add(memVO);
        });
        BeanUtils.copyProperties(mapPage, voPage);
        voPage.setRecords(list);
        return new OutMessage<>(Status.SUCCESS, voPage);
    }

    @Override
    public OutMessage<?> searchDevelopStepLog(SearchMemDto searchMemDto, User user) {
        if (StrUtil.isEmpty(searchMemDto.getOrgLevelCode())) {
            searchMemDto.setOrgLevelCode(user.getOrgCode());
        }
        // 查询指定加密字段
        String selectSql = WrapperUtil.existsEncrypt(new LambdaQueryWrapper<>(), DevelopStepLogAll.class, "name", "idcard").getSqlSelect();
        List<String> stringList = this.getSearchDevelopStepLogSql(searchMemDto, selectSql);
        if (CollUtil.isEmpty(stringList)) {
            return new OutMessage<>(Status.SUCCESS);
        }
        Page<MemDevelopVo> voPage = new Page<>(searchMemDto.getPageNum(), searchMemDto.getPageSize());
        Page<Map<String, Object>> mapPage = memDevelopMapper.selectPageBySql(voPage, stringList.get(0) + stringList.get(1));
        List<MemDevelopVo> list = new ArrayList<>();
        mapPage.getRecords().forEach(e -> {
            // TODO: 2022/6/16 修复json转实体类时，id为字符串转Long异常
            e.remove("id");
            MemDevelopVo memVO = JSON.parseObject(JSON.toJSONString(e), MemDevelopVo.class);
            memVO.setOrgName(CacheUtils.getOrgName(memVO.getOrgCode()));
            memVO.setD08Code(CommonConstant.TWO);
            memVO.setD08Name("预备党员");
            list.add(memVO);
        });
        BeanUtils.copyProperties(mapPage, voPage);
        voPage.setRecords(list);
        return new OutMessage<>(Status.SUCCESS, voPage);
    }

    /**
     * 导出党员列表
     */
    @Override
    public void exportMemExcel(SearchMemDto searchMemDto, User user) {
        List<String> searchMemSql = this.getSearchMemSql(searchMemDto, null);
        if (CollUtil.isEmpty(searchMemSql)) {
            ExcelUtil.exportErrorExcel(response, new OutMessage<>(Status.PERMISSION_DENIED));
            return;
        }
        Condition condition = noCondition().and(searchMemSql.get(2));
        // 字段集合
        List<ExportDTO> exportList = searchMemDto.getExportList();
        String fileName = CacheUtils.getOrgName(searchMemDto.getOrgCode()) + "党员名册";
        memService.dealWithMemExcel(exportList, condition, fileName, searchMemDto.getOrgLevelCode());
    }

    /**
     * 导出发展党员列表
     */
    @Override
    public void exportDevelopStepLogExcel(SearchMemDto searchMemDto, User user) {
        List<String> stepLogSql = this.getSearchDevelopStepLogSql(searchMemDto, null);
        if (CollUtil.isEmpty(stepLogSql)) {
            ExcelUtil.exportErrorExcel(response, new OutMessage<>(Status.PERMISSION_DENIED));
            return;
        }
        Condition condition = noCondition().and(stepLogSql.get(2));
        // 字段集合
        List<ExportDTO> exportList = searchMemDto.getExportList();
        String fileName = CacheUtils.getOrgName(searchMemDto.getOrgCode()) + "发展党员名册";
        this.dealWithDevelopStepLogExcel(exportList, condition, fileName);
    }

    public void dealWithDevelopStepLogExcel(List<ExportDTO> exportList, Condition condition, String fileName) {
        Long totalCount = this.getPageTotalCountJOOQ("ccp_develop_step_log_all", condition);
        List<String> strings = exportList.stream().map(ExportDTO::getAnnotation).collect(Collectors.toList());
        Map<String, String> d104Map = com.zenith.front.core.kit.CollectionUtil.listRecordToMap(iDictService.getDic(DictConstant.DICT_D104), "key", "name");
        // 处理数据并导出
        ExcelUtil.processorAndExport(totalCount, exportList, "ccp_develop_step_log_all", condition, fileName, "发展党员名册", response, record -> {
            for (String string : strings) {
                if (Objects.isNull(record.get(string))) {
                    record.set(string, null);
                }
            }
            String orgCode = record.getStr("管理党组织");
            if (StrKit.notBlank(orgCode)) {
                record.set("管理党组织", CacheUtils.getOrgName(orgCode));
            }
            // 是否是先进模范人物
            String isAdvancedModel = record.getStr("先进模范人物");
            if (isAdvancedModel != null) {
                record.set("先进模范人物", d104Map.get(isAdvancedModel));
            }
        });
    }

    @Override
    public void exportCombined(CombinedSearchDto searchDto, User user) {
        List<String> combinedSql = this.getCombinedSql(searchDto);
        if (CollUtil.isEmpty(combinedSql)) {
            ExcelUtil.exportErrorExcel(response, new OutMessage<>(Status.PERMISSION_DENIED));
            return;
        }
        // 字段集合
        List<ExportDTO> exportList = searchDto.getExportList();
        String fileName = CacheUtils.getOrgName(searchDto.getOrgCode()) + "党员名册";
        this.dealWithMemExcel(exportList, combinedSql.get(1), fileName);
    }


    public void dealWithMemExcel(List<ExportDTO> exportList, String fromSQL, String fileName) {
        for (ExportDTO exportDTO : exportList) {
            if ("d07_name".equals(exportDTO.getField())) {
                exportDTO.setField("d07_code");
            }
        }
        // 总条数
        Long total = this.getPageTotalCountSQL(fromSQL);
        // 处理数据并导出
        ExcelUtil.processorAndExportBySQL(total, exportList, "ccp_mem", fromSQL, fileName, "党员名册", response, record -> {
            String orgCode = record.getStr("所在党支部");
            if (StrKit.notBlank(orgCode)) {
                record.set("所在党支部", CacheUtils.getOrgName(orgCode));
            }
            // 是否劳务派遣
            Integer isDispatch = record.getInt("是否劳务派遣");
            if (isDispatch != null) {
                record.set("是否劳务派遣", isDispatch == CommonConstant.ONE_INT ? CommonConstant.TRUE_STRING : CommonConstant.false_STRING);
            }
            // 是否农民工
            Integer isFarmer = record.getInt("是否农民工");
            if (isFarmer != null) {
                record.set("是否农民工", isFarmer == CommonConstant.ONE_INT ? CommonConstant.TRUE_STRING : CommonConstant.false_STRING);
            }
        });
    }

    /**
     * 获取查询记录条数
     *
     * @param tableName 表名
     * @param condition 条件
     * @return
     */
    public Long getPageTotalCountJOOQ(String tableName, Condition condition) {
        SelectConditionStep<Record1<Integer>> record1s = DSL_CONTEXT.select(DSL.count(field("1")))
                .from(name(tableName)).where(condition);
        return memDevelopMapper.getDevelopTotalByConditon(record1s.toString());
    }

    public Long getPageTotalCountSQL(String fromSQL) {
        return memDevelopMapper.getDevelopTotalByConditon("select count(1) " + fromSQL);
    }

    /**
     * 获取查询发展党员条件
     */
    private List<String> getSearchDevelopStepLogSql(SearchMemDto searchDto, String selectSql) {
        List<String> condition = new ArrayList<>();
        int year = Integer.parseInt(iStatisticsYearService.getStatisticalYear());
        condition.add("ccp_develop_step_log_all.delete_time is null and d08_code='3' and EXTRACT(YEAR FROM \"topre_join_org_date\")=" + year + " and log_org_code like '" + searchDto.getOrgLevelCode() + "%'");
        //根据年龄前缀后缀查询
        if (StrUtil.isNotEmpty(searchDto.getAgePre()) || StrUtil.isNotEmpty(searchDto.getAgeFix())) {
            String sql = this.ageSearch(searchDto.getAgePre(), searchDto.getAgeFix(), searchDto.getAgeTime());
            if (StrUtil.isNotEmpty(sql)) {
                condition.add(sql.replaceAll("ccp_mem", "ccp_develop_step_log_all"));
            } else {
                return null;
            }
        }
        //根据出生年月查询
        if (StrUtil.isNotEmpty(searchDto.getBirthdayPre()) || StrUtil.isNotEmpty(searchDto.getBirthdayFix())) {
            String sql = this.betweenSearch(searchDto.getBirthdayPre(), searchDto.getBirthdayFix(), "\"ccp_develop_step_log_all\".\"birthday\"");
            if (StrUtil.isNotEmpty(sql)) {
                condition.add(sql);
            } else {
                return null;
            }
        }
        //性别
        if (StrUtil.isNotEmpty(searchDto.getSexCode())) {
            condition.add(this.checkBoxSearch(searchDto.getSexCode(), "\"ccp_develop_step_log_all\".\"sex_code\""));
        }
        //民族
        if (StrUtil.isNotEmpty(searchDto.getD06Code())) {
            condition.add(this.checkBoxSearch(searchDto.getD06Code(), "\"ccp_develop_step_log_all\".\"d06_code\""));
        }
        //政治面貌
        if (StrUtil.isNotEmpty(searchDto.getD89Code())) {
            condition.add(this.checkBoxSearch(searchDto.getD89Code(), "\"ccp_develop_step_log_all\".\"d89_code\""));
        }
        //姓名
        if (StrUtil.isNotEmpty(searchDto.getMemName())) {
            String[] split = searchDto.getMemName().replaceAll(" ", ",").replaceAll("，", ",").split(",");
            condition.add(this.likeSearch(Arrays.asList(split), "\"ccp_develop_step_log_all\".\"name\""));
        }
        //身份证号
        if (StrUtil.isNotEmpty(searchDto.getIdCard())) {
            condition.add(this.likeSearch(Arrays.asList(searchDto.getIdCard().split(",")), "\"ccp_develop_step_log_all\".\"idcard\""));
        }
        //工作岗位
        if (StrUtil.isNotEmpty(searchDto.getD09Code())) {
            condition.add(this.checkBoxSearch(searchDto.getD09Code(), "\"ccp_develop_step_log_all\".\"d09_code\""));
        }
        //学历
        if (StrUtil.isNotEmpty(searchDto.getD07Code())) {
            condition.add(this.checkBoxSearch(searchDto.getD07Code(), "\"ccp_develop_step_log_all\".\"d07_code\""));
        }
        //一线情况
        if (StrUtil.isNotEmpty(searchDto.getD21Code())) {
            condition.add(this.checkBoxSearch(searchDto.getD21Code(), "\"ccp_develop_step_log_all\".\"d21_code\""));
        }
        //加入党组织方式
        if (StrUtil.isNotEmpty(searchDto.getD27Code())) {
            condition.add(this.checkBoxSearch(searchDto.getD27Code(), "\"ccp_develop_step_log_all\".\"join_org_code\""));
        }
        //进入支部类型
        if (StrUtil.isNotEmpty(searchDto.getD11Code())) {
            condition.add(this.checkBoxSearch(searchDto.getD11Code(), "\"ccp_develop_step_log_all\".\"d11_code\""));
        }
        //申请入党时间
        if (StrUtil.isNotEmpty(searchDto.getApplyDatePre()) || StrUtil.isNotEmpty(searchDto.getApplyDateFix())) {
            String sql = this.betweenSearch(searchDto.getApplyDatePre(), searchDto.getApplyDateFix(), "\"ccp_develop_step_log_all\".\"apply_date\"");
            if (StrUtil.isNotEmpty(sql)) {
                condition.add(sql);
            } else {
                return null;
            }
        }
        //确定积极分子时间
        if (StrUtil.isNotEmpty(searchDto.getActiveDatePre()) || StrUtil.isNotEmpty(searchDto.getActiveDateFix())) {
            String sql = this.betweenSearch(searchDto.getActiveDatePre(), searchDto.getActiveDateFix(), "\"ccp_develop_step_log_all\".\"active_date\"");
            if (StrUtil.isNotEmpty(sql)) {
                condition.add(sql);
            } else {
                return null;
            }
        }
        //确定发展对象时间
        if (StrUtil.isNotEmpty(searchDto.getObjectDatePre()) || StrUtil.isNotEmpty(searchDto.getObjectDateFix())) {
            String sql = this.betweenSearch(searchDto.getObjectDatePre(), searchDto.getObjectDateFix(), "\"ccp_develop_step_log_all\".\"object_date\"");
            if (StrUtil.isNotEmpty(sql)) {
                condition.add(sql);
            } else {
                return null;
            }
        }
        //确定为预备党员时间
        if (StrUtil.isNotEmpty(searchDto.getJoinOrgDatePre()) || StrUtil.isNotEmpty(searchDto.getJoinOrgDateFix())) {
            String sql = this.betweenSearch(searchDto.getJoinOrgDatePre(), searchDto.getJoinOrgDateFix(), "\"ccp_develop_step_log_all\".\"topre_join_org_date\"");
            if (StrUtil.isNotEmpty(sql)) {
                condition.add(sql);
            } else {
                return null;
            }
        }

        String sqlCondition = CollUtil.join(condition, " and ");
        List<String> sqlList = new ArrayList<>();
        sqlList.add("select  " + (StrUtil.isNotBlank(selectSql) ? selectSql : "  ccp_develop_step_log_all.* "));
        sqlList.add(" from ccp_develop_step_log_all where " + sqlCondition + " ORDER BY topre_join_org_date desc,create_time desc");
        sqlList.add(sqlCondition);
        return sqlList;
    }

    /**
     * 获取查询党员条件
     */
    private List<String> getSearchMemSql(SearchMemDto searchDto, String selectSql) {
        List<String> condition = new ArrayList<>();
        condition.add("ccp_mem.delete_time is null and ccp_mem.d08_code in('1','2') and (ccp_mem.is_transfer!=1 or ccp_mem.is_transfer is null) and ccp_mem.mem_org_code like '" + searchDto.getOrgLevelCode() + "%'");
        //根据年龄前缀后缀查询
        if (StrUtil.isNotEmpty(searchDto.getAgePre()) || StrUtil.isNotEmpty(searchDto.getAgeFix())) {
            String sql = this.ageSearch(searchDto.getAgePre(), searchDto.getAgeFix(), searchDto.getAgeTime());
            if (StrUtil.isNotEmpty(sql)) {
                condition.add(sql);
            } else {
                return null;
            }
        }
        //根据出生年月查询
        if (StrUtil.isNotEmpty(searchDto.getBirthdayPre()) || StrUtil.isNotEmpty(searchDto.getBirthdayFix())) {
            String sql = this.betweenSearch(searchDto.getBirthdayPre(), searchDto.getBirthdayFix(), "\"ccp_mem\".\"birthday\"");
            if (StrUtil.isNotEmpty(sql)) {
                condition.add(sql);
            } else {
                return null;
            }
        }
        //性别
        if (StrUtil.isNotEmpty(searchDto.getSexCode())) {
            condition.add(this.checkBoxSearch(searchDto.getSexCode(), "\"ccp_mem\".\"sex_code\""));
        }
        //民族
        if (StrUtil.isNotEmpty(searchDto.getD06Code())) {
            condition.add(this.checkBoxSearch(searchDto.getD06Code(), "\"ccp_mem\".\"d06_code\""));
        }
        //政治面貌
        if (StrUtil.isNotEmpty(searchDto.getD89Code())) {
            condition.add(this.checkBoxSearch(searchDto.getD89Code(), "\"ccp_mem\".\"d89_code\""));
        }
        //人员类别
        if (StrUtil.isNotEmpty(searchDto.getD08Code())) {
            condition.add(this.checkBoxSearch(searchDto.getD08Code(), "\"ccp_mem\".\"d08_code\""));
        }
        //姓名
        if (StrUtil.isNotEmpty(searchDto.getMemName())) {
            String[] split = searchDto.getMemName().replaceAll(" ", ",").replaceAll("，", ",").split(",");
            condition.add(this.likeSearch(Arrays.asList(split), "\"ccp_mem\".\"name\""));
        }
        //身份证号
        if (StrUtil.isNotEmpty(searchDto.getIdCard())) {
            condition.add(this.likeSearch(Arrays.asList(searchDto.getIdCard().split(",")), "\"ccp_mem\".\"idcard\""));
        }
        //工作岗位
        if (StrUtil.isNotEmpty(searchDto.getD09Code())) {
            condition.add(this.checkBoxSearch(searchDto.getD09Code(), "\"ccp_mem\".\"d09_code\""));
        }
        //学历
        if (StrUtil.isNotEmpty(searchDto.getD07Code())) {
            condition.add(this.checkBoxSearch(searchDto.getD07Code(), "\"ccp_mem\".\"d07_code\""));
        }
        //一线情况
        if (StrUtil.isNotEmpty(searchDto.getD21Code())) {
            condition.add(this.checkBoxSearch(searchDto.getD21Code(), "\"ccp_mem\".\"d21_code\""));
        }
        //加入党组织方式
        if (StrUtil.isNotEmpty(searchDto.getD27Code())) {
            condition.add(this.checkBoxSearch(searchDto.getD27Code(), "\"ccp_mem\".\"d27_code\""));
        }
        //进入支部类型
        if (StrUtil.isNotEmpty(searchDto.getD11Code())) {
            condition.add(this.checkBoxSearch(searchDto.getD11Code(), "\"ccp_mem\".\"d11_code\""));
        }
        //申请入党时间
        if (StrUtil.isNotEmpty(searchDto.getApplyDatePre()) || StrUtil.isNotEmpty(searchDto.getApplyDateFix())) {
            String sql = this.betweenSearch(searchDto.getApplyDatePre(), searchDto.getApplyDateFix(), "\"ccp_mem\".\"apply_date\"");
            if (StrUtil.isNotEmpty(sql)) {
                condition.add(sql);
            } else {
                return null;
            }
        }
        //确定积极分子时间
        if (StrUtil.isNotEmpty(searchDto.getActiveDatePre()) || StrUtil.isNotEmpty(searchDto.getActiveDateFix())) {
            String sql = this.betweenSearch(searchDto.getActiveDatePre(), searchDto.getActiveDateFix(), "\"ccp_mem\".\"active_date\"");
            if (StrUtil.isNotEmpty(sql)) {
                condition.add(sql);
            } else {
                return null;
            }
        }
        //确定发展对象时间
        if (StrUtil.isNotEmpty(searchDto.getObjectDatePre()) || StrUtil.isNotEmpty(searchDto.getObjectDateFix())) {
            String sql = this.betweenSearch(searchDto.getObjectDatePre(), searchDto.getObjectDateFix(), "\"ccp_mem\".\"object_date\"");
            if (StrUtil.isNotEmpty(sql)) {
                condition.add(sql);
            } else {
                return null;
            }
        }
        //确定为预备党员时间
        if (StrUtil.isNotEmpty(searchDto.getJoinOrgDatePre()) || StrUtil.isNotEmpty(searchDto.getJoinOrgDateFix())) {
            String sql = this.betweenSearch(searchDto.getJoinOrgDatePre(), searchDto.getJoinOrgDateFix(), "\"ccp_mem\".\"join_org_date\"");
            if (StrUtil.isNotEmpty(sql)) {
                condition.add(sql);
            } else {
                return null;
            }
        }
        //成为正式党员时间
        if (StrUtil.isNotEmpty(searchDto.getFullMemberDatePre()) || StrUtil.isNotEmpty(searchDto.getFullMemberDateFix())) {
            String sql = this.betweenSearch(searchDto.getFullMemberDatePre(), searchDto.getFullMemberDateFix(), "\"ccp_mem\".\"full_member_date\"");
            if (StrUtil.isNotEmpty(sql)) {
                condition.add(sql);
            } else {
                return null;
            }
        }
        //党费缴纳情况
        if (StrUtil.isNotEmpty(searchDto.getD49Code())) {
            condition.add(this.checkBoxSearch(searchDto.getD49Code(), "\"ccp_mem\".\"d49_code\""));
        }
        // 关联查询
        StringBuilder sb = new StringBuilder();
        if (StrUtil.isNotEmpty(searchDto.getD22Code()) || StrUtil.isNotEmpty(searchDto.getD25Code())) {
            //党内职务
            if (StrUtil.isNotEmpty(searchDto.getD22Code())) {
                sb.append("AND EXISTS (\n" +
                        "SELECT 1 FROM ccp_org_committee WHERE delete_time IS NULL AND end_date IS NULL\n" +
                        "\tAND EXISTS (SELECT 1 FROM\n" +
                        "\t(SELECT code, tenure_end_date, ROW_NUMBER ( ) OVER ( PARTITION BY org_code ORDER BY tenure_end_date DESC ) AS rn FROM ccp_org_committee_elect WHERE delete_time IS NULL AND tenure_end_date > CURRENT_DATE) AS tm \n" +
                        "\tWHERE tm.code = ccp_org_committee.elect_code AND tm.rn = 1) \n" +
                        "\tAND EXISTS ( SELECT 1 FROM ( SELECT code FROM ccp_org_all WHERE delete_time IS NULL ) AS tx WHERE tx.code = ccp_org_committee.org_code )\n" +
                        "\tAND ccp_org_committee.mem_code = ccp_mem.code \n");
                sb.append(" AND ");
                sb.append(this.stringToArraySearch(Arrays.asList(searchDto.getD22Code().split(",")), "d022_code"));
                sb.append(")");
            }
            //行政职务
            if (StrUtil.isNotEmpty(searchDto.getD25Code())) {
                sb.append("AND EXISTS (\n" +
                        "SELECT 1 FROM ccp_unit_committee WHERE delete_time IS NULL AND end_date IS NULL\n" +
                        "\tAND EXISTS (SELECT 1 FROM\n" +
                        "\t(SELECT code, tenure_end_date, ROW_NUMBER ( ) OVER ( PARTITION BY unit_code ORDER BY tenure_end_date DESC ) AS rn FROM ccp_unit_committee_elect WHERE delete_time IS NULL AND tenure_end_date > CURRENT_DATE) AS tm \n" +
                        "\tWHERE tm.code = ccp_unit_committee.elect_code AND tm.rn = 1) \n" +
                        "\tAND EXISTS ( SELECT 1 FROM ( SELECT code FROM ccp_unit_all WHERE delete_time IS NULL ) AS tx WHERE tx.code = ccp_unit_committee.unit_code )\n" +
                        "\tAND ccp_unit_committee.mem_code = ccp_mem.code \n");
                sb.append(" AND ");
                sb.append(this.checkBoxSearch(searchDto.getD25Code(), "d25_code"));
                sb.append(")");
            }
        }


        String sqlCondition = CollUtil.join(condition, " and ");
        List<String> sqlList = new ArrayList<>();
        sqlList.add("select " + (StrUtil.isNotBlank(selectSql) ? selectSql : " ccp_mem.* "));
        sqlList.add(" from ccp_mem where " + sqlCondition + sb + " ORDER BY ccp_mem.create_time DESC,ccp_mem.\"id\" DESC");
        sqlList.add(sqlCondition + sb);
        return sqlList;
    }


    /**
     * 获取组合查询条件
     */
    private List<String> getCombinedSql(CombinedSearchDto searchDto) {
        StringBuilder sqlStr = new StringBuilder();
        Set<String> conditionTablePara = new LinkedHashSet<>();
        int index = 0;
        for (CombinedSearchSubDto queryDto : searchDto.getQueryDtos()) {
            String infoItem = queryDto.getInfoItem();
            boolean modelExist = this.isModelExist(queryDto.getInfoSetValue(), infoItem);
            if (StrUtil.isEmpty(queryDto.getOperator()) || !modelExist) {
                continue;
            }
            if (StrUtil.isNotEmpty(queryDto.getLeftBrackets())) {
                sqlStr.append("( ");
            }
            sqlStr.append("\"").append(queryDto.getInfoSetValue()).append("\"").append(".").append("\"").append(StrUtil.toUnderlineCase(infoItem)).append("\"");
            sqlStr.append(this.getConditionSQL(queryDto)).append(" ");
            if (StrUtil.isNotEmpty(queryDto.getRightBrackets())) {
                sqlStr.append(") ");
            }
            if (index != searchDto.getQueryDtos().size() - 1) {
                sqlStr.append(StrUtil.isNotEmpty(queryDto.getRelation()) ? queryDto.getRelation() : "").append(" ");
            }
            conditionTablePara.add(queryDto.getInfoSetValue());
            if (StrUtil.isEmpty(queryDto.getRelation())) {
                break;
            }
            index++;
        }
        // 处理关系符
        String sqlString = this.processRelationSQL(sqlStr);
        String tableJoinStr = this.processTableSQL(conditionTablePara);
        //括号校验
        int leftBracketNum = this.haveBracket(sqlString, "(");
        int rightBracketNum = this.haveBracket(sqlString, ")");
        if (leftBracketNum != rightBracketNum) {
            return null;
        }
        List<String> sqlList = new ArrayList<>();
        String selectSql = WrapperUtil.existsEncrypt(new LambdaQueryWrapper<>(), Mem.class, "name", "idcard")
                .getSqlSelect();
//        sqlList.add(" select ccp_mem.* ");
        List<String> selectSqlList = new ArrayList<>();
        String[] strings = selectSql.split(CommonConstant.DOU_HAO_STRING);
        for (String select : strings) {
            selectSqlList.add("ccp_mem."+select);
        }
        sqlList.add(" select distinct " + CollUtil.join(selectSqlList, CommonConstant.DOU_HAO_STRING));

        String sql = " from ccp_mem " + tableJoinStr + " where ccp_mem.delete_time is null and ccp_mem.d08_code in('1','2') and (ccp_mem.is_transfer!=1 or ccp_mem.is_transfer is null) ";
        sql += " and ccp_mem.mem_org_code like '" + searchDto.getOrgLevelCode() + "%'";
        if (StrUtil.isNotEmpty(sqlString)) {
            sql += " and " + sqlString;
        }
        sqlList.add(sql);
        sqlList.add(" ORDER BY ccp_mem.create_time DESC,ccp_mem.\"id\" DESC");
        return sqlList;
    }

    /**
     * 跨表处理
     *
     * @param conditionTablePara 表的集合
     * @return 跨表的sql
     */
    public String processTableSQL(Set<String> conditionTablePara) {
        StringBuilder result = new StringBuilder();
        for (String table : conditionTablePara) {
            if (StrUtil.equals(table, "ccp_mem")) {

            } else if (StrUtil.equals(table, "ccp_org")) {
                result.append(" left join ").append(table).append(" on ccp_mem.org_code = ").append(table).append(".code ");
            } else if (StrUtil.equals(table, "ccp_unit")) {
                result.append(" left join ").append(table).append(" on ccp_mem.statistical_unit = ").append(table).append(".code ");
            } else if (StrUtil.equalsAny(table, "ccp_mem_train", "ccp_org_reward", "ccp_org_committee_elect", "ccp_org_appraisal")) {
                result.append(" left join ").append(table).append(" on ccp_mem.org_code = ").append(table).append(".org_code ");
            } else if (StrUtil.equals(table, "ccp_mem_develop")) {
                result.append(" left join ").append(table).append(" on ccp_mem.code = ").append(table).append(".code ");
            } else {
                result.append(" left join ").append(table).append(" on ccp_mem.code = ").append(table).append(".mem_code ");
            }
        }
        return result.toString();
    }

    /**
     * @return where 条件
     */
    public String getConditionSQL(CombinedSearchSubDto queryDto) {
        String value = queryDto.getValue();
        String conditionStr;
        switch (queryDto.getOperator()) {
            case SearchConstant.NOTEQUAL_STR:
                conditionStr = processConditionValue(queryDto, " <> ? ", value);
                break;
            case SearchConstant.EQUAL_STR:
                conditionStr = processConditionValue(queryDto, " = ? ", value);
                break;
            case SearchConstant.LESS_STR:
                conditionStr = processConditionValue(queryDto, " < ?", value);
                break;
            case SearchConstant.GREATER_STR:
                conditionStr = processConditionValue(queryDto, " > ? ", value);
                break;
            case SearchConstant.LESSEQUAL_STR:
                conditionStr = processConditionValue(queryDto, " <= ? ", value);
                break;
            case SearchConstant.GREATEREQUAL_STR:
                conditionStr = processConditionValue(queryDto, " >= ? ", value);
                break;
            case SearchConstant.LEFTLIKE_STR:
                conditionStr = " like '%" + value + "'";
                break;
            case SearchConstant.RIGHTLIKE_STR:
                conditionStr = " not like '" + value + "%'";
                break;
            case SearchConstant.LIKE_STR:
                conditionStr = "like '%" + value + "%'";
                break;
            case SearchConstant.IN_STR:
                conditionStr = getInCondition(value, SearchConstant.IN_STR);
                break;
            case SearchConstant.NOTIN_STR:
                conditionStr = getInCondition(value, SearchConstant.NOTIN_STR);
                break;
            case SearchConstant.NULL_STR:
                conditionStr = " is null ";
                break;
            default:
                conditionStr = "";
                break;
        }
        return conditionStr;
    }

    private String processConditionValue(CombinedSearchSubDto queryDto, String conditionStr, String value) {
        // 获取实体对象
        Class<?> tableClass = getTableClass(queryDto.getInfoSetValue());
        try {
            if (Objects.nonNull(tableClass)) {
                Field field = tableClass.getDeclaredField(queryDto.getInfoItem());
                if (Date.class.equals(field.getType())) {
                    conditionStr = conditionStr.replace("?", "'" + DateUtil.parse(value, "yyyy.MM.dd") + "'");
                } else if (Integer.class.equals(field.getType()) || Long.class.equals(field.getType()) || BigDecimal.class.equals(field.getType())) {
                    conditionStr = conditionStr.replace("?", value);
                } else if (String.class.equals(field.getType())) {
                    conditionStr = conditionStr.replace("?", "'" + value + "'");
                } else {
                    conditionStr = conditionStr.replace("?", "'" + value + "'");
                }
            }

        } catch (Exception e) {
            // ------ 下面是旧版 --------
            List<CodeTableCol> codeTableColList = iCodeTableColService.findAllTableColByCode(queryDto.getInfoSetValue());
            Map<String, CodeTableCol> codeTableColMap = codeTableColList.stream().collect(Collectors.toMap(CodeTableCol::getColCode, v -> v, (k1, k2) -> k1));
            CodeTableCol codeTableCol = codeTableColMap.get(queryDto.getInfoItem());
            if (Objects.nonNull(codeTableCol)) {
                String colType = codeTableCol.getColType();
                if (StrUtil.equals(colType, "date")) {
                    try {
                        conditionStr = conditionStr.replace("?", "'" + DateUtil.parse(value, "yyyy.MM.dd") + "'");
                    } catch (Exception e1) {
                        return "date_error";
                    }
                } else if (StrUtil.equals(colType, "boolean")) {
                    conditionStr = conditionStr.replace("?", value);
                } else {
                    conditionStr = conditionStr.replace("?", "'" + value + "'");
                }
            }
        }
        return conditionStr;
    }

    /**
     * 处理关系符
     */
    private String processRelationSQL(StringBuilder sqlStr) {
        boolean any = StrUtil.endWithAny(StrUtil.trim(sqlStr), "and", "or");
        if (any) {
            int and = sqlStr.lastIndexOf("and");
            if (and != -1) {
                return sqlStr.substring(0, and);
            }
            int or = sqlStr.lastIndexOf("or");
            if (or != -1) {
                return sqlStr.substring(0, or);
            }
        }
        return sqlStr.toString();
    }

    private String getInCondition(String value, String searchConstant) {
        String sql = "";
        if (StrUtil.isEmpty(value)) {
            return sql;
        }
        String string = CollectionUtil.getString(Arrays.asList(value.split(",")));
        if (StrUtil.equals(searchConstant, SearchConstant.IN_STR)) {
            sql = " in (" + string + ")";
        }
        if (StrUtil.equals(searchConstant, SearchConstant.NOTIN_STR)) {
            sql = " not in (" + string + ")";
        }
        return sql;
    }

    /**
     * 字符串个数校验
     *
     * @param condition 校验的字符串
     * @param value     字符串中包含的字符个数
     * @return 包含的个数
     */
    public int haveBracket(String condition, String value) {
        int count = 0;
        while (condition.contains(value)) {
            int index = condition.indexOf(value);
            condition = condition.substring(index + value.length());
            count++;
        }
        return count;
    }

    /**
     * 表中是否含有该字段
     *
     * @param tableName 表名
     * @param fieldStr  字段名
     * @return
     */
    private boolean isModelExist(String tableName, String fieldStr) {
        if (StrUtil.isEmpty(tableName) || StrUtil.isEmpty(fieldStr)) {
            return false;
        }
        Class<?> tableClass = getTableClass(tableName);
        if (Objects.isNull(tableClass)) {
            return false;
        }
        Field[] fields = tableClass.getDeclaredFields();
        List<String> colList = Arrays.stream(fields).map(Field::getName).collect(Collectors.toList());
        return colList.contains(fieldStr);
    }


    private Class<?> getTableClass(String tableName) {
        switch (tableName) {
            case "ccp_mem":
                return Mem.class;
            case "ccp_org":
                return Org.class;
            case "ccp_unit":
                return Unit.class;
            case "ccp_unit_all":
                return UnitAll.class;
            case "ccp_mem_reward":
                return MemReward.class;
            case "ccp_mem_abroad":
                return MemAbroad.class;
            case "ccp_mem_train":
                return MemTrain.class;
            case "ccp_mem_train_info":
                return MemTrainInfo.class;
            case "ccp_org_reward":
                return OrgReward.class;
            case "ccp_org_committee":
                return OrgCommittee.class;
            case "ccp_org_committee_elect":
                return OrgCommitteeElect.class;
            case "ccp_org_appraisal":
                return OrgAppraisal.class;
            case "ccp_org_reviewers":
                return OrgReviewers.class;
            case "ccp_mem_develop":
                return MemDevelop.class;
            case "ccp_mem_flow":
                return MemFlow.class;
            case "mem_flow":
                return MemFlow1.class;
        }
        return null;
    }


    /**
     * 关于年龄和用户所填的年龄截取时间
     *
     * @param agePre  年龄前缀
     * @param ageFix  年龄后缀
     * @param ageTime 用户所填的年龄截止时间
     * @return sql
     */
    public String ageSearch(String agePre, String ageFix, String ageTime) {
        String sql = "";
        if (StrUtil.isEmpty(ageTime)) {
            //前缀为空
            if (StrUtil.isEmpty(agePre)) {
                sql = " EXTRACT(YEAR FROM age(now(),\"ccp_mem\".\"birthday\")) <= " + ageFix + "";
            }
            //后缀为空
            if (StrUtil.isEmpty(ageFix)) {
                sql = " EXTRACT(YEAR FROM age(now(),\"ccp_mem\".\"birthday\")) >= " + agePre + "";
            }
            if (StrUtil.isNotEmpty(agePre) && StrUtil.isNotEmpty(ageFix)) {
                //都不是空
                sql = " EXTRACT(YEAR FROM age(now(),\"ccp_mem\".\"birthday\")) BETWEEN " + agePre + " and " + ageFix;
            }
        } else {
            //用户所选的截止时间不为空
            Map<String, Object> ageTimeMap = this.checkTimeType(ageTime);
            if ((Boolean) ageTimeMap.get(UserConstant.FLAG)) {
                SimpleDateFormat matchFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
                String ageTimeStr = matchFormat.format((Date) ageTimeMap.get("date"));
                if (StrUtil.isEmpty(agePre)) {
                    sql = " EXTRACT(YEAR FROM age('" + ageTimeStr + "',\"ccp_mem\".\"birthday\")) <= " + ageFix + "";
                }
                if (StrUtil.isEmpty(ageFix)) {
                    sql = " EXTRACT(YEAR FROM age('" + ageTimeStr + "',\"ccp_mem\".\"birthday\")) >= " + agePre + "";
                }
                if (StrUtil.isNotEmpty(agePre) && StrUtil.isNotEmpty(ageFix)) {
                    sql = " EXTRACT(YEAR FROM age('" + ageTimeStr + "',\"ccp_mem\".\"birthday\")) BETWEEN " + agePre + " and " + ageFix;
                }
            } else {
                return null;
            }
        }
        return sql;
    }

    /**
     * 出生年月的计算
     *
     * @param pre   前缀
     * @param fix   后缀
     * @param filed 字段
     * @return sql
     */
    public String betweenSearch(String pre, String fix, String filed) {
        String sql = "";
        SimpleDateFormat matchFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        if (StrUtil.isEmpty(pre)) {
            Map<String, Object> map = this.checkTimeType(fix);
            if ((Boolean) map.get(UserConstant.FLAG)) {
                String bornFixDate = matchFormat.format((Date) map.get("date"));
                sql = " " + filed + " <= '" + bornFixDate + "'";
            } else {
                return null;
            }
        }
        //后缀为空
        if (StrUtil.isEmpty(fix)) {
            Map<String, Object> map = this.checkTimeType(pre);
            if ((Boolean) map.get(UserConstant.FLAG)) {
                String bornPreDate = matchFormat.format((Date) map.get("date"));
                sql = " " + filed + " >= '" + bornPreDate + "'";
            } else {
                return null;
            }
        }
        if (StrUtil.isNotEmpty(pre) && StrUtil.isNotEmpty(fix)) {
            //都不是空
            Map<String, Object> mapFix = this.checkTimeType(fix);
            if (!(Boolean) mapFix.get(UserConstant.FLAG)) {
                return null;
            }
            Map<String, Object> mapPre = this.checkTimeType(pre);
            if (!(Boolean) mapPre.get(UserConstant.FLAG)) {
                return null;
            }
            String bornFixDate = matchFormat.format((Date) mapFix.get("date"));
            String bornPreDate = matchFormat.format((Date) mapPre.get("date"));
            sql = " " + filed + " BETWEEN '" + bornPreDate + "' and '" + bornFixDate + "'";
        }
        return sql;
    }

    /**
     * 通用多选查询
     *
     * @param checkBoxStr 字符串
     * @param filed       字段
     * @return sql
     */
    public String checkBoxSearch(String checkBoxStr, String filed) {
        String sql = "";
        String s = checkBoxStr.replaceAll(" ", ",");
        checkBoxStr = s.replaceAll("，", ",");
        String[] checkBoxs = checkBoxStr.split(",");
        String checkBoxsIds = CollectionUtil.getString(Arrays.asList(checkBoxs));
        sql = " " + filed + " in (" + checkBoxsIds + ") ";
        return sql;
    }

    /**
     * 通用的like查询
     */
    public String likeSearch(List<String> likeStr, String filed) {
        StringBuilder sql = new StringBuilder();
        for (int i = 0; i < likeStr.size(); i++) {
            if (i == 0) {
                if (likeStr.size() != 1) {
                    sql.append(" (").append(filed).append(" like '%").append(likeStr.get(i)).append("%' ");
                } else {
                    sql.append(" (").append(filed).append(" like '%").append(likeStr.get(i)).append("%' ) ");
                }
            } else if (i == likeStr.size() - 1) {
                sql.append(" or ").append(filed).append(" like '%").append(likeStr.get(i)).append("%' )");
            } else {
                sql.append(" or ").append(filed).append(" like '%").append(likeStr.get(i)).append("%'");
            }
        }
        return sql.toString();
    }

    /**
     * string_to_array
     */
    public String stringToArraySearch(List<String> stringList, String filed) {
        StringBuilder sb = new StringBuilder();
        if (CollUtil.isEmpty(stringList)) {
            return "1=1";
        }
        int index = 1;
        sb.append("(");
        for (String str : stringList) {
            sb.append("string_to_array( ").append(filed).append(", ',' ) @> string_to_array('").append(str).append("', ',' )");
            if (index != stringList.size()) {
                sb.append(" or ");
            }
            index++;
        }
        sb.append(")");

        return sb.toString();
    }

    /**
     * LIKE '值%'
     */
    public String likeRightSearch(List<String> likeStr, String filed) {
        if (CollUtil.isEmpty(likeStr)) {
            return "1=1";
        }
        List<String> sql = likeStr.stream().map(str -> filed + " LIKE '" + str + "%'").collect(Collectors.toList());
        return "(" + CollUtil.join(sql, " or ") + ")";
    }

    /**
     * 校验时间字符串是否是认定的 YYYY.MM YYYYMM YYYYMMDD YYYY.MM.DD格式
     *
     * @param dateStr
     * @return map
     */
    private Map<String, Object> checkTimeType(String dateStr) {
        List<SimpleDateFormat> dateFormats = new ArrayList<>();
        SimpleDateFormat yyyyMM = new SimpleDateFormat("yyyyMM");
        yyyyMM.setLenient(false);
        SimpleDateFormat yyyyMMdd = new SimpleDateFormat("yyyyMMdd");
        yyyyMMdd.setLenient(false);
        SimpleDateFormat yyyy_MM_dd = new SimpleDateFormat("yyyy.MM.dd");
        yyyy_MM_dd.setLenient(false);
        SimpleDateFormat yyyy_MM = new SimpleDateFormat("yyyy.MM");
        yyyy_MM.setLenient(false);
        SimpleDateFormat yyyy = new SimpleDateFormat("yyyy");
        yyyy.setLenient(false);
        dateFormats.add(yyyyMM);
        dateFormats.add(yyyyMMdd);
        dateFormats.add(yyyy_MM_dd);
        dateFormats.add(yyyy_MM);
        dateFormats.add(yyyy);
        Map<String, Object> map = new HashMap<>();
        for (SimpleDateFormat dateFormat : dateFormats) {
            try {
                Date date = dateFormat.parse(dateStr);
                map.put("flag", true);
                map.put("date", date);
                break;
            } catch (ParseException e) {
                map.put("flag", false);
                map.put("date", dateStr);
            }
        }
        return map;
    }

    private Page<Map<String, Object>> getMybatisPage(com.jfinal.plugin.activerecord.Page<Record> mapPage) {
        Page<Map<String, Object>> voPage = new Page<>(mapPage.getPageNumber(), mapPage.getPageSize());
        List<Map<String, Object>> list = new ArrayList<>();
        mapPage.getList().forEach(objectMap -> {
            Map<String, Object> map = new HashMap<>();
            objectMap.getColumns().forEach((key, value) -> map.put(StrUtil.toCamelCase(key), value));
            list.add(map);
        });
        voPage.setRecords(list);
        voPage.setTotal(mapPage.getTotalRow());
        voPage.setPages(mapPage.getTotalPage());
        return voPage;
    }


}
