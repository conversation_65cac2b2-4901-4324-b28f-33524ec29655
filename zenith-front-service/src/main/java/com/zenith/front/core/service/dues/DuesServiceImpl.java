package com.zenith.front.core.service.dues;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.zenith.front.api.dues.DuesService;
import com.zenith.front.api.mem.IMemService;
import com.zenith.front.api.org.IOrgService;
import com.zenith.front.common.constant.CommonConstant;
import com.zenith.front.common.kit.DateUtil;
import com.zenith.front.common.kit.ExcelExportUtil;
import com.zenith.front.common.kit.StrKit;
import com.zenith.front.common.untils.AESUntil;
import com.zenith.front.core.kit.ExcelImportUtil;
import com.zenith.front.dao.mapper.dues.DuesMapper;
import com.zenith.front.model.dto.*;
import com.zenith.front.model.message.OutMessage;
import com.zenith.front.model.message.Status;
import com.zenith.front.model.bean.Dues;
import com.zenith.front.model.bean.Mem;
import com.zenith.front.model.bean.Org;
import com.zenith.front.model.vo.MemDuesExcel;
import com.zenith.front.model.vo.MemFlowExcel;
import lombok.Data;
import org.jooq.Condition;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.ResourceUtils;

import java.io.File;
import java.io.FileInputStream;
import java.io.FileNotFoundException;
import java.io.IOException;
import java.math.BigDecimal;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @author: D.watermelon
 * @date: 2023/1/3 10:27
 * @description: 入则恳恳以尽忠 出则谦谦以自悔
 */


@Service
public  class DuesServiceImpl extends ServiceImpl<DuesMapper, Dues> implements DuesService {
    @Autowired
    private IMemService memService;
    @Autowired
    private IOrgService orgService;

    private static final Logger log = LoggerFactory.getLogger(DuesServiceImpl.class);
    @Override
    public Status updateLastPayDate(String memCode, Date lastPayDate) {
        Mem mem = memService.findByCode(memCode);
        if (mem == null) {
            return Status.OBJEC_NOT_EXIST;
        }
        mem.setLastPayDate(lastPayDate);
        mem.setStartPayDate(lastPayDate);
        // TODO: 2023/3/23 这里还有关于补充相关的逻辑
        boolean flag = memService.updateById(mem);
        return flag ? Status.SUCCESS : Status.FAIL;
    }

    @Override
    public Status settingStandard(DuesDTO feeDTO) {
        boolean operateStatus = false;
        Integer isYearly = feeDTO.getIsYearly();
        String d49Code = feeDTO.getD49Code();
        BigDecimal standard = feeDTO.getStandard();
        BigDecimal zero = new BigDecimal(0);
        String memCode = feeDTO.getMemCode();
        Integer year = feeDTO.getYear();
        Integer month = feeDTO.getMonth();
        Mem byCodeMem = memService.findByCode(memCode);
        if (ObjectUtil.isNull(byCodeMem)){
            return Status.ACTIVITY_NOT_MEM;
        }
        if (!CommonConstant.FOUR.equals(d49Code) && standard.compareTo(zero) == 0) {
            return Status.STANDARD_MUST_GT_ZERO;
        }
        // 0党员；1本年；2本月之后
        if (isYearly == CommonConstant.ZERO_INT){
            Status status = this.checkLastPayDateForMonth(feeDTO);
            if (ObjectUtil.isNotNull(status)){
                return status;
            }
            //获取人员是否有当月党费标准以及是否已经缴费
            Dues oldDues = getOne(new QueryWrapper<Dues>().lambda()
                    .eq(Dues::getMemCode, memCode)
                    .eq(Dues::getYear, year)
                    .eq(Dues::getMonth, month)
                    .isNull(Dues::getDeleteTime));
            //已经存在记录，处理为更新
            if (ObjectUtil.isNotNull(oldDues)){
                BigDecimal oldDuesPayMoney = oldDues.getPayMoney();
                if (ObjectUtil.isNotNull(oldDuesPayMoney)){
                    return Status.MONTH_PAY_MONEY;
                }
                //修改当前这个月份得党费标准
                oldDues.setMonth(month);
                oldDues.setD49Code(feeDTO.getD49Code());
                oldDues.setD49Name(feeDTO.getD49Name());
                if (d49Code.equals(CommonConstant.FOUR)){
                    oldDues.setStandard(null);
                }else {
                    oldDues.setStandard(feeDTO.getStandard());
                }
                oldDues.setBase(feeDTO.getBase());
                oldDues.setReason(feeDTO.getReason());
                oldDues.setUpdateAccount(feeDTO.getUpdateAccount());
                oldDues.setUpdateTime(new Date());
                operateStatus = updateById(oldDues);
            }else {
                //不存在记录，处理为新增
                operateStatus=save(this.createDues(feeDTO,byCodeMem,month));
            }
            return operateStatus?Status.SUCCESS:Status.FAIL;
        }
        if (isYearly == CommonConstant.ONE_INT) {
            //应用全年，只应用未缴费月份，缴费月份不应用
            List<Dues> nowDuesList = list(new QueryWrapper<Dues>().lambda()
                    .eq(Dues::getMemCode, memCode)
                    .eq(Dues::getYear, year)
                    .isNull(Dues::getDeleteTime));
            Map<Integer, Dues> nowDuesMap = nowDuesList.stream().collect(Collectors.toMap(Dues::getMonth, dues->dues));
            List<Dues> saveList= new ArrayList<>();
            List<Dues> updateList= new ArrayList<>();
            for (int duesMonth = CommonConstant.ONE_INT; duesMonth <=CommonConstant.TWELVE_INT ; duesMonth++) {
                Dues oldDues = nowDuesMap.get(duesMonth);
                if (oldDues.getPayMoney()!=null&&ObjectUtil.isNotNull(oldDues.getPayMoney())){
                    return Status.MONTH_PAY_MONEY;
                }
                //新增
                if (ObjectUtil.isNull(oldDues)){
                    saveList.add(this.createDues(feeDTO,byCodeMem,duesMonth));
                }
                if (ObjectUtil.isNotNull(oldDues)&&ObjectUtil.isNull(oldDues.getPayMoney())) {
                    //更新
                    updateList.add(this.createUpdateDues(oldDues,feeDTO,duesMonth));
                }
            }
            if (saveList.size()>0){
                operateStatus= saveBatch(saveList);
            }
            if (updateList.size()>0){
                operateStatus= updateBatchById(updateList);
            }
            return operateStatus?Status.SUCCESS:Status.FAIL;
        }
        //本月及之后
        //获取当前月份
        List<Dues> nowList = list(new QueryWrapper<Dues>().lambda()
                .eq(Dues::getMemCode, memCode)
                .eq(Dues::getYear, year)
                .ge(Dues::getMonth, month)
                .isNull(Dues::getDeleteTime));
        Map<Integer, Dues> nowDuesMap = nowList.stream().collect(Collectors.toMap(Dues::getMonth, dues->dues));
        List<Dues> saveList= new ArrayList<>();
        List<Dues> updateList= new ArrayList<>();
        for (int duesMonth = month; duesMonth <=CommonConstant.TWELVE_INT ; duesMonth++) {
            Dues oldDues = nowDuesMap.get(duesMonth);
            if (oldDues.getPayMoney()!=null&&ObjectUtil.isNotNull(oldDues.getPayMoney())){
                return Status.MONTH_PAY_MONEY;
            }
            //新增
            if (ObjectUtil.isNull(oldDues)){
                saveList.add(this.createDues(feeDTO,byCodeMem,duesMonth));
            }
            if (ObjectUtil.isNotNull(oldDues)&&ObjectUtil.isNull(oldDues.getPayMoney())) {
                //更新
                updateList.add(this.createUpdateDues(oldDues,feeDTO,duesMonth));
            }
        }
        if (saveList.size()>0){
            operateStatus= saveBatch(saveList);
        }
        if (updateList.size()>0){
            operateStatus= updateBatchById(updateList);
        }
        return operateStatus?Status.SUCCESS:Status.FAIL;
    }

    @Override
    public Page<DuesData> list(DuesPageDto duesPageDto) {
        // 组装查询数据
        MemListDTO memListDTO = new MemListDTO();
        memListDTO.setMemName(duesPageDto.getMemName());
        memListDTO.setSearchType(CommonConstant.ONE);
        memListDTO.setMemOrgCode(duesPageDto.getOrgOrgCode());
        memListDTO.setSubordinate(String.valueOf(duesPageDto.getSubordinate()));
        // 获取查询条件
        Map map = memService.getListCondition(memListDTO);
        Condition cond = (Condition) map.get(CommonConstant.CONDITION);
        LambdaQueryWrapper<Mem> condition = new LambdaQueryWrapper<Mem>().apply(cond.toString());
        condition.orderByDesc(Mem::getCode);
        Page<Mem> memPage = new Page<>(duesPageDto.getPageNum(), duesPageDto.getPageSize());
        memService.page(memPage, condition);
        List<Mem> memList = memPage.getRecords();
        Integer year = duesPageDto.getYear();
        List<String> memCodeList = memList.stream().map(Mem::getCode).collect(Collectors.toList());
        // TODO: 2023/6/21 如果没有勾选包含下级， sql语句会有问题， 因为党委肯定是不可能存在党员的
        if (ObjectUtil.isNull(memCodeList)||memCodeList.size()==CommonConstant.ZERO_INT){
            memCodeList.add(CommonConstant.ZERO);
        }
        LambdaQueryWrapper<Dues> duesQueryWrapper = new LambdaQueryWrapper<Dues>().isNull(Dues::getDeleteTime).in(Dues::getMemCode, memCodeList).eq(Dues::getYear, year);
        List<Dues> duesList = list(duesQueryWrapper);
        Map<String, List<Dues>> duesMap = duesList.stream().collect(Collectors.groupingBy(Dues::getMemCode));
        List<DuesData> returnList= new ArrayList<>();
        memList.forEach(mem -> {
            String code = mem.getCode();
            List<Dues> dues = duesMap.get(code);
            DuesData duesData=new DuesData();
            duesData.setMemCode(mem.getCode());
            String orgCode = mem.getOrgCode();
            Org byOrgCode = orgService.findOrgByOrgId(orgCode);
            String orgName=ObjectUtil.isNull(byOrgCode)?"未找到组织名称":byOrgCode.getName();
            duesData.setMemName(mem.getName());
            duesData.setOrgName(orgName);
            Date lastPayDate = mem.getLastPayDate();
            duesData.setLastPayDate(ObjectUtil.isNull(lastPayDate)?null:lastPayDate);
            List<Dues> list=new ArrayList<>();
            //存在缴费记录
            if (ObjectUtil.isNotNull(dues)&&dues.size()>CommonConstant.ZERO_INT){
                dues.stream().forEach(list::add);
            }
            duesData.setData(list);
            returnList.add(duesData);
        });

        Page<DuesData> duesPageDtoPage =new Page<>();
        long total = memPage.getTotal();
        long size = memPage.getSize();
        Long maxLimit = memPage.getMaxLimit();
        long current = memPage.getCurrent();
        String countId = memPage.getCountId();
        duesPageDtoPage.setRecords(returnList);
        duesPageDtoPage.setTotal(total);
        duesPageDtoPage.setSize(size);
        duesPageDtoPage.setMaxLimit(maxLimit);
        duesPageDtoPage.setCurrent(current);
        duesPageDtoPage.setCountId(countId);
        return duesPageDtoPage;
    }

    @Override
    public Status payDues(DuesDTO data) {
        String duesCode = data.getCode();
        Dues oldDues = getOne(new QueryWrapper<Dues>().lambda()
                .eq(Dues::getCode, duesCode)
                .isNull(Dues::getDeleteTime));
        if (ObjectUtil.isNull(oldDues)){
            return Status.PAY_DUES_ERROR;
        }

        if (ObjectUtil.isNotNull(oldDues.getPayMoney())){
            return Status.MONTH_PAY_MONEY;
        }
        String memCode = oldDues.getMemCode();
        Mem byCode = memService.findByCode(memCode);
        if (ObjectUtil.isNull(byCode)){
            return Status.ACTIVITY_NOT_MEM;
        }
        // TODO: 2023/6/21 如果是全年支付,就需要做单独的额外判断
        Integer isPayYearly = data.getIsPayYearly();
        //判断是是否全年中是否存在没有设置标准的月份
        //如果已经缴纳的，不允许覆盖
        Integer year = oldDues.getYear();
        BigDecimal payMoney = data.getPayMoney();
        String updateAccount = data.getUpdateAccount();
        String orgCode = byCode.getOrgCode();
        String memOrgCode = byCode.getMemOrgCode();
        if (isPayYearly.equals(CommonConstant.ONE_INT)){
            //获取党员全年标准
            List<Dues> duesList = list(new QueryWrapper<Dues>().lambda()
                    .eq(Dues::getMemCode, memCode)
                    .eq(Dues::getYear, year)
                    .isNull(Dues::getDeleteTime));
            Map<Integer, Dues> duesMap = duesList.stream().collect(Collectors.toMap(Dues::getMonth, dues -> dues));
            for (int month = CommonConstant.ONE_INT; month <=CommonConstant.TWELVE_INT ; month++) {
                if (ObjectUtil.isNull(duesMap.get(month))){
                    return Status.PAY_STANDARD_ERROR;
                }
            }
            duesList.forEach(dues -> {
                dues.setPayMoney(payMoney);
                dues.setUpdateAccount(updateAccount);
                dues.setPayAccount(updateAccount);
                dues.setUpdateTime(new Date());
                dues.setPayOrgCode(orgCode);
                dues.setPayOrgOrgCode(memOrgCode);
                dues.setPayDate(data.getPayDate());
            });
            boolean updateBatchById = updateBatchById(duesList);
            return updateBatchById?Status.SUCCESS:Status.FAIL;
        }else {
            oldDues.setPayMoney(payMoney);
            oldDues.setUpdateAccount(updateAccount);
            oldDues.setPayAccount(updateAccount);
            oldDues.setUpdateTime(new Date());
            oldDues.setPayOrgCode(orgCode);
            oldDues.setPayOrgOrgCode(memOrgCode);
            oldDues.setPayDate(data.getPayDate());
            // TODO: 2023/2/13, 简单版不需要处理缴纳日志
            boolean updateById = updateById(oldDues);
            return updateById?Status.SUCCESS:Status.FAIL;
        }
    }

    /***
     *  获取党费标准批量设置列表,获取人员是否可以设置党费情况
     * @param memCodeList
     * @return
     */
    @Override
    public List<Map<String, Object>> filterMem(List<String> memCodeList) {
        List<Map<String,Object>> returnList= new ArrayList<>();
        //查询人员是否设置了起交时间
        memCodeList.forEach(memCode->{
            Map<String,Object> messageMap = new HashMap<>();
            Mem mem = memService.findByCode(memCode);
            Date lastPayDate = mem.getLastPayDate();
            if (ObjectUtil.isNull(lastPayDate)){
                messageMap.put("message",Status.LAST_PAY_DAY_IS_NULL.getMessage());
                messageMap.put("payDate","");
                messageMap.put("code",mem.getCode());
                messageMap.put("setting",false);
            }else {
                messageMap.put("message","请设置党费标准");
                messageMap.put("payDate",lastPayDate);
                messageMap.put("code",mem.getCode());
                messageMap.put("setting",true);
            }
            returnList.add(messageMap);
        });
        return returnList;
    }

    @Override
    public List<DuesData> findBatchFee(List<String> postMemData) {
        List<DuesData> returnList=new ArrayList<>();
        //获取党员信息
        postMemData.forEach(memCode->{
            DuesData duesDTO =new DuesData();
            Mem mem = memService.findByCode(memCode);
            duesDTO.setMemName(mem.getName());
            duesDTO.setMemCode(mem.getCode());
            duesDTO.setOrgName(mem.getOrgName());
            duesDTO.setIdCard(mem.getIdcard());
            Date startPayDate = mem.getStartPayDate();
            Date lastPayDate = mem.getLastPayDate();
            duesDTO.setStartPayDate(startPayDate);
            duesDTO.setLastPayDate(lastPayDate);
            //处理党员每个月的党费标准
            List<Dues> list = list(new QueryWrapper<Dues>().lambda().eq(Dues::getMemCode, memCode).isNull(Dues::getDeleteTime).eq(Dues::getYear, Calendar.getInstance().get(Calendar.YEAR)));
            List<Dues> duesList = list;
            duesDTO.setData(duesList);
            returnList.add(duesDTO);
        });
        return returnList;
    }

    @Override
    public OutMessage importExcelDudeStand(ImportExcelDevelopDTO data,String account) throws Exception {
        String excelFile = data.getExcelFile();
        if (StrUtil.containsAny(excelFile, "\\")) {
            excelFile = excelFile.replaceAll("\\\\", "/");
        }
        String excelFilePath = ResourceUtils.getURL(ResourceUtils.CLASSPATH_URL_PREFIX).getPath() + excelFile;
        if (!StrUtil.containsAny(excelFilePath, ".xls")) {
            return new OutMessage(Status.DEVELOP_UPLOAD_EXCEL_ERROR);
        }
        FileInputStream fileInputStream = new FileInputStream(excelFilePath);
        ExcelImportUtil<ImportStandDTO> excelImportUtil = new ExcelImportUtil<>(ImportStandDTO.class);
        // TODO: 2023/6/26 处理实体类
        List<ImportStandDTO> importDuesStandDTO = excelImportUtil.readExcel(fileInputStream, CommonConstant.TWO_INT, CommonConstant.ZERO_INT);
        //不能超过3500条数据
        if (importDuesStandDTO.size()>3500){
            return new OutMessage(Status.PAY_NUMBER_ERROR);
        }
        String orgCode = data.getOrgCode();
        Org orgByOrgCode = orgService.findOrgByOrgCode(orgCode);
        if (ObjectUtil.isNull(orgByOrgCode)){
            return  new OutMessage(Status.ORG_IS_NULL);
        }
        int rowNum=CommonConstant.THREE_INT;
        List<Dues> updateList=new ArrayList<>();
        List<Dues> saveList=new ArrayList<>();
        List<Mem> updateMemList=new ArrayList<>();
        for (ImportStandDTO importStandDTO : importDuesStandDTO) {
            String name = importStandDTO.getName();
            String birthday = importStandDTO.getBirthday();
            //党员是否存在
            Mem byNameBirthdayAndOrgCode = memService.findByNameBirthdayAndOrgCode(name, birthday, orgCode);
            if (ObjectUtil.isNull(byNameBirthdayAndOrgCode)){
                return new OutMessage(500, "导入失败第" + rowNum + "行党员信息不存在", null);
            }
            String payTypeName = importStandDTO.getPayType();
            Integer payType=CommonConstant.FOUR_INT;
            if (payTypeName.equals("按固定标准交纳")){
                payType=CommonConstant.ONE_INT;
            }
            if (payTypeName.equals("按工资比例交纳")){
                payType=CommonConstant.TWO_INT;
                String payStand = importStandDTO.getPayStand();
                if (StrUtil.isEmpty(payStand)){
                    return new OutMessage(500, "导入失败第" + rowNum + "行按工资比例缴纳未填写交费基数", null);
                }
            }
            if (payTypeName.equals("少交")){
                payType=CommonConstant.THREE_INT;
                String payRemark = importStandDTO.getPayRemark();
                if (StrUtil.isEmpty(payRemark)){
                    return new OutMessage(500, "导入失败第" + rowNum + "行少交或免交未填写原因", null);
                }
            }
            //处理起交时间
            String payStartDate = importStandDTO.getPayStartDate();
            String code = byNameBirthdayAndOrgCode.getCode();
            if (StrUtil.isNotEmpty(payStartDate)){
                SimpleDateFormat simpleDateFormat = cn.hutool.core.date.DateUtil.newSimpleFormat("yyyy-MM-dd HH:mm:ss");
                byNameBirthdayAndOrgCode.setLastPayDate(simpleDateFormat.parse(payStartDate));
                updateMemList.add(byNameBirthdayAndOrgCode);
            }

            LambdaQueryWrapper<Dues> lambdaQueryWrapper = new LambdaQueryWrapper<>();
            lambdaQueryWrapper
                    .isNull(Dues::getDeleteTime)
                    .eq(Dues::getMemCode, code)
                    .in(Dues::getYear,2023);
            List<Dues> list = list(lambdaQueryWrapper);
            Map<Integer, Dues> standMap = list.stream().collect(Collectors.toMap(Dues::getMonth, dues -> dues));
            String oneStand = importStandDTO.getOneStand();
            this.setStand(oneStand,standMap,CommonConstant.ONE_INT,byNameBirthdayAndOrgCode,payType,payTypeName,importStandDTO,account,saveList,updateList);

            String twoStand = importStandDTO.getTwoStand();
            this.setStand(twoStand,standMap,CommonConstant.TWO_INT,byNameBirthdayAndOrgCode,payType,payTypeName,importStandDTO,account,saveList,updateList);

            String  threeStand= importStandDTO.getThreeStand();
            this.setStand(threeStand,standMap,CommonConstant.THREE_INT,byNameBirthdayAndOrgCode,payType,payTypeName,importStandDTO,account,saveList,updateList);

             String fourStand = importStandDTO.getFourStand();
            this.setStand(fourStand,standMap,CommonConstant.FOUR_INT,byNameBirthdayAndOrgCode,payType,payTypeName,importStandDTO,account,saveList,updateList);

            String  fiveStand= importStandDTO.getFiveStand();
            this.setStand(fiveStand,standMap,CommonConstant.FIVE_INT,byNameBirthdayAndOrgCode,payType,payTypeName,importStandDTO,account,saveList,updateList);

            String sixStand = importStandDTO.getSixStand();
            this.setStand(sixStand,standMap,CommonConstant.SIX_INT,byNameBirthdayAndOrgCode,payType,payTypeName,importStandDTO,account,saveList,updateList);

            String sevenStand = importStandDTO.getSevenStand();
            this.setStand(sevenStand,standMap,CommonConstant.SEVEN_INT,byNameBirthdayAndOrgCode,payType,payTypeName,importStandDTO,account,saveList,updateList);

            String eightStand = importStandDTO.getEightStand();
            this.setStand(eightStand,standMap,CommonConstant.EIGHT_INT,byNameBirthdayAndOrgCode,payType,payTypeName,importStandDTO,account,saveList,updateList);

            String nineStand = importStandDTO.getNineStand();

            this.setStand(nineStand,standMap,CommonConstant.NINE_INT,byNameBirthdayAndOrgCode,payType,payTypeName,importStandDTO,account,saveList,updateList);

            String tenStand = importStandDTO.getTenStand();
            this.setStand(tenStand,standMap,CommonConstant.TEN_INT,byNameBirthdayAndOrgCode,payType,payTypeName,importStandDTO,account,saveList,updateList);

            String elevenStand = importStandDTO.getElevenStand();
            this.setStand(elevenStand,standMap,CommonConstant.ELEVEN_INT,byNameBirthdayAndOrgCode,payType,payTypeName,importStandDTO,account,saveList,updateList);

            String twelveStand = importStandDTO.getTwelveStand();
            this.setStand(twelveStand,standMap,CommonConstant.TWELVE_INT,byNameBirthdayAndOrgCode,payType,payTypeName,importStandDTO,account,saveList,updateList);
        }

        boolean status=false;
        if (saveList.size()>CommonConstant.ZERO_INT){
            status = saveBatch(saveList);
        }
        if (updateList.size()>CommonConstant.ZERO_INT){
            status = updateBatchById(updateList);
        }
        if (status){
            status=memService.updateBatchById(updateMemList);
        }
        return new OutMessage(status?Status.SUCCESS:Status.FAIL);

    }

    private void setStand(String monthStand,Map<Integer, Dues> standMap,Integer month,Mem byNameBirthdayAndOrgCode,Integer payType,String payTypeName,
                          ImportStandDTO importStandDTO,String account,List<Dues> saveList,List<Dues> updateList){
        //if (ObjectUtil.isNotNull(monthStand)&&StrUtil.isNotEmpty(monthStand)){
            Dues dues = standMap.get(month);
            if (ObjectUtil.isNull(dues)){
                //新增
                Dues saveDues=new Dues();
                saveDues.setCode(StrKit.getRandomUUID());
                saveDues.setMemCode(byNameBirthdayAndOrgCode.getCode());
                saveDues.setMemOrgCode(byNameBirthdayAndOrgCode.getOrgCode());
                saveDues.setMemOrgOrgCode(byNameBirthdayAndOrgCode.getMemOrgCode());
                saveDues.setYear(2023);
                saveDues.setMonth(month);
                saveDues.setSettingOrgCode(byNameBirthdayAndOrgCode.getOrgCode());
                saveDues.setSettingOrgOrgCode(byNameBirthdayAndOrgCode.getMemOrgCode());
                saveDues.setD49Code(String.valueOf(payType));
                saveDues.setD49Name(payTypeName);
                saveDues.setBase(new BigDecimal(StrUtil.isNotBlank(importStandDTO.getPayStand())?importStandDTO.getPayStand(): "0"));
                saveDues.setSeason(DateUtil.quarter(month));
                if (month.equals(CommonConstant.ONE_INT)){
                    saveDues.setStandard(new BigDecimal(StrUtil.isNotBlank(importStandDTO.getOneStand())?importStandDTO.getOneStand():"0"));

                }
                if (month.equals(CommonConstant.TWO_INT)){
                    saveDues.setStandard(new BigDecimal(StrUtil.isNotBlank(importStandDTO.getTwoStand())?importStandDTO.getTwoStand():"0"));
                }
                if (month.equals(CommonConstant.THREE_INT)){
                    saveDues.setStandard(new BigDecimal(StrUtil.isNotBlank(importStandDTO.getThreeStand())?importStandDTO.getThreeStand():"0"));

                }
                if (month.equals(CommonConstant.FOUR_INT)){
                    saveDues.setStandard(new BigDecimal(StrUtil.isNotBlank(importStandDTO.getFourStand())?importStandDTO.getFourStand():"0"));

                }
                if (month.equals(CommonConstant.FIVE_INT)){
                    saveDues.setStandard(new BigDecimal(StrUtil.isNotBlank(importStandDTO.getFiveStand())?importStandDTO.getFiveStand():"0"));

                }
                if (month.equals(CommonConstant.SIX_INT)){
                    saveDues.setStandard(new BigDecimal(StrUtil.isNotBlank(importStandDTO.getSixStand())?importStandDTO.getSixStand():"0"));

                }
                if (month.equals(CommonConstant.SEVEN_INT)){
                    saveDues.setStandard(new BigDecimal(StrUtil.isNotBlank(importStandDTO.getSevenStand())?importStandDTO.getSevenStand():"0"));

                }
                if (month.equals(CommonConstant.EIGHT_INT)){
                    saveDues.setStandard(new BigDecimal(StrUtil.isNotBlank(importStandDTO.getEightStand())?importStandDTO.getEightStand():"0"));

                }
                if (month.equals(CommonConstant.NINE_INT)){
                    saveDues.setStandard(new BigDecimal(StrUtil.isNotBlank(importStandDTO.getNineStand())?importStandDTO.getNineStand():"0"));

                }
                if (month.equals(CommonConstant.TEN_INT)){
                    saveDues.setStandard(new BigDecimal(StrUtil.isNotBlank(importStandDTO.getTenStand())?importStandDTO.getTenStand():"0"));

                }
                if (month.equals(CommonConstant.ELEVEN_INT)){
                    saveDues.setStandard(new BigDecimal(StrUtil.isNotBlank(importStandDTO.getElevenStand())?importStandDTO.getElevenStand():"0"));

                }
                if (month.equals(CommonConstant.TWELVE_INT)){
                    saveDues.setStandard(new BigDecimal(StrUtil.isNotBlank(importStandDTO.getTwelveStand())?importStandDTO.getTwelveStand():"0"));
                }

                if (payType.equals(CommonConstant.FOUR_INT)){
                    saveDues.setStandard(null);
                }

                saveDues.setUpdateAccount(account);
                saveDues.setReason(importStandDTO.getPayRemark());
                saveDues.setRemark("批量导入标准");
                saveDues.setCreateTime(new Date());
                saveDues.setIsYearly(CommonConstant.ZERO_INT);
                saveList.add(saveDues);
            }else {
                BigDecimal payMoney = dues.getPayMoney();
                if (ObjectUtil.isNull(payMoney)){
                    //更新
                    dues.setUpdateAccount(account);
                    dues.setUpdateTime(new Date());
                    dues.setSettingOrgCode(byNameBirthdayAndOrgCode.getOrgCode());
                    dues.setSettingOrgOrgCode(byNameBirthdayAndOrgCode.getMemOrgCode());
                    dues.setRemark("批量导入");
                    //按照标准缴纳党费
                    dues.setD49Code(String.valueOf(payType));
                    dues.setD49Name(payTypeName);
                    dues.setStandard(new BigDecimal(StrUtil.isNotBlank(monthStand)?monthStand:CommonConstant.ZERO));
                    //经批准少交党费
                    if (payType.equals(CommonConstant.TWO_INT)){
                        dues.setBase(new BigDecimal(importStandDTO.getPayStand()));
                    }
                    //少交
                    if (payType.equals(CommonConstant.THREE_INT)){
                        dues.setReason(importStandDTO.getPayRemark());
                    }
                    //少交
                    if (payType.equals(CommonConstant.FOUR_INT)){
                        dues.setStandard(null);
                        dues.setReason(importStandDTO.getPayRemark());
                    }
                    updateList.add(dues);
                }
            }
        //}
    }

    private OutMessage checekMonth(ImportDuesDTO importDuesDTO,Map<Integer, Dues> standMap,Integer rowNum,List<Dues> updateList,Org orgByOrgCode,String account){
        //校验党费缴纳数据是否正确(主要是格式，怕他们输入汉字)
        StringBuffer  message= new StringBuffer("第" + rowNum + "行导入失败，该党员");
        String oneDues = importDuesDTO.getOneDues();
        Boolean isErro=false;
        if (ObjectUtil.isNotNull(oneDues)&&StrUtil.isNotEmpty(oneDues)){
            Dues dues = standMap.get(CommonConstant.ONE_INT);
            if (ObjectUtil.isNull(dues)) {
                message.append(CommonConstant.ONE_INT);
                message.append("、");
                isErro=true;
            }else {
                this.deailUpdateDues(dues,updateList,oneDues,orgByOrgCode,account);
            }
        }
        String twoDues = importDuesDTO.getTwoDues();
        if (ObjectUtil.isNotNull(twoDues)&&StrUtil.isNotEmpty(twoDues)){
            Dues dues = standMap.get(CommonConstant.TWO_INT);
            if (ObjectUtil.isNull(dues)) {
                message.append(CommonConstant.TWO_INT);
                message.append("、");
                isErro=true;
            }else {
                this.deailUpdateDues(dues,updateList,twoDues,orgByOrgCode,account);
            }
        }
        String threeDues = importDuesDTO.getThreeDues();
        if (ObjectUtil.isNotNull(threeDues)&&StrUtil.isNotEmpty(threeDues)){
            Dues dues = standMap.get(CommonConstant.THREE_INT);
            if (ObjectUtil.isNull(dues)) {
                message.append(CommonConstant.THREE_INT);
                message.append("、");
                isErro=true;
            }else {
                this.deailUpdateDues(dues,updateList,threeDues,orgByOrgCode,account);
            }
        }
        String fourDues = importDuesDTO.getFourDues();
        if (ObjectUtil.isNotNull(fourDues)&&StrUtil.isNotEmpty(fourDues)){
            Dues dues = standMap.get(CommonConstant.FOUR_INT);
            if (ObjectUtil.isNull(dues)) {
                message.append(CommonConstant.FOUR_INT);
                message.append("、");
                isErro=true;
            }else {
                this.deailUpdateDues(dues,updateList,fourDues,orgByOrgCode,account);
            }
        }
        String fiveDues = importDuesDTO.getFiveDues();
        if (ObjectUtil.isNotNull(fiveDues)&&StrUtil.isNotEmpty(fiveDues)){
            Dues dues = standMap.get(CommonConstant.FIVE_INT);
            if (ObjectUtil.isNull(dues)) {
                message.append(CommonConstant.FIVE_INT);
                message.append("、");
                isErro=true;
            }else {
                this.deailUpdateDues(dues,updateList,fiveDues,orgByOrgCode,account);
            }
        }
        String sixDues = importDuesDTO.getSixDues();
        if (ObjectUtil.isNotNull(sixDues)&&StrUtil.isNotEmpty(sixDues)){
            Dues dues = standMap.get(CommonConstant.SIX_INT);
            if (ObjectUtil.isNull(dues)) {
                message.append(CommonConstant.SIX_INT);
                message.append("、");
                isErro=true;
            }else {
                this.deailUpdateDues(dues,updateList,sixDues,orgByOrgCode,account);
            }
        }
        String sevenDues = importDuesDTO.getSevenDues();
        if (ObjectUtil.isNotNull(sevenDues)&&StrUtil.isNotEmpty(sevenDues)){
            Dues dues = standMap.get(CommonConstant.SEVEN_INT);
            if (ObjectUtil.isNull(dues)) {
                message.append(CommonConstant.SEVEN_INT);
                message.append("、");
                isErro=true;
            }else {
                this.deailUpdateDues(dues,updateList,sevenDues,orgByOrgCode,account);
            }
        }
        String eightDues = importDuesDTO.getEightDues();
        if (ObjectUtil.isNotNull(eightDues)&&StrUtil.isNotEmpty(eightDues)){
            Dues dues = standMap.get(CommonConstant.EIGHT_INT);
            if (ObjectUtil.isNull(dues)) {
                message.append(CommonConstant.EIGHT_INT);
                message.append("、");
                isErro=true;
            }else {
                this.deailUpdateDues(dues,updateList,eightDues,orgByOrgCode,account);
            }
        }
        String nineDues = importDuesDTO.getNineDues();
        if (ObjectUtil.isNotNull(nineDues)&&StrUtil.isNotEmpty(nineDues)){
            Dues dues = standMap.get(CommonConstant.NINE_INT);
            if (ObjectUtil.isNull(dues)) {
                message.append(CommonConstant.NINE_INT);
                message.append("、");
                isErro=true;
            }else {
                this.deailUpdateDues(dues,updateList,nineDues,orgByOrgCode,account);
            }
        }
        String tenDues = importDuesDTO.getTenDues();
        if (ObjectUtil.isNotNull(tenDues)&&StrUtil.isNotEmpty(tenDues)){
            Dues dues = standMap.get(CommonConstant.TEN_INT);
            if (ObjectUtil.isNull(dues)) {
                message.append(CommonConstant.TEN_INT);
                message.append("、");
                isErro=true;
            }else {
                this.deailUpdateDues(dues,updateList,tenDues,orgByOrgCode,account);
            }
        }
        String elevenDues = importDuesDTO.getElevenDues();

        if (ObjectUtil.isNotNull(elevenDues)&&StrUtil.isNotEmpty(elevenDues)){
            Dues dues = standMap.get(CommonConstant.ELEVEN_INT);
            if (ObjectUtil.isNull(dues)) {
                message.append(CommonConstant.ELEVEN_INT);
                message.append("、");
                isErro=true;
            }else {
                this.deailUpdateDues(dues,updateList,elevenDues,orgByOrgCode,account);
            }
        }
        String twelveDues = importDuesDTO.getTwelveDues();
        if (ObjectUtil.isNotNull(twelveDues)&&StrUtil.isNotEmpty(twelveDues)){
            Dues dues = standMap.get(CommonConstant.TWELVE_INT);
            if (ObjectUtil.isNull(dues)) {
                message.append(CommonConstant.TWELVE_INT);
                message.append("、");
                isErro=true;
            }else {
                this.deailUpdateDues(dues,updateList,twelveDues,orgByOrgCode,account);
            }
        }
        if (isErro){
            String returnMessage = message.toString();
            if ( returnMessage.endsWith("、")){
                message.replace(returnMessage.length()-CommonConstant.ONE_INT, returnMessage.length(),"");
            }
            message.append("月未设置党费标准，请设置后导入");
            return new OutMessage(500,message.toString(),null);
        }
        return null;
    }

    public void deailUpdateDues(Dues dues,List<Dues> updateList,String duesMoney,Org orgByOrgCode,String account){
        BigDecimal payMoney = dues.getPayMoney();
        if (ObjectUtil.isNull(payMoney)){
            dues.setRemark("批量导入缴纳");
            dues.setPayMoney(new BigDecimal(duesMoney));
            dues.setPayDate(new Date());
            dues.setPayOrder("批量导入缴纳");
            dues.setPayOrgCode(orgByOrgCode.getCode());
            dues.setPayOrgOrgCode(orgByOrgCode.getOrgCode());
            dues.setPayType(CommonConstant.THREE);
            dues.setPayAccount(account);
            updateList.add(dues);
        }
    }

    @Override
    public OutMessage importExcelDudes(ImportExcelDevelopDTO data,String account) throws FileNotFoundException {
        String excelFile = data.getExcelFile();
        if (StrUtil.containsAny(excelFile, "\\")) {
            excelFile = excelFile.replaceAll("\\\\", "/");
        }
        String excelFilePath = ResourceUtils.getURL(ResourceUtils.CLASSPATH_URL_PREFIX).getPath() + excelFile;
        if (!StrUtil.containsAny(excelFilePath, ".xls")) {
            return new OutMessage(Status.DEVELOP_UPLOAD_EXCEL_ERROR);
        }
        FileInputStream fileInputStream = new FileInputStream(excelFilePath);
        ExcelImportUtil<ImportDuesDTO> excelImportUtil = new ExcelImportUtil<>(ImportDuesDTO.class);
        // TODO: 2023/6/26 处理实体类
        List<ImportDuesDTO> importDevelopDTOS = excelImportUtil.readExcel(fileInputStream, CommonConstant.TWO_INT, CommonConstant.ZERO_INT);
        //不能超过3500条数据
        if (importDevelopDTOS.size()>3500){
            return new OutMessage(Status.PAY_NUMBER_ERROR);
        }
        String orgCode = data.getOrgCode();
        Org orgByOrgCode = orgService.findOrgByOrgCode(orgCode);
        if (ObjectUtil.isNull(orgByOrgCode)){
            return  new OutMessage(Status.ORG_IS_NULL);
        }
        //校验数据是否正确
        int rowNum=CommonConstant.THREE_INT;
        List<Dues> updateList= new ArrayList<>();
        for (ImportDuesDTO importDuesDTO : importDevelopDTOS) {
            String name = importDuesDTO.getName();
            String birthday = importDuesDTO.getBirthday();
            //党员是否存在
            Mem byNameBirthdayAndOrgCode = memService.findByNameBirthdayAndOrgCode(name, birthday, orgCode);
            if (ObjectUtil.isNull(byNameBirthdayAndOrgCode)){
                return new OutMessage(500, "导入失败第" + rowNum + "行党员信息不存在", null);
            }
            //党费标准是否设置
            String code = byNameBirthdayAndOrgCode.getCode();
            //获取人员的党费标准
            LambdaQueryWrapper<Dues> lambdaQueryWrapper = new LambdaQueryWrapper<>();
            lambdaQueryWrapper
                    .isNull(Dues::getDeleteTime)
                    .eq(Dues::getMemCode, code)
                    .in(Dues::getYear,2023);
            List<Dues> list = list(lambdaQueryWrapper);
            Map<Integer, Dues> standMap = list.stream().collect(Collectors.toMap(Dues::getMonth, dues -> dues));
            OutMessage outMessage = this.checekMonth(importDuesDTO, standMap, rowNum,updateList,orgByOrgCode,account);
            if (ObjectUtil.isNotNull(outMessage)){
                return outMessage;
            }
            rowNum++;
        }
        Boolean updateBatchById = updateBatchById(updateList);
        return new OutMessage(Status.SUCCESS.getCode(), "数据导入成功；党费交纳仅接受第一次导入数据，后续导入数据将不做更新，仅以第一次导入数据为准！", null);
    }

    @Override
    public OutMessage templateDuesExport(String orgCode) throws Exception {
        return this.execport(orgCode,"templateDues.xlsx");
    }


    @Override
    public OutMessage templateStandExport(String orgCode) throws Exception {

        return this.execport(orgCode,"templateStand.xlsx");
    }
    private  OutMessage execport(String orgCode,String fileName) throws Exception {
        //根据组织code，获取下面所有党员
        List<Mem> byMemOrgCode = memService.findByMemOrgCode(orgCode);
        if (byMemOrgCode.size()>3500){
            return new OutMessage(Status.MEM_NUM_MAX);
        }
        List<String> orgList = byMemOrgCode.stream().map(Mem::getOrgCode).collect(Collectors.toList());
        List<Org> byOrgCodeList = orgService.findByOrgCodeList(orgList);
        Map<String, Org> orgMap = byOrgCodeList.stream().collect(Collectors.toMap(Org::getCode, org -> org));
        List<MemDuesExcel> outList= new ArrayList<>();
        byMemOrgCode.forEach(mem -> {
            MemDuesExcel memDuesExcel=new MemDuesExcel();
            memDuesExcel.setMemName(mem.getName());
            memDuesExcel.setBirthday(cn.hutool.core.date.DateUtil.format(mem.getBirthday(), "yyyyMMdd"));
            memDuesExcel.setMemOrgName(orgMap.get(mem.getOrgCode()).getName());
            outList.add(memDuesExcel);
        });
        FileInputStream in = new FileInputStream(ResourceUtils.getURL(ResourceUtils.CLASSPATH_URL_PREFIX).getPath() + "public/excelftl/"+fileName);
        ExcelExportUtil<MemDuesExcel> excelExportUtil = new ExcelExportUtil<>(MemDuesExcel.class, 2, 0);
        File result = excelExportUtil.exportFileXlsx(in, outList, cn.hutool.core.date.DateUtil.format(new Date(), "yyyyMMddHHmmssSSS") + ".xlsx");
        in.close();
        Map<String, String> map = new HashMap<>(5);
        map.put("url", "/public/export/" + result.getName());
        return new OutMessage<>(Status.SUCCESS, map);
    }


    /***
     * 根据接收dot创建党费修改实体(排除月份)
     * @param oldDues
     * @param feeDTO
     * @param month
     * @return
     */
    public Dues createUpdateDues( Dues oldDues ,DuesDTO feeDTO ,Integer month){
        oldDues.setMonth(month);
        String d49Code = feeDTO.getD49Code();
        oldDues.setD49Code(d49Code);
        oldDues.setD49Name(feeDTO.getD49Name());
        oldDues.setBase(feeDTO.getBase());
        oldDues.setReason(feeDTO.getReason());
        if (d49Code.equals(CommonConstant.FOUR_INT)){
            oldDues.setStandard(null);
        }else {
            oldDues.setStandard(feeDTO.getStandard());
        }
        if (d49Code.equals(CommonConstant.FOUR)){
            oldDues.setStandard(new BigDecimal(CommonConstant.ZERO_INT));
        }
        oldDues.setUpdateAccount(feeDTO.getUpdateAccount());
        oldDues.setUpdateTime(new Date());
        return  oldDues;
    }

    /***
     * 根据接收dot创建党费实体（排除月份以及季度）
     * @param feeDTO
     * @param byCodeMem
     * @param month
     * @return
     */
    public Dues createDues(DuesDTO feeDTO ,Mem byCodeMem,Integer month){
        Dues dues = feeDTO.toModel();
        dues.setCode(StrKit.getRandomUUID());
        dues.setMonth(month);
        dues.setSeason(DateUtil.quarter(month));
        dues.setCode(StrKit.getRandomUUID());
        dues.setCreateTime(new Date());
        dues.setUpdateAccount(feeDTO.getUpdateAccount());
        dues.setMemOrgCode(byCodeMem.getOrgCode());
        dues.setMemOrgOrgCode(byCodeMem.getMemOrgCode());
        dues.setSettingOrgCode(byCodeMem.getOrgCode());
        dues.setSettingOrgOrgCode(byCodeMem.getMemOrgCode());
        return dues;
    }

    /**
     * 校验党员最晚缴费时间
     *
     * @param data
     * @return
     */
    private Status checkLastPayDateForMonth(DuesDTO data) {
        Mem mem = memService.findByCode(data.getMemCode());
        Date lastPayDate = mem.getLastPayDate();
        if (lastPayDate != null) {
            // 已有最后缴费时间
            Integer monthInteger = data.getMonth();
            Integer yearInteger = data.getYear();
            // 最后缴费年
            int lastYear = cn.hutool.core.date.DateUtil.year(lastPayDate);
            // 最后缴费月
            int lastMonth = cn.hutool.core.date.DateUtil.month(lastPayDate) + 1;

            if (lastYear > yearInteger) {
                return Status.MONTH_NEED_AFTER_LAST_PAY_DAY;
            } else if (lastYear == yearInteger) {
                // 同一年
                if (monthInteger < lastMonth) {
                    return Status.MONTH_NEED_AFTER_LAST_PAY_DAY;
                }
            }
        } else {
            return  Status.LAST_PAY_DAY_IS_NULL;
        }
        return null;
    }
}
