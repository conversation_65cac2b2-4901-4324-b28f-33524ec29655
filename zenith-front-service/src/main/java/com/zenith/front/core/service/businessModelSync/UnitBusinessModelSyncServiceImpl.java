package com.zenith.front.core.service.businessModelSync;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.zenith.front.api.businessModelSync.UnitBusinessModelSyncService;
import com.zenith.front.api.mem.IMemService;
import com.zenith.front.api.org.IOrgCommitteeElectService;
import com.zenith.front.api.org.IOrgCommitteeService;
import com.zenith.front.api.org.IOrgService;
import com.zenith.front.api.unit.IUnitOrgLinkedService;
import com.zenith.front.api.unit.IUnitService;
import com.zenith.front.common.constant.CommonConstant;
import com.zenith.front.common.constant.DictConstant;
import com.zenith.front.core.cache.CacheUtils;
import com.zenith.front.dao.mapper.mem.MemMapper;
import com.zenith.front.dao.mapper.org.OrgMapper;
import com.zenith.front.model.bean.*;
import com.zenith.front.model.custom.Record;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * @author: D.watermelon
 * @date: 2023/11/30 22:24
 * @description: 入则恳恳以尽忠 出则谦谦以自悔
 */
public class UnitBusinessModelSyncServiceImpl implements UnitBusinessModelSyncService {

    @Resource
    private IUnitService iUnitService;
    @Resource
    private IUnitOrgLinkedService iUnitOrgLinkedService;
    @Resource
    private IOrgService iOrgService;
    @Resource
    private OrgMapper orgMapper;
    @Resource
    private IMemService iMemService;
    @Resource
    private IOrgCommitteeService iOrgCommitteeService;
    @Resource
    private IOrgCommitteeElectService iOrgCommitteeElectService;
    @Resource
    private MemMapper memMapper;


    @Override
    public void syncBusinessModelUnit(String unitCode) {
        //获取当前单位基础信息
        Unit unitByCode = iUnitService.findByCode(unitCode);
        if (ObjectUtil.isNull(unitByCode))return;
        //从关联表获取当前组织的主单位
        List<UnitOrgLinked> byUnitCodeLinked = iUnitOrgLinkedService.findByUnitCode(unitCode);
        //获取当前单位关联的党组织
        List<UnitOrgLinked> unitOrgLinkeds = byUnitCodeLinked.stream().filter(unitLinked -> unitLinked.getIsUnitMain().equals(CommonConstant.ONE_INT))
                .collect(Collectors.toList());
        //主单位的组织code集合
        List<String> mainOrgCodeList =unitOrgLinkeds.stream().map(unitLinked -> unitLinked.getOrgCode()).collect(Collectors.toList());

        //主单位的党组织需要处理党组织本级国民经济为当前单位的国民经济和生产服务行业
        List<Org> mainOrgList = iOrgService.findByOrgCodeList(mainOrgCodeList);

        String d194Code = unitByCode.getD194Code();
        String d194Name = unitByCode.getD194Name();
        String d195Code = unitByCode.getD195Code();
        String d195Name = unitByCode.getD195Name();

        //单位没有国民经济，证明无需更新处理
        if (ObjectUtil.isNull(d194Code))return;

        // TODO: 2023/12/2 这种方法有一点问题，就是会修改展示了已经修改了得值会被修改丢弃（比如展示了国民经济和生产服务行业的村、社区党组织、学生党组织等）
        List<Org> updateOrgList= new ArrayList<>();
        mainOrgList.stream().forEach(org -> {
            Org updateOrg= new Org();
            updateOrg.setId(org.getId());
            updateOrg.setD194Code(d194Code);
            updateOrg.setD195Code(d194Name);
            updateOrg.setD194Name(d195Code);
            updateOrg.setD195Name(d195Name);
            updateOrgList.add(updateOrg);
        });
        //找寻所有党组织
        List<Org> orgList = orgMapper.selectList(new QueryWrapper<Org>().lambda().eq(Org::getMainUnitCode, unitCode).isNull(Org::getDeleteTime));
        String d04Code = unitByCode.getD04Code();
        for (Org org : orgList) {
            if (mainOrgCodeList.contains(org.getOrgCode())) {
                continue;
            }
            Org updateOrg= new Org();
            //默认放入跟单位一样得东西
            updateOrg.setId(org.getId());
            updateOrg.setD194Code(d194Code);
            updateOrg.setD195Code(d194Name);
            updateOrg.setD194Name(d195Code);
            updateOrg.setD195Name(d195Name);
            //如果是单位类别是村就要置换为农牧渔民
            if (StrUtil.equalsAny(d04Code, "922", "923") ){
                updateOrg.setD194Code("A");
                updateOrg.setD195Code("V0000");
                updateOrg.setD194Name("农、林、牧、渔业");
                updateOrg.setD195Name("无");
            }
            //如果的那位类别是学校331（高等教育）
            if (StrUtil.startWithAny(d04Code, "331") ){
                updateOrg.setD194Code("U");
                updateOrg.setD195Code("V0000");
                updateOrg.setD194Name( "其他");
                updateOrg.setD195Name("无");
            }
            //是离退休党组织，直接置换为其他
            if (org.getIsRetire().equals(CommonConstant.ONE_INT)){
                updateOrg.setD194Code("U");
                updateOrg.setD195Code("V0000");
                updateOrg.setD194Name( "其他");
                updateOrg.setD195Name("无");
            }
            updateOrgList.add(updateOrg);
        }

        //同步党员----当前党组织的    //同步党员----选择当前单位的
        List<Mem> updateMemList=new ArrayList<>();
        if (orgList.size()>CommonConstant.ZERO_INT){
            List<String> allOrgCode = orgList.stream().map(org -> org.getCode()).collect(Collectors.toList());
            //通过sql 查询两类党员
            List<Mem> changeMem = memMapper.selectListByMemListAndUnit(unitCode, allOrgCode);
//            List<Mem> byMemOrgCode = iMemService.findByMemOrgCode(allOrgCode,unitCode);
//            List<Mem> changeMem = byMemOrgCode.stream().filter(mem -> mem.getHasUnitStatistics().equals(CommonConstant.ONE_INT)).collect(Collectors.toList());
            List<Record> d196List = CacheUtils.getDictNonHump(DictConstant.DICT_D196);
            Map<String, Record> d196Map = d196List.stream().collect(Collectors.toMap(e -> e.getStr("key"), e -> e, (e1, e2) -> e1));
            changeMem.stream().forEach(mem -> {
                Mem saveMem=new Mem();
                saveMem.setId(mem.getId());
                String d09Code = mem.getD09Code();
                //非特殊类，就跟单位属性一致
                saveMem.setD194Code(d194Code);
                saveMem.setD194Name(d194Name);
                saveMem.setD195Code(d195Code);
                saveMem.setD195Name(d195Name);

                //村的两委成员，是和单位是一样的
                if (StrUtil.equalsAny(d04Code, "922", "923")){
                    List<OrgCommittee> orgCommitByMemCode = iOrgCommitteeService.findOrgCommitByMemCode(mem.getCode(),null);
                    if (orgCommitByMemCode.size()>CommonConstant.ZERO_INT){
                        boolean isNow = iOrgCommitteeElectService.electIsNow(orgCommitByMemCode.stream().map(OrgCommittee::getElectCode).collect(Collectors.toList())).size() > CommonConstant.ZERO_INT;
                        //是在任的两位成员
                        if (isNow){
                            saveMem.setD194Code(d194Code);
                            saveMem.setD194Name(d194Name);
                            saveMem.setD195Code(d195Code);
                            saveMem.setD195Name(d195Name);
                        }
                        return;
                    }
                }
                //如果是字典表特殊类， 就定点归集
                if(d196Map.containsKey(d09Code)){
                    Record record = d196Map.get(d09Code);
                    saveMem.setD194Code(record.getStr("mapNewKey"));
                    saveMem.setD194Name(record.getStr("mapNewKeyName"));
                    saveMem.setD195Code("");
                    saveMem.setD195Name("");
                    return;
                }
                //如果是单位是村、农村社区的农牧渔民或者工作岗位是农牧渔民
                if (StrUtil.equalsAny(d04Code, "922", "923")||(StrUtil.equalsAny(d09Code, "10", "13", "14"))){
                    //查看相关的一线情况相关问题
                    String d21Code = mem.getD21Code();
                    //存在一线情况置换为农牧渔
                    saveMem.setD194Code("A");
                    saveMem.setD195Code("V0000");
                    saveMem.setD194Name("农、林、牧、渔业");
                    saveMem.setD195Name("无");
                    //没有一线情况，置换为其他
                    if (ObjectUtil.isNull(d21Code)){
                        saveMem.setD194Code("U");
                        saveMem.setD195Code("V0000");
                        saveMem.setD194Name( "其他");
                        saveMem.setD195Name("无");
                    }
                }
                updateMemList.add(saveMem);
            });
        }

        //修改发展党员


        //同步单位宽表
        //同步党组织宽表
        //同步党员宽表
    }
}
