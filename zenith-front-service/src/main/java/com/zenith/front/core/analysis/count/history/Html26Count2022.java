package com.zenith.front.core.analysis.count.history;

import cn.hutool.core.util.StrUtil;
import com.b1809.analysis.entity.PeggingPara;
import com.zenith.front.core.analysis.ext.condition.year2022.MemAllCondition2022B;
import com.zenith.front.core.analysis.ext.condition.year2022.OrgAllConditionB;
import org.jooq.Condition;

import java.util.Map;

import static org.jooq.impl.DSL.noCondition;

/**
 * <AUTHOR>
 * @date 2023/2/10
 */
public class Html26Count2022 {

    public Map<String, Object> getReportPeggingMap26(PeggingPara data) {
        String rowIndex = data.getRowIndex();
        if (StrUtil.equals(rowIndex, "7")) {
            OrgAllConditionB orgAllCond = new OrgAllConditionB();
            Condition condition = noCondition().and(new Html26CountHistory().getOrg26Condition(data.getOrgCode(), data.getOrgLevelCode())
                    .and(new Html26CountHistory().getOrgCheck26Condition(data.getColIndex())).toString().replace("ccp_org_all", orgAllCond.getTableName()));
            return Html48CountHistory.getReportPageResult(data, orgAllCond.getTableName(), condition, orgAllCond.getLevelCodeField());
        }

        MemAllCondition2022B memAllCond = new MemAllCondition2022B();
        Condition condition = noCondition().and(new Html26CountHistory().getMemListCondition(data.getOrgCode(), data.getOrgLevelCode())
                .and(new Html26CountHistory().getOrgCheck26Condition(data.getColIndex())).toString().replace("ccp_mem_all", memAllCond.getTableName()));
        return Html48CountHistory.getReportPageResult(data, memAllCond.getTableName(), condition, memAllCond.getLevelCodeField());
    }

}
