package com.zenith.front.core.service.activist;

import cn.afterturn.easypoi.excel.ExcelExportUtil;
import cn.afterturn.easypoi.excel.entity.ExportParams;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.thread.ThreadUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.zenith.front.api.activist.IActivistTransferApprovalService;
import com.zenith.front.api.activist.IActivistTransferLogService;
import com.zenith.front.api.activist.IActivistTransferRecordService;
import com.zenith.front.api.develop.IDevelopStepLogAllService;
import com.zenith.front.api.develop.IDevelopStepLogService;
import com.zenith.front.api.mem.IMemDevelopAllService;
import com.zenith.front.api.mem.IMemDevelopService;
import com.zenith.front.api.org.IOrgAllService;
import com.zenith.front.api.org.IOrgService;
import com.zenith.front.api.report.IStatisticsYearService;
import com.zenith.front.api.sync.ISyncMemService;
import com.zenith.front.common.constant.*;
import com.zenith.front.common.kit.Ret;
import com.zenith.front.common.kit.StrKit;
import com.zenith.front.common.kit.StringToBooleanConverter;
import com.zenith.front.core.cache.CacheUtils;
import com.zenith.front.core.kit.ExcelUtil;
import com.zenith.front.core.service.sync.SyncDevelopStepLogAll;
import com.zenith.front.core.service.transfer.TransferRecordServiceImpl;
import com.zenith.front.dao.mapper.activist.ActivistTransferRecordMapper;
import com.zenith.front.dao.mapper.mem.MemDevelopProcessMapper;
import com.zenith.front.dao.mapper.mem.MemDigitalMapper;
import com.zenith.front.dao.mapper.mem.MemDigitalOperationLogMapper;
import com.zenith.front.model.bean.*;
import com.zenith.front.model.constant.UserConstant;
import com.zenith.front.model.custom.Record;
import com.zenith.front.model.custom.UserTicket;
import com.zenith.front.model.dto.TransferRecordDTO;
import com.zenith.front.model.dto.TransferRecordListDTO;
import com.zenith.front.model.message.OutMessage;
import com.zenith.front.model.message.Status;
import com.zenith.front.model.vo.ActivistTransferListVo;
import com.zenith.front.model.vo.ActivistTransferListVoExcel;
import com.zenith.front.model.vo.BetweenReportDateVo;
import com.zenith.front.model.vo.TransferDetailVo;
import org.apache.poi.ss.usermodel.Workbook;
import org.jooq.Condition;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.Executor;
import java.util.stream.Collectors;

import static org.jooq.impl.DSL.*;

/**
 * <p>
 * 积极分子转接记录表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-05-09
 */
@Service
public class ActivistTransferRecordServiceImpl extends ServiceImpl<ActivistTransferRecordMapper, ActivistTransferRecord> implements IActivistTransferRecordService {

    @Resource
    private IOrgService iOrgService;
    @Resource
    private IOrgAllService iOrgAllService;
    @Resource
    private IMemDevelopService iMemDevelopService;
    @Resource
    private IMemDevelopAllService iMemDevelopAllService;
    @Resource
    private IDevelopStepLogService iDevelopStepLogService;
    @Resource
    private IDevelopStepLogAllService iDevelopStepLogAllService;
    @Resource
    private ActivistMemTransferRecordServiceImpl activistMemTransferRecordService;
    @Resource
    private IActivistTransferApprovalService iActivistTransferApprovalService;
    @Resource
    private ActivistTransferApprovalServiceImpl activistTransferApprovalService;
    @Resource
    private IActivistTransferLogService iActivistTransferLogService;
    @Resource
    private ISyncMemService iSyncMemService;
    @Resource
    private SyncDevelopStepLogAll syncDevelopStepLogAll;
    @Resource
    private Executor mySimpleAsync;
    @Resource
    private IStatisticsYearService iStatisticsYearService;
    @Resource
    private MemDevelopProcessMapper memDevelopProcessMapper;
    @Resource
    private MemDigitalMapper memDigitalMapper;
    @Resource
    private MemDigitalOperationLogMapper digitalOperationLogMapper;

    /**
     * 支部间人员调整
     */
    @Override
    public OutMessage<?> adjustMem(List<TransferRecordDTO> data) {
        List<ActivistTransferRecord> transferRecordList = new ArrayList<>(data.size());
        OutMessage<?> message = this.getTransferRecordList(data, transferRecordList);
        if (Objects.nonNull(message)) {
            return message;
        }
        boolean flag = false;
        for (ActivistTransferRecord transferRecord : transferRecordList) {
            //创建审批记录
            ActivistTransferApproval approval = iActivistTransferApprovalService.createOrgTransferApproval(transferRecord);
            approval.setNextOrgId("");
            flag = iActivistTransferApprovalService.save(approval);
            //创建审批日志
            iActivistTransferLogService.insertOrgTransferLog(approval.getId(), null, TransferLogConstant.PUSH_TRANSFER_STATUS, TransferLogConstant.PUSH_ADJUST_MEM_TRANSFER_REASON);
            //设置转接状态为默认完成
            transferRecord.setStatus(TransferRecordConstant.TRANSFER_SUCCESS);
            transferRecord.setCurrentApprovalId(approval.getId());
            flag &= this.save(transferRecord);

            flag &= transferAdjust(transferRecord);
        }

        return flag ? new OutMessage<>(Status.SUCCESS) : new OutMessage<>(Status.FAIL);
    }

    /**
     * 获取转接记录集合
     */
    private OutMessage<?> getTransferRecordList(List<TransferRecordDTO> data, List<ActivistTransferRecord> transferRecordList) {
        List<MemDevelop> memDevelopList = iMemDevelopService.findByCodeList(data.stream().map(TransferRecordDTO::getMemId).filter(StrUtil::isNotEmpty).collect(Collectors.toList()));
        if (CollUtil.isEmpty(memDevelopList) || memDevelopList.stream().anyMatch(e -> !StrUtil.equalsAny(e.getD08Code(), "3", "4"))) {
            return new OutMessage<>(Status.PARA_ERROR);
        }
        List<ActivistTransferRecord> tempList = new ArrayList<>();
        for (TransferRecordDTO dto : data) {
            ActivistTransferRecord temp = new ActivistTransferRecord();
            OutMessage<?> checkMessage = this.checkParams(dto, temp);
            if (checkMessage.getCode() != 0) {
                return checkMessage;
            }
            tempList.add(temp);
        }
        OutMessage<?> outMessage = this.addTransferRecordToList(data, transferRecordList, tempList);
        if (outMessage.getCode() != 0) {
            return outMessage;
        }
        return null;
    }

    /**
     * 省内关系转出
     */
    @Override
    public OutMessage<?> transferMem(List<TransferRecordDTO> data) {
        List<ActivistTransferRecord> transferRecordList = new ArrayList<>(data.size());
        OutMessage<?> message = this.getTransferRecordList(data, transferRecordList);
        if (Objects.nonNull(message)) {
            return message;
        }
        boolean flag = false;
        for (ActivistTransferRecord transferRecord : transferRecordList) {
            //创建审批记录
            ActivistTransferApproval approval = iActivistTransferApprovalService.createOrgTransferApproval(transferRecord);
            flag = iActivistTransferApprovalService.save(approval);
            //创建审批日志
            iActivistTransferLogService.insertOrgTransferLog(approval.getId(), null, TransferLogConstant.PUSH_TRANSFER_STATUS, TransferLogConstant.PUSH_MEM_TRANSFER_REASON);

            //更新转接记录中的审批记录id
            transferRecord.setCurrentApprovalId(approval.getId());
            flag &= this.save(transferRecord);
            //处理党员系统的是否在转接中的标示
            activistMemTransferRecordService.updateMemIsTransfer(transferRecord.getId(), data.stream().map(TransferRecordDTO::getMemId).collect(Collectors.toList()));
        }
        // 当如果是中间节点， 且中间节点不在当前数据库的时候， 那么这个数据就需要走中间交换区
        String targetOrgId = transferRecordList.get(0).getTargetOrgId();
        Org orgByCode = iOrgService.findOrgByCode(targetOrgId);
        if (flag && ObjectUtil.isNull(orgByCode) && StrUtil.isNotEmpty(targetOrgId)) {
            transferRecordList.forEach(transferRecord -> activistMemTransferRecordService.insertDataByMiddle(transferRecord.getId()));
        }
        return flag ? new OutMessage<>(Status.SUCCESS) : new OutMessage<>(Status.FAIL);
    }

    /**
     * 查询转接记录详情
     */
    @Override
    public OutMessage<?> detail(String transferId, boolean isOut) {
        ActivistTransferRecord record = getById(transferId);
        if (record == null) {
            return new OutMessage<>(Status.OBJEC_NOT_EXIST);
        }
        String type = record.getType();
        if (TransferRecordConstant.MEM_ADJUST_IN.equalsIgnoreCase(type) || TransferRecordConstant.MEM_ADJUST_OUT.equalsIgnoreCase(type)) {
            //如果是属于支部间人员调整,则不允许查看详情
            return new OutMessage<>(Status.TRANSFER_MEM_ADJUST_DETAIL);
        }
        //源组织关系集合
        List<String> srcOrgRelationAsList = record.getSrcOrgRelationAsList();
        //目标组织关系集合
        List<String> targetOrgRelationAsList = record.getTargetOrgRelationAsList();
        //创建详情VO对象
        TransferDetailVo detailVo = this.createTransferDetailVo(record, isOut, srcOrgRelationAsList, targetOrgRelationAsList);
        detailVo.setD92Code(record.getD92Code());
        detailVo.setD92Name(record.getD92Name());
        detailVo.setRemark(record.getRemark());
        detailVo.setD146Code(record.getD146Code());
        detailVo.setD146Name(record.getD146Name());
        if (detailVo.getSrcStepList().size() >= 2) {
            detailVo.setSrcStepList(Arrays.asList(detailVo.getSrcStepList().get(0), detailVo.getSrcStepList().get(1)));
        }
        if (detailVo.getTargetStepList().size() >= 2) {
            detailVo.setTargetStepList(Arrays.asList(detailVo.getTargetStepList().get(detailVo.getTargetStepList().size() - 2), detailVo.getTargetStepList().get(detailVo.getTargetStepList().size() - 1)));
        }
        detailVo.setMemCode(record.getMemId());
        detailVo.setId(record.getId());
        return new OutMessage<>(Status.SUCCESS, detailVo);
    }

    /***
     * 创建详情vo
     * @param record 转接记录对象
     * @param isOut 是否是转出
     * @param srcOrgRelationAsList 源组织关系集合
     * @param targetOrgRelationAsList 目标组织关系集合
     * @return 审批详情vo
     * */
    private TransferDetailVo createTransferDetailVo(ActivistTransferRecord record, boolean isOut, List<String> srcOrgRelationAsList, List<String> targetOrgRelationAsList) {
        TransferDetailVo detailVo = new TransferDetailVo();
        //判断该转接记录是人员转接还是属于组织转接
        String memId = record.getMemId();
        //如果memId不为空则说明是人员转接,否则为组织关系转接
        if (StrKit.notBlank(memId)) {
            detailVo.setMemName(record.getName());
            MemDevelop mem = iMemDevelopService.findAllByCode(memId);
            if (mem != null) {
                detailVo.setMemType(mem.getD08Name());
                detailVo.setIdCard(mem.getIdcard());
                detailVo.setPhone(mem.getPhone());
                detailVo.setDigitalLotNo(mem.getDigitalLotNo());
                detailVo.setD08Code(mem.getD08Code());
            } else {
                MemDevelop memInfoExtraData = record.getMemInfoExtraData();
                if (Objects.nonNull(memInfoExtraData)) {
                    detailVo.setMemType(memInfoExtraData.getD08Name());
                    detailVo.setIdCard(memInfoExtraData.getIdcard());
                    detailVo.setPhone(memInfoExtraData.getPhone());
                    detailVo.setDigitalLotNo(memInfoExtraData.getDigitalLotNo());
                    detailVo.setD08Code(memInfoExtraData.getD08Code());
                }
            }
        }
        detailVo.setInOrgName(record.getTargetOrgName());
        detailVo.setOutOrgName(record.getSrcOrgName());
        detailVo.setCommonNodeId(record.getCommonOrgId());
        detailVo.setCommonNodeName(record.getCommonOrgName());
        detailVo.setTransferReason(record.getReason());
        String type = record.getType();
        detailVo.setTransferTypeCode(type);
        if (isOut) {
            List<Record> dic = CacheUtils.getDic(TransferRecordConstant.DICT_OUT);
            dic.stream().filter(rec -> rec.getStr("key").equalsIgnoreCase(record.getOutType())).findFirst().ifPresent(rec -> detailVo.setTransferTypeName(rec.get("name")));
        } else {
            List<Record> dic = CacheUtils.getDic(TransferRecordConstant.DICT_IN);
            dic.stream().filter(rec -> rec.getStr("key").equalsIgnoreCase(record.getInType())).findFirst().ifPresent(rec -> detailVo.setTransferTypeName(rec.get("name")));
        }
        //源组织转接步骤集合
        List<TransferDetailVo.TransferStep> srcTransferStepList = new ArrayList<>();
        //目标组织转接步骤集合
        List<TransferDetailVo.TransferStep> targetTransferStepList = new ArrayList<>();
        Map<String, ActivistTransferApproval> map = iActivistTransferApprovalService.findByRecordId(record.getId()).stream().collect(Collectors.toMap(e -> {
            String orgId = e.getOrgId();
            String nextOrgId = e.getNextOrgId();
            return orgId + (nextOrgId == null ? "" : nextOrgId);
        }, o -> o, (key1, key2) -> key1));

        activistTransferApprovalService.removeOtherNodes(record.getCommonOrgId(), srcOrgRelationAsList);
        activistTransferApprovalService.removeOtherNodes(record.getCommonOrgId(), targetOrgRelationAsList);
        //倒叙源组织这边的关系
        Collections.reverse(srcOrgRelationAsList);
        //创建左边步骤
        activistTransferApprovalService.addTransferStepToList(type, record.getCommonOrgId(), srcOrgRelationAsList, srcTransferStepList, map);

        //创建右边步骤
        activistTransferApprovalService.addTransferStepToList(type, record.getCommonOrgId(), targetOrgRelationAsList, targetTransferStepList, map);

        //创建公共节点步骤
        TransferDetailVo.TransferStep commonNodeStep = new TransferDetailVo.TransferStep();
        commonNodeStep.setOrgId(detailVo.getCommonNodeId());
        commonNodeStep.setOrgName(detailVo.getCommonNodeName());
        Org commonOrg = iOrgService.findOrgByCode(detailVo.getCommonNodeId());
        if (commonOrg != null) {
            commonNodeStep.setOrgCode(commonOrg.getOrgCode());
        } else {
            log.warn("未能通过orgId查询到组织信息!");
        }

        //如果目标关系集合为空,说明是转出系统外
        String mapKey;
        if (targetOrgRelationAsList.isEmpty()) {
            mapKey = detailVo.getCommonNodeId();
        } else {
            if (targetOrgRelationAsList.size() > 1) {
                mapKey = detailVo.getCommonNodeId() + targetOrgRelationAsList.get(1);
            } else {
                mapKey = detailVo.getCommonNodeId();
            }
        }
        //查询公共节点是否有审批记录
        ActivistTransferApproval commonApproval = map.get(mapKey);
        if (commonApproval == null) {
            //设置公共节点状态
            activistTransferApprovalService.setCommonStepStatus(record.getCurrentApprovalId(), detailVo, commonNodeStep);
        } else {
            activistTransferApprovalService.setCommonStepStatus(record.getCurrentApprovalId(), detailVo, commonNodeStep);
            //设置公共节点状态
            List<TransferLog> logList = iActivistTransferLogService.findByHandleApprovalId(commonApproval.getId());
            commonNodeStep.setLogList(logList);
        }
        //如果记录被撤回,则没有任何操作
        if (TransferRecordConstant.TRANSFER_UNDO.equals(record.getStatus())) {
            srcTransferStepList.forEach(transferStep -> transferStep.setStatus(TransferApprovalConstant.NOT_OPERATE));
            targetTransferStepList.forEach(transferStep -> transferStep.setStatus(TransferApprovalConstant.NOT_OPERATE));
            commonNodeStep.setStatus(TransferApprovalConstant.NOT_OPERATE);
        }
        detailVo.setSrcStepList(srcTransferStepList);
        detailVo.setTargetStepList(targetTransferStepList);
        detailVo.setCommonNodeStep(commonNodeStep);
        return detailVo;
    }

    /**
     * 审核通过
     */
    @Override
    public OutMessage<?> apply(String recordId, String applyOrgId) {
        ActivistTransferRecord record = this.getById(recordId);
        // 不能审批已通过或退回的审批记录
        if (Objects.isNull(record) || !Objects.equals(record.getStatus(), TransferRecordConstant.TRANSFERING)) {
            return new OutMessage<>(Status.PARA_ERROR);
        }
        String currentApprovalId = record.getCurrentApprovalId();
        //当前审批记录
        ActivistTransferApproval currentApproval = iActivistTransferApprovalService.getById(currentApprovalId);
        if (currentApproval == null) {
            return new OutMessage<>(Status.PARA_ERROR);
        }
        String nextOrgId = currentApproval.getNextOrgId();
        ActivistTransferApproval subApproval = iActivistTransferApprovalService.findSubTransferApprovalByIdAndOrgId(currentApprovalId, nextOrgId);
        //不允许重复审批
        if (subApproval != null && TransferApprovalConstant.APPROVAL_SUCCESS.equals(subApproval.getStatus()) && StrKit.equals(nextOrgId, applyOrgId)) {
            return new OutMessage<>(Status.TRANSFER_ALREADY_APPLY_ERROR);
        }
        String userManagerOrgId = UserConstant.USER_CONTEXT.get().getUserRolePermission().getOrgId();
        //判断是否有权限审核，只能是上级才能审核
        List<String> parentOrgIdList = iOrgService.findAllParentOrg(nextOrgId).stream().map(Org::getCode).collect(Collectors.toList());
        if (!parentOrgIdList.contains(userManagerOrgId)) {
            return new OutMessage<>(Status.PERMISSION_DENIED);
        }
        //判断是否代审
        int isInstead = StrKit.equals(nextOrgId, userManagerOrgId) ? TransferApprovalConstant.NOT_INSTEAD : TransferApprovalConstant.IS_INSTEAD;
        //子审批记录
        boolean flag = false;
        ActivistTransferApproval newApproval = iActivistTransferApprovalService.createOrUpdateTransferApproval(subApproval, currentApproval, record, isInstead, TransferApprovalConstant.APPROVAL_SUCCESS);
        if (newApproval != null) {
            //创建一条审核日志
            String reason = TransferApprovalConstant.IS_INSTEAD.equals(isInstead) ? "代审通过" : "";
            flag = iActivistTransferLogService.insertOrgTransferLog(newApproval.getId(), null, TransferLogConstant.SUCCESS_STATUS, reason);

            //目标组织关系
            List<String> targetOrgRelationAsList = record.getTargetOrgRelationAsList();
            //判断下一个需要审核的是否是公共节点
            //如果是则判断公共节点是否需要审核
            if (StrKit.equals(newApproval.getNextOrgId(), record.getCommonOrgId())) {
                activistTransferApprovalService.removeOtherNodes(record.getCommonOrgId(), targetOrgRelationAsList);
                //不需要审核,直接通过
                ActivistTransferApproval commonApproval = iActivistTransferApprovalService.findSubTransferApprovalByIdAndOrgId(newApproval.getId(), newApproval.getNextOrgId());
                if (commonApproval == null) {
                    //创建一条默认通过的审核记录
                    commonApproval = activistTransferApprovalService.createApproval(isInstead, newApproval.getId(),
                            TransferApprovalConstant.APPROVAL_SUCCESS,
                            TransferApprovalConstant.DIRECTION_COMMON, record.getCommonOrgId(), recordId);
                    if (targetOrgRelationAsList.size() > 1) {
                        commonApproval.setNextOrgId(targetOrgRelationAsList.get(1));
                    } else {
                        commonApproval.setNextOrgId("");
                    }
                    iActivistTransferApprovalService.save(commonApproval);
                } else {
                    //更新记录
                    ActivistTransferApproval updateTransferApproval = new ActivistTransferApproval();
                    updateTransferApproval.setId(commonApproval.getId());
                    updateTransferApproval.setStatus(TransferApprovalConstant.APPROVAL_SUCCESS);
                    if (targetOrgRelationAsList.size() > 1) {
                        updateTransferApproval.setNextOrgId(targetOrgRelationAsList.get(1));
                    } else {
                        updateTransferApproval.setNextOrgId("");
                    }
                    iActivistTransferApprovalService.updateById(updateTransferApproval);

                    //查询是否还有子审批记录
                    ActivistTransferApproval commonSubTransferApproval = iActivistTransferApprovalService.findSubTransferApprovalByIdAndOrgId(commonApproval.getId(), commonApproval.getNextOrgId());
                    if (commonSubTransferApproval != null) {
                        ActivistTransferApproval updateSubTransfer = new ActivistTransferApproval();
                        updateSubTransfer.setId(commonSubTransferApproval.getId());
                        updateSubTransfer.setStatus(TransferApprovalConstant.APPROVALING);
                        iActivistTransferApprovalService.updateById(updateSubTransfer);
                    }
                }
                //创建日志
                iActivistTransferLogService.insertOrgTransferLog(commonApproval.getId(), null, TransferLogConstant.SUCCESS_STATUS, TransferLogConstant.SUCCESS_REASON);
                newApproval = commonApproval;
            }
            //更改转接记录中的currentId
            ActivistTransferRecord updateTransferRecord = new ActivistTransferRecord();
            updateTransferRecord.setId(record.getId());
            updateTransferRecord.setCurrentApprovalId(newApproval.getId());
            //判断是否到末尾  类别224因为如果转出到省外的中间交换区类别还不能直接完成，需要等中间交换区进行处理完成才能完成，中间节点审核完成，就等于完成，等着全国交换区反馈进行处理数据即可
            if (StrKit.isBlank(newApproval.getNextOrgId()) && !"224".equals(record.getType())) {
                //整条转接记录就完成了
                updateTransferRecord.setStatus(TransferRecordConstant.TRANSFER_SUCCESS);
                //接下来需要判断是否是人员转接记录或组织转接记录
                //如果是人员转接记录,则调整人员
                //如果是组织转接记录,则调整整个组织
                transferAdjust(record);
            }
            flag &= updateById(updateTransferRecord);
        }
        // 处理党员系统的是否在转接中的标示
        activistMemTransferRecordService.updateMemIsTransfer(recordId, Collections.singletonList(record.getMemId()));
        // 审核的时候如果接收节点不是当前数据库内，需要走中间交换区更新数据
        Org orgByCommCode = iOrgService.findOrgByCode(record.getCommonOrgId());
        String targetOrgId = record.getTargetOrgId();
        if (flag && ObjectUtil.isNull(orgByCommCode) && StrKit.notBlank(targetOrgId)) {
            // 这里审核要分为两种同步，1.跨节点单人转接同步 2.跨节点整建制转接审核同步
            //1.跨节点单人转接同步
            if (StrKit.notBlank(record.getMemId()) || "21".equals(record.getType())) {
                //这里可能有一个bug，导致两边都没人，那就是如果这个人在两个节点之间转过去又转回来的情况
                activistMemTransferRecordService.insertDataByMiddle(recordId);
            }
        }
        return flag ? new OutMessage<>(Status.SUCCESS) : new OutMessage<>(Status.FAIL);
    }

    /**
     * 退回操作
     */
    @Override
    public OutMessage<String> back(String recordId, String backReason) {
        ActivistTransferRecord record = this.getById(recordId);
        //不能审批已通过或退回的审批记录
        if (Objects.isNull(record) || !Objects.equals(record.getStatus(), TransferRecordConstant.TRANSFERING)) {
            return new OutMessage<>(Status.PARA_ERROR);
        }
        String currentApprovalId = record.getCurrentApprovalId();
        //当前审批记录
        ActivistTransferApproval currentApproval = iActivistTransferApprovalService.getById(currentApprovalId);
        if (currentApproval == null) {
            return new OutMessage<>(Status.PARA_ERROR);
        }
        String nextOrgId = currentApproval.getNextOrgId();
        String userManagerOrgId = UserConstant.USER_CONTEXT.get().getUserRolePermission().getOrgId();
        //判断是否有权限审核，只能是上级才能审核
        List<String> parentOrgIdList = iOrgService.findAllParentOrg(nextOrgId).stream().map(Org::getCode).collect(Collectors.toList());
        if (!parentOrgIdList.contains(userManagerOrgId)) {
            return new OutMessage<>(Status.PERMISSION_DENIED);
        }
        //判断是否代审
        int isInstead = StrKit.equals(nextOrgId, userManagerOrgId) ? TransferApprovalConstant.NOT_INSTEAD : TransferApprovalConstant.IS_INSTEAD;

        boolean flag = false;
        ActivistTransferApproval subApproval = iActivistTransferApprovalService.findSubTransferApprovalByIdAndOrgId(currentApprovalId, currentApproval.getNextOrgId());
        subApproval = iActivistTransferApprovalService.createOrUpdateTransferApproval(subApproval, currentApproval, record, isInstead, TransferApprovalConstant.APPROVAL_BACK);
        if (subApproval != null) {
            String id = currentApproval.getParentId();
            ActivistTransferApproval previousApproval = iActivistTransferApprovalService.getById(id);
            ActivistTransferRecord updateTransferRecord = new ActivistTransferRecord();
            updateTransferRecord.setId(recordId);
            //退回到根节点,则代表整条转接记录失败
            if (StrUtil.equals(currentApproval.getParentId(), TransferApprovalConstant.ROOT_ID)) {
                //撤销转接记录
                updateTransferRecord.setStatus(TransferRecordConstant.TRANSFER_UNDO);
                ActivistTransferApproval updateApproval = new ActivistTransferApproval();
                updateApproval.setId(currentApprovalId);
                updateApproval.setStatus(TransferApprovalConstant.APPROVAL_UNDO);
                //省外关系转入
                if (record.getType().equals(CommonConstant.OneHundredTwentyFour_INT)) {
                    updateTransferRecord.setStatus(TransferApprovalConstant.APPROVAL_BACK);
                }
                flag = iActivistTransferApprovalService.updateById(updateApproval);
            } else {
                //回退到上一个步骤，如果退回到公共节点,则跳过公共节点
                ActivistTransferApproval updateApproval = new ActivistTransferApproval();
                if (StrKit.equals(currentApproval.getOrgId(), record.getCommonOrgId())) {
                    ActivistTransferApproval updateCommonApproval = new ActivistTransferApproval();
                    updateCommonApproval.setId(currentApprovalId);
                    updateCommonApproval.setStatus(TransferApprovalConstant.APPROVAL_BACK);

                    String reason = TransferApprovalConstant.IS_INSTEAD.equals(isInstead) ?
                            "(代审退回:" + CacheUtils.getOrgName(UserConstant.USER_CONTEXT.get().getUserRolePermission().getOrgId()) + ")原因:" + backReason : "(原因:" + backReason + ")";
                    iActivistTransferLogService.insertOrgTransferLog(currentApprovalId, previousApproval.getId(), TransferLogConstant.BACK_STATUS, reason);
                    iActivistTransferApprovalService.updateById(updateCommonApproval);
                    updateApproval.setId(previousApproval.getId());
                    //判断是否回退到根节点
                    if (TransferApprovalConstant.ROOT_ID.equals(previousApproval.getParentId())) {
                        updateTransferRecord.setCurrentApprovalId(previousApproval.getId());
                        updateTransferRecord.setStatus(TransferApprovalConstant.APPROVAL_UNDO);
                        ////省外关系转入
                        if (record.getType().equals(CommonConstant.OneHundredTwentyFour_INT)) {
                            updateTransferRecord.setStatus(TransferApprovalConstant.APPROVAL_BACK);
                        }
                    } else {
                        updateTransferRecord.setCurrentApprovalId(previousApproval.getParentId());
                    }
                } else {
                    updateApproval.setId(currentApprovalId);
                    updateTransferRecord.setCurrentApprovalId(previousApproval.getId());
                }
                updateApproval.setStatus(TransferApprovalConstant.APPROVALING);
                flag = iActivistTransferApprovalService.updateById(updateApproval);
            }
            //保存记录
            String reason = TransferApprovalConstant.IS_INSTEAD.equals(isInstead) ?
                    "(代审退回:" + CacheUtils.getOrgName(UserConstant.USER_CONTEXT.get().getUserRolePermission().getOrgId()) + ")原因:" + backReason : "(原因:" + backReason + ")";
            iActivistTransferLogService.insertOrgTransferLog(subApproval.getId(), currentApprovalId, TransferLogConstant.BACK_STATUS, reason);
            //修改转接记录
            flag &= updateById(updateTransferRecord);
        }
        activistMemTransferRecordService.updateMemIsTransfer(recordId, Collections.singletonList(record.getMemId()));
        // 如果是回退的时候， 查看中间节点是否在当前数据库里面
        Org orgByCode = iOrgService.findOrgByCode(record.getCommonOrgId());
        if (flag && ObjectUtil.isNull(orgByCode)) {
            activistMemTransferRecordService.insertDataByMiddle(recordId);
        }
        return flag ? new OutMessage<>(Status.SUCCESS) : new OutMessage<>(Status.FAIL);
    }

    /**
     * 记录撤销
     */
    @Override
    public OutMessage<String> undo(String recordId, String reason, boolean isCheckUnDO) {
        ActivistTransferRecord record = this.getById(recordId);
        //不能更改已通过或退回的审批记录
        if (record == null || !TransferRecordConstant.TRANSFERING.equals(record.getStatus())) {
            return new OutMessage<>(Status.PARA_ERROR);
        }
        //非本人发起转接
//        if (isCheckUnDO && !StrKit.equals(record.getUserId(), UserConstant.USER_CONTEXT.get().getUser().getId())) {
//            return new OutMessage<>(Status.TRANSFER_UNDO_PERMISSION_ERROR);
//        }
        ActivistTransferApproval transferApproval = iActivistTransferApprovalService.findByRecordIdAndParentId(recordId, TransferApprovalConstant.ROOT_ID);
        if (transferApproval == null) {
            return new OutMessage<>(Status.PARA_ERROR);
        }
        return this.backTransfer(recordId, reason) ? new OutMessage<>(Status.SUCCESS) : new OutMessage<>(Status.FAIL);
    }


    public boolean backTransfer(String recordId, String reason) {
        ActivistTransferRecord record = getById(recordId);
        ActivistTransferApproval transferApproval = iActivistTransferApprovalService.findByRecordIdAndParentId(recordId, TransferApprovalConstant.ROOT_ID);
        //更新转接记录状态
        ActivistTransferRecord updateRecord = new ActivistTransferRecord();
        updateRecord.setId(recordId);
        updateRecord.setStatus(TransferRecordConstant.TRANSFER_UNDO);
        updateRecord.setRemark(reason);
        boolean b1 = updateById(updateRecord);
        //生成一条撤销记录日志
        boolean b2 = iActivistTransferLogService.insertOrgTransferLog(transferApproval.getId(), null, TransferLogConstant.UNDO_STATUS, reason);

        String recordMemId = record.getMemId();
        activistMemTransferRecordService.updateMemIsTransfer(recordId, Collections.singletonList(recordMemId));
        // 撤销的时候，如果接受节点不在当前数据库就需要同步到中间交换区，让接受数据节点知晓
        String targetOrgId = record.getTargetOrgId();
        Org orgByCode = iOrgService.findOrgByCode(targetOrgId);
        if (b1 && b2 && ObjectUtil.isNull(orgByCode) && StrKit.notBlank(targetOrgId)) {
            activistMemTransferRecordService.insertDataByMiddle(recordId);
        }
        return b1 && b2;
    }

    /**
     * 查询转出记录列表
     */
    @Override
    public OutMessage<?> findOutByPage(TransferRecordListDTO data, boolean isOut) {
        UserTicket userTicket = UserConstant.USER_CONTEXT.get();
        if (StrKit.isBlank(data.getOrgId())) {
            data.setOrgId(userTicket.getUserRolePermission().getOrgId());
        }
//        Org org = iOrgService.findOrgByCode(data.getOrgId());
        Org org = iOrgService.findOrgByCode(data.getOrgId(),"orgCode");
        if (org == null) {
            return new OutMessage<>(Status.ORG_NOT_EXIST);
        }
        String currentOrgCode = userTicket.getUserRolePermission().getOrgCode();
        if (StrKit.isBlank(org.getOrgCode()) || !StrUtil.startWith(org.getOrgCode(), currentOrgCode)) {
            return new OutMessage<>(Status.PERMISSION_DENIED);
        }
        Page<ActivistTransferListVo> transferPage = this.findOutTransferPage(data, isOut);
        return new OutMessage<>(Status.SUCCESS, transferPage);
    }

    /**
     * 导出Excel
     */
    @Override
    public OutMessage<?> exportXsl(TransferRecordListDTO data) throws Exception {
        data.setPageNum(1);
        data.setPageSize(10000000);
        if (StrUtil.isEmpty(data.getIsOut())) {
            return new OutMessage<>(Status.PARA_ERROR);
        }
        OutMessage<?> message = this.findOutByPage(data, StringToBooleanConverter.convert(data.getIsOut()));
        if (message.getCode() != 0) {
            return message;
        }
        List<ActivistTransferListVoExcel> result = new ArrayList<>();
        int index = 1;
        Page<ActivistTransferListVo> page = (Page<ActivistTransferListVo>) message.getData();
        for (ActivistTransferListVo vo : page.getRecords()) {
            ActivistTransferListVoExcel excel = new ActivistTransferListVoExcel();
            BeanUtils.copyProperties(vo, excel);
            excel.setXh(index);
            excel.setCreateTime(DateUtil.format(vo.getCreateTime(), "yyyy-MM-dd"));
            excel.setStatus(Objects.equals(vo.getStatus(), 0) ? "转接中" : (Objects.equals(vo.getStatus(), 1) ? "已完成" : "已撤销"));
            Object extraData = vo.getExtraData();
            if (Objects.nonNull(extraData)) {
                BeanUtils.copyProperties(extraData, excel);
            } else {
                String memId = vo.getMemId();
                MemDevelop memDevelop = iMemDevelopService.findAllByCode(memId);
                if (Objects.nonNull(memDevelop)) {
                    BeanUtils.copyProperties(memDevelop, excel);
                }
            }
            result.add(excel);
            index++;
        }
        Workbook workbook = ExcelExportUtil.exportExcel(new ExportParams(null, ""), ActivistTransferListVoExcel.class, result);
        Map<String, String> map = ExcelUtil.exportFile(workbook, "");
        return new OutMessage<>(Status.SUCCESS, map);
    }

    public Page<ActivistTransferListVo> findOutTransferPage(TransferRecordListDTO data, boolean isOut) {
        Condition condition = this.getListCondition(data, isOut);
        StringBuilder sql = new StringBuilder().append(condition.toString()).append(" ORDER BY ccp_activist_transfer_record.status,ccp_activist_transfer_record.create_time DESC").append(" LIMIT ").append(data.getPageSize()).append(" OFFSET ").append((data.getPageNum() - 1) * data.getPageSize());
        Long total = isOut ? this.getBaseMapper().findOutTotalByConditon(condition.toString()) : this.getBaseMapper().findInTotalByConditon(condition.toString());
        List<ActivistTransferRecord> recordList = isOut ? this.getBaseMapper().findOutByPage(sql.toString()) : this.getBaseMapper().findInByPage(sql.toString());
        //数据组装
        List<Record> outTypes = CacheUtils.getDic(TransferRecordConstant.DICT_OUT);
        List<Record> inTypes = CacheUtils.getDic(TransferRecordConstant.DICT_IN);
        List<ActivistTransferListVo> dtoList = recordList.stream().map(record -> {
            ActivistTransferListVo vo = new ActivistTransferListVo();
            BeanUtils.copyProperties(record, vo);
            vo.setIsOrg(record.getMemId() == null ? 1 : 0);
            vo.setType(isOut ? record.getOutType() : record.getInType());
            setTransferTypeName(isOut ? outTypes : inTypes, vo);
            vo.setExtraData(record.getMemInfoExtraData());
            return vo;
        }).collect(Collectors.toList());
        Page<ActivistTransferListVo> dtoPage = new Page<>(data.getPageNum(), data.getPageSize());
        dtoPage.setRecords(dtoList);
        dtoPage.setTotal(total);
        return dtoPage;
    }

    private void setTransferTypeName(List<Record> types, ActivistTransferListVo dto) {
        types.stream().filter(type -> type.getStr("key").equalsIgnoreCase(dto.getType())).findFirst().ifPresent(type -> dto.setTypeName(type.getStr("name")));
    }

    private Condition getListCondition(TransferRecordListDTO data, boolean isOut) {
        Org orgByCode = iOrgService.findOrgByCode(data.getOrgId(),"orgCode");
        Condition condition = noCondition()
                .and(field(name("org_code")).like(orgByCode.getOrgCode() + "%"))
                .and(field(name("type")).ne("29"));
        if (Objects.nonNull(data.getTypes()) && data.getTypes().length > 0) {
            if (isOut) {
                condition = condition.and(field(name("out_type"), String.class).in(Arrays.asList(data.getTypes())));
            } else {
                condition = condition.and(field(name("in_type"), String.class).in(Arrays.asList(data.getTypes())));
            }
        }
        if (Objects.nonNull(data.getStatus()) && data.getStatus().length > 0) {
            condition = condition.and(field(name("status"), Integer.class).in(Arrays.asList(data.getStatus())));
        }
        if (StrKit.notBlank(data.getKeyWord())) {
            condition = condition.and(field(name("ccp_activist_transfer_record", "name"), String.class).like("%" + data.getKeyWord() + "%")
                    .or(field(name("src_org_name"), String.class).like("%" + data.getKeyWord() + "%"))
                    .or(field(name("target_org_name"), String.class).like("%" + data.getKeyWord() + "%")));
        }
        return condition;
    }

    /**
     * 参数校验
     */
    private OutMessage<?> checkParams(TransferRecordDTO dto, ActivistTransferRecord transferRecord) {
        BetweenReportDateVo betweenReportDate = iStatisticsYearService.isBeforeReportDate(dto.getTransferOutTime(), "2");
        if (!betweenReportDate.getIsBetween()) {
            return new OutMessage<TransferRecord>(Status.NOT_IN_BETWEEN_DEFAULT_DATE).format("转出日期", betweenReportDate.getMessage());
        }
        transferRecord.setMemId(dto.getMemId());
        String srcOrgId = dto.getSrcOrgId();
        String targetOrgId = dto.getTargetOrgId();
        //判断转接类型
        boolean isOk = activistMemTransferRecordService.setTransferTypeCode(dto.getType(), transferRecord);
        if (!isOk) {
            return new OutMessage<>(Status.ORG_TRANSFER_TYPE_ERROR);
        }
        //设置源组织和目标组织id
        Ret ret = activistMemTransferRecordService.setSrcAndTargetOrgId(srcOrgId, targetOrgId, transferRecord);
        if (ret.isFail()) {
            return ret.getAs(RetConstant.MESSAGE);
        }
        //设置源组织和目标组织当前组织关系
        ret = activistMemTransferRecordService.setSrcAndTargetRelation(srcOrgId, targetOrgId, transferRecord);
        if (ret.isFail()) {
            return ret.getAs(RetConstant.MESSAGE);
        }
        //设置公共节点
        ret = activistMemTransferRecordService.setCommonNodeId(srcOrgId, targetOrgId, transferRecord);
        if (ret.isFail()) {
            return ret.getAs(RetConstant.MESSAGE);
        }
        //判断目标组织或组织上级是否在转接中
        if (StrKit.notBlank(targetOrgId)) {
            List<ActivistTransferRecord> records = this.targetExistParentsOrgIsTransfer(targetOrgId);
            if (CollUtil.isNotEmpty(records)) {
                return new OutMessage<TransferRecord>(Status.TRANSFER_TARGET_ORG_ERROR_MESSAGE).format(records.get(0).getName());
            }
        }
        //判断源组织或源组织上级以及源组织下级及其下级人员是否处于转接中
        if (StrKit.notBlank(srcOrgId)) {
            List<ActivistTransferRecord> records = this.srcExistParentsOrgIsTransfer(srcOrgId);
            Optional<ActivistTransferRecord> first = records.stream().filter(e -> StrUtil.isEmpty(e.getMemId())).findFirst();
            if (first.isPresent()) {
                return new OutMessage<TransferRecord>(Status.TRANSFER_SRC_ORG_ERROR_MESSAGE).format(first.get().getName());
            }
        }
        return new OutMessage<>(Status.SUCCESS, transferRecord);
    }

    /**
     * 生成转接记录模型到列表
     */
    private OutMessage<?> addTransferRecordToList(List<TransferRecordDTO> dtoList, List<ActivistTransferRecord> transferRecordList, List<ActivistTransferRecord> tempList) {
        Map<String, ActivistTransferRecord> tempMap = tempList.stream().collect(Collectors.toMap(ActivistTransferRecord::getMemId, v -> v, (key1, key2) -> key1));
        UserTicket userTicket = UserConstant.USER_CONTEXT.get();
        for (TransferRecordDTO data : dtoList) {
            ActivistTransferRecord transferRecord = new ActivistTransferRecord();
            transferRecord.setId(StrKit.getRandomUUID());
            //判断对象是否存在
            MemDevelop memDevelop = iMemDevelopService.findByCode(data.getMemId());
            if (memDevelop == null) {
                return new OutMessage<>(Status.OBJEC_NOT_EXIST);
            }
            //判断是否有权限调整
            String developOrgCode = memDevelop.getDevelopOrgCode();
            String currentOrgCode = userTicket.getUserRolePermission().getOrgCode();
            if (StrKit.isBlank(developOrgCode) || StrKit.isBlank(currentOrgCode) || !StrUtil.startWith(developOrgCode, currentOrgCode)) {
                return new OutMessage<>(Status.PERMISSION_DENIED);
            }
            ActivistTransferRecord temp = tempMap.get(data.getMemId());
            String srcOrgId = temp.getSrcOrgId();
            // 查询发起党支部得单位信息
            OrgAll byCode = iOrgAllService.findByCode(srcOrgId);
            transferRecord.setOutD04Code(byCode.getD04Code());
            transferRecord.setUserId(userTicket.getUser().getId());
            transferRecord.setName(memDevelop.getName());
            transferRecord.setMemId(data.getMemId());
            transferRecord.setSrcOrgId(srcOrgId);
            transferRecord.setSrcOrgName(data.getSrcOrgName());
            transferRecord.setTargetOrgName(data.getTargetOrgName());
            transferRecord.setTargetOrgId(temp.getTargetOrgId());
            transferRecord.setCommonOrgId(temp.getCommonOrgId());
            transferRecord.setCommonOrgName(temp.getCommonOrgName());
            transferRecord.setSrcOrgRelationAsList((List<String>) (temp.getSrcOrgRelation()));
            transferRecord.setSrcOrgRelationRelAsList((List<String>) (temp.getSrcOrgRelationRel()));
            transferRecord.setTargetOrgRelationAsList((List<String>) (temp.getTargetOrgRelation()));
            transferRecord.setTargetOrgRelationRelAsList((List<String>) (temp.getTargetOrgRelationRel()));
            transferRecord.setType(temp.getType());
            transferRecord.setInType(temp.getInType());
            transferRecord.setOutType(temp.getOutType());
            transferRecord.setStatus(TransferRecordConstant.TRANSFERING);
            transferRecord.setReason(data.getReason());
            transferRecord.setCreateTime(new Date());
            transferRecord.setUpdateTime(new Date());
            transferRecord.setWhetherExtendPrepPeriod(data.getWhetherExtendPrepPeriod());
            transferRecord.setTransferOutTime(data.getTransferOutTime());
            transferRecord.setReportTime(data.getReportTime());
            // 增加转出原因
            transferRecord.setD146Code(data.getD146Code());
            transferRecord.setD146Name(data.getD146Name());
            transferRecordList.add(transferRecord);
        }
        return new OutMessage<>(Status.SUCCESS);
    }

    public boolean transferAdjust(ActivistTransferRecord record) {
        String memId = record.getMemId();
        MemDevelop updateMem = iMemDevelopService.findByCode(memId);
        if (updateMem != null) {
            Org targetOrg = iOrgService.findOrgByCode(record.getTargetOrgId());
            //转出到其他节点
            if (targetOrg == null) {
                updateMem.setIsHistory(1);
                updateMem.setDeleteTime(new Date());
            } else {
                updateMem.setDevelopOrgCode(targetOrg.getOrgCode());
                updateMem.setHasUnitStatistics(null);
                updateMem.setHasUnitProvince(null);
                updateMem.setUnitInformation("");
                updateMem.setD04Code("");
                updateMem.setStatisticalUnit("");
            }
            updateMem.setOrgCode(record.getTargetOrgId());
            updateMem.setOrgName(record.getTargetOrgName());
            updateMem.setIsTransfer(0);
            boolean update = iMemDevelopService.update(updateMem, Wrappers.<MemDevelop>lambdaUpdate()
                    .set(Objects.isNull(updateMem.getHasUnitStatistics()), MemDevelop::getHasUnitStatistics, null)
                    .set(Objects.isNull(updateMem.getHasUnitProvince()), MemDevelop::getHasUnitProvince, null)
                    .eq(MemDevelop::getId, updateMem.getId()));
            if (update) {
                ThreadUtil.execAsync(() -> {
                    // 更新表 ccp_mem_develop_all
                    iMemDevelopAllService.update(Wrappers.<MemDevelopAll>lambdaUpdate().set(MemDevelopAll::getOrgCode, record.getTargetOrgId())
                            .set(Objects.nonNull(targetOrg), MemDevelopAll::getDevelopOrgCode, targetOrg.getOrgCode())
                            .set(Objects.isNull(targetOrg), MemDevelopAll::getDeleteTime, new Date())
                            .eq(MemDevelopAll::getCode, memId));
                    // 更新表 ccp_develop_step_log
                    iDevelopStepLogService.update(Wrappers.<DevelopStepLog>lambdaUpdate().set(Objects.isNull(targetOrg), DevelopStepLog::getDeleteTime, new Date())
                            .set(DevelopStepLog::getOrgCode, record.getTargetOrgId()).set(DevelopStepLog::getOrgName, record.getTargetOrgName())
                            .set(Objects.nonNull(targetOrg), DevelopStepLog::getLogOrgCode, targetOrg.getOrgCode())
                            .eq(DevelopStepLog::getMemCode, memId));
                    // 更新表 ccp_develop_step_log_all
                    iDevelopStepLogAllService.update(Wrappers.<DevelopStepLogAll>lambdaUpdate().set(Objects.isNull(targetOrg), DevelopStepLogAll::getDeleteTime, new Date())
                            .set(DevelopStepLogAll::getOrgCode, record.getTargetOrgId()).set(DevelopStepLogAll::getOrgName, record.getTargetOrgName())
                            .set(Objects.nonNull(targetOrg), DevelopStepLogAll::getLogOrgCode, targetOrg.getOrgCode())
                            .eq(DevelopStepLogAll::getMemCode, memId));
                    this.update(new LambdaUpdateWrapper<ActivistTransferRecord>().set(ActivistTransferRecord::getEffectMems, 1).eq(ActivistTransferRecord::getId, record.getId()));
                });
            }
            return update;
        } else {
            // 处理特殊情况,当人员在本地数据为空，但是数据里面又存着人员ID，证明是其他地方数据节点来的数据
            // 外面来得人员也有两种情况， 因为现在code不再是唯一主键了， 所以存在一种情况，一个人转出当前数据节点，然后又转回当前数据节点得情况，直接保存会造成两条code一样得数据
            if (StrKit.notBlank(memId)) {
                //1、基础表数据
                JSONObject extraData = (JSONObject) record.getExtraData();
                MemDevelop transFerMem = JSONObject.toJavaObject(extraData, MemDevelop.class);
                //处理党组织唯一标识符以党组织层级码相关得信息
                Org targetOrg = iOrgService.findOrgByCode(record.getTargetOrgId());
                transFerMem.setOrgCode(record.getTargetOrgId());
                transFerMem.setOrgName(record.getTargetOrgName());
                transFerMem.setDevelopOrgCode(targetOrg.getOrgCode());
                transFerMem.setHasUnitStatistics(null);
                transFerMem.setHasUnitProvince(null);
                transFerMem.setUnitInformation("");
                transFerMem.setD04Code("");
                transFerMem.setStatisticalUnit("");
                transFerMem.setIsTransfer(0);
                transFerMem.setId(null);
                // 因为设置了唯一主键，所以需要重新设置人员的code
                transFerMem.setCode(StrKit.getRandomUUID());
                //2、log表
                JSONArray extraDataLog = (JSONArray) record.getExtraDataLog();
                List<DevelopStepLog> stepLogs = extraDataLog.toJavaList(DevelopStepLog.class);
                stepLogs.forEach(stepLog -> {
                    stepLog.setMemCode(transFerMem.getCode());
                    stepLog.setLogOrgCode(targetOrg.getOrgCode());
                    stepLog.setOrgName(record.getTargetOrgName());
                    stepLog.setOrgCode(record.getTargetOrgId());
                    stepLog.setIsTransfer(0);
                    stepLog.setId(null);
                    stepLog.setCode(StrKit.getRandomUUID());
                    iDevelopStepLogService.save(stepLog);
                });
                boolean save = iMemDevelopService.save(transFerMem);
                if (save) {
                    if (StrUtil.isNotBlank(transFerMem.getDevelopOrgCode()) && StrUtil.startWithAny(transFerMem.getDevelopOrgCode(), com.zenith.front.common.constant.UserConstant.ZUN_YI_ORG_LEVEL_CODE)) {
                        // 当档案唯一码不存在则新生成
                        //  判断是否积极分子、发展对象
                        if (StrUtil.equalsAny(transFerMem.getD08Code(), CommonConstant.FOUR, CommonConstant.THREE)) {
                            if (StrUtil.isBlank(transFerMem.getDigitalLotNo())) {
                                transFerMem.setDigitalLotNo(IdUtil.simpleUUID());
                            }
                            List<MemDevelopProcess> processList = TransferRecordServiceImpl.generate(transFerMem.getD08Code(), transFerMem.getDigitalLotNo(), transFerMem.getActiveDate());
                            MemDevelopProcess process = processList.stream().filter(e -> Objects.isNull(e.getApproveTime())).findFirst().orElse(null);
                            if(Objects.nonNull(process)) {
                                transFerMem.setProcessNode(process.getProcessNode());
                                LambdaUpdateWrapper<MemDevelop> wrapper = Wrappers.lambdaUpdate();
                                wrapper.eq(MemDevelop::getCode, transFerMem.getCode())
                                        // 更新党员流程状态
                                        .set(MemDevelop::getProcessNode, process.getProcessNode())
                                        .set(MemDevelop::getDigitalLotNo, transFerMem.getDigitalLotNo());
                                iMemDevelopService.update(wrapper);
                                // 新增流程
                                memDevelopProcessMapper.myInserBatch(processList);
                            }
                        }
                    }

                    ThreadUtil.execAsync(() -> {
                        iSyncMemService.syncMemDevelop(transFerMem.getCode());
                        stepLogs.forEach(e -> syncDevelopStepLogAll.syncDevelopStepLog(e.getCode()));
                        // 存储新档案相关数据
                        this.insertNewDigitalData(record);
                    });
                }
                return save;
            }
        }
        return true;
    }

    /***
     * 判断目标组织和上级组织是否正在转接
     * */
    public List<ActivistTransferRecord> targetExistParentsOrgIsTransfer(String targetOrgId) {
        LambdaQueryWrapper<ActivistTransferRecord> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.isNull(ActivistTransferRecord::getMemId)
                .eq(ActivistTransferRecord::getStatus, TransferRecordConstant.TRANSFERING)
                .apply("target_org_relation :: jsonb ?? {0}", targetOrgId);
        return list(lambdaQueryWrapper);
    }

    /***
     * 判断源组织是否有上级组织正在进行关系转接 或者 下级组织转接中 或者 下级人员转接中
     * */
    public List<ActivistTransferRecord> srcExistParentsOrgIsTransfer(String srcOrgId) {
        LambdaQueryWrapper<ActivistTransferRecord> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper
                .eq(ActivistTransferRecord::getStatus, TransferRecordConstant.TRANSFERING)
                .apply(" (\"src_org_id\" IN ( SELECT \"code\" FROM find_relation_up ( '" + srcOrgId + "' ) ) OR ( src_org_relation :: jsonb ?? {0} ) ) ", srcOrgId);
        return list(lambdaQueryWrapper);
    }

    /**
     * 存储新档案相关数据
     */
    public void insertNewDigitalData(ActivistTransferRecord record) {
        // 1、流程表 ccp_mem_develop_process
//        JSONArray extraDataProcess = (JSONArray) record.getExtraDataProcess();
//        if (Objects.nonNull(extraDataProcess)) {
//            List<MemDevelopProcess> processList = extraDataProcess.toJavaList(MemDevelopProcess.class);
//            memDevelopProcessMapper.deleteBatchIds(processList.stream().map(MemDevelopProcess::getCode).collect(Collectors.toSet()));
//            processList.forEach(e -> memDevelopProcessMapper.insert(e));
//        }
        // 2、档案表 ccp_mem_digital
        JSONArray dataDigital = (JSONArray) record.getExtraDataDigital();
        if (Objects.nonNull(dataDigital)) {
            List<MemDigital> memDigitalList = dataDigital.toJavaList(MemDigital.class);
            memDigitalMapper.deleteBatchIds(memDigitalList.stream().map(MemDigital::getCode).collect(Collectors.toSet()));
            memDigitalList.forEach(e -> memDigitalMapper.insert(e));
        }

        // 3、档案操作日志表 ccp_mem_digital_operation_log
        JSONArray digitalLog = (JSONArray) record.getExtraDataDigitalLog();
        if (Objects.nonNull(digitalLog)) {
            List<MemDigitalOperationLog> operationLogList = digitalLog.toJavaList(MemDigitalOperationLog.class);
            digitalOperationLogMapper.deleteBatchIds(operationLogList.stream().map(MemDigitalOperationLog::getCode).collect(Collectors.toSet()));
            operationLogList.forEach(e -> digitalOperationLogMapper.insert(e));
        }

    }

}
