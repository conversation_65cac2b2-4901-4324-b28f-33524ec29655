package com.zenith.front.core.service.develop;

import cn.hutool.core.date.DateUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.zenith.front.api.develop.IDevelopPlanService;
import com.zenith.front.model.custom.Record;
import com.zenith.front.dao.mapper.develop.DevelopPlanMapper;
import com.zenith.front.model.bean.DevelopPlan;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;


/**
 * <p>
 * 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-07-10
 */
@Service
public class DevelopPlanServiceImpl extends ServiceImpl<DevelopPlanMapper, DevelopPlan> implements IDevelopPlanService {

    @Resource
    DevelopPlanMapper developPlanMapper;

    @Override
    public Page<Record> getIndexList(Page page, String memOrgCode, Boolean hasSubordinate) {

        Page<Map<String, Object>> indexList = developPlanMapper.getIndexList(page, memOrgCode, hasSubordinate);
        List<Record> recordList = new ArrayList<>();
        indexList.getRecords().forEach(val -> {
            Record record = new Record();
            record.put(val);
            recordList.add(record);
        });
        page.setRecords(recordList);
        return page;
    }

    @Override
    public DevelopPlan findByOrgCode(String code) {
        LambdaQueryWrapper<DevelopPlan> wrapper = new QueryWrapper<DevelopPlan>().lambda()
                .eq(DevelopPlan::getOrgCode, code)
                .eq(DevelopPlan::getYear, String.valueOf(DateUtil.year(new Date())))
                .isNull(DevelopPlan::getDeleteTime)
                .last("limit 1");
        return this.getOne(wrapper);
    }

    @Override
    public Long findSubIndexCount(String memOrgCode) {
        return developPlanMapper.findSubIndexCount(memOrgCode);
    }

}
