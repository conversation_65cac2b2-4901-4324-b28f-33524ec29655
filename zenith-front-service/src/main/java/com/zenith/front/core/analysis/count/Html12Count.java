package com.zenith.front.core.analysis.count;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.b1809.analysis.entity.PeggingPara;
import com.b1809.analysis.plugin.elasticsearch.EsKit;
import com.b1809.analysis.result.ReportResult;
import com.b1809.analysis.table.helper.TableHelper;
import com.b1809.analysis.table.helper.factory.TableHelperFactory;
import com.jfinal.plugin.activerecord.Page;
import com.jfinal.plugin.activerecord.Record;
import com.zenith.front.core.analysis.ext.condition.MemAllCondition;
import org.jooq.Condition;
import org.jooq.Record1;
import org.jooq.SelectConditionStep;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;

import static com.zenith.front.common.constant.DBConstant.DSL_CONTEXT;
import static org.jooq.impl.DSL.*;

/**
 *  十二表 党员出党情况
 * <AUTHOR>
 * @create_date 2024-01-31 10:56
 * @description
 */
@Component
@Deprecated
public class Html12Count {

    /**
     * 统计
     * @param reportCode
     * @param orgCode
     * @param orgLevelCode
     * @param resultMap
     * @param tableYear
     * @return
     */
    public Map<String, Map<String, Number>> getCount(String reportCode, String orgCode, String orgLevelCode, Map<String, Map<String, Number>> resultMap, String tableYear){
        String ccpMemAll = StrUtil.isEmpty(tableYear) ? "ccp_mem_all" : "ccp_mem_all_" + tableYear;
        // 19 行
        Condition condition = getAllCondition(orgCode, orgLevelCode).and(getRowCondition(1));
        SelectConditionStep<Record1<Object>> ccpMemAllSql = DSL_CONTEXT.select(field("d029_code,delete_time,code,d12_code,d030_code,code"))
                .from(table(name(ccpMemAll)).as("ccp_mem_all")).where(condition);
        List<Record> records = EsKit.findBySql(ccpMemAllSql.toString()).toRecord();
        if (CollUtil.isNotEmpty(records)) {
            Map<String, Number> map = resultMap.get("table0");
            map.put("cell_1_1", records.stream().filter(s -> (StrUtil.isNotEmpty(s.getStr("delete_time")) && StrUtil.equalsAny(s.getStr("d029_code"),"C15","C26","C36","C24","C34","C25","C35","C27","C37","C22","C23","C29"))
                    || StrUtil.equalsAny(s.getStr("d12_code"), "6","7")).count());
            // 出党的计算到劝而不退除名
            map.put("cell_1_5", records.stream().filter(s -> (StrUtil.isNotEmpty(s.getStr("delete_time")) && StrUtil.equalsAny(s.getStr("d029_code"), "C25","C35")) || StrUtil.equalsAny(s.getStr("d12_code"), "6","7")).count());

            map.put("cell_19_1", records.stream().filter(s -> (StrUtil.isNotEmpty(s.getStr("delete_time")) && StrUtil.equalsAny(s.getStr("d029_code"),"C15","C26","C36","C24","C34","C25","C35","C27","C37","C22","C23","C29")
                    && StrUtil.contains(s.getStr("d030_code"), "9")) || StrUtil.equalsAny(s.getStr("d12_code"), "6","7")).count());
            // 出党的计算到劝而不退除名
            map.put("cell_19_5", records.stream().filter(s -> (StrUtil.isNotEmpty(s.getStr("delete_time")) && StrUtil.equalsAny(s.getStr("d029_code"), "C25","C35")
                    && StrUtil.contains(s.getStr("d030_code"), "9")) || StrUtil.equalsAny(s.getStr("d12_code"), "6","7")).count());
        }
        return resultMap;
    }

    /**
     * 反查
     * @param peggingPara
     * @return
     */
    public Map<String, Object> getReportPageResult(PeggingPara peggingPara) {
        String tableYear = peggingPara.getYear();
        String tableName = StrUtil.isEmpty(tableYear) ? "ccp_mem_all" : "ccp_mem_all_" + tableYear;
        String rowIndex = peggingPara.getRowIndex();
        String colIndex = peggingPara.getColIndex();
        Condition condition = getAllCondition(peggingPara.getOrgCode(), peggingPara.getOrgLevelCode())
                .and(getRowCondition(Integer.parseInt(rowIndex)))
                .and(getCell(Integer.parseInt(colIndex)));
        Map<String, Object> resultMap = new HashMap<>();
        TableHelper tableHelper = TableHelperFactory.getHelper();
        Page<Map<String, Object>> page = tableHelper.findPage(peggingPara.getPageNum(), peggingPara.getPageSize(), tableName, condition,
                peggingPara.getIncludeFieldList(), peggingPara.getOrderByFieldList());
        return ReportResult.toResult(resultMap, page, tableName);
    }

    /**
     * 大条件
     * @param orgCode
     * @param orgLevelCode
     * @return
     */
    public Condition getAllCondition(String orgCode, String orgLevelCode) {
        return noCondition().and("d08_code in('1','2')")
                .and(new Html1Count().getEsReportDateSql("leave_org_date"))
                .and(new MemAllCondition().create(orgCode, orgLevelCode));
    }

    /**
     * 行
     * @param row
     * @return
     */
    public Condition getRowCondition(Integer row){
        Condition condition = noCondition();
        if(Objects.equals(row, 19)){
            condition = condition.and("((delete_time is not null and d029_code like 'C%' and d030_code like '%9%') or ((is_transfer is null or is_transfer!=1) and d12_code in ('6', '7')))");
        }
        if(Objects.equals(row, 1)){
            condition = condition.and("((delete_time is not null and d029_code like 'C%') or ((is_transfer is null or is_transfer!=1) and d12_code in ('6', '7')))");
        }
        return condition;
    }

    /**
     * 列
     * @param cell
     * @return
     */
    public Condition getCell( Integer cell){
        Condition condition = noCondition();
        if(Objects.equals(cell, 1)){
            condition = condition.and(field("d029_code", String.class).in( "C15","C26","C36","C24","C34","C25","C35","C27","C37","C22","C23","C29").and("delete_time is not null").or(field("d12_code", String.class).in("6","7")));
        }
        if(Objects.equals(cell, 2)){
            condition =  condition.and(field("d029_code", String.class).in( "C15").and("delete_time is not null"));
        }
        if(Objects.equals(cell, 3)){
            condition = condition.and(field("d029_code", String.class).in( "C26","C36").and("delete_time is not null"));
        }
        if(Objects.equals(cell, 4)){
            condition = condition.and(field("d029_code", String.class).in( "C24","C34","C38").and("delete_time is not null"));
        }
        if(Objects.equals(cell, 5)){
            condition = condition.and(field("d029_code", String.class).in( "C25","C35").and("delete_time is not null").or(field("d12_code", String.class).in("6","7")));
        }
        if(Objects.equals(cell, 6)){
            condition = condition.and(field("d029_code", String.class).in( "C27","C37").and("delete_time is not null"));
        }
        if(Objects.equals(cell, 7)){
            condition = condition.and(field("d029_code", String.class).in( "C22","C23").and("delete_time is not null"));
        }
        if(Objects.equals(cell, 8)){
            condition = condition.and(field("d029_code", String.class).in( "C29").and("delete_time is not null"));
        }
        return condition;
    }
}
