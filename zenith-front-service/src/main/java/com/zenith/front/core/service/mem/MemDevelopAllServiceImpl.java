package com.zenith.front.core.service.mem;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.zenith.front.api.mem.IMemDevelopAllService;
import com.zenith.front.dao.mapper.mem.MemDevelopAllMapper;
import com.zenith.front.model.bean.MemDevelopAll;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-10-26
 */
@Service
public class MemDevelopAllServiceImpl extends ServiceImpl<MemDevelopAllMapper, MemDevelopAll> implements IMemDevelopAllService {

    @Override
    public MemDevelopAll findByCode(String code) {
        return getOne(new LambdaQueryWrapper<MemDevelopAll>().eq(MemDevelopAll::getCode, code).orderByDesc(MemDevelopAll::getDeleteTime).last("limit 1"));
    }

}
