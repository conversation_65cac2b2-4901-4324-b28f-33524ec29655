package com.zenith.front.core.service.problemfeedback;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.zenith.front.api.problemfeedback.IProblemFeedbackService;
import com.zenith.front.common.kit.StrKit;
import com.zenith.front.dao.mapper.problemfeedback.ProblemFeedbackMapper;
import com.zenith.front.model.dto.ProblemFeedbackDTO;
import com.zenith.front.model.dto.ProblemFeedbackListDTO;
import com.zenith.front.model.message.OutMessage;
import com.zenith.front.model.message.Status;
import com.zenith.front.model.bean.ProblemFeedback;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.zenith.front.model.bean.User;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;

/**
 * <p>
 * 问题反馈 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-09-23
 */
@Service
public class ProblemFeedbackServiceImpl extends ServiceImpl<ProblemFeedbackMapper, ProblemFeedback> implements IProblemFeedbackService {

    @Resource
    private ProblemFeedbackMapper feedbackMapper;

    @Override
    public OutMessage addOrUpdate(ProblemFeedbackDTO data, User user) {
        if (StrKit.isBlank(data.getCode())) {
            ProblemFeedback feedback = new ProblemFeedback();
            BeanUtils.copyProperties(data,feedback);
            feedback.setCreateTime(new Date());
            feedback.setUserId(user.getId());
            feedbackMapper.insert(feedback);
        } else {
            ProblemFeedback feedback = feedbackMapper.selectById(data.getCode());
            if (feedback == null) {
                return new OutMessage<>(Status.OBJECT_DBISNOTALIVE);
            }
            BeanUtils.copyProperties(data,feedback);
            feedback.setUpdateTime(new Date());
            feedbackMapper.updateById(feedback);
        }
        return new OutMessage<>(Status.SUCCESS);
    }



    @Override
    public OutMessage getList(ProblemFeedbackListDTO data) {
        Page<ProblemFeedback> page = new Page<>(data.getPageNum(), data.getPageSize());
        LambdaQueryWrapper<ProblemFeedback> query = new LambdaQueryWrapper<ProblemFeedback>()
                .isNull(ProblemFeedback::getDeleteTime);
        if (StrKit.notBlank(data.getOrgName())) {
            query.like(ProblemFeedback::getQuestionsBriefly,data.getOrgName());
        }
        if ("1".equals(data.getType())) {
            query.orderByDesc(ProblemFeedback::getLookNumber);
        } else {
            query.orderByDesc(ProblemFeedback::getCreateTime);
        }
        page(page, query);
        return new OutMessage<>(Status.SUCCESS, page);
    }

    @Override
    public OutMessage details(String code) {
        ProblemFeedback feedback = feedbackMapper.selectById(code);
        feedback.setLookNumber(feedback.getLookNumber() + 1);
        int i = feedbackMapper.updateById(feedback);
        return new OutMessage<>(Status.SUCCESS, feedback);
    }

    @Override
    public OutMessage delete(String code) {
        ProblemFeedback feedback = feedbackMapper.selectById(code);
        if (feedback == null) {
            return new OutMessage<>(Status.OBJECT_DBISNOTALIVE);
        }
        feedback.setDeleteTime(new Date());
        int i = feedbackMapper.updateById(feedback);
        return new OutMessage<>(i > 0 ? Status.SUCCESS : Status.FAIL);
    }
}
