package com.zenith.front.core.feign.dto;

/**
 * <p>
 * 村社区发展党员基础信息
 * </p>
 *
 * <AUTHOR>
 * @since 2022-08-29
 */
public class VcDevelopMemInfo {

    /**
     * 人员标识
     */
    private String code;

    /**
     * 发展党员姓名
     */
    private String name;

    /**
     * 公民身份证
     */
    private String idcard;

    /**
     * 民族
     */
    private String d06Code;

    /**
     * 民族
     */
    private String d06Name;

    /**
     * 籍贯
     */
    private String d48Code;

    /**
     * 籍贯
     */
    private String d48Name;

    /**
     * 性别
     */
    private String sexCode;

    /**
     * 性别
     */
    private String sexName;

    /**
     * 电话
     */
    private String phone;

    /**
     * 学历
     */
    private String d07Code;

    /**
     * 学历
     */
    private String d07Name;

    /**
     * 工作岗位
     */
    private String d09Code;

    /**
     * 工作岗位
     */
    private String d09Name;

    /**
     * 人员类型
     */
    private String d08Code;

    /**
     * 人员类型
     */
    private String d08Name;

    /**
     * 组织code
     */
    private String orgCode;

    /**
     * 组织名称
     */
    private String orgName;

    /**
     * 数据类型
     */
    private String dataType;

    /**
     * 节点key
     */
    private String nginxKey;

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getIdcard() {
        return idcard;
    }

    public void setIdcard(String idcard) {
        this.idcard = idcard;
    }

    public String getD06Code() {
        return d06Code;
    }

    public void setD06Code(String d06Code) {
        this.d06Code = d06Code;
    }

    public String getD06Name() {
        return d06Name;
    }

    public void setD06Name(String d06Name) {
        this.d06Name = d06Name;
    }

    public String getD48Code() {
        return d48Code;
    }

    public void setD48Code(String d48Code) {
        this.d48Code = d48Code;
    }

    public String getD48Name() {
        return d48Name;
    }

    public void setD48Name(String d48Name) {
        this.d48Name = d48Name;
    }

    public String getSexCode() {
        return sexCode;
    }

    public void setSexCode(String sexCode) {
        this.sexCode = sexCode;
    }

    public String getSexName() {
        return sexName;
    }

    public void setSexName(String sexName) {
        this.sexName = sexName;
    }

    public String getPhone() {
        return phone;
    }

    public void setPhone(String phone) {
        this.phone = phone;
    }

    public String getD07Code() {
        return d07Code;
    }

    public void setD07Code(String d07Code) {
        this.d07Code = d07Code;
    }

    public String getD07Name() {
        return d07Name;
    }

    public void setD07Name(String d07Name) {
        this.d07Name = d07Name;
    }

    public String getD09Code() {
        return d09Code;
    }

    public void setD09Code(String d09Code) {
        this.d09Code = d09Code;
    }

    public String getD09Name() {
        return d09Name;
    }

    public void setD09Name(String d09Name) {
        this.d09Name = d09Name;
    }

    public String getD08Code() {
        return d08Code;
    }

    public void setD08Code(String d08Code) {
        this.d08Code = d08Code;
    }

    public String getD08Name() {
        return d08Name;
    }

    public void setD08Name(String d08Name) {
        this.d08Name = d08Name;
    }

    public String getOrgCode() {
        return orgCode;
    }

    public void setOrgCode(String orgCode) {
        this.orgCode = orgCode;
    }

    public String getOrgName() {
        return orgName;
    }

    public void setOrgName(String orgName) {
        this.orgName = orgName;
    }

    public String getDataType() {
        return dataType;
    }

    public void setDataType(String dataType) {
        this.dataType = dataType;
    }

    public String getNginxKey() {
        return nginxKey;
    }

    public void setNginxKey(String nginxKey) {
        this.nginxKey = nginxKey;
    }

    @Override
    public String toString() {
        return "VcDevelopMemInfo{" +
                "code='" + code + '\'' +
                ", name='" + name + '\'' +
                ", idcard='" + idcard + '\'' +
                ", d06Code='" + d06Code + '\'' +
                ", d06Name='" + d06Name + '\'' +
                ", d48Code='" + d48Code + '\'' +
                ", d48Name='" + d48Name + '\'' +
                ", sexCode='" + sexCode + '\'' +
                ", sexName='" + sexName + '\'' +
                ", phone='" + phone + '\'' +
                ", d07Code='" + d07Code + '\'' +
                ", d07Name='" + d07Name + '\'' +
                ", d09Code='" + d09Code + '\'' +
                ", d09Name='" + d09Name + '\'' +
                ", d08Code='" + d08Code + '\'' +
                ", d08Name='" + d08Name + '\'' +
                ", orgCode='" + orgCode + '\'' +
                ", orgName='" + orgName + '\'' +
                ", dataType='" + dataType + '\'' +
                ", nginxKey='" + nginxKey + '\'' +
                '}';
    }
}
