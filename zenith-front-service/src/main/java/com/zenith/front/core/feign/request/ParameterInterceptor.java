package com.zenith.front.core.feign.request;

import cn.hutool.json.JSONUtil;
import com.zenith.front.core.constant.VcKeyConstant;
import com.zenith.front.core.feign.util.CryptoData;
import com.zenith.front.core.feign.util.CryptoUtil;
import feign.RequestInterceptor;
import feign.RequestTemplate;
import lombok.extern.slf4j.Slf4j;

/**
 * Feign 参数加密
 *
 * <AUTHOR>
 * @version V1.0
 * @date 2022/8/19 10:01
 */
@Slf4j
public class ParameterInterceptor implements RequestInterceptor {

    @Override
    public void apply(RequestTemplate requestTemplate) {
        byte[] body = requestTemplate.body();
        String content = new String(body);
        final String key = CryptoUtil.symmetricKey();
        // 数据加密
        final String encrypt = CryptoUtil.symmetricEncrypt(key, content);
        final String keyEncrypt = CryptoUtil.asymmetricEncrypt(VcKeyConstant.PUBLIC_STR, key);
        CryptoData cryptoData = new CryptoData();
        cryptoData.setData(encrypt);
        cryptoData.setKey(keyEncrypt);
        requestTemplate.body(JSONUtil.toJsonStr(cryptoData));
    }
}
