package com.zenith.front.core.service.report;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.zenith.front.api.report.IReportRuleConfigService;
import com.zenith.front.dao.mapper.report.ReportRuleConfigMapper;
import com.zenith.front.model.bean.ReportRuleConfig;
import org.springframework.stereotype.Service;

/**
 * <p>
 * excel 系统配置表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-07-10
 */
@Service
public class ReportRuleConfigServiceImpl extends ServiceImpl<ReportRuleConfigMapper, ReportRuleConfig> implements IReportRuleConfigService {

    @Override
    public ReportRuleConfig findReportCode(String reportCode) {
        return getOne(new LambdaQueryWrapper<ReportRuleConfig>().eq(ReportRuleConfig::getReportCode, reportCode));
    }
}
