package com.zenith.front.core.analysis.count.year2023;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.b1809.analysis.entity.PeggingPara;
import com.b1809.analysis.extend.ITableCount;
import com.b1809.analysis.plugin.elasticsearch.EsKit;
import com.jfinal.plugin.activerecord.Record;
import com.zenith.front.core.analysis.ext.condition.year2023.MemAllCondition2023;
import com.zenith.front.core.analysis.ext.condition.year2023.MemFlowStatisticsCondition2023;
import com.zenith.front.core.analysis.ext.condition.year2023.OrgAllCondition2023;
import org.jooq.Condition;
import org.jooq.Record1;
import org.jooq.SelectConditionStep;
import org.jooq.SelectHavingStep;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.stream.Collectors;

import static com.zenith.front.common.constant.DBConstant.DSL_CONTEXT;
import static org.jooq.impl.DSL.*;

/**
 * 流动党员情况（二）
 *
 * <AUTHOR>
 * @date 2022/11/25
 */
@Component
public class Html42Count2023 implements ITableCount {


    @Override
    public String getReportCode() {
        return "2023_42.html";
    }

    @Override
    public Map<String, Map<String, Number>> doCount(String reportCode, String orgCode, String orgLevelCode) throws Exception {
        return getCount(orgCode, orgLevelCode, "2023");
    }

    public Map<String, Map<String, Number>> getCount(String orgCode, String orgLevelCode, String tableYear) {
        String memFlowStatistics = StrUtil.isEmpty(tableYear) ? "mem_flow_all" : "mem_flow_all_" + tableYear;
        Map<String, Number> result = new LinkedHashMap<>();
        Html52Count2023.initTableCol(1, 19, 1, 9, result);
        MemFlowStatisticsCondition2023 memFlowCond = new MemFlowStatisticsCondition2023();
        SelectHavingStep<Record1<Object>> records1 = DSL_CONTEXT.select(field("d09_code,d20_code,d04_code,d16_code,out_place_code,flow_type_code,is_hold,has_inter,count(1) as total"))
                .from(table(name(memFlowStatistics)).as("mem_flow_all")).where(memFlowCond.create(orgCode, orgLevelCode).and("flow_out_in = '2'"))
                .groupBy(field("d09_code,d20_code,d04_code,d16_code,out_place_code,flow_type_code,is_hold,has_inter"));
        List<Record> records = EsKit.findBySql(records1.toString()).toRecord();
        for (Record record : records) {
            Html41Count2023.setStatisticsValue(record, result);

        }
        Map<String, Map<String, Number>> mapMap = new HashMap<>(10);
        mapMap.put("table0", result);
        return mapMap;
    }


    @Override
    public Map<String, Object> doPegging(PeggingPara peggingPara) throws Exception {
        MemFlowStatisticsCondition2023 memFlowCond = new MemFlowStatisticsCondition2023();
        Condition condition = memFlowCond.create(peggingPara.getOrgCode(), peggingPara.getOrgLevelCode()).and("flow_out_in = '2'")
                .and(Html41Count2023.getColCondition(peggingPara.getColIndex()))
                .and(Html41Count2023.getRowCondition(peggingPara.getRowIndex()));
        return Html52Count2023.getReportPageResult(peggingPara, memFlowCond.getTableName(), condition, memFlowCond.getLevelCodeField());
    }


    /**
     * 补充资料
     */
    @Override
    public Map<String, Number> doReplenishCount(String reportCode, String orgCode, String orgLevelCode) throws Exception {
        return getReplenishCount(orgCode, orgLevelCode, null);
    }

    public Map<String, Number> getReplenishCount(String orgCode, String orgLevelCode, String tableYear) {
        String ccpOrgAll = StrUtil.isEmpty(tableYear) ? "ccp_org_all" : "ccp_org_all_" + tableYear;

        Map<String, Number> result = new LinkedHashMap<>();
        Html52Count2023.initReplenishCol(1, 6, result);
        Condition orgListCond = new Html7Count202302().getOrgListCondition(orgCode, orgLevelCode);
        List<String> flowInOrgList = getBCZLFlowInOrgList(orgCode, orgLevelCode, tableYear);
        Number number = EsKit.findListBySql(DSL_CONTEXT.select(field("count(1) as total")).from(table(name(ccpOrgAll)).as("ccp_org_all"))
                .where(orgListCond.and(field(name("code")).in(flowInOrgList))).toString());
        //1．接收流动党员的党支部        个。
        Html52Count2023.setReplenishMapValue("1", (Integer) number, result);

        // 2．建立流动党员党组织      个，其中党委      个，总支部      个，支部      个，共接收流动党员      名。
        SelectConditionStep<Record1<Object>> records1 = DSL_CONTEXT.select(field("d01_code,is_flow,code")).from(table(name(ccpOrgAll)).as("ccp_org_all"))
                .where(orgListCond.and("is_flow =1 ").and(field(name("d01_code")).in("61", "911", "62", "921", "631", "632", "633", "634", "931", "932")));
        List<Record> records = EsKit.findBySql(records1.toString()).toRecord();
        Set<String> orgCodeList = new HashSet<>();
        for (Record record : records) {
            orgCodeList.add(record.getStr("code"));
            Html52Count2023.setReplenishMapValue("2", 1, result);
            String d01Code = record.getStr("d01_code");
            if (StrUtil.equalsAny(d01Code, "61", "911")) {
                Html52Count2023.setReplenishMapValue("3", 1, result);
            }
            if (StrUtil.equalsAny(d01Code, "62", "921")) {
                Html52Count2023.setReplenishMapValue("4", 1, result);
            }
            if (StrUtil.equalsAny(d01Code, "631", "632", "633", "634", "931", "932")) {
                Html52Count2023.setReplenishMapValue("5", 1, result);
            }
        }

//        String memFlowStatistics = StrUtil.isEmpty(tableYear) ? "mem_flow_all" : "mem_flow_all_" + tableYear;
//        Condition condition = new MemFlowStatisticsCondition2023().create(orgCode, orgLevelCode).and("flow_out_in = '2'");
        String memAll = StrUtil.isEmpty(tableYear) ? "ccp_mem_all" : "ccp_mem_all_" + tableYear;
        Condition condition = new Html7Count202302().getMemListCondition(orgCode, orgLevelCode);
        if (CollUtil.isNotEmpty(orgCodeList)) {
            condition = condition.and(field(name("org_code")).in(orgCodeList));
        } else {
            condition = condition.and("1=0");
        }
        Number number1 = EsKit.findListBySql(DSL_CONTEXT.select(field("count(1) as total")).from(table(name(memAll)).as("ccp_mem_all"))
                .where(condition).toString());
        Html52Count2023.setReplenishMapValue("6", (Integer) number1, result);
        return result;
    }


    /**
     * 补充资料反查
     */
    @Override
    public Map<String, Object> doReplenishPegging(PeggingPara peggingPara) throws Exception {
        String colIndex = peggingPara.getRowIndex();
        OrgAllCondition2023 orgAllCondition = new OrgAllCondition2023();
        Condition orgListCond = new Html7Count202302().getOrgListCondition(peggingPara.getOrgCode(), peggingPara.getOrgLevelCode());
        if (StrUtil.equalsAny(colIndex, "1")) {
            List<String> flowInOrgList = getBCZLFlowInOrgList(peggingPara.getOrgCode(), peggingPara.getOrgLevelCode(), null);
            Condition condition = orgListCond.and(field(name("code")).in(flowInOrgList));
            return Html52Count2023.getReportPageResult(peggingPara, orgAllCondition.getTableName(), condition, orgAllCondition.getLevelCodeField());
        }
        Condition orgCond = orgListCond.and("is_flow =1 ").and(field(name("d01_code")).in("61", "911", "62", "921", "631", "632", "633", "634", "931", "932"));
        if (StrUtil.equalsAny(colIndex, "2", "3", "4", "5")) {
            return Html52Count2023.getReportPageResult(peggingPara, orgAllCondition.getTableName(), orgCond, orgAllCondition.getLevelCodeField());
        }
        SelectConditionStep<Record1<Object>> records1 = DSL_CONTEXT.select(field("code")).from(table(name(orgAllCondition.getTableName()))).where(orgCond);
        List<Record> records = EsKit.findBySql(records1.toString()).toRecord();
        Set<String> orgCodeList = records.stream().map(record -> record.getStr("code")).collect(Collectors.toSet());
//        MemFlowStatisticsCondition2023 memFlowCond = new MemFlowStatisticsCondition2023();
//        Condition condition = new MemFlowStatisticsCondition2023().create(peggingPara.getOrgCode(), peggingPara.getOrgLevelCode()).and("flow_out_in = '2'");
        MemAllCondition2023 memAllCond = new MemAllCondition2023();
        Condition condition = new Html7Count202302().getMemListCondition(peggingPara.getOrgCode(), peggingPara.getOrgLevelCode());
        if (CollUtil.isNotEmpty(orgCodeList)) {
            condition = condition.and(field(name("org_code")).in(orgCodeList));
        } else {
            condition = condition.and("1=0");
        }
        return Html52Count2023.getReportPageResult(peggingPara, memAllCond.getTableName(), condition, memAllCond.getLevelCodeField());
    }


    public List<String> getBCZLFlowInOrgList(String orgCode, String orgLevelCode, String tableYear) {
        String memFlowStatistics = StrUtil.isEmpty(tableYear) ? "mem_flow_all" : "mem_flow_all_" + tableYear;
        SelectConditionStep<Record1<Object>> records1 = DSL_CONTEXT.select(field("mem_org_code"))
                .from(table(name(memFlowStatistics)).as("mem_flow_all")).where(new MemFlowStatisticsCondition2023().create(orgCode, orgLevelCode).and("flow_out_in = '2'"));
        List<Record> records = EsKit.findBySql(records1.toString()).toRecord();
        return records.stream().<String>map(record -> record.get("mem_org_code")).distinct().collect(Collectors.toList());
    }


}
