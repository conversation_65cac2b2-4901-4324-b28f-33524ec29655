package com.zenith.front.core.analysis.ext.condition.year2023;

import com.b1809.analysis.extend.GenSqlConditionFuc;
import org.jooq.Condition;
import org.jooq.Field;

import static org.jooq.impl.DSL.field;
import static org.jooq.impl.DSL.name;


/**
 * <AUTHOR>
 */
public class OrgPartyCongressCommitteeAllCondition202302 implements GenSqlConditionFuc {

    private static final String ORG_LEVEL_CODE = "position_org_code";

    @Override
    public String getTableName() {
        return "ccp_org_party_congress_committee_all_202302";
    }

    @Override
    public Condition create(String orgCode, String orgLevelCode) {
        return field(name( ORG_LEVEL_CODE), String.class).like(orgLevelCode + "%");
    }

    @Override
    public Field<?> getLevelCodeField() {
        return field(name( ORG_LEVEL_CODE), String.class);
    }
}
