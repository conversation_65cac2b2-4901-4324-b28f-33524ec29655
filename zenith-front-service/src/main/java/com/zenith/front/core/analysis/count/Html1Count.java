package com.zenith.front.core.analysis.count;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.thread.ThreadUtil;
import cn.hutool.core.util.StrUtil;
import com.b1809.analysis.entity.PeggingPara;
import com.b1809.analysis.extend.ITableCount;
import com.b1809.analysis.plugin.elasticsearch.EsKit;
import com.zenith.front.core.analysis.ext.condition.DevelopStepLogAllCondition;
import com.zenith.front.core.analysis.ext.condition.MemAllCondition;
import com.zenith.front.core.analysis.ext.condition.TransferStatisticsCondition;
import org.jooq.Condition;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.Map;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutorService;

import static com.zenith.front.common.constant.DBConstant.DSL_CONTEXT;
import static org.jooq.impl.DSL.*;

/**
 * 党员数量变化情况
 *
 * <AUTHOR>
 * @date 2022/6/7
 */
@Component
public class Html1Count implements ITableCount {

    public static final String TABLE_YEAR = "2025";
    public static final Long YEAR_STATISTICAL_START = DateUtil.parseDate("2025.01.01").getTime();
    public static final Long YEAR_STATISTICAL_END = DateUtil.parseDate("2025.12.31").getTime();

    /**
     * 上一次年统统计结束时间
     */
    public static final Long AFTER_NEW_START = DateUtil.parseDate("2025.02.01").getTime();
    /**
     * 发展时间-start
     */
    public static final Long TOPRE_JOIN_ORG_DATE_START = DateUtil.parseDate("2024.01.01").getTime();
    /**
     * 发展时间-end
     */
    public static final Long TOPRE_JOIN_ORG_DATE_END = DateUtil.parseDate("2024.12.31").getTime();

    // todo es中的表，每年需要变更这里
    final static DevelopStepLogAllCondition developStepLogAllCondition = new DevelopStepLogAllCondition();
    final static MemAllCondition memAllCondition = new MemAllCondition();
    final static TransferStatisticsCondition statisticsCondition = new TransferStatisticsCondition();

    @Override
    public String getReportCode() {
        return "1.html";
    }

    @Override
    public Map<String, Map<String, Number>> doCount(String reportCode, String orgCode, String orgLevelCode) throws Exception {
        Map<String, Number> result = new LinkedHashMap<>();
        Html53Count.initTableCol(1, 17, 1, 1, result);
        ExecutorService executor = ThreadUtil.newExecutor(20);
        Map<String, String> stringMap = new HashMap<String, String>(20) {
            private static final long serialVersionUID = -7658216719447680898L;

            {
                //3 发展党员
                Condition devLog = developStepLogAllCondition.create(orgCode, orgLevelCode).and("delete_time is null and d08_code ='3'").and(getEsReportDateSql("topre_join_org_date"));
                put("3", DSL_CONTEXT.select(field("count(1) as total")).from(table(name(developStepLogAllCondition.getTableName()))).where(devLog).toString());
                //4 重新入党
                put("4", DSL_CONTEXT.select(field("count(1) as total")).from(table(name(developStepLogAllCondition.getTableName()))).where(devLog.and("join_org_code = '12'")).toString());
                //5 恢复党籍
                Condition sql5 = noCondition().and("d08_code in ('1','2') and (delete_time is null or (delete_time is not null and transfer_out_type in('223','224')))")
                        .and(getEsReportDateSql("recover_party_date")).and(memAllCondition.create(orgCode, orgLevelCode));
                put("5", DSL_CONTEXT.select(field("count(1) as total")).from(table(name(memAllCondition.getTableName()))).where(sql5.and("d11_code like '2%'")).toString());
                //6 停止党籍后恢复党籍
                put("6", DSL_CONTEXT.select(field("count(1) as total")).from(table(name(memAllCondition.getTableName()))).where(sql5.and("d11_code = '21'")).toString());
                //7 转入组织关系
                Condition sql7 = statisticsCondition.create(orgCode, orgLevelCode).and(getEsReportDateSql("transfer_time") + " and delete_time is null and transfer_type='1'");
                put("7", DSL_CONTEXT.select(field("count(1) as total")).from(table(name(statisticsCondition.getTableName()))).where(sql7).toString());
                //8 整建制转入
                put("8", DSL_CONTEXT.select(field("count(1) as total")).from(table(name(statisticsCondition.getTableName()))).where(sql7.and("transfer_category = '2'")).toString());

                //10 出党
                Condition sql10 = noCondition().and("delete_time is not null and d08_code in ('1','2')").and(getEsReportDateSql("leave_org_date"))
                        .and(memAllCondition.create(orgCode, orgLevelCode));
                // todo: 2024-01-24 10出党最新,重新履行入党手续\排查整顿发展党员违规违纪问题工作中除名处置 也出数到出党中
                Condition sql10New = noCondition().and(memAllCondition.create(orgCode, orgLevelCode)).and("d08_code in ('1','2')").and(getEsReportDateSql("leave_org_date"))
                        .and("((delete_time is not null and d029_code in ('C15','C22','C23','C24','C25','C26','C27','C28','C29','C34','C35','C36','C37','C38', 'C241', 'C251', 'C341', 'C351' )) )");
                put("10", DSL_CONTEXT.select(field("count(1) as total")).from(table(name(memAllCondition.getTableName()))).where(sql10New).toString());
                //11 停止党籍
                put("11", DSL_CONTEXT.select(field("count(1) as total")).from(table(name(memAllCondition.getTableName()))).where(sql10.and("d12_code like '3%'")).toString());
                //12 死亡
                put("12", DSL_CONTEXT.select(field("count(1) as total")).from(table(name(memAllCondition.getTableName()))).where(sql10.and("d12_code like '1%'")).toString());
                //13 转出组织关系
                Condition sql13 = statisticsCondition.create(orgCode, orgLevelCode).and(getEsReportDateSql("transfer_time") + " and delete_time is null and transfer_type='2'");
                put("13", DSL_CONTEXT.select(field("count(1) as total")).from(table(name(statisticsCondition.getTableName()))).where(sql13).toString());
                //14 整建制转出
                put("14", DSL_CONTEXT.select(field("count(1) as total")).from(table(name(statisticsCondition.getTableName()))).where(sql13.and("transfer_category = '2'")).toString());
                //16 截至报告期末实有数
                put("16", DSL_CONTEXT.select(field("count(1) as total")).from(table(name(memAllCondition.getTableName()))).where("delete_time is null and d08_code in ('1','2') and mem_org_code like '" + orgLevelCode + "%'").toString());
            }
        };
        Map<String, CompletableFuture<Integer>> res = new HashMap<>();
        stringMap.forEach((key, value) -> res.put(key, StrUtil.isEmpty(value) ? CompletableFuture.supplyAsync(() -> 0, executor) : CompletableFuture.supplyAsync(() -> (int) EsKit.findListBySql(value), executor)));
        for (Map.Entry<String, CompletableFuture<Integer>> entry : res.entrySet()) {
            Html53Count.setTableMapValue(entry.getKey(), "1", entry.getValue().get(), result);
        }
        executor.shutdown();
        //17 实有数与应有数之差：补录、错误录入，成为预备党员时间是去年且录入时间是今年的
        Condition sql171 = noCondition().and(getAfterNewYearSql("create_time") + " or " + getAfterNewYearSql("replenish_input_date"))
                .and("d08_code in ('1','2') and ((delete_time is null and d135_code='1') " +
                        "or (d135_code='1' and (d12_code in ('4','6') or d12_code like '1%' or d12_code like '2%' or d12_code='3%') and " + getEsReportDateSql("leave_org_date") + ") " +
                        "or (d135_code='1' and transfer_out_type in('223','224')))").and(memAllCondition.create(orgCode, orgLevelCode));
        //成为预备党员时间小于2022年1月1日，录入时间在去年过年后（即统计期外的）
        Condition sql172 = noCondition().and("delete_time is null and d08_code in ('3') and " + getAfterNewYearSql("create_time"))
                .and(getLastYearSql("topre_join_org_date")).and(developStepLogAllCondition.create(orgCode, orgLevelCode));
        Condition sql173 = noCondition().and("d08_code in ('1','2') and (is_transfer is null or is_transfer!=1) and d12_code='4'").and(getAfterNewYearSql("leave_org_date"))
                .and(memAllCondition.create(orgCode, orgLevelCode));
        //本年度发展党员被删除了的
        Condition sql174 = developStepLogAllCondition.create(orgCode, orgLevelCode).and("d08_code ='3'").and(getEsReportDateSql("leave_org_date")).and(getEsReportDateSql("topre_join_org_date"));
        //-重新履行入党手续
        Condition sql175 = noCondition().and("d08_code in ('1','2') and (is_transfer is null or is_transfer!=1) and d12_code='6'").and(getEsReportDateSql("leave_org_date"))
                .and(memAllCondition.create(orgCode, orgLevelCode));
        //-排查整顿发展党员违规违纪问题工作中除名处置
        Condition sql176 = noCondition().and("d08_code in ('1','2') and (is_transfer is null or is_transfer!=1) and d12_code='7'").and(getEsReportDateSql("leave_org_date"))
                .and(memAllCondition.create(orgCode, orgLevelCode));
        int number1 = (int) EsKit.findListBySql(DSL_CONTEXT.select(field("count(1) as total")).from(table(name(memAllCondition.getTableName()))).where(sql171).toString());
        int number2 = (int) EsKit.findListBySql(DSL_CONTEXT.select(field("count(1) as total")).from(table(name(developStepLogAllCondition.getTableName()))).where(sql172).toString());
        int number3 = (int) EsKit.findListBySql(DSL_CONTEXT.select(field("count(1) as total")).from(table(name(memAllCondition.getTableName()))).where(sql173).toString());
        int number4 = (int) EsKit.findListBySql(DSL_CONTEXT.select(field("count(1) as total")).from(table(name(developStepLogAllCondition.getTableName()))).where(sql174).toString());
//        int number5 = (int) EsKit.findListBySql(DSL_CONTEXT.select(field("count(1) as total")).from(table(name(memAllCondition.getTableName()))).where(sql175).toString());
//        int number6 = (int) EsKit.findListBySql(DSL_CONTEXT.select(field("count(1) as total")).from(table(name(memAllCondition.getTableName()))).where(sql176).toString());
        System.out.println(number1 + " " + number2 + " " + number3 + " " + number4);
//        int num17 = number1 + number2 - number3 + number4 - number5 - number6;
        // todo: 2024-01-24 “重新履行入党手续”、“排查整顿违规违纪” 不出在17行
        int num17 = number1 + number2 - number3 + number4;
        int num2 = res.get("3").get() + res.get("5").get() + res.get("7").get();
        int num9 = res.get("10").get() + res.get("11").get() + res.get("12").get() + res.get("13").get();
        // 17栏=16栏-15栏
        // 15栏=1栏+2栏-9栏
        //1 上年底总数   16-17+9-2
        Html53Count.setTableMapValue("1", "1", res.get("16").get() - num17 + num9 - num2, result);
        Html53Count.setTableMapValue("2", "1", num2, result);
        Html53Count.setTableMapValue("9", "1", num9, result);
        //15 截至报告期末应有数
        Html53Count.setTableMapValue("15", "1", res.get("16").get() - num17, result);
        Html53Count.setTableMapValue("17", "1", num17, result);

        Map<String, Map<String, Number>> mapMap = new HashMap<>(10);
        mapMap.put("table0", result);
        return mapMap;
    }


    @Override
    public Map<String, Object> doPegging(PeggingPara peggingPara) throws Exception {
        if (StrUtil.equals(peggingPara.getType(), "2")) {
            return this.doReplenishPegging(peggingPara);
        }
        if (StrUtil.equalsAny(peggingPara.getRowIndex(), "3", "4", "174")) {
            Condition condition = developStepLogAllCondition.create(peggingPara.getOrgCode(), peggingPara.getOrgLevelCode()).and("d08_code ='3'").and(getEsReportDateSql("topre_join_org_date"));
            if (StrUtil.equals(peggingPara.getRowIndex(), "3")) {
                condition = condition.and("delete_time is null");
            } else if (StrUtil.equals(peggingPara.getRowIndex(), "4")) {
                condition = condition.and("delete_time is null and join_org_code = '12'");
            } else if (StrUtil.equals(peggingPara.getRowIndex(), "174")) {
                condition = condition.and(getEsReportDateSql("leave_org_date"));
            }
            return Html53Count.getReportPageResult(peggingPara, developStepLogAllCondition.getTableName(), condition, developStepLogAllCondition.getLevelCodeField());
        }

        // TODO 2023-12-28： 取数的标准应该是发展时间是去年 （2022.1.1 - 2022.12.31），录入系统时间是年统计期以后到今年底 （2023.2.28 - 2023.12.31）
        if (StrUtil.equals(peggingPara.getRowIndex(), "172")) {
            Condition condition = developStepLogAllCondition.create(peggingPara.getOrgCode(), peggingPara.getOrgLevelCode()).and("delete_time is null and d08_code ='3'")
                    .and(getAfterNewYearSql("create_time")).and(getLastYearSql("topre_join_org_date"));
            return Html53Count.getReportPageResult(peggingPara, developStepLogAllCondition.getTableName(), condition, developStepLogAllCondition.getLevelCodeField());
        }
        if (StrUtil.equalsAny(peggingPara.getRowIndex(), "7", "8", "13", "14")) {
            TransferStatisticsCondition transferCond = statisticsCondition;
            Condition condition = this.getRowCondition(peggingPara.getRowIndex()).and(getEsReportDateSql("transfer_time") + " and delete_time is null")
                    .and(transferCond.create(peggingPara.getOrgCode(), peggingPara.getOrgLevelCode()));
            return Html53Count.getReportPageResult(peggingPara, transferCond.getTableName(), condition, transferCond.getLevelCodeField());
        }
        Condition condition = this.getRowCondition(peggingPara.getRowIndex()).and(memAllCondition.create(peggingPara.getOrgCode(), peggingPara.getOrgLevelCode()));
        return Html53Count.getReportPageResult(peggingPara, memAllCondition.getTableName(), condition, memAllCondition.getLevelCodeField());
    }

    public Condition getRowCondition(String rowIndex) {
        Condition condition = noCondition();
        if (StrUtil.equalsAny(rowIndex, "1", "15", "17", "2", "9")) {
            return condition.and("1=0");
        }
        String memSql = "(delete_time is null or (delete_time is not null and transfer_out_type in('223','224'))) and d08_code in ('1','2') ";
        String historySql = "delete_time is not null and d08_code in ('1','2') and " + getEsReportDateSql("leave_org_date");
        if (StrUtil.equals(rowIndex, "2")) {
            condition = condition.and("(has_develop_year='1')" +
                    "or (delete_time is null and d08_code in ('1','2') and (is_transfer is null or is_transfer!=1) and d11_code like '2%' and " + getEsReportDateSql("recover_party_date") + ") " +
                    "or (transfer_in_type like '1%')");
        } else if (StrUtil.equals(rowIndex, "5")) {
            condition = condition.and(memSql).and("d11_code like '2%'").and(getEsReportDateSql("recover_party_date"));
        } else if (StrUtil.equals(rowIndex, "6")) {
            condition = condition.and(memSql).and("d11_code = '21'").and(getEsReportDateSql("recover_party_date"));
        } else if (StrUtil.equals(rowIndex, "7")) {
            condition = condition.and("transfer_type='1'");
        } else if (StrUtil.equals(rowIndex, "8")) {
            condition = condition.and("transfer_type='1' and transfer_category = '2'");
        } else if (StrUtil.equals(rowIndex, "9")) {
            condition = condition.and("(" + historySql + " and (d12_code like '1%' or d029_code like 'C%' or d12_code like '3%')) or transfer_out_type like '2%'");
        } else if (StrUtil.equals(rowIndex, "10")) {
            condition = condition.and(historySql).and("d029_code in ('C15','C22','C23','C24','C25','C26','C27','C28','C29','C34','C35','C36','C37','C38', 'C241', 'C251', 'C341', 'C351')");
        } else if (StrUtil.equals(rowIndex, "11")) {
            condition = condition.and(historySql).and("d12_code like '3%'");
        } else if (StrUtil.equals(rowIndex, "12")) {
            condition = condition.and(historySql).and("d12_code like '1%'");
        } else if (StrUtil.equals(rowIndex, "13")) {
            condition = condition.and("transfer_type='2'");
        } else if (StrUtil.equals(rowIndex, "14")) {
            condition = condition.and("transfer_type='2' and transfer_category = '2'");
        } else if (StrUtil.equals(rowIndex, "16")) {
            condition = condition.and("delete_time is null and d08_code in ('1','2')");
        } else
            //补录
            if (StrUtil.equals(rowIndex, "171")) {
                condition = noCondition().and(getAfterNewYearSql("create_time") + " or " + getAfterNewYearSql("replenish_input_date"))
                        .and("d08_code in ('1','2') and ((delete_time is null and d135_code='1') " +
                                "or (d135_code='1' and (d12_code in ('4','6') or d12_code like '1%' or d12_code like '2%' or d12_code='3%') and " + getEsReportDateSql("leave_org_date") + ") " +
                                "or (d135_code='1' and transfer_out_type in('223','224')))");
            } else if (StrUtil.equals(rowIndex, "173")) {
                condition = noCondition().and("d08_code in ('1','2') and (is_transfer is null or is_transfer!=1) and d12_code='4'").and(getAfterNewYearSql("leave_org_date"));
            } else if (StrUtil.equals(rowIndex, "175")) {
                condition = noCondition().and("d08_code in ('1','2') and (is_transfer is null or is_transfer!=1) and d12_code='6'").and(getEsReportDateSql("leave_org_date"));
            } else if (StrUtil.equals(rowIndex, "176")) {
                condition = noCondition().and("d08_code in ('1','2') and (is_transfer is null or is_transfer!=1) and d12_code='7'").and(getEsReportDateSql("leave_org_date"));
            }
        return condition;
    }

    @Override
    public Map<String, Number> doReplenishCount(String reportCode, String orgCode, String orgLevelCode) throws Exception {
        Map<String, Number> result = new LinkedHashMap<>();
        Html53Count.initReplenishCol(1, 1, result);
        Condition condition = statisticsCondition.create(orgCode, orgLevelCode).and(getEsReportDateSql("transfer_time") + " and delete_time is null and transfer_type='2'");
        Number number = EsKit.findListBySql(DSL_CONTEXT.select(field("count(1) as total")).from(table(name(statisticsCondition.getTableName()))).where(condition).toString());
        Html53Count.setReplenishMapValue("1", (Integer) number, result);
        return result;
    }

    @Override
    public Map<String, Object> doReplenishPegging(PeggingPara peggingPara) throws Exception {
        Condition condition = statisticsCondition.create(peggingPara.getOrgCode(), peggingPara.getOrgLevelCode()).and(getEsReportDateSql("transfer_time") + " and delete_time is null and transfer_type='2'");
        return Html53Count.getReportPageResult(peggingPara, statisticsCondition.getTableName(), condition, statisticsCondition.getLevelCodeField());
    }


    public String getAfterNewYearSql(String field) {
        return field + " between " + AFTER_NEW_START + " and " + YEAR_STATISTICAL_END;
    }

    public String getLastYearSql(String field) {
        return field + " between " + TOPRE_JOIN_ORG_DATE_START + " and " + TOPRE_JOIN_ORG_DATE_END;
    }

    public String getEsReportDateSql(String field) {
        return field + " between " + YEAR_STATISTICAL_START + " and " + YEAR_STATISTICAL_END;
    }

}
