package com.zenith.front.core.service.mem;

import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.zenith.front.api.mem.IMemTrainService;
import com.zenith.front.common.kit.StrKit;
import com.zenith.front.core.service.sync.SyncOrgOtherService;
import com.zenith.front.dao.mapper.mem.MemTrainMapper;
import com.zenith.front.model.dto.MemTrainDTO;
import com.zenith.front.model.dto.MemTrainPageDTO;
import com.zenith.front.model.message.OutMessage;
import com.zenith.front.model.message.Status;
import com.zenith.front.model.bean.MemTrain;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Objects;

/**
 * <p>
 * 党员培训（组织） 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-08-05
 */
@Service
public class MemTrainServiceImpl extends ServiceImpl<MemTrainMapper, MemTrain> implements IMemTrainService {

    @Resource
    private MemTrainMapper trainMapper;

    @Resource
    private IMemTrainService iMemTrainService;
    @Resource
    private SyncOrgOtherService syncOrgOtherService;


    @Override
    public OutMessage addOrUpdate(MemTrainDTO data) {
        MemTrain train = trainMapper.selectOne(new QueryWrapper<MemTrain>().lambda()
                .eq(MemTrain::getOrgCode, data.getOrgCode())
                .eq(MemTrain::getYear, data.getYear()).isNull(MemTrain::getDeleteTime));
        if (Objects.nonNull(train) && StrUtil.isEmpty(data.getCode())) {
            return new OutMessage<>(Status.YEAR_ALREADY_EXISTS);
        }
        if (Objects.nonNull(train) && !data.getCode().equals(train.getCode())) {
            return new OutMessage<>(Status.YEAR_ALREADY_EXISTS);
        }
        if (StrKit.isBlank(data.getCode())) {
            MemTrain memTrain = new MemTrain();
            BeanUtils.copyProperties(data, memTrain);
            memTrain.setCreateTime(new Date());
            int insert = trainMapper.insert(memTrain);
            if (insert > 0) {
                syncOrgOtherService.syncOrgMemTrain(data.getOrgCode());
            }
        } else {
            MemTrain memTrain = trainMapper.selectById(data.getCode());
            if (Objects.nonNull(memTrain)) {
                BeanUtils.copyProperties(data, memTrain);
                memTrain.setUpdateTime(new Date());
                this.removeById(data.getCode());
                int i = trainMapper.insert(memTrain);
                if (i > 0) {
                    syncOrgOtherService.syncOrgMemTrain(data.getOrgCode());
                }
            }

        }
        return new OutMessage<>(Status.SUCCESS);
    }

    @Override
    public OutMessage findTrainByCode(String code) {
        MemTrain memTrain = trainMapper.selectById(code);
        return new OutMessage<>(Status.SUCCESS, memTrain);
    }

    @Override
    public OutMessage listTrainByCode(MemTrainPageDTO dto) {
        LambdaQueryWrapper<MemTrain> wrapper = new QueryWrapper<MemTrain>().lambda()
                .eq(MemTrain::getOrgCode, dto.getOrgCode()).isNull(MemTrain::getDeleteTime).orderByDesc(MemTrain::getYear);
        Page<MemTrain> memTrains = trainMapper.selectPage(new Page<>(dto.getPageNum(), dto.getPageSize()), wrapper);
        return new OutMessage<>(Status.SUCCESS, memTrains);
    }

    /**
     * 删除培训信息
     *
     * @param code
     * @return
     */
    @Override
    public OutMessage deleteTrainByCode(String code) {
        MemTrain memTrain = trainMapper.selectById(code);
        memTrain.setDeleteTime(new Date());
        int i = trainMapper.updateById(memTrain);
        if (i > 0) {
            syncOrgOtherService.syncOrgMemTrain(memTrain.getOrgCode());
        }
        return new OutMessage<>(i > 0 ? Status.SUCCESS : Status.FAIL);
    }

    @Override
    public OutMessage saveBatchTrain(List<MemTrainDTO> data) {
        if (data.size() == 1 && StrKit.notBlank(data.get(0).getCode())) {
            MemTrain memTrain = trainMapper.selectById(data.get(0).getCode());
            if (memTrain == null) {
                return new OutMessage<>(Status.OBJECT_DBISNOTALIVE);
            }
            BeanUtils.copyProperties(data.get(0), memTrain);
            memTrain.setUpdateTime(new Date());
            trainMapper.updateById(memTrain);
        } else {
            List<MemTrain> memTrains = new ArrayList<>(data.size());
            for (MemTrainDTO datum : data) {
                MemTrain memTrain = new MemTrain();
                BeanUtils.copyProperties(datum, memTrain);
                memTrain.setCreateTime(new Date());
                memTrains.add(memTrain);
            }
            iMemTrainService.saveBatch(memTrains);
        }
        return new OutMessage<>(Status.SUCCESS);
    }

    @Override
    public List<MemTrain> findTrainByOrgCode(String orgCode) {
        return list(
                new QueryWrapper<MemTrain>().lambda()
                        .eq(MemTrain::getOrgCode, orgCode)
                        .isNull(MemTrain::getDeleteTime)
                        .orderByDesc(MemTrain::getYear)
        );
    }
}
