package com.zenith.front.core.analysis.count.history;

import cn.hutool.core.util.StrUtil;
import com.b1809.analysis.entity.PeggingPara;
import com.b1809.analysis.extend.ITableCount;
import com.b1809.analysis.plugin.elasticsearch.EsKit;
import com.jfinal.plugin.activerecord.Record;
import com.zenith.front.core.analysis.ext.condition.year2021.OrgAllCondition2021;
import com.zenith.front.core.analysis.ext.condition.year2021.UnitAllCondition2021;
import org.jooq.Condition;
import org.jooq.Record1;
import org.jooq.SelectConditionStep;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.*;
import java.util.regex.Pattern;

import static com.zenith.front.common.constant.DBConstant.DSL_CONTEXT;
import static org.jooq.impl.DSL.*;

/**
 * 专题调查表一 农村党建相关情况
 *
 * <AUTHOR>
 * @date 2022/1/7
 */
@Component
public class Html43Count2021 implements ITableCount {

    @Override
    public String getReportCode() {
        return "2021_43.html";
    }

    @Override
    public Map<String, Map<String, Number>> doCount(String reportCode, String orgCode, String orgLevelCode) throws Exception {
        return getCount(orgCode, orgLevelCode, "2021");
    }

    public Map<String, Map<String, Number>> getCount(String orgCode, String orgLevelCode, String tableYear) throws Exception {
        String ccpUnitAll = StrUtil.isEmpty(tableYear) ? "ccp_unit_all" : "ccp_unit_all_" + tableYear;
        String ccpOrgAll = StrUtil.isEmpty(tableYear) ? "ccp_org_all" : "ccp_org_all_" + tableYear;
        Map<String, Number> result = new LinkedHashMap<>();
        Html48CountHistory.initTableCol(1, 1, 1, 19, result);
        SelectConditionStep<Record1<Object>> records1 = DSL_CONTEXT.select(field("first_secretary_select,first_secretary_code,secretary_training_num,has_thousand,has_bundled,promoted_num,adjusted_num,operating_expenses,village_per,secretary_salary,space_area,new_expand_area"))
                .from(table(name(ccpUnitAll)).as("ccp_unit_all")).where(new Html6CountHistory().getUnitListCondition(orgCode, orgLevelCode)).and(field(name("d04_code"), String.class).eq("923"));
        List<Record> records = EsKit.findBySql(records1.toString()).toRecord();
        BigDecimal operatingExpenses = BigDecimal.ZERO;
        BigDecimal villagePer = BigDecimal.ZERO;
        BigDecimal secretarySalary = BigDecimal.ZERO;
        int index1 = 0, index2 = 0, index3 = 0;
        for (Record record : records) {
            //行政村总数
            Html48CountHistory.setTableMapValue("1", "1", 1, result);
            //累计选派第一书记
            int firstSecretarySelect = 0;
            String select = record.getStr("first_secretary_select");
            if (StrUtil.isNotEmpty(select)) {
                boolean matches = Pattern.compile("^[0-9]*$").matcher(select).matches();
                firstSecretarySelect = matches ? Integer.parseInt(record.getStr("first_secretary_select")) : 1;
            }
            Html48CountHistory.setTableMapValue("1", "2", firstSecretarySelect, result);
            //现任第一书记
            Html48CountHistory.setTableMapValue("1", "3", StrUtil.isNotEmpty(record.getStr("first_secretary_code")) ? 1 : 0, result);
            //本年各级培训第一书记（人次）
            Html48CountHistory.setTableMapValue("1", "4", record.getInt("secretary_training_num"), result);
            //为第一书记安排不低于1万元工作经费的村
            Html48CountHistory.setTableMapValue("1", "5", record.getInt("has_thousand"), result);
            //派出单位落实责任、项目、资金捆绑的村
            Html48CountHistory.setTableMapValue("1", "6", record.getInt("has_bundled"), result);
            //提拔使用或晋级的第一书记
            Html48CountHistory.setTableMapValue("1", "7", record.getInt("promoted_num"), result);
            //因工作不胜任召回调整的第一书记
            Html48CountHistory.setTableMapValue("1", "8", record.getInt("adjusted_num"), result);
            BigDecimal expenses = record.getBigDecimal("operating_expenses");
            if (Objects.nonNull(expenses)) {
                operatingExpenses = operatingExpenses.add(expenses);
                index1++;
            }
            BigDecimal per = record.getBigDecimal("village_per");
            if (Objects.nonNull(per)) {
                villagePer = villagePer.add(per);
                index2++;
            }
            BigDecimal salary = record.getBigDecimal("secretary_salary");
            if (Objects.nonNull(salary)) {
                secretarySalary = secretarySalary.add(salary);
                index3++;
            }

            //16 暂无活动场所的行政村
            if (Objects.isNull(record.getInt("space_area")) || record.getInt("space_area") == 0) {
                Html48CountHistory.setTableMapValue("1", "16", 1, result);
            }
            //17 活动场所面积200㎡以上的行政村
            if (Objects.nonNull(record.getInt("space_area")) && record.getInt("space_area") > 200) {
                Html48CountHistory.setTableMapValue("1", "17", 1, result);
            }
            //18 本年新建或改扩建活动场所数量
            Html48CountHistory.setTableMapValue("1", "18", record.getInt("new_expand_area"), result);
            //19 未完成“五小”建设的乡镇
            Html48CountHistory.setTableMapValue("1", "19", 0, result);
        }
        //平均每村运转经费（万元∕年）
        Html48CountHistory.setTableMapValue("1", "9", Html43CountHistory.getAvgCount(operatingExpenses, index1), result);
        //平均每村办公经费（万元∕年）
        Html48CountHistory.setTableMapValue("1", "10", Html43CountHistory.getAvgCount(villagePer, index2), result);
        //村党组织书记平均报酬（万元∕年）
        Html48CountHistory.setTableMapValue("1", "11", Html43CountHistory.getAvgCount(secretarySalary, index3), result);

        SelectConditionStep<Record1<Object>> orgR = DSL_CONTEXT.select(field("d01_code")).from(table(name(ccpOrgAll)).as("ccp_org_all")).where(new Html6CountHistory().getOrgListCondition(orgCode, orgLevelCode));
        List<Record> records2 = EsKit.findBySql(orgR.toString()).toRecord();
        long count = records2.stream().filter(e -> StrUtil.startWith(e.getStr("d01_code"), "14")).count();
        //12 村干部基本报酬和村级组织办公经费合计低于11万元的县（市、区、旗）
        Html48CountHistory.setTableMapValue("1", "12", Math.toIntExact(count), result);
        //13 落实正常离任村干部生活补贴的县（市、区、旗）
        Html48CountHistory.setTableMapValue("1", "13", Math.toIntExact(count), result);
        //14 落实农村公共服务运行维护支出或服务群众专项经费的县（市、区、旗）
        Html48CountHistory.setTableMapValue("1", "14", Math.toIntExact(count), result);
        //15 落实村民小组长误工补贴的县（市、区、旗）
        Html48CountHistory.setTableMapValue("1", "15", Math.toIntExact(count), result);

        Map<String, Map<String, Number>> mapMap = new HashMap<>();
        mapMap.put("table0", result);
        return mapMap;
    }

    @Override
    public Map<String, Object> doPegging(PeggingPara peggingPara) throws Exception {
        String colIndex = peggingPara.getColIndex();
        if (StrUtil.equalsAny(colIndex, "12", "13", "14", "15")) {
            OrgAllCondition2021 cond = new OrgAllCondition2021();
            Condition condition = noCondition().and(new Html6CountHistory().getOrgListCondition(peggingPara.getOrgCode(), peggingPara.getOrgLevelCode()).toString().replace("ccp_org_all", cond.getTableName()))
                    .and("d01_code like 14%");
            return Html48CountHistory.getReportPageResult(peggingPara, cond.getTableName(), condition, cond.getLevelCodeField());
        }
        UnitAllCondition2021 cond = new UnitAllCondition2021();
        Condition condition = noCondition().and(new Html6CountHistory().getUnitListCondition(peggingPara.getOrgCode(), peggingPara.getOrgLevelCode()).toString().replace("ccp_unit_all", cond.getTableName()))
                .and(field(name("d04_code"), String.class).eq("923")).and(this.getTableRowCondition(colIndex));
        return Html48CountHistory.getReportPageResult(peggingPara, cond.getTableName(), condition, cond.getLevelCodeField());
    }

    Condition getTableRowCondition(String colIndex) {
        Condition condition = noCondition();
        if ("2".equals(colIndex)) {
            condition = condition.and("first_secretary_select is not null and first_secretary_select!=''");
        } else if ("3".equals(colIndex)) {
            condition = condition.and("first_secretary_code is not null and first_secretary_code !=''");
        } else if ("4".equals(colIndex)) {
            condition = condition.and("secretary_training_num is not null and secretary_training_num >0");
        } else if ("5".equals(colIndex)) {
            condition = condition.and("has_thousand is not null and has_thousand >0");
        } else if ("6".equals(colIndex)) {
            condition = condition.and("has_bundled is not null and has_bundled >0");
        } else if ("7".equals(colIndex)) {
            condition = condition.and("promoted_num is not null and promoted_num >0");
        } else if ("8".equals(colIndex)) {
            condition = condition.and("adjusted_num is not null and adjusted_num >0");
        } else if ("9".equals(colIndex)) {
            condition = condition.and("operating_expenses is not null and operating_expenses >0");
        } else if ("10".equals(colIndex)) {
            condition = condition.and("village_per is not null and village_per >0");
        } else if ("11".equals(colIndex)) {
            condition = condition.and("secretary_salary is not null and secretary_salary >0");
        } else if ("16".equals(colIndex)) {
            condition = condition.and("space_area is null or space_area >0");
        } else if ("17".equals(colIndex)) {
            condition = condition.and("space_area is not null and space_area >200");
        } else if ("18".equals(colIndex)) {
            condition = condition.and("new_expand_area is null and new_expand_area >0");
        } else {
            condition = condition.and("1=0");
        }
        return condition;
    }


    @Override
    public Map<String, Number> doReplenishCount(String reportCode, String orgCode, String orgLevelCode) throws Exception {
        return new Html43CountHistory().getReplenishCount(orgCode, orgLevelCode, "2021", false);
    }

    @Override
    public Map<String, Object> doReplenishPegging(PeggingPara peggingPara) throws Exception {
        if (StrUtil.equalsAny(peggingPara.getRowIndex(), "1", "2")) {
            UnitAllCondition2021 unitAllCondition = new UnitAllCondition2021();
            Condition condition = noCondition().and(new Html6CountHistory().getUnitListCondition(peggingPara.getOrgCode(), peggingPara.getOrgLevelCode()).toString().replace("ccp_unit_all", unitAllCondition.getTableName()))
                    .and(field(name("d04_code"), String.class).eq("923")).and(new Html43CountHistory().getRowCondition(peggingPara.getRowIndex()));

            return Html48CountHistory.getReportPageResult(peggingPara, unitAllCondition.getTableName(), condition, unitAllCondition.getLevelCodeField());
        }
        OrgAllCondition2021 orgAllCondition = new OrgAllCondition2021();
        Condition condition = noCondition().and(new Html6CountHistory().getOrgListCondition(peggingPara.getOrgCode(), peggingPara.getOrgLevelCode()).toString().replace("ccp_org_all", orgAllCondition.getTableName()))
                .and(new Html43CountHistory().getRow2Condition(peggingPara.getRowIndex()));
        return Html48CountHistory.getReportPageResult(peggingPara, orgAllCondition.getTableName(), condition, orgAllCondition.getLevelCodeField());
    }


}
