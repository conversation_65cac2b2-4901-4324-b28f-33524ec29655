package com.zenith.front.core.service.mem;

import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.zenith.front.api.mem.IBulletinService;
import com.zenith.front.api.mem.IMemDevelopService;
import com.zenith.front.api.mem.IMemRewardService;
import com.zenith.front.api.mem.IMemService;
import com.zenith.front.api.org.IOrgAllService;
import com.zenith.front.api.org.IOrgRewardService;
import com.zenith.front.api.unit.IUnitAllService;
import com.zenith.front.common.constant.DictConstant;
import com.zenith.front.common.kit.NumberUtil;
import com.zenith.front.core.kit.ChartUtil;
import com.zenith.front.model.custom.Record;
import com.zenith.front.dao.mapper.mem.MemAllMapper;
import com.zenith.front.dao.mapper.mem.MemMapper;
import com.zenith.front.model.message.OutMessage;
import com.zenith.front.model.message.Status;
import com.zenith.front.model.bean.Mem;
import com.zenith.front.model.vo.MemAllVO;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

/**
 * 党委党内统计公报
 *
 * <AUTHOR>
 * @date 2021/7/21
 */
@Service
public class IBulletinServiceImpl extends ServiceImpl<MemMapper, Mem> implements IBulletinService {

    @Resource
    private MemAllMapper memAllMapper;
    @Resource
    private IOrgAllService orgAllDao;
    @Resource
    private IMemService memDao;
    @Resource
    private IMemDevelopService memDevelopDao;
    @Resource
    private IOrgRewardService orgRewardDao;
    @Resource
    private IMemRewardService memRewardDao;
    @Resource
    private IUnitAllService unitAllDao;

    /**
     * 获取基础信息
     *
     * @param orgCode
     * @return
     */
    @Override
    public OutMessage getBasicInfo(String orgCode) {
        Record basicInfo = this.getBasicMemInfo(new Record(), orgCode);
        this.getBasicOrgInfo(basicInfo, orgCode);
        return new OutMessage<>(Status.SUCCESS, basicInfo);
    }

    /**
     * 获取党员总数,正式党员,预备党员
     *
     * @param record
     * @return
     */
    private Record getBasicMemInfo(Record record, String orgCode) {
        // 无数据
        List<String> list = new ArrayList<String>() {
            private static final long serialVersionUID = 6730401612166803800L;

            {
            add("党员总数");
            add("正式党员");
            add("预备党员");
        }};
        // 查询数据
        List<MemAllVO> memAllVOS = memAllMapper.getMemTotal(orgCode, null, null);
        List<Record> recordList = new ArrayList<>();
        if (CollectionUtil.isNotEmpty(memAllVOS)) {
            for (MemAllVO memAllVO : memAllVOS) {
                recordList.add(new Record().set("d08Code", memAllVO.getD08Code()).set("count", memAllVO.getCount()));
            }
        }
        if (CollectionUtil.isEmpty(recordList)) {
            ChartUtil.getListWithNoData("d08Name", list);
        }
        // 获取总人数
        Long totalCount = ChartUtil.getTotalFromList(recordList, "count");
        if (totalCount == 0) {
            ChartUtil.getListWithNoData("d08Name", list);
        }
        // 转换
        List<Record> percentRecordList = ChartUtil.getRecordList(recordList, "d08Code", "d08Name", DictConstant.DICT_D08);
        percentRecordList.add(new Record().set("d08Name", list.get(0))
                .set("count", totalCount));

        percentRecordList.forEach(record1 -> {
            if ("党员总数".equals(record1.getStr("d08Name"))) {
                record.set("memTotal", record1.get("count"));
            } else if ("正式党员".equals(record1.getStr("d08Name"))) {
                record.set("formalMem", record1.get("count"));
            } else {
                record.set("prepareMem", record1.get("count"));
            }
        });
        return record;
    }

    /**
     * 组织信息,基层组织,部门工作党委,机关党委(中共各级工作委员会),基层党委,总支部,支部
     *
     * @param record
     * @param orgCode
     * @return
     */
    private Record getBasicOrgInfo(Record record, String orgCode) {
        Record record1 = orgAllDao.getBasicOrgInfo(orgCode);
        if (record1 == null) {
            record1 = new Record();
        }
        record.set("cpcTotal", record1.getInt("cpcTotal") == null ? 0 : record1.getInt("cpcTotal"));
        record.set("departmentTotal", record1.getInt("departmentTotal") == null ? 0 : record1.getInt("departmentTotal"));
        record.set("basicOrgTotal", record1.getInt("basicOrgTotal") == null ? 0 : record1.getInt("basicOrgTotal"));
        record.set("basicPartyCommitteeTotal", record1.getInt("basicPartyCommitteeTotal") == null ? 0 : record1.getInt("basicPartyCommitteeTotal"));
        record.set("basicGeneralBranchTotal", record1.getInt("basicGeneralBranchTotal") == null ? 0 : record1.getInt("basicGeneralBranchTotal"));
        record.set("basicBranchTotal", record1.getInt("basicBranchTotal") == null ? 0 : record1.getInt("basicBranchTotal"));
        record.remove("basicUniteBranchTotal");
        return record;
    }

    /**
     * 获取党员信息
     *
     * @param orgCode
     * @return
     */
    @Override
    public OutMessage getMemInfo(String orgCode) {
        Record record = new Record();
        record.set("memSen", this.getMemSexAndEudAndNation(orgCode));
        record.set("memAgeDistribute", this.getMemAgeDistribute(orgCode));
        record.set("memJobSort", this.getMemJobSort(orgCode));
        return new OutMessage<>(Status.SUCCESS, record);
    }

    /**
     * 获取党员的性别,民族,学历
     *
     * @param orgCode
     * @return
     */
    private Record getMemSexAndEudAndNation(String orgCode) {
        Record record = memDao.getMemSexAndEudAndNation(orgCode);
        if (record == null) {
            record = new Record();
        }
        record.set("womanTotal", record.getInt("womanTotal") == null ? 0 : record.getInt("womanTotal"));
        record.set("minorityTotal", record.getInt("minorityTotal") == null ? 0 : record.getInt("minorityTotal"));
        record.set("collegeTotal", record.getInt("collegeTotal") == null ? 0 : record.getInt("collegeTotal"));

        record.set("womanPercent", NumberUtil.getPercentStr(record.getLong("womanTotal"), record.getLong("count")));
        record.set("minorityPercent", NumberUtil.getPercentStr(record.getLong("minorityTotal"), record.getLong("count")));
        record.set("collegePercent", NumberUtil.getPercentStr(record.getLong("collegeTotal"), record.getLong("count")));
        return record;
    }

    /**
     * 获取党员年龄分布
     *
     * @param orgCode
     * @return
     */
    private Record getMemAgeDistribute(String orgCode) {
        Record record = memDao.getMemAgeDistribute(orgCode);
        if (record == null) {
            record = new Record();
        }
        Long count = record.getLong("count") == null ? 0L : record.getLong("count");
        long under30 = record.getLong("30岁及以下的党员") == null ? 0L : record.getLong("30岁及以下的党员");
        record.set("under30", under30);
        record.set("under30Percent", NumberUtil.getPercentInt(under30, count));
        record.remove("30岁及以下的党员");

        long range3135 = record.getLong("31岁至35岁党员") == null ? 0L : record.getLong("31岁至35岁党员");
        record.set("range3135", range3135);
        record.set("range3135Percent", NumberUtil.getPercentInt(range3135, count));
        record.remove("31岁至35岁党员");

        long range3640 = record.getLong("36岁至40岁党员") == null ? 0L : record.getLong("36岁至40岁党员");
        record.set("range3640", range3640);
        record.set("range3640Percent", NumberUtil.getPercentInt(range3640, count));
        record.remove("36岁至40岁党员");

        long range4145 = record.getLong("41岁至45岁党员") == null ? 0L : record.getLong("41岁至45岁党员");
        record.set("range4145", range4145);
        record.set("range4145Percent", NumberUtil.getPercentInt(range4145, count));
        record.remove("41岁至45岁党员");

        long range4650 = record.getLong("46岁至50岁党员") == null ? 0L : record.getLong("46岁至50岁党员");
        record.set("range4650", range4650);
        record.set("range4650Percent", NumberUtil.getPercentInt(range4650, count));
        record.remove("46岁至50岁党员");

        long range5155 = record.getLong("51岁至55岁党员") == null ? 0L : record.getLong("51岁至55岁党员");
        record.set("range5155", range5155);
        record.set("range5155Percent", NumberUtil.getPercentInt(range5155, count));
        record.remove("51岁至55岁党员");

        long range5660 = record.getLong("56岁至60岁党员") == null ? 0L : record.getLong("56岁至60岁党员");
        record.set("range5660", range5660);
        record.set("range5660Percent", NumberUtil.getPercentInt(range5660, count));
        record.remove("56岁至60岁党员");

        long above61 = record.getLong("61岁及以上党员") == null ? 0L : record.getLong("61岁及以上党员");
        record.set("above61", above61);
        record.set("above61Percent", NumberUtil.getPercentInt(above61, count));
        record.remove("61岁及以上党员");

        return record;
    }

    /**
     * 获取党员职业分类
     *
     * @param orgCode
     * @return
     */
    private Record getMemJobSort(String orgCode) {
        Record record = memDao.getMemJobSort(orgCode);
        if (record == null) {
            record = new Record();
        }
        record.set("worker", record.getInt("worker") == null ? 0 : record.getInt("worker"));
        record.set("aahf", record.getInt("aahf") == null ? 0 : record.getInt("aahf"));
        record.set("technicist", record.getInt("technicist") == null ? 0 : record.getInt("technicist"));
        record.set("manager", record.getInt("manager") == null ? 0 : record.getInt("manager"));
        record.set("officials", record.getInt("officials") == null ? 0 : record.getInt("officials"));
        record.set("student", record.getInt("student") == null ? 0 : record.getInt("student"));
        record.set("others", record.getInt("others") == null ? 0 : record.getInt("others"));
        record.set("retirement", record.getInt("retirement") == null ? 0 : record.getInt("retirement"));
        return record;
    }

    /**
     * 获取发展党员情况
     *
     * @param orgCode
     * @return
     */
    @Override
    public OutMessage getMemDevelopInfo(String orgCode) {
        Record record = new Record();
        // 发展党员基本信息
        record.set("developType", this.getMemDevelopType(orgCode));
        record.set("developAsen", this.getMemDevelopAgeSexNativeAndEud(orgCode));
        record.set("developJob", this.getMemDevelopJobInfo(orgCode));
        return new OutMessage<>(Status.SUCCESS, record);
    }

    /**
     * 获取发展党员总数,入党申请人,积极分子,发展对象
     *
     * @return
     */
    private Record getMemDevelopType(String orgCode) {
        Record record = memDevelopDao.getMemDevelopType(orgCode);
        if (record == null) {
            record = new Record();
        }
        record.set("developObject", record.getLong("developObject") == null ? 0L : record.getLong("developObject"));
        record.set("activeMem", record.getLong("activeMem") == null ? 0L : record.getLong("activeMem"));
        record.set("applicant", record.getLong("applicant") == null ? 0L : record.getLong("applicant"));
        return record;
    }

    /**
     * 发展党员性别,民族,年龄,学历
     *
     * @return
     */
    private Record getMemDevelopAgeSexNativeAndEud(String orgCode) {
        Record record = memDevelopDao.getMemDevelopAgeSexNativeAndEud(orgCode);
        if (record == null) {
            record = new Record();
        }
        Long total = record.getLong("count");
        long womanTotal = record.getLong("womanTotal") == null ? 0L : record.getLong("womanTotal");
        record.set("womanTotal", womanTotal);
        record.set("womanPercent", NumberUtil.getPercentStr(womanTotal, total));

        long minorityTotal = record.getLong("minorityTotal") == null ? 0L : record.getLong("minorityTotal");
        record.set("minorityTotal", minorityTotal);
        record.set("minorityPercent", NumberUtil.getPercentStr(minorityTotal, total));

        long underThirtyTotal = record.getLong("underThirtyTotal") == null ? 0L : record.getLong("underThirtyTotal");
        record.set("underThirtyTotal", underThirtyTotal);
        record.set("underThirtyPercent", NumberUtil.getPercentStr(underThirtyTotal, total));

        long collegeTotal = record.getLong("collegeTotal") == null ? 0L : record.getLong("collegeTotal");
        record.set("collegeTotal", collegeTotal);
        record.set("collegePercent", NumberUtil.getPercentStr(collegeTotal, total));
        return record;
    }

    /**
     * 发展党员职业情况
     *
     * @return
     */
    private Record getMemDevelopJobInfo(String orgCode) {
        Record record = memDevelopDao.getMemDevelopJobInfo(orgCode);
        if (record == null) {
            record = new Record();
        }
        record.set("worker", record.getLong("worker") == null ? 0L : record.getLong("worker"));
        record.set("workerBoor", record.getLong("workerBoor") == null ? 0L : record.getLong("workerBoor"));
        record.set("technicist", record.getLong("technicist") == null ? 0L : record.getLong("technicist"));
        record.set("manager", record.getLong("manager") == null ? 0L : record.getLong("manager"));
        record.set("aahf", record.getLong("aahf") == null ? 0L : record.getLong("aahf"));
        record.set("aahfWorker", record.getLong("aahfWorker") == null ? 0L : record.getLong("aahfWorker"));
        record.set("officials", record.getLong("officials") == null ? 0L : record.getLong("officials"));
        record.set("student", record.getLong("student") == null ? 0L : record.getLong("student"));
        record.set("others", record.getLong("others") == null ? 0L : record.getLong("others"));
        record.set("generationLine", record.getLong("generationLine") == null ? 0L : record.getLong("generationLine"));
        return record;
    }

    /**
     * 党内表彰
     *
     * @param orgCode
     * @return
     */
    @Override
    public OutMessage getPartyCommend(String orgCode) {
        Record record = new Record();
        // 先进基层党组织
        Integer orgCommendTotal = orgRewardDao.getOrgCommend(orgCode);
        record.set("orgCommendTotal", orgCommendTotal == null ? 0 : orgCommendTotal);

        Record record1 = memRewardDao.getMemCommend(orgCode);
        if (record1 == null) {
            record1 = new Record();
        }

        record.set("excellentMem", record1.getInt("excellentMem") == null ? 0 : record1.getInt("excellentMem"));
        record.set("partyWorkers", record1.getInt("partyWorkers") == null ? 0 : record1.getInt("partyWorkers"));

        return new OutMessage<>(Status.SUCCESS, record);
    }

    /**
     * 申请入党情况
     *
     * @param orgCode
     * @return
     */
    @Override
    public OutMessage getApplyJoinParty(String orgCode) {
        Record record = this.getMemDevelopType(orgCode);
        record.set("total", record.getInt("applicant") + record.getInt("activeMem"));
        record.remove("developObject");
        record.remove("count");
        return new OutMessage<>(Status.SUCCESS, record);
    }

    /**
     * 党组织情况
     *
     * @param orgCode
     * @return
     */
    @Override
    public OutMessage getOrgInfo(String orgCode) {
        Record record = new Record();
        // 1.党的基层组织（党委,党总支,党支部,党小组）
        record.set("orgBasicInfo", this.getOrgBasicInfo(orgCode));
        record.set("unitOrgInfo", this.gerUnitOrgInfo(orgCode));
        return new OutMessage<>(Status.SUCCESS, record);
    }

    /**
     * 党的基层组织（党委,党总支,党支部,临时党支部）
     *
     * @param orgCode
     * @return
     */
    private Record getOrgBasicInfo(String orgCode) {
        Record record = orgAllDao.getBasicOrgInfo(orgCode);
        if (record == null) {
            record = new Record();
        }
        record.set("basicPartyCommitteeTotal", record.getInt("basicPartyCommitteeTotal") == null ? 0 : record.getInt("basicPartyCommitteeTotal"));
        record.set("basicGeneralBranchTotal", record.getInt("basicGeneralBranchTotal") == null ? 0 : record.getInt("basicGeneralBranchTotal"));
        record.set("basicBranchTotal", record.getInt("basicBranchTotal") == null ? 0 : record.getInt("basicBranchTotal"));
        record.set("basicBranchTotal", record.getInt("basicBranchTotal") == null ? 0 : record.getInt("basicUniteBranchTotal"));
        record.remove("cpcTotal");
        record.remove("departmentTotal");
        record.remove("basicOrgTotal");
        return record;
    }

    /**
     * 建立党组织
     * 城市街道
     * 乡镇
     * 社区（居委会）
     * 建制村
     * 机关
     * 事业单位
     * 企业
     * 公有制企业
     * 非公有制企业
     * 社会组织
     *
     * @param orgCode
     * @return
     */
    private Record gerUnitOrgInfo(String orgCode) {
        // 建立党组织的
        Record linkedRecord = unitAllDao.gerUnitLinkedOrgInfo(orgCode);
        if (linkedRecord == null) {
            linkedRecord = new Record();
        }
        // 所有
        Record unitOrgReord = unitAllDao.gerUnitOrgInfo(orgCode);
        if (unitOrgReord == null) {
            unitOrgReord = new Record();
        }
        // cityStreet villagesAndTowns communityCommittees constructedVillage organ institution enterprise publiclyOwnedEnterprise nonPublicEnterprise organism
        int cityStreet = linkedRecord.getInt("cityStreet") == null ? 0 : linkedRecord.getInt("cityStreet");
        linkedRecord.set("cityStreet", cityStreet);

        int villagesAndTowns = linkedRecord.getInt("villagesAndTowns") == null ? 0 : linkedRecord.getInt("villagesAndTowns");
        linkedRecord.set("villagesAndTowns", villagesAndTowns);

        int communityCommittees = linkedRecord.getInt("communityCommittees") == null ? 0 : linkedRecord.getInt("communityCommittees");
        linkedRecord.set("communityCommittees", communityCommittees);

        int constructedVillage = linkedRecord.getInt("constructedVillage") == null ? 0 : linkedRecord.getInt("constructedVillage");
        linkedRecord.set("constructedVillage", constructedVillage);
        linkedRecord.set("cvccTotal", cityStreet + villagesAndTowns + communityCommittees + constructedVillage);

        int organ = linkedRecord.getInt("organ") == null ? 0 : linkedRecord.getInt("organ");
        linkedRecord.set("organ", organ);
        int organTotal = unitOrgReord.getInt("organ") == null ? 0 : unitOrgReord.getInt("organ");
        linkedRecord.set("organPercent", NumberUtil.getPercentStr((long) organ, (long) organTotal));

        int institution = linkedRecord.getInt("institution") == null ? 0 : linkedRecord.getInt("institution");
        linkedRecord.set("institution", institution);
        linkedRecord.set("oiTotal", organ + institution);
        int institutionTotal = unitOrgReord.getInt("institution") == null ? 0 : unitOrgReord.getInt("institution");
        linkedRecord.set("institutionPercent", NumberUtil.getPercentStr((long) institution, (long) institutionTotal));

        int enterprise = linkedRecord.getInt("enterprise") == null ? 0 : linkedRecord.getInt("enterprise");
        linkedRecord.set("enterprise", enterprise);

        int publiclyOwnedEnterprise = linkedRecord.getInt("publiclyOwnedEnterprise") == null ? 0 : linkedRecord.getInt("publiclyOwnedEnterprise");
        linkedRecord.set("publiclyOwnedEnterprise", publiclyOwnedEnterprise);
        int publiclyOwnedEnterpriseTotal = unitOrgReord.getInt("publiclyOwnedEnterprise") == null ? 0 : unitOrgReord.getInt("publiclyOwnedEnterprise");
        linkedRecord.set("publiclyOwnedEnterprisePercent", NumberUtil.getPercentStr((long) publiclyOwnedEnterprise, (long) publiclyOwnedEnterpriseTotal));

        int nonPublicEnterprise = linkedRecord.getInt("nonPublicEnterprise") == null ? 0 : linkedRecord.getInt("nonPublicEnterprise");
        linkedRecord.set("nonPublicEnterprise", nonPublicEnterprise);
        int nonPublicEnterpriseTotal = unitOrgReord.getInt("nonPublicEnterprise") == null ? 0 : unitOrgReord.getInt("nonPublicEnterprise");
        linkedRecord.set("nonPublicEnterprisePercent", NumberUtil.getPercentStr((long) nonPublicEnterprise, (long) nonPublicEnterpriseTotal));

        int organism = linkedRecord.getInt("organism") == null ? 0 : linkedRecord.getInt("organism");
        linkedRecord.set("organism", organism);
        int organismTotal = unitOrgReord.getInt("organism") == null ? 0 : unitOrgReord.getInt("organism");
        linkedRecord.set("organismPercent", NumberUtil.getPercentStr((long) organism, (long) organismTotal));

        return linkedRecord;
    }


}
