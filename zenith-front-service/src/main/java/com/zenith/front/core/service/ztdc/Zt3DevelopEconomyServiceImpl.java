package com.zenith.front.core.service.ztdc;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.zenith.front.api.ztdc.IZt3DevelopEconomyService;
import com.zenith.front.common.kit.StrKit;
import com.zenith.front.dao.mapper.ztdc.Zt3DevelopEconomyMapper;
import com.zenith.front.model.dto.Zt3DevelopEconomyDTO;
import com.zenith.front.model.message.OutMessage;
import com.zenith.front.model.message.Status;
import com.zenith.front.model.bean.Zt3DevelopEconomy;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.Objects;

/**
 * <p>
 * 推动发展壮大村级集体经济情况 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-08-13
 */
@Service
public class Zt3DevelopEconomyServiceImpl extends ServiceImpl<Zt3DevelopEconomyMapper, Zt3DevelopEconomy> implements IZt3DevelopEconomyService {

    @Resource
    private Zt3DevelopEconomyMapper economyMapper;

    @Override
    public OutMessage addThree(Zt3DevelopEconomyDTO data) {
        Zt3DevelopEconomy optimize = economyMapper.selectOne(new QueryWrapper<Zt3DevelopEconomy>().lambda().eq(Zt3DevelopEconomy::getUnitCode, data.getUnitCode()).isNull(Zt3DevelopEconomy::getDeleteTime));
        int result;
        if (Objects.nonNull(optimize)) {
            String code = optimize.getCode();
            BeanUtils.copyProperties(data, optimize);
            optimize.setUpdateTime(new Date());
            optimize.setCode(code);
            result = economyMapper.updateById(optimize);
        } else {
            Zt3DevelopEconomy zt3DevelopEconomy = new Zt3DevelopEconomy();
            BeanUtils.copyProperties(data, zt3DevelopEconomy);
            zt3DevelopEconomy.setCode(StrKit.getRandomUUID());
            zt3DevelopEconomy.setCreateTime(new Date());
            result = economyMapper.insert(zt3DevelopEconomy);
        }
        return new OutMessage<>(result > 0 ? Status.SUCCESS : Status.FAIL);
    }
}
