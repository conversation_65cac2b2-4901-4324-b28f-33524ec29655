package com.zenith.front.core.service.org;


import cn.afterturn.easypoi.excel.ExcelExportUtil;
import cn.afterturn.easypoi.excel.entity.ExportParams;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.zenith.front.api.org.IOrgLifePartService;
import com.zenith.front.api.org.IOrgLifeService;
import com.zenith.front.api.org.IOrgService;
import com.zenith.front.common.exception.ServiceException;
import com.zenith.front.core.kit.ExcelUtil;
import com.zenith.front.dao.mapper.org.OrgLifeMapper;
import com.zenith.front.dao.mapper.org.OrgMapper;
import com.zenith.front.model.dto.FileDto;
import com.zenith.front.model.dto.OrgLifeDTO;
import com.zenith.front.model.dto.OrgLifeExcelDTO;
import com.zenith.front.model.dto.OrgLifeListDTO;
import com.zenith.front.model.message.OutMessage;
import com.zenith.front.model.message.Status;
import com.zenith.front.model.bean.Org;
import com.zenith.front.model.bean.OrgLife;
import com.zenith.front.model.bean.User;
import com.zenith.front.model.vo.OrgLifeVO;
import org.apache.poi.ss.usermodel.Workbook;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <p>
 * 组织生活服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-12-15
 */
@Service
public class OrgLifeServiceImpl extends ServiceImpl<OrgLifeMapper, OrgLife> implements IOrgLifeService {

    @Resource
    private IOrgService iOrgService;
    @Resource
    private IOrgLifePartService iOrgLifePartService;
    @Resource
    private OrgContactWorkServiceImpl service;
    @Resource
    private OrgMapper orgMapper;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public OutMessage<?> addOrgLife(OrgLifeDTO data, User user, String basePath) {
        OrgLife orgLife = new OrgLife();
        BeanUtils.copyProperties(data, orgLife);
        if (ObjectUtil.isNotNull(data.getOrgGroupCodeList())){
            orgLife.setOrgGropCode(data.getOrgGroupCodeList().toString().replace("[","").replace("]",""));
        }
        FileDto fileDto = data.getFilePath();
        if (ObjectUtil.isNotEmpty(fileDto)) {
            String url = fileDto.getUrl();
            if (StringUtils.hasText(url)) {
                String toUrl = service.moveFile(basePath, url, "org_life");
                fileDto.setUrl(toUrl);
                orgLife.setFilePath(JSON.toJSONString(fileDto));
            }
        }
        life(data);
        orgLife.setCreateTime(new Date());
        orgLife.setCreateUser(user.getAccount());
        orgLife.setManageOrgCode(user.getOrgId());
        boolean b = this.save(orgLife);
        if (b) {
            iOrgLifePartService.updateOrgLifePart(orgLife);
        }
        return new OutMessage<>(b ? Status.SUCCESS : Status.FAIL);
    }

    private void life(OrgLifeDTO data) {
        if (StrUtil.equals(data.getD01Code(), "61") || (StrUtil.equals(data.getD01Code(), "62"))) {
            String[] split = data.getD158Code().split(",");
            if (split.length>1){
            if (!StrUtil.equals(data.getD158Code(), "1,4")) {
                if (!StrUtil.equals(data.getD158Code(), "4,1")) {
                    throw new ServiceException(500, "党委或总支层不能添加除党员大会和党课以外的活动");
                }
            }
            if (!StrUtil.equals(data.getD158Code(), "4,1")) {
                if (!StrUtil.equals(data.getD158Code(), "1,4")) {
                    throw new ServiceException(500, "党委或总支层不能添加除党员大会和党课以外的活动");
                }
            }
        }else {
                if (!StrUtil.equals(data.getD158Code(), "1")) {
                    if (!StrUtil.equals(data.getD158Code(), "4")) {
                        throw new ServiceException(500, "党委或总支层不能添加除党员大会和党课以外的活动");
                    }
                }
                if (!StrUtil.equals(data.getD158Code(), "4")) {
                    if (!StrUtil.equals(data.getD158Code(), "1")) {
                        throw new ServiceException(500, "党委或总支层不能添加除党员大会和党课以外的活动");
                    }
                }
            }}
//        if (data.getD158Code().contains("2") || data.getD158Code().contains("3")) {
//            String[] split = data.getD158Code().split(",");
//            if (split.length > 1) {
//                throw new ServiceException(500, "党支部委员会会议或党小组会一次只能选中一个");
//            }
//        }
                if (data.getD158Code().contains("2") && data.getD158Code().contains("3")) {
//            String[] split = data.getD158Code().split(",");
//            if (split.length > 1) {
                throw new ServiceException(500, "党支部委员会会议或党小组会一次只能选中一个");
//            }
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public OutMessage<?> updateOrgLife(OrgLifeDTO data, String currManOrgCode, String basePath) {
        OrgLife life = this.getById(data.getCode());
        if (Objects.isNull(life)) {
            return new OutMessage<>(Status.NOT_NULL_ERROR);
        }
        boolean hasCurrManOrg = hasCurrManOrg(currManOrgCode, life.getManageOrgCode());
        if (!hasCurrManOrg) {
            return new OutMessage<>(Status.PERMISSION_DENIED);
        }
        OrgLife orgLife = new OrgLife();
        BeanUtils.copyProperties(data, orgLife);
        if (ObjectUtil.isNotNull(data.getOrgGroupCodeList())){
            orgLife.setOrgGropCode( data.getOrgGroupCodeList().toString().replace("[", "").replace("]", "").replace(" ", ""));
        }
        if (StrUtil.equals(data.getHasMoreOrgJoin(), "0")) {
            orgLife.setJoinOrgCodeList(null);
            orgLife.setJoinOrgCodeListName(null);
            orgLife.setJoinOrgLevelCodeList(null);
            if (StrUtil.isEmpty(data.getLeaveMemCodeList())){
                orgLife.setLeaveMemCodeList(null);
                orgLife.setLeaveMemCodeListName(null);
            }
        }
        if (!data.getD158Code().contains("4")){
            orgLife.setSpeaker(null);
        }
        life(data);
        FileDto fileDto = data.getFilePath();
        if (ObjectUtil.isNotEmpty(fileDto)) {
            String url = fileDto.getUrl();
            if (StringUtils.hasText(url)) {
                String toUrl = service.moveFile(basePath, url, "org_life");
                fileDto.setUrl(toUrl);
            }
            orgLife.setFilePath(JSON.toJSONString(fileDto));
        } else {
            orgLife.setFilePath("");
        }
        orgLife.setUpdateTime(new Date());
        boolean b = this.updateById(orgLife);
        if (b) {
            iOrgLifePartService.updateOrgLifePart(orgLife);
        }
        return new OutMessage<>(b ? Status.SUCCESS : Status.FAIL);
    }


    /**
     * 判断操作权限
     */
    public boolean hasCurrManOrg(String currManOrgCode, String orgCode) {
        Org orgByCode = iOrgService.findOrgByCode(orgCode);
        if (Objects.nonNull(orgByCode)) {
            return StrUtil.startWith(currManOrgCode, orgByCode.getOrgCode());
        }
        return false;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public OutMessage<?> deleteOrgLife(String code, String currManOrgCode) {
        OrgLife life = this.getById(code);
        if (Objects.isNull(life)) {
            return new OutMessage<>(Status.NOT_NULL_ERROR);
        }
        boolean hasCurrManOrg = hasCurrManOrg(currManOrgCode, life.getManageOrgCode());
        if (!hasCurrManOrg) {
            return new OutMessage<>(Status.PERMISSION_DENIED);
        }
        life.setDeleteTime(new Date());
        boolean b = this.updateById(life);
        if (b) {
            iOrgLifePartService.updateOrgLifePart(life);
        }
        return new OutMessage<>(b ? Status.SUCCESS : Status.FAIL);
    }

    @Override
    public OutMessage<?> orgLifeList(OrgLifeListDTO data) {
        Page<OrgLifeVO> page = new Page<>(data.getPageNum(), data.getPageSize());
        Page<OrgLife> orgLifePage = getBaseMapper().getOrgLifeList(new Page<>(data.getPageNum(), data.getPageSize()), data);
        //显示组织名称
        List<String> collect = orgLifePage.getRecords().stream().map(t -> t.getOrgLevelCode()).collect(Collectors.toList());
        Map<String, String> orgNameByOrgCodeList = orgMapper.getOrgNameByOrgCodeList(collect).stream().collect(Collectors.toMap(Org::getOrgCode, Org::getName));

        List<OrgLifeVO> orgLifeVOList = orgLifePage.getRecords().stream().map(t -> {
            OrgLifeVO vo = new OrgLifeVO();
            BeanUtils.copyProperties(t, vo);
            //添加参与组织名称
            if (orgNameByOrgCodeList.containsKey(t.getOrgLevelCode())){
                vo.setOrgName(orgNameByOrgCodeList.get(t.getOrgLevelCode()));
                if (StrUtil.equals("0",t.getHasMoreOrgJoin())||StrUtil.isBlank(t.getHasMoreOrgJoin())){
                    vo.setJoinOrgCodeListName(orgNameByOrgCodeList.get(t.getOrgLevelCode()));
                }
            }

            //查询党小组信息
            List<String> strings = new ArrayList<>();
            String orgGropCode = t.getOrgGropCode();
            if (ObjectUtil.isNotNull(orgGropCode)){
                for (String s : orgGropCode.replace("[", "").replace("]", "").split(",")) {
                    strings.add(s);
                }
                vo.setOrgGroupCodeList(strings);
            }

            if (StrUtil.isNotEmpty(t.getFilePath())) {
                vo.setFilePath(JSON.parseObject(t.getFilePath(), FileDto.class));
            }
            return vo;
        }).collect(Collectors.toList());
        BeanUtils.copyProperties(orgLifePage, page);
        page.setRecords(orgLifeVOList);
        return new OutMessage<>(Status.SUCCESS, page);
    }

    @Override
    public OutMessage<?> findOrgLifeById(String code) {
        OrgLife orgLife = this.getById(code);
        OrgLifeVO vo = new OrgLifeVO();
        BeanUtils.copyProperties(orgLife, vo);
        if (ObjectUtil.isNotNull( orgLife.getOrgGropCode())){
        List<String> strings = new ArrayList<>();
            for (String s : orgLife.getOrgGropCode().replace("[", "").replace("]", "").replace(" ","").split(",")) {
                strings.add(s);
            }
            vo.setOrgGroupCodeList(strings);
        }

        vo.setFilePath(JSON.parseObject(orgLife.getFilePath(), FileDto.class));
        return new OutMessage<>(Status.SUCCESS, vo);
    }

    @Override
    public OutMessage<?> outExportXlsx(OrgLifeListDTO data) throws Exception {
        data.setPageNum(1);
        data.setPageSize(10000000);
        OutMessage<?> outMessage = this.orgLifeList(data);
        Page<OrgLifeVO> page = (Page<OrgLifeVO>) outMessage.getData();
        int index = 1;
        List<OrgLifeExcelDTO> result = new ArrayList<>();
        DateFormat dateFormat = new SimpleDateFormat("yyyy/MM/dd");
        for (OrgLifeVO orgLifeVO : page.getRecords()) {
            OrgLifeExcelDTO excel = new OrgLifeExcelDTO();
            excel.setOrgName(orgLifeVO.getOrgName());
            excel.setJoinOrgCodeListName(orgLifeVO.getJoinOrgCodeListName());
            excel.setActivityName(orgLifeVO.getActivityName());
            excel.setActivityRecord(orgLifeVO.getActivityRecord());
            excel.setD158Name(orgLifeVO.getD158Name());
            excel.setActivityTime(dateFormat.format(orgLifeVO.getActivityTime()));
            excel.setIndex(index);
            result.add(excel);
            index++;
        }
        Workbook workbook = ExcelExportUtil.exportExcel(new ExportParams(null, ""), OrgLifeExcelDTO.class, result);
        Map<String, String> map = ExcelUtil.exportFile(workbook, "");
        return new OutMessage<>(Status.SUCCESS, map);
    }

    @Override
    public List<OrgLife> getListLikeOrgCode(Date startDate, Date endDate, String orgCode) {
        return baseMapper.getListLikeOrgCode(startDate, endDate, orgCode);
    }
}
