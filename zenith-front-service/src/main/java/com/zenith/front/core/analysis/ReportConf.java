package com.zenith.front.core.analysis;

import com.zenith.front.core.constant.ReportEnum;
import com.zenith.front.core.constant.ReportFilterEnum;

import java.util.LinkedHashMap;

/**
 * <AUTHOR>
 */
public class ReportConf {

    /**
     * 读取报表配置
     *
     * @param report 报表类型
     * @return
     */
    public static LinkedHashMap<String, String> read(ReportEnum report) {
        return report.apply();
    }

    /**
     * 读取报表配置
     *
     * @param report 报表类型
     * @return
     */
    public static LinkedHashMap<String, ReportMapProcessing> readFilter(ReportFilterEnum report) {
        return report.apply();
    }
}
