package com.zenith.front.core.service.dataexchange;

import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.zenith.front.api.dataexchange.IDataExchangeService;
import com.zenith.front.common.constant.CommonConstant;
import com.zenith.front.model.bean.DataExchange;
import com.zenith.front.dao.mapper.dataexchange.DataExchangeMapper;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.util.Objects;

/**
 * <AUTHOR>
 * @description 针对表【ccp_data_exchange】的数据库操作Service实现
 * @createDate 2021-12-21 15:13:04
 */
@Service
public class DataExchangeServiceImpl extends ServiceImpl<DataExchangeMapper, DataExchange> implements IDataExchangeService {

    @Resource
    DataExchangeMapper dataExchangeMapper;

    @Override
    public String lastUpdateTime() {
        //先查询表是否存在（仅内网有这张表）
        TableName annotation = DataExchange.class.getAnnotation(TableName.class);
        if (Objects.isNull(annotation)) {
            return CommonConstant.EMPTY_STRING;
        }
        //获取实体类上表名
        String tableName = annotation.value();
        if (StringUtils.isEmpty(tableName)) {
            return CommonConstant.EMPTY_STRING;
        }
        //表是否存在 1 是 0 否
        Integer flag = dataExchangeMapper.existTable(tableName);
        if (flag == 0) {
            return CommonConstant.EMPTY_STRING;
        }
        LambdaQueryWrapper<DataExchange> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.select(DataExchange::getCreateTime).orderByDesc(DataExchange::getCreateTime).last(" LIMIT 1 ");
        DataExchange dataExchange = getOne(queryWrapper);
        if (Objects.isNull(dataExchange)) {
            return CommonConstant.EMPTY_STRING;
        }
        return DateUtil.format(dataExchange.getCreateTime(), DatePattern.CHINESE_DATE_FORMATTER);
    }
}
