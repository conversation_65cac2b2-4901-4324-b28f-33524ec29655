package com.zenith.front.core.constant;

import cn.hutool.core.util.StrUtil;

import java.util.AbstractMap;
import java.util.Arrays;
import java.util.LinkedHashMap;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * <AUTHOR>
 */
public enum ReportEnum {

    ccp_org_reward {
        @Override
        public LinkedHashMap<String, String> apply() {
            return Stream.of(
                            new AbstractMap.SimpleEntry<>("d42_code", "处分类型"),
                            new AbstractMap.SimpleEntry<>("name", "党组织名称"),
                            new AbstractMap.SimpleEntry<>("org_code", "党组织code")
                    )
                    .collect(Collectors.toMap(AbstractMap.SimpleEntry::getKey, AbstractMap.SimpleEntry::getValue, (o, n) -> o, LinkedHashMap::new));
        }
    },
    ccp_develop_excellent_mem {
        @Override
        public LinkedHashMap<String, String> apply() {
            return Stream.of(
                            new AbstractMap.SimpleEntry<>("name", "姓名"),
                            new AbstractMap.SimpleEntry<>("sex_name", "性别"),
                            new AbstractMap.SimpleEntry<>("d06_name", "民族"),
                            new AbstractMap.SimpleEntry<>("birthday", "出生年月"),
                            new AbstractMap.SimpleEntry<>("apply_date", "入党时间"),
                            new AbstractMap.SimpleEntry<>("d09_name", "工作单位及职务")
                    )
                    .collect(Collectors.toMap(AbstractMap.SimpleEntry::getKey, AbstractMap.SimpleEntry::getValue, (o, n) -> o, LinkedHashMap::new));
        }
    },
    ccp_mem_all {
        @Override
        public LinkedHashMap<String, String> apply() {
            return Stream.of(
                            new AbstractMap.SimpleEntry<>("code", "code"),
                            new AbstractMap.SimpleEntry<>("name", "姓名"),
                            new AbstractMap.SimpleEntry<>("sex_name", "性别"),
                            new AbstractMap.SimpleEntry<>("idcard", "身份证号码"),
                            new AbstractMap.SimpleEntry<>("age", "年龄"),
                            new AbstractMap.SimpleEntry<>("birthday", "出生日期"),
                            new AbstractMap.SimpleEntry<>("phone", "电话"),
                            new AbstractMap.SimpleEntry<>("join_org_date", "预备党员日期"),
                            new AbstractMap.SimpleEntry<>("full_member_date", "转为正式党员日期"),
                            new AbstractMap.SimpleEntry<>("unit_name", "单位名称"),
                            new AbstractMap.SimpleEntry<>("d04_name", "单位类别"),
                            new AbstractMap.SimpleEntry<>("d06_name", "民族"),
                            new AbstractMap.SimpleEntry<>("d07_name", "学历"),
                            new AbstractMap.SimpleEntry<>("d08_name", "党员类型"),
                            new AbstractMap.SimpleEntry<>("d09_name", "工作岗位"),
                            new AbstractMap.SimpleEntry<>("d12_name", "出党类别"),
                            new AbstractMap.SimpleEntry<>("d20_name", "新社会阶层"),
                            new AbstractMap.SimpleEntry<>("d28_name", "转正类型"),
                            new AbstractMap.SimpleEntry<>("d48_name", "籍贯"),
                            new AbstractMap.SimpleEntry<>("d21_name", "一线情况"),
                            new AbstractMap.SimpleEntry<>("org_code", "组织名称"),
                            new AbstractMap.SimpleEntry<>("mem_org_code", "组织层级码"),
                            new AbstractMap.SimpleEntry<>("d194_name", "国民经济服务行业"),
                            new AbstractMap.SimpleEntry<>("d195_name", "生产性服务行业"))
                    .collect(Collectors.toMap(AbstractMap.SimpleEntry::getKey, AbstractMap.SimpleEntry::getValue, (o, n) -> o, LinkedHashMap::new));
        }
    },

    ccp_mem_develop_all {
        @Override
        public LinkedHashMap<String, String> apply() {
            return Stream.of(
                            new AbstractMap.SimpleEntry<>("code", "code"),
                            new AbstractMap.SimpleEntry<>("name", "姓名"),
                            new AbstractMap.SimpleEntry<>("sex_name", "性别"),
                            new AbstractMap.SimpleEntry<>("idcard", "身份证号码"),
                            new AbstractMap.SimpleEntry<>("age", "年龄"),
                            new AbstractMap.SimpleEntry<>("phone", "电话"),
                            new AbstractMap.SimpleEntry<>("unit_name", "单位名称"),
                            new AbstractMap.SimpleEntry<>("d04_name", "单位类别"),
                            new AbstractMap.SimpleEntry<>("d06_name", "民族"),
                            new AbstractMap.SimpleEntry<>("d07_name", "学历"),
                            new AbstractMap.SimpleEntry<>("d08_name", "党员类型"),
                            new AbstractMap.SimpleEntry<>("d09_name", "工作岗位"),
                            new AbstractMap.SimpleEntry<>("d20_name", "新社会阶层"),
                            new AbstractMap.SimpleEntry<>("d21_name", "一线情况"),
                            new AbstractMap.SimpleEntry<>("d48_name", "籍贯"),
                            new AbstractMap.SimpleEntry<>("org_code", "组织名称"),
                            new AbstractMap.SimpleEntry<>("develop_org_code", "组织层级码"))
                    .collect(Collectors.toMap(AbstractMap.SimpleEntry::getKey, AbstractMap.SimpleEntry::getValue, (o, n) -> o, LinkedHashMap::new));
        }
    },

    ccp_develop_step_log_all {
        @Override
        public LinkedHashMap<String, String> apply() {
            return Stream.of(
                            new AbstractMap.SimpleEntry<>("code", "code"),
                            new AbstractMap.SimpleEntry<>("name", "姓名"),
                            new AbstractMap.SimpleEntry<>("age", "年龄"),
                            new AbstractMap.SimpleEntry<>("sex_name", "性别"),
                            new AbstractMap.SimpleEntry<>("idcard", "发展党员身份证"),
                            new AbstractMap.SimpleEntry<>("d06_name", "民族"),
                            new AbstractMap.SimpleEntry<>("d07_name", "学历"),
                            new AbstractMap.SimpleEntry<>("d08_name", "党员类型"),
                            new AbstractMap.SimpleEntry<>("d09_name", "工作岗位"),
                            new AbstractMap.SimpleEntry<>("d11_name", "进入支部类型"),
                            new AbstractMap.SimpleEntry<>("d21_name", "一线情况"),
                            new AbstractMap.SimpleEntry<>("d28_name", "审批结果"),
                            new AbstractMap.SimpleEntry<>("d48_name", "籍贯"),
                            new AbstractMap.SimpleEntry<>("join_org_name", "加入共产党类型"),
                            new AbstractMap.SimpleEntry<>("topart_turn_party_date", "召开支委会日期(成为正式党员日期)"),
                            new AbstractMap.SimpleEntry<>("extend_prepar_date", "延长预备期到的时间"),
                            new AbstractMap.SimpleEntry<>("active_date", "召开支委会日期(成为积极分子日期)"),
                            new AbstractMap.SimpleEntry<>("object_date", "召开支委会日期(成为发展对象时间)"),
                            new AbstractMap.SimpleEntry<>("org_name", "组织名称"),
                            new AbstractMap.SimpleEntry<>("log_org_code", "组织层级码"))
                    .collect(Collectors.toMap(AbstractMap.SimpleEntry::getKey, AbstractMap.SimpleEntry::getValue, (o, n) -> o, LinkedHashMap::new));
        }
    },

    ccp_mem_flow_all {
        @Override
        public LinkedHashMap<String, String> apply() {
            return Stream.of(
                            new AbstractMap.SimpleEntry<>("code", "code"),
                            new AbstractMap.SimpleEntry<>("mem_name", "姓名"),
                            new AbstractMap.SimpleEntry<>("gender_name", "性别"),
                            new AbstractMap.SimpleEntry<>("nation_name", "民族"),
                            new AbstractMap.SimpleEntry<>("idcard", "身份证号码"),
                            new AbstractMap.SimpleEntry<>("birthday", "出生日期"),
                            new AbstractMap.SimpleEntry<>("d04_name", "组织单位类别"),
                            new AbstractMap.SimpleEntry<>("d09_code", "工作岗位"),
                            new AbstractMap.SimpleEntry<>("d20_code", "新社会阶层"),
                            new AbstractMap.SimpleEntry<>("is_prov_out_name", "流动类型"),
                            new AbstractMap.SimpleEntry<>("outflow_date", "流出或流入日期"),
                            new AbstractMap.SimpleEntry<>("outflow_type_name", "流出或流入类型代码"),
                            new AbstractMap.SimpleEntry<>("outflow_org_name", "流出或流入地党组织"),
                            new AbstractMap.SimpleEntry<>("outflow_reason_name", "流出或流入原因"),
                            new AbstractMap.SimpleEntry<>("outflow_unit_type_name", "流出或流入单位类型"),
                            new AbstractMap.SimpleEntry<>("outflow_area_name", "流出或流入地"),
                            new AbstractMap.SimpleEntry<>("outflow_org_linkman", "流出或流入党组织联系人"),
                            new AbstractMap.SimpleEntry<>("outflow_org_phone", "流出或流入党组织联系方式"),
                            new AbstractMap.SimpleEntry<>("flow_add_type", "流出或流入"),
                            new AbstractMap.SimpleEntry<>("outflow_edu_name", "党员学历"),
                            new AbstractMap.SimpleEntry<>("outflow_job_name", "原职业(工作岗位)"),
                            new AbstractMap.SimpleEntry<>("unit_name", "组织单位"),
                            new AbstractMap.SimpleEntry<>("mem_org_org_code", "组织层级码"))
                    .collect(Collectors.toMap(AbstractMap.SimpleEntry::getKey, AbstractMap.SimpleEntry::getValue, (o, n) -> o, LinkedHashMap::new));
        }
    },

    ccp_org_all {
        @Override
        public LinkedHashMap<String, String> apply() {
            return Stream.of(
                            new AbstractMap.SimpleEntry<>("name", "组织名称"),
                            new AbstractMap.SimpleEntry<>("parent_name", "上级党组织名称"),
                            new AbstractMap.SimpleEntry<>("d01_name", "党组织类型名称"),
                            new AbstractMap.SimpleEntry<>("d02_name", "党组织所在单位情况名称"),
                            new AbstractMap.SimpleEntry<>("d03_name", "党组织隶属关系名称"),
                            new AbstractMap.SimpleEntry<>("d04_name", "单位类别"),
                            new AbstractMap.SimpleEntry<>("d05_name", "建立党组织情况"),
                            new AbstractMap.SimpleEntry<>("secretary", "党组织书记"),
                            new AbstractMap.SimpleEntry<>("contacter", "联系人"),
                            new AbstractMap.SimpleEntry<>("contact_phone", "联系方式"),
                            new AbstractMap.SimpleEntry<>("main_unit_name", "单位名称"))
                    .collect(Collectors.toMap(AbstractMap.SimpleEntry::getKey, AbstractMap.SimpleEntry::getValue, (o, n) -> o, LinkedHashMap::new));
        }
    },

    ccp_unit_all {
        @Override
        public LinkedHashMap<String, String> apply() {
            //noinspection RedundantTypeArguments (explicit type arguments speedup compilation and analysis time)
            return Stream.<AbstractMap.SimpleEntry<String, String>>of(
                            new AbstractMap.SimpleEntry<>("main_org_name", "关联组织"),
                            //2024年统 王察说单位反查导出屏蔽党建指导组织
//                            new AbstractMap.SimpleEntry<>("manage_org_name", "党建指导组织"),
                            new AbstractMap.SimpleEntry<>("name", "单位名称"),
                            new AbstractMap.SimpleEntry<>("credit_code", "单位代码"),
                            new AbstractMap.SimpleEntry<>("d04_name", "单位类别"),
                            new AbstractMap.SimpleEntry<>("d16_name", "经济类型"),
                            new AbstractMap.SimpleEntry<>("d17_name", "企业规模"),
                            new AbstractMap.SimpleEntry<>("d35_name", "单位隶属关系"),
                            new AbstractMap.SimpleEntry<>("d48_name", "所在地区"),
                            new AbstractMap.SimpleEntry<>("d81_name", "公益分类"),
                            new AbstractMap.SimpleEntry<>("d111_name", "办院类型"),
                            new AbstractMap.SimpleEntry<>("d112_name", "所长负责制情况"),
                            new AbstractMap.SimpleEntry<>("on_post_num", "在岗职工数（人）"),
                            new AbstractMap.SimpleEntry<>("is_org_service", "建立党员服务机构 1是，0否"),
                            new AbstractMap.SimpleEntry<>("is_vol_team", "建立党员志愿者队伍 1是，0否"),
                            new AbstractMap.SimpleEntry<>("has_party_work", "是否配备专职党务工作人员  1是，0否"),
                            new AbstractMap.SimpleEntry<>("has_major_denew AbstractMap.SimpleEntry<>y_secretary", "配备专职副书记 1是，0否"),
                            new AbstractMap.SimpleEntry<>("has_representative", "法定代表人是否党员 1是，0否"),
                            new AbstractMap.SimpleEntry<>("has_union_organization", "是否建立工会或共青团组织  1是，0否"),
                            new AbstractMap.SimpleEntry<>("has_instructor_contact", "是否党建工作指导员联系 1是，0否"),
                            new AbstractMap.SimpleEntry<>("has_organization_secretary", "是否主要负责人担任党组织书记 1是，0否"),
                            new AbstractMap.SimpleEntry<>("is_decoupl_industry", "是否脱钩行业协会商会 1是，0否"),
                            new AbstractMap.SimpleEntry<>("unit_party_central", "中央一级机关党组数量"),
                            new AbstractMap.SimpleEntry<>("unit_party_province", "省（区、市）一级机关党组"),
                            new AbstractMap.SimpleEntry<>("unit_party_city", "市（地、州、盟）一级机关党组"),
                            new AbstractMap.SimpleEntry<>("unit_party_county", "县（市、区、旗）一级机关党组"),
                            new AbstractMap.SimpleEntry<>("absorbed_tissue_number", "吸收未转入组织关系的党员建立党组织数"),
                            new AbstractMap.SimpleEntry<>("not_turned_party", "未转组织关系党员数"),
                            new AbstractMap.SimpleEntry<>("bZT6_10", "党建工作指导员数"),
                            new AbstractMap.SimpleEntry<>("b30_a12", "党政机关工作人员"),
                            new AbstractMap.SimpleEntry<>("tec_num", "在岗专业技术人员数（人）"),
                            new AbstractMap.SimpleEntry<>("zaigang_gaoji", "在岗专业技术人员（高级职称）（人）"),
                            new AbstractMap.SimpleEntry<>("employees_number", "从业人员数"),
                            new AbstractMap.SimpleEntry<>("create_party_group", "建立分党组"),
                            new AbstractMap.SimpleEntry<>("create_party_team", "建立党组"),
                            new AbstractMap.SimpleEntry<>("create_party_committee", "建立党组性质党委"),
                            new AbstractMap.SimpleEntry<>("year", "最新第一书记年份"),
                            new AbstractMap.SimpleEntry<>("first_secretary_select", "今年选派第一书记"),
                            new AbstractMap.SimpleEntry<>("secretary_training_num", "本年各级培训第一书记"),
                            new AbstractMap.SimpleEntry<>("has_thousand", "是否为第一书记安排不低于1万元工作经费"),
                            new AbstractMap.SimpleEntry<>("has_bundled", "是否派出单位落实责任、项目、资金捆绑的"),
                            new AbstractMap.SimpleEntry<>("promoted_num", "提拔使用或晋级的第一书记数"),
                            new AbstractMap.SimpleEntry<>("adjusted_num", "因工作不胜任召回调整的第一书记数"),
                            new AbstractMap.SimpleEntry<>("operating_expenses", "运转经费（万元）"),
                            new AbstractMap.SimpleEntry<>("village_per", "每村办公经费（万元）"),
                            new AbstractMap.SimpleEntry<>("secretary_salary", "党组织书记平均报酬（万元）"),
                            new AbstractMap.SimpleEntry<>("community_workers_salary", "全部社区工作者年工资总额（万元）"),
                            new AbstractMap.SimpleEntry<>("included_financial", "社区纳入财政预算的工作经费总额（万元）"),
                            new AbstractMap.SimpleEntry<>("special_funds_masses", "社区全年服务群众专项经费总额（万元）"),
                            new AbstractMap.SimpleEntry<>("zzngzze", "全部专职网格员年工资总额（万元）"),
                            new AbstractMap.SimpleEntry<>("residential_areas", "社区中的住宅小区总数"),
                            new AbstractMap.SimpleEntry<>("grid_members", "配备专职网格员数"),
                            new AbstractMap.SimpleEntry<>("community_worker_count", "社区工作者人数"),
                            new AbstractMap.SimpleEntry<>("street_cadres", "街道干部人数"),
                            new AbstractMap.SimpleEntry<>("age35_below", "街道干部35岁及以下人数"),
                            new AbstractMap.SimpleEntry<>("age36_to_age55", "街道干部36至55岁人数"),
                            new AbstractMap.SimpleEntry<>("age_56_above", "街道干部56岁及以上人数"),
                            new AbstractMap.SimpleEntry<>("college_degree_above", "街道干部大专及以上学历人数"),
                            new AbstractMap.SimpleEntry<>("secondary_school_below", "街道干部高中中专及以下人数"),
                            new AbstractMap.SimpleEntry<>("street_cadres_civil", "街道干部公务员人数"),
                            new AbstractMap.SimpleEntry<>("street_cadres_institutions", "街道干部事业单位人数"),
                            new AbstractMap.SimpleEntry<>("cadre_other", "街道干部其他身份人数"))
                    .collect(Collectors.toMap(AbstractMap.SimpleEntry::getKey, AbstractMap.SimpleEntry::getValue, (o, n) -> o, LinkedHashMap::new));
        }
    },

    ccp_mem_reward_all {
        @Override
        public LinkedHashMap<String, String> apply() {
            return Stream.of(
                            new AbstractMap.SimpleEntry<>("name", "姓名"),
                            new AbstractMap.SimpleEntry<>("age", "年龄"),
                            new AbstractMap.SimpleEntry<>("join_org_date", "预备党员日期"),
                            new AbstractMap.SimpleEntry<>("full_member_date", "转为正式党员日期"),
                            new AbstractMap.SimpleEntry<>("d029_name", "党员受奖/惩名称"),
                            new AbstractMap.SimpleEntry<>("d030_name", "奖/惩原因名称"),
                            new AbstractMap.SimpleEntry<>("start_date", "党员奖/惩批准日期"),
                            new AbstractMap.SimpleEntry<>("org_code", "组织名称"),
                            new AbstractMap.SimpleEntry<>("mem_org_code", "组织层级码"))
                    .collect(Collectors.toMap(AbstractMap.SimpleEntry::getKey, AbstractMap.SimpleEntry::getValue, (o, n) -> o, LinkedHashMap::new));
        }
    },

    ccp_org_recognition_all {
        @Override
        public LinkedHashMap<String, String> apply() {
            return Stream.of(
                            new AbstractMap.SimpleEntry<>("d01_code", "组织类别"),
                            new AbstractMap.SimpleEntry<>("number", "人数"),
                            new AbstractMap.SimpleEntry<>("recognition_object", "表彰对象"),
                            new AbstractMap.SimpleEntry<>("recognition_level", "表彰级别"),
                            new AbstractMap.SimpleEntry<>("recognition_type", "表彰类型"),
                            new AbstractMap.SimpleEntry<>("committee_party", "党委(总支部、支部)书记优秀共产党员名数"),
                            new AbstractMap.SimpleEntry<>("committee_worker", "党委(总支部、支部)书记优秀党务工作者名数"),
                            new AbstractMap.SimpleEntry<>("difficult_party", "生活困难优秀共产党员名数"),
                            new AbstractMap.SimpleEntry<>("difficult_worker", "生活困难优秀党务工作者名数"),
                            new AbstractMap.SimpleEntry<>("add_good_party", "追授优秀共产党员名数"),
                            new AbstractMap.SimpleEntry<>("anniversary_situation", "建党50周年情况（数）"),
                            new AbstractMap.SimpleEntry<>("year", "表彰年度"),
                            new AbstractMap.SimpleEntry<>("org_code", "组织名称"))
                    .collect(Collectors.toMap(AbstractMap.SimpleEntry::getKey, AbstractMap.SimpleEntry::getValue, (o, n) -> o, LinkedHashMap::new));
        }
    },

    ccp_org_slack_all {
        @Override
        public LinkedHashMap<String, String> apply() {
            return Stream.of(
                            new AbstractMap.SimpleEntry<>("name", "组织名称"),
                            new AbstractMap.SimpleEntry<>("year", "整顿年度"),
                            new AbstractMap.SimpleEntry<>("neaten_time", "整顿开始时间（列为软弱涣散基层党组织时间）"),
                            new AbstractMap.SimpleEntry<>("neaten_endtime", "整备结束时间"),
                            new AbstractMap.SimpleEntry<>("hasNeaten", "是否整顿"),
                            new AbstractMap.SimpleEntry<>("d74_code1", "属于党组织班子配备不齐、书记长期缺职、工作处于停滞状态的"),
                            new AbstractMap.SimpleEntry<>("d74_code2", "属于党组织书记不胜任现职、工作不在状态、严重影响班子整体战斗力的"),
                            new AbstractMap.SimpleEntry<>("d74_code3", "属于班子不团结、内耗严重、工作不能正常开展的"),
                            new AbstractMap.SimpleEntry<>("d74_code4", "属于组织制度形同虚设、不开展党组织活动的"),
                            new AbstractMap.SimpleEntry<>("d74_code5", "属于换届选举拉票贿选问题突出的"),
                            new AbstractMap.SimpleEntry<>("d74_code6", "属于宗族宗族和黑恶势力干扰渗透严重的"),
                            new AbstractMap.SimpleEntry<>("d74_code7", "属于宗教势力干扰渗透的"),
                            new AbstractMap.SimpleEntry<>("d74_code8", "属于村务居务财务公开和民主管理混乱的"),
                            new AbstractMap.SimpleEntry<>("d74_code9", "属于社会治安问题和信访矛盾纠纷集中的"),
                            new AbstractMap.SimpleEntry<>("d74_code10", "属于无固定办公活动场所及便民服务设施的"),
                            new AbstractMap.SimpleEntry<>("d74_code11", "属于党组织服务意识差、服务能力弱、群众意见大的"),
                            new AbstractMap.SimpleEntry<>("early_qp_secretary", "年初缺配的软弱涣散基层党组织书记数（人）"),
                            new AbstractMap.SimpleEntry<>("early_tz_secretary", "年初需调整的软弱涣散基层党组织书记数（人）"),
                            new AbstractMap.SimpleEntry<>("has_year_selected", "本年度已选配"),
                            new AbstractMap.SimpleEntry<>("has_year_adjust", "本年度已调整"),
                            new AbstractMap.SimpleEntry<>("train_secretary", "培训软弱涣散基层党组织书记（人）"),
                            new AbstractMap.SimpleEntry<>("lc_county_level_leader", "联村的县级领导班子成员（人）"),
                            new AbstractMap.SimpleEntry<>("bc_county_level_leader", "包村的县级领导班子成员（人）"),
                            new AbstractMap.SimpleEntry<>("first_secretary", "选派第一书记（人）"),
                            new AbstractMap.SimpleEntry<>("jdbf_county_level_unit", "结对帮扶的县级及以上机关单位（人）"),
                            new AbstractMap.SimpleEntry<>("two_levels_listed", "省市两级挂牌督办的村（个）"),
                            new AbstractMap.SimpleEntry<>("special_rectification", "开展专项整治（项）"),
                            new AbstractMap.SimpleEntry<>("solve_problems", "解决各类问题（个）"),
                            new AbstractMap.SimpleEntry<>("look_into_laws", "查处违纪违法行为（例）"),
                            new AbstractMap.SimpleEntry<>("d04_name", "单位类别"),
                            new AbstractMap.SimpleEntry<>("unit_name", "单位名称"))
                    .collect(Collectors.toMap(AbstractMap.SimpleEntry::getKey, AbstractMap.SimpleEntry::getValue, (o, n) -> o, LinkedHashMap::new));
        }
    },

    ccp_org_industry_all {
        @Override
        public LinkedHashMap<String, String> apply() {
            return Stream.of(
                            new AbstractMap.SimpleEntry<>("industry_org_name", "组织名称"),
                            new AbstractMap.SimpleEntry<>("industry_org_type", "组织类别"),
                            new AbstractMap.SimpleEntry<>("industry_classification", "行业分类"),
                            new AbstractMap.SimpleEntry<>("subordinate_level", "所属层级"),
                            new AbstractMap.SimpleEntry<>("membership_function", "隶属关系"),
                            new AbstractMap.SimpleEntry<>("has_secretary_industry", "书记是否由行业主管部门党员负责同志担任"),
                            new AbstractMap.SimpleEntry<>("worker_number", "专职工作人员数"),
                            new AbstractMap.SimpleEntry<>("has_party_organizations", "是否有所属党组织"),
                            new AbstractMap.SimpleEntry<>("manage_org_count", "管理的党组织数"),
                            new AbstractMap.SimpleEntry<>("manage_mem_count", "管理的党员数"),
                            new AbstractMap.SimpleEntry<>("cover_social_org", "覆盖社会组织数"))
                    .collect(Collectors.toMap(AbstractMap.SimpleEntry::getKey, AbstractMap.SimpleEntry::getValue, (o, n) -> o, LinkedHashMap::new));
        }
    },

    ccp_org_party_congress_committee_all {
        @Override
        public LinkedHashMap<String, String> apply() {
            return Stream.of(
                            new AbstractMap.SimpleEntry<>("mem_name", "人员姓名"),
                            new AbstractMap.SimpleEntry<>("sex_name", "性别"),
                            new AbstractMap.SimpleEntry<>("mem_idcard", "身份证"),
                            new AbstractMap.SimpleEntry<>("sex_name", "性别"),
                            new AbstractMap.SimpleEntry<>("birthday", "生日"),
                            new AbstractMap.SimpleEntry<>("d07_name", "学历情况"),
                            new AbstractMap.SimpleEntry<>("d124_name", "岗位"),
                            new AbstractMap.SimpleEntry<>("d106_name", "人员身份"),
                            new AbstractMap.SimpleEntry<>("position_org_code", "组织层级码"),
                            new AbstractMap.SimpleEntry<>("position_org_name", "组织名称"))

                    .collect(Collectors.toMap(AbstractMap.SimpleEntry::getKey, AbstractMap.SimpleEntry::getValue, (o, n) -> o, LinkedHashMap::new));
        }
    },

    ccp_org_party {
        @Override
        public LinkedHashMap<String, String> apply() {
            return Stream.of(
                    new AbstractMap.SimpleEntry<>("party_name", "党组名称"),
                    new AbstractMap.SimpleEntry<>("d108_name", "党组类别"),
                    new AbstractMap.SimpleEntry<>("unit_name", "关联单位"),
                    new AbstractMap.SimpleEntry<>("builde_time", "创建时间"),
                    new AbstractMap.SimpleEntry<>("org_code", "组织名称")
            ).collect(Collectors.toMap(AbstractMap.SimpleEntry::getKey, AbstractMap.SimpleEntry::getValue, (o, n) -> o, LinkedHashMap::new));
        }
    },
    ccp_transfer_statistics {
        @Override
        public LinkedHashMap<String, String> apply() {
            return Stream.of(
                    new AbstractMap.SimpleEntry<>("transfer_mem_name", "姓名"),
                    new AbstractMap.SimpleEntry<>("transfer_time", "申请日期"),
                    new AbstractMap.SimpleEntry<>("transfrer_out_org_name", "源组织"),
                    new AbstractMap.SimpleEntry<>("transfer_in_org_name", "目的组织"),
                    new AbstractMap.SimpleEntry<>("transfer_type_name", "转接类型"),
                    new AbstractMap.SimpleEntry<>("tansfer_receive_time", "接收时间")
            ).collect(Collectors.toMap(AbstractMap.SimpleEntry::getKey, AbstractMap.SimpleEntry::getValue, (o, n) -> o, LinkedHashMap::new));
        }
    },

    mem_flow_all {
        @Override
        public LinkedHashMap<String, String> apply() {
            return Stream.of(
                            new AbstractMap.SimpleEntry<>("mem_name", "姓名"),
                            new AbstractMap.SimpleEntry<>("mem_sex_name", "性别"),
                            new AbstractMap.SimpleEntry<>("mem_phone", "联系电话"),
                            new AbstractMap.SimpleEntry<>("flow_type_name", "流动类型"),
                            new AbstractMap.SimpleEntry<>("mem_org_name", "流出地党支部"),
                            new AbstractMap.SimpleEntry<>("in_org_name", "流入地党支部"))
                    .collect(Collectors.toMap(AbstractMap.SimpleEntry::getKey, AbstractMap.SimpleEntry::getValue, (o, n) -> o, LinkedHashMap::new));
        }
    },

    ccp_mem_report {
        @Override
        public LinkedHashMap<String, String> apply() {
            return Stream.of(
                            new AbstractMap.SimpleEntry<>("name", "姓名"),
                            new AbstractMap.SimpleEntry<>("idcard", "证件号"),
                            new AbstractMap.SimpleEntry<>("phone", "联系电话"),
                            new AbstractMap.SimpleEntry<>("age", "年龄"),
                            new AbstractMap.SimpleEntry<>("sex_name", "性别"),
                            new AbstractMap.SimpleEntry<>("unit_name", "单位名称"),
                            new AbstractMap.SimpleEntry<>("d04_name", "单位类别")
                    )
                    .collect(Collectors.toMap(AbstractMap.SimpleEntry::getKey, AbstractMap.SimpleEntry::getValue, (o, n) -> o, LinkedHashMap::new));
        }
    },
    ;

    public abstract LinkedHashMap<String, String> apply();

    public static ReportEnum find(String name) {
        return Arrays.stream(ReportEnum.values()).filter(reportEnum -> StrUtil.startWith(name, reportEnum.name())).findFirst().orElse(null);
    }

}
