package com.zenith.front.core.service.logic;

import java.util.*;
import java.util.stream.Collectors;

import cn.afterturn.easypoi.excel.ExcelExportUtil;
import cn.afterturn.easypoi.excel.entity.ExportParams;
import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.zenith.front.api.develop.IDevelopStepLogService;
import com.zenith.front.api.logic.ILogicCheckIgnoreService;
import com.zenith.front.api.mem.IMemDevelopService;
import com.zenith.front.api.mem.IMemFlow1Service;
import com.zenith.front.api.mem.IMemService;
import com.zenith.front.api.org.IOrgService;
import com.zenith.front.api.unit.IUnitService;
import com.zenith.front.common.kit.EasyPoiExcelUtil;
import com.zenith.front.common.kit.StrKit;
import com.zenith.front.dao.mapper.logic.LogicCheckIgnoreMapper;
import com.zenith.front.model.message.OutMessage;
import com.zenith.front.model.message.Status;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.zenith.front.model.bean.*;
import com.zenith.front.model.vo.LogicIgnoreListVO;
import org.apache.poi.ss.usermodel.Workbook;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;

/**
 * <p>
 * 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-03-24
 */
@Service
public class LogicCheckIgnoreServiceImpl extends ServiceImpl<LogicCheckIgnoreMapper, LogicCheckIgnore> implements ILogicCheckIgnoreService {

    @Resource
    private LogicCheckIgnoreMapper logicCheckIgnoreMapper;
    @Resource
    private IMemService memService;
    @Resource
    private IMemDevelopService memDevelopService;
    @Resource
    private IDevelopStepLogService developStepLogService;
    @Resource
    private IOrgService orgService;
    @Resource
    private IUnitService unitService;
    @Resource
    private IMemFlow1Service memFlow1Service;
    @Resource
    private HttpServletResponse response;

    private static final List<String> MEM_TABLE = Arrays.asList("ccp_mem_all", "ccp_mem");

    private static final List<String> MEM_DEVELOP_TABLE = Arrays.asList("ccp_mem_develop_all", "ccp_mem_develop");

    private static final List<String> DEVELOP_STEP_LOG_TABLE = Arrays.asList("ccp_develop_step_log_all", "ccp_develop_step_log");

    private static final List<String> ORG_TABLE = Arrays.asList("ccp_org_all", "ccp_org");

    private static final List<String> UNIT_TABLE = Arrays.asList("ccp_unit_all", "ccp_unit");

    private static final List<String> MEM_FLOW_TABLE = Collections.singletonList("mem_flow");

    @Override
    public OutMessage<Object> ignore(String logicCheckCode, String logicCheckName, String type, String code, String reason, String updateAccount) {
        LogicCheckIgnore logicCheckIgnore = new LogicCheckIgnore();
        logicCheckIgnore.setId(StrKit.getRandomUUID());
        logicCheckIgnore.setLogicCheckCode(logicCheckCode);
        logicCheckIgnore.setLogicCheckName(logicCheckName);
        logicCheckIgnore.setType(type);
        logicCheckIgnore.setCode(code);
        logicCheckIgnore.setReason(reason);
        logicCheckIgnore.setUpdateAccount(updateAccount);
        logicCheckIgnore.setCreateTime(new Date());
        if (MEM_TABLE.contains(type)) {
            Mem mem = memService.findByCode(code);
            Optional.ofNullable(mem).ifPresent(t -> logicCheckIgnore.setOrgCode(mem.getMemOrgCode()));
        } else if (MEM_DEVELOP_TABLE.contains(type)) {
            MemDevelop memDevelop = memDevelopService.findByCode(code);
            Optional.ofNullable(memDevelop).ifPresent(t -> logicCheckIgnore.setOrgCode(memDevelop.getDevelopOrgCode()));
        } else if (DEVELOP_STEP_LOG_TABLE.contains(type)) {
            DevelopStepLog developStepLog = developStepLogService.findByCode(code);
            Optional.ofNullable(developStepLog).ifPresent(t -> logicCheckIgnore.setOrgCode(developStepLog.getLogOrgCode()));
        } else if (ORG_TABLE.contains(type)) {
            Org org = orgService.findOrgByCode(code);
            Optional.ofNullable(org).ifPresent(t -> logicCheckIgnore.setOrgCode(org.getOrgCode()));
        } else if (UNIT_TABLE.contains(type)) {
            Unit unit = unitService.findByCode(code);
            Optional.ofNullable(unit).ifPresent(t -> logicCheckIgnore.setOrgCode(unit.getCreateUnitOrgCode()));
        } else if (MEM_FLOW_TABLE.contains(type)) {
            MemFlow1 memFlow = memFlow1Service.getById(code);
            Optional.ofNullable(memFlow).ifPresent(t -> logicCheckIgnore.setOrgCode(memFlow.getMemOrgOrgCode()));
        }
        boolean flag = save(logicCheckIgnore);
        return new OutMessage<>(flag ? Status.SUCCESS : Status.FAIL);
    }

    @Override
    public Set<String> selectIgnoreCode(String orgCode) {
        LambdaQueryWrapper<LogicCheckIgnore> queryWrapper = new LambdaQueryWrapper<LogicCheckIgnore>()
                .isNull(LogicCheckIgnore::getDeleteTime)
                .likeRight(LogicCheckIgnore::getOrgCode, orgCode)
                .select(LogicCheckIgnore::getLogicCheckCode);
        List<LogicCheckIgnore> logicCheckIgnoreList = list(queryWrapper);
        return logicCheckIgnoreList.stream().map(LogicCheckIgnore::getLogicCheckCode).collect(Collectors.toSet());
    }

    public Page<LogicIgnoreListVO> ignorePage(String logicCheckCode, String orgCode, String tableName, Integer pageNum, Integer pageSize) {
        Page<LogicIgnoreListVO> page = new Page<>(pageNum, pageSize);
        logicCheckIgnoreMapper.ignoreList(page, tableName, orgCode, logicCheckCode);
        List<LogicIgnoreListVO> records = page.getRecords();
        if (CollUtil.isNotEmpty(records)) {
            for (LogicIgnoreListVO record : records) {
                //兼容单位名称unitName到name字段
                String unitName = record.getUnitName();
                if (StringUtils.hasText(unitName) && StringUtils.isEmpty(record.getName())) {
                    record.setName(unitName);
                    record.setUnitName(null);
                }
            }
        }
        return page;
    }

    @Override
    public OutMessage<Object> ignoreList(String logicCheckCode, String orgCode, String tableName, Integer pageNum, Integer pageSize) {
        Page<LogicIgnoreListVO> page = ignorePage(logicCheckCode, orgCode, tableName, pageNum, pageSize);
        return new OutMessage<>(Status.SUCCESS, page);
    }

    @Override
    public void exportIgnoreList(String logicCheckCode, String orgCode, String tableName, Integer pageNum, Integer pageSize) {
        Page<LogicIgnoreListVO> page = ignorePage(logicCheckCode, orgCode, tableName, 1, 10000);
        String fileName = "数据校验已忽略信息记录";
        Workbook workbook = ExcelExportUtil.exportBigExcel(new ExportParams(fileName, "第1页"), LogicIgnoreListVO.class, page.getRecords());
        EasyPoiExcelUtil.response(response, workbook, fileName);
    }

    @Override
    public OutMessage<Object> delete(String id, String account) {
        LogicCheckIgnore dbCheckIgnore = getById(id);
        if (Objects.isNull(dbCheckIgnore) || Objects.nonNull(dbCheckIgnore.getDeleteTime())) {
            return new OutMessage<>(Status.SUCCESS);
        }
        LogicCheckIgnore logicCheckIgnore = new LogicCheckIgnore();
        logicCheckIgnore.setId(dbCheckIgnore.getId());
        logicCheckIgnore.setDeleteAccount(account);
        logicCheckIgnore.setDeleteTime(new Date());
        boolean flag = updateById(logicCheckIgnore);
        return new OutMessage<>(flag ? Status.SUCCESS : Status.FAIL);
    }
}
