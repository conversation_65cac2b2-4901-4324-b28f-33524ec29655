package com.zenith.front.core.service.export;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.zenith.front.api.export.IExportService;
import com.zenith.front.common.kit.JackSonUtil;
import com.zenith.front.model.custom.Record;
import com.zenith.front.dao.mapper.export.ExportMapper;
import com.zenith.front.model.bean.Export;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * <p>
 * 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-07-10
 */
@Service
public class ExportServiceImpl extends ServiceImpl<ExportMapper, Export> implements IExportService {
    @Override
    public List<Record> getExportList(String tableName) {
        LambdaQueryWrapper<Export> tQueryWrapper = new LambdaQueryWrapper<Export>()
                .eq(Export::getTableName, tableName)
                .isNull(Export::getDeleteTime)
                .orderByAsc(Export::getId);
        List<Export> exportList = list(tQueryWrapper);
        List<Record> result = new ArrayList<>();
        exportList.forEach(val -> {
            Map<String, Object> map = JackSonUtil.beanToMap(val);
            Record record = new Record();
            record.put(map);
            result.add(record);
        });
        return result;
    }
}
