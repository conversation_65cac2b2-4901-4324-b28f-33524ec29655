package com.zenith.front.core.service.user;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.zenith.front.api.user.IUserRoleInfoViewService;
import com.zenith.front.common.constant.UserConstant;
import com.zenith.front.dao.mapper.user.UserRoleInfoViewMapper;
import com.zenith.front.model.modelview.UserRoleInfoView;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <p>
 * 用户表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-07-10
 */
@Service
public class UserRoleInfoViewServiceImpl extends ServiceImpl<UserRoleInfoViewMapper, UserRoleInfoView> implements IUserRoleInfoViewService {

    @Override
    public UserRoleInfoView findUserInfoById(String id) {
        LambdaQueryWrapper<UserRoleInfoView> queryWrapper = new LambdaQueryWrapper<UserRoleInfoView>()
                .eq(UserRoleInfoView::getId, id)
                .eq(UserRoleInfoView::getIsDelete, UserConstant.NOT_IS_DELETE);
        return getOne(queryWrapper);
    }

    @Override
    public Page<UserRoleInfoView> findUserByOrgCodePage(String orgCode, boolean isExclude, String keyword, Integer pageNum, Integer pageSize) {
        LambdaQueryWrapper<UserRoleInfoView> query = new LambdaQueryWrapper<UserRoleInfoView>()
                .eq(!isExclude, UserRoleInfoView::getIsAdmin, false)
                .eq(UserRoleInfoView::getOrgCode, orgCode)
                .eq(UserRoleInfoView::getIsDelete, UserConstant.NOT_IS_DELETE);
        if (StringUtils.hasText(keyword)) {
            query.and(wrapper -> wrapper
                    .like(UserRoleInfoView::getName, keyword)
                    .or()
                    .like(UserRoleInfoView::getAccount, keyword));
        }
        Page<UserRoleInfoView> page = new Page<>(pageNum, pageSize);
        page(page, query);
        return page;
    }

    @Override
    public Page<UserRoleInfoView> findUserByOrgCodePageTrue(String orgCode, Integer pageNum, Integer pageSize) {
        LambdaQueryWrapper<UserRoleInfoView> query = new LambdaQueryWrapper<UserRoleInfoView>()
                .likeRight(UserRoleInfoView::getOrgCode, orgCode);
        //.eq(UserRoleInfoView::getIsDelete, UserConstant.NOT_IS_DELETE);
        Page<UserRoleInfoView> page = new Page<>(pageNum, pageSize);
        page(page, query);
        return page;
    }

    @Override
    public Page<UserRoleInfoView> findUserByKeyword(Integer pageNum, Integer pageSize, String keyword, String orgCode, String userId) {
        LambdaQueryWrapper<UserRoleInfoView> query = new LambdaQueryWrapper<UserRoleInfoView>()
                .eq(UserRoleInfoView::getIsDelete, UserConstant.NOT_IS_DELETE)
                .likeRight(UserRoleInfoView::getOrgCode, orgCode)
                .and(wrapper -> wrapper
                        .like(UserRoleInfoView::getName, keyword)
                        .or()
                        .like(UserRoleInfoView::getAccount, keyword))
                .ne(UserRoleInfoView::getId, userId)
                .orderByAsc(UserRoleInfoView::getCreateTime);
        Page<UserRoleInfoView> page = new Page<>(pageNum, pageSize);
        page(page, query);
        return page;
    }

    @Override
    public List<UserRoleInfoView> findUserInfoByIdList(List<String> userIdList) {
        if (CollUtil.isEmpty(userIdList)) {
            return Collections.emptyList();
        }
        LambdaQueryWrapper<UserRoleInfoView> queryWrapper = new LambdaQueryWrapper<UserRoleInfoView>()
                .in(UserRoleInfoView::getId, userIdList)
                .eq(UserRoleInfoView::getIsDelete, UserConstant.NOT_IS_DELETE);
        return list(queryWrapper);
    }

    @Override
    public List<UserRoleInfoView> findUserViewByManagementSystem(String managementSystem) {
        if (!StringUtils.hasText(managementSystem)) {
            return Collections.emptyList();
        }
        LambdaQueryWrapper<UserRoleInfoView> queryWrapper = new LambdaQueryWrapper<UserRoleInfoView>()
                .eq(UserRoleInfoView::getIsDelete, UserConstant.NOT_IS_DELETE)
                .select(UserRoleInfoView::getId, UserRoleInfoView::getAccount, UserRoleInfoView::getMemCode);
        List<UserRoleInfoView> infoViewList = list(queryWrapper);
        if (CollUtil.isEmpty(infoViewList)) {
            return Collections.emptyList();
        }
        return infoViewList.stream()
                .filter(t ->
                        Arrays.asList(StringUtils.commaDelimitedListToStringArray(t.getManagementSystem()))
                                .contains(managementSystem))
                .collect(Collectors.toList());
    }
}
