package com.zenith.front.core.service.mem;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.thread.ThreadUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.zenith.front.api.flow.IOrgFlowService;
import com.zenith.front.api.mem.IMemFlow1Service;
import com.zenith.front.api.mem.IMemFlowSignAuditService;
import com.zenith.front.api.mem.IMemFlowSignService;
import com.zenith.front.api.org.IOrgAllService;
import com.zenith.front.api.org.IOrgService;
import com.zenith.front.common.constant.CommonConstant;
import com.zenith.front.common.kit.HttpKit;
import com.zenith.front.common.kit.StrKit;
import com.zenith.front.common.untils.FlowUntil;
import com.zenith.front.dao.mapper.mem.MemFlowSignMapper;
import com.zenith.front.model.bean.*;
import com.zenith.front.model.constant.UserConstant;
import com.zenith.front.model.custom.UserTicket;
import com.zenith.front.model.dto.MemFlowSignDTO;
import com.zenith.front.model.dto.MemFlowSignSyncDTO;
import com.zenith.front.model.message.OutMessage;
import com.zenith.front.model.message.Status;
import jodd.util.StringUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;


@Service
@Slf4j
public class MemFlowSignServiceImpl extends ServiceImpl<MemFlowSignMapper, MemFlowSign> implements IMemFlowSignService {


    @Resource
    private IMemFlowSignAuditService auditService;

    @Resource
    private IOrgService orgService;

    @Resource
    private IMemFlowSignAuditService memFlowSignAuditService;

    @Resource
    private IOrgFlowService orgFlowService;

    @Resource
    private IOrgAllService orgAllService;

    @Value("${exchange_nginx_key}")
    private String transferKey;
    @Value("${sync_flow_push}")
    private String sync_Transfer_pull;
    @Autowired
    private IOrgFlowService iOrgFlowService;
    @Resource
    private IMemFlow1Service memFlow1Service;

    @Override
    public OutMessage add(MemFlowSignDTO dto) {
//        UserTicket userTicket = UserConstant.USER_CONTEXT.get();
//        String LoginOrgCode = userTicket.getUserRolePermission().getOrgCode();
        //当前登录用户的党组织
        Org applyOrg = orgService.findOrgByCode(dto.getApplyOrgFlowCode());
        OrgFlow one = orgFlowService.getOne(new LambdaQueryWrapper<OrgFlow>().eq(OrgFlow::getCode, dto.getApplyOrgFlowCode()));

        MemFlowSign memFlowSign = BeanUtil.copyProperties(dto, MemFlowSign.class);
        memFlowSign.setCode(StrKit.getRandomUUID());
//        memFlowSign.setMemCode(StrKit.getRandomUUID());
//        memFlowSign.setApplyOrgFlowCode(org.getCode());
//        memFlowSign.setApplyOrgFlowLevelCode(org.getOrgCode());
//        memFlowSign.setApplyOrgFlowName(org.getName());
        //贵州节点为发送节点
        memFlowSign.setSendNodeCode(FlowUntil.XZQH_GUIZHOU);
        memFlowSign.setDataType("1");
        String orgConnection = ObjectUtil.isNotNull(applyOrg) ? applyOrg.getContactPhone() : one.getContactPhone();
        String orgConnectionName = ObjectUtil.isNotNull(applyOrg) ? applyOrg.getContacter() : one.getContacter();
        memFlowSign.setOrgConnection(orgConnection);
        memFlowSign.setOrgConnectionName(orgConnectionName);

//        memFlowSign.setDataType(memFlowSign.getDataType().equals("0") ? "2" : memFlowSign.getDataType());
        //判断党组织数据来源类型
//        if (dto.getDataType().equals("1")) {
//            //本省数据，判断是否为本节点数据
//            LambdaQueryWrapper<OrgAll> wrapper = new LambdaQueryWrapper<>();
//            wrapper.isNull(OrgAll::getDeleteTime)
//                    .and(q -> q
//                            .isNull(OrgAll::getIsDissolve)
//                            .or()
//                            .ne(OrgAll::getIsDissolve, CommonConstant.ONE_INT)
//                    );
//            wrapper.orderByAsc(OrgAll::getOrgCode);
//            wrapper.last("limit 1");
//            OrgAll orgAll = orgAllService.getOne(wrapper);

            //处理是否为分节点数据
//            if (StrUtil.equals(CommonConstant.ONE, dto.getDataType()) && ObjectUtil.isNotNull(orgAll)) {
//                memFlowSign.setDataType(StrUtil.startWith(dto.getOrgFlowLevelCode(), orgAll.getOrgCode().substring(0, 6)) ? CommonConstant.ZERO : CommonConstant.ONE);
//            }
//            OrgFlow one = orgFlowService.getOne(new LambdaQueryWrapper<OrgFlow>().eq(OrgFlow::getCode, dto.getOrgFlowCode())
//                    .isNull(OrgFlow::getDeleteTime).eq(OrgFlow::getIsEnable, 1).eq(OrgFlow::getSourceType,1));
//            memFlowSign.setDataType(ObjectUtil.isNull(one) ? "1" : "0");
//        }
        //保存
        save(memFlowSign);

        //保存审核记录
        MemFlowSignAudit audit = new MemFlowSignAudit();
        audit.setCode(StrKit.getRandomUUID());
        audit.setSignCode(memFlowSign.getCode());
        audit.setFlowSignCode(memFlowSign.getMemCode());
        audit.setFlowOrgCode(memFlowSign.getOrgFlowCode());
        audit.setFlowOrgName(memFlowSign.getOrgFlowName());
        audit.setStatus("0");
        auditService.save(audit);

        //推送到交换区
//        if (!StringUtil.equals(memFlowSign.getDataType(), "0")) {
        ThreadUtil.execute(() -> {
            pushFlowSignExchange(memFlowSign, audit, "1");
        });
//        }



        return new OutMessage(Status.SUCCESS);
    }

    public OutMessage pushFlowSignExchange(MemFlowSign memFlowSign,MemFlowSignAudit audit,String type) {
        String replaceUrl = sync_Transfer_pull.replace("/api/flow/push", "");
        String url = replaceUrl + "/mem/flow/sign/syncSave";
        JSONObject postData = new JSONObject();
        postData.put("nginxKey", transferKey);
        postData.put("memFlowSign", memFlowSign);
        postData.put("memFlowSignAuditDTO", audit);
        postData.put("type", type);
        String result = HttpKit.doPost(url, postData, "UTF-8");
        if (StrUtil.equals(result, "-1") || StrUtil.isBlank(result)) {
            return new OutMessage<>(Status.FAIL);
        }
        return JSONUtil.toBean(result, OutMessage.class);
    }

    @Override
    public void pullFlowSignExchange() {
        log.info("获取交换区流动提醒数据定时任务开启----------------");
        try {
            Org org = orgService.getShortestOrgCode();
            List<MemFlowSignSyncDTO> list = pull(org.getOrgCode(),1, 100);
//            log.info("获取数据为：", JSONUtil.toJsonStr(list));
            if (CollUtil.isEmpty(list)) {
                return;
            }
            handleFlowInfo(list);
            log.info("获取交换区流动提醒数据定时任务开启----------------完成");
        } catch (Exception e) {
            log.error("获取交换区流动提醒数据定时任务开启----------------异常",e);
        }
    }

    @Override
    public MemFlowSign findByFlowUpCode(String flowUpCode) {
        LambdaQueryWrapper<MemFlowSign> query = new LambdaQueryWrapper<MemFlowSign>()
                .eq(MemFlowSign::getFlowUpCode, flowUpCode);
        return getOne(query);
    }

    public List<MemFlowSignSyncDTO> pull(String orgCode,Integer pageNum, Integer pageSize) {
        List<MemFlowSignSyncDTO> list = new ArrayList<>();
        String replaceUrl = sync_Transfer_pull.replace("/api/flow/push", "");
        String url = replaceUrl + "/mem/flow/sign/sync";
        JSONObject postData = new JSONObject();
        postData.put("orgCode", orgCode);
        postData.put("pageNum", pageNum);
        postData.put("pageSize", pageSize);
        log.info("请求参数为：", postData.toJSONString());
        String result = HttpKit.doPost(url, postData, "UTF-8");
        if (StrUtil.equals(result, "-1") || StrUtil.isBlank(result)) {
            return list;
        }
        cn.hutool.json.JSONObject outMessage = JSONUtil.parseObj(result);
        Object data = outMessage.get("data");
        if(Objects.isNull(data)){
            return list;
        }
        cn.hutool.json.JSONObject dataJson = JSONUtil.parseObj(data);
        JSONArray jsonArray = dataJson.getJSONArray("list");
        if(CollUtil.isEmpty(jsonArray)){
            return list;
        }
        List<MemFlowSignSyncDTO> ll = new ArrayList<>();
        for (Object o : jsonArray) {
            String s = JSONUtil.toJsonStr(o);
            MemFlowSignSyncDTO memFlowSign = JSONUtil.toBean(s, MemFlowSignSyncDTO.class);
            ll.add(memFlowSign);
        }
        Integer pagenumber = JSONUtil.parseObj(data).getInt("pageNumber");
        Integer totalPage = JSONUtil.parseObj(data).getInt("totalPage");

        list.addAll(ll);
        if (pagenumber < totalPage) {
            pageNum++;
            list.addAll(pull(orgCode, pageNum, pageSize));
        }
        return list;
    }

    public void handleFlowInfo(List<MemFlowSignSyncDTO> list) {
        List<MemFlowSign> memFlowSignList = BeanUtil.copyToList(list, MemFlowSign.class);
        saveOrUpdateBatch(memFlowSignList);
        //处理审核数据
        List<String> codeList = list.stream().map(s -> s.getCode()).collect(Collectors.toList());
        Map<String, List<MemFlowSignSyncDTO>> memFlowSignAuditMap = list.stream().collect(Collectors.groupingBy(MemFlowSignSyncDTO::getCode));
        Map<String, List<MemFlowSignAudit>> auditMap = memFlowSignAuditService.list(new LambdaQueryWrapper<MemFlowSignAudit>().in(MemFlowSignAudit::getSignCode, codeList))
                .stream().collect(Collectors.groupingBy(MemFlowSignAudit::getSignCode));
        List<MemFlowSignAudit> auditList = new ArrayList<>();
//        List<MemFlowSignAudit> insertAuditList = new ArrayList<>();
        for (String code : codeList) {
            MemFlowSignSyncDTO memFlowSignSyncDTO = memFlowSignAuditMap.get(code).get(0);
            if (auditMap.containsKey(code)) {
                //数据库中有数据，则更新
                MemFlowSignAudit audit = auditMap.get(code).get(0);
                audit.setAuditTime(memFlowSignSyncDTO.getAuditTime());
                audit.setStatus(memFlowSignSyncDTO.getStatus());
                audit.setRefuse(memFlowSignSyncDTO.getRefuse());
                auditList.add(audit);
            }else {
                //数据库中没有数据，则新增
                MemFlowSignAudit audit = new MemFlowSignAudit();
                audit.setCode(StrKit.getRandomUUID());
                audit.setSignCode(code);
                audit.setFlowOrgCode(memFlowSignSyncDTO.getOrgFlowCode());
                audit.setFlowOrgName(memFlowSignSyncDTO.getOrgFlowName());
                audit.setStatus("0");
                auditList.add(audit);
            }
        }
        memFlowSignAuditService.saveOrUpdateBatch(auditList);
    }

    @Override
    public void pullFLowLxfs(List<String> orgCodeList) {
        try {
            String replaceUrl = sync_Transfer_pull.replace("/api/flow/push", "");
            log.info("开始拉取流动党员联系方式，地址为" + replaceUrl + "/mem/flow/findExchangeLxfsList");
            Map<String, List<String>> postData = new LinkedHashMap<>(10);
            postData.put("orgCode", orgCodeList);
            String outResult = HttpKit.doPost(replaceUrl + "/mem/flow/findExchangeLxfsList" ,JSONUtil.toJsonStr(postData), "UTF-8");
            if (StrUtil.equals(outResult, "-1")) {
                log.warn("Connect to :{} failed: Connection refused: connect", replaceUrl);
                return;
            }
            if (StringUtils.isEmpty(outResult)) {
                return;
            }
            OutMessage<?> resultOut = JSONUtil.toBean(outResult, OutMessage.class);
            if (Objects.isNull(resultOut.getData())) {
                log.warn("No data response");
                return;
            }
            List<MemFlow1> memFlowList = JSONUtil.toList(resultOut.getData().toString(), MemFlow1.class);
            if (CollUtil.isEmpty(memFlowList)) {
                return;
            }
            List<MemFlow1> updateMemFlowList = new ArrayList<>();
            for (MemFlow1 memFlow1 : memFlowList) {
                MemFlow1 updateMem = new MemFlow1();
                updateMem.setCode(memFlow1.getCode());
                updateMem.setOutOrgContact(memFlow1.getOutOrgContact());
                updateMem.setOutOrgContactPhone(memFlow1.getOutOrgContactPhone());
                updateMemFlowList.add(updateMem);
            }
            memFlow1Service.updateBatchById(updateMemFlowList);
            log.info("完成拉取流动党员联系方式，地址为" + replaceUrl + "/mem/flow/findExchangeLxfsList");
        } catch (Exception e) {
            log.error("拉取流动党员联系方式异常",e);
        }
    }

    @Override
    public void pullFLow531Data() {
        try {
            LambdaQueryWrapper<OrgFlow> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(OrgFlow::getIsEnable,1);
            queryWrapper.eq(OrgFlow::getSourceType,2);
            queryWrapper.isNull(OrgFlow::getDeleteTime);
            queryWrapper.orderByAsc(OrgFlow::getId);
            List<OrgFlow> list = iOrgFlowService.list(queryWrapper);
            if(CollUtil.isEmpty(list)){
                return;
            }
            List<String> codeList = list.stream().map(OrgFlow::getCode).collect(Collectors.toList());
            String replaceUrl = sync_Transfer_pull.replace("/api/flow/push", "");
            log.info("开始拉取531驻地党员信息，地址为" + replaceUrl + "/mem/flow/find531DataByCodeList");
            JSONObject postData = new JSONObject();
            postData.put("orgCode", codeList);
            String outResult = HttpKit.doPost(replaceUrl + "/mem/flow/find531DataByCodeList", JSONUtil.toJsonStr(postData), "UTF-8");
            if (StrUtil.equals(outResult, "-1")) {
                log.warn("Connect to :{} failed: Connection refused: connect", replaceUrl);
                return;
            }
            if (StringUtils.isEmpty(outResult)) {
                return;
            }
            OutMessage<?> resultOut = JSONUtil.toBean(outResult, OutMessage.class);
            if (Objects.isNull(resultOut.getData())) {
                log.warn("No data response");
                return;
            }
            List<MemFlow1> memFlowList = JSONUtil.toList(resultOut.getData().toString(), MemFlow1.class);
            if (CollUtil.isEmpty(memFlowList)) {
                return;
            }
            memFlowList.forEach(memFlow1 -> {
                memFlow1.setUpdateTime(null);
                memFlow1.setUpdateAccount(null);
            });
            // log.info("返回数据为" + JSONUtil.toJsonStr(memFlowList));
            memFlow1Service.saveOrUpdateBatch(memFlowList);
            log.info("完成拉取531驻地党员信息，地址为" + replaceUrl + "/mem/flow/find531DataByCodeList");
        } catch (Exception e) {
            log.error("拉取531驻地流动党员异常",e);
        }
    }


}
