package com.zenith.front.core.cache;

import com.zenith.front.common.kit.IAccessTokenCache;

/**
 * <AUTHOR>
 * @date 2019/5/1712:22 PM
 */
public class AccessCache implements IAccessTokenCache {

    @Override
    public String get(String key) {
        return CacheUtils.getWxAccessToken(key);
    }

    @Override
    public void set(String key, String jsonValue) {
        CacheUtils.setWxAccessToken(key,jsonValue);
    }

    @Override
    public void remove(String key) {
        CacheUtils.removeWxAccessToken(key);
    }
}
