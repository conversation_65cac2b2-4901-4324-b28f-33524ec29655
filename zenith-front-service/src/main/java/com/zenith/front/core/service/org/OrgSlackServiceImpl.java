package com.zenith.front.core.service.org;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.thread.ThreadUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.zenith.front.api.org.IOrgService;
import com.zenith.front.api.org.IOrgSlackService;
import com.zenith.front.api.report.IStatisticsYearService;
import com.zenith.front.common.constant.CommonConstant;
import com.zenith.front.common.kit.ExcelExportUtil;
import com.zenith.front.common.kit.StrKit;
import com.zenith.front.core.cache.CacheUtils;
import com.zenith.front.core.kit.CollectionUtil;
import com.zenith.front.core.kit.DbUtil;
import com.zenith.front.core.service.sync.SyncOrgSlackServiceImpl;
import com.zenith.front.model.custom.Record;
import com.zenith.front.dao.mapper.org.OrgSlackMapper;
import com.zenith.front.model.dto.OrgSlackDto;
import com.zenith.front.model.dto.OrgSlackListDto;
import com.zenith.front.model.message.OutMessage;
import com.zenith.front.model.message.Status;
import com.zenith.front.model.bean.Org;
import com.zenith.front.model.bean.OrgSlack;
import com.zenith.front.model.vo.OrgSlackListExcel;
import com.zenith.front.model.vo.OrgSlackListVo;
import org.jooq.Condition;
import org.jooq.SelectSeekStep1;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.ResourceUtils;

import javax.annotation.Resource;
import java.io.File;
import java.io.FileInputStream;
import java.text.DateFormat;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.concurrent.Executor;
import java.util.stream.Collectors;

import static org.jooq.impl.DSL.*;

/**
 * <p>
 * 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-08-05
 */
@Service
public class OrgSlackServiceImpl extends ServiceImpl<OrgSlackMapper, OrgSlack> implements IOrgSlackService {

    @Resource
    private OrgSlackMapper orgSlackMapper;
    @Resource
    private IOrgService orgService;
    @Resource
    private SyncOrgSlackServiceImpl syncOrgSlackService;
    @Resource
    private IStatisticsYearService iStatisticsYearService;
    @Resource
    private Executor mySimpleAsync;


    private OrgSlack findByOrgCode(String orgCode) {
        LambdaQueryWrapper<OrgSlack> wrapper = new QueryWrapper<OrgSlack>().lambda()
                .eq(OrgSlack::getOrgCode, orgCode)
                .isNull(OrgSlack::getDeleteTime)
                .last("limit 1");
        return this.getOne(wrapper);
    }

    @Override
    public OrgSlack findCode(String code) {
        LambdaQueryWrapper<OrgSlack> wrapper = new QueryWrapper<OrgSlack>().lambda()
                .eq(OrgSlack::getCode, code);
        return this.getOne(wrapper);
    }

    @Override
    public OutMessage<?> getList(OrgSlackListDto orgListDTO) throws ParseException {

        Page<OrgSlackListVo> page = this.getListPage(orgListDTO);
        return new OutMessage<>(Status.SUCCESS, page);
    }
    private Boolean checkTime(OrgSlackListDto orgListDTO){
        //orgListDTO.getNeatenEndTimepre(),orgListDTO.getNeatenEndTimeprx()

        return false;
    }

    @Override
    public OutMessage<?> findByCode(String code) {
        OrgSlack orgSlack = this.findCode(code);
        if (Objects.isNull(orgSlack)) {
            return new OutMessage<>(Status.OBJEC_NOT_EXIST);
        }
        Org org = orgService.findOrgByCode(orgSlack.getOrgCode());
        if (Objects.nonNull(org)) {
            orgSlack.setName(org.getName());
        }
        return new OutMessage<>(Status.SUCCESS, orgSlack);
    }

    private Page<OrgSlackListVo> getListPage(OrgSlackListDto orgListDTO) throws ParseException {
        Condition condition = noCondition();
        if (CommonConstant.ONE_INT == orgListDTO.getSubordinate()) {
            condition = condition.and(field(name("ccp_org_slack", "slack_org_code"), String.class).like(orgListDTO.getOrgCode() + "%")
                    .and(field(name("ccp_org_slack", "delete_time")).isNull()));
        }
        if (CommonConstant.ZERO_INT == orgListDTO.getSubordinate()) {
            condition = condition.and(field(name("ccp_org_slack", "slack_org_code"), String.class).eq(orgListDTO.getOrgCode())
                    .and(field(name("ccp_org_slack", "delete_time")).isNull()));
        }

        if (StrUtil.isNotEmpty(orgListDTO.getOrgName())) {
            condition = condition.and(field(name("ccp_org", "name")).like("%" + orgListDTO.getOrgName() + "%"));
        }
        if (CollUtil.isNotEmpty(orgListDTO.getD74CodeList())) {
            Condition cond = noCondition();
            for (String d74 : orgListDTO.getD74CodeList()) {
                cond = cond.or("string_to_array( \"d74_code\", ',' ) @> string_to_array('" + d74 + "', ',' )");
            }
            condition = condition.and(cond);
        }
        //软弱涣散组织增加时间筛选；
        //整顿开始时间两个时间段都不为空的情况
        //四个参数都有值
        if(ObjectUtil.isNotEmpty(orgListDTO.getNeatenTimePre())&&ObjectUtil.isNotEmpty(orgListDTO.getNeatenTimePrx())&&ObjectUtil.isNotEmpty(orgListDTO.getNeatenEndTimepre())&&ObjectUtil.isNotEmpty(orgListDTO.getNeatenEndTimeprx())){
//            String format = new SimpleDateFormat("yyyy-mm-dd").format(orgListDTO.getNeatenTimePre());
//            Date neatenTimePre = new SimpleDateFormat("yyyy-mm-dd").parse(format);
//
//            String format1 = new SimpleDateFormat("yyyy-mm-dd").format(orgListDTO.getNeatenTimePrx());
//            Date neatenTimePrx = new SimpleDateFormat("yyyy-mm-dd").parse(format1);
//
//            String format2 = new SimpleDateFormat("yyyy-mm-dd").format(orgListDTO.getNeatenEndTimepre());
//            Date getNeatenEndTimepre = new SimpleDateFormat("yyyy-mm-dd").parse(format2);
//            String format3 = new SimpleDateFormat("yyyy-mm-dd").format(orgListDTO.getNeatenEndTimeprx());
//            Date getNeatenEndTimeprx = new SimpleDateFormat("yyyy-mm-dd").parse(format3);

            condition = condition.and(field(name("ccp_org_slack","neaten_time")).between(orgListDTO.getNeatenTimePre(),orgListDTO.getNeatenTimePrx())).and(field(name("ccp_org_slack","neaten_endtime")).between(orgListDTO.getNeatenEndTimepre(),orgListDTO.getNeatenEndTimeprx()));
//            condition = condition.and(" ( neaten_time between "+  neatenTimePre+" and "+neatenTimePrx+" or neaten_endtime between "+getNeatenEndTimepre+" and "+getNeatenEndTimeprx+")");
        }else {
            if (ObjectUtil.isNotEmpty(orgListDTO.getNeatenTimePre())&&ObjectUtil.isNotEmpty(orgListDTO.getNeatenTimePrx())) {
                condition = condition.and(field(name("ccp_org_slack","neaten_time")).between(orgListDTO.getNeatenTimePre(),orgListDTO.getNeatenTimePrx()));
            }

            //整顿开始时间第一个开始时间段有值结束时间无值（默认结束时间为当前时间）
            if (ObjectUtil.isNotEmpty(orgListDTO.getNeatenTimePre())&&ObjectUtil.isEmpty(orgListDTO.getNeatenTimePrx())) {
                condition = condition.and(field(name("ccp_org_slack","neaten_time")).between(orgListDTO.getNeatenTimePre(),new Date()));
            }
            //整顿开始时间第一个开始时间段无值结束时间有值（结束时间以前的所有）
            if (ObjectUtil.isEmpty(orgListDTO.getNeatenTimePre())&&ObjectUtil.isNotEmpty(orgListDTO.getNeatenTimePrx())) {
                condition = condition.and(field(name("ccp_org_slack","neaten_time")).le(orgListDTO.getNeatenTimePrx()));
            }

            //整顿结束时间两个时间段都不为空的情况
            if (ObjectUtil.isNotEmpty(orgListDTO.getNeatenEndTimepre())&&ObjectUtil.isNotEmpty(orgListDTO.getNeatenEndTimeprx())) {
                condition = condition.and(field(name("ccp_org_slack","neaten_endtime")).between(orgListDTO.getNeatenEndTimepre(),orgListDTO.getNeatenEndTimeprx()));
            }
            //整顿结束时间第一个开始时间段有值结束时间无值（默认结束时间为当前时间）
            if (ObjectUtil.isNotEmpty(orgListDTO.getNeatenEndTimepre())&&ObjectUtil.isEmpty(orgListDTO.getNeatenEndTimeprx())) {
                condition = condition.and(field(name("ccp_org_slack","neaten_endtime")).between(orgListDTO.getNeatenEndTimepre(),new Date()));
            }
            //整顿结束时间第一个开始时间段无值结束时间有值（结束时间以前的所有）
            if (ObjectUtil.isEmpty(orgListDTO.getNeatenEndTimepre())&&ObjectUtil.isNotEmpty(orgListDTO.getNeatenEndTimeprx())) {
                condition = condition.and(field(name("ccp_org_slack","neaten_endtime")).le(orgListDTO.getNeatenEndTimeprx()));
            }

        }


        SelectSeekStep1 record7s = DbUtil.DSL_CONTEXT.select(
                        field(name("ccp_org_slack", "code"))
                        , field(name("ccp_org", "name"))
                        , field(name("ccp_org_slack", "d74_code"))
                        , field(name("ccp_org_slack", "d74_name"))
                        , field(name("ccp_org_slack", "neaten_time"))
                        , field(name("ccp_org_slack", "has_neaten"))
                        , field(name("ccp_org_slack", "create_time"))
                        , field(name("ccp_org_slack", "id"))
                        , field(name("ccp_org_slack", "zb_code"))
                        , field(name("ccp_org_slack", "org_code"))
                        , field(name("ccp_org_slack", "slack_org_code"))
                        , field(name("ccp_org_slack", "neaten_endtime"))
                ).from(name("ccp_org_slack")).innerJoin(table(name("ccp_org")))
                .on(field(name("ccp_org", "code")).eq(field(name("ccp_org_slack", "org_code"))).and(field(name("ccp_org", "delete_time")).isNull()))
                .where(condition)
                .orderBy(field(name("ccp_org_slack", "create_time")).desc());
        Page<OrgSlackListVo> page = new Page<>(orgListDTO.getPageNum(), orgListDTO.getPageSize());
        return orgSlackMapper.getListPage(page, record7s.toString());
    }


    @Override
    public OutMessage<?> addSlackOrg(OrgSlackDto orgDTO) {
//        String orgCode = orgDTO.getOrgCode();
//        OrgSlack orgSlack = this.findByOrgCode(orgCode);
//        if (Objects.nonNull(orgSlack)) {
//            return new OutMessage<>(Status.DO_NOT_REPEAT_ADD);
//        }
        OrgSlack slack = new OrgSlack();
        BeanUtils.copyProperties(orgDTO, slack);
        slack.setCreateTime(new Date());
        slack.setCode(StrKit.getRandomUUID());
        this.setModelFieldValue(slack);
        boolean save = this.save(slack);
        if (save) {
            ThreadUtil.execAsync(() -> syncOrgSlackService.syncOrgSlack(slack.getCode()));
        }
        return new OutMessage<>(save ? Status.SUCCESS : Status.FAIL);
    }

    @Override
    public OutMessage<?> updateSlackOrg(OrgSlackDto orgDTO) {
        OrgSlack orgSlack = this.findCode(orgDTO.getCode());
        if (Objects.isNull(orgSlack)) {
            return new OutMessage<>(Status.OBJEC_NOT_EXIST);
        }
        //当前组织涣散信息是否存在
//        if (!StrUtil.equals(orgSlack.getOrgCode(), orgDTO.getOrgCode())) {
//            OrgSlack slack = this.findByOrgCode(orgDTO.getOrgCode());
//            if (Objects.nonNull(slack)) {
//                return new OutMessage<>(Status.DO_NOT_REPEAT_ADD);
//            }
//        }
        boolean startGtEnd = (Objects.nonNull(orgDTO.getNeatenEndTime()) && Objects.nonNull(orgSlack.getNeatenTime()) && orgDTO.getNeatenEndTime().before(orgSlack.getNeatenTime())) ||
                (Objects.nonNull(orgDTO.getNeatenTime()) && Objects.nonNull(orgSlack.getNeatenEndTime()) && orgDTO.getNeatenTime().after(orgSlack.getNeatenEndTime()));
        if (startGtEnd) {
            return new OutMessage<>(Status.START_GT_END);
        }
        BeanUtils.copyProperties(orgDTO, orgSlack);
        orgSlack.setUpdateTime(new Date());
        this.setModelFieldValue(orgSlack);
        boolean b = this.updateById(orgSlack);
        if (b) {
            ThreadUtil.execAsync(() -> syncOrgSlackService.syncOrgSlack(orgSlack.getCode()));
        }
        return new OutMessage<>(b ? Status.SUCCESS : Status.FAIL);
    }

    @Override
    public OutMessage<?> delSlackOrg(String code) {
        OrgSlack orgSlack = this.findCode(code);
        if (Objects.isNull(orgSlack)) {
            return new OutMessage<>(Status.OBJEC_NOT_EXIST);
        }
        orgSlack.setDeleteTime(new Date());
        boolean b = this.updateById(orgSlack);
        if (b) {
            ThreadUtil.execAsync(() -> syncOrgSlackService.syncOrgSlack(orgSlack.getCode()));
        }
        return new OutMessage<>(b ? Status.SUCCESS : Status.FAIL);
    }


    private void setModelFieldValue(OrgSlack orgSlack) {
//        Integer lian = orgSlack.getLianVillageLeaderNum();
//        Integer bao = orgSlack.getBaoVillageLeaderNum();
//        Integer select = orgSlack.getSelectSecretaryNum();
//        Integer twinning = orgSlack.getTwinningUnitNum();
//        Integer twoLevel = orgSlack.getTwoLevelListingVillageNum();
//        boolean hasNeaten = (Objects.nonNull(lian) && lian > 0) || (Objects.nonNull(bao) && bao > 0) || (Objects.nonNull(select) && select > 0)
//                || (Objects.nonNull(twinning) && twinning > 0) || (Objects.nonNull(twoLevel) && twoLevel > 0);
//        //是否已整顿
//        if (hasNeaten) {
//            orgSlack.setHasNeaten("1");
//        } else {
//            orgSlack.setHasNeaten("0");
//        }
        //涣散类型名称
        if (StrUtil.isNotEmpty(orgSlack.getD74Code())) {
            List<Record> dictD74 = CacheUtils.getDic("dict_d74");
            Map<String, String> toMap = CollectionUtil.listRecordToMap(dictD74, "key", "name");
            List<String> stringList = Arrays.stream(orgSlack.getD74Code().split(",")).map(toMap::get).collect(Collectors.toList());
            orgSlack.setD74Name(CollUtil.join(stringList, ","));
        }
        Org org = orgService.findOrgByCode(orgSlack.getOrgCode());
        orgSlack.setSlackOrgCode(Objects.nonNull(org) ? org.getOrgCode() : "");
    }


    @Override
    public OutMessage<?> export(OrgSlackListDto orgListDTO) throws Exception {
        orgListDTO.setPageNum(1);
        orgListDTO.setPageSize(10000);
        Page<OrgSlackListVo> data = (Page<OrgSlackListVo>) this.getList(orgListDTO).getData();
        List<OrgSlackListExcel> excelList = new ArrayList<>();
        if (cn.hutool.core.collection.CollectionUtil.isNotEmpty(data.getRecords())) {
            for (OrgSlackListVo record : data.getRecords()) {
                OrgSlackListExcel excel = new OrgSlackListExcel();
                BeanUtils.copyProperties(record, excel);
                excel.setHasNeaten(CommonConstant.ONE.equals(record.getHasNeaten()) ? "是" : "否");
                excelList.add(excel);
            }
        }
        FileInputStream in = new FileInputStream(ResourceUtils.getURL(ResourceUtils.CLASSPATH_URL_PREFIX).getPath() + "public/excelftl/orgSlackExcel.xlsx");
        ExcelExportUtil<OrgSlackListExcel> excelExportUtil = new ExcelExportUtil<>(OrgSlackListExcel.class, 1, 0);
        File result = excelExportUtil.exportFileXlsx(in, excelList, cn.hutool.core.date.DateUtil.format(new Date(), "yyyyMMddHHmmssSSS") + ".xlsx");
        in.close();
        Map<String, String> map = new HashMap<>(5);
        map.put("url", "/public/export/" + result.getName());
        return new OutMessage<>(Status.SUCCESS, map);
    }

    @Override
    public List<OrgSlack> findOrgSlackByOrgCode(String orgCode) {
        return list(
                new LambdaQueryWrapper<OrgSlack>()
                        .isNull(OrgSlack::getDeleteTime)
                        .eq(OrgSlack::getOrgCode, orgCode)
                        .orderByDesc(OrgSlack::getCreateTime)
        );
    }

    @Override
    public List<OrgSlack> findSlackByOrgCode(String orgCode) {
        return list(new LambdaQueryWrapper<OrgSlack>().select(OrgSlack::getCode).eq(OrgSlack::getOrgCode, orgCode).isNull(OrgSlack::getDeleteTime).apply("to_char(neaten_time,'yyyy')='" + iStatisticsYearService.getStatisticalYear() + "'"));
    }


}
