package com.zenith.front.core.analysis.count.year2023;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.b1809.analysis.entity.PeggingPara;
import com.b1809.analysis.result.ReportResult;
import com.b1809.analysis.table.helper.TableHelper;
import com.b1809.analysis.table.helper.factory.TableHelperFactory;
import com.jfinal.plugin.activerecord.Db;
import com.jfinal.plugin.activerecord.Page;
import com.jfinal.plugin.activerecord.Record;
import com.zenith.front.core.analysis.ext.condition.year2023.MemAllCondition2023;
import com.zenith.front.core.analysis.ext.condition.year2023.OrgAllCondition2023;
import com.zenith.front.core.analysis.ext.condition.year2023.UnitAllCondition2023;
import org.jooq.Condition;
import org.jooq.Record1;
import org.jooq.SelectHavingStep;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static com.b1809.analysis.constant.AnalysisConstant.DSL_CONTEXT;
import static org.jooq.impl.DSL.*;

/**
 * 科研院所事业法人单位建立党的基层组织情况
 */
public class Html32Count2023 {

    public Condition getMemListCondition(String orgCode, String orgLevelCode) {
        return noCondition().and("delete_time is null and d08_code in ('1','2') and (is_transfer is null or is_transfer!=1) and d04_code like '32%' and d09_code like '0%'").and(new MemAllCondition2023().create(orgCode, orgLevelCode));
    }

    public Condition getUnitListCondition(String orgCode, String orgLevelCode) {
        return noCondition().and("delete_time is null and (d04_code like '32%')").and(new UnitAllCondition2023().create(orgCode, orgLevelCode));
    }

    public Map<String, Object> getCheckHtml29(PeggingPara data) {
        Map<String, Object> resultMap = new HashMap<>();
        TableHelper tableHelper = TableHelperFactory.getHelper();
        String rowIndex = data.getRowIndex();
        if (StrUtil.equals(rowIndex, "7")) {
            Condition condition = this.getOrg29Condition(data.getOrgCode(), data.getOrgLevelCode()).and(this.getOrgCheck29Condition(data.getColIndex()));
            OrgAllCondition2023 orgAllCond = new OrgAllCondition2023();
            return Html52Count2023.getReportPageResult(data, orgAllCond.getTableName(), condition, orgAllCond.getLevelCodeField());
        }
        Page<Map<String, Object>> page = tableHelper.findPage(data.getPageNum(), data.getPageSize(), "ccp_mem_all", new Html32Count2023().getMemCheckListCondition(data.getOrgCode(), data.getOrgLevelCode(), data.getColIndex(), data.getRowIndex()),
                data.getIncludeFieldList(), data.getOrderByFieldList());
        return ReportResult.toResult(resultMap, page, "ccp_mem_all");
    }

    public Condition getOrg29Condition(String orgCode, String orgLevelCode) {
        return new OrgAllCondition2023().create(orgCode, orgLevelCode).and("delete_time is null and (is_dissolve is null or is_dissolve !=1) and d01_code in('632','634','932') and d04_code like '32%' and has_unit_own_level='1'");
    }

    public Condition getOrgCheck29Condition(String colIndex) {
        Condition condition = noCondition();
        if (StrUtil.equals(colIndex, "1")) {
        } else if (StrUtil.equals(colIndex, "2")) {
            condition = condition.and("d04_code like '321%'");
        } else if (StrUtil.equals(colIndex, "3")) {
            condition = condition.and("d04_code like '3221%'");
        } else if (StrUtil.equals(colIndex, "4")) {
            condition = condition.and("d04_code like '3212%'");
        } else if (StrUtil.equals(colIndex, "5")) {
            condition = condition.and("d04_code like '3219%'");
        } else if (StrUtil.equals(colIndex, "6")) {
            condition = condition.and("d04_code like '321%' and d35_code like '1%'");
        } else if (StrUtil.equals(colIndex, "7")) {
            condition = condition.and("d04_code like '321%' and (d35_code like '2%' or d35_code like '3%')");
        } else if (StrUtil.equals(colIndex, "8")) {
            condition = condition.and("d04_code like '321%' and (d35_code like '4%' or d35_code like '5%')");
        } else if (StrUtil.equals(colIndex, "9")) {
            condition = condition.and("d04_code like '321%' and d35_code like '7%'");
        } else if (StrUtil.equals(colIndex, "10")) {
            condition = condition.and("d04_code like '322%'");
        } else if (StrUtil.equals(colIndex, "11")) {
            condition = condition.and("d04_code like '3221%'");
        } else if (StrUtil.equals(colIndex, "12")) {
            condition = condition.and("d04_code like '3222%'");
        } else if (StrUtil.equals(colIndex, "13")) {
            condition = condition.and("d04_code like '3229%'");
        } else if (StrUtil.equals(colIndex, "14")) {
            condition = condition.and("d04_code like '322%' and d35_code like '1%'");
        } else if (StrUtil.equals(colIndex, "15")) {
            condition = condition.and("d04_code like '322%' and (d35_code like '2%' or d35_code like '3%')");
        } else if (StrUtil.equals(colIndex, "16")) {
            condition = condition.and("d04_code like '322%' and (d35_code like '4%' or d35_code like '5%')");
        } else if (StrUtil.equals(colIndex, "17")) {
            condition = condition.and("d04_code like '322%' and d35_code like '7%'");
        }
        return condition;
    }

    Condition getMemCheckListCondition(String orgCode, String orgLevelCode, String colIndex, String rowIndex) {
        if ("12".equals(rowIndex)) {
            if ("1".equals(colIndex)) {
                return noCondition().and("delete_time is null and d08_code in ('1','2') and (is_transfer is null or is_transfer!=1) and d04_code like '32%' and d09_code like '0%'").and(new MemAllCondition2023().create(orgCode, orgLevelCode));
            }
            if ("2".equals(colIndex)) {
                return noCondition().and("delete_time is null and d08_code in ('1','2') and (is_transfer is null or is_transfer!=1) and d04_code like '321%' and d09_code like '0%'").and(new MemAllCondition2023().create(orgCode, orgLevelCode));
            }
            if ("3".equals(colIndex)) {
                return noCondition().and("delete_time is null and d08_code in ('1','2') and (is_transfer is null or is_transfer!=1) and d04_code='3211' and d09_code like '0%'").and(new MemAllCondition2023().create(orgCode, orgLevelCode));
            }
            if ("4".equals(colIndex)) {
                return noCondition().and("delete_time is null and d08_code in ('1','2') and (is_transfer is null or is_transfer!=1) and d04_code='3212' and d09_code like '0%'").and(new MemAllCondition2023().create(orgCode, orgLevelCode));
            }
            if ("5".equals(colIndex)) {
                return noCondition().and("delete_time is null and d08_code in ('1','2') and (is_transfer is null or is_transfer!=1) and d04_code='3219' and d09_code like '0%'").and(new MemAllCondition2023().create(orgCode, orgLevelCode));
            }
            if ("6".equals(colIndex)) {
                return noCondition().and("delete_time is null and d08_code in ('1','2') and (is_transfer is null or is_transfer!=1) and d04_code like '321%' and d09_code like '0%' and d35_code like '1%'").and(new MemAllCondition2023().create(orgCode, orgLevelCode));
            }
            if ("7".equals(colIndex)) {
                return noCondition().and("delete_time is null and d08_code in ('1','2') and (is_transfer is null or is_transfer!=1) and d04_code like '321%' and d09_code like '0%' and (d35_code like '2%' or d35_code like '3%')").and(new MemAllCondition2023().create(orgCode, orgLevelCode));
            }
            if ("8".equals(colIndex)) {
                return noCondition().and("delete_time is null and d08_code in ('1','2') and (is_transfer is null or is_transfer!=1) and d04_code like '321%' and d09_code like '0%' and (d35_code like '4%' or d35_code like '5%')").and(new MemAllCondition2023().create(orgCode, orgLevelCode));
            }
            if ("9".equals(colIndex)) {
                return noCondition().and("delete_time is null and d08_code in ('1','2') and (is_transfer is null or is_transfer!=1) and d04_code like '321%' and d09_code like '0%' and d35_code like '7%'").and(new MemAllCondition2023().create(orgCode, orgLevelCode));
            }
            if ("10".equals(colIndex)) {
                return noCondition().and("delete_time is null and d08_code in ('1','2') and (is_transfer is null or is_transfer!=1) and d04_code like '322%' and d09_code like '0%'").and(new MemAllCondition2023().create(orgCode, orgLevelCode));
            }
            if ("11".equals(colIndex)) {
                return noCondition().and("delete_time is null and d08_code in ('1','2') and (is_transfer is null or is_transfer!=1) and d04_code='3221' and d09_code like '0%'").and(new MemAllCondition2023().create(orgCode, orgLevelCode));
            }
            if ("12".equals(colIndex)) {
                return noCondition().and("delete_time is null and d08_code in ('1','2') and (is_transfer is null or is_transfer!=1) and d04_code='3222' and d09_code like '0%'").and(new MemAllCondition2023().create(orgCode, orgLevelCode));
            }
            if ("13".equals(colIndex)) {
                return noCondition().and("delete_time is null and d08_code in ('1','2') and (is_transfer is null or is_transfer!=1) and d04_code='3229' and d09_code like '0%'").and(new MemAllCondition2023().create(orgCode, orgLevelCode));
            }
            if ("14".equals(colIndex)) {
                return noCondition().and("delete_time is null and d08_code in ('1','2') and (is_transfer is null or is_transfer!=1) and d04_code like '322%' and d09_code like '0%' and d35_code like '1%'").and(new MemAllCondition2023().create(orgCode, orgLevelCode));
            }
            if ("15".equals(colIndex)) {
                return noCondition().and("delete_time is null and d08_code in ('1','2') and (is_transfer is null or is_transfer!=1) and d04_code like '322%' and d09_code like '0%' and (d35_code like '2%' or d35_code like '3%')").and(new MemAllCondition2023().create(orgCode, orgLevelCode));
            }
            if ("16".equals(colIndex)) {
                return noCondition().and("delete_time is null and d08_code in ('1','2') and (is_transfer is null or is_transfer!=1) and d04_code='3229' and (d35_code like '4%' or d35_code like '5%') and d09_code like '0%'").and(new MemAllCondition2023().create(orgCode, orgLevelCode));
            }
            if ("17".equals(colIndex)) {
                return noCondition().and("delete_time is null and d08_code in ('1','2') and (is_transfer is null or is_transfer!=1) and d04_code='3229' and d35_code like '7%' and d09_code like '0%'").and(new MemAllCondition2023().create(orgCode, orgLevelCode));
            }
        } else {
            if ("1".equals(colIndex)) {
                return noCondition().and("delete_time is null and d08_code in ('1','2') and (is_transfer is null or is_transfer!=1) and d09_code like '013%' and d04_code like '32%'").and(new MemAllCondition2023().create(orgCode, orgLevelCode));
            }
            if ("2".equals(colIndex)) {
                return noCondition().and("delete_time is null and d08_code in ('1','2') and (is_transfer is null or is_transfer!=1) and d09_code like '013%' and d04_code like '321%'").and(new MemAllCondition2023().create(orgCode, orgLevelCode));
            }
            if ("3".equals(colIndex)) {
                return noCondition().and("delete_time is null and d08_code in ('1','2') and (is_transfer is null or is_transfer!=1) and d09_code like '013%' and d04_code='3211'").and(new MemAllCondition2023().create(orgCode, orgLevelCode));
            }
            if ("4".equals(colIndex)) {
                return noCondition().and("delete_time is null and d08_code in ('1','2') and (is_transfer is null or is_transfer!=1) and d09_code like '013%' and d04_code='3212'").and(new MemAllCondition2023().create(orgCode, orgLevelCode));
            }
            if ("5".equals(colIndex)) {
                return noCondition().and("delete_time is null and d08_code in ('1','2') and (is_transfer is null or is_transfer!=1) and d09_code like '013%' and d04_code='3219'").and(new MemAllCondition2023().create(orgCode, orgLevelCode));
            }
            if ("6".equals(colIndex)) {
                return noCondition().and("delete_time is null and d08_code in ('1','2') and (is_transfer is null or is_transfer!=1) and d09_code like '013%' and d04_code like '321%' and d35_code like '1%'").and(new MemAllCondition2023().create(orgCode, orgLevelCode));
            }
            if ("7".equals(colIndex)) {
                return noCondition().and("delete_time is null and d08_code in ('1','2') and (is_transfer is null or is_transfer!=1) and d09_code like '013%' and d04_code like '321%' and (d35_code like '2%' or d35_code like '3%')").and(new MemAllCondition2023().create(orgCode, orgLevelCode));
            }
            if ("8".equals(colIndex)) {
                return noCondition().and("delete_time is null and d08_code in ('1','2') and (is_transfer is null or is_transfer!=1) and d09_code like '013%' and d04_code like '321%' and (d35_code like '4%' or d35_code like '5%')").and(new MemAllCondition2023().create(orgCode, orgLevelCode));
            }
            if ("9".equals(colIndex)) {
                return noCondition().and("delete_time is null and d08_code in ('1','2') and (is_transfer is null or is_transfer!=1) and d09_code like '013%' and d04_code like '321%' and d35_code like '7%'").and(new MemAllCondition2023().create(orgCode, orgLevelCode));
            }
            if ("10".equals(colIndex)) {
                return noCondition().and("delete_time is null and d08_code in ('1','2') and (is_transfer is null or is_transfer!=1) and d09_code like '013%' and d04_code like '322%'").and(new MemAllCondition2023().create(orgCode, orgLevelCode));
            }
            if ("11".equals(colIndex)) {
                return noCondition().and("delete_time is null and d08_code in ('1','2') and (is_transfer is null or is_transfer!=1) and d09_code like '013%' and d04_code='3221'").and(new MemAllCondition2023().create(orgCode, orgLevelCode));
            }
            if ("12".equals(colIndex)) {
                return noCondition().and("delete_time is null and d08_code in ('1','2') and (is_transfer is null or is_transfer!=1) and d09_code like '013%' and d04_code='3222'").and(new MemAllCondition2023().create(orgCode, orgLevelCode));
            }
            if ("13".equals(colIndex)) {
                return noCondition().and("delete_time is null and d08_code in ('1','2') and (is_transfer is null or is_transfer!=1) and d09_code like '013%' and d04_code='3229'").and(new MemAllCondition2023().create(orgCode, orgLevelCode));
            }
            if ("14".equals(colIndex)) {
                return noCondition().and("delete_time is null and d08_code in ('1','2') and (is_transfer is null or is_transfer!=1) and d09_code like '013%' and d04_code like '322%' and d35_code like '1%'").and(new MemAllCondition2023().create(orgCode, orgLevelCode));
            }
            if ("15".equals(colIndex)) {
                return noCondition().and("delete_time is null and d08_code in ('1','2') and (is_transfer is null or is_transfer!=1) and d09_code like '013%' and d04_code like '322%' and (d35_code like '2%' or d35_code like '3%')").and(new MemAllCondition2023().create(orgCode, orgLevelCode));
            }
            if ("16".equals(colIndex)) {
                return noCondition().and("delete_time is null and d08_code in ('1','2') and (is_transfer is null or is_transfer!=1) and d09_code like '013%' and d04_code='3229' and (d35_code like '4%' or d35_code like '5%')").and(new MemAllCondition2023().create(orgCode, orgLevelCode));
            }
            if ("17".equals(colIndex)) {
                return noCondition().and("delete_time is null and d08_code in ('1','2') and (is_transfer is null or is_transfer!=1) and d09_code like '013%' and d04_code='3229' and d35_code like '7%'").and(new MemAllCondition2023().create(orgCode, orgLevelCode));
            }
        }
        return noCondition();
    }

    /**
     * 实行院长所负责制
     *
     * @param data
     * @return
     */
    public Map<String, Object> getCheckHtml29_5(PeggingPara data) {
//        Map<String, Object> resultMap = new HashMap<>();
//        Page<Map<String, Object>> page = this.setCheckHtml29_5(data.getPageNum(), data.getPageSize(), data.getOrgLevelCode(), data.getColIndex(), data.getRowIndex(), null);
//        Map<String, Object> reportPeggingMap = ReportResult.toResult(resultMap, page, "ccp_unit_all");
//        return reportPeggingMap;

        Map<String, Object> resultMap = new HashMap<>();
        TableHelper tableHelper = TableHelperFactory.getHelper();
        Condition condition = new Html32Count2023().getUnitCheckListCondition(data.getOrgCode(), data.getOrgLevelCode(), data.getColIndex()).and("is_legal =1 and d02_code='1'");
        if (StrUtil.equals(data.getRowIndex(), "5")) {
            condition = condition.and("d112_code ='1'");
        } else {
            condition = condition.and("d112_code ='2'");
        }
        Page<Map<String, Object>> page = tableHelper.findPage(data.getPageNum(), data.getPageSize(), "ccp_unit_all", condition,
                data.getIncludeFieldList(), data.getOrderByFieldList());
        return ReportResult.toResult(resultMap, page, "ccp_unit_all");
    }

    public Page<Map<String, Object>> setCheckHtml29_5(Integer pageNum, Integer pageSize, String orgLevelCode, String colIndex, String rowIndex, String tableYear) {
        Condition condition = noCondition();
        if ("2".equals(colIndex)) {
            condition.and(field(name("ccp_unit_all", "d04_code")).like("321%"));
        }
        if ("3".equals(colIndex)) {
            condition.and(field(name("ccp_unit_all", "d04_code")).like("3221%"));
        }
        if ("4".equals(colIndex)) {
            condition.and(field(name("ccp_unit_all", "d04_code")).like("3212%"));
        }
        if ("5".equals(colIndex)) {
            condition.and(field(name("ccp_unit_all", "d04_code")).like("3219%"));
        }
        if ("6".equals(colIndex)) {
            condition.and(field(name("ccp_unit_all", "d04_code")).like("321%").and(field(name("ccp_unit_all", "d35_code")).like("1%")));
        }
        if ("7".equals(colIndex)) {
            condition.and(field(name("ccp_unit_all", "d04_code")).like("321%").and(field(name("ccp_unit_all", "d35_code")).in("2", "21", "22", "23", "24", "25", "26", "27", "28", "29", "2A", "3", "31", "32")));
        }
        if ("8".equals(colIndex)) {
            condition.and(field(name("ccp_unit_all", "d04_code")).like("321%").and(field(name("ccp_unit_all", "d35_code")).in("4", "41", "42", "43", "44", "45", "5", "6", "61")));
        }
        if ("9".equals(colIndex)) {
            condition.and(field(name("ccp_unit_all", "d04_code")).like("321%").and(field(name("ccp_unit_all", "d35_code")).like("7%")));
        }
        if ("10".equals(colIndex)) {
            condition.and(field(name("ccp_unit_all", "d04_code")).like("322%"));
        }
        if ("11".equals(colIndex)) {
            condition.and(field(name("ccp_unit_all", "d04_code")).like("3221%"));
        }
        if ("12".equals(colIndex)) {
            condition.and(field(name("ccp_unit_all", "d04_code")).like("3222%"));
        }
        if ("13".equals(colIndex)) {
            condition.and(field(name("ccp_unit_all", "d04_code")).like("3229%"));
        }
        if ("14".equals(colIndex)) {
            condition.and(field(name("ccp_unit_all", "d04_code")).like("322%")).and(field(name("ccp_unit_all", "d35_code")).like("1%"));
        }
        if ("15".equals(colIndex)) {
            condition.and(field(name("ccp_unit_all", "d04_code")).like("322%")).and(field(name("ccp_unit_all", "d35_code")).in("2", "21", "22", "23", "24", "25", "26", "27", "28", "29", "2A", "3", "31", "32"));
        }
        if ("16".equals(colIndex)) {
            condition.and(field(name("ccp_unit_all", "d04_code")).like("322%")).and(field(name("ccp_unit_all", "d35_code")).in("4", "41", "42", "43", "44", "45", "5", "6", "61"));
        }
        if ("17".equals(colIndex)) {
            condition.and(field(name("ccp_unit_all", "d04_code")).like("322%")).and(field(name("ccp_unit_all", "d35_code")).like("7%"));
        }
        return getMapPage(pageNum, pageSize, orgLevelCode, condition, rowIndex, tableYear);
    }

    private Page<Map<String, Object>> getMapPage(Integer pageNum, Integer pageSize, String orgLevelCode, Condition condition, String rowIndex, String tableYear) {
        String ccpUnitAll = StrUtil.isEmpty(tableYear) ? "ccp_unit_all" : "ccp_unit_all_" + tableYear;
        String ccpOrgAll = StrUtil.isEmpty(tableYear) ? "ccp_org_all" : "ccp_org_all_" + tableYear;
        SelectHavingStep<Record1<Object>> sqlStr = DSL_CONTEXT.select(field("\"ccp_unit_all\".*")).from(table(name(ccpUnitAll)).as("ccp_unit_all"))
                .leftJoin(table(name(ccpOrgAll)).as("ccp_org_all")).on(field(name("ccp_unit_all", "main_org_code"))
                        .eq(field(name("ccp_org_all", "code"))))
                .where(field(name("ccp_unit_all", "delete_time")).isNull())
                .and(field(name("ccp_org_all", "delete_time")).isNull())
                .and(field(name("ccp_unit_all", "d04_code")).like("32%"))
                .and(field(name("ccp_unit_all", "is_legal"), Integer.class).eq(1))
                .and(field(name("ccp_unit_all", "create_unit_org_code")).like(orgLevelCode + "%"))
                .and(field(name("ccp_unit_all", "d112_code"), String.class).eq("5".equals(rowIndex) ? "1" : "2"))
                .and(field(name("ccp_org_all", "d02_code"), String.class).eq("1"))
                .and(condition)
                .groupBy(field(name("ccp_unit_all", "id")));


        SelectHavingStep<Record1<Integer>> record1s = DSL_CONTEXT.selectCount().from(table(name(ccpUnitAll)).as("ccp_unit_all"))
                .leftJoin(table(name(ccpOrgAll)).as("ccp_org_all")).on(field(name("ccp_unit_all", "main_org_code"))
                        .eq(field(name("ccp_org_all", "code"))))
                .where(field(name("ccp_unit_all", "delete_time")).isNull())
                .and(field(name("ccp_org_all", "delete_time")).isNull())
                .and(field(name("ccp_unit_all", "d04_code")).like("32%"))
                .and(field(name("ccp_unit_all", "is_legal"), Integer.class).eq(1))
                .and(field(name("ccp_unit_all", "create_unit_org_code")).like(orgLevelCode + "%"))
                .and(field(name("ccp_unit_all", "d112_code"), String.class).eq("5".equals(rowIndex) ? "1" : "2"))
                .and(field(name("ccp_org_all", "d02_code"), String.class).eq("1"))
                .and(condition)
                .groupBy(field(name("ccp_unit_all", "id")));
        final Page<Record> recordPage = Db.paginateByFullSql(pageNum, pageSize, record1s.getSQL(), sqlStr.getSQL(), sqlStr.getBindValues().toArray());
        List<Map<String, Object>> mapList = new ArrayList<>();
        if (CollUtil.isNotEmpty(recordPage.getList())) {
            recordPage.getList().forEach(record -> mapList.add(record.getColumns()));
        }
        Page<Map<String, Object>> mapPage = new Page<>();
        mapPage.setList(mapList);
        mapPage.setTotalPage(recordPage.getTotalPage());
        mapPage.setTotalRow(recordPage.getTotalRow());
        mapPage.setPageNumber(recordPage.getPageNumber());
        mapPage.setPageSize(recordPage.getPageSize());
        return mapPage;
    }

    public Map<String, Object> getCheckHtml29_13(PeggingPara data) {
        Map<String, Object> resultMap = new HashMap<>();
        TableHelper tableHelper = TableHelperFactory.getHelper();
        Page<Map<String, Object>> page = tableHelper.findPage(data.getPageNum(), data.getPageSize(), "ccp_unit_all", new Html32Count2023().getUnitCheckListCondition(data.getOrgCode(), data.getOrgLevelCode(), data.getColIndex()),
                data.getIncludeFieldList(), data.getOrderByFieldList());
        return ReportResult.toResult(resultMap, page, "ccp_unit_all");
    }

    Condition getUnitCheckListCondition(String orgCode, String orgLevelCode, String colIndex) {
        Condition condition = noCondition().and("delete_time is null and (d04_code like '32%')");
        if ("2".equals(colIndex)) {
            condition = condition.and("d04_code like '321%'");
        }
        if ("3".equals(colIndex)) {
            condition = condition.and("d04_code like '3211%'");
        }
        if ("4".equals(colIndex)) {
            condition = condition.and("d04_code like '3212%'");
        }
        if ("5".equals(colIndex)) {
            condition = condition.and("d04_code like '3219%'");
        }
        if ("6".equals(colIndex)) {
            condition = condition.and("d04_code like '321%' and d35_code like '1%'");
        }
        if ("7".equals(colIndex)) {
            condition = condition.and("d04_code like '321%' and (d35_code like '2%' or d35_code like '3%')");
        }
        if ("8".equals(colIndex)) {
            condition = condition.and("d04_code like '321%' and (d35_code like '4%' or d35_code like '5%')");
        }
        if ("9".equals(colIndex)) {
            condition = condition.and("d04_code like '321%' and d35_code like '7%'");
        }
        if ("10".equals(colIndex)) {
            condition = condition.and("d04_code like '322%'");
        }
        if ("11".equals(colIndex)) {
            condition = condition.and("d04_code like '3221%'");
        }
        if ("12".equals(colIndex)) {
            condition = condition.and("d04_code like '3222%'");
        }
        if ("13".equals(colIndex)) {
            condition = condition.and("d04_code like '3229%'");
        }
        if ("14".equals(colIndex)) {
            condition = condition.and("d04_code like '322%' and d35_code like '1%'");
        }
        if ("15".equals(colIndex)) {
            condition = condition.and("d04_code like '322%' and (d35_code like '2%' or d35_code like '3%')");
        }
        if ("16".equals(colIndex)) {
            condition = condition.and("d04_code like '322%' and (d35_code like '4%' or d35_code like '5%')");
        }
        if ("17".equals(colIndex)) {
            condition = condition.and("d04_code like '322%' and d35_code like '7%'");
        }
        condition = condition.and(new UnitAllCondition2023().create(orgCode, orgLevelCode));
        return condition;
    }
}
