package com.zenith.front.core.service.activity;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.crypto.SecureUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.zenith.front.api.activity.IActivityMemService;
import com.zenith.front.api.activity.IActivityService;
import com.zenith.front.common.constant.ActivityConstant;
import com.zenith.front.common.constant.CommonConstant;
import com.zenith.front.common.kit.JackSonUtil;
import com.zenith.front.common.kit.ModelUtils;
import com.zenith.front.common.kit.StrKit;
import com.zenith.front.core.cache.CacheUtils;
import com.zenith.front.model.custom.Record;
import com.zenith.front.dao.mapper.activity.ActivityMapper;
import com.zenith.front.dao.mapper.mem.MemMapper;
import com.zenith.front.dao.mapper.org.OrgMapper;
import com.zenith.front.model.dto.ActivityDTO;
import com.zenith.front.model.dto.ActivityMemDTO;
import com.zenith.front.model.dto.LedgerDTO;
import com.zenith.front.model.message.OutMessage;
import com.zenith.front.model.message.Status;
import com.zenith.front.model.bean.Activity;
import com.zenith.front.model.bean.ActivityMem;
import com.zenith.front.model.bean.Mem;
import com.zenith.front.model.bean.Org;
import com.zenith.front.model.vo.ActivityVo;
import com.zenith.front.model.vo.HreMeetingsVO;
import com.zenith.front.model.vo.SituationActivityVO;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.io.IOException;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <p>
 * 活动表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-07-10
 */
@Service
public class ActivityServiceImpl extends ServiceImpl<ActivityMapper, Activity> implements IActivityService {

    @Resource
    private ActivityMapper activityMapper;
    @Resource
    private IActivityMemService activityMemService;
    @Resource
    private IActivityService activityService;
    @Resource
    private MemMapper memMapper;
    @Resource
    private OrgMapper orgMapper;

    @Override
    public OutMessage addAc(ActivityDTO data) {
        //生成唯一标识符
        String activityCode = StrKit.getRandomUUID();
        data.setCode(activityCode);
        Map<String, ActivityMemDTO> memDTOMap = processMemDTO(data);
        List<ActivityMemDTO> mem_saveList = new ArrayList<>(memDTOMap.values());
        List<ActivityMem> activityMems = activityMemTOModel(mem_saveList, activityCode);

        //处理通知程序
        String noticePlan = data.getNoticePlan();
        System.out.println("处理通知方式：" + noticePlan);

        //增加录入时活动所在组织标识符集合
        System.out.println("增加发布时所在标识符集合：");
        Activity activity = data.toModel();
        activity.setCreateTime(new Date());
        activity.setCreatorAccount("创建账号");
//        activity.remove("id");
//        activity.remove("is_open");
//        activity.remove("view_count");
//        activity.remove("good_count");
        boolean save = this.save(activity);
        //批量保存人员信息
        boolean batchSaveMem = activityMemService.saveBatch(activityMems);
        return save && batchSaveMem ? new OutMessage<>(Status.SUCCESS) : new OutMessage<>(Status.FAIL);
    }

    private Map<String, ActivityMemDTO> processMemDTO(ActivityDTO data) {
        //处理活动的所有人员集合
        Map<String, ActivityMemDTO> saveMap = new HashMap<>(20);

        List<ActivityMemDTO> leaderList = data.getLeaderList();//领导人员集合
        Map<String, ActivityMemDTO> leaderMap = processList(leaderList, saveMap, ActivityConstant.leader);

        List<ActivityMemDTO> lecturerList = data.getLecturerList();//讲课人员集合
        Map<String, ActivityMemDTO> lecturerMap = processList(lecturerList, leaderMap, ActivityConstant.lecturer);

        List<ActivityMemDTO> memList = data.getMemList();//应到人员集合
        Map<String, ActivityMemDTO> processMap = processList(memList, lecturerMap, ActivityConstant.member);

        List<ActivityMemDTO> hostorList = data.getHostorList();//主持人集合
        Map<String, ActivityMemDTO> hostorMap = processList(hostorList, processMap, ActivityConstant.hostor);

        List<ActivityMemDTO> attendList = data.getAttendList();//列席人员集合
        Map<String, ActivityMemDTO> attendMap = processList(attendList, hostorMap, ActivityConstant.attend);

        List<ActivityMemDTO> absenceList = data.getAbsenceList();//缺席人员集合
        Map<String, ActivityMemDTO> absenceMap = processList(absenceList, attendMap, ActivityConstant.absence);

        //处理人员集合，生成到人员表中去，并且生成相关的数据
        data.setLeaderCount(null == leaderList ? 0 : leaderList.size());
        data.setLecturerCount(null == lecturerList ? 0 : lecturerList.size());
        data.setHostorCount(null == hostorList ? 0 : hostorList.size());
        data.setMemCount(null == memList ? 0 : memList.size());
        data.setAbsenceCount(null == absenceList ? 0 : absenceList.size());
        data.setAttendCount(null == attendList ? 0 : attendList.size());
        return absenceMap;
    }

    private Map<String, ActivityMemDTO> processList(List<ActivityMemDTO> list, Map<String, ActivityMemDTO> saveMap, String type) {
        if (ObjectUtil.isNull(list)) {
            return saveMap;
        }
        Set<String> idcardSet = saveMap.keySet();
        //处理人员到map集合
        for (ActivityMemDTO activityMemDTO : list) {
            String memCode = activityMemDTO.getMemCode();
            if (StrKit.isBlank(memCode)) {
                memCode = SecureUtil.md5(activityMemDTO.getName());
            }
            if (idcardSet.contains(memCode)) {
                ActivityMemDTO activityMemDTOOld = saveMap.get(memCode);
                activityMemDTOOld.setReason(activityMemDTO.getReason());
                activityMemDTO = activityMemDTOOld;//更新存在key的数据
            }
            if (type.equals(ActivityConstant.hostor)) {
                activityMemDTO.setIsHostor(1);
            }
            if (type.equals(ActivityConstant.lecturer)) {
                activityMemDTO.setIsLecturer(1);
            }
            if (type.equals(ActivityConstant.leader)) {
                activityMemDTO.setIsLeader(1);
            }
            if (type.equals(ActivityConstant.member)) {
                activityMemDTO.setIsMem(1);
            }
            if (type.equals(ActivityConstant.attend)) {
                activityMemDTO.setIsAttend(1);
            }
            if (type.equals(ActivityConstant.absence)) {
                activityMemDTO.setIsAbsence(1);
            }
            saveMap.put(memCode, activityMemDTO.setMemCode(memCode));
        }
        return saveMap;
    }


    private List<ActivityMem> activityMemTOModel(List<ActivityMemDTO> list, String activityCode) {
        List<ActivityMem> listActivityMem = new ArrayList<>();
        for (ActivityMemDTO activityMemDTO : list) {
            ActivityMem e = activityMemDTO.toModel();
//            e.remove("id");
            String memCode = e.getMemCode();
            if (StrKit.isBlank(memCode)) {
                e.setIsReview(1);//如果是自定义人员，默认为已签到
            }
            Integer isAbsence = e.getIsAbsence();
            if (ObjectUtil.isNull(isAbsence)) {
                e.setIsAbsence(0);
            }
            Integer isAttend = e.getIsAttend();
            if (ObjectUtil.isNull(isAttend)) {
                e.setIsAttend(0);
            }
            String isCustomize = e.getIsCustomize();
            if (ObjectUtil.isNull(isCustomize) && StrKit.isBlank(memCode)) {
                e.setIsCustomize("0");
            }
            Integer isHostor = e.getIsHostor();
            if (ObjectUtil.isNull(isHostor)) {
                e.setIsHostor(0);
            }
            Integer isLeader = e.getIsLeader();
            if (ObjectUtil.isNull(isLeader)) {
                e.setIsLeader(0);
            }
            Integer isMem = e.getIsMem();
            if (ObjectUtil.isNull(isMem)) {
                e.setIsMem(0);
            }
            Integer isReview = e.getIsReview();
            if (ObjectUtil.isNull(isReview)) {
                e.setIsReview(0);
            }
            Integer isLecturer = e.getIsLecturer();
            if (ObjectUtil.isNull(isLecturer)) {
                e.setIsLecturer(0);
            }
            e.setCreateTime(new Date());
            e.setActivityCode(activityCode);
            listActivityMem.add(e);
        }
        return listActivityMem;
    }

    @Override
    public OutMessage findAcByCode(String code, String manageCode, Integer entryType, String memCode) throws Exception {
        //获取单个活动详情
        LambdaQueryWrapper<Activity> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(Activity::getCode, code)
                .isNull(Activity::getDeleteTime);
        Activity acByCode = getOne(queryWrapper);
        //校验活动code以及活动权限问题
        String acOrgOrgCode = acByCode.getAcOrgOrgCode();
        Integer isOpen = acByCode.getIsOpen();

        //如果是开放的,就可以广泛查看
        if (isOpen == 1) {
            return new OutMessage<>(Status.SUCCESS, findAcOne(acByCode));
        }

        //如果是PC或者微信管理端的请求,并且该活动是自己管理组织以下的活动,允许查看
        if ((entryType == 1 || entryType == 3) && acOrgOrgCode.startsWith(manageCode)) {
            return new OutMessage<>(Status.SUCCESS, findAcOne(acByCode));
        }

        //WX个人端过来的,如果是所在支部的活动,以及自己参加的活动,允许查看
        if (entryType == 2) {
            ActivityMem byCodeIdcard = activityMemService.getOne(new QueryWrapper<ActivityMem>().lambda().eq(ActivityMem::getActivityCode, code).eq(ActivityMem::getMemCode, memCode).isNull(ActivityMem::getDeleteTime));
            boolean isJoin = ObjectUtil.isNotNull(byCodeIdcard);
            if (acOrgOrgCode.startsWith(manageCode) || isJoin) {
                //代表用户可以查看该活动
                ActivityVo acOneVo = findAcOne(acByCode);
                Integer isReview = byCodeIdcard.getIsReview();
                Integer isAbsence = byCodeIdcard.getIsAbsence();
                if (isReview > 0) {
                    acOneVo.setIsReview(true);
                }
                if (isAbsence > 0) {
                    acOneVo.setIsAbsence(true);
                }
                return new OutMessage<>(Status.SUCCESS, acOneVo);
            }
        }
        return new OutMessage(Status.ACTIVITY_NOT_AUTH_SELECT);
    }

    @Override
    public OutMessage updateAc(ActivityDTO data, String manageCode) {
        String code = data.getCode();
        //判断是否能进行相关编辑权限(是否越级编辑,活动是否在进行中不允许编辑)
        //判断活动是否存在，或者是已经在进行中
        //获取单个活动详情
        Activity acByCode = activityMapper.selectOne(new QueryWrapper<Activity>().lambda().eq(Activity::getCode, code).isNull(Activity::getDeleteTime));
        if (ObjectUtil.isNull(acByCode)) {
            return new OutMessage<>(Status.ACTIVITY_NOT_PRESENCE);//活动不存在
        }
        if (!acByCode.getAcOrgOrgCode().startsWith(manageCode)) {
            return new OutMessage<>(Status.ACTIVITY_NOT_AUTH_EDIT);//无编辑权限
        }
        if (acByCode.getStatus() == 2) {
            return new OutMessage<>(Status.ACTIVITY_IS_CONDUCT);//活动正在进行中
        }


        //更新活动表 ,处理旧的人员比对新的人员，如果出现人员变更分为以下三种情况
        //1.新增的---补发参加通知
        //2.取消的---补发取消该活动通知
        //3.未变化的---不做任何通知
        //4.举办时间.议程.活动地点发生了变化---通知新的人员活动信息发生变更,注意查看的提醒


        //校验活动人员是否发生变化
        //新人员集合
        Map<String, ActivityMemDTO> newMemDTOMap = processMemDTO(data);
        Map<String, ActivityMemDTO> saveNewMemDTOMap = new HashMap<>(newMemDTOMap);
        //获取旧的人员集合
        Map<String, ActivityMemDTO> oldMemDtoMap = new Hashtable<>();
        List<ActivityMem> oldActivityMems = activityMemService.list(new QueryWrapper<ActivityMem>().lambda().eq(ActivityMem::getActivityCode, code).isNull(ActivityMem::getDeleteTime));
        oldActivityMems.forEach(oldActivityMem -> oldMemDtoMap.put(oldActivityMem.getMemCode(), oldActivityMem.toDTO()));
        Set<String> newIdCardsSet = newMemDTOMap.keySet();
        Iterator<String> oldIterator = oldMemDtoMap.keySet().iterator();
        while (oldIterator.hasNext()) {
            String oldIdCard = oldIterator.next();
            boolean contains = newIdCardsSet.contains(oldIdCard);
            if (contains) {//如果出现在了新的集合中,证明人员未发生变化,不需要进行通知
                oldIterator.remove();
                newMemDTOMap.remove(oldIdCard);
            }
        }
        if (MapUtil.isNotEmpty(oldMemDtoMap)) {//如果旧的集合中元素不为空,代表这些是被取消的人
            System.out.println("给旧的人发送取消该信息。");
        }
        if (MapUtil.isNotEmpty(newMemDTOMap)) {//如果新的集合中存在未被移除的元素，代表是新增的
            System.out.println("给新的人发送参加信息。");
        }


        //校验活动举办时间,举办地点,活动地点,是否发生变化
        String oldAddress = acByCode.getAddress();
        Date oldHoldTime = acByCode.getHoldTime();
        String newAddress = data.getAddress();
        Date newHoldTime = data.getHoldTime();
        boolean sameTime = DateUtil.isSameTime(newHoldTime, oldHoldTime);
        boolean equals = StrKit.equals(oldAddress, newAddress);
        if (!sameTime || !equals) {
            System.out.println("时间或者地点发生变更,通知所有人员。");
        }


        //操作更新数据
        Collection<ActivityMemDTO> activityMemDTOS = saveNewMemDTOMap.values();
        List<ActivityMemDTO> mem_saveList = new ArrayList<>(activityMemDTOS);
        List<ActivityMem> activityNewMems = activityMemTOModel(mem_saveList, code);
        Activity activity = data.toModel();
        activity.setIsOpen(null);
        activity.setViewCount(null);
        activity.setGoodCount(null);
        activity.setUpdateTime(new Date());


        //更新旧的活动参与人员
        LambdaUpdateWrapper<ActivityMem> updateWrapper = new UpdateWrapper<ActivityMem>().lambda().set(ActivityMem::getDeleteTime, new Date()).eq(ActivityMem::getActivityCode, code);
        boolean i = activityMemService.update(updateWrapper);
        //放入新的活动参与人员
        boolean newMems = activityMemService.saveBatch(activityNewMems);
        //更新活动信息
        int ac = activityMapper.updateById(activity);
        if (i && newMems && ac > 0) {
            return new OutMessage(Status.SUCCESS);
        } else {
            return new OutMessage(Status.FAIL);
        }
    }

    @Override
    public OutMessage cancelAc(String code, String manageOrgCode) {
        //校验是否有权限取消改活动
        //判断是否能进行相关编辑权限(是否越级编辑,活动是否在进行中不允许编辑)
        //判断活动是否存在，或者是已经在进行中
        Activity acByCode = activityMapper.selectOne(new QueryWrapper<Activity>().lambda().eq(Activity::getCode, code).isNull(Activity::getDeleteTime));
        if (ObjectUtil.isNull(acByCode)) {
            return new OutMessage<>(Status.ACTIVITY_NOT_PRESENCE);//活动不存在
        }
        if (!acByCode.getAcOrgOrgCode().startsWith(manageOrgCode)) {
            return new OutMessage<>(Status.ACTIVITY_NOT_AUTH_EDIT);//无编辑权限
        }
        if (acByCode.getStatus() == 2) {
            return new OutMessage<>(Status.ACTIVITY_IS_CONDUCT);//活动正在进行中
        }

        //撤销单个活动,并且给每个人发送活动取消通知
        //获取活动参与人员
        List<ActivityMem> activityMems = activityMemService.list(new QueryWrapper<ActivityMem>().lambda().eq(ActivityMem::getActivityCode, code).isNull(ActivityMem::getDeleteTime));
        System.out.println("取消活动发送取消信息。");


        //更新删除活动的人员信息
        LambdaUpdateWrapper<ActivityMem> updateWrapper = new UpdateWrapper<ActivityMem>().lambda().set(ActivityMem::getDeleteTime, new Date()).eq(ActivityMem::getActivityCode, code);
        boolean i = activityMemService.update(updateWrapper);

        //更新删除活动信息
        LambdaUpdateWrapper<Activity> acWrapper = new UpdateWrapper<Activity>().lambda().set(Activity::getDeleteTime, new Date()).eq(Activity::getCode, code);
        boolean update = activityService.update(acWrapper);
        if (i && update) {
            return new OutMessage(Status.SUCCESS);
        } else {
            return new OutMessage(Status.FAIL);
        }
    }

    @Override
    public OutMessage listAC(String mem_code, Integer pageNum, Integer pageSize, List<Integer> statusList, Integer entryType, String org_code, List<String> activityType, String activityName) {
        //entryType=1 代表是PC端请求列表       获取他自己的待开始，进行中，以及下面所有的已完成      所有返回
        //entryType=3 代表是微信管理端请求过来的 获取他自己的待开始，进行中，以及下面所有的已完成      所有反回
        //entryType=2 代表是微信个人端请求过来的 获取他自己的待开始，进行中，以及自己支部所有的已完成(存在极端情况,非他自己支部的让他参加的)   所有返回
        //status为空的时候    代表PC获取所有列表

        if (entryType == 2) {
            Mem memByCode = memMapper.selectOne(new QueryWrapper<Mem>().lambda().eq(Mem::getCode, mem_code).isNull(Mem::getDeleteTime));
            if (ObjectUtil.isNull(memByCode)) {
                return new OutMessage(Status.ACTIVITY_NOT_MEM);
            }
            org_code = memByCode.getMemOrgCode();
        }

        //根据状态获取相应的列表,如果是微信个人端,不仅仅是他自己组织的,还有他参加的活动
        if (entryType == 1) {
            //PC端请求，获取自己所有下级的已经完成的,还要获取自己的进行中的，以及未开始的

            LambdaQueryWrapper<Activity> wrapper = new QueryWrapper<Activity>().lambda();
            wrapper.likeRight(Activity::getAcOrgOrgCode, org_code).or().eq(Activity::getAcOrgOrgCode, org_code);
            wrapper.isNull(Activity::getDeleteTime);
            if (CollectionUtil.isNotEmpty(statusList)) {
                wrapper.in(Activity::getStatus, statusList);
            }
            if (CollectionUtil.isNotEmpty(activityType)) {

            }
            wrapper.orderByDesc(Activity::getCreateTime);
            Page<Activity> page = new Page<>(pageNum, pageSize);
            page = page(page, wrapper);
            return findList(page);
        }
        if (entryType == 2) {
            //微信个人端过来的请求,不仅要所在支部的活动,同时还要查询高于自己所在支部且自己参与的活动
            List<ActivityMem> memJoinByMemCode = activityMemService.list(new QueryWrapper<ActivityMem>().lambda().select(ActivityMem::getActivityCode).eq(ActivityMem::getMemCode, mem_code).isNull(ActivityMem::getDeleteTime).groupBy(ActivityMem::getActivityCode));
            ArrayList<String> listInCode = dealWithList(memJoinByMemCode);
            LambdaQueryWrapper<Activity> wrapper = new QueryWrapper<Activity>().lambda();
            wrapper.isNull(Activity::getDeleteTime);
            wrapper.in(Activity::getStatus, statusList);
            wrapper.likeRight(Activity::getAcOrgOrgCode, org_code).or().in(Activity::getCode, listInCode);
            if (ObjectUtil.isNotNull(activityType) && activityType.size() > 0) {
                //根据类型搜索
            }
            wrapper.orderByDesc(Activity::getCreateTime);
            Page<Activity> page = new Page<>(pageNum, pageSize);
            page = page(page, wrapper);
            return findList(page);
        }
        LambdaQueryWrapper<Activity> wrapper = new QueryWrapper<Activity>().lambda();
        wrapper.isNull(Activity::getDeleteTime);
        wrapper.in(Activity::getStatus, statusList);
        wrapper.likeRight(Activity::getAcOrgOrgCode, org_code);
        if (ObjectUtil.isNotNull(activityType) && activityType.size() > 0) {
            //根据类型搜索
        }
        wrapper.orderByDesc(Activity::getCreateTime);
        Page<Activity> page = new Page<>(pageNum, pageSize);
        page = page(page, wrapper);
        return findList(page);
    }

    @Override
    public Long getTotalByOrgCode(String orgCode) {
        LambdaQueryWrapper<Activity> wrapper = new LambdaQueryWrapper<Activity>()
                .likeRight(Activity::getAcOrgOrgCode, orgCode)
                .isNull(Activity::getDeleteTime);
        return Long.valueOf(activityMapper.selectCount(wrapper));
    }

    private OutMessage findList(Page<Activity> listByStatusAndOrg) {
        List<Activity> list = listByStatusAndOrg.getRecords();
        List<ActivityVo> listAcVo = new ArrayList<>();
        list.forEach(activity -> {
            ActivityVo acVO = new ActivityVo();
            try {
                ModelUtils.copyPropertiesToVO(activity, acVO);
                acVO.setOrgShortName(CacheUtils.getOrgShortName(activity.getAcOrgCode()));
                listAcVo.add(acVO);
            } catch (Exception e) {
                e.printStackTrace();
            }
        });

        Page<ActivityVo> ActivityVOPage = new Page<>();
        ActivityVOPage.setRecords(listAcVo);
        ActivityVOPage.setTotal(listByStatusAndOrg.getTotal());
        ActivityVOPage.setPages(listByStatusAndOrg.getPages());
        ActivityVOPage.setSize(listByStatusAndOrg.getSize());
        return new OutMessage<>(Status.SUCCESS, ActivityVOPage);
    }

    private ArrayList<String> dealWithList(List<ActivityMem> memJoinByMemCode) {
        if (ObjectUtil.isNull(memJoinByMemCode)) {
            return null;
        }
        ArrayList<String> list = new ArrayList<>();
        for (ActivityMem activityMem : memJoinByMemCode) {
            list.add(activityMem.getActivityCode());
        }
        return list;
    }

    private ActivityVo findAcOne(Activity acByCode) throws Exception {
        acByCode.setViewCount(acByCode.getViewCount() + 1);

        activityMapper.updateById(acByCode);
        ActivityVo returnActivity = new ActivityVo();
        ModelUtils.copyPropertiesToVO(acByCode, returnActivity);
        returnActivity.setOrgShortName(CacheUtils.getOrgShortName(returnActivity.getAcOrgCode()));
        Map<String, List<ActivityMem>> acMemByCode = activityMemService.findAcMemByCode(acByCode.getCode());

        //获取活动相关人员
        returnActivity.setAttendList(acMemByCode.get(ActivityConstant.attend));
        returnActivity.setMemList(acMemByCode.get(ActivityConstant.member));
        returnActivity.setHostorList(acMemByCode.get(ActivityConstant.hostor));
        returnActivity.setLeaderList(acMemByCode.get(ActivityConstant.leader));
        returnActivity.setAbsenceList(acMemByCode.get(ActivityConstant.absence));
        returnActivity.setLecturerList(acMemByCode.get(ActivityConstant.lecturer));

        //获取该活动录入类型是党委还是党支部
        String acOrgCode = acByCode.getAcOrgCode();
        Org org = orgMapper.selectOne(new QueryWrapper<Org>().lambda()
                .eq(Org::getCode, acOrgCode).isNull(Org::getDeleteTime));
        String orgType = null == org ? "无" : org.getOrgType();
        returnActivity.setOrgType(orgType);

        return returnActivity;
    }

    /**
     * 获取三会一课明细台账
     *
     * @param data
     * @return
     */
    @Override
    public OutMessage getHreMeetingsAndOneClass(LedgerDTO data) {
        String orgCode = data.getOrgCode();
        String orgName = data.getOrgName();
        Integer pageNum = data.getPageNum();
        Integer pageSize = data.getPageSize();
        boolean isLeaf = isLeaf(orgCode);
        int length = isLeaf ? orgCode.length() : orgCode.length() + 3;
        Page<HreMeetingsVO> page = new Page<>(pageNum, pageSize);
        Page<HreMeetingsVO> activityPage = activityMapper.getHreMeetingsAndOneClass(page, orgCode, isLeaf);
        if (activityPage.getPages() == 0) {
            return new OutMessage<>(Status.SUCCESS, activityPage);
        }
        List<HreMeetingsVO> recordList = activityPage.getRecords();
        // 主持人名称
        List<String> activityCodeList = recordList.stream().map(HreMeetingsVO::getCode).collect(Collectors.toList());
        List<ActivityMem> memList = activityMemService.list(new QueryWrapper<ActivityMem>().lambda()
                .select(ActivityMem::getMemCode, ActivityMem::getActivityCode, ActivityMem::getName)
                .eq(ActivityMem::getIsHostor, CommonConstant.ONE_INT)
                .in(ActivityMem::getActivityCode, activityCodeList)
                .isNull(ActivityMem::getDeleteTime));
        Map<String, String> compereMap = memList.stream()
                .collect(Collectors.groupingBy(ActivityMem::getActivityCode, Collectors.mapping(ActivityMem::getName, Collectors.joining(","))));

        recordList.forEach(record -> {
            String acOrgOrgCode = record.getAcOrgOrgCode().substring(0, length);
            String typeNames = record.getTypeNames();
            try {
                List readValue = JackSonUtil.JSON.readValue(typeNames, List.class);
                record.setTypeNames(CollectionUtil.join(readValue, ","));
            } catch (IOException e) {
                e.printStackTrace();
            }
            record.setCountOrgName(CacheUtils.getOrgNameByOrgCode(acOrgOrgCode));
            record.setOrgName(CacheUtils.getOrgName(record.getAcOrgCode()));
            record.setCompere(compereMap.get(record.getCode()));
            Integer memCount = record.getMemCount() == null ? 0 : record.getMemCount();
            Integer absenceCount = record.getAbsenceCount() == null ? 0 : record.getAbsenceCount();
            record.setMeetingCount(memCount - absenceCount);
        });
        return new OutMessage<>(Status.SUCCESS, activityPage);
    }

    /**
     * 主题党日计划
     *
     * @param data
     * @return
     */
    @Override
    public OutMessage getThematicPartyDay(LedgerDTO data) {
        String orgCode = data.getOrgCode();
        String orgName = data.getOrgName();
        Integer pageNum = data.getPageNum();
        Integer pageSize = data.getPageSize();
        Date endTime = data.getEndTime();
        DateTime beginOfYear = DateUtil.beginOfYear(endTime);
        DateTime endOfYear = DateUtil.endOfYear(endTime);
        boolean isLeaf = isLeaf(orgCode);
        int length = isLeaf ? orgCode.length() : orgCode.length() + 3;

        if (isLeaf) {
            // 叶子节点
            List<String> acOrgOrgCodeList = new ArrayList<String>() {
                private static final long serialVersionUID = -2723441399802191948L;

                {
                    add(orgCode);
                }
            };
            List<Activity> activityList = activityMapper.getThematicPartyDay(acOrgOrgCodeList, endTime, beginOfYear.toSqlDate(), endOfYear.toSqlDate());
            if (CollectionUtil.isEmpty(activityList)) {
                Page<Activity> activityPage = new Page<>();
                return new OutMessage<>(Status.SUCCESS, activityPage);
            }

            Activity activity = activityList.get(0);
            // 返回集合
            List<Record> records = new ArrayList<>();
            List<Record> recordMonthList = new ArrayList<>();

            Record recordData = new Record();
            String acOrgOrgCode = activity.getAcOrgOrgCode().substring(0, length);
            recordData.set("countOrgName", CacheUtils.getOrgNameByOrgCode(acOrgOrgCode));
            recordData.set("orgName", CacheUtils.getOrgNameByOrgCode(activity.getAcOrgCode()));
            setMonthData(activityList, records, recordMonthList, recordData, orgCode);

            Page<Record> activityPage = new Page<>();
            activityPage.setRecords(records);
            activityPage.setSize(1);
            activityPage.setPages(1);
            activityPage.setTotal(1);
            return new OutMessage<>(Status.SUCCESS, activityPage);
        } else {
            Page<HreMeetingsVO> page = new Page<>(pageNum, pageSize);
            Page<HreMeetingsVO> activityPage = activityMapper.getThematicPartyDayOrg(page, orgCode, endTime, beginOfYear.toSqlDate(), endOfYear.toSqlDate());
            if (activityPage.getPages() == 0) {
                return new OutMessage<>(Status.SUCCESS, activityPage);
            }
            List<HreMeetingsVO> recordList = activityPage.getRecords();
            List<String> acOrgOrgCodeList = recordList.stream().map(HreMeetingsVO::getAcOrgOrgCode).collect(Collectors.toList());
            // 主题党日计划
            List<Activity> activityList = activityMapper.getThematicPartyDay(acOrgOrgCodeList, endTime, beginOfYear.toSqlDate(), endOfYear.toSqlDate());
            Map<String, List<Activity>> collect = activityList.stream().collect(Collectors.groupingBy(Activity::getAcOrgOrgCode));
            Page<Record> activity = new Page<>();
            List<Record> records = new ArrayList<>();
            collect.forEach((key, value) -> {
                Record record = new Record();
                String acOrgOrgCode = key.substring(0, length);
                record.set("countOrgName", CacheUtils.getOrgNameByOrgCode(acOrgOrgCode));
                record.set("orgName", CacheUtils.getOrgNameByOrgCode(key));
                List<Record> recordMonthList = new ArrayList<>();
                setMonthData(value, records, recordMonthList, record, orgCode);
            });
            activity.setRecords(records);
            return new OutMessage<>(Status.SUCCESS, activity);
        }
    }

    /**
     * 主题党日计划12个月数据
     *
     * @param activityList
     * @param records
     * @param recordMonthList
     * @param recordData
     */
    private void setMonthData(List<Activity> activityList, List<Record> records, List<Record> recordMonthList, Record recordData, String orgCode) {
        activityList.forEach(activity1 -> {
            Date holdTime = activity1.getHoldTime();
            String name = activity1.getName();
            int month = DateUtil.month(holdTime) + 1;
            int day = DateUtil.dayOfMonth(holdTime);
            Record record = new Record();
            record.set("day", day);
            record.set("month", month);
            record.set("name", name);
            Integer memCount = activity1.getMemCount() == null ? 0 : activity1.getMemCount();
            Integer absenceCount = activity1.getAbsenceCount() == null ? 0 : activity1.getAbsenceCount();
            record.set("meetingCount", memCount - absenceCount);
            List<String> list = new ArrayList<>();
            list.add("1");
            list.add("2");
            Integer total = memMapper.selectCount(new QueryWrapper<Mem>().lambda()
                    .likeRight(Mem::getMemOrgCode, orgCode)
                    .in(Mem::getD08Code, list).isNull(Mem::getDeleteTime));
            record.set("orgMemCount", total == null ? 0 : total);
            recordMonthList.add(record);
        });
        recordData.set("monthList", recordMonthList);
        records.add(recordData);
    }

    private boolean isLeaf(String orgCode) {
        Org org = orgMapper.selectOne(new QueryWrapper<Org>().lambda().select(Org::getIsLeaf)
                .eq(Org::getOrgCode, orgCode)
                .isNull(Org::getDeleteTime));
        return org == null || org.getIsLeaf() == CommonConstant.ONE_INT;
    }

    @Override
    public OutMessage situation(String manageOrgCode) {
        SituationActivityVO activityVO =activityMapper.situationActivityCount(manageOrgCode);
        if (Objects.isNull(activityVO)) {
            activityVO = new SituationActivityVO();
        }
        List<ActivityMem> attend = activityMapper.getIsAttend(CommonConstant.ZERO_INT);
        activityVO.setAttend(attend.size());
        List<ActivityMem> notAttend = activityMapper.getIsAttend(CommonConstant.ONE_INT);
        activityVO.setNotAttend(notAttend.size());
        return new OutMessage<>(Status.SUCCESS, activityVO);
    }
}
