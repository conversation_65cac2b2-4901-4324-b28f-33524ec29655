package com.zenith.front.core.service.fee;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateField;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUnit;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.zenith.front.api.fee.IFeeLogService;
import com.zenith.front.api.fee.IFeeService;
import com.zenith.front.api.mem.IMemService;
import com.zenith.front.common.constant.CacheConstant;
import com.zenith.front.common.constant.CommonConstant;
import com.zenith.front.common.kit.CodeUtil;
import com.zenith.front.common.kit.ListUtils;
import com.zenith.front.common.kit.StrKit;
import com.zenith.front.core.cache.CacheUtils;
import com.zenith.front.core.kit.ChartUtil;
import com.zenith.front.core.kit.DbUtil;
import com.zenith.front.core.kit.ExcelUtil;
import com.zenith.front.model.bean.*;
import com.zenith.front.model.bean.User;
import com.zenith.front.model.custom.Record;
import com.zenith.front.dao.mapper.fee.FeeLogMapper;
import com.zenith.front.dao.mapper.fee.FeeMapper;
import com.zenith.front.dao.mapper.fee.FeeOrderMapper;
import com.zenith.front.dao.mapper.mem.MemMapper;
import com.zenith.front.dao.mapper.org.OrgMapper;
import com.zenith.front.model.dto.ChartDataDTO;
import com.zenith.front.model.dto.FeeDTO;
import com.zenith.front.model.dto.FeeListDTO;
import com.zenith.front.model.dto.MemListDTO;
import com.zenith.front.model.message.OutMessage;
import com.zenith.front.model.message.Status;

import com.zenith.front.model.vo.*;

import org.jooq.*;
import org.jooq.impl.DSL;
import org.redisson.api.RBucket;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDate;
import java.util.*;
import java.util.Comparator;
import java.util.stream.Collectors;

import static com.zenith.front.common.constant.CommonConstant.ONE;
import static com.zenith.front.common.constant.CommonConstant.THREE;
import static org.jooq.impl.DSL.*;

/**
 * <p>
 * 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-07-10
 */
@Service
public class FeeServiceImpl extends ServiceImpl<FeeMapper, Fee> implements IFeeService {

    @Resource
    private OrgMapper orgMapper;

    @Resource
    private FeeOrderMapper feeOrderMapper;

    @Resource
    private FeeMapper feeMapper;

    @Resource
    private MemMapper memMapper;

    @Resource
    private FeeLogMapper feeLogMapper;

    @Resource
    private IFeeService iFeeService;

    @Resource
    private IFeeLogService feeLogService;

    @Resource
    HttpServletResponse response;

    @Resource
    private CacheUtils cacheUtils;

    @Resource
    private IMemService memService;

    /**
     * 默认分页大小
     **/
    private static final Integer PAGE_SIZE = 10;
    /***
     * 组织层级长度
     * */
    private static final Integer ORG_SUB_LEN = 3;

    @Override
    public OutMessage<FeeAnaVO> indexCount(String orgId, String orgCode, Integer pageNum) {
        FeeAnaVO feeAnaVO;
        if (pageNum == 1) {
            feeAnaVO = new FeeAnaVO();
            feeAnaVO.setOrgId(orgId);
            feeAnaVO.setOrgName(CacheUtils.getOrgName(orgId));
            feeAnaVO.setOrgCode(orgCode);
            List<Org> subOrgList = orgMapper.selectList(new QueryWrapper<Org>().lambda()
                    .select(Org::getCode, Org::getOrgCode, Org::getShortName, Org::getOrgType)
                    .eq(Org::getParentCode, orgId)
                    .isNull(Org::getDeleteTime));

            java.util.Date date = new java.util.Date();
            DateTime parse = DateUtil.parse(DateUtil.format(date, "yyyy-MM-dd"), "yyyy-MM-dd");
            java.sql.Date sqlDate = parse.toSqlDate();
            BigDecimal zero = new BigDecimal(0);
            int year = DateUtil.year(date);
            int month = DateUtil.month(date) + 1;
            List<FeeCountListVO> feeCountList = feeMapper.countFee(orgCode, sqlDate, zero, year + "", month + "");
            feeAnaVO.setYjCount(sumOrg(feeCountList, "yjCount"));
            feeAnaVO.setYjnCount(sumOrg(feeCountList, "yjnCount"));
            feeAnaVO.setJrjnzeCount(sumOrg(feeCountList, "jrjnzeCount"));
            if (CollectionUtil.isNotEmpty(subOrgList)) {
                addSubData(orgCode, feeAnaVO, subOrgList, feeCountList);
            }
            RBucket<FeeAnaVO> bucket = cacheUtils.redissonClient.getBucket(CacheConstant.ANA_FEE_PREFIX + orgId);
            bucket.set(feeAnaVO);
        } else {
            RBucket<FeeAnaVO> bucket = cacheUtils.redissonClient.getBucket(CacheConstant.ANA_FEE_PREFIX + orgId);
            feeAnaVO = bucket.get();
            if (feeAnaVO == null) {
                feeAnaVO = new FeeAnaVO();
                feeAnaVO.setOrgId(orgId);
                feeAnaVO.setOrgName(CacheUtils.getOrgName(orgId));
                feeAnaVO.setOrgCode(orgCode);
                return new OutMessage<>(Status.SUCCESS, feeAnaVO);
            }
        }

        //进行分页
        List<FeeAnaVO> subOrgAna = feeAnaVO.getSubOrgAna();
        if (subOrgAna != null && !subOrgAna.isEmpty()) {
            subOrgAna = ListUtils.page(subOrgAna, pageNum, PAGE_SIZE);
            feeAnaVO.setSubOrgAna(subOrgAna);
        }

        return new OutMessage<>(Status.SUCCESS, feeAnaVO);
    }

    private BigDecimal sumOrg(List<FeeCountListVO> feeCountList, String name) {
        BigDecimal bigDecimal = null;
        if ("yjCount".equals(name)) {
            bigDecimal = feeCountList.stream().map(FeeCountListVO::getYjCount)
                    .filter(Objects::nonNull)
                    .reduce(BigDecimal.ZERO, BigDecimal::add);
        }
        if ("yjnCount".equals(name)) {
            bigDecimal = feeCountList.stream().map(FeeCountListVO::getYjnCount)
                    .filter(Objects::nonNull)
                    .reduce(BigDecimal.ZERO, BigDecimal::add);
        }
        if ("jrjnzeCount".equals(name)) {
            bigDecimal = feeCountList.stream().map(FeeCountListVO::getJrjnzeCount)
                    .filter(Objects::nonNull)
                    .reduce(BigDecimal.ZERO, BigDecimal::add);
        }
        return bigDecimal == null ? BigDecimal.ZERO : bigDecimal;
    }

    private void addSubData(String orgCode, FeeAnaVO feeAnaVO, List<Org> subOrgList, List<FeeCountListVO> feeCountList) {
        int length = orgCode.length();
        Map<String, FeeAnaVO> subMap = subOrgList.stream().collect(Collectors.toMap(Org::getOrgCode, o -> {
            FeeAnaVO subAnaVO = new FeeAnaVO();
            subAnaVO.setOrgName(o.getShortName());
            subAnaVO.setOrgCode(o.getOrgCode());
            subAnaVO.setOrgId(o.getCode());
            subAnaVO.setType(o.getOrgType());
            feeAnaVO.addSubOrg(subAnaVO);
            return subAnaVO;
        }));

        feeCountList.forEach(record -> {
            String subOrgCodeStr = record.getMem_org_org_code();
            //字符串截取得到直属下级的orgCode
            if (subOrgCodeStr != null && subOrgCodeStr.length() >= length + ORG_SUB_LEN) {
                String subOrgCode = subOrgCodeStr.substring(0, length + ORG_SUB_LEN);
                Optional<FeeAnaVO> opt = Optional.ofNullable(subMap.get(subOrgCode));
                opt.ifPresent(vo -> sumOrg(vo, record));
            }
        });
    }

    private void sumOrg(FeeAnaVO feeAnaVO, FeeCountListVO record) {
        BigDecimal yjCount = record.getYjCount();
        BigDecimal yjnCount = record.getYjnCount();
        BigDecimal jrjnzeCount = record.getJrjnzeCount();
        feeAnaVO.setYjCount(feeAnaVO.getYjCount().add(yjCount == null ? BigDecimal.ZERO : yjCount));
        feeAnaVO.setYjnCount(feeAnaVO.getYjnCount().add(yjnCount == null ? BigDecimal.ZERO : yjnCount));
        feeAnaVO.setJrjnzeCount(feeAnaVO.getJrjnzeCount().add(jrjnzeCount == null ? BigDecimal.ZERO : jrjnzeCount));
    }

    @Override
    public OutMessage wjnList(String orgCode, int pageNum, int pageSize) {
        Date now = new Date();
        int year = DateUtil.year(now);
        int month = DateUtil.month(now) + 1;
        Page<WjnListVO> page = new Page<>(pageNum, pageSize);
        Page<WjnListVO> recordPage = feeMapper.getFeeMemList(page, orgCode, year + "", month + "");
        recordPage.getRecords().forEach(record -> record.setMemName(CacheUtils.getMemName(record.getMemCode())));
        return new OutMessage<>(Status.SUCCESS, recordPage);
    }

    /**
     * 获取党费标准列表
     *
     * @param data
     * @return
     */
    @Override
    public OutMessage getList(FeeListDTO data) {
        // 组装查询数据
        MemListDTO memListDTO = new MemListDTO();
        memListDTO.setMemName(data.getMemName());
        memListDTO.setSearchType(CommonConstant.ONE);
        memListDTO.setMemOrgCode(data.getMemOrgOrgCode());
        // 获取查询条件
        Map map = memService.getListCondition(memListDTO);
        OutMessage outMessage = (OutMessage) map.get(CommonConstant.OUT_MESSAGE);
        if (outMessage != null) {
            return outMessage;
        }
        Condition cond = (Condition) map.get(CommonConstant.CONDITION);
        LambdaQueryWrapper<Mem> condition = new LambdaQueryWrapper<Mem>().apply(cond.toString());

        Page<Mem> memPage = new Page<>(data.getPageNum(), data.getPageSize());
        memService.page(memPage, condition);
        List<Mem> memList = memPage.getRecords();
        // 人员主键集合
        List<String> list = new ArrayList<>();
        memList.forEach(mem -> list.add(mem.getCode()));
        // 党员缴费标准
        List<Fee> feeList = getList(list, data.getYear());

        Map<String, List<Fee>> feeMap = feeList.stream().collect(Collectors.groupingBy(Fee::getMemCode));

        memPage.getRecords().forEach(mem -> {
            // 党费标准
            List<Fee> fees = feeMap.get(mem.getCode());
            mem.setFeeList(fees != null ? fees : new ArrayList<>());
            // 组织名称
            String orgName = CacheUtils.getOrgShortName(mem.getOrgCode());
            mem.setOrgName(orgName);
        });
        return new OutMessage<>(Status.SUCCESS, memPage);
    }

    /**
     * 获取党费列表
     *
     * @param list
     * @param year
     * @return
     */
    public List<Fee> getList(List<String> list, String year) {
        LambdaQueryWrapper<Fee> query = new LambdaQueryWrapper<Fee>()
                .eq(Fee::getYear, year)
                .isNull(Fee::getDeleteTime);
        if (CollectionUtil.isNotEmpty(list)) {
            query.in(Fee::getMemCode, list);
        }
        return list(query);
    }

    @Override
    public OutMessage saveFee(FeeDTO data, User user) throws Exception {
        Integer isYearly = data.getIsYearly();

        String d49Code = data.getD49Code();
        BigDecimal standard = data.getStandard();
        BigDecimal zero = new BigDecimal(0);
        if (!CommonConstant.FOUR.equals(d49Code) && standard.compareTo(zero) == 0) {
            return new OutMessage<>(Status.STANDARD_MUST_GT_ZERO);
        }

        if (isYearly == CommonConstant.ZERO_INT) {
            // 不应用到全年
            OutMessage outMessage = this.checkLastPayDateForMonth(data);
            if (outMessage != null) {
                return outMessage;
            }

            return this.saveMonthFee(data, user);
        } else {
            // 应用到全年

            // 需要添加的月份集合
            List<String> addList = new ArrayList<>();

            OutMessage outMessage = this.checkLastPayDateForYear(data, addList);
            if (outMessage != null) {
                return outMessage;
            }

            return this.saveYearFee(data, addList, user);
        }
    }


    /**
     * 校验党员最晚缴费时间
     *
     * @param data
     * @return
     */
    private OutMessage checkLastPayDateForMonth(FeeDTO data) {
        Mem mem = memMapper.selectOne(new QueryWrapper<Mem>().lambda().eq(Mem::getCode, data.getCode()).isNull(Mem::getDeleteTime));
        Date lastPayDate = mem.getLastPayDate();
        if (lastPayDate != null) {
            // 已有最后缴费时间
            String year = data.getYear();
            String month = data.getMonth();
            int monthInteger = Integer.parseInt(month);
            int yearInteger = Integer.parseInt(year);

            // 最后缴费年
            int lastYear = cn.hutool.core.date.DateUtil.year(lastPayDate);
            // 最后缴费月
            int lastMonth = cn.hutool.core.date.DateUtil.month(lastPayDate) + 1;

            if (lastYear > yearInteger) {
                return new OutMessage<>(Status.MONTH_NEED_AFTER_LAST_PAY_DAY);
            } else if (lastYear == yearInteger) {
                // 同一年
                if (monthInteger <= lastMonth) {
                    return new OutMessage<>(Status.MONTH_NEED_AFTER_LAST_PAY_DAY);
                }
            }
        } else {
            return new OutMessage<>(Status.LAST_PAY_DAY_IS_NULL);
        }
        return null;
    }


    /**
     * 保存单月标准数据
     *
     * @param user
     * @param data
     * @return
     */
    private OutMessage saveMonthFee(FeeDTO data, User user) throws Exception {
        String memCode = data.getMemCode();
        String year = data.getYear();
        String month = data.getMonth();
        // 设置组织唯一标示符
        String settingOrgCode = data.getSettingOrgCode();
        List<Org> srcParentOrg = orgMapper.findAllParentOrg(settingOrgCode);
        // 组织code集合
        List<String> list = srcParentOrg.stream().map(Org::getCode).collect(Collectors.toList());

        // 账户
        String account = user.getAccount();

        boolean flag = false;
        Fee fee = feeMapper.selectOne(new QueryWrapper<Fee>().lambda()
                .eq(Fee::getMemCode, memCode)
                .eq(Fee::getYear, year)
                .eq(Fee::getMonth, month)
                .isNull(Fee::getDeleteTime));
        if (fee != null) {
            // 修改
            BigDecimal payMoney = fee.getPayMoney();
            if (payMoney != null) {
                // 已经缴费
                return new OutMessage<>(Status.MONTH_PAY_MONEY);
            }

            // 修改
            BeanUtils.copyProperties(data, fee, "id", "code", "createTime");
            // 设置组织层级
            fee.setSettingOrgCodeSet(new ObjectMapper().writeValueAsString(list));
            // 设置季度
            fee.setSeason(com.zenith.front.common.kit.DateUtil.quarter(month));
            fee.setUpdateTime(new Date());
            fee.setUpdateAccount(account);
            // 应缴费时间
            Date date = com.zenith.front.common.kit.DateUtil.localeDateToDate(LocalDate.of(Integer.parseInt(year), Integer.parseInt(month), 15));
            fee.setShouldPayDate(date);
            // 日志
            FeeLog feeLog = this.getFeeLog(fee);
            // 保存
            int i = feeMapper.updateById(fee);
            int insert = feeLogMapper.insert(feeLog);
            if (i > 0 && insert > 0) {
                flag = true;
            }
        } else {
            // 新增
            // fee = data.toModel();
            fee = new Fee();
            // 设置组织层级
            fee.setSettingOrgCodeSet(new ObjectMapper().writeValueAsString(list));
            // 设置季度
            fee.setSeason(com.zenith.front.common.kit.DateUtil.quarter(month));
            fee.setCode(CodeUtil.getCode());
            fee.setUpdateTime(new Date());
            fee.setCreateTime(new DateTime());
            fee.setUpdateAccount(account);

            if (fee.getShouldPayDate() == null) {
                // 应缴费时间
                Date date = com.zenith.front.common.kit.DateUtil.localeDateToDate(LocalDate.of(Integer.parseInt(year), Integer.parseInt(month), 15));
                fee.setShouldPayDate(date);
            }

            // 日志
            FeeLog feeLog = this.getFeeLog(fee);
            // 保存
            Fee finalFee1 = fee;
            int insert = feeMapper.insert(finalFee1);
            int insert1 = feeLogMapper.insert(feeLog);
            if (insert > 0 && insert1 > 0) {
                flag = true;
            }
        }
        return new OutMessage<>(flag ? Status.SUCCESS : Status.FAIL);
    }

    /**
     * 获取日志
     *
     * @param fee
     * @return
     */
    public FeeLog getFeeLog(Fee fee) {
        FeeLog feeLog = new FeeLog();
        feeLog.setCode(CodeUtil.getCode());
        feeLog.setRemark(fee.getRemark());
        feeLog.setFeeCode(fee.getCode());
        feeLog.setCreatorAccount(fee.getUpdateAccount());
        feeLog.setMemCode(fee.getMemCode());
        feeLog.setMemOrgCode(fee.getMemOrgCode());
        feeLog.setMemOrgOrgCode(fee.getMemOrgOrgCode());
        feeLog.setBase(fee.getBase());
        feeLog.setStand(fee.getStandard());
        feeLog.setReason(fee.getReason());
        feeLog.setD49Code(fee.getD49Code());
        feeLog.setD49Name(fee.getD49Name());
        feeLog.setCreateTime(new Date());
        feeLog.setUpdateTime(new Date());
        feeLog.setId(null);
        return feeLog;
    }


    /**
     * 校验党员最晚缴费时间
     *
     * @param data
     * @return
     */
    private OutMessage checkLastPayDateForYear(FeeDTO data, List<String> addList) {
        Mem mem = memMapper.selectOne(new QueryWrapper<Mem>().lambda().eq(Mem::getCode, data.getMemCode()).isNull(Mem::getDeleteTime));
        if (Objects.isNull(mem)) {
            return new OutMessage<>(Status.MEM_IS_ERROR);
        }
        Date lastPayDate = mem.getLastPayDate();
        if (lastPayDate != null) {
            // 已有最后缴费时间
            String year = data.getYear();
            String month = data.getMonth();
            int monthInteger = Integer.parseInt(month);
            int yearInteger = Integer.parseInt(year);

            // 最后缴费年
            int lastYear = cn.hutool.core.date.DateUtil.year(lastPayDate);
            // 最后缴费月
            int lastMonth = cn.hutool.core.date.DateUtil.month(lastPayDate) + 1;

            if (lastYear > yearInteger) {
                return new OutMessage<>(Status.MONTH_NEED_AFTER_LAST_PAY_DAY);
            } else if (lastYear == yearInteger) {
                // 同一年
                if (monthInteger <= lastMonth) {
                    return new OutMessage<>(Status.MONTH_NEED_AFTER_LAST_PAY_DAY);
                } else {
                    // 判断哪几个月可以修改
                    for (int i = lastMonth + 1; i <= 12; i++) {
                        addList.add(String.valueOf(i));
                    }
                }
            } else {
                // 不同年,全部月份需要判断
                for (int i = 1; i <= 12; i++) {
                    addList.add(String.valueOf(i));
                }
            }
        } else {
            return new OutMessage<>(Status.LAST_PAY_DAY_IS_NULL);
        }

        return null;
    }

    /**
     * 保存全年标准数据
     *
     * @param data
     * @param addList
     * @return
     */
    private OutMessage saveYearFee(FeeDTO data, List<String> addList, User user) throws Exception {
        String memCode = data.getMemCode();
        String year = data.getYear();
        BigDecimal standard = data.getStandard();
        // 设置组织唯一标示符
        String settingOrgCode = data.getSettingOrgCode();
        List<Org> srcParentOrg = orgMapper.findAllParentOrg(settingOrgCode);
        // 组织code集合
        List<String> orgList = srcParentOrg.stream().map(Org::getCode).collect(Collectors.toList());
        // 当前账户
        String account = user.getAccount();

        // 查询设置月份数据
        List<Fee> feeList = feeMapper.selectList(new QueryWrapper<Fee>().lambda()
                .eq(Fee::getMemCode, memCode)
                .eq(Fee::getYear, year)
                .isNull(Fee::getDeleteTime));

        boolean flag = false;
        if (CollectionUtil.isEmpty(feeList)) {
            // 该党员该年尚未设置过党费标准
            feeList = new ArrayList<>();
            // 党费日志
            List<FeeLog> feeLogList = new ArrayList<>();

            Fee fee;
            // 设置12个月的党费标准

            for (String month : addList) {
                // fee = data.toModel();
                fee = new Fee();
                fee.setCode(CodeUtil.getCode());
                fee.setCreateTime(new Date());
                // 设置组织层级
                fee.setSettingOrgCodeSet(orgList);
                // 设置季度
                fee.setSeason(com.zenith.front.common.kit.DateUtil.quarter(month));
                // 设置月份
                fee.setMonth(month);
                fee.setUpdateTime(new Date());
                fee.setUpdateAccount(account);
                // 应缴费时间
                Date date = com.zenith.front.common.kit.DateUtil.localeDateToDate(LocalDate.of(Integer.parseInt(year), Integer.parseInt(month), 15));
                fee.setShouldPayDate(date);
                fee.setId(null);

                feeList.add(fee);
                feeLogList.add(this.getFeeLog(fee));
            }
            List<Fee> finalFeeList = feeList;
            // 批量保存
            boolean b = iFeeService.saveBatch(finalFeeList);
            boolean b1 = feeLogService.saveBatch(feeLogList);
            if (b && b1) {
                flag = true;
            }
        } else {
            // 设置了部分党费标准

            // 需要修改的党费标准
            List<Fee> updateList = new ArrayList<>();
            // 党费日志
            List<FeeLog> feeLogList = new ArrayList<>();

            for (Fee fee : feeList) {
                String monthFor = fee.getMonth();
                BigDecimal payMoney = fee.getPayMoney();
                if (payMoney == null && addList.contains(monthFor)) {
                    // 未缴纳
                    updateList.add(fee);
                }
                addList.remove(monthFor);
            }
            // 新增集合
            List<Fee> feeAddList = null;
            if (addList.size() > CommonConstant.ZERO_INT) {
                feeAddList = new ArrayList<>();
                Fee fee;
                for (String month : addList) {
                    // fee = data.toModel();
                    fee = new Fee();
                    fee.setCode(CodeUtil.getCode());
                    fee.setCreateTime(new Date());
                    // 设置组织层级
                    fee.setSettingOrgCodeSet(new ObjectMapper().writeValueAsString(orgList));
                    // 设置月份
                    fee.setMonth(month);
                    // 设置季度
                    fee.setSeason(com.zenith.front.common.kit.DateUtil.quarter(month));
                    fee.setUpdateTime(new Date());
                    fee.setUpdateAccount(account);
                    // 应缴费时间
                    Date date = com.zenith.front.common.kit.DateUtil.localeDateToDate(LocalDate.of(Integer.parseInt(year), Integer.parseInt(month), 15));
                    fee.setShouldPayDate(date);
                    fee.setId(null);
                    feeAddList.add(fee);
                    // 日志
                    feeLogList.add(this.getFeeLog(fee));
                }
            }
            // 修改集合
            updateList.forEach(feeOld -> {
                feeOld.setUpdateTime(new Date());
                feeOld.setUpdateAccount(account);
                // 设置组织层级
                try {
                    feeOld.setSettingOrgCodeSet(new ObjectMapper().writeValueAsString(orgList));
                } catch (Exception e) {
                    e.printStackTrace();
                }
                feeOld.setStandard(standard);

                if (feeOld.getShouldPayDate() == null) {
                    // 应缴费时间
                    Date date = com.zenith.front.common.kit.DateUtil.localeDateToDate(LocalDate.of(Integer.parseInt(year), Integer.parseInt(feeOld.getMonth()), 15));
                    feeOld.setShouldPayDate(date);
                }

                // 日志
                feeLogList.add(this.getFeeLog(feeOld));
            });

            // 保存
            List<Fee> finalFeeAddList = feeAddList;
            if (finalFeeAddList != null && finalFeeAddList.size() > CommonConstant.ZERO_INT) {
                iFeeService.saveBatch(finalFeeAddList);
            }
            if (updateList.size() > CommonConstant.ZERO_INT) {
                iFeeService.updateBatchById(updateList);
            }
            if (feeLogList.size() > CommonConstant.ZERO_INT) {
                feeLogService.saveBatch(feeLogList);
            }
            flag = true;
        }
        return new OutMessage<>(flag ? Status.SUCCESS : Status.FAIL);
    }


    /**
     * 查询党员党费应支付信息
     *
     * @param data
     * @return
     */
    @Override
    public OutMessage getPayList(List<FeeListDTO> data) {
        List<PayListVO> payListVOList = new ArrayList<>(data.size());

        for (FeeListDTO feeListDTO : data) {
            String memCode = feeListDTO.getMemCode();

            // 返回数据
            PayListVO payListVO = new PayListVO();
            payListVO.setMemCode(memCode);
            payListVO.setDeadline(feeListDTO.getDeadline());
            StringBuilder sb = new StringBuilder("暂无");

            // 最后缴费时间
            Date lastPayDate = feeListDTO.getLastPayDate();
            // 需要缴费到的时间
            Date deadline = feeListDTO.getDeadline();
            // 月初
            Date lastPayDateBegin = cn.hutool.core.date.DateUtil.beginOfMonth(lastPayDate);
            Date deadlineEnd = cn.hutool.core.date.DateUtil.endOfMonth(deadline);

            if (cn.hutool.core.date.DateUtil.between(lastPayDate, deadline, DateUnit.DAY, false) <= 0) {
                return new OutMessage<>(Status.PAY_MONTH_ERROR);
            }
            // 党费标准集合
            java.sql.Date lastPayDateLast = DateUtil.endOfMonth(lastPayDate).toSqlDate();
            java.sql.Date deadlineLast = DateUtil.endOfMonth(deadline).toSqlDate();
            List<Fee> feeList = feeMapper.getPayList(memCode, lastPayDateLast, deadlineLast);
            long betweenMonth = cn.hutool.core.date.DateUtil.betweenMonth(lastPayDateBegin, deadlineEnd, true);

            // 没有查询到党费标准
            if (CollectionUtil.isEmpty(feeList)) {
                payListVO.setIsError(ONE);
                for (long i = 0; i < betweenMonth; i++) {
                    DateTime dateTime = cn.hutool.core.date.DateUtil.offset(lastPayDateBegin, DateField.MONTH, Long.valueOf(i).intValue() + 1);
                    String month = String.valueOf(cn.hutool.core.date.DateUtil.month(dateTime) + 1);
                    sb.append(month).append(",");
                }

                String substring = sb.substring(0, sb.length() - 1);
                payListVO.setErrorMessage(substring + "月份党费标准");
                payListVOList.add(payListVO);
                continue;
            }

            // 检查月份
            for (long i = 0; i < betweenMonth; i++) {
                DateTime dateTime = cn.hutool.core.date.DateUtil.offset(lastPayDateBegin, DateField.MONTH, Long.valueOf(i).intValue() + 1);
                String year = String.valueOf(cn.hutool.core.date.DateUtil.year(dateTime));
                String month = String.valueOf(cn.hutool.core.date.DateUtil.month(dateTime) + 1);

                long count = feeList.stream().filter(fee -> year.equals(fee.getYear()) && month.equals(fee.getMonth())).count();
                if (count == 0) {
                    payListVO.setIsError(ONE);
                    sb.append(month).append(",");
                }
            }

            if (ONE.equals(payListVO.getIsError())) {
                String substring = sb.substring(0, sb.length() - 1);
                payListVO.setErrorMessage(substring + "月份党费标准");
                payListVOList.add(payListVO);
                continue;
            }

            // 检查成功,返回数据
            BigDecimal total = new BigDecimal(0).setScale(2, RoundingMode.HALF_UP);
            for (Fee fee : feeList) {
                BigDecimal standard = fee.getStandard();
                if (standard != null) {
                    total = total.add(standard);
                }
            }
            payListVO.setIsError(CommonConstant.ZERO);
            payListVO.setTotal(total);
            payListVOList.add(payListVO);
        }

        return new OutMessage<>(Status.SUCCESS, payListVOList);
    }


    /**
     * 获取党费标准统计项 已缴纳人数,未缴纳人数,未设置人数
     *
     * @param feeListDTO
     * @return
     */
    @Override
    public OutMessage getCountList(FeeListDTO feeListDTO) {
        String memOrgOrgCode = feeListDTO.getMemOrgOrgCode();
        String year = feeListDTO.getYear();
        Integer pageSize = feeListDTO.getPageSize();
        Integer pageNum = feeListDTO.getPageNum();
        String orgName = feeListDTO.getOrgName();

        // 分页查询组织
        Page<FeeAllVO> page = new Page<>(pageNum, pageSize);
        Page<FeeAllVO> orgPage = orgMapper.findOrgCodeByPage(page, memOrgOrgCode, orgName);
        boolean isLeaf = orgPage.getRecords() == null || orgPage.getRecords().size() <= 0;
        // 获取查询的组织层级码集合
        List<String> orgCodeList;
        if (isLeaf) {
            // 党支部,没有下级展示自己
            orgCodeList = new ArrayList<String>() {
                private static final long serialVersionUID = 4582394923316282189L;

                {
                    add(memOrgOrgCode);
                }
            };
        } else {
            // 党委,展示直属下级
            orgCodeList = orgPage.getRecords().stream().map(FeeAllVO::getOrgCode).collect(Collectors.toList());
        }

        // 层级码长度
        int length;
        if (isLeaf) {
            length = memOrgOrgCode.length();
        } else {
            length = memOrgOrgCode.length() + 3;
        }
        // 应交集合
        Map<String, Integer> shouldPayMap = new HashMap<>(16);
        // 查询党费标准表
        // 应交
        List<FeeAllVO> shouldPayList = memMapper.findCountByOrgCode(orgCodeList);
        // 没有人员
        boolean isEmptyShouldPay = CollectionUtil.isEmpty(shouldPayList);

        if (isEmptyShouldPay) {
            orgCodeList.forEach(orgCode -> shouldPayMap.put(orgCode, 0));
        } else {
            shouldPayList.forEach(record -> {
                String memOrgCode = record.getMemOrgCode().substring(0, length);
                Integer count = record.getCount();
                shouldPayMap.merge(memOrgCode, count, Integer::sum);
            });
        }

        // 组织每月党费标准
//        List<Record> recordList = feeMapper.getCountList(orgCodeList, year);
        List<Record> recordList = new ArrayList<>();
        boolean isEmptyRecordList = CollectionUtil.isEmpty(recordList);
        // 返回数据
        List<FeeCountVO> feeCountVOList = new ArrayList<>();
        for (String orgCode : orgCodeList) {
            FeeCountVO feeCountVO = new FeeCountVO();
            List<FeeVO> feeVOList = new ArrayList<>();
            // 月份
            List<String> monthList = new ArrayList<String>() {
                private static final long serialVersionUID = -1968423633134016263L;

                {
                add("1");
                add("2");
                add("3");
                add("4");
                add("5");
                add("6");
                add("7");
                add("8");
                add("9");
                add("10");
                add("11");
                add("12");
            }};

            if (isEmptyRecordList) {
                // 无数据
                setNotDataFeeVO(shouldPayMap, orgCode, feeVOList, monthList);
            } else {
                for (Record record : recordList) {
                    FeeVO feeVO = new FeeVO();
                    String month = record.getStr("month");
                    Integer isPayCount = record.getInt(orgCode + "isPay");
                    Integer isNotPayCount = record.getInt(orgCode + "isNotPay");
                    feeVO.setSubmittedCount(isPayCount);
                    feeVO.setUnpaidCount(isNotPayCount);
                    feeVO.setMonth(month);
                    feeVO.setShouldPayCount(shouldPayMap.get(orgCode) == null ? 0 : shouldPayMap.get(orgCode));
                    feeVO.setNotSetCount(feeVO.getShouldPayCount() - feeVO.getSubmittedCount() - feeVO.getUnpaidCount());
                    feeVOList.add(feeVO);
                    monthList.remove(month);
                }

                if (CollectionUtil.isNotEmpty(monthList)) {
                    setNotDataFeeVO(shouldPayMap, orgCode, feeVOList, monthList);
                }
            }

            feeCountVO.setFeeVOList(feeVOList.stream().sorted(Comparator.comparing(feeVo -> Integer.parseInt(feeVo.getMonth()))).collect(Collectors.toList()))
                    .setMemOrgOrgCode(orgCode).setOrgShortName(CacheUtils.getOrgShortNameByOrgCode(orgCode));
            feeCountVOList.add(feeCountVO);
        }

        Page<FeeCountVO> feeCountVOPage = new Page<>();
        feeCountVOPage.setSize(orgPage.getSize());
        feeCountVOPage.setRecords(feeCountVOList);
        if (isLeaf) {
            feeCountVOPage.setTotal(1);
            feeCountVOPage.setPages(1);
        } else {
            feeCountVOPage.setTotal(orgPage.getTotal());
            feeCountVOPage.setPages(orgPage.getPages());
        }

        return new OutMessage<>(Status.SUCCESS, feeCountVOPage);
    }

    public void setNotDataFeeVO(Map<String, Integer> shouldPayMap, String orgCode, List<FeeVO> feeVOList, List<String> monthList) {
        monthList.forEach(month -> {
            FeeVO feeVO = new FeeVO();
            feeVO.setSubmittedCount(0);
            feeVO.setUnpaidCount(0);
            feeVO.setMonth(month);
            feeVO.setShouldPayCount(shouldPayMap.get(orgCode) == null ? 0 : shouldPayMap.get(orgCode));
            feeVO.setNotSetCount(feeVO.getShouldPayCount());
            feeVOList.add(feeVO);
        });
    }

    @Override
    public OutMessage getFeeStandardTotal(ChartDataDTO data) {
        List<String> strings = new ArrayList<>();
        strings.add("1");
        strings.add("2");
        LambdaQueryWrapper<Mem> wrapper = new QueryWrapper<Mem>().lambda();
        wrapper.likeRight(Mem::getMemOrgCode, data.getOrgCode())
                .isNull(Mem::getDeleteTime).in(Mem::getD08Code, strings);
        if (Objects.nonNull(data.getStartDate())) {
            wrapper.ge(Mem::getFullMemberDate, data.getStartDate());
        }
        if (Objects.nonNull(data.getEndDate())) {
            wrapper.le(Mem::getFullMemberDate, data.getEndDate());
        }
        Integer memTotal = memMapper.selectCount(wrapper);
        Integer orgNotSetFeeMem = feeMapper.getOrgNotSetFeeMem(data.getOrgCode(), String.valueOf(DateUtil.year(new Date())), String.valueOf(DateUtil.month(new Date()) + 1));
        // 无数据
        List<String> list = new ArrayList<String>() {
            private static final long serialVersionUID = -2255578075092415961L;

            {
            add("标准设置");
            add("已设置人数");
            add("未设置人数");
        }};
        List<Record> recordList = new ArrayList<>();
        Record recordData;
        recordData = new Record();
        recordData.set("name", list.get(0));
        recordData.set("count", memTotal);
        recordList.add(recordData);

        recordData = new Record();
        recordData.set("name", list.get(1));
        recordData.set("count", memTotal - orgNotSetFeeMem);
        recordList.add(recordData);

        recordData = new Record();
        recordData.set("name", list.get(2));
        recordData.set("count", orgNotSetFeeMem);
        recordList.add(recordData);
        return new OutMessage<>(Status.SUCCESS, recordList);
    }

    /**
     * 党费缴纳人数统计
     *
     * @param data
     * @return
     */
    @Override
    public OutMessage getFeePayTotal(ChartDataDTO data) {
        String year = String.valueOf(DateUtil.year(new Date()));
        String month = String.valueOf(DateUtil.month(new Date()) + 1);

        Long payedCount = feeMapper.getFeePayCount(data.getOrgCode(), year, month, "yes");
        Long noPayCount = feeMapper.getFeePayCount(data.getOrgCode(), year, month, "no");
        // 无数据
        List<String> list = new ArrayList<String>() {
            private static final long serialVersionUID = -235250031160402992L;

            {
            add("党费缴纳");
            add("已缴纳人数");
            add("未缴纳人数");
        }};
        List<Record> recordList = new ArrayList<>();
        Record recordData;
        recordData = new Record();
        recordData.set("name", list.get(0));
        recordData.set("count", noPayCount + payedCount);
        recordList.add(recordData);

        recordData = new Record();
        recordData.set("name", list.get(1));
        recordData.set("count", payedCount);
        recordList.add(recordData);

        recordData = new Record();
        recordData.set("name", list.get(2));
        recordData.set("count", noPayCount);
        recordList.add(recordData);
        return new OutMessage<>(Status.SUCCESS, recordList);
    }

    /**
     * 党费缴纳金额统计
     *
     * @param data
     * @return
     */
    @Override
    public OutMessage getFeePayMoneyTotal(ChartDataDTO data) {
        String orgCode = data.getOrgCode();
        String year = String.valueOf(DateUtil.year(new Date()));
        String month = String.valueOf(DateUtil.month(new Date()) + 1);
        // 应缴金额
        BigDecimal orgFeeTotalByMonth = feeMapper.getOrgFeeTotalByMonth(orgCode, year, month);
        // 已缴金额
        BigDecimal orgFeePayTotalByMonth = feeMapper.getOrgFeePayTotalByMonth(orgCode, year, month);
        orgFeeTotalByMonth = ObjectUtil.isNull(orgFeeTotalByMonth) ? BigDecimal.ZERO : orgFeeTotalByMonth;
        orgFeePayTotalByMonth = ObjectUtil.isNull(orgFeePayTotalByMonth) ? BigDecimal.ZERO : orgFeePayTotalByMonth;
        // 无数据
        List<String> list = new ArrayList<String>() {
            private static final long serialVersionUID = 910904607336211953L;

            {
            add("党费金额");
            add("已收取金额");
            add("未收取金额");
        }};
        List<Record> recordList = new ArrayList<>();
        Record recordData;
        recordData = new Record();
        recordData.set("name", list.get(0));
        recordData.set("count", orgFeeTotalByMonth);
        recordList.add(recordData);

        recordData = new Record();
        recordData.set("name", list.get(1));
        recordData.set("count", orgFeePayTotalByMonth);
        recordList.add(recordData);

        recordData = new Record();
        recordData.set("name", list.get(2));
        recordData.set("count", orgFeeTotalByMonth.subtract(orgFeePayTotalByMonth));
        recordList.add(recordData);
        return new OutMessage<>(Status.SUCCESS, recordList);
    }

    /**
     * 党费支出金额统计
     *
     * @param data
     * @return
     */
    @Override
    public OutMessage getFeeDisburseTotal(ChartDataDTO data) {
        BigDecimal handCount = feeMapper.getFeeDisburseCount(data.getOrgCode(), "0");
        BigDecimal billCount = feeMapper.getFeeDisburseCount(data.getOrgCode(), "1");
        handCount = ObjectUtil.isNull(handCount) ? BigDecimal.ZERO : handCount;
        billCount = ObjectUtil.isNull(billCount) ? BigDecimal.ZERO : billCount;
        List<String> list = new ArrayList<String>() {
            private static final long serialVersionUID = 5889313804998686577L;

            {
            add("党费支出");
            add("手动支出金额");
            add("账单支出金额");
        }};
        List<Record> recordList = new ArrayList<>();
        Record recordData;
        recordData = new Record();
        recordData.set("name", list.get(0));
        recordData.set("count", handCount.add(billCount));
        recordList.add(recordData);

        recordData = new Record();
        recordData.set("name", list.get(1));
        recordData.set("count", handCount);
        recordList.add(recordData);

        recordData = new Record();
        recordData.set("name", list.get(2));
        recordData.set("count", billCount);
        recordList.add(recordData);
        return new OutMessage<>(Status.SUCCESS, recordList);
    }

    /**
     * 按月统计党费缴纳
     *
     * @param data
     * @return
     */
    @Override
    public OutMessage getFeePayByMonth(ChartDataDTO data) {
        String year = String.valueOf(DateUtil.year(new Date()));
        List<FeePayVO> records = feeMapper.getOrgFeePayGroupByMonth(data.getOrgCode(), year);
        List<String> strList = new ArrayList<String>() {
            private static final long serialVersionUID = -7929668108847545959L;

            {
            add("1");
            add("2");
            add("3");
            add("4");
            add("5");
            add("6");
            add("7");
            add("8");
            add("9");
            add("10");
            add("11");
            add("12");
        }};
        for (FeePayVO record : records) {
            String month = record.getMonth();
            strList.remove(month);
        }
        for (String str : strList) {
            FeePayVO feePayVO = new FeePayVO();
            feePayVO.setMonth(str);
            feePayVO.setCount(CommonConstant.ZERO);
            records.add(feePayVO);
        }
        return new OutMessage<>(Status.SUCCESS, records);
    }

    /**
     * 按月统计党费支出
     *
     * @param data
     * @return
     */
    @Override
    public OutMessage getFeeDisburseByMonth(ChartDataDTO data) {
        List<FeePayVO> list = feeMapper.getOrgFeeDisburseGroupByMonth(data.getOrgCode());
        List<String> strList = new ArrayList<String>() {
            private static final long serialVersionUID = -9045056510828793582L;

            {
            add("1");
            add("2");
            add("3");
            add("4");
            add("5");
            add("6");
            add("7");
            add("8");
            add("9");
            add("10");
            add("11");
            add("12");
        }};

        for (FeePayVO record : list) {
            //String month = record.getMonth().substring(0, record.getMonth().indexOf("."));
            record.setMonth(record.getMonth());
            strList.remove(record.getMonth());
        }

        for (String str : strList) {
            FeePayVO feePayVO = new FeePayVO();
            feePayVO.setMonth(str);
            feePayVO.setCount(CommonConstant.ZERO);
            list.add(feePayVO);
        }

        return new OutMessage<>(Status.SUCCESS, list);
    }

    /**
     * 按类型统计党费支出
     *
     * @param data
     * @return
     */
    @Override
    public OutMessage getFeeDisburseByType(ChartDataDTO data) {
        List<String> strList = new ArrayList<String>() {
            private static final long serialVersionUID = -8516123651013678218L;

            {
            add("教育培训");
            add("订报订刊");
            add("党内表彰");
            add("困难补助");
            add("受灾补助");
            add("上缴上级");
            add("下拨下级");
            add("其他");
        }};
        List<FeePayVO> recordList = feeMapper.getOrgFeeDisburseGroupByType(data.getOrgCode());
        setData(strList, recordList);
        return new OutMessage<>(Status.SUCCESS, recordList);
    }

    /**
     * 按类型统计党费支出占比
     *
     * @param data
     * @return
     */
    @Override
    public OutMessage getFeeDisburseByTypeRatio(ChartDataDTO data) {
        return this.getFeeDisburseByType(data);
    }

    /**
     * 按录入类型统计党费支出占比
     *
     * @param data
     * @return
     */
    @Override
    public OutMessage getFeeDisburseByInputRatio(ChartDataDTO data) {
        List<String> strList = new ArrayList<String>() {
            private static final long serialVersionUID = -7121523847327836901L;

            {
            add("手动录入");
            add("账单录入");
        }};
        List<FeePayVO> records = feeMapper.getOrgFeeDisburseGroupByInput(data.getOrgCode());
        setData(strList, records);

        return new OutMessage<>(Status.SUCCESS, records);
    }

    private void setData(List<String> strList, List<FeePayVO> records) {
        for (FeePayVO record : records) {
            String name = record.getName();
            strList.remove(name);
        }
        for (String str : strList) {
            FeePayVO feePayVO = new FeePayVO();
            feePayVO.setName(str);
            feePayVO.setCount(CommonConstant.ZERO);
            records.add(feePayVO);
        }
    }

    /**
     * 按计算类型统计党费缴纳占比
     *
     * @param data
     * @return
     */
    @Override
    public OutMessage getFeeByTypeRatio(ChartDataDTO data) {
        List<FeePayVO> records = feeMapper.getOrgFeePayGroupByType(data.getOrgCode());
        List<String> strList = new ArrayList<String>() {
            private static final long serialVersionUID = 1349628326603637396L;

            {
            add("按标准交纳");
            add("按工资比例");
            add("少交党费");
            add("免交党费");
        }};
        setData(strList, records);
        return new OutMessage<>(Status.SUCCESS, records);
    }

    /**
     * 统计总支出金额，总结余金额
     *
     * @param data
     * @return
     */
    @Override
    public OutMessage getFeeDisburseAndSurplus(ChartDataDTO data) {
        // 总支出金额
        BigDecimal feeDisburseCount = feeMapper.getFeeDisburseCount(data.getOrgCode(), null);
        feeDisburseCount = ObjectUtil.isNull(feeDisburseCount) ? BigDecimal.ZERO : feeDisburseCount;
        // 总下拨金额
        BigDecimal feeAllocate = feeMapper.getOrgFeeAllocate(data.getOrgCode());
        feeAllocate = ObjectUtil.isNull(feeAllocate) ? BigDecimal.ZERO : feeAllocate;
        List<String> list = new ArrayList<String>() {
            private static final long serialVersionUID = -3031068673191391109L;

            {
            add("总支出金额");
            add("总结余金额");
        }};
        List<Record> recordList = new ArrayList<>();
        Record recordData;
        recordData = new Record();
        recordData.set("name", list.get(0));
        recordData.set("count", feeDisburseCount);
        recordList.add(recordData);

        recordData = new Record();
        recordData.set("name", list.get(1));
        recordData.set("count", feeAllocate.subtract(feeDisburseCount));
        recordList.add(recordData);
        return new OutMessage<>(Status.SUCCESS, recordList);
    }

    /**
     * 统计总下拨金额，已下拨金额
     *
     * @param data
     * @return
     */
    @Override
    public OutMessage getFeeAllocateTotal(ChartDataDTO data) {
        BigDecimal orgFeeAllocate = feeMapper.getOrgFeeAllocate(data.getOrgCode());
        orgFeeAllocate = ObjectUtil.isNull(orgFeeAllocate) ? BigDecimal.ZERO : orgFeeAllocate;
        List<String> list = new ArrayList<String>() {
            private static final long serialVersionUID = -2755581630435541799L;

            {
            add("总下拨金额");
            add("已下拨金额");
        }};
        List<Record> recordList = new ArrayList<>();
        Record recordData;
        recordData = new Record();
        recordData.set("name", list.get(0));
        recordData.set("count", orgFeeAllocate);
        recordList.add(recordData);

        recordData = new Record();
        recordData.set("name", list.get(1));
        recordData.set("count", orgFeeAllocate);
        recordList.add(recordData);
        return new OutMessage<>(Status.SUCCESS, recordList);
    }

    /**
     * 统计总收取金额，已对账金额？
     *
     * @param data
     * @return
     */
    @Override
    public OutMessage getFeeAndCheckTotal(ChartDataDTO data) {

        String year = String.valueOf(DateUtil.year(new Date()));
        String month = String.valueOf(DateUtil.month(new Date()) + 1);

        // 总收取金额
        BigDecimal feePay = feeMapper.getFeePay(data.getOrgCode(), year, month, "yes");
        feePay = ObjectUtil.isNull(feePay) ? BigDecimal.ZERO : feePay;
        // 已对账金额
        BigDecimal checkMoney = BigDecimal.ZERO;
        List<String> list = new ArrayList<String>() {
            private static final long serialVersionUID = -3111208709810626356L;

            {
            add("总收取金额");
            add("已对账金额");
        }};
        List<Record> recordList = new ArrayList<>();
        Record recordData;
        recordData = new Record();
        recordData.set("name", list.get(0));
        recordData.set("count", feePay);
        recordList.add(recordData);

        recordData = new Record();
        recordData.set("name", list.get(1));
        recordData.set("count", checkMoney);
        recordList.add(recordData);
        return new OutMessage<>(Status.SUCCESS, recordList);
    }

    /**
     * 统计下拨党委，下拨党支部
     *
     * @param data
     * @return
     */
    @Override
    public OutMessage getFeeAllocateOrgCount(ChartDataDTO data) {
        List<FeePayVO> records = feeMapper.getFeeAllocateOrg(data.getOrgCode());
        Record temp = new Record();
        for (FeePayVO record : records) {
            temp.set(record.getOrgType(), record.getCount());
        }
        String dw = temp.getStr(ONE);
        dw = ObjectUtil.isNull(dw) ? "0" : dw;
        String dzb = temp.getStr(THREE);
        dzb = ObjectUtil.isNull(dzb) ? "0" : dzb;

        List<Record> recordList = new ArrayList<>();
        Record recordData;
        recordData = new Record();
        recordData.set("name", "党委");
        recordData.set("count", dw);
        recordList.add(recordData);

        recordData = new Record();
        recordData.set("name", "党支部");
        recordData.set("count", dzb);
        recordList.add(recordData);
        return new OutMessage<>(Status.SUCCESS, recordList);
    }

    /**
     * 下拨金额月份统计
     *
     * @param data
     * @return
     */
    @Override
    public OutMessage getFeeAllocateByMonth(ChartDataDTO data) {
        List<FeePayVO> records = feeMapper.getFeeAllocateGroupByMonth(data.getOrgCode());
        List<String> strList = new ArrayList<String>() {
            private static final long serialVersionUID = 4107030665053911506L;

            {
            add("1");
            add("2");
            add("3");
            add("4");
            add("5");
            add("6");
            add("7");
            add("8");
            add("9");
            add("10");
            add("11");
            add("12");
        }};
        for (FeePayVO record : records) {
            String month = record.getMonth().substring(0, record.getMonth().indexOf("."));
            record.setMonth(month);
            strList.remove(month);
        }
        for (String str : strList) {
            FeePayVO feePayVO = new FeePayVO();
            feePayVO.setMonth(str);
            feePayVO.setCount(CommonConstant.ZERO);
            records.add(feePayVO);
        }

        return new OutMessage<>(Status.SUCCESS, records);
    }

    /**
     * 党费导出
     *
     * @param feeListDTO
     */
    @Override
    public void exportFeeData(FeeListDTO feeListDTO) {
        String memOrgOrgCode = feeListDTO.getMemOrgOrgCode();
        // 生成文件名称
        String fileName = CacheUtils.getOrgNameByOrgCode(memOrgOrgCode) + "党费缴纳明细";
        /**
         * 1	年度明细(组织)
         * 2	月度明细(组织)
         * 3	当月明细(组织)
         * 4	年度明细(个人)
         * 5	月度明细(个人)
         * 6	当日明细(个人)
         */
        String exportCode = feeListDTO.getExportCode();
        // 导出时间
        Date exportDate = feeListDTO.getExportDate();

        // 是否包含下级，1--是，0--否
        String isContainLower = feeListDTO.getIsContainLower();
        // 条件
        Condition condition = field(name("delete_time")).isNull();
        if (exportCode.equals(CommonConstant.ONE) || exportCode.equals(CommonConstant.TWO) || exportCode.equals(CommonConstant.THREE)) {
            // 导出组织
            if (CommonConstant.ZERO.equals(isContainLower)) {
                // 导出直属下级
                this.exportUnderlingOrg(response, memOrgOrgCode, fileName, exportCode, exportDate, condition);
            } else {
                // 导出所有下级
                condition = condition.and(field(name("mem_org_org_code"), String.class).like(memOrgOrgCode + "%"));
                this.exportSubOrg(response, fileName, exportCode, exportDate, condition);
            }
        } else if (exportCode.equals(CommonConstant.FOUR) || exportCode.equals(CommonConstant.FIVE) || exportCode.equals(CommonConstant.SIX)) {
            condition = condition.and(field(name("mem_org_org_code"), String.class).like(memOrgOrgCode + "%"));
            this.exportMemFee(response, fileName, exportCode, exportDate, condition);
        }
    }

    /**
     * 导出直属下级
     *
     * @param response
     * @param memOrgOrgCode
     * @param fileName
     * @param exportCode
     * @param exportDate
     * @param condition
     */
    private void exportUnderlingOrg(HttpServletResponse response, String memOrgOrgCode, String fileName, String exportCode, Date exportDate, Condition condition) {
        // 开始时间
        DateTime exportDateBegin;
        // 结束时间
        DateTime exportDateEnd;
        // title
        String title;
        // 导出直属下级

        List<SubOrgVO> subOrgVOS = orgMapper.findSubOrgCode(memOrgOrgCode);
        List<Record> subOrgCode = new ArrayList<>();
        for (SubOrgVO subOrgVO : subOrgVOS) {
            Record record = new Record();
            record.set("code", subOrgVO.getCode());
            record.set("orgCode", subOrgVO.getOrgCode());
            subOrgCode.add(record);
        }
        // 直属下级层级码集合
        Map<String, String> orgCodeMap = new HashMap<>(subOrgCode.size());
        subOrgCode.forEach(record -> orgCodeMap.put(record.getStr("orgCode"), record.getStr("orgCode")));
        BigDecimal zeroBigDecimal = new BigDecimal(0);
        // 导出字段处理
        Collection<SelectFieldOrAsterisk> selectCollection = ChartUtil.getSelectCollectionByFunction(orgCodeMap, false,
                map -> sum(choose().when(field(name("mem_org_org_code"), String.class).like(map.getKey() + "%"), field(name("money"), BigDecimal.class))
                        .otherwise(zeroBigDecimal)).as(map.getValue()));

        // 处理导出时间
        if (exportCode.equals(CommonConstant.ONE)) {
            exportDateBegin = DateUtil.beginOfYear(exportDate);
            exportDateEnd = DateUtil.endOfYear(exportDate);
            int year = DateUtil.year(exportDate);
            title = year + "年度组织缴费明细";
        } else if (exportCode.equals(CommonConstant.TWO)) {
            exportDateBegin = DateUtil.beginOfMonth(exportDate);
            exportDateEnd = DateUtil.endOfMonth(exportDate);
            String format = DateUtil.format(exportDate, "yyyy-MM");
            title = format + "组织缴费明细";
        } else {
            exportDateBegin = DateUtil.beginOfMonth(exportDate);
            exportDateEnd = DateUtil.endOfMonth(exportDate);
            int month = DateUtil.month(exportDate) + 1;
            title = month + "月组织缴费明细";
        }
        // 导出时间
        condition = condition.and(field(name("pay_date"), java.sql.Date.class).greaterOrEqual(exportDateBegin.toSqlDate()))
                .and(field(name("pay_date"), java.sql.Date.class).lessOrEqual(exportDateEnd.toSqlDate()));
        if (exportCode.equals(CommonConstant.ONE) || exportCode.equals(CommonConstant.TWO)) {
            // sql
            SelectOptionStep selectOptionStep = DbUtil.getSelectRecord("ccp_fee_order", selectCollection, condition, null, null);
            Map<String, Object> recordMap = feeOrderMapper.getOneBySql(selectOptionStep.toString());
            if (recordMap == null) {
                ExcelUtil.exportErrorExcel(response, new OutMessage<>(Status.NOT_DATA));
                return;
            }
            // 要导出的集合
            List<Record> recordList = new ArrayList<>();
            this.processRecord(recordMap, recordList, exportCode, exportDate);
            ExcelUtil.exportBigExcel(title, null, recordList, fileName, response, (long) recordList.size(), null);
        } else {
            selectCollection.add(field(name("pay_date")).as("payDate"));
            // 排序
            Collection<OrderField<?>> orderCollection = new ArrayList<>();
            // sql
            orderCollection.add(field(name("pay_date")).desc());
            // 分组
            Collection<GroupField> groupCollection = new ArrayList<>();
            groupCollection.add(field(name("pay_date")));
            SelectOptionStep selectOptionStep = DbUtil.getSelectRecord("ccp_fee_order", selectCollection, condition, groupCollection, orderCollection);
            List<ExcelDecryptMap<String, Object>> recordData = feeOrderMapper.getListMapBySql(selectOptionStep.toString());
            if (CollectionUtil.isEmpty(recordData)) {
                ExcelUtil.exportErrorExcel(response, new OutMessage<>(Status.NOT_DATA));
                return;
            }
            List<Record> recordList = new ArrayList<>();
            recordData.forEach(record -> {
                Date payDate = (Date) record.get("payDate");
                record.remove("payDate");
                this.processRecord(record, recordList, exportCode, payDate);
            });
            // 自然排序
            // 降序写法报错 -- (Record record) -> record.get("mem_org_org_code")).reversed()
            List<Record> collect = recordList.stream().sorted(Comparator.comparing((record) -> record.getStr("组织名称"))).collect(Collectors.toList());
            ExcelUtil.exportBigExcel(title, null, collect, fileName, response, (long) recordList.size(), null);

        }
    }

    /**
     * 处理导出Record
     *
     * @param recordMap  导出map
     * @param exportCode 导出类型
     * @param exportDate 导出时间
     */
    private void processRecord(Map<String, Object> recordMap, List<Record> recordList, String exportCode, Date exportDate) {
        recordMap.forEach((key, value) -> {
            Record record = new Record();
            record.set("组织名称", CacheUtils.getOrgNameByOrgCode(key));
            if (exportCode.equals(CommonConstant.ONE)) {
                record.set("年份", DateUtil.year(exportDate));
            } else if (exportCode.equals(CommonConstant.TWO)) {
                record.set("月份", DateUtil.format(exportDate, "yyyy-MM"));
            } else {
                record.set("日期", DateUtil.format(exportDate, "yyyy-MM"));
            }
            record.set("金额", value == null ? 0 : value);
            recordList.add(record);
        });
    }

    /**
     * 导出人员党费
     *
     * @param response
     * @param fileName
     * @param exportCode
     * @param exportDate
     * @param condition
     */
    private void exportMemFee(HttpServletResponse response, String fileName, String exportCode, Date exportDate, Condition condition) {
        // 开始时间
        DateTime exportDateBegin;
        // 结束时间
        DateTime exportDateEnd;
        String title;
        // 导出人员
        if (exportCode.equals(CommonConstant.FOUR)) {
            // 年度明细(个人)
            exportDateBegin = DateUtil.beginOfYear(exportDate);
            exportDateEnd = DateUtil.endOfYear(exportDate);
            title = DateUtil.year(exportDate) + "年度个人缴费明细";
        } else if (exportCode.equals(CommonConstant.FIVE)) {
            // 月度明细(个人)
            exportDateBegin = DateUtil.beginOfMonth(exportDate);
            exportDateEnd = DateUtil.endOfMonth(exportDate);
            title = DateUtil.format(exportDate, "yyyy-MM") + "个人缴费明细";
        } else {
            // 当日明细(个人)
            exportDateBegin = DateUtil.beginOfDay(exportDate);
            exportDateEnd = DateUtil.endOfDay(exportDate);
            String chineseDate = DateUtil.formatChineseDate(exportDate, false,false);
            title = chineseDate + "个人缴费明细";
        }

        this.exportMemFee(condition, response, exportDateBegin, exportDateEnd, fileName, title, record -> {
            record.set("组织名称", CacheUtils.getOrgNameByOrgCode(record.getStr("组织名称")));
            record.set("姓名", CacheUtils.getMemName(record.getStr("姓名")));
            if (CommonConstant.ONE.equals(record.getStr("支付类型"))) {
                record.set("支付类型", "微信支付");
            } else {
                record.set("支付类型", "PC支付");
            }
            if (CommonConstant.ONE.equals(record.getStr("缴纳类型"))) {
                record.set("缴纳类型", "个人缴纳");
            } else {
                record.set("缴纳类型", "代缴");
            }
            String createCode = record.getStr("交纳人");
            if (StrKit.isBlank(createCode)) {
                record.set("交纳人", record.getStr("交纳账号"));
            } else {
                record.set("交纳人", CacheUtils.getMemName(createCode));
            }
        });
    }

    /**
     * 导出人员党费记录
     *
     * @param condition
     * @param response
     * @param exportDateBegin
     * @param exportDateEnd
     * @param fileName
     * @param title
     * @param recordProcessor
     */
    private void exportMemFee(Condition condition, HttpServletResponse response, DateTime exportDateBegin, DateTime exportDateEnd,
                              String fileName, String title, ExcelUtil.RecordProcessor recordProcessor) {
        // 导出字段
        Collection<SelectFieldOrAsterisk> selectCollection = new ArrayList<>();
        selectCollection.add(field(name("mem_org_org_code")).as("组织名称"));
        selectCollection.add(field(name("mem_code")).as("姓名"));
        selectCollection.add(field(name("pay_type")).as("支付类型"));
        selectCollection.add(field(name("pay_date")).as("缴费时间"));
        selectCollection.add(field(name("order_type")).as("缴纳类型"));
        selectCollection.add(field(name("money"), BigDecimal.class).as("金额"));
        selectCollection.add(field(name("creator_mem_code")).as("交纳人"));
        selectCollection.add(field(name("creator_account")).as("交纳账号"));

        // 排序
        Collection<OrderField<?>> orderCollection = new ArrayList<>();
        // 导出时间
        condition = condition.and(field(name("pay_date"), java.sql.Date.class).greaterOrEqual(exportDateBegin.toSqlDate()))
                .and(field(name("pay_date"), java.sql.Date.class).lessOrEqual(exportDateEnd.toSqlDate()));
        // sql
        orderCollection.add(field(name("pay_date")).desc());
        this.queryAndProcessor(condition, response, fileName, title, selectCollection, recordProcessor, null, orderCollection);
    }

    /**
     * 导出所有下级
     *
     * @param response
     * @param fileName
     * @param exportCode
     * @param exportDate
     * @param condition
     */
    private void exportSubOrg(HttpServletResponse response, String fileName, String exportCode, Date exportDate, Condition condition) {
        // 开始时间
        DateTime exportDateBegin;
        // 结束时间
        DateTime exportDateEnd;
        String date;
        String columnName;
        String title;
        // 导出字段
        Collection<SelectFieldOrAsterisk> selectCollection = new ArrayList<>();
        selectCollection.add(field(name("mem_org_org_code")).as("组织名称"));
        selectCollection.add(sum(field(name("money"), BigDecimal.class)).as("金额"));
        if (exportCode.equals(CommonConstant.ONE)) {
            // 年度明细(组织)
            // 开始时间
            exportDateBegin = DateUtil.beginOfYear(exportDate);
            // 结束时间
            exportDateEnd = DateUtil.endOfYear(exportDate);
            // 年份
            date = String.valueOf(DateUtil.year(exportDate));
            columnName = "年份";
            title = date + "年度组织缴费明细";
        } else if (exportCode.equals(CommonConstant.TWO)) {
            // 月度明细(组织)
            exportDateBegin = DateUtil.beginOfMonth(exportDate);
            // 结束时间
            exportDateEnd = DateUtil.endOfMonth(exportDate);
            // 月份
            date = DateUtil.format(exportDate, "yyyy-MM");
            columnName = "月份";
            title = date + "组织缴费明细";
        } else {
            // 当月明细(组织)
            exportDateBegin = DateUtil.beginOfMonth(exportDate);
            // 结束时间
            exportDateEnd = DateUtil.endOfMonth(exportDate);
            int month = DateUtil.month(exportDate) + 1;
            title = month + "月组织缴费明细";
            selectCollection = new ArrayList<>();
            selectCollection.add(field(name("mem_org_org_code")).as("组织名称"));
            selectCollection.add(field(name("pay_date")).as("日期"));
            selectCollection.add(sum(field(name("money"), BigDecimal.class)).as("金额"));

            this.exportMonthOrg(condition, response, exportDateBegin, exportDateEnd, fileName, title, selectCollection,
                    record -> record.set("组织名称", CacheUtils.getOrgNameByOrgCode(record.getStr("组织名称"))));
            return;
        }

        // 导出
        this.exportGroupOrg(condition, response, exportDateBegin, exportDateEnd, fileName, title, selectCollection, record -> {
            record.set("组织名称", CacheUtils.getOrgNameByOrgCode(record.getStr("组织名称")));
            record.set(columnName, date);
        });
    }

    /**
     *
     */
    private void exportMonthOrg(Condition condition, HttpServletResponse response, DateTime exportDateBegin, DateTime exportDateEnd,
                                String fileName, String title, Collection<SelectFieldOrAsterisk> selectCollection, ExcelUtil.RecordProcessor recordProcessor) {

        // 分组
        Collection<GroupField> groupCollection = new ArrayList<>();
        // 排序
        Collection<OrderField<?>> orderCollection = new ArrayList<>();
        // 导出时间

        condition = condition.and(field(name("pay_date"), java.sql.Date.class).greaterOrEqual(exportDateBegin.toSqlDate()))
                .and(field(name("pay_date"), java.sql.Date.class).lessOrEqual(exportDateEnd.toSqlDate()));
        // sql
        orderCollection.add(field(name("mem_org_org_code")).asc());
        orderCollection.add(field(name("pay_date")).asc());
        groupCollection.add(field(name("mem_org_org_code")));
        groupCollection.add(field(name("pay_date")));
        this.queryAndProcessor(condition, response, fileName, title, selectCollection, recordProcessor, groupCollection, orderCollection);
    }

    /**
     * 获取下级层级码
     *
     * @param memOrgOrgCode
     * @return
     */
    public List<SubOrgVO> findSubOrgCode(String memOrgOrgCode) {
        SelectSeekStep2<Record2<Object, Object>, Object, Object> record2s = DbUtil.DSL_CONTEXT.select(field(name("code")),
                field(name("org_code")).as("orgCode"))
                .from(name("ccp_org"))
                .where(field(name("org_code"), String.class).like(memOrgOrgCode + "___")
                        .or(field(name("org_code"), String.class).eq(memOrgOrgCode)))
                .and(field(name("delete_time")).isNull())
                .orderBy(field(name("create_time")).desc(), field(name("id")));
        return orgMapper.findSubOrgCode(record2s.toString());
    }

    /**
     * 查询处理
     *
     * @param condition
     * @param response
     * @param fileName
     * @param title
     * @param selectCollection
     * @param recordProcessor
     * @param groupCollection
     * @param orderCollection
     */
    private void queryAndProcessor(Condition condition, HttpServletResponse response, String fileName, String title, Collection<SelectFieldOrAsterisk> selectCollection, ExcelUtil.RecordProcessor recordProcessor, Collection<GroupField> groupCollection, Collection<OrderField<?>> orderCollection) {
        SelectOptionStep sql = DbUtil.getSelectRecord("ccp_fee_order", selectCollection, condition, groupCollection, orderCollection);
        // count
        Collection<SelectFieldOrAsterisk> selectCollectionCount = new ArrayList<>();
        selectCollectionCount.add(DSL.count(field("1")));
        SelectOptionStep step = DbUtil.getSelectRecord("ccp_fee_order", selectCollectionCount, condition, groupCollection, null);
        Long total = feeOrderMapper.getTotalBysql(step.toString());
        if (total == null || total == 0) {
            ExcelUtil.exportErrorExcel(response, new OutMessage<>(Status.NOT_DATA));
            return;
        }
        ExcelUtil.processorAndExport(total, null, sql, fileName, title, response, recordProcessor);
    }

    /**
     * 导出组织分类明细
     *
     * @param condition        条件
     * @param response         响应
     * @param exportDateBegin  开始时间
     * @param exportDateEnd    结束时间
     * @param fileName         文件名
     * @param title            工作薄名称
     * @param selectCollection 查询字段
     * @param recordProcessor  自定义操作
     */
    private void exportGroupOrg(Condition condition, HttpServletResponse response, DateTime exportDateBegin, DateTime exportDateEnd,
                                String fileName, String title, Collection<SelectFieldOrAsterisk> selectCollection, ExcelUtil.RecordProcessor recordProcessor) {
        // 分组
        Collection<GroupField> groupCollection = new ArrayList<>();
        // 排序
        Collection<OrderField<?>> orderCollection = new ArrayList<>();
        // 导出时间

        condition = condition.and(field(name("pay_date"), java.sql.Date.class).greaterOrEqual(exportDateBegin.toSqlDate()))
                .and(field(name("pay_date"), java.sql.Date.class).lessOrEqual(exportDateEnd.toSqlDate()));
        // sql
        orderCollection.add(field(name("mem_org_org_code")).asc());
        groupCollection.add(field(name("mem_org_org_code")));
        this.queryAndProcessor(condition, response, fileName, title, selectCollection, recordProcessor, groupCollection, orderCollection);
    }
}
