package com.zenith.front.core.service.org;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.zenith.front.api.org.IOrgExampleSiteService;
import com.zenith.front.dao.mapper.org.OrgExampleSiteMapper;
import com.zenith.front.model.dto.OrgContactDTO;
import com.zenith.front.model.message.OutMessage;
import com.zenith.front.model.message.Status;
import com.zenith.front.model.bean.OrgExampleSite;
import com.zenith.front.model.vo.OrgContactVO;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import java.util.Date;

/**
 * <p>
 * 党支部标准化规范化建设达标尿范点情况 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-12-14
 */
@Service
public class OrgExampleSiteServiceImpl extends ServiceImpl<OrgExampleSiteMapper, OrgExampleSite> implements IOrgExampleSiteService {

    /**
     * 修改党支部达标/示范点标记
     */
    @Override
    public OutMessage<?> updateExampleSite(OrgContactDTO data) {
        OrgExampleSite exampleSite = new OrgExampleSite();
        BeanUtils.copyProperties(data, exampleSite);
        if (StrUtil.isEmpty(data.getD157Code())){
            exampleSite.setExampleSiteDate(null);
            exampleSite.setIdentifyUnit(null);
        }
        if (StrUtil.equals(data.getHasStandardUp(),"1")){
            exampleSite.setAcceptTime(null);
            exampleSite.setAcceptUnit(null);
        }
        exampleSite.setUpdateTime(new Date());
        return new OutMessage<>(this.updateById(exampleSite) ? Status.SUCCESS : Status.FAIL);
    }

    /**
     * 党支部达标/示范点标记
     */
    @Override
    public OutMessage<?> findExampleSiteById(String orgCode) {
        OrgContactVO orgContactVO=new OrgContactVO();
        OrgExampleSite orgExampleSite = baseMapper.selectOne(new LambdaQueryWrapper<OrgExampleSite>()
                .eq(OrgExampleSite::getOrgCode, orgCode)
                .isNotNull(OrgExampleSite::getCreateTime));
        if (ObjectUtil.isEmpty(orgExampleSite)) {
            OrgExampleSite exampleSite = new OrgExampleSite();
            exampleSite.setOrgCode(orgCode);
            exampleSite.setCreateTime(new Date());
            this.save(exampleSite);
        }
        OrgExampleSite orgExampleSite1 = baseMapper.selectOne(new LambdaQueryWrapper<OrgExampleSite>()
                .eq(OrgExampleSite::getOrgCode, orgCode));
        BeanUtils.copyProperties(orgExampleSite1,orgContactVO);
        return new OutMessage<>(Status.SUCCESS, orgContactVO);
    }
}
