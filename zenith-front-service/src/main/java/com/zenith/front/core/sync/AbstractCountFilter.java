package com.zenith.front.core.sync;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2019/6/211:27 PM
 */
public abstract class AbstractCountFilter<T> {


    public static final Map<String,AbstractCountFilter> FILTER_CENTER = new HashMap<>();

    /***
     * 预期参数
     * */
    private T value;


    public AbstractCountFilter(){
        FILTER_CENTER.put(getName(),this);
    }

    /***
     * 判断是否符合预期
     * @param t 期望参数
     * @return 是否符合预期
     * */
    public abstract boolean check(T t);

    /***
     * 获取预期参数
     * */
    protected T getValue(){
        return value;
    }
    /***
     * 设置预期参数
     * **/
    public AbstractCountFilter<T> setValue(String value) {
        this.value = convert(value);
        return this;
    }

    /***
     * 获取filter名称
     * @return 名称
     * */
    public abstract String getName();
    /***
     * 字符串转换
     * @param value 配置文件参数
     * @return 转换类型
     * */
    protected abstract T convert(String value);
}
