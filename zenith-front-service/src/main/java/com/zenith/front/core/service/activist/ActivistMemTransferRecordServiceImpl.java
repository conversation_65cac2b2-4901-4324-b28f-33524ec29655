package com.zenith.front.core.service.activist;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.zenith.front.api.activist.IActivistTransferApprovalService;
import com.zenith.front.api.activist.IActivistTransferRecordService;
import com.zenith.front.api.develop.IDevelopStepLogAllService;
import com.zenith.front.api.develop.IDevelopStepLogService;
import com.zenith.front.api.mem.IMemDevelopAllService;
import com.zenith.front.api.mem.IMemDevelopService;
import com.zenith.front.api.org.IOrgDevelopRightsService;
import com.zenith.front.api.org.IOrgService;
import com.zenith.front.common.constant.RetConstant;
import com.zenith.front.common.constant.TransferRecordConstant;
import com.zenith.front.common.kit.HttpKit;
import com.zenith.front.common.kit.Ret;
import com.zenith.front.common.kit.StrKit;
import com.zenith.front.core.cache.CacheUtils;
import com.zenith.front.dao.mapper.mem.MemDevelopProcessMapper;
import com.zenith.front.dao.mapper.mem.MemDigitalMapper;
import com.zenith.front.dao.mapper.mem.MemDigitalOperationLogMapper;
import com.zenith.front.model.bean.*;
import com.zenith.front.model.constant.UserConstant;
import com.zenith.front.model.custom.Record;
import com.zenith.front.model.message.OutMessage;
import com.zenith.front.model.message.Status;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2022/5/9
 */
@Slf4j
@Service
public class ActivistMemTransferRecordServiceImpl {
    @Resource
    private IOrgService orgService;
    @Resource
    private IOrgDevelopRightsService iOrgDevelopRightsService;
    @Resource
    private IMemDevelopService iMemDevelopService;
    @Resource
    private IMemDevelopAllService iMemDevelopAllService;
    @Resource
    private IDevelopStepLogService iDevelopStepLogService;
    @Resource
    private IDevelopStepLogAllService iDevelopStepLogAllService;
    @Resource
    private IActivistTransferRecordService iActivistTransferRecordService;
    @Resource
    private IActivistTransferApprovalService iActivistTransferApprovalService;
    @Resource
    private MemDevelopProcessMapper memDevelopProcessMapper;
    @Resource
    private MemDigitalMapper memDigitalMapper;
    @Resource
    private MemDigitalOperationLogMapper digitalOperationLogMapper;

    private static final Pattern ORG_REGEX = Pattern.compile("(\\d{3})");
    @Value("baseOrgOrgCode")
    private String baseOrgOrgCode;
    @Value("${sync_flow_push}")
    private String sync_flow_push;
    @Value("${exchange_nginx_key}")
    private String exchange_nginx_key;

    /**
     * 判断转接类型
     */
    public boolean setTransferTypeCode(String type, ActivistTransferRecord transferRecord) {
        List<Record> dicIn = CacheUtils.getDic(TransferRecordConstant.DICT_IN);
        List<Record> dicOut = CacheUtils.getDic(TransferRecordConstant.DICT_OUT);
        switch (type.charAt(0)) {
            //转出类型
            case TransferRecordConstant.OUT_TYPE_KEY_PREFIX_REX:
                //设置转出类型
                transferRecord.setOutType(type);
                //查询到对应的字典表数据,设置相应的数据
                Optional<Record> inOptional = dicIn.stream().filter(re -> re.getStr("ref").equalsIgnoreCase(type)).findFirst();
                if (!inOptional.isPresent()) {
                    return false;
                }
                transferRecord.setInType(inOptional.get().getStr("key"));
                break;
            //转入类型
            case TransferRecordConstant.IN_TYPE_KEY_PREFIX_REX:
                //设置转入类型
                transferRecord.setInType(type);
                Optional<Record> outOptional = dicOut.stream().filter(re -> re.getStr("ref").equalsIgnoreCase(type)).findFirst();
                if (!outOptional.isPresent()) {
                    return false;
                }
                transferRecord.setOutType(outOptional.get().getStr("key"));
                break;
            default:
                return false;
        }
        transferRecord.setType(type);
        return true;
    }

    /**
     * 设置源组织和目标组织id
     */
    public Ret setSrcAndTargetOrgId(String srcOrgId, String targetOrgId, ActivistTransferRecord transferRecord) {
        if (StrKit.isBlank(srcOrgId) || StrKit.isBlank(targetOrgId)) {
            return Ret.fail(RetConstant.MESSAGE, new OutMessage<>(Status.TRANSFER_SRC_ORG_IS_NOT_EXIST));
        }
        Org srcOrg = orgService.findOrgByCode(srcOrgId);
        if (Objects.isNull(srcOrg)) {
            return Ret.fail(RetConstant.MESSAGE, new OutMessage<>(Status.TRANSFER_SRC_ORG_IS_NOT_EXIST));
        }
        //获取当前管理的组织id
        String userPermissionOrgCode = UserConstant.USER_CONTEXT.get().getUserRolePermission().getOrgCode();
        //判断是否有权限调整
        if (!StrUtil.startWith(srcOrg.getOrgCode(), userPermissionOrgCode)) {
            return Ret.fail(RetConstant.MESSAGE, new OutMessage<>(Status.PERMISSION_DENIED));
        }
        transferRecord.setSrcOrgId(srcOrgId);
        transferRecord.setTargetOrgId(targetOrgId);
        return Ret.ok();
    }

    /**
     * 设置源组织和目标组织当前组织关系
     */
    public Ret setSrcAndTargetRelation(String srcOrgId, String targetOrgId, ActivistTransferRecord transferRecord) {
        //源组织id不能为空、且如果源组织没有任何上级,则该节点脱节
        List<Org> srcParentOrg = orgService.findAllParentOrg(srcOrgId);
        if (CollUtil.isEmpty(srcParentOrg) || srcParentOrg.stream().anyMatch(org -> StrKit.isBlank(org.getParentCode()))) {
            return Ret.fail(RetConstant.MESSAGE, new OutMessage<>(Status.TRANSFER_SRC_ORG_IS_NOT_EXIST));
        }
        //如果只查询出一条,那么这一条就是该组织的本身id,按照常理是不可能出现这种数据的,除非是顶级节点 或者该节点脱节,顶级节点是不允许被调整的
        if (srcParentOrg.size() == 1) {
            return Ret.fail(RetConstant.MESSAGE, new OutMessage<>(Status.PERMISSION_DENIED));
        }
        List<String> list = srcParentOrg.stream().map(Org::getCode).collect(Collectors.toList());
        //需要取消掉关系转接支部间人员调整放开
        if (!StrUtil.equals(transferRecord.getType(), "29")) {
            // 是否具有预备党员审批权限的上级, 如果一个都没有, 那么不允许发起转接
            List<OrgDevelopRights> orgDevelopRights = iOrgDevelopRightsService.findOrgDevelopRightsByCodeList(list);
            if (CollUtil.isEmpty(orgDevelopRights)) {
                return Ret.fail(RetConstant.MESSAGE, new OutMessage<>(Status.ACTIVIST_PERMISSION_NODE_ERR0));
            }
            // 这里是按照层级设置的，只取出最后一个即可
            String checkTwoOrgCode = orgDevelopRights.get(orgDevelopRights.size() - 1).getOrgCode();
            list.set(list.size() - 2, checkTwoOrgCode);
        }
        transferRecord.setSrcOrgRelationRelAsList(list);
        if (srcParentOrg.size() == 2) {
            transferRecord.setSrcOrgRelationAsList(Arrays.asList(list.get(list.size() - 2), list.get(list.size() - 1)));
        } else if (srcParentOrg.size() > 2) {
            transferRecord.setSrcOrgRelationAsList(Arrays.asList(list.get(0), list.get(list.size() - 2), list.get(list.size() - 1)));
        }

        List<Org> targetParentOrg = new ArrayList<>();
        // 原来是本地组织，所以有上级,拆分为多个数据节点后，本地库可能不存在，为空则代表是非本地库
        Org orgByCode = orgService.findOrgByCode(targetOrgId);
        if (ObjectUtil.isNotNull(orgByCode)) {
            targetParentOrg = orgService.findAllParentOrg(targetOrgId);
            if (CollUtil.isEmpty(targetParentOrg) || targetParentOrg.stream().anyMatch(org -> StrKit.isBlank(org.getParentCode()))) {
                return Ret.fail(RetConstant.MESSAGE, new OutMessage<>(Status.TRANSFER_TARGET_ORG_IS_NOT_EXIST));
            }
        } else {
            Ret ret = this.getTargetOrgListByMiddle(targetParentOrg, targetOrgId);
            if (ret.isFail()) {
                return ret;
            }
        }

        List<String> list1 = targetParentOrg.stream().map(Org::getCode).collect(Collectors.toList());
        transferRecord.setTargetOrgRelationRelAsList(list1);
        if (targetParentOrg.size() == 2) {
            transferRecord.setTargetOrgRelationAsList(Arrays.asList(list1.get(list1.size() - 2), list1.get(list1.size() - 1)));
        } else if (targetParentOrg.size() > 2) {
            transferRecord.setTargetOrgRelationAsList(Arrays.asList(list1.get(0), list1.get(list1.size() - 2), list1.get(list1.size() - 1)));
        } else {
            transferRecord.setTargetOrgRelationAsList(list1);
        }
        return Ret.ok();
    }

    /**
     * 获取中间交换区组织信息
     */
    private Ret getTargetOrgListByMiddle(List<Org> targetParentOrg, String targetOrgId) {
        // 这里走数据中间交换区，并且要给出中间交换区顶层节点
        log.warn("发起转接得时候，通过交换区开始查询数据");
        JSONObject data = this.findOrgNameByMiddle(targetOrgId);
        if (Objects.nonNull(data)) {
            //处理跨数据节点转接顶层共同节点为贵州省
            Org org1 = new Org();
            org1.setCode("5048C51OE8B74ACF891A1EE5143F85A7");
            targetParentOrg.add(org1);
            //处理次一级节点
            Org org2 = new Org();
            JSONObject jsonObject = this.findOrgSuperByMiddle(data.getString("code"), data.getString("exchangeKey"));
            if (Objects.isNull(jsonObject) || StrUtil.isEmpty(jsonObject.getString("code"))) {
                return Ret.fail(RetConstant.MESSAGE, new OutMessage<>(Status.TARGET_ORG_PERMISSION_NODE_ERR0));
            }
            org2.setCode(jsonObject.getString("code"));
            targetParentOrg.add(org2);
            //处理最后节点
            Org org3 = new Org();
            org3.setCode(targetOrgId);
            targetParentOrg.add(org3);
        } else {
            return Ret.fail(RetConstant.MESSAGE, new OutMessage<>(500, "生成转接得时候，中间交换区没有目的组织得信息，请查看中间交换区!", null));
        }
        return Ret.ok();
    }

    /**
     * 设置公共节点
     */
    public Ret setCommonNodeId(String srcOrgId, String targetOrgId, ActivistTransferRecord transferRecord) {
        Org srcOrgRecord = orgService.findOrgByCode(srcOrgId);
        Org targetOrgRecord = orgService.findOrgByCode(targetOrgId);
        if (targetOrgRecord == null) {
            return setTopCode(srcOrgRecord, transferRecord);
        }
        //系统内转接
        String srcOrgCode = srcOrgRecord.getOrgCode();
        String targetOrgCode = targetOrgRecord.getOrgCode();
        Matcher matcher = ORG_REGEX.matcher(srcOrgCode);
        //生成公共节点 现在只能是公共最高节点
        String commonOrgCode = "";
        Org commonOrg = null;
        int i = 0;
        while (true) {
            matcher.find();
            commonOrgCode += matcher.group();
            commonOrg = orgService.findOrgByOrgCode(commonOrgCode);
            if (Objects.nonNull(commonOrg)) {
                break;
            }
            i++;
            if (i > 19) {
                break;
            }
        }
        if (StrKit.isBlank(commonOrgCode) || commonOrg == null) {
            return Ret.fail(RetConstant.MESSAGE, new OutMessage<>(Status.TRANSFER_SRC_ORG_IS_NOT_EXIST));
        }
        //如果公共节点等于目标节点
        //分为两种情况
        //1 目标节点为顶级节点 则 公共节点为 顶级节点
        //2 目标节点为源节点上级
        if (StrUtil.equalsIgnoreCase(commonOrgCode, targetOrgCode)) {
            if (StrUtil.startWith(srcOrgCode, targetOrgCode)) {
                //判断是否是顶级节点
                //目标组织关系里面,如果只有一条那么就是组织它本身
                //经过前面的参数校验,脱节的组织是不可能存在集合里面
                //所以目标组织是顶级节点
                int size = transferRecord.getTargetOrgRelationAsList().size();
                if (size != 1) {
                    commonOrg.setCode(targetOrgRecord.getParentCode());
                }
            }
        }
        //判断不能转到自己的下级去
        if (StrUtil.startWith(targetOrgCode, srcOrgCode)) {
            return Ret.fail(RetConstant.MESSAGE, new OutMessage<>(Status.TRANSFER_CHANGE_TARGET_FOR_SRC_ERROR));
        }
        transferRecord.setCommonOrgId(commonOrg.getCode());
        transferRecord.setCommonOrgName(commonOrg.getName());
        return Ret.ok();
    }

    private Ret setTopCode(Org org, ActivistTransferRecord transferRecord) {
        Matcher matcher = ORG_REGEX.matcher(org.getOrgCode());
        if (!matcher.find()) {
            return Ret.fail(RetConstant.MESSAGE, new OutMessage<>(Status.PARA_ERROR));
        }
        //顶层节点
        String topCode = matcher.group();
        if (StrKit.isBlank(topCode)) {
            return Ret.fail(RetConstant.MESSAGE, new OutMessage<>(Status.PARA_ERROR));
        }
        Org topOrg = orgService.findOrgByOrgCode(topCode);
        // 数据库未找到顶层节点，代表可能是从中间交换区走得，中间交换区给出默认节点,并且把转出节点得最高节点替换为这个
        if (topOrg == null) {
            topOrg = new Org();
            topOrg.setName("中共贵州省委员会");
            topOrg.setCode("5048C51OE8B74ACF891A1EE5143F85A7");
            List<String> srcOrgRelationAsList = transferRecord.getSrcOrgRelationAsList();
            srcOrgRelationAsList.set(0, "5048C51OE8B74ACF891A1EE5143F85A7");
            transferRecord.setSrcOrgRelationAsList(srcOrgRelationAsList);
        }
        transferRecord.setCommonOrgId(topOrg.getCode());
        transferRecord.setCommonOrgName(topOrg.getName());
        return Ret.ok();
    }


    /**
     * 修改党员状态是否在组织关系转接中
     *
     * @param memIds 人员的id
     */
    public void updateMemIsTransfer(String transFerId, List<String> memIds) {
        List<ActivistTransferRecord> transferRecordList = iActivistTransferRecordService.list(new LambdaQueryWrapper<ActivistTransferRecord>().in(ActivistTransferRecord::getMemId, memIds)
                .eq(ActivistTransferRecord::getId, transFerId));
        // 如果一个人被反复转接的情况下，这里可能会拿到一条完成的记录，导致出现问题
        Map<String, List<ActivistTransferRecord>> transferRecordMap = transferRecordList.stream().collect(Collectors.groupingBy(ActivistTransferRecord::getMemId));
        for (Map.Entry<String, List<ActivistTransferRecord>> entry : transferRecordMap.entrySet()) {
            int isTransfer = 1;
            // 如关系转接已经转接完成或者已经撤销，那么就要置换为0
            for (ActivistTransferRecord transferRecord : entry.getValue()) {
                if (ObjectUtil.equal(transferRecord.getStatus(), 1) || ObjectUtil.equal(transferRecord.getStatus(), 2)) {
                    isTransfer = 0;
                    break;
                }
            }
            iMemDevelopService.update(new LambdaUpdateWrapper<MemDevelop>().set(MemDevelop::getIsTransfer, isTransfer).eq(MemDevelop::getCode, entry.getKey()));
            //发起关系转接的时候，同步人员all表数据
            iMemDevelopAllService.update(new LambdaUpdateWrapper<MemDevelopAll>().set(MemDevelopAll::getIsTransfer, isTransfer).eq(MemDevelopAll::getCode, entry.getKey()));
            //同步发展记录表
            iDevelopStepLogService.update(new LambdaUpdateWrapper<DevelopStepLog>().set(DevelopStepLog::getIsTransfer, isTransfer).eq(DevelopStepLog::getMemCode, entry.getKey()));
            iDevelopStepLogAllService.update(new LambdaUpdateWrapper<DevelopStepLogAll>().set(DevelopStepLogAll::getIsTransfer, isTransfer).eq(DevelopStepLogAll::getMemCode, entry.getKey()));
        }
    }

    /**
     * 获取中间交换区组织信息
     */
    public JSONObject findOrgNameByMiddle(String orgCode) {
        String replaceUrl = sync_flow_push.replace("/api/flow/push", "");
        JSONObject postData = new JSONObject();
        postData.put("orgCode", orgCode);
        String res = HttpKit.doPost(replaceUrl + "/org/findOrgName", postData, "UTF-8");
        log.warn("查询到组织信息!" + res);
        if (StrKit.notBlank(res)) {
            JSONObject jsonObject = JSONObject.parseObject(res);
            if (Objects.nonNull(jsonObject) && jsonObject.containsKey("data")) {
                return jsonObject.getJSONObject("data");
            }
        }
        return null;
    }

    /**
     * 获取中间交换区组织信息
     */
    public JSONObject findOrgSuperByMiddle(String orgCode, String exchangeKey) {
        String replaceUrl = sync_flow_push.replace("/api/flow/push", "");
        JSONObject postData = new JSONObject();
        postData.put("orgCode", orgCode);
        postData.put("exchangeKey", exchangeKey);
        String res = HttpKit.doPost(replaceUrl + "/activist/transfer/findOrgSuperDw", postData, "UTF-8");
        log.warn("查询到上级组织信息!" + res);
        if (StrKit.notBlank(res)) {
            JSONObject jsonObject = JSONObject.parseObject(res);
            if (Objects.nonNull(jsonObject) && jsonObject.containsKey("data")) {
                return jsonObject.getJSONObject("data");
            }
        }
        return null;
    }


    /**
     * 向中间交换区存储数据
     */
    public void insertDataByMiddle(String recordId) {
        //查询转接记录
        ActivistTransferRecord recordNow = iActivistTransferRecordService.getById(recordId);
        //处理携带的人员信息
        String memId = recordNow.getMemId();
        MemDevelop memDevelop = iMemDevelopService.findAllByCode(memId);
        List<DevelopStepLog> developStepLogs = iDevelopStepLogService.findAllListByMemCode(memId);
        if (ObjectUtil.isNotNull(memDevelop)) {
            recordNow.setExtraData(JSONObject.parseObject(JSONObject.toJSONString(memDevelop)));
            recordNow.setExtraDataLog(JSONArray.parseArray(JSONArray.toJSONString(developStepLogs)));
        }
        this.processDigitalData(memDevelop, recordNow);

        JSONObject jsonObject = JSONObject.parseObject(JSONObject.toJSONString(recordNow));
        //查询转接节点审批记录
        List<ActivistTransferApproval> approvalList = iActivistTransferApprovalService.findByRecordId(recordNow.getId());
        JSONArray jsonArray = new JSONArray();
        approvalList.forEach(e -> jsonArray.add(JSONObject.parseObject(JSONObject.toJSONString(e))));
        jsonObject.put("approval", jsonArray);
        String replaceUrl = sync_flow_push.replace("/api/flow/push", "");
        String doPost = HttpKit.doPost(replaceUrl + "/activist/transfer/insertData", jsonObject, "UTF-8");
        log.warn("中间交换区存储数据 ---->>> " + doPost);
    }

    /**
     * 处理档案数据
     */
    public void processDigitalData(MemDevelop memDevelop, ActivistTransferRecord recordNow) {
        if (Objects.isNull(memDevelop) || StrUtil.isBlank(memDevelop.getDigitalLotNo())) {
            return;
        }
        // 1、流程表 ccp_mem_develop_process
//        LambdaQueryWrapper<MemDevelopProcess> wrapper = new LambdaQueryWrapper<MemDevelopProcess>()
//                .eq(MemDevelopProcess::getDigitalLotNo, memDevelop.getDigitalLotNo());
//        List<MemDevelopProcess> processList = memDevelopProcessMapper.selectList(wrapper);
        // 2、档案表 ccp_mem_digital
        LambdaQueryWrapper<MemDigital> wrapper1 = new LambdaQueryWrapper<MemDigital>()
                .isNull(MemDigital::getDeleteTime)
                .eq(MemDigital::getDigitalLotNo, memDevelop.getDigitalLotNo());
        List<MemDigital> memDigitalList = memDigitalMapper.selectList(wrapper1);
        // 3、档案操作日志表 ccp_mem_digital_operation_log
        LambdaQueryWrapper<MemDigitalOperationLog> wrapper2 = new LambdaQueryWrapper<MemDigitalOperationLog>()
                .isNull(MemDigitalOperationLog::getDeleteTime)
                .eq(MemDigitalOperationLog::getDigitalLotNo, memDevelop.getDigitalLotNo());
        List<MemDigitalOperationLog> operationLogList = digitalOperationLogMapper.selectList(wrapper2);

//        recordNow.setExtraDataProcess(JSONArray.parseArray(JSONArray.toJSONString(processList)));
        recordNow.setExtraDataDigital(JSONArray.parseArray(JSONArray.toJSONString(memDigitalList)));
        recordNow.setExtraDataDigitalLog(JSONArray.parseArray(JSONArray.toJSONString(operationLogList)));
    }

}
