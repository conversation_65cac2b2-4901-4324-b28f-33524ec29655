package com.zenith.front.core.service.role;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.zenith.front.api.permission.IPermissionService;
import com.zenith.front.api.role.IRoleService;
import com.zenith.front.api.user.IUserRolePermissionService;
import com.zenith.front.api.user.IUserService;
import com.zenith.front.common.constant.CommonConstant;
import com.zenith.front.common.constant.PermissionConstant;
import com.zenith.front.common.constant.RoleConstant;
import com.zenith.front.common.kit.StrKit;
import com.zenith.front.core.cache.CacheUtils;
import com.zenith.front.dao.mapper.role.RoleMapper;
import com.zenith.front.model.dto.RoleDTO;
import com.zenith.front.model.dto.UpdatePermissionDto;
import com.zenith.front.model.dto.UserDTO;
import com.zenith.front.model.message.OutMessage;
import com.zenith.front.model.message.Status;
import com.zenith.front.model.bean.Permission;
import com.zenith.front.model.bean.Role;
import com.zenith.front.model.bean.User;
import com.zenith.front.model.bean.UserRolePermission;
import com.zenith.front.model.custom.UserTicket;
import com.zenith.front.model.vo.RoleVo;
import com.zenith.front.model.vo.TreeNodeVo;
import com.zenith.front.model.vo.UserLoginVo;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static com.zenith.front.common.constant.CommonConstant.MINUS_ZERO;
import static com.zenith.front.common.constant.PermissionConstant.PRE_TOKEN;
import static com.zenith.front.common.constant.RoleConstant.IS_BUILT_IN;
import static com.zenith.front.model.constant.UserConstant.USER_CONTEXT;
import static com.zenith.front.core.kit.TreeUtil.buildByRecursive;

/**
 * <p>
 * 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-07-10
 */
@Service
public class RoleServiceImpl extends ServiceImpl<RoleMapper, Role> implements IRoleService {

    @Resource
    private RoleMapper roleMapper;
    @Resource
    private IRoleService roleDao;
    @Resource
    private IUserRolePermissionService userRolePermissionDao;
    @Resource
    private IUserService userDao;
    @Resource
    private IPermissionService permissionDao;

    @Override
    public Role findByIdAndValid(String roleID) {
        LambdaQueryWrapper<Role> queryWrapper = new LambdaQueryWrapper<Role>()
                .eq(Role::getId, roleID)
                .eq(Role::getIsDelete, RoleConstant.NOT_IS_DELETE)
                .and(wrapper -> wrapper.isNull(Role::getValidTime).or().ge(Role::getValidTime, new Date()))
                .last("LIMIT 1");
        return getOne(queryWrapper);
    }

    @Override
    public Role findDefaultRole() {
        LambdaQueryWrapper<Role> queryWrapper = new LambdaQueryWrapper<Role>()
                .eq(Role::getRoleTypeCode, CommonConstant.TWO_INT)
                .eq(Role::getIsDelete, RoleConstant.NOT_IS_DELETE)
                .and(wrapper -> wrapper.isNull(Role::getValidTime).or().ge(Role::getValidTime, new Date()))
                .orderByAsc(Role::getCreateTime)
                .last("LIMIT 1");
        return getOne(queryWrapper);
    }

    @Override
    public Role findById(String roleId) {
        return getById(roleId);
    }

    @Override
    public Role findSubRoleByIdAndParentId(String roleId, String currRoleId) {
        String treeSql = getTreeSql(roleId);
        return roleMapper.findSubRoleByIdAndParentId(treeSql, roleId);
    }

    @Override
    public Role findSubRoleByIdAndParentIdIncludeInvalidRole(String roleId, String currRoleId) {
        String treeSql = getTreeSql(currRoleId);
        return roleMapper.findSubRoleByIdAndParentIdIncludeInvalidRole(treeSql, roleId);
    }

    @Override
    public Role findByIdMe(String id) {
        return getById(id);
    }

    @Override
    public List<Role> findListByParentId(String roleId) {
        String treeSql = getTreeSql(roleId);
        return roleMapper.findListByParentId(treeSql, roleId);
    }

    @Override
    public List<Role> getRoleTree(String roleId) {
        String treeSql = getTreeSql(roleId);
        return roleMapper.getRoleTree(treeSql, roleId);
    }

    @Override
    public Page<Role> getList(int pageNum, int pageSize, String roleID, String orgCode, String keyword) {
        String treeSql = getTreeSql(roleID);
        return roleMapper.getList(new Page<>(pageNum, pageSize), treeSql, orgCode, keyword);
    }

    @Override
    public Page<Role> getListAndValid(int pageNum, int pageSize, String roleID) {
        String treeSql = getTreeSql(roleID);
        return roleMapper.getListAndValid(new Page<>(pageNum, pageSize), treeSql, CommonConstant.ONE_INT, RoleConstant.NOT_IS_DELETE);
    }


    public static String getTreeSql(String currRoleId) {
        return "\twith recursive \"role_tree\" as (\n" +
                "  (\n" +
                "    select *\n" +
                "    from \"sys_role\"\n" +
                "    where \"sys_role\".\"id\" = '" + currRoleId + "'\n" +
                "  )\n" +
                "  union all (\n" +
                "    select \"sys_role\".*\n" +
                "    from \"sys_role\"\n" +
                "      join \"role_tree\"\n" +
                "      on \"sys_role\".\"parent_id\" = \"role_tree\".\"id\"\n" +
                "  )\n" +
                ")";
    }

    @Override
    public boolean updateRoleOrgCode(String newOrgCode) {
        return roleMapper.updateRoleOrgCode(newOrgCode) > 0;
    }

    /**
     * 添加角色1
     *
     * @param role 角色对象
     * @return OutMessage
     */
    @Override
    public OutMessage addRole(RoleDTO role) {
        // 获取父id
        String parentId = role.getParent_id();
        Role parentRole;
        if (!MINUS_ZERO.equals(parentId)) {
            parentRole = roleDao.findByIdAndValid(parentId);
            if (parentId == null) {
                // 没有相应ID的数据
                return new OutMessage(Status.PARENTID_ISNULL);
            }

            if (parentRole == null) {
                return new OutMessage(Status.PARENT_ROLE_PAST);
            }
            role.setParent_id(parentRole.getId());
            // 获取角色字符串
            StringBuilder permission = new StringBuilder(role.getPermission());
            // 获取权限总长度
            int count = permissionDao.findCount().intValue();
            int length = count - permission.length();
            if (length < 0) {
                return new OutMessage<>(Status.PERMISSION_LENGTH);
            }

            for (int i = 0; i < length; i++) {
                permission.append("0");
            }
            role.setPermission(permission.toString());
        } else {
            role.setParent_id(MINUS_ZERO);
        }
        // 设置值
        role.setId(StrKit.getRandomUUID());
        role.setRole_type_code(CommonConstant.THREE_INT);
        role.setCreate_time(new Date());
        role.setUpdate_time(new Date());
        role.setCreate_account(USER_CONTEXT.get().getUser().getAccount());
        role.setUpdate_account(USER_CONTEXT.get().getUser().getAccount());
        Role role1 = new Role();
        BeanUtils.copyProperties(role, role1);
        role1.setOrgCode(USER_CONTEXT.get().getUserRolePermission().getOrgCode());
        role1.setOrgId(USER_CONTEXT.get().getUserRolePermission().getOrgId());
        // 保存
        boolean save = roleDao.save(role1);
        if (save) {
            // 返回vo模型数据
            RoleVo roleVo = new RoleVo();
            BeanUtil.copyProperties(role, roleVo);
            return new OutMessage<>(Status.SUCCESS, roleVo);
        } else {
            return new OutMessage<>(Status.FAIL);
        }
    }

    /**
     * 切换角色
     *
     * @param userId 用户ID
     * @param roleId 角色ID
     * @return OutMessage
     */
    @Override
    public OutMessage cutRole(String userId, String roleId) {

        User user = userDao.findById(userId);
        if (user == null) {
            return new OutMessage(Status.PARA_ERROR);
        }
        Role role = roleDao.findByIdAndValid(roleId);
        if (role == null) {
            // 没有相应ID的数据
            return new OutMessage(Status.ROLE_NOT_EXIST);
        }

        // 查询中间表是否有数据
        UserRolePermission userIdAndRoleId = userRolePermissionDao.findByUserIdAndRoleId(userId, roleId);
        if (ObjectUtil.isNull(userIdAndRoleId)) {
            // 权限不足
            return new OutMessage(Status.PARA_ERROR);
        }
        // 更改用户角色
        user.setCurrentUserRoleid(userIdAndRoleId.getId());
        user.setUpdateTime(new Date());
        user.setUpdateAccount(USER_CONTEXT.get().getUser().getAccount());
        boolean update = userDao.updateById(user);
        if (!update) {
            return new OutMessage(Status.FAIL);
        }

        if (userId.equals(USER_CONTEXT.get().getUser().getId())) {
            // 如果切换的角色是当前正在操作的用户
            UserTicket userExt = new UserTicket();
            userExt.setUser(user);
            userExt.setUserRolePermission(userIdAndRoleId);
            String token = PRE_TOKEN + userId;
            userExt.setToken(token);
            // 存入缓存
            CacheUtils.putUserCache(token, userExt);
            // 返回数据
            UserLoginVo userLoginVo = new UserLoginVo();
            userLoginVo.setAuthorization(token);
            userLoginVo.setPermission(userIdAndRoleId.getPermission());
            return new OutMessage<>(Status.SUCCESS, userLoginVo);
        } else {
            return new OutMessage<>(Status.SUCCESS);
        }
    }

    /**
     * 编辑角色
     *
     * @param data       要编辑的角色数据集
     * @param currRoleId 登陆用户的角色
     */
    @Override
    public OutMessage updateRole(RoleDTO data, String currRoleId) {
        // 当前用户角色
        Role currRole = roleDao.getById(currRoleId);
        if (ObjectUtil.isNull(currRole)) {
            return new OutMessage(Status.ROLE_NOT_EXIST);
        }
        String id = data.getId();
        String parentId = data.getParent_id();
        // 查找之前的数据
        Role role = roleDao.findByIdMe(id);
        if (StrKit.notNull(role)) {
            return new OutMessage(Status.FAIL);
        }

        // 当前角色是否是当前用户的管理层级
        Role existRole = roleDao.findSubRoleByIdAndParentIdIncludeInvalidRole(id, currRoleId);
        if (ObjectUtil.isNull(existRole)) {
            return new OutMessage<>(Status.PERMISSION_DENIED);
        }

        // 比较code是否一致
        Role parentRole = roleDao.findByIdAndValid(parentId);
        if (parentId == null) {
            // 没有相应ID的数据
            return new OutMessage(Status.PARENTID_ISNULL);
        }
        //角色已经过期
        if (parentRole == null) {
            return new OutMessage(Status.ROLE_PAST);
        }
        // 父角色为自身
        if (parentRole.getId().equals(id)) {
            return new OutMessage<>(Status.PARENTID_ERROR);
        }
        //如果父角色是当前角色子级
        Role subRole = roleDao.findSubRoleByIdAndParentIdIncludeInvalidRole(parentId, id);
        if (subRole != null) {
            return new OutMessage<>(Status.PARENTID_ERROR);
        }

        // 系统管理员不可编辑
        if (CommonConstant.ONE_INT == role.getRoleTypeCode()) {
            return new OutMessage(Status.BUILT_IN_UNEDIT);
        }
        data.setUpdate_account(USER_CONTEXT.get().getUser().getAccount());
        data.setUpdate_time(new Date());
        // dto转换为model进行更新操作
        BeanUtil.copyProperties(data, role);
        role.setValidTime(data.getValid_time());
        boolean b = roleDao.updateById(role);
        return new OutMessage(b ? Status.SUCCESS : Status.FAIL);

    }

    /**
     * 修改角色权限
     *
     * @param updatePermissionDto 更新权限dto
     */
    @Override
    @Transactional(rollbackFor = {Exception.class})
    public OutMessage updateRolePermission(UpdatePermissionDto updatePermissionDto) {

        UserTicket userTicket = USER_CONTEXT.get();
        if (userTicket == null) {
            return new OutMessage(Status.NOT_LOGIN);
        }

        //创造Permission
        //0000
        Long count = permissionDao.findCount();
        StringBuilder permissionStr = new StringBuilder();
        for (int i = 0; i < count; i++) {
            permissionStr.append(PermissionConstant.PERMISSION_OFF);
        }

        StringBuilder permission = new StringBuilder(permissionStr.toString());
        List<Permission> permissionList = updatePermissionDto.getPermissionList();
        for (Permission permissionObj : permissionList) {
            Integer id = permissionObj.getId();
            permission.setCharAt(id - 1, PermissionConstant.PERMISSION_ON);
        }

        //校验权限是否超出了用户的界限
        //用户的权限根据登录用户的权限来进行控制
        Boolean flag = checkPermission(userTicket.getUserRolePermission().getPermission(), permission.toString());
        if (!flag) {
            return new OutMessage(Status.PERMISSION_INDEX_OUT);
        }
        String roleId = updatePermissionDto.getRoleId();
        Role updateRole = roleDao.findByIdAndValid(roleId);
        if (StrKit.notNull(updateRole)) {
            return new OutMessage(Status.ROLE_NOT_EXIST);
        }
        // 系统管理员不可编辑
        if (IS_BUILT_IN == updateRole.getRoleTypeCode()) {
            return new OutMessage(Status.BUILT_IN_UNEDIT);
        }

        String updatePermissionCode = permission.toString();
        updateRole.setPermission(updatePermissionCode);
        updateRole.setUpdateTime(new Date());
        updateRole.setUpdateAccount(userTicket.getUser().getAccount());

        //所有权限项的下标
        List<Integer> allIndex = Stream.iterate(0, integer -> integer + 1).limit(updatePermissionCode.length()).collect(Collectors.toList());
        //所有被开启的权限项下标
        List<Integer> permissionOnIndexList = permissionList.stream().map(p -> p.getId() - 1).collect(Collectors.toList());
        //所有被关闭的权限项下标
        List<Integer> permissionOffIndexList = allIndex.stream().filter(index -> !permissionOnIndexList.contains(index)).collect(Collectors.toList());

        List<Role> updateRoleList = new ArrayList<>();

        updateRoleList.add(updateRole);

        //查询出被修改的角色所有下级
        List<Role> subRoleList = roleDao.findListByParentId(roleId);
        //所有角色id的集合包括子id
        List<String> roleIds = new ArrayList<>();
        roleIds.add(roleId);


        if (subRoleList != null && !subRoleList.isEmpty()) {
            for (Role subRole : subRoleList) {
                //子角色权限码
                StringBuilder subRolePermission = new StringBuilder(subRole.getPermission());
                for (Integer index : permissionOffIndexList) {
                    subRolePermission.setCharAt(index, PermissionConstant.PERMISSION_OFF);
                }
                for (Integer index : permissionOnIndexList) {
                    subRolePermission.setCharAt(index, PermissionConstant.PERMISSION_ON);
                }
                subRole.setPermission(subRolePermission.toString());
                subRole.setUpdateTime(new Date());
                subRole.setUpdateAccount(userTicket.getUser().getAccount());
                updateRoleList.add(subRole);
                roleIds.add(subRole.getId());
            }
        }
        boolean isOk = updateBatchById(updateRoleList);
        List<UserRolePermission> userRolePermissionList = userRolePermissionDao.findByRoleIds(roleIds);
        List<UserRolePermission> updateList = new ArrayList<>();
        for (UserRolePermission userRolePermission : userRolePermissionList) {
            UserRolePermission update = new UserRolePermission();
            update.setId(userRolePermission.getId());
            update.setPermission(updateRole.getPermission());
            updateList.add(update);
        }
        if (CollectionUtil.isNotEmpty(updateList)) {
            isOk = userRolePermissionDao.batchUpdate(updateList) && isOk;
        }
        return new OutMessage(isOk ? Status.SUCCESS : Status.FAIL);
    }

    /**
     * 删除角色
     *
     * @param roleId     角色id
     * @param currRoleId 当前登录用户的角色id
     * @return OutMessage
     */
    @Override
    public OutMessage delRole(String roleId, String currRoleId) {
        // 当前登录用户的角色
        Role currRole = roleDao.findByIdAndValid(currRoleId);
        if (ObjectUtil.isNull(currRole)) {
            return new OutMessage<>(Status.NOT_LOGIN);
        }

        // 要删除的查询角色信息
        Role role = roleDao.findById(roleId);
        if (ObjectUtil.isNull(role)) {
            return new OutMessage<>(Status.ROLE_NOT_EXIST);
        }
        if (CommonConstant.ONE_INT == role.getRoleTypeCode()) {
            return new OutMessage<>(Status.PERMISSION_DENIED);
        }
        // 是否还有下级
        List<Role> roleList = roleDao.findListByParentId(roleId);
        if (CollectionUtil.isNotEmpty(roleList)) {
            return new OutMessage<>(Status.ROLE_HAS_SUB);
        }

        // 是否有权限删除
        Role existRole = roleDao.findSubRoleByIdAndParentIdIncludeInvalidRole(roleId, currRoleId);
        if (ObjectUtil.isNull(existRole)) {
            return new OutMessage<>(Status.PERMISSION_DENIED);
        }
        // 获取角色对应的中间表数据
        List<UserRolePermission> userRolePermissions = userRolePermissionDao.findByRoleId(roleId);

        if (userRolePermissions == null || userRolePermissions.size() <= 0) {
            // roleID在中间表中都不存在,直接删除,并且不是内建角色
            roleDao.removeById(roleId);
        } else {
            return new OutMessage<>(Status.USER_ROLE_ERROR);
        }

        return new OutMessage<>(Status.SUCCESS);
    }

    /**
     * 获取列表
     *
     * @param pageNum  页数
     * @param pageSize 没有数量
     * @param roleID   角色id
     * @return OutMessage
     */
    @Override
    public OutMessage getListM(int pageNum, int pageSize, String roleID, String orgCode, String keyword) {

        Role role = roleDao.findByIdAndValid(roleID);
        if (ObjectUtil.isNull(role)) {
            return new OutMessage<>(Status.ROLE_NOT_EXIST);
        }

        Page<Role> list = roleDao.getList(pageNum, pageSize, roleID, orgCode, keyword);

        Page<RoleVo> roleVoPage = new Page<>(pageNum, pageSize);

        List<RoleVo> roleVos = new ArrayList<>();
        list.getRecords().forEach(role1 -> {
            RoleVo roleVo = new RoleVo();
            roleVo.setId(role1.getId());
            roleVo.setName(role1.getName());
            roleVo.setPermission(role1.getPermission());
            roleVo.setValid_time(role1.getValidTime());
            roleVo.setCreate_time(role1.getCreateTime());
            roleVo.setUpdate_time(role1.getUpdateTime());
            roleVo.setCreate_account(role1.getCreateAccount());
            roleVo.setParent_id(role1.getParentId());
            roleVo.setRole_type_code(role1.getRoleTypeCode());
            roleVo.setUpdate_account(role1.getUpdateAccount());

            String roleVoId = roleVo.getId();
            List<User> userList = userDao.findByRoleId(roleVoId, USER_CONTEXT.get().getUserRolePermission().getOrgCode());

            List<String> stringList = userList.stream().map(User::getName).collect(Collectors.toList());

            String join = CollectionUtil.join(stringList, ",");
            roleVo.setUserNames(join);
            roleVos.add(roleVo);
        });

        roleVoPage.setTotal(list.getTotal());
        roleVoPage.setRecords(roleVos);

        return new OutMessage<>(Status.SUCCESS, roleVoPage);
    }

    /***
     * 获取有效的角色列表
     * */
    @Override
    public OutMessage<Page<RoleVo>> getListAndValidM(int pageNum, int pageSize, String roleID) {

        Role role = roleDao.findByIdAndValid(roleID);
        if (ObjectUtil.isNull(role)) {
            return new OutMessage<>(Status.ROLE_NOT_EXIST);
        }
        Page<Role> list = roleDao.getListAndValid(pageNum, pageSize, roleID);
        Page<RoleVo> roleVoPage = new Page<>(pageNum, pageSize);

        List<RoleVo> roleVoList = list.getRecords().stream().map(role1 -> {
            RoleVo roleVo = new RoleVo();
            roleVo.setId(role1.getId());
            roleVo.setName(role1.getName());
            roleVo.setPermission(role1.getPermission());
            roleVo.setValid_time(role1.getValidTime());
            roleVo.setCreate_time(role1.getCreateTime());
            roleVo.setUpdate_time(role1.getUpdateTime());
            roleVo.setCreate_account(role1.getCreateAccount());
            roleVo.setParent_id(role1.getParentId());
            roleVo.setRole_type_code(role1.getRoleTypeCode());
            roleVo.setUpdate_account(role1.getUpdateAccount());
            roleVo.setUserNames(role1.getName());
            return roleVo;
        }).collect(Collectors.toList());

        roleVoPage.setTotal(list.getTotal());
        roleVoPage.setRecords(roleVoList);
        return new OutMessage<>(Status.SUCCESS, roleVoPage);
    }


    /**
     * 根据ID查找
     *
     * @param roleId 角色ID
     */
    @Override
    public OutMessage getByIdM(String roleId) {
        Role role = roleDao.getById(roleId);
        if (ObjectUtil.isNull(role)) {
            return new OutMessage<>(Status.ROLE_NOT_EXIST);
        }
        RoleVo roleVo = new RoleVo();
        // model转vo返回
        BeanUtil.copyProperties(role, roleVo);
        roleVo.setValid_time(role.getValidTime());
        return new OutMessage<>(Status.SUCCESS, roleVo);
    }

    /**
     * 获取角色树
     *
     * @param roleId 角色ID
     * @return OutMessage
     */
    @Override
    public OutMessage getRoleTreeM(String roleId) {
        Role parentRole = roleDao.getById(roleId);
        if (ObjectUtil.isNull(parentRole)) {
            return new OutMessage<>(Status.USER_CHOOSE_ROLE_ISEMPTY);
        }

        // 获取自身及其下级角色信息
        List<Role> roleList = roleDao.getRoleTree(roleId);
        List<TreeNodeVo> treeNodes = new ArrayList<>();
        // 添加节点数据
        for (Role role : roleList) {
            String code = role.getId();
            String parentCode = role.getParentId();
            treeNodes.add(new TreeNodeVo(role.getId(), role.getName(), code, parentCode));
        }

        // 获取树形结构
        List<TreeNodeVo> treeNodeVos = buildByRecursive(treeNodes, parentRole.getParentId());

        return new OutMessage<>(Status.SUCCESS, treeNodeVos);
    }

    /**
     * 获取所有使用该角色的用户名称
     *
     * @param pageNum        页码
     * @param pageSize       页大小
     * @param roleId         角色id
     * @param currManOrgCode 组织code
     */
    @Override
    public OutMessage<Page<UserDTO>> getUserRole(int pageNum, int pageSize, String roleId, String currManOrgCode) {
        UserTicket userTicket = USER_CONTEXT.get();
        if (userTicket == null) {
            return new OutMessage<>(Status.NOT_LOGIN);
        }
        Page<User> page = userDao.findByPermissionRoleId(pageNum, pageSize, roleId, currManOrgCode);
        List<UserDTO> data = page.getRecords().stream().map(user -> {
            UserDTO userDTO = new UserDTO();
            BeanUtil.copyProperties(user, userDTO);
            String orgId = user.getOrgId();
            userDTO.setOrgName(CacheUtils.getOrgName(orgId));
            return userDTO;
        }).collect(Collectors.toList());
        Page<UserDTO> dtoPage = new Page<>(pageNum, pageSize);
        dtoPage.setTotal(page.getTotal());
        dtoPage.setRecords(data);
        return new OutMessage<>(Status.SUCCESS, dtoPage);
    }

    /**
     * 对比用户权限字符串 判断是否有权限修改这个字符串
     *
     * @param userPermission   用户本身的权限
     * @param updatePermission 用户想要修该的权限
     * @return Map
     */
    private Boolean checkPermission(String userPermission, String updatePermission) {
        //判断是否
        if (userPermission.length() == updatePermission.length()) {
            for (int i = 0; i < userPermission.length(); i++) {
                if (userPermission.charAt(i) == '0' && updatePermission.charAt(i) == '1') {
                    return false;
                }
            }
            return true;
        } else {
            return false;
        }
    }
}
