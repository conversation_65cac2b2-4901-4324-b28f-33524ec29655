package com.zenith.front.core.feign.dto;

import lombok.Data;

import java.util.Date;

/**
 * 两委班子成员模型
 *
 * <AUTHOR>
 * @since 2023/1/17 11:03
 */
@Data
public class PushCommitteeInfo {
    /**
     * 对方业务主键
     */
    private String id;
    /**
     * 姓名
     */
    private String name;
    /**
     * 身份证
     */

    private String idCard;
    /**
     * 性别
     */
    private String sexCode;
    /**
     * 机构id
     */
    private String unitId;
    /**
     * 机构code
     */
    private String unitCode;
    /**
     * 组织code
     */
    private String orgCode;
    /**
     * 职务
     */
    private String jobLevel;
    /**
     * 任职开始时间
     */
    private Date jobLevelStartDate;
    /**
     * 任职开始时间,yyyy-MM-dd
     */
    private String jobLevelStartDateStr;

    /**
     * 出生日期
     */
    private Date birthday;
    /**
     * 出生日期,yyyy-MM-dd
     */
    private String birthdayStr;
}
