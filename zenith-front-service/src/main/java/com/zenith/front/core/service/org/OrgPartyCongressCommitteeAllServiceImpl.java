package com.zenith.front.core.service.org;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.zenith.front.api.org.IOrgPartyCongressCommitteeAllService;
import com.zenith.front.dao.mapper.org.OrgPartyCongressCommitteeAllMapper;
import com.zenith.front.model.bean.OrgPartyCongressCommitteeAll;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <p>
 * 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-01-27
 */
@Service
public class OrgPartyCongressCommitteeAllServiceImpl extends ServiceImpl<OrgPartyCongressCommitteeAllMapper, OrgPartyCongressCommitteeAll> implements IOrgPartyCongressCommitteeAllService {

    @Override
    public List<OrgPartyCongressCommitteeAll> findOrgPartyCongressCommitteeAllByElectCode(List<String> electCodeList) {
        return list(
                new LambdaQueryWrapper<OrgPartyCongressCommitteeAll>()
                        .in(OrgPartyCongressCommitteeAll::getElectCode, electCodeList)
                        .isNull(OrgPartyCongressCommitteeAll::getDeleteTime)
        );
    }
}
