package com.zenith.front.core.service.fee;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.jfinal.kit.StrKit;
import com.zenith.front.api.fee.IFeeOrderService;
import com.zenith.front.core.cache.CacheUtils;
import com.zenith.front.dao.mapper.fee.FeeMapper;
import com.zenith.front.dao.mapper.fee.FeeOrderMapper;
import com.zenith.front.dao.mapper.org.OrgMapper;
import com.zenith.front.model.dto.FeeListDTO;
import com.zenith.front.model.message.OutMessage;
import com.zenith.front.model.message.Status;
import com.zenith.front.model.bean.FeeOrder;
import com.zenith.front.model.vo.FeeAllVO;
import com.zenith.front.model.vo.FeeCountVO;
import com.zenith.front.model.vo.FeeVO;
import com.zenith.front.model.vo.PaymentSituationVO;

import org.jooq.Condition;
import org.jooq.SelectFieldOrAsterisk;
import org.jooq.SelectHavingStep;
import org.jooq.impl.DSL;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.stream.Collectors;

import static org.jooq.impl.DSL.*;

/**
 * <p>
 * 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-07-10
 */
@Service
public class FeeOrderServiceImpl extends ServiceImpl<FeeOrderMapper, FeeOrder> implements IFeeOrderService {

    @Resource
    private FeeOrderMapper feeOrderMapper;

    @Resource
    private OrgMapper orgMapper;

    @Resource
    private FeeMapper feeMapper;

    /**
     * 获取党费详情列表
     *
     * @param dto
     * @return
     */
    @Override
    public OutMessage getList(FeeListDTO dto) {
        Date findDate = dto.getFindDate();
        DateTime beginOfMonth = DateUtil.beginOfMonth(findDate);
        DateTime endOfMonth = DateUtil.endOfMonth(findDate);
        Page<FeeOrder> page = new Page<>(dto.getPageNum(), dto.getPageSize());
        Page<FeeOrder> list = feeOrderMapper.getList(page, dto, beginOfMonth, endOfMonth);
        list.getRecords().forEach(feeOrder -> {
            feeOrder.setMemName(CacheUtils.getMemName(feeOrder.getMemCode()));
            String creatorMemCode = feeOrder.getCreatorMemCode();
            if (StrKit.notBlank(creatorMemCode)) {
                feeOrder.setCreatorName(CacheUtils.getMemName(creatorMemCode));
            }
            feeOrder.setOrgName(CacheUtils.getOrgShortName(feeOrder.getMemOrgCode()));
        });
        return new OutMessage<>(Status.SUCCESS, page);
    }

    /**
     * 获取党费缴纳统计项 已缴纳金额,未缴纳金额
     *
     * @param feeListDTO
     * @return
     */
    @Override
    public OutMessage getPayTotalList(FeeListDTO feeListDTO) {
        String memOrgOrgCode = feeListDTO.getMemOrgOrgCode();
        String year = feeListDTO.getYear();
        Integer pageSize = feeListDTO.getPageSize();
        Integer pageNum = feeListDTO.getPageNum();
        String orgName = feeListDTO.getOrgName();
        // 分页查询组织
        Page<FeeAllVO> page = new Page<>(pageNum, pageSize);
        Page<FeeAllVO> orgPage = orgMapper.findOrgCodeByPage(page, memOrgOrgCode, orgName);
        boolean isLeaf = orgPage.getRecords() == null || orgPage.getRecords().size() <= 0;
        // 获取查询的组织层级码集合
        List<String> orgCodeList;
        if (isLeaf) {
            // 党支部,没有下级展示自己
            orgCodeList = new ArrayList<String>() {
                private static final long serialVersionUID = -5511656875755028361L;

                {
                add(memOrgOrgCode);
            }};
        } else {
            // 党委,展示直属下级
            orgCodeList = orgPage.getRecords().stream().map(FeeAllVO::getOrgCode).collect(Collectors.toList());
        }
        // 组织每月党费金额
        List<Map<String, Object>> totalList = this.getTotalList(orgCodeList, year);
        boolean isEmptyRecordList = CollectionUtil.isEmpty(totalList);
        // 返回数据
        List<FeeCountVO> feeCountVOList = new ArrayList<>();
        for (String orgCode : orgCodeList) {
            FeeCountVO feeCountVO = new FeeCountVO();
            List<FeeVO> feeVOList = new ArrayList<>();

            if (!isEmptyRecordList) {
                for (Map<String, Object> record : totalList) {
                    FeeVO feeVO = new FeeVO();
                    BigDecimal isPayBigDecimal = (BigDecimal)record.get(orgCode + "isPay");
                    BigDecimal isNotPayBigDecimal = (BigDecimal)record.get(orgCode + "isNotPay");
                    feeVO.setSubmittedMoney(isPayBigDecimal);
                    feeVO.setUnpaidMoney(isNotPayBigDecimal);
                    feeVO.setMonth(record.get("month").toString());
                    feeVOList.add(feeVO);
                }
            }
            feeCountVO.setFeeVOList(feeVOList.stream().sorted(Comparator.comparing(feeVo -> Integer.parseInt(feeVo.getMonth()))).collect(Collectors.toList()))
                    .setMemOrgOrgCode(orgCode).setOrgShortName(CacheUtils.getOrgShortNameByOrgCode(orgCode));
            feeCountVOList.add(feeCountVO);
        }
       Page<FeeCountVO> feeCountVOPage = new Page<>();
        feeCountVOPage.setSize(orgPage.getSize());
        feeCountVOPage.setRecords(feeCountVOList);
        if (isLeaf) {
            feeCountVOPage.setTotal(1);
            feeCountVOPage.setPages(1);
        } else {
            feeCountVOPage.setTotal(orgPage.getTotal());
            feeCountVOPage.setPages(orgPage.getPages());
        }
        return new OutMessage<>(Status.SUCCESS, feeCountVOPage);
    }

    private List<Map<String,Object>> getTotalList(List<String> orgCodeList, String year) {
        BigDecimal zero = new BigDecimal(0).setScale(2, RoundingMode.HALF_UP);
        Collection<SelectFieldOrAsterisk> selectCollection = new ArrayList<>();
        selectCollection.add(field(name("month")));
        Condition condition = noCondition();
        for (String orgCode : orgCodeList) {
            selectCollection.add(sum(choose().when(field(name("mem_org_org_code"), String.class).like(orgCode + "%")
                    .and(field(name("pay_money")).isNotNull()), field(name("pay_money"), BigDecimal.class)).otherwise(zero)).as(orgCode + "isPay"));
            selectCollection.add(sum(choose().when(field(name("mem_org_org_code")).like(orgCode + "%")
                    .and(field(name("pay_money")).isNull()), field(name("standard"), BigDecimal.class)).otherwise(zero)).as(orgCode + "isNotPay"));
            condition.or(field(name("mem_org_org_code")).like(orgCode + "%"));
        }

        SelectHavingStep<org.jooq.Record> records = DSL.select(selectCollection)
                .from(name("ccp_fee"))
                .where(field(name("year")).eq(year))
                .and(condition)
                .and(field(name("delete_time")).isNull())
                .groupBy(field(name("month")));
        return feeMapper.getTotalList(records.toString());
    }

    @Override
    public OutMessage paymentSituation(FeeListDTO feeListDTO) {
        PaymentSituationVO situationVO = feeOrderMapper.getMoney(feeListDTO.getMemOrgOrgCode());
        if (Objects.isNull(situationVO)) {
            situationVO = new PaymentSituationVO();
        }
        List<FeeOrder> orders = feeOrderMapper.getIsPayCount(feeListDTO.getMemOrgOrgCode());
        long count = orders.stream().filter(s -> "SUCCESS".equals(s.getState())).count();
        situationVO.setPay(Math.toIntExact(count));
        long count2 = orders.stream().filter(s -> "NOTPAY".equals(s.getState())).count();
        situationVO.setNotPay(Math.toIntExact(count2));
        return new OutMessage<>(Status.SUCCESS, situationVO);
    }
}
