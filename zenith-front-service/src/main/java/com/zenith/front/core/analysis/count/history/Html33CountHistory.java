package com.zenith.front.core.analysis.count.history;

import cn.hutool.core.util.StrUtil;
import com.b1809.analysis.entity.PeggingPara;
import com.b1809.analysis.result.ReportResult;
import com.b1809.analysis.table.helper.TableHelper;
import com.b1809.analysis.table.helper.factory.TableHelperFactory;
import com.jfinal.plugin.activerecord.Page;
import com.zenith.front.core.analysis.ext.condition.MemAllCondition;
import com.zenith.front.core.analysis.ext.condition.OrgAllCondition;
import com.zenith.front.core.analysis.ext.condition.UnitAllCondition;
import org.jooq.Condition;

import java.util.HashMap;
import java.util.Map;

import static org.jooq.impl.DSL.noCondition;

/**
 * <AUTHOR>
 * @Date 2022/1/16 23:58
 * @Version 1.0
 */
public class Html33CountHistory {

    public Condition getMemListCondition(String orgCode, String orgLevelCode) {
        return noCondition().and("delete_time is null and d08_code in ('1','2') and (is_transfer is null or is_transfer!=1) and (d04_code like '51%' or d04_code like '52%' or d04_code like '53%') and d09_code like '0%'").and(new MemAllCondition().create(orgCode, orgLevelCode));
    }

    public Map<String, Object> getCheckHtml33(PeggingPara data) {
        Map<String, Object> resultMap = new HashMap<>();
        TableHelper tableHelper = TableHelperFactory.getHelper();
        String rowIndex = data.getRowIndex();
        //7联合党支部数 16配备专职党务工作者的党组织数
        if (StrUtil.equals(rowIndex, "7")) {
            Condition condition = this.getOrg33Condition(data.getOrgCode(), data.getOrgLevelCode()).and(this.getOrgCheck33Condition(data.getColIndex()))
                    .and("d01_code in('632','634','932')");
            OrgAllCondition orgAllCond = new OrgAllCondition();
            return Html48CountHistory.getReportPageResult(data, orgAllCond.getTableName(), condition, orgAllCond.getLevelCodeField());
        }

        if (StrUtil.equals(rowIndex, "16")) {
            Condition condition = this.getOrg33Condition(data.getOrgCode(), data.getOrgLevelCode()).and(this.getOrgCheck33Condition(data.getColIndex()))
                    .and("has_party_affairs_workers=1");
            OrgAllCondition orgAllCond = new OrgAllCondition();
            return Html48CountHistory.getReportPageResult(data, orgAllCond.getTableName(), condition, orgAllCond.getLevelCodeField());
        }
        //11已派党建工作指导员的
        if (StrUtil.equals(rowIndex, "11")) {
            Condition condition = this.getUnit33Condition(data.getOrgCode(), data.getOrgLevelCode()).and(this.getOrgCheck33Condition(data.getColIndex())).and("has_instructor_contact=1");
            UnitAllCondition unitAllCondition = new UnitAllCondition();
            return Html48CountHistory.getReportPageResult(data, unitAllCondition.getTableName(), condition, unitAllCondition.getLevelCodeField());
        }
        Page<Map<String, Object>> page = tableHelper.findPage(data.getPageNum(), data.getPageSize(), "ccp_mem_all", new Html33CountHistory().getMemCheckListCondition(data.getOrgCode(), data.getOrgLevelCode(), data.getColIndex()),
                data.getIncludeFieldList(), data.getOrderByFieldList());
        return ReportResult.toResult(resultMap, page, "ccp_mem_all");
    }

    public Condition getOrg33Condition(String orgCode, String orgLevelCode) {
        return new OrgAllCondition().create(orgCode, orgLevelCode).and("delete_time is null and (is_dissolve is null or is_dissolve !=1) " +
                "and (d04_code like '51%' or d04_code like '52%' or d04_code like '53%' or d04_code like '59%') and has_unit_own_level='1'");
    }

    public Condition getUnit33Condition(String orgCode, String orgLevelCode) {
        return new UnitAllCondition().create(orgCode, orgLevelCode).and("delete_time is null and (d04_code like '51%' or d04_code like '52%' or d04_code like '53%' or d04_code like '59%') and is_legal=1 and d05_code like '4%'");
    }


    public Condition getOrgCheck33Condition(String colIndex) {
        Condition condition = noCondition();
        if (StrUtil.equals(colIndex, "3")) {
            return condition.and("d35_code like '2%' or d35_code like '3%'");
        } else if (StrUtil.equals(colIndex, "4")) {
            return condition.and("(d35_code like '2%' or d35_code like '3%') and is_decoupl_industry=1");
        } else if (StrUtil.equals(colIndex, "5")) {
            return condition.and("d35_code like '4%' or d35_code like '5%'");
        } else if (StrUtil.equals(colIndex, "6")) {
            return condition.and("(d35_code like '4%' or d35_code like '5%') and is_decoupl_industry=1");
        } else if (StrUtil.equals(colIndex, "7")) {
            return condition.and("d35_code like '7%' or d35_code like '8%' or d35_code like '9%' or  d35_code like 'A%'");
        } else if (StrUtil.equals(colIndex, "8")) {
            return condition.and("(d35_code like '7%' or d35_code like '8%' or d35_code like '9%' or  d35_code like 'A%') and is_decoupl_industry=1");
        }
        return condition.and("1=0");
    }

    Condition getMemCheckListCondition(String orgCode, String orgLevelCode, String colIndex) {
        if ("3".equals(colIndex)) {
            return noCondition().and("delete_time is null and d08_code in ('1','2') and (is_transfer is null or is_transfer!=1) and (d04_code like '51%' or d04_code like '52%' or d04_code like '53%') and d09_code like '0%' and (d35_code like '2%' or d35_code like '3%')").and(new MemAllCondition().create(orgCode, orgLevelCode));
        }
        if ("4".equals(colIndex)) {
            return noCondition().and("delete_time is null and d08_code in ('1','2') and (is_transfer is null or is_transfer!=1) and (d04_code like '51%' or d04_code like '52%' or d04_code like '53%') and d09_code like '0%' and (d35_code like '2%' or d35_code like '3%') and is_decoupl_industry=1").and(new MemAllCondition().create(orgCode, orgLevelCode));
        }
        if ("5".equals(colIndex)) {
            return noCondition().and("delete_time is null and d08_code in ('1','2') and (is_transfer is null or is_transfer!=1) and (d04_code like '51%' or d04_code like '52%' or d04_code like '53%') and d09_code like '0%' and (d35_code like '4%' or d35_code like '5%')").and(new MemAllCondition().create(orgCode, orgLevelCode));
        }
        if ("6".equals(colIndex)) {
            return noCondition().and("delete_time is null and d08_code in ('1','2') and (is_transfer is null or is_transfer!=1) and (d04_code like '51%' or d04_code like '52%' or d04_code like '53%') and d09_code like '0%' and (d35_code like '4%' or d35_code like '5%') and is_decoupl_industry=1").and(new MemAllCondition().create(orgCode, orgLevelCode));
        }
        if ("7".equals(colIndex)) {
            return noCondition().and("delete_time is null and d08_code in ('1','2') and (is_transfer is null or is_transfer!=1) and (d04_code like '51%' or d04_code like '52%' or d04_code like '53%') and d09_code like '0%' and (d35_code like '7%' or d35_code like '8%' or d35_code like '9%' or  d35_code like 'A%')").and(new MemAllCondition().create(orgCode, orgLevelCode));
        }
        if ("8".equals(colIndex)) {
            return noCondition().and("delete_time is null and d08_code in ('1','2') and (is_transfer is null or is_transfer!=1) and (d04_code like '51%' or d04_code like '52%' or d04_code like '53%') and d09_code like '0%' and (d35_code like '7%' or d35_code like '8%' or d35_code like '9%' or  d35_code like 'A%') and is_decoupl_industry=1").and(new MemAllCondition().create(orgCode, orgLevelCode));
        }
        return noCondition();
    }
}
