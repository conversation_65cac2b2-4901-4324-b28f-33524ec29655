package com.zenith.front.core.analysis.ext.condition.year2022;

import com.b1809.analysis.extend.GenSqlConditionFuc;
import org.jooq.Condition;
import org.jooq.Field;

import static org.jooq.impl.DSL.field;
import static org.jooq.impl.DSL.name;

/**
 * <AUTHOR>
 * @date 2022/11/25
 */
public class MemFlowStatisticsConditionB implements GenSqlConditionFuc {
    @Override
    public String getTableName() {
        return "mem_flow_all_2022_b";
    }

    @Override
    public Condition create(String orgCode, String orgLevelCode) {
        return getLevelCodeField().like(orgLevelCode + "%");
    }

    @Override
    public Field<?> getLevelCodeField() {
        return field(name(getTableName(), "mem_org_org_code"), String.class);
    }
}
