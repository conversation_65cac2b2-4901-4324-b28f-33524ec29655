package com.zenith.front.core.analysis.count.history;

import cn.hutool.core.util.StrUtil;
import com.b1809.analysis.entity.PeggingPara;
import com.b1809.analysis.extend.ITableCount;
import com.b1809.analysis.plugin.elasticsearch.EsKit;
import com.b1809.analysis.result.ReportResult;
import com.b1809.analysis.table.helper.TableHelper;
import com.b1809.analysis.table.helper.factory.TableHelperFactory;
import com.jfinal.plugin.activerecord.Page;
import com.jfinal.plugin.activerecord.Record;
import com.zenith.front.common.constant.CommonConstant;
import com.zenith.front.common.kit.StrKit;
import com.zenith.front.core.analysis.count.CountMethod;
import com.zenith.front.core.analysis.ext.condition.OrgSlackAllCondition;
import org.jooq.Condition;
import org.jooq.Field;
import org.jooq.SelectConditionStep;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.*;

import static com.zenith.front.common.constant.DBConstant.DSL_CONTEXT;
import static org.jooq.impl.DSL.*;

/**
 * 专题调查表六 整顿软弱涣散基层党组织情况
 *
 * <AUTHOR>
 * @date 2021/11/5
 */
@Component
public class Html48CountHistory implements ITableCount {

    @Override
    public String getReportCode() {
        return "History_48.html";
    }

    @Override
    public Map<String, Map<String, Number>> doCount(String reportCode, String orgCode, String orgLevelCode) throws Exception {
        return null;
    }

    @Override
    public Map<String, Object> doPegging(PeggingPara peggingPara) throws Exception {
        return null;
    }

    @Override
    public Map<String, Number> doReplenishCount(String reportCode, String orgCode, String orgLevelCode) throws Exception {
        return getReplenishCount(orgCode, orgLevelCode, null);
    }

    public Map<String, Number> getReplenishCount(String orgCode, String orgLevelCode, String tableYear) throws Exception {
        String ccpOrgAll = StrUtil.isEmpty(tableYear) ? "ccp_org_slack_all" : "ccp_org_slack_all_" + tableYear;
        Map<String, Number> result = new LinkedHashMap<>();
        initReplenishCol(1, 8, result);
        SelectConditionStep<org.jooq.Record1<Object>> records1 = DSL_CONTEXT.select(field("d04_code,has_neaten"))
                .from(table(name(ccpOrgAll)).as("ccp_org_slack_all")).where(this.getListCondition(orgCode, orgLevelCode, tableYear));
        List<Record> records = EsKit.findBySql(records1.toString()).toRecord();
        for (Record record : records) {
            String d04Code = record.getStr("d04_code");
            String hasNeaten = record.getStr("has_neaten");
            //国有企业排查软弱涣散基层党组织
            if (StrUtil.startWith(d04Code, "411")) {
                setReplenishMapValue("1", 1, result);
                //整顿提升
                if (StrUtil.equals(hasNeaten, "1")) {
                    setReplenishMapValue("2", 1, result);
                }
            }
            //高校排查软弱涣散基层党组织
            if (StrUtil.startWith(d04Code, "331")) {
                setReplenishMapValue("3", 1, result);
                if (StrUtil.equals(hasNeaten, "1")) {
                    setReplenishMapValue("4", 1, result);
                }
            }
            //非公企业排查软弱涣散基层党组织
            if (StrUtil.startWith(d04Code, "42")) {
                setReplenishMapValue("5", 1, result);
                if (StrUtil.equals(hasNeaten, "1")) {
                    setReplenishMapValue("6", 1, result);
                }
            }
            //社会组织排查软弱涣散基层党组织
            if (StrUtil.startWith(d04Code, "5")) {
                setReplenishMapValue("7", 1, result);
                if (StrUtil.equals(hasNeaten, "1")) {
                    setReplenishMapValue("8", 1, result);
                }
            }
        }
        return result;
    }


    public Condition getListCondition(String orgCode, String orgLevelCode, String tableYear) {
        tableYear = CountMethod.getTableYearByIndex(tableYear);
        return noCondition().and(field(name("delete_time")).isNull()
                .and(new OrgSlackAllCondition().create(orgCode, orgLevelCode)).and(CountMethod.getEsReportDateSql("neaten_time", tableYear)));
    }

    @Override
    public Map<String, Object> doReplenishPegging(PeggingPara peggingPara) throws Exception {
        Condition condition = this.getListCondition(peggingPara.getOrgCode(), peggingPara.getOrgLevelCode(), null).and(this.getRowCondition(peggingPara.getRowIndex()));

        OrgSlackAllCondition orgSlackAllCondition = new OrgSlackAllCondition();
        return getReportPageResult(peggingPara, orgSlackAllCondition.getTableName(), condition, orgSlackAllCondition.getLevelCodeField());
    }

    /**
     * 获取反查列表
     *
     * @param peggingPara 反查参数
     * @param tableName   数据表名
     * @param condition   查询条件
     * @return 反查列表
     */
    public static Map<String, Object> getReportPageResult(PeggingPara peggingPara, String tableName, Condition condition, Field<?> levelCodeField) {
        Map<String, Object> resultMap = new HashMap<>();
        TableHelper tableHelper = TableHelperFactory.getHelper();
        String peggingType = peggingPara.getPeggingType();
        if (StrKit.notBlank(peggingType) && peggingType.equals(CommonConstant.TWO)) {
            Page<Map<String, Object>> page = tableHelper.findGroupPage(peggingPara.getPageNum(), peggingPara.getPageSize(), tableName, condition,
                    peggingPara.getOrderByFieldList(), levelCodeField);
            return ReportResult.toResult(resultMap, page, tableName);
        }
        Page<Map<String, Object>> page = tableHelper.findPage(peggingPara.getPageNum(), peggingPara.getPageSize(), tableName, condition,
                peggingPara.getIncludeFieldList(), peggingPara.getOrderByFieldList());

        return ReportResult.toResult(resultMap, page, tableName);
    }

    Condition getRowCondition(String rowIndex) {
        Condition condition = noCondition();
        if ("1".equals(rowIndex)) {
            condition = condition.and(field(name("d04_code"), String.class).like("411%"));
        }
        if ("2".equals(rowIndex)) {
            condition = condition.and(field(name("d04_code"), String.class).like("411%")
                    .and(field(name("has_neaten"), String.class).eq("1")));
        }
        if ("3".equals(rowIndex)) {
            condition = condition.and(field(name("d04_code"), String.class).like("331%"));
        }
        if ("4".equals(rowIndex)) {
            condition = condition.and(field(name("d04_code"), String.class).like("331%")
                    .and(field(name("has_neaten"), String.class).eq("1")));
        }
        if ("5".equals(rowIndex)) {
            condition = condition.and(field(name("d04_code"), String.class).like("42%"));
        }
        if ("6".equals(rowIndex)) {
            condition = condition.and(field(name("d04_code"), String.class).like("42%")
                    .and(field(name("has_neaten"), String.class).eq("1")));
        }
        if ("7".equals(rowIndex)) {
            condition = condition.and(field(name("d04_code"), String.class).like("5%"));
        }
        if ("8".equals(rowIndex)) {
            condition = condition.and(field(name("d04_code"), String.class).like("5%")
                    .and(field(name("has_neaten"), String.class).eq("1")));
        }
        return condition;
    }


    /**
     * 设置报表数据
     */
    public static void setTableMapValue(String ro, String co, Integer addNum, Map<String, Number> map) {
        addNum = Objects.isNull(addNum) ? 0 : addNum;
        if (!map.containsKey("cell_" + ro + "_" + co)) {
            map.put("cell_" + ro + "_" + co, addNum);
        } else {
            map.put("cell_" + ro + "_" + co, addNum + map.get("cell_" + ro + "_" + co).intValue());
        }
    }

    public static void setTableMapValue(String ro, String co, BigDecimal addNum, Map<String, Number> map) {
        addNum = Objects.isNull(addNum) ? BigDecimal.ZERO : addNum;
        if (!map.containsKey("cell_" + ro + "_" + co)) {
            map.put("cell_" + ro + "_" + co, addNum);
        } else {
            Number number = map.get("cell_" + ro + "_" + co);
            map.put("cell_" + ro + "_" + co, addNum.add(BigDecimal.valueOf(number.doubleValue())));
        }
    }

    /**
     * 初始化报表数据
     */
    public static void initTableCol(Integer startNum, Integer lastNum, Integer startColNum, Integer lastColNum, Map<String, Number> result) {
        for (int row = startNum; row <= lastNum; row++) {
            for (int col = startColNum; col <= lastColNum; col++) {
                result.put("cell_" + row + "_" + col, 0);
            }
        }
    }

    /**
     * 设置补充资料数据
     *
     * @param col    序号
     * @param addNum 数值
     */
    public static void setReplenishMapValue(String col, Integer addNum, Map<String, Number> map) {
        addNum = Objects.isNull(addNum) ? 0 : addNum;
        if (!map.containsKey("cell_" + col)) {
            map.put("cell_" + col, addNum);
        } else {
            map.put("cell_" + col, addNum + map.get("cell_" + col).intValue());
        }
    }

    /**
     * 初始化补充资料数据
     *
     * @param startColNum 开始列
     * @param lastColNum  最大列
     */
    public static void initReplenishCol(Integer startColNum, Integer lastColNum, Map<String, Number> result) {
        for (int col = startColNum; col <= lastColNum; col++) {
            result.put("cell_" + col, 0);
        }
    }


}
