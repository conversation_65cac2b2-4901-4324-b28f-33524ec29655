package com.zenith.front.core.analysis.count;

import com.b1809.analysis.entity.PeggingPara;
import com.b1809.analysis.extend.ITableCount;
import com.zenith.front.core.analysis.ext.condition.OrgSlackAllCondition;
import org.jooq.Condition;
import org.springframework.stereotype.Component;

import java.util.Map;

/**
 * 专题调查表8 整顿软弱涣散基层党组织情况
 *
 */
@Component
public class Html53CountBackups extends Html53Count implements ITableCount {

    @Override
    public String getReportCode() {
        return "53_csq.html";
    }

    @Override
    public Map<String, Map<String, Number>> doCount(String reportCode, String orgCode, String orgLevelCode) throws Exception {
        return null;
    }

    @Override
    public Map<String, Object> doPegging(PeggingPara peggingPara) throws Exception {
        return null;
    }

    @Override
    public Map<String, Number> doReplenishCount(String reportCode, String orgCode, String orgLevelCode) throws Exception {
        return getReplenishCount(orgCode, orgLevelCode, null);
    }


    @Override
    public Map<String, Object> doReplenishPegging(PeggingPara peggingPara) throws Exception {
        Condition condition = this.getListCondition(peggingPara.getOrgCode(), peggingPara.getOrgLevelCode(), null).and(this.getRowCondition(peggingPara.getRowIndex()));

        OrgSlackAllCondition orgSlackAllCondition = new OrgSlackAllCondition();
        return getReportPageResult(peggingPara, orgSlackAllCondition.getTableName(), condition, orgSlackAllCondition.getLevelCodeField());
    }


}
