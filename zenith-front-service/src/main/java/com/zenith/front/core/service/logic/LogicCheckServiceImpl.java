package com.zenith.front.core.service.logic;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.zenith.front.model.bean.LogicCheck;
import com.zenith.front.api.logic.LogicCheckService;
import com.zenith.front.dao.mapper.mem.LogicCheckMapper;
import com.zenith.front.model.vo.DecryptMap;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 */
@Service
public class LogicCheckServiceImpl extends ServiceImpl<LogicCheckMapper, LogicCheck> implements LogicCheckService {

    @Resource
    private LogicCheckMapper logicCheckMapper;

    @Override
    public Page<DecryptMap<String, Object>> selectPage(Page page, String checkSql) {
        return logicCheckMapper.page(page, checkSql);
    }

    @Override
    public int selectCount(String checkSql) {
        return logicCheckMapper.count(checkSql);
    }
}




