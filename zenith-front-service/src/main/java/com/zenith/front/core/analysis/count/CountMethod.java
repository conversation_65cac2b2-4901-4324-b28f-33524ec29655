package com.zenith.front.core.analysis.count;

import cn.hutool.core.util.StrUtil;
import com.zenith.front.core.analysis.count.history.Html1Count2022;
import com.zenith.front.core.analysis.count.year2023.Html1Count2023;

/**
 * 统计公共方法
 * <AUTHOR>
 * @create_date 2023-11-06 16:55
 * @description
 */
public class CountMethod {

    /**
     * 根据统计年度获取报告期内查询条件
     *
     * @param field     查询字段
     * @param tableYear 统计年度
     * @return 查询条件
     */
    public static String getEsReportDateSql(String field, String tableYear) {
        switch (tableYear) {
            case Html1Count.TABLE_YEAR:
                return new Html1Count().getEsReportDateSql(field);
            case "2023":
            case "202302":
                return new Html1Count2023().getEsReportDateSql(field);
            case "2022":
                return new Html1Count2022().getEsReportDateSql(field);
            case "2021":
                return new Html1Count2022().getEs2021ReportDateSql(field);
            default:
                return "1=0";
        }
    }

    /**
     * 根据参数获取统计年度
     */
    public static String getTableYearByIndex(String tableYear) {
        if (StrUtil.isEmpty(tableYear)) {
            return Html1Count.TABLE_YEAR;
        } else if (StrUtil.contains(tableYear, "_")) {
            return tableYear.substring(0, tableYear.indexOf("_"));
        }
        return tableYear;
    }
}
