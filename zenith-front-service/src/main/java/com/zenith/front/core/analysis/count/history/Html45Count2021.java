package com.zenith.front.core.analysis.count.history;

import com.b1809.analysis.entity.PeggingPara;
import com.b1809.analysis.extend.ITableCount;
import com.zenith.front.core.analysis.ext.condition.year2021.UnitAllCondition2021;
import org.jooq.Condition;
import org.springframework.stereotype.Component;

import java.util.Map;

import static org.jooq.impl.DSL.*;

/**
 * 专题调查表三 推动发展壮大村级集体经济情况
 *
 * <AUTHOR>
 * @date 2021/11/17
 */
@Deprecated
@Component
public class Html45Count2021 extends Html45CountHistory implements ITableCount {

    @Override
    public String getReportCode() {
        return "2021_45.html";
    }

    @Override
    public Map<String, Map<String, Number>> doCount(String reportCode, String orgCode, String orgLevelCode) throws Exception {
        return getCount(orgCode, orgLevelCode, "2021");
    }

    @Override
    public Map<String, Object> doPegging(PeggingPara peggingPara) throws Exception {
        UnitAllCondition2021 unitAllCondition = new UnitAllCondition2021();
        Condition condition = noCondition().and(new Html6CountHistory().getUnitListCondition(peggingPara.getOrgCode(), peggingPara.getOrgLevelCode()).toString().replace("ccp_unit_all", unitAllCondition.getTableName()))
                .and(field(name("d04_code"), String.class).eq("923")).and(this.getRowCondition(peggingPara.getRowIndex()));

        return Html48CountHistory.getReportPageResult(peggingPara, unitAllCondition.getTableName(), condition, unitAllCondition.getLevelCodeField());
    }


    @Override
    public Map<String, Number> doReplenishCount(String reportCode, String orgCode, String orgLevelCode) throws Exception {
        return null;
    }

    @Override
    public Map<String, Object> doReplenishPegging(PeggingPara peggingPara) throws Exception {
        return null;
    }
}
