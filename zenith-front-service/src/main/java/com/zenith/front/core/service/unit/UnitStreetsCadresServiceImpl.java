package com.zenith.front.core.service.unit;

import cn.hutool.core.thread.ThreadUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.zenith.front.api.unit.IUnitStreetsCadresService;
import com.zenith.front.common.kit.CodeUtil;
import com.zenith.front.common.kit.StrKit;
import com.zenith.front.core.service.sync.SyncUnitService;
import com.zenith.front.dao.mapper.unit.UnitStreetsCadresMapper;
import com.zenith.front.model.dto.UnitStreetsCadresDTO;
import com.zenith.front.model.dto.UnitStreetsCadresListDTO;
import com.zenith.front.model.message.OutMessage;
import com.zenith.front.model.message.Status;
import com.zenith.front.model.bean.UnitStreetsCadres;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.concurrent.Executor;

/**
 * <p>
 * 单位街道干部表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-10-08
 */
@Service
public class UnitStreetsCadresServiceImpl extends ServiceImpl<UnitStreetsCadresMapper, UnitStreetsCadres> implements IUnitStreetsCadresService {

    @Resource
    private UnitStreetsCadresMapper cadresMapper;
    @Resource
    private SyncUnitService syncUnitService;
    @Resource
    Executor mySimpleAsync;

    @Override
    public OutMessage addOrUpdate(UnitStreetsCadresDTO data) {
        int i = 0;
        if (StrKit.isBlank(data.getCode())) {
            UnitStreetsCadres conditions = new UnitStreetsCadres();
            BeanUtils.copyProperties(data, conditions);
            conditions.setEsId(CodeUtil.getEsId());
            conditions.setCreateTime(new Date());
            i = cadresMapper.insert(conditions);
        } else {
            UnitStreetsCadres conditions = cadresMapper.selectById(data.getCode());
            if (conditions == null) {
                return new OutMessage<>(Status.OBJECT_DBISNOTALIVE);
            }
            BeanUtils.copyProperties(data, conditions);
            conditions.setUpdateTime(new Date());
            i = cadresMapper.updateById(conditions);
        }
        if (i > 0) {
            ThreadUtil.execAsync(() -> syncUnitService.setBaseUnit(data.getUnitCode(), "6"));
        }
        return new OutMessage<>(Status.SUCCESS);
    }

    @Override
    public OutMessage getList(UnitStreetsCadresListDTO data) {
        LambdaQueryWrapper<UnitStreetsCadres> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(UnitStreetsCadres::getUnitCode, data.getUnitCode());
        wrapper.isNull(UnitStreetsCadres::getDeleteTime)
                .orderByAsc(UnitStreetsCadres::getCreateTime);
        Page<UnitStreetsCadres> page = new Page<>(data.getPageNum(), data.getPageSize());
        Page<UnitStreetsCadres> pageList = page(page, wrapper);
        return new OutMessage(Status.SUCCESS, pageList);
    }

    @Override
    public OutMessage findByCode(String code) {
        LambdaQueryWrapper<UnitStreetsCadres> sql = new LambdaQueryWrapper<>();
        sql.eq(UnitStreetsCadres::getCode, code).isNull(UnitStreetsCadres::getDeleteTime);
        return new OutMessage(Status.SUCCESS, getOne(sql));
    }

    @Override
    public OutMessage delByCode(String code) {
        UnitStreetsCadres cadres = getOne(new LambdaQueryWrapper<UnitStreetsCadres>().eq(UnitStreetsCadres::getCode, code));
        LambdaUpdateWrapper<UnitStreetsCadres> sql = new LambdaUpdateWrapper<>();
        sql.set(UnitStreetsCadres::getDeleteTime, new Date()).eq(UnitStreetsCadres::getCode, code);
        boolean update = update(sql);
        if (update) {
            ThreadUtil.execAsync(() -> syncUnitService.setBaseUnit(cadres.getUnitCode(), "6"));
        }
        return new OutMessage(Status.SUCCESS);
    }
}
