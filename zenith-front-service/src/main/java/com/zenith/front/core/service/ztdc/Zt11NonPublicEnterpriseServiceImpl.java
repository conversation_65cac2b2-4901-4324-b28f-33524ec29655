package com.zenith.front.core.service.ztdc;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.zenith.front.api.unit.IUnitService;
import com.zenith.front.api.ztdc.IZt11NonPublicEnterpriseService;
import com.zenith.front.dao.mapper.ztdc.Zt11NonPublicEnterpriseMapper;
import com.zenith.front.model.dto.Zt11NonPublicEnterpriseDto;
import com.zenith.front.model.message.OutMessage;
import com.zenith.front.model.message.Status;
import com.zenith.front.model.bean.Unit;
import com.zenith.front.model.bean.Zt11NonPublicEnterprise;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.Date;
import java.util.Objects;

/**
 * <p>
 * 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-08-13
 */
@Service
public class Zt11NonPublicEnterpriseServiceImpl extends ServiceImpl<Zt11NonPublicEnterpriseMapper, Zt11NonPublicEnterprise> implements IZt11NonPublicEnterpriseService {

    @Resource
    private IUnitService unitService;

    @Override
    public Zt11NonPublicEnterprise findByUnitCode(String unitCode) {
        LambdaQueryWrapper<Zt11NonPublicEnterprise> query = new LambdaQueryWrapper<Zt11NonPublicEnterprise>()
                .eq(Zt11NonPublicEnterprise::getUnitCode, unitCode)
                .isNull(Zt11NonPublicEnterprise::getDeleteTime)
                .last("limit 1");
        return getOne(query);
    }

    @Override
    @Transactional(rollbackFor = {Exception.class})
    public OutMessage<?> saveZt11Data(Zt11NonPublicEnterpriseDto data) {
        Unit unit = unitService.findByCode(data.getUnitCode());
        if (Objects.isNull(unit)) {
            return new OutMessage<>(Status.OBJEC_NOT_EXIST);
        }
        Zt11NonPublicEnterprise enterprise = this.findByUnitCode(unit.getCode());
        boolean result;
        if (Objects.nonNull(enterprise)) {
            String code = enterprise.getCode();
            BeanUtils.copyProperties(data, enterprise);
            enterprise.setUpdateTime(new Date());
            enterprise.setCode(code);
            result = this.updateById(enterprise);
        } else {
            Zt11NonPublicEnterprise zt11NonPublicEnterprise = new Zt11NonPublicEnterprise();
            BeanUtils.copyProperties(data, zt11NonPublicEnterprise);
            zt11NonPublicEnterprise.setCreateTime(new Date());
            result = this.save(zt11NonPublicEnterprise);
        }
        return new OutMessage<>(result ? Status.SUCCESS : Status.FAIL);
    }
}
