package com.zenith.front.core.analysis.count.history;

import cn.hutool.core.util.StrUtil;
import com.b1809.analysis.entity.PeggingPara;
import com.zenith.front.core.analysis.ext.condition.year2022.MemAllCondition2022B;
import com.zenith.front.core.analysis.ext.condition.year2022.OrgAllConditionB;
import org.jooq.Condition;

import java.util.Map;

import static org.jooq.impl.DSL.noCondition;

/**
 * <AUTHOR>
 * @date 2023/2/10
 */
public class Html35Count2022 {

    public Map<String, Object> getReportPeggingMap35(PeggingPara data) {
        String rowIndex = data.getRowIndex();
        if (StrUtil.equals(rowIndex, "7")) {
            OrgAllConditionB orgAllCond = new OrgAllConditionB();
            Condition condition = noCondition().and(new Html35CountHistory().getOrg35Condition(data.getOrgCode(), data.getOrgLevelCode()).toString().replace("ccp_org_all", orgAllCond.getTableName()))
                    .and(new Html35CountHistory().getOrgCheck35Condition(data.getColIndex()));
            return Html48CountHistory.getReportPageResult(data, orgAllCond.getTableName(), condition, orgAllCond.getLevelCodeField());
        }

        MemAllCondition2022B memAllCond = new MemAllCondition2022B();
        Condition condition = noCondition().and(new Html35CountHistory().getMemCheckListCondition(data.getOrgCode(), data.getOrgLevelCode(), data.getColIndex(), data.getRowIndex()).toString().replace("ccp_mem_all", memAllCond.getTableName()));
        return Html48CountHistory.getReportPageResult(data, memAllCond.getTableName(), condition, memAllCond.getLevelCodeField());
    }
}
