package com.zenith.front.core.service.user;

import cn.hutool.core.collection.CollUtil;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.zenith.front.api.permission.IPermissionService;
import com.zenith.front.api.role.IRoleService;
import com.zenith.front.api.user.IUserRolePermissionService;
import com.zenith.front.api.user.IUserService;
import com.zenith.front.common.constant.PermissionConstant;
import com.zenith.front.common.kit.HttpKit;
import com.zenith.front.common.kit.StrKit;
import com.zenith.front.core.cache.CacheUtils;
import com.zenith.front.dao.mapper.user.UserRolePermissionMapper;
import com.zenith.front.model.bean.Permission;
import com.zenith.front.model.bean.Role;
import com.zenith.front.model.bean.User;
import com.zenith.front.model.bean.UserRolePermission;
import com.zenith.front.model.constant.UserConstant;
import com.zenith.front.model.custom.UserTicket;
import com.zenith.front.model.dto.UserRolePermissionDTO;
import com.zenith.front.model.message.OutMessage;
import com.zenith.front.model.message.Status;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <p>
 * 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-07-10
 */
@Service
public class UserRolePermissionServiceImpl extends ServiceImpl<UserRolePermissionMapper, UserRolePermission> implements IUserRolePermissionService {

    @Resource
    private UserRolePermissionMapper userRolePermissionMapper;
    @Resource
    private IUserService iUserService;
    @Resource
    private UserServiceImpl userService;
    @Resource
    private IRoleService roleService;
    @Resource
    IPermissionService permissionService;
    @Value("${sync_flow_push}")
    private String sync_flow_push;

    @Override
    public boolean batchSave(List<UserRolePermission> userRolePermissionList) {
        return saveBatch(userRolePermissionList);
    }

    @Override
    public UserRolePermission findById(String currentUserRoleId) {
        return getById(currentUserRoleId);
    }

    @Override
    public boolean batchUpdate(List<UserRolePermission> userRolePermissionList) {
        return updateBatchById(userRolePermissionList);
    }

    @Override
    public boolean cleanAll(String userId) {
        return userRolePermissionMapper.cleanAll(userId) > 0;
    }

    @Override
    public OutMessage<String> editUserPermission(List<UserRolePermissionDTO> dtos) {
        UserTicket userVO = UserConstant.USER_CONTEXT.get();
        if (userVO == null) {
            return new OutMessage<>(Status.NOT_LOGIN);
        }

        // userId->permission
        Map<String, String> userIdPermissionMap = new HashMap<>(10);
        List<UserRolePermission> updateList = new ArrayList<>();
        for (UserRolePermissionDTO dto : dtos) {
            //查询被修改的用户权限关系是否存在,不存在则没有权限访问
            UserRolePermission userRolePermission = findById(dto.getId());
            if (userRolePermission == null) {
                return new OutMessage<>(Status.PERMISSION_DENIED);
            }

            //不能修改自己的权限
            String currentUserId = userVO.getUser().getId();
            if (StrKit.equals(currentUserId, userRolePermission.getUserId())) {
                return new OutMessage<>(Status.PERMISSION_DENIED);
            }
            //判断用户是否存在
            User user = iUserService.findById(userRolePermission.getUserId());
            if (user == null) {
                return new OutMessage<>(Status.USER_NOT_EXIST);
            }
            //当前用户登录的所管理的orgCode
            String currentOrgCode = userVO.getUserRolePermission().getOrgCode();
            //被修改用户的所属orgCode
            String orgCode = user.getOrgCode();
            //如果被修改的用户不属于当前用户所管理组织下,则没有权限访问
            if (!orgCode.startsWith(currentOrgCode)) {
                return new OutMessage<>(Status.PERMISSION_DENIED);
            }

            //判断角色是否存在
            Role role = roleService.findByIdAndValid(userRolePermission.getRoleId());
            if (role == null) {
                return new OutMessage<>(Status.ROLE_NOT_EXIST);
            }
            //当前登录用户的角色id
            String currentRoleId = userVO.getUserRolePermission().getRoleId();
            //判断被修改的角色 是否是当前登录角色的下级角色code
            Role subRole = roleService.findSubRoleByIdAndParentId(userRolePermission.getRoleId(), currentRoleId);
            if (subRole == null) {
                return new OutMessage<>(Status.PERMISSION_DENIED);
            }
            //权限编号 例 "1,5,6,7,8,10,11,12,14"
            String permissions = dto.getPermissions();
            Set<String> permissionsIdSet = StringUtils.commaDelimitedListToSet(permissions);
            //排序
            List<String> permissionsIdList = permissionsIdSet.stream().sorted(Comparator.naturalOrder()).collect(Collectors.toList());
            List<Permission> permissionList = permissionService.findAll();
            //被修改的角色权限码 调整前：前端传权限码。调整后，前端传被选中的权限编号，由程序来处理权限码
            String permission = permissionList.stream().map(p -> permissionsIdList.contains(p.getId().toString()) ? "1" : "0").collect(Collectors.joining());
            //当前角色的权限码
            char[] currentPermissionCodes = userVO.getUserRolePermission().getPermission().toCharArray();
            char[] permissionCodes = permission.toCharArray();
            if (currentPermissionCodes.length != permissionCodes.length || permissionCodes.length != userRolePermission.getPermission().length()) {
                return new OutMessage<>(Status.PERMISSION_DENIED);
            }

            for (int i = 0; i < currentPermissionCodes.length; i++) {
                //92 补录，93 错误录入，94 确定预备党员
                // TODO: 2023/2/21 存在上级无92-94权限，下级有，上级修改下级权限时，会提示 无权限处理
                if (i >= 91 && i <= 93) {
                    continue;
                }
                char code = currentPermissionCodes[i];
                //如果当前登录用户的权限码为0,那么被修改的用户角色权限码不能为1
                if (code == PermissionConstant.PERMISSION_OFF) {
                    char permissionCode = permissionCodes[i];
                    if (permissionCode == PermissionConstant.PERMISSION_ON) {
                        return new OutMessage<>(Status.PERMISSION_DENIED);
                    }
                }
            }

            //前面所有验证都通过,更新用户权限
            UserRolePermission updatePermission = new UserRolePermission();
            updatePermission.setId(userRolePermission.getId());
            updatePermission.setPermission(permission);
            updateList.add(updatePermission);

            userIdPermissionMap.put(user.getId(), permission);
            //编辑了用户权限强行让用户重新登录
            String userToken = CacheUtils.getUserToken(user.getId());
            if (userToken != null) {
                CacheUtils.removeUserCache(userToken);
            }
        }

        boolean isOk = batchUpdate(updateList);
        // todo 2025年5月16日 开启用户信息同步
        if (isOk) {
            userIdPermissionMap.forEach(this::updateUserPermissionToMiddle);
        }
        return isOk ? new OutMessage<>(Status.SUCCESS) : new OutMessage<>(Status.FAIL);
    }

    /**
     * 更新中间交换区用户数据
     *
     * @param userId     用户id
     * @param permission 权限编码
     */
    public void updateUserPermissionToMiddle(String userId, String permission) {
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("id", userId);
        jsonObject.put("permission", permission);
        String replaceUrl = sync_flow_push.replace("/api/flow/push", "");
        String s = HttpKit.doPost(replaceUrl + "/login/updateUserPermission", jsonObject, "UTF-8");
        System.out.println("账号权限同步情况===>" + s);
    }

    @Override
    public UserRolePermission findByUserIdAndRoleId(String userId, String roleId) {
        return getOne(new LambdaQueryWrapper<UserRolePermission>()
                .eq(UserRolePermission::getUserId, userId).eq(UserRolePermission::getRoleId, roleId).last("limit 1"));
    }

    @Override
    public List<UserRolePermission> updateUserRolePermissionOrgCode(String newOrgCode) {
        return userRolePermissionMapper.updateUserRolePermissionOrgCode(newOrgCode);
    }

    @Override
    public List<UserRolePermission> findByRoleIds(List<String> roleIds) {
        return list(new LambdaQueryWrapper<UserRolePermission>().in(CollUtil.isNotEmpty(roleIds), UserRolePermission::getRoleId, roleIds));
    }

    @Override
    public List<UserRolePermission> findByRoleId(String roleId) {
        return list(new LambdaQueryWrapper<UserRolePermission>().eq(UserRolePermission::getRoleId, roleId));
    }

    @Override
    public List<UserRolePermission> findByIndexValue(Integer index, String value, List<String> orgIdList) {
        return list(new LambdaQueryWrapper<UserRolePermission>().in(UserRolePermission::getOrgId, orgIdList).last(" AND  substr(permission," + index + ",1)='" + value + "'  ORDER BY org_code ASC"));
    }

}
