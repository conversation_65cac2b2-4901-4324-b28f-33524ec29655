package com.zenith.front.core.service.mem;

import cn.hutool.core.date.DateUtil;
import com.zenith.front.api.mem.ICalculateAgeTask;
import com.zenith.front.api.report.IStatisticsYearService;
import com.zenith.front.common.constant.CommonConstant;
import com.zenith.front.dao.mapper.mem.CalculateAgeTaskMapper;
import com.zenith.front.model.bean.StatisticsYear;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.Objects;

/**
 * 计算年龄任务
 *
 * <AUTHOR>
 * @version V1.0
 * @Package com.zenith.front.core.service.mem
 * @date 2022/5/16 15:32
 */
@Slf4j
@Service
public class CalculateAgeTaskImpl implements ICalculateAgeTask {

    @Resource
    CalculateAgeTaskMapper calculateAgeTaskMapper;
    @Resource
    private IStatisticsYearService iStatisticsYearService;

    @Override
    public void calculateMemAge() {
        calculateAgeTaskMapper.calculateMemAge(getStandardDate());
    }

    @Override
    public void calculateDevelopMemAge() {
        calculateAgeTaskMapper.calculateDevelopMemAge(getStandardDate());
    }

    @Override
    public void calculateDevelopStepLogAge() {
        calculateAgeTaskMapper.calculateDevelopStepLogAge(getStandardDate());
    }

    /**
     * 切换时间
     * 年统期间计算年龄以年统结束时间为准。年统年份与当前年份一致时以当前时间为准。所以年统结束后年统时间要更新为下一年的统计时间
     *
     * @return
     */
    private Date getStandardDate() {
        Date computeDate = new Date();
        StatisticsYear statisticsYear = iStatisticsYearService.getStatisticsYear(CommonConstant.ONE);
        if (Objects.nonNull(statisticsYear) && Objects.nonNull(statisticsYear.getEndDate())) {
            // 年统结束时间
            Date date = statisticsYear.getEndDate();
            // 年统时间年份小于当前时间年份，以年统结束时间为准
            if (DateUtil.year(date) < DateUtil.year(computeDate)) {
                computeDate = date;
            }
        }
        return computeDate;
    }
}
