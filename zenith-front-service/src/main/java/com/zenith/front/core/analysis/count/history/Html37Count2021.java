package com.zenith.front.core.analysis.count.history;

import com.b1809.analysis.entity.PeggingPara;
import com.b1809.analysis.extend.ITableCount;
import com.zenith.front.core.analysis.ext.condition.year2021.OrgAllCondition2021;
import org.jooq.Condition;
import org.springframework.stereotype.Component;

import java.util.Map;

import static org.jooq.impl.DSL.noCondition;

/**
 * 第三十七表 党的地方委员会换届和领导班子召开民主生活会情况 2021年统计走的页面配置
 *
 * <AUTHOR>
 * @date 2023/01/03
 */
@Component
public class Html37Count2021 extends Html37CountHistory implements ITableCount {

    @Override
    public String getReportCode() {
        return "2021_37.html";
    }

    @Override
    public Map<String, Map<String, Number>> doCount(String reportCode, String orgCode, String orgLevelCode) throws Exception {
        return null;
    }

    @Override
    public Map<String, Object> doPegging(PeggingPara peggingPara) throws Exception {
        return null;
    }

    @Override
    public Map<String, Number> doReplenishCount(String reportCode, String orgCode, String orgLevelCode) throws Exception {
        return getReplenishCount(orgCode, orgLevelCode, "2021");
    }


    @Override
    public Map<String, Object> doReplenishPegging(PeggingPara peggingPara) throws Exception {
        OrgAllCondition2021 orgAllCondition = new OrgAllCondition2021();
        Condition condition = noCondition().and(new Html6CountHistory().getOrgListCondition(peggingPara.getOrgCode(), peggingPara.getOrgLevelCode()).toString().replace("ccp_org_all", orgAllCondition.getTableName()))
                .and(this.getRowCondition(peggingPara.getRowIndex()));
        return Html48CountHistory.getReportPageResult(peggingPara, orgAllCondition.getTableName(), condition, orgAllCondition.getLevelCodeField());
    }


}
