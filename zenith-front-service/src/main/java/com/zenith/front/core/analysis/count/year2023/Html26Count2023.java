package com.zenith.front.core.analysis.count.year2023;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.StrUtil;
import com.b1809.analysis.entity.PeggingPara;
import com.b1809.analysis.extend.ITableCount;
import com.b1809.analysis.plugin.elasticsearch.EsKit;
import com.b1809.analysis.result.ReportResult;
import com.b1809.analysis.table.helper.TableHelper;
import com.b1809.analysis.table.helper.factory.TableHelperFactory;
import com.jfinal.plugin.activerecord.Page;
import com.jfinal.plugin.activerecord.Record;
import com.zenith.front.core.analysis.ext.condition.year2023.OrgAllCondition2023;
import com.zenith.front.core.analysis.ext.condition.year2023.OrgPartyCondition2023;
import com.zenith.front.core.analysis.ext.condition.year2023.UnitAllCondition2023;
import lombok.extern.slf4j.Slf4j;
import org.jooq.Condition;
import org.jooq.Record1;
import org.jooq.SelectConditionStep;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.Executor;
import java.util.stream.Collectors;

import static com.zenith.front.common.constant.DBConstant.DSL_CONTEXT;
import static org.jooq.impl.DSL.*;

/**
 *   党的基层组织数量情况和换届情况
 *
 */
@Component
@Slf4j
public class Html26Count2023 implements ITableCount {

    private static Html26Count2023 html26Count;

    @PostConstruct
    public void init() {
        html26Count = this;
    }

    @Resource
    private Executor mySimpleAsync;

    @Override
    public String getReportCode() {
        return "2023_26.html";
    }

    @Override
    public Map<String, Map<String, Number>> doCount(String reportCode, String orgCode, String orgLevelCode) throws Exception {
        return null;
    }

    @Override
    public Map<String, Object> doPegging(PeggingPara peggingPara) throws Exception {
        return null;
    }

    @Override
    public Map<String, Number> doReplenishCount(String reportCode, String orgCode, String orgLevelCode) throws Exception {
        return getReplenishCount(orgCode, orgLevelCode, "2023");
    }

    public Map<String, Number> getReplenishCount(String orgCode, String orgLevelCode, String tableYear) throws Exception {
        String ccpOrgParty = StrUtil.isEmpty(tableYear) ? "ccp_org_party" : "ccp_org_party_" + tableYear;
        String ccpUnitAll = StrUtil.isEmpty(tableYear) ? "ccp_unit_all" : "ccp_unit_all_" + tableYear;
        String ccpOrgAll = StrUtil.isEmpty(tableYear) ? "ccp_org_all" : "ccp_org_all_" + tableYear;
        Map<String, Number> result = new LinkedHashMap<>(100);
        Html52Count2023.initReplenishCol(1, 40, result);

        SelectConditionStep<Record1<Object>> condition1 = DSL_CONTEXT.select(field("d108_code,d35_code"))
                .from(table(name(ccpOrgParty)).as("ccp_org_party"))
                .where(new OrgPartyCondition2023().create(orgCode, orgLevelCode).and("delete_time IS NULL and d108_code like '3%'"));
        CompletableFuture<List<Map<String, Object>>> future1 = CompletableFuture.supplyAsync(() -> EsKit.findBySql(condition1.toString()).toMap(), html26Count.mySimpleAsync);
        //2 中央和各级地方党委派出工委
        SelectConditionStep<Record1<Object>> condition2 = DSL_CONTEXT.select(field("org_code,d01_code,d03_code,d04_code,is_retire,is_towns,is_community"))
                .from(table(name(ccpOrgAll)).as("ccp_org_all")).where(new Html7Count202302().getOrgListCondition(orgCode, orgLevelCode).and(" d01_code LIKE '2%' "));
        CompletableFuture<List<Map<String, Object>>> future2 = CompletableFuture.supplyAsync(() -> EsKit.findBySql(condition2.toString()).toMap(), html26Count.mySimpleAsync);

//        SelectConditionStep<Record1<Object>> condition3 = DSL_CONTEXT.select(field("org_code,d01_code,d03_code,d04_code,is_retire,is_towns,is_community"))
//                .from(table(name(ccpOrgAll)).as("ccp_org_all")).where(new Html6Count().getOrgListCondition(orgCode, orgLevelCode).and(" d04_code in ('131', '132') "));
//        CompletableFuture<List<Map<String, Object>>> future3 = CompletableFuture.supplyAsync(() -> EsKit.findBySql(condition3.toString()).toMap(), html24Count.mySimpleAsync);
        //4 行政村
        SelectConditionStep<Record1<Object>> condition4 = DSL_CONTEXT.select(field("org_code,d01_code,d03_code,d04_code,is_retire,is_towns,is_community"))
                .from(table(name(ccpOrgAll)).as("ccp_org_all")).where(new Html7Count202302().getOrgListCondition(orgCode, orgLevelCode).and(" is_community = 1 or d04_code='923' "));
        CompletableFuture<List<Map<String, Object>>> future4 = CompletableFuture.supplyAsync(() -> EsKit.findBySql(condition4.toString()).toMap(), html26Count.mySimpleAsync);
        //5 农村专业技术协会 农民专业合作社 家庭农场
        SelectConditionStep<Record1<Object>> condition5 = DSL_CONTEXT.select(field("org_code,d01_code,d03_code,d04_code,is_retire,is_towns,is_community"))
                .from(table(name(ccpOrgAll)).as("ccp_org_all")).where(new Html7Count202302().getOrgListCondition(orgCode, orgLevelCode).and(" d04_code IN ('513','94','95')"));
        CompletableFuture<List<Map<String, Object>>> future5 = CompletableFuture.supplyAsync(() -> EsKit.findBySql(condition5.toString()).toMap(), html26Count.mySimpleAsync);
        SelectConditionStep<Record1<Object>> condition51 = DSL_CONTEXT.select(field("code,d04_code"))
                .from(table(name(ccpUnitAll)).as("ccp_unit_all")).where(new Html7Count202302().getUnitListCondition(orgCode, orgLevelCode).and(" d04_code IN ('513','94','95')"));
        CompletableFuture<List<Map<String, Object>>> future51 = CompletableFuture.supplyAsync(() -> EsKit.findBySql(condition51.toString()).toMap(), html26Count.mySimpleAsync);
        //TODO 20230203 表24补充资料5，农村专业技术协会、农民专业合作社、家庭农场，这几个数改成统计乡镇、街道单位信息上的相应字段之和
        SelectConditionStep<Record1<Object>> condition52022 = DSL_CONTEXT.select(field("code,d04_code,family_farm_num,farmer_specialized_cooperatives_num,rural_professional_technical_association_num"))
                .from(table(name(ccpUnitAll)).as("ccp_unit_all")).where(new Html7Count202302().getUnitListCondition(orgCode, orgLevelCode).and(" d04_code like '91%'"));
        CompletableFuture<List<Map<String, Object>>> future52022 = CompletableFuture.supplyAsync(() -> EsKit.findBySql(condition52022.toString()).toMap(), html26Count.mySimpleAsync);
        //6 街道、社区
        SelectConditionStep<Record1<Object>> condition6 = DSL_CONTEXT.select(field("org_code,code,d01_code,d03_code,d04_code,is_retire,is_towns,is_community"))
                .from(table(name(ccpOrgAll)).as("ccp_org_all")).where(new Html7Count202302().getOrgListCondition(orgCode, orgLevelCode).and(" is_towns=3 or is_community in(2,3)"));
        CompletableFuture<List<Map<String, Object>>> future6 = CompletableFuture.supplyAsync(() -> EsKit.findBySql(condition6.toString()).toMap(), html26Count.mySimpleAsync);

        List<CompletableFuture<List<Map<String, Object>>>> futureList = new ArrayList<>();
        futureList.add(future2);
        //futureList.add(future3);
        futureList.add(future4);
        futureList.add(future5);
        futureList.add(future6);
        futureList.add(future51);
        futureList.add(future52022);
        CompletableFuture<Void> combinedFutures = CompletableFuture.allOf(futureList.toArray(new CompletableFuture[0]));
        combinedFutures.whenComplete((v, th) -> log.info("========================第24表 党的基层组织数量情况和换届情况==============================")).join();
        //1．党组	个，其中中央一级机关	个、省（区、市）一级机关	个、市（地、州、盟）一级机关	个、县（市、区、旗）一级机关	个。
        for (Map<String, Object> map : future1.get()) {
            String d35Code = Objects.nonNull(map.get("d35_code")) ? map.get("d35_code").toString() : "";
            String d108Code = (String) map.getOrDefault("d108_code", "");
            int authority = StrUtil.equalsAny(d108Code, "31", "32", "33", "36", "3A", "3C") ? 1 : 0;
            Html52Count2023.setReplenishMapValue("1", 1, result);
            Html52Count2023.setReplenishMapValue("2", StrUtil.startWith(d35Code, "1") ? authority : 0, result);
            Html52Count2023.setReplenishMapValue("3", StrUtil.startWithAny(d35Code, "2", "3") ? authority : 0, result);
            Html52Count2023.setReplenishMapValue("4", StrUtil.startWithAny(d35Code, "4", "5") ? authority : 0, result);
            Html52Count2023.setReplenishMapValue("5", StrUtil.startWithAny(d35Code, "7", "8", "9", "A") ? authority : 0, result);
        }
        List<Map<String, Object>> list = futureList.stream().flatMap(future -> future.join().stream()).collect(Collectors.toList());

        //3．国务院和地方政府工作部门中有基层党组织   个，其中党委    个、总支部    个、支部    个。
        SelectConditionStep<Record1<Object>> ccp_org_all_list = DSL_CONTEXT.select(field("org_code,d01_code,d03_code,d04_code,is_retire,is_towns,is_community"))
                .from(table(name(ccpOrgAll)).as("ccp_org_all")).where(new Html7Count202302().getOrgListCondition(orgCode, orgLevelCode).and(" is_state_council = 1"));
        List<Record> recordTotalList = EsKit.findBySql(ccp_org_all_list.toString()).toRecord();
        if (CollectionUtil.isNotEmpty(recordTotalList)) {
            for (Record record : recordTotalList) {
                String d01Codes = record.getStr("d01_code");
                Html52Count2023.setReplenishMapValue("10", StrUtil.startWithAny(d01Codes, "6", "9") ? 1 : 0, result);
                Html52Count2023.setReplenishMapValue("11", StrUtil.equalsAny(d01Codes, "61", "911") ? 1 : 0, result);
                Html52Count2023.setReplenishMapValue("12", StrUtil.equalsAny(d01Codes, "62", "921") ? 1 : 0, result);
                Html52Count2023.setReplenishMapValue("13", StrUtil.startWithAny(d01Codes, "63", "931", "932") ? 1 : 0, result);
            }

        }
//        List<Map<String, Object>> maps = future3.get();
//        if (CollectionUtil.isNotEmpty(maps)) {
//            List<String> collect = maps.stream().map(s -> (String) s.get("org_code")).collect(Collectors.toList());
//            String value = "";
//            int i = 1;
//            for (String s : collect) {
//                if (i == 1) {
//                    value += "(org_code like '" + s + "%'";
//                }
//                value += " or org_code like '" + s + "%'";
//                if (collect.size()==i) {
//                    value += " or org_code like '" + s + "%')";
//                }
//                i++;
//            }
//            SelectConditionStep<Record1<Object>> ccp_org_all = DSL_CONTEXT.select(field("org_code,d01_code,d03_code,d04_code,is_retire,is_towns,is_community"))
//                    .from(table(name(ccpOrgAll)).as("ccp_org_all")).where(noCondition().and("delete_time is null and (is_dissolve is null or is_dissolve !=1)").and(value));
//            List<com.jfinal.plugin.activerecord.Record> records = EsKit.findBySql(ccp_org_all.toString()).toRecord();
//            if (CollectionUtil.isNotEmpty(records)) {
//                for (Record record : records) {
//                    String d01Codes = record.getStr("d01_code");
//                    Html48Count.setReplenishMapValue("10", StrUtil.startWithAny(d01Codes, "6", "9") ? 1 : 0, result);
//                    Html48Count.setReplenishMapValue("11", StrUtil.equalsAny(d01Codes, "61", "911") ? 1 : 0, result);
//                    Html48Count.setReplenishMapValue("12", StrUtil.equalsAny(d01Codes, "62", "921") ? 1 : 0, result);
//                    Html48Count.setReplenishMapValue("13", StrUtil.startWithAny(d01Codes, "63", "931", "932") ? 1 : 0, result);
//                }
//
//            }
//
//        }
        List<Map<String, Object>> mapList = futureList.get(0).get();
        for (Map<String, Object> map : mapList) {
            String d01Code = (String) map.getOrDefault("d01_code", "");
            String d03Code = (String) map.getOrDefault("d03_code", "");
            //2．中央和各级地方党委派出工委   个，其中省（区、市）委派出工委  个、省辖市（州）委派出工委  个、县（市、区、旗）委派出工委  个。
            if (StrUtil.startWithAny(d01Code, "2")) {
                Html52Count2023.setReplenishMapValue("6", StrUtil.startWithAny(d01Code, "2") ? 1 : 0, result);
                Html52Count2023.setReplenishMapValue("7", StrUtil.startWith(d03Code, "2") ? 1 : 0, result);
                Html52Count2023.setReplenishMapValue("8", StrUtil.startWith(d03Code, "3") ? 1 : 0, result);
                // todo 2024-01-30 新的统计口径，不统计其他工委
                Html52Count2023.setReplenishMapValue("9", StrUtil.startWithAny(d03Code, "4") && !StrUtil.equals(d01Code, "29")? 1 : 0, result);
            }
        }

        List<Map<String, Object>> mapList1 = futureList.get(1).get();
        for (Map<String, Object> map : mapList1) {
            String d01Code = (String) map.getOrDefault("d01_code", "");
            String isTowns = String.valueOf(map.get("is_towns"));
            //4．行政村中有党组织    个，其中党委    个、总支部    个、支部    个。
            if (Objects.equals(map.get("is_community"), 1) || Objects.equals(map.get("d04_code"), "923")) {
                Html52Count2023.setReplenishMapValue("14", StrUtil.startWithAny(d01Code, "6", "9") ? 1 : 0, result);
                Html52Count2023.setReplenishMapValue("15", StrUtil.equalsAny(d01Code, "61", "911") ? 1 : 0, result);
                Html52Count2023.setReplenishMapValue("16", StrUtil.equalsAny(d01Code, "62", "921") ? 1 : 0, result);
                Html52Count2023.setReplenishMapValue("17", StrUtil.startWithAny(d01Code, "63", "931", "932") ? 1 : 0, result);
                //城市街道所属的行政村中有党组织    个，其中党委    个、总支部    个、支部    个
                Html52Count2023.setReplenishMapValue("18", (StrUtil.startWithAny(d01Code, "6", "9") && StrUtil.equalsAny(isTowns,"3")) ? 1 : 0, result);
                Html52Count2023.setReplenishMapValue("19", (StrUtil.equalsAny(d01Code, "61", "911") && StrUtil.equalsAny(isTowns,"3")) ? 1 : 0, result);
                Html52Count2023.setReplenishMapValue("20", (StrUtil.equalsAny(d01Code, "62", "921") && StrUtil.equalsAny(isTowns,"3"))? 1 : 0, result);
                Html52Count2023.setReplenishMapValue("21", (StrUtil.startWithAny(d01Code, "63", "931", "932") && StrUtil.equalsAny(isTowns,"3"))? 1 : 0, result);
            }
        }

        List<Map<String, Object>> mapList2 = futureList.get(2).get();
        for (Map<String, Object> map : mapList2) {
            String d01Code = (String) map.getOrDefault("d01_code", "");
            String d04Code = (String) map.getOrDefault("d04_code", "");
            //5．农村专业技术协会    个，共有党组织    个，其中党委    个、总支部    个、支部    个；农民专业合作社    个，共有党组织    个，其中党委    个、总支部    个、支部    个；家庭农场    个，共有党组织    个，其中党委    个、总支部    个、支部    个。
            if (StrUtil.equals(d04Code, "513")) {
                Html52Count2023.setReplenishMapValue("23", StrUtil.startWithAny(d01Code, "6", "9") ? 1 : 0, result);
                Html52Count2023.setReplenishMapValue("24", StrUtil.equalsAny(d01Code, "61", "911") ? 1 : 0, result);
                Html52Count2023.setReplenishMapValue("25", StrUtil.equalsAny(d01Code, "62", "921") ? 1 : 0, result);
                Html52Count2023.setReplenishMapValue("26", StrUtil.startWithAny(d01Code, "63", "931", "932") ? 1 : 0, result);
            }
            if (StrUtil.equals(d04Code, "94")) {
                Html52Count2023.setReplenishMapValue("28", StrUtil.startWithAny(d01Code, "6", "9") ? 1 : 0, result);
                Html52Count2023.setReplenishMapValue("29", StrUtil.equalsAny(d01Code, "61", "911") ? 1 : 0, result);
                Html52Count2023.setReplenishMapValue("30", StrUtil.equalsAny(d01Code, "62", "921") ? 1 : 0, result);
                Html52Count2023.setReplenishMapValue("31", StrUtil.startWithAny(d01Code, "63", "931", "932") ? 1 : 0, result);
            }
            if (StrUtil.equals(d04Code, "95")) {
                Html52Count2023.setReplenishMapValue("33", StrUtil.startWithAny(d01Code, "6", "9") ? 1 : 0, result);
                Html52Count2023.setReplenishMapValue("34", StrUtil.equalsAny(d01Code, "61", "911") ? 1 : 0, result);
                Html52Count2023.setReplenishMapValue("35", StrUtil.equalsAny(d01Code, "62", "921") ? 1 : 0, result);
                Html52Count2023.setReplenishMapValue("36", StrUtil.startWithAny(d01Code, "63", "931", "932") ? 1 : 0, result);
            }
        }
        List<Map<String, Object>> mapList4 = futureList.get(4).get();
        List<Map<String, Object>> mapList52022 = futureList.get(5).get();
        if (StrUtil.isEmpty(tableYear)) {
            for (Map<String, Object> map : mapList52022) {
                Object num1 = map.get("rural_professional_technical_association_num");
                Object num2 = map.get("farmer_specialized_cooperatives_num");
                Object num3 = map.get("family_farm_num");
                Html52Count2023.setReplenishMapValue("22", Objects.nonNull(num1) ? Integer.parseInt(num1.toString()) : 0, result);
                Html52Count2023.setReplenishMapValue("27", Objects.nonNull(num2) ? Integer.parseInt(num2.toString()) : 0, result);
                Html52Count2023.setReplenishMapValue("32", Objects.nonNull(num3) ? Integer.parseInt(num3.toString()) : 0, result);
            }
        } else {
            for (Map<String, Object> map : mapList4) {
                String d04Code = (String) map.getOrDefault("d04_code", "");
                Html52Count2023.setReplenishMapValue("22", StrUtil.equals(d04Code, "513") ? 1 : 0, result);
                Html52Count2023.setReplenishMapValue("27", StrUtil.equals(d04Code, "94") ? 1 : 0, result);
                Html52Count2023.setReplenishMapValue("32", StrUtil.equals(d04Code, "95") ? 1 : 0, result);
            }
        }

        List<Map<String, Object>> mapList3 = futureList.get(3).get();
        for (Map<String, Object> map : mapList3) {
            String d01Code = (String) map.getOrDefault("d01_code", "");
            //6．街道、社区中共有国有企业离退休干部党组织    个，其中党委    个、总支部    个、支部    个。
            if (Objects.nonNull(map.get("is_retire")) && Objects.equals(map.get("is_retire").toString(), "1") && (StrUtil.startWithAny(d01Code, "6") || StrUtil.startWithAny(d01Code, "9")) &&
                    ((Objects.nonNull(map.get("is_towns")) && "3".equals(map.get("is_towns").toString())) || (Objects.nonNull(map.get("is_community")) && StrUtil.equalsAny(map.get("is_community").toString(), "2", "3")))) {
                Html52Count2023.setReplenishMapValue("37", StrUtil.startWithAny(d01Code, "6", "9") ? 1 : 0, result);
                Html52Count2023.setReplenishMapValue("38", StrUtil.equalsAny(d01Code, "61", "911") ? 1 : 0, result);
                Html52Count2023.setReplenishMapValue("39", StrUtil.equalsAny(d01Code, "62", "921") ? 1 : 0, result);
                Html52Count2023.setReplenishMapValue("40", StrUtil.startWithAny(d01Code, "63", "931", "932") ? 1 : 0, result);
            }
        }
        return result;
    }

    @Override
    public Map<String, Object> doReplenishPegging(PeggingPara peggingPara) {
        String colIndex = peggingPara.getRowIndex();
        if (StrUtil.equalsAny(colIndex, "1", "2", "3", "4", "5")) {
            OrgPartyCondition2023 partyCondition = new OrgPartyCondition2023();
            Condition condition = partyCondition.create(peggingPara.getOrgCode(), peggingPara.getOrgLevelCode()).and(this.getPartyRowCondition(peggingPara.getRowIndex()));
            return Html52Count2023.getReportPageResult(peggingPara, partyCondition.getTableName(), condition, partyCondition.getLevelCodeField());
        } else if (StrUtil.equalsAny(colIndex, "22", "27", "32")) {
            Condition condition = new Html7Count202302().getUnitListCondition(peggingPara.getOrgCode(), peggingPara.getOrgLevelCode()).and(this.getUnitRowCondition(colIndex, null));
            UnitAllCondition2023 unitAllCondition = new UnitAllCondition2023();
            return Html52Count2023.getReportPageResult(peggingPara, unitAllCondition.getTableName(), condition, unitAllCondition.getLevelCodeField());
        } else {
            Condition condition = this.getOrgRowCondition(peggingPara.getRowIndex(), peggingPara.getOrgCode(), peggingPara.getOrgLevelCode(), null);
            OrgAllCondition2023 orgAllCondition = new OrgAllCondition2023();
            return Html52Count2023.getReportPageResult(peggingPara, orgAllCondition.getTableName(), condition, orgAllCondition.getLevelCodeField());
        }
    }

    Condition getOrgRowCondition(String rowIndex, String orgCode, String orgLevelCode, String tableYear) {
        Condition condition = noCondition();
        condition = condition.and(new Html7Count202302().getOrgListCondition(orgCode, orgLevelCode));
        //2．中央和各级地方党委派出工委   个，其中省（区、市）委派出工委  个、省辖市（州）委派出工委  个、县（市、区、旗）委派出工委  个。
        if (StrUtil.equalsAny(rowIndex, "6", "7", "8", "9")) {
            condition = condition.and("d01_code like '2%'");
            if (StrUtil.equals(rowIndex, "7")) {
                condition = condition.and("d03_code like '2'");
            }
            if (StrUtil.equals(rowIndex, "8")) {
                condition = condition.and("d03_code like '3'");
            }
            if (StrUtil.equals(rowIndex, "9")) {
                condition = condition.and("d03_code like '4%'");
            }
        }
        //3．国务院和地方政府工作部门中有基层党组织   个，其中党委    个、总支部    个、支部    个。
        if (StrUtil.equalsAny(rowIndex, "10", "11", "12", "13")) {
            String ccpOrgAll = StrUtil.isEmpty(tableYear) ? "ccp_org_all" : "ccp_org_all_" + tableYear;
            SelectConditionStep<Record1<Object>> condition3 = DSL_CONTEXT.select(field("org_code,d01_code,d03_code,d04_code,is_retire,is_towns,is_community"))
                    .from(table(name(ccpOrgAll)).as("ccp_org_all")).where(new Html7Count202302().getOrgListCondition(orgCode, orgLevelCode).and(" d04_code in ('131', '132') "));
            CompletableFuture<List<Map<String, Object>>> future3 = CompletableFuture.supplyAsync(() -> EsKit.findBySql(condition3.toString()).toMap(), html26Count.mySimpleAsync);


            List<Map<String, Object>> maps = null;
            try {
                maps = future3.get();
            } catch (InterruptedException | ExecutionException e) {
                e.printStackTrace();
            }
            StringBuilder value = new StringBuilder();
            if (CollUtil.isNotEmpty(maps)) {
                List<String> collect = maps.stream().map(s -> (String) s.get("org_code")).collect(Collectors.toList());

                int i = 1;
                for (String s : collect) {
                    if (i == 1) {
                        value.append("(org_code like '").append(s).append("%'");
                    }
                    value.append(" or org_code like '").append(s).append("%'");
                    if (collect.size() == i) {
                        value.append(" or org_code like '").append(s).append("%')");
                    }
                    i++;
                }
            }
            //condition = condition.and("d04_code like '131%' or d04_code like '132%'");
            if (StrUtil.equals(rowIndex, "10")) {
                condition = condition.and("d01_code like '6%' or d01_code like '9%'").and(value.toString());
            }
            if (StrUtil.equals(rowIndex, "11")) {
                condition = condition.and("d01_code in ('61','911')").and(value.toString());
            }
            if (StrUtil.equals(rowIndex, "12")) {
                condition = condition.and("d01_code in ('62','921')").and(value.toString());
            }
            if (StrUtil.equals(rowIndex, "13")) {
                condition = condition.and("d01_code like '63%' or d01_code like '931%' or d01_code like '932%' ").and(value.toString());
            }
        }
        //4．行政村中有党组织    个，其中党委    个、总支部    个、支部    个。
        if (StrUtil.equalsAny(rowIndex, "14", "15", "16", "17","18","19","20","21")) {
            condition = condition.and("is_community = 1 or d04_code='923'").and("d01_code like '6%' or d01_code like '9%'");
            if (StrUtil.equals(rowIndex, "15")) {
                condition = condition.and("d01_code in ('61','911')");
            }
            if (StrUtil.equals(rowIndex, "16")) {
                condition = condition.and("d01_code in ('62','921')");
            }
            if (StrUtil.equals(rowIndex, "17")) {
                condition = condition.and("d01_code like '63%' or d01_code like '931%' or d01_code like '932%' ");
            }
            //城市街道所属的行政村中有党组织    个，其中党委    个、总支部    个、支部    个
            if (StrUtil.equals(rowIndex, "18")) {
                condition = condition.and("is_towns in ('1','2','3')");
            }
            if (StrUtil.equals(rowIndex, "19")) {
                condition = condition.and("d01_code in ('61','911')").and("is_towns in ('1','2','3')");
            }
            if (StrUtil.equals(rowIndex, "20")) {
                condition = condition.and("d01_code in ('62','921')").and("is_towns in ('1','2','3')");
            }
            if (StrUtil.equals(rowIndex, "21")) {
                condition = condition.and("d01_code like '63%' or d01_code like '931%' or d01_code like '932%'").and("is_towns in ('1','2','3')");
            }
        }
        //5．农村专业技术协会    个，共有党组织    个，其中党委    个、总支部    个、支部    个；农民专业合作社    个，共有党组织    个，其中党委    个、总支部    个、支部    个；家庭农场    个，共有党组织    个，其中党委    个、总支部    个、支部    个。
        if (StrUtil.equalsAny(rowIndex, "22", "23", "24", "25", "26")) {
            condition = condition.and("d04_code = '513'").and("d01_code like '6%' or d01_code like '9%'");
            if (StrUtil.equals(rowIndex, "24")) {
                condition = condition.and("d01_code in ('61','911')");
            }
            if (StrUtil.equals(rowIndex, "25")) {
                condition = condition.and("d01_code in ('62','921')");
            }
            if (StrUtil.equals(rowIndex, "26")) {
                condition = condition.and("d01_code like '63%' or d01_code like '931%' or d01_code like '932%' ");
            }
        }
        if (StrUtil.equalsAny(rowIndex, "27", "28", "29", "30", "31")) {
            condition = condition.and("d04_code = '94'").and("d01_code like '6%' or d01_code like '9%'");
            if (StrUtil.equals(rowIndex, "29")) {
                condition = condition.and("d01_code in ('61','911')");
            }
            if (StrUtil.equals(rowIndex, "30")) {
                condition = condition.and("d01_code in ('62','921')");
            }
            if (StrUtil.equals(rowIndex, "31")) {
                condition = condition.and("d01_code like '63%' or d01_code like '931%' or d01_code like '932%' ");
            }
        }
        if (StrUtil.equalsAny(rowIndex, "32", "33", "34", "35", "36")) {
            condition = condition.and("d04_code = '95'").and("d01_code like '6%' or d01_code like '9%'");
            if (StrUtil.equals(rowIndex, "34")) {
                condition = condition.and("d01_code in ('61','911')");
            }
            if (StrUtil.equals(rowIndex, "35")) {
                condition = condition.and("d01_code in ('62','921')");
            }
            if (StrUtil.equals(rowIndex, "36")) {
                condition = condition.and("d01_code like '63%' or d01_code like '931%' or d01_code like '932%' ");
            }
        }
        //6．街道、社区中共有国有企业离退休干部党组织    个，其中党委    个、总支部    个、支部    个。
        if (StrUtil.equalsAny(rowIndex, "37", "38", "39", "40")) {
            condition = condition.and("is_retire = 1 and (is_towns=3 or is_community in(2,3)) and (d01_code like '6%' or d01_code like '9%')");
            if (StrUtil.equals(rowIndex, "38")) {
                condition = condition.and("d01_code in ('61','911')");
            }
            if (StrUtil.equals(rowIndex, "39")) {
                condition = condition.and("d01_code in ('62','921')");
            }
            if (StrUtil.equals(rowIndex, "40")) {
                condition = condition.and("d01_code like '63%' or d01_code like '931%' or d01_code like '932%' ");
            }
        }
        return condition;
    }

    Condition getPartyRowCondition(String colIndex) {
        Condition condition = noCondition().and("delete_time IS NULL and d108_code like '3%'");
        if ("2".equals(colIndex)) {
            condition = condition.and(field(name("d35_code"), String.class).like("1%"));
        } else if ("3".equals(colIndex)) {
            condition = condition.and("d35_code like '2%' or d35_code like '3%'");
        } else if ("4".equals(colIndex)) {
            condition = condition.and("d35_code like '4%' or d35_code like '5%'");
        } else if ("5".equals(colIndex)) {
            condition = condition.and("d35_code like '7%' or d35_code like '8%' or d35_code like '9%' or d35_code like 'A%'");
        }
        if (StrUtil.equalsAny(colIndex, "2", "3", "4", "5")) {
            condition = condition.and(field(name("d108_code"), String.class).in("31","32", "33", "36", "3A", "3C"));
        }
        return condition;
    }

    Condition getUnitRowCondition(String colIndex, String tableYear) {
        Condition condition = noCondition();
        if (StrUtil.isEmpty(tableYear)) {
            condition = condition.and("d04_code like '91%'");
            if (StrUtil.equals(colIndex, "22")) {
                condition = condition.and("rural_professional_technical_association_num >0");
            } else if (StrUtil.equals(colIndex, "27")) {
                condition = condition.and("farmer_specialized_cooperatives_num >0");
            } else if (StrUtil.equals(colIndex, "32")) {
                condition = condition.and("family_farm_num >0");
            }
        } else {
            if (StrUtil.equals(colIndex, "22")) {
                condition = condition.and(field(name("d04_code"), String.class).eq("513"));
            } else if (StrUtil.equals(colIndex, "27")) {
                condition = condition.and(field(name("d04_code"), String.class).eq("94"));
            } else if (StrUtil.equals(colIndex, "32")) {
                condition = condition.and(field(name("d04_code"), String.class).eq("95"));
            }
        }
        return condition;
    }

    public Map<String, Object> getCheckHtml24(PeggingPara data) {
        Map<String, Object> resultMap = new HashMap<>();
        TableHelper tableHelper = TableHelperFactory.getHelper();
        Page<Map<String, Object>> page = tableHelper.findPage(data.getPageNum(), data.getPageSize(), "ccp_org_all", this.getOrgAllCheckListCondition(data.getOrgCode(), data.getOrgLevelCode(), data.getColIndex(), data.getRowIndex()),
                data.getIncludeFieldList(), data.getOrderByFieldList());
        return ReportResult.toResult(resultMap, page, "ccp_org_all");
    }

    Condition getOrgAllCheckListCondition(String orgCode, String orgLevelCode, String colIndex, String rowIndex) {
        Condition condition = new Html7Count202302().getOrgListCondition(orgCode, orgLevelCode);
        if ("10".equals(rowIndex)) {
            condition = condition.and(" is_state_council = 1 and (d01_code like '6%' or d01_code like '9%')");
        }
        if ("11".equals(rowIndex)) {
            condition = condition.and(" is_state_council = 1 and d01_code in('61','911')");
        }
        if ("12".equals(rowIndex)) {
            condition = condition.and(" is_state_council = 1 and d01_code in('62','921')");
        }
        if ("13".equals(rowIndex)) {
            condition = condition.and(" is_state_council = 1 and d01_code in('63','931','932')");
        }
        return condition;
    }
}
