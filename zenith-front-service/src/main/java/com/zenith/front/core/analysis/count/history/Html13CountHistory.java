package com.zenith.front.core.analysis.count.history;

import cn.hutool.core.util.StrUtil;
import com.b1809.analysis.entity.PeggingPara;
import com.b1809.analysis.extend.ITableCount;
import com.b1809.analysis.plugin.elasticsearch.EsKit;
import com.jfinal.plugin.activerecord.Record;
import com.zenith.front.core.analysis.count.CountMethod;
import com.zenith.front.core.analysis.ext.condition.MemAllCondition;
import org.jooq.Condition;
import org.jooq.Record1;
import org.jooq.SelectHavingStep;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

import static com.zenith.front.common.constant.DBConstant.DSL_CONTEXT;
import static org.jooq.impl.DSL.*;

/**
 * 党员受党的纪律处分情况（二）
 *
 * <AUTHOR>
 * @date 2023/1/3
 */
@Component
public class Html13CountHistory implements ITableCount {


    @Override
    public String getReportCode() {
        return "History_13.html";
    }

    @Override
    public Map<String, Map<String, Number>> doCount(String reportCode, String orgCode, String orgLevelCode) throws Exception {
        return getCount(orgCode, orgLevelCode, null);
    }

    public Map<String, Map<String, Number>> getCount(String orgCode, String orgLevelCode, String tableYear) throws Exception {
        String ccpMemAll = StrUtil.isEmpty(tableYear) ? "ccp_mem_all" : "ccp_mem_all_" + tableYear;
        Map<String, Number> result = new LinkedHashMap<>();
        Html48CountHistory.initTableCol(1, 8, 1, 6, result);
        Condition condition = new MemAllCondition().create(orgCode, orgLevelCode);
        SelectHavingStep<Record1<Object>> records = DSL_CONTEXT.select(field("d030_code,d029_code,count(1) as total")).from(table(name(ccpMemAll)).as("ccp_mem_all"))
                .where(getMemTotalCondition(tableYear).and(condition)).groupBy(field("d030_code,d029_code"));
        List<Record> records1 = EsKit.findBySql(records.toString()).toRecord();
        records1.forEach(record -> setStatisticsValue(record, result, "1"));

        SelectHavingStep<Record1<Object>> records2 = DSL_CONTEXT.select(field("d030_code,d029_code,count(1) as total")).from(table(name(ccpMemAll)).as("ccp_mem_all"))
                .where(getMemListCondition().and(condition)).groupBy(field("d030_code,d029_code"));
        List<Record> records2345 = EsKit.findBySql(records2.toString()).toRecord();
        records2345.forEach(record -> setStatisticsValue(record, result, "2"));

        SelectHavingStep<Record1<Object>> records3 = DSL_CONTEXT.select(field("d030_code,d029_code,count(1) as total")).from(table(name(ccpMemAll)).as("ccp_mem_all"))
                .where(getMemOutPartyCondition(tableYear).and(condition)).groupBy(field("d030_code,d029_code"));
        List<Record> records6 = EsKit.findBySql(records3.toString()).toRecord();
        records6.forEach(record -> setStatisticsValue(record, result, "3"));

        Map<String, Map<String, Number>> mapMap = new HashMap<>();
        mapMap.put("table0", result);
        return mapMap;
    }

    @Override
    public Map<String, Object> doPegging(PeggingPara peggingPara) throws Exception {
        MemAllCondition memAllCond = new MemAllCondition();
        Condition condition = memAllCond.create(peggingPara.getOrgCode(), peggingPara.getOrgLevelCode())
                .and(this.getColCondition(peggingPara.getColIndex(), null))
                .and(this.getRowCondition(peggingPara.getRowIndex()));
        return Html48CountHistory.getReportPageResult(peggingPara, memAllCond.getTableName(), condition, memAllCond.getLevelCodeField());
    }

    public Condition getMemTotalCondition(String tableYear) {
        tableYear = CountMethod.getTableYearByIndex(tableYear);
        //d08_code in('1','2') and ((delete_time is null and d029_code in ('C11','C12','C13','C14')) or (delete_time is not null and d029_code ='C15' and leave_org_date between 1640966400000 and 1672416000000))
        return noCondition().and("d08_code in('1','2') and ((delete_time is null and d029_code in ('C11','C12','C13','C14')) or " +
                "(delete_time is not null and d029_code ='C15' and " + CountMethod.getEsReportDateSql("leave_org_date", tableYear) + "))");
    }

    public Condition getMemListCondition() {
        return noCondition().and("delete_time is null and d029_code like 'C%' and d08_code in('1','2')");
    }

    public Condition getMemOutPartyCondition(String tableYear) {
        tableYear = CountMethod.getTableYearByIndex(tableYear);
        //delete_time is not null and d029_code like 'C15' and d08_code in('1','2') and leave_org_date between 1640966400000 and 1672416000000
        return noCondition().and("delete_time is not null and d029_code like 'C15' and d08_code in('1','2')").and(CountMethod.getEsReportDateSql("leave_org_date", tableYear));
    }


    public static void setStatisticsValue(Record record, Map<String, Number> result, String type) {
        String d030Code = record.getStr("d030_code");
        if (StrUtil.equals(d030Code, "21") || StrUtil.contains(d030Code, "21,")) {
            setCellValue(record, "1", result, type);
        }
        if (StrUtil.contains(d030Code, "22")) {
            setCellValue(record, "2", result, type);
        }
        if (StrUtil.contains(d030Code, "221")) {
            setCellValue(record, "3", result, type);
        }
        if (StrUtil.contains(d030Code, "23")) {
            setCellValue(record, "4", result, type);
        }
        if (StrUtil.contains(d030Code, "24")) {
            setCellValue(record, "5", result, type);
        }
        if (StrUtil.contains(d030Code, "25")) {
            setCellValue(record, "6", result, type);
        }
        if (StrUtil.contains(d030Code, "26")) {
            setCellValue(record, "7", result, type);
        }
        if (StrUtil.contains(d030Code, "27")) {
            setCellValue(record, "8", result, type);
        }
    }

    public static void setCellValue(Record record, String row, Map<String, Number> result, String type) {
        Integer total = record.getInt("total");
        String d029Code = record.getStr("d029_code");
        // 总数行
        if (StrUtil.equals(type, "1")) {
            Html48CountHistory.setTableMapValue(row, "1", total, result);
        }
        if (StrUtil.equals(type, "2")) {
            if (StrUtil.equals(d029Code, "C11")) {
                Html48CountHistory.setTableMapValue(row, "2", total, result);
            } else if (StrUtil.equals(d029Code, "C12")) {
                Html48CountHistory.setTableMapValue(row, "3", total, result);
            } else if (StrUtil.equals(d029Code, "C13")) {
                Html48CountHistory.setTableMapValue(row, "4", total, result);
            } else if (StrUtil.equals(d029Code, "C14")) {
                Html48CountHistory.setTableMapValue(row, "5", total, result);
            }
        }
        // 开除党籍
        if (StrUtil.equals(type, "3")) {
            if (StrUtil.equals(d029Code, "C15")) {
                Html48CountHistory.setTableMapValue(row, "6", total, result);
            }
        }
    }


    /**
     * 反查条件
     *
     * @param colIndex 序号
     * @return 查询条件
     */
    public Condition getColCondition(String colIndex, String tableYear) {
        Condition condition = noCondition();
        if (StrUtil.equals(colIndex, "1")) {
            return getMemTotalCondition(tableYear);
        }
        if (StrUtil.equalsAny(colIndex, "2", "3", "4", "5")) {
            condition = condition.and(getMemListCondition());
            if (StrUtil.equals(colIndex, "2")) {
                condition = condition.and("d029_code = 'C11'");
            } else if (StrUtil.equals(colIndex, "3")) {
                condition = condition.and("d029_code = 'C12'");
            } else if (StrUtil.equals(colIndex, "4")) {
                condition = condition.and("d029_code = 'C13'");
            } else if (StrUtil.equals(colIndex, "5")) {
                condition = condition.and("d029_code = 'C14'");
            }
        }
        if (StrUtil.equals(colIndex, "6")) {
            return getMemOutPartyCondition(tableYear);
        }
        return condition;
    }

    /**
     * 反查条件
     *
     * @param rowIndex 序号
     * @return 查询条件
     */
    public Condition getRowCondition(String rowIndex) {
        Condition condition = noCondition();
        if ("1".equals(rowIndex)) {
            condition = condition.and("d030_code='21' or d030_code like '%21,%' or d030_code like '%,21%'");
        } else if ("2".equals(rowIndex)) {
            condition = condition.and("d030_code like '%22%'");
        } else if ("3".equals(rowIndex)) {
            condition = condition.and("d030_code like '%221%'");
        } else if ("4".equals(rowIndex)) {
            condition = condition.and("d030_code like '%23%'");
        } else if ("5".equals(rowIndex)) {
            condition = condition.and("d030_code like '%24%'");
        } else if ("6".equals(rowIndex)) {
            condition = condition.and("d030_code like '%25%'");
        } else if ("7".equals(rowIndex)) {
            condition = condition.and("d030_code like '%26%'");
        } else if ("8".equals(rowIndex)) {
            condition = condition.and("d030_code like '%27%'");
        }
        return condition;
    }

    @Override
    public Map<String, Number> doReplenishCount(String reportCode, String orgCode, String orgLevelCode) throws Exception {
        return null;
    }

    @Override
    public Map<String, Object> doReplenishPegging(PeggingPara peggingPara) throws Exception {
        return null;
    }

}
