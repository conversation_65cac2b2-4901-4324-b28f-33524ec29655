package com.zenith.front.core.service.org;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.zenith.front.api.org.IOrgRecognitionAllService;
import com.zenith.front.dao.mapper.org.OrgRecognitionAllMapper;
import com.zenith.front.model.bean.OrgRecognitionAll;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <p>
 * 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-11-04
 */
@Service
public class OrgRecognitionAllServiceImpl extends ServiceImpl<OrgRecognitionAllMapper, OrgRecognitionAll> implements IOrgRecognitionAllService {

    @Override
    public List<OrgRecognitionAll> findOrgRecognitionAllByOrgCode(String orgCode) {
        return list(
                new LambdaQueryWrapper<OrgRecognitionAll>()
                        .eq(OrgRecognitionAll::getOrgCode, orgCode)
                        .isNull(OrgRecognitionAll::getDeleteTime)
                        .orderByDesc(OrgRecognitionAll::getAnnual)
        );
    }
}
