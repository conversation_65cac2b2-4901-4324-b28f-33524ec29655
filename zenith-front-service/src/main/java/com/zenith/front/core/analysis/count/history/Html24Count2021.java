package com.zenith.front.core.analysis.count.history;

import cn.hutool.core.util.StrUtil;
import com.b1809.analysis.entity.PeggingPara;
import com.b1809.analysis.extend.ITableCount;
import com.b1809.analysis.result.ReportResult;
import com.b1809.analysis.table.helper.TableHelper;
import com.b1809.analysis.table.helper.factory.TableHelperFactory;
import com.jfinal.plugin.activerecord.Page;
import com.zenith.front.core.analysis.ext.condition.year2021.OrgAllCondition2021;
import com.zenith.front.core.analysis.ext.condition.year2021.OrgPartyCondition2021;
import com.zenith.front.core.analysis.ext.condition.year2021.UnitAllCondition2021;
import org.jooq.Condition;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;

import static org.jooq.impl.DSL.noCondition;

/**
 * 第二十四表 党的基层组织数量情况和换届情况
 *
 * <AUTHOR>
 * @date 2021/01/10
 */
@Component
public class Html24Count2021 extends Html24CountHistory implements ITableCount {

    private static final String TABLE_YEAR = "2021";

    @Override
    public String getReportCode() {
        return "2021_24.html";
    }

    @Override
    public Map<String, Map<String, Number>> doCount(String reportCode, String orgCode, String orgLevelCode) throws Exception {
        return null;
    }

    @Override
    public Map<String, Object> doPegging(PeggingPara peggingPara) throws Exception {
        return null;
    }

    @Override
    public Map<String, Number> doReplenishCount(String reportCode, String orgCode, String orgLevelCode) throws Exception {
        return getReplenishCount(orgCode, orgLevelCode, TABLE_YEAR);
    }


    @Override
    public Map<String, Object> doReplenishPegging(PeggingPara peggingPara) {
        String colIndex = peggingPara.getRowIndex();
        if (StrUtil.equalsAny(colIndex, "1", "2", "3", "4", "5")) {
//            UnitAll2021Condition unitAllCondition = new UnitAll2021Condition();
//            Condition condition = noCondition().and(new Html6Count().getUnitListCondition(peggingPara.getOrgCode(), peggingPara.getOrgLevelCode()).toString().replace("ccp_unit_all", unitAllCondition.getTableName()))
//                    .and(field(name("has_party")).eq(1))
//                    .and(this.getPartyRowCondition(peggingPara.getRowIndex()));
//            return Html48Count.getReportPageResult(peggingPara, unitAllCondition.getTableName(), condition, unitAllCondition.getLevelCodeField());
            OrgPartyCondition2021 party2021Condition = new OrgPartyCondition2021();
            Condition condition = party2021Condition.create(peggingPara.getOrgCode(), peggingPara.getOrgLevelCode()).and(this.getPartyRowCondition(peggingPara.getRowIndex()));
            return Html48CountHistory.getReportPageResult(peggingPara, party2021Condition.getTableName(), condition, party2021Condition.getLevelCodeField());
        } else if (StrUtil.equalsAny(colIndex, "18", "23", "28")) {
            UnitAllCondition2021 unitAllCondition = new UnitAllCondition2021();
            Condition condition = this.getUnitRowCondition(colIndex, TABLE_YEAR).and(new Html6CountHistory().getUnitListCondition(peggingPara.getOrgCode(), peggingPara.getOrgLevelCode()).toString().replace("ccp_unit_all", unitAllCondition.getTableName()));
            return Html48CountHistory.getReportPageResult(peggingPara, unitAllCondition.getTableName(), condition, unitAllCondition.getLevelCodeField());
        } else {
            OrgAllCondition2021 orgAllCondition = new OrgAllCondition2021();
            Condition condition = noCondition().and(this.getOrgRowCondition(peggingPara.getRowIndex(), peggingPara.getOrgCode(), peggingPara.getOrgLevelCode(), TABLE_YEAR).toString().replace("ccp_org_all", orgAllCondition.getTableName()));
            return Html48CountHistory.getReportPageResult(peggingPara, orgAllCondition.getTableName(), condition, orgAllCondition.getLevelCodeField());
        }
    }


    public Map<String, Object> getCheckHtml24_2021(PeggingPara data) {
        Map<String, Object> resultMap = new HashMap<>();
        TableHelper tableHelper = TableHelperFactory.getHelper();
        OrgAllCondition2021 orgAllCondition = new OrgAllCondition2021();
        Condition condition = noCondition().and(this.getOrgAllCheckListCondition(data.getOrgCode(), data.getOrgLevelCode(), data.getColIndex(), data.getRowIndex()).toString().replace("ccp_org_all", orgAllCondition.getTableName()));
        Page<Map<String, Object>> page = tableHelper.findPage(data.getPageNum(), data.getPageSize(), orgAllCondition.getTableName(),
                condition, data.getIncludeFieldList(), data.getOrderByFieldList());
        return ReportResult.toResult(resultMap, page, orgAllCondition.getTableName());
    }

}
