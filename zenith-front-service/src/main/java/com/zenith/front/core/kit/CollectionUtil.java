package com.zenith.front.core.kit;

import cn.hutool.core.util.StrUtil;
import com.zenith.front.model.custom.Record;
import com.zenith.front.model.dto.ExportDTO;

import java.util.*;

/**
 * <AUTHOR>
 * @version 1.0
 * @Description: 集合
 * @date 2019/3/21 10:24
 */
public class CollectionUtil {

    /**
     * list<Record>转map
     * 请尽量使用自定义key,value的方法
     *
     * @param collection
     * @return
     */
    @Deprecated
    public static Map<String, String> listRecordToMap(List<Record> collection) {

        Map<String, String> map = new HashMap<>(collection.size());

        collection.forEach(record -> map.put(record.getStr("key"), record.getStr("name")));
        return map;
    }

    /**
     * list<Record>转map
     *
     * @param collection
     * @param key
     * @param value
     * @return
     */
    public static Map<String, String> listRecordToMap(List<Record> collection, String key, String value) {
        Map<String, String> map = new HashMap<>(collection.size());
        collection.forEach(record -> map.put(record.getStr(key), record.getStr(value)));
        return map;
    }

    /**
     * map 转 list<Record>
     *
     * @param map
     * @return
     */
    public static List<Record> mapToListRecord(Map<String, Object> map, String... values) {
        List<Record> list = new ArrayList<>(map.size());
        Record record;
        Set<Map.Entry<String, Object>> entrySet = map.entrySet();
        for (Map.Entry<String, Object> entry : entrySet) {
            record = new Record();
            record.set(values[0], entry.getKey());
            record.set(values[1], entry.getValue() != null ? entry.getValue() : 0);
            list.add(record);
        }
        return list;
    }

    /**
     * map 转 list<String>
     *
     * @param map
     * @param isKey true 转换key,false 转换value
     * @return
     */
    public static List<String> mapToListString(Map<String, String> map, boolean isKey) {
        List<String> list;
        if (isKey) {
            list = new ArrayList<>(map.keySet());
        } else {
            list = new ArrayList<>(map.values());
        }
        return list;
    }

    /**
     * map 转 list<String>
     *
     * @param list
     * @return
     */
    public static List<String> listToListString(List<ExportDTO> list) {
        List<String> stringList = new ArrayList<>(list.size());
        list.forEach(exportDTO -> stringList.add(exportDTO.getAnnotation()));
        return stringList;
    }

    /**
     * 拼接ids
     *
     * @param ids
     * @return
     */
    public static String getString(List<String> ids) {
        StringBuilder s = new StringBuilder();
        int i = 0;
        for (String id : ids) {
            if (StrUtil.isNotEmpty(id)) {
                if (i == 0) {
                    s.append("'").append(id).append("'");
                } else {
                    s.append("," + "'").append(id).append("'");
                }
                i++;
            }
        }
        return s.toString();
    }

    /**
     * List分页
     */
    public static List<List<String>> subList(Integer pageSize, List<String> list) {
        List<List<String>> result = new ArrayList<>();
        int subSize = pageSize;
        int subCount = list.size();
        int subPageTotal = (subCount / subSize) + ((subCount % subSize > 0) ? 1 : 0);
        // 根据页码取数据
        for (int i = 0, len = subPageTotal - 1; i <= len; i++) {
            // 分页计算
            int fromIndex = i * subSize;
            int toIndex = ((i == len) ? subCount : ((i + 1) * subSize));
            List<String> strings = list.subList(fromIndex, toIndex);
            result.add(strings);
        }
        return result;
    }
}
