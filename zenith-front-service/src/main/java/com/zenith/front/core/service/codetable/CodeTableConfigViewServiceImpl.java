package com.zenith.front.core.service.codetable;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.zenith.front.api.codetable.CodeTableConfigViewService;
import com.zenith.front.common.constant.LogicCompareType;
import com.zenith.front.core.constant.LogicClassEnum;
import com.zenith.front.core.kit.DbUtil;
import com.zenith.front.dao.mapper.codetable.CodeTableConfigViewMapper;
import com.zenith.front.model.bean.*;
import com.zenith.front.model.modelview.CodeTableConfigView;
import com.zenith.front.model.vo.DecryptMap;
import org.jooq.Condition;
import org.jooq.Record;
import org.jooq.Record1;
import org.jooq.SelectConditionStep;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.CompletableFuture;

import static org.jooq.impl.DSL.*;

/**
 * <AUTHOR>
 */
@Service
public class CodeTableConfigViewServiceImpl extends ServiceImpl<CodeTableConfigViewMapper, CodeTableConfigView> implements CodeTableConfigViewService {

    @Resource
    private CodeTableConfigViewMapper codeTableConfigViewMapper;

    @Override
    public List<CodeTableConfigView> amount(String orgCode, Set<String> ignoreCode) {
        List<CodeTableConfigView> configViewList = codeTableConfigViewMapper.selectList(Wrappers.emptyWrapper());
        // 多线程分组处理
        List<CompletableFuture<Object>> supplierList = new ArrayList<>();
        int k = 0;
        final int size = configViewList.size();
        for (CodeTableConfigView configView : configViewList) {
            // 跳过单位扩展信息校核
            if(StrUtil.equalsIgnoreCase(UnitExtend.class.getAnnotation(TableName.class).value(), configView.getTableCode())){
                continue;
            }
            k++;
            supplierList.add(CompletableFuture.supplyAsync(
                    () -> {
                        LogicClassEnum logicClassEnum = LogicClassEnum.matching(configView.getTableCode());
                            String filedName = getFiledName(logicClassEnum);
                            String tableName = getTableName(logicClassEnum);
                            Condition condition =noCondition();
                            if (StrUtil.isNotEmpty(filedName)){
                                     condition = getCondition(filedName, orgCode, configView);
                            }
                            condition = condition.and(extraCondition(logicClassEnum));
                            //处理逻辑校核被忽略部分
                            condition = ignoreCondition(configView.getId(), orgCode, ignoreCode, condition);
                            SelectConditionStep<Record1<Integer>> step = DbUtil.DSL_CONTEXT.selectCount()
                                    .from(name(tableName))
                                    .where(condition);
//                        System.out.println(tableName+"====>"+step.toString());
                            int count = codeTableConfigViewMapper.selectCheckCount(step.toString());
                            configView.setFail(count);
                        return null;
                    }));
            if (supplierList.size() % 10 == 0 || k == size) {
                CompletableFuture.allOf(supplierList.toArray(new CompletableFuture[0])).join();
                supplierList.clear();
            }
        }
        return configViewList;
    }

    private String getTableName(LogicClassEnum logicClassEnum) {
        String value = "";
            if (logicClassEnum.name().equals(MemAllInfo.class.getSimpleName())) {
                value = MemAllInfo.class.getAnnotation(TableName.class).value();
            } else if (logicClassEnum.name().equals(OrgAll.class.getSimpleName())) {
                value = OrgAll.class.getAnnotation(TableName.class).value();
            } else if (logicClassEnum.name().equals(UnitAll.class.getSimpleName())) {
                value = UnitAll.class.getAnnotation(TableName.class).value();
            } else if (logicClassEnum.name().equals(MemDevelopAll.class.getSimpleName())) {
                value = MemDevelopAll.class.getAnnotation(TableName.class).value();
            } else if (logicClassEnum.name().equals(DevelopStepLogAll.class.getSimpleName())) {
                value = DevelopStepLogAll.class.getAnnotation(TableName.class).value();
            }  else if (logicClassEnum.name().equals(UnitExtend.class.getSimpleName())) {
                value = UnitExtend.class.getAnnotation(TableName.class).value();
            } else {

            }
        return value;
    }

    private String getFiledName(LogicClassEnum logicClassEnum) {
        String value = "";
        if(ObjectUtil.isNotNull(logicClassEnum)){
            if (logicClassEnum.name().equals(MemAllInfo.class.getSimpleName())) {
                value = "mem_org_code";
            } else if (logicClassEnum.name().equals(OrgAll.class.getSimpleName())) {
                value = "org_code";
            } else if (logicClassEnum.name().equals(UnitAll.class.getSimpleName())) {
                value = "create_unit_org_code";
            } else if (logicClassEnum.name().equals(MemDevelopAll.class.getSimpleName())) {
                value = "develop_org_code";
            } else if (logicClassEnum.name().equals(DevelopStepLogAll.class.getSimpleName())) {
                value = "log_org_code";
            }  else if (logicClassEnum.name().equals(UnitExtend.class.getSimpleName())) {

            } else {

            }
        }
        return value;
    }

    private Condition extraCondition(LogicClassEnum logicClassEnum) {
            if (logicClassEnum.name().equals(MemAllInfo.class.getSimpleName())) {
                return field(name("d08_code")).in(Arrays.asList("1", "2"))
                        .and(
                                or(
                                        //转接问题
                                        field(name("is_transfer")).notEqual(1))
                                        .or(field(name("is_transfer")).isNull())
                        );
            } else if (logicClassEnum.name().equals(MemDevelopAll.class.getSimpleName())) {
                return field(name("d08_code")).in(Arrays.asList("3", "4", "5"));
            } else {
                return noCondition();
            }
    }

    @Override
    public Page<DecryptMap<String, Object>> detail(Integer pageNum, Integer pageSize, String orgCode, String id, Set<String> ignoreCode) {
        CodeTableConfigView configView = codeTableConfigViewMapper.selectById(id);
        LogicClassEnum logicClassEnum = LogicClassEnum.matching(configView.getTableCode());
        String filedName = getFiledName(logicClassEnum);
        Condition condition = getCondition(filedName, orgCode, configView);
        String tableName = getTableName(logicClassEnum);
        condition = condition.and(extraCondition(logicClassEnum));
        condition = ignoreCondition(configView.getId(), orgCode, ignoreCode, condition);
        SelectConditionStep<Record> where = DbUtil.DSL_CONTEXT.select()
                .from(name(tableName))
                .where(condition);
        Page page = new Page(pageNum, pageSize);
        Page<DecryptMap<String, Object>> mapPage = codeTableConfigViewMapper.pageMap(page, where.toString());
        List<DecryptMap<String, Object>> records = mapPage.getRecords();
        if (CollUtil.isNotEmpty(records)) {
            List<DecryptMap<String, Object>> list = new ArrayList<>();
            //下划线转驼峰
            records.stream().map(MapUtil::toCamelCaseMap).forEachOrdered(toCamelCaseMap -> {
                DecryptMap<String, Object> decryptMap = new DecryptMap<>();
                decryptMap.putAll(toCamelCaseMap);
                list.add(decryptMap);
            });
            mapPage.setRecords(list);
        }
        return mapPage;
    }

    private Condition ignoreCondition(String key, String orgCode, Set<String> ignoreCode, Condition condition) {
        if (ignoreCode.contains(key)) {
            condition = condition.and(
                    field(name("code")).notIn(
                            select(field(name("code"))).from("ccp_logic_check_ignore")
                                    .where(
                                            field(name("logic_check_code")).eq(key)
                                                    .and(field(name("delete_time")).isNull())
                                                    .and(field(name("org_code"), String.class).like(orgCode + "%"))
                                    )
                    )
            );
        }
        return condition;
    }

    /**
     * 基础sql条件
     *
     * @param filedName
     * @param orgCode
     * @param configView
     * @return
     */
    private Condition getCondition(String filedName, String orgCode, CodeTableConfigView configView) {
        LogicCompareType logicCompareType = Objects.isNull(configView.getCompareType()) ? null : LogicCompareType.matching(configView.getCompareType());
        String[] compareValueArray = configView.getCompareValue().substring(1, configView.getCompareValue().length() - 1).trim().replaceAll(" ", "").split(",");
        Condition condition = noCondition();
        // TODO: 2022/5/12 处理逻辑配置规则有些选择了上级，现在采取like形式

        String compareIdCol = StrUtil.toUnderlineCase(configView.getFiled());
        // 是否国民经济字段
        boolean compareIdColFlag = StrUtil.equalsAnyIgnoreCase(compareIdCol, "d194_code", "d195_code");
        if(StrUtil.isNotEmpty(filedName)){
            // todo: 2023-12-25 国民经济字段直接用in 或者not in
            if(compareIdColFlag){
                if(Objects.nonNull(logicCompareType) && logicCompareType == LogicCompareType.NOT_EQUAL){
                    condition = field(name(StrUtil.toUnderlineCase(configView.getFiled()))).in(compareValueArray);
                } else {
                    condition = field(name(StrUtil.toUnderlineCase(configView.getFiled()))).notIn(compareValueArray);
                }
            } else {

                for (String compareValue : compareValueArray) {
                    // todo: 兼容 compare_type 为 notEqual 情况
                    if(Objects.nonNull(logicCompareType) && logicCompareType == LogicCompareType.NOT_EQUAL){
                        condition = condition.or(field(name(StrUtil.toUnderlineCase(configView.getFiled()))).like(compareValue + "%"));
                    } else {
                        condition = condition.and(field(name(StrUtil.toUnderlineCase(configView.getFiled()))).notLike(compareValue + "%"));
                    }
                }
            }

            return field(name(StrUtil.toUnderlineCase(configView.getColCode()))).eq(configView.getColValue())
                    .and(field(name(filedName)).like(orgCode + "%"))
                    .and(
                            or(
                                    condition
                            )
                                    .or(field(name(StrUtil.toUnderlineCase(configView.getFiled()))).isNull())
                    )
                    .and(field(name("delete_time")).isNull());
        }else {
            return field(name(StrUtil.toUnderlineCase(configView.getColCode()))).eq(configView.getColValue())
                    .and(
                            or(
                                    condition
                            )
                                    .or(field(name(StrUtil.toUnderlineCase(configView.getFiled()))).isNull())
                    )
                    .and(field(name("delete_time")).isNull());
        }
    }
}




