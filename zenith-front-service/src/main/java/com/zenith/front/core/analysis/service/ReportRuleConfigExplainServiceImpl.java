package com.zenith.front.core.analysis.service;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.StrUtil;
import com.b1809.analysis.entity.PeggingPara;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.zenith.front.common.kit.JackSonUtil;
import com.zenith.front.dao.mapper.report.ReportRuleConfigExplainMapper;
import com.zenith.front.model.bean.ReportRuleConfigExplain;
import com.zenith.front.model.dto.PeggingParaDTO;
import com.zenith.front.model.message.OutMessage;
import com.zenith.front.model.message.Status;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 报表单元格解释信息
 *
 * <AUTHOR>
 * @create_date 2025-02-07 14:32
 * @description
 */
@Service
public class ReportRuleConfigExplainServiceImpl {
    @Resource
    private ReportRuleConfigExplainMapper reportRuleConfigExplainMapper;

    public Map<String, Map<String, String>> getReportExplainMap(String reportCode) {
        Map<String, Map<String, String>> map = new HashMap<>();
        LambdaQueryWrapper<ReportRuleConfigExplain> wrapper = Wrappers.lambdaQuery();
        wrapper.eq(ReportRuleConfigExplain::getReportCode, reportCode);
        List<ReportRuleConfigExplain> list = reportRuleConfigExplainMapper.selectList(wrapper);
        if (CollUtil.isEmpty(list)) {
            return new HashMap<>();
        }
        // 基础统计项跟补充资料统计项
        list.stream().collect(Collectors.groupingBy(ReportRuleConfigExplain::getTableType)).forEach(
                (k, v) -> map.put(k, v.stream().collect(Collectors.toMap(ReportRuleConfigExplain::getCell,
                        e -> StrUtil.isNotBlank(e.getExplain()) && e.getExplain().length() > 5 ? e.getExplain().substring(0, 2) + "..." : e.getExplain())))
        );
        return map;
    }

    /**
     * 表单元格解释编辑
     *
     * @param data
     * @return
     */
    public OutMessage<?> saveReportExplain(PeggingParaDTO data) {
        // 这里代表单元格数据解释
        final String explain = data.getOrgCode();
        // 1--普通表，2--补充资料
        String type = data.getType();
        // 行
        String rowIndex = data.getRowIndex();
        // 列
        String colIndex = data.getColIndex();
        // 报表code
        String reportCode = data.getReportCode();
        // 单元格所属表类型  1--普通表，2--补充资料
        String tableType = Objects.equals(type, "1") ? "table0" : "replenish";
        // 普通表例子：cell_26_1  补充资料例子：cell_1
        String cell = "cell_" + rowIndex + (Objects.equals(tableType, "table0") ? "_" + colIndex : "");
        //表名  类似于ccp_mem_all
        String reportType = data.getReportType();
        //这里代表  反查显示列配置
        List<Object> includeFieldList = data.getFieldList();
        LambdaQueryWrapper<ReportRuleConfigExplain> wrapper = Wrappers.lambdaQuery();
        wrapper.eq(ReportRuleConfigExplain::getTableType, tableType)
                .eq(ReportRuleConfigExplain::getReportCode, reportCode)
                .eq(ReportRuleConfigExplain::getCell, cell);
        ReportRuleConfigExplain entity = Optional.ofNullable(reportRuleConfigExplainMapper.selectOne(wrapper))
                .orElse(new ReportRuleConfigExplain());
        entity.setExplain(explain);
        entity.setIncludeFieldList(JackSonUtil.objectToString(includeFieldList));
        entity.setReportType(reportType);
        if (StrUtil.isBlank(entity.getId())) {
            // 新增
            entity.setId(IdUtil.simpleUUID());
            entity.setReportCode(reportCode);
            entity.setCell(cell);
            entity.setCreateTime(new Date());
            entity.setTableType(tableType);
            reportRuleConfigExplainMapper.insert(entity);
        } else {
            // 修改
            entity.setUpdateTime(new Date());
            reportRuleConfigExplainMapper.updateById(entity);
        }
        return new OutMessage<>(Status.SUCCESS);
    }

    /**
     * 获取单个表单元格解释
     *
     * @param data
     * @return
     */
    public ReportRuleConfigExplain findReportExplain(PeggingParaDTO data) {
        // 1--普通表，2--补充资料
        final String type = data.getType();
        // 行
        final String rowIndex = data.getRowIndex();
        // 列
        final String colIndex = data.getColIndex();
        // 报表code
        final String reportCode = data.getReportCode();
        // 单元格所属表类型  1--普通表，2--补充资料
        final String tableType = Objects.equals(type, "1") ? "table0" : "replenish";
        // 普通表例子：cell_26_1  补充资料例子：cell_1
        final String cell = "cell_" + rowIndex + (Objects.equals(tableType, "table0") ? "_" + colIndex : "");

        LambdaQueryWrapper<ReportRuleConfigExplain> wrapper = Wrappers.lambdaQuery();
        wrapper.eq(ReportRuleConfigExplain::getTableType, tableType)
                .eq(ReportRuleConfigExplain::getReportCode, reportCode)
                .eq(ReportRuleConfigExplain::getCell, cell);
        return Optional.ofNullable(reportRuleConfigExplainMapper.selectOne(wrapper))
                .orElse(new ReportRuleConfigExplain());
    }

}
