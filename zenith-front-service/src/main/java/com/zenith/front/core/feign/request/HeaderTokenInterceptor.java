package com.zenith.front.core.feign.request;

import feign.RequestInterceptor;
import feign.RequestTemplate;
import lombok.extern.slf4j.Slf4j;

/**
 * Feign 标识传递
 *
 * <AUTHOR>
 * @version V1.0
 * @date 2022/8/19 10:01
 */
@Slf4j
public class HeaderTokenInterceptor implements RequestInterceptor {

    @Override
    public void apply(RequestTemplate requestTemplate) {
        requestTemplate.header("feign", "t");
    }
}
