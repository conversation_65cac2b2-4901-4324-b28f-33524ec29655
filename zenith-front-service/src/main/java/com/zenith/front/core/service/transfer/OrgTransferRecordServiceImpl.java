package com.zenith.front.core.service.transfer;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.io.FileUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.extra.pinyin.PinyinUtil;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.conditions.update.LambdaUpdateChainWrapper;
import com.zenith.front.api.democraticreview.IDemocraticReviewMemService;
import com.zenith.front.api.develop.IDevelopStepLogAllService;
import com.zenith.front.api.develop.IDevelopStepLogService;
import com.zenith.front.api.mem.*;
import com.zenith.front.api.org.*;
import com.zenith.front.api.transfer.IOrgTransferRecordService;
import com.zenith.front.api.unit.*;
import com.zenith.front.common.constant.CommonConstant;
import com.zenith.front.common.constant.RetConstant;
import com.zenith.front.common.constant.TransferLogConstant;
import com.zenith.front.common.constant.TransferRecordConstant;
import com.zenith.front.common.encrypt.utli.ReflectionUtil;
import com.zenith.front.common.kit.CodeUtil;
import com.zenith.front.common.kit.HttpKit;
import com.zenith.front.common.kit.Ret;
import com.zenith.front.common.kit.StrKit;
import com.zenith.front.common.mybatisplus.WrapperUtil;
import com.zenith.front.model.bean.*;
import com.zenith.front.model.constant.UserConstant;
import com.zenith.front.core.kit.ExcelImportUtil;
import com.zenith.front.model.custom.Record;
import com.zenith.front.model.custom.UserTicket;
import com.zenith.front.dao.mapper.democraticreview.DemocraticReviewMemMapper;
import com.zenith.front.dao.mapper.develop.DevelopStepLogMapper;
import com.zenith.front.dao.mapper.mem.*;
import com.zenith.front.dao.mapper.org.*;
import com.zenith.front.dao.mapper.unit.*;
import com.zenith.front.dao.mapper.user.UserMapper;
import com.zenith.front.dao.mapper.user.UserRolePermissionMapper;
import com.zenith.front.model.dto.MemExportDTO;
import com.zenith.front.model.dto.TransferRecordDTO;
import com.zenith.front.model.dto.TransferRecordUploadDTO;
import com.zenith.front.model.message.OutMessage;
import com.zenith.front.model.message.Status;

import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.util.ResourceUtils;

import javax.annotation.Resource;
import java.io.File;
import java.io.FileInputStream;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <p>
 * 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-07-10
 */
@Service("orgTransferRecordService")
public class OrgTransferRecordServiceImpl extends TransferRecordServiceImpl implements IOrgTransferRecordService {


    @Value("${sync_flow_push}")
    private  String sync_flow_push;
    @Resource
    private UnitMapper unitMapper;
    @Resource
    private UnitCitySituationMapper unitCitySituationMapper;
    @Resource
    private OrgMapper orgMapper;
    @Resource
    OrgAllMapper orgAllMapper;
    @Resource
    private UnitCollectiveEconomicMapper unitCollectiveEconomicMapper;
    @Resource
    private UnitCommitteeElectMapper unitCommitteeElectMapper;
    @Resource
    private UnitCommitteeMapper unitCommitteeMapper;
    @Resource
    private UnitCommunityMapper unitCommunityMapper;
    @Resource
    private UnitCountrysideMapper unitCountrysideMapper;
    @Resource
    private UnitExtendMapper unitExtendMapper;
    @Resource
    private UnitIncomeMapper unitIncomeMapper;
    @Resource
    private UnitOrgLinkedMapper unitOrgLinkedMapper;
    @Resource
    private UnitSecondaryMapper unitSecondaryMapper;
    @Resource
    private  UnitSiteConditionsMapper unitSiteConditionsMapper;
    @Resource
    private  UnitStreetsCadresMapper unitStreetsCadresMapper;
    @Resource
    private OrgAppraisalMapper orgAppraisalMapper;
    @Resource
    private OrgAssessMapper orgAssessMapper;
    @Resource
    private OrgCaucusMapper orgCaucusMapper;
    @Resource
    private OrgCommitteeElectMapper orgCommitteeElectMapper;
    @Resource
    private OrgCommitteeMapper orgCommitteeMapper;
    @Resource
    private  OrgDevelopRightsMapper orgDevelopRightsMapper;
    @Resource
    private  OrgExtendMapper orgExtendMapper;
    @Resource
    private  OrgGroupMapper orgGroupMapper;
    @Resource
    private  OrgGroupMemberMapper orgGroupMemberMapper;
    @Resource
    private  OrgIndustryMapper orgIndustryMapper;
    @Resource
    private  OrgIndustryAllMapper orgIndustryAllMapper;
    @Resource
    private  OrgNonPublicPartyMapper orgNonPublicPartyMapper;
    @Resource
    private  OrgPartyMapper orgPartyMapper;
    @Resource
    private  OrgPartyCongressElectMapper orgPartyCongressElectMapper;
    @Resource
    private  OrgPartyCongressCommitteeMapper orgPartyCongressCommitteeMapper;
    @Resource
    private  OrgRecognitionMapper orgRecognitionMapper;
    @Resource
    private  OrgRecognitionAllMapper orgRecognitionAllMapper;
    @Resource
    private  OrgRecognitionDataMapper orgRecognitionDataMapper;
    @Resource
    private  OrgRecognitionSituationMapper orgRecognitionSituationMapper;
    @Resource
    private  OrgReviewersMapper orgReviewersMapper;
    @Resource
    private  OrgRewardMapper orgRewardMapper;
    @Resource
    private  OrgSlackMapper orgSlackMapper;
    @Resource
    private  OrgSlackAllMapper orgSlackAllMapper;
    @Resource
    private  OrgSlackRectificationMapper orgSlackRectificationMapper;
    @Resource
    private  OrgSpecialNatureMapper orgSpecialNatureMapper;
    @Resource
    private  OrgTownshipLeadershipMapper orgTownshipLeadershipMapper;
    @Resource
    private MemMapper memMapper;
    @Resource
    private MemAllInfoMapper memAllInfoMapper;
    @Resource
    private DemocraticReviewMemMapper democraticReviewMemMapper;
    @Resource
    private MemAbroadMapper memAbroadMapper;
    @Resource
    private MemDevelopMapper memDevelopMapper;
    @Resource
    private MemDevelopOperationMapper memDevelopOperationMapper;
    @Resource
    private MemDifficultMapper memDifficultMapper;
    @Resource
    private MemExtendMapper memExtendMapper;
    @Resource
    private MemFlowMapper memFlowMapper;
    @Resource
    private MemRewardMapper memRewardMapper;
    @Resource
    private MemRewardAllMapper memRewardAllMapper;
    @Resource
    private MemTrainMapper memTrainMapper;
    @Resource
    private UnitAllMapper unitAllMapper;
    @Resource
    private MemDevelopAllMapper memDevelopAllMapper;
    @Value("${exchange_nginx_key}")
    private String exchangeNginxKey;
    @Resource
    private UserMapper userMapper;
    @Resource
    private UserRolePermissionMapper userRolePermissionMapper;
    @Resource
    private DevelopStepLogMapper developStepLogMapper;
    @Resource
    MemFlowAllMapper memFlowAllMapper;
    @Resource
    private IUnitCitySituationService unitCitySituationService;
    @Resource
    private IUnitCommitteeService iUnitCommitteeService;
    @Resource
    private IUnitCommunityService iUnitCommunityService;
    @Resource
    private IUnitCountrusideService iUnitCountrusideService;
    @Resource
    private IUnitExtendService iUnitExtendService;
    @Resource
    private  IUnitOrgLinkedService iUnitOrgLinkedService;
    @Resource
    private  IUnitSecondaryService iUnitSecondaryService;
    @Resource
    private  IUnitSiteConditionsService iUnitSiteConditionsService;
    @Resource
    private  IUnitStreetsCadresService iUnitStreetsCadresService;
    @Resource
    private  IUnitCollectiveEconomicService iUnitCollectiveEconomicService;
    @Resource
    private  IUnitIncomeService iUnitIncomeService;
    @Resource
    private IOrgService iOrgService;
    @Resource
    private IOrgAssessService iOrgAssessService;
    @Resource
    private IOrgCaucusService iOrgCaucusService;
    @Resource
    private IOrgCommitteeElectService iOrgCommitteeElectService;
    @Resource
    private IOrgCommitteeService iOrgCommitteeService;
    @Resource
    private  IOrgDevelopRightsService iOrgDevelopRightsService;
    @Resource
    private  IOrgExtendService iOrgExtendService;
    @Resource
    private  IOrgGroupService iOrgGroupService;
    @Resource
    private  IOrgGroupMemberService iOrgGroupMemberService;
    @Resource
    private  IOrgIndustryService industryService;
    @Resource
    private  IOrgIndustryAllService iOrgIndustryAllService;
    @Resource
    private  IOrgNonPublicPartyService iOrgNonPublicPartyService;
    @Resource
    private  IOrgPartyService iOrgPartyService;
    @Resource
    private  IOrgPartyCongressElectService iOrgPartyCongressElectService;
    @Resource
    private  IOrgPartyCongressCommitteeService iOrgPartyCongressCommitteeService;
    @Resource
    private  IOrgPartyCongressCommitteeAllService iOrgPartyCongressCommitteeAllService;
    @Resource
    private  IOrgRecognitionService iOrgRecognitionService;
    @Resource
    private  IOrgRecognitionAllService iOrgRecognitionAllService;
    @Resource
    private  IOrgRecognitionDataService iOrgRecognitionDataService;
    @Resource
    private  IOrgRecognitionSituationService iOrgRecognitionSituationService;
    @Resource
    private  IOrgAppraisalService iOrgAppraisalService;
    @Resource
    private  IOrgReviewersService iOrgReviewersService;
    @Resource
    private  IOrgRewardService iOrgRewardService;
    @Resource
    private  IOrgSlackService iOrgSlackService;
    @Resource
    private  IOrgSlackAllService iOrgSlackAllService;
    @Resource
    private  IOrgSpecialNatureService iOrgSpecialNatureService;
    @Resource
    private  IOrgTownshipLeadershipService iOrgTownshipLeadershipService;
    @Resource
    private IMemService iMemService;
    @Resource
    private IMemAllInfoService iMemAllInfoService;
    @Resource
    private IDemocraticReviewMemService iDemocraticReviewMemService;
    @Resource
    private IMemAbroadService iMemAbroadService;
    @Resource
    private IMemDevelopService iMemDevelopService;
    @Resource
    private IMemDevelopAllService iMemDevelopAllService;
    @Resource
    private IMemDevelopOperationService iMemDevelopOperationService;
    @Resource
    private IMemDifficultService iMemDifficultService;
    @Resource
    private  IMemFlowService iMemFlowService;
    @Resource
    private IMemFlowAllService iMemFlowAllService;
    @Resource
    private  IMemRewardService iMemRewardService;
    @Resource
    private  IMemRewardAllService iMemRewardAllService;
    @Resource
    private  IMemTrainService iMemTrainService;

    @Resource
    private  IUnitCommitteeElectService  iUnitCommitteeElectService;
    @Resource
    private IDevelopStepLogService iDevelopStepLogService;
    @Resource
    private IDevelopStepLogAllService iDevelopStepLogAllService;
    @Resource
    private IUnitService iUnitService;
    @Resource
    private IUnitAllService iUnitAllService;
    @Resource
    private IOrgHistoryService iOrgHistoryService;
    @Resource
    private IMemHsitoryService iMemHsitoryService;

    @Resource
    private MemDevelopProcessMapper memDevelopProcessMapper;
    @Resource
    private MemDigitalMapper memDigitalMapper;
    @Resource
    private MemDigitalOperationLogMapper memDigitalOperationLogMapper;
    @Resource
    private MemDigitalCountMapper memDigitalCountMapper;



    @Override
    public OutMessage<String> add(TransferRecordDTO data) {
        UserTicket userTicket = UserConstant.USER_CONTEXT.get();
        // TODO: 2021/11/26  这是不是浩然后面改得省外整建制转入？
//        if(StrUtil.equals(data.getType(),"212")){
//            //整建制人员转入
//            this.addZjZUpload(data);
//            this.addOrgTransferOutData(data, userTicket);
//            return new OutMessage<>(Status.SUCCESS);
//        }
        if (userTicket == null) {
            return new OutMessage<>(Status.NOT_LOGIN);
        }
        //暂时测试代码
        TransferRecord transferRecord = new TransferRecord();
        String srcOrgId = data.getSrcOrgId();
        String targetOrgId = data.getTargetOrgId();
        //获取转接类型
        String type = data.getType();
        //判断转接类型
        boolean isOk = setTransferTypeCode(type, transferRecord);
        if (!isOk) {
            return new OutMessage<>(Status.ORG_TRANSFER_TYPE_ERROR);
        }
        //设置源组织和目标组织当前组织关系
        Ret ret = setSrcAndTargetRelation(srcOrgId, targetOrgId, transferRecord,true);
        if (ret.isFail()) {
            return ret.getAs(RetConstant.MESSAGE);
        }
        //设置源组织和目标组织id
        ret = setSrcAndTargetOrgId(srcOrgId, targetOrgId, transferRecord, true);
        if (ret.isFail()) {
            return ret.getAs(RetConstant.MESSAGE);
        }
        //设置公共节点
        ret = setCommonNodeId(srcOrgId, targetOrgId, transferRecord);
        if (ret.isFail()) {
            return ret.getAs(RetConstant.MESSAGE);
        }
        //判断源组织或源组织上级以及源组织下级及其下级人员是否处于转接中
        if (StrKit.notBlank(transferRecord.getSrcOrgId())) {
            List<TransferRecord> records = srcExistParentsOrgIsTransfer(transferRecord.getSrcOrgId());
            for (TransferRecord record : records) {
                String memId = record.getMemId();
                //应该是状态不为1
                if (StrKit.isBlank(memId)) {
//                if(ObjectUtil.equal(record.getStatus(),0)){
                    return new OutMessage<String>(Status.TRANSFER_SRC_ORG_ERROR_MESSAGE).format(record.getName());
                } else {
                    return new OutMessage<String>(Status.TRANSFER_SRC_MEM_ERROR_MESSAGE).format(record.getName());
                }
            }
        }
        //判断目标组织或组织上级是否在转接中
        if (StrKit.notBlank(transferRecord.getTargetOrgId())) {
            List<TransferRecord> records = targetExistParentsOrgIsTransfer(transferRecord.getSrcOrgId());
            for (TransferRecord record : records) {
                return new OutMessage<String>(Status.TRANSFER_TARGET_ORG_ERROR_MESSAGE).format(record.getName());
            }
        }

        transferRecord.setUserId(userTicket.getUser().getId());
        transferRecord.setName(data.getSrcOrgName());
        transferRecord.setSrcOrgName(data.getSrcOrgName());
        transferRecord.setTargetOrgName(data.getTargetOrgName());
        transferRecord.setReason(data.getReason());
        transferRecord.setStatus(TransferRecordConstant.TRANSFERING);
        transferRecord.setCreateTime(new Date());
        transferRecord.setUpdateTime(new Date());
        transferRecord.setTransferOutTime(data.getTransferOutTime());

        // TODO: 2021/7/16 事务
        boolean flag;
        flag = rewriteSave(transferRecord);
        //创建审批记录
        TransferApproval approval = createOrgTransferApproval(transferRecord);
        approval.setRecordId(transferRecord.getId());
        flag &= transferApprovalService.rewriteSave(approval);
        //创建审批日志
        TransferLog transferLog = transferLogService.createOrgTransferLog();
        transferLog.setHandleApprovalId(approval.getId());
        transferLog.setReason(TransferLogConstant.PUSH_ORG_TRANSFER_REASON);
        flag &= transferLogService.rewriteSave(transferLog);

        //更新转接记录中的审批记录id
        TransferRecord updateRecord = new TransferRecord();
        updateRecord.setId(transferRecord.getId());
        updateRecord.setCurrentApprovalId(approval.getId());
        flag &= updateById(updateRecord);

        // TODO: 2021/12/12 整建制如果目的组织是中间交换区，那么就要把这个数据同步到中间交换区里面去
        Org orgByCode = orgService.findOrgByCode(targetOrgId);
        if (flag&& ObjectUtil.isNull(orgByCode)){
            Org SrcOrg = orgService.findOrgByCode(srcOrgId);
            //同步数据到中间交换区
            //查询转接记录
            TransferRecord recordNow = getById(transferRecord.getId());
            JSONObject dataJsonObject= new JSONObject();
            this.deailOrgTransferData(SrcOrg.getOrgCode(),dataJsonObject);
            if (ObjectUtil.isNull(dataJsonObject)){
                return  new OutMessage<>(Status.DATA_EXCEPTION);
            }
            recordNow.setDataText(dataJsonObject.toJSONString());
            JSONObject postJsonObject = JSONObject.parseObject(JSONObject.toJSONString(recordNow));
            //查询转接节点审批记录
            JSONArray jsonArray =new JSONArray();
            List<TransferApproval> byRecordId = transferApprovalService.findByRecordId(recordNow.getId());
            byRecordId.forEach(TransferApproval-> jsonArray.add(JSONObject.parseObject(JSONObject.toJSONString(TransferApproval))));
            postJsonObject.put("approval",jsonArray);
            //处理携带中间交换区的key的问题
            postJsonObject.put("srcExchangeKey",exchangeNginxKey);
            String replaceUrl = sync_flow_push.replace("/api/flow/push", "");
            HttpKit.doPost(replaceUrl+"/transfer/insertData",postJsonObject,"UTF-8");
        }
        return flag ? new OutMessage<>(Status.SUCCESS) : new OutMessage<>(Status.FAIL);
    }

    @Override
    public void deailEntireData(TransferRecord transferRecord) {
        System.out.println("处理程序回调相关=============>"+transferRecord.getId());
        String srcOrgId = transferRecord.getSrcOrgId();
        String targetOrgId = transferRecord.getTargetOrgId();
        //不带删除条件是因为第二次再进来的时候， 前面已经处理掉删除的数据需要兼容
        Org SrcOrg = orgService.findOrgByOrgId(srcOrgId);
        // TODO: 2021/12/27 只有是我发起的整建制，才进行本地数据处理
        Org tarOrg = orgService.findOrgByCode(targetOrgId);
        if (ObjectUtil.isNotNull(SrcOrg)&&ObjectUtil.isNull(tarOrg)){
            String srcOrgCode = SrcOrg.getOrgCode();
            // TODO: 2021/12/27 整建制处理数据的时候， 只能是处理未删除的数据，已经删除的数据，不能进行处理，否则会出现数据更改错乱的问题
            LambdaQueryWrapper<Unit> unitLambdaQueryWrapper = new LambdaQueryWrapper<>();
            unitLambdaQueryWrapper.isNull(Unit::getDeleteTime);
            unitLambdaQueryWrapper.and(unitLambdaQueryWrapper1 ->
                    unitLambdaQueryWrapper1.likeRight(Unit::getManageUnitOrgCode,srcOrgCode)
                            .or().likeRight(Unit::getMainUnitOrgCode,srcOrgCode)
                            .or().likeRight(Unit::getCreateUnitOrgCode,srcOrgCode)
            );
            List<Unit> unitData = unitMapper.selectList(unitLambdaQueryWrapper);
            if (ObjectUtil.isNotNull(unitData)&&unitData.size()>0){
                List<String> unitCodes = unitData.stream().map(Unit::getCode).collect(Collectors.toList());
                //ccp_unit
                iUnitService.getBaseMapper().delete(new QueryWrapper<Unit>().lambda().in(Unit::getCode, unitCodes));

                //ccp_unit_all
                iUnitAllService.getBaseMapper().delete(new QueryWrapper<UnitAll>().lambda().in(UnitAll::getCode, unitCodes));
                //ccp_unit_city_situation
                unitCitySituationService.getBaseMapper().delete(new QueryWrapper<UnitCitySituation>().lambda().in(UnitCitySituation::getUnitCode, unitCodes));

                //ccp_unit_committee_elect
                iUnitCommitteeElectService.getBaseMapper().delete(new QueryWrapper<UnitCommitteeElect>().lambda().in(UnitCommitteeElect::getUnitCode, unitCodes));

                //ccp_unit_committee
                iUnitCommitteeService.getBaseMapper().delete(new QueryWrapper<UnitCommittee>().lambda().in(UnitCommittee::getUnitCode, unitCodes));

                //ccp_unit_community
                iUnitCommunityService.getBaseMapper().delete(new QueryWrapper<UnitCommunity>().lambda().in(UnitCommunity::getUnitCode, unitCodes));

                //ccp_unit_countryside
                iUnitCountrusideService.getBaseMapper().delete(new QueryWrapper<UnitCountryside>().lambda().in(UnitCountryside::getUnitCode, unitCodes));

                //ccp_unit_extend
                iUnitExtendService.getBaseMapper().delete(new QueryWrapper<UnitExtend>().lambda().in(UnitExtend::getCode, unitCodes));

                //ccp_unit_income
                iUnitIncomeService.getBaseMapper().delete(new QueryWrapper<UnitIncome>().lambda().in(UnitIncome::getUnitCode, unitCodes));

                //ccp_unit_org_linked
                iUnitOrgLinkedService.getBaseMapper().delete(new QueryWrapper<UnitOrgLinked>().lambda().in(UnitOrgLinked::getUnitCode, unitCodes));

                //ccp_unit_secondary
                iUnitSecondaryService.getBaseMapper().delete(new QueryWrapper<UnitSecondary>().lambda().in(UnitSecondary::getUnitCode, unitCodes));

                //ccp_unit_site_conditions
                iUnitSiteConditionsService.getBaseMapper().delete(new QueryWrapper<UnitSiteConditions>().lambda().in(UnitSiteConditions::getUnitCode, unitCodes));

                //ccp_unit_streets_cadres
                iUnitStreetsCadresService.getBaseMapper().delete(new QueryWrapper<UnitStreetsCadres>().lambda().in(UnitStreetsCadres::getUnitCode, unitCodes));
            }

            LambdaQueryWrapper<UnitCollectiveEconomic> unitCollectiveEconomicLambdaQueryWrapper = new LambdaQueryWrapper<>();
            unitCollectiveEconomicLambdaQueryWrapper.isNull(UnitCollectiveEconomic::getDeleteTime);
            unitCollectiveEconomicLambdaQueryWrapper.likeRight(UnitCollectiveEconomic::getEconomicOrgCode,srcOrgCode);
            List<UnitCollectiveEconomic> unitCollectiveEconomics = unitCollectiveEconomicMapper.selectList(unitCollectiveEconomicLambdaQueryWrapper);

            if (unitCollectiveEconomics.size()>0){
                List<String> economicCodes = unitCollectiveEconomics.stream().map(UnitCollectiveEconomic::getCode).collect(Collectors.toList());
                //ccp_unit_collective_economic
                iUnitCollectiveEconomicService.getBaseMapper().delete(new QueryWrapper<UnitCollectiveEconomic>().lambda().in(UnitCollectiveEconomic::getCode, economicCodes));

                //ccp_unit_income
                iUnitIncomeService.getBaseMapper().delete(new QueryWrapper<UnitIncome>().lambda().in(UnitIncome::getEconomicCode, economicCodes));
            }

            //ccp_org
            LambdaQueryWrapper<Org> orgLambdaQueryWrapper = new LambdaQueryWrapper<>();
            orgLambdaQueryWrapper.likeRight(Org::getOrgCode,srcOrgCode);
            orgLambdaQueryWrapper.isNull(Org::getDeleteTime);
            orgLambdaQueryWrapper.and(orgLambdaQueryWrapper1 -> orgLambdaQueryWrapper1.isNull(Org::getIsDissolve)
                    .or().ne(Org::getIsDissolve, CommonConstant.ONE_INT));
            List<Org> orgData = orgMapper.selectList(orgLambdaQueryWrapper);
            if (ObjectUtil.isNotNull(orgData)&&orgData.size()>0){
                List<OrgHistory> orgHistoryList = new ArrayList<>();
                orgData.forEach(org -> {
                    OrgHistory orgHistory =new OrgHistory();
                    org.setReason("整建制转出");
                    org.setId(null);
                    BeanUtils.copyProperties(org,orgHistory);
                    orgHistory.setHistoryTime(new Date());
                    orgHistoryList.add(orgHistory);
                });
                List<String> orgs = orgData.stream().map(Org::getCode).collect(Collectors.toList());
                // TODO: 2022/1/7 组织表删除以前，需要进入历史表存储
                iOrgHistoryService.saveBatch(orgHistoryList);
                //ccp_org
                iOrgService.getBaseMapper().delete(new QueryWrapper<Org>().lambda().in(Org::getCode, orgs));

                //ccp_org_all
                orgAllService.getBaseMapper().delete(new QueryWrapper<OrgAll>().lambda().in(OrgAll::getCode, orgs));

                //ccp_org_assess
                iOrgAssessService.getBaseMapper().delete(new QueryWrapper<OrgAssess>().lambda().in(OrgAssess::getOrgCode, orgs));

                //ccp_org_caucus
                iOrgCaucusService.getBaseMapper().delete(new QueryWrapper<OrgCaucus>().lambda().in(OrgCaucus::getOrgCode, orgs));

                //ccp_org_committee_elect
                iOrgCommitteeElectService.getBaseMapper().delete(new QueryWrapper<OrgCommitteeElect>().lambda().in(OrgCommitteeElect::getOrgCode, orgs));

                //ccp_org_committee
                iOrgCommitteeService.getBaseMapper().delete(new QueryWrapper<OrgCommittee>().lambda().in(OrgCommittee::getOrgCode, orgs));

                //ccp_org_develop_rights
                iOrgDevelopRightsService.getBaseMapper().delete(new QueryWrapper<OrgDevelopRights>().lambda().in(OrgDevelopRights::getOrgCode, orgs));

                //ccp_org_extend
                iOrgExtendService.getBaseMapper().delete(new QueryWrapper<OrgExtend>().lambda().in(OrgExtend::getCode, orgs));


                LambdaQueryWrapper<OrgGroup> orgGroupLambdaQueryWrapper = new LambdaQueryWrapper<>();
                orgGroupLambdaQueryWrapper.isNull(OrgGroup::getDeleteTime);
                orgGroupLambdaQueryWrapper.in(OrgGroup::getOrgCode,orgs);
                List<OrgGroup> orgGroups = orgGroupMapper.selectList(orgGroupLambdaQueryWrapper);
                if (ObjectUtil.isNotNull(orgGroups)&&orgGroups.size()>0){
                    //ccp_org_group
                    List<String> orgGroup = orgGroups.stream().map(OrgGroup::getCode).collect(Collectors.toList());
                    iOrgGroupService.getBaseMapper().delete(new QueryWrapper<OrgGroup>().lambda().in(OrgGroup::getCode, orgGroup));

                    //ccp_org_group_member
                    iOrgGroupMemberService.getBaseMapper().delete(new QueryWrapper<OrgGroupMember>().lambda().in(OrgGroupMember::getGroupCode, orgGroup));

                }
                //ccp_org_industry
                industryService.getBaseMapper().delete(new QueryWrapper<OrgIndustry>().lambda().in(OrgIndustry::getOrgCode, orgs));

                //ccp_org_industry_all
                iOrgIndustryAllService.getBaseMapper().delete(new QueryWrapper<OrgIndustryAll>().lambda().in(OrgIndustryAll::getOrgCode, orgs));


                //ccp_org_non_public_party
                iOrgNonPublicPartyService.getBaseMapper().delete(new QueryWrapper<OrgNonPublicParty>().lambda().in(OrgNonPublicParty::getOrgCode, orgs));

                //ccp_org_party
                iOrgPartyService.getBaseMapper().delete(new QueryWrapper<OrgParty>().lambda().in(OrgParty::getOrgCode, orgs));


                LambdaQueryWrapper<OrgPartyCongressElect> orgPartyCongressElectLambdaQueryWrapper = new LambdaQueryWrapper<>();
                orgPartyCongressElectLambdaQueryWrapper.isNull(OrgPartyCongressElect::getDeleteTime);
                orgPartyCongressElectLambdaQueryWrapper.in(OrgPartyCongressElect::getOrgCode,orgs);
                List<OrgPartyCongressElect> orgPartyCongressElects = orgPartyCongressElectMapper.selectList(orgPartyCongressElectLambdaQueryWrapper);


                if (ObjectUtil.isNotNull(orgPartyCongressElects) && orgPartyCongressElects.size() > 0) {
                    List<String> collect = orgPartyCongressElects.stream().map(OrgPartyCongressElect::getCode).collect(Collectors.toList());
                    //ccp_org_party_congress_elect
                    iOrgPartyCongressElectService.getBaseMapper().delete(new QueryWrapper<OrgPartyCongressElect>().lambda().in(OrgPartyCongressElect::getCode, collect));

                    //ccp_org_party_congress_committee
                    iOrgPartyCongressCommitteeService.getBaseMapper().delete(new QueryWrapper<OrgPartyCongressCommittee>().lambda().in(OrgPartyCongressCommittee::getElectCode, collect));
                    //ccp_org_party_congress_committee_all
                    iOrgPartyCongressCommitteeAllService.getBaseMapper().delete(new QueryWrapper<OrgPartyCongressCommitteeAll>().lambda().in(OrgPartyCongressCommitteeAll::getElectCode, collect));
                }

                //ccp_org_recognition
                LambdaQueryWrapper<OrgRecognition> orgRecognitionLambdaQueryWrapper = new LambdaQueryWrapper<>();
                orgRecognitionLambdaQueryWrapper.isNull(OrgRecognition::getDeleteTime);
                orgRecognitionLambdaQueryWrapper.in(OrgRecognition::getOrgCode,orgs);
                List<OrgRecognition> orgRecognitions = orgRecognitionMapper.selectList(orgRecognitionLambdaQueryWrapper);
                if (ObjectUtil.isNotNull(orgRecognitions)&&orgRecognitions.size()>0){
                    List<String> collect = orgRecognitions.stream().map(OrgRecognition::getCode).collect(Collectors.toList());
                    //ccp_org_recognition
                    iOrgRecognitionService.getBaseMapper().delete(new QueryWrapper<OrgRecognition>().lambda().in(OrgRecognition::getCode, collect));
                    //ccp_org_recognition_data
                    iOrgRecognitionDataService.getBaseMapper().delete(new QueryWrapper<OrgRecognitionData>().lambda().in(OrgRecognitionData::getRecognitionCode, collect));
                }
                //ccp_org_recognition_all
                iOrgRecognitionAllService.getBaseMapper().delete(new QueryWrapper<OrgRecognitionAll>().lambda().in(OrgRecognitionAll::getOrgCode, orgs));

                //ccp_org_recognition_situation
                iOrgRecognitionSituationService.getBaseMapper().delete(new QueryWrapper<OrgRecognitionSituation>().lambda().in(OrgRecognitionSituation::getOrgCode, orgs));

                //ccp_org_appraisal
                LambdaQueryWrapper<OrgAppraisal> orgAppraisalLambdaQueryWrapper = new LambdaQueryWrapper<>();
                orgAppraisalLambdaQueryWrapper.isNull(OrgAppraisal::getDeleteTime);
                orgAppraisalLambdaQueryWrapper.in(OrgAppraisal::getOrgCode,orgs);
                List<OrgAppraisal> orgAppraisals = orgAppraisalMapper.selectList(orgAppraisalLambdaQueryWrapper);
                //ccp_org_reviewers
                if (ObjectUtil.isNotNull(orgAppraisals)&&orgAppraisals.size()>0){
                    List<String> collect = orgAppraisals.stream().map(OrgAppraisal::getCode).collect(Collectors.toList());
                    //ccp_org_appraisal
                    iOrgAppraisalService.getBaseMapper().delete(new QueryWrapper<OrgAppraisal>().lambda().in(OrgAppraisal::getCode, collect));

                    //ccp_org_reviewers
                    iOrgReviewersService.getBaseMapper().delete(new QueryWrapper<OrgReviewers>().lambda().in(OrgReviewers::getAppraisalCode, collect));
                }

                //ccp_org_reward
                iOrgRewardService.getBaseMapper().delete(new QueryWrapper<OrgReward>().lambda().in(OrgReward::getOrgCode, orgs));

                //ccp_org_slack
                LambdaQueryWrapper<OrgSlack> orgSlackLambdaQueryWrapper = new LambdaQueryWrapper<>();
                orgSlackLambdaQueryWrapper.isNull(OrgSlack::getDeleteTime);
                orgSlackLambdaQueryWrapper.in(OrgSlack::getOrgCode,orgs);
                List<OrgSlack> orgSlacks = orgSlackMapper.selectList(orgSlackLambdaQueryWrapper);
                if (ObjectUtil.isNotNull(orgSlacks)&&orgSlacks.size()>0){
                    //List<String> collect = orgSlacks.stream().map(orgSlack -> orgSlack.getCode()).collect(Collectors.toList());
                    //ccp_org_slack
                    iOrgSlackService.getBaseMapper().delete(new QueryWrapper<OrgSlack>().lambda().in(OrgSlack::getCode, orgs));

                    //ccp_org_slack_all
                    iOrgSlackAllService.getBaseMapper().delete(new QueryWrapper<OrgSlackAll>().lambda().in(OrgSlackAll::getCode, orgs));

                    //ccp_org_slack_rectification
                    //LambdaUpdateChainWrapper<OrgSlackRectification> orgSlackRectificationLambdaUpdateChainWrapper = new LambdaUpdateChainWrapper<>(orgSlackRectificationMapper);
                    //orgSlackRectificationLambdaUpdateChainWrapper.in(OrgSlackRectification::getSlackCode,collect).isNull(OrgSlackRectification::getDeleteTime).set(OrgSlackRectification::getDeleteTime,new Date()).update();
                }

                //ccp_org_special_nature
                iOrgSpecialNatureService.getBaseMapper().delete(new QueryWrapper<OrgSpecialNature>().lambda().in(OrgSpecialNature::getOrgCode, orgs));

                //ccp_org_township_leadership
                iOrgTownshipLeadershipService.getBaseMapper().delete(new QueryWrapper<OrgTownshipLeadership>().lambda().in(OrgTownshipLeadership::getOrgCode, orgs));

            }

            //ccp_mem
            LambdaQueryWrapper<Mem> memLambdaQueryWrapper = new LambdaQueryWrapper<>();
            memLambdaQueryWrapper.likeRight(Mem::getMemOrgCode,srcOrgCode);
            memLambdaQueryWrapper.isNull(Mem::getDeleteTime);
            List<Mem> memList = memMapper.selectList(memLambdaQueryWrapper);
            if (ObjectUtil.isNotNull(memList)&&memList.size()>0){
                List<String> collect = memList.stream().map(Mem::getCode).collect(Collectors.toList());
                //ccp_mem
                // TODO: 2022/1/7 人员基础信息需要进入历史表存储
                List<MemHistory> memHistoryList =new ArrayList<>();
                memList.forEach(mem -> {
                    MemHistory memHistory= new MemHistory();
                    mem.setId(null);
                    mem.setD12Code("6");
                    mem.setD12Name("整建制转出");
                    BeanUtils.copyProperties(mem,memHistory);
                    memHistory.setHistoryTime(new Date());
                    memHistoryList.add(memHistory);
                });
                iMemHsitoryService.saveBatch(memHistoryList);
                iMemService.getBaseMapper().delete(new QueryWrapper<Mem>().lambda().in(Mem::getCode, collect));

                //ccp_mem_all
                iMemAllInfoService.getBaseMapper().delete(new QueryWrapper<MemAllInfo>().lambda().in(MemAllInfo::getCode, collect));

                //ccp_mem_reward_all
                iMemRewardAllService.getBaseMapper().delete(new QueryWrapper<MemRewardAll>().lambda().in(MemRewardAll::getMemCode, collect));
                }

            //ccp_democratic_review_mem
            iDemocraticReviewMemService.getBaseMapper().delete(new QueryWrapper<DemocraticReviewMem>().lambda().like(DemocraticReviewMem::getReviewMemOrgCode, srcOrgCode));

            //ccp_mem_abroad
            iMemAbroadService.getBaseMapper().delete(new QueryWrapper<MemAbroad>().lambda().like(MemAbroad::getAbroadOrgCode, srcOrgCode));

            //ccp_mem_develop
            LambdaQueryWrapper<MemDevelop> memDevelopLambdaQueryWrapper = new LambdaQueryWrapper<>();
//            memDevelopLambdaQueryWrapper.select(MemDevelop.class, et -> !ReflectionUtil.getEntityEncryptFields(MemDevelop.class).contains(et.getProperty()));
            WrapperUtil.excludeEncrypt(memDevelopLambdaQueryWrapper, MemDevelop.class);
            memDevelopLambdaQueryWrapper.likeRight(MemDevelop::getDevelopOrgCode,srcOrgCode);
            memDevelopLambdaQueryWrapper.isNull(MemDevelop::getDeleteTime);
            List<MemDevelop> memDevelops = memDevelopMapper.selectList(memDevelopLambdaQueryWrapper);
            if (ObjectUtil.isNotNull(memDevelops)&&memDevelops.size()>0){
                List<String> collect = memDevelops.stream().map(MemDevelop::getCode).collect(Collectors.toList());
                //ccp_mem_develop
                iMemDevelopService.getBaseMapper().delete(new QueryWrapper<MemDevelop>().lambda().in(MemDevelop::getCode, collect));
                //ccp_mem_develop_operation
                iMemDevelopOperationService.getBaseMapper().delete(new QueryWrapper<MemDevelopOperation>().lambda().in(MemDevelopOperation::getDevelopCode, collect));
                //ccp_mem_develop_all
                iMemDevelopAllService.getBaseMapper().delete(new QueryWrapper<MemDevelopAll>().lambda().in(MemDevelopAll::getCode, collect));

            }

            //ccp_mem_difficult
            iMemDifficultService.getBaseMapper().delete(new QueryWrapper<MemDifficult>().lambda().likeRight(MemDifficult::getDiffOrgCode, srcOrgCode));

            //ccp_mem_extend说是没用
    //        LambdaUpdateChainWrapper<MemExtend> memExtendLambdaUpdateChainWrapper = new LambdaUpdateChainWrapper<>(memExtendMapper);
    //        memExtendLambdaUpdateChainWrapper.likeRight(MemExtend::getExtendOrgCode,srcOrgCode).isNull(MemExtend::getDeleteTime).set(MemExtend::getDeleteTime,new Date()).update();

            //ccp_mem_flow
            iMemFlowService.getBaseMapper().delete(new QueryWrapper<MemFlow>().lambda().likeRight(MemFlow::getMemOrgOrgCode, srcOrgCode));

            //ccp_mem_flow_all
            LambdaQueryWrapper<MemFlow> memFlowLambdaQueryWrapper = new LambdaQueryWrapper<>();
//            memFlowLambdaQueryWrapper.select(MemFlow.class,et -> !ReflectionUtil.getEntityEncryptFields(MemFlow.class).contains(et.getProperty()));
            WrapperUtil.excludeEncrypt(memFlowLambdaQueryWrapper, MemFlow.class);

            memFlowLambdaQueryWrapper.likeRight(MemFlow::getMemOrgOrgCode,srcOrgCode);
            memFlowLambdaQueryWrapper.isNull(MemFlow::getDeleteTime);
            List<MemFlow> memFlows = memFlowMapper.selectList(memFlowLambdaQueryWrapper);
            if (ObjectUtil.isNotNull(memFlows)&&memFlows.size()>0){
                List<String> collect = memFlows.stream().map(MemFlow::getCode).collect(Collectors.toList());
                iMemFlowAllService.getBaseMapper().delete(new QueryWrapper<MemFlowAll>().lambda().in(MemFlowAll::getCode, collect));
            }

            //ccp_mem_reward
            iMemRewardService.getBaseMapper().delete(new QueryWrapper<MemReward>().lambda().likeRight(MemReward::getRewardOrgCode, srcOrgCode));

            //ccp_mem_train
            iMemTrainService.getBaseMapper().delete(new QueryWrapper<MemTrain>().lambda().likeRight(MemTrain::getOrgOrgCode, srcOrgCode));

            //sys_user
            LambdaQueryWrapper<User> userLambdaQueryWrapper = new LambdaQueryWrapper<>();
            userLambdaQueryWrapper.likeRight(User::getOrgCode,srcOrgCode);
            userLambdaQueryWrapper.eq(User::getIsDelete,CommonConstant.ZERO_INT);
            List<User> users = userMapper.selectList(userLambdaQueryWrapper);
            if (ObjectUtil.isNotNull(users)&&users.size()>0){
                List<String> collect = users.stream().map(User::getId).collect(Collectors.toList());
                userService.getBaseMapper().delete(new QueryWrapper<User>().lambda().in(User::getId, collect));
                LambdaUpdateChainWrapper<User> userLambdaUpdateChainWrapper = new LambdaUpdateChainWrapper<>(userMapper);
                userLambdaUpdateChainWrapper.likeRight(User::getOrgCode,srcOrgCode).eq(User::getIsDelete,CommonConstant.ZERO_INT).set(User::getIsDelete,CommonConstant.ONE_INT).update();
                userRolePermissionService.getBaseMapper().delete(new QueryWrapper<UserRolePermission>().lambda().in(UserRolePermission::getUserId, collect));

                //sys_user_role_permission 这个表没有删除标识， 只有整建制反复转过去转过来的时候，会有问题
//                LambdaUpdateChainWrapper<UserRolePermission> userRolePermissionLambdaUpdateChainWrapper = new LambdaUpdateChainWrapper<>(memFlowAllMapper);
//                userRolePermissionLambdaUpdateChainWrapper.in(UserRolePermission::getUserId,collect).isNull(MemFlowAll::getDeleteTime).set(MemFlowAll::getDeleteTime,new Date()).update();
            }
            //ccp_develop_step_log
            iDevelopStepLogService.getBaseMapper().delete(new QueryWrapper<DevelopStepLog>().lambda().likeRight(DevelopStepLog::getLogOrgCode, srcOrgCode));
            //ccp_develop_step_log_all
            iDevelopStepLogAllService.getBaseMapper().delete(new QueryWrapper<DevelopStepLogAll>().lambda().likeRight(DevelopStepLogAll::getLogOrgCode, srcOrgCode));

            // todo 2025-03-19 添加该处是因为遵义党员、入党申请人、积极份子、发展对象需要走流程
            // 获取档案唯一码
            Set<String> digLotNoSet = new HashSet<>();
            if (CollUtil.isNotEmpty(memList)) {
                Set<String> collect = memList.stream().map(Mem::getDigitalLotNo).filter(StrUtil::isNotBlank).collect(Collectors.toSet());
                if (CollUtil.isNotEmpty(collect)) {
                    digLotNoSet.addAll(collect);
                }
            }
            if (CollUtil.isNotEmpty(memDevelops)) {
                Set<String> collect = memDevelops.stream().map(MemDevelop::getDigitalLotNo).filter(StrUtil::isNotBlank).collect(Collectors.toSet());
                if (CollUtil.isNotEmpty(collect)) {
                    digLotNoSet.addAll(collect);
                }
            }
            // 档案唯一码有值 todo 这里不做物理删除，逻辑删除
            this.deleteDigLotNo(digLotNoSet);
        }
    }

    /**
     * 转接转出方档案相关信息处理
     * @param digLotNoSet
     */
    @Override
    public void deleteDigLotNo(Set<String> digLotNoSet){
        // 档案唯一码有值 todo 这里不做物理删除，逻辑删除
        if (CollUtil.isNotEmpty(digLotNoSet)) {
            // ccp_mem_develop_process 流程信息
            LambdaUpdateWrapper<MemDevelopProcess> memDevelopProcessLambdaQueryWrapper = Wrappers.lambdaUpdate();
            memDevelopProcessLambdaQueryWrapper.in(MemDevelopProcess::getDigitalLotNo, digLotNoSet)
                    .set(MemDevelopProcess::getApproveTime, new Date())
                    .set(MemDevelopProcess::getApproveUser, "关系转接流程作废并留存依据处理")
                    .set(MemDevelopProcess::getDeleteTime, new Date());
            memDevelopProcessMapper.update(null, memDevelopProcessLambdaQueryWrapper);

            // ccp_mem_digital 档案
            LambdaUpdateWrapper<MemDigital> memDigitalLambdaQueryWrapper = Wrappers.lambdaUpdate();
            // 设置删除时间跟转出时间
            memDigitalLambdaQueryWrapper
                    .set(MemDigital::getDeleteTime, new Date())
                    .set(MemDigital::getTransferTime, new Date())
                    .in(MemDigital::getDigitalLotNo, digLotNoSet)
                    .isNull(MemDigital::getDeleteTime);
            memDigitalMapper.update(null, memDigitalLambdaQueryWrapper);

            // ccp_mem_digital_operation_log 档案日志
            LambdaUpdateWrapper<MemDigitalOperationLog> memDigitalOperationLogLambdaQueryWrapper = Wrappers.lambdaUpdate();
            // 设置删除时间
            memDigitalOperationLogLambdaQueryWrapper.set(MemDigitalOperationLog::getDeleteTime, new Date())
                    .in(MemDigitalOperationLog::getDigitalLotNo, digLotNoSet)
                    .isNull(MemDigitalOperationLog::getDeleteTime);
            memDigitalOperationLogMapper.update(null, memDigitalOperationLogLambdaQueryWrapper);

            // 这里物理删除档案完整信息统计表，这张表实时变化的
            LambdaQueryWrapper<MemDigitalCount> memDigitalCountLambdaQueryWrapper = Wrappers.lambdaQuery();
            memDigitalCountLambdaQueryWrapper.in(MemDigitalCount::getDigitalLotNo, digLotNoSet);
            memDigitalCountMapper.delete(memDigitalCountLambdaQueryWrapper);
        }
    }

    @Override
    public void deailOrgTransferData(String srcOrgCode,JSONObject jsonObject){
        //1.单位管理相关情况数据
        //ccp_unit
        LambdaQueryWrapper<Unit> unitLambdaQueryWrapper = new LambdaQueryWrapper<>();
        unitLambdaQueryWrapper.isNull(Unit::getDeleteTime);
        unitLambdaQueryWrapper.and(unitLambdaQueryWrapper1 ->
            unitLambdaQueryWrapper1.likeRight(Unit::getManageUnitOrgCode,srcOrgCode)
                    .or().likeRight(Unit::getMainUnitOrgCode,srcOrgCode)
                    .or().likeRight(Unit::getCreateUnitOrgCode,srcOrgCode)
        );

        List<Unit> unitData = unitMapper.selectList(unitLambdaQueryWrapper);
        JSONArray ccp_unit = this.deailJsonArray(unitData);
        if (ccp_unit != null) {
            jsonObject.put("ccp_unit", ccp_unit);
        }
        System.out.println("获取到ccp_unit数量===>"+unitData.size());

        List<UnitIncome> unitIncomesAll =new ArrayList<>();
        if (ObjectUtil.isNotNull(unitData)&&unitData.size()>0){
            List<String> unitCodes = unitData.stream().map(Unit::getCode).collect(Collectors.toList());
            //ccp_unit_all
            LambdaQueryWrapper<UnitAll> unitAllLambdaQueryWrapper = new LambdaQueryWrapper<>();
            unitAllLambdaQueryWrapper.isNull(UnitAll::getDeleteTime);
            unitAllLambdaQueryWrapper.in(UnitAll::getCode,unitCodes);
            List<UnitAll> unitAlls = unitAllMapper.selectList(unitAllLambdaQueryWrapper);
            JSONArray ccp_unit_all = this.deailJsonArray(unitAlls);
            if (unitAlls != null) {
                jsonObject.put("ccp_unit_all", ccp_unit_all);
            }
            System.out.println("获取到ccp_unit_all数量===>"+unitAlls.size());

            //ccp_unit_city_situation
            LambdaQueryWrapper<UnitCitySituation> unitCitySituationLambdaQueryWrapper = new LambdaQueryWrapper<>();
            unitCitySituationLambdaQueryWrapper.isNull(UnitCitySituation::getDeleteTime);
            unitCitySituationLambdaQueryWrapper.in(UnitCitySituation::getUnitCode,unitCodes);
            List<UnitCitySituation> unitCitySituations = unitCitySituationMapper.selectList(unitCitySituationLambdaQueryWrapper);
            JSONArray ccp_unit_city_situation = this.deailJsonArray(unitCitySituations);
            if (ccp_unit_city_situation != null) {
                jsonObject.put("ccp_unit_city_situation", ccp_unit_city_situation);
            }
            System.out.println("获取到ccp_unit_city_situation数量===>"+unitCitySituations.size());
            //ccp_unit_committee_elect
            LambdaQueryWrapper<UnitCommitteeElect> unitCommitteeElectLambdaQueryWrapper = new LambdaQueryWrapper<>();
            unitCommitteeElectLambdaQueryWrapper.isNull(UnitCommitteeElect::getDeleteTime);
            unitCommitteeElectLambdaQueryWrapper.in(UnitCommitteeElect::getUnitCode,unitCodes);
            List<UnitCommitteeElect> unitCommitteeElects = unitCommitteeElectMapper.selectList(unitCommitteeElectLambdaQueryWrapper);
            JSONArray ccp_unit_committee_elect = this.deailJsonArray(unitCommitteeElects);
            if (ccp_unit_committee_elect != null) {
                jsonObject.put("ccp_unit_committee_elect", ccp_unit_committee_elect);
            }
            System.out.println("获取到ccp_unit_committee_elect数量===>"+unitCommitteeElects.size());

            //ccp_unit_committee
            LambdaQueryWrapper<UnitCommittee> unitCommitteeLambdaQueryWrapper = new LambdaQueryWrapper<>();
            unitCommitteeLambdaQueryWrapper.isNull(UnitCommittee::getDeleteTime);
            unitCommitteeLambdaQueryWrapper.in(UnitCommittee::getUnitCode,unitCodes);
            List<UnitCommittee> unitCommittees = unitCommitteeMapper.selectList(unitCommitteeLambdaQueryWrapper);
            JSONArray ccp_unit_committee = this.deailJsonArray(unitCommittees);
            if (ccp_unit_committee != null) {
                jsonObject.put("ccp_unit_committee", ccp_unit_committee);
            }
            System.out.println("获取到ccp_unit_committee数量===>"+unitCommittees.size());

            //ccp_unit_community
            LambdaQueryWrapper<UnitCommunity> unitCommunityLambdaQueryWrapper = new LambdaQueryWrapper<>();
            unitCommunityLambdaQueryWrapper.isNull(UnitCommunity::getDeleteTime);
            unitCommunityLambdaQueryWrapper.in(UnitCommunity::getUnitCode,unitCodes);
            List<UnitCommunity> unitCommunities = unitCommunityMapper.selectList(unitCommunityLambdaQueryWrapper);
            JSONArray ccp_unit_community = this.deailJsonArray(unitCommunities);
            if (ccp_unit_community != null) {
                jsonObject.put("ccp_unit_community", ccp_unit_community);
            }
            System.out.println("获取到unitCommunities数量===>"+unitCommunities.size());

            //ccp_unit_countryside
            LambdaQueryWrapper<UnitCountryside> unitCountrysideLambdaQueryWrapper = new LambdaQueryWrapper<>();
            unitCountrysideLambdaQueryWrapper.isNull(UnitCountryside::getDeleteTime);
            unitCountrysideLambdaQueryWrapper.in(UnitCountryside::getUnitCode,unitCodes);
            List<UnitCountryside> unitCountrysides = unitCountrysideMapper.selectList(unitCountrysideLambdaQueryWrapper);
            JSONArray ccp_unit_countryside = this.deailJsonArray(unitCountrysides);
            if (ccp_unit_countryside != null) {
                jsonObject.put("ccp_unit_countryside", ccp_unit_countryside);
            }
            System.out.println("获取到ccp_unit_countryside数量===>"+unitCountrysides.size());

            //ccp_unit_extend
            LambdaQueryWrapper<UnitExtend> unitExtendLambdaQueryWrapper = new LambdaQueryWrapper<>();
            unitExtendLambdaQueryWrapper.isNull(UnitExtend::getDeleteTime);
            unitExtendLambdaQueryWrapper.in(UnitExtend::getCode,unitCodes);
            List<UnitExtend> unitExtends = unitExtendMapper.selectList(unitExtendLambdaQueryWrapper);
            JSONArray ccp_unit_extend = this.deailJsonArray(unitExtends);
            if (ccp_unit_extend != null) {
                jsonObject.put("ccp_unit_extend", ccp_unit_extend);
            }
            System.out.println("获取到ccp_unit_extend数量===>"+unitExtends.size());

            //ccp_unit_income
            LambdaQueryWrapper<UnitIncome> unitIncomeLambdaQueryWrapper = new LambdaQueryWrapper<>();
            unitIncomeLambdaQueryWrapper.isNull(UnitIncome::getDeleteTime);
            unitIncomeLambdaQueryWrapper.in(UnitIncome::getUnitCode,unitCodes);
            List<UnitIncome> unitIncomes = unitIncomeMapper.selectList(unitIncomeLambdaQueryWrapper);
            if (unitIncomes.size()>0){
                unitIncomesAll.addAll(unitIncomes);
                System.out.println("通过单位获取到ccp_unit_income数量===>"+unitIncomes.size());
            }
            //ccp_unit_org_linked
            LambdaQueryWrapper<UnitOrgLinked> unitOrgLinkedLambdaQueryWrapper = new LambdaQueryWrapper<>();
            unitOrgLinkedLambdaQueryWrapper.isNull(UnitOrgLinked::getDeleteTime);
            unitOrgLinkedLambdaQueryWrapper.in(UnitOrgLinked::getUnitCode,unitCodes);
            List<UnitOrgLinked> unitOrgLinkeds = unitOrgLinkedMapper.selectList(unitOrgLinkedLambdaQueryWrapper);
            JSONArray ccp_unit_org_linked = this.deailJsonArray(unitOrgLinkeds);
            if (ccp_unit_org_linked != null) {
                jsonObject.put("ccp_unit_org_linked", ccp_unit_org_linked);
            }
            System.out.println("获取到ccp_unit_org_linked数量===>"+unitOrgLinkeds.size());

            //ccp_unit_secondary
            LambdaQueryWrapper<UnitSecondary> unitSecondaryLambdaQueryWrapper = new LambdaQueryWrapper<>();
            unitSecondaryLambdaQueryWrapper.isNull(UnitSecondary::getDeleteTime);
            unitSecondaryLambdaQueryWrapper.in(UnitSecondary::getUnitCode,unitCodes);
            List<UnitSecondary> unitSecondaries = unitSecondaryMapper.selectList(unitSecondaryLambdaQueryWrapper);
            JSONArray ccp_unit_secondary = this.deailJsonArray(unitSecondaries);
            if (ccp_unit_secondary != null) {
                jsonObject.put("ccp_unit_secondary", ccp_unit_secondary);
            }
            System.out.println("获取到ccp_unit_secondary数量===>"+unitSecondaries.size());

            //ccp_unit_site_conditions
            LambdaQueryWrapper<UnitSiteConditions> unitSiteConditionsLambdaQueryWrapper = new LambdaQueryWrapper<>();
            unitSiteConditionsLambdaQueryWrapper.isNull(UnitSiteConditions::getDeleteTime);
            unitSiteConditionsLambdaQueryWrapper.in(UnitSiteConditions::getUnitCode,unitCodes);
            List<UnitSiteConditions> unitSiteConditions = unitSiteConditionsMapper.selectList(unitSiteConditionsLambdaQueryWrapper);
            JSONArray ccp_unit_site_conditions = this.deailJsonArray(unitSiteConditions);
            if (ccp_unit_site_conditions != null) {
                jsonObject.put("ccp_unit_site_conditions", ccp_unit_site_conditions);
            }
            System.out.println("获取到ccp_unit_site_conditions数量===>"+unitSiteConditions.size());

            //ccp_unit_streets_cadres
            LambdaQueryWrapper<UnitStreetsCadres> unitStreetsCadresLambdaQueryWrapper = new LambdaQueryWrapper<>();
            unitStreetsCadresLambdaQueryWrapper.isNull(UnitStreetsCadres::getDeleteTime);
            unitStreetsCadresLambdaQueryWrapper.in(UnitStreetsCadres::getUnitCode,unitCodes);
            List<UnitStreetsCadres> unitStreetsCadres = unitStreetsCadresMapper.selectList(unitStreetsCadresLambdaQueryWrapper);
            JSONArray ccp_unit_streets_cadres = this.deailJsonArray(unitStreetsCadres);
            if (ccp_unit_streets_cadres != null) {
                jsonObject.put("ccp_unit_streets_cadres", ccp_unit_streets_cadres);
            }
            System.out.println("获取到ccp_unit_streets_cadres数量===>"+unitStreetsCadres.size());


        }
        //ccp_unit_collective_economic
        LambdaQueryWrapper<UnitCollectiveEconomic> unitCollectiveEconomicLambdaQueryWrapper = new LambdaQueryWrapper<>();
        unitCollectiveEconomicLambdaQueryWrapper.isNull(UnitCollectiveEconomic::getDeleteTime);
        unitCollectiveEconomicLambdaQueryWrapper.likeRight(UnitCollectiveEconomic::getEconomicOrgCode,srcOrgCode);
        List<UnitCollectiveEconomic> unitCollectiveEconomics = unitCollectiveEconomicMapper.selectList(unitCollectiveEconomicLambdaQueryWrapper);
        JSONArray ccp_unit_collective_economic = this.deailJsonArray(unitCollectiveEconomics);
        if (ccp_unit_collective_economic != null) {
            jsonObject.put("ccp_unit_collective_economic", ccp_unit_collective_economic);
        }
        System.out.println("获取到ccp_unit_collective_economic数量===>"+unitCollectiveEconomics.size());

        //ccp_unit_income
        if (unitCollectiveEconomics.size()>0){
            List<String> economicCodes = unitCollectiveEconomics.stream().map(UnitCollectiveEconomic::getCode).collect(Collectors.toList());
            LambdaQueryWrapper<UnitIncome> unitIncomeLambdaQueryWrapper = new LambdaQueryWrapper<>();
            unitIncomeLambdaQueryWrapper.isNull(UnitIncome::getDeleteTime);
            unitIncomeLambdaQueryWrapper.in(UnitIncome::getEconomicCode,economicCodes);
            List<UnitIncome> unitIncomes = unitIncomeMapper.selectList(unitIncomeLambdaQueryWrapper);
            unitIncomesAll.addAll(unitIncomes);
            System.out.println("通过ccp_unit_collective_economic获取到ccp_unit_income数量===>"+unitIncomes.size());
        }
        JSONArray ccp_unit_income = this.deailJsonArray(unitIncomesAll);
        if (ccp_unit_income != null) {
            jsonObject.put("ccp_unit_income", ccp_unit_income);
        }
        System.out.println("获取到ccp_unit_income数量===>"+unitIncomesAll.size());
        System.out.println("<=========获取到单位数据情况结束===>");


        //4.党组织管理
        //ccp_org
        LambdaQueryWrapper<Org> orgLambdaQueryWrapper = new LambdaQueryWrapper<>();
        orgLambdaQueryWrapper.likeRight(Org::getOrgCode,srcOrgCode);
        orgLambdaQueryWrapper.isNull(Org::getDeleteTime);
        orgLambdaQueryWrapper.and(orgLambdaQueryWrapper1 -> orgLambdaQueryWrapper1.isNull(Org::getIsDissolve)
                .or().ne(Org::getIsDissolve, CommonConstant.ONE_INT));
        List<Org> orgData = orgMapper.selectList(orgLambdaQueryWrapper);
        JSONArray ccp_org = this.deailJsonArray(orgData);
        if (ccp_org != null) {
            jsonObject.put("ccp_org", ccp_org);
        }
        System.out.println("获取到ccp_org数量===>"+orgData.size());

        if (ObjectUtil.isNotNull(orgData)&&orgData.size()>0){
            List<String> orgs = orgData.stream().map(Org::getCode).collect(Collectors.toList());
            //ccp_org_all
            LambdaQueryWrapper<OrgAll> orgAllLambdaQueryWrapper = new LambdaQueryWrapper<>();
            orgAllLambdaQueryWrapper.in(OrgAll::getCode,orgs);
            List<OrgAll> orgAlls = orgAllMapper.selectList(orgAllLambdaQueryWrapper);
            JSONArray ccp_org_all = this.deailJsonArray(orgAlls);
            if (ccp_org_all != null) {
                jsonObject.put("ccp_org_all", orgAlls);
            }
            System.out.println("获取到ccp_org_all数量===>"+ccp_org_all.size());

            //ccp_org_assess
            LambdaQueryWrapper<OrgAssess> orgAssessLambdaQueryWrapper = new LambdaQueryWrapper<>();
            orgAssessLambdaQueryWrapper.isNull(OrgAssess::getDeleteTime);
            orgAssessLambdaQueryWrapper.in(OrgAssess::getOrgCode,orgs);
            List<OrgAssess> orgAssesses = orgAssessMapper.selectList(orgAssessLambdaQueryWrapper);
            JSONArray ccp_org_assess = this.deailJsonArray(orgAssesses);
            if (ccp_org_assess != null) {
                jsonObject.put("ccp_org_assess", ccp_org_assess);
            }
            System.out.println("获取到ccp_org_assess数量===>"+orgAssesses.size());

            //ccp_org_caucus
            LambdaQueryWrapper<OrgCaucus> orgCaucusLambdaQueryWrapper = new LambdaQueryWrapper<>();
            orgCaucusLambdaQueryWrapper.isNull(OrgCaucus::getDeleteTime);
            orgCaucusLambdaQueryWrapper.in(OrgCaucus::getOrgCode,orgs);
            List<OrgCaucus> orgCaucuses = orgCaucusMapper.selectList(orgCaucusLambdaQueryWrapper);
            JSONArray ccp_org_caucus = this.deailJsonArray(orgCaucuses);
            if (ccp_org_caucus != null) {
                jsonObject.put("ccp_org_caucus", ccp_org_caucus);
            }
            System.out.println("获取到ccp_org_caucus数量===>"+orgCaucuses.size());

            //ccp_org_committee_elect
            LambdaQueryWrapper<OrgCommitteeElect> orgCommitteeElectLambdaQueryWrapper = new LambdaQueryWrapper<>();
            orgCommitteeElectLambdaQueryWrapper.isNull(OrgCommitteeElect::getDeleteTime);
            orgCommitteeElectLambdaQueryWrapper.in(OrgCommitteeElect::getOrgCode,orgs);
            List<OrgCommitteeElect> orgCommitteeElectList = orgCommitteeElectMapper.selectList(orgCommitteeElectLambdaQueryWrapper);
            JSONArray ccp_org_committee_elect = this.deailJsonArray(orgCommitteeElectList);
            if (ccp_org_committee_elect != null) {
                jsonObject.put("ccp_org_committee_elect", ccp_org_committee_elect);
            }
            System.out.println("获取到ccp_org_committee_elect数量===>"+orgCommitteeElectList.size());

            //ccp_org_committee
            LambdaQueryWrapper<OrgCommittee> orgCommitteeLambdaQueryWrapper = new LambdaQueryWrapper<>();
            orgCommitteeLambdaQueryWrapper.isNull(OrgCommittee::getDeleteTime);
            orgCommitteeLambdaQueryWrapper.in(OrgCommittee::getOrgCode,orgs);
            List<OrgCommittee> orgCommittees = orgCommitteeMapper.selectList(orgCommitteeLambdaQueryWrapper);
            JSONArray ccp_org_committee = this.deailJsonArray(orgCommittees);
            if (ccp_org_committee != null) {
                jsonObject.put("ccp_org_committee", ccp_org_committee);
            }
            System.out.println("获取到ccp_org_committee数量===>"+orgCommittees.size());

            //ccp_org_develop_rights
            LambdaQueryWrapper<OrgDevelopRights> orgDevelopRightsLambdaQueryWrapper = new LambdaQueryWrapper<>();
            orgDevelopRightsLambdaQueryWrapper.isNull(OrgDevelopRights::getDeleteTime);
            orgDevelopRightsLambdaQueryWrapper.in(OrgDevelopRights::getOrgCode,orgs);
            List<OrgDevelopRights> orgDevelopRights = orgDevelopRightsMapper.selectList(orgDevelopRightsLambdaQueryWrapper);
            JSONArray ccp_org_develop_rights = this.deailJsonArray(orgDevelopRights);
            if (ccp_org_develop_rights != null) {
                jsonObject.put("ccp_org_develop_rights", ccp_org_develop_rights);
            }
            System.out.println("获取到ccp_org_develop_rights数量===>"+orgDevelopRights.size());

            //ccp_org_extend
            LambdaQueryWrapper<OrgExtend> orgExtendLambdaQueryWrapper = new LambdaQueryWrapper<>();
            orgExtendLambdaQueryWrapper.isNull(OrgExtend::getDeleteTime);
            orgExtendLambdaQueryWrapper.in(OrgExtend::getCode,orgs);
            List<OrgExtend> orgExtends = orgExtendMapper.selectList(orgExtendLambdaQueryWrapper);
            JSONArray ccp_org_extend = this.deailJsonArray(orgExtends);
            if (ccp_org_extend != null) {
                jsonObject.put("ccp_org_extend", ccp_org_extend);
            }
            System.out.println("获取到ccp_org_extend数量===>"+orgExtends.size());

            //ccp_org_group
            LambdaQueryWrapper<OrgGroup> orgGroupLambdaQueryWrapper = new LambdaQueryWrapper<>();
            orgGroupLambdaQueryWrapper.isNull(OrgGroup::getDeleteTime);
            orgGroupLambdaQueryWrapper.in(OrgGroup::getOrgCode,orgs);
            List<OrgGroup> orgGroups = orgGroupMapper.selectList(orgGroupLambdaQueryWrapper);
            JSONArray ccp_org_group = this.deailJsonArray(orgGroups);
            if (ccp_org_group != null) {
                jsonObject.put("ccp_org_group", ccp_org_group);
            }
            System.out.println("获取到ccp_org_group数量===>"+orgGroups.size());

            //ccp_org_group_member
            if (ObjectUtil.isNotNull(orgGroups)&&orgGroups.size()>0){
                List<String> orgGroup = orgGroups.stream().map(OrgGroup::getCode).collect(Collectors.toList());

                LambdaQueryWrapper<OrgGroupMember> orgGroupMemberLambdaQueryWrapper = new LambdaQueryWrapper<>();
                orgGroupMemberLambdaQueryWrapper.isNull(OrgGroupMember::getDeleteTime);
                orgGroupMemberLambdaQueryWrapper.in(OrgGroupMember::getGroupCode,orgGroup);
                List<OrgGroupMember> orgGroupMembers = orgGroupMemberMapper.selectList(orgGroupMemberLambdaQueryWrapper);
                JSONArray ccp_org_group_member = this.deailJsonArray(orgGroupMembers);
                if (ccp_org_group_member != null) {
                    jsonObject.put("ccp_org_group_member", ccp_org_group_member);
                }
                System.out.println("获取到ccp_org_group_member数量===>"+orgGroupMembers.size());
            }

            //ccp_org_industry
            LambdaQueryWrapper<OrgIndustry> orgIndustryLambdaQueryWrapper = new LambdaQueryWrapper<>();
            orgIndustryLambdaQueryWrapper.isNull(OrgIndustry::getDeleteTime);
            orgIndustryLambdaQueryWrapper.in(OrgIndustry::getOrgCode,orgs);
            List<OrgIndustry> orgIndustries = orgIndustryMapper.selectList(orgIndustryLambdaQueryWrapper);
            JSONArray ccp_org_industry = this.deailJsonArray(orgIndustries);
            if (ccp_org_industry != null) {
                jsonObject.put("ccp_org_industry", ccp_org_industry);
            }
            System.out.println("获取到ccp_org_industry数量===>"+orgIndustries.size());

            //ccp_org_industry_all
            LambdaQueryWrapper<OrgIndustryAll> orgIndustryAllLambdaQueryWrapper = new LambdaQueryWrapper<>();
            orgIndustryAllLambdaQueryWrapper.isNull(OrgIndustryAll::getDeleteTime);
            orgIndustryAllLambdaQueryWrapper.in(OrgIndustryAll::getOrgCode,orgs);
            List<OrgIndustryAll> orgIndustryAllList = orgIndustryAllMapper.selectList(orgIndustryAllLambdaQueryWrapper);
            JSONArray ccp_org_industry_all = this.deailJsonArray(orgIndustryAllList);
            if (ccp_org_industry_all != null) {
                jsonObject.put("ccp_org_industry_all", ccp_org_industry_all);
            }
            System.out.println("获取到ccp_org_industry_all数量===>"+orgIndustryAllList.size());

            //ccp_org_non_public_party
            LambdaQueryWrapper<OrgNonPublicParty> orgNonPublicPartyLambdaQueryWrapper = new LambdaQueryWrapper<>();
            orgNonPublicPartyLambdaQueryWrapper.isNull(OrgNonPublicParty::getDeleteTime);
            orgNonPublicPartyLambdaQueryWrapper.in(OrgNonPublicParty::getOrgCode,orgs);
            List<OrgNonPublicParty> orgNonPublicParties = orgNonPublicPartyMapper.selectList(orgNonPublicPartyLambdaQueryWrapper);
            JSONArray ccp_org_non_public_party = this.deailJsonArray(orgNonPublicParties);
            if (ccp_org_non_public_party != null) {
                jsonObject.put("ccp_org_non_public_party", ccp_org_non_public_party);
            }
            System.out.println("获取到ccp_org_non_public_party数量===>"+orgNonPublicParties.size());

            //ccp_org_party
            LambdaQueryWrapper<OrgParty> orgPartyLambdaQueryWrapper = new LambdaQueryWrapper<>();
            orgPartyLambdaQueryWrapper.isNull(OrgParty::getDeleteTime);
            orgPartyLambdaQueryWrapper.in(OrgParty::getOrgCode,orgs);
            List<OrgParty> orgParties = orgPartyMapper.selectList(orgPartyLambdaQueryWrapper);
            JSONArray ccp_org_party = this.deailJsonArray(orgParties);
            if (ccp_org_party != null) {
                jsonObject.put("ccp_org_party", ccp_org_party);
            }
            System.out.println("获取到ccp_org_party数量===>"+orgParties.size());


            //ccp_org_party_congress_elect
            LambdaQueryWrapper<OrgPartyCongressElect> orgPartyCongressElectLambdaQueryWrapper = new LambdaQueryWrapper<>();
            orgPartyCongressElectLambdaQueryWrapper.isNull(OrgPartyCongressElect::getDeleteTime);
            orgPartyCongressElectLambdaQueryWrapper.in(OrgPartyCongressElect::getOrgCode,orgs);
            List<OrgPartyCongressElect> orgPartyCongressElects = orgPartyCongressElectMapper.selectList(orgPartyCongressElectLambdaQueryWrapper);
            JSONArray ccp_org_party_congress_elect = this.deailJsonArray(orgPartyCongressElects);
            if (ccp_org_party_congress_elect != null) {
                jsonObject.put("ccp_org_party_congress_elect", ccp_org_party_congress_elect);
            }
            System.out.println("获取到ccp_org_party_congress_elect数量===>"+orgPartyCongressElects.size());


            //ccp_org_party_congress_committee
            if (ObjectUtil.isNotNull(orgPartyCongressElects)&&orgPartyCongressElects.size()>0){
                List<String> collect = orgPartyCongressElects.stream().map(OrgPartyCongressElect::getCode).collect(Collectors.toList());
                //ccp_org_party_congress_committee_all
                LambdaQueryWrapper<OrgPartyCongressCommitteeAll> partyCongressCommitteeAllWrapper = new LambdaQueryWrapper<>();
                partyCongressCommitteeAllWrapper.isNull(OrgPartyCongressCommitteeAll::getDeleteTime);
                partyCongressCommitteeAllWrapper.in(OrgPartyCongressCommitteeAll::getElectCode, collect);
                List<OrgPartyCongressCommitteeAll> orgPartyCongressCommitteeAlls = iOrgPartyCongressCommitteeAllService.list(partyCongressCommitteeAllWrapper);
                JSONArray ccp_org_party_congress_committee_all = this.deailJsonArray(orgPartyCongressCommitteeAlls);
                if (ccp_org_party_congress_committee_all != null) {
                    jsonObject.put("ccp_org_party_congress_committee_all", ccp_org_party_congress_committee_all);
                }
                System.out.println("获取到ccp_org_party_congress_committee_all数量===>" + orgPartyCongressCommitteeAlls.size());

                LambdaQueryWrapper<OrgPartyCongressCommittee> orgPartyCongressCommitteeLambdaQueryWrapper = new LambdaQueryWrapper<>();
                orgPartyCongressCommitteeLambdaQueryWrapper.isNull(OrgPartyCongressCommittee::getDeleteTime);
                orgPartyCongressCommitteeLambdaQueryWrapper.in(OrgPartyCongressCommittee::getElectCode,collect);
                List<OrgPartyCongressCommittee> orgPartyCongressCommittees = orgPartyCongressCommitteeMapper.selectList(orgPartyCongressCommitteeLambdaQueryWrapper);
                JSONArray ccp_org_party_congress_committee = this.deailJsonArray(orgPartyCongressCommittees);
                if (ccp_org_party_congress_committee != null) {
                    jsonObject.put("ccp_org_party_congress_committee", ccp_org_party_congress_committee);
                }
                System.out.println("获取到ccp_org_party_congress_committee数量===>"+orgPartyCongressCommittees.size());
            }

            //ccp_org_recognition
            LambdaQueryWrapper<OrgRecognition> orgRecognitionLambdaQueryWrapper = new LambdaQueryWrapper<>();
            orgRecognitionLambdaQueryWrapper.isNull(OrgRecognition::getDeleteTime);
            orgRecognitionLambdaQueryWrapper.in(OrgRecognition::getOrgCode,orgs);
            List<OrgRecognition> orgRecognitions = orgRecognitionMapper.selectList(orgRecognitionLambdaQueryWrapper);
            JSONArray ccp_org_recognition = this.deailJsonArray(orgRecognitions);
            if (ccp_org_recognition != null) {
                jsonObject.put("ccp_org_recognition", ccp_org_recognition);
            }
            System.out.println("获取到ccp_org_recognition数量===>"+orgRecognitions.size());

            //ccp_org_recognition_all
            LambdaQueryWrapper<OrgRecognitionAll> orgRecognitionAllLambdaQueryWrapper = new LambdaQueryWrapper<>();
            orgRecognitionAllLambdaQueryWrapper.isNull(OrgRecognitionAll::getDeleteTime);
            orgRecognitionAllLambdaQueryWrapper.in(OrgRecognitionAll::getOrgCode,orgs);
            List<OrgRecognitionAll> orgRecognitionAlls = orgRecognitionAllMapper.selectList(orgRecognitionAllLambdaQueryWrapper);
            JSONArray ccp_org_recognition_all = this.deailJsonArray(orgRecognitionAlls);
            if (ccp_org_recognition_all != null) {
                jsonObject.put("ccp_org_recognition_all", ccp_org_recognition_all);
            }
            System.out.println("获取到ccp_org_recognition_all数量===>"+ccp_org_recognition_all.size());




            //ccp_org_recognition_data
            if (ObjectUtil.isNotNull(orgRecognitions)&&orgRecognitions.size()>0){
                List<String> collect = orgRecognitions.stream().map(OrgRecognition::getCode).collect(Collectors.toList());
                LambdaQueryWrapper<OrgRecognitionData> orgRecognitionDataLambdaQueryWrapper = new LambdaQueryWrapper<>();
                orgRecognitionDataLambdaQueryWrapper.isNull(OrgRecognitionData::getDeleteTime);
                orgRecognitionDataLambdaQueryWrapper.in(OrgRecognitionData::getRecognitionCode,collect);
                List<OrgRecognitionData> orgRecognitionData = orgRecognitionDataMapper.selectList(orgRecognitionDataLambdaQueryWrapper);
                JSONArray ccp_org_recognition_data = this.deailJsonArray(orgRecognitionData);
                if (ccp_org_recognition_data != null) {
                    jsonObject.put("ccp_org_recognition_data", ccp_org_recognition_data);
                }
                System.out.println("获取到ccp_org_recognition_data数量===>"+orgRecognitionData.size());
            }

            //ccp_org_recognition_situation
            LambdaQueryWrapper<OrgRecognitionSituation> orgRecognitionSituationLambdaQueryWrapper = new LambdaQueryWrapper<>();
            orgRecognitionSituationLambdaQueryWrapper.isNull(OrgRecognitionSituation::getDeleteTime);
            orgRecognitionSituationLambdaQueryWrapper.in(OrgRecognitionSituation::getOrgCode,orgs);
            List<OrgRecognitionSituation> orgRecognitionSituations = orgRecognitionSituationMapper.selectList(orgRecognitionSituationLambdaQueryWrapper);
            JSONArray ccp_org_recognition_situation = this.deailJsonArray(orgRecognitionSituations);
            if (ccp_org_recognition_situation != null) {
                jsonObject.put("ccp_org_recognition_situation", ccp_org_recognition_situation);
            }
            System.out.println("获取到ccp_org_recognition_situation数量===>"+orgRecognitionSituations.size());

            //ccp_org_appraisal
            LambdaQueryWrapper<OrgAppraisal> orgAppraisalLambdaQueryWrapper = new LambdaQueryWrapper<>();
            orgAppraisalLambdaQueryWrapper.isNull(OrgAppraisal::getDeleteTime);
            orgAppraisalLambdaQueryWrapper.in(OrgAppraisal::getOrgCode,orgs);
            List<OrgAppraisal> orgAppraisals = orgAppraisalMapper.selectList(orgAppraisalLambdaQueryWrapper);
            JSONArray ccp_org_appraisal = this.deailJsonArray(orgAppraisals);
            if (ccp_org_appraisal != null) {
                jsonObject.put("ccp_org_appraisal", ccp_org_appraisal);
            }
            System.out.println("获取到ccp_org_appraisal数量===>"+orgAppraisals.size());

            //ccp_org_reviewers
            if (ObjectUtil.isNotNull(orgAppraisals)&&orgAppraisals.size()>0){
                List<String> collect = orgAppraisals.stream().map(OrgAppraisal::getCode).collect(Collectors.toList());
                LambdaQueryWrapper<OrgReviewers> orgRecognitionDataLambdaQueryWrapper = new LambdaQueryWrapper<>();
                orgRecognitionDataLambdaQueryWrapper.isNull(OrgReviewers::getDeleteTime);
                orgRecognitionDataLambdaQueryWrapper.in(OrgReviewers::getAppraisalCode,collect);
                List<OrgReviewers> orgReviewers = orgReviewersMapper.selectList(orgRecognitionDataLambdaQueryWrapper);
                JSONArray ccp_org_reviewers = this.deailJsonArray(orgReviewers);
                if (ccp_org_reviewers != null) {
                    jsonObject.put("ccp_org_reviewers", ccp_org_reviewers);
                }
                System.out.println("获取到ccp_org_reviewers数量===>"+orgReviewers.size());
            }

            //ccp_org_reward
            LambdaQueryWrapper<OrgReward> orgRewardLambdaQueryWrapper = new LambdaQueryWrapper<>();
            orgRewardLambdaQueryWrapper.isNull(OrgReward::getDeleteTime);
            orgRewardLambdaQueryWrapper.in(OrgReward::getOrgCode,orgs);
            List<OrgReward> orgRewards = orgRewardMapper.selectList(orgRewardLambdaQueryWrapper);
            JSONArray ccp_org_reward = this.deailJsonArray(orgRewards);
            if (ccp_org_reward != null) {
                jsonObject.put("ccp_org_reward", ccp_org_reward);
            }
            System.out.println("获取到ccp_org_reward数量===>"+orgRewards.size());

            //ccp_org_slack
            LambdaQueryWrapper<OrgSlack> orgSlackLambdaQueryWrapper = new LambdaQueryWrapper<>();
            orgSlackLambdaQueryWrapper.isNull(OrgSlack::getDeleteTime);
            orgSlackLambdaQueryWrapper.in(OrgSlack::getOrgCode,orgs);
            List<OrgSlack> orgSlacks = orgSlackMapper.selectList(orgSlackLambdaQueryWrapper);
            JSONArray ccp_org_slack = this.deailJsonArray(orgSlacks);
            if (ccp_org_slack != null) {
                jsonObject.put("ccp_org_slack", ccp_org_slack);
            }
            System.out.println("获取到ccp_org_slack数量===>"+orgSlacks.size());

            //ccp_org_slack_all
            LambdaQueryWrapper<OrgSlackAll> orgSlackAllLambdaQueryWrapper = new LambdaQueryWrapper<>();
            orgSlackAllLambdaQueryWrapper.isNull(OrgSlackAll::getDeleteTime);
            orgSlackAllLambdaQueryWrapper.in(OrgSlackAll::getOrgCode,orgs);
            List<OrgSlackAll> orgSlackAlls = orgSlackAllMapper.selectList(orgSlackAllLambdaQueryWrapper);
            JSONArray ccp_org_slack_all = this.deailJsonArray(orgSlackAlls);
            if (ccp_org_slack_all != null) {
                jsonObject.put("ccp_org_slack_all", ccp_org_slack_all);
            }
            System.out.println("获取到ccp_org_slack数量===>"+orgSlackAlls.size());


            //ccp_org_slack_rectification
            if (ObjectUtil.isNotNull(orgSlacks)&&orgSlacks.size()>0){
                List<String> collect = orgSlacks.stream().map(OrgSlack::getCode).collect(Collectors.toList());
                LambdaQueryWrapper<OrgSlackRectification> orgRecognitionDataLambdaQueryWrapper = new LambdaQueryWrapper<>();
                orgRecognitionDataLambdaQueryWrapper.in(OrgSlackRectification::getSlackCode,collect);
                List<OrgSlackRectification> orgSlackRectifications = orgSlackRectificationMapper.selectList(orgRecognitionDataLambdaQueryWrapper);
                JSONArray ccp_org_slack_rectification = this.deailJsonArray(orgSlackRectifications);
                if (ccp_org_slack_rectification != null) {
                    jsonObject.put("ccp_org_slack_rectification", ccp_org_slack_rectification);
                }
                System.out.println("获取到ccp_org_slack_rectification数量===>"+orgSlackRectifications.size());
            }

            //ccp_org_special_nature
            LambdaQueryWrapper<OrgSpecialNature> specialNatureLambdaQueryWrapper = new LambdaQueryWrapper<>();
            specialNatureLambdaQueryWrapper.isNull(OrgSpecialNature::getDeleteTime);
            specialNatureLambdaQueryWrapper.in(OrgSpecialNature::getOrgCode,orgs);
            List<OrgSpecialNature> orgSpecialNatures = orgSpecialNatureMapper.selectList(specialNatureLambdaQueryWrapper);
            JSONArray ccp_org_special_nature = this.deailJsonArray(orgSpecialNatures);
            if (ccp_org_special_nature != null) {
                jsonObject.put("ccp_org_special_nature", ccp_org_special_nature);
            }
            System.out.println("获取到ccp_org_special_nature数量===>"+orgSpecialNatures.size());

            //ccp_org_township_leadership
            LambdaQueryWrapper<OrgTownshipLeadership> orgTownshipLeadershipLambdaQueryWrapper = new LambdaQueryWrapper<>();
            orgTownshipLeadershipLambdaQueryWrapper.isNull(OrgTownshipLeadership::getDeleteTime);
            orgTownshipLeadershipLambdaQueryWrapper.in(OrgTownshipLeadership::getOrgCode,orgs);
            List<OrgTownshipLeadership> orgTownshipLeaderships = orgTownshipLeadershipMapper.selectList(orgTownshipLeadershipLambdaQueryWrapper);
            JSONArray ccp_org_township_leadership = this.deailJsonArray(orgTownshipLeaderships);
            if (ccp_org_township_leadership != null) {
                jsonObject.put("ccp_org_township_leadership", ccp_org_township_leadership);
            }
            System.out.println("获取到ccp_org_township_leadership数量===>"+orgTownshipLeaderships.size());
            //ccp_unit_org_linked
        }

        //ccp_mem
        LambdaQueryWrapper<Mem> memLambdaQueryWrapper = new LambdaQueryWrapper<>();
        memLambdaQueryWrapper.likeRight(Mem::getMemOrgCode,srcOrgCode);
        memLambdaQueryWrapper.isNull(Mem::getDeleteTime);
        List<Mem> memList = memMapper.selectList(memLambdaQueryWrapper);
        JSONArray ccp_mem = this.deailJsonArray(memList);
        if (ccp_mem != null) {
            jsonObject.put("ccp_mem", ccp_mem);
        }
        System.out.println("获取到ccp_mem数量===>"+memList.size());

        if (ObjectUtil.isNotNull(memList)&&memList.size()>0){
            List<String> collect = memList.stream().map(Mem::getCode).collect(Collectors.toList());
            LambdaQueryWrapper<MemAllInfo> memAllInfoLambdaQueryWrapper = new LambdaQueryWrapper<>();
            memAllInfoLambdaQueryWrapper.in(MemAllInfo::getCode,collect);
            List<MemAllInfo> allInfoList = memAllInfoMapper.selectList(memAllInfoLambdaQueryWrapper);
            JSONArray ccp_mem_all = this.deailJsonArray(allInfoList);
            if (ccp_mem_all != null) {
                jsonObject.put("ccp_mem_all", ccp_mem_all);
            }
            System.out.println("获取到ccp_mem_all数量===>"+allInfoList.size());

            //ccp_mem_reward_all
            LambdaQueryWrapper<MemRewardAll> memRewardAllLambdaQueryWrapper = new LambdaQueryWrapper<>();
            memRewardAllLambdaQueryWrapper.in(MemRewardAll::getMemCode,collect);
            memRewardAllLambdaQueryWrapper.isNull(MemRewardAll::getDeleteTime);
            List<MemRewardAll> memRewardAllList = memRewardAllMapper.selectList(memRewardAllLambdaQueryWrapper);
            JSONArray ccp_mem_reward_all = this.deailJsonArray(memRewardAllList);
            if (ccp_mem_reward_all != null) {
                jsonObject.put("ccp_mem_reward_all", ccp_mem_reward_all);
            }
            System.out.println("获取到ccp_mem_reward_all数量===>"+ccp_mem_reward_all.size());


        }


        //ccp_democratic_review_mem
        LambdaQueryWrapper<DemocraticReviewMem> democraticReviewMemLambdaQueryWrapper = new LambdaQueryWrapper<>();
        democraticReviewMemLambdaQueryWrapper.likeRight(DemocraticReviewMem::getReviewMemOrgCode,srcOrgCode);
        democraticReviewMemLambdaQueryWrapper.isNull(DemocraticReviewMem::getDeleteTime);
        List<DemocraticReviewMem> democraticReviewMems = democraticReviewMemMapper.selectList(democraticReviewMemLambdaQueryWrapper);
        JSONArray ccp_democratic_review_mem = this.deailJsonArray(democraticReviewMems);
        if (ccp_democratic_review_mem != null) {
            jsonObject.put("ccp_democratic_review_mem", ccp_democratic_review_mem);
        }
        System.out.println("获取到ccp_democratic_review_mem数量===>"+democraticReviewMems.size());

        //ccp_mem_abroad
        LambdaQueryWrapper<MemAbroad> memAbroadLambdaQueryWrapper = new LambdaQueryWrapper<>();
        memAbroadLambdaQueryWrapper.likeRight(MemAbroad::getAbroadOrgCode,srcOrgCode);
        memAbroadLambdaQueryWrapper.isNull(MemAbroad::getDeleteTime);
        List<MemAbroad> memAbroads = memAbroadMapper.selectList(memAbroadLambdaQueryWrapper);
        JSONArray ccp_mem_abroad = this.deailJsonArray(memAbroads);
        if (ccp_mem_abroad != null) {
            jsonObject.put("ccp_mem_abroad", ccp_mem_abroad);
        }
        System.out.println("获取到ccp_mem_abroad数量===>"+memAbroads.size());
        //ccp_mem_analysis_test

        //ccp_mem_develop
        LambdaQueryWrapper<MemDevelop> memDevelopLambdaQueryWrapper = new LambdaQueryWrapper<>();
        memDevelopLambdaQueryWrapper.likeRight(MemDevelop::getDevelopOrgCode,srcOrgCode);
        memDevelopLambdaQueryWrapper.isNull(MemDevelop::getDeleteTime);
        List<MemDevelop> memDevelops = memDevelopMapper.selectList(memDevelopLambdaQueryWrapper);
        JSONArray ccp_mem_develop = this.deailJsonArray(memDevelops);
        if (ccp_mem_develop != null) {
            jsonObject.put("ccp_mem_develop", ccp_mem_develop);
        }
        //ccp_mem_develop_all
        System.out.println("获取到ccp_mem_develop数量===>"+memDevelops.size());
        if (ObjectUtil.isNotNull(memDevelops)&&memDevelops.size()>0){
            List<String> collect = memDevelops.stream().map(MemDevelop::getCode).collect(Collectors.toList());
            LambdaQueryWrapper<MemDevelopAll> memDevelopAllLambdaQueryWrapper = new LambdaQueryWrapper<>();
            memDevelopAllLambdaQueryWrapper.isNull(MemDevelopAll::getDeleteTime);
            memDevelopAllLambdaQueryWrapper.in(MemDevelopAll::getCode,collect);
            List<MemDevelopAll> memDevelopAlls = memDevelopAllMapper.selectList(memDevelopAllLambdaQueryWrapper);
            JSONArray ccp_mem_develop_operation = this.deailJsonArray(memDevelopAlls);
            if (ccp_mem_develop_operation != null) {
                jsonObject.put("ccp_mem_develop_all", ccp_mem_develop_operation);
            }
            System.out.println("获取到ccp_mem_develop_all数量===>"+memDevelopAlls.size());
        }


        //ccp_mem_develop_operation
        if (ObjectUtil.isNotNull(memDevelops)&&memDevelops.size()>0){
            List<String> collect = memDevelops.stream().map(MemDevelop::getCode).collect(Collectors.toList());
            LambdaQueryWrapper<MemDevelopOperation> memDevelopOperationLambdaQueryWrapper = new LambdaQueryWrapper<>();
            memDevelopOperationLambdaQueryWrapper.isNull(MemDevelopOperation::getDeleteTime);
            memDevelopOperationLambdaQueryWrapper.in(MemDevelopOperation::getDevelopCode,collect);
            List<MemDevelopOperation> memDevelopOperations = memDevelopOperationMapper.selectList(memDevelopOperationLambdaQueryWrapper);
            JSONArray ccp_mem_develop_operation = this.deailJsonArray(memDevelopOperations);
            if (ccp_mem_develop_operation != null) {
                jsonObject.put("ccp_mem_develop_operation", ccp_mem_develop_operation);
            }
            System.out.println("获取到ccp_mem_develop_operation数量===>"+memDevelopOperations.size());
        }
        //ccp_mem_difficult
        LambdaQueryWrapper<MemDifficult> memDifficultLambdaQueryWrapper = new LambdaQueryWrapper<>();
        memDifficultLambdaQueryWrapper.likeRight(MemDifficult::getDiffOrgCode,srcOrgCode);
        memDifficultLambdaQueryWrapper.isNull(MemDifficult::getDeleteTime);
        List<MemDifficult> memDifficults = memDifficultMapper.selectList(memDifficultLambdaQueryWrapper);
        JSONArray ccp_mem_difficult = this.deailJsonArray(memDifficults);
        if (ccp_mem_difficult != null) {
            jsonObject.put("ccp_mem_difficult", ccp_mem_difficult);
        }
        System.out.println("获取到ccp_mem_difficult数量===>"+memDifficults.size());

        //ccp_mem_extend
        LambdaQueryWrapper<MemExtend> memExtendLambdaQueryWrapper = new LambdaQueryWrapper<>();
        memExtendLambdaQueryWrapper.likeRight(MemExtend::getExtendOrgCode,srcOrgCode);
        List<MemExtend> memExtends = memExtendMapper.selectList(memExtendLambdaQueryWrapper);
        JSONArray ccp_mem_extend = this.deailJsonArray(memExtends);
        if (ccp_mem_extend != null) {
            jsonObject.put("ccp_mem_extend", ccp_mem_extend);
        }
        System.out.println("获取到ccp_mem_extend数量===>"+memExtends.size());

        //ccp_mem_flow
        LambdaQueryWrapper<MemFlow> memFlowLambdaQueryWrapper = new LambdaQueryWrapper<>();
        memFlowLambdaQueryWrapper.likeRight(MemFlow::getMemOrgOrgCode,srcOrgCode);
        memFlowLambdaQueryWrapper.isNull(MemFlow::getDeleteTime);
        List<MemFlow> memFlows = memFlowMapper.selectList(memFlowLambdaQueryWrapper);
        JSONArray ccp_mem_flow = this.deailJsonArray(memFlows);
        if (ccp_mem_flow != null) {
            jsonObject.put("ccp_mem_flow", ccp_mem_flow);
        }
        System.out.println("获取到ccp_mem_flow数量===>"+memFlows.size());

        //ccp_mem_flow_all
        if (ObjectUtil.isNotNull(memFlows)&&memFlows.size()>0){
            List<String> collect = memFlows.stream().map(MemFlow::getCode).collect(Collectors.toList());
            LambdaQueryWrapper<MemFlowAll> memFlowAllLambdaQueryWrapper = new LambdaQueryWrapper<>();
            memFlowAllLambdaQueryWrapper.isNull(MemFlowAll::getDeleteTime);
            memFlowAllLambdaQueryWrapper.in(MemFlowAll::getCode,collect);
            List<MemFlowAll> memFlowAllList = memFlowAllMapper.selectList(memFlowAllLambdaQueryWrapper);
            JSONArray ccp_mem_flow_all = this.deailJsonArray(memFlowAllList);
            if (ccp_mem_flow_all != null) {
                jsonObject.put("ccp_mem_flow_all", ccp_mem_flow_all);
            }
            System.out.println("获取到ccp_mem_flow_all数量===>"+ccp_mem_flow_all.size());
        }

        //ccp_mem_flow_log
        //ccp_mem_log
        //ccp_mem_logic
        //ccp_mem_many

        //ccp_mem_reward
        LambdaQueryWrapper<MemReward> memRewardLambdaQueryWrapper = new LambdaQueryWrapper<>();
        memRewardLambdaQueryWrapper.likeRight(MemReward::getRewardOrgCode,srcOrgCode);
        memRewardLambdaQueryWrapper.isNull(MemReward::getDeleteTime);
        List<MemReward> rewards = memRewardMapper.selectList(memRewardLambdaQueryWrapper);
        JSONArray ccp_mem_reward = this.deailJsonArray(rewards);
        if (ccp_mem_reward != null) {
            jsonObject.put("ccp_mem_reward", ccp_mem_reward);
        }
        System.out.println("获取到ccp_mem_reward数量===>"+rewards.size());




        //ccp_mem_train
        LambdaQueryWrapper<MemTrain> memTrainLambdaQueryWrapper = new LambdaQueryWrapper<>();
        memTrainLambdaQueryWrapper.likeRight(MemTrain::getOrgOrgCode,srcOrgCode);
        memTrainLambdaQueryWrapper.isNull(MemTrain::getDeleteTime);
        List<MemTrain> memTrains = memTrainMapper.selectList(memTrainLambdaQueryWrapper);
        JSONArray ccp_mem_train = this.deailJsonArray(memTrains);
        if (ccp_mem_train != null) {
            jsonObject.put("ccp_mem_train", ccp_mem_train);
        }
        System.out.println("获取到ccp_mem_train数量===>"+memTrains.size());
        //ccp_transfer_effect_mems

        //处理用户相关问题
        //sys_user
        LambdaQueryWrapper<User> userLambdaQueryWrapper = new LambdaQueryWrapper<>();
        userLambdaQueryWrapper.likeRight(User::getOrgCode,srcOrgCode);
        userLambdaQueryWrapper.eq(User::getIsDelete,CommonConstant.ZERO_INT);
        List<User> users = userMapper.selectList(userLambdaQueryWrapper);
        JSONArray sys_user = this.deailJsonArray(users);
        if (sys_user != null) {
            jsonObject.put("sys_user", sys_user);
        }
        System.out.println("获取到sys_user数量===>"+sys_user.size());
        //sys_user_role_permission
        if (ObjectUtil.isNotNull(users)&&users.size()>0){
            List<String> collect = users.stream().map(User::getId).collect(Collectors.toList());
            LambdaQueryWrapper<UserRolePermission> permissionLambdaQueryWrapper = new LambdaQueryWrapper<>();
            permissionLambdaQueryWrapper.in(UserRolePermission::getUserId,collect);
            List<UserRolePermission> userRolePermissions = userRolePermissionMapper.selectList(permissionLambdaQueryWrapper);
            JSONArray sys_user_role_permission = this.deailJsonArray(userRolePermissions);
            if (sys_user_role_permission != null) {
                jsonObject.put("sys_user_role_permission", sys_user_role_permission);
            }
            System.out.println("获取到sys_user_role_permission数量===>"+sys_user_role_permission.size());
        }

        //ccp_develop_step_log_all
        LambdaQueryWrapper<DevelopStepLogAll> stepLogAllLambdaQueryWrapper = new LambdaQueryWrapper<>();
        stepLogAllLambdaQueryWrapper.likeRight(DevelopStepLogAll::getLogOrgCode, srcOrgCode);
        stepLogAllLambdaQueryWrapper.isNull(DevelopStepLogAll::getDeleteTime);
        List<DevelopStepLogAll> developStepLogAllList = iDevelopStepLogAllService.list(stepLogAllLambdaQueryWrapper);
        JSONArray ccp_develop_step_log_all = this.deailJsonArray(developStepLogAllList);
        if (ccp_develop_step_log_all != null) {
            jsonObject.put("ccp_develop_step_log_all", ccp_develop_step_log_all);
        }
        System.out.println("获取到ccp_develop_step_log_all数量===>" + developStepLogAllList.size());
        //ccp_develop_step_log
        LambdaQueryWrapper<DevelopStepLog> developStepLogLambdaQueryWrapper = new LambdaQueryWrapper<>();
        developStepLogLambdaQueryWrapper.likeRight(DevelopStepLog::getLogOrgCode,srcOrgCode);
        developStepLogLambdaQueryWrapper.isNull(DevelopStepLog::getDeleteTime);
        List<DevelopStepLog> developStepLogs = developStepLogMapper.selectList(developStepLogLambdaQueryWrapper);
        JSONArray ccp_develop_step_log = this.deailJsonArray(developStepLogs);
        if (ccp_develop_step_log != null) {
            jsonObject.put("ccp_develop_step_log", ccp_develop_step_log);
        }
        System.out.println("获取到ccp_develop_step_log数量===>"+developStepLogs.size());


        // todo 2025-03-19 添加该处是因为遵义党员、入党申请人、积极份子、发展对象需要走流程
        // 获取档案唯一码
        Set<String> digLotNoSet = new HashSet<>();
        if (CollUtil.isNotEmpty(memList)) {
            Set<String> collect = memList.stream().map(Mem::getDigitalLotNo).filter(StrUtil::isNotBlank).collect(Collectors.toSet());
            if (CollUtil.isNotEmpty(collect)) {
                digLotNoSet.addAll(collect);
            }
        }
        if (CollUtil.isNotEmpty(memDevelops)) {
            Set<String> collect = memDevelops.stream().map(MemDevelop::getDigitalLotNo).filter(StrUtil::isNotBlank).collect(Collectors.toSet());
            if (CollUtil.isNotEmpty(collect)) {
                digLotNoSet.addAll(collect);
            }
        }
        // 档案唯一码有值
        if (CollUtil.isNotEmpty(digLotNoSet)) {
            // ccp_mem_develop_process 流程信息 todo: 流程先不用传输，接收走一个固定流程节点，等确定好后再来
//            LambdaQueryWrapper<MemDevelopProcess> memDevelopProcessLambdaQueryWrapper = new LambdaQueryWrapper<>();
//            memDevelopProcessLambdaQueryWrapper.in(MemDevelopProcess::getDigitalLotNo, digLotNoSet)
//                    .isNull(MemDevelopProcess::getDeleteTime);
//            List<MemDevelopProcess> memDevelopProcessList = memDevelopProcessMapper.selectList(memDevelopProcessLambdaQueryWrapper);
//            JSONArray ccp_mem_develop_process = this.deailJsonArray(memDevelopProcessList);
//            if (ccp_mem_develop_process != null) {
//                jsonObject.put("ccp_mem_develop_process", ccp_mem_develop_process);
//            }
//            System.out.println("获取到ccp_mem_develop_process数量===>" + memDevelopProcessList.size());

            // ccp_mem_digital 档案
            LambdaQueryWrapper<MemDigital> memDigitalLambdaQueryWrapper = new LambdaQueryWrapper<>();
            memDigitalLambdaQueryWrapper.in(MemDigital::getDigitalLotNo, digLotNoSet)
                    .isNull(MemDigital::getDeleteTime);
            List<MemDigital> memDigitalList = memDigitalMapper.selectList(memDigitalLambdaQueryWrapper);
            JSONArray ccp_mem_digital = this.deailJsonArray(memDigitalList);
            if (ccp_mem_digital != null) {
                jsonObject.put("ccp_mem_digital", ccp_mem_digital);
            }
            System.out.println("获取到ccp_mem_digital数量===>" + memDigitalList.size());

            // ccp_mem_digital_operation_log 档案日志
            LambdaQueryWrapper<MemDigitalOperationLog> memDigitalOperationLogLambdaQueryWrapper = new LambdaQueryWrapper<>();
            memDigitalOperationLogLambdaQueryWrapper.in(MemDigitalOperationLog::getDigitalLotNo, digLotNoSet)
                    .isNull(MemDigitalOperationLog::getDeleteTime);
            List<MemDigitalOperationLog> memDigitalOperationLogList = memDigitalOperationLogMapper.selectList(memDigitalOperationLogLambdaQueryWrapper);
            JSONArray ccp_mem_digital_operation_log = this.deailJsonArray(memDigitalOperationLogList);
            if (ccp_mem_digital_operation_log != null) {
                jsonObject.put("ccp_mem_digital_operation_log", ccp_mem_digital_operation_log);
            }
            System.out.println("获取到ccp_mem_digital_operation_log数量===>" + memDigitalOperationLogList.size());

        }

        // TODO: 2021/12/27 如果后面增加其他业务，那么需要记着deailEntireData这个放里面也要去做回调删除以及transferCrossNode这个方法里面去做接收!!!!!!!!!
        // TODO: 2021/12/27 如果后面增加其他业务，那么需要记着deailEntireData这个放里面也要去做回调删除以及transferCrossNode这个方法里面去做接收!!!!!!!!!
        // TODO: 2021/12/27 如果后面增加其他业务，那么需要记着deailEntireData这个放里面也要去做回调删除以及transferCrossNode这个方法里面去做接收!!!!!!!!!
        // TODO: 2021/12/27 如果后面增加其他业务，那么需要记着deailEntireData这个放里面也要去做回调删除以及transferCrossNode这个方法里面去做接收!!!!!!!!!
        // TODO: 2021/12/27 如果后面增加其他业务，那么需要记着deailEntireData这个放里面也要去做回调删除以及transferCrossNode这个方法里面去做接收!!!!!!!!!

        //        2.集体经济情况
        //        3.村社区后备干部情况
        //        5.软弱涣散组织
        //        6.行业党组织
        //        7.党组性质党委
        //        8.党员管理
        //        9.历史党员
        //        10.入党申请人
        //        11.积极分子
        //        12.发展对象
        //        13.党员流出
        //        14.党员流入
        //        15.流动历史
        //        16.党代表
        //        17.关系转出
        //        18.关系转入
        //        19.历史转出
        //        20.历史转入
        //        21.系统管理
        //        22.交换区单位更新
        //        23.交换区党组织更新
    }

    public JSONArray  deailJsonArray(List list){
        JSONArray jsonArray =new JSONArray();
        list.forEach(data-> jsonArray.add(JSONObject.parseObject(JSONObject.toJSONString(data))));
        return jsonArray;
    }


    private void addOrgTransferOutData(TransferRecordDTO data, UserTicket userTicket) {
        if (StrUtil.isNotEmpty(data.getSrcOrgId()) && StrUtil.isNotEmpty(data.getTargetOrgId())) {
            TransferRecord transferRecord = new TransferRecord();
            BeanUtils.copyProperties(data, transferRecord);
            transferRecord.setCreateTime(new Date());
            transferRecord.setUpdateTime(new Date());
            transferRecord.setStatus(TransferRecordConstant.TRANSFERING);
            transferRecord.setUserId(userTicket.getUser().getId());
            transferRecord.setName(data.getSrcOrgName());
            transferRecord.setCommonOrgId("");
            transferRecord.setCommonOrgName("");
            transferRecord.setSrcOrgRelationAsList(new ArrayList<>());
            transferRecord.setTargetOrgRelationAsList(new ArrayList<>());
            transferRecord.setOutType(data.getType());
            transferRecord.setInType("");
            this.save(transferRecord);
        }
    }

    /**
     * 整建制人员转入
     * @param data 前端传递的参数
     */
    private void addZjZUpload(TransferRecordDTO data) throws Exception{
        List<Mem> result = new ArrayList<>();

        List<TransferRecordUploadDTO> uploadList = data.getUploadList();
        if(cn.hutool.core.collection.CollectionUtil.isNotEmpty(uploadList)){
            for (TransferRecordUploadDTO transferRecordUploadDTO : uploadList) {
                String excelPath = transferRecordUploadDTO.getExcelPath();
                String basePath = ResourceUtils.getURL(ResourceUtils.CLASSPATH_URL_PREFIX).getPath();
                //todo::读取文件转化成 mem对象保存
                File excelFile = new File(basePath+excelPath);
                if(FileUtil.exist(excelFile)) {
                    List<Mem> excelMem = this.getExcelContent(transferRecordUploadDTO, excelFile);
                    if(cn.hutool.core.collection.CollectionUtil.isNotEmpty(excelMem)){
                        result.addAll(excelMem);
                    }
                }
            }
        }

        if(cn.hutool.core.collection.CollectionUtil.isNotEmpty(result)) {
            memService.saveBatch(result);
        }
    }

    /**
     * 讲上传的excel文件的内容转换为 memList 保存
     * @param transferRecordUploadDTO 上传参数
     * @param excelFile Excel 文件
     * @return
     */
    private List<Mem> getExcelContent(TransferRecordUploadDTO transferRecordUploadDTO, File excelFile) throws Exception {
        List<Mem> result = new ArrayList<>();
        ExcelImportUtil<MemExportDTO> exportDTOExcelImportUtil = new ExcelImportUtil<>(MemExportDTO.class);
        FileInputStream fileInputStream =new FileInputStream(excelFile);
        List<MemExportDTO> memExportDTOS = exportDTOExcelImportUtil.readExcel(fileInputStream, 1, 0);
        Map<String, String> d49Map = this.processDic(dictService.getDic("dict_d49"));
        Map<String, String> d48Map = this.processDic(dictService.getDic("dict_d48"));
        Map<String, String> d06Map = this.processDic(dictService.getDic("dict_d06"));
        Map<String, String> d07Map = this.processDic(dictService.getDic("dict_d07"));
        Map<String, String> d09Map = this.processDic(dictService.getDic("dict_d09"));
        Map<String, String> d08Map = this.processDic(dictService.getDic("dict_d08"));
        Map<String, String> d19Map = this.processDic(dictService.getDic("dict_d19"));
        Map<String, String> d20Map = this.processDic(dictService.getDic("dict_d20"));
        Map<String, String> d21Map = this.processDic(dictService.getDic("dict_d21"));
        Map<String, String> d11Map = this.processDic(dictService.getDic("dict_d11"));
        Map<String, String> d27Map = this.processDic(dictService.getDic("dict_d27"));
        Map<String, String> d28Map = this.processDic(dictService.getDic("dict_d28"));
        Map<String, String> d18Map = this.processDic(dictService.getDic("dict_d18"));
        for (MemExportDTO memExportDTO : memExportDTOS) {
            Mem mem = new Mem();
            mem.setCode(StrUtil.uuid().toUpperCase());
            mem.setEsId(CodeUtil.getEsId());
            mem.setName(memExportDTO.getName());
            mem.setPinyin(PinyinUtil.getPinyin(memExportDTO.getName()));
            mem.setIdcard(memExportDTO.getIdcard());
            mem.setMemOrgCode(transferRecordUploadDTO.getOrgCode());
            mem.setOrgZbCode(transferRecordUploadDTO.getZbCode());
            mem.setOrgCode(transferRecordUploadDTO.getCode());
            mem.setPhone(memExportDTO.getPhone());
            mem.setOtherTel(memExportDTO.getOtherTel());
            if(StrUtil.isNotEmpty(memExportDTO.getJoinOrgDate())) {
                mem.setJoinOrgDate(DateUtil.parse(memExportDTO.getJoinOrgDate(), "yyyy-MM-dd"));
            }else {
                mem.setJoinOrgDate(null);
            }


            if(StrUtil.isNotEmpty(memExportDTO.getBirthday())) {
                mem.setBirthday(DateUtil.parse(memExportDTO.getBirthday(), "yyyy-MM-dd"));
            }else {
                mem.setBirthday(null);
            }

            if(StrUtil.equals(memExportDTO.getSexName(),"男")){
                mem.setSexCode("1");
                mem.setSexName("男");
            } else {
                mem.setSexCode("0");
                mem.setSexName("女");
            }

            if(StrUtil.isNotEmpty(memExportDTO.getActiveDate())) {
                mem.setActiveDate(DateUtil.parse(memExportDTO.getActiveDate(), "yyyy-MM-dd"));
            }else {
                mem.setActiveDate(null);
            }

            if(StrUtil.isNotEmpty(memExportDTO.getFullMemberDate())) {
                mem.setFullMemberDate(DateUtil.parse(memExportDTO.getFullMemberDate(), "yyyy-MM-dd"));
            }else {
                mem.setFullMemberDate(null);
            }

            if(StrUtil.isNotEmpty(memExportDTO.getD49Name())){
                mem.setD49Name(memExportDTO.getD49Name());
                mem.setD49Code(d49Map.getOrDefault(memExportDTO.getD49Name(),""));
            } else {
                mem.setD49Name(null);
                mem.setD49Code(null);
            }

            mem.setHomeAddress(memExportDTO.getHomeAddress());

            if(StrUtil.isNotEmpty(memExportDTO.getD48Name())){
                mem.setD48Name(memExportDTO.getD48Name());
                mem.setD48Code(d48Map.getOrDefault(memExportDTO.getD48Name(),""));
            } else {
                mem.setD48Name(null);
                mem.setD48Code(null);
            }

            if(StrUtil.isNotEmpty(memExportDTO.getD06Name())){
                mem.setD06Name(memExportDTO.getD06Name());
                mem.setD06Code(d06Map.getOrDefault(memExportDTO.getD06Name(),""));
            } else {
                mem.setD06Name(null);
                mem.setD06Code(null);
            }

            if(StrUtil.isNotEmpty(memExportDTO.getD07Name())){
                mem.setD07Name(memExportDTO.getD07Name());
                mem.setD07Code(d07Map.getOrDefault(memExportDTO.getD07Name(),""));
            } else {
                mem.setD07Name(null);
                mem.setD07Code(null);
            }

            mem.setIsDispatch(StrUtil.equals(memExportDTO.getIsDispatch(),"是")?1:0);
            if(StrUtil.isNotEmpty(memExportDTO.getD09Name())){
                mem.setD09Name(memExportDTO.getD09Name());
                mem.setD09Code(d09Map.getOrDefault(memExportDTO.getD09Name(),""));
            } else {
                mem.setD09Name(null);
                mem.setD09Code(null);
            }

            mem.setIsFarmer(StrUtil.equals(memExportDTO.getIsFarmer(),"是")?1:0);

            mem.setPhone(memExportDTO.getPhone());
            if(StrUtil.isNotEmpty(memExportDTO.getD08Name())){
                mem.setD08Name(memExportDTO.getD08Name());
                mem.setD08Code(d08Map.getOrDefault(memExportDTO.getD08Name(),""));
            } else {
                mem.setD08Name(null);
                mem.setD08Code(null);
            }


            if(StrUtil.isNotEmpty(memExportDTO.getApplyDate())) {
                mem.setApplyDate(DateUtil.parse(memExportDTO.getApplyDate(), "yyyy-MM-dd"));
            }else {
                mem.setApplyDate(null);
            }

            if(StrUtil.isNotEmpty(memExportDTO.getObjectDate())) {
                mem.setObjectDate(DateUtil.parse(memExportDTO.getObjectDate(), "yyyy-MM-dd"));
            }else {
                mem.setObjectDate(null);
            }

            if(StrUtil.isNotEmpty(memExportDTO.getD19Name())){
                mem.setD19Name(memExportDTO.getD19Name());
                mem.setD19Code(d19Map.getOrDefault(memExportDTO.getD19Name(),""));
            } else {
                mem.setD19Name(null);
                mem.setD19Code(null);
            }

            if(StrUtil.isNotEmpty(memExportDTO.getD20Name())){
                mem.setD20Name(memExportDTO.getD20Name());
                mem.setD20Code(d20Map.getOrDefault(memExportDTO.getD20Name(),""));
            } else {
                mem.setD20Name(null);
                mem.setD20Code(null);
            }

            if(StrUtil.isNotEmpty(memExportDTO.getD21Name())){
                mem.setD21Name(memExportDTO.getD21Name());
                mem.setD21Code(d21Map.getOrDefault(memExportDTO.getD21Name(),""));
            } else {
                mem.setD21Name(null);
                mem.setD21Code(null);
            }

            if(StrUtil.isNotEmpty(memExportDTO.getD11Name())){
                mem.setD11Name(memExportDTO.getD11Name());
                mem.setD11Code(d11Map.getOrDefault(memExportDTO.getD11Name(),""));
            } else {
                mem.setD11Name(null);
                mem.setD11Code(null);
            }

            if(StrUtil.isNotEmpty(memExportDTO.getD27Name())){
                mem.setD27Name(memExportDTO.getD27Name());
                mem.setD27Code(d27Map.getOrDefault(memExportDTO.getD27Name(),""));
            } else {
                mem.setD27Name(null);
                mem.setD27Code(null);
            }

            if(StrUtil.isNotEmpty(memExportDTO.getJoinOrgPartyDate())) {
                mem.setJoinOrgPartyDate(DateUtil.parse(memExportDTO.getJoinOrgPartyDate(), "yyyy-MM-dd"));
            }else {
                mem.setJoinOrgPartyDate(null);
            }

            if(StrUtil.isNotEmpty(memExportDTO.getFullMemberDate())) {
                mem.setFullMemberDate(DateUtil.parse(memExportDTO.getFullMemberDate(), "yyyy-MM-dd"));
            }else {
                mem.setFullMemberDate(null);
            }

            if(StrUtil.isNotEmpty(memExportDTO.getD28Name())){
                mem.setD28Name(memExportDTO.getD28Name());
                mem.setD28Code(d28Map.getOrDefault(memExportDTO.getD28Name(),""));
            } else {
                mem.setD28Name(null);
                mem.setD28Code(null);
            }

            if(StrUtil.isNotEmpty(memExportDTO.getD18Name())){
                mem.setD18Name(memExportDTO.getD18Name());
                mem.setD18Code(d18Map.getOrDefault(memExportDTO.getD18Name(),""));
            } else {
                mem.setD18Name(null);
                mem.setD18Code(null);
            }

            if(StrUtil.isNotEmpty(memExportDTO.getLostContactDate())) {
                mem.setLostContactDate(DateUtil.parse(memExportDTO.getLostContactDate(), "yyyy-MM-dd"));
            }else {
                mem.setLostContactDate(null);
            }

            result.add(mem);
        }

        return result;
    }


    /**
     * 处理字典表
     * @param recordList 字典表的集合
     * @return key为name value 为key
     */
    public Map<String,String> processDic(List<Record> recordList){
        Map<String,String> result = new LinkedHashMap<>();
        recordList.forEach(var-> result.put(var.getStr("name"),var.getStr("key")));

        return result;
    }

    /***
     * 设置转接类型
     * @param type 转接类型
     * @param transferRecord 转接记录
     * @return true 设置成功 false 设置失败 失败的原因要么是参数错误 要么是字典表有问题
     * */
    @Override
    protected boolean setTransferTypeCode(String type, TransferRecord transferRecord) {
        //如果不是整建制类型,则参数错误
        if (!(TransferRecordConstant.ORG_ALL_IN.contains(type) || TransferRecordConstant.ORG_ALL_OUT.contains(type))) {
            return false;
        }
        return super.setTransferTypeCode(type, transferRecord);
    }
}
