package com.zenith.front.core.analysis.ext.condition.year2024;

import com.b1809.analysis.extend.GenSqlConditionFuc;
import org.jooq.Condition;
import org.jooq.Field;

import static org.jooq.impl.DSL.field;
import static org.jooq.impl.DSL.name;

/**
 * 党组
 *
 * <AUTHOR>
 * @date 2022/6/28
 */
public class OrgPartyCondition2024 implements GenSqlConditionFuc {

    @Override
    public String getTableName() {
        return "ccp_org_party_2024";
    }

    @Override
    public Condition create(String orgCode, String orgLevelCode) {
        return getLevelCodeField().like(orgLevelCode + "%");
    }

    @Override
    public Field<?> getLevelCodeField() {
        return field(name( "party_org_code"), String.class);
    }
}
