package com.zenith.front.core.analysis.count.year2023;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import com.b1809.analysis.entity.PeggingPara;
import com.b1809.analysis.extend.ITableCount;
import com.b1809.analysis.plugin.elasticsearch.EsKit;
import com.jfinal.kit.StrKit;
import com.jfinal.plugin.activerecord.Record;
import com.zenith.front.dao.mapper.unit.UnitCommitteeMapper;
import com.zenith.front.dao.mapper.unit.UnitCountrysideMapper;
import com.zenith.front.model.bean.UnitCountryside;
import com.zenith.front.model.vo.UnitCommiteeCountVo;
import org.jooq.Record1;
import org.jooq.SelectConditionStep;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.*;

import static com.zenith.front.common.constant.DBConstant.DSL_CONTEXT;
import static org.jooq.impl.DSL.*;

/**
 * 专题调查表四  选调生到村任职、按照大学生村官管理工作基本情况
 */
@Component
public class Html49Count202302 implements ITableCount {


    @Resource
    @Autowired
    private UnitCommitteeMapper unitCommitteeMapper;
    @Resource
    @Autowired
    private UnitCountrysideMapper unitCountrysideMapper;

    @Override
    public String getReportCode() {
        return "202302_49.html";
    }

    @Override
    public Map<String, Map<String, Number>> doCount(String reportCode, String orgCode, String orgLevelCode) throws Exception {
        Map<String, Map<String, Number>> resultMap =new HashMap<>();
        Map<String, Number> result = new LinkedHashMap<>();
        resultMap.put("table0",result);
        Html52Count2023.initTableCol(1, 5, 1, 10, result);
        List<UnitCommiteeCountVo> unitCommiteeCountVos =new ArrayList<>();
        SelectConditionStep<Record1<Object>> where = DSL_CONTEXT.select(field(" uc.\"has_village_transfer_student\" as hasVillageTransferStudent,\n" +
                "             uc.\"start_date\" as startDate,\n" +
                "             uc.\"is_incumbent\" as isIncumbent,\n" +
                "             uc.\"d144_code\" as d144Code,\n" +
                "             ma.\"sex_code\" as sexCode,\n" +
                "             ma.\"d07_code\" as d07Code,\n" +
                "             ma.\"d08_code\" as d08Code")).from(table(name("ccp_unit_committee")).as("uc")).leftJoin("\"ccp_mem_all\" ma ").on("uc.\"mem_code\" = ma.\"code\"").where(" elect_code IN ( SELECT CODE FROM \"ccp_unit_committee_elect\" WHERE unit_code IN ( SELECT CODE FROM ccp_unit WHERE create_unit_org_code LIKE '" + orgLevelCode + "%') ) and uc.\"has_village_transfer_student\"='1'\n");
        List<Record> recordList = EsKit.findBySql(where.toString()).toRecord();
        recordList.forEach(record -> {
            UnitCommiteeCountVo unitCommiteeCountVo = new UnitCommiteeCountVo();
            BeanUtils.copyProperties(record,unitCommiteeCountVo);
            unitCommiteeCountVos.add(unitCommiteeCountVo);

        });
        List<UnitCountryside> unitCountrysides =new ArrayList<>();
        SelectConditionStep<Record1<Object>> ccp_unit_countryside = DSL_CONTEXT.select(field("*")).from(table(name("ccp_unit_countryside"))).where("unit_code IN (SELECT code FROM ccp_unit WHERE create_unit_org_code LIKE '" + orgLevelCode + "%')");
        List<Record> recordList2 = EsKit.findBySql(ccp_unit_countryside.toString()).toRecord();
        recordList2.forEach(record -> {
            UnitCountryside unitCountryside = new UnitCountryside();
            BeanUtils.copyProperties(record,unitCountryside);
            unitCountrysides.add(unitCountryside);
        });
        //单位班子records
        SelectConditionStep<Record1<Object>> ccp_unit_committee = DSL_CONTEXT.select(field("mem_code,d25_code,d144_code")).from(table(name("ccp_unit_committee"))).where("elect_code in (SELECT\n" +
                "\tcode \n" +
                "FROM\n" +
                "\tccp_unit_committee_elect\n" +
                "\twhere unit_code in (select code from ccp_unit_all where create_unit_org_code like '" + orgLevelCode + "%' and delete_time is null and  tenure_end_date >'2023-12-31'\n" +
                ")\n" +
                ") and has_village_transfer_student ='1'");
        //组织
        SelectConditionStep<Record1<Object>> ccp_org_committee1 = DSL_CONTEXT.select(field("mem_code,d022_code,d144_code")).from(table(name("ccp_org_committee"))).where("\telect_code IN ( SELECT CODE FROM \"ccp_org_committee_elect\" WHERE elect_org_code LIKE '" + orgLevelCode + "%' AND tenure_end_date > '2023-12-31' AND delete_time IS NULL ) \n" +
                "\tAND delete_time IS NULL \n" +
                "\tAND end_date IS NULL and has_village_transfer_student ='1'");
        List<Record> records = EsKit.findBySql(ccp_org_committee1.unionAll(ccp_unit_committee).toString()).toRecord();
        HashMap<String, Integer> recordCountMap = new HashMap<>();
        HashMap<String, String> recordMap = new HashMap<>();
        HashMap<String, String> recordMap2 = new HashMap<>();
        HashMap<String, String> recordMap3 = new HashMap<>();
        HashMap<String, String> recordMap4 = new HashMap<>();
        HashMap<String, String> recordMap5 = new HashMap<>();

        int count =0;
        int count2 =0;
        int count3 =0;
        int count4 =0;
        int count5 =0;
        //进两委的

        for (int i = 0; i < records.size(); i++) {
            String mem_code = records.get(i).getStr("mem_code");
            boolean notBlank = StrUtil.isNotBlank(mem_code);
            if (StrUtil.equalsAny(records.get(i).getStr("d25_code"),"1","41","51")){
                count++;
            }
            recordMap.put(notBlank?mem_code:StrKit.getRandomUUID(),records.get(i).getStr("d25_code"));

            if (StrUtil.equals("2",records.get(i).getStr("d144_code"))){
                if (StrUtil.equalsAny(records.get(i).getStr("d25_code"),"1","41","51")&&!recordMap2.containsKey(mem_code)){
                    count2++;
                }
                recordMap2.put(notBlank?mem_code:StrKit.getRandomUUID(),records.get(i).getStr("d25_code"));
            }
            if (StrUtil.equals("3",records.get(i).getStr("d144_code"))){
                if (StrUtil.equalsAny(records.get(i).getStr("d25_code"),"1","41","51")&&!recordMap3.containsKey(mem_code)){
                    count3++;
                }
                recordMap.put(notBlank?mem_code:StrKit.getRandomUUID(),records.get(i).getStr("d25_code"));
            }
            if (StrUtil.equals("4",records.get(i).getStr("d144_code"))){
                if (StrUtil.equalsAny(records.get(i).getStr("d25_code"),"1","41","51")&&!recordMap4.containsKey(mem_code)){
                    count4++;
                }
                recordMap4.put(notBlank?mem_code:StrKit.getRandomUUID(),records.get(i).getStr("d25_code"));
            }
            if (StrUtil.equals("5",records.get(i).getStr("d144_code"))){
                if (StrUtil.equalsAny(records.get(i).getStr("d25_code"),"1","41","51")&&!recordMap5.containsKey(mem_code)){
                    count5++;
                }
                recordMap5.put(notBlank?mem_code:StrKit.getRandomUUID(),records.get(i).getStr("d25_code"));
            }
        }
        recordCountMap.put("count",count);
        recordCountMap.put("count2",count2);
        recordCountMap.put("count3",count3);
        recordCountMap.put("count4",count4);
        recordCountMap.put("count5",count5);
        if (CollUtil.isNotEmpty(unitCommiteeCountVos)) {
            Map<String, Number> map = resultMap.get("table0");
            map.put("cell_1_1", unitCommiteeCountVos.stream().filter(s -> s.getIsIncumbent()==1).count());
            map.put("cell_1_2", unitCommiteeCountVos.stream().filter(s -> StrUtil.equalsAny(s.getSexCode(), "0")).count());
            map.put("cell_1_3", unitCommiteeCountVos.stream().filter(s -> StrUtil.equalsAny(s.getD08Code(), "1", "2")).count());
            map.put("cell_1_4", unitCommiteeCountVos.stream().filter(s -> StrUtil.equalsAny(s.getD07Code(), "1","12","13","14","111","112","11")).count());
            map.put("cell_1_5", unitCommiteeCountVos.stream().filter(s -> StrUtil.equals(s.getD07Code(), "111")).count());
            map.put("cell_1_6", 0);
            map.put("cell_1_7", unitCommiteeCountVos.stream().filter(s -> DateUtil.compare(s.getStartDate(),DateUtil.parse("2023-01-01"))>1).count());
            map.put("cell_1_8", unitCountrysides.stream().filter(s-> StrUtil.equalsAny(s.getD143Code(),"1","2")).count());
            map.put("cell_1_9", recordMap.size());
            map.put("cell_1_10", recordCountMap.get("count"));
            //2
            map.put("cell_2_1", unitCommiteeCountVos.stream().filter(s -> s.getIsIncumbent()==1&&StrUtil.equals(s.getD144Code(),"2")).count());
            map.put("cell_2_2", unitCommiteeCountVos.stream().filter(s -> StrUtil.equalsAny(s.getSexCode(), "0")&&StrUtil.equals(s.getD144Code(),"2")).count());
            map.put("cell_2_3", unitCommiteeCountVos.stream().filter(s -> StrUtil.equalsAny(s.getD08Code(), "1", "2")&&StrUtil.equals(s.getD144Code(),"2")).count());
            map.put("cell_2_4", unitCommiteeCountVos.stream().filter(s -> StrUtil.equalsAny(s.getD07Code(), "1","12","13","14","111","112","11")&&StrUtil.equals(s.getD144Code(),"2")).count());
            map.put("cell_2_5", unitCommiteeCountVos.stream().filter(s -> StrUtil.equals(s.getD07Code(), "111")&&StrUtil.equals(s.getD144Code(),"2")).count());
            map.put("cell_2_6", 0);
            map.put("cell_2_7", unitCommiteeCountVos.stream().filter(s -> DateUtil.compare(s.getStartDate(),DateUtil.parse("2023-01-01"))>1&&StrUtil.equals(s.getD144Code(),"2")).count());
            map.put("cell_2_8", unitCountrysides.stream().filter(s-> StrUtil.equalsAny(s.getD143Code(),"1","2")&&StrUtil.equals(s.getD144Code(),"2")).count());
            map.put("cell_2_9", recordMap2.size());
            map.put("cell_2_10",recordCountMap.get("count2"));
            //3
            map.put("cell_3_1", unitCommiteeCountVos.stream().filter(s -> s.getIsIncumbent()==1&&StrUtil.equals(s.getD144Code(),"3")).count());
            map.put("cell_3_2", unitCommiteeCountVos.stream().filter(s -> StrUtil.equalsAny(s.getSexCode(), "0")&&StrUtil.equals(s.getD144Code(),"3")).count());
            map.put("cell_3_3", unitCommiteeCountVos.stream().filter(s -> StrUtil.equalsAny(s.getD08Code(), "1", "2")&&StrUtil.equals(s.getD144Code(),"3")).count());
            map.put("cell_3_4", unitCommiteeCountVos.stream().filter(s -> StrUtil.equalsAny(s.getD07Code(), "1","12","13","14","111","112","11")&&StrUtil.equals(s.getD144Code(),"3")).count());
            map.put("cell_3_5", unitCommiteeCountVos.stream().filter(s -> StrUtil.equals(s.getD07Code(), "111")&&StrUtil.equals(s.getD144Code(),"3")).count());
            map.put("cell_3_6", 0);
            map.put("cell_3_7", unitCommiteeCountVos.stream().filter(s -> DateUtil.compare(s.getStartDate(),DateUtil.parse("2023-01-01"))>1&&StrUtil.equals(s.getD144Code(),"3")).count());
            map.put("cell_3_8", unitCountrysides.stream().filter(s-> StrUtil.equalsAny(s.getD143Code(),"1","2")&&StrUtil.equals(s.getD144Code(),"3")).count());
            map.put("cell_3_9", recordMap3.size());
            map.put("cell_3_10",recordCountMap.get("count3"));

            //4
            map.put("cell_4_1", unitCommiteeCountVos.stream().filter(s -> s.getIsIncumbent()==1&&StrUtil.equals(s.getD144Code(),"4")).count());
            map.put("cell_4_2", unitCommiteeCountVos.stream().filter(s -> StrUtil.equalsAny(s.getSexCode(), "0")&&StrUtil.equals(s.getD144Code(),"4")).count());
            map.put("cell_4_3", unitCommiteeCountVos.stream().filter(s -> StrUtil.equalsAny(s.getD08Code(), "1", "2")&&StrUtil.equals(s.getD144Code(),"4")).count());
            map.put("cell_4_4", unitCommiteeCountVos.stream().filter(s -> StrUtil.equalsAny(s.getD07Code(), "1","12","13","14","111","112","11")&&StrUtil.equals(s.getD144Code(),"4")).count());
            map.put("cell_4_5", unitCommiteeCountVos.stream().filter(s -> StrUtil.equals(s.getD07Code(), "111")&&StrUtil.equals(s.getD144Code(),"4")).count());
            map.put("cell_4_6", 0);
            map.put("cell_4_7", unitCommiteeCountVos.stream().filter(s -> DateUtil.compare(s.getStartDate(),DateUtil.parse("2023-01-01"))>1&&StrUtil.equals(s.getD144Code(),"4")).count());
            map.put("cell_4_8", unitCountrysides.stream().filter(s-> StrUtil.equalsAny(s.getD143Code(),"1","2")&&StrUtil.equals(s.getD144Code(),"4")).count());
            map.put("cell_4_9", recordMap4.size());
            map.put("cell_4_10",recordCountMap.get("count4"));

            //5
            map.put("cell_5_1", unitCommiteeCountVos.stream().filter(s -> s.getIsIncumbent()==1&&StrUtil.equals(s.getD144Code(),"5")).count());
            map.put("cell_5_2", unitCommiteeCountVos.stream().filter(s -> StrUtil.equalsAny(s.getSexCode(), "0")&&StrUtil.equals(s.getD144Code(),"5")).count());
            map.put("cell_5_3", unitCommiteeCountVos.stream().filter(s -> StrUtil.equalsAny(s.getD08Code(), "1", "2")&&StrUtil.equals(s.getD144Code(),"5")).count());
            map.put("cell_5_4", unitCommiteeCountVos.stream().filter(s -> StrUtil.equalsAny(s.getD07Code(), "1","12","13","14","111","112","11")&&StrUtil.equals(s.getD144Code(),"5")).count());
            map.put("cell_5_5", unitCommiteeCountVos.stream().filter(s -> StrUtil.equals(s.getD07Code(), "111")&&StrUtil.equals(s.getD144Code(),"5")).count());
            map.put("cell_5_6", 0);
            map.put("cell_5_7", unitCommiteeCountVos.stream().filter(s -> DateUtil.compare(s.getStartDate(),DateUtil.parse("2023-01-01"))>1&&StrUtil.equals(s.getD144Code(),"5")).count());
            map.put("cell_5_8", unitCountrysides.stream().filter(s-> StrUtil.equalsAny(s.getD143Code(),"1","2")&&StrUtil.equals(s.getD144Code(),"5")).count());
            map.put("cell_5_9", recordMap5.size());
            map.put("cell_5_10", recordCountMap.get("count5"));

        }
        return resultMap;
    }

    @Override
    public Map<String, Object> doPegging(PeggingPara peggingPara) throws Exception {
        return null;
    }

    @Override
    public Map<String, Number> doReplenishCount(String reportCode, String orgCode, String orgLevelCode) throws Exception {
        return null;
    }

    @Override
    public Map<String, Object> doReplenishPegging(PeggingPara peggingPara) throws Exception {
        return null;
    }
}
