package com.zenith.front.core.service.ztdc;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.zenith.front.api.unit.IUnitService;
import com.zenith.front.api.ztdc.IZt12InternetEnterprisService;
import com.zenith.front.dao.mapper.ztdc.Zt12InternetEnterprisMapper;
import com.zenith.front.model.dto.Zt12InternetEnterprisDto;
import com.zenith.front.model.message.OutMessage;
import com.zenith.front.model.message.Status;
import com.zenith.front.model.bean.Unit;
import com.zenith.front.model.bean.Zt12InternetEnterpris;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.Date;
import java.util.Objects;

/**
 * <p>
 * 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-08-13
 */
@Service
public class Zt12InternetEnterprisServiceImpl extends ServiceImpl<Zt12InternetEnterprisMapper, Zt12InternetEnterpris> implements IZt12InternetEnterprisService {
    @Resource
    private IUnitService unitService;

    @Override
    public Zt12InternetEnterpris findByUnitCode(String unitCode) {
        LambdaQueryWrapper<Zt12InternetEnterpris> query = new LambdaQueryWrapper<Zt12InternetEnterpris>()
                .eq(Zt12InternetEnterpris::getUnitCode, unitCode)
                .isNull(Zt12InternetEnterpris::getDeleteTime)
                .last("limit 1");
        return getOne(query);
    }

    @Override
    @Transactional(rollbackFor = {Exception.class})
    public OutMessage<?> saveZt12Data(Zt12InternetEnterprisDto data) {
        Unit unit = unitService.findByCode(data.getUnitCode());
        if (Objects.isNull(unit)) {
            return new OutMessage<>(Status.OBJEC_NOT_EXIST);
        }
        Zt12InternetEnterpris enterpris = this.findByUnitCode(unit.getCode());
        boolean result;
        if (Objects.nonNull(enterpris)) {
            String code = enterpris.getCode();
            BeanUtils.copyProperties(data, enterpris);
            enterpris.setUpdateTime(new Date());
            enterpris.setCode(code);
            result = this.updateById(enterpris);
        } else {
            Zt12InternetEnterpris zt12InternetEnterpris = new Zt12InternetEnterpris();
            BeanUtils.copyProperties(data, zt12InternetEnterpris);
            zt12InternetEnterpris.setCreateTime(new Date());
            result = this.save(zt12InternetEnterpris);
        }
        return new OutMessage<>(result ? Status.SUCCESS : Status.FAIL);
    }


}
