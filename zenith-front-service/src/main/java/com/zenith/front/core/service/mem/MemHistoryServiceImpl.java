package com.zenith.front.core.service.mem;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.thread.ThreadUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.zenith.front.api.develop.IDevelopStepLogService;
import com.zenith.front.api.mem.IMemAllInfoService;
import com.zenith.front.api.mem.IMemHistoryService;
import com.zenith.front.api.mem.IMemLogService;
import com.zenith.front.api.mem.IMemService;
import com.zenith.front.api.report.IStatisticsYearService;
import com.zenith.front.api.sync.ISyncMemService;
import com.zenith.front.common.constant.CommonConstant;
import com.zenith.front.common.encrypt.properties.EncryptProperties;
import com.zenith.front.common.kit.StrKit;
import com.zenith.front.common.kit.StringToBooleanConverter;
import com.zenith.front.common.mybatisplus.WrapperUtil;
import com.zenith.front.common.untils.SM4Untils;
import com.zenith.front.core.cache.CacheUtils;
import com.zenith.front.core.service.org.OrgReviewersServiceImpl;
import com.zenith.front.dao.mapper.mem.MemMapper;
import com.zenith.front.model.bean.Mem;
import com.zenith.front.model.bean.MemAllInfo;
import com.zenith.front.model.bean.MemLog;
import com.zenith.front.model.dto.MemDTO;
import com.zenith.front.model.dto.MemHistoryListDTO;
import com.zenith.front.model.dto.MemLogDTO;
import com.zenith.front.model.message.OutMessage;
import com.zenith.front.model.message.Status;
import com.zenith.front.model.vo.BetweenReportDateVo;
import com.zenith.front.model.vo.MemVO;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import javax.sound.sampled.Line;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 历史党员
 *
 * <AUTHOR>
 * @date 2021/7/15
 */
@Service
public class MemHistoryServiceImpl extends ServiceImpl<MemMapper, Mem> implements IMemHistoryService {

    @Resource
    MemMapper memMapper;
    @Resource
    IMemService memService;
    @Resource
    IMemLogService memLogService;
    @Resource
    IDevelopStepLogService iDevelopStepLogService;
    @Resource
    IMemAllInfoService iMemAllInfoService;
    @Resource
    private MemRewardServiceImpl memRewardService;
    @Resource
    private OrgReviewersServiceImpl orgReviewersService;
    @Resource
    private ISyncMemService iSyncMemService;
    @Resource
    private IStatisticsYearService iStatisticsYearService;
    @Value("${exchange_nginx_key}")
    private String exchange_nginx_key;

    /**
     * 获取历史党员列表
     *
     * @param memHistoryListDTO
     * @return
     */
    @Override
    public OutMessage getList(MemHistoryListDTO memHistoryListDTO) throws Exception {
        String memName = memHistoryListDTO.getMemName();
        String memOrgCode = memHistoryListDTO.getMemOrgCode();
        Integer pageNum = memHistoryListDTO.getPageNum();
        Integer pageSize = memHistoryListDTO.getPageSize();
        List<String> d12CodeList = memHistoryListDTO.getD12CodeList();
        List<String> d50CodeList = memHistoryListDTO.getD50CodeList();
        List<String> d51CodeList = memHistoryListDTO.getD51CodeList();
        String subordinate = memHistoryListDTO.getSubordinate();
        Boolean convert = StringToBooleanConverter.convert(subordinate);

        LambdaQueryWrapper<Mem> wrapper = new QueryWrapper<Mem>().lambda()
                .in(CollectionUtil.isNotEmpty(d12CodeList), Mem::getD12Code, d12CodeList)
                .in(CollectionUtil.isNotEmpty(d50CodeList), Mem::getD50Code, d50CodeList)
                .in(CollectionUtil.isNotEmpty(d51CodeList), Mem::getD51Name, d51CodeList)
                .isNotNull(Mem::getDeleteTime)
                .in(Mem::getD08Code, "1", "2");
        if (StrKit.notBlank(memName)) {
            if (EncryptProperties.enable) {
                // TODO: 2022/10/20 加密身份证
                memName = SM4Untils.encryptContent(exchange_nginx_key, memName);
            }
            wrapper.like(Mem::getName, memName);
        }
        //是否显示下级
        if (Boolean.TRUE.equals(convert)) {
            wrapper.likeRight(Mem::getMemOrgCode, memOrgCode);
        } else {
            wrapper.eq(Mem::getMemOrgCode, memOrgCode);
        }
        wrapper.and(q -> q.isNotNull(Mem::getD12Code).or().isNotNull(Mem::getD50Code))
                .orderByDesc(Mem::getLeaveOrgDate).last("NULLS LAST");
//        wrapper.select(Mem.class,info -> !"idcard".equals(info.getColumn()) &&
//                        ! "phone".equals(info.getColumn()) && !"homeAddress".equals(info.getColumn()));
        //返回指定加密数据
        WrapperUtil.existsEncrypt(wrapper, Mem.class, "name");
        Page<Mem> page = memMapper.selectPage(new Page<>(pageNum, pageSize), wrapper);

        List<Mem> records = page.getRecords();
        Map<String, MemAllInfo> memCodeToD030Map = new LinkedHashMap<>();
        if (CollUtil.isNotEmpty(records)) {
            List<MemAllInfo> list = iMemAllInfoService.list(new LambdaQueryWrapper<MemAllInfo>().select(MemAllInfo::getCode, MemAllInfo::getD030Code, MemAllInfo::getD029Code).in(MemAllInfo::getCode, records.stream().map(Mem::getCode).collect(Collectors.toSet())));
            memCodeToD030Map = list.stream().collect(Collectors.toMap(MemAllInfo::getCode, v -> v, (key1, key2) -> key1));
        }
        Map<String, MemAllInfo> memCodeToD030Mapi = memCodeToD030Map;
        Page<MemVO> pageVO = new Page<>();
        List<MemVO> list = new ArrayList<>();
        Map<String, String> d50Map = CacheUtils.getDic("dict_d50").stream().collect(Collectors.toMap(record -> record.get("key"), record1 -> record1.get("name"), (a, b) -> b, LinkedHashMap::new));
        Map<String, String> d301Map = CacheUtils.getDic("dict_d301").stream().collect(Collectors.toMap(record -> record.get("key"), record1 -> record1.get("name"), (a, b) -> b, LinkedHashMap::new));
        page.getRecords().forEach(mem -> {
            MemVO memVO = new MemVO();
            try {
                // TODO 20250103 内网历史党员列表返回 掉数据处理
                BeanUtils.copyProperties(mem, memVO);
                String orgName = CacheUtils.getOrgName(mem.getOrgCode());
                memVO.setOrgName(orgName);
                // 离开类型
                String d12Code = mem.getD12Code();
                if ((StrUtil.isNotBlank(d12Code) && d12Code.startsWith(CommonConstant.THREE)) || "229".equals(d12Code)) {
                    memVO.setIsReconvert(CommonConstant.ONE_INT);
                } else {
                    memVO.setIsReconvert(CommonConstant.ZERO_INT);
                }
                //出党原因
                MemAllInfo allInfo = memCodeToD030Mapi.get(mem.getCode());
                if (Objects.nonNull(allInfo) && StrUtil.isNotEmpty(allInfo.getD030Code()) && StrUtil.isNotEmpty(allInfo.getD029Code())) {
                    StringBuilder builder = new StringBuilder();
                    Arrays.stream(allInfo.getD030Code().split(",")).forEach(key -> builder.append(d301Map.get(key)).append(","));
                    memVO.setD50Name(builder.deleteCharAt(builder.length() - 1).toString());
                }
                String d50Code = memVO.getD50Code();
                if (StringUtils.hasText(d50Code) && StrUtil.isEmpty(memVO.getD50Name())) {
                    StringBuilder builder = new StringBuilder();
                    Arrays.stream(d50Code.split(",")).forEach(key -> builder.append(d50Map.get(key)).append(","));
                    memVO.setD50Name(builder.deleteCharAt(builder.length() - 1).toString());
                }
                list.add(memVO);
            } catch (Exception e) {
                e.printStackTrace();
            }
        });
        BeanUtils.copyProperties(page, pageVO, "list");
        pageVO.setRecords(list);
        return new OutMessage<>(Status.SUCCESS, pageVO);
    }

    /**
     * 撤销历史党员
     *
     * @param memLogDTO
     * @return
     */
    @Override
    public OutMessage delMemHistory(MemLogDTO memLogDTO) throws Exception {
        String code = memLogDTO.getCode();
        Mem mem = memService.findDelByCode(code);
        if (ObjectUtil.isNull(mem)) {
            return new OutMessage<>(Status.OBJEC_NOT_EXIST);
        }
        //历史党员的撤销操作，只能撤销离开时间是统计期以内的
        if (!iStatisticsYearService.isBetweenReportDate(mem.getLeaveOrgDate())) {
            return new OutMessage<>(500, "只能撤销离开时间是统计期以内的!", null);
        }
        MemLog memLog = new MemLog();
        BeanUtils.copyProperties(mem, memLog);
        memLog.setBackoutReason(memLogDTO.getBackoutReason());
        boolean b = memService.update(null, Wrappers.<Mem>lambdaUpdate()
                .set(Mem::getLeaveOrgDate, null)
                .set(Mem::getDeleteTime, null)
                .set(Mem::getD12Code, null)
                .set(Mem::getD12Name, null)
                .set(Mem::getWorkPost, null)
                .set(Mem::getSettleArea, null)
                .set(Mem::getD50Code, null)
                .set(Mem::getD50Name, null)
                .set(Mem::getD51Code, null)
                .set(Mem::getD51Name, null)
                .set(Mem::getD029Code, null)
                .set(Mem::getIsLost, 0).set(Mem::getD18Code, null).set(Mem::getD18Name, null).set(Mem::getLostContactDate, null)
                .set(Mem::getHasStopParty, null).set(Mem::getHasGetTouch, null).set(Mem::getGetTouchDate, null)
                .eq(Mem::getId, mem.getId()));
        boolean b1 = memLogService.save(memLog);
        if (b) {
            iDevelopStepLogService.updateDevelopStepLog(memLog.getD12Code(), mem.getCode(), null, null);
            ThreadUtil.execAsync(() -> {
                // TODO 20230119 历史党员撤销需清除全部出党记录
                memRewardService.clearRewardData(mem.getCode());
                orgReviewersService.clearReviewersData(mem.getCode());
                iSyncMemService.syncMem(mem.getCode(), "1");
            });
        }
        return new OutMessage<>(b && b1 ? Status.SUCCESS : Status.FAIL);
    }

    /**
     * 恢复党籍
     *
     * @param memDTO
     * @return
     */
    @Override
    public OutMessage reconvertMemHistory(MemDTO memDTO) throws Exception {
        BetweenReportDateVo betweenReportDate = iStatisticsYearService.isBeforeReportDate(memDTO.getRecoverPartyDate(), "2");
        if (!betweenReportDate.getIsBetween()) {
            return new OutMessage<String>(Status.NOT_IN_BETWEEN_DEFAULT_DATE).format("恢复党籍时间", betweenReportDate.getMessage());
        }
        String code = memDTO.getCode();
        Mem mem = memService.findDelByCode(code);
        String d11Code = "", d11Name = "";
        String d27Code = mem.getD27Code(), d27Name = mem.getD27Name();
        if ("21".equals(mem.getD12Code())) {
            d11Code = "2";
            d11Name = "恢复党籍";
        }
        if (mem.getD12Code().startsWith(CommonConstant.THREE)) {
            d11Code = "21";
            d11Name = "停止党籍人员恢复党籍";
        }
        if (StrUtil.startWith(d11Code, "2")) {
            d27Code = "31";
            d27Name = "恢复党籍";
        }
        // 保存之前数据
        MemLog memLog = new MemLog();
        BeanUtils.copyProperties(mem, memLog);
        memLog.setD11Code(d11Code);
        memLog.setD11Name(d11Name);
        memLog.setD18Code(memDTO.getD18Code());
        memLog.setD27Code(d27Code);
        memLog.setD27Name(d27Name);
        // 重置数据
        boolean b = memService.update(null, Wrappers.<Mem>lambdaUpdate()
                .set(Mem::getD12Code, null)
                .set(Mem::getD12Name, null)
                .set(Mem::getLeaveOrgDate, null)
                .set(Mem::getD50Code, null)
                .set(Mem::getD50Name, null)
                .set(Mem::getD51Code, null)
                .set(Mem::getD51Name, null)
                .set(Mem::getDeleteTime, null)
                .set(Mem::getUpdateTime, new Date())
                .set(Mem::getSettleArea, null)
                .set(Mem::getStopPartyDate, null)
                .set(Mem::getWorkPost, memDTO.getWorkPost())
                .set(Mem::getRecoverPartyDate, memDTO.getRecoverPartyDate())
                .set(Mem::getRecoverPartyReason, memDTO.getRecoverPartyReason())
                .set(Mem::getD11Code, d11Code)
                .set(Mem::getD11Name, d11Name)
                .set(Mem::getD27Code, d27Code)
                .set(Mem::getD27Name, d27Name)
                .set(Mem::getIsLost, 0).set(Mem::getD18Code, null).set(Mem::getD18Name, null).set(Mem::getLostContactDate, null)
                .set(Mem::getHasStopParty, null).set(Mem::getHasGetTouch, null).set(Mem::getGetTouchDate, null)
                .eq(Mem::getId, mem.getId())
        );
        boolean b1 = memLogService.save(memLog);
        iDevelopStepLogService.updateDevelopStepLog(memLog.getD12Code(), mem.getCode(), null, null);
        if (b) {
            ThreadUtil.execAsync(() -> iSyncMemService.syncMem(mem.getCode(), "1"));
        }
        return new OutMessage<>(b && b1 ? Status.SUCCESS : Status.FAIL);
    }

    /**
     * 脱党处理
     */
    @Override
    public OutMessage<?> quitParty(MemDTO memDTO) {
        Mem mem = memService.findDelByCode(memDTO.getCode());
        mem.setApprovalOrganName(memDTO.getApprovalOrganName());
        mem.setD52Code(memDTO.getD52Code());
        mem.setD52Name(memDTO.getD52Name());
        mem.setApprovalTime(memDTO.getApprovalTime());
        final boolean update = this.updateById(mem);
        return new OutMessage<>(update ? Status.SUCCESS : Status.FAIL);
    }
}
