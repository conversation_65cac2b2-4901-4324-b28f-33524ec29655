package com.zenith.front.core.service.businessModelSync;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.zenith.front.api.businessModelSync.RunnerSyncBusinessModelService;
import com.zenith.front.api.develop.IDevelopStepLogAllService;
import com.zenith.front.api.develop.IDevelopStepLogService;
import com.zenith.front.api.mem.IMemAllInfoService;
import com.zenith.front.api.mem.IMemDevelopAllService;
import com.zenith.front.api.mem.IMemDevelopService;
import com.zenith.front.api.mem.IMemService;
import com.zenith.front.api.org.IOrgAllService;
import com.zenith.front.api.org.IOrgCommitteeElectService;
import com.zenith.front.api.org.IOrgCommitteeService;
import com.zenith.front.api.org.IOrgService;
import com.zenith.front.api.report.IStatisticsYearService;
import com.zenith.front.api.unit.IUnitCommitteeElectService;
import com.zenith.front.api.unit.IUnitCommitteeService;
import com.zenith.front.api.unit.IUnitOrgLinkedService;
import com.zenith.front.api.unit.IUnitService;
import com.zenith.front.common.constant.CommonConstant;
import com.zenith.front.common.constant.DictConstant;
import com.zenith.front.core.cache.CacheUtils;
import com.zenith.front.model.bean.*;
import com.zenith.front.model.custom.Record;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @create_date 2023-12-18 15:57
 * @description
 */
@Service
@Slf4j
public class RunnerSyncBusinessModelServiceImpl implements RunnerSyncBusinessModelService {
    @Resource
    private IOrgService iOrgService;
    @Resource
    private IUnitService iUnitService;
    @Resource
    private IOrgAllService iOrgAllService;
    @Resource
    private IUnitOrgLinkedService iUnitOrgLinkedService;
    @Resource
    private IMemService iMemService;
    @Resource
    private IOrgCommitteeService iOrgCommitteeService;
    @Resource
    private IOrgCommitteeElectService iOrgCommitteeElectService;
    @Resource
    IMemAllInfoService iMemAllInfoService;
    @Resource
    private IDevelopStepLogService iDevelopStepLogService;
    @Resource
    private IDevelopStepLogAllService iDevelopStepLogAllService;
    @Resource
    private IStatisticsYearService iStatisticsYearService;
    @Resource
    private IMemDevelopAllService iMemDevelopAllService;
    @Resource
    private IMemDevelopService iMemDevelopService;
    @Resource
    private IUnitCommitteeService iUnitCommitteeService;
    @Resource
    private IUnitCommitteeElectService iUnitCommitteeElectService;

    /**
     * 初始化修复党员、发展党员、本年度发展党员 国民经济
     */
    @Override
    public void repair() {
        LambdaQueryWrapper<Unit> unitLambdaQueryWrapper = Wrappers.lambdaQuery();
        // todo 单位国民经济不为空
        unitLambdaQueryWrapper.isNull(Unit::getDeleteTime)
                .isNotNull(Unit::getD194Code);
        List<Unit> unitList = iUnitService.list(unitLambdaQueryWrapper);
        for (Unit unit : unitList) {
            this.syncBusinessModelOrg(unit.getMainOrgCode(), unit.getCode());
        }
    }

    private void syncBusinessModelOrg(String code, String updateUnitCode) {
        //根据组织code查询组织情况
        Org orgByCode = iOrgService.findOrgByCode(code);
        if (ObjectUtil.isNull(orgByCode))return;
        //重新根据关联关系推算当前党组织的关联组织
        String d02Code = orgByCode.getD02Code();
        //与上级党组织相同，需要递归找寻上级进行处理
        String mainUnitCode;
        // 与上级联合党支部的上级是否不为单位的主组织
        //boolean parentIsNotMainOrg = false;
        if (StrUtil.equals(d02Code,CommonConstant.TWO)){
            mainUnitCode = this.findParentUnit(orgByCode.getParentCode());
        }else {
            //不是与上级党组织相同的情况下
            UnitOrgLinked byOrgCodeAndIsMainUnit = iUnitOrgLinkedService.findByOrgCodeAndIsMainUnit(orgByCode.getCode());
            //关联关系为空
            if (ObjectUtil.isNull(byOrgCodeAndIsMainUnit)){
                mainUnitCode=null;
            }else {
                mainUnitCode=byOrgCodeAndIsMainUnit.getUnitCode();
            }
        }
        log.info("mainUnitCode====找到的主单位code====> "+mainUnitCode);
        Unit unitByCode = iUnitService.findByCode(mainUnitCode);
        if (ObjectUtil.isNull(unitByCode))return;
        //递归寻找当前党组织的下级中选择与上级党组织相同的
        List<Org> orgList =new ArrayList<>();
        this.findSubset(code,orgList);
        log.info("this.findSubset(code,orgList)====下级选择与上级相同的个数====> "+orgList.size());
        List<Org> updateOrgList= new ArrayList<>();
        //修改党组织只要是单独建立的（单独建立，联合党支部），逻辑应该是跟着单位走
        // 单独建立 or 不与上级联合党支部  or (与上级联合支部，但是上级党组织为单位的主组织)!parentIsNotMainOrg
        String d194Code = StrUtil.isEmpty(unitByCode.getD194Code())?"":unitByCode.getD194Code();
        String d194Name = StrUtil.isEmpty(unitByCode.getD194Name())?"":unitByCode.getD194Name();
        String d195Code = StrUtil.isEmpty(unitByCode.getD195Code())?"":unitByCode.getD195Code();
        String d195Name = StrUtil.isEmpty(unitByCode.getD195Name())?"":unitByCode.getD195Name();
        if (!StrUtil.equals(d02Code,CommonConstant.TWO)){
            Org parentOrg=new Org();
            parentOrg.setId(orgByCode.getId());
            parentOrg.setCode(code);
            parentOrg.setD194Code(d194Code);
            parentOrg.setD194Name(d194Name);
            parentOrg.setD195Code(d195Code);
            parentOrg.setD195Name(d195Name);
            //如果勾选离退休，则直接设置为离退休
            if (Objects.equals(orgByCode.getIsRetire(), CommonConstant.ONE_INT)){
                parentOrg.setD194Code("U");
                parentOrg.setD195Code("V0000");
                parentOrg.setD194Name( "其他");
                parentOrg.setD195Name("无");
            }
            updateOrgList.add(parentOrg);
        }else {
            //与上级党组织相同，自己也当作子集处理
            orgList.add(orgByCode);
        }


        // TODO: 2023/12/4 修改党组织和党员联动情况====
        if (StrUtil.isNotEmpty(updateUnitCode)&&StrUtil.equalsAny(d02Code,CommonConstant.THREE,CommonConstant.FOUR)&&!StrUtil.equalsAny(updateUnitCode,mainUnitCode)){
            Unit byCode = iUnitService.findByCode(updateUnitCode);
            this.syncMem(byCode,d02Code,code);
        }else {
            this.syncMem(unitByCode,d02Code,code);
        }
        // 本年度发展党员
        saveBatchDevelopStepLog(updateOrgList, unitByCode);

        // 发展党员
        saveBatchMemDevelop(updateOrgList, unitByCode);
    }

    /***
     * 修改编辑同步党员
     */
    public void  syncMem(Unit unit,String d02Code,String orgCode){

        //本级党员需要特殊处理一下，因为存在村的两位成员的概念（而村可能是一个党委也可能是一个党支部，所以他的两位成员极有可能在当前党委下面的，也有可能在当前党支部下面）
        List<Mem> updateMemList=new ArrayList<>();
        //这是以前得思路， 直接找下面下级所有党组织得党员

        String unitByCodeCode=unit.getCode();
        String d04Code = unit.getD04Code();
        //获取统计关系所在单位是当前单位得所有党员（此操作倚赖党组织更改关联单位同步党员及党员自己编辑修改统计关系所在单位得时候对于mem表得statistical_unit字段同步）
        List<Mem> byMemOrgCode = iMemService.findByStatistical(unitByCodeCode);
        // todo  2023-12-18 筛选出d194Code为空的数据
        byMemOrgCode = byMemOrgCode.stream().filter(e -> StrUtil.isEmpty(e.getD194Code())).collect(Collectors.toList());
        if(CollUtil.isEmpty(byMemOrgCode)){
            return;
        }

        //村的两委成员，改党组织的时候需要看建立单位情况
        List<String> deailIsMemCode=new ArrayList<>();
        //如果需要修改得单位是村或者社区，并且党组织建立单位情况是单独建立
        //StrUtil.equalsAny(d04Code, "922", "923")&&
        String d194Code = StrUtil.isEmpty(unit.getD194Code())?"":unit.getD194Code();
        String d194Name = StrUtil.isEmpty(unit.getD194Name())?"":unit.getD194Name();
        String d195Code = StrUtil.isEmpty(unit.getD195Code())?"":unit.getD195Code();
        String d195Name = StrUtil.isEmpty(unit.getD195Name())?"":unit.getD195Name();
        if (StrUtil.equals(d02Code, CommonConstant.ONE)){
            Set<String> memCodeSet = new HashSet<>();
            //找寻当前党组织得班子成员
            OrgCommitteeElect orgStatisticsYearElect = iOrgCommitteeElectService.findOrgStatisticsYearElect(orgCode);
            if (ObjectUtil.isNotNull(orgStatisticsYearElect)){
                String electCode = orgStatisticsYearElect.getCode();
                List<OrgCommittee> commitByElect = iOrgCommitteeService.findCommitByElect(electCode);
                if(CollUtil.isNotEmpty(commitByElect)){
                    memCodeSet.addAll(commitByElect.stream().map(OrgCommittee::getMemCode).collect(Collectors.toSet()));
                }
            }
            //单位班子成员
            UnitCommitteeElect unitCommitteeElect = iUnitCommitteeElectService.newElect(unitByCodeCode);
            if(Objects.nonNull(unitCommitteeElect) && StrUtil.isNotEmpty(unitCommitteeElect.getCode())){
                List<UnitCommittee> unitCommitteeList = iUnitCommitteeService.findAllByElect(unitCommitteeElect.getCode());
                if(CollUtil.isNotEmpty(unitCommitteeList)){
                    memCodeSet.addAll(unitCommitteeList.stream().map(UnitCommittee::getMemCode).collect(Collectors.toSet()));
                }
            }

            // 处理单位及党组织班子成员
            for (String memCode : memCodeSet) {
                if(StrUtil.isBlank(memCode))continue;
                Mem updateMem = new Mem();
                Mem byCode = iMemService.findByCode(memCode);
                if (ObjectUtil.isNull(byCode))continue;
                if (StrUtil.equalsAny(d04Code, "922", "923")&&StrUtil.isNotEmpty(memCode)){
                    updateMem.setCode(memCode);
                    updateMem.setD194Code(d194Code);
                    updateMem.setD194Name(d194Name);
                    updateMem.setD195Code(d195Code);
                    updateMem.setD195Name(d195Name);
                    updateMem.setId(byCode.getId());
                    updateMemList.add(updateMem);
                    deailIsMemCode.add(memCode);
                }else {
                    String statisticalUnit = byCode.getStatisticalUnit();
                    if (StrUtil.isNotEmpty(statisticalUnit)){
                        Unit memIsBycCodeUnit = iUnitService.findByCode(statisticalUnit);
                        if (ObjectUtil.isNotNull(memIsBycCodeUnit)&&StrUtil.isNotEmpty(memIsBycCodeUnit.getD194Code())){
                            updateMem.setCode(memCode);
                            updateMem.setD194Code(StrUtil.isEmpty(memIsBycCodeUnit.getD194Code())?"":memIsBycCodeUnit.getD194Code());
                            updateMem.setD194Name(StrUtil.isEmpty(memIsBycCodeUnit.getD194Name())?"":memIsBycCodeUnit.getD194Name());
                            updateMem.setD195Code(StrUtil.isEmpty(memIsBycCodeUnit.getD195Code())?"":memIsBycCodeUnit.getD195Code());
                            updateMem.setD195Name(StrUtil.isEmpty(memIsBycCodeUnit.getD195Name())?"":memIsBycCodeUnit.getD195Name());
                            updateMem.setId(byCode.getId());
                            updateMemList.add(updateMem);
                            deailIsMemCode.add(memCode);
                        }
                    }
                }
            }
        }

        //处理所有下级党员
        List<Record> d196List = CacheUtils.getDictNonHump(DictConstant.DICT_D196);
        Map<String, Record> d196Map = d196List.stream().collect(Collectors.toMap(e -> e.getStr("key"), e -> e, (e1, e2) -> e1));
        byMemOrgCode.stream().forEach(mem -> {
            log.info("顶层memcode======>"+mem.getCode());
            //如果此党员是前面已经处理过的，则不需要处理（比如村党总支的党员是在下属支部的、或者其他党支部的）
            if (ObjectUtil.isNotNull(deailIsMemCode)&&deailIsMemCode.contains(mem.getCode()))return;
            Mem updateMem=new Mem();
            updateMem.setId(mem.getId());
            updateMem.setCode(mem.getCode());
            //默认情况下是跟着单位走的
            updateMem.setD194Code(d194Code);
            updateMem.setD194Name(d194Name);
            updateMem.setD195Code(d195Code);
            updateMem.setD195Name(d195Name);
            //还有一种情况，修改党组织下得党员（党员在其他非我得下级党支部或者党委有任职）（村里面是有任职情况得,需要置换为跟相应的村社区是一直的）
            String unitType = this.isIntOtherOrg(mem.getCode());
            log.info("intOtherOrg===>"+unitType);
            if(StrUtil.isBlank(unitType)){
                //单位班子成员
                unitType = this.isIntOtherUnit(mem.getCode());
                log.info("isIntOtherUnit===>"+unitType);
            }
            if (StrUtil.isNotEmpty(unitType)){
                if (unitType.equals("922")){
                    updateMem.setD194Code("S9610");
                    updateMem.setD195Code("V0000");
                    updateMem.setD194Name("社区居民自治组织");
                    updateMem.setD195Name("无");

                }
                if (unitType.equals("923")){
                    updateMem.setD194Code("S9620");
                    updateMem.setD195Code("V0000");
                    updateMem.setD194Name("村民自治组织");
                    updateMem.setD195Name("无");
                }
                updateMemList.add(updateMem);
                return;
            }

            //如果是字典表特殊类， 就定点归集
            String d09Code = mem.getD09Code();
            if(d196Map.containsKey(d09Code)){
                Record record = d196Map.get(d09Code);
                updateMem.setD194Code(record.getStr("mapNewKey"));
                updateMem.setD194Name(record.getStr("mapNewKeyName"));
                updateMem.setD195Code(record.getStr("oldKey"));
                updateMem.setD195Name(record.getStr("oldName"));
                updateMemList.add(updateMem);
                return;
            }

            //如果是单位是村、农村社区的农牧渔民或者工作岗位是农牧渔民
            if (StrUtil.equalsAny(d04Code, "922", "923")||(StrUtil.equalsAny(d09Code, "10", "13", "14"))){
                //查看相关的一线情况相关问题
                String d21Code = mem.getD21Code();
                //存在一线情况置换为农牧渔
                updateMem.setD194Code("A");
                updateMem.setD195Code("V0000");
                updateMem.setD194Name("农、林、牧、渔业");
                updateMem.setD195Name("无");
                //没有一线情况，置换为其他
                if (ObjectUtil.isNull(d21Code)||StrUtil.equals(d21Code,CommonConstant.ZERO)){
                    updateMem.setD194Code("U");
                    updateMem.setD195Code("V0000");
                    updateMem.setD194Name( "其他");
                    updateMem.setD195Name("无");
                }
            }
            updateMemList.add(updateMem);
        });

        List<MemAllInfo> updateList=new ArrayList<>();
        for (Mem mem : updateMemList) {
            MemAllInfo memAllByCode = iMemAllInfoService.findMemAllByCode(mem.getCode());
            if (ObjectUtil.isNull(memAllByCode))continue;
            MemAllInfo updateMemAll=new MemAllInfo();
            updateMemAll.setId(memAllByCode.getId());
            updateMemAll.setD194Code(mem.getD194Code());
            updateMemAll.setD194Name(mem.getD194Name());
            updateMemAll.setD195Code(mem.getD195Code());
            updateMemAll.setD195Name(mem.getD195Name());
            updateMemAll.setIndustry(this.setIndustry(mem.getD194Code()));
            updateList.add(updateMemAll);
        }

        iMemService.batchUpdate(updateMemList);
        iMemAllInfoService.batchUpdate(updateList);
    }


    /**
     * 本年度发展党员
     * @param updateOrgList
     * @param unitByCode
     */
    private void saveBatchDevelopStepLog(List<Org> updateOrgList,  Unit unitByCode){
        final String d04Code = unitByCode.getD04Code();
        List<DevelopStepLogAll> updateAllList = new ArrayList<>();
        List<DevelopStepLogAll> stepLogs = iDevelopStepLogAllService.list(new LambdaQueryWrapper<DevelopStepLogAll>()
                .eq(DevelopStepLogAll::getUnitCode, unitByCode.getCode())
                .in(DevelopStepLogAll::getOrgCode, updateOrgList.stream().map(Org::getCode).collect(Collectors.toList()))
                .apply("delete_time is null and d08_code = '3' and EXTRACT ( YEAR FROM \"topre_join_org_date\" ) = " + iStatisticsYearService.getStatisticalYear()));
        if(CollUtil.isEmpty(stepLogs)){
            return;
        }
        // todo 2023-12-18 筛选出d194Code为空的数据
        stepLogs = stepLogs.stream().filter(e -> StrUtil.isEmpty(e.getD194Code())).collect(Collectors.toList());
        if(CollUtil.isEmpty(stepLogs)){
            return;
        }

        final String d194Code = StrUtil.isBlank(unitByCode.getD194Code()) ? "" : unitByCode.getD194Code();
        final String d194Name = StrUtil.isBlank(unitByCode.getD194Name()) ? "" : unitByCode.getD194Name();
        final String d195Code = StrUtil.isBlank(unitByCode.getD195Code()) ? "" : unitByCode.getD195Code();
        final String d195Name = StrUtil.isBlank(unitByCode.getD195Name()) ? "" : unitByCode.getD195Name();

        //处理所有下级党员
        List<Record> d196List = CacheUtils.getDictNonHump(DictConstant.DICT_D196);
        Map<String, Record> d196Map = d196List.stream().collect(Collectors.toMap(e -> e.getStr("key"), e -> e, (e1, e2) -> e1));
        stepLogs.forEach(mem -> {
            //如果此党员是前面已经处理过的，则不需要处理（比如村党总支的党员是在下属支部的、或者其他党支部的）
            DevelopStepLogAll updateMem = new DevelopStepLogAll();
            updateMem.setId(mem.getId());
            updateMem.setCode(mem.getCode());
            //默认情况下是跟着单位走的
            updateMem.setD194Code(d194Code);
            updateMem.setD194Name(d194Name);
            updateMem.setD195Code(d195Code);
            updateMem.setD195Name(d195Name);

            //如果是字典表特殊类， 就定点归集
            String d09Code = mem.getD09Code();
            if(d196Map.containsKey(d09Code)){
                Record record = d196Map.get(d09Code);
                updateMem.setD194Code(record.getStr("mapNewKey"));
                updateMem.setD194Name(record.getStr("mapNewKeyName"));
                updateMem.setD195Code(record.getStr("oldKey"));
                updateMem.setD195Name(record.getStr("oldName"));
                updateAllList.add(updateMem);
                return;
            }

            //如果是单位是村、农村社区的农牧渔民或者工作岗位是农牧渔民
            if (StrUtil.equalsAny(d04Code, "922", "923")||(StrUtil.equalsAny(d09Code, "10", "13", "14"))){
                //查看相关的一线情况相关问题
                String d21Code = mem.getD21Code();
                //存在一线情况置换为农牧渔
                updateMem.setD194Code("A");
                updateMem.setD195Code("V0000");
                updateMem.setD194Name("农、林、牧、渔业");
                updateMem.setD195Name("无");
                //没有一线情况，置换为其他
                if (ObjectUtil.isNull(d21Code)||StrUtil.equals(d21Code,CommonConstant.ZERO)){
                    updateMem.setD194Code("U");
                    updateMem.setD195Code("V0000");
                    updateMem.setD194Name( "其他");
                    updateMem.setD195Name("无");
                }
            }
            updateAllList.add(updateMem);
        });

        List<DevelopStepLog> updateList = new ArrayList<>();
        for (DevelopStepLogAll data : updateAllList) {
            DevelopStepLog developStepLog = iDevelopStepLogService.findByCode(data.getCode());
            if (ObjectUtil.isNull(developStepLog))continue;
            DevelopStepLog developStepLog1 = new DevelopStepLog();
            developStepLog1.setId(developStepLog.getId());
            developStepLog1.setD194Code(data.getD194Code());
            developStepLog1.setD194Name(data.getD194Name());
            developStepLog1.setD195Code(data.getD195Code());
            developStepLog1.setD195Name(data.getD195Name());
            updateList.add(developStepLog1);
        }

        updateAllList.forEach(developStepLogAll -> developStepLogAll.setIndustry(this.setIndustry(developStepLogAll.getD194Code())));
        iDevelopStepLogService.updateBatchById(updateList);
        iDevelopStepLogAllService.updateBatchById(updateAllList);
    }

    /**
     * 发展党员
     * @param updateOrgList
     * @param unitByCode
     */
    private void saveBatchMemDevelop(List<Org> updateOrgList,  Unit unitByCode){
        final String d04Code = unitByCode.getD04Code();

        List<MemDevelopAll> updateAllList = new ArrayList<>();
        List<MemDevelopAll> develops = iMemDevelopAllService.list(new LambdaQueryWrapper<MemDevelopAll>()
                .eq(MemDevelopAll::getUnitCode, unitByCode.getCode())
                .in(MemDevelopAll::getOrgCode, updateOrgList.stream().map(Org::getCode).collect(Collectors.toList()))
                .isNull(MemDevelopAll::getDeleteTime));
        if(CollUtil.isEmpty(develops)){
            return;
        }

        // todo 2023-12-18 筛选出d194Code为空的数据
        develops = develops.stream().filter(e -> StrUtil.isEmpty(e.getD194Code())).collect(Collectors.toList());
        if(CollUtil.isEmpty(develops)){
            return;
        }

        final String d194Code = StrUtil.isBlank(unitByCode.getD194Code()) ? "" : unitByCode.getD194Code();
        final String d194Name = StrUtil.isBlank(unitByCode.getD194Name()) ? "" : unitByCode.getD194Name();
        final String d195Code = StrUtil.isBlank(unitByCode.getD195Code()) ? "" : unitByCode.getD195Code();
        final String d195Name = StrUtil.isBlank(unitByCode.getD195Name()) ? "" : unitByCode.getD195Name();

        //处理所有下级党员
        List<Record> d196List = CacheUtils.getDictNonHump(DictConstant.DICT_D196);
        Map<String, Record> d196Map = d196List.stream().collect(Collectors.toMap(e -> e.getStr("key"), e -> e, (e1, e2) -> e1));
        develops.forEach(mem -> {
            //如果此党员是前面已经处理过的，则不需要处理（比如村党总支的党员是在下属支部的、或者其他党支部的）
            MemDevelopAll updateMem = new MemDevelopAll();
            updateMem.setId(mem.getId());
            updateMem.setCode(mem.getCode());
            //默认情况下是跟着单位走的
            updateMem.setD194Code(d194Code);
            updateMem.setD194Name(d194Name);
            updateMem.setD195Code(d195Code);
            updateMem.setD195Name(d195Name);

            //如果是字典表特殊类， 就定点归集
            String d09Code = mem.getD09Code();
            if(d196Map.containsKey(d09Code)){
                Record record = d196Map.get(d09Code);
                updateMem.setD194Code(record.getStr("mapNewKey"));
                updateMem.setD194Name(record.getStr("mapNewKeyName"));
                updateMem.setD195Code(record.getStr("oldKey"));
                updateMem.setD195Name(record.getStr("oldName"));
                updateAllList.add(updateMem);
                return;
            }

            //如果是单位是村、农村社区的农牧渔民或者工作岗位是农牧渔民
            if (StrUtil.equalsAny(d04Code, "922", "923")||(StrUtil.equalsAny(d09Code, "10", "13", "14"))){
                //查看相关的一线情况相关问题
                String d21Code = mem.getD21Code();
                //存在一线情况置换为农牧渔
                updateMem.setD194Code("A");
                updateMem.setD195Code("V0000");
                updateMem.setD194Name("农、林、牧、渔业");
                updateMem.setD195Name("无");
                //没有一线情况，置换为其他
                if (ObjectUtil.isNull(d21Code)||StrUtil.equals(d21Code,CommonConstant.ZERO)){
                    updateMem.setD194Code("U");
                    updateMem.setD195Code("V0000");
                    updateMem.setD194Name( "其他");
                    updateMem.setD195Name("无");
                }
            }
            updateAllList.add(updateMem);
        });

        // 同步MemDevelop表
        List<MemDevelop> updateList = new ArrayList<>();
        for (MemDevelopAll data : updateAllList) {
            MemDevelop developStepLog = iMemDevelopService.findByCode(data.getCode());
            if (ObjectUtil.isNull(developStepLog))continue;
            MemDevelop memDevelop = new MemDevelop();
            memDevelop.setId(developStepLog.getId());
            memDevelop.setD194Code(data.getD194Code());
            memDevelop.setD194Name(data.getD194Name());
            memDevelop.setD195Code(data.getD195Code());
            memDevelop.setD195Name(data.getD195Name());
            updateList.add(memDevelop);
        }

        iMemDevelopService.updateBatchById(updateList);
        iMemDevelopAllService.updateBatchById(updateAllList);
    }

    private void findSubset(String orgCode,List<Org> orgList){
        List<Org> byParentCodeAndD02 = iOrgService.findByParentCodeAndD02(orgCode, CommonConstant.TWO);
        if (byParentCodeAndD02.size()==CommonConstant.ZERO_INT){
            return;
        }
        for (Org org : byParentCodeAndD02) {
            orgList.add(org);
            this.findSubset(org.getCode(),orgList);
        }
    }


    private void findParent(String parentOrgCode,List<String> parunit){
        Org orgByCode = iOrgService.findOrgByCode(parentOrgCode);
        //父级为空
        if (ObjectUtil.isNull(orgByCode)){
            return;
        }
        String d02Code = orgByCode.getD02Code();
        if (StrUtil.equals(d02Code,CommonConstant.TWO)){
            this.findParent(orgByCode.getParentCode(),parunit);
        }else {
            parunit.add(orgByCode.getCode());
        }
    }
    public String findParentUnit(String parentOrgCode){
        List<String> parunit=new ArrayList<>();
        this.findParent(parentOrgCode, parunit);
        if (parunit.size()>CommonConstant.ZERO_INT){
            UnitOrgLinked byOrgCodeAndIsMainUnit = iUnitOrgLinkedService.findByOrgCodeAndIsMainUnit(parunit.get(0));
            if (ObjectUtil.isNull(byOrgCodeAndIsMainUnit))return null;
            return byOrgCodeAndIsMainUnit.getUnitCode();
        }
        return null;
    }

    private UnitOrgLinked findParentUnitLinked(String parentOrgCode){
        List<String> parunit=new ArrayList<>();
        this.findParent(parentOrgCode, parunit);
        if (parunit.size()>CommonConstant.ZERO_INT){
            UnitOrgLinked byOrgCodeAndIsMainUnit = iUnitOrgLinkedService.findByOrgCodeAndIsMainUnit(parunit.get(0));
            if (ObjectUtil.isNull(byOrgCodeAndIsMainUnit))return null;
            return byOrgCodeAndIsMainUnit;
        }
        return null;
    }


    /**
     * 处理党员在其他单位存在任职的情况
     * @param memCode
     * @return
     */
    public String isIntOtherOrg(String memCode){
        //查询党员是否在其他地方有任职
        List<OrgCommittee> orgCommitByMemCode = iOrgCommitteeService.findOrgCommitByMemCode(memCode,null);
        if (orgCommitByMemCode.size()==CommonConstant.ZERO_INT)return null;
        //查询所有党组织1.可能是单独建立的可以直接找，2.如果是上级相同的，需要单独隔离开
        List<Org> byOrgCodeList = iOrgService.findByOrgCodeList(orgCommitByMemCode.stream().map(OrgCommittee::getOrgCode).collect(Collectors.toList()));

        //不与上级党组织相同的
        List<String> orgD02IsNotTwoOrgCode = byOrgCodeList.stream().filter(org -> !StrUtil.equals(org.getD02Code(), CommonConstant.TWO)).map(Org::getCode).collect(Collectors.toList());
        //计算找寻所有党组织关联单位情况
        List<UnitOrgLinked> byOrgCodeAndIsMain=new ArrayList<>() ;
        if (orgD02IsNotTwoOrgCode.size()>CommonConstant.ZERO_INT){
            List<UnitOrgLinked> byOrgCodeAndIsMainUnit = iUnitOrgLinkedService.findByOrgCodeAndIsMainUnit(orgD02IsNotTwoOrgCode);
            byOrgCodeAndIsMain.addAll(byOrgCodeAndIsMainUnit);
        }

        //与上级党组织相同的
        List<Org> orgD02IsTwo = byOrgCodeList.stream().filter(org -> StrUtil.equals(org.getD02Code(), CommonConstant.TWO)).collect(Collectors.toList());
        orgD02IsTwo.forEach(org -> {
            UnitOrgLinked parentUnitLinked = this.findParentUnitLinked(org.getParentCode());
            if (ObjectUtil.isNotNull(parentUnitLinked)){
                //放如与上级党组织相同的UnitLinked
                byOrgCodeAndIsMain.add(parentUnitLinked);
            }
        });
        if (byOrgCodeAndIsMain.size()==CommonConstant.ZERO_INT)return null;
        //党组织关联的主单位是村或者农村社区的集合
        Map<String, UnitOrgLinked> unitOrgLinkedMap = byOrgCodeAndIsMain.stream().filter(unitLinked -> StrUtil.equalsAny(unitLinked.getUnitType(), "922", "923"))
                .collect(Collectors.toMap(UnitOrgLinked::getOrgCode, e -> e, (e1, e2) -> e1));

        //所有任职是在有效届次内的
        List<String> allElectCode = orgCommitByMemCode.stream().map(OrgCommittee::getElectCode).collect(Collectors.toList());
        Map<String, OrgCommitteeElect> orgCommitteeElectMap = iOrgCommitteeElectService.electIsNow(allElectCode).stream().collect(Collectors.toMap(OrgCommitteeElect::getOrgCode, e -> e, (e1, e2) -> e1));
        Collection<String> intersection =
                CollectionUtils.intersection(orgCommitteeElectMap.keySet(), unitOrgLinkedMap.keySet());
        ArrayList<String> strings = new ArrayList<>(intersection);

        if (strings.size()>CommonConstant.ZERO_INT){
            String s = strings.get(CommonConstant.ZERO_INT);
            UnitOrgLinked unitOrgLinked = unitOrgLinkedMap.get(s);
            return unitOrgLinked.getUnitType();
        }
        //  boolean b = this.hasDuplicateKeys(orgCommitteeElectMap, unitOrgLinkedMap);
        return null;
    }


    /**
     * 处理党员在其他单位存在任职的情况
     * @param memCode
     * @return
     */
    public String isIntOtherUnit(String memCode){
        //查询党员是否在其他地方有任职
        List<UnitCommittee> unitCommitByMemCode = iUnitCommitteeService.findUnitCommitByMemCode(memCode);
        if (unitCommitByMemCode.size()==CommonConstant.ZERO_INT)return null;
        //查询所有党组织1.可能是单独建立的可以直接找，2.如果是上级相同的，需要单独隔离开
        List<Unit> byUnitCodeList = iUnitService.findByCodes(unitCommitByMemCode.stream().map(UnitCommittee::getUnitCode).collect(Collectors.toList()));
        Map<String, Unit> unitCodeMap = byUnitCodeList.stream().filter(e ->  StrUtil.equalsAny(e.getD04Code(), "922", "923"))
                .collect(Collectors.toMap(Unit::getCode, e -> e, (e1, e2) -> e1));
        //所有任职是在有效届次内的
        List<String> allElectCode = unitCommitByMemCode.stream().map(UnitCommittee::getElectCode).collect(Collectors.toList());
        Map<String, UnitCommitteeElect> unitCommitteeElectMap = iUnitCommitteeElectService.electIsNow(allElectCode).stream().collect(Collectors.toMap(UnitCommitteeElect::getUnitCode, e -> e, (e1, e2) -> e1));
        Collection<String> intersection =
                CollectionUtils.intersection(unitCommitteeElectMap.keySet(), unitCodeMap.keySet());
        ArrayList<String> strings = new ArrayList<>(intersection);

        if (strings.size()> CommonConstant.ZERO_INT){
            String s = strings.get(CommonConstant.ZERO_INT);
            Unit unit = unitCodeMap.get(s);
            return unit.getD04Code();
        }
        //  boolean b = this.hasDuplicateKeys(orgCommitteeElectMap, unitOrgLinkedMap);
        return null;
    }

    private String setIndustry(String d194Code) {
        if (StrUtil.startWith(d194Code,"A")){
            return ("1");
        }else if (StrUtil.startWithAny(d194Code,"B","C","D","E")){
            return("2");
        }else if(!StrUtil.equals(d194Code,"U")){
            return("3");
        }
        return ("0");
    }

}
