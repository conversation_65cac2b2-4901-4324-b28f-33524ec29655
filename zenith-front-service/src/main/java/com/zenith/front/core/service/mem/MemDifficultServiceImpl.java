package com.zenith.front.core.service.mem;

import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.zenith.front.api.mem.IMemDifficultService;
import com.zenith.front.common.constant.CommonConstant;
import com.zenith.front.common.kit.ModelUtils;
import com.zenith.front.common.kit.StrKit;
import com.zenith.front.common.kit.StringToBooleanConverter;
import com.zenith.front.core.cache.CacheUtils;
import com.zenith.front.dao.mapper.mem.MemDifficultMapper;
import com.zenith.front.model.dto.MemDifficultDTO;
import com.zenith.front.model.message.OutMessage;
import com.zenith.front.model.message.Status;
import com.zenith.front.model.bean.MemDifficult;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;

import static com.zenith.front.model.constant.UserConstant.USER_CONTEXT;

/**
 * <p>
 * 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-07-10
 */
@Service
public class MemDifficultServiceImpl extends ServiceImpl<MemDifficultMapper, MemDifficult> implements IMemDifficultService {

    @Resource
    MemDifficultMapper memDifficultMapper;

    @Override
    public MemDifficult findByMemCode(String memCode) {
        LambdaQueryWrapper<MemDifficult> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.isNull(MemDifficult::getDeleteTime)
                .eq(MemDifficult::getMemCode, memCode)
                .eq(MemDifficult::getIsDifficulty, CommonConstant.ONE_INT);
        return getOne(queryWrapper);
    }

    @Override
    public MemDifficult findByCode(String code) {
        LambdaQueryWrapper<MemDifficult> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(MemDifficult::getCode, code)
                .isNull(MemDifficult::getDeleteTime);
        return getOne(wrapper);
    }

    /**
     * 新增困难党员
     *
     * @param memDifficultDTO
     * @return
     */
    @Override
    public OutMessage addMemDifficult(MemDifficultDTO memDifficultDTO) throws Exception {
        String memCode = memDifficultDTO.getMemCode();
        MemDifficult memDifficult = this.findByMemCode(memCode);
        boolean flag;
        if (ObjectUtil.isNull(memDifficult)) {
            // 没有对象,进行新增
            memDifficultDTO.setCode(StrKit.getRandomUUID());
            memDifficultDTO.setCreateTime(new Date());
            memDifficultDTO.setUpdateAccount(USER_CONTEXT.get().getUser().getAccount());
            memDifficultDTO.setUpdateTime(new Date());
            memDifficultDTO.setIsHistory(CommonConstant.ZERO_INT);
            memDifficult = memDifficultDTO.toModel();
            memDifficult.setId(null);
            flag = this.save(memDifficult);
        } else {
            // 进行修改
            if (CommonConstant.ZERO_INT == memDifficultDTO.getIsDisease()) {
                memDifficultDTO.setD56Code("");
                memDifficultDTO.setD56Name("");
            }
            memDifficultDTO.setId(memDifficult.getId());
            memDifficultDTO.setUpdateTime(new Date());
            memDifficultDTO.setUpdateAccount(USER_CONTEXT.get().getUser().getAccount());
            ModelUtils.copyPropertiesWithNull(memDifficultDTO, memDifficult, "id", "code", "createTime", "isHistory");
            flag = this.updateById(memDifficult);
        }

        return new OutMessage<>(flag ? Status.SUCCESS : Status.FAIL, memDifficult);
    }

    /**
     * 根据人员查询困难党员信息
     *
     * @param memCode
     * @return
     */
    @Override
    public OutMessage findByMemCodeOut(String memCode) {
        MemDifficult memDifficult = this.findByMemCode(memCode);
        if (ObjectUtil.isNull(memDifficult)) {
            return new OutMessage<>(Status.SUCCESS, memDifficult);
        }
        String diffOrgCode = memDifficult.getDiffOrgCode();
        if (!diffOrgCode.startsWith(USER_CONTEXT.get().getUserRolePermission().getOrgCode())) {
            return new OutMessage<>(Status.PERMISSION_DENIED);
        }
        return new OutMessage<>(Status.SUCCESS, memDifficult);
    }

    /**
     * 修改困难党员信息
     *
     * @param memDifficultDTO
     * @return
     */
    @Override
    public OutMessage updateMemDifficult(MemDifficultDTO memDifficultDTO) throws Exception {
        String code = memDifficultDTO.getCode();
        MemDifficult memDifficult = this.findByCode(code);
        if (ObjectUtil.isNull(memDifficult)) {
            return new OutMessage<>(Status.OBJEC_NOT_EXIST);
        }
        memDifficultDTO.setId(memDifficult.getId());
        memDifficultDTO.setUpdateTime(new Date());
        memDifficultDTO.setUpdateAccount(USER_CONTEXT.get().getUser().getAccount());
        ModelUtils.copyPropertiesWithNull(memDifficultDTO, memDifficult, "id", "code", "createTime", "isHistory");
        boolean flag = updateById(memDifficult);
        return new OutMessage<>(flag ? Status.SUCCESS : Status.FAIL, memDifficult);
    }

    /**
     * 获取困难党员列表
     *
     * @param pageNum
     * @param pageSize
     * @param orgCode
     * @param subordinate
     * @param memName
     * @return
     */
    @Override
    public OutMessage getList(int pageNum, int pageSize, String orgCode, String subordinate, String memName) {
        Boolean convert = StringToBooleanConverter.convert(subordinate);
        LambdaQueryWrapper<MemDifficult> wrapper = new QueryWrapper<MemDifficult>().select().lambda();
        wrapper.eq(MemDifficult::getIsDifficulty, CommonConstant.ONE_INT)
                .like(StrKit.notBlank(memName), MemDifficult::getMemName, memName)
                .isNull(MemDifficult::getDeleteTime);
        if (Boolean.TRUE.equals(convert)) {
            //显示下级
            wrapper.likeRight(MemDifficult::getDiffOrgCode, orgCode);
        } else {
            //显示本身
            wrapper.eq(MemDifficult::getDiffOrgCode, orgCode);
        }
        wrapper.orderByDesc(MemDifficult::getCreateTime).orderByDesc(MemDifficult::getId);
        Page<MemDifficult> page = memDifficultMapper.selectPage(new Page<>(pageNum, pageSize), wrapper);
        page.getRecords().forEach(memDifficult -> {
            String orgCode1 = memDifficult.getOrgCode();
            String orgName = CacheUtils.getOrgName(orgCode1);
            memDifficult.setOrgName(orgName);
            String memCode = memDifficult.getMemCode();
            String memName1 = CacheUtils.getMemName(memCode);
            memDifficult.setMemName(memName1);
        });
        return new OutMessage<>(Status.SUCCESS, page);
    }

    /**
     * 删除困难党员
     *
     * @param code
     * @return
     */
    @Override
    public OutMessage delMemDifficult(String code) {
        MemDifficult byCode = this.findByCode(code);
        MemDifficult memDifficult = new MemDifficult();
        memDifficult.setId(byCode.getId());
        memDifficult.setCode(byCode.getCode());
        memDifficult.setCreateTime(byCode.getCreateTime());
        memDifficult.setUpdateAccount(USER_CONTEXT.get().getUser().getAccount());
        memDifficult.setUpdateTime(new Date());
        memDifficult.setIsHistory(CommonConstant.ONE_INT);
        memDifficult.setIsDifficulty(CommonConstant.ZERO_INT);
        boolean flag = updateById(memDifficult);
        return new OutMessage<>(flag ? Status.SUCCESS : Status.FAIL);
    }


}
