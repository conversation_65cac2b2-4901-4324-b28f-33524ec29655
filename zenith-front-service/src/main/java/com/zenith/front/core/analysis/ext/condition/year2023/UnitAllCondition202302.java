package com.zenith.front.core.analysis.ext.condition.year2023;

import com.b1809.analysis.extend.GenSqlConditionFuc;
import org.jooq.Condition;
import org.jooq.Field;

import static org.jooq.impl.DSL.*;

/**
 * 软弱涣散
 *
 * <AUTHOR>
 * @Date 2021/8/16 15:49
 * @Version 1.0
 */
public class UnitAllCondition202302 implements GenSqlConditionFuc {

    @Override
    public String getTableName() {
        return "ccp_unit_all_202302";
    }

    @Override
    public Condition create(String orgCode, String orgLevelCode) {
        return noCondition().and(getLevelCodeField().like(orgLevelCode + "%")
                .or(field(name( "main_unit_org_code"), String.class).like(orgLevelCode + "%")));
    }

    @Override
    public Field<?> getLevelCodeField() {
        return field(name("create_unit_org_code"), String.class);
    }
}
