package com.zenith.front.core.service.org;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.thread.ThreadUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.zenith.front.api.org.IOrgAllService;
import com.zenith.front.api.org.IOrgDevelopRightsService;
import com.zenith.front.api.org.IOrgService;
import com.zenith.front.common.constant.CommonConstant;
import com.zenith.front.common.kit.HttpKit;
import com.zenith.front.core.cache.CacheUtils;
import com.zenith.front.core.kit.CollectionUtil;
import com.zenith.front.dao.mapper.org.OrgDevelopRightsMapper;
import com.zenith.front.dao.mapper.org.OrgMapper;
import com.zenith.front.dao.mapper.user.UserRolePermissionMapper;
import com.zenith.front.model.bean.Org;
import com.zenith.front.model.bean.OrgAll;
import com.zenith.front.model.bean.OrgDevelopRights;
import com.zenith.front.model.dto.CommonParamDto;
import com.zenith.front.model.dto.OrgListDTO;
import com.zenith.front.model.message.OutMessage;
import com.zenith.front.model.message.Status;
import com.zenith.front.model.vo.OrgDevelopRightsVO;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <p>
 * 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-12-15
 */
@Service
public class OrgDevelopRightsServiceImpl extends ServiceImpl<OrgDevelopRightsMapper, OrgDevelopRights> implements IOrgDevelopRightsService {

    @Resource
    private IOrgService iOrgService;
    @Resource
    private OrgMapper orgMapper;
    @Resource
    private IOrgAllService iOrgAllService;
    @Resource
    private UserRolePermissionMapper userRolePermissionMapper;

    @Value("${sync_flow_push}")
    private String sync_flow_push;

    private OrgDevelopRights findByOrgCode(String orgCode) {
        LambdaQueryWrapper<OrgDevelopRights> wrapper = new LambdaQueryWrapper<OrgDevelopRights>()
                .eq(OrgDevelopRights::getOrgCode, orgCode).isNull(OrgDevelopRights::getDeleteTime).last("limit 1");
        return getOne(wrapper);
    }

    @Override
    public OutMessage<?> getList(OrgListDTO orgListDTO) {
        Page page = new Page<>(orgListDTO.getPageNum(), orgListDTO.getPageSize());
        Page<Org> list = orgMapper.getOrgDevelopRightsList(page, orgListDTO);
        Page<OrgAll> orgPage = new Page<>();
        BeanUtils.copyProperties(list, orgPage);
        Map<String, String> d01Map = CollectionUtil.listRecordToMap(CacheUtils.getDic("dict_d01"), "key", "name");
        Map<String, String> d03Map = CollectionUtil.listRecordToMap(CacheUtils.getDic("dict_d03"), "key", "name");
        List<OrgAll> records = new ArrayList<>();
        list.getRecords().forEach(org -> {
            OrgAll orgAll = new OrgAll();
            BeanUtils.copyProperties(org, orgAll);
            orgAll.setD01Name(d01Map.getOrDefault(org.getD01Code(), ""));
            orgAll.setD03Name(d03Map.getOrDefault(org.getD03Code(), ""));
            records.add(orgAll);
        });
        orgPage.setRecords(records);
        return new OutMessage<>(Status.SUCCESS, orgPage);
    }

    @Override
    public OutMessage<?> save(CommonParamDto data) {
        List<String> codeList = data.getCodeList();
        if (CollUtil.isEmpty(codeList)) {
            return new OutMessage<>(Status.SELECT_OBJECT_CAN_NOT_EMPTY);
        }

        // TODO: 2022/8/10 增加一是各级地方委员会，如县级委员会不能选；二是根据《中国共产党发展党员工作细则》规定：乡镇（街道）党委所属的基层党委，不能审批预备党员。
        for (String code : codeList) {
            //查询所有的上级党组织是否包含乡镇（街道），注： 乡镇本级党委是可以选的
            List<Org> allParentOrg = iOrgService.findAllParentOrg(code);
            List<String> parentCodeList = allParentOrg.stream().map(Org::getCode).collect(Collectors.toList());
            List<OrgAll> byOrgCodeList = iOrgAllService.findByOrgCodeList(parentCodeList);
            Org orgByCode = iOrgService.findOrgByCode(code);
            String d01Code = orgByCode.getD01Code();
            if (d01Code.startsWith(CommonConstant.ONE)) {
                return (new OutMessage<String>(Status.ORG_PROPERTY_TYPE_ERRO).format(orgByCode.getName()));
            }
            for (OrgAll orgAll : byOrgCodeList) {
                String orgAllCode = orgAll.getCode();
                //如果是本级， 同时是乡镇那些， 需要额外判断是否自己关联
                boolean isCounty = orgAll.getD04Code().startsWith(CommonConstant.NINETY_ONE);
                String d02Code = null == orgAll.getD02Code() ? "" : orgAll.getD02Code();
                boolean isHe = d02Code.equals(CommonConstant.ONE);
                //如果是自己， 是乡镇，是自己建立的
                if (orgAllCode.equals(code) && isCounty && isHe) {
                    continue;
                }
                //如果是自己，是乡镇，上级选择上级相同赋予的乡镇
                if (orgAllCode.equals(code) && isCounty && !isHe) {
                    return (new OutMessage<String>(Status.ORG_PROPERTY_ERRO).format(orgByCode.getName()));
                }
                //不是本级， 就要看上级是不是乡镇，如果是或者存在那一定是乡镇那些
                if (!orgAllCode.equals(code) && orgAll.getD04Code().startsWith(CommonConstant.NINETY_ONE)) {
                    return (new OutMessage<String>(Status.ORG_PROPERTY_ERRO).format(orgByCode.getName()));
                }
            }
        }

        List<OrgDevelopRights> dbList = list(new LambdaQueryWrapper<OrgDevelopRights>().in(OrgDevelopRights::getOrgCode, codeList).isNull(OrgDevelopRights::getDeleteTime));
        if (CollUtil.isNotEmpty(dbList)) {
            dbList.forEach(e -> e.setDeleteTime(new Date()));
            updateBatchById(dbList, dbList.size());
        }
        List<Org> list = iOrgService.list(new LambdaQueryWrapper<Org>().in(Org::getCode, codeList));
        List<OrgDevelopRights> saveList = new ArrayList<>();
        List<Org> updateOrgList = new ArrayList<>();
        list.forEach(org -> {
            OrgDevelopRights rights = new OrgDevelopRights();
            rights.setZbCode(org.getZbCode());
            rights.setOrgCode(org.getCode());
            rights.setOrgLevelCode(org.getOrgCode());
            rights.setName(org.getName());
            rights.setCreateTime(new Date());
            Org updateOrg = new Org();
            updateOrg.setId(org.getId());
            updateOrg.setIsApprovalMem(CommonConstant.ONE_INT);
            updateOrgList.add(updateOrg);
            saveList.add(rights);
        });
        boolean b = saveBatch(saveList, 100);
        // TODO: 2022/5/5 处理新增完成后，修改触发中间交换区
        if (b) {
            // 处理这个党组织的用户拥有预备党员审批权限
            List<String> orgCodes = saveList.stream().map(OrgDevelopRights::getOrgCode).filter(StrUtil::isNotBlank).collect(Collectors.toList());
            userRolePermissionMapper.updateUserPermissionDevelop(orgCodes, "1");

            List<OrgAll> orgAllList = iOrgAllService.list(new LambdaQueryWrapper<OrgAll>().in(OrgAll::getCode, codeList));
            List<OrgAll> orgAllUpdateList = new ArrayList<>();
            orgAllList.forEach(orgAll -> {
                OrgAll updateOrgAll = new OrgAll();
                updateOrgAll.setId(orgAll.getId());
                updateOrgAll.setIsApprovalMem(CommonConstant.ONE_INT);
                updateOrgAll.setCode(orgAll.getCode());
                orgAllUpdateList.add(updateOrgAll);
            });
            //触发修改org的是否具有预备党员审批权限
            iOrgService.updateBatchById(updateOrgList);
            //修改触发orgAll表得是否具有预备党员审批权限
            iOrgAllService.updateBatchById(orgAllUpdateList);
            ThreadUtil.execAsync(() -> {
                //触发修改中间交换区修改相关信息
                orgAllUpdateList.forEach(orgAll -> iOrgService.initExchangeOrg(orgAll.getCode()));
            });
        }
        return new OutMessage<>(b ? Status.SUCCESS : Status.FAIL);
    }

    @Override
    public OutMessage<?> del(String code) {
        OrgDevelopRights developRights = this.findByOrgCode(code);
        if (Objects.isNull(developRights)) {
            return new OutMessage<>(Status.OBJEC_NOT_EXIST);
        }
        developRights.setDeleteTime(new Date());
        boolean b = updateById(developRights);
        //触发撤销中间交换区
        if (b) {
            // 处理这个党组织的用户拥有预备党员审批权限
            userRolePermissionMapper.updateUserPermissionDevelop(Collections.singletonList(code), "0");

            String orgCode = developRights.getOrgCode();
            OrgAll byCode = iOrgAllService.findByCode(orgCode);
            OrgAll updateOrgAll = new OrgAll();
            updateOrgAll.setId(byCode.getId());
            updateOrgAll.setIsApprovalMem(CommonConstant.ZERO_INT);

            Org orgByCode = iOrgService.findOrgByCode(orgCode);
            Org updateOrg = new Org();
            updateOrg.setId(orgByCode.getId());
            updateOrg.setIsApprovalMem(CommonConstant.ZERO_INT);

            iOrgAllService.updateById(updateOrgAll);
            iOrgService.updateById(updateOrg);
            //触发中间交换区
            iOrgService.initExchangeOrg(byCode.getCode());
        }
        return new OutMessage<>(b ? Status.SUCCESS : Status.FAIL);
    }

    @Override
    public List<OrgDevelopRights> findOrgDevelopRightsByOrgCode(String orgCode) {
        return list(
                new LambdaQueryWrapper<OrgDevelopRights>()
                        .eq(OrgDevelopRights::getOrgCode, orgCode)
                        .isNull(OrgDevelopRights::getDeleteTime)
        );
    }


    @Override
    public List<OrgDevelopRights> findOrgDevelopRightsByLevelCode(String leveCode) {
        return list(
                new LambdaQueryWrapper<OrgDevelopRights>()
                        .likeRight(OrgDevelopRights::getOrgLevelCode, leveCode)
                        .isNull(OrgDevelopRights::getDeleteTime)
        );
    }

    @Override
    public List<OrgDevelopRights> findOrgDevelopRightsByCodeList(List<String> orgCodeList) {
        return list(
                new LambdaQueryWrapper<OrgDevelopRights>()
                        .in(OrgDevelopRights::getOrgCode, orgCodeList)
                        .isNull(OrgDevelopRights::getDeleteTime).orderByAsc(OrgDevelopRights::getOrgLevelCode)
        );
    }

    @Override
    public String findOrgDevelopRightsByOrgCode(List<String> orgCodeList) {
        if (CollUtil.isEmpty(orgCodeList)) {
            return "";
        }
        OrgDevelopRights orgDevelopRights = getOne(
                new LambdaQueryWrapper<OrgDevelopRights>()
                        .in(OrgDevelopRights::getOrgCode, orgCodeList)
                        .isNull(OrgDevelopRights::getDeleteTime)
                        .orderByDesc(OrgDevelopRights::getOrgLevelCode)
                        .last(" limit 1 ")
                        .select(OrgDevelopRights::getOrgCode)
        );
        if (Objects.isNull(orgDevelopRights)) {
            return "";
        }
        return orgDevelopRights.getOrgCode();
    }

    /**
     * 详情
     *
     * @param orgCode 党组织code
     * @return
     */
    @Override
    public OutMessage<?> find(String orgCode) {
        final OrgDevelopRightsVO result = new OrgDevelopRightsVO();
        LambdaQueryWrapper<Org> wrapper = Wrappers.lambdaQuery();
        wrapper.eq(Org::getCode, orgCode);
        Org org = orgMapper.selectOne(wrapper);
        if(Objects.isNull(org) || StrUtil.isBlank(org.getCode())){
            return new OutMessage<>(Status.OBJEC_NOT_EXIST);
        }
        result.setContacter(org.getContacter());
        result.setOrgName(org.getName());
        result.setContactPhone(org.getContactPhone());
        result.setPostAddress(org.getPostAddress());
        result.setCode(org.getCode());
        // 上级党组织唯一码
        String parentOrgCode = org.getParentCode();
        wrapper.clear();
        wrapper.eq(Org::getCode, parentOrgCode);
        org = orgMapper.selectOne(wrapper);
        result.setParentOrgName(Objects.nonNull(org) ? org.getName() : null);

        // 请求中间交换区获取D01001
        JSONObject postJson = new JSONObject();
        postJson.put("orgCode", orgCode);
        String replaceUrl = sync_flow_push.replace("/api/flow/push", "");
        String resultMessage = HttpKit.doPost(replaceUrl + "/org/findOrgName", postJson, "UTF-8");
        JSONObject messageJson = JSONObject.parseObject(resultMessage);
        if(messageJson.getInteger("code") == 0 && messageJson.containsKey("data")){
            JSONObject orgExchange = messageJson.getJSONObject("data");
            result.setD01001(orgExchange.getString("d01001"));
        }
        return new OutMessage<>(Status.SUCCESS, result);
    }
}
