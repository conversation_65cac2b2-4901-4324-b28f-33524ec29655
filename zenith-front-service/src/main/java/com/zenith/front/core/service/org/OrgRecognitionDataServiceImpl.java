package com.zenith.front.core.service.org;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.zenith.front.api.org.IOrgRecognitionDataService;
import com.zenith.front.dao.mapper.org.OrgRecognitionDataMapper;
import com.zenith.front.model.bean.OrgRecognitionData;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <p>
 * 表彰数据表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-09-14
 */
@Service
public class OrgRecognitionDataServiceImpl extends ServiceImpl<OrgRecognitionDataMapper, OrgRecognitionData> implements IOrgRecognitionDataService {

    @Override
    public List<OrgRecognitionData> findOrgRecognitionDataByRecognitionCode(List<String> recognitionCodeList) {
        return list(
                new LambdaQueryWrapper<OrgRecognitionData>()
                        .in(OrgRecognitionData::getRecognitionCode, recognitionCodeList)
                        .isNull(OrgRecognitionData::getDeleteTime)
        );
    }
}
