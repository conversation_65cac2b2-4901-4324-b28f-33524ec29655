package com.zenith.front.core.analysis.count.year2024;

import cn.hutool.core.util.StrUtil;
import com.b1809.analysis.entity.PeggingPara;
import com.b1809.analysis.extend.ITableCount;
import com.b1809.analysis.plugin.elasticsearch.EsKit;
import com.zenith.front.core.analysis.ext.condition.year2024.OrgAllCondition2024;
import org.jooq.Condition;
import org.jooq.Record1;
import org.jooq.SelectConditionStep;
import org.springframework.stereotype.Component;

import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

import static com.zenith.front.common.constant.DBConstant.DSL_CONTEXT;
import static org.jooq.impl.DSL.*;

/**
 * 第三十七表 党的地方委员会换届和领导班子召开民主生活会情况
 *
 * <AUTHOR>
 * @date 2023/01/03
 */
@Component
public class Html40Count2024 implements ITableCount {

    @Override
    public String getReportCode() {
        return "2024_40.html";
    }

    @Override
    public Map<String, Map<String, Number>> doCount(String reportCode, String orgCode, String orgLevelCode) throws Exception {
        return null;
    }

    @Override
    public Map<String, Object> doPegging(PeggingPara peggingPara) throws Exception {
        return null;
    }

    @Override
    public Map<String, Number> doReplenishCount(String reportCode, String orgCode, String orgLevelCode) throws Exception {
        return getReplenishCount(orgCode, orgLevelCode, Html1Count2024.TABLE_YEAR);
    }

    public Map<String, Number> getReplenishCount(String orgCode, String orgLevelCode, String tableYear) {
        String ccpAll = StrUtil.isEmpty(tableYear) ? "ccp_org_all" : "ccp_org_all_" + tableYear;
        Map<String, Number> result = new LinkedHashMap<>();
        Html53Count2024.initReplenishCol(1, 3, result);
        SelectConditionStep<Record1<Object>> records1 = DSL_CONTEXT.select(field("d01_code,life_situation,participants,code"))
                .from(table(name(ccpAll)).as("ccp_org_all")).where(new Html7Count2024().getOrgListCondition(orgCode, orgLevelCode).and("congress_situation in ('1','2','3') and d01_code='131'"));
        List<com.jfinal.plugin.activerecord.Record> records = EsKit.findBySql(records1.toString()).toRecord();
        records.forEach(record -> {
            //地（盟）委		个，已召开民主生活会的		个，参加人员		名。
            Html53Count2024.setReplenishMapValue("1", 1, result);
            if (StrUtil.equals(record.getStr("life_situation"), "2")) {
                Html53Count2024.setReplenishMapValue("2", 1, result);
                Html53Count2024.setReplenishMapValue("3", record.getInt("participants"), result);
            }
        });
        return result;
    }


    @Override
    public Map<String, Object> doReplenishPegging(PeggingPara peggingPara) throws Exception {
        Condition condition = new Html7Count2024().getOrgListCondition(peggingPara.getOrgCode(), peggingPara.getOrgLevelCode()).and(this.getRowCondition(peggingPara.getRowIndex()));
        OrgAllCondition2024 orgAllCondition = new OrgAllCondition2024();
        return Html53Count2024.getReportPageResult(peggingPara, orgAllCondition.getTableName(), condition, orgAllCondition.getLevelCodeField());
    }

    /**
     * 反查条件
     *
     * @param rowIndex 序号
     * @return 查询条件
     */
    Condition getRowCondition(String rowIndex) {
        Condition condition = noCondition().and("congress_situation in ('1','2','3') and d01_code='131'");
        if ("1".equals(rowIndex)) {
        } else if ("2".equals(rowIndex)) {
            condition = condition.and("life_situation='2'");
        } else if ("3".equals(rowIndex)) {
            condition = condition.and("life_situation='2' and participants>0");
        }
        return condition;
    }


}
