package com.zenith.front.core.analysis;

import com.zenith.front.common.constant.MapConstant;

import java.util.Map;
import java.util.Objects;

/**
 * <AUTHOR>
 */
public class ReportMapProcessingFlowAddType implements ReportMapProcessing {

    @Override
    public void process(String field, Map<String, Object> map) {
        convertType(field, map);
    }

    private void convertType(String filed, Map<String, Object> map) {
        Object obj = map.get(filed);
        if (Objects.nonNull(obj)) {
            map.put(filed, MapConstant.FLOW_ADD_TYPE_MAP.getOrDefault(obj.toString(), ""));
        }
    }
}
