package com.zenith.front.core.service.org;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.date.SystemClock;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.support.SFunction;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.common.collect.Maps;
import com.zenith.front.api.org.IOrgGroupMemberService;
import com.zenith.front.api.org.IOrgGroupService;
import com.zenith.front.api.org.IOrgService;
import com.zenith.front.common.constant.CommonConstant;
import com.zenith.front.common.kit.StrKit;
import com.zenith.front.core.cache.CacheUtils;
import com.zenith.front.core.kit.DbUtil;
import com.zenith.front.core.kit.ExcelUtil;
import com.zenith.front.dao.mapper.fee.FeeOrderMapper;
import com.zenith.front.dao.mapper.org.OrgGroupMapper;
import com.zenith.front.model.dto.LedgerDTO;
import com.zenith.front.model.dto.OrgGroupDTO;
import com.zenith.front.model.message.OutMessage;
import com.zenith.front.model.message.Status;
import com.zenith.front.model.bean.OrgGroup;
import com.zenith.front.model.bean.OrgGroupMember;
import org.jooq.*;
import org.jooq.impl.DSL;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import static com.zenith.front.model.constant.UserConstant.USER_CONTEXT;
import static org.jooq.impl.DSL.*;

/**
 * <p>
 * 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-07-10
 */
@Service
public class OrgGroupServiceImpl extends ServiceImpl<OrgGroupMapper, OrgGroup> implements IOrgGroupService {
    @Resource
    private IOrgGroupService orgGroupDao;
    @Resource
    private IOrgGroupMemberService orgGroupMemberDao;
    @Resource
    private IOrgService orgDao;

    @Resource
    private FeeOrderMapper orderMapper;
    @Resource
    HttpServletResponse response;


    @Override
    public OrgGroup findByCode(String code) {
        return orgGroupDao.getOne(new LambdaQueryWrapper<OrgGroup>().eq(OrgGroup::getCode, code).isNull(OrgGroup::getDeleteTime));
    }

    /**
     * 获取党小组列表
     *
     * @param pageNum
     * @param pageSize
     * @param orgCode
     * @return
     */
    @Override
    public OutMessage getList(int pageNum, int pageSize, String orgCode) {
        Page<OrgGroup> page = orgGroupDao.getBaseMapper().selectPage(new Page<>(pageNum, pageSize),
                new LambdaQueryWrapper<OrgGroup>().eq(OrgGroup::getGroupOrgCode, orgCode)
                        .isNull(OrgGroup::getDeleteTime)
                        .orderByAsc(OrgGroup::getId));
        return new OutMessage<>(Status.SUCCESS, page);
    }

    /**
     * 新增党小组
     *
     * @param orgGroupDTO
     * @return
     */
    @Override
    public OutMessage addGroup(OrgGroupDTO orgGroupDTO) {
        // TODO: 2019/4/9 支部才能添加党小组
        String orgCode = orgGroupDTO.getOrgCode();
//        Org org = orgDao.findOrgByCode(orgCode);
//        if (!CommonConstant.THREE.startsWith(org.getOrgType())) {
//            // 只有党支部才能添加党小组
//            return new OutMessage<>(Status.ORG_GROUP_NEED_DZB);
//        }
        orgGroupDTO.setCode(StrKit.getRandomUUID());
        orgGroupDTO.setCreateTime(new Date());
        orgGroupDTO.setUpdateTime(new Date());
        orgGroupDTO.setUpdateAccount(USER_CONTEXT.get().getUser().getAccount());
        orgGroupDTO.setTimestamp(new Date());
        orgGroupDTO.setIsHistory(CommonConstant.ZERO_INT);
        OrgGroup orgGroup = orgGroupDTO.toModel();
        orgGroup.setId(null);
        boolean flag = orgGroupDao.save(orgGroup);
        return new OutMessage(flag ? Status.SUCCESS : Status.FAIL);
    }

    /**
     * 根据code查找党小组
     *
     * @param code
     */
    @Override
    public OutMessage findByCodeM(String code) {
        OrgGroup orgGroup = orgGroupDao.findByCode(code);
        if (!orgGroup.getGroupOrgCode().startsWith(USER_CONTEXT.get().getUserRolePermission().getOrgCode())) {
            return new OutMessage(Status.PERMISSION_DENIED);
        }
        return new OutMessage<>(Status.SUCCESS, orgGroup);
    }

    /**
     * 修改党小组
     * todo: 是否在业务中修改党小组成员表中的信息
     *
     * @param orgGroupDTO
     * @return
     */
    @Override
    public OutMessage updateGroup(OrgGroupDTO orgGroupDTO) {
        String code = orgGroupDTO.getCode();
        OrgGroup orgGroup = orgGroupDao.findByCode(code);
        orgGroupDTO.setUpdateTime(new Date());
        orgGroupDTO.setUpdateAccount(USER_CONTEXT.get().getUser().getAccount());
        orgGroupDTO.setTimestamp(new Date());
        // 转换
        BeanUtils.copyProperties(orgGroupDTO, orgGroup);
        boolean flag = updateById(orgGroup);
        return new OutMessage<>(flag ? Status.SUCCESS : Status.FAIL);
    }

    /**
     * 删除党小组
     *
     * @param code
     * @return
     */
    @Override
    public OutMessage delGroup(String code) {
        OrgGroup orgGroup = orgGroupDao.findByCode(code);
        if (ObjectUtil.isNull(orgGroup)) {
            return new OutMessage<>(Status.PARA_ERROR);
        }
        if (!orgGroup.getGroupOrgCode().startsWith(USER_CONTEXT.get().getUserRolePermission().getOrgCode())) {
            return new OutMessage(Status.PERMISSION_DENIED);
        }

        List<OrgGroupMember> list = orgGroupMemberDao.findByGroupCode(code);
        if (list != null && list.size() > CommonConstant.ZERO_INT) {
            // 党小组下面还有成员不能删除
            return new OutMessage<>(Status.NEED_DEL_GROUP_MEM);
        }
        orgGroup.setDeleteTime(new Date());
        orgGroup.setUpdateAccount(USER_CONTEXT.get().getUser().getAccount());
        boolean flag = updateById(orgGroup);
        return new OutMessage<>(flag ? Status.SUCCESS : Status.FAIL);
    }

    /**
     * 导出两委委员数据
     *
     * @param data
     */
    @Override
    public void exportTwoCommittee(LedgerDTO data) {
        Long total = this.getTwoCommitteeTotal(data);
        if (total == null || total == 0) {
            ExcelUtil.exportErrorExcel(response, new OutMessage<>(Status.NOT_DATA));
        }
        SelectOptionStep selectOptionStep = this.getTwoCommitteeSql(data);

        String fileName = CacheUtils.getOrgShortNameByOrgCode(data.getOrgCode()) + "村（社区）“两委”委员基本情况统计表";

        Integer one = 1;

        ExcelUtil.processorAndExport(total, null, selectOptionStep, fileName, "村（社区）“两委”委员基本情况统计表", response, record -> {
            Map<String, Object> linkedHashMap = Maps.newLinkedHashMap();
            Integer isCreateOrg = record.getInt("isCreateOrg");
            String orgCode;
            if (isCreateOrg == 1) {
                // 建立党组织的
                orgCode = record.getStr("mainUnitOrgCode");
            } else {
                // 未建立党组情况
                orgCode = record.getStr("manageOrgCode");
            }

            linkedHashMap.put("乡镇街道", CacheUtils.getOrgNameByOrgCode(orgCode.substring(0, orgCode.length() - 3)));
            linkedHashMap.put("村（社区）", CacheUtils.getOrgNameByOrgCode(orgCode));
            linkedHashMap.put("姓名", record.getStr("memName"));
            linkedHashMap.put("性别", record.getStr("sexName"));
            linkedHashMap.put("年龄", null);
            linkedHashMap.put("政治面貌", "");
            linkedHashMap.put("是中共党员的入党时间（年月日）", "");
            linkedHashMap.put("是中共党员的转正时间（年月日）", "");
            linkedHashMap.put("个人身份（农村村民、城镇居民）", "");
            linkedHashMap.put("学历", "");
            linkedHashMap.put("所学专业", "");
            linkedHashMap.put("职业水平", "");
            linkedHashMap.put("党内职务", "");
            linkedHashMap.put("行政职务", record.getStr("d25Name"));
            linkedHashMap.put("兼任职务", record.getStr("d26Name"));
            linkedHashMap.put("干部来源", "");
            linkedHashMap.put("任职情况", one.equals(record.getInt("isIncumbent")) ? "在任" : "不在任");
            linkedHashMap.put("身份证号", "");
            linkedHashMap.put("手机号码", "");
            linkedHashMap.put("备注", record.getStr("remark"));

            String memCode = record.getStr("memCode");
            if (StrKit.isBlank(memCode)) {
                // 非党员
                linkedHashMap.put("政治面貌", "群众");
                linkedHashMap.put("身份证号", record.getStr("memIdcard"));
            } else {
                linkedHashMap.put("姓名", record.getStr("name"));
                linkedHashMap.put("性别", record.getStr("sexName"));
                linkedHashMap.put("政治面貌", "党员");
                linkedHashMap.put("是中共党员的入党时间（年月日）", record.get("joinOrgDate"));
                linkedHashMap.put("是中共党员的转正时间（年月日）", record.get("fullMemberDate"));
                linkedHashMap.put("个人身份（农村村民、城镇居民）", one.equals(record.getInt("isFarmer")) ? "从事农业生产人员" : "城镇居民");
                linkedHashMap.put("学历", record.getStr("d07Name"));
                linkedHashMap.put("职业水平", record.getStr("d19Name"));
                linkedHashMap.put("身份证号", record.getStr("idcard"));
                linkedHashMap.put("手机号码", record.getStr("phone"));
                linkedHashMap.put("党内职务", record.getStr("d022Name"));
                // 年龄
                Date birthday = record.get("birthday");
                if (birthday != null) {
                    linkedHashMap.put("年龄", DateUtil.ageOfNow(birthday));
                }
            }
            record.getColumns().clear();
            record.setColumns(linkedHashMap);
        });
    }

    /**
     * 两委委员总条数
     *
     * @param data
     * @return
     */
    public Long getTwoCommitteeTotal(LedgerDTO data) {
        SelectConditionStep record1s = getSelectConditionStep();

        SelectConditionStep record2s = getSelectConditionStep(data);

        SelectOnConditionStep<Record1<Integer>> record1s1 = DbUtil.DSL_CONTEXT.with("record1s")
                .as(record1s)
                .with("record2s")
                .as(record2s)
                .select(DSL.count(field("1"))).from(name("record2s"))
                .leftJoin(table(name("record1s")))
                .on(field(name("record2s", "memCode")).eq(field(name("record1s", "orgCommitteeMemCode"))));
        return orderMapper.getTotalBysql(record1s1.toString());
    }

    private SelectConditionStep getSelectConditionStep() {
        return DSL.select(field(name("ccp_org_committee", "d022_name")).as("d022Name"),
                        field(name("ccp_org_committee", "mem_code")).as("orgCommitteeMemCode"))
                .distinctOn(field(name("ccp_org_committee", "mem_code")))
                .from(name("ccp_org_committee"))
                .where(field(name("ccp_org_committee", "delete_time")).isNull());
    }

    private SelectConditionStep getSelectConditionStep(LedgerDTO data) {
        Condition condition = getTwoCommitteeCondition(data);
        condition = condition.and(field(name("ccp_mem", "delete_time")).isNull());

        return DbUtil.DSL_CONTEXT.select(
                        field(name("ccp_unit_all", "is_create_org")).as("isCreateOrg"),
                        field(name("ccp_unit_all", "manage_org_code")).as("manageOrgCode"),
                        field(name("ccp_unit_all", "main_unit_org_code")).as("mainUnitOrgCode"),
                        field(name("ccp_unit_committee", "mem_code")).as("memCode"),
                        field(name("ccp_unit_committee", "mem_name")).as("memName"),
                        field(name("ccp_unit_committee", "d25_name")).as("d25Name"),
                        field(name("ccp_unit_committee", "d26_name")).as("d26Name"),
                        field(name("ccp_unit_committee", "mem_idcard")).as("memIdcard"),
                        field(name("ccp_unit_committee", "is_incumbent")).as("isIncumbent"),
                        field(name("ccp_unit_committee", "remark")),
                        field(name("ccp_mem", "name")),
                        field(name("ccp_mem", "sex_name")).as("sexName"),
                        field(name("ccp_mem", "join_org_date")).as("joinOrgDate"),
                        field(name("ccp_mem", "full_member_date")).as("fullMemberDate"),
                        field(name("ccp_mem", "is_farmer")).as("isFarmer"),
                        field(name("ccp_mem", "d07_name")).as("d07Name"),
                        field(name("ccp_mem", "d19_name")).as("d19Name"),
                        field(name("ccp_mem", "idcard")),
                        field(name("ccp_mem", "phone")),
                        field(name("ccp_mem", "birthday")))
                .from(name("ccp_unit_all"))
                .innerJoin(table(name("ccp_unit_committee")))
                .on(field(name("ccp_unit_all", "code")).eq(field(name("ccp_unit_committee", "unit_code"))))
                .leftJoin(table(name("ccp_mem"))).on(field(name("ccp_unit_committee", "mem_code")).eq(field(name("ccp_mem", "code"))))
                .where(condition);
    }

    /**
     * 两委委员查询条件
     *
     * @param data
     * @return
     */
    private Condition getTwoCommitteeCondition(LedgerDTO data) {
        String orgCode = data.getOrgCode();
        Date startTime = data.getStartTime();
        Date endTime = data.getEndTime();

        Condition condition = ((field(name("ccp_unit_all", "is_create_org")).eq(1)
                .and(field(name("ccp_unit_all", "main_unit_org_code"), String.class).like(orgCode + "%")))
                .or(field(name("ccp_unit_all", "is_create_org")).eq(0)
                        .and(field(name("ccp_unit_all", "manage_org_code"), String.class).like(orgCode + "%"))))
                .and(field(name("ccp_unit_committee", "d25_code"), String.class).in("41", "42", "43", "51", "52", "53"));

        if (startTime != null) {
            condition = condition.and(field(name("ccp_unit_committee", "start_date"), java.sql.Date.class).greaterOrEqual(new java.sql.Date(startTime.getTime())));
        }

        if (endTime != null) {
            condition = condition.and(field(name("ccp_unit_committee", "start_date"), java.sql.Date.class).lessOrEqual(new java.sql.Date(endTime.getTime())));
        }
        condition = condition.and(field(name("ccp_unit_all", "delete_time")).isNull()).and(field(name("ccp_unit_committee", "delete_time")).isNull());
        return condition;
    }

    /**
     * 导出两委委员数据sql
     *
     * @param data
     * @return
     */
    public SelectOptionStep getTwoCommitteeSql(LedgerDTO data) {

        /**
         * WITH test1 as (
         * 	SELECT DISTINCT on (ccp_org_committee.mem_code) ccp_org_committee.d022_name,ccp_org_committee.mem_code FROM ccp_org_committee
         * ), test2 as (
         * 	SELECT
         * 	a.is_create_org,
         * 	a.manage_org_code,
         * 	a.main_unit_org_code,
         * 	b.mem_code,
         * 	b.mem_name,
         * 	b.d25_code,
         * 	b.d26_code,
         * 	b.is_incumbent,
         * 	b.remark
         * FROM
         * 	ccp_unit_all a
         * 	INNER JOIN ccp_unit_committee b ON a.CODE = b.unit_code
         * 	LEFT JOIN ccp_mem c ON c.CODE = b.mem_code
         * WHERE
         * 	(
         * 	(
         * 	( a."is_create_org" = 1 AND a."main_unit_org_code" LIKE '500%' )
         * 	OR ( a."is_create_org" = 0 AND a."manage_org_code" LIKE '500%' )
         * 	)
         * 	AND b."d25_code" IN ( '41', '42', '43', '51', '52', '53' )
         * 	AND a."delete_time" IS NULL
         * 	AND b."delete_time" IS NULL
         * 	)
         * )
         * SELECT * FROM test2 left JOIN test1 on test2.mem_code=test1.mem_code
         */
        SelectConditionStep record1s = getSelectConditionStep();

        SelectConditionStep record2s = getSelectConditionStep(data);

        return DbUtil.DSL_CONTEXT.with("record1s")
                .as(record1s)
                .with("record2s")
                .as(record2s)
                .select().from(name("record2s"))
                .leftJoin(table(name("record1s")))
                .on(field(name("record2s", "memCode")).eq(field(name("record1s", "orgCommitteeMemCode"))))
                .orderBy(field(name("record2s", "manageOrgCode")));
    }


    /**
     * 导出基层党组织换届情况统计表
     *
     * @param data
     */
    @Override
    public void exportChangeElection(LedgerDTO data) {
        String orgCode = data.getOrgCode();
        Long total = this.getChangeElectionTotal(orgCode);
        if (total == null || total == 0) {
            ExcelUtil.exportErrorExcel(response, new OutMessage<>(Status.NOT_DATA));
        }
        // 获取数据sql
        SelectOptionStep selectOptionStep = this.getChangeElectionSql(orgCode);
        int length = orgCode.length();

        String fileName = CacheUtils.getOrgShortNameByOrgCode(data.getOrgCode()) + "基层党组织换届情况统计表";
        // 处理数据并导出
        ExcelUtil.processorAndExport(total, null, selectOptionStep, fileName, "基层党组织换届情况统计表", response, record -> {
            Map<String, Object> linkedHashMap = Maps.newLinkedHashMap();
            linkedHashMap.put("单位名称", record.getStr("unit_name"));
            linkedHashMap.put("党组织名称", record.getStr("org_name"));
            linkedHashMap.put("党组织类型", record.getStr("d01_name"));
            linkedHashMap.put("当前届期换届日期", record.get("tenure_start_date"));
            linkedHashMap.put("现任书记姓名及电话", record.getStr("secretary"));
            linkedHashMap.put("现任班子成员人数", record.getInt("count"));
            linkedHashMap.put("换届审批文号", "");
            linkedHashMap.put("下届应换届日期", record.get("tenure_end_date"));
            linkedHashMap.put("所属党统单位", CacheUtils.getOrgShortNameByOrgCode(record.getStr("elect_org_code").substring(0, length)));
            linkedHashMap.put("备注", record.getStr("special_explain"));
            record.getColumns().clear();
            record.setColumns(linkedHashMap);
        });
    }

    /**
     * 基层党组织换届情况总数
     *
     * @param orgCode
     * @return
     */
    public Long getChangeElectionTotal(String orgCode) {
        SelectConditionStep record1s = getOrgCommitteeAll(orgCode);
        SelectConditionStep record2s = getMemOrgCommittee();

        SelectOnConditionStep count = DbUtil.DSL_CONTEXT.with("record1s").as(record1s)
                .with("record2s").as(record2s)
                .selectCount()
                .from(name("record1s"))
                .leftJoin(name("record2s"))
                .on(field(name("record1s", "code")).eq(field(name("record2s", "elect_code"))))
                .leftJoin(name("org_committee_count_view"))
                .on(field(name("record1s", "code")).eq(field(name("org_committee_count_view", "elect_code"))));
        return orderMapper.getTotalBysql(count.toString());
    }

    /**
     * 基层党组织
     *
     * @param orgCode
     * @return
     */
    private SelectConditionStep getOrgCommitteeAll(String orgCode) {
        java.sql.Date now = new java.sql.Date(SystemClock.now());
        return DbUtil.DSL_CONTEXT.select(
                        field(name("ccp_org_committee_elect", "code")),
                        field(name("ccp_org_committee_elect", "tenure_start_date")),
                        field(name("ccp_org_committee_elect", "tenure_end_date")),
                        field(name("ccp_org_committee_elect", "elect_org_code")),
                        field(name("ccp_org_committee_elect", "special_explain")),
                        field(name("ccp_org_all", "name")).as("org_name"),
                        field(name("ccp_org_all", "main_unit_name")).as("unit_name"),
                        field(name("ccp_org_all", "d01_name")))
                .from(name("ccp_org_committee_elect"))
                .leftJoin(name("ccp_org_all"))
                .on(field(name("ccp_org_committee_elect", "org_code")).eq(field(name("ccp_org_all", "code"))))
                .where(field(name("ccp_org_committee_elect", "tenure_end_date"), java.sql.Date.class).greaterThan(now))
                .and(field(name("ccp_org_committee_elect", "elect_org_code"), String.class).like(orgCode + "%"))
                .and(field(name("ccp_org_committee_elect", "elect_org_code")).ne(orgCode))
                .and(field(name("ccp_org_committee_elect", "delete_time")).isNull())
                .and(field(name("ccp_org_all", "delete_time")).isNull());
    }

    /**
     * 基层党组织
     *
     * @return
     */
    private SelectConditionStep getMemOrgCommittee() {
        return DbUtil.DSL_CONTEXT.select(
                        concat(field(name("ccp_mem", "name"), String.class), field(name("ccp_mem", "phone"), String.class)).as("secretary"),
                        field(name("ccp_org_committee", "elect_code")))
                .distinctOn(field(name("ccp_org_committee", "elect_code")))
                .from(name("ccp_org_committee"))
                .leftJoin(name("ccp_mem"))
                .on(field(name("ccp_org_committee", "mem_code")).eq(field(name("ccp_mem", "code"))))
                .where(field(name("ccp_org_committee", "d022_code"), String.class).in("11", "21", "31", "41", "51"))
                .and(field(name("ccp_org_committee", "delete_time")).isNull())
                .and(field(name("ccp_mem", "delete_time")).isNull());
    }

    /**
     * 基层党组织换届情况获取数据sql
     *
     * @param orgCode
     * @return
     */
    public SelectOptionStep getChangeElectionSql(String orgCode) {

        SelectConditionStep record1s = getOrgCommitteeAll(orgCode);
        SelectConditionStep record2s = getMemOrgCommittee();

        return DbUtil.DSL_CONTEXT.with("record1s").as(record1s)
                .with("record2s").as(record2s)
                .select(
                        table(name("record1s")).asterisk(),
                        table(name("record2s")).asterisk(),
                        field(name("org_committee_count_view", "count"))
                )
                .from(name("record1s"))
                .leftJoin(name("record2s"))
                .on(field(name("record1s", "code")).eq(field(name("record2s", "elect_code"))))
                .leftJoin(name("org_committee_count_view"))
                .on(field(name("record1s", "code")).eq(field(name("org_committee_count_view", "elect_code"))))
                .orderBy(field(name("record1s", "elect_org_code")));
    }

    @Override
    public List<OrgGroup> findOrgGroupByOrgCode(String orgCode) {
        return list(
                new LambdaQueryWrapper<OrgGroup>()
                        .eq(OrgGroup::getOrgCode, orgCode)
                        .isNull(OrgGroup::getDeleteTime)
                        .orderByAsc(OrgGroup::getId)
        );
    }

    @Override
    public Map<String, List<OrgGroup>> getListLikeOrgCode(String orgCode) {
        List<OrgGroup> orgGroups = baseMapper.selectGroupListLikeOrgCode(orgCode);
        return orgGroups.stream().collect(Collectors.groupingBy(v -> v.getGroupOrgCode()));
    }
}
