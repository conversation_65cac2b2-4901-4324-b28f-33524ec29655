package com.zenith.front.core.service.sync;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.zenith.front.api.org.IOrgService;
import com.zenith.front.api.org.IOrgSlackAllService;
import com.zenith.front.api.org.IOrgSlackRectificationService;
import com.zenith.front.api.org.IOrgSlackService;
import com.zenith.front.api.sync.ISyncMemService;
import com.zenith.front.common.annualstatisticsconf.AnnualStatisticsConstant;
import com.zenith.front.model.message.OutMessage;
import com.zenith.front.model.message.Status;

import com.zenith.front.model.bean.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2021/11/3
 */
@Service
@Slf4j
public class SyncOrgSlackServiceImpl {

    @Resource
    private IOrgSlackService iOrgSlackService;
    @Resource
    private IOrgSlackRectificationService iOrgSlackRectificationService;
    @Resource
    private IOrgSlackAllService iOrgSlackAllService;
    @Resource
    private ISyncMemService iSyncMemService;
    @Resource
    private SyncUnitService syncUnitService;
    @Resource
    private IOrgService iOrgService;


    /**
     * 同步软弱涣散党组织基本信息
     *
     * @param code 软弱涣散党组织code
     */
    public OutMessage<?> syncOrgSlack(String code) {
        if (!AnnualStatisticsConstant.SYNC_DB_ENABLE || StrUtil.isEmpty(code)) {
            return new OutMessage<>(Status.SUCCESS);
        }
        OrgSlack orgSlack = iOrgSlackService.findCode(code);
        if (Objects.isNull(orgSlack)) {
            log.warn("同步失败！OrgSlack对象不存在，code= " + code);
            return new OutMessage<>(Status.SUCCESS);
        }
        boolean isExist = true;
        OrgSlackAll orgSlackAll = iOrgSlackAllService.findByCode(code);
        if (Objects.isNull(orgSlackAll)) {
            orgSlackAll = new OrgSlackAll();
            BeanUtils.copyProperties(orgSlack, orgSlackAll, "id");
            isExist = false;
        }

        // 涣散组织基础信息
        this.setOrgSlackAllByBase(orgSlack, orgSlackAll);
        // 涣散组织整顿信息
        this.setOrgSlackAllByRectification(code, orgSlackAll);

        if (isExist) {
            iOrgSlackAllService.removeById(orgSlackAll);
        }
        iOrgSlackAllService.save(orgSlackAll);
        //设置是否党组织软弱涣散村
        syncUnitService.setHasOrgSlackVillage(orgSlackAll.getUnitCode(), null);
        log.info("正在同步OrgSlackAll，code= " + code);
        return new OutMessage<>(Status.SUCCESS);
    }


    private void setOrgSlackAllByBase(OrgSlack orgSlack, OrgSlackAll orgSlackAll) {
        BeanUtils.copyProperties(orgSlack, orgSlackAll, "id");
        orgSlackAll.setNeatenEndtime(orgSlack.getNeatenEndTime());
        //涣散组织关联单位信息
        UnitOrgLinked unitOrgLinked = iSyncMemService.getUnitOrgLinked(orgSlack.getOrgCode());
        if (Objects.nonNull(unitOrgLinked)) {
            orgSlackAll.setUnitCode(unitOrgLinked.getUnitCode());
            orgSlackAll.setUnitName(unitOrgLinked.getUnitName());
            orgSlackAll.setd04Code(unitOrgLinked.getUnitType());
            orgSlackAll.setd04Name(unitOrgLinked.getUnitTypeName());
        }
        if (Objects.nonNull(orgSlack.getNeatenTime())) {
            orgSlackAll.setYear(DateUtil.year(orgSlack.getNeatenTime()));
        }
        Org org = iOrgService.findOrgByCode(orgSlack.getOrgCode());
        if (Objects.nonNull(org)) {
            orgSlackAll.setName(org.getName());
        }
        //涣散类型
        if (Objects.nonNull(orgSlack.getD74Code())) {
            orgSlackAll.setd74Code1(0);
            orgSlackAll.setd74Code2(0);
            orgSlackAll.setd74Code3(0);
            orgSlackAll.setd74Code4(0);
            orgSlackAll.setd74Code5(0);
            orgSlackAll.setd74Code6(0);
            orgSlackAll.setd74Code7(0);
            orgSlackAll.setd74Code8(0);
            orgSlackAll.setd74Code9(0);
            orgSlackAll.setd74Code10(0);
            orgSlackAll.setd74Code11(0);
            //24 年统专题调查表八 新增软弱涣散基层党组织 其他
            orgSlackAll.setd74Code24(0);
            for (String d74Code : orgSlack.getD74Code().split(",")) {
                switch (d74Code) {
                    case "1":
                        orgSlackAll.setd74Code1(1);
                        break;
                    case "2":
                        orgSlackAll.setd74Code2(1);
                        break;
                    case "3":
                        orgSlackAll.setd74Code3(1);
                        break;
                    case "4":
                        orgSlackAll.setd74Code4(1);
                        break;
                    case "5":
                        orgSlackAll.setd74Code5(1);
                        break;
                    case "6":
                        orgSlackAll.setd74Code6(1);
                        break;
                    case "7":
                        orgSlackAll.setd74Code7(1);
                        break;
                    case "8":
                        orgSlackAll.setd74Code8(1);
                        break;
                    case "9":
                        orgSlackAll.setd74Code9(1);
                        break;
                    case "10":
                        orgSlackAll.setd74Code10(1);
                        break;
                    case "11":
                        orgSlackAll.setd74Code11(1);
                        break;
                    case "24":
                        orgSlackAll.setd74Code24(1);
                        break;
                    default:
                        break;
                }
            }
        }
    }

    private void setOrgSlackAllByRectification(String slackCode, OrgSlackAll orgSlackAll) {
        orgSlackAll.setEarlyQpSecretary(0);
        orgSlackAll.setHasYearSelected(0);
        orgSlackAll.setEarlyTzSecretary(0);
        orgSlackAll.setHasYearAdjust(0);
        orgSlackAll.setTrainSecretary(0);
        orgSlackAll.setLcCountyLevelLeader(0);
        orgSlackAll.setBcCountyLevelLeader(0);
        orgSlackAll.setFirstSecretary(0);
        orgSlackAll.setJdbfCountyLevelUnit(0);
        orgSlackAll.setTwoLevelsListed(0);
        orgSlackAll.setSpecialRectification(0);
        orgSlackAll.setSolveProblems(0);
        orgSlackAll.setLookIntoLaws(0);
        List<OrgSlackRectification> rectifications = iOrgSlackRectificationService.getBaseMapper().selectList(new LambdaQueryWrapper<OrgSlackRectification>().eq(OrgSlackRectification::getSlackCode, slackCode));
        if (CollUtil.isNotEmpty(rectifications)) {
            orgSlackAll.setEarlyQpSecretary(Math.toIntExact(rectifications.stream().filter(e -> Objects.nonNull(e.getCurrentYearSelected()) && StrUtil.equals(e.getD91Code(), "11")).mapToInt(OrgSlackRectification::getCurrentYearSelected).sum()));
            orgSlackAll.setHasYearSelected(Math.toIntExact(rectifications.stream().filter(e -> Objects.nonNull(e.getCurrentYearAdjusted()) && StrUtil.equals(e.getD91Code(), "11")).mapToInt(OrgSlackRectification::getCurrentYearAdjusted).sum()));
            orgSlackAll.setEarlyTzSecretary(Math.toIntExact(rectifications.stream().filter(e -> Objects.nonNull(e.getCurrentYearSelected()) && StrUtil.equals(e.getD91Code(), "12")).mapToInt(OrgSlackRectification::getCurrentYearSelected).sum()));
            orgSlackAll.setHasYearAdjust(Math.toIntExact(rectifications.stream().filter(e -> Objects.nonNull(e.getCurrentYearAdjusted()) && StrUtil.equals(e.getD91Code(), "12")).mapToInt(OrgSlackRectification::getCurrentYearAdjusted).sum()));
            orgSlackAll.setTrainSecretary(Math.toIntExact(rectifications.stream().filter(e -> Objects.nonNull(e.getCurrentYearSelected()) && StrUtil.equals(e.getD91Code(), "13")).mapToInt(OrgSlackRectification::getCurrentYearSelected).sum()));
            orgSlackAll.setLcCountyLevelLeader(Math.toIntExact(rectifications.stream().filter(e -> Objects.nonNull(e.getCurrentYearSelected()) && StrUtil.equals(e.getD91Code(), "21")).mapToInt(OrgSlackRectification::getCurrentYearSelected).sum()));
            orgSlackAll.setBcCountyLevelLeader(Math.toIntExact(rectifications.stream().filter(e -> Objects.nonNull(e.getCurrentYearSelected()) && StrUtil.equals(e.getD91Code(), "22")).mapToInt(OrgSlackRectification::getCurrentYearSelected).sum()));
            orgSlackAll.setFirstSecretary(Math.toIntExact(rectifications.stream().filter(e -> Objects.nonNull(e.getCurrentYearSelected()) && StrUtil.equals(e.getD91Code(), "23")).mapToInt(OrgSlackRectification::getCurrentYearSelected).sum()));
            orgSlackAll.setJdbfCountyLevelUnit(Math.toIntExact(rectifications.stream().filter(e -> Objects.nonNull(e.getCurrentYearSelected()) && StrUtil.equals(e.getD91Code(), "24")).mapToInt(OrgSlackRectification::getCurrentYearSelected).sum()));
            orgSlackAll.setTwoLevelsListed(Math.toIntExact(rectifications.stream().filter(e -> Objects.nonNull(e.getCurrentYearSelected()) && StrUtil.equals(e.getD91Code(), "25")).mapToInt(OrgSlackRectification::getCurrentYearSelected).sum()));
            orgSlackAll.setSpecialRectification(Math.toIntExact(rectifications.stream().filter(e -> Objects.nonNull(e.getCurrentYearSelected()) && StrUtil.equals(e.getD91Code(), "31")).mapToInt(OrgSlackRectification::getCurrentYearSelected).sum()));
            orgSlackAll.setSolveProblems(Math.toIntExact(rectifications.stream().filter(e -> Objects.nonNull(e.getCurrentYearSelected()) && StrUtil.equals(e.getD91Code(), "32")).mapToInt(OrgSlackRectification::getCurrentYearSelected).sum()));
            orgSlackAll.setLookIntoLaws(Math.toIntExact(rectifications.stream().filter(e -> Objects.nonNull(e.getCurrentYearSelected()) && StrUtil.equals(e.getD91Code(), "33")).mapToInt(OrgSlackRectification::getCurrentYearSelected).sum()));
        }
    }

    /**
     * 全表初始化方法
     */
    void initSyncOrgSlack() {
        iOrgSlackAllService.remove(Wrappers.emptyWrapper());
        int count = iOrgSlackAllService.count();
        if (count > 0) {
            return;
        }
        List<OrgSlack> list = iOrgSlackService.list(new LambdaQueryWrapper<OrgSlack>().select(OrgSlack::getCode).isNull(OrgSlack::getDeleteTime));
        if (CollUtil.isNotEmpty(list)) {
            list.parallelStream().forEach(orgSlack -> this.syncOrgSlack(orgSlack.getCode()));
        }
    }

}
