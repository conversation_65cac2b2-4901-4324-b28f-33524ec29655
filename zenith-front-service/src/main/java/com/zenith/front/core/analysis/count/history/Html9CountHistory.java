package com.zenith.front.core.analysis.count.history;

import cn.hutool.core.util.StrUtil;
import com.b1809.analysis.entity.PeggingPara;
import com.b1809.analysis.extend.ITableCount;
import com.b1809.analysis.plugin.elasticsearch.EsKit;
import com.zenith.front.core.analysis.ext.condition.OrgAllCondition;
import org.jooq.Condition;
import org.jooq.Record1;
import org.jooq.SelectConditionStep;
import org.springframework.stereotype.Component;

import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

import static com.zenith.front.common.constant.DBConstant.DSL_CONTEXT;
import static org.jooq.impl.DSL.*;

/**
 * 第九表 党员培训情况
 *
 * <AUTHOR>
 * @date 2021/11/11
 */
@Component
public class Html9CountHistory implements ITableCount {

    @Override
    public String getReportCode() {
        return "History_9.html";
    }

    @Override
    public Map<String, Map<String, Number>> doCount(String reportCode, String orgCode, String orgLevelCode) throws Exception {
        return null;
    }

    @Override
    public Map<String, Object> doPegging(PeggingPara peggingPara) throws Exception {
        return null;
    }

    @Override
    public Map<String, Number> doReplenishCount(String reportCode, String orgCode, String orgLevelCode) throws Exception {
        return getReplenishCount(orgCode, orgLevelCode, null);
    }

    public Map<String, Number> getReplenishCount(String orgCode, String orgLevelCode, String tableYear) {
        String ccpOrgAll = StrUtil.isEmpty(tableYear) ? "ccp_org_all" : "ccp_org_all_" + tableYear;
        Map<String, Number> result = new LinkedHashMap<>();
        Html48CountHistory.initReplenishCol(1, 29, result);
        SelectConditionStep<Record1<Object>> records1 = DSL_CONTEXT.select(field("d04_code," +
                        "organization_secretary," +
                        "community_party," +
                        "provincial_train_class," +
                        "provincial_train_member," +
                        "city_train_class," +
                        "city_train_member," +
                        "county_train_class," +
                        "county_train_member," +
                        "level_party_class," +
                        "level_party_member," +
                        "rural_party_villages," +
                        "has_will_lesson," +
                        "has_party_day," +
                        "remote_education," +
                        "remote_education_villages," +
                        "remote_education_administrative_village," +
                        "remote_education_committee," +
                        "internet," +
                        "wired," +
                        "satellite," +
                        "site_administrator," +
                        "villages_cadres," +
                        "village_community," +
                        "volunteers," +
                        "rural_remote_education_party"))
                .from(table(name(ccpOrgAll)).as("ccp_org_all")).where(new Html6CountHistory().getOrgListCondition(orgCode, orgLevelCode));
        List<com.jfinal.plugin.activerecord.Record> records = EsKit.findBySql(records1.toString()).toRecord();
        for (com.jfinal.plugin.activerecord.Record record : records) {
            String d04Code = record.getStr("d04_code");
            Integer organizationSecretary = record.getInt("organization_secretary");
            //1.农村党组织书记培训
            if (StrUtil.equals(d04Code, "923")) {
                Html48CountHistory.setReplenishMapValue("1", organizationSecretary, result);
            }
            //社区党务工作者培训
            Html48CountHistory.setReplenishMapValue("2", record.getInt("community_party"), result);
            //国有企业二级及以下单位（部门）党组织书记培训
            if (StrUtil.startWith(d04Code, "411")) {
                Html48CountHistory.setReplenishMapValue("3", organizationSecretary, result);
            }
            //非公有制经济控制企业党组织书记培训
            if (StrUtil.startWith(d04Code, "42")) {
                Html48CountHistory.setReplenishMapValue("4", organizationSecretary, result);
            }
            //2.党员年度集中学习培训达标的
            //基层党组织书记和班子成员年度集中学习培训达标
            //3.省级举办培训班
            Html48CountHistory.setReplenishMapValue("7", record.getInt("provincial_train_class"), result);
            Html48CountHistory.setReplenishMapValue("8", record.getInt("provincial_train_member"), result);
            //市级举办培训班
            Html48CountHistory.setReplenishMapValue("9", record.getInt("city_train_class"), result);
            Html48CountHistory.setReplenishMapValue("10", record.getInt("city_train_member"), result);
            //县级举办培训班
            Html48CountHistory.setReplenishMapValue("11", record.getInt("county_train_class"), result);
            Html48CountHistory.setReplenishMapValue("12", record.getInt("county_train_member"), result);
            //基层党委举办培训班
            Html48CountHistory.setReplenishMapValue("13", record.getInt("level_party_class"), result);
            Html48CountHistory.setReplenishMapValue("14", record.getInt("level_party_member"), result);
            //直接组织开展农村党员集中培训的乡镇
            Html48CountHistory.setReplenishMapValue("15", record.getInt("rural_party_villages"), result);
            //4.按规定开展“三会一课”的党支部
            if (StrUtil.equals(record.getStr("has_will_lesson"), "1")) {
                Html48CountHistory.setReplenishMapValue("16", 1, result);
            }
            //开展主题党日的党支部
            if (StrUtil.equals(record.getStr("has_party_day"), "1")) {
                Html48CountHistory.setReplenishMapValue("17", 1, result);
            }
            //5.党员干部现代远程教育终端点
            Html48CountHistory.setReplenishMapValue("18", record.getInt("remote_education"), result);
            //其中乡镇（街道） 个，行政村 个，社区（居委会） 个；通过互联网传播 个，有线 个，
            Html48CountHistory.setReplenishMapValue("19", record.getInt("remote_education_villages"), result);
            Html48CountHistory.setReplenishMapValue("20", record.getInt("remote_education_administrative_village"), result);
            Html48CountHistory.setReplenishMapValue("21", record.getInt("remote_education_committee"), result);
            Html48CountHistory.setReplenishMapValue("22", record.getInt("internet"), result);
            Html48CountHistory.setReplenishMapValue("23", record.getInt("wired"), result);
            //卫星 个；站点管理员共 名，其中乡镇（街道）干部 名，村、社区干部 名，志愿者 名。农村党员远程教育培训党员 人次。
            Html48CountHistory.setReplenishMapValue("24", record.getInt("satellite"), result);
            Html48CountHistory.setReplenishMapValue("25", record.getInt("site_administrator"), result);
            Html48CountHistory.setReplenishMapValue("26", record.getInt("villages_cadres"), result);
            Html48CountHistory.setReplenishMapValue("27", record.getInt("village_community"), result);
            Html48CountHistory.setReplenishMapValue("28", record.getInt("volunteers"), result);
            Html48CountHistory.setReplenishMapValue("29", record.getInt("rural_remote_education_party"), result);
        }
        return result;
    }


    @Override
    public Map<String, Object> doReplenishPegging(PeggingPara peggingPara) throws Exception {
        Condition condition = new Html6CountHistory().getOrgListCondition(peggingPara.getOrgCode(), peggingPara.getOrgLevelCode()).and(this.getRowCondition(peggingPara.getRowIndex()));

        OrgAllCondition orgAllCondition = new OrgAllCondition();
        return Html48CountHistory.getReportPageResult(peggingPara, orgAllCondition.getTableName(), condition, orgAllCondition.getLevelCodeField());
    }

    /**
     * 反查条件
     *
     * @param rowIndex 序号
     * @return 查询条件
     */
    Condition getRowCondition(String rowIndex) {
        Condition condition = noCondition();
        if ("1".equals(rowIndex)) {
            condition = condition.and(field(name("d04_code"), String.class).eq("923").and(field(name("organization_secretary")).isNotNull()));
        } else if ("2".equals(rowIndex)) {
            condition = condition.and(field(name("community_party")).isNotNull().and(field(name("community_party")).greaterThan(0)));
        } else if ("3".equals(rowIndex)) {
            condition = condition.and(field(name("d04_code"), String.class).like("411%").and(field(name("organization_secretary")).isNotNull()));
        } else if ("4".equals(rowIndex)) {
            condition = condition.and(field(name("d04_code"), String.class).like("42%").and(field(name("organization_secretary")).isNotNull()));
        } else if ("7".equals(rowIndex)) {
            condition = condition.and(field(name("provincial_train_class")).isNotNull());
        } else if ("8".equals(rowIndex)) {
            condition = condition.and(field(name("provincial_train_member")).isNotNull());
        } else if ("9".equals(rowIndex)) {
            condition = condition.and(field(name("city_train_class")).isNotNull());
        } else if ("10".equals(rowIndex)) {
            condition = condition.and(field(name("city_train_member")).isNotNull());
        } else if ("11".equals(rowIndex)) {
            condition = condition.and(field(name("county_train_class")).isNotNull());
        } else if ("12".equals(rowIndex)) {
            condition = condition.and(field(name("county_train_member")).isNotNull());
        } else if ("13".equals(rowIndex)) {
            condition = condition.and(field(name("level_party_class")).isNotNull());
        } else if ("14".equals(rowIndex)) {
            condition = condition.and(field(name("level_party_member")).isNotNull());
        } else if ("15".equals(rowIndex)) {
            condition = condition.and(field(name("rural_party_villages")).isNotNull());
        } else if ("16".equals(rowIndex)) {
            condition = condition.and(field(name("has_will_lesson"), String.class).eq("1"));
        } else if ("17".equals(rowIndex)) {
            condition = condition.and(field(name("has_party_day"), String.class).eq("1"));
        } else if ("18".equals(rowIndex)) {
            condition = condition.and(field(name("remote_education")).isNotNull());
        } else if ("19".equals(rowIndex)) {
            condition = condition.and(field(name("remote_education_villages")).isNotNull());
        } else if ("20".equals(rowIndex)) {
            condition = condition.and(field(name("remote_education_administrative_village")).isNotNull());
        } else if ("21".equals(rowIndex)) {
            condition = condition.and(field(name("remote_education_committee")).isNotNull());
        } else if ("22".equals(rowIndex)) {
            condition = condition.and(field(name("internet")).isNotNull());
        } else if ("23".equals(rowIndex)) {
            condition = condition.and(field(name("wired")).isNotNull());
        } else if ("24".equals(rowIndex)) {
            condition = condition.and(field(name("satellite")).isNotNull());
        } else if ("25".equals(rowIndex)) {
            condition = condition.and(field(name("site_administrator")).isNotNull());
        } else if ("26".equals(rowIndex)) {
            condition = condition.and(field(name("villages_cadres")).isNotNull());
        } else if ("27".equals(rowIndex)) {
            condition = condition.and(field(name("village_community")).isNotNull());
        } else if ("28".equals(rowIndex)) {
            condition = condition.and(field(name("volunteers")).isNotNull());
        } else if ("29".equals(rowIndex)) {
            condition = condition.and(field(name("rural_remote_education_party")).isNotNull());
        } else {
            condition = condition.and("1=0");
        }
        return condition;
    }


}
