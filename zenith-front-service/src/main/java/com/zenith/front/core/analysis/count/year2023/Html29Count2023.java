package com.zenith.front.core.analysis.count.year2023;

import cn.hutool.core.util.StrUtil;
import com.b1809.analysis.entity.PeggingPara;
import com.zenith.front.core.analysis.ext.condition.year2023.MemAllCondition2023;
import com.zenith.front.core.analysis.ext.condition.year2023.OrgAllCondition2023;
import org.jooq.Condition;

import java.util.Map;

import static org.jooq.impl.DSL.*;

/**
 * 公有经济控制的企业法人单位建立党的基层组织情况
 */
public class Html29Count2023 {
    public Condition getMemListCondition(String orgCode, String orgLevelCode) {
        return noCondition().and("d09_code is not null and d09_code <> '' and delete_time is null and d08_code in('1','2')  and (is_transfer !=1 or is_transfer is null) and (d04_code like '411%' or d04_code in('412','413','414','416') or d04_code like '415%')  and d09_code like '0%' and d16_code in('11','111','12','121','122','123','21','22','23','24')").and(new MemAllCondition2023().create(orgCode, orgLevelCode));
    }

    public Map<String, Object> getCheckHtml26(PeggingPara data) {
        String rowIndex = data.getRowIndex();
        if (StrUtil.equals(rowIndex, "7")) {
            Condition condition = this.getOrg26Condition(data.getOrgCode(), data.getOrgLevelCode()).and(this.getOrgCheck26Condition(data.getColIndex()));
            OrgAllCondition2023 orgAllCond = new OrgAllCondition2023();
            return Html52Count2023.getReportPageResult(data, orgAllCond.getTableName(), condition, orgAllCond.getLevelCodeField());
        }

        Condition condition = this.getMemListCondition(data.getOrgCode(), data.getOrgLevelCode()).and(this.getOrgCheck26Condition(data.getColIndex()));
        MemAllCondition2023 memAllCond = new MemAllCondition2023();
        return Html52Count2023.getReportPageResult(data, memAllCond.getTableName(), condition, memAllCond.getLevelCodeField());
    }

    Condition getMemCheckListCondition(String orgCode, String orgLevelCode, String colIndex, String rowIndex) {
        if ("1".equals(colIndex)) {
            return this.getMemListCondition(orgCode, orgLevelCode);
        }
        if ("2".equals(colIndex)) {
            return this.getMemListCondition(orgCode, orgLevelCode).and("d17_code in('1','2')");
        }
        if ("3".equals(colIndex)) {
            return this.getMemListCondition(orgCode, orgLevelCode).and("d04_code in('41111','411121','411122','41113')");
        }
        if ("4".equals(colIndex)) {
            return this.getMemListCondition(orgCode, orgLevelCode).and("d04_code='41111'");
        }
        if ("5".equals(colIndex)) {
            return this.getMemListCondition(orgCode, orgLevelCode).and("d04_code in('411121','411122')");
        }
        if ("6".equals(colIndex)) {
            return this.getMemListCondition(orgCode, orgLevelCode).and("d04_code='411121'");
        }
        if ("7".equals(colIndex)) {
            return this.getMemListCondition(orgCode, orgLevelCode).and("d16_code in('11','12','121','122')");
        }
        if ("8".equals(colIndex)) {
            return this.getMemListCondition(orgCode, orgLevelCode).and("d16_code='11'");
        }
        if ("9".equals(colIndex)) {
            return this.getMemListCondition(orgCode, orgLevelCode).and("d16_code in('12','121','122')");
        }
        if ("10".equals(colIndex)) {
            return this.getMemListCondition(orgCode, orgLevelCode).and("d16_code='121'");
        }
        if ("11".equals(colIndex)) {
            return this.getMemListCondition(orgCode, orgLevelCode).and("d16_code='122'");
        }
        if ("12".equals(colIndex)) {
            return this.getMemListCondition(orgCode, orgLevelCode).and("d16_code in('21','22','23')");
        }
        if ("13".equals(colIndex)) {
            return this.getMemListCondition(orgCode, orgLevelCode).and("d16_code='21'");
        }
        if ("14".equals(colIndex)) {
            return this.getMemListCondition(orgCode, orgLevelCode).and("d16_code='22'");
        }
        if ("15".equals(colIndex)) {
            return this.getMemListCondition(orgCode, orgLevelCode).and("d16_code='23'");
        }
        return noCondition();
    }


    public Condition getOrgCheck26Condition(String colIndex) {
        Condition condition = noCondition();
        if (StrUtil.equals(colIndex, "1")) {
            condition = condition.and(field(name("d16_code")).in("11", "111", "12", "121", "122", "123", "2", "21", "22", "23", "24"));
        } else if (StrUtil.equals(colIndex, "2")) {
            condition = condition.and(field(name("d17_code")).in("1", "2"));
        } else if (StrUtil.equals(colIndex, "3")) {
            condition = condition.and(field(name("d04_code")).in("41111", "411121", "411122", "41113"));
        } else if (StrUtil.equals(colIndex, "4")) {
            condition = condition.and(field(name("d04_code")).in("41111"));
        } else if (StrUtil.equals(colIndex, "5")) {
            condition = condition.and(field(name("d04_code")).in("41112", "411121", "411122"));
        } else if (StrUtil.equals(colIndex, "6")) {
            condition = condition.and(field(name("d04_code")).in("411121"));
        } else if (StrUtil.equals(colIndex, "7")) {
            condition = condition.and(field(name("d16_code")).in("11", "111", "12", "121", "122", "123"));
        } else if (StrUtil.equals(colIndex, "8")) {
            condition = condition.and(field(name("d16_code")).in("11", "111"));
        } else if (StrUtil.equals(colIndex, "9")) {
            condition = condition.and(field(name("d16_code")).in("12", "121", "122", "123"));
        } else if (StrUtil.equals(colIndex, "10")) {
            condition = condition.and(field(name("d16_code")).in("121"));
        } else if (StrUtil.equals(colIndex, "11")) {
            condition = condition.and(field(name("d16_code")).in("122"));
        } else if (StrUtil.equals(colIndex, "12")) {
            condition = condition.and(field(name("d16_code")).in("2", "21", "22", "23", "24"));
        } else if (StrUtil.equals(colIndex, "13")) {
            condition = condition.and(field(name("d16_code")).in("21"));
        } else if (StrUtil.equals(colIndex, "14")) {
            condition = condition.and(field(name("d16_code")).in("22"));
        } else if (StrUtil.equals(colIndex, "15")) {
            condition = condition.and(field(name("d16_code")).in("23"));
        }
        return condition;
    }

    public Condition getOrg26Condition(String orgCode, String orgLevelCode) {
        return new OrgAllCondition2023().create(orgCode, orgLevelCode).and("delete_time is null and (is_dissolve is null or is_dissolve !=1) and d01_code in('632','634','932') " +
                "and (d04_code like '411%' or d04_code in('412','413','414','416','418') or d04_code like '415%') and has_unit_own_level='1'");
    }


}
