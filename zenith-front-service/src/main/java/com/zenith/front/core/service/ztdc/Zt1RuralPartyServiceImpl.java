package com.zenith.front.core.service.ztdc;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.zenith.front.api.ztdc.IZt1RuralPartyService;
import com.zenith.front.common.kit.StrKit;
import com.zenith.front.dao.mapper.ztdc.*;
import com.zenith.front.model.dto.Zt1RuralPartyDTO;
import com.zenith.front.model.message.OutMessage;
import com.zenith.front.model.message.Status;

import com.zenith.front.model.bean.*;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;

/**
 * <p>
 * 农村党建有关情况 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-08-13
 */
@Service
public class Zt1RuralPartyServiceImpl extends ServiceImpl<Zt1RuralPartyMapper, Zt1RuralParty> implements IZt1RuralPartyService {

    @Resource
    private Zt1RuralPartyMapper partyMapper;

    @Resource
    private Zt2TeamOptimizeMapper optimizeMapper;

    @Resource
    private Zt3DevelopEconomyMapper economyMapper;

    @Resource
    private Zt4BasicWordMapper wordMapper;

    @Resource
    private Zt5CommunityPartyMapper communityPartyMapper;


    @Override
    public OutMessage addOne(Zt1RuralPartyDTO data) {
        Zt1RuralParty ruralParty = partyMapper.selectOne(new QueryWrapper<Zt1RuralParty>().lambda().eq(Zt1RuralParty::getUnitCode, data.getUnitCode()).isNull(Zt1RuralParty::getDeleteTime));
        int result;
        if (ruralParty != null) {
            String code = ruralParty.getCode();
            BeanUtils.copyProperties(data, ruralParty);
            ruralParty.setUpdateTime(new Date());
            ruralParty.setCode(code);
            result = partyMapper.updateById(ruralParty);
        } else {
            Zt1RuralParty zt1RuralParty = new Zt1RuralParty();
            BeanUtils.copyProperties(data, zt1RuralParty);
            zt1RuralParty.setCode(StrKit.getRandomUUID());
            zt1RuralParty.setCreateTime(new Date());
            result = partyMapper.insert(zt1RuralParty);
        }
        return new OutMessage<>(result > 0 ? Status.SUCCESS : Status.FAIL);
    }

    @Override
    public OutMessage findDataByCode(String unitCode, String type) {
        if ("1".equals(type)) {
            Zt1RuralParty zt1RuralParty = partyMapper.selectOne(new QueryWrapper<Zt1RuralParty>().lambda().eq(Zt1RuralParty::getUnitCode, unitCode).isNull(Zt1RuralParty::getDeleteTime).last("limit 1"));
            return new OutMessage<>(Status.SUCCESS, zt1RuralParty);
        }
        if ("2".equals(type)) {
            Zt2TeamOptimize optimize = optimizeMapper.selectOne(new QueryWrapper<Zt2TeamOptimize>().lambda().eq(Zt2TeamOptimize::getUnitCode, unitCode).isNull(Zt2TeamOptimize::getDeleteTime).last("limit 1"));
            return new OutMessage<>(Status.SUCCESS, optimize);
        }
        if ("3".equals(type)) {
            Zt3DevelopEconomy economy = economyMapper.selectOne(new QueryWrapper<Zt3DevelopEconomy>().lambda().eq(Zt3DevelopEconomy::getUnitCode, unitCode).isNull(Zt3DevelopEconomy::getDeleteTime).last("limit 1"));
            return new OutMessage<>(Status.SUCCESS, economy);
        }
        if ("4".equals(type)) {
            Zt4BasicWord word = wordMapper.selectOne(new QueryWrapper<Zt4BasicWord>().lambda().eq(Zt4BasicWord::getUnitCode, unitCode).isNull(Zt4BasicWord::getDeleteTime).last("limit 1"));
            return new OutMessage<>(Status.SUCCESS, word);
        }
        if ("5".equals(type)) {
            Zt5CommunityParty zt5CommunityParty = communityPartyMapper.selectOne(new QueryWrapper<Zt5CommunityParty>().lambda().eq(Zt5CommunityParty::getUnitCode, unitCode).isNull(Zt5CommunityParty::getDeleteTime).last("limit 1"));
            return new OutMessage<>(Status.SUCCESS, zt5CommunityParty);
        }
        return null;
    }
}
