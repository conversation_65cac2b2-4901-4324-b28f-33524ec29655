package com.zenith.front.core.analysis.count.history;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.b1809.analysis.entity.PeggingPara;
import com.b1809.analysis.extend.ITableCount;
import com.b1809.analysis.plugin.elasticsearch.EsKit;
import com.jfinal.plugin.activerecord.Record;
import com.zenith.front.core.analysis.ext.condition.year2022.MemAllCondition2022B;
import com.zenith.front.core.analysis.ext.condition.year2022.MemFlowStatisticsConditionB;
import com.zenith.front.core.analysis.ext.condition.year2022.OrgAllConditionB;
import org.jooq.Condition;
import org.jooq.Record1;
import org.jooq.SelectConditionStep;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

import static com.zenith.front.common.constant.DBConstant.DSL_CONTEXT;
import static org.jooq.impl.DSL.*;

/**
 * 流动党员情况（二）
 *
 * <AUTHOR>
 * @date 2022/11/25
 */
@Component
public class Html39Count2022 extends Html39CountHistory implements ITableCount {


    @Override
    public String getReportCode() {
        return "2022_39.html";
    }

    @Override
    public Map<String, Map<String, Number>> doCount(String reportCode, String orgCode, String orgLevelCode) throws Exception {
        return getCount(orgCode, orgLevelCode, Html1Count2022.TABLE_ES_YEAR_B);
    }


    @Override
    public Map<String, Object> doPegging(PeggingPara peggingPara) throws Exception {
        MemFlowStatisticsConditionB memFlowCond = new MemFlowStatisticsConditionB();
        Condition condition = memFlowCond.create(peggingPara.getOrgCode(), peggingPara.getOrgLevelCode()).and("flow_out_in = '2'")
                .and(Html38CountHistory.getColCondition(peggingPara.getColIndex()))
                .and(Html38CountHistory.getRowCondition(peggingPara.getRowIndex()));
        return Html48CountHistory.getReportPageResult(peggingPara, memFlowCond.getTableName(), condition, memFlowCond.getLevelCodeField());
    }

    /**
     * 补充资料
     */
    @Override
    public Map<String, Number> doReplenishCount(String reportCode, String orgCode, String orgLevelCode) throws Exception {
        return getReplenishCount(orgCode, orgLevelCode, Html1Count2022.TABLE_ES_YEAR_B);
    }


    /**
     * 补充资料反查
     */
    @Override
    public Map<String, Object> doReplenishPegging(PeggingPara peggingPara) throws Exception {
        String colIndex = peggingPara.getRowIndex();
        OrgAllConditionB orgAllCondition = new OrgAllConditionB();
        Condition orgListCond = noCondition().and(new Html6CountHistory().getOrgListCondition(peggingPara.getOrgCode(), peggingPara.getOrgLevelCode()).toString().replace("ccp_org_all", orgAllCondition.getTableName()));
        if (StrUtil.equalsAny(colIndex, "1")) {
            List<String> flowInOrgList = getBCZLFlowInOrgList(peggingPara.getOrgCode(), peggingPara.getOrgLevelCode(), Html1Count2022.TABLE_ES_YEAR_B);
            Condition condition = orgListCond.and(field(name("code")).in(flowInOrgList));
            return Html48CountHistory.getReportPageResult(peggingPara, orgAllCondition.getTableName(), condition, orgAllCondition.getLevelCodeField());
        }
        Condition orgCond = orgListCond.and("is_flow =1 ").and(field(name("d01_code")).in("61", "911", "62", "921", "631", "632", "633", "634", "931", "932"));
        if (StrUtil.equalsAny(colIndex, "2", "3", "4", "5")) {
            return Html48CountHistory.getReportPageResult(peggingPara, orgAllCondition.getTableName(), orgCond, orgAllCondition.getLevelCodeField());
        }
        SelectConditionStep<Record1<Object>> records1 = DSL_CONTEXT.select(field("code")).from(table(name(orgAllCondition.getTableName()))).where(orgListCond);
        List<Record> records = EsKit.findBySql(records1.toString()).toRecord();
        Set<String> orgCodeList = records.stream().map(record -> record.getStr("code")).collect(Collectors.toSet());

        MemAllCondition2022B memAllCond = new MemAllCondition2022B();
        Condition condition = noCondition().and(new Html6CountHistory().getMemListCondition(peggingPara.getOrgCode(), peggingPara.getOrgLevelCode()).toString().replace("ccp_mem_all", memAllCond.getTableName()));
        if (CollUtil.isNotEmpty(orgCodeList)) {
            condition = condition.and(field(name("org_code")).in(orgCodeList));
        } else {
            condition = condition.and("1=0");
        }
        return Html48CountHistory.getReportPageResult(peggingPara, memAllCond.getTableName(), condition, memAllCond.getLevelCodeField());
    }


}
