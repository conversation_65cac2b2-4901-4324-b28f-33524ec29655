package com.zenith.front.core.service.unit;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.zenith.front.api.unit.IUnitSiteConditionsService;
import com.zenith.front.common.kit.CodeUtil;
import com.zenith.front.common.kit.StrKit;
import com.zenith.front.dao.mapper.unit.UnitSiteConditionsMapper;
import com.zenith.front.model.dto.UnitSiteConditionsDTO;
import com.zenith.front.model.dto.UnitSiteConditionsListDTO;
import com.zenith.front.model.message.OutMessage;
import com.zenith.front.model.message.Status;
import com.zenith.front.model.bean.UnitSiteConditions;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;

/**
 * <p>
 * 经费场地情况 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-10-07
 */
@Service
public class UnitSiteConditionsServiceImpl extends ServiceImpl<UnitSiteConditionsMapper, UnitSiteConditions> implements IUnitSiteConditionsService {

    @Resource
    private UnitSiteConditionsMapper conditionsMapper;

    @Override
    public OutMessage addOrUpdate(UnitSiteConditionsDTO data) {
        if (StrKit.isBlank(data.getCode())) {
            UnitSiteConditions conditions = new UnitSiteConditions();
            BeanUtils.copyProperties(data,conditions);
            conditions.setEsId(CodeUtil.getEsId());
            conditions.setCreateTime(new Date());
            conditionsMapper.insert(conditions);
        } else {
            UnitSiteConditions conditions = conditionsMapper.selectById(data.getCode());
            if (conditions == null) {
                return new OutMessage<>(Status.OBJECT_DBISNOTALIVE);
            }
            BeanUtils.copyProperties(data,conditions);
            conditions.setUpdateTime(new Date());
            conditionsMapper.updateById(conditions);
        }
        return new OutMessage<>(Status.SUCCESS);
    }

    @Override
    public OutMessage getList(UnitSiteConditionsListDTO data) {
        LambdaQueryWrapper<UnitSiteConditions> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(UnitSiteConditions::getUnitCode,data.getUnitCode());
        wrapper.isNull(UnitSiteConditions::getDeleteTime)
                .orderByAsc(UnitSiteConditions::getCreateTime);
        Page<UnitSiteConditions> page = new Page<>(data.getPageNum(), data.getPageSize());
        Page<UnitSiteConditions> pageList = page(page, wrapper);
        return new OutMessage(Status.SUCCESS,pageList);
    }

    @Override
    public OutMessage findByCode(String code) {
        LambdaQueryWrapper<UnitSiteConditions> sql = new LambdaQueryWrapper<>();
        sql.eq(UnitSiteConditions::getCode,code).isNull(UnitSiteConditions::getDeleteTime);
        return new OutMessage(Status.SUCCESS,getOne(sql));
    }

    @Override
    public OutMessage delByCode(String code) {
        LambdaUpdateWrapper<UnitSiteConditions> sql = new LambdaUpdateWrapper<>();
        sql.set(UnitSiteConditions::getDeleteTime,new Date()).eq(UnitSiteConditions::getCode,code);
        update(sql);
        return new OutMessage(Status.SUCCESS);
    }
}
