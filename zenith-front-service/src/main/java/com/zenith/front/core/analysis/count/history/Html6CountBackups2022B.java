package com.zenith.front.core.analysis.count.history;

import cn.hutool.core.util.StrUtil;
import com.b1809.analysis.entity.PeggingPara;
import com.b1809.analysis.extend.ITableCount;
import com.zenith.front.core.analysis.ext.condition.year2022.MemAllCondition2022B;
import com.zenith.front.core.analysis.ext.condition.year2022.UnitAllConditionB;
import org.jooq.Condition;
import org.springframework.stereotype.Component;

import java.util.Map;

import static org.jooq.impl.DSL.*;

/**
 * 第六表 街道、社区（居委会）党员情况（乡镇社区作为村统计）
 *
 * <AUTHOR>
 * @date 2021/11/16
 */
@Component
public class Html6CountBackups2022B extends Html6CountHistory implements ITableCount {


    @Override
    public String getReportCode() {
        return "2022_6b_csq.html";
    }

    @Override
    public Map<String, Map<String, Number>> doCount(String reportCode, String orgCode, String orgLevelCode) throws Exception {
        return null;
    }

    @Override
    public Map<String, Object> doPegging(PeggingPara peggingPara) throws Exception {
        return null;
    }

    @Override
    public Map<String, Number> doReplenishCount(String reportCode, String orgCode, String orgLevelCode) throws Exception {
        //TODO 表6统计社区的地方，改成只统计城市社区
        return getReplenishCount(orgCode, orgLevelCode, Html1Count2022.TABLE_ES_YEAR_B, true);
    }

    @Override
    public Map<String, Object> doReplenishPegging(PeggingPara peggingPara) throws Exception {
        if (StrUtil.equalsAny(peggingPara.getRowIndex(), "3", "4", "5", "6", "7", "8", "9", "10", "11")) {
            UnitAllConditionB unitAllCondition = new UnitAllConditionB();
            Condition condition = noCondition().and(new Html6CountHistory().getUnitListCondition(peggingPara.getOrgCode(), peggingPara.getOrgLevelCode()).toString().replace("ccp_unit_all", unitAllCondition.getTableName()))
                    .and(field(name("d04_code"), String.class).eq("911")).and(getRowUnitCondition(peggingPara.getRowIndex()));
            return Html48CountHistory.getReportPageResult(peggingPara, unitAllCondition.getTableName(), condition, unitAllCondition.getLevelCodeField());
        }
        MemAllCondition2022B memAllCondition = new MemAllCondition2022B();
        Condition condition = noCondition().and(new Html6CountHistory().getMemListCondition(peggingPara.getOrgCode(), peggingPara.getOrgLevelCode()).toString().replace("ccp_mem_all", memAllCondition.getTableName()))
                .and(this.getRowCondition(peggingPara.getRowIndex(), true));
        return Html48CountHistory.getReportPageResult(peggingPara, memAllCondition.getTableName(), condition, memAllCondition.getLevelCodeField());
    }

}
