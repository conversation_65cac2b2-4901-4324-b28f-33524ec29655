package com.zenith.front.core.service.unit;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.zenith.front.api.mem.IMemService;
import com.zenith.front.api.org.IOrgCommitteeService;
import com.zenith.front.api.unit.IUnitCommitteeService;
import com.zenith.front.api.unit.IUnitCountrusideService;
import com.zenith.front.api.unit.IUnitMemSelectService;
import com.zenith.front.api.unit.IUnitResidentService;
import com.zenith.front.common.constant.CommonConstant;
import com.zenith.front.common.encrypt.properties.EncryptProperties;
import com.zenith.front.common.kit.StrKit;
import com.zenith.front.common.untils.BeanUtil;
import com.zenith.front.common.untils.SM4Untils;
import com.zenith.front.dao.mapper.org.OrgCommitteeElectMapper;
import com.zenith.front.dao.mapper.unit.UnitCommitteeElectMapper;
import com.zenith.front.dao.mapper.unit.UnitMemSelectMapper;
import com.zenith.front.dao.mapper.unit.UnitOrgLinkedMapper;
import com.zenith.front.model.bean.*;
import com.zenith.front.model.constant.UserConstant;
import com.zenith.front.model.dto.*;
import com.zenith.front.model.message.OutMessage;
import com.zenith.front.model.message.Status;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.interceptor.TransactionAspectSupport;

import javax.annotation.Resource;
import java.util.Date;
import java.util.Objects;

/**
 * <p>
 * 选调生信息 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-07
 */
@Service
public class UnitMemSelectServiceImpl extends ServiceImpl<UnitMemSelectMapper, UnitMemSelect> implements IUnitMemSelectService {
    @Resource
    private UnitMemSelectMapper unitMemSelectMapper;
    @Resource
    private UnitCommitteeElectMapper unitCommitteeElectMapper;
    @Resource
    private IUnitCommitteeService unitCommitteeService;
    @Resource
    private UnitOrgLinkedMapper unitOrgLinkedMapper;
    @Resource
    private OrgCommitteeElectMapper orgElectMapper;
    @Resource
    private IMemService memService;
    @Resource
    private IOrgCommitteeService orgCommitteeService;
    @Resource
    private IUnitResidentService unitResidentService;
    @Resource
    private IUnitCountrusideService unitCountrusideService;

    @Override
    public OutMessage<?> list(UnitMemSelectListDTO dto) {
        Page<UnitMemSelect> page = new Page<>(dto.getPageNum(), dto.getPageSize());
        LambdaQueryWrapper<UnitMemSelect> wrapper = Wrappers.lambdaQuery();
        wrapper.isNull(UnitMemSelect::getDeleteTime)
                .eq(UnitMemSelect::getUnitCode, dto.getUnitCode())
                .orderByDesc(UnitMemSelect::getCreateTime);
        page = page(page, wrapper);
        return new OutMessage<>(Status.SUCCESS, page);
    }

    /**
     * 新增
     *
     * @param dto
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public OutMessage<?> add(UnitMemSelectDTO dto) {
        UnitMemSelect entity = new UnitMemSelect();
        entity.setCode(IdUtil.simpleUUID());
        final User user = UserConstant.USER_CONTEXT.get().getUser();
        // 单位班子
        UnitCommitteeDTO unitCommitteeDTO = null;
        // 组织班子
        OrgCommitteeDTO orgCommitteeDTO = null;
        // 驻村干部
        UnitResidentDTO unitResidentDTO = null;
        // 村社区工作者
        UnitCountrysideDTO workUnitCountrysideDTO = null;
        // 村社区后备干部
        UnitCountrysideDTO reserveUnitCountrysideDTO = null;

        // 身份证加密
        final String memIdCard = SM4Untils.encryptContent(EncryptProperties.nginxKey, dto.getMemIdcard());
        final String memCode = dto.getMemCode();
        final String unitCode = dto.getUnitCode();

        // 1.根据身份证或者menCode查询当前数据库有效的选调生；不允许同个选调生同时添加
        LambdaQueryWrapper<UnitMemSelect> wrapper = Wrappers.lambdaQuery();
        wrapper.isNull(UnitMemSelect::getDeleteTime)
                .and(cond -> cond.eq(StrUtil.isNotBlank(memCode), UnitMemSelect::getMemCode, memCode).or().eq(UnitMemSelect::getMemIdcard, memIdCard));
        int count = count(wrapper);
        if (count > 0) {
            return new OutMessage<>(5010, "该党员选调生已存在，禁止重复添加！", null);
        }

        // 拷贝传入参数
        BeanUtils.copyProperties(dto, entity);

        // 选择本库党员的，查询是否存在
        if (StrUtil.equals(entity.getMemTypeCode(), "1") && StrUtil.isNotBlank(memCode)) {
            Mem memByCode = memService.findByCode(memCode);
            if (ObjectUtil.isNull(memByCode) || StrKit.isBlank(memByCode.getMemOrgCode())) {
                return new OutMessage<>(Status.MEM_IS_ERROR);
            }
        }

        // 单位班子参数对象转换、校验
        if (Objects.equals(entity.getIsUnitMem(), CommonConstant.ONE_INT)) {
            OutMessage<UnitCommitteeDTO> out = checkUnitMem(entity, unitCode);
            if (!Objects.equals(out.getCode(), Status.SUCCESS.getCode())) {
                return out;
            }
            unitCommitteeDTO = out.getData();
        }

        // 组织班子参数对象转换、校验
        if (Objects.equals(entity.getIsOrgMem(), CommonConstant.ONE_INT)) {
            OutMessage<OrgCommitteeDTO> out = checkOrgMem(entity, unitCode);
            if (!Objects.equals(out.getCode(), Status.SUCCESS.getCode())) {
                return out;
            }
            orgCommitteeDTO = out.getData();
        }

        // 单位驻村干部参数对象转换、校验
        if (Objects.equals(entity.getIsCadreMem(), CommonConstant.ONE_INT)) {
            OutMessage<UnitResidentDTO> out = checkCadreMem(entity, unitCode, memCode, memIdCard);
            if (!Objects.equals(out.getCode(), Status.SUCCESS.getCode())) {
                return out;
            }
            unitResidentDTO = out.getData();
        }
        // 村（社区）工作者参数对象转换、校验
        if (Objects.equals(entity.getIsWorkMem(), CommonConstant.ONE_INT)) {
            OutMessage<UnitCountrysideDTO> out = checkWorkMemOrReserveMem(entity, CommonConstant.ONE);
            if (!Objects.equals(out.getCode(), Status.SUCCESS.getCode())) {
                return out;
            }
            workUnitCountrysideDTO = out.getData();
        }
        // 村后备干部参数对象转换、校验
        if (Objects.equals(entity.getIsReserveMem(), CommonConstant.ONE_INT)) {
            OutMessage<UnitCountrysideDTO> out = checkWorkMemOrReserveMem(entity, CommonConstant.TWO);
            if (!Objects.equals(out.getCode(), Status.SUCCESS.getCode())) {
                return out;
            }
            reserveUnitCountrysideDTO = out.getData();
        }


        // 单位班子调用原有保存方法
        if (Objects.equals(entity.getIsUnitMem(), CommonConstant.ONE_INT) && Objects.nonNull(unitCommitteeDTO)) {
            OutMessage out = unitCommitteeService.addUnitCommittee(unitCommitteeDTO);
            if (!Objects.equals(out.getCode(), Status.SUCCESS.getCode())) {
                TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
                return out;
            }
            entity.setUnitMemCode(((UnitCommittee) out.getData()).getCode());
        }
        // 组织班子调用原有保存方法
        if (Objects.equals(entity.getIsOrgMem(), CommonConstant.ONE_INT) && Objects.nonNull(orgCommitteeDTO)) {
            OutMessage out = orgCommitteeService.add(orgCommitteeDTO);
            if (!Objects.equals(out.getCode(), Status.SUCCESS.getCode())) {
                TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
                return out;
            }
            entity.setOrgMemCode(((OrgCommittee) out.getData()).getCode());
        }
        // 驻村干部调用原有保存方法
        if (Objects.equals(entity.getIsCadreMem(), CommonConstant.ONE_INT) && Objects.nonNull(unitResidentDTO)) {
            OutMessage out = unitResidentService.doSave(unitResidentDTO, user.getAccount());
            if (!Objects.equals(out.getCode(), Status.SUCCESS.getCode())) {
                TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
                return out;
            }
            entity.setCadreMemCode(((UnitResident) out.getData()).getCode());
        }
        // 社区工作者调用原有保存方法
        if (Objects.equals(entity.getIsWorkMem(), CommonConstant.ONE_INT) && Objects.nonNull(workUnitCountrysideDTO)) {
            OutMessage out = unitCountrusideService.addUnitCountryside(workUnitCountrysideDTO);
            if (!Objects.equals(out.getCode(), Status.SUCCESS.getCode())) {
                TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
                return out;
            }
            entity.setWorkMemCode(((UnitCountryside) out.getData()).getCode());
        }
        //社区后备干部调用原有保存方法
        if (Objects.equals(entity.getIsReserveMem(), CommonConstant.ONE_INT) && Objects.nonNull(reserveUnitCountrysideDTO)) {
            OutMessage out = unitCountrusideService.addUnitCountryside(reserveUnitCountrysideDTO);
            if (!Objects.equals(out.getCode(), Status.SUCCESS.getCode())) {
                TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
                return out;
            }
            entity.setReserveMemCode(((UnitCountryside) out.getData()).getCode());
        }

        entity.setCreateTime(new Date());
        entity.setCreateUser(UserConstant.USER_CONTEXT.get().getUser().getAccount());
        entity.setUpdateTime(new Date());
        entity.setUpdateUser(entity.getCreateUser());
        boolean flag = save(entity);
        return new OutMessage<>(flag ? Status.SUCCESS : Status.FAIL, entity);
    }


    /**
     * 编辑-同步选调生的基础信息
     * 基本思想就是调用原来的接口进行修改，一旦原来接口有逻辑变动时候这边不用修改
     *
     * @param dto
     * @param isLeave 是否离开， true-是
     * @return
     */
    @Override
    public OutMessage<?> update(UnitMemSelectBaseDTO dto, Boolean isLeave) throws Exception {
        final String code = dto.getCode();
        LambdaQueryWrapper<UnitMemSelect> wrapper = Wrappers.lambdaQuery();
        wrapper.isNull(UnitMemSelect::getDeleteTime)
                .eq(UnitMemSelect::getCode, code);
        UnitMemSelect entity = getOne(wrapper, false);
        if (Objects.isNull(entity) || StrUtil.isBlank(entity.getCode())) {
            return new OutMessage<>(Status.OBJEC_NOT_EXIST);
        }

        // 离任时间
        Date leaveTime = null;
        // 离任原因
        String leaveRemark = null;
        if (Objects.equals(isLeave, true)) {
            leaveTime = dto.getLeaveTime();
            leaveRemark = dto.getLeaveRemark();
        } else {
            // 各个模块关联的进行同步更新
            dto.setDoubleFirstName(Objects.equals(dto.getIsDoubleFirst(), CommonConstant.ONE_INT) ? dto.getDoubleFirstName() : "");
            BeanUtils.copyProperties(dto, entity);
        }

        // 是否关联单位班子成员
        final String unitMemCode = entity.getUnitMemCode();
        if (Objects.equals(entity.getIsUnitMem(), CommonConstant.ONE_INT) && StrUtil.isNotBlank(unitMemCode)) {
            // 判断这个在班子中是否正常存在，存在则进行编辑、离开等操作
            LambdaQueryWrapper<UnitCommittee> w1 = Wrappers.lambdaQuery();
            w1.eq(UnitCommittee::getCode, unitMemCode)
                    .isNull(UnitCommittee::getDeleteTime)
                    .isNull(UnitCommittee::getEndDate);
            UnitCommittee obj = unitCommitteeService.getOne(w1, false);
            if (Objects.nonNull(obj) && StrUtil.isNotBlank(obj.getCode())) {
                UnitCommitteeDTO unitCommitteeDTO = new UnitCommitteeDTO();
                // 原来不会改的信息也要作为参数传过去，否则会置为空
                BeanUtils.copyProperties(obj, unitCommitteeDTO);

                // 设置修改的基础信息
                this.toUnitMem(entity, unitCommitteeDTO);
                // 设置保存的关联code
                unitCommitteeDTO.setCode(unitMemCode);
                unitCommitteeDTO.setEndDate(leaveTime);
                unitCommitteeDTO.setElectCode(entity.getUnitMemElectCode());
                OutMessage out = unitCommitteeService.updateUnitCommittee(unitCommitteeDTO, false);
                if (!Objects.equals(out.getCode(), Status.SUCCESS.getCode())) {
                    return out;
                }
            }

        }

        // 是否关联组织班子成员
        final String orgMemCode = entity.getOrgMemCode();
        if (Objects.equals(entity.getIsOrgMem(), CommonConstant.ONE_INT) && StrUtil.isNotBlank(orgMemCode)) {
            // 判断这个在班子中是否正常存在，存在则进行编辑、离开等操作
            LambdaQueryWrapper<OrgCommittee> w1 = Wrappers.lambdaQuery();
            w1.eq(OrgCommittee::getCode, orgMemCode)
                    .isNull(OrgCommittee::getDeleteTime)
                    .isNull(OrgCommittee::getEndDate);
            OrgCommittee obj = orgCommitteeService.getOne(w1, false);
            if (Objects.nonNull(obj) && StrUtil.isNotBlank(obj.getCode())) {
                OrgCommitteeDTO orgCommitteeDTO = new OrgCommitteeDTO();
                // 原来不会改的信息也要作为参数传过去，否则会置为空
                BeanUtils.copyProperties(obj, orgCommitteeDTO);

                // 设置修改的基础信息
                this.toOrgMem(entity, orgCommitteeDTO);
                // 设置保存的关联code
                orgCommitteeDTO.setCode(orgMemCode);
                orgCommitteeDTO.setElectCode(entity.getOrgMemElectCode());
                // 是否离开
                if (Objects.nonNull(leaveTime)) {
                    orgCommitteeDTO.setD120Code("99");
                    orgCommitteeDTO.setD120Name("选调生离开");
                    orgCommitteeDTO.setEndDate(leaveTime);
                }
                OutMessage out = orgCommitteeService.updateM(orgCommitteeDTO);
                if (!Objects.equals(out.getCode(), Status.SUCCESS.getCode())) {
                    return out;
                }
            }
        }

        // 是否关联驻村干部
        final String cadreMemCode = entity.getCadreMemCode();
        if (Objects.equals(entity.getIsCadreMem(), CommonConstant.ONE_INT) && StrUtil.isNotBlank(cadreMemCode)) {
            // 判断这个在班子中是否正常存在，存在则进行编辑、离开等操作
            LambdaQueryWrapper<UnitResident> w1 = Wrappers.lambdaQuery();
            w1.eq(UnitResident::getCode, cadreMemCode)
                    .isNull(UnitResident::getDeleteTime)
                    .isNull(UnitResident::getEndDate);
            UnitResident obj = unitResidentService.getOne(w1, false);
            if (Objects.nonNull(obj) && StrUtil.isNotBlank(obj.getCode())) {
                UnitResidentDTO unitResidentDTO = new UnitResidentDTO();
                // 原来不会改的信息也要作为参数传过去，否则会置为空
                BeanUtils.copyProperties(obj, unitResidentDTO);

                // 设置修改的基础信息
                this.toCadreMem(entity, unitResidentDTO);
                UnitResidentUpdateDTO unitResidentUpdateDTO = new UnitResidentUpdateDTO();
                BeanUtils.copyProperties(unitResidentDTO, unitResidentUpdateDTO);
                // 设置保存的关联code
                unitResidentUpdateDTO.setCode(cadreMemCode);
                unitResidentUpdateDTO.setEndDate(leaveTime);
                OutMessage out = unitResidentService.doUpdate(unitResidentUpdateDTO, UserConstant.USER_CONTEXT.get().getUser().getAccount());
                if (!Objects.equals(out.getCode(), Status.SUCCESS.getCode())) {
                    return out;
                }
            }
        }

        // 是否关联村工作者
        final String workMemCode = entity.getWorkMemCode();
        if (Objects.equals(entity.getIsWorkMem(), CommonConstant.ONE_INT) && StrUtil.isNotBlank(workMemCode)) {
            // 判断这个在班子中是否正常存在，存在则进行编辑、离开等操作
            LambdaQueryWrapper<UnitCountryside> w1 = Wrappers.lambdaQuery();
            w1.eq(UnitCountryside::getCode, workMemCode)
                    .isNull(UnitCountryside::getDeleteTime)
                    .isNull(UnitCountryside::getLeaveTime);
            UnitCountryside obj = unitCountrusideService.getOne(w1, false);
            if (Objects.nonNull(obj) && StrUtil.isNotBlank(obj.getCode())) {
                UnitCountrysideDTO unitCountrysideDTO = new UnitCountrysideDTO();
                // 原来不会改的信息也要作为参数传过去，否则会置为空
                BeanUtils.copyProperties(obj, unitCountrysideDTO);

                // 设置保存的关联code
                unitCountrysideDTO.setCode(workMemCode);
                if (Objects.isNull(leaveTime)) {
                    // 设置修改的基础信息
                    this.toWorkMemOrReserveMem(entity, unitCountrysideDTO);
                    OutMessage out = unitCountrusideService.updateUnitCountryside(unitCountrysideDTO);
                    if (!Objects.equals(out.getCode(), Status.SUCCESS.getCode())) {
                        return out;
                    }
                } else {
                    unitCountrysideDTO.setUnitCode(entity.getUnitCode());
                    unitCountrysideDTO.setLeaveTime(leaveTime);
                    unitCountrysideDTO.setD117Code("99");
                    unitCountrysideDTO.setD117Name("选调生离开");
                    OutMessage out = unitCountrusideService.delUnitCountryside(unitCountrysideDTO);
                    if (!Objects.equals(out.getCode(), Status.SUCCESS.getCode())) {
                        return out;
                    }
                }
            }
        }

        // 是否关联村后备干部
        final String reserveMemCode = entity.getReserveMemCode();
        if (Objects.equals(entity.getIsReserveMem(), CommonConstant.ONE_INT) && StrUtil.isNotBlank(reserveMemCode)) {
            // 判断这个在班子中是否正常存在，存在则进行编辑、离开等操作
            LambdaQueryWrapper<UnitCountryside> w1 = Wrappers.lambdaQuery();
            w1.eq(UnitCountryside::getCode, reserveMemCode)
                    .isNull(UnitCountryside::getDeleteTime)
                    .isNull(UnitCountryside::getLeaveTime);
            UnitCountryside obj = unitCountrusideService.getOne(w1, false);
            if (Objects.nonNull(obj) && StrUtil.isNotBlank(obj.getCode())) {
                UnitCountrysideDTO unitCountrysideDTO = new UnitCountrysideDTO();
                // 原来不会改的信息也要作为参数传过去，否则会置为空
                BeanUtils.copyProperties(obj, unitCountrysideDTO);

                // 设置保存的关联code
                unitCountrysideDTO.setCode(reserveMemCode);
                if (Objects.isNull(leaveTime)) {
                    // 设置修改的基础信息
                    this.toWorkMemOrReserveMem(entity, unitCountrysideDTO);
                    OutMessage out = unitCountrusideService.updateUnitCountryside(unitCountrysideDTO);
                    if (!Objects.equals(out.getCode(), Status.SUCCESS.getCode())) {
                        return out;
                    }
                } else {
                    unitCountrysideDTO.setCode(entity.getReserveMemCode());
                    unitCountrysideDTO.setLeaveTime(leaveTime);
                    unitCountrysideDTO.setD117Code("99");
                    unitCountrysideDTO.setD117Name("选调生离开");
                    OutMessage out = unitCountrusideService.delUnitCountryside(unitCountrysideDTO);
                    if (!Objects.equals(out.getCode(), Status.SUCCESS.getCode())) {
                        return out;
                    }
                }
            }
        }

        if (Objects.nonNull(leaveTime)) {
            // 如果离开时间不为空则离开处理
            entity.setDeleteTime(new Date());
            entity.setLeaveTime(leaveTime);
            entity.setLeaveRemark(leaveRemark);
        }
        entity.setUpdateTime(new Date());
        entity.setUpdateUser(UserConstant.USER_CONTEXT.get().getUser().getAccount());
        boolean flag = updateById(entity);
        return new OutMessage<>(flag ? Status.SUCCESS : Status.FAIL, entity);
    }

    /**
     * 离开-将个模块成员数据转为历史任职
     *
     * @param dto
     * @return
     */
    @Override
    public OutMessage<?> leave(UnitMemSelectLeaveDTO dto) throws Exception {
        UnitMemSelectBaseDTO unitMemSelectBaseDTO = new UnitMemSelectBaseDTO();
        unitMemSelectBaseDTO.setLeaveTime(dto.getLeaveTime());
        unitMemSelectBaseDTO.setLeaveRemark(dto.getLeaveRemark());
        unitMemSelectBaseDTO.setCode(dto.getCode());
        return this.update(unitMemSelectBaseDTO, true);
    }

    /**
     * 删除-将各个模块关联数据删除
     *
     * @param code
     * @return
     */
    @Override
    public OutMessage<?> delete(String code) {
        LambdaQueryWrapper<UnitMemSelect> wrapper = Wrappers.lambdaQuery();
        wrapper.isNull(UnitMemSelect::getDeleteTime)
                .eq(UnitMemSelect::getCode, code);
        UnitMemSelect entity = getOne(wrapper, false);
        if (Objects.isNull(entity) || StrUtil.isBlank(entity.getCode())) {
            return new OutMessage<>(Status.OBJEC_NOT_EXIST);
        }
        entity.setDeleteTime(new Date());
        entity.setUpdateTime(new Date());
        entity.setUpdateUser(UserConstant.USER_CONTEXT.get().getUser().getAccount());

        // 是否关联单位班子成员
        if (Objects.equals(entity.getIsUnitMem(), CommonConstant.ONE_INT) && StrUtil.isNotBlank(entity.getUnitMemCode())) {
            unitCommitteeService.delUnitCommittee(entity.getUnitMemCode());
        }

        // 是否关联组织班子成员
        if (Objects.equals(entity.getIsOrgMem(), CommonConstant.ONE_INT) && StrUtil.isNotBlank(entity.getOrgMemCode())) {
            OrgCommitteeDTO orgCommitteeDTO = new OrgCommitteeDTO();
            orgCommitteeDTO.setElectCode(entity.getOrgMemElectCode());
            orgCommitteeDTO.setCode(entity.getOrgMemCode());
            orgCommitteeService.del(orgCommitteeDTO);

        }

        // 是否关联驻村干部
        if (Objects.equals(entity.getIsCadreMem(), CommonConstant.ONE_INT) && StrUtil.isNotBlank(entity.getCadreMemCode())) {
            PrimaryKeyDTO data = new PrimaryKeyDTO();
            data.setCode(entity.getCadreMemCode());
            unitResidentService.doDel(data, UserConstant.USER_CONTEXT.get().getUser().getAccount());
        }

        // 是否关联村工作者
        if (Objects.equals(entity.getIsWorkMem(), CommonConstant.ONE_INT) && StrUtil.isNotBlank(entity.getWorkMemCode())) {
            UnitCountrysideDTO unitCountrysideDTO = new UnitCountrysideDTO();
            unitCountrysideDTO.setCode(entity.getWorkMemCode());
            unitCountrysideDTO.setUnitCode(entity.getUnitCode());
            unitCountrysideDTO.setLeaveTime(entity.getDeleteTime());
            unitCountrysideDTO.setD117Code("99");
            unitCountrysideDTO.setD117Name("选调生离开");
            unitCountrusideService.delUnitCountryside(unitCountrysideDTO);
        }

        // 是否关联村后备干部
        if (Objects.equals(entity.getIsReserveMem(), CommonConstant.ONE_INT) && StrUtil.isNotBlank(entity.getReserveMemCode())) {
            UnitCountrysideDTO unitCountrysideDTO = new UnitCountrysideDTO();
            unitCountrysideDTO.setCode(entity.getReserveMemCode());
            unitCountrysideDTO.setUnitCode(entity.getUnitCode());
            unitCountrysideDTO.setLeaveTime(entity.getDeleteTime());
            unitCountrysideDTO.setD117Code("99");
            unitCountrysideDTO.setD117Name("选调生离开");
            unitCountrusideService.delUnitCountryside(unitCountrysideDTO);

        }
        LambdaUpdateWrapper<UnitMemSelect> updateWrapper = Wrappers.lambdaUpdate();
        updateWrapper.set(UnitMemSelect::getDeleteTime, entity.getDeleteTime())
                .eq(UnitMemSelect::getCode, code);
        boolean flag = update(updateWrapper);
        return new OutMessage<>(flag ? Status.SUCCESS : Status.FAIL, entity);
    }

    /**
     * 单位班子成员信息
     *
     * @param dto
     * @return
     */
    private OutMessage<UnitCommitteeDTO> checkUnitMem(UnitMemSelect dto, String unitCode) {
        UnitCommitteeDTO unitCommitteeDTO = new UnitCommitteeDTO();
        BeanUtil.copyLikeFirst(dto, unitCommitteeDTO, "UnitMem");
        this.toUnitMem(dto, unitCommitteeDTO);
        unitCommitteeDTO.setUnitCode(unitCode);

        // 查询是否存在最新届次
        LambdaQueryWrapper<UnitCommitteeElect> queryWrapper = new LambdaQueryWrapper<UnitCommitteeElect>()
                .eq(UnitCommitteeElect::getUnitCode, unitCode)
                .isNull(UnitCommitteeElect::getDeleteTime)
                .ge(UnitCommitteeElect::getTenureEndDate, DateUtil.beginOfDay(new Date()))
                .orderByDesc(UnitCommitteeElect::getTenureEndDate)
                .last("LIMIT 1");
        UnitCommitteeElect elect = unitCommitteeElectMapper.selectOne(queryWrapper);
        if (Objects.isNull(elect) || StrUtil.isBlank(elect.getCode())) {
            return new OutMessage<>(5010, "单位行政班子暂无有效最新届次，请添加届次后选择！", null);
        }
        Date tenureStartDate = elect.getTenureStartDate();
        Date startDate = dto.getUnitMemStartDate();
        if(tenureStartDate.getTime() > startDate.getTime()) {
            return new OutMessage<>(5010, "单位行政班子任职起始日期不能早于：" + DateUtil.format(tenureStartDate, "yyyy-MM-dd"), null);
        }
        unitCommitteeDTO.setElectCode(elect.getCode());
        dto.setUnitMemElectCode(elect.getCode());
        OutMessage out = unitCommitteeService.addUnitCommittee(unitCommitteeDTO, true);
        if (!Objects.equals(out.getCode(), Status.SUCCESS.getCode())) {
            return out;
        }

        return new OutMessage<>(Status.SUCCESS, unitCommitteeDTO);
    }

    /**
     * 组织班子成员信息
     *
     * @param dto
     * @return
     */
    private OutMessage<OrgCommitteeDTO> checkOrgMem(UnitMemSelect dto, String unitCode) {
        OrgCommitteeDTO orgCommitteeDTO = new OrgCommitteeDTO();
        BeanUtil.copyLikeFirst(dto, orgCommitteeDTO, "OrgMem");
        this.toOrgMem(dto, orgCommitteeDTO);

        // 查询是否存在关联单位的组织是否存在最新届次
        LambdaQueryWrapper<UnitOrgLinked> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(UnitOrgLinked::getUnitCode, unitCode)
                .eq(UnitOrgLinked::getIsUnitMain, CommonConstant.ONE_INT)
                .eq(UnitOrgLinked::getIsOrgMain, CommonConstant.ONE_INT)
                .isNull(UnitOrgLinked::getDeleteTime)
                .last("LIMIT 1");
        UnitOrgLinked unitOrgLinked = unitOrgLinkedMapper.selectOne(queryWrapper);
        if (Objects.isNull(unitOrgLinked) || StrUtil.isBlank(unitOrgLinked.getOrgCode())) {
            return new OutMessage<>(5010, "单位未关联有效党组织，请关联组织后再选择组织行政班子成员！", null);
        }
        LambdaQueryWrapper<OrgCommitteeElect> orgElectWrapper = new LambdaQueryWrapper<>();
        orgElectWrapper.eq(OrgCommitteeElect::getOrgCode, unitOrgLinked.getOrgCode())
                .isNull(OrgCommitteeElect::getDeleteTime)
                .ge(OrgCommitteeElect::getTenureEndDate, DateUtil.beginOfDay(new Date()))
                .orderByDesc(OrgCommitteeElect::getTenureEndDate)
                .last("LIMIT 1");
        OrgCommitteeElect orgCommitteeElect = orgElectMapper.selectOne(orgElectWrapper);
        if (Objects.isNull(orgCommitteeElect) || StrUtil.isBlank(orgCommitteeElect.getCode())) {
            return new OutMessage<>(5010, "组织行政班子暂无有效最新届次，请添加届次后选择！", null);
        }

        Date tenureStartDate = orgCommitteeElect.getTenureStartDate();
        Date startDate = dto.getOrgMemStartDate();
        if(tenureStartDate.getTime() > startDate.getTime()) {
            return new OutMessage<>(5010, "组织行政班子任职起始日期不能早于：" + DateUtil.format(tenureStartDate, "yyyy-MM-dd"), null);
        }

        orgCommitteeDTO.setElectCode(orgCommitteeElect.getCode());
        dto.setOrgMemElectCode(orgCommitteeElect.getCode());
        OutMessage out = orgCommitteeService.add(orgCommitteeDTO, true);
        if (!Objects.equals(out.getCode(), Status.SUCCESS.getCode())) {
            return out;
        }
        return new OutMessage(Status.SUCCESS, orgCommitteeDTO);
    }

    /**
     * 驻村干部信息
     *
     * @param dto
     * @param unitCode
     * @param memCode
     * @param memIdCard
     * @return
     */
    private OutMessage<UnitResidentDTO> checkCadreMem(UnitMemSelect dto, String unitCode, String memCode, String memIdCard) {
        UnitResidentDTO unitResidentDTO = new UnitResidentDTO();
        BeanUtil.copyLikeFirst(dto, unitResidentDTO, "CadreMem");
        this.toCadreMem(dto, unitResidentDTO);

        LambdaQueryWrapper<UnitResident> wrapper = Wrappers.lambdaQuery();
        wrapper.eq(UnitResident::getUnitCode, unitCode)
                .isNull(UnitResident::getEndDate)
                .isNull(UnitResident::getDeleteTime)
                .and(e -> e.eq(UnitResident::getMemIdcard, memIdCard).or().eq(UnitResident::getMemCode, memCode));
        int count = unitResidentService.count(wrapper);
        if (count > 0) {
            return new OutMessage<>(5010, "驻村干部已存在该党员，请不要重复添加", null);
        }
        return new OutMessage<>(Status.SUCCESS, unitResidentDTO);
    }

    /**
     * 村（社区）工作者、后备干部
     *
     * @param dto
     * @param type 1社区工作者，2村后备干部
     * @return
     */
    private OutMessage<UnitCountrysideDTO> checkWorkMemOrReserveMem(UnitMemSelect dto, String type) {
        UnitCountrysideDTO unitCountrysideDTO = new UnitCountrysideDTO();
        BeanUtil.copyLikeFirst(dto, unitCountrysideDTO, Objects.equals(CommonConstant.ONE, type) ? "WorkMem" : "ReserveMem");
        this.toWorkMemOrReserveMem(dto, unitCountrysideDTO);
        // 数据类型
        unitCountrysideDTO.setType(type);

        OutMessage out = unitCountrusideService.addUnitCountryside(unitCountrysideDTO, true);
        if (!Objects.equals(out.getCode(), Status.SUCCESS.getCode())) {
            out.setMessage((Objects.equals(CommonConstant.ONE, type) ? "村（社区）工作者：" : "村（社区）后备干部：") + out.getMessage());
            return out;
        }
        return new OutMessage<>(Status.SUCCESS, unitCountrysideDTO);
    }

    /**
     * 单位班子基础 信息
     *
     * @param dto
     * @param unitCommitteeDTO
     */
    private void toUnitMem(UnitMemSelect dto, UnitCommitteeDTO unitCommitteeDTO) {
        // 基础信息
        unitCommitteeDTO.setMemTypeCode(dto.getMemTypeCode());
        unitCommitteeDTO.setMemTypeName(dto.getMemTypeName());
        unitCommitteeDTO.setSexCode(dto.getSexCode());
        unitCommitteeDTO.setSexName(dto.getSexName());
        unitCommitteeDTO.setMemIdcard(dto.getMemIdcard());
        unitCommitteeDTO.setBirthday(dto.getBirthday());
        unitCommitteeDTO.setD89Code(dto.getD89Code());
        unitCommitteeDTO.setD89Name(dto.getD89Name());
        unitCommitteeDTO.setD07Code(dto.getD07Code());
        unitCommitteeDTO.setD07Name(dto.getD07Name());
        unitCommitteeDTO.setMemName(dto.getMemName());
        unitCommitteeDTO.setMemCode(dto.getMemCode());
        unitCommitteeDTO.setHasVillageTransferStudent(CommonConstant.ONE);
        unitCommitteeDTO.setD144Code(dto.getD144Code());
        unitCommitteeDTO.setD144Name(dto.getD144Name());
        unitCommitteeDTO.setIsDoubleFirst(dto.getIsDoubleFirst());
        unitCommitteeDTO.setUnitCode(dto.getUnitCode());
        unitCommitteeDTO.setUnitName(dto.getUnitName());
        unitCommitteeDTO.setElectCode(dto.getUnitMemElectCode());
    }

    /**
     * 组织班子成员基础信息
     *
     * @param dto
     * @param orgCommitteeDTO
     */
    private void toOrgMem(UnitMemSelect dto, OrgCommitteeDTO orgCommitteeDTO) {
        // 基础信息
        orgCommitteeDTO.setMemTypeCode(dto.getMemTypeCode());
        orgCommitteeDTO.setMemTypeName(dto.getMemTypeName());
        orgCommitteeDTO.setSexCode(dto.getSexCode());
        orgCommitteeDTO.setSexName(dto.getSexName());
        orgCommitteeDTO.setMemIdcard(dto.getMemIdcard());
        orgCommitteeDTO.setBirthday(dto.getBirthday());
        orgCommitteeDTO.setD89Code(dto.getD89Code());
        orgCommitteeDTO.setD89Name(dto.getD89Name());
        orgCommitteeDTO.setD07Code(dto.getD07Code());
        orgCommitteeDTO.setD07Name(dto.getD07Name());
        orgCommitteeDTO.setMemName(dto.getMemName());
        orgCommitteeDTO.setMemCode(dto.getMemCode());
        orgCommitteeDTO.setHasVillageTransferStudent(CommonConstant.ONE_INT);
        orgCommitteeDTO.setD144Code(dto.getD144Code());
        orgCommitteeDTO.setD144Name(dto.getD144Name());
        orgCommitteeDTO.setIsDoubleFirst(dto.getIsDoubleFirst());
    }

    /**
     * 驻村干部基础信息
     *
     * @param dto
     * @param unitResidentDTO
     */
    private void toCadreMem(UnitMemSelect dto, UnitResidentDTO unitResidentDTO) {
        // 基础信息
        unitResidentDTO.setD139Code(Objects.equals(dto.getMemTypeCode(), CommonConstant.ONE) ? dto.getMemTypeCode() : "3");
        unitResidentDTO.setD139Name(Objects.equals(dto.getMemTypeCode(), CommonConstant.ONE) ? "本党组织内党员" : "非党员");
        unitResidentDTO.setSexCode(dto.getSexCode());
        unitResidentDTO.setSexName(dto.getSexName());
        unitResidentDTO.setMemIdcard(dto.getMemIdcard());
        unitResidentDTO.setMemBirthday(dto.getBirthday());
        unitResidentDTO.setD07Code(dto.getD07Code());
        unitResidentDTO.setD07Name(dto.getD07Name());
        unitResidentDTO.setMemName(dto.getMemName());
        unitResidentDTO.setMemCode(dto.getMemCode());
        unitResidentDTO.setHasVillageTransferStudent(CommonConstant.ONE_INT);
        unitResidentDTO.setD144Code(dto.getD144Code());
        unitResidentDTO.setD144Name(dto.getD144Name());
        unitResidentDTO.setUnitCode(dto.getUnitCode());
    }

    /**
     * 村工作者、后备干部基础信息
     *
     * @param dto
     * @param unitCountrysideDTO
     */
    private void toWorkMemOrReserveMem(UnitMemSelect dto, UnitCountrysideDTO unitCountrysideDTO) {
        // 基础信息
        unitCountrysideDTO.setMemTypeCode(dto.getMemTypeCode());
        unitCountrysideDTO.setMemTypeName(dto.getMemTypeName());
        unitCountrysideDTO.setSexCode(dto.getSexCode());
        unitCountrysideDTO.setSexName(dto.getSexName());
        unitCountrysideDTO.setMemIdcard(dto.getMemIdcard());
        unitCountrysideDTO.setBirthday(dto.getBirthday());
        unitCountrysideDTO.setD07Code(dto.getD07Code());
        unitCountrysideDTO.setD07Name(dto.getD07Name());
        unitCountrysideDTO.setMemName(dto.getMemName());
        unitCountrysideDTO.setMemCode(dto.getMemCode());
        unitCountrysideDTO.setHasVillageTransferStudent(CommonConstant.ONE_INT);
        unitCountrysideDTO.setD144Code(dto.getD144Code());
        unitCountrysideDTO.setD144Name(dto.getD144Name());
        unitCountrysideDTO.setIsDoubleFirst(dto.getIsDoubleFirst());
        unitCountrysideDTO.setUnitCode(dto.getUnitCode());
        unitCountrysideDTO.setUnitName(dto.getUnitName());
        unitCountrysideDTO.setPhone(dto.getPhone());
    }
}
