package com.zenith.front.core.service.ztdc;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.zenith.front.api.ztdc.IZt4BasicWordService;
import com.zenith.front.common.kit.StrKit;
import com.zenith.front.dao.mapper.ztdc.Zt4BasicWordMapper;
import com.zenith.front.model.dto.Zt4BasicWordDTO;
import com.zenith.front.model.message.OutMessage;
import com.zenith.front.model.message.Status;
import com.zenith.front.model.bean.Zt4BasicWord;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.Objects;

/**
 * <p>
 * 城市基层党建工作有关情况 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-08-13
 */
@Service
public class Zt4BasicWordServiceImpl extends ServiceImpl<Zt4BasicWordMapper, Zt4BasicWord> implements IZt4BasicWordService {

    @Resource
    private Zt4BasicWordMapper basicWordMapper;

    @Override
    public OutMessage addFour(Zt4BasicWordDTO data) {
        Zt4BasicWord optimize = basicWordMapper.selectOne(new QueryWrapper<Zt4BasicWord>().lambda().eq(Zt4BasicWord::getUnitCode, data.getUnitCode()).isNull(Zt4BasicWord::getDeleteTime));
        int result;
        if (Objects.nonNull(optimize)) {
            String code = optimize.getCode();
            BeanUtils.copyProperties(data, optimize);
            optimize.setUpdateTime(new Date());
            optimize.setCode(code);
            result = basicWordMapper.updateById(optimize);
        } else {
            Zt4BasicWord zt4BasicWord = new Zt4BasicWord();
            BeanUtils.copyProperties(data, zt4BasicWord);
            zt4BasicWord.setCode(StrKit.getRandomUUID());
            zt4BasicWord.setCreateTime(new Date());
            result = basicWordMapper.insert(zt4BasicWord);
        }
        return new OutMessage<>(result > 0 ? Status.SUCCESS : Status.FAIL);
    }
}
