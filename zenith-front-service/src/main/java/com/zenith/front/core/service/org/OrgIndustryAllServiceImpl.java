package com.zenith.front.core.service.org;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.zenith.front.api.org.IOrgIndustryAllService;
import com.zenith.front.dao.mapper.org.OrgIndustryAllMapper;
import com.zenith.front.model.bean.OrgIndustryAll;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <p>
 * 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-11-22
 */
@Service
public class OrgIndustryAllServiceImpl extends ServiceImpl<OrgIndustryAllMapper, OrgIndustryAll> implements IOrgIndustryAllService {


    @Override
    public OrgIndustryAll findByCode(String code) {
        return getOne(new LambdaQueryWrapper<OrgIndustryAll>().eq(OrgIndustryAll::getCode, code).last("limit 1"));
    }

    @Override
    public List<OrgIndustryAll> findOrgIndustryAllByOrgCode(String orgCode) {
        return list(
                new LambdaQueryWrapper<OrgIndustryAll>()
                        .isNull(OrgIndustryAll::getDeleteTime)
                        .eq(OrgIndustryAll::getOrgCode, orgCode)
                        .orderByDesc(OrgIndustryAll::getCreateTime)
        );
    }
}
