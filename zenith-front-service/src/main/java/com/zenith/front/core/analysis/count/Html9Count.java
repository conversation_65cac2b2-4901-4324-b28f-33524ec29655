package com.zenith.front.core.analysis.count;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.b1809.analysis.entity.PeggingPara;
import com.b1809.analysis.extend.ITableCount;
import com.b1809.analysis.plugin.elasticsearch.EsKit;
import com.jfinal.plugin.activerecord.Record;
import com.zenith.front.core.analysis.ext.condition.MemAllCondition;
import com.zenith.front.core.analysis.ext.condition.OrgAllCondition;
import org.jooq.Condition;
import org.jooq.Record1;
import org.jooq.SelectHavingStep;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.stream.Collectors;

import static com.zenith.front.common.constant.DBConstant.DSL_CONTEXT;
import static org.jooq.impl.DSL.*;

/**
 * 民主评议党员情况
 *
 */
@Component
public class Html9Count implements ITableCount {

    @Override
    public String getReportCode() {
        return "9.html";
    }

    @Override
    public Map<String, Map<String, Number>> doCount(String reportCode, String orgCode, String orgLevelCode) throws Exception {
        return getCount(orgCode, orgLevelCode, null);
    }

    public Map<String, Map<String, Number>> getCount(String orgCode, String orgLevelCode, String tableYear) throws Exception {
        String ccpOrgAll = StrUtil.isEmpty(tableYear) ? "ccp_org_all" : "ccp_org_all_" + tableYear;
        String ccpMemAll = StrUtil.isEmpty(tableYear) ? "ccp_mem_all" : "ccp_mem_all_" + tableYear;
        Map<String, Number> result = new LinkedHashMap<>();
        Html53Count.initTableCol(1, 12, 1, 9, result);
        Condition condition = noCondition().and("appraisal_org_code like '" + orgLevelCode + "%'");

        SelectHavingStep<Record1<Object>> ccp_org_all = DSL_CONTEXT.select(field("has_join_reviewers,has_end_reviewers,d04_code, d16_code,is_retire,code ,count(1) as total"))
                .from(table(name(ccpOrgAll)).as("ccp_org_all")).where(getOrgCondition().and(condition)).groupBy(field("d04_code,has_join_reviewers,has_end_reviewers, d16_code,is_retire,code"));
        List<Record> orgList = EsKit.findBySql(ccp_org_all.toString()).toRecord();

        //筛选出离休党支部
        SelectHavingStep<Record1<Object>> retireOrgListSql = DSL_CONTEXT.select(field("code")).from(table(name("ccp_org_all"))).where(getOrgCondition().and("is_retire = 1"));
        List<Record> retireOrgR = EsKit.findBySql(retireOrgListSql.toString()).toRecord();
        HashSet<String> retireOrgList = retireOrgR.stream().filter(e -> StrUtil.isNotEmpty(e.getStr("code"))).map(e -> e.getStr("code")).collect(Collectors.toCollection(HashSet::new));

        orgList.forEach(record -> {
            Integer total = record.getInt("total");
            Integer hasJoinReviewers = record.getInt("has_join_reviewers");
            Integer hasEndReviewers = record.getInt("has_end_reviewers");

            if (Objects.equals(hasJoinReviewers, 1)) {
                setCellValue(record, "1", result, total, retireOrgList);
            }
            if (Objects.equals(hasEndReviewers, 1)) {
                setCellValue(record, "2", result, total, retireOrgList);
            }
        });


        SelectHavingStep<Record1<Object>> ccp_mem_all = DSL_CONTEXT.select(field("has_unit_statistics,has_unit_province,has_join_reviewers,has_end_reviewers,result,situation,delete_time,d04_code, d16_code,org_code,count(1) as total"))
                .from(table(name(ccpMemAll)).as("ccp_mem_all")).where(condition.and("has_join_reviewers=1 and d08_code in('1','2')")).groupBy(field("has_unit_statistics,has_unit_province,d04_code,result,situation,has_join_reviewers,has_end_reviewers,delete_time, d16_code,org_code"));
        List<Record> memList = EsKit.findBySql(ccp_mem_all.toString()).toRecord();
        memList.forEach(record -> setMemStatisticsValue(record, result, retireOrgList));

        Map<String, Map<String, Number>> mapMap = new HashMap<>();
        mapMap.put("table0", result);
        return mapMap;
    }

    public Condition getOrgCondition() {
        return noCondition().and("delete_time is null and (is_dissolve is null or is_dissolve!=1) and has_join_reviewers=1 and d01_code in('631', '632', '634', '931', '932')");
    }

    @Override
    public Map<String, Object> doPegging(PeggingPara peggingPara) throws Exception {
        //查询党支部
        Condition queryOrgCondition = noCondition().and("appraisal_org_code like '" + peggingPara.getOrgLevelCode() + "%'").and("is_retire = 1");
        SelectHavingStep<Record1<Object>> ccp_org_all = DSL_CONTEXT.select(field("has_join_reviewers,has_end_reviewers,d04_code, d16_code,is_retire,code, count(1) as total"))
                .from(table(name("ccp_org_all"))).where(getOrgCondition().and(queryOrgCondition)).groupBy(field("d04_code,has_join_reviewers,has_end_reviewers, d16_code,is_retire,code"));
        List<Record> orgList = EsKit.findBySql(ccp_org_all.toString()).toRecord();
        List<String> retireOrgList = new ArrayList<>();
        //筛选出离休党支部
        for (Record record : orgList) {
                String orgCode = record.getStr("code");
                retireOrgList.add(orgCode);
        }
        String rowIndex = peggingPara.getRowIndex();
        Condition condition = getColCondition(peggingPara.getColIndex(),rowIndex,retireOrgList).and("appraisal_org_code like '" + peggingPara.getOrgLevelCode() + "%'");
        if (StrUtil.equalsAny(rowIndex, "1", "2")) {
            condition = condition.and(getOrgCondition()).and(StrUtil.equals(rowIndex, "1") ? "has_join_reviewers=1" : "has_end_reviewers=1");
            OrgAllCondition cond = new OrgAllCondition();
            return Html53Count.getReportPageResult(peggingPara, cond.getTableName(), condition, cond.getLevelCodeFieldAppraisal());
        }
        MemAllCondition memAllCond = new MemAllCondition();
        condition = condition.and(getRowCondition(rowIndex)).and("has_join_reviewers=1 and d08_code in('1','2')");
        return Html53Count.getReportPageResult(peggingPara, memAllCond.getTableName(), condition, memAllCond.getLevelCodeFieldAppraisal());
    }


    public static void setMemStatisticsValue(Record record, Map<String, Number> result, HashSet<String> retireOrgList) {
        Integer total = record.getInt("total");
        Integer hasJoinReviewers = record.getInt("has_join_reviewers");
        Integer hasEndReviewers = record.getInt("has_end_reviewers");
        String situation = record.getStr("situation");
        String res = record.getStr("result");
        //参 加 评 议 的 党 员
        if (Objects.equals(hasJoinReviewers, 1)) {
            setCellValue(record, "3", result, total,retireOrgList);
        }
        //结 束 评 议 的 党 员
        if (Objects.equals(hasEndReviewers, 1)) {
            setCellValue(record, "4", result, total,retireOrgList);
        }
        //优 秀 等 次 党 员
        if (StrUtil.equals(res, "7")) {
            setCellValue(record, "5", result, total,retireOrgList);
        }
        //被 评 定 为 “不 合 格 党 员”
        if (StrUtil.equals(res, "5")) {
            setCellValue(record, "6", result, total,retireOrgList);
        }
    }


    public static void setCellValue(Record record, String row, Map<String, Number> result, Integer addNum, HashSet<String> retireOrgList) {
        String d04Code = record.getStr("d04_code");

        // todo 2023-12-28  公有经济控制企业 与非公有经济控制企业 通过经济类型判断
        String d16Code = record.getStr("d16_code");
        Html53Count.setTableMapValue(row, "1", addNum, result);
        String isRetire = record.getStr("is_retire");
        Integer hasUnitStatistics = record.getInt("has_unit_statistics");
        Integer hasUnitProvince = record.getInt("has_unit_province");
        // 9 其他  20250114 (2024年统) 党员人事关系选择双否 无法填写d16_code经济类型  出数到其他
        boolean col9condi = Objects.equals(hasUnitStatistics, 0) && Objects.equals(hasUnitProvince, 0) &&
                (StrUtil.equalsAny(d04Code, "4111", "41111", "41112", "411121", "411122", "41113", "4112", "412", "413", "414", "415", "4151", "4152", "416") && StrUtil.isBlank(d16Code) ||
                        (StrUtil.equalsAny(d04Code, "4171", "4172", "4173", "4174", "418", "421", "422", "423", "424", "431", "432", "433", "434") && StrUtil.isBlank(d16Code)));

        //1、2行多加一个判断条件  只有1、2行是离退休的党组织出数到其他
        if (StrUtil.equalsAny(row,"1","2")){
            //2 行政村
            if (StrUtil.equals(d04Code, "923") && !StrUtil.equals(isRetire,"1")) {
                Html53Count.setTableMapValue(row, "2", addNum, result);
            } else
            //3 社区（居委会）
            if (StrUtil.equalsAny(d04Code, "921", "922") && !StrUtil.equals(isRetire,"1")) {
                Html53Count.setTableMapValue(row, "3", addNum, result);
            } else
            //4 机关
            if (StrUtil.equalsAny(d04Code, "11", "12", "120", "13", "131", "132", "133", "14", "15", "16", "17", "18", "19", "21", "22", "23", "29", "911", "9121", "9122")
                    && !StrUtil.equals(isRetire,"1")) {
                Html53Count.setTableMapValue(row, "4", addNum, result);
            } else
            //5 事业单位
            if (StrUtil.startWithAny(d04Code, "3") && !StrUtil.equals(isRetire,"1")) {
                Html53Count.setTableMapValue(row, "5", addNum, result);
            } else
            //6 公有经济控制企业
            if (StrUtil.equalsAny(d04Code, "4111", "41111", "41112", "411121", "411122", "41113", "4112", "412", "413", "414", "415", "4151", "4152", "416")
                    && StrUtil.startWithAny(d16Code, "1", "2")  && !StrUtil.equals(isRetire,"1")) {
                Html53Count.setTableMapValue(row, "6", addNum, result);
            } else
            //7 非公有经济控制企业
            if (StrUtil.equalsAny(d04Code, "4171", "4172", "4173", "4174", "418", "421", "422", "423", "424", "431", "432", "433", "434")
                    && StrUtil.startWithAny(d16Code, "3", "4", "5") && !StrUtil.equals(isRetire,"1")) {
                Html53Count.setTableMapValue(row, "7", addNum, result);
            } else
            //8 社会组织
            if (StrUtil.startWithAny(d04Code, "5") && !StrUtil.equals(isRetire,"1")) {
                Html53Count.setTableMapValue(row, "8", addNum, result);
            } else
            //9 其他
            if (StrUtil.equalsAny(d04Code, "61", "62", "93", "931", "932", "94", "95", "96", "97") || StrUtil.equals(isRetire, "1")) {
                Html53Count.setTableMapValue(row, "9", addNum, result);
            }
        }else {
            boolean b = retireOrgList.contains(record.getStr("org_code"));
            //2 行政村
            if (StrUtil.equals(d04Code, "923") && !b) {
                Html53Count.setTableMapValue(row, "2", addNum, result);
            }else
            //3 社区（居委会）
            if (StrUtil.equalsAny(d04Code, "921", "922") && !b) {
                Html53Count.setTableMapValue(row, "3", addNum, result);
            } else
            //4 机关
            if (StrUtil.equalsAny(d04Code, "11", "12", "120", "13", "131", "132", "133", "14", "15", "16", "17", "18", "19", "21", "22", "23", "29", "911", "9121", "9122") && !b) {
                Html53Count.setTableMapValue(row, "4", addNum, result);
            } else
            //5 事业单位
            if (StrUtil.startWithAny(d04Code, "3") && !b) {
                Html53Count.setTableMapValue(row, "5", addNum, result);
            } else
            //6 公有经济控制企业
            if (StrUtil.equalsAny(d04Code, "4111", "41111", "41112", "411121", "411122", "41113", "4112", "412", "413", "414", "415", "4151", "4152", "416")
                    && StrUtil.startWithAny(d16Code, "1", "2") && !b) {
                Html53Count.setTableMapValue(row, "6", addNum, result);
            } else
            //7 非公有经济控制企业
            if (StrUtil.equalsAny(d04Code, "4171", "4172", "4173", "4174", "418", "421", "422", "423", "424", "431", "432", "433", "434")
                    && StrUtil.startWithAny(d16Code, "3", "4", "5") && !b) {
                Html53Count.setTableMapValue(row, "7", addNum, result);
            } else
            //8 社会组织
            if (StrUtil.startWithAny(d04Code, "5") && !b) {
                Html53Count.setTableMapValue(row, "8", addNum, result);
            } else
            //9 其他  (2024年统) 党员人事关系选择双否 无法填写d16_code经济类型  出数到其他
            if (StrUtil.equalsAny(d04Code, "61", "62", "93", "931", "932", "94", "95", "96", "97") || b || col9condi) {
                Html53Count.setTableMapValue(row, "9", addNum, result);
            }
        }
    }

    /**
     * 反查条件
     *
     * @param colIndex      序号
     * @param retireOrgList
     * @return 查询条件
     */
    public Condition getColCondition(String colIndex, String rowIndex, List<String> retireOrgList) {
        Condition condition = noCondition();
        //1、2行多加一个判断条件  只有1、2行是离退休的党组织出数到其他
        if (StrUtil.equalsAny(rowIndex, "1", "2")) {
            if (StrUtil.equals(colIndex, "1")) {
            } else if (StrUtil.equals(colIndex, "2")) {
                condition = condition.and("d04_code = '923'").and("is_retire != 1");
            } else if (StrUtil.equals(colIndex, "3")) {
                condition = condition.and("d04_code in ('921','922') ").and("is_retire != 1");
            } else if (StrUtil.equals(colIndex, "4")) {
                condition = condition.and(field(name("d04_code")).in("11", "12", "120", "13", "131", "132", "133", "14", "15", "16", "17", "18", "19", "21", "22", "23", "29", "911", "9121", "9122").and("is_retire != 1"));
            } else if (StrUtil.equals(colIndex, "5")) {
                condition = condition.and("d04_code like '3%'").and("is_retire != 1");
            } else if (StrUtil.equals(colIndex, "6")) {
                condition = condition.and(field(name("d04_code")).in("4111", "41111", "41112", "411121", "411122", "41113", "4112", "412", "413", "414", "415", "4151", "4152", "416").and("(d16_code like '1%' or d16_code like '2%')").and("is_retire != 1"));
            } else if (StrUtil.equals(colIndex, "7")) {
                condition = condition.and(field(name("d04_code")).in("4171", "4172", "4173", "4174", "418", "421", "422", "423", "424", "431", "432", "433", "434").and("(d16_code like '3%' or d16_code like '4%' or d16_code like '5%')").and("is_retire != 1"));
            } else if (StrUtil.equals(colIndex, "8")) {
                condition = condition.and("d04_code like '5%'").and("is_retire != 1");
            } else if (StrUtil.equals(colIndex, "9")) {
                condition = condition.and(field(name("d04_code")).in("61", "62", "93", "931", "932", "94", "95", "96", "97").or("is_retire = 1"));
            }
        } else {
            String col9cond = "(has_unit_statistics = 0 and has_unit_province = 0 and " +
                    "((d04_code in('4111', '41111', '41112', '411121', '411122', '41113', '4112', '412', '413', '414', '415', '4151', '4152', '416') and d16_code is NULL) OR " +
                    "(d04_code in('4171', '4172', '4173', '4174', '418', '421', '422', '423', '424', '431', '432', '433', '434') AND d16_code is NULL)))";
            //离退休党组值不为空
            if (CollUtil.isNotEmpty(retireOrgList)) {
                Condition cond = field(name("org_code"), String.class).notIn(retireOrgList);
                Condition incond = field(name("org_code"), String.class).in(retireOrgList);
                if (StrUtil.equals(colIndex, "1")) {
                } else if (StrUtil.equals(colIndex, "2")) {
                    condition = condition.and("d04_code = '923'").and(cond);
                } else if (StrUtil.equals(colIndex, "3")) {
                    condition = condition.and("d04_code in ('921','922')").and(cond);
                } else if (StrUtil.equals(colIndex, "4")) {
                    condition = condition.and(field(name("d04_code")).in("11", "12", "120", "13", "131", "132", "133", "14", "15", "16", "17", "18", "19", "21", "22", "23", "29", "911", "9121", "9122")).and(cond);
                } else if (StrUtil.equals(colIndex, "5")) {
                    condition = condition.and("d04_code like '3%'").and(cond);
                } else if (StrUtil.equals(colIndex, "6")) {
                    condition = condition.and(field(name("d04_code")).in("4111", "41111", "41112", "411121", "411122", "41113", "4112", "412", "413", "414", "415", "4151", "4152", "416").and("(d16_code like '1%' or d16_code like '2%')").and(cond));
                } else if (StrUtil.equals(colIndex, "7")) {
                    condition = condition.and(field(name("d04_code")).in("4171", "4172", "4173", "4174", "418", "421", "422", "423", "424", "431", "432", "433", "434").and("(d16_code like '3%' or d16_code like '4%' or d16_code like '5%')")).and(cond);
                } else if (StrUtil.equals(colIndex, "8")) {
                    condition = condition.and("d04_code like '5%'").and(cond);
                } else if (StrUtil.equals(colIndex, "9")) {
                    condition = condition.and(field(name("d04_code")).in("61", "62", "93", "931", "932", "94", "95", "96", "97")).or(incond).or(col9cond);
                }
            } else {
                if (StrUtil.equals(colIndex, "1")) {
                } else if (StrUtil.equals(colIndex, "2")) {
                    condition = condition.and("d04_code = '923'");
                } else if (StrUtil.equals(colIndex, "3")) {
                    condition = condition.and("d04_code in ('921','922')");
                } else if (StrUtil.equals(colIndex, "4")) {
                    condition = condition.and(field(name("d04_code")).in("11", "12", "120", "13", "131", "132", "133", "14", "15", "16", "17", "18", "19", "21", "22", "23", "29", "911", "9121", "9122"));
                } else if (StrUtil.equals(colIndex, "5")) {
                    condition = condition.and("d04_code like '3%'");
                } else if (StrUtil.equals(colIndex, "6")) {
                    condition = condition.and(field(name("d04_code")).in("4111", "41111", "41112", "411121", "411122", "41113", "4112", "412", "413", "414", "415", "4151", "4152", "416").and("(d16_code like '1%' or d16_code like '2%')"));
                } else if (StrUtil.equals(colIndex, "7")) {
                    condition = condition.and(field(name("d04_code")).in("4171", "4172", "4173", "4174", "418", "421", "422", "423", "424", "431", "432", "433", "434").and("(d16_code like '3%' or d16_code like '4%' or d16_code like '5%')"));
                } else if (StrUtil.equals(colIndex, "8")) {
                    condition = condition.and("d04_code like '5%'");
                } else if (StrUtil.equals(colIndex, "9")) {
                    condition = condition.and(field(name("d04_code")).in("61", "62", "93", "931", "932", "94", "95", "96", "97").or(col9cond));
                }
            }
        }


        return condition;
    }

    public Condition getRowCondition(String rowIndex) {
        Condition condition = noCondition();
        if (StrUtil.equals(rowIndex, "3")) {
            condition = condition.and("has_join_reviewers=1");
        } else if (StrUtil.equals(rowIndex, "4")) {
            condition = condition.and("has_end_reviewers=1");
        }
        if (StrUtil.equals(rowIndex, "5")) {
            condition = condition.and("result='7'");
        } else if (StrUtil.equals(rowIndex, "6")) {
            condition = condition.and("result='5'");
        }

        if (StrUtil.equals(rowIndex, "7")) {
            condition = condition.and("result='5'").and(field(name("situation")).in("C31", "C34", "C35", "C37"));
        } else if (StrUtil.equals(rowIndex, "8")) {
            condition = condition.and("result='5' and situation='C31' ");
        } else if (StrUtil.equals(rowIndex, "9")) {
            condition = condition.and("result='5' and situation='C34' ");
        } else if (StrUtil.equals(rowIndex, "10")) {
            condition = condition.and("result='5' and situation='C35' ");
        } else if (StrUtil.equals(rowIndex, "11")) {
            condition = condition.and("result='5' and situation='C37' ");
        } else if (StrUtil.equals(rowIndex, "12")) {
            condition = condition.and("result='5' and situation in ('C38','C35') ");
        }


        return condition;
    }


    @Override
    public Map<String, Number> doReplenishCount(String reportCode, String orgCode, String orgLevelCode) throws Exception {
        return null;
    }

    @Override
    public Map<String, Object> doReplenishPegging(PeggingPara peggingPara) throws Exception {
        return null;
    }
}
