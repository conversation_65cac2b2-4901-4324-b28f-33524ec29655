package com.zenith.front.core.analysis.count.history;

import cn.hutool.core.util.StrUtil;
import com.b1809.analysis.entity.PeggingPara;
import com.b1809.analysis.extend.ITableCount;
import com.b1809.analysis.result.ReportResult;
import com.b1809.analysis.table.helper.TableHelper;
import com.b1809.analysis.table.helper.factory.TableHelperFactory;
import com.jfinal.plugin.activerecord.Page;
import com.zenith.front.core.analysis.ext.condition.year2022.OrgAllCondition20220630;
import com.zenith.front.core.analysis.ext.condition.year2022.OrgPartyCondition20220630;
import com.zenith.front.core.analysis.ext.condition.year2022.UnitAllCondition20220630;
import org.jooq.Condition;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;

import static org.jooq.impl.DSL.noCondition;

/**
 * 第二十四表 党的基层组织数量情况和换届情况
 *
 * <AUTHOR>
 * @date 2022/10/21
 */
@Component
public class Html24Count20220630 extends Html24CountHistory implements ITableCount {

    private static final String TABLE_YEAR = "20220630";

    @Override
    public String getReportCode() {
        return "20220630_24.html";
    }

    @Override
    public Map<String, Map<String, Number>> doCount(String reportCode, String orgCode, String orgLevelCode) throws Exception {
        return null;
    }

    @Override
    public Map<String, Object> doPegging(PeggingPara peggingPara) throws Exception {
        return null;
    }

    @Override
    public Map<String, Number> doReplenishCount(String reportCode, String orgCode, String orgLevelCode) throws Exception {
        return getReplenishCount(orgCode, orgLevelCode, TABLE_YEAR);
    }


    @Override
    public Map<String, Object> doReplenishPegging(PeggingPara peggingPara) {
        String colIndex = peggingPara.getRowIndex();
        if (StrUtil.equalsAny(colIndex, "1", "2", "3", "4", "5")) {
            OrgPartyCondition20220630 partyCond = new OrgPartyCondition20220630();
            Condition condition = partyCond.create(peggingPara.getOrgCode(), peggingPara.getOrgLevelCode()).and(this.getPartyRowCondition(peggingPara.getRowIndex()));
            return Html48CountHistory.getReportPageResult(peggingPara, partyCond.getTableName(), condition, partyCond.getLevelCodeField());
        } else if (StrUtil.equalsAny(colIndex, "18", "23", "28")) {
            UnitAllCondition20220630 unitAllCond = new UnitAllCondition20220630();
            Condition condition = this.getUnitRowCondition(colIndex, TABLE_YEAR).and(new Html6CountHistory().getUnitListCondition(peggingPara.getOrgCode(), peggingPara.getOrgLevelCode()).toString().replace("ccp_unit_all", unitAllCond.getTableName()));
            return Html48CountHistory.getReportPageResult(peggingPara, unitAllCond.getTableName(), condition, unitAllCond.getLevelCodeField());
        } else {
            OrgAllCondition20220630 orgAllCond = new OrgAllCondition20220630();
            Condition condition = noCondition().and(this.getOrgRowCondition(peggingPara.getRowIndex(), peggingPara.getOrgCode(), peggingPara.getOrgLevelCode(), TABLE_YEAR).toString().replace("ccp_org_all", orgAllCond.getTableName()));
            return Html48CountHistory.getReportPageResult(peggingPara, orgAllCond.getTableName(), condition, orgAllCond.getLevelCodeField());
        }
    }


    public Map<String, Object> getCheckHtml24_20220630(PeggingPara data) {
        Map<String, Object> resultMap = new HashMap<>();
        TableHelper tableHelper = TableHelperFactory.getHelper();
        OrgAllCondition20220630 orgAllCond = new OrgAllCondition20220630();
        Condition condition = noCondition().and(this.getOrgAllCheckListCondition(data.getOrgCode(), data.getOrgLevelCode(), data.getColIndex(), data.getRowIndex()).toString().replace("ccp_org_all", orgAllCond.getTableName()));
        Page<Map<String, Object>> page = tableHelper.findPage(data.getPageNum(), data.getPageSize(), orgAllCond.getTableName(),
                condition, data.getIncludeFieldList(), data.getOrderByFieldList());
        return ReportResult.toResult(resultMap, page, orgAllCond.getTableName());
    }

}
