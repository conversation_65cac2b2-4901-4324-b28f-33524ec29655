package com.zenith.front.core.sync.operator;

import com.zenith.front.core.cache.CacheUtils;
import com.zenith.front.core.sync.AbstractOperator;
import org.redisson.api.RAtomicLong;

import javax.annotation.Resource;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2019/6/44:58 PM
 * 根据组织id,进行累加
 * 无任何规则,简单的加一或减一
 */
public class DefaultOperator extends AbstractOperator {

    @Resource
    private CacheUtils cacheUtils;

    @Override
    public void add(String redisKey, String orgCodeKey, Map<String, Object> value) {
        RAtomicLong atomicLong = cacheUtils.redissonClient.getAtomicLong(redisKey);
        //自增结果
        long result = atomicLong.incrementAndGet();
        Object o = value.get(orgCodeKey);
    }

    @Override
    public void sub(String redisKey, String orgCodeKey, Map<String, Object> value) {

    }
}
