package com.zenith.front.core.service.develop;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.zenith.front.api.develop.IDevelopPlanLogService;
import com.zenith.front.common.constant.CommonConstant;
import com.zenith.front.common.kit.StrKit;
import com.zenith.front.dao.mapper.develop.DevelopPlanLogMapper;
import com.zenith.front.model.bean.DevelopPlanLog;
import org.springframework.stereotype.Service;

import java.util.Date;

import static com.zenith.front.model.constant.UserConstant.USER_CONTEXT;

/**
 * <p>
 * 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-07-10
 */
@Service
public class DevelopPlanLogServiceImpl extends ServiceImpl<DevelopPlanLogMapper, DevelopPlanLog> implements IDevelopPlanLogService {


    @Override
    public boolean saveDevelopPlanLog(DevelopPlanLog developPlanLog) {
        developPlanLog.setCode(StrKit.getRandomUUID());
        developPlanLog.setCreateTime(new Date());
        developPlanLog.setUpdateTime(new Date());
        developPlanLog.setTimestamp(new Date());
        developPlanLog.setUpdateAccount(USER_CONTEXT.get().getUser().getAccount());
        developPlanLog.setIsHistory(CommonConstant.ZERO_INT);
        developPlanLog.setId(null);
        return save(developPlanLog);
    }
}
