package com.zenith.front.core.analysis.count.year2024;

import cn.hutool.core.util.StrUtil;
import com.b1809.analysis.entity.PeggingPara;
import com.zenith.front.core.analysis.ext.condition.year2024.MemAllCondition2024;
import com.zenith.front.core.analysis.ext.condition.year2024.OrgAllCondition2024;
import com.zenith.front.core.analysis.ext.condition.year2024.UnitAllCondition2024;
import org.jooq.Condition;

import java.util.HashMap;
import java.util.Map;

import static org.jooq.impl.DSL.noCondition;

/**
 * 非公有经济控制的企业法人单位建立党的基层组织情况
 *
 */
public class Html30Count2024 {


    public Condition getMemListCondition(String orgCode, String orgLevelCode) {
//        return noCondition().and("d09_code is not null and d09_code <> '' and d09_code like '0%' and delete_time is null and d08_code in('1','2')  and (is_transfer !=1 or is_transfer is null) and \"d04_code\" in (\n" +
//                "    '417', '4171', '4172', '4173', '4174', '418', '42', '421', '422', '423', '424', \n" +
//                "    '43', '431', '432', '433', '434'\n" +
//                "  )").and(new MemAllCondition2024().create(orgCode, orgLevelCode));
        return noCondition().and("d09_code is not null and d09_code <> '' and d09_code like '0%' and delete_time is null and d08_code in('1','2')  and (is_transfer !=1 or is_transfer is null) and " +
                "d04_code like '4%' and (d16_code like '3%' or d16_code like '4%' or d16_code like '5%') and is_legal=1").and(new MemAllCondition2024().create(orgCode, orgLevelCode));
    }

    public Map<String, Object> getCheckHtml27(PeggingPara data) {
        Map<String, Object> resultMap = new HashMap<>();
        String rowIndex = data.getRowIndex();
        if (StrUtil.equals(rowIndex, "8")) {
            Condition condition = this.getOrg27Condition(data.getOrgCode(), data.getOrgLevelCode()).and(this.getOrgCheck27Condition(data.getColIndex()));
            OrgAllCondition2024 orgAllCond = new OrgAllCondition2024();
            return Html53Count2024.getReportPageResult(data, orgAllCond.getTableName(), condition, orgAllCond.getLevelCodeField());
        }
        //12已派党建工作指导员的	13建立工会或共青团组织的
        if (StrUtil.equalsAny(rowIndex, "12", "13")) {
            Condition condition = this.getUnit27Condition(data.getOrgCode(), data.getOrgLevelCode()).and(this.getOrgCheck27Condition(data.getColIndex()))
                    .and(StrUtil.equals(rowIndex, "12") ? "has_instructor_contact=1" : "(has_labour_union=1 or has_youth_league=1)");
            UnitAllCondition2024 unitAllCondition = new UnitAllCondition2024();
            return Html53Count2024.getReportPageResult(data, unitAllCondition.getTableName(), condition, unitAllCondition.getLevelCodeField());
        }
        if (StrUtil.equals(rowIndex, "18")) {
            Condition condition = getMemListCondition(data.getOrgCode(), data.getOrgLevelCode()).and(getMemCheckListCondition(data.getColIndex()));
            MemAllCondition2024 memAllCond = new MemAllCondition2024();
            return Html53Count2024.getReportPageResult(data, memAllCond.getTableName(), condition, memAllCond.getLevelCodeField());
        }
        return resultMap;
    }

    public Condition getOrg27Condition(String orgCode, String orgLevelCode) {
        return new OrgAllCondition2024().create(orgCode, orgLevelCode).and("delete_time is null and (is_dissolve is null or is_dissolve !=1) and d01_code in('632','634','932') " +
                "and d04_code like '4%' and (d16_code like '3%' or d16_code like '4%' or d16_code like '5%') and has_unit_own_level='1'");
//        return new OrgAllCondition2024().create(orgCode, orgLevelCode).and("delete_time is null and (is_dissolve is null or is_dissolve !=1) and d01_code in('632','634','932') " +
//                "and (d04_code in ('417','4171','4172','4173','4174','418','42','421','422', '423','424','43','431','432','433','434'))");
    }

    public Condition getUnit27Condition(String orgCode, String orgLevelCode) {
        return new Html7Count2024().getUnitListCondition(orgCode, orgLevelCode).and("d04_code like '4%' and (d16_code like '3%' or d16_code like '4%' or d16_code like '5%') and d05_code like '4%'");
    }

    Condition getOrgCheck27Condition(String colIndex) {
        Condition condition = noCondition();
        if (StrUtil.equals(colIndex, "1")) {
        } else if (StrUtil.equals(colIndex, "2")) {
            condition = condition.and("on_post_num >= 50");
        } else if (StrUtil.equals(colIndex, "3")) {
            condition = condition.and("on_post_num >= 100");
        } else {
            return getCheckListCond(colIndex);
        }
        return condition;
    }

    Condition getCheckListCond(String colIndex) {
        Condition condition = noCondition();
        if (StrUtil.equals(colIndex, "4")) {
            condition = condition.and("has_representative=1");
        } else if (StrUtil.equals(colIndex, "5")) {
            condition = condition.and("has_proper_secretary=1");
        } else if (StrUtil.equals(colIndex, "6")) {
            condition = condition.and("d16_code like '3%'");
        } else if (StrUtil.equals(colIndex, "7")) {
            condition = condition.and("d16_code like '31%'");
        } else if (StrUtil.equals(colIndex, "8")) {
            condition = condition.and("d16_code like '32%'");
        } else if (StrUtil.equals(colIndex, "9")) {
            condition = condition.and("d16_code like '33%'");
        } else if (StrUtil.equals(colIndex, "10")) {
            condition = condition.and("d16_code like '4%'");
        } else if (StrUtil.equals(colIndex, "11")) {
            condition = condition.and("d16_code='41'");
        } else if (StrUtil.equals(colIndex, "12")) {
            condition = condition.and("d16_code like '5%'");
        } else if (StrUtil.equals(colIndex, "13")) {
            condition = condition.and("d16_code='51'");
        }
        return condition;
    }

    Condition getMemCheckListCondition(String colIndex) {
        Condition condition = noCondition();
        if ("1".equals(colIndex)) {
            return condition;
        }
        if ("2".equals(colIndex)) {
            return condition.and("has_more_than50=1");
        }
        if ("3".equals(colIndex)) {
            return condition.and("has_more_than100=1");
        }
        return condition.and(getCheckListCond(colIndex));
    }

}
