package com.zenith.front.core.analysis.count.year2024;

import cn.hutool.core.util.StrUtil;
import com.b1809.analysis.entity.PeggingPara;
import com.b1809.analysis.extend.ITableCount;
import com.b1809.analysis.result.ReportResult;
import com.b1809.analysis.table.helper.TableHelper;
import com.b1809.analysis.table.helper.factory.TableHelperFactory;
import com.jfinal.plugin.activerecord.Page;
import com.zenith.front.common.constant.CommonConstant;
import com.zenith.front.common.kit.StrKit;
import com.zenith.front.core.analysis.ext.condition.year2024.MemAllCondition2024;
import com.zenith.front.core.analysis.ext.condition.year2024.UnitAllCondition2024;
import org.jooq.Condition;
import org.jooq.Field;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;

import static org.jooq.impl.DSL.*;

/**
 * 第六表 街道、社区（居委会）党员情况（乡镇社区作为村统计）
 *
 * <AUTHOR>
 * @date 2021/11/16
 */
@Component
public class Html7CountBackups2024 extends Html7Count2024 implements ITableCount {


    @Override
    public String getReportCode() {
        return "2024_7_csq.html";
    }

    @Override
    public Map<String, Map<String, Number>> doCount(String reportCode, String orgCode, String orgLevelCode) throws Exception {
        return null;
    }

    @Override
    public Map<String, Object> doPegging(PeggingPara peggingPara) throws Exception {
        return null;
    }

    @Override
    public Map<String, Number> doReplenishCount(String reportCode, String orgCode, String orgLevelCode) throws Exception {
        //TODO 统计社区的地方，改成只统计城市社区
        return getReplenishCount(orgCode, orgLevelCode, Html1Count2024.TABLE_YEAR, true);
    }

    @Override
    public Map<String, Object> doReplenishPegging(PeggingPara peggingPara) throws Exception {
        if (StrUtil.equalsAny(peggingPara.getRowIndex(), "3", "4", "5", "6", "7", "8", "9", "10", "11")) {
            Condition condition = getUnitListCondition(peggingPara.getOrgCode(), peggingPara.getOrgLevelCode())
                    .and(field(name("d04_code"), String.class).eq("911")).and(getRowUnitCondition(peggingPara.getRowIndex()));
            UnitAllCondition2024 unitAllCondition = new UnitAllCondition2024();
            return getReportPageResult(peggingPara, unitAllCondition.getTableName(), condition, unitAllCondition.getLevelCodeField());
        }
        Condition condition = getRowCondition(peggingPara.getOrgCode(), peggingPara.getOrgLevelCode(), peggingPara.getRowIndex(), true);
        MemAllCondition2024 memAllCondition = new MemAllCondition2024();
        return getReportPageResult(peggingPara, memAllCondition.getTableName(), condition, memAllCondition.getLevelCodeField());
    }

    public Condition getUnitListCondition(String orgCode, String orgLevelCode) {
        return noCondition().and("delete_time is null and is_legal=1 ").and(new UnitAllCondition2024().create(orgCode, orgLevelCode));
    }

    public Condition getMemListCondition(String orgCode, String orgLevelCode) {
        return noCondition().and("delete_time is null and d08_code in ('1','2')").and(new MemAllCondition2024().create(orgCode, orgLevelCode));
    }

    /**
     * 获取反查列表
     *
     * @param peggingPara 反查参数
     * @param tableName   数据表名
     * @param condition   查询条件
     * @return 反查列表
     */
    public Map<String, Object> getReportPageResult(PeggingPara peggingPara, String tableName, Condition condition, Field<?> levelCodeField) {
        Map<String, Object> resultMap = new HashMap<>();
        TableHelper tableHelper = TableHelperFactory.getHelper();
        String peggingType = peggingPara.getPeggingType();
        if (StrKit.notBlank(peggingType) && peggingType.equals(CommonConstant.TWO)) {
            Page<Map<String, Object>> page = tableHelper.findGroupPage(peggingPara.getPageNum(), peggingPara.getPageSize(), tableName, condition,
                    peggingPara.getOrderByFieldList(), levelCodeField);
            return ReportResult.toResult(resultMap, page, tableName);
        }
        Page<Map<String, Object>> page = tableHelper.findPage(peggingPara.getPageNum(), peggingPara.getPageSize(), tableName, condition,
                peggingPara.getIncludeFieldList(), peggingPara.getOrderByFieldList());

        return ReportResult.toResult(resultMap, page, tableName);
    }
}
