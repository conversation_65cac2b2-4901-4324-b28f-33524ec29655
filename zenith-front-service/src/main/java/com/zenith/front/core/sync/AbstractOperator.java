package com.zenith.front.core.sync;

import java.util.Map;

/**
 * <AUTHOR>
 * @date 2019/6/44:54 PM
 */
public abstract class AbstractOperator {


    /***
     * 维度key增加
     * @param redisKey redis key
     * @param orgCodeKey orgCode列名
     * @param value 变化的值
     * **/
    public abstract void add(String redisKey,String orgCodeKey,Map<String,Object> value);
    /***
     * 维度key减少
     * @param redisKey redis key
     * @param orgCodeKey orgCode列名
     * @param value 变化的值
     * **/
    public abstract void sub(String redisKey,String orgCodeKey,Map<String,Object> value);
}
