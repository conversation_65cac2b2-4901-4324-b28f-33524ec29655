package com.zenith.front.core.service.codetable;

import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.zenith.front.api.codetable.ICodeTableColService;
import com.zenith.front.api.codetable.ICodeTableService;
import com.zenith.front.dao.mapper.codetable.CodeTableColMapper;
import com.zenith.front.dao.mapper.codetable.CodeTableMapper;
import com.zenith.front.model.bean.CodeTable;
import com.zenith.front.model.bean.CodeTableCol;
import com.zenith.front.model.message.OutMessage;
import com.zenith.front.model.message.Status;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <p>
 * 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-07-10
 */
@Service
public class CodeTableServiceImpl extends ServiceImpl<CodeTableMapper, CodeTable> implements ICodeTableService {

    @Resource
    private ICodeTableColService codeTableColService;
    @Resource
    private CodeTableColMapper codeTableColMapper;

    @Override
    public OutMessage findTableConfig() {
        List<CodeTable> allTable = findAllTable();

        return new OutMessage<>(Status.SUCCESS, allTable);
    }

    private List<CodeTable> findAllTable() {
        LambdaQueryWrapper<CodeTable> lambdaQueryWrapper = new LambdaQueryWrapper<CodeTable>()
                .select(CodeTable::getId, CodeTable::getTableName, CodeTable::getTableCode);
        return list(lambdaQueryWrapper);
    }

    @Override
    public OutMessage tableFind(String id) {
        //根据id获取tableCode
        CodeTable tableById = findTableById(id);
        if (ObjectUtil.isNull(tableById)) {
            return new OutMessage(Status.FAIL);
        }

        String tableCode = tableById.getTableCode();

        //查询所有表得列字段
        List<CodeTableCol> allTableColByCode = codeTableColService.findAllTableColByCode(tableCode);

        return new OutMessage<>(Status.SUCCESS, allTableColByCode);
    }

    private CodeTable findTableById(String id) {
        LambdaQueryWrapper<CodeTable> lambdaQueryWrapper = new LambdaQueryWrapper<CodeTable>()
                .eq(CodeTable::getId, id)
                .eq(CodeTable::getIsUse, 1)
                .select(CodeTable::getId, CodeTable::getTableCode);
        return getOne(lambdaQueryWrapper);
    }


    @Override
    public OutMessage<?> findTableConfigNew() {
        List<CodeTable> allTable = codeTableColMapper.findTableConfigNew();

        return new OutMessage<>(Status.SUCCESS, allTable);
    }

    public OutMessage<?> tableAllNew() {
        //获取所有表得字段提示项目
        Map<String, List<CodeTableCol>> returnMap = new HashMap<>(50);
        List<CodeTable> tableAll = codeTableColMapper.findTableConfigNew();

        tableAll.forEach(codeTable -> {
            String tableCode = codeTable.getTableCode();
            String tableName = codeTable.getTableName();
            //查询所有表得列字段
            List<CodeTableCol> allTableColByCode = codeTableColMapper.findAllTableColByCode(tableCode);
            returnMap.put(tableName, allTableColByCode);
        });
        return new OutMessage<>(Status.SUCCESS, returnMap);
    }

}
