package com.zenith.front.core.analysis.count.year2023;

import com.b1809.analysis.entity.PeggingPara;
import com.b1809.analysis.extend.ITableCount;
import com.zenith.front.core.analysis.ext.condition.year2023.OrgSlackAllCondition2023;
import org.jooq.Condition;
import org.springframework.stereotype.Component;

import java.util.Map;

/**
 * 专题调查表七 整顿软弱涣散基层党组织情况
 *
 */
@Component
public class Html52CountBackups2023 extends Html52Count2023 implements ITableCount {

    @Override
    public String getReportCode() {
        return "2023_52_csq.html";
    }

    @Override
    public Map<String, Map<String, Number>> doCount(String reportCode, String orgCode, String orgLevelCode) throws Exception {
        return null;
    }

    @Override
    public Map<String, Object> doPegging(PeggingPara peggingPara) throws Exception {
        return null;
    }

    @Override
    public Map<String, Number> doReplenishCount(String reportCode, String orgCode, String orgLevelCode) throws Exception {
        return getReplenishCount(orgCode, orgLevelCode, "2023");
    }


    @Override
    public Map<String, Object> doReplenishPegging(PeggingPara peggingPara) throws Exception {
        Condition condition = this.getListCondition(peggingPara.getOrgCode(), peggingPara.getOrgLevelCode(), null).and(this.getRowCondition(peggingPara.getRowIndex()));

        OrgSlackAllCondition2023 orgSlackAllCondition = new OrgSlackAllCondition2023();
        return getReportPageResult(peggingPara, orgSlackAllCondition.getTableName(), condition, orgSlackAllCondition.getLevelCodeField());
    }


}
