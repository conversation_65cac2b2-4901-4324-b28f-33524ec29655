package com.zenith.front.core.sync.filter;

import com.zenith.front.core.sync.AbstractCountFilter;

/**
 * <AUTHOR>
 * @date 2019/6/211:36 PM
 */
public class IntIsFilter extends AbstractCountFilter<Integer> {

    @Override
    public boolean check(Integer integer) {
        if(integer == null){
            return false;
        }
        if(getValue() == null){
            return false;
        }
        return integer.equals(getValue());
    }

    @Override
    public String getName() {
        return "intIs";
    }

    @Override
    protected Integer convert(String value) {
        return Integer.valueOf(value);
    }
}
