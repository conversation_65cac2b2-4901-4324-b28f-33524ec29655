package com.zenith.front.core.job;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DatePattern;
import com.xxl.job.core.handler.annotation.XxlJob;
import com.zenith.front.api.mem.IMemService;
import com.zenith.front.common.kit.DateUtil;
import com.zenith.front.common.kit.StrKit;
import com.zenith.front.model.bean.MqMessage;
import com.zenith.front.model.vo.ProbationaryPartyMemberBecomesRegularMessageVO;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.text.MessageFormat;
import java.time.LocalDate;
import java.time.Period;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * 预备党员转正提醒
 * 到期前7天提示语：预备党员XXX按期转正时间为XXX年XX月XX日，请及时讨论。
 * 到期后提示语：预备党员XXX按期转正时间为XXX年XX月XX日，请及时讨论。
 * 每1天提醒一次，按期转正或延长预备期后即不再提醒。延长预备期的在延长预备期满后依然提醒
 *
 * <AUTHOR>
 * @version V1.0
 * @date 2022/7/28 14:22
 */
@Component
public class ProbationaryPartyMemberBecomesRegular extends BaseMqMessageService {

    private static final String NORMAL_MESSAGE = "预备党员{0}按期转正时间为{1}，请及时讨论。";
    private static final String EXPIRE_MESSAGE = "预备党员{0}按期转正时间为{1}，请及时讨论。";
    private static final String TYPE = "13";

    @Resource
    private IMemService memService;

    @XxlJob("probationaryPartyMemberBecomesRegular")
    public void probationaryPartyMemberBecomesRegular() {
        List<ProbationaryPartyMemberBecomesRegularMessageVO> memList = memService.findProbationaryPartyMember();
        if (CollUtil.isNotEmpty(memList)) {
            List<MqMessage> mqMessageList = new ArrayList<>();

            for (ProbationaryPartyMemberBecomesRegularMessageVO mem : memList) {
                Date extendPreparDate = mem.getExtendPreparDate();
                String name = mem.getName();
                String messageTemplate = Period.between(
                        DateUtil.dateToLocaleDate(extendPreparDate), LocalDate.now().minusYears(1).plusDays(7)
                ).getDays() < 7 ? NORMAL_MESSAGE : EXPIRE_MESSAGE;
                String messageFormat = MessageFormat.format(
                        messageTemplate,
                        name
                        , cn.hutool.core.date.DateUtil.format(extendPreparDate, DatePattern.CHINESE_DATE_FORMAT)
                );
                MqMessage message = new MqMessage();
                message.setCode(StrKit.getRandomUUID());
                message.setType(TYPE);
                message.setMessage(messageFormat);
                message.setOrgCode(mem.getMemOrgCode());
                message.setCreateTime(new Date());
                message.setSortTime(extendPreparDate);
                mqMessageList.add(message);
            }
            //按类型删除
            mqMessageService.deleteByType(TYPE);
            //批量保存
            mqMessageService.saveBatch(mqMessageList);
        }
    }
}
