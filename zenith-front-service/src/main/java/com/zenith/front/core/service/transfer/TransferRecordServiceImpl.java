package com.zenith.front.core.service.transfer;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateField;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.extra.pinyin.PinyinUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.conditions.update.LambdaUpdateChainWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.zenith.front.api.democraticreview.IDemocraticReviewMemService;
import com.zenith.front.api.develop.IDevelopStepLogAllService;
import com.zenith.front.api.develop.IDevelopStepLogService;
import com.zenith.front.api.dict.IDictService;
import com.zenith.front.api.mem.*;
import com.zenith.front.api.org.*;
import com.zenith.front.api.sync.ISyncMemService;
import com.zenith.front.api.transfer.*;
import com.zenith.front.api.unit.*;
import com.zenith.front.api.user.IUserRolePermissionService;
import com.zenith.front.api.user.IUserService;
import com.zenith.front.common.constant.*;
import com.zenith.front.common.encrypt.properties.EncryptProperties;
import com.zenith.front.common.kit.*;
import com.zenith.front.common.untils.SM4Untils;
import com.zenith.front.core.cache.CacheUtils;
import com.zenith.front.core.kit.CollectionUtil;
import com.zenith.front.dao.mapper.develop.DevelopStepLogAllMapper;
import com.zenith.front.dao.mapper.develop.DevelopStepLogMapper;
import com.zenith.front.dao.mapper.mem.*;
import com.zenith.front.dao.mapper.org.*;
import com.zenith.front.dao.mapper.transfer.TransferHintMapper;
import com.zenith.front.dao.mapper.transfer.TransferRecordMapper;
import com.zenith.front.dao.mapper.unit.*;
import com.zenith.front.dao.mapper.user.UserMapper;
import com.zenith.front.dao.mapper.user.UserRolePermissionMapper;
import com.zenith.front.model.bean.*;
import com.zenith.front.model.constant.UserConstant;
import com.zenith.front.model.custom.Record;
import com.zenith.front.model.custom.UserTicket;
import com.zenith.front.model.dto.*;
import com.zenith.front.model.message.OutMessage;
import com.zenith.front.model.message.Status;
import com.zenith.front.model.modelview.TransFerSrcView;
import com.zenith.front.model.modelview.TransFerTarView;
import com.zenith.front.model.vo.*;
import org.redisson.api.RBucket;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.ResourceUtils;

import javax.annotation.Resource;
import java.io.File;
import java.io.FileInputStream;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.Executor;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * <p>
 * 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-07-10
 */
@Service("transferRecordService")
public class TransferRecordServiceImpl extends ServiceImpl<TransferRecordMapper, TransferRecord> implements ITransferRecordService {

    private static final Integer PAGE_SIZE = 10;

    private static final Pattern ORG_REGEX = Pattern.compile("(\\d{3})");

    private static final Logger log = LoggerFactory.getLogger(TransferRecordServiceImpl.class);
    @Resource
    private ISyncMemService iSyncMemService;
    @Resource
    private TransferRecordMapper recordMapper;
    @Resource
    private OrgMapper orgMapper;
    @Resource
    protected IOrgService orgService;
    @Resource
    protected ITransferApprovalService transferApprovalService;
    @Resource
    protected ITransferLogService transferLogService;
    @Resource
    protected IMemService memService;
    @Resource
    protected IUserService userService;
    @Resource
    protected IOrgAllService orgAllService;
    @Resource
    private Executor mySimpleAsync;
    @Resource
    protected IUserRolePermissionService userRolePermissionService;
    @Resource
    private IUnitService iUnitService;
    @Resource
    private IUnitAllService iUnitAllService;
    @Resource
    private ITransfrtSrcViewService iTransfrtSrcViewService;
    @Resource
    private ITransfertarViewService iTransfertarViewService;

    @Resource
    CacheUtils cacheUtils;
    @Resource
    IDictService dictService;
    @Value("${baseOrgOrgCode}")
    private String baseOrgOrgCode;

    @Value("${sync_flow_push}")
    private String sync_flow_push;
    @Value("${exchange_nginx_key}")
    private String exchange_nginx_key;

    //组织关系调整更新锁需要的mapper资源
    @Resource
    MemMapper memMapper;
    @Resource
    MemAbroadMapper memAbroadMapper;
    @Resource
    private MemFlow1Mapper flow1Mapper;
    @Resource
    private UnitCommitteeMapper unitCommitteeMapper;
    @Resource
    private UnitCountrysideMapper unitCountrysideMapper;
    @Resource
    private UnitResidentMapper unitResidentMapper;
    @Resource
    private MemAllInfoMapper memAllInfoMapper;
    @Resource
    MemDevelopMapper memDevelopMapper;
    @Resource
    MemDevelopAllMapper developAllMapper;
    @Resource
    MemDifficultMapper memDifficultMapper;
    @Resource
    MemFlowMapper memFlowMapper;
    @Resource
    MemFlowAllMapper memFlowAllMapper;
    @Resource
    private MemRewardMapper rewardMapper;
    @Resource
    private MemRewardAllMapper memRewardAllMapper;
    @Resource
    private MemTrainMapper trainMapper;
    @Resource
    private OrgAllMapper orgAllMapper;
    @Resource
    private OrgCaucusMapper caucusMapper;
    @Resource
    private OrgCommitteeMapper orgCommitteeMapper;
    @Resource
    private OrgCommitteeElectMapper electMapper;
    @Resource
    private OrgExtendMapper extendMapper;
    @Resource
    private OrgGroupMapper groupMapper;
    @Resource
    private OrgIndustryMapper orgIndustryMapper;
    @Resource
    private OrgIndustryAllMapper orgIndustryAllMapper;
    @Resource
    private OrgPartyMapper orgPartyMapper;
    @Resource
    private OrgPartyCongressCommitteeMapper orgPartyCongressCommitteeMapper;
    @Resource
    private OrgPartyCongressElectMapper orgPartyCongressElectMapper;
    @Resource
    private OrgRecognitionMapper recognitionMapper;
    @Resource
    private OrgRecognitionAllMapper orgRecognitionAllMapper;
    @Resource
    private OrgRecognitionSituationMapper situationMapper;
    @Resource
    private OrgRewardMapper orgRewardMapper;
    @Resource
    private OrgSlackMapper orgSlackMapper;
    @Resource
    private OrgSlackAllMapper orgSlackAllMapper;
    @Resource
    private OrgSlackRectificationMapper orgSlackRectificationMapper;
    @Resource
    private OrgSpecialNatureMapper natureMapper;
    @Resource
    private UnitOrgLinkedMapper linkedMapper;
    @Resource
    private UnitMapper unitMapper;
    @Resource
    private UnitAllMapper unitAllMapper;
    @Resource
    private UnitCollectiveEconomicMapper unitCollectiveEconomicMapper;
    @Resource
    private UserMapper userMapper;
    @Resource
    private UserRolePermissionMapper userRolePermissionMapper;
    @Resource
    private IUnitCitySituationService unitCitySituationService;
    @Resource
    private IUnitCommitteeService iUnitCommitteeService;
    @Resource
    private IUnitCommunityService iUnitCommunityService;
    @Resource
    private IUnitCountrusideService iUnitCountrusideService;
    @Resource
    private IUnitExtendService iUnitExtendService;
    @Resource
    private IUnitOrgLinkedService iUnitOrgLinkedService;
    @Resource
    private IUnitSecondaryService iUnitSecondaryService;
    @Resource
    private IUnitSiteConditionsService iUnitSiteConditionsService;
    @Resource
    private IUnitStreetsCadresService iUnitStreetsCadresService;
    @Resource
    private IUnitCollectiveEconomicService iUnitCollectiveEconomicService;
    @Resource
    private IUnitIncomeService iUnitIncomeService;
    @Resource
    private IOrgService iOrgService;
    @Resource
    private IOrgAssessService iOrgAssessService;
    @Resource
    private IOrgCaucusService iOrgCaucusService;
    @Resource
    private IOrgCommitteeElectService iOrgCommitteeElectService;
    @Resource
    private IOrgCommitteeService iOrgCommitteeService;
    @Resource
    private IOrgDevelopRightsService iOrgDevelopRightsService;
    @Resource
    private IOrgExtendService iOrgExtendService;
    @Resource
    private IOrgGroupService iOrgGroupService;
    @Resource
    private IOrgGroupMemberService iOrgGroupMemberService;
    @Resource
    private IOrgIndustryService industryService;
    @Resource
    private IOrgIndustryAllService iOrgIndustryAllService;
    @Resource
    private IOrgNonPublicPartyService iOrgNonPublicPartyService;
    @Resource
    private IOrgPartyService iOrgPartyService;
    @Resource
    private IOrgPartyCongressElectService iOrgPartyCongressElectService;
    @Resource
    private IOrgPartyCongressCommitteeService iOrgPartyCongressCommitteeService;
    @Resource
    private IOrgPartyCongressCommitteeAllService iOrgPartyCongressCommitteeAllService;
    @Resource
    private IOrgRecognitionService iOrgRecognitionService;
    @Resource
    private IOrgRecognitionAllService iOrgRecognitionAllService;
    @Resource
    private IOrgRecognitionDataService iOrgRecognitionDataService;
    @Resource
    private IOrgRecognitionSituationService iOrgRecognitionSituationService;
    @Resource
    private IOrgAppraisalService iOrgAppraisalService;
    @Resource
    private IOrgReviewersService iOrgReviewersService;
    @Resource
    private IOrgRewardService iOrgRewardService;
    @Resource
    private IOrgSlackService iOrgSlackService;
    @Resource
    private IOrgSlackAllService iOrgSlackAllService;
    @Resource
    private IOrgSlackRectificationService iOrgSlackRectificationService;
    @Resource
    private IOrgSpecialNatureService iOrgSpecialNatureService;
    @Resource
    private IOrgTownshipLeadershipService iOrgTownshipLeadershipService;
    @Resource
    private IMemService iMemService;
    @Resource
    private IMemAllInfoService iMemAllInfoService;
    @Resource
    private IDemocraticReviewMemService iDemocraticReviewMemService;
    @Resource
    private IMemAbroadService iMemAbroadService;
    @Resource
    private IMemDevelopService iMemDevelopService;
    @Resource
    private IMemDevelopAllService iMemDevelopAllService;
    @Resource
    private IMemDevelopOperationService iMemDevelopOperationService;
    @Resource
    private IMemDifficultService iMemDifficultService;
    @Resource
    private IMemExtendService iMemExtendService;
    @Resource
    private IMemFlowService iMemFlowService;
    @Resource
    private IMemFlowAllService iMemFlowAllService;
    @Resource
    private IMemRewardService iMemRewardService;
    @Resource
    private IMemRewardAllService iMemRewardAllService;
    @Resource
    private IMemTrainService iMemTrainService;
    @Resource
    private IUnitCommitteeElectService iUnitCommitteeElectService;
    @Resource
    IOrgTransferRecordService iOrgTransferRecordService;
    @Resource
    private IDevelopStepLogService iDevelopStepLogService;
    @Resource
    private IDevelopStepLogAllService iDevelopStepLogAllService;
    @Resource
    private DevelopStepLogMapper developStepLogMapper;
    @Resource
    private DevelopStepLogAllMapper developStepLogAllMapper;
    @Resource
    private MemReportMapper memReportMapper;
    @Resource
    private UnitReportMapper unitReportMapper;
    @Resource
    private OrgLifeMapper orgLifeMapper;
    @Resource
    private OrgLifePartMapper orgLifePartMapper;
    @Resource
    private MemDevelopProcessMapper memDevelopProcessMapper;
    @Resource
    private MemDigitalMapper memDigitalMapper;
    @Resource
    private MemDigitalOperationLogMapper memDigitalOperationLogMapper;
    @Resource
    private UnitMemSelectMapper unitMemSelectMapper;
    @Resource
    private TransferHintMapper transferHintMapper;

    @Override
    public OutMessage exportOut(Page<TransferRecordDTO> result) throws Exception {
        List<InAndOutDTO> excelList = new ArrayList<>();
        if (cn.hutool.core.collection.CollectionUtil.isNotEmpty(result.getRecords())) {
            for (TransferRecordDTO record : result.getRecords()) {
                InAndOutDTO excel = new InAndOutDTO();
                BeanUtils.copyProperties(record, excel);
                excel.setCreateTime(DateUtil.format(record.getCreateTime(), "yyyy-MM-dd"));
                if (ObjectUtil.equal(record.getStatus(), 1)) {
                    excel.setStatus("已完成");
                } else if (ObjectUtil.equal(record.getStatus(), 2)) {
                    excel.setStatus("已撤销");
                } else if (ObjectUtil.equal(record.getStatus(), 0)) {
                    excel.setStatus("转接中");
                }
                excelList.add(excel);
            }
        }
        FileInputStream in = new FileInputStream(ResourceUtils.getURL(ResourceUtils.CLASSPATH_URL_PREFIX).getPath() + "public/excelftl/outAndIdExcel.xlsx");
        ExcelExportUtil<InAndOutDTO> excelExportUtil = new ExcelExportUtil<>(InAndOutDTO.class, 1, 0);
        File resultFile = excelExportUtil.exportFileXlsx(in, excelList, cn.hutool.core.date.DateUtil.format(new Date(), "yyyyMMddHHmmssSSS") + ".xlsx");
        in.close();
        Map<String, String> map = new HashMap<>(5);
        map.put("url", "/public/export/" + resultFile.getName());
        return new OutMessage<>(Status.SUCCESS, map);

    }


    @Override
    public Status moveOrg(String srcOrgId, String targetOrgId, boolean isTransfer) {
        //获取原组织
        Org srcOrg = orgService.findOrgByCode(srcOrgId);
        if (ObjectUtil.isNull(srcOrg)) {
            return Status.OBJEC_NOT_EXIST;
        }
        //获取目的组织,如果需要处理目的组织的isLeaf
        Org tarOrg = orgService.findOrgByCode(targetOrgId);
        if (ObjectUtil.isNull(tarOrg)) {
            return Status.TRANSFER_TARGET_ORG_IS_NOT_EXIST;
        }
        String orgType = tarOrg.getOrgType();
        //如果目的组织是党支部或者联合党支部，不允许移动下去
        if (StrUtil.equalsAny(orgType, "3", "4", "5")) {
            return Status.UNION_MOVE_MUST;
        }

        //不能自己移动自己到自己那里
        if (srcOrgId.equals(targetOrgId)) {
            return Status.CURRENT_ORG_IS_ZIJ;
        }
        //不能自己移动自己到自己子类面去
        String srcOrgCode = srcOrg.getOrgCode();
        String tarOrgOrgCode = tarOrg.getOrgCode();
        if (tarOrgOrgCode.startsWith(srcOrgCode)) {
            return Status.CURRENT_ORG_IS_ZIJ;
        }

        if (!isTransfer) {
            //如果有组织关系转接正在进行，不能移动
            List<Org> allSubOrgByOrgCode = orgService.findAllSubOrgByOrgCode(srcOrg.getOrgCode());
            if (allSubOrgByOrgCode.size() > 0) {
                List<String> orgId = allSubOrgByOrgCode.stream().map(Org::getCode).collect(Collectors.toList());
                LambdaQueryWrapper<TransferRecord> srcInsql = new LambdaQueryWrapper<>();
                srcInsql.eq(TransferRecord::getStatus, CommonConstant.ZERO_INT).in(TransferRecord::getSrcOrgId, orgId);
                List<TransferRecord> srcList = list(srcInsql);
                LambdaQueryWrapper<TransferRecord> tarInsql = new LambdaQueryWrapper<>();
                tarInsql.eq(TransferRecord::getStatus, CommonConstant.ZERO_INT).in(TransferRecord::getTargetOrgId, orgId);
                List<TransferRecord> tarList = list(tarInsql);
                //直接撤销当前组织及其下属组织发起的关系转接
                if (srcList.size() > 0) {
                    //srcList.forEach(transferRecord -> this.undo(transferRecord.getId(),"因当发起党组织或接收党组织发生架构调整系统自动撤回所有关系转接",false));
                    //需求变更为自己或者自己节点发起得需要提示取消
                    return Status.NEED_DEL_ORG_TRANSFER;
                }
                //直接撤销转入当前组织及其下属组织的关系转接
                if (tarList.size() > 0) {
                    for (TransferRecord transferRecord : tarList) {
                        //这里分为两种情况，如果是同一个节点发起的转入，直接走撤销接口即可(撤销接口会自动恢复人员情况)
                        String tarSrcOrgId = transferRecord.getSrcOrgId();
                        Org tarSrcOrg = orgService.findOrgByCode(tarSrcOrgId);
                        //发起的组织就是在当前数据节点直接走撤销接口
                        if (ObjectUtil.isNotNull(tarSrcOrg)) {
                            this.undo(transferRecord.getId(), "因当发起党组织或接收党组织发生架构调整系统自动撤回所有关系转接", false);
                        } else {
                            //发起的组织不在当前数据节点证明是是其他数据节点发起到这边接收的转接
                            String replaceUrl = sync_flow_push.replace("/api/flow/push", "");
                            // TODO: 2021/11/17 请求中间交换区中，转接到我的目的节点的数据
                            JSONObject postjson = new JSONObject();
                            postjson.put("record", transferRecord.getId());
                            HttpKit.doPost(replaceUrl + "/transfer/undoData", postjson, "UTF-8");
                        }
                    }
                }
            }
        }
        //生成新的层级码
        String newOrgCode = orgService.createOrgCode(tarOrg);
        // TODO: 2021/11/28 处理发起组织如果是一个党委或者是一个党总支的情况
        //获取目的组织的所有下级
        List<Org> byOrgCodeAllList = orgService.findByOrgCodeAllList(srcOrgCode);
        //替换所有的开始层级码
        for (Org org : byOrgCodeAllList) {
            String code = org.getCode();
            String orgCode = org.getOrgCode();
            String newOrgOrgCode = orgCode.replace(srcOrgCode, newOrgCode);
            this.updateMoveOrgData(code, newOrgOrgCode);
        }
        // TODO: 2021/11/26 这里还差一些判断，比如是否有关系转接，是否有流动党员正在流动，是否有发展党员等相关信息
        //单独处理顶层节点转移过去的问题
        //组织信息--code//org_code//parent_code
        LambdaUpdateChainWrapper<Org> orgLambdaUpdateChainWrapper = new LambdaUpdateChainWrapper<>(orgMapper);
        boolean org = orgLambdaUpdateChainWrapper.eq(Org::getCode, srcOrgId)
                .set(Org::getOrgCode, newOrgCode)
                .set(Org::getParentCode, tarOrg.getCode()).update();

        //组织信息--all表 code//org_code
        LambdaUpdateChainWrapper<OrgAll> orgAllLambdaUpdateChainWrapper = new LambdaUpdateChainWrapper<>(orgAllMapper);
        boolean orgAll = orgAllLambdaUpdateChainWrapper.eq(OrgAll::getCode, srcOrgId)
                .set(OrgAll::getOrgCode, newOrgCode)
                .set(OrgAll::getParentCode, tarOrg.getCode())
                .set(OrgAll::getParentName, tarOrg.getName())
                .set(OrgAll::getParentOrgCode, tarOrgOrgCode).update();

        //更新目的组织表的isLeaf情况
        tarOrg.setIsLeaf(CommonConstant.ZERO_INT);
        orgService.updateById(tarOrg);
        //更新目的组织all表的isLeaf情况
        OrgAll byCode = orgAllService.findByCode(targetOrgId);
        if (ObjectUtil.isNotNull(byCode)) {
            byCode.setIsLeaf(CommonConstant.ZERO_INT);
            orgAllService.updateById(byCode);
        }

        //因为目标组织发生了变化， 所以需要同步中间交换区得组织情况
        CompletableFuture.supplyAsync(() -> {
            // TODO: 2021/12/27 处理中间交换区上级党组织问题
            //获取移动后得最新得党组织得情况
            Org orgByCode = orgService.findOrgByCode(srcOrgId);
            String orgCode = orgByCode.getOrgCode();
            List<OrgAll> allSubOrgByOrgCode = orgService.findExchangeOrgByOrgCode(orgCode);
            if (allSubOrgByOrgCode.size() > 0) {
                JSONArray postJsonArray = new JSONArray();
                allSubOrgByOrgCode.forEach(moveOrg -> {
                    JSONObject jsonObject = JSONObject.parseObject(JSONObject.toJSONString(moveOrg));
                    jsonObject.put("exchangeKey", exchange_nginx_key);
                    postJsonArray.add(jsonObject);
                });
                JSONObject postJson = new JSONObject();
                postJson.put("data", postJsonArray);
                String replaceUrl = sync_flow_push.replace("/api/flow/push", "");
                String rultMessage = HttpKit.doPost(replaceUrl + "/org/addOrg", postJson, "UTF-8");

            }
            return true;
        }, mySimpleAsync);


        return Status.SUCCESS;
    }

    @Override
    public List<TransferRecord> allTransFer() {
        return list(new LambdaQueryWrapper<TransferRecord>().orderByAsc(TransferRecord::getCreateTime));
    }

    @Override
    public List<TransferRecord> allProcessingTransFer() {
        return list(new LambdaQueryWrapper<TransferRecord>().select(TransferRecord::getCreateTime, TransferRecord::getSrcOrgName,
                        TransferRecord::getTargetOrgName, TransferRecord::getId, TransferRecord::getCurrentApprovalId, TransferRecord::getName,
                        TransferRecord::getType, TransferRecord::getSrcOrgRelation)
                .eq(TransferRecord::getStatus, CommonConstant.ZERO_INT).orderByAsc(TransferRecord::getCreateTime));
    }


    /**
     * ！！！！！注意，请注意看查询列是否有你所需要的信息！！！！！
     ***/
    @Override
    public TransferRecord findTransferByMemCode(String memCode) {
        //获取最新的数据
        LambdaQueryWrapper<TransferRecord> queryWrapper = new LambdaQueryWrapper<TransferRecord>().eq(TransferRecord::getMemId, memCode)
                .orderByDesc(TransferRecord::getCreateTime).last("limit 1");
        return getOne(queryWrapper);
    }


    @Override
    public OutMessage<Object> transferMsg(MemFlowRegisterDTO data) {
        List<Record> res = new ArrayList<>();
        for (String memCode : data.getCodeList()) {
            Record resumeVoList = getResumeVos(memCode);
            if (Objects.nonNull(resumeVoList)) {
                res.add(resumeVoList);
            }
        }
        return new OutMessage<>(Status.SUCCESS, res);
    }

    private Record getResumeVos(String memCode) {
        String property = System.getProperty("line.separator");
        List<ResumeVo> resumeVoList = new ArrayList<>();
        Record record  = new Record();
        Mem mem = iMemService.findAllByCode(memCode);
        //党员在单位管理--最新届次中是否有任职
        List<UnitCommittee> unitJobList = unitCommitteeMapper.findJobsBycode(memCode);
        if (CollUtil.isNotEmpty(unitJobList)) {
            String value = "单位任职";
            String desc = unitJobList.stream()
                    .map(job -> {
                        String unitName  = Objects.isNull(job.getUnitName()) ? "" : job.getUnitName();
                        String jobNmae = Objects.isNull(job.getD25Name()) ? "" : job.getD25Name();
                        return unitName + " " + jobNmae;
                    })
                    .map(s -> "单位任职：" + s).collect(Collectors.joining(property));
            resumeVoList.add(ResumeVo.builder().value(value).desc(desc)
                    .solve("在单位管理页面，点击党员任职单位，点击村（居）委会，在最新届次对该党员进行转为历史任职操作").build());
        }
        //党员在党组织管理--最新届次中是否有任职
        List<OrgCommittee> orgJobList = orgCommitteeMapper.findJobsBycode(memCode);
        if (CollUtil.isNotEmpty(orgJobList)) {
            String value = "组织任职";
            String desc = orgJobList.stream()
                    .map(job -> {
                        String orgtName  = Objects.isNull(job.getPositionOrgName()) ? "" : job.getPositionOrgName();
                        String jobNmae = Objects.isNull(job.getD022Name()) ? "" : job.getD022Name();
                        return orgtName + " " + jobNmae;
                    })
                    .map(s -> "组织任职：" + s).collect(Collectors.joining(property));
            resumeVoList.add(ResumeVo.builder().value(value).desc(desc)
                    .solve("在党组织管理页面，点击党员任职组织，点击班子成员，在最新届次对该党员进行转为历史任职操作").build());
        }
        //党员在党员管理中--是否有出国信息，但未回国的情况
        List<MemAbroad> memAbroads = memAbroadMapper.selectList(new LambdaUpdateWrapper<MemAbroad>()
                .eq(MemAbroad::getMemCode, memCode)
                .isNull(MemAbroad::getDeleteTime)
                .isNull(MemAbroad::getBackHomeDate));
        if (CollUtil.isNotEmpty(memAbroads)) {
            String value = "出国信息";
            String desc = memAbroads.stream()
                    .map(memAbroad -> Objects.isNull(memAbroad.getCountryName()) ? "" :
                            (Objects.equals(memAbroad.getCountryCode(), "3") ? "台湾" : memAbroad.getCountryName()))
                    .map(s -> "所至国家（地区）名称：" + s).collect(Collectors.joining(property));
            resumeVoList.add(ResumeVo.builder().value(value).desc(desc)
                    .solve("在党员管理页面。点击该党员，点击出国出境，添加回国信息").build());
        }
        //党员在流动党员流出管理中（未纳入流入地管理、已纳入流入地管理、流出被退回）是否存在存在流动信息
        List<MemFlow1> memFlows = flow1Mapper.selectList(new LambdaQueryWrapper<MemFlow1>()
                .eq(MemFlow1::getMemCode, memCode)
                .eq(MemFlow1::getFlowOut, "1")
                .in(MemFlow1::getFlowStep, "1", "2", "3"));
        if (CollUtil.isNotEmpty(memFlows)) {
            String value = "流动党员信息";
            String desc = memFlows.stream()
                    .map(memFlow -> Objects.isNull(memFlow.getOutOrgName()) ? "" : memFlow.getOutOrgName())
                    .map(s -> "流入党委：" + s).collect(Collectors.joining(property));
            resumeVoList.add(ResumeVo.builder().value(value)
                    .solve("在流动党员页面，撤销党员的未纳入流入地管理数据，主动流回或者联系流入方流回已纳入流入地管理数据、终止流出被退回数据").desc(desc).build());
        }
        //党员在党代表中是否存在党代表任职情况（此信息仅做提示，因存在县下面换单位，但是依旧是党代表身份）
        List<OrgPartyCongressCommittee> orgPartyCongressCommittees = orgPartyCongressCommitteeMapper
                .selectList(new LambdaQueryWrapper<OrgPartyCongressCommittee>()
                        .eq(OrgPartyCongressCommittee::getMemCode, memCode).isNull(OrgPartyCongressCommittee::getDeleteTime)
                        .eq(OrgPartyCongressCommittee::getMemTypeCode, "1")
                        .isNull(OrgPartyCongressCommittee::getEndDate));
        if (CollUtil.isNotEmpty(orgPartyCongressCommittees)) {
            String value = "党代表任职情况";
            String desc = orgPartyCongressCommittees.stream()
                    .map(orgParty -> {
                        String orgtName  = Objects.isNull(orgParty.getPositionOrgName()) ? "" : orgParty.getPositionOrgName();
                        String jobNmae = Objects.isNull(orgParty.getd106Name()) ? "" : orgParty.getd106Name();
                        return orgtName + " " + jobNmae;
                    })
                    .map(s -> "党代表任职情况：" + s).collect(Collectors.joining(property));
            resumeVoList.add(ResumeVo.builder().value(value).desc(desc)
                    .solve("此信息仅做提示").build());
        }
        //todo 等选调生模块做好 党员在单位中的选调生是否有任职
        //党员在单位中的村社区工作者和后备干部是否有任职
        List<UnitCountryside> unitCountrysides = unitCountrysideMapper.selectList(new LambdaQueryWrapper<UnitCountryside>()
                .eq(UnitCountryside::getMemIdcard, EncryptProperties.enable?SM4Untils.encryptContent(exchange_nginx_key, mem.getIdcard()):mem.getIdcard())
                .isNull(UnitCountryside::getDeleteTime)
                .apply("exists (select 1 from ccp_unit where ccp_unit.delete_time is null and ccp_unit.code = ccp_unit_countryside.unit_code)")
        );
        if (CollUtil.isNotEmpty(unitCountrysides)) {
            String value = "村社区工作者和后备干部任职情况";
            String desc = unitCountrysides.stream()
                    .map(unitCountryside -> {
                        String orgtName  = Objects.isNull(unitCountryside.getUnitName()) ? "" : unitCountryside.getUnitName();
                        String jobNmae = Objects.isNull(unitCountryside.getD143Name()) ? "" : unitCountryside.getD143Name();
                        return orgtName + " " + jobNmae;
                    })
                    .map(s -> "村社区工作者和后备干部任职情况：" + s).collect(Collectors.joining(property));
            resumeVoList.add(ResumeVo.builder().value(value).desc(desc)
                    .solve("在单位管理页面，点击党员任职单位，点击村（社区）工作者和后备干部，对该党员进行离开操作").build());
        }
        //党员在单位中的驻村干部中是否有任职
        List<UnitResident> unitResidents = unitResidentMapper.selectList(new LambdaQueryWrapper<UnitResident>()
                .eq(UnitResident::getMemCode, memCode)
                // 是党员
                .eq(UnitResident::getd139Code, "1")
                .isNull(UnitResident::getDeleteTime).isNull(UnitResident::getEndDate)
                .apply("exists (select 1 from ccp_unit where ccp_unit.delete_time is null and ccp_unit.code = ccp_unit_resident.unit_code)")
        );
        if (CollUtil.isNotEmpty(unitResidents)) {
            String value = "驻村干部任职情况";
            String desc = unitResidents.stream()
                    .map(unitResident -> {
                        String unitCode  = Objects.isNull(unitResident.getUnitCode()) ? "" : unitResident.getUnitCode();
                        Unit byCode = iUnitService.findByCode(unitCode);
                        String jobNmae = Objects.isNull(unitResident.getd140Name()) ? "" : unitResident.getd140Name();
                        return byCode.getName() + " " + jobNmae;
                    })
                    .map(s -> "驻村干部任职情况：" + s).collect(Collectors.joining(property));
            resumeVoList.add(ResumeVo.builder().value(value).desc(desc)
                    .solve("在单位管理页面，点击党员任职单位，点击驻村干部，对该党员进行离开操作").build());
        }

        // 校验党员在单位中的选调生是否有任职
        LambdaQueryWrapper<UnitMemSelect> unitMemSelectLambdaQueryWrapper = Wrappers.lambdaQuery();
        unitMemSelectLambdaQueryWrapper.isNull(UnitMemSelect::getLeaveTime)
                .isNull(UnitMemSelect::getDeleteTime)
                // 是党员
                .eq(UnitMemSelect::getMemTypeCode, "1")
                .eq(UnitMemSelect::getMemCode, memCode)
                .apply("exists (select 1 from ccp_unit where ccp_unit.delete_time is null and ccp_unit.code = ccp_unit_mem_select.unit_code)");
        List<UnitMemSelect> unitMemSelectList = unitMemSelectMapper.selectList(unitMemSelectLambdaQueryWrapper);
        if (CollUtil.isNotEmpty(unitMemSelectList)) {
            String value = "选调生情况";
            String desc = unitMemSelectList.stream()
                    .map(UnitMemSelect::getUnitName)
                    .map(s -> "选调生在职单位：" + s).collect(Collectors.joining(property));
            resumeVoList.add(ResumeVo.builder().value(value).desc(desc)
                    .solve("在单位管理页面，点击党员任职单位，点击选调生，对该选调生进行离开操作").build());
        }

        if (CollUtil.isNotEmpty(resumeVoList)) {
            record.set("memName", mem.getName());
            record.set("memCode", mem.getCode());
            record.set("resumeVoList", resumeVoList);
            return record;
        }else {
            return null;
        }
    }

    @Override
    public OutMessage findOrgByName(OrgListDTO data) {
        LambdaQueryWrapper<Org> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.isNull(Org::getDeleteTime)
                .ne(Org::getOrgCode, "10").ne(Org::getOrgCode, "100")
                .orderByAsc(Org::getOrgCode).last("limit 1");
        Org one = orgService.getOne(lambdaQueryWrapper);
        data.setOrgCode(one.getOrgCode());
        return orgService.getTransferList(data);
    }

    /**
     * 修改党员状态是否在组织关系转接中
     *
     * @param memIds 人员的id
     */
    public void updateMemIsTransfer(String transFerId, List<String> memIds) {
        LambdaQueryWrapper<TransferRecord> sql = new LambdaQueryWrapper<TransferRecord>()
                .in(TransferRecord::getMemId, memIds).eq(TransferRecord::getId, transFerId);
        List<TransferRecord> transferRecordList = list(sql);
        List<Mem> updateMem = new ArrayList<>();
        // TODO: 2021/12/6 如果一个人被反复转接的情况下，这里可能会拿到一条完成的记录，导致出现问题
        Map<String, List<TransferRecord>> transferRecordMap = transferRecordList.stream().collect(Collectors.groupingBy(TransferRecord::getMemId));
        Set<String> memKey = transferRecordMap.keySet();
        memKey.forEach(memCode->{
            List<TransferRecord> transferRecords = transferRecordMap.get(memCode);
            List<TransferRecord> sortList = transferRecords.stream().sorted(Comparator.comparing(TransferRecord::getCreateTime)).collect(Collectors.toList());
            Mem mem = new Mem();
            Integer stauts =  CommonConstant.ONE_INT;
            for (TransferRecord transferRecord : sortList) {
                if (ObjectUtil.equal(transferRecord.getStatus(), CommonConstant.ONE_INT) || ObjectUtil.equal(transferRecord.getStatus(), CommonConstant.TWO_INT)
                        || ObjectUtil.equal(transferRecord.getStatus(), TransferRecordConstant.TRANSFER_OVERDUE_BACK)) {
                    stauts =  CommonConstant.ZERO_INT;
                    break;
                }
            }
            mem.setIsTransfer(stauts);
            mem.setCode(memCode);
            updateMem.add(mem);
            iMemService.update(new LambdaUpdateWrapper<Mem>().set(Mem::getIsTransfer, stauts).set(Mem::getUpdateTime, new Date()).eq(Mem::getCode, memCode));
            iMemAllInfoService.update(new LambdaUpdateWrapper<MemAllInfo>().set(MemAllInfo::getIsTransfer, stauts).set(MemAllInfo::getUpdateTime, new Date()).eq(MemAllInfo::getCode, memCode));
        });

//        for (Map.Entry<String, List<TransferRecord>> entry : transferRecordMap.entrySet()) {
//            Mem mem = new Mem();
//            int stauts = 1;
//            // TODO: 2021/12/6 如关系转接已经转接完成或者已经撤销，那么就要置换为0
//            for (TransferRecord transferRecord : entry.getValue()) {
//                if (ObjectUtil.equal(transferRecord.getStatus(), 1) || ObjectUtil.equal(transferRecord.getStatus(), 2)) {
//                    stauts = 0;
//                    break;
//                }
//            }
//            mem.setIsTransfer(stauts);
//            mem.setMemCode(entry.getKey());
//            updateMem.add(mem);
//            iMemService.update(new LambdaUpdateWrapper<Mem>().set(Mem::getIsTransfer, stauts).set(Mem::getUpdateTime, new Date()).eq(Mem::getCode, entry.getKey()));
//            iMemAllInfoService.update(new LambdaUpdateWrapper<MemAllInfo>().set(MemAllInfo::getIsTransfer, stauts).set(MemAllInfo::getUpdateTime, new Date()).eq(MemAllInfo::getCode, entry.getKey()));
//        }
    }

    @Override
    public OutMessage updateByFileType(TransferRecordDTO data) {
        TransferRecord transferRecord = new TransferRecord();
        transferRecord.setId(data.getId());
        transferRecord.setD92Code(data.getD92Code());
        transferRecord.setD92Name(data.getD92Name());
        if (StrUtil.equals(transferRecord.getD92Code(), CommonConstant.THREE)) {
            transferRecord.setRemark(data.getRemark());
        } else {
            transferRecord.setRemark(null);
        }
        updateById(transferRecord);
        return new OutMessage(Status.SUCCESS);
    }

    @Override
    public Long getTotalByOrgId(String code) {
        LambdaQueryWrapper<TransferRecord> wrapper = new LambdaQueryWrapper<TransferRecord>()
                .eq(TransferRecord::getStatus, 0);
        wrapper.and(r -> r.apply("src_org_relation :: jsonb ?? {0}", code)
                .or()
                .apply("target_org_relation :: jsonb ?? {0}", code));
        return Long.valueOf(recordMapper.selectCount(wrapper));
    }

    @Override
    public OutMessage<TransferAnaVO> indexCount(String orgId, Integer pageNum) {
        TransferAnaVO transferAnaVO;
        //如果获取第一页则在数据库中进行统计,否则在内存中进行分页
        if (pageNum == 1) {
            List<TransferRecord> transferRecordList = recordMapper.findRecordByOrgId(orgId);
            transferAnaVO = new TransferAnaVO();
            transferAnaVO.setOrgId(orgId);
            transferAnaVO.setOrgName(CacheUtils.getOrgName(orgId));
            List<Org> subOrgList = orgMapper.selectList(new QueryWrapper<Org>().lambda()
                    .select(Org::getCode, Org::getOrgCode, Org::getShortName, Org::getOrgType)
                    .eq(Org::getParentCode, orgId)
                    .isNull(Org::getDeleteTime));
            //如果没有直属下级,则返回待审核列表
            if (subOrgList == null || subOrgList.isEmpty()) {
                analysisList(orgId, transferRecordList, transferAnaVO);
            } else {
                analysisNum(transferAnaVO, orgId, transferRecordList, subOrgList);
            }

            RBucket<TransferAnaVO> bucket = cacheUtils.redissonClient.getBucket(CacheConstant.ANA_TRANSFER_PREFIX + orgId);
            bucket.set(transferAnaVO);
        } else {
            RBucket<TransferAnaVO> bucket = cacheUtils.redissonClient.getBucket(CacheConstant.ANA_TRANSFER_PREFIX + orgId);
            transferAnaVO = bucket.get();
            if (transferAnaVO == null) {
                transferAnaVO = new TransferAnaVO();
                transferAnaVO.setOrgId(orgId);
                transferAnaVO.setOrgName(CacheUtils.getOrgName(orgId));
                return new OutMessage<>(Status.SUCCESS, transferAnaVO);
            }
        }

        //进行分页
        List<TransferAnaVO> subOrgAna = transferAnaVO.getSubOrgAna();
        if (subOrgAna != null && !subOrgAna.isEmpty()) {
            subOrgAna = ListUtils.page(subOrgAna, pageNum, PAGE_SIZE);
            transferAnaVO.setSubOrgAna(subOrgAna);
        }
        List<TransferRecordDTO> memTransferList = transferAnaVO.getMemTransferList();
        if (memTransferList != null && !memTransferList.isEmpty()) {
            memTransferList = ListUtils.page(memTransferList, pageNum, PAGE_SIZE);
            transferAnaVO.setMemTransferList(memTransferList);
        }
        List<TransferRecordDTO> orgTransferList = transferAnaVO.getOrgTransferList();
        if (orgTransferList != null && !orgTransferList.isEmpty()) {
            orgTransferList = ListUtils.page(orgTransferList, pageNum, PAGE_SIZE);
            transferAnaVO.setOrgTransferList(orgTransferList);
        }


        return new OutMessage<>(Status.SUCCESS, transferAnaVO);
    }

    /**
     * 分析数字
     *
     * @param orgId              上级组织id
     * @param subOrgList         直属组织列表
     * @param transferAnaVO      vo
     * @param transferRecordList 关系转接列表
     **/
    private void analysisNum(TransferAnaVO transferAnaVO, String orgId, List<TransferRecord> transferRecordList, List<Org> subOrgList) {
        List<String> subOrgIdList = subOrgList.stream().map(Org::getCode).collect(Collectors.toList());
        Map<String, TransferAnaVO> subVoMap = subOrgList.stream().collect(Collectors.toMap(Org::getCode, o -> {
            TransferAnaVO subVo = new TransferAnaVO();
            subVo.setOrgId(o.getCode());
            subVo.setOrgName(o.getShortName());
            subVo.setType(o.getOrgType());
            transferAnaVO.addSubVo(subVo);
            return subVo;
        }));
        transferRecordList.forEach(record -> {
            Set<String> srcSet = record.getSrcOrgRelationAsSet();
            Set<String> targetSet = record.getTargetOrgRelationAsSet();
            //转入
            String type = record.getType();
            parentAnd(orgId, record, transferAnaVO, srcSet, targetSet);
            //遍历子类型
            for (String subOrgId : subOrgIdList) {
                //转入
                if (targetSet.contains(subOrgId)) {
                    Optional<TransferAnaVO> opt = Optional.ofNullable(subVoMap.get(subOrgId));
                    opt.ifPresent(TransferAnaVO::incrementInTotal);
                    //整建制转入
                    if (TransferRecordConstant.ORG_ALL_IN.contains(type)) {
                        opt.ifPresent(TransferAnaVO::incrementZjzTotal);
                    }
                    //转接完成
                    if (TransferRecordConstant.TRANSFER_SUCCESS.equals(record.getStatus())) {
                        opt.ifPresent(TransferAnaVO::incrementFinishTotal);
                    }
                    //未转接完成
                    if (TransferRecordConstant.TRANSFERING.equals(record.getStatus())) {
                        opt.ifPresent(TransferAnaVO::incrementNotFinishTotal);
                    }
                }
                //转出
                if (srcSet.contains(subOrgId)) {
                    Optional<TransferAnaVO> opt = Optional.ofNullable(subVoMap.get(subOrgId));
                    opt.ifPresent(TransferAnaVO::incrementOutTotal);
                    //整建制转出
                    if (TransferRecordConstant.ORG_ALL_OUT.contains(type)) {
                        opt.ifPresent(TransferAnaVO::incrementZjzTotal);
                    }
                    //转接完成
                    if (TransferRecordConstant.TRANSFER_SUCCESS.equals(record.getStatus())) {
                        opt.ifPresent(TransferAnaVO::incrementFinishTotal);
                    }
                    //未转接完成
                    if (TransferRecordConstant.TRANSFERING.equals(record.getStatus())) {
                        opt.ifPresent(TransferAnaVO::incrementNotFinishTotal);
                    }
                }
                //待审批
                if (TransferRecordConstant.TRANSFERING.equals(record.getStatus()) && StrKit.equals(record.getNext_org_id(), subOrgId)) {
                    Optional<TransferAnaVO> opt = Optional.ofNullable(subVoMap.get(subOrgId));
                    opt.ifPresent(TransferAnaVO::incrementPendingTotal);
                }
            }
        });
    }

    /**
     * 统计组织的转入,转出,整建制,转接完成,转接未完成数量
     **/
    private void parentAnd(String orgId, TransferRecord record, TransferAnaVO transferAnaVO, Set<String> srcSet, Set<String> targetSet) {
        //转入
        if (targetSet.contains(orgId)) {
            transferAnaVO.incrementInTotal();
        }
        //转出
        if (srcSet.contains(orgId)) {
            transferAnaVO.incrementOutTotal();
        }
        //转接类型
        String type = record.getType();
        if (TransferRecordConstant.ORG_ALL_IN.contains(type) || TransferRecordConstant.ORG_ALL_OUT.contains(type)) {
            transferAnaVO.incrementZjzTotal();
        }
        //待审批
        if (TransferRecordConstant.TRANSFERING.equals(record.getStatus()) && StrKit.equals(record.getNext_org_id(), orgId)) {
            transferAnaVO.incrementPendingTotal();
        }
        //转接完成
        if (TransferRecordConstant.TRANSFER_SUCCESS.equals(record.getStatus())) {
            transferAnaVO.incrementFinishTotal();
        }
        //未转接完成
        if (TransferRecordConstant.TRANSFERING.equals(record.getStatus())) {
            transferAnaVO.incrementNotFinishTotal();
        }

    }

    /**
     * 返回组织待审核列表
     **/
    private void analysisList(String orgId, List<TransferRecord> transferRecordList, TransferAnaVO transferAnaVO) {
        transferRecordList.forEach(record -> {
            Set<String> srcSet = record.getSrcOrgRelationAsSet();
            Set<String> targetSet = record.getTargetOrgRelationAsSet();
            parentAnd(orgId, record, transferAnaVO, srcSet, targetSet);
            // 如果是待审核,判断是否该本组织进行审核
            if (TransferRecordConstant.TRANSFERING.equals(record.getStatus())) {
                if (StrKit.equals(record.getNext_org_id(), orgId)) {
                    TransferRecordDTO recordDTO = new TransferRecordDTO();
                    recordDTO.setId(record.getId());
                    recordDTO.setTargetOrgId(record.getTargetOrgId());
                    recordDTO.setSrcOrgId(record.getSrcOrgId());
                    recordDTO.setSrcOrgName(record.getSrcOrgName());
                    recordDTO.setTargetOrgName(record.getTargetOrgName());
                    recordDTO.setName(record.getName());
                    //本组织审核
                    //判断转接记录是否是人员
                    if (TransferRecordConstant.MEM_OUT.contains(record.getType()) || TransferRecordConstant.MEM_IN.contains(record.getType())) {
                        transferAnaVO.addMenListRecord(recordDTO);
                    } else if (TransferRecordConstant.ORG_ALL_OUT.contains(record.getType()) || TransferRecordConstant.ORG_ALL_IN.contains(record.getType())) {
                        transferAnaVO.addOrgTransferListRecord(recordDTO);
                    }
                }
            }
        });

    }

    /***
     * 设置转接类型
     * @param type 转接类型
     * @param transferRecord 转接记录
     * @return true 设置成功 false 设置失败 失败的原因要么是参数错误 要么是字典表有问题
     * */
    protected boolean setTransferTypeCode(String type, TransferRecord transferRecord) {
        switch (type.charAt(0)) {
            //转出类型
            case TransferRecordConstant.OUT_TYPE_KEY_PREFIX_REX:
                //设置转出类型
                transferRecord.setOutType(type);
                //如果是系统外的类型
                if (type.matches(TransferRecordConstant.SYSTEM_OUT_OUT_REX)) {
                    transferRecord.setInType("");
                    transferRecord.setType(type);
                    return true;
                }
                //查找对应的字典表中的类型,如果是转入就查转出的字典表
                //查询到对应的字典表数据,设置相应的数据
                Optional<Record> inOptional = CacheUtils.getDic(TransferRecordConstant.DICT_IN)
                        .stream()
                        .filter(re -> re.getStr("ref").equalsIgnoreCase(type))
                        .findFirst();
                if (!inOptional.isPresent()) {
                    //没有对应的转入类型
                    return false;
                }
                Record inRec = inOptional.get();
                String inType = inRec.getStr("key");
                transferRecord.setInType(inType);
                break;
            //转入类型
            case TransferRecordConstant.IN_TYPE_KEY_PREFIX_REX:
                //设置转入类型
                transferRecord.setInType(type);
                //如果是系统外的类型
                if (type.matches(TransferRecordConstant.SYSTEM_OUT_IN_REX)) {
                    transferRecord.setOutType("");
                    transferRecord.setType(type);
                    return true;
                }
                Optional<Record> outOptional = CacheUtils.getDic(TransferRecordConstant.DICT_OUT)
                        .stream()
                        .filter(re -> re.getStr("ref").equalsIgnoreCase(type))
                        .findFirst();
                if (!outOptional.isPresent()) {
                    //没有对应的转入类型
                    return false;
                }
                Record outRec = outOptional.get();
                String outType = outRec.getStr("key");
                transferRecord.setOutType(outType);
                break;
            default:
                return false;
        }
        transferRecord.setType(type);
        return true;
    }

    /***
     * @param srcOrgId 源组织id
     * @param targetOrgId 目标组织id
     * @param transferRecord 转接记录
     * @return true 通过参数校验并设置  false 未通过参数校验
     * */
    Ret setSrcAndTargetRelation(String srcOrgId, String targetOrgId, TransferRecord transferRecord, boolean isOrg) {
        String inType = transferRecord.getInType();
        String outType = transferRecord.getOutType();
        //如果类型同时满足 系统外转出 与 系统外转入
        //抛出错误
        if (inType.matches(TransferRecordConstant.SYSTEM_OUT_IN_REX) && outType.matches(TransferRecordConstant.SYSTEM_OUT_OUT_REX)) {
            return Ret.fail(RetConstant.MESSAGE, new OutMessage<>(Status.ORG_TRANSFER_TYPE_ERROR));
        }
        //系统外转入
        if (inType.matches(TransferRecordConstant.SYSTEM_OUT_IN_REX)) {
            transferRecord.setSrcOrgRelationAsList(new ArrayList<>());
        } else {
            //非系统外转入,则源组织id不能为空
            if (StrKit.isBlank(srcOrgId)) {
                return Ret.fail(RetConstant.MESSAGE, new OutMessage<>(Status.TRANSFER_SRC_ORG_IS_NOT_EXIST));
            }
            //如果源组织没有任何上级,则该节点脱节
            List<Org> srcParentOrg = orgService.findAllParentOrg(srcOrgId);
            if (srcParentOrg == null || srcParentOrg.isEmpty()) {
                return Ret.fail(RetConstant.MESSAGE, new OutMessage<>(Status.TRANSFER_SRC_ORG_IS_NOT_EXIST));
            }
            //如果查询出的上级包含空的parentCode
            //则认为该组织被认为脱节组织关系
            boolean isMatch = srcParentOrg.stream().anyMatch(org -> StrKit.isBlank(org.getParentCode()));
            if (isMatch) {
                return Ret.fail(RetConstant.MESSAGE, new OutMessage<>(Status.TRANSFER_SRC_ORG_IS_NOT_EXIST));
            }
            //如果只查询出一条,那么这一条就是该组织的本身id
            //按照常理是不可能出现这种数据的,除非是顶级节点 或者该节点脱节
            //顶级节点是不允许被调整的
            if (srcParentOrg.size() == 1) {
                return Ret.fail(RetConstant.MESSAGE, new OutMessage<>(Status.PERMISSION_DENIED));
            }
            // 20210927 改为只需要直属上级审核就行了
            List<String> list = srcParentOrg.stream().map(Org::getCode).collect(Collectors.toList());
            String type = transferRecord.getType();
            // TODO: 2022/4/30 需要取消掉关系转接支部间人员调整放开
            if (!"29".equals(type)) {
                // TODO: 2022/4/20 增加判断是否具有预备党员审批权限的上级， 如果一个都没有，那么不允许发起转接
                List<OrgDevelopRights> orgDevelopRightsByCodeList = iOrgDevelopRightsService.findOrgDevelopRightsByCodeList(list);
                if (ObjectUtil.isNull(orgDevelopRightsByCodeList) || orgDevelopRightsByCodeList.size() == CommonConstant.ZERO_INT) {
                    return Ret.fail(RetConstant.MESSAGE, new OutMessage<>(Status.PERMISSION_NODE_ERR0));
                }
                // TODO: 2022/4/20 这里是按照层级设置的，只取出最后一个即可
                String checkTwoOrgCode = orgDevelopRightsByCodeList.get(orgDevelopRightsByCodeList.size() - CommonConstant.ONE_INT).getOrgCode();

                //todo 修复从顶层节点直接转出到其他层级导致左侧无法显示的问题
                if (!checkTwoOrgCode.equals(srcOrgId)){
                    list.set(list.size() - 2, checkTwoOrgCode);
                }
            }
            transferRecord.setSrcOrgRelationRelAsList(list);
            if (srcParentOrg.size() == 2) {
                List<String> list1 = Arrays.asList(list.get(list.size() - 2), list.get(list.size() - 1));
                transferRecord.setSrcOrgRelationAsList(list1);
            } else if (srcParentOrg.size() >= 2) {
                transferRecord.setSrcOrgRelationAsList(Arrays.asList(list.get(0), list.get(list.size() - 2), list.get(list.size() - 1)));
            } else {
                transferRecord.setSrcOrgRelationAsList(list);
            }
            // TODO: 2021/12/30 如果是整建制转接， 只需要双方同意即可
            if (isOrg) {
                if (list.size() >= 2) {
                    transferRecord.setSrcOrgRelationAsList(Arrays.asList(list.get(0), list.get(list.size() - 1)));
                }
            }
        }

        //转出到系统外
        if (outType.matches(TransferRecordConstant.SYSTEM_OUT_OUT_REX)) {
            transferRecord.setTargetOrgRelationAsList(new ArrayList<>());
        } else {
            //非系统外转出,则目标组织id不能为空
            if (StrKit.isBlank(targetOrgId)) {
                return Ret.fail(RetConstant.MESSAGE, new OutMessage<>(Status.TRANSFER_TARGET_ORG_IS_NOT_EXIST));
            }
            // TODO: 2021/11/14 原来是本地组织，所以有上级,拆分为多个数据节点后，本地库可能不存在，为空则代表是非本地库
            Org orgServiceOrgByCode = orgService.findOrgByCode(targetOrgId);
            List<Org> targetParentOrg = new ArrayList<>();
            if (ObjectUtil.isNotNull(orgServiceOrgByCode)) {
                targetParentOrg = orgService.findAllParentOrg(targetOrgId);
                if (targetParentOrg == null || targetParentOrg.isEmpty()) {
                    return Ret.fail(RetConstant.MESSAGE, new OutMessage<>(Status.TRANSFER_TARGET_ORG_IS_NOT_EXIST));
                }
                boolean isMatch = targetParentOrg.stream().anyMatch(org -> StrKit.isBlank(org.getParentCode()));
                if (isMatch) {
                    return Ret.fail(RetConstant.MESSAGE, new OutMessage<>(Status.TRANSFER_TARGET_ORG_IS_NOT_EXIST));
                }
            } else {
                // TODO: 2021/11/14 这里走数据中间交换区，并且要给出中间交换区顶层节点
                String replaceUrl = sync_flow_push.replace("/api/flow/push", "");
                JSONObject postJson = new JSONObject();
                postJson.put("orgCode", targetOrgId);
                String res = HttpKit.doPost(replaceUrl + "/org/findOrgByIdAsAprove", postJson, "UTF-8");
                if (StrKit.notBlank(res)) {
                    JSONObject jsonObject = JSONObject.parseObject(res);
                    if (jsonObject.containsKey("data")) {
                        JSONObject data = jsonObject.getJSONObject("data");
                        if (ObjectUtil.isNotNull(data)) {
                            //处理跨数据节点转接顶层共同节点为贵州省
                            Org org1 = new Org();
                            org1.setCode("5048C51OE8B74ACF891A1EE5143F85A7");
                            //处理次一级节点
                            Org org2 = new Org();
                            String parentCode = data.getString("parentCode");
                            org2.setCode(parentCode);
                            //处理最后节点
                            Org org3 = new Org();
                            org3.setCode(targetOrgId);
                            targetParentOrg.add(org1);
                            targetParentOrg.add(org2);
                            targetParentOrg.add(org3);
                        } else {
                            return Ret.fail(RetConstant.MESSAGE, new OutMessage<>(Status.TRANSFER_TARGET_ORG_IS_NOT_EXIST_APPROVE));
                        }
                    } else {
                        return Ret.fail(RetConstant.MESSAGE, new OutMessage<>(Status.TRANSFER_TARGET_ORG_IS_NOT_EXIST_APPROVE));
                    }
                } else {
                    return Ret.fail(RetConstant.MESSAGE, new OutMessage<>(Status.TRANSFER_TARGET_ORG_IS_NOT_EXIST_APPROVE));
                }
            }
            //如果查询出的上级包含空的parentCode
            //则认为该组织被认为脱节组织关系
            // 20210927 改为只需要直属上级审核就行了
            List<String> list = targetParentOrg.stream().map(Org::getCode).collect(Collectors.toList());
            transferRecord.setTargetOrgRelationRelAsList(list);
            if (targetParentOrg.size() == 2) {
                transferRecord.setTargetOrgRelationAsList(Arrays.asList(list.get(list.size() - 2), list.get(list.size() - 1)));
            } else if (targetParentOrg.size() >= 2) {
                transferRecord.setTargetOrgRelationAsList(Arrays.asList(list.get(0), list.get(list.size() - 2), list.get(list.size() - 1)));
            } else {
                transferRecord.setTargetOrgRelationAsList(list);
            }
            // TODO: 2021/12/30 如果是整建制转接， 只需要双方同意即可
            if (isOrg) {
                // TODO: 2021/12/30 如果是整建制转接， 只需要双方同意即可
                if (list.size() >= 2) {
                    transferRecord.setTargetOrgRelationAsList(Arrays.asList(list.get(0), list.get(list.size() - 1)));
                }
            }

        }

        return Ret.ok();
    }

    /****
     * 如果果转接类型是从系统内转出,则目标组织id允许为空
     * 如果转接类型是从系统外转入,则源组织id允许为空
     * 如果两个id都为空则不允许
     * 如果接收方不是党委则失败
     * @param srcOrgId 源组织id
     * @param targetOrgId 目标组织id
     * @param transferRecord 转接记录
     * @return true 设置成功 false 设置失败 失败原因参数问题
     * */
    Ret setSrcAndTargetOrgId(String srcOrgId, String targetOrgId, TransferRecord transferRecord, boolean isOrg) {
        String inType = transferRecord.getInType();
        String outType = transferRecord.getOutType();
        if (StrKit.isBlank(srcOrgId) && StrKit.isBlank(targetOrgId)) {
            return Ret.fail(RetConstant.MESSAGE, new OutMessage<>(Status.TRANSFER_SRC_OR_TARGET_NULL_ERROR));
        }
        //如果非系统外转入,源组织id不能为空
        if (!inType.matches(TransferRecordConstant.SYSTEM_OUT_IN_REX)) {
            if (StrKit.isBlank(srcOrgId)) {
                return Ret.fail(RetConstant.MESSAGE, new OutMessage<>(Status.TRANSFER_NOT_SYSTEM_OUT_IN_SRC_NULL_ERROR));
            }
        }
        //如果非转出系统外,目标组织id不能为空
        if (!outType.matches(TransferRecordConstant.SYSTEM_OUT_OUT_REX)) {
            if (StrKit.isBlank(targetOrgId)) {
                return Ret.fail(RetConstant.MESSAGE, new OutMessage<>(Status.TRANSFER_NOT_SYSTEM_OUT_OUT_SRC_NULL_ERROR));
            }
        }

        if (StrKit.notBlank(srcOrgId)) {
            Org srcOrg = orgService.findOrgByCode(srcOrgId);
            if (srcOrg == null) {
                return Ret.fail(RetConstant.MESSAGE, new OutMessage<>(Status.TRANSFER_SRC_ORG_IS_NOT_EXIST));
            }
            //获取当前管理的组织id
            String userPermissionOrgCode = UserConstant.USER_CONTEXT.get().getUserRolePermission().getOrgCode();
            //判断是否有权限调整
            if (!srcOrg.getOrgCode().startsWith(userPermissionOrgCode)) {
                return Ret.fail(RetConstant.MESSAGE, new OutMessage<>(Status.PERMISSION_DENIED));
            }

        }

        transferRecord.setSrcOrgId(srcOrgId);
        transferRecord.setTargetOrgId(targetOrgId);
        return Ret.ok();
    }

    /***
     * 如果发起的请求是系统外转入
     * 那么公共节点为目标组织的最高节点
     * 如果发起的请求是转出到系统外
     * 那么公共节点为源组织的最高节点
     * 不能转入到自己的下级去
     * 设置公共节点id
     * @param srcOrgId 源组织id
     * @param targetOrgId 目标组织id
     * @param transferRecord 转接记录
     * */
    Ret setCommonNodeId(String srcOrgId, String targetOrgId, TransferRecord transferRecord) {
        Org srcOrgRecord = orgService.findOrgByCode(srcOrgId);
        Org targetOrgRecord = orgService.findOrgByCode(targetOrgId);
        if (srcOrgRecord == null && targetOrgRecord == null) {
            return Ret.fail(RetConstant.MESSAGE, new OutMessage<>(Status.PARA_ERROR));
        }
        if (srcOrgRecord == null && transferRecord == null) {
            return Ret.fail(RetConstant.MESSAGE, new OutMessage<>(Status.PARA_ERROR));
        }
        //系统外转入
        if (srcOrgRecord == null) {
            return setTopCode(targetOrgRecord, transferRecord);
        }
        //转出到系统外
        if (targetOrgRecord == null) {
            return setTopCode(srcOrgRecord, transferRecord);
        }

        //系统内转接
        String targetOrgCode = targetOrgRecord.getOrgCode();
        if (StrKit.isBlank(targetOrgCode)) {
            return Ret.fail(RetConstant.MESSAGE, new OutMessage<>(Status.TRANSFER_TARGET_ORG_IS_NOT_EXIST));
        }
        Matcher matcher = ORG_REGEX.matcher(srcOrgRecord.getOrgCode());
        //生成公共节点 现在只能是公共最高节点
        String commonOrgCode = "";
        int i = 0;
        Org commonOrg = null;
        while (true) {
            matcher.find();
            commonOrgCode += matcher.group();
            commonOrg = orgService.findOrgByOrgCode(commonOrgCode);
            if (ObjectUtil.isNotNull(commonOrg)) {
                break;
            }
            i++;
            if (i > 19) {
                break;
            }
        }
//        while (matcher.find()) {
//            String group = matcher.group();
//            boolean contains = targetOrgCode.contains(commonOrgCode + group);
//            if (contains) {
//                commonOrgCode += group;
//            } else {
//                break;
//            }
//        }
        if (StrKit.isBlank(commonOrgCode)) {
            return Ret.fail(RetConstant.MESSAGE, new OutMessage<>(Status.PARA_ERROR));
        }

        if (commonOrg == null) {
            return Ret.fail(RetConstant.MESSAGE, new OutMessage<>(Status.PARA_ERROR));
        }
        //如果公共节点等于目标节点
        //分为两种情况
        //1 目标节点为顶级节点 则 公共节点为 顶级节点
        //2 目标节点为源节点上级
        if (commonOrgCode.equalsIgnoreCase(targetOrgCode)) {
            boolean isSubOrg = srcOrgRecord.getOrgCode().startsWith(targetOrgRecord.getOrgCode());
            if (isSubOrg) {
                //判断是否是顶级节点
                //目标组织关系里面,如果只有一条那么就是组织它本身
                //经过前面的参数校验,脱节的组织是不可能存在集合里面
                //所以目标组织是顶级节点
                int size = transferRecord.getTargetOrgRelationAsList().size();
                if (size != 1) {
                    commonOrg.setCode(targetOrgRecord.getParentCode());
                }
            }
        }
        //判断不能转到自己的下级去
        if (targetOrgRecord.getOrgCode().startsWith(srcOrgRecord.getOrgCode())) {
            return Ret.fail(RetConstant.MESSAGE, new OutMessage<>(Status.TRANSFER_CHANGE_TARGET_FOR_SRC_ERROR));
        }

        transferRecord.setCommonOrgId(commonOrg.getCode());
        transferRecord.setCommonOrgName(commonOrg.getName());
        return Ret.ok();
    }

    /***
     * @param org 组织对象
     * @param transferRecord 转接对接
     * @return true 设置成功 false 设置失败  失败原因 数据不对
     * */
    private Ret setTopCode(Org org, TransferRecord transferRecord) {
        String orgCode = org.getOrgCode();
        if (StrKit.isBlank(orgCode)) {
            return Ret.fail(RetConstant.MESSAGE, new OutMessage<>(Status.PARA_ERROR));
        }
        Matcher matcher = ORG_REGEX.matcher(orgCode);
        if (!matcher.find()) {
            return Ret.fail(RetConstant.MESSAGE, new OutMessage<>(Status.PARA_ERROR));
        }
        //顶层节点
        String topCode = matcher.group();
        if (StrKit.isBlank(topCode)) {
            return Ret.fail(RetConstant.MESSAGE, new OutMessage<>(Status.PARA_ERROR));
        }
        Org topOrg = orgService.findOrgByOrgCode(topCode);
        // TODO: 2021/11/14  数据库未找到顶层节点，代表可能是从中间交换区走得，中间交换区给出默认节点,并且把转出节点得最高节点替换为这个
        if (topOrg == null) {
            List<String> srcOrgRelationAsList = transferRecord.getSrcOrgRelationAsList();
            if (transferRecord.getType().equals("224") && srcOrgRelationAsList.size() == CommonConstant.TWO_INT) {
                List<String> newsrcOrgRelationAsList = new ArrayList<>();
                topOrg = new Org();
                topOrg.setCode("5048C51OE8B74ACF891A1EE5143F85A7");
                topOrg.setName("中共贵州省委员会");
                newsrcOrgRelationAsList.add(CommonConstant.ZERO_INT, "5048C51OE8B74ACF891A1EE5143F85A7");
                newsrcOrgRelationAsList.addAll(srcOrgRelationAsList);
                transferRecord.setSrcOrgRelationAsList(newsrcOrgRelationAsList);
            }else if (transferRecord.getType().equals("223")){
                // TODO: 2023/4/26 增加转出到省委未接入全国交换区的，需要县委节点审核
                //获取具有县委审核的节点
                List<Org> srcParentOrg = orgService.findAllParentOrg(transferRecord.getSrcOrgId());
                List<String> list = srcParentOrg.stream().map(Org::getCode).collect(Collectors.toList());
                List<UserRolePermission> byIndexValue = userRolePermissionService.findByIndexValue(95, CommonConstant.ONE, list);
                String orgId ;
                if ((byIndexValue.size()==CommonConstant.ZERO_INT)){
                    //没有权限，默认为顶层
                    Org byOrgCode = orgService.findByOrgCode(baseOrgOrgCode);
                    if (ObjectUtil.isNull(byOrgCode)){
                        return Ret.fail(RetConstant.MESSAGE, new OutMessage<>(Status.NOT_IS_ADMINORG));
                    }else {
                        orgId=byOrgCode.getCode();
                    }
                }else {
                    orgId = byIndexValue.get(byIndexValue.size() - CommonConstant.ONE_INT).getOrgId();
                }
                //新增顶层具有县委审核节点
                // TODO: 2023/4/26 这里有一种情况，如果最近具有关系转接审核权限的党组织就是具有预备党员审批权限，就只能生成一个审核节点
                List<String> newsrcOrgRelationAsList=new ArrayList<>();
                //顶层节点
                topOrg = new Org();
                topOrg.setCode("5048C51OE8B74ACF891A1EE5143F85A7");
                topOrg.setName("中共贵州省委员会");
                newsrcOrgRelationAsList.add(CommonConstant.ZERO_INT, "5048C51OE8B74ACF891A1EE5143F85A7");
                //县委节点
                if (srcOrgRelationAsList.size()<=CommonConstant.TWO_INT&&!srcOrgRelationAsList.contains(orgId)){
                    newsrcOrgRelationAsList.add(orgId);
                }
                if (srcOrgRelationAsList.size()>CommonConstant.TWO_INT){
                    String  threeOrg= srcOrgRelationAsList.get(srcOrgRelationAsList.size() - CommonConstant.TWO_INT);
                    String  twoOrg= srcOrgRelationAsList.get(srcOrgRelationAsList.size() - CommonConstant.ONE_INT);
                    if (!StrUtil.equalsAny(orgId,threeOrg,twoOrg)){
                        newsrcOrgRelationAsList.add(orgId);
                    }
                }
                //放置后续节点相关东西
                if (srcOrgRelationAsList.size()==CommonConstant.TWO_INT){
                    newsrcOrgRelationAsList.addAll(srcOrgRelationAsList);
                } else {
                    newsrcOrgRelationAsList.add(srcOrgRelationAsList.get(srcOrgRelationAsList.size()-CommonConstant.TWO_INT));
                    newsrcOrgRelationAsList.add(srcOrgRelationAsList.get(srcOrgRelationAsList.size()-CommonConstant.ONE_INT));
                }
                transferRecord.setSrcOrgRelationAsList(newsrcOrgRelationAsList);
            } else {
                topOrg = new Org();
                topOrg.setCode("5048C51OE8B74ACF891A1EE5143F85A7");
                topOrg.setName("中共贵州省委员会");
                srcOrgRelationAsList.set(CommonConstant.ZERO_INT, "5048C51OE8B74ACF891A1EE5143F85A7");
                transferRecord.setSrcOrgRelationAsList(srcOrgRelationAsList);
            }
        }
        transferRecord.setCommonOrgId(topOrg.getCode());
        transferRecord.setCommonOrgName(topOrg.getName());
        return Ret.ok();
    }

    /***
     * 判断源组织是否有上级组织正在进行关系转接 或者 下级组织转接中 或者 下级人员转接中
     * */
    public List<TransferRecord> srcExistParentsOrgIsTransfer(String srcOrgId) {
        LambdaQueryWrapper<TransferRecord> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper
                .eq(TransferRecord::getStatus, TransferRecordConstant.TRANSFERING)
                .apply(" (\"src_org_id\" IN ( SELECT \"code\" FROM find_relation_up ( '" + srcOrgId + "' ) ) OR ( src_org_relation :: jsonb ?? {0} ) ) ", srcOrgId);
        return list(lambdaQueryWrapper);
    }

    /***
     * 判断目标组织和上级组织是否正在转接
     * */
    public List<TransferRecord> targetExistParentsOrgIsTransfer(String targetOrgId) {
        LambdaQueryWrapper<TransferRecord> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.isNull(TransferRecord::getMemId)
                .eq(TransferRecord::getStatus, TransferRecordConstant.TRANSFERING)
                .eq(TransferRecord::getType,"212")
                .apply("target_org_relation :: jsonb ?? {0}", targetOrgId);
        return list(lambdaQueryWrapper);
    }

    protected boolean rewriteSave(TransferRecord transferRecord) {
        return save(transferRecord);
    }

    /**
     * 创建一条审批记录 针对于发起第一次审批时
     * 判断发起转接的类型
     * 如果是系统外转入 则从目标组织这侧开始
     *
     * @param record 转接记录
     * @return 审批记录
     */
    TransferApproval createOrgTransferApproval(TransferRecord record) {
        TransferApproval orgTransferApproval = new TransferApproval();
        //用户id
        orgTransferApproval.setUserId(UserConstant.USER_CONTEXT.get().getUser().getId());
        String memName = userService.findMemNameByUserId(orgTransferApproval.getUserId());
        orgTransferApproval.setHandlerMan(memName);
        //是否代审
        orgTransferApproval.setIsInstead(TransferApprovalConstant.NOT_INSTEAD);
        //父id
        orgTransferApproval.setParentId(TransferApprovalConstant.ROOT_ID);
        //状态
        orgTransferApproval.setStatus(TransferApprovalConstant.APPROVAL_SUCCESS);
        //创建时间
        orgTransferApproval.setCreateTime(new Date());
        //更新时间
        orgTransferApproval.setUpdateTime(new Date());

        String type = record.getType();
        //判断是否是系统外转入
        if (type.matches(TransferRecordConstant.SYSTEM_OUT_IN_REX)) {
            orgTransferApproval.setOrgId(record.getCommonOrgId());
            List<String> relationAsList = record.getTargetOrgRelationAsList();
            int index = relationAsList.indexOf(record.getCommonOrgId());
            //判断下个组织id 是否存在
            if (index + 1 < relationAsList.size()) {
                orgTransferApproval.setNextOrgId(relationAsList.get(index + 1));
            } else {
                orgTransferApproval.setNextOrgId("");
            }
            orgTransferApproval.setDirection(TransferApprovalConstant.DIRECTION_TARGET);
            return orgTransferApproval;
        }

        orgTransferApproval.setOrgId(record.getSrcOrgId());
        List<String> srcOrgrelationAsList = record.getSrcOrgRelationAsList();
        int index = srcOrgrelationAsList.indexOf(record.getSrcOrgId());
        orgTransferApproval.setDirection(TransferApprovalConstant.DIRECTION_SRC);
        //判断下个组织id 是否存在
//        if (index - 1 >= 0 && index - 1 < relationAsList.size()) {
//            orgTransferApproval.setNextOrgId(relationAsList.get(index - 1));
        //  todo: srcOrgRelation字段存在公共节点顺序不固定情况，比如（052001113）下的两个整建制（id=“81e104a8115ab824bd25db86fa10a8dd”， “7d21e9e7f57c886fbe4723019c44c3ea”）
        // 如果index为0，说明公共节点在relationAsList的最后下标。 index大于0，则公共节点有可能在最前面。
        if ((index == 0 && srcOrgrelationAsList.size() > 1) || index > 0) {
            orgTransferApproval.setNextOrgId(srcOrgrelationAsList.get(index == 0 ? index + 1 : index - 1));
            String nextOrgId = orgTransferApproval.getNextOrgId();
            //如果下一个节点是公共节点则创建一条公共节点记录
            if (StrKit.equals(nextOrgId, record.getCommonOrgId())) {
                orgTransferApproval.setRecordId(record.getId());
                transferApprovalService.rewriteSave(orgTransferApproval);
                TransferLog transferLog = transferLogService.createOrgTransferLog();
                transferLog.setHandleApprovalId(orgTransferApproval.getId());
                transferLog.setReason(TransferLogConstant.PUSH_MEM_TRANSFER_REASON);
                transferLogService.rewriteSave(transferLog);
                TransferApproval commonTransferApproval = new TransferApproval();
                commonTransferApproval.setOrgId(record.getCommonOrgId());
                //用户id
                commonTransferApproval.setUserId(UserConstant.USER_CONTEXT.get().getUser().getId());
                commonTransferApproval.setHandlerMan(memName);
                //是否代审
                commonTransferApproval.setIsInstead(TransferApprovalConstant.NOT_INSTEAD);
                //父id
                commonTransferApproval.setParentId(orgTransferApproval.getId());
                //状态
                commonTransferApproval.setStatus(TransferApprovalConstant.APPROVAL_SUCCESS);
                //创建时间
                commonTransferApproval.setCreateTime(new Date());
                //更新时间
                commonTransferApproval.setUpdateTime(new Date());
                commonTransferApproval.setDirection(TransferApprovalConstant.DIRECTION_COMMON);
                List<String> targetOrgRelationAsList = record.getTargetOrgRelationAsList();
                index = targetOrgRelationAsList.indexOf(record.getCommonOrgId());
                //判断下个组织id 是否存在
                if (index + 1 < targetOrgRelationAsList.size()) {
                    commonTransferApproval.setNextOrgId(targetOrgRelationAsList.get(index + 1));
                }
                //todo 这行代码是用来兼容整建制右边仅生成了一个， 但是没有生成下一个nextId问题
                else if(targetOrgRelationAsList.size()==CommonConstant.ONE_INT){
                    commonTransferApproval.setNextOrgId(targetOrgRelationAsList.get(CommonConstant.ZERO_INT));
                }
                else {
                    commonTransferApproval.setNextOrgId("");
                }
                return commonTransferApproval;
            }

        } else {
            orgTransferApproval.setNextOrgId("");
        }
        return orgTransferApproval;
    }

    private List<Object> checkDta(JSONArray jsonArray, Class clazz) {
        List<Object> returnList = new ArrayList<>();
        for (Object jsonObject : jsonArray) {
            JSONObject object = (JSONObject) jsonObject;
            // TODO: 2024/3/28 因为使用得是id为主键，所以不能移除 
            if (!clazz.isInstance(User.class)||!clazz.isInstance(UserRolePermission.class)){
                object.remove("id");
            }
            returnList.add(JSONObject.toJavaObject(object, clazz));
        }
        return returnList;
    }

    @Transactional
    boolean transferCrossNode(TransferRecord record) {
        //整建制数据入库
        String dataText = record.getDataText();
        //如果传入数据为空，啥也没有，证明数据有问题，转接失败
        if (StrKit.isBlank(dataText)) {
            log.error("跨节点数据中dataText没有任何数据");
            return false;
        }
        JSONObject jsonDataObject = JSONObject.parseObject(dataText);
        //数据入库
        JSONArray ccp_unit = jsonDataObject.getJSONArray("ccp_unit");
        if (ObjectUtil.isNotNull(ccp_unit) && ccp_unit.size() > 0) {
            List<Unit> objectList = (List) this.checkDta(ccp_unit, Unit.class);
            List<String> collect = objectList.stream().map(Unit::getCode).collect(Collectors.toList());
            iUnitService.getBaseMapper().delete(new QueryWrapper<Unit>().lambda().in(Unit::getCode, collect));
            //剔除吊自增长id，防止自增长id出现错误
            objectList.forEach(unit -> unit.setId(null));
            boolean saveUnit = iUnitService.saveBatch(objectList);
            System.out.println("转换后得对象情况ccp_unit数量以及入库情况======>" + objectList.size() + saveUnit);
        }
        JSONArray ccp_unit_all = jsonDataObject.getJSONArray("ccp_unit_all");
        if (ObjectUtil.isNotNull(ccp_unit_all) && ccp_unit_all.size() > 0) {
            List<UnitAll> objectList = (List) this.checkDta(ccp_unit_all, UnitAll.class);
            List<String> collect = objectList.stream().map(UnitAll::getCode).collect(Collectors.toList());
            iUnitAllService.getBaseMapper().delete(new QueryWrapper<UnitAll>().lambda().in(UnitAll::getCode, collect));
            boolean saveUnit = iUnitAllService.saveBatch(objectList);
            System.out.println("转换后得对象情况ccp_unit_all数量以及入库情况======>" + objectList.size() + saveUnit);
        }

        JSONArray ccp_unit_city_situation = jsonDataObject.getJSONArray("ccp_unit_city_situation");
        if (ObjectUtil.isNotNull(ccp_unit_city_situation) && ccp_unit_city_situation.size() > 0) {
            List<UnitCitySituation> objectList = (List) this.checkDta(ccp_unit_city_situation, UnitCitySituation.class);
            List<String> collect = objectList.stream().map(UnitCitySituation::getCode).collect(Collectors.toList());
            unitCitySituationService.getBaseMapper().delete(new QueryWrapper<UnitCitySituation>().lambda().in(UnitCitySituation::getCode, collect));
            boolean saveUnit = unitCitySituationService.saveBatch(objectList);
            System.out.println("转换后得对象情况ccp_unit_city_situation数量以及入库情况======>" + objectList.size() + saveUnit);
        }

        JSONArray ccp_unit_committee_elect = jsonDataObject.getJSONArray("ccp_unit_committee_elect");
        if (ObjectUtil.isNotNull(ccp_unit_committee_elect) && ccp_unit_committee_elect.size() > 0) {
            List<UnitCommitteeElect> objectList = (List) this.checkDta(ccp_unit_committee_elect, UnitCommitteeElect.class);
            List<String> collect = objectList.stream().map(UnitCommitteeElect::getCode).collect(Collectors.toList());
            iUnitCommitteeElectService.getBaseMapper().delete(new QueryWrapper<UnitCommitteeElect>().lambda().in(UnitCommitteeElect::getCode, collect));
            objectList.forEach(unitCommitteeElect -> unitCommitteeElect.setId(null));
            boolean saveUnit = iUnitCommitteeElectService.saveBatch(objectList);
            System.out.println("转换后得对象情况ccp_unit_committee_elect数量以及入库情况======>" + objectList.size() + saveUnit);
        }
        JSONArray ccp_unit_committee = jsonDataObject.getJSONArray("ccp_unit_committee");
        if (ObjectUtil.isNotNull(ccp_unit_committee) && ccp_unit_committee.size() > 0) {
            List<UnitCommittee> objectList = (List) this.checkDta(ccp_unit_committee, UnitCommittee.class);
            List<String> collect = objectList.stream().map(UnitCommittee::getCode).collect(Collectors.toList());
            iUnitCommitteeService.getBaseMapper().delete(new QueryWrapper<UnitCommittee>().lambda().in(UnitCommittee::getCode, collect));
            objectList.forEach(unitCommittee -> unitCommittee.setId(null));
            boolean saveUnit = iUnitCommitteeService.saveBatch(objectList);
            System.out.println("转换后得对象情况ccp_unit_committee数量以及入库情况======>" + objectList.size() + saveUnit);
        }
        JSONArray ccp_unit_community = jsonDataObject.getJSONArray("ccp_unit_community");
        if (ObjectUtil.isNotNull(ccp_unit_community) && ccp_unit_community.size() > 0) {
            List<UnitCommunity> objectList = (List) this.checkDta(ccp_unit_community, UnitCommunity.class);
            List<String> collect = objectList.stream().map(UnitCommunity::getCode).collect(Collectors.toList());
            iUnitCommunityService.getBaseMapper().delete(new QueryWrapper<UnitCommunity>().lambda().in(UnitCommunity::getCode, collect));
            objectList.forEach(unitCommunity -> unitCommunity.setId(null));
            boolean saveUnit = iUnitCommunityService.saveBatch(objectList);
            System.out.println("转换后得对象情况ccp_unit_community数量以及入库情况======>" + objectList.size() + saveUnit);
        }
        JSONArray ccp_unit_countryside = jsonDataObject.getJSONArray("ccp_unit_countryside");
        if (ObjectUtil.isNotNull(ccp_unit_countryside) && ccp_unit_countryside.size() > 0) {
            List<UnitCountryside> objectList = (List) this.checkDta(ccp_unit_countryside, UnitCountryside.class);
            List<String> collect = objectList.stream().map(UnitCountryside::getCode).collect(Collectors.toList());
            iUnitCountrusideService.getBaseMapper().delete(new QueryWrapper<UnitCountryside>().lambda().in(UnitCountryside::getCode, collect));
            objectList.forEach(unitCountryside -> unitCountryside.setId(null));
            boolean saveUnit = iUnitCountrusideService.saveBatch(objectList);
            System.out.println("转换后得对象情况ccp_unit_countryside数量以及入库情况======>" + objectList.size() + saveUnit);
        }
        JSONArray ccp_unit_extend = jsonDataObject.getJSONArray("ccp_unit_extend");
        if (ObjectUtil.isNotNull(ccp_unit_extend) && ccp_unit_extend.size() > 0) {
            List<UnitExtend> objectList = (List) this.checkDta(ccp_unit_extend, UnitExtend.class);
            List<String> collect = objectList.stream().map(UnitExtend::getCode).collect(Collectors.toList());
            iUnitExtendService.getBaseMapper().delete(new QueryWrapper<UnitExtend>().lambda().in(UnitExtend::getCode, collect));
            objectList.forEach(unitExtend -> unitExtend.setId(null));
            boolean saveUnit = iUnitExtendService.saveBatch(objectList);
            System.out.println("转换后得对象情况ccp_unit_extend数量以及入库情况======>" + objectList.size() + saveUnit);
        }

        JSONArray ccp_unit_org_linked = jsonDataObject.getJSONArray("ccp_unit_org_linked");
        if (ObjectUtil.isNotNull(ccp_unit_org_linked) && ccp_unit_org_linked.size() > 0) {
            List<UnitOrgLinked> objectList = (List) this.checkDta(ccp_unit_org_linked, UnitOrgLinked.class);
            List<String> collect = objectList.stream().map(UnitOrgLinked::getCode).collect(Collectors.toList());
            iUnitOrgLinkedService.getBaseMapper().delete(new QueryWrapper<UnitOrgLinked>().lambda().in(UnitOrgLinked::getCode, collect));
            objectList.forEach(unitOrgLinked -> unitOrgLinked.setId(null));
            boolean saveUnit = iUnitOrgLinkedService.saveBatch(objectList);
            System.out.println("转换后得对象情况ccp_unit_org_linked数量以及入库情况======>" + objectList.size() + saveUnit);
        }
        JSONArray ccp_unit_secondary = jsonDataObject.getJSONArray("ccp_unit_secondary");
        if (ObjectUtil.isNotNull(ccp_unit_secondary) && ccp_unit_secondary.size() > 0) {
            List<UnitSecondary> objectList = (List) this.checkDta(ccp_unit_secondary, UnitSecondary.class);

            List<String> collect = objectList.stream().map(UnitSecondary::getCode).collect(Collectors.toList());
            iUnitSecondaryService.getBaseMapper().delete(new QueryWrapper<UnitSecondary>().lambda().in(UnitSecondary::getCode, collect));
            objectList.forEach(unitSecondary -> unitSecondary.setId(null));
            boolean saveUnit = iUnitSecondaryService.saveBatch(objectList);
            System.out.println("转换后得对象情况ccp_unit_secondary数量以及入库情况======>" + objectList.size() + saveUnit);
        }
        JSONArray ccp_unit_site_conditions = jsonDataObject.getJSONArray("ccp_unit_site_conditions");
        if (ObjectUtil.isNotNull(ccp_unit_site_conditions) && ccp_unit_site_conditions.size() > 0) {
            List<UnitSiteConditions> objectList = (List) this.checkDta(ccp_unit_site_conditions, UnitSiteConditions.class);

            List<String> collect = objectList.stream().map(UnitSiteConditions::getCode).collect(Collectors.toList());
            iUnitSiteConditionsService.getBaseMapper().delete(new QueryWrapper<UnitSiteConditions>().lambda().in(UnitSiteConditions::getCode, collect));
            boolean saveUnit = iUnitSiteConditionsService.saveBatch(objectList);
            System.out.println("转换后得对象情况ccp_unit_site_conditions数量以及入库情况======>" + objectList.size() + saveUnit);
        }
        JSONArray ccp_unit_streets_cadres = jsonDataObject.getJSONArray("ccp_unit_streets_cadres");
        if (ObjectUtil.isNotNull(ccp_unit_streets_cadres) && ccp_unit_streets_cadres.size() > 0) {
            List<UnitStreetsCadres> objectList = (List) this.checkDta(ccp_unit_streets_cadres, UnitStreetsCadres.class);

            List<String> collect = objectList.stream().map(UnitStreetsCadres::getCode).collect(Collectors.toList());
            iUnitStreetsCadresService.getBaseMapper().delete(new QueryWrapper<UnitStreetsCadres>().lambda().in(UnitStreetsCadres::getCode, collect));
            boolean saveUnit = iUnitStreetsCadresService.saveBatch(objectList);
            System.out.println("转换后得对象情况ccp_unit_streets_cadres数量以及入库情况======>" + objectList.size() + saveUnit);
        }
        JSONArray ccp_unit_collective_economic = jsonDataObject.getJSONArray("ccp_unit_collective_economic");
        if (ObjectUtil.isNotNull(ccp_unit_collective_economic) && ccp_unit_collective_economic.size() > 0) {
            List<UnitCollectiveEconomic> objectList = (List) this.checkDta(ccp_unit_collective_economic, UnitCollectiveEconomic.class);

            List<String> collect = objectList.stream().map(UnitCollectiveEconomic::getCode).collect(Collectors.toList());
            iUnitCollectiveEconomicService.getBaseMapper().delete(new QueryWrapper<UnitCollectiveEconomic>().lambda().in(UnitCollectiveEconomic::getCode, collect));
            objectList.forEach(unitCollectiveEconomic -> unitCollectiveEconomic.setId(null));
            boolean saveUnit = iUnitCollectiveEconomicService.saveBatch(objectList);
            System.out.println("转换后得对象情况ccp_unit_collective_economic数量以及入库情况======>" + objectList.size() + saveUnit);
        }
        JSONArray ccp_unit_income = jsonDataObject.getJSONArray("ccp_unit_income");
        if (ObjectUtil.isNotNull(ccp_unit_income) && ccp_unit_income.size() > 0) {
            List<UnitIncome> objectList = (List) this.checkDta(ccp_unit_income, UnitIncome.class);

            List<String> collect = objectList.stream().map(UnitIncome::getCode).collect(Collectors.toList());
            iUnitIncomeService.getBaseMapper().delete(new QueryWrapper<UnitIncome>().lambda().in(UnitIncome::getCode, collect));
            objectList.forEach(unitIncome -> unitIncome.setId(null));
            boolean saveUnit = iUnitIncomeService.saveBatch(objectList);
            System.out.println("转换后得对象情况ccp_unit_income数量以及入库情况======>" + objectList.size() + saveUnit);
        }

        JSONArray ccp_org = jsonDataObject.getJSONArray("ccp_org");
        if (ObjectUtil.isNotNull(ccp_org) && ccp_org.size() > 0) {
            List<Org> orgList = (List) this.checkDta(ccp_org, Org.class);

            List<String> collect = orgList.stream().map(Org::getCode).collect(Collectors.toList());
            iOrgService.getBaseMapper().delete(new QueryWrapper<Org>().lambda().in(Org::getCode, collect));
            orgList.forEach(org -> org.setId(null));
            boolean saveUnit = iOrgService.saveBatch(orgList);
            System.out.println("转换后得对象情况ccp_org数量以及入库情况======>" + orgList.size() + saveUnit);
        }

        JSONArray ccp_org_all = jsonDataObject.getJSONArray("ccp_org_all");
        if (ObjectUtil.isNotNull(ccp_org_all) && ccp_org_all.size() > 0) {
            List<OrgAll> objectList = (List) this.checkDta(ccp_org_all, OrgAll.class);

            List<String> collect = objectList.stream().map(OrgAll::getCode).collect(Collectors.toList());
            orgAllService.getBaseMapper().delete(new QueryWrapper<OrgAll>().lambda().in(OrgAll::getCode, collect));
            boolean saveUnit = orgAllService.saveBatch(objectList);
            System.out.println("转换后得对象情况ccp_org_all数量以及入库情况======>" + objectList.size() + saveUnit);
        }

        JSONArray ccp_org_assess = jsonDataObject.getJSONArray("ccp_org_assess");
        if (ObjectUtil.isNotNull(ccp_org_assess) && ccp_org_assess.size() > 0) {
            List<OrgAssess> objectList = (List) this.checkDta(ccp_org_assess, OrgAssess.class);

            List<String> collect = objectList.stream().map(OrgAssess::getCode).collect(Collectors.toList());
            iOrgAssessService.getBaseMapper().delete(new QueryWrapper<OrgAssess>().lambda().in(OrgAssess::getCode, collect));
            boolean saveUnit = iOrgAssessService.saveBatch(objectList);
            System.out.println("转换后得对象情况ccp_org_assess数量以及入库情况======>" + objectList.size() + saveUnit);
        }
        JSONArray ccp_org_caucus = jsonDataObject.getJSONArray("ccp_org_caucus");
        if (ObjectUtil.isNotNull(ccp_org_caucus) && ccp_org_caucus.size() > 0) {
            List<OrgCaucus> objectList = (List) this.checkDta(ccp_org_caucus, OrgCaucus.class);

            List<String> collect = objectList.stream().map(OrgCaucus::getCode).collect(Collectors.toList());
            iOrgCaucusService.getBaseMapper().delete(new QueryWrapper<OrgCaucus>().lambda().in(OrgCaucus::getCode, collect));
            boolean saveUnit = iOrgCaucusService.saveBatch(objectList);
            System.out.println("转换后得对象情况ccp_org_caucus数量以及入库情况======>" + objectList.size() + saveUnit);
        }
        JSONArray ccp_org_committee_elect = jsonDataObject.getJSONArray("ccp_org_committee_elect");
        if (ObjectUtil.isNotNull(ccp_org_committee_elect) && ccp_org_committee_elect.size() > 0) {
            List<OrgCommitteeElect> objectList = (List) this.checkDta(ccp_org_committee_elect, OrgCommitteeElect.class);

            List<String> collect = objectList.stream().map(OrgCommitteeElect::getCode).collect(Collectors.toList());
            iOrgCommitteeElectService.getBaseMapper().delete(new QueryWrapper<OrgCommitteeElect>().lambda().in(OrgCommitteeElect::getCode, collect));
            objectList.forEach(orgCommitteeElect -> orgCommitteeElect.setId(null));
            boolean saveUnit = iOrgCommitteeElectService.saveBatch(objectList);
            System.out.println("转换后得对象情况ccp_org_committee_elect数量以及入库情况======>" + objectList.size() + saveUnit);
        }
        JSONArray ccp_org_committee = jsonDataObject.getJSONArray("ccp_org_committee");
        if (ObjectUtil.isNotNull(ccp_org_committee) && ccp_org_committee.size() > 0) {
            List<OrgCommittee> objectList = (List) this.checkDta(ccp_org_committee, OrgCommittee.class);

            List<String> collect = objectList.stream().map(OrgCommittee::getCode).collect(Collectors.toList());
            iOrgCommitteeService.getBaseMapper().delete(new QueryWrapper<OrgCommittee>().lambda().in(OrgCommittee::getCode, collect));
            objectList.forEach(orgCommittee -> orgCommittee.setId(null));
            boolean saveUnit = iOrgCommitteeService.saveBatch(objectList);
            System.out.println("转换后得对象情况ccp_org_committee数量以及入库情况======>" + objectList.size() + saveUnit);
        }
        JSONArray ccp_org_develop_rights = jsonDataObject.getJSONArray("ccp_org_develop_rights");
        if (ObjectUtil.isNotNull(ccp_org_develop_rights) && ccp_org_develop_rights.size() > 0) {
            List<OrgDevelopRights> objectList = (List) this.checkDta(ccp_org_develop_rights, OrgDevelopRights.class);

            List<String> collect = objectList.stream().map(OrgDevelopRights::getCode).collect(Collectors.toList());
            iOrgDevelopRightsService.getBaseMapper().delete(new QueryWrapper<OrgDevelopRights>().lambda().in(OrgDevelopRights::getCode, collect));
            boolean saveUnit = iOrgDevelopRightsService.saveBatch(objectList);
            System.out.println("转换后得对象情况ccp_org_develop_rights数量以及入库情况======>" + objectList.size() + saveUnit);
        }
        JSONArray ccp_org_extend = jsonDataObject.getJSONArray("ccp_org_extend");
        if (ObjectUtil.isNotNull(ccp_org_extend) && ccp_org_extend.size() > 0) {
            List<OrgExtend> objectList = (List) this.checkDta(ccp_org_extend, OrgExtend.class);

            List<String> collect = objectList.stream().map(OrgExtend::getCode).collect(Collectors.toList());
            iOrgExtendService.getBaseMapper().delete(new QueryWrapper<OrgExtend>().lambda().in(OrgExtend::getCode, collect));
            objectList.forEach(orgExtend -> orgExtend.setId(null));
            boolean saveUnit = iOrgExtendService.saveBatch(objectList);
            System.out.println("转换后得对象情况ccp_org_extend数量以及入库情况======>" + objectList.size() + saveUnit);
        }
        JSONArray ccp_org_group = jsonDataObject.getJSONArray("ccp_org_group");
        if (ObjectUtil.isNotNull(ccp_org_group) && ccp_org_group.size() > 0) {
            List<OrgGroup> objectList = (List) this.checkDta(ccp_org_group, OrgGroup.class);

            List<String> collect = objectList.stream().map(OrgGroup::getCode).collect(Collectors.toList());
            iOrgGroupService.getBaseMapper().delete(new QueryWrapper<OrgGroup>().lambda().in(OrgGroup::getCode, collect));
            objectList.forEach(orgGroup -> orgGroup.setId(null));
            boolean saveUnit = iOrgGroupService.saveBatch(objectList);
            System.out.println("转换后得对象情况ccp_org_group数量以及入库情况======>" + objectList.size() + saveUnit);
        }
        JSONArray ccp_org_group_member = jsonDataObject.getJSONArray("ccp_org_group_member");
        if (ObjectUtil.isNotNull(ccp_org_group_member) && ccp_org_group_member.size() > 0) {
            List<OrgGroupMember> objectList = (List) this.checkDta(ccp_org_group_member, OrgGroupMember.class);

            List<String> collect = objectList.stream().map(OrgGroupMember::getCode).collect(Collectors.toList());
            iOrgGroupMemberService.getBaseMapper().delete(new QueryWrapper<OrgGroupMember>().lambda().in(OrgGroupMember::getCode, collect));
            objectList.forEach(orgGroupMember -> orgGroupMember.setId(null));
            boolean saveUnit = iOrgGroupMemberService.saveBatch(objectList);
            System.out.println("转换后得对象情况ccp_org_group_member数量以及入库情况======>" + objectList.size() + saveUnit);
        }
        JSONArray ccp_org_industry = jsonDataObject.getJSONArray("ccp_org_industry");
        if (ObjectUtil.isNotNull(ccp_org_industry) && ccp_org_industry.size() > 0) {
            List<OrgIndustry> objectList = (List) this.checkDta(ccp_org_industry, OrgIndustry.class);

            List<String> collect = objectList.stream().map(OrgIndustry::getCode).collect(Collectors.toList());
            industryService.getBaseMapper().delete(new QueryWrapper<OrgIndustry>().lambda().in(OrgIndustry::getCode, collect));
            boolean saveUnit = industryService.saveBatch(objectList);
            System.out.println("转换后得对象情况ccp_org_industry数量以及入库情况======>" + objectList.size() + saveUnit);
        }

        JSONArray ccp_org_industry_all = jsonDataObject.getJSONArray("ccp_org_industry_all");
        if (ObjectUtil.isNotNull(ccp_org_industry_all) && ccp_org_industry_all.size() > 0) {
            List<OrgIndustryAll> objectList = (List) this.checkDta(ccp_org_industry_all, OrgIndustryAll.class);

            List<String> collect = objectList.stream().map(OrgIndustryAll::getCode).collect(Collectors.toList());
            iOrgIndustryAllService.getBaseMapper().delete(new QueryWrapper<OrgIndustryAll>().lambda().in(OrgIndustryAll::getCode, collect));
            boolean saveUnit = iOrgIndustryAllService.saveBatch(objectList);
            System.out.println("转换后得对象情况ccp_org_industry_all数量以及入库情况======>" + objectList.size() + saveUnit);
        }

        JSONArray ccp_org_non_public_party = jsonDataObject.getJSONArray("ccp_org_non_public_party");
        if (ObjectUtil.isNotNull(ccp_org_non_public_party) && ccp_org_non_public_party.size() > 0) {
            List<OrgNonPublicParty> objectList = (List) this.checkDta(ccp_org_non_public_party, OrgNonPublicParty.class);

            List<String> collect = objectList.stream().map(OrgNonPublicParty::getCode).collect(Collectors.toList());
            iOrgNonPublicPartyService.getBaseMapper().delete(new QueryWrapper<OrgNonPublicParty>().lambda().in(OrgNonPublicParty::getCode, collect));
            boolean saveUnit = iOrgNonPublicPartyService.saveBatch(objectList);
            System.out.println("转换后得对象情况ccp_org_non_public_party数量以及入库情况======>" + objectList.size() + saveUnit);
        }
        JSONArray ccp_org_party = jsonDataObject.getJSONArray("ccp_org_party");
        if (ObjectUtil.isNotNull(ccp_org_party) && ccp_org_party.size() > 0) {
            List<OrgParty> objectList = (List) this.checkDta(ccp_org_party, OrgParty.class);

            List<String> collect = objectList.stream().map(OrgParty::getCode).collect(Collectors.toList());
            iOrgPartyService.getBaseMapper().delete(new QueryWrapper<OrgParty>().lambda().in(OrgParty::getCode, collect));
            objectList.forEach(orgParty -> orgParty.setId(null));
            boolean saveUnit = iOrgPartyService.saveBatch(objectList);
            System.out.println("转换后得对象情况ccp_org_party数量以及入库情况======>" + objectList.size() + saveUnit);
        }
        JSONArray ccp_org_party_congress_elect = jsonDataObject.getJSONArray("ccp_org_party_congress_elect");
        if (ObjectUtil.isNotNull(ccp_org_party_congress_elect) && ccp_org_party_congress_elect.size() > 0) {
            List<OrgPartyCongressElect> objectList = (List) this.checkDta(ccp_org_party_congress_elect, OrgPartyCongressElect.class);

            List<String> collect = objectList.stream().map(OrgPartyCongressElect::getCode).collect(Collectors.toList());
            iOrgPartyCongressElectService.getBaseMapper().delete(new QueryWrapper<OrgPartyCongressElect>().lambda().in(OrgPartyCongressElect::getCode, collect));
            objectList.forEach(orgPartyCongressElect -> orgPartyCongressElect.setId(null));
            boolean saveUnit = iOrgPartyCongressElectService.saveBatch(objectList);
            System.out.println("转换后得对象情况ccp_org_party_congress_elect数量以及入库情况======>" + objectList.size() + saveUnit);
        }


        JSONArray ccp_org_party_congress_committee_all = jsonDataObject.getJSONArray("ccp_org_party_congress_committee_all");
        if (ObjectUtil.isNotNull(ccp_org_party_congress_committee_all) && ccp_org_party_congress_committee_all.size() > 0) {
            List<OrgPartyCongressCommitteeAll> objectList = (List) this.checkDta(ccp_org_party_congress_committee_all, OrgPartyCongressCommitteeAll.class);

            List<String> collect = objectList.stream().map(OrgPartyCongressCommitteeAll::getCode).collect(Collectors.toList());
            iOrgPartyCongressCommitteeAllService.getBaseMapper().delete(new QueryWrapper<OrgPartyCongressCommitteeAll>().lambda().in(OrgPartyCongressCommitteeAll::getCode, collect));
            objectList.forEach(orgPartyCongressCommittee -> orgPartyCongressCommittee.setId(null));
            boolean saveUnit = iOrgPartyCongressCommitteeAllService.saveBatch(objectList);
            System.out.println("转换后得对象情况ccp_org_party_congress_committee_all数量以及入库情况======>" + objectList.size() + saveUnit);
        }

        JSONArray ccp_org_party_congress_committee = jsonDataObject.getJSONArray("ccp_org_party_congress_committee");
        if (ObjectUtil.isNotNull(ccp_org_party_congress_committee) && ccp_org_party_congress_committee.size() > 0) {
            List<OrgPartyCongressCommittee> objectList = (List) this.checkDta(ccp_org_party_congress_committee, OrgPartyCongressCommittee.class);

            List<String> collect = objectList.stream().map(OrgPartyCongressCommittee::getCode).collect(Collectors.toList());
            iOrgPartyCongressCommitteeService.getBaseMapper().delete(new QueryWrapper<OrgPartyCongressCommittee>().lambda().in(OrgPartyCongressCommittee::getCode, collect));
            objectList.forEach(orgPartyCongressCommittee -> orgPartyCongressCommittee.setId(null));
            boolean saveUnit = iOrgPartyCongressCommitteeService.saveBatch(objectList);
            System.out.println("转换后得对象情况ccp_org_party_congress_committee数量以及入库情况======>" + objectList.size() + saveUnit);
        }
        JSONArray ccp_org_recognition = jsonDataObject.getJSONArray("ccp_org_recognition");
        if (ObjectUtil.isNotNull(ccp_org_recognition) && ccp_org_recognition.size() > 0) {
            List<OrgRecognition> objectList = (List) this.checkDta(ccp_org_recognition, OrgRecognition.class);

            List<String> collect = objectList.stream().map(OrgRecognition::getCode).collect(Collectors.toList());
            iOrgRecognitionService.getBaseMapper().delete(new QueryWrapper<OrgRecognition>().lambda().in(OrgRecognition::getCode, collect));
            objectList.forEach(orgRecognition -> orgRecognition.setId(null));
            boolean saveUnit = iOrgRecognitionService.saveBatch(objectList);
            System.out.println("转换后得对象情况ccp_org_recognition数量以及入库情况======>" + objectList.size() + saveUnit);
        }

        JSONArray ccp_org_recognition_all = jsonDataObject.getJSONArray("ccp_org_recognition_all");
        if (ObjectUtil.isNotNull(ccp_org_recognition_all) && ccp_org_recognition_all.size() > 0) {
            List<OrgRecognitionAll> objectList = (List) this.checkDta(ccp_org_recognition_all, OrgRecognitionAll.class);

            List<String> collect = objectList.stream().map(OrgRecognitionAll::getCode).collect(Collectors.toList());
            iOrgRecognitionAllService.getBaseMapper().delete(new QueryWrapper<OrgRecognitionAll>().lambda().in(OrgRecognitionAll::getCode, collect));
            boolean saveUnit = iOrgRecognitionAllService.saveBatch(objectList);
            System.out.println("转换后得对象情况ccp_org_recognition_all数量以及入库情况======>" + objectList.size() + saveUnit);
        }

        JSONArray ccp_org_recognition_data = jsonDataObject.getJSONArray("ccp_org_recognition_data");
        if (ObjectUtil.isNotNull(ccp_org_recognition_data) && ccp_org_recognition_data.size() > 0) {
            List<OrgRecognitionData> objectList = (List) this.checkDta(ccp_org_recognition_data, OrgRecognitionData.class);

            List<String> collect = objectList.stream().map(OrgRecognitionData::getCode).collect(Collectors.toList());
            iOrgRecognitionDataService.getBaseMapper().delete(new QueryWrapper<OrgRecognitionData>().lambda().in(OrgRecognitionData::getCode, collect));
            boolean saveUnit = iOrgRecognitionDataService.saveBatch(objectList);
            System.out.println("转换后得对象情况ccp_org_recognition_data数量以及入库情况======>" + objectList.size() + saveUnit);
        }
        JSONArray ccp_org_recognition_situation = jsonDataObject.getJSONArray("ccp_org_recognition_situation");
        if (ObjectUtil.isNotNull(ccp_org_recognition_situation) && ccp_org_recognition_situation.size() > 0) {
            List<OrgRecognitionSituation> objectList = (List) this.checkDta(ccp_org_recognition_situation, OrgRecognitionSituation.class);

            List<String> collect = objectList.stream().map(OrgRecognitionSituation::getCode).collect(Collectors.toList());
            iOrgRecognitionSituationService.getBaseMapper().delete(new QueryWrapper<OrgRecognitionSituation>().lambda().in(OrgRecognitionSituation::getCode, collect));
            boolean saveUnit = iOrgRecognitionSituationService.saveBatch(objectList);
            System.out.println("转换后得对象情况ccp_org_recognition_situation数量以及入库情况======>" + objectList.size() + saveUnit);
        }
        JSONArray ccp_org_appraisal = jsonDataObject.getJSONArray("ccp_org_appraisal");
        if (ObjectUtil.isNotNull(ccp_org_appraisal) && ccp_org_appraisal.size() > 0) {
            List<OrgAppraisal> objectList = (List) this.checkDta(ccp_org_appraisal, OrgAppraisal.class);

            List<String> collect = objectList.stream().map(OrgAppraisal::getCode).collect(Collectors.toList());
            iOrgAppraisalService.getBaseMapper().delete(new QueryWrapper<OrgAppraisal>().lambda().in(OrgAppraisal::getCode, collect));
            boolean saveUnit = iOrgAppraisalService.saveBatch(objectList);
            System.out.println("转换后得对象情况ccp_org_appraisal数量以及入库情况======>" + objectList.size() + saveUnit);
        }
        JSONArray ccp_org_reviewers = jsonDataObject.getJSONArray("ccp_org_reviewers");
        if (ObjectUtil.isNotNull(ccp_org_reviewers) && ccp_org_reviewers.size() > 0) {
            List<OrgReviewers> objectList = (List) this.checkDta(ccp_org_reviewers, OrgReviewers.class);

            List<String> collect = objectList.stream().map(OrgReviewers::getCode).collect(Collectors.toList());
            iOrgReviewersService.getBaseMapper().delete(new QueryWrapper<OrgReviewers>().lambda().in(OrgReviewers::getCode, collect));
            boolean saveUnit = iOrgReviewersService.saveBatch(objectList);
            System.out.println("转换后得对象情况ccp_org_reviewers数量以及入库情况======>" + objectList.size() + saveUnit);
        }
        JSONArray ccp_org_reward = jsonDataObject.getJSONArray("ccp_org_reward");
        if (ObjectUtil.isNotNull(ccp_org_reward) && ccp_org_reward.size() > 0) {
            List<OrgReward> objectList = (List) this.checkDta(ccp_org_reward, OrgReward.class);

            List<String> collect = objectList.stream().map(OrgReward::getCode).collect(Collectors.toList());
            iOrgRewardService.getBaseMapper().delete(new QueryWrapper<OrgReward>().lambda().in(OrgReward::getCode, collect));
            objectList.forEach(orgReward -> orgReward.setId(null));
            boolean saveUnit = iOrgRewardService.saveBatch(objectList);
            System.out.println("转换后得对象情况ccp_org_reward数量以及入库情况======>" + objectList.size() + saveUnit);
        }
        JSONArray ccp_org_slack = jsonDataObject.getJSONArray("ccp_org_slack");
        if (ObjectUtil.isNotNull(ccp_org_slack) && ccp_org_slack.size() > 0) {
            List<OrgSlack> objectList = (List) this.checkDta(ccp_org_slack, OrgSlack.class);

            List<String> collect = objectList.stream().map(OrgSlack::getCode).collect(Collectors.toList());
            iOrgSlackService.getBaseMapper().delete(new QueryWrapper<OrgSlack>().lambda().in(OrgSlack::getCode, collect));
            objectList.forEach(orgSlack -> orgSlack.setId(null));
            boolean saveUnit = iOrgSlackService.saveBatch(objectList);
            System.out.println("转换后得对象情况ccp_org_slack数量以及入库情况======>" + objectList.size() + saveUnit);
        }
        JSONArray ccp_org_slack_all = jsonDataObject.getJSONArray("ccp_org_slack_all");
        if (ObjectUtil.isNotNull(ccp_org_slack_all) && ccp_org_slack_all.size() > 0) {
            List<OrgSlackAll> objectList = (List) this.checkDta(ccp_org_slack_all, OrgSlackAll.class);

            List<String> collect = objectList.stream().map(OrgSlackAll::getCode).collect(Collectors.toList());
            iOrgSlackAllService.getBaseMapper().delete(new QueryWrapper<OrgSlackAll>().lambda().in(OrgSlackAll::getCode, collect));
            boolean saveUnit = iOrgSlackAllService.saveBatch(objectList);
            System.out.println("转换后得对象情况ccp_org_slack数量以及入库情况======>" + objectList.size() + saveUnit);
        }

        JSONArray ccp_org_slack_rectification = jsonDataObject.getJSONArray("ccp_org_slack_rectification");
        if (ObjectUtil.isNotNull(ccp_org_slack_rectification) && ccp_org_slack_rectification.size() > 0) {
            List<OrgSlackRectification> objectList = (List) this.checkDta(ccp_org_slack_rectification, OrgSlackRectification.class);


            List<String> collect = objectList.stream().map(OrgSlackRectification::getCode).collect(Collectors.toList());
            iOrgSlackRectificationService.getBaseMapper().delete(new QueryWrapper<OrgSlackRectification>().lambda().in(OrgSlackRectification::getCode, collect));
            objectList.forEach(orgSlackRectification -> orgSlackRectification.setId(null));
            boolean saveUnit = iOrgSlackRectificationService.saveBatch(objectList);
            System.out.println("转换后得对象情况ccp_org_slack_rectification数量以及入库情况======>" + objectList.size() + saveUnit);
        }
        JSONArray ccp_org_special_nature = jsonDataObject.getJSONArray("ccp_org_special_nature");
        if (ObjectUtil.isNotNull(ccp_org_special_nature) && ccp_org_special_nature.size() > 0) {
            List<OrgSpecialNature> objectList = (List) this.checkDta(ccp_org_special_nature, OrgSpecialNature.class);

            List<String> collect = objectList.stream().map(OrgSpecialNature::getCode).collect(Collectors.toList());
            iOrgSpecialNatureService.getBaseMapper().delete(new QueryWrapper<OrgSpecialNature>().lambda().in(OrgSpecialNature::getCode, collect));
            objectList.forEach(orgSpecialNature -> orgSpecialNature.setId(null));
            boolean saveUnit = iOrgSpecialNatureService.saveBatch(objectList);
            System.out.println("转换后得对象情况ccp_org_special_nature数量以及入库情况======>" + objectList.size() + saveUnit);
        }
        JSONArray ccp_org_township_leadership = jsonDataObject.getJSONArray("ccp_org_township_leadership");
        if (ObjectUtil.isNotNull(ccp_org_township_leadership) && ccp_org_township_leadership.size() > 0) {
            List<OrgTownshipLeadership> objectList = (List) this.checkDta(ccp_org_township_leadership, OrgTownshipLeadership.class);

            List<String> collect = objectList.stream().map(OrgTownshipLeadership::getCode).collect(Collectors.toList());
            iOrgTownshipLeadershipService.getBaseMapper().delete(new QueryWrapper<OrgTownshipLeadership>().lambda().in(OrgTownshipLeadership::getCode, collect));
            boolean saveUnit = iOrgTownshipLeadershipService.saveBatch(objectList);
            System.out.println("转换后得对象情况ccp_org_township_leadership数量以及入库情况======>" + objectList.size() + saveUnit);
        }

        // 需要保存的流程信息（遵义才会有）
        final List<MemDevelopProcess> memDevelopProcessList = new ArrayList<>();
        JSONArray ccp_mem = jsonDataObject.getJSONArray("ccp_mem");
        if (ObjectUtil.isNotNull(ccp_mem) && ccp_mem.size() > 0) {
            List<Mem> objectList = (List) this.checkDta(ccp_mem, Mem.class);
            //处理人员进入支部类型为整建制转入
            objectList.forEach(mem -> {
                mem.setD11Code("3812");
                mem.setD11Name("省内整建制转入");
            });

            List<String> collect = objectList.stream().map(Mem::getCode).collect(Collectors.toList());
            iMemService.getBaseMapper().delete(new QueryWrapper<Mem>().lambda().in(Mem::getCode, collect));

            //删除人员奖惩all表数据
            iMemRewardAllService.getBaseMapper().delete(new QueryWrapper<MemRewardAll>().lambda().in(MemRewardAll::getMemCode, collect));

            objectList.forEach(mem -> {
                mem.setId(null);
                // 判断该党员属于指定遵义节点
                if(com.zenith.front.common.constant.UserConstant.HAS_ZUN_YI) {
                    // 当档案唯一码不存在则新生成
                    //  判断是否预备党员
                    if(StrUtil.equals(mem.getD08Code(), CommonConstant.TWO)){
                        if (StrUtil.isBlank(mem.getDigitalLotNo())) {
                            mem.setDigitalLotNo(IdUtil.simpleUUID());
                        }
                        List<MemDevelopProcess> processList = generate(mem.getD08Code(), mem.getDigitalLotNo(), mem.getJoinOrgDate());
                        MemDevelopProcess process = processList.stream().filter(e -> Objects.isNull(e.getApproveTime())).findFirst().orElse(null);
                        if(Objects.nonNull(process)) {
                            mem.setProcessNode(process.getProcessNode());
                            mem.setIsCatalogue(CommonConstant.ONE_INT);
                            if (StrUtil.isNotBlank(process.getProcessNode())) {
                                memDevelopProcessList.addAll(processList);
                            }
                        }
                    }
                }
            });
            boolean saveUnit = iMemService.saveBatch(objectList);
            System.out.println("转换后得对象情况ccp_mem数量以及入库情况======>" + objectList.size() + saveUnit);
        }

        JSONArray ccp_mem_all = jsonDataObject.getJSONArray("ccp_mem_all");
        if (ObjectUtil.isNotNull(ccp_mem_all) && ccp_mem_all.size() > 0) {
            List<MemAllInfo> objectList = (List) this.checkDta(ccp_mem_all, MemAllInfo.class);
            //处理人员进入支部类型为整建制转入但是all表没有这个数据
            List<String> collect = objectList.stream().map(MemAllInfo::getCode).collect(Collectors.toList());
            iMemAllInfoService.getBaseMapper().delete(new QueryWrapper<MemAllInfo>().lambda().in(MemAllInfo::getCode, collect));
            boolean saveUnit = iMemAllInfoService.saveBatch(objectList);
            System.out.println("转换后得对象情况ccp_mem_all数量以及入库情况======>" + objectList.size() + saveUnit);
        }

        JSONArray ccp_mem_reward_all = jsonDataObject.getJSONArray("ccp_mem_reward_all");
        if (ObjectUtil.isNotNull(ccp_mem_reward_all) && ccp_mem_reward_all.size() > 0) {
            List<MemRewardAll> objectList = (List) this.checkDta(ccp_mem_reward_all, MemRewardAll.class);
            //处理人员进入支部类型为整建制转入但是all表没有这个数据
            boolean saveUnit = iMemRewardAllService.saveBatch(objectList);
            System.out.println("转换后得对象情况ccp_mem_reward_all数量以及入库情况======>" + objectList.size() + saveUnit);
        }

        JSONArray ccp_democratic_review_mem = jsonDataObject.getJSONArray("ccp_democratic_review_mem");
        if (ObjectUtil.isNotNull(ccp_democratic_review_mem) && ccp_democratic_review_mem.size() > 0) {
            List<DemocraticReviewMem> objectList = (List) this.checkDta(ccp_democratic_review_mem, DemocraticReviewMem.class);

            List<String> collect = objectList.stream().map(DemocraticReviewMem::getCode).collect(Collectors.toList());
            iDemocraticReviewMemService.getBaseMapper().delete(new QueryWrapper<DemocraticReviewMem>().lambda().in(DemocraticReviewMem::getCode, collect));
            objectList.forEach(democraticReviewMem -> democraticReviewMem.setId(null));
            boolean saveUnit = iDemocraticReviewMemService.saveBatch(objectList);
            System.out.println("转换后得对象情况ccp_democratic_review_mem数量以及入库情况======>" + objectList.size() + saveUnit);
        }
        JSONArray ccp_mem_abroad = jsonDataObject.getJSONArray("ccp_mem_abroad");
        if (ObjectUtil.isNotNull(ccp_mem_abroad) && ccp_mem_abroad.size() > 0) {
            List<MemAbroad> objectList = (List) this.checkDta(ccp_mem_abroad, MemAbroad.class);

            List<String> collect = objectList.stream().map(MemAbroad::getCode).collect(Collectors.toList());
            iMemAbroadService.getBaseMapper().delete(new QueryWrapper<MemAbroad>().lambda().in(MemAbroad::getCode, collect));
            objectList.forEach(memAbroad -> memAbroad.setId(null));
            boolean saveUnit = iMemAbroadService.saveBatch(objectList);
            System.out.println("转换后得对象情况ccp_mem_abroad数量以及入库情况======>" + objectList.size() + saveUnit);
        }
        JSONArray ccp_mem_develop = jsonDataObject.getJSONArray("ccp_mem_develop");
        if (ObjectUtil.isNotNull(ccp_mem_develop) && ccp_mem_develop.size() > 0) {
            List<MemDevelop> objectList = (List) this.checkDta(ccp_mem_develop, MemDevelop.class);

            List<String> collect = objectList.stream().map(MemDevelop::getCode).collect(Collectors.toList());
            iMemDevelopService.getBaseMapper().delete(new QueryWrapper<MemDevelop>().lambda().in(MemDevelop::getCode, collect));
            objectList.forEach(memDevelop -> {
                memDevelop.setId(null);
                // 判断该党员属于指定遵义节点
                if(com.zenith.front.common.constant.UserConstant.HAS_ZUN_YI) {
                    // 当档案唯一码不存在则新生成
                    //  判断是否积极份子、入党申请人、发展对象
                    if(StrUtil.equalsAny(memDevelop.getD08Code(), CommonConstant.THREE, CommonConstant.FOUR, CommonConstant.FIVE)){
                        if (StrUtil.isBlank(memDevelop.getDigitalLotNo())) {
                            memDevelop.setDigitalLotNo(IdUtil.simpleUUID());
                        }
                        // 如果是入党申请人则取值入党申请时间
                        Date cDate = Objects.equals(memDevelop.getD08Code(), CommonConstant.FIVE) ? memDevelop.getApplyDate() : memDevelop.getActiveDate();
                        List<MemDevelopProcess> processList = generate(memDevelop.getD08Code(), memDevelop.getDigitalLotNo(), cDate);
                        MemDevelopProcess process = processList.stream().filter(e -> Objects.isNull(e.getApproveTime())).findFirst().orElse(null);
                        if(Objects.nonNull(process)) {
                            memDevelop.setProcessNode(process.getProcessNode());
                            if (StrUtil.isNotBlank(process.getProcessNode())) {
                                memDevelopProcessList.addAll(processList);
                            }
                        }
                    }
                }
            });

            boolean saveUnit = iMemDevelopService.saveBatch(objectList);
            System.out.println("转换后得对象情况ccp_mem_develop数量以及入库情况======>" + objectList.size() + saveUnit);
        }
        JSONArray ccp_mem_develop_all = jsonDataObject.getJSONArray("ccp_mem_develop_all");
        if (ObjectUtil.isNotNull(ccp_mem_develop_all) && ccp_mem_develop_all.size() > 0) {
            List<MemDevelopAll> objectList = (List) this.checkDta(ccp_mem_develop_all, MemDevelopAll.class);

            List<String> collect = objectList.stream().map(MemDevelopAll::getCode).collect(Collectors.toList());
            iMemDevelopAllService.getBaseMapper().delete(new QueryWrapper<MemDevelopAll>().lambda().in(MemDevelopAll::getCode, collect));
            boolean saveUnit = iMemDevelopAllService.saveBatch(objectList);
            System.out.println("转换后得对象情况ccp_mem_develop数量以及入库情况======>" + objectList.size() + saveUnit);
        }
        JSONArray ccp_mem_develop_operation = jsonDataObject.getJSONArray("ccp_mem_develop_operation");
        if (ObjectUtil.isNotNull(ccp_mem_develop_operation) && ccp_mem_develop_operation.size() > 0) {
            List<MemDevelopOperation> objectList = (List) this.checkDta(ccp_mem_develop_operation, MemDevelopOperation.class);

            List<String> collect = objectList.stream().map(MemDevelopOperation::getCode).collect(Collectors.toList());
            iMemDevelopOperationService.getBaseMapper().delete(new QueryWrapper<MemDevelopOperation>().lambda().in(MemDevelopOperation::getCode, collect));
            boolean saveUnit = iMemDevelopOperationService.saveBatch(objectList);
            System.out.println("转换后得对象情况ccp_mem_develop_operation数量以及入库情况======>" + objectList.size() + saveUnit);
        }
        JSONArray ccp_mem_difficult = jsonDataObject.getJSONArray("ccp_mem_difficult");
        if (ObjectUtil.isNotNull(ccp_mem_difficult) && ccp_mem_difficult.size() > 0) {
            List<MemDifficult> objectList = (List) this.checkDta(ccp_mem_difficult, MemDifficult.class);

            List<String> collect = objectList.stream().map(MemDifficult::getCode).collect(Collectors.toList());
            iMemDifficultService.getBaseMapper().delete(new QueryWrapper<MemDifficult>().lambda().in(MemDifficult::getCode, collect));
            objectList.forEach(memDifficult -> memDifficult.setId(null));
            boolean saveUnit = iMemDifficultService.saveBatch(objectList);
            System.out.println("转换后得对象情况ccp_mem_difficult数量以及入库情况======>" + objectList.size() + saveUnit);
        }
        JSONArray ccp_mem_extend = jsonDataObject.getJSONArray("ccp_mem_extend");
        if (ObjectUtil.isNotNull(ccp_mem_extend) && ccp_mem_extend.size() > 0) {
            List<MemExtend> objectList = (List) this.checkDta(ccp_mem_extend, MemExtend.class);

            List<String> collect = objectList.stream().map(MemExtend::getCode).collect(Collectors.toList());
            iMemExtendService.getBaseMapper().delete(new QueryWrapper<MemExtend>().lambda().in(MemExtend::getCode, collect));
            objectList.forEach(memExtend -> memExtend.setId(null));
            boolean saveUnit = iMemExtendService.saveBatch(objectList);
            System.out.println("转换后得对象情况ccp_mem_extend数量以及入库情况======>" + objectList.size() + saveUnit);
        }
        JSONArray ccp_mem_flow = jsonDataObject.getJSONArray("ccp_mem_flow");
        if (ObjectUtil.isNotNull(ccp_mem_flow) && ccp_mem_flow.size() > 0) {
            List<MemFlow> objectList = (List) this.checkDta(ccp_mem_flow, MemFlow.class);

            List<String> collect = objectList.stream().map(MemFlow::getCode).collect(Collectors.toList());
            iMemFlowService.getBaseMapper().delete(new QueryWrapper<MemFlow>().lambda().in(MemFlow::getCode, collect));
            objectList.forEach(memFlow -> memFlow.setId(null));
            boolean saveUnit = iMemFlowService.saveBatch(objectList);
            System.out.println("转换后得对象情况ccp_mem_flow数量以及入库情况======>" + objectList.size() + saveUnit);
        }

        JSONArray ccp_mem_flow_all = jsonDataObject.getJSONArray("ccp_mem_flow_all");
        if (ObjectUtil.isNotNull(ccp_mem_flow_all) && ccp_mem_flow_all.size() > 0) {
            List<MemFlowAll> objectList = (List) this.checkDta(ccp_mem_flow_all, MemFlowAll.class);

            List<String> collect = objectList.stream().map(MemFlowAll::getCode).collect(Collectors.toList());
            iMemFlowAllService.getBaseMapper().delete(new QueryWrapper<MemFlowAll>().lambda().in(MemFlowAll::getCode, collect));
            boolean saveUnit = iMemFlowAllService.saveBatch(objectList);
            System.out.println("转换后得对象情况ccp_mem_flow_all数量以及入库情况======>" + objectList.size() + saveUnit);
        }

        JSONArray ccp_mem_reward = jsonDataObject.getJSONArray("ccp_mem_reward");
        if (ObjectUtil.isNotNull(ccp_mem_reward) && ccp_mem_reward.size() > 0) {
            List<MemReward> objectList = (List) this.checkDta(ccp_mem_reward, MemReward.class);

            List<String> collect = objectList.stream().map(MemReward::getCode).collect(Collectors.toList());
            iMemRewardService.getBaseMapper().delete(new QueryWrapper<MemReward>().lambda().in(MemReward::getCode, collect));
            objectList.forEach(memReward -> memReward.setId(null));
            boolean saveUnit = iMemRewardService.saveBatch(objectList);
            System.out.println("转换后得对象情况ccp_mem_reward数量以及入库情况======>" + objectList.size() + saveUnit);
        }
        JSONArray ccp_mem_train = jsonDataObject.getJSONArray("ccp_mem_train");
        if (ObjectUtil.isNotNull(ccp_mem_train) && ccp_mem_train.size() > 0) {
            List<MemTrain> objectList = (List) this.checkDta(ccp_mem_train, MemTrain.class);

            List<String> collect = objectList.stream().map(MemTrain::getCode).collect(Collectors.toList());
            iMemTrainService.getBaseMapper().delete(new QueryWrapper<MemTrain>().lambda().in(MemTrain::getCode, collect));
            boolean saveUnit = iMemTrainService.saveBatch(objectList);
            System.out.println("转换后得对象情况ccp_mem_train数量以及入库情况======>" + objectList.size() + saveUnit);
        }
        //处理用户相关得权限以及角色
        JSONArray sys_user = jsonDataObject.getJSONArray("sys_user");
        if (ObjectUtil.isNotNull(sys_user) && sys_user.size() > 0) {
            List<User> objectList = (List) this.checkDta(sys_user, User.class);
            List<String> collect = objectList.stream().map(User::getId).collect(Collectors.toList());
            List<String> account = objectList.stream().map(User::getAccount).collect(Collectors.toList());
            userService.getBaseMapper().delete(new QueryWrapper<User>().lambda().in(User::getId, collect));
            userService.getBaseMapper().delete(new QueryWrapper<User>().lambda().in(User::getAccount, account));
            boolean saveUnit = userService.saveBatch(objectList);
            System.out.println("转换后得对象情况sys_user数量以及入库情况======>" + objectList.size() + saveUnit);
        }
        JSONArray sys_user_role_permission = jsonDataObject.getJSONArray("sys_user_role_permission");
        if (ObjectUtil.isNotNull(sys_user_role_permission) && sys_user_role_permission.size() > 0) {
            List<UserRolePermission> objectList = (List) this.checkDta(sys_user_role_permission, UserRolePermission.class);

            List<String> collect = objectList.stream().map(UserRolePermission::getId).collect(Collectors.toList());
            userRolePermissionService.getBaseMapper().delete(new QueryWrapper<UserRolePermission>().lambda().in(UserRolePermission::getId, collect));
            boolean saveUnit = userRolePermissionService.saveBatch(objectList);
            System.out.println("转换后得对象情况sys_user_role_permission数量以及入库情况======>" + objectList.size() + saveUnit);
        }


        JSONArray ccp_develop_step_log_all = jsonDataObject.getJSONArray("ccp_develop_step_log_all");
        if (ObjectUtil.isNotNull(ccp_develop_step_log_all) && ccp_develop_step_log_all.size() > 0) {
            List<DevelopStepLogAll> objectList = (List) this.checkDta(ccp_develop_step_log_all, DevelopStepLogAll.class);

            List<String> collect = objectList.stream().map(DevelopStepLogAll::getCode).collect(Collectors.toList());
            iDevelopStepLogAllService.getBaseMapper().delete(new QueryWrapper<DevelopStepLogAll>().lambda().in(DevelopStepLogAll::getCode, collect));
            objectList.forEach(developStepLog -> developStepLog.setId(null));
            boolean saveUnit = iDevelopStepLogAllService.saveBatch(objectList);
            System.out.println("转换后得对象情况ccp_develop_step_log_all数量以及入库情况======>" + objectList.size() + saveUnit);
        }

        JSONArray ccp_develop_step_log = jsonDataObject.getJSONArray("ccp_develop_step_log");

        if (ObjectUtil.isNotNull(ccp_develop_step_log) && ccp_develop_step_log.size() > 0) {
            List<DevelopStepLog> objectList = (List) this.checkDta(ccp_develop_step_log, DevelopStepLog.class);

            List<String> collect = objectList.stream().map(DevelopStepLog::getCode).collect(Collectors.toList());
            iDevelopStepLogService.getBaseMapper().delete(new QueryWrapper<DevelopStepLog>().lambda().in(DevelopStepLog::getCode, collect));
            objectList.forEach(developStepLog -> developStepLog.setId(null));
            boolean saveUnit = iDevelopStepLogService.saveBatch(objectList);
            System.out.println("转换后得对象情况ccp_develop_step_log数量以及入库情况======>" + objectList.size() + saveUnit);
        }

        // todo 2025-03-19
        // 遵义档案流程信息
//        JSONArray ccp_mem_develop_process = jsonDataObject.getJSONArray("ccp_mem_develop_process");
//        if (ObjectUtil.isNotNull(ccp_mem_develop_process) && ccp_mem_develop_process.size() > 0) {
//            List<MemDevelopProcess> objectList = (List) this.checkDta(ccp_mem_develop_process, MemDevelopProcess.class);
//            List<String> collect = objectList.stream().map(MemDevelopProcess::getCode).collect(Collectors.toList());
//            memDevelopProcessMapper.delete(new QueryWrapper<MemDevelopProcess>().lambda().in(MemDevelopProcess::getCode, collect));
//            Integer save = memDevelopProcessMapper.myInserBatch(objectList);
//            System.out.println("转换后得对象情况ccp_mem_develop_process数量以及入库情况======>" + objectList.size() + ":saveSize="+save);
//        }
        if (CollUtil.isNotEmpty(memDevelopProcessList)) {
            // 保存生成的流程信息
            List<String> collect =  memDevelopProcessList.stream().map(MemDevelopProcess::getDigitalLotNo).collect(Collectors.toList());
            memDevelopProcessMapper.delete(new QueryWrapper<MemDevelopProcess>().lambda().in(MemDevelopProcess::getDigitalLotNo, collect));
            Integer save = memDevelopProcessMapper.myInserBatch(memDevelopProcessList);
            System.out.println("转换后得对象情况ccp_mem_develop_process数量以及入库情况======>" + memDevelopProcessList.size() + ":saveSize="+save);
        }

        // 遵义档案信息
        JSONArray ccp_mem_digital = jsonDataObject.getJSONArray("ccp_mem_digital");
        if (ObjectUtil.isNotNull(ccp_mem_digital) && ccp_mem_digital.size() > 0) {
            List<MemDigital> objectList = (List) this.checkDta(ccp_mem_digital, MemDigital.class);
            List<String> collect = objectList.stream().map(MemDigital::getCode).collect(Collectors.toList());
            memDigitalMapper.delete(new QueryWrapper<MemDigital>().lambda().in(MemDigital::getCode, collect));
            Integer save = memDigitalMapper.myInserBatch(objectList);
            System.out.println("转换后得对象情况ccp_mem_digital数量以及入库情况======>" + objectList.size() + ":saveSize="+save);
        }
        // 遵义档案信息操作日志
        JSONArray ccp_mem_digital_operation_log = jsonDataObject.getJSONArray("ccp_mem_digital_operation_log");
        if (ObjectUtil.isNotNull(ccp_mem_digital_operation_log) && ccp_mem_digital_operation_log.size() > 0) {
            List<MemDigitalOperationLog> objectList = (List) this.checkDta(ccp_mem_digital_operation_log, MemDigitalOperationLog.class);
            List<String> collect = objectList.stream().map(MemDigitalOperationLog::getCode).collect(Collectors.toList());
            memDigitalOperationLogMapper.delete(new QueryWrapper<MemDigitalOperationLog>().lambda().in(MemDigitalOperationLog::getCode, collect));
            Integer save = memDigitalOperationLogMapper.myInserBatch(objectList);
            System.out.println("转换后得对象情况ccp_mem_digital_operation_log数量以及入库情况======>" + objectList.size() + ":saveSize="+save);
        }

        //处理用户上级相关得是否叶子节点问题
        return true;
    }

    boolean transferAdjust(TransferRecord record) {
        String memId = record.getMemId();
        //组织转接
        if (StrKit.isBlank(memId)) {
            // TODO: 2021/12/26 全新非跨节点数据整建制转接处理
            // TODO: 2021/12/26 全新整建制转接处理
            String type = record.getType();
            if (type.equals("212")) {
                //入库数据
                transferCrossNode(record);
                //通过移动党组织移动接口，移动数据
                this.moveOrg(record.getSrcOrgId(), record.getTargetOrgId(), true);
                //修改审核情况
                return true;
            }
            return false;
        } else {
            //人员调整
            Mem mem = memService.findByCode(memId);
            if (mem != null) {
                if(Objects.nonNull(record.getExtraData())) {
                    // 先将确认前填写的信息保存一次
                    MemDTO data = JSONUtil.toBean(JSONUtil.parseObj(record.getExtraData()), MemDTO.class);
                    BeanUtil.copyProperties(data, mem, "id");
                }

                Org targetOrg = orgService.findOrgByCode(record.getTargetOrgId());
                //转出系统外
                if (targetOrg == null) {
                    mem.setIsHistory(1);
                    mem.setDeleteTime(new Date());
                    mem.setD12Code("5");
                    mem.setD51Name("跨省（系统）组织关系转出");
                    mem.setLeaveOrgDate(new Date());
                } else {
                    mem.setMemOrgCode(targetOrg.getOrgCode());
                    mem.setOrgCode(targetOrg.getCode());
                    // TODO 20220622 之前这里设为0数据校验不出来，所以要设为null
//                    mem.setHasUnitStatistics(null);
//                    //has_unit_province
//                    mem.setHasUnitProvince(null);
//                    //unit_information
//                    mem.setUnitInformation("");
//                    //d04_code
//                    mem.setD04Code("");
//                    //statistical_unit
//                    mem.setStatisticalUnit("");
                }
                mem.setOrgName(record.getTargetOrgName());
                mem.setIsTransfer(0);
                TransferRecord updateRecord = new TransferRecord();
                updateRecord.setId(record.getId());
                updateRecord.setEffectMems(1);
                updateById(updateRecord);
                boolean updateById = iMemService.update(mem, Wrappers.<Mem>lambdaUpdate()
                        .set(Objects.isNull(mem.getHasUnitStatistics()), Mem::getHasUnitStatistics, null)
                        .set(Objects.isNull(mem.getHasUnitProvince()), Mem::getHasUnitProvince, null)
                        .eq(Mem::getId, mem.getId()));
                if (updateById) {
                    //todo 20220620 修复未同步all表问题，之前updateMem.getCode() 一直都是是null
                    iSyncMemService.syncMem(memId, CommonConstant.ONE);
                }
                return updateById;
            } else {
                // TODO: 2021/11/17 处理特殊情况,当人员在本地数据为空，但是数据里面又存着人员ID，证明是其他地方数据节点来的数据
                // TODO: 2021/12/26 外面来得人员也有两种情况， 因为现在code不再是唯一主键了， 所以存在一种情况，一个人转出当前数据节点，然后又转回当前数据节点得情况，直接保存会造成两条code一样得数据
                if (StrKit.notBlank(memId)) {
                    Object extraData = record.getExtraData();
                    JSONObject jsonObject = (JSONObject) extraData;
                    Mem transFerMem = JSONObject.toJavaObject(jsonObject, Mem.class);
                    MemDTO digMemDTO = JSONObject.toJavaObject(jsonObject, MemDTO.class);

                    //处理党组织唯一标识符以党组织层级码相关得信息
                    Org targetOrg = orgService.findOrgByCode(record.getTargetOrgId());
                    transFerMem.setMemOrgCode(targetOrg.getOrgCode());
                    transFerMem.setOrgCode(targetOrg.getCode());
                    transFerMem.setOrgName(record.getTargetOrgName());
                    // TODO 20220622 之前这里设为0数据校验不出来，所以要设为null
//                    transFerMem.setHasUnitStatistics(null);
//                    //has_unit_province
//                    transFerMem.setHasUnitProvince(null);
//                    //unit_information
//                    transFerMem.setUnitInformation("");
//                    //d04_code
//                    transFerMem.setD04Code("");
//                    //statistical_unit
//                    transFerMem.setStatisticalUnit("");
//                    transFerMem.setIsTransfer(CommonConstant.ZERO_INT);
//                    transFerMem.setId(null);
                    transFerMem.setRemark("关系转接生成数据并且重置唯一标识符"+new Date());
                    // TODO: 2022/3/27 因为设置了唯一主键，所以需要重新设置人员的code
                    transFerMem.setCode(StrKit.getRandomUUID());
                    // TODO: 2023/4/6 最近出现大量重复，通过姓名和身份证进行一次查询是否重复
                    Mem memIsHave = memService.
                            findMemByIdCardAndName(SM4Untils.encryptContent(exchange_nginx_key, transFerMem.getName()),
                                    SM4Untils.encryptContent(exchange_nginx_key, transFerMem.getIdcard()));
                    boolean save ;
                    // 如果上面查到本地库有相同人员的证件号跟姓名一致。那么这个党员需要绑定转过来的档案唯一码
                    String memCode = transFerMem.getCode();
                    if (ObjectUtil.isNull(memIsHave)){
                         save = memService.save(transFerMem);
                    }else {
                        memCode = memIsHave.getCode();
                        save=true;
                    }
                    if (save) {
                        // todo ********: 遵义原因接收党员需要添加流程或者档案
                        this.withDig(digMemDTO, memCode);
                        iSyncMemService.syncMem(transFerMem.getCode(), CommonConstant.ONE);
                    }
                    return save;
                }else {
                    //此时 是 系统外转入 或者是整建制流入
                    Object extraData = record.getExtraData();
                    List<TransferRecord.MemInfoExtraData> memInfoExtraDataList = JackSonUtil.jsonToList(JackSonUtil.toJson(extraData), TransferRecord.MemInfoExtraData.class);
                    //存入党员列表
                    List<Mem> memList = new ArrayList<>();
                    if (ObjectUtil.isNotNull(memInfoExtraDataList)) {
                        Org targetOrg = orgService.findOrgByCode(record.getTargetOrgId());
                        for (TransferRecord.MemInfoExtraData memInfoExtraData : memInfoExtraDataList) {
                            Mem mems = new Mem();
                            mems.setCode(StrUtil.uuid().toUpperCase());
                            mems.setIsTransfer(null);
                            mems.setEsId(CodeUtil.getEsId());
                            mems.setName(memInfoExtraData.getMemName());
                            mems.setPinyin(PinyinUtil.getPinyin(memInfoExtraData.getMemName()));
                            mems.setIdcard(memInfoExtraData.getIdCard());
                            mems.setMemOrgCode(targetOrg.getOrgCode());
                            mems.setOrgZbCode(targetOrg.getZbCode());
                            mems.setOrgCode(targetOrg.getCode());
                            mems.setPhone(memInfoExtraData.getPhone());
                            mems.setJoinOrgDate(memInfoExtraData.getInOrgTime());
                            mems.setFullMemberDate(memInfoExtraData.getTurnTime());
                        }
                    }
                    if (cn.hutool.core.collection.CollectionUtil.isNotEmpty(memList)) {
                        memService.saveBatch(memList);
                        // todo ********: 遵义原因接收党员需要添加流程或者档案
                        memList.forEach(e -> this.withDig(new MemDTO(), e.getCode()));
                    }
                }
            }
            return true;
        }
    }

    @Override
    public OutMessage<TransferDetailVo> detail(String transferId, boolean isOut) {
        UserTicket userTicket = UserConstant.USER_CONTEXT.get();
        if (userTicket == null) {
            return new OutMessage<>(Status.NOT_LOGIN);
        }
        TransferRecord record = getById(transferId);
        //判断转接记录是否存在
        if (record == null) {
            return new OutMessage<>(Status.PARA_ERROR);
        }
        //转接类型
        String type = record.getType();
        if (TransferRecordConstant.MEM_ADJUST_IN.equalsIgnoreCase(type) || TransferRecordConstant.MEM_ADJUST_OUT.equalsIgnoreCase(type)) {
            //如果是属于支部间人员调整,则不允许查看详情
            return new OutMessage<>(Status.TRANSFER_MEM_ADJUST_DETAIL);
        }
        //当前用户的组织id
//        String currentOrgId = userTicket.getUserRolePermission().getOrgId();
        //String currentOrgId="BA5D58785AC74DC5A8D6CF5817F2E2BD";
        //源组织关系集合
        List<String> srcOrgRelationAsList = record.getSrcOrgRelationAsList();
        //目标组织关系集合
        List<String> targetOrgRelationAsList = record.getTargetOrgRelationAsList();
        // TODO: 2021/11/14 这个判断是用来判断接受组织数组，或者是查看组织数组是否有当前权限， 如果有才允许，如果没得就不允许，因为现在变成了最后两层审核，所有她可能没得这个东西
//        if (isOut) {
//            if (!srcOrgRelationAsList.contains(currentOrgId)) {
//                return new OutMessage<>(Status.PERMISSION_DENIED);
//            }
//        } else {
//            if (!targetOrgRelationAsList.contains(currentOrgId)) {
//                return new OutMessage<>(Status.PERMISSION_DENIED);
//            }
//        }
        //创建详情VO对象
        TransferDetailVo detailVo = createTransferDetailVo(record, isOut, srcOrgRelationAsList, targetOrgRelationAsList);
        detailVo.setD92Code(record.getD92Code());
        detailVo.setD92Name(record.getD92Name());
        detailVo.setRemark(record.getRemark());
        detailVo.setD146Code(record.getD146Code());
        detailVo.setD146Name(record.getD146Name());
        if (detailVo.getSrcStepList().size() >= CommonConstant.TWO_INT) {
            if (type.equals("223")&&detailVo.getSrcStepList().size()>CommonConstant.TWO_INT){
                detailVo.setSrcStepList(Arrays.asList(detailVo.getSrcStepList().get(CommonConstant.ZERO_INT), detailVo.getSrcStepList().get(CommonConstant.ONE_INT), detailVo.getSrcStepList().get(CommonConstant.TWO_INT)));
            }else {
                detailVo.setSrcStepList(Arrays.asList(detailVo.getSrcStepList().get(CommonConstant.ZERO_INT), detailVo.getSrcStepList().get(CommonConstant.ONE_INT)));
            }
        }
        if (detailVo.getTargetStepList().size() >= CommonConstant.TWO_INT) {
            if (type.equals("125")&&detailVo.getTargetStepList().size()>CommonConstant.TWO_INT){
                detailVo.setTargetStepList(Arrays.asList(detailVo.getTargetStepList().get(CommonConstant.ZERO_INT), detailVo.getTargetStepList().get(CommonConstant.ONE_INT),detailVo.getTargetStepList().get(CommonConstant.TWO_INT)));
            }else {
                detailVo.setTargetStepList(Arrays.asList(detailVo.getTargetStepList().get(detailVo.getTargetStepList().size() - CommonConstant.TWO_INT), detailVo.getTargetStepList().get(detailVo.getTargetStepList().size() - CommonConstant.ONE_INT)));
            }
        }
        return new OutMessage<>(Status.SUCCESS, detailVo);
    }

    /***
     * 创建详情vo
     * @param record 转接记录对象
     * @param isOut 是否是转出
     * @param srcOrgRelationAsList 源组织关系集合
     * @param targetOrgRelationAsList 目标组织关系集合
     * @return 审批详情vo
     * */
    private TransferDetailVo createTransferDetailVo(TransferRecord record, boolean isOut, List<String> srcOrgRelationAsList, List<String> targetOrgRelationAsList) {
        TransferDetailVo detailVo = new TransferDetailVo();
        //判断该转接记录是人员转接还是属于组织转接
        String memId = record.getMemId();
        //如果memId为空则说明是人员转接,否则为组织关系转接
        if (StrKit.notBlank(memId)) {
            detailVo.setMemName(record.getName());
            Mem mem = memService.findAllByCode(memId);
            if (mem != null) {
                detailVo.setFeeEndTime(record.getMemFeeEndTime());
                if (record.getMemFeeStandard() == null) {
                    BigDecimal decimal = mem.getDuesPrice() == null ? null : BigDecimal.valueOf(mem.getDuesPrice());
                    detailVo.setFeeStandard(decimal);
                } else {
                    detailVo.setFeeStandard(record.getMemFeeStandard());
                }
                detailVo.setMemType(mem.getD08Name());
                detailVo.setIdCard(mem.getIdcard());
                detailVo.setTurnTime(mem.getFullMemberDate());
                detailVo.setPhone(mem.getPhone());
                detailVo.setInTime(mem.getJoinOrgDate());
            } else {
                TransferRecord.MemInfoExtraData memInfoExtraData = record.getMemInfoExtraData();
                if (memInfoExtraData != null) {
                    detailVo.setMemType(CollectionUtil.listRecordToMap(CacheUtils.getDic(DictConstant.DICT_D08), "key", "name").getOrDefault(memInfoExtraData.getMemType(), ""));
                    detailVo.setIdCard(memInfoExtraData.getIdCard());
                    detailVo.setTurnTime(memInfoExtraData.getTurnTime());
                    detailVo.setPhone(memInfoExtraData.getPhone());
                    detailVo.setInTime(memInfoExtraData.getInOrgTime());
                    detailVo.setFeeEndTime(record.getMemFeeEndTime());
                    detailVo.setFeeStandard(record.getMemFeeStandard());
                }
            }

        }
        detailVo.setInOrgName(record.getTargetOrgName());
        detailVo.setOutOrgName(record.getSrcOrgName());
        detailVo.setCommonNodeId(record.getCommonOrgId());
        detailVo.setCommonNodeName(record.getCommonOrgName());
        detailVo.setTransferReason(record.getReason());
        String type = record.getType();
        detailVo.setTransferTypeCode(type);
        detailVo.setLetterNumber(record.getLetterNumber());
        if (isOut) {
            List<Record> dic = CacheUtils.getDic(TransferRecordConstant.DICT_OUT);
            dic.stream().filter(rec -> rec.getStr("key").equalsIgnoreCase(record.getOutType())).findFirst().ifPresent(rec -> detailVo.setTransferTypeName(rec.get("name")));
        } else {
            List<Record> dic = CacheUtils.getDic(TransferRecordConstant.DICT_IN);
            dic.stream().filter(rec -> rec.getStr("key").equalsIgnoreCase(record.getInType())).findFirst().ifPresent(rec -> detailVo.setTransferTypeName(rec.get("name")));
        }
        //源组织转接步骤集合
        List<TransferDetailVo.TransferStep> srcTransferStepList = new ArrayList<>();
        //目标组织转接步骤集合
        List<TransferDetailVo.TransferStep> targetTransferStepList = new ArrayList<>();
        Map<String, TransferApproval> map = transferApprovalService.findByRecordId(record.getId()).stream().collect(Collectors.toMap(e -> {
            String orgId = e.getOrgId();
            String nextOrgId = e.getNextOrgId();
            return orgId + (nextOrgId == null ? "" : nextOrgId);
        }, o -> o, (key1, key2) -> key1));

        removeOtherNodes(record.getCommonOrgId(), srcOrgRelationAsList);
        removeOtherNodes(record.getCommonOrgId(), targetOrgRelationAsList);
        //倒叙源组织这边的关系
        Collections.reverse(srcOrgRelationAsList);
        //创建左边步骤
        addTransferStepToList(type, record.getCommonOrgId(), srcOrgRelationAsList, srcTransferStepList, map);

        //创建右边步骤
        addTransferStepToList(type, record.getCommonOrgId(), targetOrgRelationAsList, targetTransferStepList, map);
        // TODO: 2022/4/20 转出省外或者转出市外需要进行右边节点伪造
        if (StrUtil.equalsAny(type, "223", "224") && targetOrgRelationAsList.isEmpty()) {
            TransferDetailVo.TransferStep step = new TransferDetailVo.TransferStep();
            Integer status = record.getStatus();
            if (status.equals(CommonConstant.ONE_INT)) {
                step.setStatus(CommonConstant.ONE_INT);
            } else {
                step.setStatus(-1);
            }
            step.setContactPhone("无");
            step.setOrgCode("组织code");
            step.setOrgId(record.getTargetOrgId());
            step.setOrgName(record.getTargetOrgName());
            targetTransferStepList.add(step);
        }

        // TODO: 2022/4/26 出现省外转入的情况需要进行左边节点伪造
        if (StrUtil.equalsAny(type, "124","125")) {
            System.out.println("出现省外转入得情况======>");
            srcTransferStepList.clear();
            TransferDetailVo.TransferStep step = new TransferDetailVo.TransferStep();
            step.setStatus(CommonConstant.ONE_INT);
            step.setContactPhone("无");
            step.setOrgCode("组织code");
            step.setOrgId(record.getSrcOrgId());
            step.setOrgName(record.getSrcOrgName());
            srcTransferStepList.add(step);
            if (targetTransferStepList.size() == CommonConstant.ONE_INT) {
                detailVo.setIsCheck(CommonConstant.ONE_INT);
            } else {
                detailVo.setIsCheck(CommonConstant.ZERO_INT);
            }
        }
        //创建公共节点步骤
        TransferDetailVo.TransferStep commonNodeStep = new TransferDetailVo.TransferStep();
        commonNodeStep.setOrgId(detailVo.getCommonNodeId());
        commonNodeStep.setOrgName(detailVo.getCommonNodeName());
        Org commonOrg = orgService.findOrgByCode(detailVo.getCommonNodeId());
        if (commonOrg != null) {
            commonNodeStep.setOrgCode(commonOrg.getOrgCode());
        } else {
            log.warn("未能通过orgId查询到组织信息!");
        }

        //如果目标关系集合为空,说明是转出系统外
        String mapKey;
        if (targetOrgRelationAsList.isEmpty()) {
            mapKey = detailVo.getCommonNodeId();
        } else {
            if (targetOrgRelationAsList.size() > 1) {
                mapKey = detailVo.getCommonNodeId() + targetOrgRelationAsList.get(1);
            } else {
                mapKey = detailVo.getCommonNodeId();
            }
        }
        //查询公共节点是否有审批记录
        TransferApproval commonApproval = map.get(mapKey);
        if (commonApproval == null) {
            //设置公共节点状态
            setCommonStepStatus(record.getCurrentApprovalId(), detailVo, commonNodeStep);
        } else {
            setCommonStepStatus(record.getCurrentApprovalId(), detailVo, commonNodeStep);
            //设置公共节点状态
            List<TransferLog> logList = transferLogService.findByHandleApprovalId(commonApproval.getId());
            commonNodeStep.setLogList(logList);
        }
        //如果记录被撤回,则没有任何操作
        if (TransferRecordConstant.TRANSFER_UNDO.equals(record.getStatus())) {
            srcTransferStepList.forEach(transferStep -> transferStep.setStatus(TransferApprovalConstant.NOT_OPERATE));
            targetTransferStepList.forEach(transferStep -> transferStep.setStatus(TransferApprovalConstant.NOT_OPERATE));
            commonNodeStep.setStatus(TransferApprovalConstant.NOT_OPERATE);
        }


        detailVo.setSrcStepList(srcTransferStepList);
        detailVo.setTargetStepList(targetTransferStepList);
        detailVo.setCommonNodeStep(commonNodeStep);
        return detailVo;
    }

    /**
     * 假如数据结构如下
     * [0,1,2,4,5,6]
     * 公共节点为4,需要移除0 1 2
     *
     * @param commonNodeId   公共节点id
     * @param relationAsList 关系列表list
     */
    private void removeOtherNodes(String commonNodeId, List<String> relationAsList) {
        //todo 修复整建制转接转入，当从其他节点转入到顶级节点下面右边没有问题
        if (relationAsList.size()==CommonConstant.ONE_INT){
            return;
        }
        Iterator<String> iterator = relationAsList.iterator();
        while (iterator.hasNext()) {
            String next = iterator.next();
            if (StrKit.equals(commonNodeId, next)) {
                return;
            }
            iterator.remove();
        }
    }

    /***
     * 创建步骤,并且排除掉公共节点,之前的节点
     * @param relationAsList 关系集合
     * @param transferStepList 步骤集合
     * @param map 使用orgId映射的步骤集合
     * */
    private void addTransferStepToList(String type, String commonNodeId, List<String> relationAsList, List<TransferDetailVo.TransferStep> transferStepList, Map<String, TransferApproval> map) {
        //下个审批的组织id
        String nextOrgId = null;
        for (int i = 0; i < relationAsList.size(); i++){
            String stepOrgId = relationAsList.get(i);
            //生成下一个审批记录key
            String mapKey = stepOrgId;
            if (i + 1 < relationAsList.size()) {
                mapKey += relationAsList.get(i + 1);
            }
            //todo 增加对于直接转到公共节点的兼容
            if (relationAsList.size()==CommonConstant.ONE_INT){
                nextOrgId=relationAsList.get(CommonConstant.ZERO_INT);
            }
            if (StrKit.equals(stepOrgId, commonNodeId)&&relationAsList.size()>CommonConstant.ONE_INT) {
                TransferApproval commonApproval = map.get(mapKey);
                if (commonApproval != null) {
                    if (TransferApprovalConstant.APPROVAL_SUCCESS.equals(commonApproval.getStatus()) || TransferApprovalConstant.APPROVAL_CHANGE_TARGET_ORG.equals(commonApproval.getStatus())) {
                        nextOrgId = commonApproval.getNextOrgId();
                    }
                }
                continue;
            }
            TransferDetailVo.TransferStep step = new TransferDetailVo.TransferStep();
            Org org = orgService.findOrgByCode(stepOrgId);
            if (org != null) {
                step.setOrgCode(org.getOrgCode());
                step.setOrgName(org.getName());
                step.setContacter(org.getContacter());
                step.setContactPhone(org.getContactPhone());
            } else {
                String replaceUrl = sync_flow_push.replace("/api/flow/push", "");
                JSONObject postJson = new JSONObject();
                postJson.put("orgCode", stepOrgId);
                String res = HttpKit.doPost(replaceUrl + "/org/findOrgName", postJson, "UTF-8");
                if (StrKit.notBlank(res)) {
                    JSONObject jsonObject = JSONObject.parseObject(res);
                    if (jsonObject.containsKey("data")) {
                        JSONObject data = jsonObject.getJSONObject("data");
                        if (ObjectUtil.isNotNull(data)) {
                            String name = data.getString("name");
                            String orgCode = data.getString("orgCode");
                            String contacter = data.getString("contacter");
                            String contacterPhone = data.getString("contactPhone");
                            step.setOrgCode(orgCode);
                            step.setOrgName(name);
                            step.setContacter(contacter);
                            step.setContactPhone(contacterPhone);
                        }
                    }
                }
            }
            step.setOrgId(stepOrgId);
            //获取对应的审批记录
            TransferApproval approval = map.get(mapKey);
            if (approval != null) {
                step.setStatus(approval.getStatus());
                //挂上对应的日志
                List<TransferLog> logList = transferLogService.findByHandleApprovalId(approval.getId());
                step.setLogList(logList);
                //如果是已退回或待审核,则一下step不能操作
                if (TransferApprovalConstant.APPROVAL_BACK.equals(approval.getStatus()) || TransferApprovalConstant.APPROVALING.equals(approval.getStatus())) {
                    nextOrgId = null;
                } else {
                    nextOrgId = approval.getNextOrgId();
                }
            } else {
                //如果没有审批记录,判断是否是下一个待审批的组织
                if (StrKit.equals(stepOrgId, nextOrgId)) {
                    step.setStatus(TransferApprovalConstant.APPROVALING);
                } else {
                    step.setStatus(TransferApprovalConstant.NOT_OPERATE);
                }

            }
            transferStepList.add(step);
        }
    }

    private void setCommonStepStatus(String currentApprovalId, TransferDetailVo detailVo, TransferDetailVo.TransferStep commonNodeStep) {
        TransferApproval approval = transferApprovalService.findById(currentApprovalId);
        if (approval != null) {
            if (StrKit.equals(approval.getNextOrgId(), detailVo.getCommonNodeId()) && TransferApprovalConstant.APPROVALING.equals(approval.getStatus())) {
                commonNodeStep.setStatus(TransferApprovalConstant.APPROVALING);
            } else {
                commonNodeStep.setStatus(TransferApprovalConstant.NOT_OPERATE);
            }
        } else {
            commonNodeStep.setStatus(TransferApprovalConstant.NOT_OPERATE);
        }
    }

    @Override
    public OutMessage<String> apply(String recordId, String applyOrgId) {
        //判断用户是否登录
        UserTicket userTicket = UserConstant.USER_CONTEXT.get();
        if (userTicket == null) {
            return new OutMessage<>(Status.NOT_LOGIN);
        }

        TransferRecord record = getById(recordId);
        if (record == null) {
            return new OutMessage<>(Status.PARA_ERROR);
        }
        //不能审批已通过或退回的审批记录
        Integer status = record.getStatus();
        if (!TransferRecordConstant.TRANSFERING.equals(status)) {
            return new OutMessage<>(Status.PARA_ERROR);
        }
        String userManagerOrgId = userTicket.getUserRolePermission().getOrgId();
        //目标组织关系
        List<String> targetOrgRelationAsList = record.getTargetOrgRelationAsList();
        //源组织关系
//        List<String> srcOrgRelationAsList = record.getSrcOrgRelationAsList();
        // TODO: 2021/11/14 下面代码现在是因为层级审核了， 所以没有超过两层以上节点了，所以这个判断需要取消掉
        //如果左右两边都不包含当前用户的id 则没有权限审批
//        if (!(targetOrgRelationAsList.contains(userManagerOrgId) || srcOrgRelationAsList.contains(userManagerOrgId))) {
//            return new OutMessage<>(Status.PERMISSION_DENIED);
//        }
        String currentApprovalId = record.getCurrentApprovalId();
        //当前审批记录
        TransferApproval currentApproval = transferApprovalService.findById(currentApprovalId);
        //审批记录不存在
        if (currentApproval == null) {
            return new OutMessage<>(Status.PARA_ERROR);
        }
        String nextOrgId = currentApproval.getNextOrgId();

        TransferApproval subApproval = transferApprovalService.findSubTransferApprovalByIdAndOrgId(currentApprovalId, currentApproval.getNextOrgId());
        //不允许重复审批
        if (subApproval != null && TransferApprovalConstant.APPROVAL_SUCCESS.equals(subApproval.getStatus()) && StrKit.equals(nextOrgId, applyOrgId)) {
            return new OutMessage<>(Status.TRANSFER_ALREADY_APPLY_ERROR);
        }

        //判断是否有权限审核
        //只能是上级才能审核
        List<String> parentOrgIdList = orgService.findAllParentOrg(nextOrgId).stream().map(Org::getCode).collect(Collectors.toList());
        boolean contains = parentOrgIdList.contains(userManagerOrgId);
        if (!contains) {
            return new OutMessage<>(Status.PERMISSION_DENIED);
        }
        //判断是否代审
        int isInstead = StrKit.equals(nextOrgId, userManagerOrgId) ? TransferApprovalConstant.NOT_INSTEAD : TransferApprovalConstant.IS_INSTEAD;

        //类别为125的时候，需要提前判断人员是否存在数据库中
        Object extraData = record.getExtraData();
        JSONObject jsonObjectPase = (JSONObject) extraData;
        MemDTO transFerMemDto = JSONObject.toJavaObject(jsonObjectPase, MemDTO.class);
        // 处理档案的备份数据
        final MemDTO digMemDTO = JSONObject.toJavaObject(jsonObjectPase, MemDTO.class);
        if (record.getType().equals("125")){
            String name = transFerMemDto.getName();
            String idcard = transFerMemDto.getIdcard();
            List<CheckIdCardDuplicateVO> mapList = memMapper.checkIdCardDuplicate(Collections.singletonList(SM4Untils.encryptContent(exchange_nginx_key, idcard)));
            if (mapList.size()>CommonConstant.ZERO_INT){
                return new OutMessage<String>(Status.MEM_IN_HAVE).format(name,mapList.get(CommonConstant.ZERO_INT).getOrgName()+"(联系电话:"+mapList.get(CommonConstant.ZERO_INT).getContactPhone()+")");
            }
        }
        //子审批记录
        boolean flag = false;
        TransferApproval newApproval = createOrUpdateTransferApproval(subApproval, currentApproval, record, isInstead, TransferApprovalConstant.APPROVAL_SUCCESS);
        if (newApproval != null) {
            //创建一条审核日志
            TransferLog transferLog = new TransferLog();
            transferLog.setHandleApprovalId(newApproval.getId());
            if (TransferApprovalConstant.IS_INSTEAD.equals(isInstead)) {
                //transferLog.setReason("(代审通过:" + CacheUtils.getOrgName(userTicket.getUserRolePermission().getOrgId()) + ")");
                transferLog.setReason("代审通过");
            } else {
                transferLog.setReason("");
            }

            transferLog.setType(TransferLogConstant.SUCCESS_STATUS);
            transferLog.setCreateTime(new Date());
            transferLog.setUpdateTime(new Date());
            flag = transferLogService.rewriteSave(transferLog);

            //判断下一个需要审核的是否是公共节点
            //如果是则判断公共节点是否需要审核
            if (StrKit.equals(newApproval.getNextOrgId(), record.getCommonOrgId())) {
                removeOtherNodes(record.getCommonOrgId(), targetOrgRelationAsList);
                //不需要审核,直接通过
                TransferApproval commonApproval = transferApprovalService.findSubTransferApprovalByIdAndOrgId(newApproval.getId(), newApproval.getNextOrgId());
                if (commonApproval == null) {
                    commonApproval = new TransferApproval();
                    commonApproval.setStatus(TransferApprovalConstant.APPROVAL_SUCCESS);
                    commonApproval.setOrgId(record.getCommonOrgId());
                    if (targetOrgRelationAsList.size() > 1) {
                        commonApproval.setNextOrgId(targetOrgRelationAsList.get(1));
                    } else {
                        commonApproval.setNextOrgId("");
                    }
                    //创建一条默认通过的审核记录
                    commonApproval.setUserId(UserConstant.USER_CONTEXT.get().getUser().getId());
                    commonApproval.setHandlerMan(userService.findMemNameByUserId(commonApproval.getUserId()));
                    commonApproval.setCreateTime(new Date());
                    commonApproval.setUpdateTime(new Date());
                    commonApproval.setIsInstead(isInstead);
                    commonApproval.setRecordId(recordId);
                    commonApproval.setParentId(newApproval.getId());
                    commonApproval.setDirection(TransferApprovalConstant.DIRECTION_COMMON);
                    flag &= transferApprovalService.rewriteSave(commonApproval);
                } else {
                    //更新记录
                    TransferApproval updateTransferApproval = new TransferApproval();
                    updateTransferApproval.setId(commonApproval.getId());
                    updateTransferApproval.setStatus(TransferApprovalConstant.APPROVAL_SUCCESS);
                    if (targetOrgRelationAsList.size() > 1) {
                        commonApproval.setNextOrgId(targetOrgRelationAsList.get(1));
                    } else {
                        commonApproval.setNextOrgId("");
                    }
                    flag &= transferApprovalService.rewriteUpdateById(updateTransferApproval);
                    //查询是否还有子审批记录
                    TransferApproval commonSubTransferApproval = transferApprovalService.findSubTransferApprovalByIdAndOrgId(commonApproval.getId(), commonApproval.getNextOrgId());
                    if (commonSubTransferApproval != null) {
                        TransferApproval updateSubTransfer = new TransferApproval();
                        updateSubTransfer.setId(commonSubTransferApproval.getId());
                        updateSubTransfer.setStatus(TransferApprovalConstant.APPROVALING);
                        flag &= transferApprovalService.rewriteUpdateById(updateSubTransfer);
                    }
                }
                //创建日志
                TransferLog commonLog = new TransferLog();
                commonLog.setHandleApprovalId(commonApproval.getId());
                commonLog.setReason(TransferLogConstant.SUCCESS_REASON);
                commonLog.setType(TransferLogConstant.SUCCESS_STATUS);
                commonLog.setCreateTime(new Date());
                commonLog.setUpdateTime(new Date());
                flag &= transferLogService.rewriteSave(commonLog);
                newApproval = commonApproval;
            }
            //更改转接记录中的currentId
            TransferRecord updateTransferRecord = new TransferRecord();
            updateTransferRecord.setId(record.getId());
            updateTransferRecord.setCurrentApprovalId(newApproval.getId());
            //判断是否到末尾  类别224因为如果转出到省外的中间交换区类别还不能直接完成，需要等中间交换区进行处理完成才能完成，中间节点审核完成，就等于完成，等着全国交换区反馈进行处理数据即可
            String transferRecordtype = record.getType();
            // TODO: 2023/4/28 如果审核的是省外转入,当省外转入的审核的下一个节点是录入党支部的时候,整个流程就算完成了
            if (transferRecordtype.equals("125")&&newApproval.getNextOrgId().equals(record.getTargetOrgId())){
                //处理支部审核步骤
                TransferApproval endTransferApprovl= new TransferApproval();
                String endApprovalId = StrKit.getRandomUUID();
                endTransferApprovl.setId(endApprovalId);
                endTransferApprovl.setRecordId(recordId);
                endTransferApprovl.setUserId(userTicket.getUser().getId());
                endTransferApprovl.setOrgId(newApproval.getNextOrgId());
                endTransferApprovl.setNextOrgId("");
                endTransferApprovl.setIsInstead(CommonConstant.ONE_INT);
                endTransferApprovl.setStatus(CommonConstant.ONE_INT);
                endTransferApprovl.setParentId(newApproval.getId());
                endTransferApprovl.setCreateTime(new Date());
                endTransferApprovl.setDirection(CommonConstant.ONE_INT);
                endTransferApprovl.setHandlerMan(userTicket.getUser().getAccount());

                updateTransferRecord.setStatus(TransferRecordConstant.TRANSFER_SUCCESS);
                updateTransferRecord.setUpdateTime(new Date());
                updateTransferRecord.setCurrentApprovalId(endApprovalId);
                OutMessage outMessage = memService.addMem(transFerMemDto);
                int code = outMessage.getCode();
                flag &= code == Status.SUCCESS.getCode();
                flag &= transferApprovalService.rewriteSave(endTransferApprovl);
                if(code == Status.SUCCESS.getCode()){
                    // todo ********: 遵义原因接收党员需要添加流程或者档案
                    this.withDig(digMemDTO, ((Mem)outMessage.getData()).getCode());
                }
            }

            if (StrKit.isBlank(newApproval.getNextOrgId()) && !"224".equals(transferRecordtype)) {
                //整条转接记录就完成了
                updateTransferRecord.setStatus(TransferRecordConstant.TRANSFER_SUCCESS);
                updateTransferRecord.setUpdateTime(new Date());
                //接下来需要判断是否是人员转接记录或组织转接记录
                //如果是人员转接记录,则调整人员
                //如果是组织转接记录,则调整整个组织
                if (transferRecordtype.equals("125")){
                    OutMessage outMessage = memService.addMem(transFerMemDto);
                    int code = outMessage.getCode();
                    flag &= code == Status.SUCCESS.getCode();
                    if(code == Status.SUCCESS.getCode()){
                        // todo ********: 遵义原因接收党员需要添加流程或者档案
                        this.withDig(digMemDTO, ((Mem)outMessage.getData()).getCode());
                    }
                }else {
                    flag &= transferAdjust(record);
                }

            }
            flag &= updateById(updateTransferRecord);
        }
        // TODO: 2021/12/6  处理党员系统的是否在转接中的标示
        this.updateMemIsTransfer(recordId, Collections.singletonList(record.getMemId()));
        // TODO: 2021/12/1 增加同步memAll表的同步
        //CompletableFuture.supplyAsync(() -> iSyncMemService.syncMem(record.getMemId(), CommonConstant.ONE), mySimpleAsync);
        // TODO: 2021/11/17 审核的时候如果接收节点不是当前数据库内，需要走中间交换区更新数据
        String commonOrgId = record.getCommonOrgId();
        Org orgByCommCode = orgService.findOrgByCode(commonOrgId);
        String targetOrgId = record.getTargetOrgId();
        if (flag && ObjectUtil.isNull(orgByCommCode) && StrKit.notBlank(targetOrgId)) {
            // TODO: 2021/12/27  这里审核要分为两种同步，1.跨节点单人转接同步 2.跨节点整建制转接审核同步
            //查询转接记录
            TransferRecord recordNow = getById(recordId);
            String type = recordNow.getType();
            //处理携带的人员信息
            String memId = recordNow.getMemId();
            //1.跨节点单人转接同步
            if (StrKit.notBlank(memId) || type.equals(21)) {
                //这里可能有一个bug，导致两边都没人，那就是如果这个人在两个节点之间转过去又转回来的情况
                Mem allByCode = memService.findAllByCode(memId);
                if (ObjectUtil.isNotNull(allByCode)) {
                    JSONObject extraDataJsonObject = JSONObject.parseObject(JSONObject.toJSONString(allByCode));
                    recordNow.setExtraData(extraDataJsonObject);
                    // todo 2025-03-20: 个人党员转出添加档案信息
                    if(StrUtil.isNotBlank(allByCode.getDigitalLotNo())) {
                        this.digLotNoData(allByCode.getDigitalLotNo(), extraDataJsonObject);
                    }
                }
            }
            //2.跨节点整建制转接审核同步
            if (StrKit.isBlank(memId) || type.equals(212)) {
                String srcOrgId = recordNow.getSrcOrgId();
                Org orgBySrcCode = orgService.findOrgByCode(srcOrgId);
                //右边审核得时候，当前数据节点不能生成数据， 那样会出现数据为空的,这样虽然临时解决了，但是反复转的时候还是会有问题
                if (ObjectUtil.isNotNull(orgBySrcCode)) {
                    JSONObject dataJsonObject = new JSONObject();
                    iOrgTransferRecordService.deailOrgTransferData(orgBySrcCode.getOrgCode(), dataJsonObject);
                    recordNow.setDataText(dataJsonObject.toJSONString());
                }
            }
            JSONObject jsonObject = JSONObject.parseObject(JSONObject.toJSONString(recordNow));
            JSONArray jsonArray = new JSONArray();
            //查询转接节点审批记录
            List<TransferApproval> byRecordId = transferApprovalService.findByRecordId(recordNow.getId());
            byRecordId.forEach(TransferApproval -> jsonArray.add(JSONObject.parseObject(JSONObject.toJSONString(TransferApproval))));
            jsonObject.put("approval", jsonArray);
            // 125  跨省转入（未接入全国交换区） 223  跨省转出（未接入全国交换区） 这两种类型不应该进入交换区
            if (!type.equals("125") && !type.equals("223")){
                String replace = sync_flow_push.replace("/api/flow/push", "");
                HttpKit.doPost(replace + "/transfer/insertData", jsonObject, "UTF-8");
            }
        }

        return flag ? new OutMessage<>(Status.SUCCESS) : new OutMessage<>(Status.FAIL);
    }

    /***
     * 创建或者更新一条审批记录
     * @param subApproval 子审批记录
     * @param parentApproval 父审批记录
     * @param record 转接记录
     * @param isInstead 是否待审
     * @param status 审批状态
     * */
    private TransferApproval createOrUpdateTransferApproval(TransferApproval subApproval, TransferApproval parentApproval, TransferRecord record, int isInstead, int status) {
        boolean flag;
        List<String> targetOrgRelationAsList = record.getTargetOrgRelationAsList();
        List<String> srcOrgRelationAsList = record.getSrcOrgRelationAsList();
        removeOtherNodes(record.getCommonOrgId(), targetOrgRelationAsList);
        removeOtherNodes(record.getCommonOrgId(), srcOrgRelationAsList);
        if (subApproval == null) {
            subApproval = new TransferApproval();
            subApproval.setParentId(parentApproval.getId());
            subApproval.setRecordId(record.getId());
            subApproval.setOrgId(parentApproval.getNextOrgId());
            boolean result = setNextOrgId(parentApproval, subApproval, record, targetOrgRelationAsList, srcOrgRelationAsList);
            if (!result) {
                return null;
            }
            subApproval.setIsInstead(isInstead);
            subApproval.setStatus(status);
            subApproval.setCreateTime(new Date());
            subApproval.setUpdateTime(new Date());
            subApproval.setUserId(UserConstant.USER_CONTEXT.get().getUser().getId());
            subApproval.setHandlerMan(userService.findMemNameByUserId(subApproval.getUserId()));
            flag = transferApprovalService.rewriteSave(subApproval);
        } else {
            //如果已经存在审批记录,则代表有退回记录
            TransferApproval updateTransferApproval = new TransferApproval();
            updateTransferApproval.setId(subApproval.getId());
            updateTransferApproval.setIsInstead(isInstead);
            updateTransferApproval.setStatus(status);
            updateTransferApproval.setUpdateTime(new Date());
            //因为存在改变目标组织节点的原因
            //需要再次判断该节点是属于那边的节点
            boolean result = setNextOrgId(parentApproval, updateTransferApproval, record, targetOrgRelationAsList, srcOrgRelationAsList);
            if (!result) {
                return null;
            }
            flag = transferApprovalService.rewriteUpdateById(updateTransferApproval);
            if (TransferApprovalConstant.APPROVAL_SUCCESS.equals(status) || TransferApprovalConstant.APPROVAL_CHANGE_TARGET_ORG.equals(status)) {
                //如果把退回的记录审核通过了,如果下一个step也是退回需要改变状态为待审核
                TransferApproval nextStep = transferApprovalService.findSubTransferApprovalByIdAndOrgId(updateTransferApproval.getId(), updateTransferApproval.getNextOrgId());
                if (nextStep != null && TransferApprovalConstant.APPROVAL_BACK.equals(nextStep.getStatus())) {
                    TransferApproval updateNextStep = new TransferApproval();
                    updateNextStep.setId(nextStep.getId());
                    updateNextStep.setStatus(TransferApprovalConstant.APPROVALING);
                    flag &= transferApprovalService.rewriteUpdateById(updateNextStep);
                }

            }

        }
        return flag ? subApproval : null;
    }


    /***
     * 设置下一个组织id
     * */
    private boolean setNextOrgId(TransferApproval parentApproval, TransferApproval approval, TransferRecord record, List<String> targetOrgRelationAsList, List<String> srcOrgRelationAsList) {
        if (StrKit.equals(parentApproval.getOrgId(), record.getCommonOrgId()) || TransferApprovalConstant.DIRECTION_TARGET.equals(parentApproval.getDirection())) {
            //该节点过了公共节点,属于转入方
            approval.setDirection(TransferApprovalConstant.DIRECTION_TARGET);
            return setNextOrgIdForTarget(targetOrgRelationAsList, record, parentApproval, approval);
        } else {
            if (StrKit.equals(record.getCommonOrgId(), parentApproval.getNextOrgId())) {
                approval.setDirection(TransferApprovalConstant.DIRECTION_COMMON);
            } else {
                approval.setDirection(TransferApprovalConstant.DIRECTION_SRC);
            }
            return setNextOrgIdForSrc(targetOrgRelationAsList, srcOrgRelationAsList, parentApproval, approval);
        }
    }

    /***
     * 设置下一个组织id 当目标组织时
     * */
    private boolean setNextOrgIdForTarget(List<String> targetOrgRelationAsList, TransferRecord record, TransferApproval parentApproval, TransferApproval approval) {
        if (StrKit.equals(parentApproval.getNextOrgId(), record.getTargetOrgId())) {
            //到达了目标节点
            approval.setNextOrgId("");
        } else {
            if(record.getType().equals("125")){
                int index = targetOrgRelationAsList.indexOf(parentApproval.getNextOrgId());
                if (index == -1) {
                    return false;
                }
                if (index + 1 > targetOrgRelationAsList.size()) {
                    return false;
                }
                approval.setNextOrgId(targetOrgRelationAsList.get(index + 1));
            }else {
                approval.setNextOrgId(targetOrgRelationAsList.get(targetOrgRelationAsList.size() - 1));
            }
        }
        return true;
    }

    /***
     * 设置下一个组织id 当源组织时
     * */
    private boolean setNextOrgIdForSrc(List<String> targetOrgRelationAsList, List<String> srcOrgRelationAsList, TransferApproval parentApproval, TransferApproval approval) {
        int index = srcOrgRelationAsList.indexOf(parentApproval.getNextOrgId());
        if (index == -1) {
            return false;
        }
        if (index - 1 < 0) {
            //如果获得的下标为0 说明是审批走到了源组织那边的最后一步了
            if (index == 0) {
                if (targetOrgRelationAsList.size() > 1) {
                    approval.setNextOrgId(targetOrgRelationAsList.get(1));
                } else {
                    approval.setNextOrgId("");
                }
            } else {
                approval.setNextOrgId("");
            }
        } else {
            approval.setNextOrgId(srcOrgRelationAsList.get(index - 1));
        }
        return true;

    }

    @Override
    public OutMessage<String> back(String recordId, String backReason) {
        //判断用户是否登录
        UserTicket userTicket = UserConstant.USER_CONTEXT.get();
        if (userTicket == null) {
            return new OutMessage<>(Status.NOT_LOGIN);
        }
        TransferRecord record = getById(recordId);
        if (record == null) {
            return new OutMessage<>(Status.PARA_ERROR);
        }
        //不能审批已通过或退回的审批记录
        Integer status = record.getStatus();
        if (!TransferRecordConstant.TRANSFERING.equals(status)) {
            return new OutMessage<>(Status.PARA_ERROR);
        }
        String userManagerOrgId = userTicket.getUserRolePermission().getOrgId();
//        //目标组织关系
//        List<String> targetOrgRelationAsList = record.getTargetOrgRelationAsList();
//        //源组织关系
//        List<String> srcOrgRelationAsList = record.getSrcOrgRelationAsList();
        //如果左右两边都不包含当前用户管理的的id 则没有权限审批
//        if (!(targetOrgRelationAsList.contains(userManagerOrgId) || srcOrgRelationAsList.contains(userManagerOrgId))) {
//            return new OutMessage<>(Status.PERMISSION_DENIED);
//        }
        String currentApprovalId = record.getCurrentApprovalId();
        //当前审批记录
        TransferApproval currentApproval = transferApprovalService.findById(currentApprovalId);
        //审批记录不存在
        if (currentApproval == null) {
            return new OutMessage<>(Status.PARA_ERROR);
        }
        String nextOrgId = currentApproval.getNextOrgId();
        //判断是否有权限审核
        //只能是上级才能审核
        List<String> parentOrgIdList = orgService.findAllParentOrg(nextOrgId).stream().map(Org::getCode).collect(Collectors.toList());
        boolean contains = parentOrgIdList.contains(userManagerOrgId);
        if (!contains) {
            return new OutMessage<>(Status.PERMISSION_DENIED);
        }
        //判断是否代审
        int isInstead = StrKit.equals(nextOrgId, userManagerOrgId) ? TransferApprovalConstant.NOT_INSTEAD : TransferApprovalConstant.IS_INSTEAD;

        boolean flag = false;
        TransferApproval subApproval = transferApprovalService.findSubTransferApprovalByIdAndOrgId(currentApprovalId, currentApproval.getNextOrgId());
        subApproval = createOrUpdateTransferApproval(subApproval, currentApproval, record, isInstead, TransferApprovalConstant.APPROVAL_BACK);
        if (subApproval != null) {
            String id = currentApproval.getParentId();
            TransferApproval previousApproval = transferApprovalService.findById(id);
            TransferRecord updateTransferRecord = new TransferRecord();
            updateTransferRecord.setId(recordId);
            //退回到根节点,则代表整条转接记录失败
            if (currentApproval.getParentId().equals(TransferApprovalConstant.ROOT_ID)) {
                //撤销转接记录
                updateTransferRecord.setStatus(TransferRecordConstant.TRANSFER_UNDO);
                updateTransferRecord.setUpdateTime(new Date());
                TransferApproval updateApproval = new TransferApproval();
                updateApproval.setId(currentApprovalId);
                updateApproval.setStatus(TransferApprovalConstant.APPROVAL_UNDO);
                if (record.getType().equals(CommonConstant.OneHundredTwentyFour_INT)) {
                    updateTransferRecord.setStatus(TransferApprovalConstant.APPROVAL_BACK);
                }
                System.out.println("updateApproval: " + updateApproval);
                flag = transferApprovalService.rewriteUpdateById(updateApproval);
            } else {
                //回退到上一个步骤
                //如果退回到公共节点,则跳过公共节点
                TransferApproval updateApproval = new TransferApproval();
                if (StrKit.equals(currentApproval.getOrgId(), record.getCommonOrgId())) {
                    TransferApproval updateCommonApproval = new TransferApproval();
                    updateCommonApproval.setId(currentApprovalId);
                    updateCommonApproval.setStatus(TransferApprovalConstant.APPROVAL_BACK);
                    TransferLog transferLog = new TransferLog();
                    transferLog.setHandleApprovalId(currentApprovalId);
                    transferLog.setEffectApprovalId(previousApproval.getId());
                    transferLog.setType(TransferLogConstant.BACK_STATUS);
                    transferLog.setCreateTime(new Date());
                    transferLog.setUpdateTime(new Date());
                    //是否待审
                    if (TransferApprovalConstant.IS_INSTEAD.equals(isInstead)) {
                        transferLog.setReason("(代审退回:" + CacheUtils.getOrgName(UserConstant.USER_CONTEXT.get().getUserRolePermission().getOrgId()) + ")原因:" + backReason);
                    } else {
                        transferLog.setReason("(原因:" + backReason + ")");

                    }
                    transferApprovalService.rewriteUpdateById(updateCommonApproval);
                    transferLogService.rewriteSave(transferLog);
                    updateApproval.setId(previousApproval.getId());
                    //判断是否回退到根节点
                    if (TransferApprovalConstant.ROOT_ID.equals(previousApproval.getParentId())) {
                        updateTransferRecord.setCurrentApprovalId(previousApproval.getId());
                        updateTransferRecord.setStatus(TransferApprovalConstant.APPROVAL_BACK);
                        updateTransferRecord.setUpdateTime(new Date());
                        if (record.getType().equals(CommonConstant.OneHundredTwentyFour_INT)) {
                            updateTransferRecord.setStatus(TransferApprovalConstant.APPROVAL_BACK);
                        }

                    } else {
                        updateTransferRecord.setCurrentApprovalId(previousApproval.getParentId());
                    }
                } else {
                    updateApproval.setId(currentApprovalId);
                    updateTransferRecord.setCurrentApprovalId(previousApproval.getId());
                }
                updateApproval.setStatus(TransferApprovalConstant.APPROVALING);
                flag = transferApprovalService.rewriteUpdateById(updateApproval);
            }
            //生成记录
            TransferLog transferLog = new TransferLog();
            transferLog.setHandleApprovalId(subApproval.getId());
            transferLog.setEffectApprovalId(currentApprovalId);
            //是否待审
            if (TransferApprovalConstant.IS_INSTEAD.equals(isInstead)) {
                transferLog.setReason("(代审退回:" + CacheUtils.getOrgName(UserConstant.USER_CONTEXT.get().getUserRolePermission().getOrgId()) + ")原因:" + backReason);
            } else {
                transferLog.setReason("(原因:" + backReason + ")");
            }
            transferLog.setType(TransferLogConstant.BACK_STATUS);
            transferLog.setCreateTime(new Date());
            transferLog.setUpdateTime(new Date());
            //保存记录
            flag &= transferLogService.rewriteSave(transferLog);
            //修改转接记录
            flag &= updateById(updateTransferRecord);

        }
        this.updateMemIsTransfer(recordId, Collections.singletonList(record.getMemId()));
        // TODO: 2021/11/17 如果是回退的时候， 查看中间节点是否在当前数据库里面
        Org orgByCode = orgService.findOrgByCode(record.getCommonOrgId());
        if (flag && ObjectUtil.isNull(orgByCode)) {
            //查询转接记录
            TransferRecord recordNow = getById(recordId);
            //处理携带的人员信息
            String memId = recordNow.getMemId();
            Mem allByCode = memService.findAllByCode(memId);
            if (ObjectUtil.isNotNull(allByCode)) {
                recordNow.setExtraData(JSONObject.parseObject(JSONObject.toJSONString(allByCode)));
                //recordNow.setExtraData(allByCode);
            }
            JSONObject jsonObject = JSONObject.parseObject(JSONObject.toJSONString(recordNow));
            JSONArray jsonArray = new JSONArray();
            //查询转接节点审批记录
            List<TransferApproval> byRecordId = transferApprovalService.findByRecordId(recordNow.getId());
            byRecordId.forEach(TransferApproval -> jsonArray.add(JSONObject.parseObject(JSONObject.toJSONString(TransferApproval))));
            jsonObject.put("approval", jsonArray);
            String replaceUrl = sync_flow_push.replace("/api/flow/push", "");
            HttpKit.doPost(replaceUrl + "/transfer/insertData", jsonObject, "UTF-8");
        }
        return flag ? new OutMessage<>(Status.SUCCESS) : new OutMessage<>(Status.FAIL);
    }

    @Override
    public OutMessage<String> undo(String recordId, String reason, boolean isCheckUnDO) {
        TransferRecord record = getById(recordId);
        if (record == null) {
            return new OutMessage<>(Status.PARA_ERROR);
        }
        //不能更改已通过或退回的审批记录
        Integer status = record.getStatus();
        if (!TransferRecordConstant.TRANSFERING.equals(status)) {
            return new OutMessage<>(Status.PARA_ERROR);
        }
        //非本人发起转接
//        if (isCheckUnDO) {
//            UserTicket userTicket = UserConstant.USER_CONTEXT.get();
//            if (userTicket == null) {
//                return new OutMessage<>(Status.NOT_LOGIN);
//            }
//            if (!StrKit.equals(record.getUserId(), userTicket.getUser().getId())) {
//                return new OutMessage<>(Status.TRANSFER_UNDO_PERMISSION_ERROR);
//            }
//        }
        TransferApproval transferApproval = transferApprovalService.findByRecordIdAndParentId(recordId, TransferApprovalConstant.ROOT_ID);
        if (transferApproval == null) {
            return new OutMessage<>(Status.PARA_ERROR);
        }
        return this.backTransfer(recordId, reason) ? new OutMessage<>(Status.SUCCESS) : new OutMessage<>(Status.FAIL);
    }

    @Override
    public boolean backTransfer(String recordId, String reason) {
        TransferRecord record = getById(recordId);
        TransferApproval transferApproval = transferApprovalService.findByRecordIdAndParentId(recordId, TransferApprovalConstant.ROOT_ID);
        //更新转接记录状态
        TransferRecord updateRecord = new TransferRecord();
        updateRecord.setId(recordId);
        updateRecord.setStatus(TransferRecordConstant.TRANSFER_UNDO);
        updateRecord.setUpdateTime(new Date());
        updateRecord.setRemark(reason);

        //生成一条撤销记录日志
        TransferLog transferLog = new TransferLog();
        transferLog.setHandleApprovalId(transferApproval.getId());
        transferLog.setReason(reason);
        transferLog.setType(TransferLogConstant.UNDO_STATUS);

        // TODO: 2021/7/16 事务
        boolean b1 = updateById(updateRecord);
        boolean b2 = transferLogService.rewriteSave(transferLog);
        String recordMemId = record.getMemId();
        this.updateMemIsTransfer(recordId, Collections.singletonList(recordMemId));
        // TODO: 2021/12/6 撤销同步all表数据
//        // TODO: 2021/12/6  发起关系转接的时候，同步人员all表数据
//        CompletableFuture.supplyAsync(() -> iSyncMemService.syncMem(recordMemId, CommonConstant.ONE), mySimpleAsync);
        // TODO: 2021/11/17 撤销的时候，如果接受节点不在当前数据库就需要同步到中间交换区，让接受数据节点知晓
        String targetOrgId = record.getTargetOrgId();
        Org orgByCode = orgService.findOrgByCode(targetOrgId);
        if (b1 && b2 && ObjectUtil.isNull(orgByCode) && StrKit.notBlank(targetOrgId)) {
            //查询转接记录
            TransferRecord recordNow = getById(record.getId());
            //处理携带的人员信息
            String memId = recordNow.getMemId();
            Mem allByCode = memService.findAllByCode(memId);
            if (ObjectUtil.isNotNull(allByCode)) {
                //recordNow.setExtraData(allByCode);
                JSONObject extraDataJsonObject = JSONObject.parseObject(JSONObject.toJSONString(allByCode));
                recordNow.setExtraData(extraDataJsonObject);
                // todo 2025-03-20: 个人党员转出添加档案信息
                if(StrUtil.isNotBlank(allByCode.getDigitalLotNo())) {
                    this.digLotNoData(allByCode.getDigitalLotNo(), extraDataJsonObject);
                }
            }
            JSONObject jsonObject = JSONObject.parseObject(JSONObject.toJSONString(recordNow));
            JSONArray jsonArray = new JSONArray();
            //查询转接节点审批记录
            List<TransferApproval> byRecordId = transferApprovalService.findByRecordId(recordNow.getId());
            byRecordId.forEach(TransferApproval -> jsonArray.add(JSONObject.parseObject(JSONObject.toJSONString(TransferApproval))));
            jsonObject.put("approval", jsonArray);
            String replaceUrl = sync_flow_push.replace("/api/flow/push", "");
            HttpKit.doPost(replaceUrl + "/transfer/insertData", jsonObject, "UTF-8");
        }
        return b1 && b2;
    }

    @Override
    public OutMessage<String> editTransferForMem(TransferRecordDTO transferRecordDTO) {

        String recordId = transferRecordDTO.getId();
        TransferRecord transferRecord = getById(recordId);
        if (transferRecord == null) {
            return new OutMessage<>(Status.TRANSFER_CHANGE_MEM_FEE_ERROR);
        }
        if (!TransferRecordConstant.TRANSFERING.equals(transferRecord.getStatus())) {
            return new OutMessage<>(Status.TRANSFER_CHANGE_MEM_FEE_ERROR);
        }
        if (StrKit.isBlank(transferRecord.getMemId())) {
            return new OutMessage<>(Status.TRANSFER_CHANGE_MEM_FEE_ERROR);
        }

        TransferRecord updateRecord = new TransferRecord();
        updateRecord.setId(recordId);
        boolean isUpdate = false;
        if (transferRecordDTO.getMemFeeStandard() != null) {
            updateRecord.setMemFeeStandard(transferRecordDTO.getMemFeeStandard());
            isUpdate = true;
        }
        if (transferRecordDTO.getMemFeeEndTime() != null) {
            updateRecord.setMemFeeEndTime(transferRecordDTO.getMemFeeEndTime());
            isUpdate = true;
        }
        if (StrKit.notBlank(transferRecordDTO.getReason())) {
            updateRecord.setReason(transferRecordDTO.getReason());
            isUpdate = true;
        }
        boolean isOk = true;
        if (isUpdate) {
            isOk = updateById(updateRecord);
        }
        return isOk ? new OutMessage<>(Status.SUCCESS) : new OutMessage<>(Status.FAIL);
    }

    @Override
    public OutMessage<TransferMemInfoVO> memInfo(String recordId) {

        TransferRecord record = getById(recordId);
        if (record == null) {
            return new OutMessage<>(Status.FAIL);
        }

        Integer status = record.getStatus();
        if (!TransferRecordConstant.TRANSFERING.equals(status)) {
            return new OutMessage<>(Status.FAIL);
        }
        String memId = record.getMemId();
        if (StrKit.isBlank(memId)) {
            return new OutMessage<>(Status.FAIL);
        }

        Mem mem = memService.findByCode(memId);
        TransferMemInfoVO infoVO = new TransferMemInfoVO();
        infoVO.setRecordId(record.getId());
        infoVO.setMemFeeStandard(record.getMemFeeStandard());
        infoVO.setMemFeeEndTime(record.getMemFeeEndTime());
        infoVO.setMemName(record.getName());
        infoVO.setReason(record.getReason());
        infoVO.setD146Code(record.getD146Code());
        infoVO.setD146Name(record.getD146Name());
        if (mem != null) {
            infoVO.setMemType(mem.getD08Name());
            infoVO.setIdCard(mem.getIdcard());
            infoVO.setPhone(mem.getPhone());
        }
        return new OutMessage<>(Status.SUCCESS, infoVO);
    }

    @Override
    public OutMessage<String> changeTargetOrg(TransferRecordDTO data) {
        UserTicket userTicket = UserConstant.USER_CONTEXT.get();
        if (userTicket == null) {
            return new OutMessage<>(Status.NOT_LOGIN);
        }
        TransferRecord record = getById(data.getId());
        if (record == null) {
            return new OutMessage<>(Status.PARA_ERROR);
        }
        //不能更改已通过或撤销的审批记录
        Integer status = record.getStatus();
        if (!TransferRecordConstant.TRANSFERING.equals(status)) {
            return new OutMessage<>(Status.PARA_ERROR);
        }
        //目标组织关系
        List<String> targetOrgRelationAsList = record.getTargetOrgRelationAsList();
        if (targetOrgRelationAsList == null || targetOrgRelationAsList.isEmpty()) {
            return new OutMessage<>(Status.TRANSFER_CHANGE_TARGET_EMPTY_TARGET_ERROR);
        }
        //处理变更后的目的组织关系
        String targetOrgId = data.getTargetOrgId();
        targetOrgRelationAsList.add(targetOrgId);

        //处理当前审核组织默认为通过
        //查询当前审核的最后一条
        String currentApprovalId = record.getCurrentApprovalId();
        TransferApproval currentApproval = transferApprovalService.findById(currentApprovalId);
        record.setNext_org_id(currentApproval.getNextOrgId());
        TransferApproval oneTransferApproval = new TransferApproval();
        String oneAppId = StrKit.getRandomUUID().replace("-", "");
        oneTransferApproval.setId(oneAppId);
        oneTransferApproval.setRecordId(record.getId());
        oneTransferApproval.setUserId(userTicket.getUser().getId());
        oneTransferApproval.setOrgId(record.getNext_org_id());
        oneTransferApproval.setNextOrgId(data.getTargetOrgId());
        int isInstead = StrKit.equals(record.getNext_org_id(), userTicket.getUserRolePermission().getOrgId()) ? TransferApprovalConstant.NOT_INSTEAD : TransferApprovalConstant.IS_INSTEAD;
        oneTransferApproval.setIsInstead(isInstead);
        oneTransferApproval.setStatus(CommonConstant.ONE_INT);
        oneTransferApproval.setParentId(record.getCurrentApprovalId());
        oneTransferApproval.setCreateTime(new Date());
        oneTransferApproval.setUpdateTime(new Date());
        oneTransferApproval.setDirection(CommonConstant.ONE_INT);

        //处理更新主流程情况数据
        TransferRecord updateTransfer = new TransferRecord();
        updateTransfer.setId(record.getId());
        updateTransfer.setCurrentApprovalId(oneAppId);
        Org orgByOrgCode = orgService.findOrgByOrgId(targetOrgId);
        updateTransfer.setTargetOrgId(targetOrgId);
        updateTransfer.setTargetOrgRelationAsList(targetOrgRelationAsList);
        updateTransfer.setTargetOrgRelation(targetOrgRelationAsList);
        updateTransfer.setTargetOrgName(orgByOrgCode.getName());

        boolean updateById = updateById(updateTransfer);
        boolean save = transferApprovalService.save(oneTransferApproval);
        //触发变更本地交换区
        if (updateById && save) {
            //查询转接记录
            TransferRecord recordNow = getById(record.getId());
            JSONObject jsonObject = JSONObject.parseObject(JSONObject.toJSONString(recordNow));
            JSONArray jsonArray = new JSONArray();
            //查询转接节点审批记录
            List<TransferApproval> byRecordId = transferApprovalService.findByRecordId(recordNow.getId());
            byRecordId.forEach(TransferApproval -> jsonArray.add(JSONObject.parseObject(JSONObject.toJSONString(TransferApproval))));
            jsonObject.put("approval", jsonArray);
            String replaceUrl = sync_flow_push.replace("/api/flow/push", "");
            HttpKit.doPost(replaceUrl + "/transfer/insertData", jsonObject, "UTF-8");
        }
        return new OutMessage<>(updateById && save ? Status.SUCCESS : Status.FAIL);
    }


    public OutMessage<String> changeTargetOrgOld(TransferRecordDTO data) {
        UserTicket userTicket = UserConstant.USER_CONTEXT.get();
        if (userTicket == null) {
            return new OutMessage<>(Status.NOT_LOGIN);
        }
        TransferRecord record = getById(data.getId());
        if (record == null) {
            return new OutMessage<>(Status.PARA_ERROR);
        }
        //不能更改已通过或插销的审批记录
        Integer status = record.getStatus();
        if (!TransferRecordConstant.TRANSFERING.equals(status)) {
            return new OutMessage<>(Status.PARA_ERROR);
        }
        String userManagerOrgId = userTicket.getUserRolePermission().getOrgId();
        //目标组织关系
        List<String> targetOrgRelationAsList = record.getTargetOrgRelationAsList();
        if (targetOrgRelationAsList == null || targetOrgRelationAsList.isEmpty()) {
            return new OutMessage<>(Status.TRANSFER_CHANGE_TARGET_EMPTY_TARGET_ERROR);
        }
        //源组织关系
//        List<String> srcOrgRelationAsList = record.getSrcOrgRelationAsList();
        //如果左右两边都不包含当前用户管理的的id 则没有权限审批
//        if (!(targetOrgRelationAsList.contains(userManagerOrgId) || srcOrgRelationAsList.contains(userManagerOrgId))) {
//            return new OutMessage<>(Status.PERMISSION_DENIED);
//        }
        String currentApprovalId = record.getCurrentApprovalId();
        TransferApproval currentApproval = transferApprovalService.findById(currentApprovalId);

        //限制目标组织范围
        //判断当前步骤是否过了公共节点
        //如果过了公共节点那么就属于转入
        //如果没过公共节点那么就属于转出
        boolean isLeft;
        if (TransferApprovalConstant.DIRECTION_SRC.equals(currentApproval.getDirection())) {
            isLeft = true;
        } else if (TransferApprovalConstant.DIRECTION_COMMON.equals(currentApproval.getDirection())) {
            isLeft = false;
        } else {
            isLeft = false;
        }

        String oldOrgId = currentApproval.getOrgId();
        String newOrgId = data.getTargetOrgId();
        String type = record.getType();
        if (!type.equals(CommonConstant.OneHundredTwentyFour_INT)) {
            if (isLeft) {
                //左边逻辑,目标组织不能是当前组织及其下级
                boolean isOk = orgService.isSubOrg(oldOrgId, newOrgId);
                if (isOk) {
                    return new OutMessage<>(Status.TRANSFER_CHANGE_TARGET_FOR_SRC_ERROR);
                }
            } else {
                //右边逻辑,目标组织只能是当前组织下级
                boolean isOk = orgService.isSubOrg(oldOrgId, newOrgId);
                if (!isOk) {
                    return new OutMessage<>(Status.TRANSFER_CHANGE_TARGET_FOR_TARGET_ERROR);
                }
            }
        }
        //更改目标组织,以及目标组织关系
        TransferRecord updateRecord = new TransferRecord();
        updateRecord.setId(record.getId());
        updateRecord.setType(type);
        updateRecord.setInType(type);
        updateRecord.setOutType(type);
        updateRecord.setUpdateTime(new Date());
        updateRecord.setTargetOrgName(data.getTargetOrgName());
        //判断是否是组织关系转接
        boolean isOrg = StrKit.isBlank(record.getMemId());
//        Ret ret = setSrcAndTargetOrgId(record.getSrcOrgId(), data.getTargetOrgId(), updateRecord, isOrg);
//        if (ret.isFail()) {
//            return ret.getAs(RetConstant.MESSAGE);
//        }
//        ret = setSrcAndTargetRelation(updateRecord.getSrcOrgId(), updateRecord.getTargetOrgId(), updateRecord, false);
//        if (ret.isFail()) {
//            return ret.getAs(RetConstant.MESSAGE);
//        }
//        ret = setCommonNodeId(updateRecord.getSrcOrgId(), updateRecord.getTargetOrgId(), updateRecord);
//        if (ret.isFail()) {
//            return ret.getAs(RetConstant.MESSAGE);
//        }

        //由于数据已经修改 在查询一次
        currentApproval = transferApprovalService.findById(currentApprovalId);
        //审批记录不存在
        if (currentApproval == null) {
            return new OutMessage<>(Status.PARA_ERROR);
        }
        String nextOrgId = currentApproval.getNextOrgId();
        //判断是否有权限审核
        //只能是上级才能审核
        List<String> parentOrgIdList = orgService.findAllParentOrg(nextOrgId).stream().map(Org::getCode).collect(Collectors.toList());
        boolean contains = parentOrgIdList.contains(userManagerOrgId);
        if (!contains) {
            return new OutMessage<>(Status.PERMISSION_DENIED);
        }
        //判断是否代审
        int isInstead = StrKit.equals(nextOrgId, userManagerOrgId) ? TransferApprovalConstant.NOT_INSTEAD : TransferApprovalConstant.IS_INSTEAD;
        boolean flag = false;

        //新增或者更改一条审批记录
        TransferApproval subApproval = transferApprovalService.findSubTransferApprovalByIdAndOrgId(currentApprovalId, currentApproval.getNextOrgId());
        subApproval = createOrUpdateTransferApproval(subApproval, currentApproval, updateRecord, isInstead, TransferApprovalConstant.APPROVAL_CHANGE_TARGET_ORG);
        if (subApproval != null) {
            //增加一条日志记录
            TransferLog transferLog = new TransferLog();
            transferLog.setHandleApprovalId(subApproval.getId());
            //是否待审
            if (TransferApprovalConstant.IS_INSTEAD.equals(isInstead)) {
                transferLog.setReason("(代审更改目标组织:" + CacheUtils.getOrgName(UserConstant.USER_CONTEXT.get().getUserRolePermission().getOrgId()) + ")原因:" + data.getReason());
            } else {
                transferLog.setReason("(原因:" + data.getReason() + ")");
            }
            transferLog.setType(TransferLogConstant.RESET_TARGET_ORG_STATUS);

            flag = transferLogService.rewriteSave(transferLog);
            updateRecord.setCurrentApprovalId(subApproval.getId());
            //判断是否是左边发起的更改,如果是左边发起的更改需要判断公共节点是否存在
            //如果存在需要更改公共节点下一个id
            //如果不存在则不需要任何参数
            List<String> targetOrgRelationList = updateRecord.getTargetOrgRelationAsList();
            List<String> srcOrgRelationList = updateRecord.getSrcOrgRelationAsList();
            removeOtherNodes(updateRecord.getCommonOrgId(), targetOrgRelationList);
            removeOtherNodes(updateRecord.getCommonOrgId(), srcOrgRelationList);
            if (isLeft) {
                TransferApproval commonNodeApproval = transferApprovalService.findCommonNodeByRecordId(updateRecord.getId(), updateRecord.getCommonOrgId());
                if (commonNodeApproval != null) {
                    TransferApproval updateCommonNodeApproval = new TransferApproval();
                    updateCommonNodeApproval.setId(commonNodeApproval.getId());
                    if (targetOrgRelationList.size() > 1) {
                        updateCommonNodeApproval.setNextOrgId(targetOrgRelationList.get(1));
                    } else {
                        updateCommonNodeApproval.setNextOrgId("");
                    }
                    //判断下一个是否该公共节点审核
                    if (StrKit.equals(subApproval.getNextOrgId(), updateRecord.getCommonOrgId())) {
                        updateCommonNodeApproval.setStatus(TransferApprovalConstant.APPROVAL_SUCCESS);
                        TransferLog commonLog = new TransferLog();
                        commonLog.setHandleApprovalId(commonNodeApproval.getId());
                        commonLog.setReason("(原因:" + TransferLogConstant.SUCCESS_REASON + ")");
                        commonLog.setType(TransferLogConstant.SUCCESS_STATUS);
                        flag &= transferLogService.rewriteSave(commonLog);
                        updateRecord.setCurrentApprovalId(commonNodeApproval.getId());
                        //还需要加入一条记录 和 修改
                        TransferApproval byRecordIdAndParentId = transferApprovalService.findByRecordIdAndParentId(updateRecord.getId(), updateCommonNodeApproval.getId());
                        if (ObjectUtil.isNull(byRecordIdAndParentId)) {
                            TransferApproval newCommonOrgId = new TransferApproval();
                            newCommonOrgId.setStatus(TransferApprovalConstant.APPROVAL_SUCCESS);
                            newCommonOrgId.setCreateTime(new Date());
                            newCommonOrgId.setUpdateTime(new Date());
                            newCommonOrgId.setIsInstead(0);
                            newCommonOrgId.setDirection(2);
                            newCommonOrgId.setOrgId(updateRecord.getCommonOrgId());
                            newCommonOrgId.setNextOrgId(updateCommonNodeApproval.getNextOrgId());
                            newCommonOrgId.setParentId(updateCommonNodeApproval.getId());
                            newCommonOrgId.setRecordId(updateRecord.getId());
                            newCommonOrgId.setUserId(userTicket.getUser().getId());
                            transferApprovalService.save(newCommonOrgId);
                            updateRecord.setCurrentApprovalId(newCommonOrgId.getId());
                        } else {
                            byRecordIdAndParentId.setStatus(TransferApprovalConstant.APPROVALING);
                            updateRecord.setCurrentApprovalId(byRecordIdAndParentId.getId());
                            transferApprovalService.rewriteUpdateById(byRecordIdAndParentId);
                        }
                    } else {
                        updateCommonNodeApproval.setStatus(TransferApprovalConstant.APPROVALING);
                    }
                    boolean update = transferApprovalService.rewriteUpdateById(updateCommonNodeApproval);
                    if (!update) {
                        return new OutMessage<>(Status.FAIL);
                    }
                }
            }

            flag &= fixNextOrgId(targetOrgRelationList, srcOrgRelationList, updateRecord.getCommonOrgId(), updateRecord);
            flag &= updateById(updateRecord);
        }
        this.updateMemIsTransfer(data.getId(), Collections.singletonList(record.getMemId()));
        return flag ? new OutMessage<>(Status.SUCCESS) : new OutMessage<>(Status.FAIL);
    }

    /***
     * 修正已存在的审批记录的nextOrgId
     * */
    private boolean fixNextOrgId(List<String> targetOrgRelationList, List<String> srcOrgRelationList, String commonNodeId, TransferRecord record) {
        String recordId = record.getId();


        Map<String, Node> nodeMap = new LinkedHashMap<>(10);
        for (int i = srcOrgRelationList.size() - 1; i >= 0; i--) {
            String orgId = srcOrgRelationList.get(i);
            Node node = new Node();
            node.setOrgId(orgId);
            //说明是最后一个
            if (i - 1 < 0) {
                node.setNextOrgId(commonNodeId);
            } else {
                node.setNextOrgId(srcOrgRelationList.get(i - 1));
            }
            nodeMap.put(orgId, node);
        }

        Node commonNode = new Node();
        commonNode.setOrgId(commonNodeId);
        nodeMap.put(commonNodeId, commonNode);
        if (targetOrgRelationList != null && !targetOrgRelationList.isEmpty()) {
            commonNode.setNextOrgId(targetOrgRelationList.get(0));
            for (int i = 0; i < targetOrgRelationList.size(); i++) {
                String orgId = targetOrgRelationList.get(i);
                Node node = new Node();
                node.setOrgId(orgId);
                if (i + 1 == targetOrgRelationList.size()) {
                    node.setNextOrgId("");
                } else {
                    node.setNextOrgId(targetOrgRelationList.get(i + 1));
                }
                nodeMap.put(orgId, node);
            }
        } else {
            commonNode.setNextOrgId("");
        }

        List<TransferApproval> approvals = transferApprovalService.findByRecordId(recordId);
        List<TransferApproval> updateList = new ArrayList<>();
        for (TransferApproval approval : approvals) {
            String orgId = approval.getOrgId();
            Node node = nodeMap.get(orgId);
            if (node != null) {
                TransferApproval update = new TransferApproval();
                update.setId(approval.getId());
                update.setOrgId(orgId);
                update.setNextOrgId(node.getNextOrgId());
                updateList.add(update);
            }
        }

        if (updateList.isEmpty()) {
            return true;
        }

        return transferApprovalService.batchUpdate(updateList);
    }

    @Override
    public OutMessage<Page<TransferRecordDTO>> findInByPage(Integer pageSize, Integer pageNum, String[] types, String orgId, String keyWord, boolean isHistory, Integer[] status, TransferRecordListDTO data) {
        UserTicket userTicket = UserConstant.USER_CONTEXT.get();
        if (userTicket == null) {
            return new OutMessage<>(Status.NOT_LOGIN);
        }
        if (StrKit.isBlank(orgId)) {
            orgId = userTicket.getUserRolePermission().getOrgId();
        }

        //todo 20250722加密优化
        Org org = orgService.findOrgByCode(orgId,"orgCode","code");
        if (org == null) {
            return new OutMessage<>(10012, "流动党员党组织不能进行关系转接", null);
        }
        String currentOrgCode = userTicket.getUserRolePermission().getOrgCode();
        String orgCode = org.getOrgCode();
        if (StrKit.isBlank(orgCode) || !(orgCode.startsWith(currentOrgCode))) {
            return new OutMessage<>(Status.PERMISSION_DENIED);
        }

        //Page<TransferRecord> modelPage = findByPage(pageSize, pageNum, types, orgId, keyWord, isHistory);
        Page<TransFerTarView> modelPage = findByPage(pageSize, pageNum, types, orgId, keyWord, isHistory, status,data);
        List<TransferRecordDTO> dtoList = modelPage.getRecords().stream().map(record -> {
            TransferRecordDTO transferRecordDTO = new TransferRecordDTO();
            BeanUtils.copyProperties(record, transferRecordDTO);
            transferRecordDTO.setType(record.getInType());
            transferRecordDTO.setIsOrg(record.getMemId() == null ? 1 : 0);
            return transferRecordDTO;
        }).collect(Collectors.toList());
        Page<TransferRecordDTO> dtoPage = new Page<>();
        dtoPage.setCurrent(modelPage.getCurrent());
        dtoPage.setSize(modelPage.getSize());
        dtoPage.setTotal(modelPage.getTotal());
        dtoPage.setRecords(dtoList);

        if (!dtoList.isEmpty()) {
            List<Record> inTypes = CacheUtils.getDic(TransferRecordConstant.DICT_IN);
            //设置类型名称
            setTransferTypeName(inTypes, dtoList);
        }
        // TODO: 2022/8/9  增加列表处理， 需要他审核的时候反馈特殊标识
        List<TransferRecordDTO> records = dtoPage.getRecords();
        if (CollUtil.isNotEmpty(records)) {
            Set<String> currentApprovalIdList = records.stream().map(TransferRecordDTO::getCurrentApprovalId).collect(Collectors.toSet());
            Map<String, String> map = new LinkedHashMap<>();
            if (CollUtil.isNotEmpty(currentApprovalIdList)) {
                List<TransferApproval> approvalByList = transferApprovalService.findApprovalByList(currentApprovalIdList);
                if (CollUtil.isNotEmpty(approvalByList)) {
                    Map<String, String> tmpMap = approvalByList.stream().collect(Collectors.toMap(TransferApproval::getId, TransferApproval::getNextOrgId));
                    map.putAll(tmpMap);
                }
            }
            records.forEach(transferRecordDTO -> {
                transferRecordDTO.setIsCheck(CommonConstant.ZERO_INT);
                if (CollUtil.isNotEmpty(map)) {
                    String currentApprovalId = transferRecordDTO.getCurrentApprovalId();
                    String nextOrgId = map.get(currentApprovalId);
                    if (StrUtil.equals(nextOrgId, org.getCode())) {
                        transferRecordDTO.setIsCheck(CommonConstant.ONE_INT);
                    }
                }
            });
            dtoPage.setRecords(records);
        }
        return new OutMessage<>(Status.SUCCESS, dtoPage);
    }

    private Page<TransFerTarView> findByPage(Integer pageSize, Integer pageNum, String[] types, String orgId, String keyWord, boolean isHistory, Integer[] status, TransferRecordListDTO data) {
        Page<TransFerTarView> page = new Page<>(pageNum, pageSize);
        //todo 20250722加密优化
        Org orgByCode = orgService.findOrgByCode(orgId,"orgCode");
        LambdaQueryWrapper<TransFerTarView> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        if (Objects.nonNull(types) && types.length > 0) {
            lambdaQueryWrapper.in(TransFerTarView::getInType, Arrays.asList(types));
        }
        // 添加剩余时间判断
        if(Objects.nonNull(data)&&StrUtil.equals("1",data.getIsReminder())&&Objects.nonNull(data.getReminderDayInt())){
            Integer reminderDayInt = data.getReminderDayInt();
            lambdaQueryWrapper.and(wrapper ->
                    wrapper.apply(
                            "(transfer_out_time + ((letter_validity - {0}) || ' days')::INTERVAL) < NOW()",
                            reminderDayInt
                    )
            );
        }

        if(CollUtil.isNotEmpty(data.getHintTypes()) && data.getHintTypes().contains(4)) {
            // 超期退回提醒
            lambdaQueryWrapper.eq(TransFerTarView::getStatus, TransferRecordConstant.TRANSFER_OVERDUE_BACK);
            lambdaQueryWrapper.notExists("(select 1 from ccp_transfer_hint where ccp_transfer_hint.transfer_id = ccp_transfer_tar_org_view.id " +
                    " and ccp_transfer_hint.type = 4 )");
        }

        if (isHistory) {
            if (Objects.nonNull(status) && status.length > 0) {
                lambdaQueryWrapper.in(TransFerTarView::getStatus, Arrays.asList(status));
            }
        } else {
            lambdaQueryWrapper.eq(TransFerTarView::getStatus, TransferRecordConstant.TRANSFERING);
        }
        if (StrKit.notBlank(keyWord)) {
            lambdaQueryWrapper.like(TransFerTarView::getName, keyWord);
        }
        //lambdaQueryWrapper.apply("target_org_relation ?? {0}", orgId);
        lambdaQueryWrapper.ne(TransFerTarView::getType, "29");
        lambdaQueryWrapper.likeRight(TransFerTarView::getOrgCode, orgByCode.getOrgCode());
        int count = iTransfertarViewService.count(lambdaQueryWrapper);

        lambdaQueryWrapper.orderByDesc(TransFerTarView::getCreateTime);
        lambdaQueryWrapper.last("LIMIT " + pageSize + " OFFSET " + (pageNum - 1) * pageSize + "");
        List<TransFerTarView> list = iTransfertarViewService.list(lambdaQueryWrapper);

        page.setTotal(count);
        page.setRecords(list);
        return page;
    }

    private void setTransferTypeName(List<Record> types, List<TransferRecordDTO> list) {
        list.forEach(record -> types.stream().filter(r -> r.getStr("key").equalsIgnoreCase(record.getType())).findFirst().ifPresent(r -> record.setTypeName(r.getStr("name"))));
    }

    @Override
    public OutMessage<Page<TransferRecordDTO>> findOutByPage(Integer pageSize, Integer pageNum, String[] types, String orgId, String keyWord, boolean isHistory, Integer[] status, TransferRecordListDTO data) {
        UserTicket userTicket = UserConstant.USER_CONTEXT.get();
        if (userTicket == null) {
            return new OutMessage<>(Status.NOT_LOGIN);
        }
        if (StrKit.isBlank(orgId)) {
            orgId = userTicket.getUserRolePermission().getOrgId();
        }
        Org org = orgService.findOrgByCode(orgId);
        if (org == null) {
            return new OutMessage<>(10012, "流动党员党组织不能进行关系转接", null);
        }
        String currentOrgCode = userTicket.getUserRolePermission().getOrgCode();
        String orgCode = org.getOrgCode();
        if (StrKit.isBlank(orgCode) || !(orgCode.startsWith(currentOrgCode))) {
            return new OutMessage<>(Status.PERMISSION_DENIED);
        }
        Page<TransferRecordDTO> transferPage = this.findOutTransferPage(pageSize, pageNum, types, orgId, keyWord, isHistory, status,data);
        // TODO: 2022/8/9  增加列表处理， 需要他审核的时候反馈特殊标识
        List<TransferRecordDTO> records = transferPage.getRecords();
        if (CollUtil.isNotEmpty(records)) {
            Set<String> currentApprovalIdList = records.stream().map(TransferRecordDTO::getCurrentApprovalId).collect(Collectors.toSet());
            Map<String, String> map = new LinkedHashMap<>();
            if (CollUtil.isNotEmpty(currentApprovalIdList)) {
                List<TransferApproval> approvalByList = transferApprovalService.findApprovalByList(currentApprovalIdList);
                if (CollUtil.isNotEmpty(approvalByList)) {
                    Map<String, String> tmpMap = approvalByList.stream().collect(Collectors.toMap(TransferApproval::getId, TransferApproval::getNextOrgId));
                    map.putAll(tmpMap);
                }
            }
            records.forEach(transferRecordDTO -> {
                transferRecordDTO.setIsCheck(CommonConstant.ZERO_INT);
                if (CollUtil.isNotEmpty(map)) {
                    String currentApprovalId = transferRecordDTO.getCurrentApprovalId();
                    String nextOrgId = map.get(currentApprovalId);
                    if (StrUtil.equals(nextOrgId, org.getCode())) {
                        transferRecordDTO.setIsCheck(CommonConstant.ONE_INT);
                    }
                }
            });
            transferPage.setRecords(records);
        }
        return new OutMessage<>(Status.SUCCESS, transferPage);
    }

    @Override
    public Page<TransferRecordDTO> findOutTransferPage(Integer pageSize, Integer pageNum, String[] types, String orgId, String keyWord, boolean isHistory, Integer[] status, TransferRecordListDTO data) {
        Page<TransFerSrcView> modelPage = findOuByPage(pageSize, pageNum, types, orgId, keyWord, isHistory, status,data);
        //Page<TransferRecord> modelPage = findOuByPage(pageSize, pageNum, types, orgId, keyWord, isHistory);
        List<TransferRecordDTO> dtoList = modelPage.getRecords().stream().map(record -> {
            TransferRecordDTO transferRecordDTO = new TransferRecordDTO();
            BeanUtils.copyProperties(record, transferRecordDTO);
            transferRecordDTO.setType(record.getOutType());
            transferRecordDTO.setIsOrg(record.getMemId() == null ? 1 : 0);
            return transferRecordDTO;
        }).collect(Collectors.toList());
        Page<TransferRecordDTO> dtoPage = new Page<>();
        dtoPage.setCurrent(modelPage.getCurrent());
        dtoPage.setSize(modelPage.getSize());
        dtoPage.setTotal(modelPage.getTotal());
        dtoPage.setRecords(dtoList);
        if (!dtoList.isEmpty()) {
            List<Record> outTypes = CacheUtils.getDic(TransferRecordConstant.DICT_OUT);
            //设置转出类型名称
            setTransferTypeName(outTypes, dtoList);
        }
        return dtoPage;
    }

    private Page<TransFerSrcView> findOuByPage(Integer pageSize, Integer pageNum, String[] types, String orgId, String keyWord, boolean isHistory, Integer[] status, TransferRecordListDTO data) {
        //todo 20250722加密优化
        Org orgByCode = orgService.findOrgByCode(orgId,"orgCode");
        Page<TransFerSrcView> page = new Page<>(pageNum, pageSize);
        LambdaQueryWrapper<TransFerSrcView> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        if (Objects.nonNull(types) && types.length > 0) {
            lambdaQueryWrapper.in(TransFerSrcView::getOutType, Arrays.asList(types));
        }
        // 添加剩余时间判断
        if(Objects.nonNull(data)&&StrUtil.equals("1",data.getIsReminder())&&Objects.nonNull(data.getReminderDayInt())){
            Integer reminderDayInt = data.getReminderDayInt();
            lambdaQueryWrapper.and(wrapper ->
                    wrapper.apply(
                            "(transfer_out_time + ((letter_validity - {0}) || ' days')::INTERVAL) < NOW()",
                            reminderDayInt
                    )
            );
        }

        // 关系转出中，上传中组部交换区失败，主动撤回的，点击关系转出的时候，弹出提示框展示信息，10秒后可以点击确定
        // 在关系转接中，整合关系转出提醒界面，增加超期退回提醒，10秒后才可以点击关闭
        if(CollUtil.isNotEmpty(data.getHintTypes())) {
            if(data.getHintTypes().contains(1) || data.getHintTypes().contains(2)) {
                lambdaQueryWrapper.eq(TransFerSrcView::getType, "224");
                lambdaQueryWrapper.and(cond -> {
                    // 上传中组部交换区失败
                    if (data.getHintTypes().contains(1)) {
                        cond.and(c1 -> c1.and( c2 -> c2.isNull(TransFerSrcView::getLetterNumber).or().eq(TransFerSrcView::getLetterNumber, ""))
                                .isNotNull(TransFerSrcView::getLetterMessageCode)
                                .ne(TransFerSrcView::getLetterMessageCode, "")
                                // 成功的返回“0”。例子：OutMessage(code=0, message=数据上传成功, data=0520000122232025052000171)
                                .ne(TransFerSrcView::getLetterMessageCode, "0")
                                .eq(TransFerSrcView::getStatus, TransferRecordConstant.TRANSFERING)
                        );
                    }
                    if (data.getHintTypes().contains(1) && data.getHintTypes().contains(2)) {
                        cond.or();
                    }

                    // 主动撤回的
                    if(data.getHintTypes().contains(2)) {
                        cond.eq(TransFerSrcView::getStatus, TransferRecordConstant.TRANSFER_UNDO);
                        cond.exists(" (select 1 from ccp_transfer_approval, ccp_transfer_log where ccp_transfer_approval.record_id = ccp_transfer_src_org_view.id \n" +
                                "and ccp_transfer_approval.id = ccp_transfer_log.handle_approval_id and ccp_transfer_log.type = '3' and ccp_transfer_log.reason = '撤销'\n" +
                                ")");
                    }

                });
                // 筛除已经提示过的数据
                String join = CollUtil.join(data.getHintTypes().stream().filter(e -> Arrays.asList(1, 2).contains(e)).collect(Collectors.toList()), ",");
                lambdaQueryWrapper.notExists("(select 1 from ccp_transfer_hint where ccp_transfer_hint.transfer_id = ccp_transfer_src_org_view.id " +
                        " and ccp_transfer_hint.type in ("+ join +"))");
            }
            else if(data.getHintTypes().contains(4)) {
                // 超期退回提醒
                // 只针对省内关系转出类型
                lambdaQueryWrapper.eq(TransFerSrcView::getStatus, TransferRecordConstant.TRANSFER_OVERDUE_BACK);
                lambdaQueryWrapper.notExists("(select 1 from ccp_transfer_hint where ccp_transfer_hint.transfer_id = ccp_transfer_src_org_view.id " +
                        " and ccp_transfer_hint.type = 4 )");
            }
        }


        if (isHistory) {
            // TODO: 2022/5/16 历史记录中需要区分已完成和已撤销
            if (Objects.nonNull(status) && status.length > 0) {
                lambdaQueryWrapper.in(TransFerSrcView::getStatus, Arrays.asList(status));
            }
        } else {
            lambdaQueryWrapper.eq(TransFerSrcView::getStatus, TransferRecordConstant.TRANSFERING);
        }

        if (StrKit.notBlank(keyWord)) {
            lambdaQueryWrapper.like(TransFerSrcView::getName, keyWord);
        }
        // TODO: 2021/12/29 修改为上级可以查看下级得转接
        lambdaQueryWrapper.ne(TransFerSrcView::getType, "29");
        //lambdaQueryWrapper.apply("src_org_relation_rel ?? {0}", orgId);
        lambdaQueryWrapper.likeRight(TransFerSrcView::getOrgCode, orgByCode.getOrgCode());
        int count = iTransfrtSrcViewService.count(lambdaQueryWrapper);

        lambdaQueryWrapper.orderByDesc(TransFerSrcView::getCreateTime);
        lambdaQueryWrapper.last("LIMIT " + pageSize + " OFFSET " + (pageNum - 1) * pageSize + "");
        List<TransFerSrcView> list = iTransfrtSrcViewService.list(lambdaQueryWrapper);

        page.setTotal(count);
        page.setRecords(list);
        return page;
    }

    public static class Node implements Serializable {

        private static final long serialVersionUID = 568896715783996989L;

        private String orgId;

        private String nextOrgId;

        public String getOrgId() {
            return orgId;
        }

        public Node setOrgId(String orgId) {
            this.orgId = orgId;
            return this;
        }

        String getNextOrgId() {
            return nextOrgId;
        }

        Node setNextOrgId(String nextOrgId) {
            this.nextOrgId = nextOrgId;
            return this;
        }
    }


    private void updateMoveOrgData(String srcOrgId, String newOrgCode) {
        //处理原组织的数据情况
        //党员数据--基础数据//mem_org_code  //org_code
        LambdaUpdateChainWrapper<Mem> memLambdaUpdateChainWrapper = new LambdaUpdateChainWrapper<>(memMapper);
        boolean memUpdate = memLambdaUpdateChainWrapper.eq(Mem::getOrgCode, srcOrgId).set(Mem::getMemOrgCode, newOrgCode).update();
        log.warn("党员基础数据更新情况=====>" + memUpdate);

        //党员数据--出国境信息//abroad_org_code //org_code
        LambdaUpdateChainWrapper<MemAbroad> memAbroadLambdaUpdateChainWrapper = new LambdaUpdateChainWrapper<>(memAbroadMapper);
        boolean memAbroadUpdate = memAbroadLambdaUpdateChainWrapper.eq(MemAbroad::getOrgCode, srcOrgId).set(MemAbroad::getAbroadOrgCode, newOrgCode).update();
        log.warn("党员出国境数据更新情况=====>" + memAbroadUpdate);
        //党员数据--all表//mem_org_code//org_code
//        LambdaUpdateChainWrapper<MemAll> memAllLambdaUpdateChainWrapper = new LambdaUpdateChainWrapper<>(memAllMapper);
//        boolean memAllUpdate = memAllLambdaUpdateChainWrapper.eq(MemAll::getOrgCode, srcOrgId).set(MemAll::getMemOrgCode, newOrgCode).update();

        LambdaUpdateChainWrapper<MemAllInfo> memAllLambdaUpdateChainWrapper = new LambdaUpdateChainWrapper<>(memAllInfoMapper);
        boolean memAllUpdate = memAllLambdaUpdateChainWrapper.eq(MemAllInfo::getOrgCode, srcOrgId).set(MemAllInfo::getMemOrgCode, newOrgCode).update();


        log.warn("党员基础数据ALL更新情况=====>" + memAllUpdate);
        //发展党员--基础信息//applied_org_code//develop_applied_org_code//org_code//develop_org_code//
        LambdaUpdateChainWrapper<MemDevelop> memDevelopLambdaUpdateChainWrapper = new LambdaUpdateChainWrapper<>(memDevelopMapper);
        boolean memDevoelop = memDevelopLambdaUpdateChainWrapper.eq(MemDevelop::getAppliedOrgCode, srcOrgId).set(MemDevelop::getDevelopAppliedOrgCode, newOrgCode).update();
        log.warn("发展党员基础数据更新情况=====>" + memDevoelop);
        LambdaUpdateChainWrapper<MemDevelop> memDevelopLambdaUpdateChainWrapper1 = new LambdaUpdateChainWrapper<>(memDevelopMapper);
        boolean memDevoelop1 = memDevelopLambdaUpdateChainWrapper1.eq(MemDevelop::getOrgCode, srcOrgId).set(MemDevelop::getDevelopOrgCode, newOrgCode).update();
        log.warn("发展党员基础数据更新情况=====>" + memDevoelop1);

        //发展党员--all表//develop_org_code//org_code
        LambdaUpdateChainWrapper<MemDevelopAll> memDevelopAllLambdaUpdateChainWrapper = new LambdaUpdateChainWrapper<>(developAllMapper);
        boolean memDevoelopAll = memDevelopAllLambdaUpdateChainWrapper.eq(MemDevelopAll::getOrgCode, srcOrgId).set(MemDevelopAll::getDevelopOrgCode, newOrgCode).update();
        log.warn("发展党员基础数据All更新情况=====>" + memDevoelopAll);

        //困难党员--diff_org_code//org_code
        LambdaUpdateChainWrapper<MemDifficult> memDifficultLambdaUpdateChainWrapper = new LambdaUpdateChainWrapper<>(memDifficultMapper);
        boolean memDiff = memDifficultLambdaUpdateChainWrapper.eq(MemDifficult::getOrgCode, srcOrgId).set(MemDifficult::getDiffOrgCode, newOrgCode).update();
        log.warn("困难党员基础数据更新情况=====>" + memDiff);

        //流动党员--mem_org_org_code//mem_org_code//backflow_org_code//backflow_org_org_code//
        LambdaUpdateChainWrapper<MemFlow> memFlowLambdaUpdateChainWrapper = new LambdaUpdateChainWrapper<>(memFlowMapper);
        boolean memFlow1 = memFlowLambdaUpdateChainWrapper.eq(MemFlow::getMemOrgCode, srcOrgId).set(MemFlow::getMemOrgOrgCode, newOrgCode).update();
        log.warn("流动党员基础数据更新情况=====>" + memFlow1);
        LambdaUpdateChainWrapper<MemFlow> memFlowLambdaUpdateChainWrapper1 = new LambdaUpdateChainWrapper<>(memFlowMapper);
        boolean memFlow2 = memFlowLambdaUpdateChainWrapper1.eq(MemFlow::getBackflowOrgCode, srcOrgId).set(MemFlow::getBackflowOrgOrgCode, newOrgCode).update();
        log.warn("流动党员基础数据更新情况=====>" + memFlow2);

        //流动党员--all表mem_org_org_code//mem_org_code//backflow_org_code//backflow_org_org_code//
        LambdaUpdateChainWrapper<MemFlowAll> memFlowAllLambdaUpdateChainWrapper = new LambdaUpdateChainWrapper<>(memFlowAllMapper);
        boolean memFlowAll1 = memFlowAllLambdaUpdateChainWrapper.eq(MemFlowAll::getMemOrgCode, srcOrgId).set(MemFlowAll::getMemOrgOrgCode, newOrgCode).update();
        log.warn("流动党员基础数据ALL更新情况=====>" + memFlowAll1);
        LambdaUpdateChainWrapper<MemFlowAll> memFlowAllLambdaUpdateChainWrapper1 = new LambdaUpdateChainWrapper<>(memFlowAllMapper);
        boolean memFlowAll2 = memFlowAllLambdaUpdateChainWrapper1.eq(MemFlowAll::getBackflowOrgCode, srcOrgId).set(MemFlowAll::getBackflowOrgOrgCode, newOrgCode).update();
        log.warn("流动党员基础数据ALL更新情况=====>" + memFlowAll2);

        //党员奖惩表
        LambdaUpdateChainWrapper<MemReward> memRewardLambdaUpdateChainWrapper = new LambdaUpdateChainWrapper<>(rewardMapper);
        boolean memFlow = memRewardLambdaUpdateChainWrapper.eq(MemReward::getOrgCode, srcOrgId).set(MemReward::getRewardOrgCode, newOrgCode).update();
        log.warn("党员奖惩基础数据更新情况=====>" + memFlow);

        //党员奖惩--all表org_code//mem_org_code
        LambdaUpdateChainWrapper<MemRewardAll> memRewardAllLambdaUpdateChainWrapper = new LambdaUpdateChainWrapper<>(memRewardAllMapper);
        boolean memFlowAll = memRewardAllLambdaUpdateChainWrapper.eq(MemRewardAll::getOrgCode, srcOrgId).set(MemRewardAll::getMemOrgCode, newOrgCode).update();
        log.warn("党员奖惩基础数据ALL更新情况=====>" + memFlowAll);

        //培训情况--org_code//org_org_code
        LambdaUpdateChainWrapper<MemTrain> memTrainLambdaUpdateChainWrapper = new LambdaUpdateChainWrapper<>(trainMapper);
        boolean memTrain = memTrainLambdaUpdateChainWrapper.eq(MemTrain::getOrgCode, srcOrgId).set(MemTrain::getOrgOrgCode, newOrgCode).update();
        log.warn("党员培训基础数据更新情况=====>" + memTrain);

        //党代会情况--org_code//caucus_org_code
        LambdaUpdateChainWrapper<OrgCaucus> orgCaucusLambdaUpdateChainWrapper = new LambdaUpdateChainWrapper<>(caucusMapper);
        boolean orgCaucs = orgCaucusLambdaUpdateChainWrapper.eq(OrgCaucus::getOrgCode, srcOrgId).set(OrgCaucus::getCaucusOrgCode, newOrgCode).update();
        log.warn("党代会基础数据更新情况=====>" + orgCaucs);

        //组织信息--code//org_code//parent_code
        LambdaUpdateChainWrapper<Org> orgLambdaUpdateChainWrapper = new LambdaUpdateChainWrapper<>(orgMapper);
        boolean orgupdate = orgLambdaUpdateChainWrapper.eq(Org::getCode, srcOrgId).set(Org::getOrgCode, newOrgCode).update();
        log.warn("组织基础数据更新情况=====>" + orgupdate);
        if (orgupdate) {
            //移动党组织更新完成后，同步组织信息到中间交换区
            List<OrgAll> allSubOrgByOrgCode = orgService.findOrgByCodeNotDeleteTime(baseOrgOrgCode);
            if (allSubOrgByOrgCode.size() > 0) {
                JSONArray postJsonArray = new JSONArray();
                allSubOrgByOrgCode.forEach(org -> {
                    JSONObject jsonObject = JSONObject.parseObject(JSONObject.toJSONString(org));
                    jsonObject.put("exchangeKey", exchange_nginx_key);
                    postJsonArray.add(jsonObject);
                });
                JSONObject postJson = new JSONObject();
                postJson.put("data", postJsonArray);
                String replaceUrl = sync_flow_push.replace("/api/flow/push", "");
                String rultMessage = HttpKit.doPost(replaceUrl + "/org/addOrg", postJson, "UTF-8");
                System.out.println("组织中间交换区组织信息反馈====》" + rultMessage);
            }
        }
        //组织信息--all表 code//org_code
        LambdaUpdateChainWrapper<OrgAll> orgAllLambdaUpdateChainWrapper = new LambdaUpdateChainWrapper<>(orgAllMapper);
        boolean orgAll = orgAllLambdaUpdateChainWrapper.eq(OrgAll::getCode, srcOrgId).set(OrgAll::getOrgCode, newOrgCode).update();
        log.warn("组织基础数据ALL更新情况=====>" + orgAll);

        //组织班子成员--org_code//position_org_code//
        LambdaUpdateChainWrapper<OrgCommittee> orgCommitteeLambdaUpdateChainWrapper = new LambdaUpdateChainWrapper<>(orgCommitteeMapper);
        boolean orgcommit = orgCommitteeLambdaUpdateChainWrapper.eq(OrgCommittee::getOrgCode, srcOrgId).set(OrgCommittee::getPositionOrgCode, newOrgCode).update();
        log.warn("组织班子基础数据更新情况=====>" + orgcommit);

        //组织届次表--org_code//elect_org_code
        LambdaUpdateChainWrapper<OrgCommitteeElect> orgCommitteeElectLambdaUpdateChainWrapper = new LambdaUpdateChainWrapper<>(electMapper);
        boolean committeeElect = orgCommitteeElectLambdaUpdateChainWrapper.eq(OrgCommitteeElect::getOrgCode, srcOrgId).set(OrgCommitteeElect::getElectOrgCode, newOrgCode).update();
        log.warn("组织届次基础数据更新情况=====>" + committeeElect);

        //组织扩展信息--//code//org_code
        LambdaUpdateChainWrapper<OrgExtend> orgExtendLambdaUpdateChainWrapper = new LambdaUpdateChainWrapper<>(extendMapper);
        boolean extend = orgExtendLambdaUpdateChainWrapper.eq(OrgExtend::getCode, srcOrgId).set(OrgExtend::getOrgCode, newOrgCode).update();
        log.warn("组织扩展基础数据更新情况=====>" + extend);

        //组织党小组--org_code//group_org_code
        LambdaUpdateChainWrapper<OrgGroup> orgGroupLambdaUpdateChainWrapper = new LambdaUpdateChainWrapper<>(groupMapper);
        boolean group = orgGroupLambdaUpdateChainWrapper.eq(OrgGroup::getOrgCode, srcOrgId).set(OrgGroup::getGroupOrgCode, newOrgCode).update();
        log.warn("党小组基础数据更新情况=====>" + group);

        //ccp_org_industry--//org_code//industry_org_code
        LambdaUpdateChainWrapper<OrgIndustry> orgIndustryLambdaUpdateChainWrapper = new LambdaUpdateChainWrapper<>(orgIndustryMapper);
        boolean orgIndustry = orgIndustryLambdaUpdateChainWrapper.eq(OrgIndustry::getOrgCode, srcOrgId).set(OrgIndustry::getIndustryOrgCode, newOrgCode).update();
        log.warn("ccp_org_industry基础数据更新情况=====>" + orgIndustry);

        //ccp_org_industry_all--org_code//industry_org_code
        LambdaUpdateChainWrapper<OrgIndustryAll> orgIndustryAllLambdaUpdateChainWrapper = new LambdaUpdateChainWrapper<>(orgIndustryAllMapper);
        boolean orgIndustryAll = orgIndustryAllLambdaUpdateChainWrapper.eq(OrgIndustryAll::getOrgCode, srcOrgId).set(OrgIndustryAll::getIndustryOrgCode, newOrgCode).update();
        log.warn("ccp_org_industry_all基础数据更新情况=====>" + orgIndustryAll);

        //ccp_org_party--party_org_code//org_code
        LambdaUpdateChainWrapper<OrgParty> orgPartyLambdaUpdateChainWrapper = new LambdaUpdateChainWrapper<>(orgPartyMapper);
        boolean orgParty = orgPartyLambdaUpdateChainWrapper.eq(OrgParty::getOrgCode, srcOrgId).set(OrgParty::getPartyOrgCode, newOrgCode).update();
        log.warn("ccp_org_party基础数据更新情况=====>" + orgParty);

        //ccp_org_party_congress_committee--org_code//position_org_code
        LambdaUpdateChainWrapper<OrgPartyCongressCommittee> orgPartyCongressCommitteeLambdaUpdateChainWrapper = new LambdaUpdateChainWrapper<>(orgPartyCongressCommitteeMapper);
        boolean orgPartyComiit = orgPartyCongressCommitteeLambdaUpdateChainWrapper.eq(OrgPartyCongressCommittee::getOrgCode, srcOrgId).set(OrgPartyCongressCommittee::getPositionOrgCode, newOrgCode).update();
        log.warn("ccp_org_party_congress_committee基础数据更新情况=====>" + orgPartyComiit);

        //ccp_org_party_congress_elect--elect_org_code//org_code
        LambdaUpdateChainWrapper<OrgPartyCongressElect> orgPartyCongressElectLambdaUpdateChainWrapper = new LambdaUpdateChainWrapper<>(orgPartyCongressElectMapper);
        boolean orgPartyElect = orgPartyCongressElectLambdaUpdateChainWrapper.eq(OrgPartyCongressElect::getOrgCode, srcOrgId).set(OrgPartyCongressElect::getElectOrgCode, newOrgCode).update();
        log.warn("ccp_org_party_congress_elect基础数据更新情况=====>" + orgPartyElect);

        //ccp_org_recognition--org_code//recognition_org_code
        LambdaUpdateChainWrapper<OrgRecognition> orgRecognitionLambdaUpdateChainWrapper = new LambdaUpdateChainWrapper<>(recognitionMapper);
        boolean orgRecognition = orgRecognitionLambdaUpdateChainWrapper.eq(OrgRecognition::getOrgCode, srcOrgId).set(OrgRecognition::getRecognitionOrgCode, newOrgCode).update();
        log.warn("ccp_org_recognition基础数据更新情况=====>" + orgRecognition);

        //ccp_org_recognition_all--org_code//org_level_code
        LambdaUpdateChainWrapper<OrgRecognitionAll> orgRecognitionAllLambdaUpdateChainWrapper = new LambdaUpdateChainWrapper<>(orgRecognitionAllMapper);
        boolean orgLevel = orgRecognitionAllLambdaUpdateChainWrapper.eq(OrgRecognitionAll::getOrgCode, srcOrgId).set(OrgRecognitionAll::getOrgLevelCode, newOrgCode).update();
        log.warn("ccp_org_recognition_all基础数据更新情况=====>" + orgLevel);

        //ccp_org_recognition_situation --org_code//situation_org_code
        LambdaUpdateChainWrapper<OrgRecognitionSituation> orgRecognitionSituationLambdaUpdateChainWrapper = new LambdaUpdateChainWrapper<>(situationMapper);
        boolean ccpOrgRecognition = orgRecognitionSituationLambdaUpdateChainWrapper.eq(OrgRecognitionSituation::getOrgCode, srcOrgId).set(OrgRecognitionSituation::getSituationOrgCode, newOrgCode).update();
        log.warn("ccp_org_recognition_situation基础数据更新情况=====>" + ccpOrgRecognition);
        //ccp_org_reward --org_code//reward_org_code
        LambdaUpdateChainWrapper<OrgReward> orgRewardLambdaUpdateChainWrapper = new LambdaUpdateChainWrapper<>(orgRewardMapper);
        boolean ccpOrgReward = orgRewardLambdaUpdateChainWrapper.eq(OrgReward::getOrgCode, srcOrgId).set(OrgReward::getRewardOrgCode, newOrgCode).update();
        log.warn("ccp_org_reward基础数据更新情况=====>" + ccpOrgReward);
        //ccp_org_slack --org_code//slack_org_code
        LambdaUpdateChainWrapper<OrgSlack> orgSlackLambdaUpdateChainWrapper = new LambdaUpdateChainWrapper<>(orgSlackMapper);
        boolean ccpSlack = orgSlackLambdaUpdateChainWrapper.eq(OrgSlack::getOrgCode, srcOrgId).set(OrgSlack::getSlackOrgCode, newOrgCode).update();
        log.warn("ccp_org_slack基础数据更新情况=====>" + ccpSlack);
        //ccp_org_slack_all--org_code//slack_org_code
        LambdaUpdateChainWrapper<OrgSlackAll> orgSlackAllLambdaUpdateChainWrapper = new LambdaUpdateChainWrapper<>(orgSlackAllMapper);
        boolean ccpSlackAll = orgSlackAllLambdaUpdateChainWrapper.eq(OrgSlackAll::getOrgCode, srcOrgId).set(OrgSlackAll::getSlackOrgCode, newOrgCode).update();
        log.warn("ccp_org_slack_all基础数据更新情况=====>" + ccpSlackAll);
        //ccp_org_slack_rectification --org_code//slack_org_code
        LambdaUpdateChainWrapper<OrgSlackRectification> orgSlackRectificationLambdaUpdateChainWrapper = new LambdaUpdateChainWrapper<>(orgSlackRectificationMapper);
        boolean ccpSlackRec = orgSlackRectificationLambdaUpdateChainWrapper.eq(OrgSlackRectification::getOrgCode, srcOrgId).set(OrgSlackRectification::getSlackOrgCode, newOrgCode).update();
        log.warn("ccp_org_slack_rectification基础数据更新情况=====>" + ccpSlackRec);
        //ccp_org_special_nature --org_code//special_org_code
        LambdaUpdateChainWrapper<OrgSpecialNature> orgSpecialNatureLambdaUpdateChainWrapper = new LambdaUpdateChainWrapper<>(natureMapper);
        boolean orgSpecial = orgSpecialNatureLambdaUpdateChainWrapper.eq(OrgSpecialNature::getOrgCode, srcOrgId).set(OrgSpecialNature::getSpecialOrgCode, newOrgCode).update();
        log.warn("ccp_org_special_nature党员基础数据更新情况=====>" + orgSpecial);
        //ccp_unit_org_linked -- org_code//linked_org_code
        LambdaUpdateChainWrapper<UnitOrgLinked> orgLinkedLambdaUpdateChainWrapper = new LambdaUpdateChainWrapper<>(linkedMapper);
        boolean orgLinked = orgLinkedLambdaUpdateChainWrapper.eq(UnitOrgLinked::getOrgCode, srcOrgId).set(UnitOrgLinked::getLinkedOrgCode, newOrgCode).update();
        log.warn("ccp_unit_org_linked基础数据更新情况=====>" + orgLinked);
        //ccp_unit--create_org_code//create_unit_org_code//main_org_code//main_unit_org_code//manage_unit_org_code//manage_org_code
        LambdaUpdateChainWrapper<Unit> unitLambdaUpdateChainWrapperCreate = new LambdaUpdateChainWrapper<>(unitMapper);
        boolean unitCreate = unitLambdaUpdateChainWrapperCreate.eq(Unit::getCreateOrgCode, srcOrgId).set(Unit::getCreateUnitOrgCode, newOrgCode).update();
        log.warn("ccp_unit基础数据更新情况=====>" + unitCreate);
        LambdaUpdateChainWrapper<Unit> unitLambdaUpdateChainWrapperMain = new LambdaUpdateChainWrapper<>(unitMapper);
        boolean unitMain = unitLambdaUpdateChainWrapperMain.eq(Unit::getMainOrgCode, srcOrgId).set(Unit::getMainUnitOrgCode, newOrgCode).update();
        log.warn("ccp_unit基础数据更新情况=====>" + unitMain);
        LambdaUpdateChainWrapper<Unit> unitLambdaUpdateChainWrapperManage = new LambdaUpdateChainWrapper<>(unitMapper);
        boolean unitManage = unitLambdaUpdateChainWrapperManage.eq(Unit::getManageOrgCode, srcOrgId).set(Unit::getManageUnitOrgCode, newOrgCode).update();
        log.warn("ccp_unit基础数据更新情况=====>" + unitManage);
        //ccp_unit_all--create_unit_org_code//create_org_code//manage_org_code//main_unit_org_code
        LambdaUpdateChainWrapper<UnitAll> unitLambdaUpdateChainWrapperAllCreate = new LambdaUpdateChainWrapper<>(unitAllMapper);
        boolean unitCreateAll = unitLambdaUpdateChainWrapperAllCreate.eq(UnitAll::getCreateOrgCode, srcOrgId).set(UnitAll::getCreateUnitOrgCode, newOrgCode).update();
        log.warn("ccp_unit_all基础数据更新情况=====>" + unitCreateAll);
        LambdaUpdateChainWrapper<UnitAll> unitLambdaUpdateChainWrapperAllMain = new LambdaUpdateChainWrapper<>(unitAllMapper);
        boolean unitAllMain = unitLambdaUpdateChainWrapperAllMain.eq(UnitAll::getMainOrgCode, srcOrgId).set(UnitAll::getMainUnitOrgCode, newOrgCode).update();
        log.warn("ccp_unit_all基础数据更新情况=====>" + unitAllMain);
        LambdaUpdateChainWrapper<UnitAll> unitAllLambdaUpdateChainWrapper = new LambdaUpdateChainWrapper<>(unitAllMapper);
        boolean unitAllManage = unitAllLambdaUpdateChainWrapper.eq(UnitAll::getManageOrgCode, srcOrgId).set(UnitAll::getManageUnitOrgCode, newOrgCode).update();
        log.warn("ccp_unit_all基础数据更新情况=====>" + unitAllManage);
        //ccp_unit_city_situation--org_code//

        //ccp_unit_collective_economic--org_code//economic_org_code
        LambdaUpdateChainWrapper<UnitCollectiveEconomic> unitCollectiveEconomicLambdaUpdateChainWrapper = new LambdaUpdateChainWrapper<>(unitCollectiveEconomicMapper);
        unitCollectiveEconomicLambdaUpdateChainWrapper.eq(UnitCollectiveEconomic::getOrgCode, srcOrgId).set(UnitCollectiveEconomic::getEconomicOrgCode, newOrgCode).update();

        // TODO: 2021/12/7 处理更新账户数据
        LambdaUpdateChainWrapper<User> userLambdaUpdateChainWrapper = new LambdaUpdateChainWrapper<>(userMapper);
        userLambdaUpdateChainWrapper.eq(User::getOrgId, srcOrgId).set(User::getOrgCode, newOrgCode).update();

        LambdaUpdateChainWrapper<UserRolePermission> userRolePermissionMapperLambdaUpdateChainWrapper = new LambdaUpdateChainWrapper<>(userRolePermissionMapper);
        userRolePermissionMapperLambdaUpdateChainWrapper.eq(UserRolePermission::getOrgId, srcOrgId).set(UserRolePermission::getOrgCode, newOrgCode).update();
        //ccp_develop_step_log
        LambdaUpdateChainWrapper<DevelopStepLog> developStepLogLambdaUpdateChainWrapper = new LambdaUpdateChainWrapper<>(developStepLogMapper);
        developStepLogLambdaUpdateChainWrapper.eq(DevelopStepLog::getOrgCode, srcOrgId).set(DevelopStepLog::getLogOrgCode, newOrgCode).update();
        //ccp_develop_step_log_all
        LambdaUpdateChainWrapper<DevelopStepLogAll> developStepLogAllWrapper = new LambdaUpdateChainWrapper<>(developStepLogAllMapper);
        developStepLogAllWrapper.eq(DevelopStepLogAll::getOrgCode, srcOrgId).set(DevelopStepLogAll::getLogOrgCode, newOrgCode).update();

        // TODO: 2022/12/16 ccp_mem_report 人员报表组织层级码同步
        LambdaUpdateChainWrapper<MemReport> memReportLambdaUpdateChainWrapper = new LambdaUpdateChainWrapper<>(memReportMapper);
        memReportLambdaUpdateChainWrapper.eq(MemReport::getOrgOrgCode, srcOrgId).set(MemReport::getMemOrgCode, newOrgCode).update();

        // TODO: 2022/12/16 ccp_unit_report 单位报表组织层级码同步
        LambdaUpdateChainWrapper<UnitReport> unitReportLambdaUpdateChainWrapper = new LambdaUpdateChainWrapper<>(unitReportMapper);
        unitReportLambdaUpdateChainWrapper.eq(UnitReport::getCreateOrgCode, srcOrgId).set(UnitReport::getCreateUnitOrgCode, newOrgCode).update();

        LambdaUpdateChainWrapper<OrgLife> orgLifeLambdaUpdateChainWrapper = new LambdaUpdateChainWrapper<>(orgLifeMapper);
        boolean orgLifeManage = orgLifeLambdaUpdateChainWrapper.eq(OrgLife::getOrgCode, srcOrgId).set(OrgLife::getOrgLevelCode, newOrgCode).update();
        log.warn("ccp_org_life数据更新情况=====>" + orgLifeManage);
        LambdaUpdateChainWrapper<OrgLifePart> orgLifePartLambdaUpdateChainWrapper = new LambdaUpdateChainWrapper<>(orgLifePartMapper);
        boolean orgLifePartManage = orgLifePartLambdaUpdateChainWrapper.eq(OrgLifePart::getOrgCode, srcOrgId).set(OrgLifePart::getOrgLevelCode, newOrgCode).update();
        log.warn("ccp_org_life_part数据更新情况=====>" + orgLifePartManage);
    }


    /**
     * 关系转接生成流程
     * @param d08Code 党员类型
     * @param digLotNo 档案唯一码
     * @param joinOrgDate 成为预备党员时间\满足积极分子时间\入党申请人（党员类型为入党申请人）
     * @return
     */
    public static List<MemDevelopProcess> generate(String d08Code, String digLotNo, Date joinOrgDate){
        List<MemDevelopProcess> result = new ArrayList<>();
        MemDevelopProcess memDevelopProcess = new MemDevelopProcess();
        memDevelopProcess.setCode(IdUtil.simpleUUID());
        memDevelopProcess.setCreateTime(new Date());
        memDevelopProcess.setCreateUser("关系转接生成");
        memDevelopProcess.setDigitalLotNo(digLotNo);
        memDevelopProcess.setd08Code(d08Code);
        joinOrgDate = Objects.nonNull(joinOrgDate) ? joinOrgDate : new Date();
        switch (d08Code) {
            case CommonConstant.TWO: // 预备党员
                memDevelopProcess.setProcessNode(ProcessNodeEnum.YBQ_1_1.getNode());
                memDevelopProcess.setExtendStarTime(joinOrgDate);
                // 定时半年
                memDevelopProcess.setExtendEndTime(DateUtil.offset(memDevelopProcess.getExtendStarTime(), DateField.MONTH, 6));
                // 比较当前时间是否已经触发到进入满足考察
                if(DateUtil.compare(memDevelopProcess.getExtendEndTime(), new Date(), "yyyy-MM-dd") <= 0) {
                    memDevelopProcess.setProcessNode(ProcessNodeEnum.YBQ_1_2.getNode());
                }
                break;
            case CommonConstant.THREE: // 发展对象
                memDevelopProcess.setProcessNode(ProcessNodeEnum.FZ_1.getNode());
                break;
            case CommonConstant.FOUR: // 积极分子
                memDevelopProcess.setProcessNode(ProcessNodeEnum.JJ_1.getNode());
                memDevelopProcess.setExtendStarTime(joinOrgDate);
                // 进入第一次考察的触发时间
                memDevelopProcess.setExtendEndTime(DateUtil.offset(memDevelopProcess.getExtendStarTime(), DateField.MONTH, 6));
                // 比较当前时间是否已经触发到进入第一次考察
                if(DateUtil.compare(memDevelopProcess.getExtendEndTime(), new Date(), "yyyy-MM-dd") <= 0) {
                    memDevelopProcess.setProcessNode(ProcessNodeEnum.JJ_2.getNode());
                    Date day15 = DateUtil.offsetDay(memDevelopProcess.getExtendEndTime(), 15);
                    // 是否超过了15天，进入超半月未按时考察
                    if(DateUtil.compare(day15, new Date(), "yyyy-MM-dd") <= 0){
                        // 将经过的流程自动审批
                        memDevelopProcess.setApproveTime(new Date());
                        memDevelopProcess.setApproveUser("关系转接生成");
                        // 生成超时流程
                        MemDevelopProcess JJ_4Process = new MemDevelopProcess();
                        JJ_4Process.setCode(IdUtil.simpleUUID());
                        JJ_4Process.setCreateTime(new Date());
                        JJ_4Process.setCreateUser("关系转接生成");
                        JJ_4Process.setDigitalLotNo(digLotNo);
                        JJ_4Process.setd08Code(d08Code);
                        JJ_4Process.setExtendStarTime(joinOrgDate);
                        JJ_4Process.setExtendEndTime(memDevelopProcess.getExtendEndTime());
                        JJ_4Process.setProcessNode(ProcessNodeEnum.JJ_4.getNode());
                        JJ_4Process.setPreviousCode(memDevelopProcess.getCode());
                        result.add(JJ_4Process);
                    }
                }
                // 定时半年
                break;
            case CommonConstant.FIVE: // 入党申请人
                memDevelopProcess.setExtendStarTime(joinOrgDate);
                // 当前时间加1个月
                Date mothDat = DateUtil.offsetMonth(memDevelopProcess.getExtendStarTime(), 1);
                // 减少10天
                Date dayDat = DateUtil.offsetDay(mothDat, -10);
                if(DateUtil.compare(mothDat, new Date(), "yyyy-MM-dd") <= 0) {
                    // 一月未进行谈话
                    memDevelopProcess.setProcessNode(ProcessNodeEnum.RD_2_3.getNode());
                    memDevelopProcess.setExtendEndTime(mothDat);
                } else if(DateUtil.compare(dayDat, new Date(), "yyyy-MM-dd") <= 0) {
                    // 谈话时间少于10天
                    memDevelopProcess.setProcessNode(ProcessNodeEnum.RD_2_2.getNode());
                    memDevelopProcess.setExtendEndTime(mothDat);
                } else {
                    // 一月内需要谈话
                    memDevelopProcess.setProcessNode(ProcessNodeEnum.RD_2_1.getNode());
                    memDevelopProcess.setExtendEndTime(dayDat);
                }
                break;
            default:
                break;
        }
        if(StrUtil.isNotBlank(memDevelopProcess.getProcessNode())){
            result.add(memDevelopProcess);
        }
        return result;
    }

    /**
     * 档案、档案日志信息添加到扩展信息
     * @param digLotNo
     * @param jsonObject
     */
    public void digLotNoData(String digLotNo, JSONObject jsonObject){
        // ccp_mem_digital 档案
        LambdaQueryWrapper<MemDigital> memDigitalLambdaQueryWrapper = new LambdaQueryWrapper<>();
        memDigitalLambdaQueryWrapper.eq(MemDigital::getDigitalLotNo, digLotNo)
                .isNull(MemDigital::getDeleteTime);
        List<MemDigital> memDigitalList = memDigitalMapper.selectList(memDigitalLambdaQueryWrapper);
        if (CollUtil.isNotEmpty(memDigitalList)) {
            JSONArray jsonArray = new JSONArray();
            memDigitalList.forEach(data-> jsonArray.add(JSONObject.parseObject(JSONObject.toJSONString(data))));
            jsonObject.put("memDigital", jsonArray);
        }

        // ccp_mem_digital_operation_log 档案日志
        LambdaQueryWrapper<MemDigitalOperationLog> memDigitalOperationLogLambdaQueryWrapper = new LambdaQueryWrapper<>();
        memDigitalOperationLogLambdaQueryWrapper.eq(MemDigitalOperationLog::getDigitalLotNo, digLotNo)
                .isNull(MemDigitalOperationLog::getDeleteTime);
        List<MemDigitalOperationLog> memDigitalOperationLogList = memDigitalOperationLogMapper.selectList(memDigitalOperationLogLambdaQueryWrapper);
        if (CollUtil.isNotEmpty(memDigitalOperationLogList)) {
            JSONArray jsonArray = new JSONArray();
            memDigitalOperationLogList.forEach(data-> jsonArray.add(JSONObject.parseObject(JSONObject.toJSONString(data))));
            jsonObject.put("memDigitalOperationLog", jsonArray);
        }
    }

    /**
     * todo 遵义档案功能需要每个节点都接收档案
     * 党员转入生成流程跟接收档案信息
     * @param memDTO
     * @param memCode
     */
    private void withDig(MemDTO memDTO, String memCode){

        Mem mem = memService.findByCode(memCode);
        // todo 2025-03-20: 遵义转接接收处理
        if(Objects.nonNull(mem)) {
            LambdaUpdateWrapper<Mem> wrapper = Wrappers.lambdaUpdate();
            // 获取档案唯一码
            mem.setDigitalLotNo(StrUtil.isNotBlank(memDTO.getDigitalLotNo()) ? memDTO.getDigitalLotNo() : mem.getDigitalLotNo());
            // 判断该党员属于指定遵义节，只有遵义节点的才会生成流程信息
            //  判断是否预备党员
            if(com.zenith.front.common.constant.UserConstant.HAS_ZUN_YI && StrUtil.equals(mem.getD08Code(), CommonConstant.TWO)){
                // 当两个参数都没有这个档案唯一码则新生成
                mem.setDigitalLotNo(StrUtil.isNotBlank(mem.getDigitalLotNo()) ? mem.getDigitalLotNo() : IdUtil.simpleUUID());
                // 生成指定流程
                List<MemDevelopProcess> processList = TransferRecordServiceImpl.generate(mem.getD08Code(), mem.getDigitalLotNo(), mem.getJoinOrgDate());
                // 获取最新未审批流程
                MemDevelopProcess process = processList.stream().filter(e -> Objects.isNull(e.getApproveTime())).findFirst().orElse(null);
                if(Objects.nonNull(process)){
                    // 先清空一次档案唯一码一致的流程
                    LambdaQueryWrapper<MemDevelopProcess> memDevelopProcessLambdaQueryWrapper = Wrappers.lambdaQuery();
                    memDevelopProcessLambdaQueryWrapper.eq(MemDevelopProcess::getDigitalLotNo, mem.getDigitalLotNo())
                            .eq(MemDevelopProcess::getProcessNode, process.getProcessNode());
                    memDevelopProcessMapper.delete(memDevelopProcessLambdaQueryWrapper);

                    mem.setProcessNode(process.getProcessNode());
                    // 更新党员流程状态
                    wrapper.set(Mem::getProcessNode, process.getProcessNode());
                    wrapper.set(Mem::getIsCatalogue, CommonConstant.ONE_INT);
                    memDevelopProcessMapper.myInserBatch(processList);
                }
            }
            // 更新唯一码
            wrapper.set(Mem::getDigitalLotNo, mem.getDigitalLotNo());
            wrapper.eq(Mem::getCode, mem.getCode());
            memService.update(wrapper);
        }
        // 遵义-档案信息，不管当前节点是否遵义，它都要接收并保存，下次转到遵义节点时带过去查看
        if(CollUtil.isNotEmpty(memDTO.getMemDigital())){
            List<MemDigital> memDigitals = JSONUtil.toList(JSONUtil.parseArray(memDTO.getMemDigital()), MemDigital.class);
            if(CollUtil.isNotEmpty(memDigitals)) {
                List<String> collect = memDigitals.stream().map(MemDigital::getCode).collect(Collectors.toList());
                memDigitalMapper.delete(new QueryWrapper<MemDigital>().lambda().in(MemDigital::getCode, collect));
                memDigitalMapper.myInserBatch(memDigitals);
            }
        }
        // 遵义-档案操作信息日志，不管当前节点是否遵义，它都要接收并保存，下次转到遵义节点时带过去查看
        if(CollUtil.isNotEmpty(memDTO.getMemDigitalOperationLog())){
            List<MemDigitalOperationLog> memDigitalOperationLogList = JSONUtil.toList(JSONUtil.parseArray(memDTO.getMemDigitalOperationLog()), MemDigitalOperationLog.class);
            if(CollUtil.isNotEmpty(memDigitalOperationLogList)) {
                List<String> collect = memDigitalOperationLogList.stream().map(MemDigitalOperationLog::getCode).collect(Collectors.toList());
                memDigitalOperationLogMapper.delete(new QueryWrapper<MemDigitalOperationLog>().lambda().in(MemDigitalOperationLog::getCode, collect));
                memDigitalOperationLogMapper.myInserBatch(memDigitalOperationLogList);
            }
        }
    }

    /**
     * 取消提示
     *
     * @param data
     * @return
     */
    @Override
    public OutMessage<?> cancelHint(TransferCancelHintDTO data) {
        if(CollUtil.isEmpty(data.getTransferIdList())) {
            return new OutMessage<>(Status.SUCCESS);
        }
        final String account = UserConstant.USER_CONTEXT.get().getUser().getAccount();
        final Date currentDate = new Date();
        List<TransferHint> saveList = data.getTransferIdList().stream().map(e -> {
            TransferHint th = new TransferHint();
            th.setCode(IdUtil.simpleUUID());
            th.setTransferId(e);
            th.setHintDate(currentDate);
            th.setType(data.getHintType());
            th.setHintAccount(account);
            return th;
        }).collect(Collectors.toList());
        transferHintMapper.myInserBatch(saveList);
        return new OutMessage<>(Status.SUCCESS);
    }

    /**
     * 超期自动退回
     * @param record
     * @param reason
     * @return
     */
    @Override
    public boolean overdueBackTransfer(TransferRecord record, String reason) {
        if (record == null) {
            return false;
        }
        //不能更改已通过或退回的审批记录
        Integer status = record.getStatus();
        if (!TransferRecordConstant.TRANSFERING.equals(status)) {
            return false;
        }

        String recordId = record.getId();
        TransferApproval transferApproval = transferApprovalService.findByRecordIdAndParentId(recordId, TransferApprovalConstant.ROOT_ID);
        if(Objects.isNull(transferApproval) || StrUtil.isBlank(transferApproval.getId())) {
            return false;
        }
        //更新转接记录状态
        TransferRecord updateRecord = new TransferRecord();
        updateRecord.setId(recordId);
        updateRecord.setStatus(TransferRecordConstant.TRANSFER_OVERDUE_BACK);
        updateRecord.setUpdateTime(new Date());
        updateRecord.setRemark(reason);

        //生成一条超期自动退回记录日志
        TransferLog transferLog = new TransferLog();
        transferLog.setHandleApprovalId(transferApproval.getId());
        transferLog.setReason(reason);
        transferLog.setType(TransferLogConstant.OVERDUE_BACK_STATUS);

        // TODO: 2021/7/16 事务
        boolean b1 = updateById(updateRecord);
        boolean b2 = transferLogService.rewriteSave(transferLog);
        String recordMemId = record.getMemId();
        this.updateMemIsTransfer(recordId, Collections.singletonList(recordMemId));
        // TODO: 2021/11/17 撤销的时候，如果接受节点不在当前数据库就需要同步到中间交换区，让接受数据节点知晓
        String targetOrgId = record.getTargetOrgId();
        Org orgByCode = orgService.findOrgByCode(targetOrgId);
        if (b1 && b2 && ObjectUtil.isNull(orgByCode) && StrKit.notBlank(targetOrgId)) {
            //查询转接记录
            TransferRecord recordNow = getById(record.getId());
            //处理携带的人员信息
            String memId = recordNow.getMemId();
            Mem allByCode = memService.findAllByCode(memId);
            if (ObjectUtil.isNotNull(allByCode)) {
                //recordNow.setExtraData(allByCode);
                JSONObject extraDataJsonObject = JSONObject.parseObject(JSONObject.toJSONString(allByCode));
                recordNow.setExtraData(extraDataJsonObject);
                // todo 2025-03-20: 个人党员转出添加档案信息
                if(StrUtil.isNotBlank(allByCode.getDigitalLotNo())) {
                    this.digLotNoData(allByCode.getDigitalLotNo(), extraDataJsonObject);
                }
            }
            JSONObject jsonObject = JSONObject.parseObject(JSONObject.toJSONString(recordNow));
            JSONArray jsonArray = new JSONArray();
            //查询转接节点审批记录
            List<TransferApproval> byRecordId = transferApprovalService.findByRecordId(recordNow.getId());
            byRecordId.forEach(TransferApproval -> jsonArray.add(JSONObject.parseObject(JSONObject.toJSONString(TransferApproval))));
            jsonObject.put("approval", jsonArray);
            String replaceUrl = sync_flow_push.replace("/api/flow/push", "");
            HttpKit.doPost(replaceUrl + "/transfer/insertData", jsonObject, "UTF-8");
        }
        return b1 && b2;
    }
}
