package com.zenith.front.core.service.user;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.crypto.digest.DigestUtil;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.zenith.front.api.role.IRoleService;
import com.zenith.front.api.sheence.ISheenceService;
import com.zenith.front.api.user.IUserRoleInfoViewService;
import com.zenith.front.api.user.IUserRolePermissionService;
import com.zenith.front.api.user.IUserService;
import com.zenith.front.common.constant.CacheConstant;
import com.zenith.front.common.constant.CommonConstant;
import com.zenith.front.common.constant.RoleConstant;
import com.zenith.front.common.kit.HttpKit;
import com.zenith.front.common.kit.StrKit;
import com.zenith.front.common.untils.SM4Untils;
import com.zenith.front.core.cache.CacheUtils;
import com.zenith.front.core.kit.TokenUtils;
import com.zenith.front.dao.mapper.mem.MemMapper;
import com.zenith.front.dao.mapper.org.OrgAllMapper;
import com.zenith.front.dao.mapper.user.UserMapper;
import com.zenith.front.model.bean.*;
import com.zenith.front.model.constant.UserConstant;
import com.zenith.front.model.custom.UserTicket;
import com.zenith.front.model.dto.LoginDTO;
import com.zenith.front.model.dto.PageDTO;
import com.zenith.front.model.dto.UserDTO;
import com.zenith.front.model.dto.UserLoginFailedDTO;
import com.zenith.front.model.message.OutMessage;
import com.zenith.front.model.message.Status;
import com.zenith.front.model.modelview.UserRoleInfoView;
import com.zenith.front.model.vo.LoginVO;
import com.zenith.front.model.vo.UserPO;
import org.bouncycastle.crypto.Digest;
import org.bouncycastle.crypto.digests.SM3Digest;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <p>
 * 用户表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-07-10
 */
@Service
public class UserServiceImpl extends ServiceImpl<UserMapper, User> implements IUserService {

    @Resource
    private IUserRolePermissionService userRolePermissionService;
    @Resource
    private IRoleService roleService;
    @Resource
    private ISheenceService sheenceService;
    @Resource
    private IUserRoleInfoViewService userRoleInfoViewService;
    @Resource
    private UserMapper userMapper;
    @Resource
    private MemMapper memMapper;
    @Value("${server_url}")
    private String server_url;
    @Value("${accessKeyId}")
    private String accessKeyId;
    @Value("${accessKeyScrect}")
    private String accessKeyScrect;
    @Value("${exchange}")
    private Boolean exchange;
    @Value("${exchange_user_link}")
    private String exchange_user_link;
    @Value("${exchange_nginx_key}")
    private String exchange_nginx_key;
    @Value("${exchange_user_login}")
    private String exchange_user_login;
    @Value("${exchange_user_add}")
    private String exchange_user_add;
    @Value("${exchange_key}")
    private String exchange_key;
    @Value("${spring.profiles.active}")
    private String env;
    @Value("${sync_flow_push}")
    private String sync_flow_push;

    @Autowired
    private OrgAllMapper orgAllMapper;

    @Override
    public User findUserByOpenId(String openId) {
        LambdaQueryWrapper<User> query = new LambdaQueryWrapper<User>()
                .eq(User::getOpenId, openId)
                .select(User::getId, User::getPassword, User::getAccount);
        return getOne(query);
    }

    @Override
    public String findMemNameByUserId(String userId) {
        Mem mem = memMapper.findMemByUserId(userId);
        return Objects.nonNull(mem) ? mem.getName() : null;
    }

    @Override
    public Page<User> findByPermissionRoleId(int pageNum, int pageSize, String roleId, String currManOrgCode) {
        return userMapper.findByPermissionRoleId(new Page<>(pageNum, pageSize), roleId, currManOrgCode);
    }

    @Override
    public List<User> findByRoleId(String roleVoId, String orgCode) {
        return userMapper.findByRoleId(roleVoId, orgCode);
    }

    @Override
    public User findById(String id) {
        return getById(id);
    }

    @Override
    public OutMessage<LoginVO> login(LoginDTO data) {
        //更新用户的最后登录时间
        User updateUser = new User();
        String account = data.getAccount();
        String password = data.getPassword();
        UserLoginFailedDTO userLoginFailedDTO = CacheUtils.getUserLoginFailedDTO(CacheConstant.CACHE_USER_LOGIN_FAILED_COUNT_PREFIX + data.getAccount());
        User user = findByAccount(account);
        //登录失败,数据库没有该用户
        if (user == null) {
            return new OutMessage<>(Status.USER_NOT_EXIST);
        }
        String md5Hex = DigestUtil.md5Hex(password);
        // todo 加密机加密比对密码
        String mpPassword = SM4Untils.encryptContent(password);
        String appcode = data.getAppcode();
        String openToken = data.getToken();

        //密码不匹配(因为改造了对接渝快政，所以渝快政这边过来的用户不会出现锁定情况)
        if (StrKit.isBlank(appcode) && StrKit.isBlank(openToken) && !md5Hex.equalsIgnoreCase(user.getPassword()) && !mpPassword.equals(user.getPassword())) {
            if (userLoginFailedDTO == null) {
                //放入计数对象
                userLoginFailedDTO = new UserLoginFailedDTO(data.getAccount(), 1);
                CacheUtils.putUserLoginFailedDTO(CacheConstant.CACHE_USER_LOGIN_FAILED_COUNT_PREFIX + data.getAccount(), userLoginFailedDTO);
            } else {
                userLoginFailedDTO.increment();
                int count = userLoginFailedDTO.getCount();
                //增加锁定次数
                CacheUtils.putUserLoginFailedDTO(CacheConstant.CACHE_USER_LOGIN_FAILED_COUNT_PREFIX + data.getAccount(), userLoginFailedDTO);
                if (count == com.zenith.front.common.constant.UserConstant.LOGIN_ERROR_MAX_NUM) {
                    User lock = new User();
                    lock.setUpdateTime(new Date());
                    lock.setId(user.getId());
                    //锁定用户
                    lock.setIsLock(com.zenith.front.common.constant.UserConstant.LOCK);
                    updateById(lock);
                    return new OutMessage<>(Status.USER_IS_LOCK);
                }
            }
            int count = userLoginFailedDTO.getCount();
            OutMessage outMessage = new OutMessage(Status.LOGIN_FAILED);
            outMessage.setMessage(outMessage.getMessage() + ",剩余" + (com.zenith.front.common.constant.UserConstant.LOGIN_ERROR_MAX_NUM - count) + "次机会,超过后账号将会被锁定");
            return outMessage;
        }
        UserTicket userTicket = new UserTicket();
        userTicket.setCreateTime(System.currentTimeMillis());
        //生成token
        String token = TokenUtils.createToken(data.getIp(), data.getUa(), userTicket.getCreateTime());
        LoginVO loginVO = new LoginVO();
        loginVO.setToken(token);
        //查询出用户角色权限关系

        UserRolePermission userRolePermission = userRolePermissionService.getById(user.getCurrentUserRoleid());
        //无权限关系
        if (userRolePermission == null) {
            return new OutMessage<>(Status.ROLE_PAST);
        }
        String roleId = userRolePermission.getRoleId();
        Role role = roleService.findByIdAndValid(roleId);
        if (role == null) {
            //角色已过期,需要设置个默认角色给用户
            LambdaQueryWrapper<UserRolePermission> queryWrapper = new LambdaQueryWrapper<UserRolePermission>()
                    .eq(UserRolePermission::getUserId, user.getId())
                    .select(UserRolePermission::getId, UserRolePermission::getUserId, UserRolePermission::getRoleId, UserRolePermission::getPermission
                            , UserRolePermission::getOrgCode, UserRolePermission::getOrgId, UserRolePermission::getCreateTime, UserRolePermission::getUpdateTime);
            List<UserRolePermission> permissionList = userRolePermissionService.list(queryWrapper);
            for (int i = 0; i < permissionList.size(); i++) {
                UserRolePermission userRolePermission1 = permissionList.get(i);
                Role validRole = roleService.findByIdAndValid(userRolePermission1.getRoleId());
                if (validRole != null) {
                    updateUser.setCurrentUserRoleid(userRolePermission1.getId());
                    userRolePermission = userRolePermission1;
                    user.setCurrentUserRoleid(userRolePermission1.getId());
                    break;
                }
                if (i == permissionList.size() - 1) {
                    return new OutMessage<>(Status.ROLE_PAST);
                }
            }

        }
        userTicket.setToken(token);
        userTicket.setUser(user);
        userTicket.setUserRolePermission(userRolePermission);
//        String oldToken = CacheUtils.getUserToken(user.getId());
//        if (oldToken != null) {
//            //移除旧token 所对应的 用户缓存信息
//            CacheUtils.removeUserCache(oldToken);
//        }
        //缓存放入用户信息
        CacheUtils.putUserCache(token, userTicket);
        CacheUtils.putUserToken(user.getId(), token);
        UserConstant.USER_CONTEXT.set(userTicket);
        updateUser.setId(user.getId());
        updateUser.setLastLoginTime(new Date());
        updateUser.setIsLock(com.zenith.front.common.constant.UserConstant.UNLOCK);
        updateById(updateUser);
        loginVO.setExchange(exchange_key);
        //移除登录错误信息
        CacheUtils.removeUserLoginFailedDTO(CacheConstant.CACHE_USER_LOGIN_FAILED_COUNT_PREFIX + data.getAccount());
        // 设置是否需要强制修改密码
        Date lastUpdatePasswordTime = user.getLastUpdatePasswordTime();
        if (lastUpdatePasswordTime != null && DateUtil.offsetMonth(lastUpdatePasswordTime, 3).getTime() < new DateTime().getTime()) {
            loginVO.setNeedUpdatePassword(true);
        } else {
            loginVO.setNeedUpdatePassword(false);
        }

        return new OutMessage<>(Status.SUCCESS, loginVO);
    }

    public User findByAccount(String account) {
        LambdaQueryWrapper<User> query = new LambdaQueryWrapper<User>().eq(User::getAccount, account).eq(User::getIsDelete, com.zenith.front.common.constant.UserConstant.NOT_IS_DELETE);
        return getOne(query);
    }

    @Override
    public OutMessage sheLogin(String name, String phone, String unit, String zhiwu, String youxiang, String zixunxuqiu) {
        Sheence sheence = new Sheence();
        sheence.setName(name);
        sheence.setPhone(phone);
        sheence.setUnit(unit);
        sheence.setZhiwu(zhiwu);
        sheence.setYouxiang(youxiang);
        sheence.setZixunxuqiu(zixunxuqiu);
        boolean save = sheenceService.save(sheence);
        return new OutMessage(save ? Status.SUCCESS : Status.FAIL);
    }

    @Override
    public OutMessage<UserDTO> findUserById(String id) {
        UserTicket userTicket = UserConstant.USER_CONTEXT.get();
        if (userTicket == null) {
            return new OutMessage<>(Status.NOT_LOGIN);
        }
        UserRoleInfoView user = userRoleInfoViewService.findUserInfoById(id);
        if (user == null) {
            return new OutMessage<>(Status.USER_NOT_EXIST);
        }
        //不是获取自己的权限信息 进行校验
        if (!userTicket.getUser().getId().equalsIgnoreCase(id)) {
            String userOrgCode = userTicket.getUserRolePermission().getOrgCode();
            //判断当前用户是否有权限获取用户信息
            String orgCode = user.getOrgCode();
            if (StrKit.notBlank(userOrgCode)) {
                if (!orgCode.startsWith(userOrgCode)) {
                    return new OutMessage<>(Status.PERMISSION_DENIED);
                }
            }
        }
        UserDTO userDTO = new UserDTO();
        BeanUtils.copyProperties(user, userDTO);
        userDTO.setOrgName(CacheUtils.getOrgName(user.getOrgId()));
        userDTO.setMemName(CacheUtils.getMemName(user.getMemCode()));
        List<UserDTO.UserRolePermissionInfo> roleInfo = user.roleInfo();
        roleInfo.forEach(var -> var.setManagerOrgName(CacheUtils.getOrgName(var.getManagerOrgId())));
        userDTO.setManages(roleInfo);
        return new OutMessage<>(Status.SUCCESS, userDTO);
    }

    @Override
    public OutMessage<Page<UserDTO>> getUserOrgCodePage(PageDTO pageDTO) {
        UserTicket userTicket = UserConstant.USER_CONTEXT.get();
        if (userTicket == null) {
            return new OutMessage<>(Status.NOT_LOGIN);
        }
        //判断用户是否有权限查询
        String currentOrgCode = userTicket.getUserRolePermission().getOrgCode();
        String code = pageDTO.getCode();
        if (!code.startsWith(currentOrgCode)) {
            return new OutMessage<>(Status.PERMISSION_DENIED);
        }
        // TODO: 2022/4/28 处理上级账号登录后,用户管理列表当下级账号存在管理员时不显示问题
        boolean isExclude = !StrUtil.equals(code, currentOrgCode) && StrUtil.startWith(code, currentOrgCode);
        Page<UserRoleInfoView> page = userRoleInfoViewService.findUserByOrgCodePage(code, isExclude, null, pageDTO.getPageNumber(), pageDTO.getPageSize());
        Page<UserDTO> dtoPage = userPageToUserDtoPage(page);
        return new OutMessage<>(Status.SUCCESS, dtoPage);
    }

    /***
     * 用户分页 转 dto分页
     * */
    private Page<UserDTO> userPageToUserDtoPage(Page<UserRoleInfoView> page) {
        List<UserDTO> data = page.getRecords().stream().map(user -> {
            UserDTO userDTO = new UserDTO();
            BeanUtils.copyProperties(user, userDTO);
            String orgId = user.getOrgId();
            userDTO.setOrgName(CacheUtils.getOrgName(orgId));
            List<UserDTO.UserRolePermissionInfo> list = user.roleInfo();
            list.forEach(val -> val.setManagerOrgName(CacheUtils.getOrgName(val.getManagerOrgId())));
            userDTO.setManages(list);
            userDTO.setIp(CacheUtils.getUserLoginIp(user.getAccount()));
            userDTO.setLoginCount(CacheUtils.getUserLoginCount(user.getAccount()));
            userDTO.setMemName(CacheUtils.getMemName(user.getMemCode()));
            return userDTO;
        }).collect(Collectors.toList());
        Page<UserDTO> dtoPage = new Page<>();
        dtoPage.setCurrent(page.getCurrent());
        dtoPage.setSize(page.getSize());
        dtoPage.setTotal(page.getTotal());
        dtoPage.setRecords(data);
        return dtoPage;
    }

    @Override
    public OutMessage<String> addUser(UserDTO userDTO) {
        UserTicket userTicket = UserConstant.USER_CONTEXT.get();
        if (Objects.isNull(userTicket)) {
            return new OutMessage<>(Status.NOT_LOGIN);
        }
        //获取用户登录信息
        User currentUser = userTicket.getUser();
        String currentAccount = currentUser.getAccount();
        //查询该账户是否存在
        String account = userDTO.getAccount();
        User byAccount = findByAccount(account);
        if (byAccount != null) {
            return new OutMessage<>(Status.USER_HAVE_LIVE);
        }
        //从中间交换区验证账号
        if (exchange) {
            JSONObject postJson = new JSONObject();
            postJson.put("account", account.trim());
            String replaceUrl = sync_flow_push.replace("/api/flow/push", "");
            String res = HttpKit.doPost(replaceUrl + "/login/exist", postJson, "UTF-8");
            JSONObject jsonObject = JSONObject.parseObject(res);
            Integer code = jsonObject.getInteger("code");
            if (code != 0) {
                return new OutMessage<>(Status.USER_HAVE_LIVE);
            }
        }
        List<UserDTO.UserRolePermissionInfo> manages = userDTO.getManages();
        if (CollUtil.isEmpty(manages)) {
            return new OutMessage<>(Status.PERMISSION_DENIED);
        }
        // TODO: 2022/8/23 从管理单位中获取
        UserDTO.UserRolePermissionInfo userRolePermissionInfo = manages.get(0);
        String managerOrgCode = userRolePermissionInfo.getManagerOrgCode();
        String managerOrgId = userRolePermissionInfo.getManagerOrgId();
        //创建用户
        User user = new User();
        String id = IdUtil.simpleUUID();
        user.setId(id);
        user.setAccount(account);
        user.setName(userDTO.getName());
//        user.setPassword(DigestUtil.md5Hex(userDTO.getPassword()));
        // todo 加密机处理密码
        user.setPassword(SM4Untils.encryptContent(userDTO.getPassword()));
        user.setPhone(userDTO.getPhone());
        user.setIsDelete(com.zenith.front.common.constant.UserConstant.NOT_IS_DELETE);
        user.setIsLock(com.zenith.front.common.constant.UserConstant.UNLOCK);
        user.setOrgCode(managerOrgCode);
        user.setOrgId(managerOrgId);
        user.setManagementSystem(userDTO.getManagementSystem());
        user.setCreateAccount(currentUser.getAccount());
        //判断用户管理角色是否有效
        List<UserRolePermission> userRolePermissionList = new ArrayList<>();
        //用户默认权限
        UserRolePermission defaultUserRolePermission = null;
        for (UserDTO.UserRolePermissionInfo manage : manages) {
            String roleId = manage.getRoleId();
            Role role = roleService.findSubRoleByIdAndParentId(manage.getRoleId(), userTicket.getUserRolePermission().getRoleId());
            if (role == null) {
                return new OutMessage<>(Status.ROLE_NOT_EXIST);
            }
            UserRolePermission userRolePermission = new UserRolePermission();
            userRolePermission.setId(IdUtil.simpleUUID());
            userRolePermission.setUserId(user.getId());
            userRolePermission.setRoleId(roleId);
            userRolePermission.setOrgCode(manage.getManagerOrgCode());
            userRolePermission.setOrgId(manage.getManagerOrgId());
            userRolePermission.setPermission(role.getPermission());
            userRolePermission.setCreateAccount(currentAccount);
            userRolePermission.setUpdateAccount(currentAccount);
            //默认角色关系
            if (manage.getIsDefault() == 1) {
                defaultUserRolePermission = userRolePermission;
            }
            userRolePermissionList.add(userRolePermission);
        }
        //如果都没有默认权限,默认生成一条
        if (defaultUserRolePermission == null) {
            defaultUserRolePermission = userRolePermissionList.get(0);
        }

        //设置用户当前角色权限id
        user.setCurrentUserRoleid(defaultUserRolePermission.getId());

        // TODO: 2021/7/14 事务
        boolean save = save(user);
        boolean batchSave = userRolePermissionService.batchSave(userRolePermissionList);
        boolean isOk = save && batchSave;
        //todo 开启同步的情况下，对账号进行处理同步问题，不然无法处理
        if (isOk && exchange) {
            this.insertUserToMiddle(id, defaultUserRolePermission.getPermission());
        }
        return isOk ? new OutMessage<>(Status.SUCCESS, user.getId()) : new OutMessage<>(Status.FAIL);
    }

    /**
     * 向中间交换区存储用户数据
     */
    public void insertUserToMiddle(String userId, String permission) {
        User user = userMapper.selectById(userId);
        if (Objects.isNull(user)) {
            return;
        }
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("data_key", exchange_nginx_key);
        jsonObject.put("account", user.getAccount().trim());
        jsonObject.put("id", userId);
        jsonObject.put("exchange_user_login", exchange_user_login);

        // TODO ******** 分节点需要将账户权限同步至中间交换区，同时在中间交换区开发账户显示体系，拥有哪些权限和权限菜单展示
        jsonObject.put("create_time", user.getCreateTime());
        jsonObject.put("org_level_code", user.getOrgCode());
        jsonObject.put("org_name", CacheUtils.getOrgName(user.getOrgId()));
        jsonObject.put("permission", permission);
        jsonObject.put("is_delete", user.getIsDelete());
        //临时解决同步
        String replaceUrl = sync_flow_push.replace("/api/flow/push", "");
        String s = HttpKit.doPost(replaceUrl + "/login/add", jsonObject, "UTF-8");
        System.out.println("账号同步情况===>" + s);
    }


    public String port() {
        return StrUtil.equals("prod", env) ? "8080" : "9099";
    }

    @Override
    public OutMessage<String> editUser(UserDTO userDTO, boolean isBatch) {
        UserTicket userVO = UserConstant.USER_CONTEXT.get();
        if (userVO == null) {
            return new OutMessage<>(Status.NOT_LOGIN);
        }
        List<UserDTO.UserRolePermissionInfo> manages = userDTO.getManages();
        if (CollUtil.isEmpty(manages)) {
            return new OutMessage<>(Status.PERMISSION_DENIED);
        }
        String currentAccount = userVO.getUser().getAccount();
        User userById = getById(userDTO.getId());
        if (userById == null) {
            return new OutMessage<>(Status.USER_NOT_EXIST);
        }
        UserDTO.UserRolePermissionInfo userRolePermissionInfo = manages.get(0);
        //判断用户是否有权限修改
        String orgCode = userVO.getUserRolePermission().getOrgCode();
        String requestOrgCode = userRolePermissionInfo.getManagerOrgCode();
        String managerOrgId = userRolePermissionInfo.getManagerOrgId();
        if (!StrUtil.startWith(requestOrgCode, orgCode)) {
            return new OutMessage<>(Status.PERMISSION_DENIED);
        }
        User user = new User();
        user.setId(userDTO.getId());
        //判断用户姓名 手机号 关联人员不为空
        if (StrKit.notBlank(userDTO.getName()) && !isBatch) {
            user.setName(userDTO.getName());
        }
        //用户手机号
        if (StrKit.notBlank(userDTO.getPhone()) && !isBatch) {
            user.setPhone(userDTO.getPhone());
        }
        //用户关联
        if (StrKit.notBlank(userDTO.getMemCode()) && !isBatch) {
            user.setName(userDTO.getMemName());
            user.setMemCode(userDTO.getMemCode());
        }
        //判断用户所属组织不为空
        if (StrKit.notBlank(requestOrgCode, managerOrgId) && !isBatch) {
            user.setOrgCode(requestOrgCode);
            user.setOrgId(managerOrgId);
        }
        //判断只读字段是否不为空
        if (userDTO.getReadOnly() != null) {
            user.setReadOnly(userDTO.getReadOnly());
        }
        //判断用户是否被锁定
        if (userDTO.getIsLock() != null) {
            Integer isLock = userDTO.getIsLock();
            switch (isLock) {
                case com.zenith.front.common.constant.UserConstant.LOCK:
                    user.setIsLock(com.zenith.front.common.constant.UserConstant.LOCK);
                    CacheUtils.putUserLoginFailedDTO(CacheConstant.CACHE_USER_LOGIN_FAILED_COUNT_PREFIX + userById.getAccount()
                            , new UserLoginFailedDTO(userById.getAccount()
                                    , com.zenith.front.common.constant.UserConstant.LOGIN_ERROR_MAX_NUM));
                    //锁定用户
                    break;
                case com.zenith.front.common.constant.UserConstant.UNLOCK:
                    //解锁用户
                    user.setIsLock(com.zenith.front.common.constant.UserConstant.UNLOCK);
                    CacheUtils.removeUserLoginFailedDTO(CacheConstant.CACHE_USER_LOGIN_FAILED_COUNT_PREFIX + userById.getAccount());
                    break;
                default:
                    return new OutMessage<>(Status.PARA_ERROR);

            }
        }

        user.setUpdateAccount(currentAccount);
        user.setUpdateTime(new Date());
        //用户角色权限
        List<UserRolePermission> userRolePermissionList = new ArrayList<>();
        UserRolePermission defaultUserRolePermission = null;
        for (UserDTO.UserRolePermissionInfo manage : manages) {
            Role role = roleService.findSubRoleByIdAndParentId(manage.getRoleId(), userVO.getUserRolePermission().getRoleId());
            if (role == null) {
                return new OutMessage<>(Status.ROLE_NOT_EXIST);
            }
            //如果管理组织code 和 id 为空 为批量修改,批量修改 修改用户默认角色
            if (isBatch) {
                UserRolePermission userRolePermission = userRolePermissionService.findById(userById.getCurrentUserRoleid());
                if (userRolePermission == null) {
                    return new OutMessage<>(Status.ROLE_NOT_EXIST);
                }
                userRolePermission.setRoleId(role.getId());
                userRolePermission.setPermission(role.getPermission());
                userRolePermissionList.add(userRolePermission);
            } else {
                //如果为单个修改 需要情况用户之前的所有角色
                UserRolePermission userRolePermission = new UserRolePermission();
                userRolePermission.setId(IdUtil.simpleUUID());
                userRolePermission.setUserId(user.getId());
                userRolePermission.setRoleId(manage.getRoleId());
                userRolePermission.setOrgCode(manage.getManagerOrgCode());
                userRolePermission.setOrgId(manage.getManagerOrgId());
                userRolePermission.setPermission(role.getPermission());
                userRolePermission.setCreateAccount(currentAccount);
                userRolePermission.setUpdateAccount(currentAccount);
                //默认角色关系
                if (manage.getIsDefault() == 1) {
                    defaultUserRolePermission = userRolePermission;
                }
                userRolePermissionList.add(userRolePermission);
            }
        }
        //如果都没有默认权限,默认生成一条
        if (defaultUserRolePermission == null) {
            defaultUserRolePermission = userRolePermissionList.get(0);
        }
        user.setCurrentUserRoleid(defaultUserRolePermission.getId());

        boolean flag;
        if (isBatch) {
            flag = userRolePermissionService.batchUpdate(userRolePermissionList);
        } else {
            //清空用户所有权限
            flag = userRolePermissionService.cleanAll(user.getId());
            //新增用户权限
            flag = userRolePermissionService.batchSave(userRolePermissionList) && flag;
        }

        boolean isOk = updateById(user) && flag;
        //如果被修改的用户正在登录中,需要移除用户的登录信息,迫使用户重新登录
        String userToken = CacheUtils.getUserToken(user.getId());
        if (userToken != null) {
            CacheUtils.removeUserCache(userToken);
        }
        // todo 2025年5月16日 开启用户信息同步
        if (isOk && exchange) {
            this.insertUserToMiddle(user.getId(), defaultUserRolePermission.getPermission());
        }
        return isOk ? new OutMessage<>(Status.SUCCESS) : new OutMessage<>(Status.FAIL);
    }

    @Override
    public OutMessage<String> batchEdit(List<UserDTO> list) {
        boolean isOk = true;
        for (UserDTO userDTO : list) {
            OutMessage<String> message = editUser(userDTO, true);
            if (!Status.SUCCESS.getMessage().equalsIgnoreCase(message.getMessage())) {
                isOk = false;
                break;
            }
        }
        return isOk ? new OutMessage<>(Status.SUCCESS) : new OutMessage<>(Status.FAIL);
    }

    @Override
    public OutMessage<String> delUser(UserDTO userDTO) {
        UserTicket userVO = UserConstant.USER_CONTEXT.get();
        if (userVO == null) {
            return new OutMessage<>(Status.NOT_LOGIN);
        }
        //不允许删除自己的账号
        if (userVO.getUser().getId().equalsIgnoreCase(userDTO.getId())) {
            return new OutMessage<>(Status.PERMISSION_DENIED);
        }
        UserRoleInfoView roleInfoView = userRoleInfoViewService.findUserInfoById(userDTO.getId());
        if (roleInfoView == null) {
            return new OutMessage<>(Status.USER_NOT_EXIST);
        }
        UserDTO userInfoDto = new UserDTO();
        BeanUtils.copyProperties(roleInfoView, userInfoDto);
        userInfoDto.setManages(roleInfoView.roleInfo());
        //不允许删除超级管理员
        for (UserDTO.UserRolePermissionInfo info : userInfoDto.getManages()) {
            Integer roleType = info.getRoleType();
            if (roleType != null && roleType.equals(RoleConstant.ROLE_TYPE_SUPER_ADMIN)) {
                return new OutMessage<>(Status.PERMISSION_DENIED);
            }
        }

        //判断被修改的用户是否是管理范围下的组织
        if (!roleInfoView.getOrgCode().startsWith(userVO.getUserRolePermission().getOrgCode())) {
            return new OutMessage<>(Status.PERMISSION_DENIED);
        }
        //若果被删除的用户在登录中,需要移除用户的登录信息,迫使用户重新登录
        String userToken = CacheUtils.getUserToken(roleInfoView.getId());
        if (userToken != null) {
            CacheUtils.removeUserCache(userToken);
        }

        User user = new User();
        user.setId(userDTO.getId());
        user.setIsDelete(com.zenith.front.common.constant.UserConstant.IS_DELETE);
        user.setUpdateTime(new Date());
        user.setUpdateAccount(userVO.getUser().getAccount());
        boolean isOk = updateById(user);
        // todo 2025年5月16日 开启用户信息同步
        if (isOk && exchange) {
            this.insertUserToMiddle(user.getId(), null);
        }
        return isOk ? new OutMessage<>(Status.SUCCESS) : new OutMessage<>(Status.FAIL);
    }

    @Override
    public OutMessage<String> lockUser(UserDTO userDTO) {
        UserTicket userVO = UserConstant.USER_CONTEXT.get();
        if (userVO == null) {
            return new OutMessage<>(Status.NOT_LOGIN);
        }
        //不允许指定自己
        if (userVO.getUser().getId().equalsIgnoreCase(userDTO.getId())) {
            return new OutMessage<>(Status.PERMISSION_DENIED);
        }

        UserRoleInfoView user = userRoleInfoViewService.findUserInfoById(userDTO.getId());
        if (user == null) {
            return new OutMessage<>(Status.USER_NOT_EXIST);
        }
        UserDTO userInfoDto = new UserDTO();
        BeanUtils.copyProperties(user, userInfoDto);
        userInfoDto.setManages(user.roleInfo());
        //不允许锁定超级管理员
        for (UserDTO.UserRolePermissionInfo info : userInfoDto.getManages()) {
            Integer roleType = info.getRoleType();
            if (roleType != null && roleType.equals(RoleConstant.ROLE_TYPE_SUPER_ADMIN)) {
                return new OutMessage<>(Status.PERMISSION_DENIED);
            }
        }

        //判断被修改的用户是否是管理范围下的组织
        if (!user.getOrgCode().startsWith(userVO.getUserRolePermission().getOrgCode())) {
            return new OutMessage<>(Status.PERMISSION_DENIED);
        }

        User data = new User();
        data.setId(user.getId());
        data.setIsLock(com.zenith.front.common.constant.UserConstant.LOCK);
        data.setUpdateTime(new Date());
        data.setUpdateAccount(userVO.getUser().getAccount());
        boolean isOk = updateById(data);
        if (isOk) {
            //如果用户被锁定了,需要移除登录用户的token,迫使用户重新登录
            String userToken = CacheUtils.getUserToken(user.getId());
            if (userToken != null) {
                CacheUtils.removeUserCache(userToken);
            }
            CacheUtils.putUserLoginFailedDTO(CacheConstant.CACHE_USER_LOGIN_FAILED_COUNT_PREFIX + user.getAccount(), new UserLoginFailedDTO(user.getAccount(), com.zenith.front.common.constant.UserConstant.LOGIN_ERROR_MAX_NUM));
        }
        return isOk ? new OutMessage<>(Status.SUCCESS) : new OutMessage<>(Status.FAIL);
    }

    @Override
    public OutMessage<String> unlockUser(UserDTO userDTO) {

        UserTicket userTicket = UserConstant.USER_CONTEXT.get();
        if (userTicket == null) {
            return new OutMessage<>(Status.NOT_LOGIN);
        }
        //不允许解锁自己
        if (userTicket.getUser().getId().equalsIgnoreCase(userDTO.getId())) {
            return new OutMessage<>(Status.PERMISSION_DENIED);
        }
        UserRoleInfoView user = userRoleInfoViewService.findUserInfoById(userDTO.getId());
        if (user == null) {
            return new OutMessage<>(Status.USER_NOT_EXIST);
        }
        UserDTO userInfoDto = new UserDTO();
        BeanUtils.copyProperties(user, userInfoDto);
        userInfoDto.setManages(user.roleInfo());
        //不允许解锁超级管理员
        for (UserDTO.UserRolePermissionInfo info : userInfoDto.getManages()) {
            Integer roleType = info.getRoleType();
            if (roleType != null && roleType.equals(RoleConstant.ROLE_TYPE_SUPER_ADMIN)) {
                return new OutMessage<>(Status.PERMISSION_DENIED);
            }
        }

        //判断被修改的用户是否是管理范围下的组织
        if (!user.getOrgCode().startsWith(userTicket.getUserRolePermission().getOrgCode())) {
            return new OutMessage<>(Status.PERMISSION_DENIED);
        }

        User data = new User();
        data.setId(userDTO.getId());
        data.setIsLock(com.zenith.front.common.constant.UserConstant.UNLOCK);
        data.setUpdateTime(new Date());
        data.setUpdateAccount(userTicket.getUser().getAccount());
        boolean isOk = updateById(data);
        if (isOk) {
            CacheUtils.removeUserLoginFailedDTO(CacheConstant.CACHE_USER_LOGIN_FAILED_COUNT_PREFIX + user.getAccount());
        }
        return isOk ? new OutMessage<>(Status.SUCCESS) : new OutMessage<>(Status.FAIL);
    }

    @Override
    public OutMessage<String> changePassword(UserDTO userDTO) {
        UserTicket userVO = UserConstant.USER_CONTEXT.get();
        if (userVO == null) {
            return new OutMessage<>(Status.NOT_LOGIN);
        }
        User user = getById(userDTO.getId());
        if (user == null) {
            return new OutMessage<>(Status.USER_NOT_EXIST);
        }
        //判断被修改的用户是否是管理范围下的组织
        if (!user.getOrgCode().startsWith(userVO.getUserRolePermission().getOrgCode())) {
            return new OutMessage<>(Status.PERMISSION_DENIED);
        }
        //修改了用户密码需要让用户进行重新登录
        //String userToken = CacheUtils.getUserToken(user.getId());
        //if (userToken != null) {
        //    CacheUtils.removeUserCache(userToken);
        //}

        //如果不是修改自己的密码,需要校验当前用户是否拥有权限修改密码
        if (!userVO.getUser().getId().equalsIgnoreCase(userDTO.getId())) {
            //判断被修改的用户是否是管理范围下的组织
            if (!user.getOrgCode().startsWith(userVO.getUserRolePermission().getOrgCode())) {
                return new OutMessage<>(Status.PERMISSION_DENIED);
            }
            //修改了用户密码需要让用户进行重新登录
            String token = CacheUtils.getUserToken(user.getId());
            if (token != null) {
                CacheUtils.removeUserCache(token);
            }
        } else {
            //如果是修改自己的密码,则需要校验旧密码 是否正确
            if(!StrUtil.equals(userDTO.getIsLoginUpdatePassword(),"1")){
                String oldPassword = userDTO.getOldPassword();
                if (StrKit.isBlank(oldPassword)) {
                    return new OutMessage<>(Status.PASSWORD_PASSWORD_NOT_EQ);
                }
                String md5Hex = DigestUtil.md5Hex(oldPassword);
                if (!user.getPassword().equalsIgnoreCase(md5Hex)) {
                    // todo 如果MD5方式解密未通过则调用加密机验证次
                    if(!user.getPassword().equals(SM4Untils.encryptContent(oldPassword))) {
                        return new OutMessage<>(Status.USER_OLDPASSWORD_ISNOTTRUE);
                    }
                }
            }
            //移除用户缓存
            CacheUtils.removeUserCache(userVO.getToken());
        }
        user = new User();
        user.setId(userDTO.getId());
//        user.setPassword(DigestUtil.md5Hex(userDTO.getPassword()));
        // todo 用户密码调整为加密机处理
        user.setPassword(SM4Untils.encryptContent(userDTO.getPassword()));
        user.setUpdateTime(new Date());
        user.setUpdateAccount(userVO.getUser().getAccount());
        user.setLastUpdatePasswordTime(new Date());
        boolean isOk = updateById(user);
        return isOk ? new OutMessage<>(Status.SUCCESS) : new OutMessage<>(Status.FAIL);
    }

    @Override
    public OutMessage<String> exist(String account) {
        //为空或者存在空格时
        if (StrUtil.isBlank(account) || account.contains(" ")) {
            return new OutMessage<>(Status.USERNAME_ISEMPTY);
        }
        if (exchange) {
            //临时解决账号验证是否通过问题
            //String res = HttpKit.doGet(exchange_user_link+"?account="+account);
            JSONObject postJson = new JSONObject();
            postJson.put("account", account.trim());
            String replaceUrl = sync_flow_push.replace("/api/flow/push", "");
            String res = HttpKit.doPost(replaceUrl + "/login/exist", postJson, "UTF-8");
            System.out.println("账号验证反馈===>" + res);
            JSONObject jsonObject = JSONObject.parseObject(res);
            Integer code = jsonObject.getInteger("code");
            return code == 0 ? new OutMessage<>(Status.SUCCESS) : new OutMessage<>(Status.USER_HAVE_LIVE);
        }
        User user = findByAccount(account);
        return user == null ? new OutMessage<>(Status.SUCCESS) : new OutMessage<>(Status.USER_HAVE_LIVE);
    }

    @Override
    public OutMessage<Page<UserDTO>> searchUserByKeyword(Integer pageNum, Integer pageSize, String keyword, String orgCode) {
        UserTicket userTicket = UserConstant.USER_CONTEXT.get();
        if (userTicket == null) {
            return new OutMessage<>(Status.NOT_LOGIN);
        }
        //当前用户所管理的组织
        String currentOrgCode = userTicket.getUserRolePermission().getOrgCode();
        //如果超过查询组织范围则拒绝访问
        if (!orgCode.startsWith(currentOrgCode)) {
            return new OutMessage<>(Status.PERMISSION_DENIED);
        }

        Page<UserRoleInfoView> page = userRoleInfoViewService.findUserByKeyword(pageNum, pageSize, keyword, orgCode, userTicket.getUser().getId());
        Page<UserDTO> dtoPage = userPageToUserDtoPage(page);
        return new OutMessage<>(Status.SUCCESS, dtoPage);
    }

    @Override
    public boolean updateUserOrgCode(String newOrgCode) {
        return userMapper.updateUserOrgCode(newOrgCode) > 0;
    }

    @Override
    public OutMessage checkIdCard(String idCard, String name) {
        JSONObject jsonData = new JSONObject();
        jsonData.put("name", name.replace(" ", ""));
        jsonData.put("idCard", idCard.replace(" ", ""));
        String data = jsonData.toString();

        String verifyDigest = data + accessKeyId + accessKeyScrect;
        JSONObject param = new JSONObject();
        param.put("data", data);
        param.put("accessKeyId", accessKeyId);

        byte[] bytes = verifyDigest.getBytes();
        Digest md = new SM3Digest();
        md.update(bytes, 0, bytes.length);
        byte[] digest = new byte[md.getDigestSize()];
        md.doFinal(digest, 0);

        param.put("signature", digest);
        String res = HttpKit.doPost(server_url, param, "UTF-8");
        JSONObject jsonObject = JSONObject.parseObject(res);
        return new OutMessage<>(Status.SUCCESS, jsonObject);

    }

    @Override
    public OutMessage<Object> resetPassword(String account) {
        //根据账户查询账号
        User byAccount = findByAccount(account);
        byAccount.setPassword("c3d77590397dac609b28016215eb33a2");
        boolean b = updateById(byAccount);
        return new OutMessage<>(b ? Status.SUCCESS : Status.FAIL);
    }

    @Override
    public OutMessage<Page<UserDTO>> findValidUser(PageDTO data) {
        String code = data.getCode();
        Integer pageNum = data.getPageNumber();
        Integer pageSize = data.getPageSize();
        Page<User> userPage = new Page<>(pageNum, pageSize);
        LambdaQueryWrapper<User> userLambdaQueryWrapper = new LambdaQueryWrapper<User>()
                .likeRight(User::getOrgCode, code)
                .eq(User::getIsDelete, com.zenith.front.common.constant.UserConstant.NOT_IS_DELETE)
                .like(StrUtil.isNotEmpty(data.getAccount()), User::getAccount, data.getAccount())
                .select(User::getAccount, User::getManagementSystem, User::getLastLoginTime, User::getIsLock, User::getId,User::getUkey)
                .orderByDesc(User::getCreateTime);
        page(userPage, userLambdaQueryWrapper);
        List<User> records = userPage.getRecords();
        List<String> userIdList = records.stream().map(User::getId).collect(Collectors.toList());
        List<UserRoleInfoView> infoViewList = userRoleInfoViewService.findUserInfoByIdList(userIdList);
        Map<String, List<UserDTO.UserRolePermissionInfo>> map = new LinkedHashMap<>();
        for (UserRoleInfoView userRoleInfoView : infoViewList) {
            String userId = userRoleInfoView.getId();
            List<UserDTO.UserRolePermissionInfo> roleInfo = userRoleInfoView.roleInfo();
            roleInfo.forEach(var -> var.setManagerOrgName(CacheUtils.getOrgName(var.getManagerOrgId())));
            if (!map.containsKey(userId)) {
                List<UserDTO.UserRolePermissionInfo> list = new ArrayList<>(roleInfo);
                map.put(userId, list);
            } else {
                List<UserDTO.UserRolePermissionInfo> list = map.get(userId);
                list.addAll(roleInfo);
            }
        }
        List<UserDTO> userDTOList = records.stream().map(user -> {
            UserDTO userDTO = new UserDTO();
            BeanUtils.copyProperties(user, userDTO);
            userDTO.setPassword(null);
            userDTO.setIp(CacheUtils.getUserLoginIp(user.getAccount()));
            userDTO.setLoginCount(CacheUtils.getUserLoginCount(user.getAccount()));
            userDTO.setManages(map.get(user.getId()));
            return userDTO;
        }).collect(Collectors.toList());
        Page<UserDTO> dtoPage = new Page<>();
        dtoPage.setCurrent(userPage.getCurrent());
        dtoPage.setSize(userPage.getSize());
        dtoPage.setTotal(userPage.getTotal());
        dtoPage.setRecords(userDTOList);
        return new OutMessage<>(Status.SUCCESS, dtoPage);
    }

    @Override
    public OutMessage<Page<UserDTO>> updateManagementSystem(String id, String managementSystem, List<UserDTO.UserRolePermissionInfo> manages, String account) {
        if (CollUtil.isEmpty(manages)) {
            return new OutMessage<>(Status.PERMISSION_DENIED);
        }
        User dbUser = getById(id);
        if (Objects.isNull(dbUser)) {
            return new OutMessage<>(Status.USER_NOT_EXIST);
        }
        UserDTO.UserRolePermissionInfo userRolePermissionInfo = manages.get(0);
        String managerOrgId = userRolePermissionInfo.getManagerOrgId();
        String requestOrgCode = userRolePermissionInfo.getManagerOrgCode();
        String roleId = userRolePermissionInfo.getRoleId();
        Integer isDefault = userRolePermissionInfo.getIsDefault();
        Role role = roleService.findById(roleId);
        if (role == null) {
            return new OutMessage<>(Status.ROLE_NOT_EXIST);
        }
        User user = new User();
        user.setId(dbUser.getId());
        user.setManagementSystem(managementSystem);
        user.setOrgCode(requestOrgCode);
        user.setOrgId(managerOrgId);
        String userRolePermissionId = IdUtil.simpleUUID();
        if (isDefault == 1) {
            user.setCurrentUserRoleid(userRolePermissionId);
        }
        boolean flag = updateById(user);
        if (flag) {
            //如果为单个修改 需要情况用户之前的所有角色
            UserRolePermission userRolePermission = new UserRolePermission();
            userRolePermission.setId(userRolePermissionId);
            userRolePermission.setUserId(user.getId());
            userRolePermission.setRoleId(roleId);
            userRolePermission.setOrgCode(requestOrgCode);
            userRolePermission.setOrgId(managerOrgId);
            userRolePermission.setPermission(role.getPermission());
            userRolePermission.setCreateAccount(account);
            userRolePermission.setUpdateAccount(account);
            userRolePermissionService.cleanAll(user.getId());
            userRolePermissionService.save(userRolePermission);
        }
        return new OutMessage<>(flag ? Status.SUCCESS : Status.FAIL);
    }

    @Override
    public List<UserPO> findUserByManagementSystem(String managementSystem) {
        if (!StringUtils.hasText(managementSystem)) {
            return Collections.emptyList();
        }
        return userMapper.findUserByManagementSystem(managementSystem);
    }

    @Override
    public OutMessage<Object> verifyAccount(String account) {
        if (!StringUtils.hasText(account)) {
            return new OutMessage<>(Status.USER_NOT_EXIST);
        }
        User user = findByAccount(account);
        if (Objects.isNull(user)) {
            return new OutMessage<>(Status.USER_NOT_EXIST);
        }
        String managementSystem = user.getManagementSystem();
        if (!StringUtils.commaDelimitedListToSet(managementSystem).contains(CommonConstant.TWO)) {
            return new OutMessage<>(Status.NO_ACCESS_SYSTEM);
        }
        Map<String, String> map = new LinkedHashMap<>(10);
        map.put("id", user.getId());
        map.put("account", user.getAccount());
        map.put("password", user.getPassword());
        return new OutMessage<>(Status.SUCCESS, map);
    }

    @Override
    public List<User> findValidUserListByMemCodeIsNotNull() {
        LambdaQueryWrapper<User> queryWrapper = new LambdaQueryWrapper<User>()
                .eq(User::getIsDelete, com.zenith.front.common.constant.UserConstant.NOT_IS_DELETE)
                .isNotNull(User::getMemCode)
                .select(User::getMemCode, User::getId)
                .orderByDesc(User::getCreateTime);
        return list(queryWrapper);
    }

    @Override
    public List<User> findAccountByOrgs(List<String> orgCode) {
        LambdaQueryWrapper<User> queryWrapper = new LambdaQueryWrapper<User>()
                .eq(User::getIsDelete, com.zenith.front.common.constant.UserConstant.NOT_IS_DELETE)
                .in(User::getOrgId, orgCode)
                .select(User::getAccount)
                .orderByDesc(User::getCreateTime);
        return list(queryWrapper);
    }

    /**
     * 删除用户
     *
     * @param account
     * @return
     */
    @Override
    public OutMessage<Object> deleteUser(String account) {
        User byAccount = findByAccount(account);
        if (Objects.isNull(byAccount)) {
            return new OutMessage<>(Status.FAIL);
        }
        byAccount.setIsDelete(1);
        boolean b = updateById(byAccount);
        return new OutMessage<>(b ? Status.SUCCESS : Status.FAIL);
    }

    @Override
    public OutMessage<Boolean> getUserIsTop(String userId) {
        try {
            User user = this.getById(userId);
            OrgAll org = orgAllMapper.getTopOrg();
            if (StrUtil.equals(user.getOrgCode(), org.getOrgCode())) {
                return new OutMessage<>(Status.SUCCESS, true);
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return new OutMessage<>(Status.SUCCESS, false);
    }
    /**
     * 绑定用户ukey
     *
     * @param id   用户id
     * @param ukey 序列号
     * @return
     */
    @Override
    public OutMessage<Object> bindUkey(String id, String ukey) {
        if (exchange) {
            JSONObject postJson = new JSONObject();
            postJson.put("ukey", ukey.trim());
            String replaceUrl = sync_flow_push.replace("/api/flow/push", "");
            String url = replaceUrl + "/login/findByUkey";
            String res = HttpKit.doPost(url, postJson, "UTF-8");
            JSONObject jsonObject = JSONObject.parseObject(res);
            if(jsonObject.getInteger("code") == 0 ){
                JSONObject data = jsonObject.getJSONObject("data");
                //data为空  则该序列号没有绑定用户
                if (Objects.isNull(data) || data.getString("userid").equals(id)) {
                    User user = this.getById(id);
                    //修改all_user库的用户ukey
                    String bindUrl = replaceUrl + "/login/bindUkey";
                    JSONObject postObj = new JSONObject();
                    postObj.put("account", user.getAccount());
                    postObj.put("ukey", ukey.trim());
                    String result = HttpKit.doPost(bindUrl, postObj, "UTF-8");
                    JSONObject json = JSONObject.parseObject(result);
                    if (json.getInteger("code") == 0) {
                        //修改业务库用户ukey
                        user.setUkey(ukey);
                        this.saveOrUpdate(user);
                        return new OutMessage<>(Status.SUCCESS);
                    } else {
                        return new OutMessage<>(Status.USER_IS_NOT_EXIST);
                    }
                }else {
                    return new OutMessage<>(Status.UKEY_HAS_BIND_USER).format(data.getString("account"));
                }
            }
        }
        return new OutMessage<>(Status.FAIL);
    }

    /**
     * 通过序列号查询用户
     * @param ukey 序列号
     * @return
     */
    @Override
    public OutMessage<Object> findByUkey(String ukey) {
        if (exchange) {
            JSONObject postJson = new JSONObject();
            postJson.put("ukey", ukey.trim());
            String replaceUrl = sync_flow_push.replace("/api/flow/push", "");
            String url = replaceUrl + "/login/findByUkey";
            String res = HttpKit.doPost(url, postJson, "UTF-8");
            System.out.println("用户账号===>" + res);
            JSONObject jsonObject = JSONObject.parseObject(res);
            if(jsonObject.getInteger("code") == 0 && jsonObject.containsKey("data")){
                JSONObject data = jsonObject.getJSONObject("data");
                return new OutMessage<>(Status.SUCCESS,data);
            }
        }
        User user = findUserByUkey(ukey);
        return new OutMessage<>(Status.SUCCESS,user);
    }

    private User findUserByUkey(String ukey) {
        LambdaQueryWrapper<User> queryWrapper = new LambdaQueryWrapper<User>()
                .eq(User::getUkey, ukey)
                .eq(User::getIsDelete, com.zenith.front.common.constant.UserConstant.NOT_IS_DELETE);
        return userMapper.selectOne(queryWrapper);
    }
}
