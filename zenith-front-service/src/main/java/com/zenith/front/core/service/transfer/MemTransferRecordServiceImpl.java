package com.zenith.front.core.service.transfer;

import cn.afterturn.easypoi.word.WordExportUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.io.FileUtil;
import cn.hutool.core.util.IdcardUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.zenith.front.api.org.IOrgDevelopRightsService;
import com.zenith.front.api.org.IOrgService;
import com.zenith.front.api.report.IStatisticsYearService;
import com.zenith.front.api.transfer.IMemTransferRecordService;
import com.zenith.front.api.transfer.ITransferApprovalService;
import com.zenith.front.common.constant.CommonConstant;
import com.zenith.front.common.constant.RetConstant;
import com.zenith.front.common.constant.TransferLogConstant;
import com.zenith.front.common.constant.TransferRecordConstant;
import com.zenith.front.common.encrypt.properties.EncryptProperties;
import com.zenith.front.common.kit.HttpKit;
import com.zenith.front.common.kit.Ret;
import com.zenith.front.common.kit.StrKit;
import com.zenith.front.common.untils.SM4Untils;
import com.zenith.front.core.service.activist.ActivistMemTransferRecordServiceImpl;
import com.zenith.front.dao.mapper.transfer.TransferRecordMapper;
import com.zenith.front.model.bean.*;
import com.zenith.front.model.constant.UserConstant;
import com.zenith.front.model.custom.UserTicket;
import com.zenith.front.model.dto.MemDTO;
import com.zenith.front.model.dto.TransferRecordDTO;
import com.zenith.front.model.message.OutMessage;
import com.zenith.front.model.message.Status;
import com.zenith.front.model.vo.BetweenReportDateVo;
import com.zenith.front.model.vo.TransferIntroduceLetterVo;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.xwpf.usermodel.IBodyElement;
import org.apache.poi.xwpf.usermodel.XWPFDocument;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.util.ResourceUtils;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.io.File;
import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <p>
 * 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-07-10
 */
@Slf4j
@Service("memTransferRecordService")
public class MemTransferRecordServiceImpl extends TransferRecordServiceImpl implements IMemTransferRecordService {

    @Value("${sync_flow_push}")
    private String sync_flow_push;
    @Resource
    private IOrgService iOrgService;
    @Resource
    private IOrgDevelopRightsService iOrgDevelopRightsService;
    @Resource
    private ITransferApprovalService iTransferApprovalService;
    @Resource
    private ActivistMemTransferRecordServiceImpl activistMemTransferRecordService;
    @Resource
    private IStatisticsYearService iStatisticsYearService;
    @Value("${baseOrgOrgCode}")
    private String baseOrgOrgCode;
    @Resource
    private TransferRecordMapper recordMapper;

    /**
     * @param dtoList 转接参数
     * @return 是否成功
     * @update 20210927 由于业务需求现在 组织关系转接的 审核机构只包括一层上级 不在多层 <AUTHOR>
     */
    @Override
    public OutMessage<String> add(ArrayList<TransferRecordDTO> dtoList) {
        UserTicket userTicket = UserConstant.USER_CONTEXT.get();
        if (userTicket == null) {
            return new OutMessage<>(Status.NOT_LOGIN);

        }
        TransferRecord saveTransferRecord = new TransferRecord();
        String transFerId = StrKit.getRandomUUID();
        saveTransferRecord.setId(transFerId);
        if (dtoList == null || dtoList.isEmpty()) {
            return new OutMessage<>(Status.SUCCESS);
        }
        TransferRecordDTO dto = dtoList.get(0);
        saveTransferRecord.setType(dto.getType());
        //校验参数
        OutMessage<TransferRecord> checkMessage = checkParams(dto, saveTransferRecord);
        OutMessage<String> re = returnErrorMessage(checkMessage);
        if (re != null) {
            return re;
        }
        //生成模型列表
        List<TransferRecord> transferRecordList = new ArrayList<>(dtoList.size());
        boolean isOk = addTransferRecordToList(dtoList, transferRecordList, saveTransferRecord);
        if (!isOk) {
            return new OutMessage<>(Status.FAIL);
        }
        // TODO: 2021/7/16 事务
        boolean flag = false;
        for (TransferRecord transferRecord : transferRecordList) {
            flag = rewriteSave(transferRecord);
            //创建审批记录
            TransferApproval approval = createOrgTransferApproval(transferRecord);
            approval.setRecordId(transferRecord.getId());
            flag &= transferApprovalService.rewriteSave(approval);
            //创建审批日志
            TransferLog transferLog = transferLogService.createOrgTransferLog();
            transferLog.setHandleApprovalId(approval.getId());
            transferLog.setReason(TransferLogConstant.PUSH_MEM_TRANSFER_REASON);
            flag &= transferLogService.rewriteSave(transferLog);
            //更新转接记录中的审批记录id
            TransferRecord updateRecord = new TransferRecord();
            updateRecord.setId(transferRecord.getId());
            updateRecord.setCurrentApprovalId(approval.getId());
            flag &= updateById(updateRecord);
            //处理党员系统的是否在转接中的标示
            this.updateMemIsTransfer(transferRecord.getId(), dtoList.stream().map(TransferRecordDTO::getMemId).collect(Collectors.toList()));
        }
//        // TODO: 2021/12/6  发起关系转接的时候，同步人员all表数据
//        dtoList.forEach(transferRecordDTO -> {
//            String memId = transferRecordDTO.getMemId();
//            System.out.println("同步人员数据====》"+memId);
//            CompletableFuture.supplyAsync(() -> iSyncMemService.syncMem(memId, CommonConstant.ONE), mySimpleAsync);
//        });
        //todo 当如果是中间节点， 且中间节点不在当前数据库的时候， 那么这个数据就需要走中间交换区
        String targetOrgId = dto.getTargetOrgId();
        Org orgByCode = orgService.findOrgByCode(targetOrgId);
        if (flag && ObjectUtil.isNull(orgByCode) && StrKit.notBlank(targetOrgId)) {
            transferRecordList.forEach(transferRecord -> {
                //查询转接记录
                TransferRecord recordNow = getById(transferRecord.getId());
                //处理携带的人员信息
                String memId = recordNow.getMemId();
                Mem allByCode = memService.findAllByCode(memId);
                if (ObjectUtil.isNotNull(allByCode)) {
                    //recordNow.setExtraData(allByCode);
                    JSONObject extraDataJsonObject = JSONObject.parseObject(JSONObject.toJSONString(allByCode));
                    recordNow.setExtraData(extraDataJsonObject);
                    // todo 2025-03-20: 个人党员转出添加档案信息
                    if(StrUtil.isNotBlank(allByCode.getDigitalLotNo())) {
                        this.digLotNoData(allByCode.getDigitalLotNo(), extraDataJsonObject);
                    }

                }
                JSONObject jsonObject = JSONObject.parseObject(JSONObject.toJSONString(recordNow));
                JSONArray jsonArray = new JSONArray();
                //查询转接节点审批记录
                List<TransferApproval> byRecordId = transferApprovalService.findByRecordId(recordNow.getId());
                byRecordId.forEach(TransferApproval -> jsonArray.add(JSONObject.parseObject(JSONObject.toJSONString(TransferApproval))));
                jsonObject.put("approval", jsonArray);
                String replaceUrl = sync_flow_push.replace("/api/flow/push", "");
                HttpKit.doPost(replaceUrl + "/transfer/insertData", jsonObject, "UTF-8");
            });
        }
        return flag ? new OutMessage<>(Status.SUCCESS) : new OutMessage<>(Status.FAIL);
    }


    /***
     * 参数校验
     * */
    private OutMessage<TransferRecord> checkParams(TransferRecordDTO dto, TransferRecord saveTransferRecord) {
        BetweenReportDateVo betweenReportDate = iStatisticsYearService.isBeforeReportDate(dto.getTransferOutTime(), "2");
        if (!betweenReportDate.getIsBetween()) {
            return new OutMessage<TransferRecord>(Status.NOT_IN_BETWEEN_DEFAULT_DATE).format("转出日期", betweenReportDate.getMessage());
        }
        String srcOrgId = dto.getSrcOrgId();
        String targetOrgId = dto.getTargetOrgId();
        //获取转接类型
        String type = dto.getType();
        //判断转接类型
        boolean isOk = setTransferTypeCode(type, saveTransferRecord);
        if (!isOk) {
            return new OutMessage<>(Status.ORG_TRANSFER_TYPE_ERROR);
        }
        //设置源组织和目标组织当前组织关系
        Ret ret = setSrcAndTargetRelation(srcOrgId, targetOrgId, saveTransferRecord, false);
        if (ret.isFail()) {
            return ret.getAs(RetConstant.MESSAGE);
        }
        //设置源组织和目标组织id
        ret = setSrcAndTargetOrgId(srcOrgId, targetOrgId, saveTransferRecord, false);
        if (ret.isFail()) {
            return ret.getAs(RetConstant.MESSAGE);
        }
        //设置公共节点
        ret = setCommonNodeId(srcOrgId, targetOrgId, saveTransferRecord);
        if (ret.isFail()) {
            return ret.getAs(RetConstant.MESSAGE);
        }
        //判断目标组织或组织上级是否在转接中
        if (StrKit.notBlank(targetOrgId)) {
            List<TransferRecord> records = targetExistParentsOrgIsTransfer(targetOrgId);
            for (TransferRecord record : records) {
                return new OutMessage<TransferRecord>(Status.TRANSFER_TARGET_ORG_ERROR_MESSAGE).format(record.getName());
            }
        }
        //判断源组织或源组织上级以及源组织下级及其下级人员是否处于转接中
        if (StrKit.notBlank(srcOrgId)) {
            // TODO: 2021/11/14 这里可能又一个BUG，就是源组织发起了一个转接，因为这里判断，所以无法发起第二个人转接， 能否转应该是用于当整建制转接组织得时候，才判断源组织是否有人在转接中
            List<TransferRecord> records = srcExistParentsOrgIsTransfer(srcOrgId);
            for (TransferRecord record : records) {
                String memId = record.getMemId();
                if (StrKit.isBlank(memId)) {
                    return new OutMessage<TransferRecord>(Status.TRANSFER_SRC_ORG_ERROR_MESSAGE).format(record.getName());
                } /*else {
                    return new OutMessage<TransferRecord>(Status.TRANSFER_SRC_MEM_ERROR_MESSAGE).format(record.getName());
                }*/
            }
        }
        return new OutMessage<>(Status.SUCCESS, saveTransferRecord);
    }

    private OutMessage<String> returnErrorMessage(OutMessage<TransferRecord> checkMessage) {
        if (!Status.SUCCESS.getCode().equals(checkMessage.getCode())) {
            OutMessage<String> re = new OutMessage<>();
            re.setMessage(checkMessage.getMessage());
            re.setCode(checkMessage.getCode());
            return re;
        }
        return null;
    }

    /***
     * 生成转接记录模型到列表
     * @return true 参数校验通过  false 参数校验失败
     * */
    private boolean addTransferRecordToList(ArrayList<TransferRecordDTO> dtoList, List<TransferRecord> transferRecordList, TransferRecord temp) {
        UserTicket userTicket = UserConstant.USER_CONTEXT.get();
        for (TransferRecordDTO data : dtoList) {
            String srcOrgId = temp.getSrcOrgId();
            TransferRecord transferRecord = new TransferRecord();
            String randomUUID = StrKit.getRandomUUID();
            transferRecord.setId(randomUUID);
            //判断用户是否存在
            Mem mem = memService.findByCode(data.getMemId());
            if (mem == null) {
                return false;
            }
            //判断是否有权限调整
            String memOrgCode = mem.getMemOrgCode();
            String currentOrgCode = userTicket.getUserRolePermission().getOrgCode();
            if (StrKit.isBlank(memOrgCode) || StrKit.isBlank(currentOrgCode) || !memOrgCode.startsWith(currentOrgCode)) {
                return false;
            }

            // TODO: 2022/4/21 查询发起党支部得单位信息
            OrgAll byCode = orgAllService.findByCode(srcOrgId);
            transferRecord.setOutD04Code(byCode.getD04Code());
            transferRecord.setMemId(data.getMemId());
            transferRecord.setName(mem.getName());
            transferRecord.setMemFeeEndTime(data.getMemFeeEndTime());
            transferRecord.setMemFeeStandard(data.getMemFeeStandard());
            transferRecord.setSrcOrgId(srcOrgId);
            transferRecord.setTargetOrgId(temp.getTargetOrgId());
            transferRecord.setCommonOrgId(temp.getCommonOrgId());
            transferRecord.setCommonOrgName(temp.getCommonOrgName());
            transferRecord.setSrcOrgName(data.getSrcOrgName());
            transferRecord.setTargetOrgName(data.getTargetOrgName());
            transferRecord.setSrcOrgRelationAsList((List<String>) (temp.getSrcOrgRelation()));
            transferRecord.setTargetOrgRelationAsList((List<String>) (temp.getTargetOrgRelation()));
            transferRecord.setSrcOrgRelationRelAsList((List<String>) (temp.getSrcOrgRelationRel()));
            transferRecord.setTargetOrgRelationRelAsList((List<String>) (temp.getTargetOrgRelationRel()));
            String type = temp.getType();
            // TODO: 2021/12/18 增加省外转出的转出时间
            if (type.equals("223") || type.equals("224") || true) {
                transferRecord.setTransferOutTime(data.getTransferOutTime());
            }
            transferRecord.setType(type);
            transferRecord.setInType(temp.getInType());
            transferRecord.setOutType(temp.getOutType());
            transferRecord.setReason(data.getReason());
            transferRecord.setStatus(TransferRecordConstant.TRANSFERING);
            transferRecord.setCreateTime(new Date());
            transferRecord.setUpdateTime(new Date());
            transferRecord.setUserId(userTicket.getUser().getId());
            transferRecord.setWhetherExtendPrepPeriod(data.getWhetherExtendPrepPeriod());
            // TODO: 2022/4/20 增加转出原因
            transferRecord.setReportTime(data.getReportTime());
            transferRecord.setD146Code(data.getD146Code());
            transferRecord.setD146Name(data.getD146Name());
            transferRecord.setLetterValidity(data.getLetterValidity());
            // TODO: 2022/7/4 增加转出目的党支部
            transferRecord.setTargetPartyBranch(data.getTargetPartyBranch());
            transferRecordList.add(transferRecord);
        }
        return true;
    }

    @Override
    public OutMessage<String> memTransferInFromSysOut(TransferRecordDTO data) {
        UserTicket userTicket = UserConstant.USER_CONTEXT.get();
        if (userTicket == null) {
            return new OutMessage<>(Status.NOT_LOGIN);
        }
        TransferRecord transferRecord = new TransferRecord();
        transferRecord.setId(StrKit.getRandomUUID());
        data.setSrcOrgId("");

        transferRecord.setSrcOrgName(data.getSrcOrgName());
        transferRecord.setTargetOrgId(data.getTargetOrgId());
        transferRecord.setTargetOrgName(data.getTargetOrgName());
        transferRecord.setType(data.getType());
        transferRecord.setName(data.getMemInfoExtraData().getMemName());
        transferRecord.setMemId("系统外转入");
        transferRecord.setMemFeeStandard(data.getMemFeeStandard());
        transferRecord.setMemFeeEndTime(data.getMemFeeEndTime());
        TransferRecord.MemInfoExtraData memInfoExtraData = data.getMemInfoExtraData();
        //校验身份证号码
        if (!IdcardUtil.isValidCard(memInfoExtraData.getIdCard())) {
            return new OutMessage<>(Status.TRANSFER_MEM_SYS_OUT_IN_ID_CARD_ERROR);
        }
        //设置人员性别
        memInfoExtraData.setSex(String.valueOf(IdcardUtil.getGenderByIdCard(memInfoExtraData.getIdCard())));
        List<TransferRecord.MemInfoExtraData> innerList = new ArrayList<>();
        innerList.add(data.getMemInfoExtraData());
        transferRecord.setMemInfoExtraData(innerList);
        transferRecord.setReason(data.getReason());

        //校验参数
        OutMessage<TransferRecord> checkMessage = checkParams(data, transferRecord);
        OutMessage<String> re = returnErrorMessage(checkMessage);
        if (re != null) {
            return re;
        }

        String manageOrgCode = userTicket.getUserRolePermission().getOrgCode();
        String targetOrgId = data.getTargetOrgId();

        Org targetOrg = orgService.findOrgByCode(targetOrgId);
        if (targetOrg == null) {
            return new OutMessage<>(Status.PARA_ERROR);
        }

        String orgCode = targetOrg.getOrgCode();
        if (StrKit.isBlank(orgCode) || !(orgCode.startsWith(manageOrgCode))) {
            return new OutMessage<>(Status.PERMISSION_DENIED);
        }
        transferRecord.setUserId(userTicket.getUser().getId());
        transferRecord.setCreateTime(new Date());
        transferRecord.setUpdateTime(new Date());
        transferRecord.setLetterUrl(data.getLetterUrl());
        transferRecord.setStatus(TransferRecordConstant.TRANSFERING);

        // TODO: 2021/7/16 事务
        boolean flag = rewriteSave(transferRecord);

        //生成审批记录
        TransferApproval approval = createOrgTransferApproval(transferRecord);
        approval.setStatus(TransferRecordConstant.TRANSFER_SUCCESS);
        approval.setRecordId(transferRecord.getId());
        flag &= transferApprovalService.rewriteSave(approval);
        //创建审批日志
        TransferLog transferLog = transferLogService.createOrgTransferLog();
        transferLog.setHandleApprovalId(approval.getId());
        transferLog.setReason(TransferLogConstant.SUCCESS_REASON);
        flag &= transferLogService.rewriteSave(transferLog);
        //更新转接记录中的审批记录id
        TransferRecord updateRecord = new TransferRecord();
        updateRecord.setId(transferRecord.getId());
        updateRecord.setCurrentApprovalId(approval.getId());
        flag &= updateById(updateRecord);
        //处理党员系统的是否在转接中的标示
        this.updateMemIsTransfer(transferRecord.getId(), Collections.singletonList(data.getMemId()));

        return flag ? new OutMessage<>(Status.SUCCESS) : new OutMessage<>(Status.FAIL);
    }

    @Override
    public OutMessage<String> adjustMem(ArrayList<TransferRecordDTO> dtoList) {
        UserTicket userTicket = UserConstant.USER_CONTEXT.get();
        if (userTicket == null) {
            return new OutMessage<>(Status.NOT_LOGIN);
        }
        if (dtoList == null || dtoList.isEmpty()) {
            return new OutMessage<>(Status.SUCCESS);
        }
        TransferRecord temp = new TransferRecord();
        temp.setId(StrKit.getRandomUUID());
        TransferRecordDTO dto = dtoList.get(0);

        //校验参数
        OutMessage<TransferRecord> checkMessage = checkParams(dto, temp);
        if (!Status.SUCCESS.getCode().equals(checkMessage.getCode())) {
            OutMessage<String> re = new OutMessage<>();
            re.setCode(checkMessage.getCode());
            re.setMessage(checkMessage.getMessage());
            return re;
        }
        List<TransferRecord> transferRecordList = new ArrayList<>(dtoList.size());
        //生成转接记录集合
        boolean isOk = addTransferRecordToList(dtoList, transferRecordList, temp);
        if (!isOk) {
            return new OutMessage<>(Status.FAIL);
        }

        boolean flag = false;
        for (TransferRecord transferRecord : transferRecordList) {
            //设置转接状态为默认完成
            transferRecord.setUpdateTime(new Date());
            transferRecord.setStatus(TransferRecordConstant.TRANSFER_SUCCESS);
            flag = rewriteSave(transferRecord);
            //创建审批记录
            TransferApproval approval = createOrgTransferApproval(transferRecord);
            approval.setRecordId(transferRecord.getId());
            approval.setNextOrgId("");
            flag &= transferApprovalService.save(approval);
            //创建审批日志
            TransferLog transferLog = transferLogService.createOrgTransferLog();
            transferLog.setHandleApprovalId(approval.getId());
            transferLog.setReason(TransferLogConstant.PUSH_ADJUST_MEM_TRANSFER_REASON);
            flag &= transferLogService.rewriteSave(transferLog);
            //更新转接记录中的审批记录id
            TransferRecord updateRecord = new TransferRecord();
            updateRecord.setId(transferRecord.getId());
            updateRecord.setCurrentApprovalId(approval.getId());
            flag &= updateById(updateRecord);
            flag &= transferAdjust(transferRecord);
//            // TODO: 2021/12/7 修复支部间人员调整，未同步all表问题 （重复调用，所以注释掉）
//            iSyncMemService.syncMem(transferRecord.getMemId(), CommonConstant.ONE);
        }

        return flag ? new OutMessage<>(Status.SUCCESS) : new OutMessage<>(Status.FAIL);
    }

    @Override
    public OutMessage<?> exportLetter(String transferId, String isOut) {
        TransferRecord record = this.getById(transferId);
        if (Objects.isNull(record)) {
            return new OutMessage<>(Status.SELECT_OBJECT_CAN_NOT_EMPTY);
        }
        TransferIntroduceLetterVo introduceLetterVo = this.getDataLetter(record);
        if (Objects.isNull(introduceLetterVo)) {
            return new OutMessage<>(Status.MEM_IS_ERROR);
        }
        Map<String, Object> map = new HashMap<>();
        String string = JSON.toJSONString(introduceLetterVo);
        Map<String, Object> resultMap = JSON.parseObject(string, HashMap.class);
        try {
            String basePath = ResourceUtils.getURL(ResourceUtils.CLASSPATH_URL_PREFIX).getPath();
            String ftlPath = basePath + "public/wordftl/transferIntroduceLetterWord.docx";
            FileInputStream fin = new FileInputStream(ftlPath);
            XWPFDocument document = WordExportUtil.exportWord07(ftlPath, resultMap);

            // TODO：2022年5月26日 根据夏文档需求说明，按照转接状态下载对应介绍信模板
            Integer status = record.getStatus();
            if (Objects.equals(status, 0)) {
                if (StrUtil.equals(isOut, "1")) {
                    // 关系转出 不显示第三联
                    this.getWordCopy12(document);
                } else {
                    // 关系转入 只显示第二联
                    this.getWordCopy2(document);
                }
            } else {
                if (StrUtil.equals(isOut, "1")) {
                    if (!Objects.equals(status, 1)) {
                        this.getWordCopy12(document);
                    }
                } else {
                    if (Objects.equals(status, 1)) {
                        // 历史转入 不显示第一联
                        this.getWordCopy23(document);
                    } else {
                        this.getWordCopy2(document);
                    }
                }
            }

            String fileUrl = "/public/export/";
            File baseFile = new File(basePath + fileUrl);
            if (!baseFile.exists()) {
                baseFile.mkdirs();
            }
            // 生成文件
            String name = System.currentTimeMillis() + ".doc";
            FileOutputStream fout = new FileOutputStream(FileUtil.file(basePath + fileUrl, name));
            document.write(fout);
            fout.close();
            fin.close();
            map.put("url", fileUrl + name);
        } catch (Exception e) {
            log.error("导出党员组织介绍信异常！", e);
            return null;
        }
        return new OutMessage<>(Status.SUCCESS, map);
    }

    public TransferIntroduceLetterVo getDataLetter(TransferRecord record) {
        // TODO: 2022/4/15 只有当跨节点的时候，才会有这个字段的信息
        Object extraData = record.getExtraData();
        Mem mem;
        if (Objects.isNull(extraData)) {
            String memId = record.getMemId();
            mem = memService.findAllByCode(memId);
        } else {
            try {
                mem = JSON.parseObject(JSONObject.toJSONString(extraData), Mem.class);
            } catch (Exception e) {
                return null;
            }
        }
        if (ObjectUtil.isNull(mem)) {
            return null;
        }
        Org org = this.getOrgInfoByMiddle(record.getSrcOrgId());
        Org toOrg = this.getOrgInfoByMiddle(record.getTargetOrgId());
        Org srcOrgDw = new Org();
        List<String> srcOrgRelationAsList = record.getSrcOrgRelationAsList();
        if (CollUtil.isNotEmpty(srcOrgRelationAsList) && srcOrgRelationAsList.size() > 1) {
            srcOrgDw = this.getOrgInfoByMiddle(srcOrgRelationAsList.get(srcOrgRelationAsList.size() - 2));
        }
        Org targetOrgDw = this.getOrgSuperInfoByMiddle(record.getTargetOrgId());

        TransferIntroduceLetterVo letterVo = new TransferIntroduceLetterVo();
        letterVo.setCopy1Num("    ");
        letterVo.setName(StrUtil.isNotEmpty(mem.getName()) ? mem.getName() : "");
        letterVo.setD08(StrUtil.isNotEmpty(mem.getD08Code()) ? (StrUtil.equals(mem.getD08Code(), "1") ? "正式" : "预备") : " ");
        letterVo.setSrcOrg(StrUtil.isNotEmpty(record.getSrcOrgName()) ? record.getSrcOrgName() : " ");
        letterVo.setTargetOrg(StrUtil.isNotEmpty(record.getTargetOrgName()) ? record.getTargetOrgName() : " ");
        //转出时间
        Date transferOutTime = Objects.isNull(record.getTransferOutTime()) ? record.getCreateTime() : record.getTransferOutTime();
        letterVo.setCopy1Date(DateUtil.format(transferOutTime, "yyyy年MM月dd日"));
        //转出党委
        letterVo.setSrcOrgDw(Objects.nonNull(srcOrgDw) && StrUtil.isNotEmpty(srcOrgDw.getName()) ? srcOrgDw.getName() : letterVo.getSrcOrg());
        //接收方党委
        letterVo.setTargetOrgDw(Objects.nonNull(targetOrgDw) && StrUtil.isNotEmpty(targetOrgDw.getName()) ? targetOrgDw.getName() : letterVo.getTargetOrg());

        letterVo.setCopy2Num("    ");
        letterVo.setCopy2Date(DateUtil.format(record.getCreateTime(), "yyyy年MM月dd日"));
        letterVo.setSex(StrUtil.equals(mem.getSexCode(), "1") ? "男" : "女");
        letterVo.setAge(Objects.nonNull(mem.getBirthday()) ? String.valueOf(DateUtil.ageOfNow(mem.getBirthday())) : " ");
        letterVo.setD06(StrUtil.isNotEmpty(mem.getD06Name()) ? mem.getD06Name().replace("族", "") : " ");
        letterVo.setIdCard(StrUtil.isNotEmpty(mem.getIdcard()) ? mem.getIdcard() : " ");
        Date memFeeEndTime = record.getMemFeeEndTime();
        letterVo.setFeeY(Objects.nonNull(memFeeEndTime) ? StrUtil.toString(DateUtil.year(memFeeEndTime)) : " ");
        letterVo.setFeeM(Objects.nonNull(memFeeEndTime) ? StrUtil.toString(DateUtil.month(memFeeEndTime) + 1) : " ");
        letterVo.setTerm(Objects.nonNull(record.getLetterValidity()) ? record.getLetterValidity().toString() : " ");
        letterVo.setContact(StrUtil.isNotEmpty(mem.getPhone()) ? mem.getPhone() : " ");
        // TODO: 2022/7/21 第二联  党员原所在基层党委通讯地址 -> 具有审批预备党员权限的党委的地址和电话
        //递归查询本级到上级的所有组织
        letterVo.setAddress(" ");
        letterVo.setPhone(" ");
        letterVo.setFax(" ");
        letterVo.setPostal(" ");
        if (Objects.nonNull(org)) {
            List<Org> orgList = orgService.fromSonToFather(org.getCode());
            List<String> orgCodeList = orgList.stream().map(Org::getCode).collect(Collectors.toList());
            String orgCode = iOrgDevelopRightsService.findOrgDevelopRightsByOrgCode(orgCodeList);
            if (StringUtils.hasText(orgCode)) {
                Org orgByCode = orgService.findOrgByCode(orgCode);
                if (Objects.nonNull(orgByCode)) {
                    letterVo.setAddress((StrUtil.isNotEmpty(orgByCode.getPostAddress()) ? orgByCode.getPostAddress() : " "));
                    letterVo.setPhone((StrUtil.isNotEmpty(orgByCode.getContactPhone()) ? orgByCode.getContactPhone() : " "));
                    letterVo.setFax((StrUtil.isNotEmpty(orgByCode.getFaxNumber()) ? orgByCode.getFaxNumber() : " "));
                    letterVo.setPostal((StrUtil.isNotEmpty(orgByCode.getPostCode()) ? orgByCode.getPostCode() : " "));
                }
            }
        }
        letterVo.setCopy3Num("    ");
        //接收时间
        Date copy3Date = new Date();
        String handlerMan = "";
        if (Objects.equals(record.getStatus(), 1)) {
            TransferApproval approval = iTransferApprovalService.getOne(new LambdaQueryWrapper<TransferApproval>().eq(TransferApproval::getRecordId, record.getId())
                    .orderByDesc(TransferApproval::getCreateTime).last("LIMIT 1"));
            if (Objects.nonNull(approval)) {
                copy3Date = approval.getCreateTime();
                handlerMan = approval.getHandlerMan();
            }
        }
        letterVo.setCopy3Date(DateUtil.format(copy3Date, "yyyy年MM月dd日"));
        //接收方的管理员姓名
        letterVo.setHandler(StrUtil.isNotEmpty(handlerMan) ? handlerMan : " ");
        //接收方党组织的联系方式
        letterVo.setPhone2(Objects.nonNull(toOrg) ? (StrUtil.isNotEmpty(toOrg.getContactPhone()) ? toOrg.getContactPhone() : " ") : " ");
        return letterVo;
    }

    @Override
    public Org getOrgInfoByMiddle(String orgId) {
        Org org = iOrgService.findOrgByCode(orgId);
        if (Objects.isNull(org)) {
            JSONObject jsonObject = activistMemTransferRecordService.findOrgNameByMiddle(orgId);
            if (Objects.nonNull(jsonObject)) {
                return JSONObject.toJavaObject(jsonObject, Org.class);
            }
        }
        return org;
    }

    @Override
    public OutMessage<Object> addWb(MemDTO memDTO, UserTicket currUser) {
        // TODO: 2023/12/20 增加关于重复录入的校验，处理验证是否有重复录入的情况
        //去党员表校验
        String name = memDTO.getName();
        String idcard = memDTO.getIdcard();
        // 查询值加密处理
        name = SM4Untils.encryptContent(EncryptProperties.nginxKey, name);
        idcard = SM4Untils.encryptContent(EncryptProperties.nginxKey, idcard);
        Mem byNameAndIdcard = memService.findByNameAndIdcard(name, idcard);
        if (ObjectUtil.isNotEmpty(byNameAndIdcard)){
            return new OutMessage<>(Status.PERSONNEL_ALREADY_EXIST);
        }
        //去关系转接表校验
        LambdaQueryWrapper<TransferRecord> transferRecordLambdaQueryWrapper = new LambdaQueryWrapper<>();
        transferRecordLambdaQueryWrapper.eq(TransferRecord::getStatus,CommonConstant.ZERO_INT);
        transferRecordLambdaQueryWrapper.apply("  extra_data ->>'name' = '"+name+"' and extra_data ->>'idcard' = '"+idcard+"'   ");
        boolean b = list(transferRecordLambdaQueryWrapper).size() > CommonConstant.ZERO_INT;
        if (b){
            return new OutMessage<>(Status.TRANSFER_MME_IS_TRANSFER);
        }
        //转出党组织的名称
        String outlandTransferOrg = memDTO.getOutlandTransferOrg();
        //转入党组织
        String orgCode = memDTO.getOrgCode();
        //获取转入党组织的所有上级，所有上级中具有关系转接审核的组织、所有上级中具有预备党员审批权限的的组织
        List<Org> srcParentOrg = orgService.findAllParentOrg(orgCode);
        List<String> list = srcParentOrg.stream().map(Org::getCode).collect(Collectors.toList());
        List<UserRolePermission> byIndexValue = userRolePermissionService.findByIndexValue(95, CommonConstant.ONE, list);
        List<OrgDevelopRights> orgDevelopRightsByCodeList = iOrgDevelopRightsService.findOrgDevelopRightsByCodeList(list);
        if (ObjectUtil.isNull(orgDevelopRightsByCodeList) || orgDevelopRightsByCodeList.size() == CommonConstant.ZERO_INT) {
            return new OutMessage<>(Status.PERMISSION_NODE_ERR0);
        }
        String haveGXZJPermission ;
        if ((byIndexValue.size()==CommonConstant.ZERO_INT)){
            //没有权限，默认为顶层
            Org byOrgCode = orgService.findByOrgCode(baseOrgOrgCode);
            if (ObjectUtil.isNull(byOrgCode)){
                return new OutMessage<>(Status.NOT_IS_ADMINORG);
            }else {
                haveGXZJPermission=byOrgCode.getCode();
            }
        }else {
            haveGXZJPermission = byIndexValue.get(byIndexValue.size() - CommonConstant.ONE_INT).getOrgId();
        }
        String checkTwoOrgCode = orgDevelopRightsByCodeList.get(orgDevelopRightsByCodeList.size() - CommonConstant.ONE_INT).getOrgCode();
        //生成关系转接表
        TransferRecord saveTransferRecord= new TransferRecord();
        String transferId = StrKit.getRandomUUID();
        saveTransferRecord.setId(transferId);
        saveTransferRecord.setUserId(currUser.getUser().getId());
        saveTransferRecord.setName(memDTO.getName());
        //saveTransferRecord.setMemId("省外（含系统外、如军队、银行等单位）关系转入");
        saveTransferRecord.setSrcOrgId(outlandTransferOrg);
        saveTransferRecord.setSrcOrgName(outlandTransferOrg);
        Org byOrgCode = orgService.findOrgByOrgId(orgCode);
        saveTransferRecord.setTargetOrgId(orgCode);
        saveTransferRecord.setTargetOrgName(byOrgCode.getName());
        saveTransferRecord.setCommonOrgId("5048C51OE8B74ACF891A1EE5143F85A7");
        saveTransferRecord.setCommonOrgName("中共贵州省委员会");
        saveTransferRecord.setExtraData(JSONObject.parseObject(JSONObject.toJSONString(memDTO)));

        List<String> targetOrgRelation = new ArrayList<>();
        targetOrgRelation.add("5048C51OE8B74ACF891A1EE5143F85A7");
        if (haveGXZJPermission.equals(checkTwoOrgCode)){
            targetOrgRelation.add(haveGXZJPermission);
        }else {
            targetOrgRelation.add(checkTwoOrgCode);
            targetOrgRelation.add(haveGXZJPermission);
        }
        targetOrgRelation.add(orgCode);
        saveTransferRecord.setTargetOrgRelation(targetOrgRelation);
        saveTransferRecord.setTargetOrgRelationAsList(targetOrgRelation);
        saveTransferRecord.setSrcOrgRelation(new ArrayList<>());
        saveTransferRecord.setSrcOrgRelationAsList(new ArrayList<>());
        saveTransferRecord.setOutType("");
        saveTransferRecord.setInType("125");
        saveTransferRecord.setType("125");
        saveTransferRecord.setStatus(CommonConstant.ZERO_INT);
        saveTransferRecord.setCreateTime(new Date());

        //处理生成审核相关的信息
        //生成关系转接审核表
        TransferApproval firstTransferApproval= new TransferApproval();
        String currAppId = StrKit.getRandomUUID();
        saveTransferRecord.setCurrentApprovalId(currAppId);
        firstTransferApproval.setId(currAppId);
        firstTransferApproval.setRecordId(transferId);
        firstTransferApproval.setUserId(currUser.getUser().getId());
        firstTransferApproval.setOrgId("5048C51OE8B74ACF891A1EE5143F85A7");
        firstTransferApproval.setNextOrgId(checkTwoOrgCode);
        firstTransferApproval.setIsInstead(CommonConstant.ZERO_INT);
        firstTransferApproval.setStatus(CommonConstant.ONE_INT);
        firstTransferApproval.setParentId(CommonConstant.MINUS_ZERO);
        firstTransferApproval.setCreateTime(new Date());
        firstTransferApproval.setUpdateTime(new Date());
        firstTransferApproval.setDirection(CommonConstant.ONE_INT);
        //保存关系转接入库
        boolean save = save(saveTransferRecord);
        boolean save1 = transferApprovalService.save(firstTransferApproval);
        return new OutMessage<>(save&&save1?Status.SUCCESS:Status.FAIL);

    }

    /**
     * 获取上级组织信息
     */
    public Org getOrgSuperInfoByMiddle(String orgId) {
        JSONObject data = activistMemTransferRecordService.findOrgNameByMiddle(orgId);
        if (Objects.nonNull(data)) {
            JSONObject jsonObject = activistMemTransferRecordService.findOrgSuperByMiddle(data.getString("code"), data.getString("exchangeKey"));
            if (Objects.nonNull(jsonObject)) {
                return JSONObject.toJavaObject(jsonObject, Org.class);
            }
        }
        return null;
    }

    /**
     * 获取介绍信第二联
     */
    private void getWordCopy2(XWPFDocument document) {
        List<IBodyElement> bodyElements = document.getBodyElements();
        int index = 0;
        int delIndex = 0;
        int size = bodyElements.size();
        for (int i = 0; i < size; i++) {
            if (index != 5 - delIndex && index != 6 - delIndex) {
                document.removeBodyElement(index);
                delIndex++;
            } else {
                index++;
            }
        }
    }

    /**
     * 获取介绍信第一二联
     */
    private void getWordCopy12(XWPFDocument document) {
        List<IBodyElement> bodyElements = document.getBodyElements();
        int index = 0;
        int size = bodyElements.size();
        for (int i = 0; i < size; i++) {
            if (i > 6) {
                document.removeBodyElement(index);
            } else {
                index++;
            }
        }
    }

    /**
     * 获取介绍信第二三联
     */
    private void getWordCopy23(XWPFDocument document) {
        List<IBodyElement> bodyElements = document.getBodyElements();
        int index = 0;
        int size = bodyElements.size();
        for (int i = 0; i < size; i++) {
            if (i < 5) {
                document.removeBodyElement(index);
            } else {
                index++;
            }
        }
    }


    @Override
    public Integer findTransferOfflineCount(Date time,String type,Integer... status) {
        LambdaQueryWrapper<TransferRecord> transferRecordLambdaQueryWrapper = new LambdaQueryWrapper<>();
        transferRecordLambdaQueryWrapper.in(TransferRecord::getStatus,status);
        transferRecordLambdaQueryWrapper.eq(TransferRecord::getType,type);
        String dateFormat = DateUtil.format(time, "yyyy-MM");
        transferRecordLambdaQueryWrapper.last(" and to_char(update_time,'yyyy-mm')='"+dateFormat+"'");
        return recordMapper.selectCount(transferRecordLambdaQueryWrapper);
    }

    @Override
    public Integer findTransferProvinceCount(Date time,Integer status,String... type) {
        LambdaQueryWrapper<TransferRecord> transferRecordLambdaQueryWrapper = new LambdaQueryWrapper<>();
        transferRecordLambdaQueryWrapper.in(TransferRecord::getType,type);
        String dateFormat = DateUtil.format(time, "yyyy-MM");
        if (ObjectUtil.isNotNull(status)){
            transferRecordLambdaQueryWrapper.eq(TransferRecord::getStatus,status);
            transferRecordLambdaQueryWrapper.last(" and to_char(create_time,'yyyy-mm')='"+dateFormat+"'");
        }else {
            transferRecordLambdaQueryWrapper.last(" and to_char(update_time,'yyyy-mm')='"+dateFormat+"'");
        }
        return recordMapper.selectCount(transferRecordLambdaQueryWrapper);
    }

    @Override
    public Integer findTransferOfflineOutCount(Date time, String type, boolean add, Integer... status) {
        LambdaQueryWrapper<TransferRecord> transferRecordLambdaQueryWrapper = new LambdaQueryWrapper<>();
        transferRecordLambdaQueryWrapper.in(TransferRecord::getStatus,status);
        transferRecordLambdaQueryWrapper.eq(TransferRecord::getType,type);
        String dateFormat = DateUtil.format(time, "yyyy-MM");
        if (add){
            DateTime addDate = DateUtil.offsetDay(time, -90);
            transferRecordLambdaQueryWrapper.lt(TransferRecord::getCreateTime,addDate);
        }else {
            transferRecordLambdaQueryWrapper.last(" and to_char(create_time,'yyyy-mm')='"+dateFormat+"'");
        }
        String sqlSelect = transferRecordLambdaQueryWrapper.getSqlSelect();
        return recordMapper.selectCount(transferRecordLambdaQueryWrapper);
    }

    @Override
    public OutMessage findMemInfo(String transferId) {
        TransferRecord transferRecord = getById(transferId);
        Object extraData = transferRecord.getExtraData();
        String orgCode = transferRecord.getTargetOrgId();
//        return memService.selectByCode("42acc3e43fc34ac7b8645d8c0fd59003", false);
        Mem info;
        Org org = Optional.ofNullable(orgService.findOrgByCode(orgCode)).orElse(new Org());
        if(Objects.nonNull(extraData)){
            JSONObject jsonObjectPase = (JSONObject) extraData;
            info = JSONObject.toJavaObject(jsonObjectPase, Mem.class);
            if(StrUtil.isNotBlank(transferRecord.getTargetOrgId())) {
                info.setOrgCode(transferRecord.getTargetOrgId());
                info.setOrgName(transferRecord.getTargetOrgName());
                info.setHasUnitStatistics(null);
                info.setHasUnitProvince(null);
                info.setUnitInformation(null);
                info.setStatisticalUnit(null);
                info.setD01Code(org.getD01Code());
            }
            return new OutMessage<>(Status.SUCCESS,info);
        }else{
            OutMessage<Mem> out = memService.selectByCode(transferRecord.getMemId(), false, false);
            info = out.getData();
            if(Objects.nonNull(info)) {
                info.setOrgCode(transferRecord.getTargetOrgId());
                info.setOrgName(transferRecord.getTargetOrgName());
                info.setHasUnitStatistics(null);
                info.setHasUnitProvince(null);
                info.setUnitInformation(null);
                info.setStatisticalUnit(null);
                info.setD01Code(org.getD01Code());
            }
            return out;
        }
    }

    @Override
    public void savextraData(MemDTO data) {
        TransferRecord transferRecord = getById(data.getTransferId());
        if (Objects.equals(data.getHasUnitStatistics(), 0)) {
            if (Objects.equals(data.getHasUnitProvince(), 1)) {
                data.setUnitInformation(data.getMiddleUnitName());
                data.setStatisticalUnit(data.getMiddleUnitCode());
            } else {
                data.setUnitInformation(data.getSelfUnitName());
            }
        }
        JSONObject jsonObject = JSONObject.parseObject(JSONObject.toJSONString(data));
        jsonObject.remove("transferId");
        transferRecord.setExtraData(jsonObject);
        this.updateById(transferRecord);
    }
}
