package com.zenith.front.core.service.org;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.jfinal.kit.StrKit;
import com.zenith.front.api.org.IOrgAssessService;
import com.zenith.front.dao.mapper.org.OrgAssessMapper;
import com.zenith.front.model.dto.OrgAssessDTO;
import com.zenith.front.model.dto.OrgAssessListDTO;
import com.zenith.front.model.message.OutMessage;
import com.zenith.front.model.message.Status;
import com.zenith.front.model.bean.OrgAssess;
import com.zenith.front.model.vo.OrgAssessVO;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Objects;

/**
 * <p>
 * 考核信息 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-08-23
 */
@Service
public class OrgAssessServiceImpl extends ServiceImpl<OrgAssessMapper, OrgAssess> implements IOrgAssessService {

    @Resource
    private OrgAssessMapper assessMapper;

    @Override
    public OutMessage<?> addAssess(OrgAssessDTO orgDTO) {
        if (StrKit.isBlank(orgDTO.getCode())) {
            OrgAssess orgAssess = new OrgAssess();
            BeanUtils.copyProperties(orgDTO, orgAssess);
            orgAssess.setCode(StrKit.getRandomUUID());
            orgAssess.setCreateTime(new Date());
            orgAssess.setInspectionTime(orgDTO.getInspectionTime());
            assessMapper.insert(orgAssess);
        } else {
            OrgAssess orgAssess = assessMapper.selectById(orgDTO.getCode());
            if (orgAssess != null) {
                BeanUtils.copyProperties(orgDTO, orgAssess);
                if (Objects.nonNull(orgDTO.getInspectionTime())) {
                    orgAssess.setInspectionTime(orgDTO.getInspectionTime());
                }
                orgAssess.setUpdateTime(new Date());
                assessMapper.updateById(orgAssess);
            }
        }
        return new OutMessage<>(Status.SUCCESS);
    }

    @Override
    public OutMessage<?> deleteAssess(String code) {
        OrgAssess orgAssess = assessMapper.selectById(code);
        orgAssess.setDeleteTime(new Date());
        int i = assessMapper.updateById(orgAssess);
        return new OutMessage<>(i > 0 ? Status.SUCCESS : Status.FAIL);
    }


    @Override
    public OutMessage<?> detailsAssess(String code) {
        OrgAssess orgAssess = assessMapper.selectById(code);
        return new OutMessage<>(Status.SUCCESS, orgAssess);
    }

    @Override
    public OutMessage<?> getListAssess(OrgAssessListDTO data) {
        Page<OrgAssess> page = new Page<>(data.getPageNum(), data.getPageSize());
        LambdaQueryWrapper<OrgAssess> query = new LambdaQueryWrapper<OrgAssess>()
                .eq(OrgAssess::getOrgCode, data.getOrgCode())
                .isNull(OrgAssess::getDeleteTime)
                .orderByDesc(OrgAssess::getCreateTime);
        page(page, query);
        List<OrgAssessVO> records = new ArrayList<>();
        page.getRecords().forEach(val -> {
            OrgAssessVO orgAssessVO = new OrgAssessVO();
            BeanUtils.copyProperties(val, orgAssessVO);
            orgAssessVO.setInspectionTime(val.getInspectionTime());
            records.add(orgAssessVO);
        });
        Page<OrgAssessVO> voPage = new Page<>();
        BeanUtils.copyProperties(page, voPage);
        voPage.setRecords(records);
        return new OutMessage<>(Status.SUCCESS, voPage);
    }
}
