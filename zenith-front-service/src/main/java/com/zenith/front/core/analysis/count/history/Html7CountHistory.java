package com.zenith.front.core.analysis.count.history;

import cn.hutool.core.util.StrUtil;
import com.b1809.analysis.entity.PeggingPara;
import com.b1809.analysis.extend.ITableCount;
import com.b1809.analysis.plugin.elasticsearch.EsKit;
import com.zenith.front.core.analysis.count.CountMethod;
import com.zenith.front.core.analysis.ext.condition.DevelopStepLogAllCondition;
import com.zenith.front.core.analysis.ext.condition.MemAllCondition;
import com.zenith.front.core.analysis.ext.condition.UnitAllCondition;
import org.jooq.Condition;
import org.jooq.Record1;
import org.jooq.SelectConditionStep;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

import static com.zenith.front.common.constant.DBConstant.DSL_CONTEXT;
import static org.jooq.impl.DSL.*;

/**
 * 第七表 学生党员情况
 *
 * <AUTHOR>
 * @date 2021/11/23
 */
@Component
public class Html7CountHistory implements ITableCount {

    @Override
    public String getReportCode() {
        return "History_7.html";
    }

    @Override
    public Map<String, Map<String, Number>> doCount(String reportCode, String orgCode, String orgLevelCode) throws Exception {
        return null;
    }

    @Override
    public Map<String, Object> doPegging(PeggingPara peggingPara) throws Exception {
        return null;
    }

    @Override
    public Map<String, Number> doReplenishCount(String reportCode, String orgCode, String orgLevelCode) throws Exception {
        return getReplenishCount(orgCode, orgLevelCode, null);
    }

    public Map<String, Number> getReplenishCount(String orgCode, String orgLevelCode, String tableYear) {
        String ccpUnitAll = StrUtil.isEmpty(tableYear) ? "ccp_unit_all" : "ccp_unit_all_" + tableYear;
        String ccpMemAll = StrUtil.isEmpty(tableYear) ? "ccp_mem_all" : "ccp_mem_all_" + tableYear;
        String ccpDevelopStepLogAll = StrUtil.isEmpty(tableYear) ? "ccp_develop_step_log_all" : "ccp_develop_step_log_all_" + tableYear;
        Map<String, Number> result = new LinkedHashMap<>();
        Html48CountHistory.initReplenishCol(1, 14, result);
        SelectConditionStep<Record1<Object>> ccp_unit_all = DSL_CONTEXT.select(field("d04_code,graduate_student,undergraduate_student,junior_college_student,technical_secondary_student,middle_technical_students,teachers_institutions_higher,teachers_higher_women,teachers_age_thirty_five_below"))
                .from(table(name(ccpUnitAll)).as("ccp_unit_all")).where(new Html6CountHistory().getUnitListCondition(orgCode, orgLevelCode).and(this.getCond1()));
        List<com.jfinal.plugin.activerecord.Record> records = EsKit.findBySql(ccp_unit_all.toString()).toRecord();
        for (com.jfinal.plugin.activerecord.Record record : records) {
            //1．全体在校学生中研究生 人，大学本科生 人，大学专科生 人，中专生 人，高中、中技学生 人。
            Html48CountHistory.setReplenishMapValue("1", record.getInt("graduate_student"), result);
            Html48CountHistory.setReplenishMapValue("2", record.getInt("undergraduate_student"), result);
            Html48CountHistory.setReplenishMapValue("3", record.getInt("junior_college_student"), result);
            Html48CountHistory.setReplenishMapValue("4", record.getInt("technical_secondary_student"), result);
            Html48CountHistory.setReplenishMapValue("5", record.getInt("middle_technical_students"), result);
            //2．高等学校共有教师 人，其中女性 人、35 岁及以下的 人
            String d04Code = record.getStr("d04_code");
            if (StrUtil.startWith(d04Code, "331")) {
                Html48CountHistory.setReplenishMapValue("6", record.getInt("teachers_institutions_higher"), result);
                Html48CountHistory.setReplenishMapValue("7", record.getInt("teachers_higher_women"), result);
                Html48CountHistory.setReplenishMapValue("8", record.getInt("teachers_age_thirty_five_below"), result);
            }
        }
        //共有教师党员 人，其中女性 人，35 岁及以下的 人；本年发展教师党员 人，其中女性 人，35 岁及以下的 人。
        Condition memCond = noCondition().and(new Html6CountHistory().getMemListCondition(orgCode, orgLevelCode).and("d04_code like '331%' and d09_code in('0121','0131')"));
        Number number1 = EsKit.findListBySql(DSL_CONTEXT.select(field("count(1) as total")).from(table(name(ccpMemAll)).as("ccp_mem_all")).where(memCond).toString());
        Number number2 = EsKit.findListBySql(DSL_CONTEXT.select(field("count(1) as total")).from(table(name(ccpMemAll)).as("ccp_mem_all")).where(memCond.and("sex_code ='0'")).toString());
        Number number3 = EsKit.findListBySql(DSL_CONTEXT.select(field("count(1) as total")).from(table(name(ccpMemAll)).as("ccp_mem_all")).where(memCond.and("age<=35")).toString());
        Html48CountHistory.setReplenishMapValue("9", (Integer) number1, result);
        Html48CountHistory.setReplenishMapValue("10", (Integer) number2, result);
        Html48CountHistory.setReplenishMapValue("11", (Integer) number3, result);

        String year = CountMethod.getTableYearByIndex(tableYear);
        Condition devLog = noCondition().and(new Html6CountHistory().getDevelopLogListCondition(orgCode, orgLevelCode, "topre_join_org_date_year=" + year)
                .and("d04_code like '331%' and d09_code in('0121','0131')"));
        Number number4 = EsKit.findListBySql(DSL_CONTEXT.select(field("count(1) as total")).from(table(name(ccpDevelopStepLogAll)).as("ccp_develop_step_log_all")).where(devLog).toString());
        Number number5 = EsKit.findListBySql(DSL_CONTEXT.select(field("count(1) as total")).from(table(name(ccpDevelopStepLogAll)).as("ccp_develop_step_log_all")).where(devLog.and("sex_code ='0'")).toString());
        Number number6 = EsKit.findListBySql(DSL_CONTEXT.select(field("count(1) as total")).from(table(name(ccpDevelopStepLogAll)).as("ccp_develop_step_log_all")).where(devLog.and("age<=35")).toString());
        Html48CountHistory.setReplenishMapValue("12", (Integer) number4, result);
        Html48CountHistory.setReplenishMapValue("13", (Integer) number5, result);
        Html48CountHistory.setReplenishMapValue("14", (Integer) number6, result);
        return result;
    }

    private Condition getCond1() {
        //d04_code 331普通高等学校、332中等学校，5211民办高等学校、52121民办普通高中、5213民办中等职业技术学校
        return noCondition().and(field(name("d04_code")).like("33%").or(field(name("d04_code")).like("521%")));
    }

    private Condition getCond2() {
        return noCondition().and(field(name("d04_code")).like("331%"));
    }

    @Override
    public Map<String, Object> doReplenishPegging(PeggingPara peggingPara) throws Exception {
        String colIndex = peggingPara.getRowIndex();
        if (StrUtil.equalsAny(colIndex, "1", "2", "3", "4", "5", "6", "7", "8")) {
            Condition condition = new Html6CountHistory().getUnitListCondition(peggingPara.getOrgCode(), peggingPara.getOrgLevelCode()).and(this.getUnitRowCondition(peggingPara.getRowIndex()));
            UnitAllCondition unitAllCondition = new UnitAllCondition();
            return Html48CountHistory.getReportPageResult(peggingPara, unitAllCondition.getTableName(), condition, unitAllCondition.getLevelCodeField());
        } else if (StrUtil.equalsAny(colIndex, "9", "10", "11")) {
            Condition condition = new Html6CountHistory().getMemListCondition(peggingPara.getOrgCode(), peggingPara.getOrgLevelCode()).and(this.getMemRowCondition(peggingPara.getRowIndex()));
            MemAllCondition memAllCondition = new MemAllCondition();
            return Html48CountHistory.getReportPageResult(peggingPara, memAllCondition.getTableName(), condition, memAllCondition.getLevelCodeField());
        } else if (StrUtil.equalsAny(colIndex, "12", "13", "14")) {
            Condition condition = new Html6CountHistory().getDevelopLogListCondition(peggingPara.getOrgCode(), peggingPara.getOrgLevelCode(), "topre_join_org_date_year=" + Html1CountHistory.TABLE_YEAR)
                    .and(this.getMemRowCondition(peggingPara.getRowIndex()));
            DevelopStepLogAllCondition developStepLogAllCondition = new DevelopStepLogAllCondition();
            return Html48CountHistory.getReportPageResult(peggingPara, developStepLogAllCondition.getTableName(), condition, developStepLogAllCondition.getLevelCodeField());
        } else {
            return new HashMap<>();
        }
    }

    Condition getMemRowCondition(String rowIndex) {
        Condition condition = noCondition();
        if (StrUtil.equalsAny(rowIndex, "9", "10", "11", "12", "13", "14")) {
            condition = condition.and("d09_code in('0121','0131') and d04_code like '331%'");
            if (StrUtil.equalsAny(rowIndex, "10", "13")) {
                condition = condition.and("sex_code ='0'");
            } else if (StrUtil.equalsAny(rowIndex, "11", "14")) {
                condition = condition.and("age <=35");
            }
        }
        return condition;
    }

    /**
     * 反查条件
     *
     * @param rowIndex 序号
     * @return 查询条件
     */
    Condition getUnitRowCondition(String rowIndex) {
        Condition condition = noCondition();
        if (StrUtil.equalsAny(rowIndex, "1", "2", "3", "4", "5")) {
            condition = condition.and(getCond1());
            if (StrUtil.equals(rowIndex, "1")) {
                condition = condition.and("graduate_student is not null and graduate_student>0");
            }
            if (StrUtil.equals(rowIndex, "2")) {
                condition = condition.and("undergraduate_student is not null and undergraduate_student>0");
            }
            if (StrUtil.equals(rowIndex, "3")) {
                condition = condition.and("junior_college_student is not null and junior_college_student>0");
            }
            if (StrUtil.equals(rowIndex, "4")) {
                condition = condition.and("technical_secondary_student is not null and technical_secondary_student>0");
            }
            if (StrUtil.equals(rowIndex, "5")) {
                condition = condition.and("middle_technical_students is not null and middle_technical_students>0");
            }
        } else if (StrUtil.equalsAny(rowIndex, "6", "7", "8")) {
            condition = condition.and(getCond2());
            if (StrUtil.equals(rowIndex, "6")) {
                condition = condition.and("teachers_institutions_higher is not null and teachers_institutions_higher>0");
            }
            if (StrUtil.equals(rowIndex, "7")) {
                condition = condition.and("teachers_higher_women is not null and teachers_higher_women>0");
            }
            if (StrUtil.equals(rowIndex, "8")) {
                condition = condition.and("teachers_age_thirty_five_below is not null and teachers_age_thirty_five_below>0");
            }
        } else {
            condition = condition.and("1=0");
        }
        return condition;
    }

}
