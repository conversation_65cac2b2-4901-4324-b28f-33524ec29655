package com.zenith.front.core.analysis.count.history;

import cn.hutool.core.util.StrUtil;
import com.b1809.analysis.entity.PeggingPara;
import com.b1809.analysis.extend.ITableCount;
import com.b1809.analysis.plugin.elasticsearch.EsKit;
import com.zenith.front.core.analysis.ext.condition.OrgIndustryAllCondition;
import org.jooq.Condition;
import org.jooq.Record1;
import org.jooq.SelectConditionStep;
import org.springframework.stereotype.Component;

import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

import static com.zenith.front.common.constant.DBConstant.DSL_CONTEXT;
import static org.jooq.impl.DSL.*;

/**
 * 专题调查表十三 社会组织分层级建立行业党组织情况
 *
 * <AUTHOR>
 * @date 2022/1/5
 */
@Component
public class Html55CountHistory implements ITableCount {


    @Override
    public String getReportCode() {
        return "History55.html";
    }

    @Override
    public Map<String, Map<String, Number>> doCount(String reportCode, String orgCode, String orgLevelCode) throws Exception {
        return null;
    }

    @Override
    public Map<String, Object> doPegging(PeggingPara peggingPara) throws Exception {
        return null;
    }

    @Override
    public Map<String, Number> doReplenishCount(String reportCode, String orgCode, String orgLevelCode) throws Exception {
        return getReplenishCount(orgCode, orgLevelCode, null);
    }

    public Map<String, Number> getReplenishCount(String orgCode, String orgLevelCode, String tableYear) throws Exception {
        String ccpAll = StrUtil.isEmpty(tableYear) ? "ccp_org_industry_all" : "ccp_org_industry_all_" + tableYear;
        Map<String, Number> result = new LinkedHashMap<>();
        Html48CountHistory.initReplenishCol(1, 9, result);
        SelectConditionStep<Record1<Object>> records1 = DSL_CONTEXT.select(field("subordinate_level,manage_org_count,cover_social_org"))
                .from(table(name(ccpAll)).as("ccp_org_industry_all")).where(field(name("delete_time")).isNull()
                        .and(new OrgIndustryAllCondition().create(orgCode, orgLevelCode)).and(field(name("industry_classification")).eq("48")));
        List<com.jfinal.plugin.activerecord.Record> records = EsKit.findBySql(records1.toString()).toRecord();
        for (com.jfinal.plugin.activerecord.Record record : records) {
            String subordinateLevel = record.getStr("subordinate_level");
            Integer orgCount = record.getInt("manage_org_count");
            Integer socialOrg = record.getInt("cover_social_org");
            //依托民政部门建立省级社会组织党委		个，管理党组织		个，覆盖社会组织		个；
            if (StrUtil.equals(subordinateLevel, "1")) {
                Html48CountHistory.setReplenishMapValue("1", 1, result);
                Html48CountHistory.setReplenishMapValue("2", orgCount, result);
                Html48CountHistory.setReplenishMapValue("3", socialOrg, result);
            }
            //建立市级社会组织党委		个，管理党组织		个，覆盖社会组织			个；
            if (StrUtil.equals(subordinateLevel, "2")) {
                Html48CountHistory.setReplenishMapValue("4", 1, result);
                Html48CountHistory.setReplenishMapValue("5", orgCount, result);
                Html48CountHistory.setReplenishMapValue("6", socialOrg, result);
            }
            //建立县级社会组织党委		个，管理党组织		个，覆盖社会组织		个。
            if (StrUtil.equals(subordinateLevel, "3")) {
                Html48CountHistory.setReplenishMapValue("7", 1, result);
                Html48CountHistory.setReplenishMapValue("8", orgCount, result);
                Html48CountHistory.setReplenishMapValue("9", socialOrg, result);
            }
        }
        return result;
    }


    @Override
    public Map<String, Object> doReplenishPegging(PeggingPara peggingPara) throws Exception {
        OrgIndustryAllCondition orgIndustryAllCondition = new OrgIndustryAllCondition();
        Condition condition = noCondition().and(field(name("delete_time")).isNull().and(orgIndustryAllCondition.create(peggingPara.getOrgCode(), peggingPara.getOrgLevelCode()))
                .and(field(name("industry_classification")).eq("48"))).and(this.getRowCondition(peggingPara.getRowIndex()));

        return Html48CountHistory.getReportPageResult(peggingPara, orgIndustryAllCondition.getTableName(), condition, orgIndustryAllCondition.getLevelCodeField());
    }

    Condition getRowCondition(String rowIndex) {
        Condition condition = noCondition();
        if (StrUtil.equalsAny(rowIndex, "1", "2", "3")) {
            condition = condition.and(field(name("subordinate_level"), String.class).eq("1"));
        } else if (StrUtil.equalsAny(rowIndex, "4", "5", "6")) {
            condition = condition.and(field(name("subordinate_level"), String.class).eq("2"));
        } else if (StrUtil.equalsAny(rowIndex, "7", "8", "9")) {
            condition = condition.and(field(name("subordinate_level"), String.class).eq("3"));
        } else {
            condition = condition.and("1=0");
        }
        return condition;
    }


}
