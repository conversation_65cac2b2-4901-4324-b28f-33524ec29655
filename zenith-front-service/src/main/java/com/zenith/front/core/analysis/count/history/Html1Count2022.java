package com.zenith.front.core.analysis.count.history;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.thread.ThreadUtil;
import cn.hutool.core.util.StrUtil;
import com.b1809.analysis.entity.PeggingPara;
import com.b1809.analysis.extend.ITableCount;
import com.b1809.analysis.plugin.elasticsearch.EsKit;
import com.zenith.front.core.analysis.ext.condition.year2022.DevelopStepLogAllConditionA;
import com.zenith.front.core.analysis.ext.condition.year2022.MemAllCondition2022A;
import com.zenith.front.core.analysis.ext.condition.year2022.TransferStatisticsConditionA;
import org.jooq.Condition;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.Map;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutorService;

import static com.zenith.front.common.constant.DBConstant.DSL_CONTEXT;
import static org.jooq.impl.DSL.*;

/**
 * 党员数量变化情况
 *
 * <AUTHOR>
 * @date 2022/6/7
 */
@Component
@SuppressWarnings("all")
public class Html1Count2022 implements ITableCount {

    public static final String TABLE_ES_YEAR_A = "2022_a";
    public static final String TABLE_ES_YEAR_B = "2022_b";
    public static final Long Y2022_STATISTICAL_START = DateUtil.parseDate("2022.01.01").getTime();
    public static final Long Y2022_STATISTICAL_END = DateUtil.parseDate("2022.12.31").getTime();

    @Override
    public String getReportCode() {
        return "2022_1.html";
    }

    @Override
    public Map<String, Map<String, Number>> doCount(String reportCode, String orgCode, String orgLevelCode) throws Exception {
        Map<String, Number> result = new LinkedHashMap<>();
        Html48CountHistory.initTableCol(1, 17, 1, 1, result);
        ExecutorService executor = ThreadUtil.newExecutor(20);

        MemAllCondition2022A memCond = new MemAllCondition2022A();
        DevelopStepLogAllConditionA stepLogCond = new DevelopStepLogAllConditionA();
        TransferStatisticsConditionA transferCond = new TransferStatisticsConditionA();
        Map<String, String> stringMap = new HashMap<String, String>(20) {
            private static final long serialVersionUID = -7658216719447680898L;

            {
                //3 发展党员
                Condition devLog = stepLogCond.create(orgCode, orgLevelCode).and("delete_time is null and d08_code ='3'").and(getEsReportDateSql("topre_join_org_date"));
                put("3", DSL_CONTEXT.select(field("count(1) as total")).from(table(name(stepLogCond.getTableName()))).where(devLog).toString());
                //4 重新入党
                put("4", DSL_CONTEXT.select(field("count(1) as total")).from(table(name(stepLogCond.getTableName()))).where(devLog.and("join_org_code = '12'")).toString());
                //5 恢复党籍
                Condition sql5 = noCondition().and("d08_code in ('1','2') and (delete_time is null or (delete_time is not null and transfer_out_type in('223','224')))")
                        .and(getEsReportDateSql("recover_party_date")).and(memCond.create(orgCode, orgLevelCode));
                put("5", DSL_CONTEXT.select(field("count(1) as total")).from(table(name(memCond.getTableName()))).where(sql5.and("d11_code like '2%'")).toString());
                //6 停止党籍后恢复党籍
                put("6", DSL_CONTEXT.select(field("count(1) as total")).from(table(name(memCond.getTableName()))).where(sql5.and("d11_code = '21'")).toString());
                //7 转入组织关系
                Condition sql7 = transferCond.create(orgCode, orgLevelCode).and(getEsReportDateSql("transfer_time") + " and delete_time is null and transfer_type='1'");
                put("7", DSL_CONTEXT.select(field("count(1) as total")).from(table(name(transferCond.getTableName()))).where(sql7).toString());
                //8 整建制转入
                put("8", DSL_CONTEXT.select(field("count(1) as total")).from(table(name(transferCond.getTableName()))).where(sql7.and("transfer_category = '2'")).toString());

                //10 出党
                Condition sql10 = noCondition().and("delete_time is not null and d08_code in ('1','2')").and(getEsReportDateSql("leave_org_date"))
                        .and(memCond.create(orgCode, orgLevelCode));
                put("10", DSL_CONTEXT.select(field("count(1) as total")).from(table(name(memCond.getTableName()))).where(sql10.and("d029_code like 'C%'")).toString());
                //11 停止党籍
                put("11", DSL_CONTEXT.select(field("count(1) as total")).from(table(name(memCond.getTableName()))).where(sql10.and("d12_code like '3%'")).toString());
                //12 死亡
                put("12", DSL_CONTEXT.select(field("count(1) as total")).from(table(name(memCond.getTableName()))).where(sql10.and("d12_code like '1%'")).toString());
                //13 转出组织关系
                Condition sql13 = transferCond.create(orgCode, orgLevelCode).and(getEsReportDateSql("transfer_time") + " and delete_time is null and transfer_type='2'");
                put("13", DSL_CONTEXT.select(field("count(1) as total")).from(table(name(transferCond.getTableName()))).where(sql13).toString());
                //14 整建制转出
                put("14", DSL_CONTEXT.select(field("count(1) as total")).from(table(name(transferCond.getTableName()))).where(sql13.and("transfer_category = '2'")).toString());
                //16 截至报告期末实有数
                put("16", DSL_CONTEXT.select(field("count(1) as total")).from(table(name(memCond.getTableName()))).where("delete_time is null and d08_code in ('1','2') and mem_org_code like '" + orgLevelCode + "%'").toString());
            }
        };
        Map<String, CompletableFuture<Integer>> res = new HashMap<>();
        stringMap.forEach((key, value) -> res.put(key, StrUtil.isEmpty(value) ? CompletableFuture.supplyAsync(() -> 0, executor) : CompletableFuture.supplyAsync(() -> (int) EsKit.findListBySql(value), executor)));
        for (Map.Entry<String, CompletableFuture<Integer>> entry : res.entrySet()) {
            Html48CountHistory.setTableMapValue(entry.getKey(), "1", entry.getValue().get(), result);
        }
        executor.shutdown();
        //17 实有数与应有数之差：补录、错误录入，成为预备党员时间是去年且录入时间是今年的
        Condition sql171 = noCondition().and(getAfterNewYearSql("create_time") + " or " + getAfterNewYearSql("replenish_input_date"))
                .and("d08_code in ('1','2') and ((delete_time is null and d135_code='1') " +
                        "or (d135_code='1' and (d12_code in ('4','6') or d12_code like '1%' or d12_code like '2%' or d12_code='3%') and " + this.getEsReportDateSql("leave_org_date") + ") " +
                        "or (d135_code='1' and transfer_out_type in('223','224')))").and(memCond.create(orgCode, orgLevelCode));
        //成为预备党员时间小于2022年1月1日，录入时间在去年过年后（即统计期外的）
        Condition sql172 = noCondition().and("delete_time is null and d08_code in ('3') and " + getAfterNewYearSql("create_time"))
                .and(getLastYearSql("topre_join_org_date")).and(stepLogCond.create(orgCode, orgLevelCode));
        Condition sql173 = noCondition().and("d08_code in ('1','2') and (is_transfer is null or is_transfer!=1) and d12_code='4'").and(getAfterNewYearSql("leave_org_date"))
                .and(memCond.create(orgCode, orgLevelCode));
        //本年度发展党员被删除了的
        Condition sql174 = stepLogCond.create(orgCode, orgLevelCode).and("d08_code ='3'").and(this.getEsReportDateSql("leave_org_date")).and(this.getEsReportDateSql("topre_join_org_date"));
        //-重新履行入党手续
        Condition sql175 = noCondition().and("d08_code in ('1','2') and (is_transfer is null or is_transfer!=1) and d12_code='6'").and(this.getEsReportDateSql("leave_org_date"))
                .and(memCond.create(orgCode, orgLevelCode));
        //-排查整顿发展党员违规违纪问题工作中除名处置
        Condition sql176 = noCondition().and("d08_code in ('1','2') and (is_transfer is null or is_transfer!=1) and d12_code='7'").and(this.getEsReportDateSql("leave_org_date"))
                .and(memCond.create(orgCode, orgLevelCode));
        int number1 = (int) EsKit.findListBySql(DSL_CONTEXT.select(field("count(1) as total")).from(table(name(memCond.getTableName()))).where(sql171).toString());
        int number2 = (int) EsKit.findListBySql(DSL_CONTEXT.select(field("count(1) as total")).from(table(name(stepLogCond.getTableName()))).where(sql172).toString());
        int number3 = (int) EsKit.findListBySql(DSL_CONTEXT.select(field("count(1) as total")).from(table(name(memCond.getTableName()))).where(sql173).toString());
        int number4 = (int) EsKit.findListBySql(DSL_CONTEXT.select(field("count(1) as total")).from(table(name(stepLogCond.getTableName()))).where(sql174).toString());
        int number5 = (int) EsKit.findListBySql(DSL_CONTEXT.select(field("count(1) as total")).from(table(name(memCond.getTableName()))).where(sql175).toString());
        int number6 = (int) EsKit.findListBySql(DSL_CONTEXT.select(field("count(1) as total")).from(table(name(memCond.getTableName()))).where(sql176).toString());
        System.out.println(number1 + " " + number2 + " " + number3 + " " + number4 + " " + number5);
        int num17 = number1 + number2 - number3 + number4 - number5 - number6;
        int num2 = res.get("3").get() + res.get("5").get() + res.get("7").get();
        int num9 = res.get("10").get() + res.get("11").get() + res.get("12").get() + res.get("13").get();
        // 17栏=16栏-15栏
        // 15栏=1栏+2栏-9栏
        //1 上年底总数   16-17+9-2
        Html48CountHistory.setTableMapValue("1", "1", res.get("16").get() - num17 + num9 - num2, result);
        Html48CountHistory.setTableMapValue("2", "1", num2, result);
        Html48CountHistory.setTableMapValue("9", "1", num9, result);
        //15 截至报告期末应有数
        Html48CountHistory.setTableMapValue("15", "1", res.get("16").get() - num17, result);
        Html48CountHistory.setTableMapValue("17", "1", num17, result);

        Map<String, Map<String, Number>> mapMap = new HashMap<>(10);
        mapMap.put("table0", result);
        return mapMap;
    }


    @Override
    public Map<String, Object> doPegging(PeggingPara peggingPara) throws Exception {
        if (StrUtil.equals(peggingPara.getType(), "2")) {
            return this.doReplenishPegging(peggingPara);
        }
        DevelopStepLogAllConditionA stepLogCond = new DevelopStepLogAllConditionA();
        if (StrUtil.equalsAny(peggingPara.getRowIndex(), "3", "4", "174")) {
            Condition condition = stepLogCond.create(peggingPara.getOrgCode(), peggingPara.getOrgLevelCode()).and("d08_code ='3'").and(this.getEsReportDateSql("topre_join_org_date"));
            if (StrUtil.equals(peggingPara.getRowIndex(), "3")) {
                condition = condition.and("delete_time is null");
            } else if (StrUtil.equals(peggingPara.getRowIndex(), "4")) {
                condition = condition.and("delete_time is null and join_org_code = '12'");
            } else if (StrUtil.equals(peggingPara.getRowIndex(), "174")) {
                condition = condition.and(this.getEsReportDateSql("leave_org_date"));
            }
            return Html48CountHistory.getReportPageResult(peggingPara, stepLogCond.getTableName(), condition, stepLogCond.getLevelCodeField());
        }
        if (StrUtil.equals(peggingPara.getRowIndex(), "172")) {
            Condition condition = stepLogCond.create(peggingPara.getOrgCode(), peggingPara.getOrgLevelCode()).and("delete_time is null and d08_code ='3'")
                    .and(getAfterNewYearSql("create_time")).and(getLastYearSql("topre_join_org_date"));
            return Html48CountHistory.getReportPageResult(peggingPara, stepLogCond.getTableName(), condition, stepLogCond.getLevelCodeField());
        }
        if (StrUtil.equalsAny(peggingPara.getRowIndex(), "7", "8", "13", "14")) {
            TransferStatisticsConditionA transferCond = new TransferStatisticsConditionA();
            Condition condition = this.getRowCondition(peggingPara.getRowIndex()).and(this.getEsReportDateSql("transfer_time") + " and delete_time is null")
                    .and(transferCond.create(peggingPara.getOrgCode(), peggingPara.getOrgLevelCode()));
            return Html48CountHistory.getReportPageResult(peggingPara, transferCond.getTableName(), condition, transferCond.getLevelCodeField());
        }
        MemAllCondition2022A memAllCondition = new MemAllCondition2022A();
        Condition condition = this.getRowCondition(peggingPara.getRowIndex()).and(memAllCondition.create(peggingPara.getOrgCode(), peggingPara.getOrgLevelCode()));
        return Html48CountHistory.getReportPageResult(peggingPara, memAllCondition.getTableName(), condition, memAllCondition.getLevelCodeField());
    }

    public Condition getRowCondition(String rowIndex) {
        Condition condition = noCondition();
        if (StrUtil.equalsAny(rowIndex, "1", "15", "17", "2", "9")) {
            return condition.and("1=0");
        }
        String memSql = "(delete_time is null or (delete_time is not null and transfer_out_type in('223','224'))) and d08_code in ('1','2') ";
        String historySql = "delete_time is not null and d08_code in ('1','2') and " + this.getEsReportDateSql("leave_org_date");
        if (StrUtil.equals(rowIndex, "2")) {
            condition = condition.and("(has_develop_year='1')" +
                    "or (delete_time is null and d08_code in ('1','2') and (is_transfer is null or is_transfer!=1) and d11_code like '2%' and " + this.getEsReportDateSql("recover_party_date") + ") " +
                    "or (transfer_in_type like '1%')");
        } else if (StrUtil.equals(rowIndex, "5")) {
            condition = condition.and(memSql).and("d11_code like '2%'").and(this.getEsReportDateSql("recover_party_date"));
        } else if (StrUtil.equals(rowIndex, "6")) {
            condition = condition.and(memSql).and("d11_code = '21'").and(this.getEsReportDateSql("recover_party_date"));
        } else if (StrUtil.equals(rowIndex, "7")) {
            condition = condition.and("transfer_type='1'");
        } else if (StrUtil.equals(rowIndex, "8")) {
            condition = condition.and("transfer_type='1' and transfer_category = '2'");
        } else if (StrUtil.equals(rowIndex, "9")) {
            condition = condition.and("(" + historySql + " and (d12_code like '1%' or d029_code like 'C%' or d12_code like '3%')) or transfer_out_type like '2%'");
        } else if (StrUtil.equals(rowIndex, "10")) {
            condition = condition.and(historySql).and("d029_code like 'C%'");
        } else if (StrUtil.equals(rowIndex, "11")) {
            condition = condition.and(historySql).and("d12_code like '3%'");
        } else if (StrUtil.equals(rowIndex, "12")) {
            condition = condition.and(historySql).and("d12_code like '1%'");
        } else if (StrUtil.equals(rowIndex, "13")) {
            condition = condition.and("transfer_type='2'");
        } else if (StrUtil.equals(rowIndex, "14")) {
            condition = condition.and("transfer_type='2' and transfer_category = '2'");
        } else if (StrUtil.equals(rowIndex, "16")) {
            condition = condition.and("delete_time is null and d08_code in ('1','2')");
        } else
            //补录
            if (StrUtil.equals(rowIndex, "171")) {
                condition = noCondition().and(getAfterNewYearSql("create_time") + " or " + getAfterNewYearSql("replenish_input_date"))
                        .and("d08_code in ('1','2') and ((delete_time is null and d135_code='1') " +
                                "or (d135_code='1' and (d12_code in ('4','6') or d12_code like '1%' or d12_code like '2%' or d12_code='3%') and " + this.getEsReportDateSql("leave_org_date") + ") " +
                                "or (d135_code='1' and transfer_out_type in('223','224')))");
            } else if (StrUtil.equals(rowIndex, "173")) {
                condition = noCondition().and("d08_code in ('1','2') and (is_transfer is null or is_transfer!=1) and d12_code='4'").and(getAfterNewYearSql("leave_org_date"));
            } else if (StrUtil.equals(rowIndex, "175")) {
                condition = noCondition().and("d08_code in ('1','2') and (is_transfer is null or is_transfer!=1) and d12_code='6'").and(this.getEsReportDateSql("leave_org_date"));
            } else if (StrUtil.equals(rowIndex, "176")) {
                condition = noCondition().and("d08_code in ('1','2') and (is_transfer is null or is_transfer!=1) and d12_code='7'").and(this.getEsReportDateSql("leave_org_date"));
            }
        return condition;
    }

    @Override
    public Map<String, Number> doReplenishCount(String reportCode, String orgCode, String orgLevelCode) throws Exception {
        Map<String, Number> result = new LinkedHashMap<>();
        Html48CountHistory.initReplenishCol(1, 1, result);
        TransferStatisticsConditionA transferCond = new TransferStatisticsConditionA();
        Condition condition = transferCond.create(orgCode, orgLevelCode).and(this.getEsReportDateSql("transfer_time") + " and delete_time is null and transfer_type='2'");
        Number number = EsKit.findListBySql(DSL_CONTEXT.select(field("count(1) as total")).from(table(name(transferCond.getTableName()))).where(condition).toString());
        Html48CountHistory.setReplenishMapValue("1", (Integer) number, result);
        return result;
    }

    @Override
    public Map<String, Object> doReplenishPegging(PeggingPara peggingPara) throws Exception {
        TransferStatisticsConditionA transferCond = new TransferStatisticsConditionA();
        Condition condition = transferCond.create(peggingPara.getOrgCode(), peggingPara.getOrgLevelCode()).and(this.getEsReportDateSql("transfer_time") + " and delete_time is null and transfer_type='2'");
        return Html48CountHistory.getReportPageResult(peggingPara, transferCond.getTableName(), condition, transferCond.getLevelCodeField());
    }


    public String getAfterNewYearSql(String field) {
        return field + " between " + new Html1Count20220630().DATE_20220207 + " and " + Y2022_STATISTICAL_END;
    }

    public String getLastYearSql(String field) {
        return field + " < " + Y2022_STATISTICAL_START;
    }

    /**
     * 获取报告期内查询条件
     */
    public String getEsReportDateSql(String field) {
        return field + " between " + Y2022_STATISTICAL_START + " and " + Y2022_STATISTICAL_END;
    }

    /**
     * 获取2021年度查询
     */
    public String getEs2021ReportDateSql(String field) {
        final long y2021Start = DateUtil.parseDate("2021.01.01").getTime();
        final long y2021End = DateUtil.parseDate("2021.12.31").getTime();
        return field + " between " + y2021Start + " and " + y2021End;
    }

}
