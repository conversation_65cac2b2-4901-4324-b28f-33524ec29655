package com.zenith.front.core.job;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import com.zenith.front.api.org.IOrgCommitteeElectService;
import com.zenith.front.common.kit.StrKit;
import com.zenith.front.model.bean.MqMessage;
import com.zenith.front.model.vo.ChangeOfPartyOrganizationMessageVO;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.text.MessageFormat;
import java.util.*;

/**
 * 党组织班子换届消息提醒
 * 最新的一个届次结束时间在3个月以内
 *
 * <AUTHOR>
 * @version V1.0
 * @date 2022/7/27 10:01
 */
@Component
public class ChangeOfPartyOrganization extends BaseMqMessageService {

    @Resource
    private IOrgCommitteeElectService orgCommitteeElectService;

    private static final String NORMAL_MESSAGE = "{0}将于{1}任职换届到期，请及时维护";
    private static final String EXPIRE_MESSAGE = "{0}已于{1}任期届满，请及时维护";
    private static final String TYPE = "1";

    @XxlJob("changeOfPartyOrganization")
    public void changeOfPartyOrganization() {
        XxlJobHelper.log("党组织班子换届,消息提醒.");
        //查询所有党组织班子届次
        List<ChangeOfPartyOrganizationMessageVO> committeeElectList = orgCommitteeElectService.findAssignOrgCommitteeElectMessage(null);
        if (CollUtil.isNotEmpty(committeeElectList)) {

            List<MqMessage> mqMessageList = new ArrayList<>();
            for (ChangeOfPartyOrganizationMessageVO orgCommitteeElect : committeeElectList) {
                Date tenureEndDate = orgCommitteeElect.getTenureEndDate();
                String orgName = orgCommitteeElect.getOrgName();
                String electOrgCode = orgCommitteeElect.getElectOrgCode();

                String messageFormat = MessageFormat.format(
                        tenureEndDate.after(DateUtil.endOfDay(new Date())) ? NORMAL_MESSAGE : EXPIRE_MESSAGE
                        , orgName
                        , DateUtil.format(tenureEndDate, DatePattern.CHINESE_DATE_FORMAT)
                );

                MqMessage message = new MqMessage();
                message.setCode(StrKit.getRandomUUID());
                message.setType(TYPE);
                message.setMessage(messageFormat);
                message.setOrgCode(electOrgCode);
                message.setCreateTime(new Date());
                message.setSortTime(tenureEndDate);
                mqMessageList.add(message);
            }
            //按类型删除
            mqMessageService.deleteByType(TYPE);
            //批量保存
            mqMessageService.saveBatch(mqMessageList);
        }
    }

}
