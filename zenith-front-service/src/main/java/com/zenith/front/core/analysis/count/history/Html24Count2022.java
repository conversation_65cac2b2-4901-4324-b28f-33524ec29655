package com.zenith.front.core.analysis.count.history;

import cn.hutool.core.util.StrUtil;
import com.b1809.analysis.entity.PeggingPara;
import com.b1809.analysis.extend.ITableCount;
import com.zenith.front.core.analysis.ext.condition.year2022.OrgAllConditionB;
import com.zenith.front.core.analysis.ext.condition.year2022.OrgPartyConditionB;
import com.zenith.front.core.analysis.ext.condition.year2022.UnitAllConditionB;
import org.jooq.Condition;
import org.springframework.stereotype.Component;

import java.util.Map;

import static org.jooq.impl.DSL.noCondition;

/**
 * 第二十四表 党的基层组织数量情况和换届情况
 *
 * <AUTHOR>
 * @date 2022/10/21
 */
@Component
public class Html24Count2022 extends Html24CountHistory implements ITableCount {


    @Override
    public String getReportCode() {
        return "2022_24.html";
    }

    @Override
    public Map<String, Map<String, Number>> doCount(String reportCode, String orgCode, String orgLevelCode) throws Exception {
        return null;
    }

    @Override
    public Map<String, Object> doPegging(PeggingPara peggingPara) throws Exception {
        return null;
    }

    @Override
    public Map<String, Number> doReplenishCount(String reportCode, String orgCode, String orgLevelCode) throws Exception {
        return getReplenishCount(orgCode, orgLevelCode, Html1Count2022.TABLE_ES_YEAR_B);
    }


    @Override
    public Map<String, Object> doReplenishPegging(PeggingPara peggingPara) {
        String colIndex = peggingPara.getRowIndex();
        if (StrUtil.equalsAny(colIndex, "1", "2", "3", "4", "5")) {
            OrgPartyConditionB partyCond = new OrgPartyConditionB();
            Condition condition = partyCond.create(peggingPara.getOrgCode(), peggingPara.getOrgLevelCode()).and(this.getPartyRowCondition(peggingPara.getRowIndex()));
            return Html48CountHistory.getReportPageResult(peggingPara, partyCond.getTableName(), condition, partyCond.getLevelCodeField());
        } else if (StrUtil.equalsAny(colIndex, "18", "23", "28")) {
            UnitAllConditionB unitAllCond = new UnitAllConditionB();
            Condition condition = this.getUnitRowCondition(colIndex, Html1Count2022.TABLE_ES_YEAR_B).and(new Html6CountHistory().getUnitListCondition(peggingPara.getOrgCode(), peggingPara.getOrgLevelCode()).toString().replace("ccp_unit_all", unitAllCond.getTableName()));
            return Html48CountHistory.getReportPageResult(peggingPara, unitAllCond.getTableName(), condition, unitAllCond.getLevelCodeField());
        } else {
            OrgAllConditionB orgAllCond = new OrgAllConditionB();
            Condition condition = noCondition().and(this.getOrgRowCondition(peggingPara.getRowIndex(), peggingPara.getOrgCode(), peggingPara.getOrgLevelCode(), Html1Count2022.TABLE_ES_YEAR_B).toString().replace("ccp_org_all", orgAllCond.getTableName()));
            return Html48CountHistory.getReportPageResult(peggingPara, orgAllCond.getTableName(), condition, orgAllCond.getLevelCodeField());
        }

    }


}
