package com.zenith.front.core.sync.rule;

import com.fasterxml.jackson.databind.JsonNode;
import com.zenith.front.core.sync.AbstractCountFilter;
import com.zenith.front.core.sync.AbstractCountRule;
import com.zenith.front.core.sync.AbstractOperator;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 默认统计规则
 *
 * <AUTHOR>
 * @date 2019/6/211:43 PM
 */
public class DefaultCountRule extends AbstractCountRule {

    /***
     * 统计操作符
     * */
    private AbstractOperator operator;

    @Override
    public boolean isOk(Map<String, Object> value) {
        //获取所有关心的维度
        List<String> dimensionColumnNames = getDimensionColumnNames();
        //验证数据是否符合规则
        for (String dimensionColumnName : dimensionColumnNames) {
            Object o = value.get(dimensionColumnName);
            if (o == null) {
                return false;
            }
            List<AbstractCountFilter> filters = getFilters().get(dimensionColumnName);
            if (filters == null) {
                return false;
            }
            for (AbstractCountFilter filter : filters) {
                boolean result = filter.check(o);
                if (!result) {
                    return false;
                }
            }

        }
        return true;
    }

    @Override
    public String getKey(Map<String, Object> value) {
        //按照维度生成redis key
        StringBuilder builder = new StringBuilder();
        for (int i = 0; i < getDimensionColumnNames().size(); i++) {
            String key = getDimensionColumnNames().get(i);
            Object o = value.get(key);
            builder.append(o);
            if (i != getDimensionColumnNames().size() - 1) {
                builder.append("-");
            }
        }
        return builder.toString();
    }

    @Override
    public AbstractOperator getOperator() {
        return operator;
    }

    @Override
    public void init(JsonNode jsonNode) throws ClassNotFoundException, IllegalAccessException, InstantiationException {
        String tableName = jsonNode.get("tableName").asText();
        this.setTableName(tableName);
        String orgCodeKey = jsonNode.get("orgCodeColumnName").asText();
        this.setOrgCodeColumnName(orgCodeKey);
        //获取操作符类型
        String operatorClass = jsonNode.get("operatorClass").asText();
        //实例化操作符
        operator = (AbstractOperator) Class.forName(operatorClass).newInstance();
        //获取关心的维度列表
        List<String> dimensionColumnNames = jsonNode.findValuesAsText("dimensionColumnNames");
        this.setDimensionColumnNames(dimensionColumnNames);
        Map<String, List<AbstractCountFilter>> map = new HashMap<>(4);
        for (JsonNode dimensionFilters : jsonNode.get("dimensionFilters")) {
            String columnName = dimensionFilters.get("columnName").asText();
            List<AbstractCountFilter> countFilterList = map.computeIfAbsent(columnName, s -> new ArrayList<>());
            for (JsonNode filters : dimensionFilters.get("filters")) {
                String filterName = filters.get("filterName").asText();
                AbstractCountFilter filter = AbstractCountFilter.FILTER_CENTER.get(filterName);
                if (filter == null) {
                    continue;
                }
                if (filters.has("value")) {
                    String value = filters.get("value").asText();
                    filter.setValue(value);
                }
                countFilterList.add(filter);
            }
        }
        this.setFilters(map);
    }
}
