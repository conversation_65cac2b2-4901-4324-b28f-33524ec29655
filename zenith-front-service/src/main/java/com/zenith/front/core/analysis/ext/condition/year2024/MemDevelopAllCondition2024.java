package com.zenith.front.core.analysis.ext.condition.year2024;

import com.b1809.analysis.extend.GenSqlConditionFuc;
import org.jooq.Condition;
import org.jooq.Field;

import static org.jooq.impl.DSL.field;
import static org.jooq.impl.DSL.name;

/**
 * 发展党员表查询条件
 *
 * <AUTHOR>
 * @since 2021/7/13 15:21
 */
public class MemDevelopAllCondition2024 implements GenSqlConditionFuc {

    @Override
    public String getTableName() {
        return "ccp_mem_develop_all_2024";
    }

    @Override
    public Condition create(String orgCode, String orgLevelCode) {
        return field(name( "develop_org_code"), String.class).like(orgLevelCode + "%");
    }

    @Override
    public Field<?> getLevelCodeField() {
        return field(name("develop_org_code"), String.class);
    }
}
