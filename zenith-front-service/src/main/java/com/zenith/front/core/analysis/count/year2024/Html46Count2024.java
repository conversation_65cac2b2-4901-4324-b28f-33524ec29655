package com.zenith.front.core.analysis.count.year2024;

import cn.hutool.core.util.StrUtil;
import com.b1809.analysis.entity.PeggingPara;
import com.b1809.analysis.extend.ITableCount;
import com.b1809.analysis.plugin.elasticsearch.EsKit;
import com.jfinal.plugin.activerecord.Record;
import com.zenith.front.common.constant.CommonConstant;
import com.zenith.front.core.analysis.ext.condition.year2024.MemAllCondition2024;
import com.zenith.front.core.analysis.ext.condition.year2024.OrgAllCondition2024;
import com.zenith.front.core.analysis.ext.condition.year2024.UnitAllCondition2024;
import org.jooq.Condition;
import org.jooq.Record1;
import org.jooq.SelectConditionStep;
import org.jooq.SelectHavingStep;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

import static com.zenith.front.common.constant.DBConstant.DSL_CONTEXT;
import static org.jooq.impl.DSL.*;

/**
 * 专题调查表一 农村党建相关情况
 *
 * <AUTHOR>
 * @date 2022/1/7
 */
@Component
public class Html46Count2024 implements ITableCount {
    private static final Pattern NUMBER_PATTERN = Pattern.compile("^[0-9]*$");

    @Override
    public String getReportCode() {
        return "2024_46.html";
    }

    @Override
    public Map<String, Map<String, Number>> doCount(String reportCode, String orgCode, String orgLevelCode) throws Exception {
        return getCount(orgCode, orgLevelCode, Html1Count2024.TABLE_YEAR);
    }

    public Map<String, Map<String, Number>> getCount(String orgCode, String orgLevelCode, String tableYear) throws Exception {
        String ccpUnitAll = StrUtil.isEmpty(tableYear) ? "ccp_unit_all" : "ccp_unit_all_" + tableYear;
        String ccpOrgAll = StrUtil.isEmpty(tableYear) ? "ccp_org_all" : "ccp_org_all_" + tableYear;
        String ccpMemAll = StrUtil.isEmpty(tableYear) ? "ccp_mem_all" : "ccp_mem_all_" + tableYear;
        Map<String, Number> result = new LinkedHashMap<>();
        Html53Count2024.initTableCol(1, 1, 1, 23, result);
        Condition condition = noCondition().and(field(name("d04_code"), String.class).in("922", "923", "921"));
        SelectConditionStep<Record1<Object>> records1 = DSL_CONTEXT.select(field("d155_code,d04_code,first_secretary_select,first_secretary_code,secretary_training_num,has_thousand,has_bundled,promoted_num,adjusted_num,operating_expenses,village_per,secretary_salary,space_area,new_expand_area"))
                .from(table(name(ccpUnitAll)).as("ccp_unit_all")).where(new Html7Count2024().getUnitListCondition(orgCode, orgLevelCode)).and(condition);
        List<Record> records = EsKit.findBySql(records1.toString()).toRecord();
        BigDecimal operatingExpenses = BigDecimal.ZERO;
        BigDecimal villagePer = BigDecimal.ZERO;
        BigDecimal secretarySalary = BigDecimal.ZERO;
        int index1 = 0, index2 = 0, index3 = 0;
        for (Record record : records) {
            String d04Code = record.getStr("d04_code");
            String d155Code = record.getStr("d155_code");
            // 社区中为派驻异地扶贫搬迁安置村的要累积也要统计
            if (StrUtil.equalsAny(d04Code, "921", "922") ) {
                if (StrUtil.contains(d155Code, "2")){
                    //16 本年各级培训第一书记（人次）
                    Html53Count2024.setTableMapValue("1", "16", record.getInt("secretary_training_num"), result);
                    //17 为第一书记安排不低于1万元工作经费的村
                    Html53Count2024.setTableMapValue("1", "17", record.getInt("has_thousand"), result);
                    //18 派出单位落实责任、项目、资金捆绑的村
                    Html53Count2024.setTableMapValue("1", "18", record.getInt("has_bundled"), result);
                    //19 本年提拔使用或晋级的第一书记
                    Html53Count2024.setTableMapValue("1", "19", record.getInt("promoted_num"), result);
                    //20 本年因工作不胜任召回调整的第一书记
                    Html53Count2024.setTableMapValue("1", "20", record.getInt("adjusted_num"), result);
                }
                // 单位类别为社区的 则跳过当前循环
                continue;
            }
            //1 行政村总数
            Html53Count2024.setTableMapValue("1", "1", 1, result);

            //16 本年各级培训第一书记（人次）
            Html53Count2024.setTableMapValue("1", "16", record.getInt("secretary_training_num"), result);
            //17 为第一书记安排不低于1万元工作经费的村
            Html53Count2024.setTableMapValue("1", "17", record.getInt("has_thousand"), result);
            //18 派出单位落实责任、项目、资金捆绑的村
            Html53Count2024.setTableMapValue("1", "18", record.getInt("has_bundled"), result);
            //19 本年提拔使用或晋级的第一书记
            Html53Count2024.setTableMapValue("1", "19", record.getInt("promoted_num"), result);
            //20 本年因工作不胜任召回调整的第一书记
            Html53Count2024.setTableMapValue("1", "20", record.getInt("adjusted_num"), result);
            BigDecimal expenses = record.getBigDecimal("operating_expenses");
            if (Objects.nonNull(expenses)) {
                operatingExpenses = operatingExpenses.add(expenses);
                index1++;
            }
            BigDecimal per = record.getBigDecimal("village_per");
            if (Objects.nonNull(per)) {
                villagePer = villagePer.add(per);
                index2++;
            }
            BigDecimal salary = record.getBigDecimal("secretary_salary");
            if (Objects.nonNull(salary) && salary.compareTo(BigDecimal.ZERO) > 0) {
                secretarySalary = secretarySalary.add(salary);
                index3++;
            }

            // 暂无活动场所的行政村
            if (Objects.nonNull(record.getInt("space_area")) && record.getInt("space_area") == 0) {
                Html53Count2024.setTableMapValue("1", "28", CommonConstant.ONE_INT, result);
            }
            // 活动场所面积200㎡以上的行政村
            if (Objects.nonNull(record.getInt("space_area")) && record.getInt("space_area") > 200) {
                Html53Count2024.setTableMapValue("1", "29", CommonConstant.ONE_INT, result);
            }
            // 本年新建或改扩建活动场所数量
            if (Objects.nonNull(record.getInt("new_expand_area")) && record.getInt("new_expand_area") > CommonConstant.ZERO_INT) {
                // TODO: 2023/1/12 调整为当村社区场所填写只要有数字，只算1
                Html53Count2024.setTableMapValue("1", "30", CommonConstant.ONE_INT, result);
            }

            // 未完成“五小”建设的乡镇
            Html53Count2024.setTableMapValue("1", "31", CommonConstant.ZERO_INT, result);
        }
        //21 平均每村运转经费（万元∕年）
        Html53Count2024.setTableMapValue("1", "21", getAvgCount(operatingExpenses, index1), result);
        //22 平均每村办公经费（万元∕年）
        Html53Count2024.setTableMapValue("1", "22", getAvgCount(villagePer, index2), result);
        //23 村党组织书记平均报酬（万元∕年）
        Html53Count2024.setTableMapValue("1", "23", getAvgCount(secretarySalary, index3), result);

        SelectConditionStep<Record1<Object>> orgR = DSL_CONTEXT.select(field("d01_code")).from(table(name(ccpOrgAll)).as("ccp_org_all")).where(new Html7Count2024().getOrgListCondition(orgCode, orgLevelCode));
        List<Record> records2 = EsKit.findBySql(orgR.toString()).toRecord();
        long count = records2.stream().filter(e -> StrUtil.startWith(e.getStr("d01_code"), "14")).count();
        // 24 村干部基本报酬和村级组织办公经费合计低于11万元的县（市、区、旗）TODO 专调1  p列不出数
        Html53Count2024.setTableMapValue("1", "24", 0, result);
        // 25 落实正常离任村干部生活补贴的县（市、区、旗）
        Html53Count2024.setTableMapValue("1", "25", Math.toIntExact(count), result);
        // 26落实农村公共服务运行维护支出或服务群众专项经费的县（市、区、旗）
        Html53Count2024.setTableMapValue("1", "26", Math.toIntExact(count), result);
        // 27落实村民小组长误工补贴的县（市、区、旗）
        Html53Count2024.setTableMapValue("1", "27", Math.toIntExact(count), result);

        //2024 H列统计城市社区和乡镇社区 村类别为2异地扶贫搬迁安置区的也要出
        Condition memCondition = noCondition().and(field(name("d04_code"), String.class).in("922", "923", "921"));
        //现任第一书记 2-7
        SelectHavingStep<Record1<Object>> memStep = DSL_CONTEXT.select(field("d04_code,end_date,industry,d08_code,d155_code,has_org_slack_village,org_commit_code,count(1) total")).from(table(name(ccpMemAll)).as("ccp_mem_all"))
                .where(new MemAllCondition2024().create(orgCode, orgLevelCode).and(memCondition.and("d08_code in ('5', '50')"))).groupBy(field("d04_code,end_date,industry,d08_code,d155_code,has_org_slack_village,org_commit_code"));
        List<Record> records3 = EsKit.findBySql(memStep.toString()).toRecord();
        for (Record record : records3) {
            // 5：驻村第一书记  50：工作队员
            String d08Code = record.getStr("d08_code");
            String d04Code = record.getStr("d04_code");
            Integer total = record.getInt("total");
            String d155Code = record.getStr("d155_code");
            String hasOrgSlackVillage = record.getStr("has_org_slack_village");
            // 任职结束时间
            Object endDate = record.getObject("end_date");
            //  选派层级-种数据不会在其他表中统计。所以用这个字段来接收选派层级
            String d197Code =  record.getStr("industry");


//            if (StrUtil.equalsAny(d04Code, "921", "922") ) {
//                if(StrUtil.equals(d08Code, "5") && StrUtil.contains(d155Code, "2")) {
//                    //2 累计选派第一书记 2024新增城市社区和乡镇社区 也要出
//                    Html53Count.setTableMapValue("1", "2", total, result);
//                    // 如果任职时间不为空则下列不需要统计值
//                    if(Objects.nonNull(endDate)){
//                        continue;
//                    }
//                    //3 现任第一书记 2024新增城市社区和乡镇社区 也要出
//                    Html53Count.setTableMapValue("1", "3", total, result);
//                    //8 派驻异地扶贫搬迁安置村的 2024新增城市社区和乡镇社区 村类别为2异地扶贫搬迁安置区的也要出
//                    Html53Count.setTableMapValue("1", "8", total, result);
//                }
//                // 单位类别为社区的 则跳过当前循环
//                continue;
//            }
            // 社区中为派驻异地扶贫搬迁安置村的要累积也要统计
            if (StrUtil.equals(d08Code, "5") && (StrUtil.equals(d04Code, "923") || (StrUtil.equalsAny(d04Code, "921", "922") && StrUtil.contains(d155Code, "2")))) {
                // todo 2024-12-11:  累计选派第一书记这个出数 向夏云松核实 “这个是每年手工处理的，就是去年的数加上今年系统算出来的新选派的”
                //2 累计选派第一书记
                Html53Count2024.setTableMapValue("1", "2", total, result);
                // 如果任职时间不为空则下列不需要统计值
                if(Objects.nonNull(endDate)){
                    continue;
                }
                //3 现任第一书记
                Html53Count2024.setTableMapValue("1", "3", total, result);
                //4 省级选派的
                if (StrUtil.contains(d197Code, "1")) {
                    Html53Count2024.setTableMapValue("1", "4", total, result);
                }
                //5 市级选派的
                if (StrUtil.contains(d197Code, "2")) {
                    Html53Count2024.setTableMapValue("1", "5", total, result);
                }
                //6 县级选派的  (2024乡镇选派也统计进去--王察)
                if (StrUtil.equalsAny(d197Code, "3","4")) {
                    Html53Count2024.setTableMapValue("1", "6", total, result);
                }

                //7 派驻脱贫村的
                if (StrUtil.contains(d155Code, "1")) {
                    Html53Count2024.setTableMapValue("1", "7", total, result);
                }
                //8 派驻异地扶贫搬迁安置村的
                if (StrUtil.contains(d155Code, "2")) {
                    Html53Count2024.setTableMapValue("1", "8", total, result);
                }
                //9 派驻乡村振兴重点村的
                if (StrUtil.containsAny(d155Code, "3", "4")) {
                    Html53Count2024.setTableMapValue("1", "9", total, result);
                }
                //10 派驻党组织软弱涣散村的
                if (StrUtil.equals(hasOrgSlackVillage, "1")) {
                    Html53Count2024.setTableMapValue("1", "10", total, result);
                }
            } else if(StrUtil.equals(d08Code, "50") && (StrUtil.equals(d04Code, "923") || (StrUtil.equalsAny(d04Code, "921", "922") && StrUtil.contains(d155Code, "2")))){

                //11 累计选派工作队员
                Html53Count2024.setTableMapValue("1", "11", total, result);
                // 如果任职时间不为空则下列不需要统计值
                if(Objects.nonNull(endDate)){
                    continue;
                }
                //12 现任工作队员
                Html53Count2024.setTableMapValue("1", "12", total, result);

                //13 省级选派的
                if (StrUtil.contains(d197Code, "1")) {
                    Html53Count2024.setTableMapValue("1", "13", total, result);
                }
                //14 市级选派的
                if (StrUtil.contains(d197Code, "2")) {
                    Html53Count2024.setTableMapValue("1", "14", total, result);
                }
                //15 县级选派的(2024乡镇选派也统计进去--王察)
                if (StrUtil.equalsAny(d197Code, "3","4"))  {
                    Html53Count2024.setTableMapValue("1", "15", total, result);
                }
            }
        }
        Map<String, Map<String, Number>> mapMap = new HashMap<>();
        mapMap.put("table0", result);
        return mapMap;
    }

    @Override
    public Map<String, Object> doPegging(PeggingPara peggingPara) throws Exception {
        String colIndex = peggingPara.getColIndex();
        if (StrUtil.equalsAny(colIndex,  "2", "3","4", "5", "6", "7", "8","9", "10", "11", "12", "13", "14", "15")) {
         Condition condition = new MemAllCondition2024().create(peggingPara.getOrgCode(), peggingPara.getOrgLevelCode())
                 .and(this.getSecretaryCondition(colIndex, null))
                    .and(field(name("d04_code"), String.class).eq("923").or(
                            //或者单位是城市社区和乡镇社区的 村类别为派驻异地扶贫搬迁安置村
                            field(name("d04_code"), String.class).in("922", "921").and("d155_code like '%2%'")
                    ));
            MemAllCondition2024 memAllCondition = new MemAllCondition2024();
            return Html53Count2024.getReportPageResult(peggingPara, memAllCondition.getTableName(), condition, memAllCondition.getLevelCodeField());
        }
        // 村干部基本报酬和村级组织办公经费合计低于11万元的县（市、区、旗）
        if (StrUtil.equalsAny(colIndex, "24", "25", "26", "27")) {
            Condition condition = new Html7Count2024().getOrgListCondition(peggingPara.getOrgCode(), peggingPara.getOrgLevelCode()).and("d01_code like '14%'");
            if (StrUtil.equals(colIndex, "24")) {
                condition = condition.and("1=0");
            }
            OrgAllCondition2024 cond = new OrgAllCondition2024();
            return Html53Count2024.getReportPageResult(peggingPara, cond.getTableName(), condition, cond.getLevelCodeField());
        }

        Condition condition = new Html7Count2024().getUnitListCondition(peggingPara.getOrgCode(), peggingPara.getOrgLevelCode())
                .and(field(name("d04_code"), String.class).eq("923")).and(this.getTableRowCondition(colIndex, false));
        UnitAllCondition2024 cond = new UnitAllCondition2024();
        if (StrUtil.equalsAny(colIndex,  "16", "17","18", "19", "20")) {
            //或者单位是城市社区和乡镇社区的 村类别为派驻异地扶贫搬迁安置村
            condition.or(field(name("d04_code"), String.class).in("922", "921").and("d155_code like '%2%'"));
        }
        return Html53Count2024.getReportPageResult(peggingPara, cond.getTableName(), condition, cond.getLevelCodeField());
    }

    /**
     * 现任第一书记code
     */
    public Set<String> getFirstSecretaryCodes(String orgCode, String orgLevelCode, String tableYear, boolean isBackups) {
        String ccpUnitAll = StrUtil.isEmpty(tableYear) ? "ccp_unit_all" : "ccp_unit_all_" + tableYear;
        Condition condition = noCondition();
        if (isBackups) {
            condition = condition.and("d04_code in('922','923') and first_secretary_code is not null and first_secretary_code !=''");
        } else {
            condition = condition.and("d04_code = '923' and first_secretary_code is not null and first_secretary_code !=''");
        }
        SelectConditionStep<Record1<Object>> records1 = DSL_CONTEXT.select(field("first_secretary_code"))
                .from(table(name(ccpUnitAll)).as("ccp_unit_all")).where(new Html7Count2024().getUnitListCondition(orgCode, orgLevelCode)).and(condition);
        List<Record> records = EsKit.findBySql(records1.toString()).toRecord();
        return records.stream().map(e -> e.getStr("first_secretary_code")).collect(Collectors.toSet());
    }

    Condition getSecretaryCondition(String colIndex, Set<String> firstSecretaryCodes) {
        Condition condition = noCondition();
        if (StrUtil.equalsAny(colIndex, "2", "3", "4", "5", "6", "7", "8", "9", "10")) {
            condition = condition.and("d08_code='5'");
            // 非累任的添加任职时间为空
            if(!"2".equals(colIndex)){
                condition = condition.and("end_date is null");
            }
            if ("2".equals(colIndex)) {
            } else if ("3".equals(colIndex)) {
            } else if ("7".equals(colIndex)) {
                condition = condition.and("d155_code like '%1%'");
            } else if ("8".equals(colIndex)) {
                condition = condition.and("d155_code like '%2%'");
            } else if ("9".equals(colIndex)) {
                condition = condition.and("d155_code like '%3%' or d155_code like '%4%'");
            } else if ("10".equals(colIndex)) {
                condition = condition.and("has_org_slack_village = '1'");
            }

            else if ("4".equals(colIndex)) {
                condition = condition.and(" industry = '1' ");
            }
            else if ("5".equals(colIndex)) {
                condition = condition.and(" industry = '2' ");
            }
            else if ("6".equals(colIndex)) {
                condition = condition.and("  industry in( '3','4') ");
            }
        } else if(StrUtil.equalsAny(colIndex, "11", "12", "13","14","15")) {
            condition = condition.and("d08_code='50'");
            // 非累任的添加任职时间为空
            if(!"11".equals(colIndex)){
                condition = condition.and("end_date is null");
            }
            switch (colIndex){
                case "11":
                    break;
                case "12":
                    break;
                case "13":
                    condition = condition.and(" industry = '1'");
                    break;
                case "14":
                    condition = condition.and(" industry = '2'");
                    break;
                case "15":
                    condition = condition.and("  industry in( '3','4') ");
                    break;
                default:
                    break;
            }
        }else {
            condition = condition.and("1=0");
        }
        return condition;
    }

    Condition getTableRowCondition(String colIndex, boolean isBackups) {
        Condition condition = noCondition();
        if (isBackups && StrUtil.equalsAny(colIndex, "1", "21", "22", "23", "28", "29", "30")) {
            condition = condition.and("d04_code in ('922','923') and is_legal=1");
        }
        if ("1".equals(colIndex)) {
        } else if ("16".equals(colIndex)) {
            condition = condition.and("secretary_training_num is not null and secretary_training_num >0");
        } else if ("17".equals(colIndex)) {
            condition = condition.and("has_thousand is not null and has_thousand >0");
        } else if ("18".equals(colIndex)) {
            condition = condition.and("has_bundled is not null and has_bundled >0");
        } else if ("19".equals(colIndex)) {
            condition = condition.and("promoted_num is not null and promoted_num >0");
        } else if ("20".equals(colIndex)) {
            condition = condition.and("adjusted_num is not null and adjusted_num >0");
        } else if ("21".equals(colIndex)) {
            condition = condition.and("operating_expenses is not null and operating_expenses >0");
        } else if ("22".equals(colIndex)) {
            condition = condition.and("village_per is not null and village_per >0");
        } else if ("23".equals(colIndex)) {
            condition = condition.and("secretary_salary is not null and secretary_salary >0");
        } else if ("28".equals(colIndex)) {
            condition = condition.and("space_area =0");
        } else if ("29".equals(colIndex)) {
            condition = condition.and("space_area is not null and space_area >200");
        } else if ("30".equals(colIndex)) {
            condition = condition.and("new_expand_area is not null and new_expand_area >0");
        } else {
            condition = condition.and("1=0");
        }
        return condition;
    }

    @Override
    public Map<String, Number> doReplenishCount(String reportCode, String orgCode, String orgLevelCode) throws Exception {
        return getReplenishCount(orgCode, orgLevelCode, null, false);
    }

    public Map<String, Number> getReplenishCount(String orgCode, String orgLevelCode, String tableYear, boolean isBackups) throws Exception {
        String ccpUnitAll = StrUtil.isEmpty(tableYear) ? "ccp_unit_all" : "ccp_unit_all_" + tableYear;
        String ccpOrgAll = StrUtil.isEmpty(tableYear) ? "ccp_org_all" : "ccp_org_all_" + tableYear;
        Map<String, Number> result = new LinkedHashMap<>();
        Html53Count2024.initReplenishCol(1, 9, result);
        Condition condition = noCondition();
        if (isBackups) {
            condition = condition.and(field(name("d04_code"), String.class).in("922", "923"));
        } else {
            condition = condition.and(field(name("d04_code"), String.class).eq("923"));
        }
        SelectConditionStep<Record1<Object>> records1 = DSL_CONTEXT.select(field("has_four_two_open_work,has_community_supervisory"))
                .from(table(name(ccpUnitAll)).as("ccp_unit_all")).where(new Html7Count2024().getUnitListCondition(orgCode, orgLevelCode)).and(condition);
        List<Record> records = EsKit.findBySql(records1.toString()).toRecord();
        for (Record record : records) {
            //1.实行“四议两公开”工作法的行政村	个，成立村务监督委员会或其他村务监督机构的行政村	个。
            if (StrUtil.equals(record.getStr("has_four_two_open_work"), "1")) {
                Html53Count2024.setReplenishMapValue("4", 1, result);
            }
            if (StrUtil.equals(record.getStr("has_community_supervisory"), "1")) {
                Html53Count2024.setReplenishMapValue("5", 1, result);
            }
        }
        //从乡镇事业编制人员中选拔乡镇领导干部		人，从到村任职过的选调生中选拔乡镇领导干部		人，从第一书记中选拔乡镇领导干部		人，从驻村工作队员中选拔乡镇领导干部		人。
        SelectConditionStep<Record1<Object>> orgSQL = DSL_CONTEXT.select(field("lead_cadres_towns,village_lead_cadres,first_secretary_cadres,village_leaders"))
                .from(table(name(ccpOrgAll)).as("ccp_org_all")).where(new Html7Count2024().getOrgListCondition(orgCode, orgLevelCode)
                        .and(field(name("d01_code")).like("14%")));
        List<Record> orgList = EsKit.findBySql(orgSQL.toString()).toRecord();
        //lead_cadres_towns
        int sum = orgList.stream().filter(e -> Objects.nonNull(e.getInt("lead_cadres_towns"))).mapToInt(e -> e.getInt("lead_cadres_towns")).sum();
        //village_lead_cadres
        int sum1 = orgList.stream().filter(e -> Objects.nonNull(e.getInt("village_lead_cadres"))).mapToInt(e -> e.getInt("village_lead_cadres")).sum();
        //first_secretary_cadres
        int sum2 = orgList.stream().filter(e -> Objects.nonNull(e.getInt("first_secretary_cadres"))).mapToInt(e -> e.getInt("first_secretary_cadres")).sum();
        //village_leaders
        int sum3 = orgList.stream().filter(e -> Objects.nonNull(e.getInt("village_leaders"))).mapToInt(e -> e.getInt("village_leaders")).sum();

        Html53Count2024.setReplenishMapValue("6", sum, result);
        Html53Count2024.setReplenishMapValue("7", sum1, result);
        Html53Count2024.setReplenishMapValue("8", sum2, result);
        Html53Count2024.setReplenishMapValue("9", sum3, result);
        return result;
    }

    @Override
    public Map<String, Object> doReplenishPegging(PeggingPara peggingPara) throws Exception {
        if (StrUtil.equalsAny(peggingPara.getRowIndex(), "4", "5")) {
            Condition condition = new Html7Count2024().getUnitListCondition(peggingPara.getOrgCode(), peggingPara.getOrgLevelCode())
                    .and(field(name("d04_code"), String.class).eq("923")).and(this.getRowCondition(peggingPara.getRowIndex()));

            UnitAllCondition2024 unitAllCondition = new UnitAllCondition2024();
            return Html53Count2024.getReportPageResult(peggingPara, unitAllCondition.getTableName(), condition, unitAllCondition.getLevelCodeField());
        }
        Condition condition = new Html7Count2024().getOrgListCondition(peggingPara.getOrgCode(), peggingPara.getOrgLevelCode()).and(this.getRow2Condition(peggingPara.getRowIndex()));
        OrgAllCondition2024 orgAllCondition = new OrgAllCondition2024();
        return Html53Count2024.getReportPageResult(peggingPara, orgAllCondition.getTableName(), condition, orgAllCondition.getLevelCodeField());
    }

    Condition getRow2Condition(String rowIndex) {
        Condition condition = noCondition().and("d01_code like '14%'");
        if ("6".equals(rowIndex)) {
            condition = condition.and("lead_cadres_towns is not null and lead_cadres_towns>0");
        } else if ("7".equals(rowIndex)) {
            condition = condition.and("village_lead_cadres is not null and village_lead_cadres>0");
        } else if ("8".equals(rowIndex)) {
            condition = condition.and("first_secretary_cadres is not null and first_secretary_cadres>0");
        } else if ("9".equals(rowIndex)) {
            condition = condition.and("village_leaders is not null and village_leaders>0");
        }
        return condition;
    }

    Condition getRowCondition(String colIndex) {
        Condition condition = noCondition();
        if ("4".equals(colIndex)) {
            condition = condition.and(field(name("has_four_two_open_work"), String.class).eq("1"));
        } else if ("5".equals(colIndex)) {
            condition = condition.and(field(name("has_community_supervisory"), String.class).eq("1"));
        } else {
            condition = condition.and("1=0");
        }
        return condition;
    }


    /**
     * 获取平均数
     */
    public static BigDecimal getAvgCount(BigDecimal sumCount, int count) {
        if (Objects.nonNull(sumCount) && count != 0) {
            return sumCount.divide(BigDecimal.valueOf(count), 2, RoundingMode.HALF_UP);
        }
        return BigDecimal.ZERO;
    }

}
