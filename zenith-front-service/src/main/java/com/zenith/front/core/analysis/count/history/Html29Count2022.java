package com.zenith.front.core.analysis.count.history;

import cn.hutool.core.util.StrUtil;
import com.b1809.analysis.entity.PeggingPara;
import com.zenith.front.core.analysis.ext.condition.year2022.MemAllCondition2022B;
import com.zenith.front.core.analysis.ext.condition.year2022.OrgAllConditionB;
import com.zenith.front.core.analysis.ext.condition.year2022.UnitAllConditionB;
import org.jooq.Condition;

import java.util.HashMap;
import java.util.Map;

import static org.jooq.impl.DSL.noCondition;

/**
 * <AUTHOR>
 * @date 2023/2/10
 */
public class Html29Count2022 {


    public Map<String, Object> getReportPeggingMap29(PeggingPara data) {
        String rowIndex = data.getRowIndex();
        if (StrUtil.equalsAny(data.getRowIndex(), "9", "14", "16")) {
            if (StrUtil.equals(rowIndex, "9")) {
                OrgAllConditionB orgAllCond = new OrgAllConditionB();
                Condition condition = noCondition().and(new Html29CountHistory().getOrg29Condition(data.getOrgCode(), data.getOrgLevelCode())
                        .and(new Html29CountHistory().getOrgCheck29Condition(data.getColIndex())).toString().replace("ccp_org_all", orgAllCond.getTableName()));
                return Html48CountHistory.getReportPageResult(data, orgAllCond.getTableName(), condition, orgAllCond.getLevelCodeField());
            }
            MemAllCondition2022B memAllCond = new MemAllCondition2022B();
            Condition condition = noCondition().and(new Html29CountHistory().getMemCheckListCondition(data.getOrgCode(), data.getOrgLevelCode(), data.getColIndex(), data.getRowIndex()).toString().replace("ccp_mem_all", memAllCond.getTableName()));
            return Html48CountHistory.getReportPageResult(data, memAllCond.getTableName(), condition, memAllCond.getLevelCodeField());
        }

        UnitAllConditionB unitAllCond = new UnitAllConditionB();
        if (StrUtil.equalsAny(data.getRowIndex(), "13")) {
            Condition condition = noCondition().and(new Html29CountHistory().getUnitCheckListCondition(data.getOrgCode(), data.getOrgLevelCode(), data.getColIndex()).toString().replace("ccp_unit_all", unitAllCond.getTableName()));
            return Html48CountHistory.getReportPageResult(data, unitAllCond.getTableName(), condition, unitAllCond.getLevelCodeField());
        }

        if (StrUtil.equalsAny(data.getRowIndex(), "5", "6")) {
            Condition condition = noCondition().and(new Html29CountHistory().getUnitCheckListCondition(data.getOrgCode(), data.getOrgLevelCode(), data.getColIndex()).toString().replace("ccp_unit_all", unitAllCond.getTableName()))
                    .and("is_legal =1 and d02_code='1'");
            if (StrUtil.equals(data.getRowIndex(), "5")) {
                condition = condition.and("d112_code ='1'");
            } else {
                condition = condition.and("d112_code ='2'");
            }
            return Html48CountHistory.getReportPageResult(data, unitAllCond.getTableName(), condition, unitAllCond.getLevelCodeField());
        }
        return new HashMap<>(1);
    }


}
