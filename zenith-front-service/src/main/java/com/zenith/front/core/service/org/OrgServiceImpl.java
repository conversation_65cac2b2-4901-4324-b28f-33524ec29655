package com.zenith.front.core.service.org;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.thread.ThreadUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.extra.pinyin.PinyinUtil;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.b1809.analysis.plugin.elasticsearch.EsKit;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.zenith.front.api.activity.IActivityService;
import com.zenith.front.api.businessModelSync.OrgBusinessModelSyncService;
import com.zenith.front.api.flow.IOrgFlowService;
import com.zenith.front.api.mem.*;
import com.zenith.front.api.org.*;
import com.zenith.front.api.report.IStatisticsYearService;
import com.zenith.front.api.representative.IRepresentativeContactService;
import com.zenith.front.api.representative.IRepresentativeElectService;
import com.zenith.front.api.representative.IRepresentativeService;
import com.zenith.front.api.sync.ISyncMemService;
import com.zenith.front.api.transfer.ITransferRecordService;
import com.zenith.front.api.transfer.ITransfertarViewService;
import com.zenith.front.api.transfer.ITransfrtSrcViewService;
import com.zenith.front.api.unit.IUnitAllService;
import com.zenith.front.api.unit.IUnitExtendService;
import com.zenith.front.api.unit.IUnitOrgLinkedService;
import com.zenith.front.api.unit.IUnitService;
import com.zenith.front.api.user.IUserService;
import com.zenith.front.common.constant.CommonConstant;
import com.zenith.front.common.constant.DictConstant;
import com.zenith.front.common.constant.OrgConstant;
import com.zenith.front.common.constant.TransferRecordConstant;
import com.zenith.front.common.kit.*;
import com.zenith.front.core.cache.CacheUtils;
import com.zenith.front.core.feign.client.VillageCommunityClient;
import com.zenith.front.core.kit.DbUtil;
import com.zenith.front.core.kit.ExcelUtil;
import com.zenith.front.core.service.mem.MemServiceImpl;
import com.zenith.front.core.service.sync.SyncOrgService;
import com.zenith.front.core.service.sync.SyncUnitService;
import com.zenith.front.dao.mapper.fee.FeeOrderMapper;
import com.zenith.front.dao.mapper.mem.MemMapper;
import com.zenith.front.dao.mapper.org.*;
import com.zenith.front.dao.mapper.transfer.TransferRecordMapper;
import com.zenith.front.model.bean.*;
import com.zenith.front.model.custom.Record;
import com.zenith.front.model.custom.UserTicket;
import com.zenith.front.model.dto.*;
import com.zenith.front.model.message.OutMessage;
import com.zenith.front.model.message.Status;
import com.zenith.front.model.modelview.TransFerSrcView;
import com.zenith.front.model.modelview.TransFerTarView;
import com.zenith.front.model.vo.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ArrayUtils;
import org.jooq.Condition;
import org.jooq.Record1;
import org.jooq.SelectConditionStep;
import org.jooq.impl.DSL;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.Executor;
import java.util.concurrent.atomic.AtomicReference;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.zenith.front.common.constant.DBConstant.DSL_CONTEXT;
import static com.zenith.front.common.constant.OrgConstant.*;
import static com.zenith.front.model.constant.UserConstant.USER_CONTEXT;
import static org.jooq.impl.DSL.*;

/**
 * <p>
 * 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-07-10
 */
@Service
@Slf4j
public class OrgServiceImpl extends ServiceImpl<OrgMapper, Org> implements IOrgService {

    public static final HashMap<String, String> d01MappingD05Map = new HashMap<String, String>(10) {
        private static final long serialVersionUID = 1636087242691078154L;

        {
            put("61", "11");//党委,建立党委的
            put("62", "12");//党总支部,建立党总支部的
            put("631", "13");//党支部,建立党支部的
            put("3", "15");
            put("21", "16");//直属机关工委,建立工委的
            put("22", "16");//教育工委,建立工委的
            put("23", "16");//卫健工委,建立工委的
            put("24", "16");//非公有制经济组织和社会组织工委,建立工委的
            put("25", "16");//城市街道工委,建立工委的
            put("26", "16");//国企工委,建立工委的
            put("29", "16");//其他工委,建立工委的
            put("632", "2");//联合党支部,建立联合党支部的
            put("633", "13");
            put("634", "2");//联合党支部（具有三名以上党员的）,建立联合党支部的
            put("911", "11");//临时党委,建立党委的
            put("921", "12");//临时党总支部,建立党总支部的
            put("931", "13");//临时党支部,建立党支部的
            put("932", "2");//临时联合党支部,建立联合党支部的
            put("A", "13");//其他（文件夹）,建立党支部的
        }
    };
    @Resource
    IOrgAllService orgAllService;
    @Resource
    OrgAllMapper orgAllMapper;
    @Resource
    IUnitOrgLinkedService unitOrgLinkedService;
    @Resource
    IUnitAllService unitAllService;
    @Resource
    IUnitExtendService iUnitExtendService;
    @Resource
    IMemService memService;
    @Resource
    IMemManyService memManyService;
    @Resource
    IMemDevelopService memDevelopService;
    @Resource
    IMemFlow1Service memFlow1Service;
    @Resource
    IRepresentativeService representativeService;
    @Resource
    IRepresentativeElectService representativeElectService;
    @Resource
    IRepresentativeContactService representativeContactService;
    @Resource(name = "transferRecordService")
    ITransferRecordService transferRecordService;
    @Resource
    IMemFlowService memFlowService;
    @Resource
    IActivityService activityService;
    @Resource
    HttpServletResponse response;
    @Resource
    IUnitService unitService;
    @Resource
    IOrgCommitteeElectService electService;
    @Resource
    OrgCommitteeElectMapper electMapper;
    @Resource
    IOrgCommitteeService committeeService;
    @Resource
    OrgCommitteeMapper committeeMapper;
    @Resource
    Executor mySimpleAsync;
    @Resource
    ISyncMemService iSyncMemService;
    @Resource
    private ITransfrtSrcViewService transfrtSrcViewService;
    @Resource
    private ITransfertarViewService transfertarViewService;
    @Resource
    private FeeOrderMapper feeOrderMapper;
    @Resource
    private OrgMapper orgMapper;
    @Resource
    private MemMapper memMapper;
    @Resource
    private MemServiceImpl memServiceImpl;
    @Resource
    private OrgRewardMapper orgRewardMapper;
    @Resource
    private IOrgRewardService orgRewardService;
    @Resource
    private TransferRecordMapper transferRecordMapper;
    @Resource
    private SyncOrgService syncOrgService;
    @Resource
    private IMemReportService iMemReportService;
    @Resource
    private SyncUnitService syncUnitService;
    @Resource
    private IOrgGroupService orgGroupService;
    @Resource
    private IOrgAppraisalService orgAppraisalService;
    @Resource
    private IOrgReviewersService orgReviewersService;
    @Resource
    private IOrgRecognitionService orgRecognitionService;
    @Resource
    private IOrgRecognitionDataService orgRecognitionDataService;
    @Resource
    private IOrgRecognitionAllService orgRecognitionAllService;
    @Resource
    private IOrgCaucusService orgCaucusService;
    @Resource
    private IOrgPartyCongressElectService orgPartyCongressElectService;
    @Resource
    private IOrgPartyCongressCommitteeService orgPartyCongressCommitteeService;
    @Resource
    private IOrgPartyCongressCommitteeAllService orgPartyCongressCommitteeAllService;
    @Resource
    private IMemTrainService memTrainService;
    @Resource
    private IOrgTownshipLeadershipService orgTownshipLeadershipService;
    @Resource
    private IOrgNonPublicPartyService orgNonPublicPartyService;
    @Resource
    private IOrgIndustryService orgIndustryService;
    @Resource
    private IOrgIndustryAllService orgIndustryAllService;
    @Resource
    private IOrgSlackService orgSlackService;
    @Resource
    private IOrgSlackAllService orgSlackAllService;
    @Resource
    private IOrgPartyService orgPartyService;
    @Resource
    private IOrgDevelopRightsService orgDevelopRightsService;
    @Resource
    private IMemAllInfoService memAllInfoService;
    @Resource
    private IStatisticsYearService iStatisticsYearService;
    @Value("${sync_flow_push}")
    private String sync_flow_push;
    @Value("${exchange_nginx_key}")
    private String exchange_nginx_key;
    @Resource
    private VillageCommunityClient villageCommunityClient;
    @Resource
    private IUserService iUserService;
    @Resource
    private OrgBusinessModelSyncService orgBusinessModelSyncService;

    @Resource
    private IOrgFlowService orgFlowService;

    @Override
    public Org findOrgByOrgId(String orgId, String... selectFieldName) {
        LambdaQueryWrapper<Org> query = new LambdaQueryWrapper<Org>()
                .eq(Org::getCode, orgId);
        // 只查询返回指定字段信息
        if(selectFieldName.length > 0) {
            query.select(Org.class, et -> {
                for (String s : selectFieldName) {
                    if(et.getProperty().equals(s)) {
                        return true;
                    }
                }
               return false;
            });
        }
        return getOne(query);
    }

    @Override
    public Org findOrgByOrgCode(String orgCode, String... selectFieldName) {
        LambdaQueryWrapper<Org> query = new LambdaQueryWrapper<Org>()
                .eq(Org::getOrgCode, orgCode)
                .isNull(Org::getDeleteTime)
                .select(Org::getName, Org::getShortName, Org::getParentCode, Org::getCode, Org::getOrgCode)
                .last("limit 1");
        // 只查询返回指定字段信息
        if(selectFieldName.length > 0) {
            query.select(Org.class, et -> {
                for (String s : selectFieldName) {
                    if(et.getProperty().equals(s)) {
                        return true;
                    }
                }
                return false;
            });
        }
        return getOne(query);
    }

    @Override
    public Org findByOrgCode(String orgCode, String... selectFieldName) {
        LambdaQueryWrapper<Org> query = new LambdaQueryWrapper<Org>().eq(Org::getOrgCode, orgCode).isNull(Org::getDeleteTime).last("limit 1");

        // 只查询返回指定字段信息
        if(selectFieldName.length > 0) {
            query.select(Org.class, et -> {
                for (String s : selectFieldName) {
                    if(et.getProperty().equals(s)) {
                        return true;
                    }
                }
                return false;
            });
        }
        return getOne(query);
    }

    @Override
    public Org findOrgByCode(String code) {
        LambdaQueryWrapper<Org> query = new LambdaQueryWrapper<Org>()
                .isNull(Org::getDeleteTime)
                .eq(Org::getCode, code)
                .and(q -> q
                        .isNull(Org::getIsDissolve)
                        .or()
                        .ne(Org::getIsDissolve, CommonConstant.ONE_INT)
                );
        return getOne(query);
    }

    @Override
    public Org findOrgByCode(String code, String... selectFieldName) {
        LambdaQueryWrapper<Org> query = new LambdaQueryWrapper<Org>()
                .isNull(Org::getDeleteTime)
                .eq(Org::getCode, code)
                .and(q -> q
                        .isNull(Org::getIsDissolve)
                        .or()
                        .ne(Org::getIsDissolve, CommonConstant.ONE_INT)
                );
        // 只查询返回指定字段信息
        if(selectFieldName.length > 0) {
            query.select(Org.class, et -> {
                for (String s : selectFieldName) {
                    if(et.getProperty().equals(s)) {
                        return true;
                    }
                }
                return false;
            });
        }
        return getOne(query);
    }

    @Override
    public List<OrgAll> findOrgByCodeNotDeleteTime(String code) {
        LambdaQueryWrapper<OrgAll> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(OrgAll::getCode, code)
                .select(OrgAll::getCode, OrgAll::getParentCode, OrgAll::getName,
                        OrgAll::getShortName, OrgAll::getOrgCode, OrgAll::getD01Code, OrgAll::getOrgType,
                        OrgAll::getParentName, OrgAll::getEsId, OrgAll::getZbCode, OrgAll::getPinyin, OrgAll::getContacter, OrgAll::getContactPhone, OrgAll::getDeleteTime);
        return orgAllMapper.selectList(lambdaQueryWrapper);
    }

    @Override
    public Org findOrgByZbCode(String zbCode) {
        LambdaQueryWrapper<Org> query = new LambdaQueryWrapper<Org>()
                .isNull(Org::getDeleteTime)
                .eq(Org::getZbCode, zbCode)
                .last("limit 1");
        return getOne(query);
    }

    @Override
    public List<Org> findOrgByMemOrgCode(List<String> list) {
        LambdaQueryWrapper<Org> lambdaQueryWrapper = new LambdaQueryWrapper<Org>()
                .in(Org::getOrgCode, list)
                .isNull(Org::getDeleteTime)
                .last("ORDER BY LENGTH (org_code)")
                .select(Org::getCode, Org::getName, Org::getShortName, Org::getContacter, Org::getContactPhone);
        return list(lambdaQueryWrapper);
    }

    @Override
    public List<Org> findOrgByParentCode(String orgCode) {
        LambdaQueryWrapper<Org> wrapper = new LambdaQueryWrapper<>();
        wrapper.apply("\"org_code\" like '" + orgCode + "___'")
                .isNull(Org::getDeleteTime)
                .last("ORDER BY length(\"org_code\"),\"create_time\" DESC,\"id\" DESC");
        return list(wrapper);
    }

    @Override
    public List<Org> findByOrgCodeList(Collection<String> orgCodeList,String... selectFieldName) {
        if (CollUtil.isEmpty(orgCodeList)) {
            return Collections.emptyList();
        }
        LambdaQueryWrapper<Org> queryWrapper = new LambdaQueryWrapper<Org>()
                .in(Org::getCode, orgCodeList)
                .and(q -> q
                        .isNull(Org::getIsDissolve)
                        .or()
                        .ne(Org::getIsDissolve, CommonConstant.ONE_INT)
                )
                .isNull(Org::getDeleteTime);

        // 只查询返回指定字段信息
        if(selectFieldName.length > 0) {
            queryWrapper.select(Org.class, et -> {
                for (String s : selectFieldName) {
                    if(et.getProperty().equals(s)) {
                        return true;
                    }
                }
                return false;
            });
        }
        return list(queryWrapper);
    }

    @Override
    public List<String> findOrgCodeListByOrgCode(String orgCode) {
        return list(new LambdaQueryWrapper<Org>()
                .likeRight(Org::getOrgCode, orgCode)
                .and(q -> q
                        .isNull(Org::getIsDissolve)
                        .or()
                        .ne(Org::getIsDissolve, CommonConstant.ONE_INT)
                )
                .in(Org::getD01Code, "61", "62", "631", "632", "633", "634", "911", "921", "931", "932")
                .isNull(Org::getDeleteTime)
                .select(Org::getOrgCode)
        ).stream().map(Org::getOrgCode).collect(Collectors.toList());
    }

    @Override
    public List<String> findJCDWOrgCodeListByOrgCode(String orgCode) {
        return list(new LambdaQueryWrapper<Org>()
                .likeRight(Org::getOrgCode, orgCode)
                .and(q -> q
                        .isNull(Org::getIsDissolve)
                        .or()
                        .ne(Org::getIsDissolve, CommonConstant.ONE_INT)
                )
                .eq(Org::getD01Code, "61")
                .isNull(Org::getDeleteTime)
                .select(Org::getOrgCode)
        ).stream().map(Org::getOrgCode).collect(Collectors.toList());
    }

    @Override
    public Page<Org> findOrgPageByOrgCode(Integer pageNum, Integer pageSize, String orgCode) {
        Page<Org> page = new Page<>(pageNum, pageSize);
        return page(page, new LambdaQueryWrapper<Org>()
                .likeRight(Org::getOrgCode, orgCode)
                .and(q -> q
                        .isNull(Org::getIsDissolve)
                        .or()
                        .ne(Org::getIsDissolve, CommonConstant.ONE_INT)
                )
                .in(Org::getD01Code, "61", "62", "631", "632", "633", "634", "911", "921", "931", "932")
                .isNull(Org::getDeleteTime)
                .orderByAsc(Org::getOrgCode)
                .select(Org::getName, Org::getD01Code, Org::getContacter, Org::getContactPhone, Org::getSecretary, Org::getOrgCode)
        );
    }

    @Override
    public List<Org> findOrgListByOrgCode(String orgCode) {
        return list(new LambdaQueryWrapper<Org>()
                .likeRight(Org::getOrgCode, orgCode)
                .and(q -> q
                        .isNull(Org::getIsDissolve)
                        .or()
                        .ne(Org::getIsDissolve, CommonConstant.ONE_INT)
                )
                .in(Org::getD01Code, "61", "62", "631", "632", "633", "634", "911", "921", "931", "932")
                .isNull(Org::getDeleteTime)
                .select(Org::getName, Org::getD01Code, Org::getContacter, Org::getContactPhone, Org::getSecretary, Org::getCode)
        );
    }

    @Override
    public boolean batchUpdate(List<Org> orgList) {
        return updateBatchById(orgList);
    }

    @Override
    public List<Org> findOrgByName(String manOrgCode, String name) {
        LambdaQueryWrapper<Org> wrapper = new LambdaQueryWrapper<Org>()
                .likeRight(Org::getOrgCode, manOrgCode)
                .isNull(Org::getDeleteTime)
                .and(q -> q.like(Org::getName, name).or().like(Org::getShortName, name))
                .and(q -> q
                        .isNull(Org::getIsDissolve)
                        .or()
                        .ne(Org::getIsDissolve, CommonConstant.ONE_INT))
                .last("limit 100");
        return list(wrapper);
    }

    @Override
    public Org findOrgMaxCodeByParentCode(String parentOrgCode) {
        LambdaQueryWrapper<Org> wrapper = new LambdaQueryWrapper<Org>()
                .apply("\"org_code\" like '" + parentOrgCode + "___'")
                // .isNull(Org::getDeleteTime) // todo: tanmw 2025/1/17 16:56 适配流动党组织
                .last("ORDER BY substring(org_code,length('" + parentOrgCode + "')) desc limit 1");
        return getOne(wrapper);
    }

    /**
     * 获取组织树
     *
     * @param orgTreeDTO 获取组织树的code
     * @return
     */
    @Override
    public OutMessage getOrgTree(OrgTreeDTO orgTreeDTO) {
        // 获取集合
        List<String> orgCodeList = orgTreeDTO.getOrgCodeList();
        String manOrgCode = this.getUserManOrgCode();
        if (CollectionUtil.isEmpty(orgCodeList)) {
            orgCodeList = new ArrayList<>();
            orgCodeList.add(manOrgCode);
        }
        // 排除集合
        List<String> excludeOrgCodeList = orgTreeDTO.getExcludeOrgCodeList();
//        CompletableFuture<List<Map<String, String>>> completableFuture = null;
        //是否显示党员数
//        String dtoDisplay = orgTreeDTO.getDisplay();
//        if (StrUtil.isNotBlank(dtoDisplay)) {
//            Boolean display = StringToBooleanConverter.convert(dtoDisplay);
//            if (Boolean.TRUE.equals(display)) {
//                //异步执行
//                final List<String> finalOrgCodeList = orgCodeList;
//                completableFuture = CompletableFuture.supplyAsync(() -> memMapper.groupByOrgCode(finalOrgCodeList, excludeOrgCodeList), mySimpleAsync);
//            }
//        }
        // 是否权限检验
        String isPermissionCheck = orgTreeDTO.getIsPermissionCheck();
        LambdaQueryWrapper<Org> wrapper = new LambdaQueryWrapper<>();
        wrapper.select(Org.class,org->{;
            if (org.getProperty().equals("secretary")){
                return false;
            }else if(org.getProperty().equals("contacter")){
                return false;
            }else if (org.getProperty().equals("contactPhone")){
                return false;
            }else {;
                return true;
            }
        });
        wrapper.isNull(Org::getDeleteTime);
        wrapper.and(queryWrapper -> queryWrapper
                .isNull(Org::getIsDissolve)
                .or()
                .ne(Org::getIsDissolve, CommonConstant.ONE_INT)
        );
        List<String> conditionOrgList = new ArrayList<>();
        for (String orgCode : orgCodeList) {
            if (!(StrKit.notBlank(isPermissionCheck) && CommonConstant.ZERO.equals(isPermissionCheck))) {
                if (!orgCode.startsWith(manOrgCode)) {
                    return new OutMessage<>(Status.PERMISSION_DENIED);
                }
            }
            // 查询当前节点及下一级节点
            conditionOrgList.add("\"org_code\"='" + orgCode + "' or \"org_code\" like '" + orgCode + "___'");
        }
        wrapper.and(w -> w.apply(CollUtil.isNotEmpty(conditionOrgList), CollUtil.join(conditionOrgList, " or ")));
        // 排除集合
        if (CollectionUtil.isNotEmpty(excludeOrgCodeList)) {
            for (String excludeOrgCode : excludeOrgCodeList) {
                wrapper.apply("\"org_code\" not like '" + excludeOrgCode + "%'");
            }
        }
        wrapper.last("ORDER BY length(\"org_code\"),sort,\"create_time\" DESC,\"id\" DESC");
        List<Org> orgList = list(wrapper);
//        if (StrUtil.isNotBlank(dtoDisplay)) {
//            Boolean display = StringToBooleanConverter.convert(dtoDisplay);
//            if (Boolean.TRUE.equals(display) && Objects.nonNull(completableFuture)) {
//                //获取异步执行的结果
//                List<Map<String, String>> groupingResultsMapList = completableFuture.get();
//                //遍历求和
//                for (Org org : orgList) {
//                    int memNumber = groupingResultsMapList.stream().filter(map -> StrUtil.startWith(map.get("mem_org_code"), org.getOrgCode())).mapToInt(map -> Integer.parseInt(String.valueOf(map.get("count")))).sum();
//                    org.setMemNumber(memNumber);
//                }
//            }
//        }
        orgList.forEach(s -> {
            s.setIsFlowStatus("0");
        });
        //新增组织树显示流动党组织信息
        if (orgTreeDTO.getIsFlowStatus().equals("1")) {
            orgList.addAll(orgFlowService.getOrgFlowTree(orgTreeDTO));
        }
        return new OutMessage<>(Status.SUCCESS, orgList);
    }

    @Override
    public List<Org> getOrgTreeData(OrgTreeDTO orgTreeDTO) {
        // 获取集合
        List<String> orgCodeList = orgTreeDTO.getOrgCodeList();
        String manOrgCode = this.getUserManOrgCode();
        if (CollectionUtil.isEmpty(orgCodeList)) {
            orgCodeList = new ArrayList<>();
            orgCodeList.add(manOrgCode);
        }
        // 排除集合
        List<String> excludeOrgCodeList = orgTreeDTO.getExcludeOrgCodeList();
        LambdaQueryWrapper<Org> wrapper = new LambdaQueryWrapper<>();
        wrapper.isNull(Org::getDeleteTime);
        wrapper.and(queryWrapper -> queryWrapper
                .isNull(Org::getIsDissolve)
                .or()
                .ne(Org::getIsDissolve, CommonConstant.ONE_INT)
        );
        List<String> conditionOrgList = new ArrayList<>();
        for (String orgCode : orgCodeList) {
            // 查询当前节点及下一级节点
            conditionOrgList.add("\"org_code\"='" + orgCode + "' or \"org_code\" like '" + orgCode + "___'");
        }
        wrapper.and(w -> w.apply(CollUtil.isNotEmpty(conditionOrgList), CollUtil.join(conditionOrgList, " or ")));
        // 排除集合
        if (CollectionUtil.isNotEmpty(excludeOrgCodeList)) {
            for (String excludeOrgCode : excludeOrgCodeList) {
                wrapper.apply("\"org_code\" not like '" + excludeOrgCode + "%'");
            }
        }
        wrapper.last("ORDER BY length(\"org_code\"),sort,\"create_time\" DESC,\"id\" DESC");

        return list(wrapper);
    }

    /**
     * 按名称搜索组织树
     *
     * @param name
     * @return
     */
    @Override
    public OutMessage findTreeByName(String name) {
        String manOrgCode = this.getUserManOrgCode();
        List<Org> orgList = this.findOrgByName(manOrgCode, name);
        return new OutMessage<>(Status.SUCCESS, orgList);
    }

    /**
     * 获取组织列表
     * // TODO: 2019/4/12 查询字典表
     *
     * @param orgListDTO
     * @return
     */
    @Override
    public OutMessage getList(OrgListDTO orgListDTO) {
        String orgCode = orgListDTO.getOrgCode();
        // 组织名称
        String orgName = orgListDTO.getOrgName();

        // TODO: 2021/9/28 是否显示下级 1 显示 0 不显示
        String subordinate = orgListDTO.getSubordinate();
        Boolean convert = StringToBooleanConverter.convert(subordinate);
        LambdaQueryWrapper<OrgAll> wrapper = new LambdaQueryWrapper<>();
        //todo 修改加密情况，处理密评相关慢的问题
        wrapper.select(OrgAll::getId,OrgAll::getCode,OrgAll::getOrgCode,OrgAll::getName,OrgAll::getShortName,
                OrgAll::getD01Code,OrgAll::getD01Name,OrgAll::getSecretary,OrgAll::getContacter,OrgAll::getCreateDate);
        if (Boolean.TRUE.equals(convert) || StrKit.notBlank(orgName)) {
            // 所有下级
            wrapper.likeRight(OrgAll::getOrgCode, orgCode);
        } else {
            //自己
            wrapper.eq(OrgAll::getOrgCode, orgCode);
        }
        return this.getListCondition(orgListDTO, wrapper, "ORDER BY length(\"org_code\"),sort,\"create_time\" DESC,\"id\" DESC");
    }

    /**
     * 获取组织列表
     * // TODO: 2019/4/12 查询字典表
     *
     * @param orgListDTO
     * @return
     */
    @Override
    public OutMessage getTransferList(OrgListDTO orgListDTO) {
        LambdaQueryWrapper<OrgAll> wrapper = new LambdaQueryWrapper<>();
        wrapper.isNull(OrgAll::getDeleteTime);
        return this.getListCondition(orgListDTO, wrapper, "ORDER BY length(\"org_code\"),sort,\"create_time\" DESC,\"id\" DESC");
    }

    /**
     * 新增组织
     *
     * @param orgDTO
     * @return
     */
    @Override
    public OutMessage addOrg(OrgDTO orgDTO) throws Exception {

        // 前端如果给的空 字符串则置为null，防止覆盖；情况1：组织关联单位没有国民经济
        if (StrUtil.isBlank(orgDTO.getD194Code())) {
            orgDTO.setD194Code(null);
            orgDTO.setD194Name(null);
            orgDTO.setD195Code(null);
            orgDTO.setD195Name(null);
        }

        String parentCode = orgDTO.getParentCode();
        Org parentOrg = this.findOrgByCode(parentCode);
        OutMessage outMessage = getOutMessageSaveOrUpdate(orgDTO, parentOrg);
        if (outMessage != null) {
            return outMessage;
        }
        //1	法人单位（独立单位）
        //2 与上级党组织相同
        //3 联合党支部

        String d02Code = orgDTO.getD02Code();
        String d01Code = orgDTO.getD01Code();

        //this.checkD195(orgDTO,d02Code,parentOrg.getCode());

        boolean isBig = StrUtil.startWithAny(d01Code, "1", "2");
        boolean b2 = StrUtil.startWithAny(d01Code, "632", "932", "634");
        //如果不是1和2开头，就需要看d01的属性
        if (!isBig) {
            //如果是联合党支部，或者是临时联合党支部，必须是联合党支部类型
            if (b2 && !StrUtil.equalsAny(d02Code, "3", "4")) {
                return new OutMessage(Status.UNION_DZB_MUST_THREE);
            }
            //如果是党委和党总支以及党支部只能选择与上级相同或者是独立法人单位
            if (!b2 && "3".equals(d02Code)) {
                return new OutMessage(Status.UNION_DZB_MUST_SIX);
            }
        } else {
            //如果是1和2开头的,关联单位情况不允许选择
            if (StrUtil.startWithAny(d02Code, "1", "2", "3", "4") && !StrUtil.equalsAny(d01Code, "25")) {
                return new OutMessage(Status.UNION_DZB_MUST_Five);
            }
        }

        String tempUnitCode = getOrgMainUnitCode(orgDTO);
        if (StrUtil.isNotEmpty(tempUnitCode)) {
            // 上面都是校验代码
            //2023年统修改新增
            Unit tempUnit = unitService.findByCode(tempUnitCode);
            orgDTO.setMainUnitCode(tempUnit.getCode());
            // 是否离退休, true-是 false-否
            boolean isRetireBol = Objects.nonNull(orgDTO.getIsRetire()) && Objects.equals(orgDTO.getIsRetire(), 1);
            Org tempOrg = new Org();
            tempOrg.setD02Code(orgDTO.getD02Code());
            // 前端未传值时从单位获取
            // 是离退休  前端也没有传国民经济值（部分情况党组织可以修改维护国民经济），从新的单位获取国民经济
            if (isRetireBol || (ObjectUtil.isNotNull(tempUnit) && StrUtil.isBlank(orgDTO.getD194Code()))) {
                syncOrgService.org194And195Logic(tempOrg, false, isRetireBol, true, tempUnit);
                orgDTO.setD194Code(tempOrg.getD194Code());
                orgDTO.setD194Name(tempOrg.getD194Name());
                orgDTO.setD195Code(tempOrg.getD195Code());
                orgDTO.setD195Name(tempOrg.getD195Name());
            }
        } else {
            // 没有单位情况
            // 属于离退休
            if (Objects.equals(orgDTO.getIsRetire(), 1)) {
                orgDTO.setD194Code("U");
                orgDTO.setD194Name("其他");
                orgDTO.setD195Code("V0000");
                orgDTO.setD195Name("无");
            } else {
                // 非离退休
                orgDTO.setD194Code("");
                orgDTO.setD194Name("");
                orgDTO.setD195Code("");
                orgDTO.setD195Name("");
            }
        }

        orgDTO.setCode(StrKit.getRandomUUID());
        orgDTO.setZbCode(CodeUtil.getZbCode());
        orgDTO.setEsId(CodeUtil.getEsId());
        orgDTO.setIsLeaf(CommonConstant.ONE_INT);
        orgDTO.setPinyin(PinyinUtil.getPinyin(orgDTO.getName()));
        orgDTO.setUpdateAccount(USER_CONTEXT.get().getUser().getAccount());
        orgDTO.setIsHistory(CommonConstant.ZERO_INT);
        // 获取组织大类
        String orgType = this.transformToOrgType(orgDTO.getD01Code());
        orgDTO.setOrgType(orgType);

        // 生成层级码
        String orgCode = this.createOrgCode(parentOrg);
        // 判断父积是否是叶子节点
        Integer isLeaf = parentOrg.getIsLeaf();
        if (isLeaf.equals(CommonConstant.ONE_INT)) {
            // 是叶子节点
            parentOrg.setIsLeaf(CommonConstant.ZERO_INT);
            updateById(parentOrg);
        }

        orgDTO.setOrgCode(orgCode);
        // 属性拷贝
        Org org = new Org();
        ModelUtils.copyPropertiesExclude(orgDTO, org, new String[]{"linkedDTOList", "collectiveEconomy"});
        if (CollectionUtil.isNotEmpty(orgDTO.getCollectiveEconomy())) {
            String s = JackSonUtil.objectToString(orgDTO.getCollectiveEconomy());
            org.setCollectiveEconomy(s);
        }
        org.setId(null);
        org.setCreateTime(new Date());
        org.setUpdateTime(new Date());
        org.setTimestamp(new Date());

        OrgAll orgAll = new OrgAll();
        ModelUtils.copyPropertiesExclude(orgDTO, orgAll, new String[]{"linkedDTOList", "collectiveEconomy"});
        orgAll.setParentName(parentOrg.getName());
        // 设置字典表转换
        this.setOrgDict(orgAll);

        OutMessage outMessage1 = this.OrgLinkedUnit(orgDTO, orgAll);
        if (outMessage1 != null) {
            return outMessage1;
        }
        //处理到村任职补助经费使用率字段
        org.setToTheVillageOfficeSubsidyFundsUtilizationRate(StrUtil.startWith(d01Code, "14") ? orgDTO.getToTheVillageOfficeSubsidyFundsUtilizationRate() : null);
        boolean b = this.save(org);
        //设置第几产业
        //setIndustry(org,orgAll);
        boolean b1 = orgAllService.save(orgAll);
        orgDTO.setAdd(true);
        List<UnitOrgLinked> unitOrgLinkedList = this.getUnitOrgLinkedList(orgDTO);
        if (CollUtil.isNotEmpty(unitOrgLinkedList)) {
            boolean save = unitOrgLinkedService.batchSave(unitOrgLinkedList);
            if (save) {
                for (UnitOrgLinked orgLinked : unitOrgLinkedList) {
                    syncUnitService.setPartyBranchesNumber(orgLinked.getUnitCode());
                }
            }
        }
        if (b && b1) {
            ThreadUtil.execAsync(() -> {
                syncOrgService.setBaseOrg(org.getCode(), CommonConstant.FOUR);
                syncUnitService.setUnitByUnitOrg(org.getCode());
                syncOrgService.setOrgByOrgCode(org.getCode(), true);
                //todo 增加新增组织的时候，实时同步
                this.initExchangeOrg(org.getCode());
                // TODO: 2022/1/12 增加处理村社区相关信息同步
                syncOrgService.setOrgSyncUnit(org.getCode());
                // TODO: 2023/2/28 同步到驻村系统
                syncVillageCommunityOrg(orgAll);
            });
        }
        return new OutMessage<>(b && b1 ? Status.SUCCESS : Status.FAIL, org);
    }

    /**
     * 根据code查找组织
     *
     * @param code
     * @return
     */
    @Override
    public OutMessage findByCode(String code) throws Exception {
        Org org = this.findOrgByCode(code);
        if (Objects.isNull(org)) {
            return new OutMessage<>(Status.ORG_NOT_EXIST);
        }
        if (!org.getOrgCode().startsWith(this.getUserManOrgCode())) {
            // 权限不足
            return new OutMessage<>(Status.PERMISSION_DENIED);
        }
        String parentCode = org.getParentCode();
        org.setParentOrgName(CacheUtils.getOrgNameWithNull(parentCode));
        String d02Code = org.getD02Code();
        List<String> orgCodeList = new ArrayList<>();
        orgCodeList.add(org.getCode());
        if (StrUtil.equals("4", d02Code)) {
            //递归查询本级到上级的所有组织
            List<Org> orgList = this.fromSonToFather(org.getCode());
            String superOrgCode = orgList.stream().filter(dbOrg -> !StrUtil.equals(dbOrg.getCode(), org.getCode()) && !"2".equals(dbOrg.getD02Code())).findFirst().map(Org::getCode).orElse(null);
            if (StrUtil.isNotBlank(superOrgCode)) {
                orgCodeList.add(superOrgCode);
            }
        }
        List<UnitOrgLinked> unitOrgLinkeds = unitOrgLinkedService.findByOrgCodeList(orgCodeList);
        List<UnitOrgLinkedDTO> unitOrgLinkedDTOList = new ArrayList<>();
        List<UnitOrgLinkedDTO> unitTopOrgLinkedDTOList = new ArrayList<>();
        if (CollectionUtil.isEmpty(unitOrgLinkeds) && org.getD01Code().startsWith("63")) {
            if ("2".equals(d02Code)) {
                Org topOrg = new Org();
                this.getTopOrg(org.getParentCode(), topOrg);
                List<UnitOrgLinked> unitOrgLinkedList = unitOrgLinkedService.findByOrgCode(topOrg.getCode());
                List<String> unitCodeList = unitOrgLinkedList.stream().map(UnitOrgLinked::getUnitCode).filter(StrUtil::isNotEmpty).collect(Collectors.toList());
                if (CollectionUtil.isNotEmpty(unitCodeList)) {
                    List<Unit> unitList = unitService.findByCodes(unitCodeList);
                    Map<String, Unit> unitMap = unitList.stream().collect(Collectors.toMap(Unit::getCode, value -> value, (key1, key2) -> key1));
                    for (UnitOrgLinked unitOrgLinked : unitOrgLinkedList) {
                        if (unitMap.containsKey(unitOrgLinked.getUnitCode())) {
                            UnitOrgLinkedDTO unitOrgLinkedDTO = new UnitOrgLinkedDTO();
                            ModelUtils.copyPropertiesToVO(unitOrgLinked, unitOrgLinkedDTO);
                            unitOrgLinkedDTO.setUnit(unitMap.get(unitOrgLinked.getUnitCode()));
                            unitTopOrgLinkedDTOList.add(unitOrgLinkedDTO);
                        }
                    }
                }
            }
        } else {
            if (CollectionUtil.isNotEmpty(unitOrgLinkeds)) {
                List<String> unitCodeList = unitOrgLinkeds.stream().map(UnitOrgLinked::getUnitCode).filter(StrUtil::isNotEmpty).distinct().collect(Collectors.toList());
                if (CollectionUtil.isNotEmpty(unitCodeList)) {
                    List<Unit> unitList = unitService.findByCodes(unitCodeList);
                    Map<String, Unit> unitMap = unitList.stream().collect(Collectors.toMap(Unit::getCode, value -> value, (key1, key2) -> key1));
                    // 单位扩展信息表
                    List<UnitExtend> extendList = iUnitExtendService.findAnyByCodes(unitCodeList);
                    Map<String, UnitExtend> unitExtendMap = extendList.stream().collect(Collectors.toMap(UnitExtend::getCode, value -> value, (key1, key2) -> key1));
                    //过滤除本级以外的单位code
                    Set<String> unitCodeSet = unitOrgLinkeds.stream().filter(t -> !t.getOrgCode().equals(org.getCode())).map(UnitOrgLinked::getUnitCode).collect(Collectors.toSet());
                    //当上级组织和本级组织都有同一个关联时,去重处理
                    List<UnitOrgLinked> distinctUnitOrgLinkedList = new ArrayList<>(unitOrgLinkeds.stream().collect(
                            Collectors.toMap(UnitOrgLinked::getUnitCode, Function.identity(), (e1, e2) -> e2, LinkedHashMap::new)
                    ).values());
                    for (UnitOrgLinked unitOrgLinked : distinctUnitOrgLinkedList) {
                        if (unitMap.containsKey(unitOrgLinked.getUnitCode())) {
                            UnitOrgLinkedDTO unitOrgLinkedDTO = new UnitOrgLinkedDTO();
                            ModelUtils.copyPropertiesToVO(unitOrgLinked, unitOrgLinkedDTO);
                            if (unitCodeSet.contains(unitOrgLinked.getUnitCode())) {
                                //有上级单位code时
                                unitOrgLinkedDTO.setSelf(false);
                                //有上级单位关联关系时,默认不是主单位
                                if (!StrUtil.equals(unitOrgLinked.getOrgCode(), org.getCode())) {
                                    unitOrgLinkedDTO.setIsUnitMain(0);
                                }
                            } else if (!StrUtil.equals(unitOrgLinked.getOrgCode(), org.getCode())) {
                                //无上级单位code并且不是本级
                                unitOrgLinkedDTO.setIsUnitMain(0);
                                unitOrgLinkedDTO.setSelf(false);
                            } else {
                                unitOrgLinkedDTO.setSelf(true);
                            }
                            unitOrgLinkedDTO.setUnit(unitMap.get(unitOrgLinked.getUnitCode()));
                            // 获取扩展信息表相关信息
                            UnitExtend unitExtend = unitExtendMap.get(unitOrgLinked.getUnitCode());
                            if (Objects.nonNull(unitExtend)) {
                                unitOrgLinkedDTO.setD16Code(unitExtend.getD16Code());
                                unitOrgLinkedDTO.setD114Code(unitExtend.getD114Code());
                            }
                            unitOrgLinkedDTOList.add(unitOrgLinkedDTO);
                        }
                    }
                }
            }
        }
        OrgVO orgVO = new OrgVO();
        BeanUtils.copyProperties(org, orgVO);
        orgVO.setLinkedDTOList(unitOrgLinkedDTOList);
        orgVO.setLinkedDTOListUpOrg(unitTopOrgLinkedDTOList);
        orgVO.setParentOrgName(CacheUtils.getOrgNameWithNull(parentCode));
        orgVO.setLockFields(Convert.toList(String.class, org.getLockFields()));
        Org parentD01 = this.findOrgByCode(parentCode);
        if (Objects.nonNull(parentD01)) {
            orgVO.setParentD01Code(parentD01.getD01Code());
        }
        if (StrUtil.isNotEmpty(org.getCollectiveEconomy())) {
            List<AssociatedOrganizationVO> economy = JackSonUtil.jsonToList(org.getCollectiveEconomy(), AssociatedOrganizationVO.class);
            orgVO.setCollectiveEconomy(economy);
        }
        return new OutMessage<>(Status.SUCCESS, orgVO);
    }

    /**
     * 编辑组织
     *
     * @param orgDTO
     * @return
     */
    @Override
    public OutMessage updateOrg(OrgDTO orgDTO) throws Exception {
        String code = orgDTO.getCode();
        // 前端如果给的空 字符串则置为null，防止覆盖；情况1：组织关联单位没有国民经济
        if (StrUtil.isBlank(orgDTO.getD194Code())) {
            orgDTO.setD194Code(null);
            orgDTO.setD194Name(null);
            orgDTO.setD195Code(null);
            orgDTO.setD195Name(null);
        }

        Org org = this.findOrgByCode(code);
        if (ObjectUtil.isNull(org)) {
            // 组织不存在
            return new OutMessage<>(Status.ORG_NOT_EXIST);
        }
        //1	法人单位（独立单位）
        //2 与上级党组织相同
        //3 联合党支部
        // TODO: 2021/11/29 增加关于关联单位情况逻辑判断
        String dbD02Code = org.getD02Code();
        String d02Code = orgDTO.getD02Code();
        String d01Code = orgDTO.getD01Code();

        // TODO: 2023/12/5 增加国民经济前端兼容后端的值
        //this.checkD195(orgDTO,d02Code,org.getParentCode());

        if (StrUtil.isEmpty(org.getD01Code())) {
            if (!StrUtil.equalsAny(d01Code, "631", "632", "633", "634", "931", "932")) {
                List<Mem> memList = memMapper.selectList(
                        new QueryWrapper<Mem>().lambda()
                                .eq(Mem::getOrgCode, org.getCode())
                                // TODO: 2021/12/2  修改组织时查询人员时未添加党员类型 d08Code
                                .in(Mem::getD08Code, Arrays.asList("1", "2"))
                                .isNull(Mem::getDeleteTime)
                );
                if (CollectionUtil.isNotEmpty(memList)) {
                    return new OutMessage<>(Status.THERE_PARTY_MEMBERS_IN_THE_CURRENT_ORGANIZATION);
                }
            }
            if (StrUtil.equalsAny(d01Code, "631", "632", "633", "634", "931", "932")) {
                List<Org> orgList = orgMapper.findOrgByParentCode(org.getOrgCode());
                if (CollectionUtil.isNotEmpty(orgList)) {
                    return new OutMessage<>(Status.THERE_ORG_IN_THE_CURRENT_ORGANIZATION);
                }
            }
        } else {
            //判断原组织类别是党支部，联合党支部，临时联合党支部被改成非党支部，联合党支部，临时联合党支部
            if (org.getD01Code().startsWith("63") || "931".equals(org.getD01Code()) || "932".equals(org.getD01Code())) {
                if (!StrUtil.equalsAny(d01Code, "631", "632", "633", "634", "931", "932")) {
                    List<Mem> memList = memMapper.selectList(
                            new QueryWrapper<Mem>().lambda()
                                    // TODO: 2021/12/16 修改组织时查询人员时未添加党员类型 d08Code
                                    .in(Mem::getD08Code, Arrays.asList("1", "2"))
                                    .eq(Mem::getOrgCode, org.getCode())
                                    .isNull(Mem::getDeleteTime));
                    if (CollectionUtil.isNotEmpty(memList)) {
                        return new OutMessage<>(Status.THERE_PARTY_MEMBERS_IN_THE_CURRENT_ORGANIZATION);
                    }
                    // TODO: 2022/12/23 党支部修改成党总支时，需要判断 党员，入党申请人，积极分子转出，积极分子转入，流动党员，关系转出，关系转入
                    int memDevelopCount = memDevelopService.count(
                            new LambdaQueryWrapper<MemDevelop>()
                                    .eq(MemDevelop::getOrgCode, org.getCode())
                                    .isNull(MemDevelop::getDeleteTime)
                                    .isNull(MemDevelop::getDeadTime)
                                    .in(MemDevelop::getD08Code, "3", "4", "5")
                    );
                    if (memDevelopCount > 0) {
                        return new OutMessage<>(Status.THERE_JOIN_THE_PARTY_IN_THE_CURRENT_ORGANIZATION);
                    }
                    int memFlowCount = memFlow1Service.count(
                            new LambdaQueryWrapper<MemFlow1>()
                                    .in(MemFlow1::getFlowStep, "1", "2", "3")
                                    .and(t -> t.likeRight(MemFlow1::getMemOrgOrgCode, org.getOrgCode()))
                                    .or()
                                    .likeRight(MemFlow1::getOutOrgBranchOrgCode, org.getOrgCode())
                    );
                    if (memFlowCount > 0) {
                        return new OutMessage<>(Status.THERE_FLOW_PARTY_MEMBERS_IN_THE_CURRENT_ORGANIZATION);
                    }
                    int transFerSrcViewCount = transfrtSrcViewService.count(
                            new LambdaQueryWrapper<TransFerSrcView>()
                                    .eq(TransFerSrcView::getStatus, CommonConstant.ZERO_INT)
                                    .ne(TransFerSrcView::getType, CommonConstant.TWENTY_NINE)
                                    .likeRight(TransFerSrcView::getOrgCode, org.getOrgCode())
                    );
                    if (transFerSrcViewCount > 0) {
                        return new OutMessage<>(Status.THERE_TRANSFER_OUT_IN_THE_CURRENT_ORGANIZATION);
                    }
                    int transFerTarViewCount = transfertarViewService.count(
                            new LambdaQueryWrapper<TransFerTarView>()
                                    .eq(TransFerTarView::getStatus, CommonConstant.ZERO_INT)
                                    .ne(TransFerTarView::getType, CommonConstant.TWENTY_NINE)
                                    .likeRight(TransFerTarView::getOrgCode, org.getOrgCode())
                    );
                    if (transFerTarViewCount > 0) {
                        return new OutMessage<>(Status.THERE_TRANSFER_IN_IN_THE_CURRENT_ORGANIZATION);
                    }
                }
            }
            //判断原组织是非党支部，联合党支部，临时联合党支部被改成党支部，联合党支部，临时联合党支部时
            if (!org.getD01Code().startsWith("63") || !"931".equals(org.getD01Code()) || !"932".equals(org.getD01Code())) {
                if (StrUtil.equalsAny(d01Code, "63", "631", "632", "633", "634", "931", "932")) {
                    List<Org> orgList = orgMapper.findOrgByParentCode(org.getOrgCode());
                    if (CollectionUtil.isNotEmpty(orgList)) {
                        return new OutMessage<>(Status.THERE_ORG_IN_THE_CURRENT_ORGANIZATION);
                    }
                }
            }
        }
        boolean isBig = StrUtil.startWithAny(d01Code, "1", "2");
        boolean b2 = StrUtil.startWithAny(d01Code, "632", "932", "634");
        //如果不是1和2开头，就需要看d01的属性
        if (!isBig) {
            //如果是联合党支部，或者是临时联合党支部，必须是联合党支部类型
            if (b2 && !StrUtil.equalsAny(d02Code, "3", "4")) {
                return new OutMessage(Status.UNION_DZB_MUST_THREE);
            }
            //如果是党委和党总支以及党支部只能选择与上级相同或者是独立法人单位
            if (!b2 && "3".equals(d02Code)) {
                return new OutMessage(Status.UNION_DZB_MUST_SIX);
            }
        } else {
            //如果是1和2开头的,关联单位情况不允许选择
            if (StrUtil.startWithAny(d02Code, "1", "2", "3", "4") && !StrUtil.equalsAny(d01Code, "25")) {
                return new OutMessage(Status.UNION_DZB_MUST_Five);
            }
        }
        // 上面的都是校验代码

        String tempUnitCode = getOrgMainUnitCode(orgDTO);
        boolean mainUniCodeAccordBol = false;
        if (StrUtil.isNotEmpty(tempUnitCode)) {
            Unit tempUnit = unitService.findByCode(tempUnitCode);
            final String oldMainUnitCode = org.getMainUnitCode();
            final Integer oldIsRetire = org.getIsRetire();
            final Integer isRetire = orgDTO.getIsRetire();
            final String newUnitCode = tempUnit.getCode();
            orgDTO.setMainUnitCode(newUnitCode);

            // 是否离退休, true-是 false-否
            boolean isRetireBol = Objects.nonNull(isRetire) && Objects.equals(isRetire, 1);
            // 传值的与原始数据离退休是否发生变更 1 1  true = true == true ;  0 0 true = (false == false);  1 0 false = (ture == false)
            boolean alterIsRetireBol = !Objects.equals(isRetireBol, Objects.nonNull(oldIsRetire) && Objects.equals(oldIsRetire, 1));
            // 主单位是否变更，true-已变更， false -未变更
            mainUniCodeAccordBol = !Objects.equals(oldMainUnitCode, newUnitCode);
            Org tempOrg = new Org();
            tempOrg.setD02Code(orgDTO.getD02Code());
            // 是离退休发生变更 or 主单位发生变更 并且 前端也没有传国民经济值（部分情况党组织可以修改维护国民经济），从新的单位获取国民经济
            if (alterIsRetireBol || (mainUniCodeAccordBol && ObjectUtil.isNotNull(tempUnit) && StrUtil.isBlank(orgDTO.getD194Code()))) {
                syncOrgService.org194And195Logic(tempOrg, true, isRetireBol, true, tempUnit);
                orgDTO.setD194Code(tempOrg.getD194Code());
                orgDTO.setD194Name(tempOrg.getD194Name());
                orgDTO.setD195Code(tempOrg.getD195Code());
                orgDTO.setD195Name(tempOrg.getD195Name());
            } else if (StrUtil.isBlank(orgDTO.getD194Code())) {
                //  前端也没传值，从org里面原来数据，后面代码都是覆盖性，只能先设置
                orgDTO.setD195Name(org.getD195Name());
                orgDTO.setD194Code(org.getD194Code());
                orgDTO.setD194Name(org.getD194Name());
                orgDTO.setD195Code(org.getD195Code());
            }
        } else {
            // 没有单位情况
            // 属于离退休
            if (Objects.equals(orgDTO.getIsRetire(), 1)) {
                orgDTO.setD194Code("U");
                orgDTO.setD194Name("其他");
                orgDTO.setD195Code("V0000");
                orgDTO.setD195Name("无");
            }
            //
//            else if (!(StrUtil.isNotBlank(d01Code) && d01Code.startsWith("2"))) {
//                orgDTO.setD194Code("");
//                orgDTO.setD194Name("");
//                orgDTO.setD195Code("");
//                orgDTO.setD195Name("");
//            }
        }

        String parentCode = orgDTO.getParentCode();
        Org parentOrg = this.findOrgByCode(parentCode);
        OutMessage outMessage = getOutMessageSaveOrUpdate(orgDTO, parentOrg);
        if (outMessage != null) {
            return outMessage;
        }
        orgDTO.setPinyin(PinyinUtil.getPinyin(orgDTO.getName()));
        orgDTO.setUpdateAccount(USER_CONTEXT.get().getUser().getAccount());
        // 获取组织大类
        String orgType = this.transformToOrgType(d01Code);
        orgDTO.setOrgType(orgType);
        // TODO: 2022/3/10 数据锁定部分字段
        Object lockFields = org.getLockFields();
        List<String> lockFieldList = Convert.toList(String.class, lockFields);
        String[] ignoreProperties = {"linkedDTOList", "collectiveEconomy"};
        if (CollUtil.isNotEmpty(lockFieldList)) {
            String[] mergeIgnoreProperties = ArrayUtils.addAll(ignoreProperties, lockFieldList.toArray(new String[0]));
            ModelUtils.copyPropertiesExclude(orgDTO, org, mergeIgnoreProperties);
        } else {
            ModelUtils.copyPropertiesExclude(orgDTO, org, ignoreProperties);
        }
        if (CollectionUtil.isNotEmpty(orgDTO.getCollectiveEconomy())) {
            org.setCollectiveEconomy(JackSonUtil.objectToString(orgDTO.getCollectiveEconomy()));
        }
        org.setUpdateTime(new Date());
        org.setTimestamp(new Date());
        org.setIsLeaf(count(new LambdaQueryWrapper<Org>().eq(Org::getParentCode, org.getCode()).isNull(Org::getDeleteTime)) > 0 ? 0 : 1);
        //是否建立党建引领基层治理领导协调机制 0否，1是
        org.setHasLeadingBasicCoordinating(StrUtil.startWithAny(d01Code, "13", "14") ? orgDTO.getHasLeadingBasicCoordinating() : "");
        //市、区、街道、社区均建立党建联席会议制度的城市 0否，1是
        org.setHasBuildJointMeeting(StrUtil.startWithAny(d01Code, "13", "14") ? orgDTO.getHasBuildJointMeeting() : "");
        //社区党组织书记实行县级党委备案管理的区
        org.setHasSecretaryBuildCountyFiling(StrUtil.startWithAny(d01Code, "14") ? orgDTO.getHasSecretaryBuildCountyFiling() : "");
        List<UnitOrgLinkedDTO> orgDTOLinkedDTOList = orgDTO.getLinkedDTOList();
        if (CollectionUtil.isNotEmpty(orgDTOLinkedDTOList)) {
            UnitOrgLinked unitOrgLinked = iSyncMemService.getUnitOrgLinked(orgDTO.getCode());
            if (Objects.nonNull(unitOrgLinked)) {
                org.setMainUnitCode(unitOrgLinked.getUnitCode());
            }
        }

        // 查找all表
        OrgAll orgAll = orgAllService.findByCode(code);
        String orgAllId = orgAll.getId();
        ModelUtils.copyPropertiesModelExclude(org, orgAll, "id", "code", "esId");
        orgAll.setParentName(Objects.isNull(parentOrg) ? "" : parentOrg.getName());
        // 设置字典表转换
        this.setOrgDict(orgAll);

        OutMessage outMessage1 = this.OrgLinkedUnit(orgDTO, orgAll);
        if (outMessage1 != null) {
            return outMessage1;
        }
        //组织类型发生改变时，处理相关数据
        this.changeOrgTypeAndProcessAssociatedInfo(code, d01Code, d02Code, orgDTOLinkedDTOList);
        //到村任职补助经费使用率字段
        org.setToTheVillageOfficeSubsidyFundsUtilizationRate(StrUtil.startWith(d01Code, "14") ? orgDTO.getToTheVillageOfficeSubsidyFundsUtilizationRate() : CommonConstant.ZERO);
        boolean b = update(org, Wrappers.<Org>lambdaUpdate()
                .set(Objects.isNull(org.getHasSecretaryHighLevel()), Org::getHasSecretaryHighLevel, null)
                .set(Objects.isNull(org.getHasPullDownAllPartyFee()), Org::getHasPullDownAllPartyFee, null)
                .set(Objects.isNull(org.getHasGiveFundsSupport()), Org::getHasGiveFundsSupport, null)
                .set(Objects.isNull(org.getHasOwnActivityPlace()), Org::getHasOwnActivityPlace, null)
                .set(Objects.isNull(org.getHasPartyAffairsWorkers()), Org::getHasPartyAffairsWorkers, null)
                .eq(Org::getId, org.getId()));
        //设置第几产业
        setIndustry(org, orgAll);
        boolean b1 = orgAllService.updateById(orgAll);

        /**
         * 20210906 删除关联表
         * <AUTHOR>
         * 1:如果关联表中的单位不在有关联组织改为未建立党组织的形式
         * 2:如果关联表中的单位主组织消失但是还有关联组织 随便取一个组织改为主组织
         */
        //查找党组织关联的住单位
        String unitCode = null;
        if (CollUtil.isNotEmpty(orgDTOLinkedDTOList)) {
            unitCode = orgDTOLinkedDTOList.stream().filter(unitOrgLinkedDTO -> unitOrgLinkedDTO.getIsUnitMain().equals(CommonConstant.ONE_INT)).findFirst().map(UnitOrgLinkedDTO::getUnitCode).orElse(null);
        }

        this.processDeleteOrgLinkedUnit(code, d02Code, dbD02Code, unitCode);
        orgDTO.setAdd(false);
        // 添加新的数据
        List<UnitOrgLinked> unitOrgLinkedList = this.getUnitOrgLinkedList(orgDTO);
        if (unitOrgLinkedList != null) {
            boolean save = unitOrgLinkedService.batchSave(unitOrgLinkedList);
            if (save) {
                unitOrgLinkedList.forEach(orgLinked -> syncUnitService.setPartyBranchesNumber(orgLinked.getUnitCode()));
            }
        }
        final boolean finalMainUniCodeAccordBol = mainUniCodeAccordBol;
        if (b1) {
            iSyncMemService.syncMemByOrg(org.getCode());
            // TODO: 2023/12/3 WaterMelon 重新修改党组织修逻辑
            //orgBusinessModelSyncService.syncBusinessModelOrg(code,null);
            // 单位发生变更后执行同步国民经济逻辑
            if (finalMainUniCodeAccordBol) {
                orgBusinessModelSyncService.syncBusinessModelOrg(code, null);
            }
            ThreadUtil.execAsync(() -> {
                //todo 增加新增组织的时候，实时同步
                syncUnitService.setUnitByUnitOrg(org.getCode());
                syncOrgService.setBaseOrg(org.getCode(), CommonConstant.FOUR);
                syncOrgService.setOrgByOrgCode(org.getCode(), finalMainUniCodeAccordBol);
                this.initExchangeOrg(org.getCode());
                // TODO: 2022/1/12 增加处理村社区相关信息同步
                syncOrgService.setOrgSyncUnit(org.getCode());
                //todo: ******** 同步农村党建调度表
                iMemReportService.syncOrgCommittee(org.getCode());
                // TODO: 2022/12/15 高校党支部类型
                OrgAll all = orgAllMapper.selectById(orgAllId);
                if (Objects.nonNull(all)) {
                    String d04Code = all.getD04Code();
                    String allCode = all.getCode();
                    //d04Code 331 开头的党支部
                    if (!StrUtil.startWithAny(d04Code, "331", "5211")) {
                        this.updateOrgD113Code(allCode);
                    } else if (!StrUtil.equals(all.getD113Code(), "1")) {
                        this.updateOrgHasTeachersDoubleLeaders(allCode);
                    }
                }
                // TODO: 2023/2/28 同步到驻村系统
                syncVillageCommunityOrg(all);
                // 党组织同步党员的国民经济
//                iSyncMemService.syncMemD194Logic(org.getCode(), finalMainUniCodeAccordBol);
            });

        }

        // 移除缓存中数据
        CacheUtils.removeOrgName(code);
        CacheUtils.removeOrgShortName(code);
        CacheUtils.removeOrgNameWithNull(code);
        CacheUtils.removeOrgNameByOrgCode(org.getOrgCode());
        CacheUtils.removeOrgShortNameByOrgCode(org.getOrgCode());
        return new OutMessage<>(b && b1 ? Status.SUCCESS : Status.FAIL);
    }

    /**
     * //党组织类别发生改变的时候，其他业务模块相关的多的信息项目应该置空，并且进行删除标识处理
     * //比如党代表，比如民主评议，比如培训情况等相关信息，只有当按钮出现的时候，才需要进行展示和处理
     * //班子成员，奖惩信息，党小组，民主评议，发出表彰，地方委员会，党代表，培训情况，乡镇班子换届，非公党建
     *
     * @param code          组织code
     * @param d01Code       组织类别
     * @param d02Code       党组织所在单位情况代码
     * @param linkedDTOList
     */
    private void changeOrgTypeAndProcessAssociatedInfo(String code, String d01Code, String d02Code, List<UnitOrgLinkedDTO> linkedDTOList) {
        final String statisticalYear = iStatisticsYearService.getStatisticalYear();
        if (StrUtil.equals(d01Code, "A")) {
            //只有基础信息其他全部删除
            // 过滤党小组
            filterPartyGroup(code);
            //过滤民主评议
            filterDemocraticReview(code, statisticalYear);
            //发出表彰情况
            filterOrgRecognition(code);
            //班子成员
            filterOrgCommittee(code);
            //地方委员会情况和非公党建情况
            filterOrgCaucus(code);
            //党代表情况
            handlingPartyRepresentatives(code);
            //培训情况
            filterMemTrain(code);
            //乡镇班子换届情况
            filterOrgTownshipLeadership(code);
            //奖惩信息
            filterOrgReward(code);
        } else {
            filterByOrgType(code, d01Code, d02Code, linkedDTOList, statisticalYear);
        }
    }

    private void setIndustry(Org org, OrgAll orgAll) {
        String d194Code = org.getD194Code();
        if (StrUtil.startWith(d194Code, "A")) {
            orgAll.setIndustry("1");
        } else if (StrUtil.startWithAny(d194Code, "B", "C", "D", "E")) {
            orgAll.setIndustry("2");
        } else if (!StrUtil.equals(d194Code, "U")) {
            orgAll.setIndustry("3");
        }
    }

    /**
     * 删除关联表逻辑处理
     * 1:如果关联表中的单位不在有关联组织改为未建立党组织的形式
     * 2:如果关联表中的单位主组织消失但是还有关联组织 随便取一个组织改为主组织
     *
     * @param code              机构唯一编码
     * @param orgLinkedUnitCode
     */
    public void processDeleteOrgLinkedUnit(String code, String d02Code, String dbD02Code, String orgLinkedUnitCode) {
        //查找当前组织的关联单位信息
        List<UnitOrgLinked> unitOrgLinkeds = unitOrgLinkedService.findByOrgCode(code);
        //当前组织关联的单位
        List<String> unitCodes = unitOrgLinkeds.stream().map(UnitOrgLinked::getUnitCode).collect(Collectors.toList());
        //删除当前组织的关联单位信息
        unitOrgLinkedService.delUnitOrgLinkedByOrg(code);
        if (CollectionUtil.isNotEmpty(unitOrgLinkeds)) {
            // TODO: 2022/6/27 处理关联单位类型由单独建立党组织变更为与上级党组织相同时
            if (!StrUtil.equals(d02Code, dbD02Code) && StrUtil.equals(d02Code, "2")) {
                UnitOrgLinked unitOrgLinked = unitOrgLinkeds.stream().filter(t -> t.getIsUnitMain().equals(1)).findFirst().orElse(null);
                if (Objects.nonNull(unitOrgLinked)) {
                    // TODO: 2022/6/28 处理关联单位变更
                    //递归查找当前组织的所有下级组织
                    List<Org> orgList = fromFatherToSon(code);
                    if (CollUtil.isNotEmpty(orgList)) {
                        //过滤出类型为与上级党组织所在单位一起建立联合支部的党组织
                        List<String> orgCodeList = orgList.stream().filter(t -> "4".equals(t.getD02Code())).map(Org::getCode).collect(Collectors.toList());
                        if (CollUtil.isNotEmpty(orgCodeList)) {
                            List<UnitOrgLinked> orgLinkedList = unitOrgLinkedService.findByOrgCodeListAndUnitCode(orgCodeList, unitOrgLinked.getUnitCode());
                            //递归查找当前组织的所有上级组织
                            List<Org> superOrgList = fromSonToFather(code);
                            String superOrgCode = superOrgList.stream().filter(dbOrg -> !dbOrg.getCode().equals(code) && !"2".equals(dbOrg.getD02Code())).findFirst().map(Org::getCode).orElse(null);
                            if (StrUtil.isNotBlank(superOrgCode)) {
                                if (CollUtil.isNotEmpty(orgLinkedList)) {
                                    //查找上级党组织的主单位
                                    UnitOrgLinked superUnitOrgLinked = unitOrgLinkedService.findByOrgCodeAndIsMainUnit(superOrgCode);
                                    if (Objects.nonNull(superUnitOrgLinked)) {
                                        String superUnitCode = superUnitOrgLinked.getUnitCode();
                                        Unit unit = unitService.findByCode(superUnitCode);
                                        if (Objects.nonNull(unit)) {
                                            List<UnitOrgLinked> updateUnitOrgLinkedList = new ArrayList<>();
                                            orgLinkedList.forEach(t -> {
                                                UnitOrgLinked updateUnitOrgLinked = new UnitOrgLinked();
                                                updateUnitOrgLinked.setId(t.getId());
                                                updateUnitOrgLinked.setUnitCode(unit.getCode());
                                                updateUnitOrgLinked.setUnitName(unit.getName());
                                                updateUnitOrgLinked.setTimestamp(new Date());
                                                updateUnitOrgLinked.setUpdateTime(new Date());
                                                updateUnitOrgLinked.setUnitType(unit.getD04Code());
                                                updateUnitOrgLinked.setUnitTypeName(unit.getD04Name());
                                                updateUnitOrgLinkedList.add(updateUnitOrgLinked);
                                            });
                                            unitOrgLinkedService.updateBatchById(updateUnitOrgLinkedList, updateUnitOrgLinkedList.size());
                                        }
                                    }
                                }
                            } else {
                                unitOrgLinkedService.delUnitOrgLinkedByUnit(unitOrgLinked.getUnitCode());
                            }
                        }
                    }
                }
            }
            //查找单位下的关联组织关联关系
            List<UnitOrgLinked> unitOrgLinkedListByUnits = unitOrgLinkedService.findByUnitCodes(unitCodes);
            LinkedHashMap<String, List<UnitOrgLinked>> unitLinkedOrgMap = unitOrgLinkedListByUnits.stream().collect(Collectors.groupingBy(UnitOrgLinked::getUnitCode, LinkedHashMap::new, Collectors.toList()));
            //过滤出单位关联的主组织信息
            Set<String> unitCodeSet = unitOrgLinkedListByUnits.stream().filter(var -> ObjectUtil.equal(var.getIsOrgMain(), 1)).map(UnitOrgLinked::getUnitCode).collect(Collectors.toSet());
            List<String> updateUnitList = new ArrayList<>();
            Map<String, UnitOrgLinked> updateMap = new HashMap<>(20);
            for (String unitCode : unitCodes) {
                if (!unitCodeSet.contains(unitCode)) {
                    //这里为没有主组织的单位
                    if (unitLinkedOrgMap.containsKey(unitCode)) {
                        //这种没有主组织的单位 应该不会太多
                        List<UnitOrgLinked> unitOrgLinkedCodes = unitLinkedOrgMap.get(unitCode);
                        updateMap.put(unitCode, unitOrgLinkedCodes.get(0));
                    } else {
                        updateUnitList.add(unitCode);
                    }
                }
            }
            if (CollectionUtil.isNotEmpty(updateUnitList)) {
                unitService.batchMainOrgByCodes(0, null, null, null, null, null, updateUnitList);
                unitAllService.batchMainOrgByCodes(0, null, null, null, null, null, updateUnitList);
            }

            if (CollectionUtil.isNotEmpty(updateMap)) {
                List<Unit> unitList = unitService.selectIdByCode(updateMap.keySet());
                for (Unit unit : unitList) {
                    UnitOrgLinked unitOrgLinked1 = updateMap.get(unit.getCode());
                    unit.setIsCreateOrg(1);
                    unit.setMainUnitOrgCode(unitOrgLinked1.getLinkedOrgCode());
                    unit.setMainOrgCode(unitOrgLinked1.getOrgCode());
                    unit.setMainOrgName(unitOrgLinked1.getOrgName());
                    unit.setMainOrgType(unitOrgLinked1.getOrgType());
                    unit.setMainOrgTypeCode(unitOrgLinked1.getOrgTypeCode());
                }
                unitService.updateBatchById(unitList);

                List<UnitAll> unitAllList = unitAllService.selectIdByCode(updateMap.keySet());
                for (UnitAll unitAll : unitAllList) {
                    UnitOrgLinked unitOrgLinked1 = updateMap.get(unitAll.getCode());
                    unitAll.setIsCreateOrg(1);
                    unitAll.setMainUnitOrgCode(unitOrgLinked1.getLinkedOrgCode());
                    unitAll.setMainOrgCode(unitOrgLinked1.getOrgCode());
                    unitAll.setMainOrgName(unitOrgLinked1.getOrgName());
                    unitAll.setMainOrgType(unitOrgLinked1.getOrgType());
                    unitAll.setMainOrgTypeCode(unitOrgLinked1.getOrgTypeCode());
                }
                unitAllService.updateBatchById(unitAllList);
            }

            // TODO: 2022/6/27 处理党组织单位选择单独建立党组织时上级修改主单位，下级选择与上级党组织所在单位一起建立联合支部时，单位未变化
            String dbOrgLinkedUnitCode = unitOrgLinkeds.stream().filter(orgLinked -> orgLinked.getIsUnitMain().equals(CommonConstant.ONE_INT)).findFirst().map(UnitOrgLinked::getUnitCode).orElse(null);
            if (StrUtil.equals(dbOrgLinkedUnitCode, orgLinkedUnitCode)) {
                return;
            }
            // TODO: 2022/6/27 查询单位中关联组织的相关信息
            List<Unit> unitList = unitService.findByMainOrgCode(code);
            if (CollUtil.isNotEmpty(unitList)) {
                unitService.batchMainOrgByCodes(0, null, null, null, null, null, unitList.stream().map(Unit::getCode).collect(Collectors.toList()));
            }
            List<UnitAll> unitAllList = unitAllService.findByMainOrgCode(code);
            if (CollUtil.isNotEmpty(unitAllList)) {
                unitAllService.batchMainOrgByCodes(0, null, null, null, null, null, unitAllList.stream().map(UnitAll::getCode).collect(Collectors.toList()));
            }
            //查询当前组织下，组织选择了与上级党组织所在单位一起建立联合支部的党组织
            List<Org> orgList = this.fromFatherToSon(code);
            if (CollUtil.isEmpty(orgList)) {
                return;
            }
            List<String> orgCodeList = orgList.stream().filter(t -> "4".equals(t.getD02Code())).map(Org::getCode).collect(Collectors.toList());
            List<UnitOrgLinked> unitOrgLinkedList = unitOrgLinkedListByUnits.stream().filter(t -> orgCodeList.contains(t.getOrgCode())).collect(Collectors.toList());
            if (CollUtil.isEmpty(unitOrgLinkedList)) {
                return;
            }
            //查找最新的单位信息
            if (Objects.isNull(orgLinkedUnitCode)) {
                return;
            }
            Unit unit = unitService.findByCode(orgLinkedUnitCode);
            if (Objects.isNull(unit)) {
                return;
            }
            List<UnitOrgLinked> updateUnitOrgLinkedList = new ArrayList<>();
            for (UnitOrgLinked orgLinked : unitOrgLinkedList) {
                UnitOrgLinked updateUnitOrgLinked = new UnitOrgLinked();
                updateUnitOrgLinked.setId(orgLinked.getId());
                updateUnitOrgLinked.setUnitCode(unit.getCode());
                updateUnitOrgLinked.setUnitName(unit.getName());
                updateUnitOrgLinked.setD04Code(unit.getD04Code());
                updateUnitOrgLinked.setTimestamp(new Date());
                updateUnitOrgLinked.setUpdateTime(new Date());
                updateUnitOrgLinked.setUnitType(unit.getD04Code());
                updateUnitOrgLinked.setUnitTypeName(unit.getD04Name());
                updateUnitOrgLinkedList.add(updateUnitOrgLinked);
            }
            if (CollUtil.isNotEmpty(updateUnitOrgLinkedList)) {
                unitOrgLinkedService.updateBatchById(updateUnitOrgLinkedList, updateUnitOrgLinkedList.size());
            }
        }
        if (!StrUtil.equals(d02Code, dbD02Code) && StrUtil.equals(d02Code, "1")) {
            // TODO: 2022/6/28 处理关联单位变更
            //递归查找当前组织的所有下级组织
            List<Org> orgList = fromFatherToSon(code);
            if (CollUtil.isNotEmpty(orgList)) {
                //过滤出类型为与上级党组织所在单位一起建立联合支部的党组织
                List<String> orgCodeList = orgList.stream().filter(t -> "4".equals(t.getD02Code())).map(Org::getCode).collect(Collectors.toList());
                if (CollUtil.isNotEmpty(orgCodeList)) {
                    //递归查找当前组织的所有上级组织
                    List<Org> superOrgList = fromSonToFather(code);
                    String superOrgCode = superOrgList.stream().filter(dbOrg -> !dbOrg.getCode().equals(code) && !"2".equals(dbOrg.getD02Code())).findFirst().map(Org::getCode).orElse(null);
                    if (StrUtil.isNotBlank(superOrgCode)) {
                        UnitOrgLinked unitOrgLinked = unitOrgLinkedService.findByOrgCodeAndIsMainUnit(superOrgCode);
                        if (Objects.nonNull(unitOrgLinked)) {
                            List<UnitOrgLinked> unitOrgLinkedList = unitOrgLinkedService.findByOrgCodeListAndUnitCode(orgCodeList, unitOrgLinked.getUnitCode());
                            if (CollUtil.isNotEmpty(unitOrgLinkedList)) {
                                Unit unit = unitService.findByCode(orgLinkedUnitCode);
                                if (Objects.nonNull(unit)) {
                                    List<UnitOrgLinked> updateUnitOrgLinkedList = new ArrayList<>();
                                    unitOrgLinkedList.forEach(t -> {
                                        UnitOrgLinked updateUnitOrgLinked = new UnitOrgLinked();
                                        updateUnitOrgLinked.setId(t.getId());
                                        updateUnitOrgLinked.setUnitCode(unit.getCode());
                                        updateUnitOrgLinked.setUnitName(unit.getName());
                                        updateUnitOrgLinked.setTimestamp(new Date());
                                        updateUnitOrgLinked.setUpdateTime(new Date());
                                        updateUnitOrgLinked.setUnitType(unit.getD04Code());
                                        updateUnitOrgLinked.setUnitTypeName(unit.getD04Name());
                                        updateUnitOrgLinkedList.add(updateUnitOrgLinked);
                                    });
                                    unitOrgLinkedService.updateBatchById(updateUnitOrgLinkedList, updateUnitOrgLinkedList.size());
                                }
                            }
                        }
                    }
                }
            }
        }
    }

    private void filterPartyGroup(String code) {
        List<OrgGroup> orgGroupList = orgGroupService.findOrgGroupByOrgCode(code);
        if (CollUtil.isNotEmpty(orgGroupList)) {
            //无其它关联表，只需要删除
            orgGroupList.forEach(t -> t.setDeleteTime(new Date()));
            orgGroupService.updateBatchById(orgGroupList, orgGroupList.size());
        }
    }

    private void filterDemocraticReview(String code, String statisticalYear) {
        List<OrgAppraisal> orgAppraisalList = orgAppraisalService.findOrgAppraisalByOrgCode(code);
        if (CollUtil.isNotEmpty(orgAppraisalList)) {
            orgAppraisalList.forEach(t -> t.setDeleteTime(new Date()));
            orgAppraisalService.updateBatchById(orgAppraisalList, orgAppraisalList.size());
        }
        orgAllService.update(
                Wrappers.<OrgAll>lambdaUpdate()
                        .eq(OrgAll::getCode, code)
                        .isNull(OrgAll::getDeleteTime)
                        .set(OrgAll::getReviewersYear, null)
                        .set(OrgAll::getHasJoinReviewers, null)
                        .set(OrgAll::getHasEndReviewers, null)
        );

        List<OrgAppraisal> appraisalList = new ArrayList<>();
        for (OrgAppraisal orgAppraisal : orgAppraisalList) {
            String compareYear = String.valueOf(DateUtil.year(orgAppraisal.getYear()));
            //过滤统计年份，统计年份减一年，并且结束时间是统计年份的民主评议
            if (StrUtil.equalsAny(compareYear, statisticalYear, String.valueOf(Integer.parseInt(statisticalYear) - 1)) && String.valueOf(DateUtil.year(orgAppraisal.getEndTime())).equals(statisticalYear)) {
                appraisalList.add(orgAppraisal);
            }
        }
        if (CollUtil.isNotEmpty(appraisalList)) {
            //按结束时间和创建时间排序
            Optional<OrgAppraisal> orgAppraisalOptional = appraisalList.stream()
                    .min(Comparator.comparing(OrgAppraisal::getEndTime, Comparator.reverseOrder())
                            .thenComparing(OrgAppraisal::getCreateTime, Comparator.reverseOrder()));
            if (orgAppraisalOptional.isPresent()) {
                OrgAppraisal orgAppraisal = orgAppraisalOptional.get();
                String appraisalCode = orgAppraisal.getCode();
                List<OrgReviewers> orgReviewersList = orgReviewersService.findOrgReviewersByAppraisalCode(appraisalCode);
                if (CollUtil.isNotEmpty(orgReviewersList)) {
                    List<String> memCodeList = orgReviewersList.stream().map(OrgReviewers::getMemCode).collect(Collectors.toList());
                    //更新memAll表相关字段
                    memAllInfoService.update(
                            Wrappers.<MemAllInfo>lambdaUpdate()
                                    .in(MemAllInfo::getCode, memCodeList)
                                    .isNull(MemAllInfo::getDeleteTime)
                                    .set(MemAllInfo::getOrgCodeReviewers, null)
                                    .set(MemAllInfo::getResult, null)
                                    .set(MemAllInfo::getSituation, null)
                                    .set(MemAllInfo::getReviewersYear, null)
                                    .set(MemAllInfo::getHasJoinReviewers, null)
                                    .set(MemAllInfo::getHasEndReviewers, null)
                    );
                    orgReviewersService.updateBatchById(orgReviewersList, orgReviewersList.size());
                }
            }
        }
    }

    private void filterOrgRecognition(String code) {
        List<OrgRecognition> recognitionList = orgRecognitionService.findOrgRecognitionByOrgCode(code);
        if (CollUtil.isNotEmpty(recognitionList)) {
            List<String> recognitionCodeList = new ArrayList<>();
            for (OrgRecognition orgRecognition : recognitionList) {
                orgRecognition.setDeleteTime(new Date());

                recognitionCodeList.add(orgRecognition.getCode());
            }
            orgRecognitionService.updateBatchById(recognitionList, recognitionList.size());

            //OrgRecognitionData
            List<OrgRecognitionData> orgRecognitionDataList = orgRecognitionDataService.findOrgRecognitionDataByRecognitionCode(recognitionCodeList);
            orgRecognitionDataList.forEach(t -> t.setDeleteTime(new Date()));
            orgRecognitionDataService.updateBatchById(orgRecognitionDataList, orgRecognitionDataList.size());
        }

        List<OrgRecognitionAll> recognitionAllList = orgRecognitionAllService.findOrgRecognitionAllByOrgCode(code);
        if (CollUtil.isNotEmpty(recognitionAllList)) {
            recognitionAllList.forEach(t -> t.setDeleteTime(new Date()));
            orgRecognitionAllService.updateBatchById(recognitionAllList, recognitionAllList.size());
        }
    }

    private void filterOrgCommittee(String code) {
        handlingOrgCommittee(code);

        orgAllService.update(
                Wrappers.<OrgAll>lambdaUpdate()
                        .eq(OrgAll::getCode, code)
                        .isNull(OrgAll::getDeleteTime)
                        .set(OrgAll::getHasInternalInstitutions, null)
                        .set(OrgAll::getHasInternalInstitutionsTransition, null)
                        .set(OrgAll::getSecretaryCount, null)
                        .set(OrgAll::getLeadCadresTowns, null)
                        .set(OrgAll::getVillageLeadCadres, null)
                        .set(OrgAll::getFirstSecretaryCadres, null)
                        .set(OrgAll::getVillageLeaders, null)
        );
    }

    private void filterOrgCaucus(String code) {
        List<OrgCaucus> orgCaucusList = orgCaucusService.findOrgCaucusByOrgCode(code);
        if (CollUtil.isNotEmpty(orgCaucusList)) {
            orgCaucusList.forEach(t -> t.setDeleteTime(new Date()));
            orgCaucusService.updateBatchById(orgCaucusList, orgCaucusList.size());

            orgAllService.update(
                    Wrappers.<OrgAll>lambdaUpdate()
                            .eq(OrgAll::getCode, code)
                            .isNull(OrgAll::getDeleteTime)
                            .set(OrgAll::getSituation, null)
                            .set(OrgAll::getCongressSituation, null)
                            .set(OrgAll::getRepresentatives, null)
                            .set(OrgAll::getPartyCommittee, null)
                            .set(OrgAll::getStandingCommittee, null)
                            .set(OrgAll::getPartyAlternateCommittee, null)
                            .set(OrgAll::getInspectionCommittee, null)
                            .set(OrgAll::getInspectionStandingCommittee, null)
                            .set(OrgAll::getWholeSituation, null)
                            .set(OrgAll::getLifeSituation, null)
                            .set(OrgAll::getAttendMember, null)
                            .set(OrgAll::getParticipants, null)
            );
        }

        //非公党建情况
        List<OrgNonPublicParty> orgNonPublicPartyList = orgNonPublicPartyService.findOrgNonPublicPartyByOrgCode(code);
        if (CollUtil.isNotEmpty(orgNonPublicPartyList)) {
            orgNonPublicPartyList.forEach(t -> t.setDeleteTime(new Date()));
            orgNonPublicPartyService.updateBatchById(orgNonPublicPartyList, orgNonPublicPartyList.size());

            orgAllService.update(
                    Wrappers.<OrgAll>lambdaUpdate()
                            .eq(OrgAll::getCode, code)
                            .isNull(OrgAll::getDeleteTime)
                            .set(OrgAll::getHasWorkCommittee, null)
                            .set(OrgAll::getHasWorkingBody, null)
                            .set(OrgAll::getWorkingBodyNumber, null)
                            .set(OrgAll::getManageOrganizationNumber, null)
                            .set(OrgAll::getConnectOrganizationNumber, null)
                            .set(OrgAll::getFiscalFunds, null)
                            .set(OrgAll::getPartyExpenses, null)
                            .set(OrgAll::getActivityServiceCenter, null)
                            .set(OrgAll::getNewActivityServiceCenter, null)
            );
        }
    }

    private void filterMemTrain(String code) {
        List<MemTrain> memTrainList = memTrainService.findTrainByOrgCode(code);
        if (CollUtil.isNotEmpty(memTrainList)) {
            memTrainList.forEach(t -> t.setDeleteTime(new Date()));
            memTrainService.updateBatchById(memTrainList, memTrainList.size());

            orgAllService.update(
                    Wrappers.<OrgAll>lambdaUpdate()
                            .eq(OrgAll::getCode, code)
                            .isNull(OrgAll::getDeleteTime)
                            .set(OrgAll::getTrainTotal, null)
                            .set(OrgAll::getSchoolTrain, null)
                            .set(OrgAll::getNewTrain, null)
                            .set(OrgAll::getYouthTrain, null)
                            .set(OrgAll::getElderlyTrain, null)
                            .set(OrgAll::getFlowTrain, null)
                            .set(OrgAll::getLaidOffTrain, null)
                            .set(OrgAll::getOrganizationSecretary, null)
                            .set(OrgAll::getCountyPartyCommittee, null)
                            .set(OrgAll::getLevelOrganizationsSecretary, null)
                            .set(OrgAll::getCommunityParty, null)
                            .set(OrgAll::getHasWillLesson, null)
                            .set(OrgAll::getHasPartyDay, null)
                            .set(OrgAll::getProvincialTrainClass, null)
                            .set(OrgAll::getProvincialTrainMember, null)
                            .set(OrgAll::getCityTrainClass, null)
                            .set(OrgAll::getCityTrainMember, null)
                            .set(OrgAll::getCountyTrainClass, null)
                            .set(OrgAll::getCountyTrainMember, null)
                            .set(OrgAll::getLevelPartyClass, null)
                            .set(OrgAll::getLevelPartyMember, null)
                            .set(OrgAll::getRuralPartyVillages, null)
                            .set(OrgAll::getRemoteEducation, null)
                            .set(OrgAll::getRemoteEducationVillages, null)
                            .set(OrgAll::getRemoteEducationAdministrativeVillage, null)
                            .set(OrgAll::getRemoteEducationCommittee, null)
                            .set(OrgAll::getInternet, null)
                            .set(OrgAll::getWired, null)
                            .set(OrgAll::getSatellite, null)
                            .set(OrgAll::getSiteAdministrator, null)
                            .set(OrgAll::getVillagesCadres, null)
                            .set(OrgAll::getVillageCommunity, null)
                            .set(OrgAll::getVolunteers, null)
                            .set(OrgAll::getRuralRemoteEducationParty, null)
            );
        }
    }

    private void filterOrgTownshipLeadership(String code) {
        List<OrgTownshipLeadership> orgTownshipLeadershipList = orgTownshipLeadershipService.findOrgTownshipLeadershipByOrgCode(code);
        if (CollUtil.isNotEmpty(orgTownshipLeadershipList)) {
            orgTownshipLeadershipList.forEach(t -> t.setDeleteTime(new Date()));
            orgTownshipLeadershipService.updateBatchById(orgTownshipLeadershipList, orgTownshipLeadershipList.size());

            orgAllService.update(
                    Wrappers.<OrgAll>lambdaUpdate()
                            .eq(OrgAll::getCode, code)
                            .isNull(OrgAll::getDeleteTime)
                            .set(OrgAll::getLeadCadresTowns, null)
                            .set(OrgAll::getVillageLeadCadres, null)
                            .set(OrgAll::getFirstSecretaryCadres, null)
                            .set(OrgAll::getVillageLeaders, null)
            );
        }
    }

    private void filterOrgReward(String code) {
        List<OrgReward> orgRewardList = orgRewardService.findOrgRewardByOrgCode(code);
        if (CollUtil.isNotEmpty(orgRewardList)) {
            orgRewardList.forEach(orgReward -> orgReward.setDeleteTime(new Date()));
            orgRewardService.updateBatchById(orgRewardList, orgRewardList.size());
            //同步组织OrgAll奖惩信息 d42_code
            orgAllService.update(
                    Wrappers.<OrgAll>lambdaUpdate()
                            .eq(OrgAll::getCode, code)
                            .isNull(OrgAll::getDeleteTime)
                            .set(OrgAll::getD42Code, null)
            );
        }
    }

    /**
     * 根据d01类型进行过滤
     *
     * @param code
     * @param d01Code
     * @param d02Code
     * @param linkedDTOList
     * @param statisticalYear
     */
    private void filterByOrgType(String code, String d01Code, String d02Code, List<UnitOrgLinkedDTO> linkedDTOList, String statisticalYear) {
        if (!"631".equals(d01Code) && !"632".equals(d01Code) && !"931".equals(d01Code) && !"932".equals(d01Code)) {
            // 过滤党小组
            filterPartyGroup(code);
            if (!"634".equals(d01Code)) {
                //过滤民主评议
                filterDemocraticReview(code, statisticalYear);
            }
        }
        if (!(StrUtil.startWithAny(d01Code, "1", "2") || StrUtil.equals(d01Code, "61") || StrUtil.equals(d01Code, "911"))) {
            //发出表彰情况
            filterOrgRecognition(code);
        }
        if (StrUtil.startWithAny(d01Code, "1", "2")) {
            //班子成员
            filterOrgCommittee(code);
        }
        if (!StrUtil.startWith(d01Code, "1")) {
            //地方委员会情况和非公党建情况
            filterOrgCaucus(code);
        }
        boolean d04Code = false;
        if (CollUtil.isNotEmpty(linkedDTOList)) {
            d04Code = linkedDTOList.stream().anyMatch(t -> StrUtil.startWith(t.getUnitType(), "912"));
        }
        if (!(StrUtil.startWith(d01Code, "1") || (StrUtil.equals(d02Code, "1") && d04Code))) {
            //党代表情况
            handlingPartyRepresentatives(code);
        }
        if (!(StrUtil.startWithAny(d01Code, "1", "61", "63") || StrUtil.equals(d01Code, "911"))) {
            //培训情况
            filterMemTrain(code);
        }
        if (!StrUtil.startWith(d01Code, "14")) {
            //乡镇班子换届情况
            filterOrgTownshipLeadership(code);
        }
    }

    /**
     * 班子成员所涉及的人员职务信息
     *
     * @param orgCode 组织code
     */
    private void handlingOrgCommittee(String orgCode) {
        //查询组织下组织届次信息
        List<OrgCommitteeElect> orgCommitteeElectList = electService.findOrgCommitteeElectByOrgCode(orgCode);
        if (CollUtil.isNotEmpty(orgCommitteeElectList)) {
            List<String> orgCommitteeElectCodeList = new ArrayList<>();
            for (OrgCommitteeElect orgCommitteeElect : orgCommitteeElectList) {
                //更新组织届次
                orgCommitteeElect.setDeleteTime(new Date());
                //集合存储届次code
                orgCommitteeElectCodeList.add(orgCommitteeElect.getCode());
            }
            //根据届次code查询届次人员信息
            List<OrgCommittee> orgCommitteeList = committeeMapper.selectList(
                    new LambdaQueryWrapper<OrgCommittee>()
                            .in(OrgCommittee::getElectCode, orgCommitteeElectCodeList)
                            .isNull(OrgCommittee::getDeleteTime)
            );
            if (CollUtil.isNotEmpty(orgCommitteeList)) {
                List<String> memCodeList = new ArrayList<>();
                for (OrgCommittee orgCommittee : orgCommitteeList) {
                    //更新届次人员
                    orgCommittee.setDeleteTime(new Date());
                    orgCommittee.setIsHistory(1);
                    //根据届次人员关联code查询all表人员信息
                    memCodeList.add(orgCommittee.getMemCode());
                }
                if (CollUtil.isNotEmpty(memCodeList)) {
                    //更新人员all表涉及到相关届次字段信息
                    memAllInfoService.update(
                            Wrappers.<MemAllInfo>lambdaUpdate()
                                    .in(MemAllInfo::getCode, memCodeList)
                                    .set(MemAllInfo::getD121Code, null)
                                    .set(MemAllInfo::getD121Name, null)
                                    .set(MemAllInfo::getOrgCommitCode, null)
                                    .set(MemAllInfo::getD120Code, null)
                                    .set(MemAllInfo::getD120Name, null)
                                    .set(MemAllInfo::getHasPartTraining, null)
                    );
                }
                //更新届次人员相关字段信息
                committeeService.updateBatchById(orgCommitteeList, orgCommitteeList.size());
            }
            //更新届次相关信息
            electService.updateBatchById(orgCommitteeElectList, orgCommitteeElectList.size());
        }
    }

    /**
     * 组织排序
     *
     * @param sortDTOList
     * @return
     */
    @Override
    public OutMessage updateOrgSort(List<SortDTO> sortDTOList) {
        boolean b = updateSort("ccp_org", "code", "sort", sortDTOList);
        return new OutMessage<>(b ? Status.SUCCESS : Status.FAIL);
    }

    @Override
    public OutMessage getOrgSortList(OrgListDTO orgListDTO) {
        List<OrgAll> list = orgAllMapper.getOrgSortList(orgListDTO.getOrgCode());
        Map<String, String> dictD01Map = com.zenith.front.core.kit.CollectionUtil.listRecordToMap(CacheUtils.getDic("dict_d01"), "key", "name");
        list.forEach(e -> e.setD01Name(StrUtil.isEmpty(e.getD01Name()) ? dictD01Map.get(e.getD01Code()) : e.getD01Name()));
        return new OutMessage<>(Status.SUCCESS, list);
    }

    /**
     * 获取组织选择器列表
     *
     * @param orgListDTO
     * @return
     */
    @Override
    public OutMessage getSelectorList(OrgListDTO orgListDTO) {
        //todo 增加判断是否只返回流动党组织判断
        if (StrUtil.equals(orgListDTO.getMustFlowStatus(), CommonConstant.ONE)) {
            return orgFlowService.getFLowSelectorList(orgListDTO);
        }

        // 组织层级码
        String orgCode = orgListDTO.getOrgCode();
        List<String> excludeOrgCodeList = orgListDTO.getExcludeOrgCodeList();
        LambdaQueryWrapper<OrgAll> wrapper = new LambdaQueryWrapper<>();

        String subordinate = orgListDTO.getSubordinate();
        Boolean convert = StringToBooleanConverter.convert(subordinate);
        if (Boolean.TRUE.equals(convert)) {
            // 所有下级
            wrapper.likeRight(OrgAll::getOrgCode, orgCode);
        } else {
            //自己
            wrapper.eq(OrgAll::getOrgCode, orgCode);
        }
        if (StrUtil.isNotEmpty(orgListDTO.getOrgName())) {
            wrapper.like(OrgAll::getName, orgListDTO.getOrgName());
        }
        if (CollectionUtil.isNotEmpty(excludeOrgCodeList)) {
            for (String excludeOrgCode : excludeOrgCodeList) {
                wrapper.apply("\"org_code\" not like '" + excludeOrgCode + "%'");
            }
        }
        return this.getListCondition(orgListDTO, wrapper, "ORDER BY sort,\"create_time\" DESC,\"id\" DESC");
    }

    /**
     * 删除组织
     * // TODO: 2019/5/28 1.关联单位,先删除单位;2.下面有党员不能删除,流动党员,活动,转接中不能删除,发展党员,党代表,有下级组织不能删除
     *
     * @param code
     * @return
     */
    @Override
    public OutMessage delOrg(String code) {
        Org org = this.findOrgByCode(code);
        if (org == null) {
            return new OutMessage<>(Status.ORG_NOT_EXIST);
        }
        String orgCode = org.getOrgCode();
        // 下级组织
        List<Org> orgList = this.findOrgByParentCode(orgCode);
        if (CollectionUtil.isNotEmpty(orgList)) {
            return new OutMessage<>(Status.NEED_DEL_SUB_ORG);
        }

        // 党员
        LambdaQueryWrapper<Mem> condition = new LambdaQueryWrapper<Mem>().likeRight(Mem::getMemOrgCode, orgCode)
                .in(Mem::getD08Code, "1", "2")
                .isNull(Mem::getDeleteTime);
        Long total = memService.getMemTotal(condition);
        if (total != null && total > 0) {
            return new OutMessage<>(Status.NEED_DEL_ORG_MEM);
        }

        // 发展党员
        // TODO: 2019/5/28 处理发展指标
        Long developTotal = memDevelopService.getDevelopTotal(new LambdaQueryWrapper<MemDevelop>().likeRight(MemDevelop::getDevelopOrgCode, orgCode).isNull(MemDevelop::getDeleteTime));
        if (developTotal != null && developTotal > 0) {
            return new OutMessage<>(Status.NEED_DEL_ORG_DEVELOP_MEM);
        }

        // 关联单位
        List<UnitAll> list = unitAllService.list(new QueryWrapper<UnitAll>().lambda().eq(UnitAll::getCreateOrgCode, code).isNull(UnitAll::getDeleteTime));
        if (CollectionUtil.isNotEmpty(list)) {
            return new OutMessage<>(Status.NEED_CANCEL_UNIT);
        }
        // 多重党员
        Long memMultipleTotal = memManyService.getTotal(orgCode);
        if (memMultipleTotal != null && memMultipleTotal > 0) {
            return new OutMessage<>(Status.NEED_DEL_ORG_MANY_MEM);
        }

        // 党代表
        Long representativeTotal = representativeService.getTotalByOrgCode(orgCode);
        if (representativeTotal != null && representativeTotal > 0) {
            return new OutMessage<>(Status.NEED_DEL_ORG_REPRESENTATIVE);
        }

        // 届次信息
        Long representativeElectTotal = representativeElectService.getTotalByOrgCode(orgCode);
        if (representativeElectTotal != null && representativeElectTotal > 0) {
            return new OutMessage<>(Status.NEED_DEL_ORG_REPRESENTATIVE);
        }

        // 联络机构
        Long representativeContactTotal = representativeContactService.getTotalByOrgCode(orgCode);
        if (representativeContactTotal != null && representativeContactTotal > 0) {
            return new OutMessage<>(Status.NEED_DEL_ORG_REPRESENTATIVE);
        }

        // 关系转接
//        Long transferRecordTotal = transferRecordService.getTotalByOrgId(code);
//        if (transferRecordTotal != null && transferRecordTotal > 0) {
//            return new OutMessage<>(Status.NEED_DEL_ORG_TRANSFER);
//        }

        // 流动党员
        List<MemFlow> totalByOrgCode = memFlowService.getTotalByOrgCode(orgCode);
        if (totalByOrgCode != null && totalByOrgCode.size() > 0) {
            totalByOrgCode.forEach(memFlow -> memFlow.setDeleteTime(new Date()));
            memFlowService.updateBatchById(totalByOrgCode);
            //return new OutMessage<>(Status.NEED_DEL_ORG_FLOW);
        }
        // 活动
        Long activityTotal = activityService.getTotalByOrgCode(orgCode);
        if (activityTotal != null && activityTotal > 0) {
            return new OutMessage<>(Status.NEED_DEL_ORG_ACTIVITY);
        }
        // 党费
        // TODO: 2019/5/28 党费怎么操作

        // delete
        OrgAll orgAll = orgAllService.findByCode(code);
        Date now = new Date();
        String account = USER_CONTEXT.get().getUser().getAccount();
        org.setTimestamp(now);
        org.setUpdateAccount(account);
        org.setDeleteTime(now);
        orgAll.setTimestamp(now);
        orgAll.setDeleteTime(now);

        boolean b = this.updateById(org);
        boolean b1 = orgAllService.updateById(orgAll);
        CompletableFuture.supplyAsync(() -> syncOrgService.delOrg(org.getCode()), mySimpleAsync);
        return new OutMessage<>(b && b1 ? Status.SUCCESS : Status.FAIL);
    }

    @Override
    public List<Org> findAllParentOrg(String orgId) {
        if (StrKit.isBlank(orgId)) {
            return new ArrayList<>();
        }
        return orgMapper.findAllParentOrg(orgId);
    }

    @Override
    public boolean rewriteUpdateById(Org updateSrcOrg) {
        return updateById(updateSrcOrg);
    }

    @Override
    public List<Org> findAllSubOrgByOrgCode(String orgCode) {
        LambdaQueryWrapper<Org> lambdaQueryWrapper = new LambdaQueryWrapper<Org>()
                .likeRight(Org::getOrgCode, orgCode).isNull(Org::getDeleteTime).and(queryWrapper -> queryWrapper
                        .isNull(Org::getIsDissolve)
                        .or()
                        .ne(Org::getIsDissolve, CommonConstant.ONE_INT)).isNull(Org::getDeleteTime)
                .select(Org::getId, Org::getOrgCode, Org::getCode, Org::getD01Code);
        return list(lambdaQueryWrapper);
    }

    @Override
    public boolean updateOrgLeaf(String code, String orgCode) {
        return orgMapper.updateOrgLeaf(code, orgCode) > 0;
    }

    @Override
    public boolean updateNotOrgLeaf(String code, String orgCode) {
        return orgMapper.updateNotOrgLeaf(code, orgCode) > 0;
    }

    @Override
    public boolean isSubOrg(String parentOrgId, String subOrgId) {
        return orgMapper.isSubOrg(parentOrgId, subOrgId) != null;
    }

    /**
     * 导出组织数据
     *
     * @param orgListDTO
     */
    @Override
    public void exportOrgData(OrgListDTO orgListDTO) {
        // 导出类别：1-党组织基本信息  2-应换 3-未换 4-已换
        final Integer type = orgListDTO.getType();
        String isExportAll = orgListDTO.getIsExportAll();
        String orgCode = orgListDTO.getOrgCode();
        // 条件
        Condition condition;
        if (CommonConstant.ONE.equals(isExportAll)) {
            // 导出下级所有
            condition = field(name("org_code"), String.class).like(orgCode + "%");
        } else {
            // 导出直属下级
            condition = field(name("org_code")).eq(orgCode)
                    .or(field(name("org_code")).like(orgCode + "___"));
        }
        condition = this.getListCondition(orgListDTO, condition);

        // 换届信息  2-应换 3-未换 4-已换
        if (Objects.equals(type, 2) || Objects.equals(type, 3) || Objects.equals(type, 4)) {
            condition = this.getOrgElectCondition(condition, type);
        }

        // 字段集合
        List<ExportDTO> exportList = orgListDTO.getExportList();
        String tableName = orgListDTO.getTableName();

        // 生成文件名称
        String fileName = CacheUtils.getOrgNameByOrgCode(orgCode) + "组织名册";
        Map<String, String> d04Map = com.zenith.front.core.kit.CollectionUtil.listRecordToMap(CacheUtils.getDic(DictConstant.DICT_D04), "key", "name");

        // 总条数
        Long total = this.getOrgTotal(condition);

        // 匹配是否有导出党员数项，有则查询出所有放内存
        List<Mem> memList = new ArrayList<>();
        long count1 = exportList.stream().filter(e -> StrUtil.equalsAny(e.getField(), "org_mem_all_count", "org_mem_count", "org_developMem_count")).count();
        if(count1 > 0L) {
            memList = queryMemList(orgCode, "1", "2");
        }

        // 匹配是否有导出入党申请人数项，有则查询出所有放内存
        List<MemDevelop> memDevelopList = new ArrayList<>();
        long count2 = exportList.stream().filter(e -> StrUtil.equalsAny(e.getField(), "org_applyMem_count", "org_activist_count", "org_developObject_count")).count();
        if(count2 > 0L) {
            memDevelopList = queryMemDevelopList(orgCode, "3", "4", "5");
        }

        // 处理数据并导出
        final List<Mem> finalMemList = memList;
        final List<MemDevelop> finalMemDevelopList = memDevelopList;

        ExcelUtil.processorAndExport(total, exportList, tableName, condition, fileName, "组织名册", response, record -> {
            // 是否劳务派遣
            Integer isRetire = record.getInt("是否离退休党组织");
            if (isRetire != null) {
                record.set("是否离退休党组织", isRetire == CommonConstant.ONE_INT ? CommonConstant.TRUE_STRING : CommonConstant.false_STRING);
            }
            // 是否农民工
            Integer isFlow = record.getInt("是否流动党员党组织");
            if (isFlow != null) {
                record.set("是否流动党员党组织", isFlow == CommonConstant.ONE_INT ? CommonConstant.TRUE_STRING : CommonConstant.false_STRING);
            }
            String mainUnitType = record.getStr("主单位类型");
            if (mainUnitType != null) {
                record.set("主单位类型", d04Map.get(mainUnitType));
            }
            //党组织层级码
            String orgLevelCode = record.getStr("层级码");
            //组织党员人数
            MemListDTO memListDTO = new MemListDTO();
            memListDTO.setMemOrgCode(orgLevelCode);
            memListDTO.setPageNum(1);
            memListDTO.setPageSize(1);
            memListDTO.setSearchType("1");
            memListDTO.setSubordinate("1");
            Integer orgMemAllCount = record.getInt("组织党员人数");
            if (orgMemAllCount != null) {
                record.set("组织党员人数", finalMemList.stream().filter(e -> e.getMemOrgCode().startsWith(orgLevelCode)).count());
//                OutMessage memTotal = memServiceImpl.getList(memListDTO);
//                if (memTotal.getCode() == 0) {
//                    Page<MemVO> percentRecordList = (Page<MemVO>) memTotal.getData();
//                    if (ObjectUtil.isNotNull(percentRecordList) && ObjectUtil.isNotNull(percentRecordList.getTotal()) && percentRecordList.getTotal() > 0L) {
//                        long total1 = percentRecordList.getTotal();
//                        record.set("组织党员人数", total1);
//                    }
//                }
            }
            //组织正式党员人数
            memListDTO.setD08CodeList(Collections.singletonList("1"));
            Integer orgMemCount = record.getInt("组织正式党员人数");
            if (orgMemCount != null) {
                record.set("组织正式党员人数", finalMemList.stream().filter(e -> e.getMemOrgCode().startsWith(orgLevelCode)
                        && Objects.equals(e.getD08Code(), "1")).count());

//                OutMessage memTotal1 = memServiceImpl.getList(memListDTO);
//                if (memTotal1.getCode() == 0) {
//                    Page<MemVO> percentRecordList = (Page<MemVO>) memTotal1.getData();
//                    if (ObjectUtil.isNotNull(percentRecordList) && ObjectUtil.isNotNull(percentRecordList.getTotal()) && percentRecordList.getTotal() > 0L) {
//                        long total1 = percentRecordList.getTotal();
//                        record.set("组织正式党员人数", total1);
//                    }
//                }
            }
            //组织预备党员人数
            memListDTO.setD08CodeList(Collections.singletonList("2"));
            Integer orgDevelopMemCount = record.getInt("组织预备党员人数");
            if (orgDevelopMemCount != null) {
                record.set("组织预备党员人数", finalMemList.stream().filter(e -> e.getMemOrgCode().startsWith(orgLevelCode)
                        && Objects.equals(e.getD08Code(), "2")).count());
//                OutMessage memTotal2 = memServiceImpl.getList(memListDTO);
//                if (memTotal2.getCode() == 0) {
//                    Page<MemVO> percentRecordList = (Page<MemVO>) memTotal2.getData();
//                    if (ObjectUtil.isNotNull(percentRecordList) && ObjectUtil.isNotNull(percentRecordList.getTotal()) && percentRecordList.getTotal() > 0L) {
//                        long total1 = percentRecordList.getTotal();
//                        record.set("组织预备党员人数", total1);
//                    }
//                }
            }
            //组织入党申请人人数
            memListDTO.setD08CodeList(Collections.singletonList("5"));
            Integer orgApplyMemCount = record.getInt("组织入党申请人人数");
            if (orgApplyMemCount != null) {
                record.set("组织入党申请人人数", finalMemDevelopList.stream().filter(e -> e.getDevelopOrgCode().startsWith(orgLevelCode)
                        && Objects.equals(e.getD08Code(), "5")).count());
//                OutMessage memTotal3 = memDevelopService.getList(memListDTO);
//                if (memTotal3.getCode() == 0) {
//                    Page<MemVO> percentRecordList = (Page<MemVO>) memTotal3.getData();
//                    if (ObjectUtil.isNotNull(percentRecordList) && ObjectUtil.isNotNull(percentRecordList.getTotal()) && percentRecordList.getTotal() > 0L) {
//                        long total1 = percentRecordList.getTotal();
//                        record.set("组织入党申请人人数", total1);
//                    }
//                }
            }
            //组织积极分子人数
            memListDTO.setD08CodeList(Collections.singletonList("4"));
            Integer orgActivistCount = record.getInt("组织积极分子人数");
            if (orgActivistCount != null) {
                record.set("组织积极分子人数", finalMemDevelopList.stream().filter(e -> e.getDevelopOrgCode().startsWith(orgLevelCode)
                        && Objects.equals(e.getD08Code(), "4")).count());
//                OutMessage memTotal4 = memDevelopService.getList(memListDTO);
//                if (memTotal4.getCode() == 0) {
//                    Page<MemVO> percentRecordList = (Page<MemVO>) memTotal4.getData();
//                    if (ObjectUtil.isNotNull(percentRecordList) && ObjectUtil.isNotNull(percentRecordList.getTotal()) && percentRecordList.getTotal() > 0L) {
//                        long total1 = percentRecordList.getTotal();
//                        record.set("组织积极分子人数", total1);
//                    }
//                }
            }
            //组织发展对象人数
            memListDTO.setD08CodeList(Collections.singletonList("3"));
            Integer orgDevelopObjectCount = record.getInt("组织发展对象人数");
            if (orgDevelopObjectCount != null) {
                record.set("组织发展对象人数", finalMemDevelopList.stream().filter(e -> e.getDevelopOrgCode().startsWith(orgLevelCode)
                        && Objects.equals(e.getD08Code(), "3")).count());
//                OutMessage memTotal5 = memDevelopService.getList(memListDTO);
//                if (memTotal5.getCode() == 0) {
//                    Page<MemVO> percentRecordList = (Page<MemVO>) memTotal5.getData();
//                    if (ObjectUtil.isNotNull(percentRecordList) && ObjectUtil.isNotNull(percentRecordList.getTotal()) && percentRecordList.getTotal() > 0L) {
//                        long total1 = percentRecordList.getTotal();
//                        record.set("组织发展对象人数", total1);
//                    }
//                }
            }
        });
    }

    /**
     * 根据条件查询党员数
     * @param orgLevelCode
     * @param d08Code
     * @return
     */
    private List<Mem> queryMemList(String orgLevelCode, String... d08Code) {
        LambdaQueryWrapper<Mem> wrapper = Wrappers.lambdaQuery();
        wrapper.likeRight(Mem::getMemOrgCode, orgLevelCode)
                .isNull(Mem::getDeleteTime).and(et -> et.isNull(Mem::getIsTransfer).or().ne(Mem::getIsTransfer, 1))
                .in(Mem::getD08Code, Arrays.asList(d08Code));
        wrapper.select(Mem::getMemOrgCode, Mem::getD08Code);
        return memService.list(wrapper);
    }

    /**
     * 根据查询如入党申请人数
     * @param orgLevelCode
     * @param d08Code
     * @return
     */
    private List<MemDevelop> queryMemDevelopList(String orgLevelCode, String... d08Code) {
        LambdaQueryWrapper<MemDevelop> wrapper = Wrappers.lambdaQuery();
        wrapper.likeRight(MemDevelop::getDevelopOrgCode, orgLevelCode)
                .isNull(MemDevelop::getDeleteTime).and(et -> et.isNull(MemDevelop::getIsTransfer).or().ne(MemDevelop::getIsTransfer, 1))
                .in(MemDevelop::getD08Code, Arrays.asList(d08Code));
        wrapper.select(MemDevelop::getDevelopOrgCode, MemDevelop::getD08Code);
        return memDevelopService.list(wrapper);
    }

    /**
     * 获取查询条件
     *
     * @param orgListDTO
     * @param condition
     * @return
     */
    public Condition getListCondition(OrgListDTO orgListDTO, Condition condition) {
        // 组织类别
        List<String> d01CodeList = orgListDTO.getD01CodeList();
        // 所在单位情况代码
        List<String> d02CodeList = orgListDTO.getD02CodeList();
        // 隶属关系
        List<String> d03CodeList = orgListDTO.getD03CodeList();
        // 组织大类
        List<String> orgTypeList = orgListDTO.getOrgTypeList();
        // 单位类别
        List<String> d04CodeList = orgListDTO.getD04CodeList();
        // 组织名称
        String orgName = orgListDTO.getOrgName();

        // 组织类别
        if (!CollectionUtil.isEmpty(d01CodeList)) {
            condition = condition.and(field(name("d01_code"), String.class).in(d01CodeList));
        }

        // 所在单位情况代码
        if (!CollectionUtil.isEmpty(d02CodeList)) {
            condition = condition.and(field(name("d02_code"), String.class).in(d02CodeList));
        }

        // 隶属关系
        if (!CollectionUtil.isEmpty(d03CodeList)) {
            condition = condition.and(field(name("d03_code"), String.class).in(d03CodeList));
        }

        // 组织大类
        if (CollectionUtil.isNotEmpty(orgTypeList)) {
            condition = condition.and(field(name("org_type"), String.class).in(orgTypeList));
        }

        // 单位类别
        if (CollectionUtil.isNotEmpty(d04CodeList)) {
            condition = condition.and(field(name("d04_code"), String.class).in(d04CodeList));
        }
        if (!StrKit.isBlank(orgName)) {
            condition = condition.and(or(field(name("name"), String.class).like("%" + orgName + "%"))
                    .or(field(name("short_name"), String.class).like("%" + orgName + "%")
                            .or(field(name("pinyin"), String.class).like("%" + orgName + "%"))));
        }
        condition = condition.and(field(name("delete_time")).isNull()).and(field(name("is_dissolve")).isNull().or(field(name("is_dissolve")).ne(1)));
        return condition;
    }

    /**
     * 换届信息导出条件
     *
     * @param condition
     * @param type      2-应换 3-未换 4-已换
     * @return
     */
    public Condition getOrgElectCondition(Condition condition, Integer type) {
        StringBuilder sb = new StringBuilder();
        sb.append(" exists (SELECT 1\n" +
                "FROM (\n" +
                "    SELECT\n" +
                "        org_code,\n" +
                "        tenure_start_date,\n" +
                "        tenure_end_date,\n" +
                "        ROW_NUMBER() OVER (PARTITION BY org_code ORDER BY tenure_end_date DESC) AS rn\n" +
                "    FROM ccp_org_committee_elect where delete_time is null and (is_history != 1 or is_history is null)\n" +
                ") AS tm\n" +
                "WHERE  tm.org_code = ccp_org_all.code");
        // 取最新一条
        sb.append("  and tm.rn = 1 ");
        // 2-应换 其中有条数据结束时间超过当前时间，并且是本地年的
        sb.append(" and (to_char(tm.tenure_start_date, 'yyyy') = to_char(now(), 'yyyy')  or to_char(tm.tenure_end_date, 'yyyy') <= to_char(now(), 'yyyy')) ");

        // 4-（应换已换）已换届=已经换届的党组织（届次的开始时间是2024年或者届次的结束时间是2024年，并且已经产生新的届次的党组织）
        if (Objects.equals(type, 4)) {
            sb.append(" and to_char(tm.tenure_start_date, 'yyyy') = to_char(now(), 'yyyy') ");
        }
        // 3-（应换未换）未换届= 应该换届的党组织（届次结束时间是2024年，但是没产生新届次的党组织）
        if (Objects.equals(type, 3)) {
            sb.append("  and to_char(tm.tenure_end_date, 'yyyy') <= to_char(now(), 'yyyy') ");
        }
        sb.append(" )");
        //TODO 2024.11.28 这三个导出要排除单位类别是1、A开头的组织，这些组织没有班子成员这一项。
        sb.append(" AND ( d01_code in ('61','62','631','632','633','634','911','921','931','932') )");
        return condition.and(sb.toString());
    }

    /**
     * 获取党组织总数
     *
     * @param condition
     * @return
     */
    public Long getOrgTotal(Condition condition) {
        SelectConditionStep<Record1<Integer>> record1s = DbUtil.DSL_CONTEXT.select(DSL.count(field("1")))
                .from(name("ccp_org_all"))
                .where(condition);
        return feeOrderMapper.getTotalBysql(record1s.toString());
    }

    @Override
    public OutMessage split(OrgSplitDTO dto) {
        String name = dto.getName();
        if (StrKit.isBlank(name)) {
            return new OutMessage<>(Status.ORG_NAME_EROOR);
        }
        boolean notStartsWith = !name.startsWith(OrgConstant.PRE_ORG_NAME) && !name.startsWith(OrgConstant.PRE_ORG_NAME_ALL);
        if (notStartsWith || !name.endsWith(OrgConstant.SUF_ORG_NAME)) {
            // 组织名称不符合规范
            return new OutMessage<>(Status.ORG_NAME_EROOR);
        }
        String requestCode = dto.getCode();
        Org originalOrg = this.findOrgByCode(requestCode);
        if (Objects.isNull(originalOrg)) {
            return new OutMessage<>(Status.ORG_NOT_EXIST);
        }
        String parentCode = originalOrg.getParentCode();
        Org parentOrg = this.findOrgByCode(parentCode);
        if (Objects.isNull(parentOrg)) {
            return new OutMessage<>(Status.ORG_NOT_EXIST);
        }

        String orgCode = originalOrg.getOrgCode();
        final UserTicket userTicket = USER_CONTEXT.get();
        if (!orgCode.startsWith(userTicket.getUserRolePermission().getOrgCode())) {
            return new OutMessage<>(Status.PERMISSION_DENIED);
        }
        List<Long> memIds = dto.getMemIds();
        if (CollUtil.isEmpty(memIds)) {
            return new OutMessage<>(Status.PLEASE_SELECT_A_PARTY_MEMBER);
        }
        final String account = userTicket.getUser().getAccount();
        Org org = new Org();
        org.setParentOrgName("");
        org.setCode(StrKit.getRandomUUID());
        org.setEsId(CodeUtil.getEsId());
        org.setZbCode(CodeUtil.getZbCode());
        String newOrgCode = this.createOrgCode(parentOrg);
        org.setOrgCode(newOrgCode);
        org.setName(name);
        org.setShortName(name);
        org.setPinyin(PinyinUtil.getPinyin(name));
        org.setIsLeaf(CommonConstant.ONE_INT);
        org.setParentCode(parentCode);
        org.setD01Code(originalOrg.getD01Code());
        org.setOrgType(originalOrg.getOrgType());
        org.setD03Code(originalOrg.getD03Code());
        org.setD02Code(originalOrg.getD02Code());
        org.setIsRetire(originalOrg.getIsRetire());
        org.setIsFlow(originalOrg.getIsFlow());
        org.setSecretaryCode(originalOrg.getSecretaryCode());
        org.setContacter(originalOrg.getContacter());
        org.setContactPhone(originalOrg.getContactPhone());
        org.setFaxNumber(originalOrg.getFaxNumber());
        org.setPostAddress(originalOrg.getPostAddress());
        org.setPostCode(originalOrg.getPostCode());
        org.setUnits(originalOrg.getUnits());
        org.setCreateDate(new Date());
        org.setSort(originalOrg.getSort());
        org.setCreateTime(new Date());
        org.setUpdateTime(new Date());
        org.setKeywords(originalOrg.getKeywords());
        org.setRemark(originalOrg.getRemark());
        org.setAddressPosition(originalOrg.getAddressPosition());
        org.setTimestamp(new Date());
        org.setRatio(originalOrg.getRatio());
        org.setMainUnitCode(originalOrg.getMainUnitCode());
        org.setIsHistory(originalOrg.getIsHistory());
        org.setUpdateAccount(account);
        org.setSecretary(originalOrg.getSecretary());

        List<Mem> memList = memService.listByIds(memIds);
        memList.forEach(val -> {
            val.setMemOrgCode(newOrgCode);
            val.setUpdateTime(new Date());
            val.setUpdateAccount(account);
        });
        OrgAll orgAll = new OrgAll();
        BeanUtils.copyProperties(org, orgAll);
        orgAll.setParentName(originalOrg.getName());
        // 设置字典表转换
        this.setOrgDict(orgAll);
        boolean insert = orgMapper.insert(org) > 0;
        boolean insert1 = orgAllMapper.insert(orgAll) > 0;
        boolean updateBatchById = memService.updateBatchById(memList);
        return new OutMessage<>((insert && updateBatchById && insert1) ? Status.SUCCESS : Status.FAIL);
    }

    @Override
    public OutMessage combined(OrgCombinedDTO dto) {
        String code = dto.getCode();
        List<String> codes = dto.getCodes();
        codes.add(code);
        Org parentOrg = this.findOrgByCode(code);
        if (Objects.isNull(parentOrg)) {
            return new OutMessage<>(Status.ORG_NOT_EXIST);
        }
        String orgCode = parentOrg.getOrgCode();
        String account = USER_CONTEXT.get().getUser().getAccount();
        List<Org> orgColl = orgMapper.selectList(new QueryWrapper<Org>().lambda().in(Org::getCode, codes).isNull(Org::getDeleteTime));
        List<String> collect = orgColl.stream().map(Org::getCode).collect(Collectors.toList());
        List<Mem> memList = memService.list(new QueryWrapper<Mem>().lambda().in(Mem::getOrgCode, collect).isNull(Mem::getDeleteTime));
        // 比较合并党组织层级码，如果合并过程中有父级党组织会出现断层，所以以上级层级码为准
        List<String> levelCodes = orgColl.stream().map(Org::getOrgCode).filter(StrUtil::isNotEmpty).collect(Collectors.toList());
        String levelCode = levelCodes.stream().min(Comparator.comparing(String::length)).orElse("");
        if (StrUtil.isNotEmpty(levelCode) && orgCode.length() > levelCode.length()) {
            Org byOrgCode = this.findByOrgCode(levelCode);
            if (Objects.nonNull(byOrgCode)) {
                parentOrg.setOrgCode(levelCode);
                parentOrg.setParentCode(byOrgCode.getParentCode());
            }
        }
        memList.forEach(val -> {
            val.setMemOrgCode(parentOrg.getOrgCode());
            val.setOrgCode(parentOrg.getCode());
            val.setOrgZbCode(parentOrg.getZbCode());
            val.setUpdateTime(new Date());
            val.setUpdateAccount(account);
            val.setBranchOrgZbCode(parentOrg.getZbCode());
            val.setBranchOrgName(parentOrg.getName());
            val.setBranchOrgKey(parentOrg.getCode());
            val.setBranchOrgCode(parentOrg.getOrgCode());
        });

        List<Org> orgList = orgMapper.selectList(new LambdaQueryWrapper<Org>()
                .in(Org::getCode, codes)
                .ne(Org::getCode, code)
                .isNull(Org::getDeleteTime)
        );
        orgList.forEach(val -> val.setDeleteTime(new Date()));

        List<OrgAll> orgAllList = orgAllMapper.selectList(new LambdaQueryWrapper<OrgAll>()
                .in(OrgAll::getCode, codes)
                .ne(OrgAll::getCode, code)
                .isNull(OrgAll::getDeleteTime)
        );
        orgAllList.forEach(val -> val.setDeleteTime(new Date()));

        boolean updateBatchById = true;
        if (CollUtil.isNotEmpty(orgList)) {
            updateBatchById = updateBatchById(orgList);
        }

        boolean updateOrgAllBatchById = true;
        if (CollUtil.isNotEmpty(orgAllList)) {
            updateOrgAllBatchById = orgAllService.updateBatchById(orgAllList);
        }
        boolean batchById = true;
        if (CollUtil.isNotEmpty(memList)) {
            batchById = memService.updateBatchById(memList);
        }
        updateById(parentOrg);
        orgAllService.update(new LambdaUpdateWrapper<OrgAll>()
                .set(OrgAll::getOrgCode, parentOrg.getOrgCode()).set(OrgAll::getParentCode, parentOrg.getParentCode())
                .eq(OrgAll::getCode, code)
                .isNull(OrgAll::getDeleteTime));

        //sys_user_role_permission  sys_user    sys_role
        return updateBatchById && batchById && updateOrgAllBatchById ? new OutMessage<>(Status.SUCCESS) : new OutMessage<>(Status.FAIL);
    }

    @Override
    public OutMessage dissolve(OrgDissolveDTO dto) {
        String code = dto.getCode();
        Org org = this.findOrgByCode(code);
        if (Objects.isNull(org)) {
            return new OutMessage<>(Status.ORG_NOT_EXIST);
        }
        String orgCode = org.getOrgCode();
        // 下级组织
        List<Org> orgList = this.findOrgByParentCode(orgCode);
        if (CollectionUtil.isNotEmpty(orgList)) {
            return new OutMessage<>(Status.NEED_DEL_SUB_ORG);
        }
        // 党员
        LambdaQueryWrapper<Mem> condition = new LambdaQueryWrapper<Mem>()
                .likeRight(Mem::getMemOrgCode, orgCode)
                .in(Mem::getD08Code, "1", "2")
                // TODO: 2021/12/24 转接党员
                .and(w -> w.ne(Mem::getIsTransfer, CommonConstant.ONE_INT).or().isNull(Mem::getIsTransfer))
                .isNull(Mem::getDeleteTime);
        Long total = memService.getMemTotal(condition);
        if (total != null && total > 0) {
            return new OutMessage<>(Status.NEED_DEL_ORG_MEM);
        }

        List<UnitOrgLinked> unitOrgLinkedList = unitOrgLinkedService.findByOrgCode(code);
        if (CollectionUtil.isNotEmpty(unitOrgLinkedList)) {
            return new OutMessage<>(Status.NEED_CANCEL_UNIT);
        }

        // 发展党员
        // TODO: 2019/5/28 处理发展指标
        Long developTotal = memDevelopService.getDevelopTotal(
                new LambdaQueryWrapper<MemDevelop>()
                        .likeRight(MemDevelop::getDevelopOrgCode, orgCode)
                        // TODO: 2021/12/9 预备党员退回，垃圾数据未清理
                        .in(MemDevelop::getD08Code, Arrays.asList(CommonConstant.THREE, CommonConstant.FOUR, CommonConstant.FIVE))
                        .isNull(MemDevelop::getDeleteTime)
        );
        if (developTotal != null && developTotal > 0) {
            return new OutMessage<>(Status.NEED_DEL_ORG_DEVELOP_MEM);
        }

        // TODO: 2021/11/29 现在业务逻辑变更为只需要校验党员、发展党员、以及下面是否有党支部等情况，其他得多余信息直接处理掉
        // 关联单位
        // TODO: 2022/1/6 根据客户要求，以客户端查询单位条件来验证 (mainUnitOrgCode 或者 manageUnitOrgCode)

        // todo: 2024/01/05 unitAll表CreateUnitOrgCode 出现为空的情形导致党组织删除了但是单位存在不能展示也不能新建占用该信用码单位，从unit表中去查询验证
        List<Unit> list = unitService.list(new QueryWrapper<Unit>().lambda().and(wrapper -> wrapper
                .like(Unit::getManageUnitOrgCode, orgCode)
                .or()
                .and(queryWrapper -> queryWrapper
                        .like(Unit::getMainUnitOrgCode, orgCode)
                        .or()
                        .like(Unit::getCreateUnitOrgCode, orgCode)
                )
        ).isNull(Unit::getDeleteTime));
        if (CollectionUtil.isNotEmpty(list)) {
            return new OutMessage<>(Status.NEED_CANCEL_UNIT);
        }

        // 多重党员
        Long memMultipleTotal = memManyService.getTotal(orgCode);
        if (memMultipleTotal != null && memMultipleTotal > 0) {
            return new OutMessage<>(Status.NEED_DEL_ORG_MANY_MEM);
        }

        // 党代表
        Long representativeTotal = representativeService.getTotalByOrgCode(orgCode);
        if (representativeTotal != null && representativeTotal > 0) {
            return new OutMessage<>(Status.NEED_DEL_ORG_REPRESENTATIVE);
        }

        // 届次信息
        Long representativeElectTotal = representativeElectService.getTotalByOrgCode(orgCode);
        if (representativeElectTotal != null && representativeElectTotal > 0) {
            return new OutMessage<>(Status.NEED_DEL_ORG_REPRESENTATIVE);
        }

        // 联络机构
        Long representativeContactTotal = representativeContactService.getTotalByOrgCode(orgCode);
        if (representativeContactTotal != null && representativeContactTotal > 0) {
            return new OutMessage<>(Status.NEED_DEL_ORG_REPRESENTATIVE);
        }

        // 用户账号情况
        List<Org> orgListByOrgCode = this.findOrgListByOrgCode(org.getOrgCode());
        if (orgListByOrgCode.size() > CommonConstant.ZERO_INT) {
            List<String> orgCodeS = orgListByOrgCode.stream().map(Org::getCode).collect(Collectors.toList());
            List<User> accountByOrgS = iUserService.findAccountByOrgs(orgCodeS);
            if (ObjectUtil.isNotNull(accountByOrgS) && accountByOrgS.size() > CommonConstant.ZERO_INT) {
                return new OutMessage<>(Status.USER_DEL_ORG_ACCOUNT);
            }
        }

        //如果有组织关系转接正在进行，不能移动
        List<Org> allSubOrgByOrgCode = this.findAllSubOrgByOrgCode(org.getOrgCode());
        if (allSubOrgByOrgCode.size() > 0) {
            List<String> orgId = allSubOrgByOrgCode.stream().map(Org::getCode).collect(Collectors.toList());
            LambdaQueryWrapper<TransferRecord> srcInsql = new LambdaQueryWrapper<>();
            srcInsql.eq(TransferRecord::getStatus, CommonConstant.ZERO_INT).in(TransferRecord::getSrcOrgId, orgId);
            List<TransferRecord> srcList = transferRecordService.list(srcInsql);
            LambdaQueryWrapper<TransferRecord> tarInsql = new LambdaQueryWrapper<>();
            tarInsql.eq(TransferRecord::getStatus, CommonConstant.ZERO_INT).in(TransferRecord::getTargetOrgId, orgId);
            List<TransferRecord> tarList = transferRecordService.list(tarInsql);
            //直接撤销当前组织及其下属组织发起的关系转接
            if (srcList.size() > 0) {
                //srcList.forEach(transferRecord -> this.undo(transferRecord.getId(),"因当发起党组织或接收党组织发生架构调整系统自动撤回所有关系转接",false));
                //需求变更为自己或者自己节点发起得需要提示取消
                return new OutMessage<>(Status.NEED_DEL_ORG_TRANSFER);
            }
            //直接撤销转入当前组织及其下属组织的关系转接
            if (tarList.size() > 0) {
                for (TransferRecord transferRecord : tarList) {
                    //这里分为两种情况，如果是同一个节点发起的转入，直接走撤销接口即可(撤销接口会自动恢复人员情况)
                    String tarSrcOrgId = transferRecord.getSrcOrgId();
                    Org tarSrcOrg = this.findOrgByCode(tarSrcOrgId);
                    //发起的组织就是在当前数据节点直接走撤销接口
                    if (ObjectUtil.isNotNull(tarSrcOrg)) {
                        transferRecordService.undo(transferRecord.getId(), "因当发起党组织或接收党组织发生架构调整系统自动撤回所有关系转接", false);
                    } else {
                        //发起的组织不在当前数据节点证明是是其他数据节点发起到这边接收的转接
                        String replaceUrl = sync_flow_push.replace("/api/flow/push", "");
                        // TODO: 2021/11/17 请求中间交换区中，转接到我的目的节点的数据
                        JSONObject postData = new JSONObject();
                        postData.put("record", transferRecord.getId());
                        String rultMessageAsTypeOne = HttpKit.doPost(replaceUrl + "/transfer/undoData", postData, "UTF-8");
                        System.out.println("请求撤销中间交换区地址反馈结果=====>" + rultMessageAsTypeOne);
                    }
                }
            }
        }

        // 流动党员
        List<MemFlow> totalByOrgCode = memFlowService.getTotalByOrgCode(orgCode);
        if (totalByOrgCode != null && totalByOrgCode.size() > 0) {
            totalByOrgCode.forEach(memFlow -> memFlow.setDeleteTime(new Date()));
            memFlowService.updateBatchById(totalByOrgCode);
            //return new OutMessage<>(Status.NEED_DEL_ORG_FLOW);
        }
        // 活动
        Long activityTotal = activityService.getTotalByOrgCode(orgCode);
        if (activityTotal != null && activityTotal > 0) {
            return new OutMessage<>(Status.NEED_DEL_ORG_ACTIVITY);
        }
        // 党费
        // TODO: 2019/5/28 党费怎么操作

        // TODO: 2022/2/25 党组织进行撤销的时候，下面的党代表需要同步进行置空和删除，不然会出现党代表没有地方挂的情况
        handlingPartyRepresentatives(org.getCode());
        // TODO: 2022/2/25  党组织进行撤销的时候，相应的行业党组织也需要进行联动撤销，不然会出现组织已经不存在的情况下，依旧是一个行业党组织
        handlingIndustryPartyOrganization(org.getCode());
        // TODO: 2022/2/28 党组织进行撤销的时候，相应的软弱涣散党组织也要进行撤销以及删除处理，不然会出现这个组织已经撤销，但是这个组织还是一个软弱涣散组织
        handlingOrgSlack(org.getCode());
        // TODO: 2022/2/28 处理党组(党组性质党委)管理
        handlingOrgParty(org.getCode());
        // TODO: 2022/2/28 党组织进行撤销的时候，审批预备党员党组织也要进行相应的权限删除，不然会导致组织关系找不到
        handlingOrgDevelopRights(org.getCode());
        // TODO: 2022/3/1 党组织撤销的时候，需要将组织所涉及的相关信息置空，涵盖组织的班子成员所涉及的人员职务信息
        handlingOrgCommittee(org.getCode());
        // TODO: 2022/3/1 党组织撤销的时候，党组织所涉及的奖惩信息
        handlingOrgReward(org.getCode());
        // TODO: 2022/3/1 党组织撤销的时候，党组织所涉及的培训情况
        handlingMemTrain(org.getCode());

        OrgAll orgAll = orgAllService.findByCode(code);
        Date now = new Date();
        boolean updateOrgAllById = true;
        if (Objects.nonNull(orgAll)) {
            orgAll.setTimestamp(now);
            orgAll.setUpdateTime(now);
            orgAll.setDate(Objects.nonNull(dto.getDate()) ? dto.getDate() : now);
            orgAll.setDocumentNum(dto.getDocumentNum());
            orgAll.setReason(dto.getReason());
            orgAll.setIsDissolve(CommonConstant.ONE_INT);
            orgAll.setDeleteTime(now);
            updateOrgAllById = orgAllService.updateById(orgAll);
        }
        String account = USER_CONTEXT.get().getUser().getAccount();

        org.setTimestamp(now);
        org.setUpdateAccount(account);
        org.setUpdateTime(now);
        org.setDate(Objects.nonNull(dto.getDate()) ? dto.getDate() : now);
        org.setDocumentNum(dto.getDocumentNum());
        org.setReason(dto.getReason());
        org.setIsDissolve(CommonConstant.ONE_INT);
        org.setDeleteTime(now);
        boolean updateOrgById = this.updateById(org);
        this.updateOrgParentLeaf(org.getParentCode());
        //todo: ******** 同步农村党建调度表
        iMemReportService.syncOrgCommittee(org.getCode());
        //撤销后，更新到交换区
        //移动党组织更新完成后，同步组织信息到中间交换区
        List<OrgAll> allOrgByOrgCode = this.findOrgByCodeNotDeleteTime(code);
        if (allOrgByOrgCode.size() > 0) {
            JSONArray postJsonArray = new JSONArray();
            allOrgByOrgCode.forEach(orgModel -> {
                JSONObject jsonObject = JSONObject.parseObject(JSONObject.toJSONString(orgModel));
                jsonObject.put("exchangeKey", exchange_nginx_key);
                postJsonArray.add(jsonObject);
            });
            JSONObject postJson = new JSONObject();
            postJson.put("data", postJsonArray);
            String replaceUrl = sync_flow_push.replace("/api/flow/push", "");
            String rultMessage = HttpKit.doPost(replaceUrl + "/org/addOrg", postJson, "UTF-8");
            System.out.println("组织中间交换区组织信息反馈====》" + rultMessage);
        }
//        if (updateOrgAllById) {
//            //同步到驻村系统总库
//            Map<String, Object> map = new HashMap<>(10);
//            map.put("id", orgAll.getCode());
//            villageCommunityClient.removeOrgInfo(map);
//        }
        return new OutMessage<>((updateOrgById && updateOrgAllById) ? Status.SUCCESS : Status.FAIL);
    }
//    private List<UnitOrgLinked> getUnitOrgLinkedList(OrgDTO orgDTO) {
//        // 21 与上级党组织在同一法人单位
//        String d02Code = orgDTO.getD02Code();
//        if (CommonConstant.TWENTY_ONE.equals(d02Code)) {
//            return null;
//        } else {
//            List<UnitOrgLinkedDTO> linkedDTOList = orgDTO.getLinkedDTOList();
//            List<UnitOrgLinked> unitOrgLinkedList = new ArrayList<>();
//            for (UnitOrgLinkedDTO unitOrgLinkedDTO : linkedDTOList) {
//                UnitOrgLinked unitOrgLinked = new UnitOrgLinked();
//                try {
//                    BeanUtils.copyProperties(unitOrgLinkedDTO, unitOrgLinked);
//                } catch (Exception e) {
//                    e.printStackTrace();
//                }
//
//                unitOrgLinked.setIsOrgMain(unitOrgLinkedDTO.getIsOrgMain());
//                unitOrgLinked.setIsUnitMain(unitOrgLinkedDTO.getIsUnitMain());
//                unitOrgLinked.setOrgCode(orgDTO.getCode());
//                unitOrgLinked.setOrgName(orgDTO.getName());
//                unitOrgLinked.setOrgType(orgDTO.getD01Code());
//                unitOrgLinked.setOrgTypeCode(orgDTO.getOrgType());
//                unitOrgLinked.setCode(StrKit.getRandomUUID());
//                unitOrgLinked.setCreateTime(new Date());
//                unitOrgLinked.setUpdateTime(new Date());
//                unitOrgLinked.setUpdateAccount(USER_CONTEXT.get().getUser().getAccount());
//                unitOrgLinked.setTimestamp(new Date());
//                unitOrgLinked.setEsId(CodeUtil.getEsId());
//                unitOrgLinked.setId(null);
//                unitOrgLinkedList.add(unitOrgLinked);
//            }
//            return unitOrgLinkedList;
//        }
//    }

    /**
     * 行业党组织
     *
     * @param orgCode 组织code
     */
    private void handlingIndustryPartyOrganization(String orgCode) {
        //基础表
        List<OrgIndustry> orgIndustryList = orgIndustryService.findOrgIndustryByOrgCode(orgCode);
        if (CollUtil.isNotEmpty(orgIndustryList)) {
            orgIndustryList.forEach(t -> t.setDeleteTime(new Date()));
            orgIndustryService.updateBatchById(orgIndustryList, orgIndustryList.size());
        }
        //all表
        List<OrgIndustryAll> orgIndustryAllList = orgIndustryAllService.findOrgIndustryAllByOrgCode(orgCode);
        if (CollUtil.isNotEmpty(orgIndustryAllList)) {
            orgIndustryAllList.forEach(t -> t.setDeleteTime(new Date()));
            orgIndustryAllService.updateBatchById(orgIndustryAllList, orgIndustryAllList.size());
        }
    }

    /**
     * 软弱涣散党组织
     *
     * @param orgCode 组织code
     */
    private void handlingOrgSlack(String orgCode) {
        //基础表
        List<OrgSlack> orgSlackList = orgSlackService.findOrgSlackByOrgCode(orgCode);
        if (CollUtil.isNotEmpty(orgSlackList)) {
            orgSlackList.forEach(t -> t.setDeleteTime(new Date()));
            orgSlackService.updateBatchById(orgSlackList, orgSlackList.size());
        }
        //all表
        List<OrgSlackAll> orgIndustryAllList = orgSlackAllService.findOrgSlackAllByOrgCode(orgCode);
        if (CollUtil.isNotEmpty(orgIndustryAllList)) {
            orgIndustryAllList.forEach(t -> t.setDeleteTime(new Date()));
            orgSlackAllService.updateBatchById(orgIndustryAllList, orgIndustryAllList.size());
        }
    }

    /**
     * 处理党组(党组性质党委)管理
     *
     * @param orgCode 组织code
     */
    private void handlingOrgParty(String orgCode) {
        //基础表
        List<OrgParty> orgPartyList = orgPartyService.findOrgPartyByOrgCode(orgCode);
        if (CollUtil.isNotEmpty(orgPartyList)) {
            List<String> unitCodeList = new ArrayList<>();
            for (OrgParty orgParty : orgPartyList) {
                orgParty.setDeleteTime(new Date());
                //单位code
                unitCodeList.add(orgParty.getUnitCode());
            }
            orgPartyService.updateBatchById(orgPartyList, orgPartyList.size());
            //处理UnitAll同步信息
            if (CollUtil.isNotEmpty(unitCodeList)) {
                List<UnitAll> unitAllList = unitAllService.findByCodes(unitCodeList);
                if (CollUtil.isNotEmpty(unitAllList)) {
                    List<String> ids = unitAllList.stream().map(UnitAll::getId).collect(Collectors.toList());
                    if (CollUtil.isNotEmpty(ids)) {
                        unitAllService.update(Wrappers.<UnitAll>lambdaUpdate()
                                .in(UnitAll::getId, ids)
                                .set(UnitAll::getCreateUnitOrgCode, null)
                                .set(UnitAll::getCreatePartyGroup, null)
                                .set(UnitAll::getCreatePartyCommittee, null)
                                .set(UnitAll::getHasParty, null)
                                .set(UnitAll::getHasAuthority, null)
                        );
                    }
                }
            }
        }
    }

    /**
     * 审批预备党员权限管理
     *
     * @param orgCode 组织code
     */
    private void handlingOrgDevelopRights(String orgCode) {
        List<OrgDevelopRights> orgDevelopRightsList = orgDevelopRightsService.findOrgDevelopRightsByOrgCode(orgCode);
        if (CollUtil.isNotEmpty(orgDevelopRightsList)) {
            orgDevelopRightsList.forEach(t -> t.setDeleteTime(new Date()));
            orgDevelopRightsService.updateBatchById(orgDevelopRightsList, orgDevelopRightsList.size());
        }
    }

    /**
     * 党组织所涉及的奖惩信息
     *
     * @param orgCode 党组织code
     * @return
     */
    private void handlingOrgReward(String orgCode) {
        List<OrgReward> orgRewardList = orgRewardService.findOrgRewardByOrgCode(orgCode);
        if (CollUtil.isNotEmpty(orgRewardList)) {
            orgRewardList.forEach(orgReward -> orgReward.setDeleteTime(new Date()));
            orgRewardService.updateBatchById(orgRewardList, orgRewardList.size());
            //同步组织OrgAll奖惩信息 d42_code
        }
    }

    /**
     * 培训情况
     *
     * @param orgCode 党组织code
     */
    private void handlingMemTrain(String orgCode) {
        List<MemTrain> memTrainList = memTrainService.findTrainByOrgCode(orgCode);
        if (CollUtil.isNotEmpty(memTrainList)) {
            memTrainList.forEach(memTrain -> memTrain.setDeleteTime(new Date()));
            memTrainService.updateBatchById(memTrainList, memTrainList.size());
            //OrgAll同步问题，因涉及到组织会撤销，处理OrgAll表会更好，不处理也不影响统计
        }
    }

    /**
     * 更新父级节点的isLeaf
     */
    private void updateOrgParentLeaf(String parentCode) {
        if (StrUtil.isNotEmpty(parentCode)) {
            Org org = this.findOrgByCode(parentCode);
            if (Objects.nonNull(org)) {
                org.setIsLeaf(count(new LambdaQueryWrapper<Org>().eq(Org::getParentCode, org.getCode()).isNull(Org::getDeleteTime)) > 0 ? 0 : 1);
                this.updateById(org);
            }
            OrgAll orgAll = orgAllService.findByCode(parentCode);
            if (Objects.nonNull(orgAll)) {
                orgAll.setIsLeaf(orgAllService.count(new LambdaQueryWrapper<OrgAll>().eq(OrgAll::getParentCode, orgAll.getCode()).isNull(OrgAll::getDeleteTime)) > 0 ? 0 : 1);
                orgAllService.updateById(orgAll);
            }
        }
    }

    @Override
    public OutMessage<?> findOrgResume(String code) {
        Org org = this.findOrgByCode(code);
        if (Objects.isNull(org)) {
            return new OutMessage<>(Status.SUCCESS, new ArrayList<>());
        }
        if (!org.getOrgCode().startsWith(this.getUserManOrgCode())) {
            return new OutMessage<>(Status.PERMISSION_DENIED);
        }
        List<ResumeVo> resumeVos = new ArrayList<>();
        //党组织建立日期
        Date createDate = org.getCreateDate();
        memServiceImpl.getResumeVoList(resumeVos, createDate, "开始建立党组织");
        //换届
        List<OrgCommitteeElect> orgCommitteeElects = electMapper.selectList(new QueryWrapper<OrgCommitteeElect>().lambda().eq(OrgCommitteeElect::getElectOrgCode, org.getOrgCode())
                .isNull(OrgCommitteeElect::getDeleteTime).orderByAsc(OrgCommitteeElect::getTenureStartDate));
        orgCommitteeElects.forEach(elect -> memServiceImpl.getResumeVoList(resumeVos, elect.getTenureStartDate(), "党组织换届"));
        //奖惩
        List<OrgReward> orgRewards = orgRewardMapper.selectList(new LambdaQueryWrapper<OrgReward>().eq(OrgReward::getRewardOrgCode, org.getOrgCode())
                .isNull(OrgReward::getDeleteTime).orderByAsc(OrgReward::getStartDate));
        Map<String, String> d42Map = com.zenith.front.core.kit.CollectionUtil.listRecordToMap(CacheUtils.getDic("dict_d42"), "key", "name");
        for (OrgReward orgReward : orgRewards) {
            String d42NameP = d42Map.get(Objects.nonNull(orgReward.getD42Code()) ? orgReward.getD42Code().substring(0, 1) : "");
            memServiceImpl.getResumeVoList(resumeVos, orgReward.getStartDate(), "党组织" + d42NameP + "为" + orgReward.getD42Name());
        }
        //组织关系转接
        List<TransferRecord> transferRecords = transferRecordMapper.selectList(new LambdaQueryWrapper<TransferRecord>().eq(TransferRecord::getStatus, TransferRecordConstant.TRANSFERING)
                .eq(TransferRecord::getSrcOrgId, code).eq(TransferRecord::getType, "212").isNotNull(TransferRecord::getTargetOrgId));
        if (CollUtil.isNotEmpty(transferRecords)) {
            Optional<TransferRecord> transferRecord = transferRecords.stream().findAny();
            String desc = transferRecord.map(record -> record.getSrcOrgName() + "->" + record.getTargetOrgName()).orElse("");
            Date date = transferRecord.isPresent() ? transferRecord.get().getCreateTime() : new Date();
            memServiceImpl.getResumeVoList(resumeVos, date, "省内转接（整建制）" + desc);
        }

        return new OutMessage<>(Status.SUCCESS, memServiceImpl.processResumeList(resumeVos));
    }

    @Override
    public List<Org> findByOrgNames(List<String> orgNameList) {
        List<Org> orgList = new ArrayList<>();
        List<List<String>> lists = com.zenith.front.core.kit.CollectionUtil.subList(1000, orgNameList);
        for (List<String> list : lists) {
            LambdaQueryWrapper<Org> wrapper = new LambdaQueryWrapper<Org>()
                    .isNull(Org::getDeleteTime)
                    .in(Org::getName, orgNameList);
            orgList.addAll(list(wrapper));
        }
        return orgList;
    }

    @Override
    public List<OrgAll> findExchangeOrgByOrgCode(String orgCode) {
        LambdaQueryWrapper<OrgAll> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.and(queryWrapper -> queryWrapper
                        .isNull(OrgAll::getIsDissolve)
                        .or()
                        .ne(OrgAll::getIsDissolve, CommonConstant.ONE_INT)).likeRight(OrgAll::getOrgCode, orgCode)
                .select(OrgAll::getCode, OrgAll::getParentCode, OrgAll::getName,
                        OrgAll::getShortName, OrgAll::getOrgCode, OrgAll::getD01Code, OrgAll::getOrgType,
                        OrgAll::getParentName, OrgAll::getEsId, OrgAll::getZbCode, OrgAll::getPinyin, OrgAll::getContacter, OrgAll::getContactPhone, OrgAll::getIsApprovalMem);
        return orgAllMapper.selectList(lambdaQueryWrapper);
    }

    @Override
    public List<OrgAll> findExchangeOrgByCode(String code) {
        LambdaQueryWrapper<OrgAll> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.and(queryWrapper -> queryWrapper
                        .isNull(OrgAll::getIsDissolve)
                        .or()
                        .ne(OrgAll::getIsDissolve, CommonConstant.ONE_INT))
                .eq(OrgAll::getCode, code)
                .isNull(OrgAll::getDeleteTime)
                .select(OrgAll::getCode, OrgAll::getParentCode, OrgAll::getName,
                        OrgAll::getShortName, OrgAll::getOrgCode, OrgAll::getD01Code, OrgAll::getOrgType,
                        OrgAll::getParentName, OrgAll::getEsId, OrgAll::getZbCode, OrgAll::getPinyin,
                        OrgAll::getContacter, OrgAll::getContactPhone, OrgAll::getIsApprovalMem, OrgAll::getAdministrativeRegion);
        return orgAllMapper.selectList(lambdaQueryWrapper);
    }

    /**
     * 生成层级码
     *
     * @param parentOrg
     * @return
     */
    @Override
    public String createOrgCode(Org parentOrg) {
        String parentOrgCode = parentOrg.getOrgCode();
        // 查找下级最大层级码
        Org org = this.findOrgMaxCodeByParentCode(parentOrgCode);

        String targetOrgCode;
        if (ObjectUtil.isNull(org)) {
            targetOrgCode = parentOrgCode + ORG_FRIST_CODE;
        } else {
            String orgCode = org.getOrgCode();
            if (ORG_MAX_CODE.equals(orgCode.substring(orgCode.length() - 3))) {
                throw new RuntimeException("层级码达到最大!最大层级码为:" + orgCode);
            }
            //截取最后三位
            targetOrgCode = this.getOrgCode(orgCode);
        }
        return LevelCodeUtils.getLevelCode(targetOrgCode, this::getOrgCode);
    }

    /***
     * !!!!!!注意!!!!
     * 这个方法请勿随意使用，未带任何限制条件like 查询组织
     * 也就是说所有的有效的、无效的、历史数据都会被连带查询出来
     *
     */
    @Override
    public List<Org> findByOrgCodeAllList(String orgCode) {
        if (StrKit.isBlank(orgCode)) {
            return new ArrayList<>();
        }
        LambdaQueryWrapper<Org> lambdaQueryWrapper = new LambdaQueryWrapper<Org>()
                .likeRight(Org::getOrgCode, orgCode).select(Org::getCode, Org::getOrgCode);
        return list(lambdaQueryWrapper);
    }

    @Override
    public List<Org> findByOrgCodeInList() {
        LambdaQueryWrapper<Org> lambdaQueryWrapper = new LambdaQueryWrapper<Org>()
                .select(Org::getCode, Org::getOrgCode).isNull(Org::getDeleteTime);
        return list(lambdaQueryWrapper);
    }

    @Override
    public List<Org> fromSonToFather(String code) {
        return orgMapper.fromSonToFather(code);
    }

    @Override
    public List<Org> fromFatherToSon(String code) {
        return orgMapper.fromFatherToSon(code);
    }

    @Override
    public OutMessage<Object> dissolveMsg(String orgCode) {
        Org org = findOrgByCode(orgCode);
        if (Objects.isNull(org)) {
            return new OutMessage<>(Status.ORG_NOT_EXIST);
        }
        String property = System.getProperty("line.separator");
        List<ResumeVo> resumeVoList = new ArrayList<>();
        //班子成员
//        List<OrgCommitteeElect> orgCommitteeElectList = electService.findOrgCommitteeElectByOrgCode(org.getOrgCode());
        //通过组织code去查询，原来通过组织层级码查询，党组织撤销后新增的组织层级码会重复
        List<OrgCommitteeElect> orgCommitteeElectList = electService.findOrgCommitteeElectByCode(org.getCode());
        if (CollUtil.isNotEmpty(orgCommitteeElectList)) {
            String value = "班子成员";
            String desc = orgCommitteeElectList.stream().map(elect ->
                            (Objects.isNull(elect.getTenureStartDate()) ? "" : DateUtil.format(elect.getTenureStartDate(), "yyyy-MM-dd"))
                                    + "~" +
                                    (Objects.isNull(elect.getTenureEndDate()) ? "" : DateUtil.format(elect.getTenureEndDate(), "yyyy-MM-dd")) + property)
                    .map(s -> "换届日期：" + s).collect(Collectors.joining());
            resumeVoList.add(ResumeVo.builder().value(value).desc(desc).build());
        }
        //奖惩信息
        List<OrgReward> orgRewardList = orgRewardService.findOrgRewardByOrgCode(org.getCode());
        if (CollUtil.isNotEmpty(orgRewardList)) {
            String value = "奖惩信息";
            String desc = orgRewardList.stream().map(orgReward -> Objects.isNull(orgReward.getD42Name()) ? "" : orgReward.getD42Name() + property).map(s -> "奖惩名称：" + s).collect(Collectors.joining());
            resumeVoList.add(ResumeVo.builder().value(value).desc(desc).build());
        }
        //党小组
        List<OrgGroup> orgGroupList = orgGroupService.findOrgGroupByOrgCode(org.getCode());
        if (CollUtil.isNotEmpty(orgGroupList)) {
            String value = "党小组";
            String desc = orgGroupList.stream().map(orgGroup -> Objects.isNull(orgGroup.getName()) ? "" : orgGroup.getName() + property).map(s -> "党小组名称：" + s).collect(Collectors.joining());
            resumeVoList.add(ResumeVo.builder().value(value).desc(desc).build());
        }
        //民主评议
        List<OrgAppraisal> orgAppraisalList = orgAppraisalService.findOrgAppraisalByOrgCode(org.getCode());
        if (CollUtil.isNotEmpty(orgAppraisalList)) {
            String value = "民主评议";
            String desc = orgAppraisalList.stream().map(orgAppraisal -> Objects.isNull(orgAppraisal.getYear()) ? "" : DateUtil.format(orgAppraisal.getYear(), "yyyy") + property).map(s -> "评议年度：" + s).collect(Collectors.joining());
            resumeVoList.add(ResumeVo.builder().value(value).desc(desc).build());
        }
        //发出表彰情况
        List<OrgRecognition> recognitionList = orgRecognitionService.findOrgRecognitionByOrgCode(org.getCode());
        if (CollUtil.isNotEmpty(recognitionList)) {
            String value = "发出表彰情况";
            String desc = recognitionList.stream().map(orgRecognition -> Objects.isNull(orgRecognition.getAnnual()) ? "" : DateUtil.format(orgRecognition.getAnnual(), "yyyy")).distinct().map(orgRecognition -> "表彰年度：" + orgRecognition + property).collect(Collectors.joining());
            resumeVoList.add(ResumeVo.builder().value(value).desc(desc).build());
        }
        //地方委员会情况
        List<OrgCaucus> orgCaucusList = orgCaucusService.findOrgCaucusByOrgCode(org.getCode());
        if (CollUtil.isNotEmpty(orgCaucusList)) {
            String value = "地方委员会情况";
            String desc = orgCaucusList.stream().map(orgCaucus -> Objects.isNull(orgCaucus.getYear()) ? "" : orgCaucus.getYear() + property).map(s -> "年度：" + s).collect(Collectors.joining());
            resumeVoList.add(ResumeVo.builder().value(value).desc(desc).build());
        }
        //党代表情况
        List<OrgPartyCongressElect> orgPartyCongressElectList = orgPartyCongressElectService.findOrgPartyCongressElectByOrgCode(org.getCode());
        if (CollUtil.isNotEmpty(orgPartyCongressElectList)) {
            Date time = new Date();
            String value = "党代表情况";
            String desc = orgPartyCongressElectList.stream().map(orgPartyCongressElect ->
                            (Objects.isNull(orgPartyCongressElect.getTenureStartDate()) ? "" : DateUtil.format(orgPartyCongressElect.getTenureStartDate(), "yyyy-MM-dd"))
                                    + "~" +
                                    (Objects.isNull(orgPartyCongressElect.getTenureEndDate()) ? "" : DateUtil.format(orgPartyCongressElect.getTenureEndDate(), "yyyy-MM-dd")) + property)
                    .map(s -> "换届日期：" + s).collect(Collectors.joining());
            resumeVoList.add(ResumeVo.builder().value(value).desc(desc).time(time).build());
        }
        //培训情况
        List<MemTrain> memTrainList = memTrainService.findTrainByOrgCode(org.getCode());
        if (CollUtil.isNotEmpty(memTrainList)) {
            String value = "培训情况";
            String desc = memTrainList.stream().map(memTrain -> Objects.isNull(memTrain.getYear()) ? "" : memTrain.getYear() + property).map(s -> "年度：" + s).collect(Collectors.joining());
            resumeVoList.add(ResumeVo.builder().value(value).desc(desc).build());
        }
        //乡镇班子换届情况
        List<OrgTownshipLeadership> orgTownshipLeadershipList = orgTownshipLeadershipService.findOrgTownshipLeadershipByOrgCode(org.getCode());
        if (CollUtil.isNotEmpty(orgTownshipLeadershipList)) {
            String value = "乡镇班子换届情况";
            String desc = orgTownshipLeadershipList.stream().map(orgTownshipLeadership -> Objects.isNull(orgTownshipLeadership.getYear()) ? "" : orgTownshipLeadership.getYear() + property).map(s -> "年度：" + s).collect(Collectors.joining());
            resumeVoList.add(ResumeVo.builder().value(value).desc(desc).build());
        }
        //非公党建情况
        List<OrgNonPublicParty> orgNonPublicPartyList = orgNonPublicPartyService.findOrgNonPublicPartyByOrgCode(org.getCode());
        if (CollUtil.isNotEmpty(orgNonPublicPartyList)) {
            String value = "非公党建情况";
            String desc = orgNonPublicPartyList.stream().map(orgNonPublicParty -> Objects.isNull(orgNonPublicParty.getYear()) ? "" : orgNonPublicParty.getYear() + property).map(s -> "年度：" + s).collect(Collectors.joining());
            resumeVoList.add(ResumeVo.builder().value(value).desc(desc).build());
        }

        // 用户账号情况
        List<Org> orgListByOrgCode = this.findOrgListByOrgCode(org.getOrgCode());
        if (orgListByOrgCode.size() > CommonConstant.ZERO_INT) {
            List<String> orgCodeS = orgListByOrgCode.stream().map(Org::getCode).collect(Collectors.toList());
            List<User> accountByOrgs = iUserService.findAccountByOrgs(orgCodeS);
            if (accountByOrgs.size() > CommonConstant.ZERO_INT) {
                String value = "用户账号情况";
                List<User> spitList;
                if (accountByOrgs.size() > CommonConstant.FIVE_INT) {
                    spitList = accountByOrgs.subList(CommonConstant.ONE_INT, CommonConstant.FOUR_INT);
                } else {
                    spitList = accountByOrgs;
                }
                String desc = spitList.stream().map(user -> Objects.isNull(user.getAccount()) ? "" : user.getAccount() + property).map(s -> "账号：" + s).collect(Collectors.joining());
                resumeVoList.add(ResumeVo.builder().value(value).desc(desc).build());
            }
        }
        return new OutMessage<>(Status.SUCCESS, resumeVoList);
    }

    @Override
    public OutMessage<Object> tipsForChangingOrgType(String orgCode, String d01Code) {
        Org org = findOrgByCode(orgCode);
        if (Objects.isNull(org)) {
            return new OutMessage<>(Status.ORG_NOT_EXIST);
        }
        String dbOrgCode = org.getCode();
        List<ResumeVo> resumeVoList = new ArrayList<>();
        String property = System.getProperty("line.separator");
        String suffix = "条" + property;
        if (StrUtil.equals(d01Code, "A")) {
            // 过滤党小组
            List<OrgGroup> orgGroupList = orgGroupService.findOrgGroupByOrgCode(dbOrgCode);
            if (CollUtil.isNotEmpty(orgGroupList)) {
                String value = "党小组";
                String desc = orgGroupList.size() + suffix;
                resumeVoList.add(ResumeVo.builder().value(value).desc(desc).build());
            }
            //过滤民主评议
            List<OrgAppraisal> orgAppraisalList = orgAppraisalService.findOrgAppraisalByOrgCode(dbOrgCode);
            if (CollUtil.isNotEmpty(orgAppraisalList)) {
                String value = "民主评议";
                String desc = orgAppraisalList.size() + suffix;
                resumeVoList.add(ResumeVo.builder().value(value).desc(desc).build());
            }
            //发出表彰情况
            List<OrgRecognition> recognitionList = orgRecognitionService.findOrgRecognitionByOrgCode(dbOrgCode);
            if (CollUtil.isNotEmpty(recognitionList)) {
                String value = "发出表彰情况";
                String desc = recognitionList.size() + suffix;
                resumeVoList.add(ResumeVo.builder().value(value).desc(desc).build());
            }
            //班子成员
            List<OrgCommitteeElect> orgCommitteeElectList = electService.findOrgCommitteeElectByOrgCode(dbOrgCode);
            if (CollUtil.isNotEmpty(orgCommitteeElectList)) {
                String value = "班子成员";
                String desc = orgCommitteeElectList.size() + suffix;
                resumeVoList.add(ResumeVo.builder().value(value).desc(desc).build());
            }
            //地方委员会情况
            List<OrgCaucus> orgCaucusList = orgCaucusService.findOrgCaucusByOrgCode(dbOrgCode);
            if (CollUtil.isNotEmpty(orgCaucusList)) {
                String value = "地方委员会情况";
                String desc = orgCaucusList.size() + suffix;
                resumeVoList.add(ResumeVo.builder().value(value).desc(desc).build());
            }
            //非公党建情况
            List<OrgNonPublicParty> orgNonPublicPartyList = orgNonPublicPartyService.findOrgNonPublicPartyByOrgCode(dbOrgCode);
            if (CollUtil.isNotEmpty(orgNonPublicPartyList)) {
                String value = "非公党建情况";
                String desc = orgNonPublicPartyList.size() + suffix;
                resumeVoList.add(ResumeVo.builder().value(value).desc(desc).build());
            }
            //党代表情况
            List<OrgPartyCongressElect> orgPartyCongressElectList = orgPartyCongressElectService.findOrgPartyCongressElectByOrgCode(dbOrgCode);
            if (CollUtil.isNotEmpty(orgPartyCongressElectList)) {
                Date time = new Date();
                String value = "党代表情况";
                String desc = orgPartyCongressElectList.size() + suffix;
                resumeVoList.add(ResumeVo.builder().value(value).desc(desc).time(time).build());
            }
            //培训情况
            List<MemTrain> memTrainList = memTrainService.findTrainByOrgCode(dbOrgCode);
            if (CollUtil.isNotEmpty(memTrainList)) {
                String value = "培训情况";
                String desc = memTrainList.size() + suffix;
                resumeVoList.add(ResumeVo.builder().value(value).desc(desc).build());
            }
            //乡镇班子换届情况
            List<OrgTownshipLeadership> orgTownshipLeadershipList = orgTownshipLeadershipService.findOrgTownshipLeadershipByOrgCode(dbOrgCode);
            if (CollUtil.isNotEmpty(orgTownshipLeadershipList)) {
                String value = "乡镇班子换届情况";
                String desc = orgTownshipLeadershipList.size() + suffix;
                resumeVoList.add(ResumeVo.builder().value(value).desc(desc).build());
            }
            //奖惩信息
            List<OrgReward> orgRewardList = orgRewardService.findOrgRewardByOrgCode(dbOrgCode);
            if (CollUtil.isNotEmpty(orgRewardList)) {
                String value = "奖惩信息";
                String desc = orgRewardList.size() + suffix;
                resumeVoList.add(ResumeVo.builder().value(value).desc(desc).build());
            }
        } else {
            if (!"631".equals(d01Code) && !"632".equals(d01Code) && !"931".equals(d01Code) && !"932".equals(d01Code)) {
                // 过滤党小组
                List<OrgGroup> orgGroupList = orgGroupService.findOrgGroupByOrgCode(dbOrgCode);
                if (CollUtil.isNotEmpty(orgGroupList)) {
                    String value = "党小组";
                    String desc = orgGroupList.size() + suffix;
                    resumeVoList.add(ResumeVo.builder().value(value).desc(desc).build());
                }
                if (!"634".equals(d01Code)) {
                    //过滤民主评议
                    List<OrgAppraisal> orgAppraisalList = orgAppraisalService.findOrgAppraisalByOrgCode(dbOrgCode);
                    if (CollUtil.isNotEmpty(orgAppraisalList)) {
                        String value = "民主评议";
                        String desc = orgAppraisalList.size() + suffix;
                        resumeVoList.add(ResumeVo.builder().value(value).desc(desc).build());
                    }
                }
            }
            if (!(StrUtil.startWithAny(d01Code, "1", "2") || StrUtil.equals(d01Code, "61") || StrUtil.equals(d01Code, "911"))) {
                //发出表彰情况
                List<OrgRecognition> recognitionList = orgRecognitionService.findOrgRecognitionByOrgCode(dbOrgCode);
                if (CollUtil.isNotEmpty(recognitionList)) {
                    String value = "发出表彰情况";
                    String desc = recognitionList.size() + suffix;
                    resumeVoList.add(ResumeVo.builder().value(value).desc(desc).build());
                }
            }
            if (StrUtil.startWithAny(d01Code, "1", "2")) {
                //班子成员
                List<OrgCommitteeElect> orgCommitteeElectList = electService.findOrgCommitteeElectByOrgCode(dbOrgCode);
                if (CollUtil.isNotEmpty(orgCommitteeElectList)) {
                    String value = "班子成员";
                    String desc = orgCommitteeElectList.size() + suffix;
                    resumeVoList.add(ResumeVo.builder().value(value).desc(desc).build());
                }
            }
            if (!StrUtil.startWith(d01Code, "1")) {
                //地方委员会情况和非公党建情况
                List<OrgCaucus> orgCaucusList = orgCaucusService.findOrgCaucusByOrgCode(dbOrgCode);
                if (CollUtil.isNotEmpty(orgCaucusList)) {
                    String value = "地方委员会情况";
                    String desc = orgCaucusList.size() + suffix;
                    resumeVoList.add(ResumeVo.builder().value(value).desc(desc).build());
                }
                //非公党建情况
                List<OrgNonPublicParty> orgNonPublicPartyList = orgNonPublicPartyService.findOrgNonPublicPartyByOrgCode(dbOrgCode);
                if (CollUtil.isNotEmpty(orgNonPublicPartyList)) {
                    String value = "非公党建情况";
                    String desc = orgNonPublicPartyList.size() + suffix;
                    resumeVoList.add(ResumeVo.builder().value(value).desc(desc).build());
                }
            }
            if (!(StrUtil.startWith(d01Code, "1"))) {
                //党代表情况
                List<OrgPartyCongressElect> orgPartyCongressElectList = orgPartyCongressElectService.findOrgPartyCongressElectByOrgCode(dbOrgCode);
                if (CollUtil.isNotEmpty(orgPartyCongressElectList)) {
                    Date time = new Date();
                    String value = "党代表情况";
                    String desc = orgPartyCongressElectList.size() + suffix;
                    resumeVoList.add(ResumeVo.builder().value(value).desc(desc).time(time).build());
                }
            }
            if (!(StrUtil.startWithAny(d01Code, "1", "61", "63") || StrUtil.equals(d01Code, "911"))) {
                //培训情况
                List<MemTrain> memTrainList = memTrainService.findTrainByOrgCode(dbOrgCode);
                if (CollUtil.isNotEmpty(memTrainList)) {
                    String value = "培训情况";
                    String desc = memTrainList.size() + suffix;
                    resumeVoList.add(ResumeVo.builder().value(value).desc(desc).build());
                }
            }
            if (!StrUtil.startWith(d01Code, "14")) {
                //乡镇班子换届情况
                List<OrgTownshipLeadership> orgTownshipLeadershipList = orgTownshipLeadershipService.findOrgTownshipLeadershipByOrgCode(dbOrgCode);
                if (CollUtil.isNotEmpty(orgTownshipLeadershipList)) {
                    String value = "乡镇班子换届情况";
                    String desc = orgTownshipLeadershipList.size() + suffix;
                    resumeVoList.add(ResumeVo.builder().value(value).desc(desc).build());
                }
            }
        }
        return new OutMessage<>(Status.SUCCESS, resumeVoList);
    }

    @Override
    public Page<Org> findPageByCondition(Integer pageNum, Integer pageSize, Boolean hasLocked, Boolean hasSubordinate, String orgCode, String keyword) {
        Page<Org> page = new Page<>(pageNum, pageSize);
        LambdaQueryWrapper<Org> wrapper = new LambdaQueryWrapper<Org>()
                .isNull(Org::getDeleteTime)
                .and(queryWrapper -> queryWrapper
                        .isNull(Org::getIsDissolve)
                        .or()
                        .ne(Org::getIsDissolve, CommonConstant.ONE_INT));
        if (hasLocked) {
            wrapper.isNotNull(Org::getLockFields);
        } else {
            wrapper.isNull(Org::getLockFields);
        }
        //是否包含下级
        if (hasSubordinate) {
            wrapper.likeRight(Org::getOrgCode, orgCode);
        } else {
            wrapper.eq(Org::getOrgCode, orgCode);
        }
        //按照姓名，联系人进行筛选
        if (StrUtil.isNotBlank(keyword)) {
            wrapper.and(t -> t
                    .like(Org::getName, keyword)
                    .or()
                    .like(Org::getContacter, keyword)
            );
        }
        wrapper.orderByDesc(Org::getCreateTime);
        wrapper.orderByDesc(Org::getId);
        wrapper.select(Org::getCode, Org::getName, Org::getD01Code, Org::getContacter, Org::getContactPhone, Org::getSecretary, Org::getLockFields, Org::getCode);
        page(page, wrapper);
        List<Org> records = page.getRecords();
        if (CollUtil.isNotEmpty(records)) {
            Map<String, String> dictD01Map = com.zenith.front.core.kit.CollectionUtil.listRecordToMap(CacheUtils.getDic("dict_d01"), "key", "name");
            records.forEach(record -> record.setD01Name(dictD01Map.get(record.getD01Code())));
        }
        return page;
    }

    @Override
    public boolean updateLockFields(List<String> filedList) {
        return orgMapper.updateLockFields(filedList) > 0;
    }

    @Override
    public List<BaseReport> selectListByOrgCode(String orgCode) {
        List<Org> orgList = orgMapper.selectListByOrgCode(orgCode);
        // TODO: 2022/10/26 数据转换，兼容加解密
        return orgList.stream().map(pojo -> {
            BaseReport baseReport = new BaseReport();
            BeanUtils.copyProperties(pojo, baseReport);
            return baseReport;
        }).collect(Collectors.toList());
    }

    @Override
    public List<Org> selectUnderlingByOrgCode(String orgCode) {
        return orgMapper.selectUnderlingByOrgCode(orgCode);
    }

    @Override
    public void initExchangeOrg(String baseOrgOrgCode) {
        List<OrgAll> allSubOrgByOrgCode = this.findExchangeOrgByCode(baseOrgOrgCode);
        if (allSubOrgByOrgCode.size() > 0) {
            JSONArray postJsonArray = new JSONArray();
            allSubOrgByOrgCode.forEach(org -> {
                JSONObject jsonObject = JSONObject.parseObject(JSONObject.toJSONString(org));
                jsonObject.put("exchangeKey", exchange_nginx_key);
                postJsonArray.add(jsonObject);
            });
            JSONObject postJson = new JSONObject();
            postJson.put("data", postJsonArray);
            String replaceUrl = sync_flow_push.replace("/api/flow/push", "");
            String rultMessage = HttpKit.doPost(replaceUrl + "/org/addOrg", postJson, "UTF-8");
            System.out.println("组织中间交换区组织信息反馈====》" + rultMessage);
        }
    }

    @Override
    public List<UnitOrgLinkedDTO> superUnitOrgLinked(String orgCode, boolean added) {
        Org org = this.findOrgByCode(orgCode);
        if (Objects.isNull(org)) {
            return Collections.emptyList();
        }
        List<UnitOrgLinkedDTO> unitOrgLinkedDTOList = new ArrayList<>();
        //递归查询本级到上级的所有组织
        List<Org> orgList = this.fromSonToFather(org.getCode());
        String code = added ? orgList.stream().filter(dbOrg -> !"2".equals(dbOrg.getD02Code())).findFirst().map(Org::getCode).orElse(null)
                : orgList.stream().filter(dbOrg -> !StrUtil.equals(dbOrg.getCode(), orgCode) && !"2".equals(dbOrg.getD02Code())).findFirst().map(Org::getCode).orElse(null);
        if (StrUtil.isBlank(code)) {
            return unitOrgLinkedDTOList;
        }
        UnitOrgLinked unitOrgLinked = unitOrgLinkedService.findByOrgCodeAndIsMainUnit(code);
        if (Objects.isNull(unitOrgLinked)) {
            return unitOrgLinkedDTOList;
        }
        Unit unit = unitService.findByCode(unitOrgLinked.getUnitCode());
        UnitOrgLinkedDTO unitOrgLinkedDTO = new UnitOrgLinkedDTO();
        BeanUtils.copyProperties(unitOrgLinked, unitOrgLinkedDTO);
        unitOrgLinkedDTO.setUnit(unit);
        unitOrgLinkedDTO.setIsUnitMain(0);
        unitOrgLinkedDTO.setSelf(false);
        unitOrgLinkedDTOList.add(unitOrgLinkedDTO);
        return unitOrgLinkedDTOList;
    }

    @Override
    public List<Org> findByParentCodeAndD02(String parentCode, String d02Code) {
        LambdaQueryWrapper<Org> query = new LambdaQueryWrapper<Org>()
                .isNull(Org::getDeleteTime)
                .eq(Org::getParentCode, parentCode)
                .eq(Org::getD02Code, d02Code)
                .and(q -> q
                        .isNull(Org::getIsDissolve)
                        .or()
                        .ne(Org::getIsDissolve, CommonConstant.ONE_INT)
                );
        return list(query);
    }

    /**
     * 党代表
     *
     * @param orgCode 组织code
     */
    @Override
    public void handlingPartyRepresentatives(String orgCode) {
        List<OrgPartyCongressElect> orgPartyCongressElectList = orgPartyCongressElectService.findOrgPartyCongressElectByOrgCode(orgCode);
        if (CollUtil.isNotEmpty(orgPartyCongressElectList)) {
            List<String> electCodeList = new ArrayList<>();
            for (OrgPartyCongressElect orgPartyCongressElect : orgPartyCongressElectList) {
                orgPartyCongressElect.setDeleteTime(new Date());
                //党代表届次code
                String electCode = orgPartyCongressElect.getCode();
                electCodeList.add(electCode);
            }
            List<OrgPartyCongressCommittee> orgPartyCongressCommitteeList = new ArrayList<>();
            List<OrgPartyCongressCommitteeAll> orgPartyCongressCommitteeAllList = new ArrayList<>();
            if (CollUtil.isNotEmpty(electCodeList)) {
                //处理党代表基础表
                List<OrgPartyCongressCommittee> congressCommitteeList = orgPartyCongressCommitteeService.findOrgPartyCongressCommitteeByElectCode(electCodeList);
                orgPartyCongressCommitteeList.addAll(congressCommitteeList);
                orgPartyCongressCommitteeList.forEach(orgPartyCongressCommittee -> orgPartyCongressCommittee.setDeleteTime(new Date()));
                //处理党代表all表
                List<OrgPartyCongressCommitteeAll> congressCommitteeAllList = orgPartyCongressCommitteeAllService.findOrgPartyCongressCommitteeAllByElectCode(electCodeList);
                orgPartyCongressCommitteeAllList.addAll(congressCommitteeAllList);
                orgPartyCongressCommitteeAllList.forEach(orgPartyCongressCommitteeAll -> orgPartyCongressCommitteeAll.setDeleteTime(new Date()));
            }
            //更新党代表届次
            orgPartyCongressElectService.updateBatchById(orgPartyCongressElectList, orgPartyCongressElectList.size());
            //更新党代表人员
            if (CollUtil.isNotEmpty(orgPartyCongressCommitteeList)) {
                orgPartyCongressCommitteeService.updateBatchById(orgPartyCongressCommitteeList, orgPartyCongressCommitteeList.size());
            }
            if (CollUtil.isNotEmpty(orgPartyCongressCommitteeAllList)) {
                orgPartyCongressCommitteeAllService.updateBatchById(orgPartyCongressCommitteeAllList, orgPartyCongressCommitteeAllList.size());
            }
        }
    }

    @Override
    public List<String> selectDirectOrgGt300(String codeList) {
        return orgMapper.selectDirectOrgGt300(codeList, 300);
    }

    @Override
    public List<String> selectGeneralPartyBranch(String orgCode) {
        return orgMapper.selectGeneralPartyBranch(orgCode);
    }

    @Override
    public List<Map<String, Object>> selectPartyBranchMemCountList(String orgCode) {
        return orgMapper.selectPartyBranchMemCountList(orgCode);
    }

    @Override
    public List<Map<String, Object>> selectPartyBranchMemCounNewtList(String orgCode) {
        return orgMapper.selectPartyBranchMemCountListNew(orgCode);
    }

    @Override
    public void updateOrgD113Code(String code) {
        orgAllService.update(
                Wrappers.<OrgAll>lambdaUpdate()
                        .eq(OrgAll::getCode, code)
                        .set(OrgAll::getD113Code, null)
                        .set(OrgAll::getHasTeachersDoubleLeaders, null)
        );
        update(
                Wrappers.<Org>lambdaUpdate()
                        .eq(Org::getCode, code)
                        .set(Org::getD113Code, null)
                        .set(Org::getHasTeachersDoubleLeaders, null)
        );
    }

    @Override
    public void updateOrgHasTeachersDoubleLeaders(String code) {
        update(Wrappers.<Org>lambdaUpdate()
                .eq(Org::getCode, code)
                .set(Org::getHasTeachersDoubleLeaders, null));
        orgAllService.update(Wrappers.<OrgAll>lambdaUpdate()
                .eq(OrgAll::getCode, code)
                .set(OrgAll::getHasTeachersDoubleLeaders, null));
    }

    @Override
    public List<String> findNodeByOrgCodeLength(int length) {
        return orgMapper.findNodeByOrgCodeLength(length);
    }

    @Override
    public List<String> findNodeByOrgAdministrativeRegion() {
        return orgMapper.findNodeByOrgAdministrativeRegion();
    }

    @Override
    public List<Map<String, Object>> selectPartyBranchFormalMemCountList(String orgCode) {
        return orgMapper.selectPartyBranchFormalMemCountList(orgCode);
    }

    @Override
    public Page<Org> findPartyCommitteePageByOrgCode(Integer pageNum, Integer pageSize, String orgCode) {
        Page<Org> page = new Page<>(pageNum, pageSize);
        return page(page, new LambdaQueryWrapper<Org>()
                .likeRight(Org::getOrgCode, orgCode)
                .and(q -> q
                        .isNull(Org::getIsDissolve)
                        .or()
                        .ne(Org::getIsDissolve, CommonConstant.ONE_INT)
                )
                .in(Org::getD01Code, "61", "911")
                .isNull(Org::getDeleteTime)
                .orderByAsc(Org::getOrgCode)
                .select(Org::getName, Org::getD01Code, Org::getContacter, Org::getContactPhone, Org::getSecretary, Org::getOrgCode)
        );
    }

    @Override
    public List<Map<String, Object>> selectPartyGeneralBranchMemCountList(String orgCode) {
        return orgMapper.selectPartyGeneralBranchMemCountMap(orgCode, "0");
    }

    /**
     * 获取组织树
     *
     * @param orgTreeDTO 获取组织树的code
     * @return
     */
    @Override
    public OutMessage getOldOrgTree(OrgTreeDTO orgTreeDTO) {
        // 获取集合
        List<String> orgCodeList = orgTreeDTO.getOrgCodeList();
        String manOrgCode = this.getUserManOrgCode();
        if (CollectionUtil.isEmpty(orgCodeList)) {
            orgCodeList = new ArrayList<>();
            orgCodeList.add(manOrgCode);
        }
        // 排除集合
        List<String> excludeOrgCodeList = orgTreeDTO.getExcludeOrgCodeList();
        // 是否权限检验
        String isPermissionCheck = orgTreeDTO.getIsPermissionCheck();
        String oldYear = orgTreeDTO.getOldYear();

        String esIndexName = StrUtil.isEmpty(oldYear) ? "ccp_org_all" : "ccp_org_all_" + oldYear;
        SelectConditionStep<org.jooq.Record> selectConditionStep = DSL_CONTEXT.select()
                .from(table(name(esIndexName)).as("ccp_org_all")).where("delete_time is null and (is_dissolve is null or is_dissolve != 1)");

        List<Condition> conditionOrgList = new ArrayList<>();
        for (String orgCode : orgCodeList) {
            // 权限检查
            if (!(StrKit.notBlank(isPermissionCheck) && CommonConstant.ZERO.equals(isPermissionCheck))) {
                if (!orgCode.startsWith(manOrgCode)) {
                    return new OutMessage<>(Status.PERMISSION_DENIED);
                }
            }

            // 查询当前节点及下一级节点
            Condition condition = DSL.field("org_code").eq(orgCode)
                    .or(DSL.field("org_code").like(orgCode + "___"));
            conditionOrgList.add(condition);
        }
        Condition finalCondition = DSL.or(conditionOrgList);
        selectConditionStep.and(finalCondition);
        Condition excludeCondition = DSL.trueCondition(); // 初始化为一个恒为真的条件
        if (CollectionUtil.isNotEmpty(excludeOrgCodeList)) {
            for (String excludeOrgCode : excludeOrgCodeList) {
                excludeCondition = excludeCondition.and(DSL.field("org_code").notLike(excludeOrgCode + "%"));
            }
        }
        selectConditionStep.and(excludeCondition);
        selectConditionStep.orderBy(
                DSL.field("length(" + DSL.field("org_code") + ")"),
                DSL.field("sort"),
                DSL.field("create_time").desc(),
                DSL.field("id").desc());
        log.info("往年年度统计机构树ES查询：{}", selectConditionStep.toString());
        List<com.jfinal.plugin.activerecord.Record> records = EsKit.findBySql(selectConditionStep.toString()).toRecord();
        List<Org> orgList = new ArrayList<>();
        records.forEach(et -> {
            Map<String, Object> columns = et.getColumns();
            orgList.add(BeanUtil.mapToBean(columns, Org.class, true));
        });
        return new OutMessage<>(Status.SUCCESS, orgList);
    }

    @Override
    public Org getShortestOrgCode() {
        LambdaQueryWrapper<Org> wrapper = new LambdaQueryWrapper<>();
        wrapper.isNull(Org::getDeleteTime)
                .and(q -> q
                        .isNull(Org::getIsDissolve)
                        .or()
                        .ne(Org::getIsDissolve, CommonConstant.ONE_INT)
                )
                // 只查询需要的字段，避免加密字段解密
                .select(Org::getCode, Org::getOrgCode, Org::getName, Org::getShortName);
        wrapper.last("order by length(org_code) asc limit 1");
        return getOne(wrapper);
    }

    /**
     * 修改排序
     *
     * @param tableName   表名
     * @param primaryKey  主键
     * @param sortName    排序名称
     * @param sortDtoList 排序集合
     * @return
     */
    public boolean updateSort(String tableName, String primaryKey, String sortName, List<SortDTO> sortDtoList) {
        List<Org> idList = new ArrayList<>();
        List<OrgAll> orgAllList = new ArrayList<>();
        sortDtoList.forEach(sortDto -> {
            LambdaQueryWrapper<Org> wrapper = new LambdaQueryWrapper<Org>().select(Org::getId, Org::getCode, Org::getSort)
                    .eq(Org::getCode, sortDto.getCode()).last("limit 1");
            Org org = this.getOne(wrapper);
            if (Objects.nonNull(org)) {
                org.setSort(sortDto.getSort());
                idList.add(org);
            }
            LambdaQueryWrapper<OrgAll> wrapper1 = new LambdaQueryWrapper<OrgAll>().select(OrgAll::getId, OrgAll::getCode, OrgAll::getSort)
                    .eq(OrgAll::getCode, sortDto.getCode()).last("limit 1");
            OrgAll orgAll = orgAllService.getOne(wrapper1);
            if (Objects.nonNull(orgAll)) {
                orgAll.setSort(sortDto.getSort());
                orgAllList.add(orgAll);
            }
        });
        boolean b = updateBatchById(idList, idList.size());
        boolean b1 = orgAllService.updateBatchById(orgAllList, orgAllList.size());
        if (b1) {
//            List<OrgAll> updateOrgAllList = new ArrayList<>();
//            orgAllList.forEach(orgAll -> {
//                OrgAll org = new OrgAll();
//                org.setId(orgAll.getId());
//                org.setSort(orgAll.getSort());
//                updateOrgAllList.add(org);
//            });
            //同步到驻村系统总库
//            DynamicDatasourceUtil.change(DynamicDatasourceUtil.STATION_DATASOURCE, () -> orgAllService.updateBatchById(updateOrgAllList));
        }
        return b && b1;
    }

    /**
     * 新增或修改时业务处理
     *
     * @param orgDTO
     * @param parentOrg
     * @return
     */
    private OutMessage getOutMessageSaveOrUpdate(OrgDTO orgDTO, Org parentOrg) {
        if (Objects.isNull(parentOrg)) {
            return null;
        }
        String orgCode = parentOrg.getOrgCode();
        if (!orgCode.startsWith(USER_CONTEXT.get().getUserRolePermission().getOrgCode()) && !orgDTO.getCode().equals(USER_CONTEXT.get().getUserRolePermission().getOrgId())) {
            return new OutMessage<>(Status.PERMISSION_DENIED);
        }

        String orgType = parentOrg.getOrgType();
        if (CommonConstant.FOUR.equals(orgType) || CommonConstant.THREE.equals(orgType)) {
            // 党支部或联合党支部
            return new OutMessage<>(Status.PARTY_BRANCH_ERROR);
        }

        String name = orgDTO.getName();
        if ((!name.startsWith(OrgConstant.PRE_ORG_NAME) && !name.startsWith(OrgConstant.PRE_ORG_NAME_ALL)) && !name.endsWith(OrgConstant.SUF_ORG_NAME)) {
            // 组织名称不符合规范
            return new OutMessage<>(Status.ORG_NAME_EROOR);
        }
        // 组织类别
        String parentD01Code = parentOrg.getD01Code();
        String d01Code = orgDTO.getD01Code();
        if (DZZ_CODE.contains(parentD01Code) && !OrgConstant.PARTY_BRANCH_LIST.contains(d01Code)) {
            // 上级是党总支部
            return new OutMessage<>(Status.D01_CODE_ERROR);
        }
        return null;
    }

    /**
     * 获取主单位code
     *
     * @param dto
     * @return
     */
    private String getOrgMainUnitCode(OrgDTO dto) {
        List<UnitOrgLinkedDTO> linkedDTOList = dto.getLinkedDTOList();
        String d02Code = dto.getD02Code();
        if (StrUtil.isEmpty(d02Code)) return null;
        if (StrUtil.equals(d02Code, CommonConstant.TWO)) {
            OrgAll orgAll = orgAllService.findByCode(dto.getParentCode());
            return ObjectUtil.isNull(orgAll) ? null : orgAll.getMainUnitCode();
        } else {
            return linkedDTOList.stream().filter(e -> Objects.equals(e.getIsUnitMain(), 1)).findFirst().orElse(new UnitOrgLinkedDTO()).getUnitCode();
        }
    }

    /**
     * 根据组织类别转换成组织大类
     *
     * @return
     */
    private String transformToOrgType(String d01Code) {
        // 1--党委,2--党总支,3--党支部,4--联合党支部,5--党组
        if (StrKit.isBlank(d01Code)) {
            return null;
        }
        // 党委
        if (d01Code.startsWith(CommonConstant.ONE) || d01Code.startsWith(CommonConstant.TWO)
                || d01Code.startsWith(CommonConstant.FOUR) || d01Code.startsWith(OrgConstant.DW_CODE)
                || d01Code.startsWith(OrgConstant.TSDW_CODE) || d01Code.startsWith(OrgConstant.MZDW_CODE)
                || d01Code.startsWith(OrgConstant.LSZDW_CODE) || StrUtil.equals(d01Code, "A")) {
            return CommonConstant.ONE;
        }

        // 党组
        if (d01Code.startsWith(CommonConstant.THREE)) {
            return CommonConstant.FIVE;
        }

        // 党总支
        if (DZZ_CODE.contains(d01Code)) {
            return CommonConstant.TWO;
        }

        // 党支部
        if (DZB_CODE.contains(d01Code)) {
            return CommonConstant.THREE;
        }

        // 联合党支部
        if (LHDZB_CODE.contains(d01Code)) {
            return CommonConstant.FOUR;
        }
        return null;
    }

    /**
     * 设置组织类别及隶属关系
     *
     * @param orgAll 组织扩展信息表对象
     */
    private void setOrgDict(OrgAll orgAll) {
        // 类别
        List<Record> dictD01List = CacheUtils.getDic("dict_d01");
        Map<String, String> dictD01Map = com.zenith.front.core.kit.CollectionUtil.listRecordToMap(dictD01List, "key", "name");
        // 组织关联单位情况
        List<Record> dictD02List = CacheUtils.getDic("dict_d02");
        Map<String, String> dictD02Map = com.zenith.front.core.kit.CollectionUtil.listRecordToMap(dictD02List, "key", "name");
        // 隶属关系
        List<Record> dictD03List = CacheUtils.getDic("dict_d03");
        Map<String, String> dictD03Map = com.zenith.front.core.kit.CollectionUtil.listRecordToMap(dictD03List, "key", "name");

        // 组装名称
        orgAll.setD01Name(dictD01Map.get(orgAll.getD01Code()));
        orgAll.setD02Name(dictD02Map.get(orgAll.getD02Code()));
        orgAll.setD03Name(dictD03Map.get(orgAll.getD03Code()));
    }

    /**
     * 组织关联单位
     *
     * @param orgDTO
     * @return
     */
    private OutMessage OrgLinkedUnit(OrgDTO orgDTO, OrgAll orgAll) {
        List<UnitOrgLinkedDTO> linkedDTOList = orgDTO.getLinkedDTOList();
        // 21 与上级党组织在同一法人单位
        String d02Code = orgDTO.getD02Code();
        // 组织层级码
        String orgCode = orgDTO.getOrgCode();
        if (StrUtil.startWithAny(orgDTO.getD01Code(), "1", "2")) {
            return null;
        }
        if (!d02Code.startsWith("2")) {
            // 必须有一个关联
            if (CollectionUtil.isEmpty(linkedDTOList) || linkedDTOList.size() < CommonConstant.ONE_INT) {
                // 必须关联单位
                return new OutMessage(Status.ORG_MUST_UNION_UNIT);
            }
            int size = linkedDTOList.size();

            String orgType = orgDTO.getOrgType();
//            if (CommonConstant.FOUR.equals(orgType) && size < CommonConstant.TWO_INT) {
//                // 联合党支部
//                return new OutMessage(Status.UNION_DZB_MUST_TWO);
//            }
            if (CommonConstant.ONE.equals(d02Code) && size > CommonConstant.ONE_INT) {
                // 非联合党支部
                return new OutMessage(Status.NOT_UNION_DZB_MUST_ONE);
            }

            int isUnitMainCount = 0;
            Map map = new HashMap(linkedDTOList.size());
            for (UnitOrgLinkedDTO unitOrgLinkedDTO : linkedDTOList) {
                String unitCode = unitOrgLinkedDTO.getUnitCode();
                Object object = map.get(unitCode);
                if (ObjectUtil.isNotNull(object)) {
                    return new OutMessage<>(Status.ORG_REPEAT_UNIT);
                } else {
                    map.put(unitCode, unitCode);
                }
                if (unitOrgLinkedDTO.getIsUnitMain() == CommonConstant.ONE_INT) {
                    isUnitMainCount++;
                    orgAll.setMainUnitCode(unitOrgLinkedDTO.getUnitCode());
                    orgAll.setMainUnitName(unitOrgLinkedDTO.getUnitName());
                    orgAll.setMainUnitType(unitOrgLinkedDTO.getUnitType());
                }
                unitOrgLinkedDTO.setLinkedOrgCode(orgCode);
            }

            if (isUnitMainCount != CommonConstant.ONE_INT) {
                // 只能有一个主单位
                return new OutMessage<>(Status.LINKED_IS_MAIN_UNIT_ONE);
            }
        }
        return null;
    }

    /**
     * 拼接数据
     * 20210903 如果党组织关联单位 单位没有主组织 这个组织就是这个单位的主组织
     *
     * @param orgDTO
     * @return
     */
    private List<UnitOrgLinked> getUnitOrgLinkedList(OrgDTO orgDTO) {
        // 21 与上级党组织在同一法人单位
        String d02Code = orgDTO.getD02Code();

        String orgCode = orgDTO.getCode();
        if (Objects.nonNull(d02Code) && d02Code.startsWith(CommonConstant.TWO)) {
            List<UnitOrgLinked> list = unitOrgLinkedService.list(new QueryWrapper<UnitOrgLinked>().lambda().eq(UnitOrgLinked::getOrgCode, orgCode).isNull(UnitOrgLinked::getDeleteTime));
            if (CollectionUtil.isNotEmpty(list)) {
                List<String> collect = list.stream().map(UnitOrgLinked::getUnitCode).collect(Collectors.toList());
                List<UnitAll> unitAllS = unitAllService.selectIdByCode(collect);
                for (UnitAll unitAll : unitAllS) {
                    unitAll.setIsCreateOrg(0);
                }
            }
            return null;
        } else {
            List<UnitOrgLinkedDTO> linkedDTOList = orgDTO.getLinkedDTOList();
            //组织类别为中共各级委员会和中共各级工作委员会时，已经不用选单位了
            if (CollUtil.isEmpty(linkedDTOList)) {
                return null;
            }
            List<UnitOrgLinked> unitOrgLinkedList = new ArrayList<>();
            List<String> unitCodeList = linkedDTOList.stream().map(UnitOrgLinkedDTO::getUnitCode).collect(Collectors.toList());
            List<UnitOrgLinked> orgLinkedList = unitOrgLinkedService.findByUnitCodesAndIsOrgMain(unitCodeList, 1);
            //查找当前组织主单位关联关系
            UnitOrgLinked orgLinked = unitOrgLinkedService.findByOrgCodeAndIsMainUnit(orgCode);
            Set<String> isOrgMainHaveSet = orgLinkedList.stream().map(UnitOrgLinked::getUnitCode).filter(StrUtil::isNotEmpty).collect(Collectors.toSet());
            List<String> updateOrgUnitList = new ArrayList<>();

            for (UnitOrgLinkedDTO unitOrgLinkedDTO : linkedDTOList) {
                UnitOrgLinked unitOrgLinked = new UnitOrgLinked();
                BeanUtils.copyProperties(unitOrgLinkedDTO, unitOrgLinked);
                unitOrgLinked.setIsOrgMain(unitOrgLinkedDTO.getIsOrgMain());
                unitOrgLinked.setOrgCode(orgCode);
                unitOrgLinked.setOrgName(orgDTO.getName());
                unitOrgLinked.setOrgType(orgDTO.getD01Code());
                unitOrgLinked.setOrgTypeCode(orgDTO.getOrgType());
                unitOrgLinked.setCode(StrKit.getRandomUUID());
                // TODO: 2022/6/27 修复 linked_org_code 为空 bug
                unitOrgLinked.setLinkedOrgCode(orgDTO.getOrgCode());
                unitOrgLinked.setCreateTime(new Date());
                unitOrgLinked.setUpdateTime(new Date());
                unitOrgLinked.setUpdateAccount(USER_CONTEXT.get().getUser().getAccount());
                unitOrgLinked.setTimestamp(new Date());
                unitOrgLinked.setEsId(CodeUtil.getEsId());
                unitOrgLinked.setId(null);
                boolean flag = !isOrgMainHaveSet.contains(unitOrgLinked.getUnitCode());
                unitOrgLinked.setIsOrgMain(flag ? 1 : 0);
                if (flag) {
                    updateOrgUnitList.add(unitOrgLinked.getUnitCode());
                }
                //存在主单位关联关系时，不能设置为主单位
                if (Objects.nonNull(orgLinked)) {
                    unitOrgLinked.setIsUnitMain(0);
                }
                unitOrgLinkedList.add(unitOrgLinked);
            }

            if (CollectionUtil.isNotEmpty(updateOrgUnitList)) {
                unitService.batchMainOrgByCodes(1, orgCode, orgDTO.getName(), orgDTO.getD01Code(), orgDTO.getOrgType(), orgDTO.getOrgCode(), updateOrgUnitList);
                unitAllService.batchMainOrgByCodes(1, orgCode, orgDTO.getName(), orgDTO.getD01Code(), orgDTO.getOrgType(), orgDTO.getOrgCode(), updateOrgUnitList);
            }
            if (orgDTO.isAdd()) {
                // 新增时修改创建单位的组织相关字段
                this.processCreateUnitOrgCode(orgDTO);
            }

            //修复关联单位选择 与上级党组织所在单位建立联合党支部 时，上级党组织关联单位的 建立党组织情况 被更改
            List<UnitOrgLinkedDTO> unitOrgLinkedDTOList = superUnitOrgLinked(orgCode, orgDTO.isAdd());
            if (CollectionUtil.isNotEmpty(unitOrgLinkedDTOList)) {
                unitCodeList.removeAll(unitOrgLinkedDTOList.stream().map(UnitOrgLinkedDTO::getUnitCode).collect(Collectors.toSet()));
            }
            if (CollUtil.isNotEmpty(unitCodeList)) {
                String d01Code = orgDTO.getD01Code();
                String key;
                if (d01Code.startsWith("2")) {
                    key = "16";
                } else if (d01Code.startsWith("3")) {
                    key = "15";
                } else {
                    key = d01MappingD05Map.get(d01Code);
                }
                List<Record> dictD01List = CacheUtils.getDic("dict_d05");
                Map<String, String> dictD01Map = com.zenith.front.core.kit.CollectionUtil.listRecordToMap(dictD01List, "key", "name");
                String name = dictD01Map.get(key);
                unitService.updateD05ByIds(key, name, unitCodeList);
                unitAllService.updateUnitD05ByIds(key, name, unitCodeList);
            }
            return unitOrgLinkedList;
        }
    }

    /**
     * 新增时修改单位的创建单位的组织相关字段
     * 问题描述：上级党委创建单位后，在党组织新增时关联该单位，然后进行组织架构调整或整建制转接，该创建党组织修改不了的情况
     * 处理方式：新增时，这个党组织就是这个单位的主组织，按照该组织进行修改
     */
    private void processCreateUnitOrgCode(OrgDTO orgDTO) {
        List<UnitOrgLinkedDTO> linkedDTOList = orgDTO.getLinkedDTOList();
        if (CollUtil.isEmpty(linkedDTOList)) {
            return;
        }
        String code = orgDTO.getCode();
        String orgCode = orgDTO.getOrgCode();
        String zbCode = orgDTO.getZbCode();
        Set<String> unitCodes = linkedDTOList.stream().map(UnitOrgLinkedDTO::getUnitCode).collect(Collectors.toSet());
        if (CollUtil.isNotEmpty(unitCodes)) {
            unitService.update(new LambdaUpdateWrapper<Unit>()
                    .set(Unit::getCreateOrgCode, code)
                    .set(Unit::getCreateUnitOrgCode, orgCode)
                    .set(Unit::getCreateOrgZbCode, zbCode)
                    .in(Unit::getCode, unitCodes));
            unitAllService.update(new LambdaUpdateWrapper<UnitAll>()
                    .set(UnitAll::getCreateOrgCode, code)
                    .set(UnitAll::getCreateUnitOrgCode, orgCode)
                    .set(UnitAll::getCreateOrgZbCode, zbCode)
                    .in(UnitAll::getCode, unitCodes));
        }

    }

    private void syncVillageCommunityOrg(OrgAll orgAll) {
        String orgCode = orgAll.getOrgCode();
        OrgAll all = new OrgAll();
        all.setId(orgAll.getCode());
        all.setIsApprovalMem(orgAll.getIsApprovalMem());
        all.setCode(orgAll.getCode());
        all.setOrgCode(orgCode);
        all.setName(orgAll.getName());
        all.setShortName(orgAll.getShortName());
        all.setIsLeaf(orgAll.getIsLeaf());
        all.setParentCode(orgAll.getParentCode());
        all.setD01Code(orgAll.getD01Code());
        all.setD02Code(orgAll.getD02Code());
        all.setD03Code(orgAll.getD03Code());
        all.setD04Code(orgAll.getD04Code());
        all.setContacter(orgAll.getContacter());
        all.setContactPhone(orgAll.getContactPhone());
        all.setFaxNumber(orgAll.getFaxNumber());
        all.setPostAddress(orgAll.getPostAddress());
        all.setPostCode(orgAll.getPostCode());
        all.setSort(orgAll.getSort());
        if (orgCode.length() == 3) {
            all.setOrgType("2");
        } else if (orgCode.length() == 6) {
            all.setOrgType("3");
        } else if (orgCode.length() == 9) {
            all.setOrgType("4");
        } else if (StrUtil.startWith(orgAll.getD04Code(), "91")) {
            all.setOrgType("5");
        } else if (StrUtil.startWith(orgAll.getD04Code(), "92")) {
            all.setOrgType("6");
        } else {
            all.setOrgType("-1");
        }
        all.setD16Code(orgAll.getD16Code());
        String mainUnitCode = orgAll.getMainUnitCode();
        all.setMainUnitCode(mainUnitCode);
        if (Objects.nonNull(mainUnitCode)) {
            Unit unit = unitService.findByCode(mainUnitCode);
            if (Objects.nonNull(unit)) {
                all.setMainUnitName(unit.getName());
            }
        }
        Map<String, Object> map = BeanUtil.beanToMap(all, false, true);
        map.put("levelCode", orgCode);
        String parentOrgCode = orgAllService.findOrgCodeByCode(orgAll.getParentCode());
        map.put("parentLevelCode", parentOrgCode);
        map.put("createTime", DateUtil.formatDate(orgAll.getCreateTime()));
        map.put("updateTime", DateUtil.formatDate(orgAll.getUpdateTime()));
        map.put("isDelete", CommonConstant.ZERO_INT);
        villageCommunityClient.pushOrgInfo(map);
    }

    /**
     * 生成层级码逻辑
     *
     * @param handler
     * @return
     */
    private String getOrgCode(String handler) {
        StringBuilder builder = new StringBuilder(handler);
        // 获取最后三位
        String substring = builder.substring(builder.length() - CommonConstant.THREE_INT);
        int parseInt = Integer.parseInt(substring);
        parseInt++;
        String valueOf = String.valueOf(parseInt);
        StringBuilder sb = new StringBuilder(valueOf);
        for (int y = 0; y < (CommonConstant.THREE_INT - valueOf.length()); y++) {
            sb.insert(0, CommonConstant.ZERO);
        }
        StringBuilder replace = builder.replace(builder.length() - CommonConstant.THREE_INT, builder.length(), sb.toString());
        return replace.toString();
    }

    /**
     * 获取查询条件
     *
     * @param orgListDTO
     * @param wrapper
     * @param orderFields 排序字段
     * @return
     */
    public OutMessage getListCondition(OrgListDTO orgListDTO, LambdaQueryWrapper<OrgAll> wrapper, String orderFields) {
        int pageNum = Objects.nonNull(orgListDTO.getPageNum()) ? orgListDTO.getPageNum() : 1;
        int pageSize = Objects.nonNull(orgListDTO.getPageSize()) ? orgListDTO.getPageSize() : 10;

        this.getListCondition(orgListDTO, wrapper);
        wrapper.isNull(OrgAll::getDeleteTime);
        wrapper.and(queryWrapper -> queryWrapper
                .isNull(OrgAll::getIsDissolve)
                .or()
                .ne(OrgAll::getIsDissolve, CommonConstant.ONE_INT));
        wrapper.last(orderFields);
        Page<OrgAll> orgPage = orgAllMapper.selectPage(new Page<>(pageNum, pageSize), wrapper);
        // 类别
        List<Record> dictD01List = CacheUtils.getDic("dict_d01");
        Map<String, String> dictD01Map = com.zenith.front.core.kit.CollectionUtil.listRecordToMap(dictD01List, "key", "name");
        // 隶属关系
        List<Record> dictD03List = CacheUtils.getDic("dict_d03");
        Map<String, String> dictD03Map = com.zenith.front.core.kit.CollectionUtil.listRecordToMap(dictD03List, "key", "name");
        List<String> orgList = new ArrayList<>();
        // 组装名称
        List<OrgAll> pageRecords = orgPage.getRecords();
        pageRecords.forEach(orgAll -> {
            orgAll.setD01Name(dictD01Map.get(orgAll.getD01Code()));
            orgAll.setD03Name(dictD03Map.get(orgAll.getD03Code()));
            //当前记录组织code
            orgList.add(orgAll.getCode());
        });
        //查询当前记录的组织是否有党组织班子换届消息提醒
        List<ChangeOfPartyOrganizationMessageVO> organizationMessageVOList = electService.findAssignOrgCommitteeElectMessage(orgList);
        List<String> existChangeTheTermOfOfficeCodeList = organizationMessageVOList.stream().map(ChangeOfPartyOrganizationMessageVO::getOrgCode).collect(Collectors.toList());
        //记录打上组织换届标识
        pageRecords.forEach(pageRecord -> pageRecord.setChangeTheTermOfOffice(existChangeTheTermOfOfficeCodeList.contains(pageRecord.getCode())));
        return new OutMessage<>(Status.SUCCESS, orgPage);
    }

    /**
     * 获取查询条件
     *
     * @param orgListDTO
     * @param wrapper
     * @return
     */
    public void getListCondition(OrgListDTO orgListDTO, LambdaQueryWrapper<OrgAll> wrapper) {
        // 组织类别
        List<String> d01CodeList = orgListDTO.getD01CodeList();
        // 所在单位情况代码
        List<String> d02CodeList = orgListDTO.getD02CodeList();
        // 隶属关系
        List<String> d03CodeList = orgListDTO.getD03CodeList();
        // 组织大类
        List<String> orgTypeList = orgListDTO.getOrgTypeList();
        // 组织名称
        String orgName = orgListDTO.getOrgName();
        // 国民经济
        List<String> d194CodeList = orgListDTO.getD194CodeList();
        // 国民经济
        List<String> d195CodeList = orgListDTO.getD195CodeList();
        wrapper.in(CollUtil.isNotEmpty(d194CodeList), OrgAll::getD194Code, d194CodeList);
        wrapper.in(CollUtil.isNotEmpty(d195CodeList), OrgAll::getD195Code, d195CodeList);
        //单位类别
        List<String> d04CodeList = orgListDTO.getD04CodeList();
        wrapper.in(CollUtil.isNotEmpty(d04CodeList), OrgAll::getD04Code, d04CodeList);

        // 组织类别
        wrapper.in(CollUtil.isNotEmpty(d01CodeList), OrgAll::getD01Code, d01CodeList);

        // 所在单位情况代码
        wrapper.in(CollUtil.isNotEmpty(d02CodeList), OrgAll::getD02Code, d02CodeList);

        // 隶属关系
        wrapper.in(CollUtil.isNotEmpty(d03CodeList), OrgAll::getD03Code, d03CodeList);

        // 组织大类
        wrapper.in(CollUtil.isNotEmpty(orgTypeList), OrgAll::getOrgType, orgTypeList);
        if (!StrKit.isBlank(orgName)) {
            wrapper.and(r -> r.like(OrgAll::getName, orgName).or().like(OrgAll::getShortName, orgName).or().like(OrgAll::getPinyin, orgName));
        }

    }

    /**
     * 获取当前用户管理的组织code
     *
     * @return
     */
    public String getUserManOrgCode() {
        return USER_CONTEXT.get().getUserRolePermission().getOrgCode();
    }

    /**
     * 党组织编辑保存获取主单位
     *
     * @param orgDTO
     * @return
     */
    private Unit checkChangeOrgInfo(OrgDTO orgDTO) {
        String mainUnitCode = orgDTO.getMainUnitCode();
        List<UnitOrgLinkedDTO> orgDTOLinkedDTOList = orgDTO.getLinkedDTOList();
        if (CollectionUtil.isNotEmpty(orgDTOLinkedDTOList)) {
            UnitOrgLinked unitOrgLinked = iSyncMemService.getUnitOrgLinked(orgDTO.getCode());
            if (Objects.nonNull(unitOrgLinked)) {
                mainUnitCode = unitOrgLinked.getUnitCode();
            }
        }
        return unitService.findByCode(mainUnitCode);
    }

    public void getTopOrg(String orgCode, Org topOrg) {
        OrgAll orgAll = orgAllService.findByCode(orgCode);
        if (Objects.nonNull(orgAll)) {
            if ("2".equals(orgAll.getD02Code())) {
                this.getTopOrg(orgAll.getParentCode(), topOrg);
            } else {
                BeanUtils.copyProperties(orgAll, topOrg);
            }
        }
    }

    /**
     * 当党组织的（单位属性为教育大类中331、是法人单位、国国民经济行业是普通高等教育）三个条件同时满足此类党组织编辑这里增加一个限制，就是本级组织不能更改单位同步过来的国民经济行业那两个属性，选择
     * 与上级党组织相同的下级党组织不能选择本级组织同步过来的国民经济行业
     */
    private OutMessage checkUnitTypeOne(OrgDTO orgDTO) {
        String mainUnitCode = orgDTO.getMainUnitCode();
        //获取党组织的主单位
        Unit unit = unitService.findByCode(mainUnitCode);
        String d194Code = unit.getD194Code();
        // 单位未设置国民经济行业
        if (StrUtil.isBlank(d194Code)) {
            return null;
        }
        //单位属性为教育大类中331、是法人单位、国民经济行业是普通高等教育
        if ("331".startsWith(unit.getD04Code()) && ObjectUtil.equals(CommonConstant.ONE_INT, unit.getIsLegal()) && "P8341".startsWith(unit.getD194Code())) {
            //1	法人单位（独立单位）
            //2 与上级党组织相同
            //本级组织不能更改单位同步过来的国民经济行业那两个属性
            if (orgDTO.getD02Code().startsWith(CommonConstant.ONE)) {
                orgDTO.setD194Name(unit.getD194Name());
                orgDTO.setD194Code(unit.getD194Code());
            }
            //与上级党组织相同的下级党组织不能选择本级组织同步过来的国民经济行业
            if (orgDTO.getD02Code().startsWith(CommonConstant.TWO)) {
                if (orgDTO.getD194Code().equals(unit.getD194Code())) {
                    return new OutMessage<>(Status.ORG_NATIONAL_ECONOMY_SAME_UNIT);
                }
            }
        }
        return null;
    }

    /**
     * 当单位基础信息中单位类别是教育大类中教育大类中332、333、334、335，国民经济行业是除普通高等教育以外的 中等教育(P833)，普通高中教育，初等教育(P832)，学前教育(P831)此类  党组织编辑这里增加一个限制，
     * 就是本级组织不能更改单位同步过来的国民经济行业那两个属性，选择与上级党组织相同的下级党组织不能选择本级组织同步过来的国民经济行业
     */

    private OutMessage checkUnitTypeTwo(OrgDTO orgDTO) {
        String mainUnitCode = orgDTO.getMainUnitCode();
        //获取党组织的主单位
        Unit unit = unitService.findByCode(mainUnitCode);
        String d194Code = unit.getD194Code();
        // 单位未设置国民经济行业
        if (StrUtil.isBlank(d194Code)) {
            return null;
        }
        if (StrUtil.startWithAny(unit.getD04Code(), "332", "333", "334", "335") && StrUtil.startWithAny(unit.getD194Code(), "P831", "P832", "P833")) {
            //1	法人单位（独立单位）
            //2 与上级党组织相同
            //本级组织不能更改单位同步过来的国民经济行业那两个属性
            if (orgDTO.getD02Code().startsWith(CommonConstant.ONE)) {
                orgDTO.setD194Name(unit.getD194Name());
                orgDTO.setD194Code(unit.getD194Code());
            }
            //与上级党组织相同的下级党组织不能选择本级组织同步过来的国民经济行业
            if (orgDTO.getD02Code().startsWith(CommonConstant.TWO)) {
                if (orgDTO.getD194Code().equals(unit.getD194Code())) {
                    return new OutMessage<>(Status.ORG_NATIONAL_ECONOMY_SAME_UNIT);
                }
            }
        }
        return null;
    }

    /**
     * 处理组织树排序号
     */
    private void processOrgTreeSort() {
        Org topOrg = this.findTopOrg();
        if (Objects.isNull(topOrg)) {
            return;
        }
        String shortOrgCode = topOrg.getOrgCode();
        List<Org> orgList = list(new QueryWrapper<Org>().lambda().select().likeRight(Org::getOrgCode, shortOrgCode).isNull(Org::getDeleteTime).last("ORDER BY length(\"org_code\"),sort,\"create_time\" DESC,\"id\" DESC"));
        List<OrgAll> orgAllList = orgAllService.list(new QueryWrapper<OrgAll>().lambda().select().likeRight(OrgAll::getOrgCode, shortOrgCode).isNull(OrgAll::getDeleteTime).last("ORDER BY length(\"org_code\"),sort,\"create_time\" DESC,\"id\" DESC"));
        AtomicReference<Integer> sortId = new AtomicReference<>();
        if (CollUtil.isNotEmpty(orgList)) {
            sortId.set(0);
            orgList.forEach(org -> {
                Integer integer = sortId.get();
                org.setSort(integer);
                sortId.set(integer + 1);
            });

            this.updateBatchById(orgList, orgList.size());
        }
        if (CollUtil.isNotEmpty(orgAllList)) {
            sortId.set(0);
            orgList.forEach(org -> {
                Integer integer = sortId.get();
                org.setSort(integer);
                sortId.set(integer + 1);
            });

            orgAllService.updateBatchById(orgAllList, orgList.size());
        }
    }

    private Org findTopOrg() {
        LambdaQueryWrapper<Org> wrapper = new LambdaQueryWrapper<Org>()
                .select(Org::getCode, Org::getOrgCode).orderByAsc(Org::getOrgCode).last("limit 1");
        return getOne(wrapper);
    }

    private void checkD195(OrgDTO orgDTO, String d02Code, String parentOrgCode) {
        // TODO: 2023/12/5 增加国民经济兼容前端相关方法
        List<UnitOrgLinkedDTO> linkedDTOList = orgDTO.getLinkedDTOList();
        if (StrUtil.isNotEmpty(d02Code)) {
            //单独建立，永远不允许修改
            if (StrUtil.equals(d02Code, CommonConstant.ONE)) {
                orgDTO.setD195Code(null);
                orgDTO.setD195Name(null);
                orgDTO.setD194Code(null);
                orgDTO.setD194Name(null);
            } else {
                //与上级党组织相同或者联合党支部情况都需要看单位情况，才决定是否显示
                //与上级党组织相同
                String unitCode = null;
                if (StrUtil.equals(d02Code, CommonConstant.TWO)) {
                    unitCode = orgBusinessModelSyncService.findParentUnit(parentOrgCode);
                } else {
                    //与上级联合建立或者是联合党支部单独建立
                    List<UnitOrgLinkedDTO> linkedDTOS = linkedDTOList.stream().filter(link -> link.getIsUnitMain() == CommonConstant.ONE_INT).collect(Collectors.toList());
                    if (linkedDTOS.size() > CommonConstant.ZERO_INT) {
                        unitCode = linkedDTOS.get(CommonConstant.ZERO_INT).getUnitCode();
                    }
                }
                if (StrUtil.isNotEmpty(unitCode)) {
                    Unit byCode = unitService.findByCode(unitCode);
                    String d04Code = byCode.getD04Code();
                    if (!d04Code.startsWith("3") || !StrUtil.equalsAny(d04Code, "922", "923")) {
                        orgDTO.setD195Code(null);
                        orgDTO.setD195Name(null);
                        orgDTO.setD194Code(null);
                        orgDTO.setD194Name(null);
                    }

                }
            }
        }
    }
}
