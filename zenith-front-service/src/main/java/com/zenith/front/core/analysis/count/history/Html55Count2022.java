package com.zenith.front.core.analysis.count.history;

import com.b1809.analysis.entity.PeggingPara;
import com.b1809.analysis.extend.ITableCount;
import com.zenith.front.core.analysis.ext.condition.year2022.OrgIndustryAllConditionB;
import org.jooq.Condition;
import org.springframework.stereotype.Component;

import java.util.Map;

import static org.jooq.impl.DSL.*;

/**
 * 专题调查表十三 社会组织分层级建立行业党组织情况
 *
 * <AUTHOR>
 * @date 2022/1/5
 */
@Component
public class Html55Count2022 extends Html55CountHistory implements ITableCount {


    @Override
    public String getReportCode() {
        return "2022_55.html";
    }

    @Override
    public Map<String, Map<String, Number>> doCount(String reportCode, String orgCode, String orgLevelCode) throws Exception {
        return null;
    }

    @Override
    public Map<String, Object> doPegging(PeggingPara peggingPara) throws Exception {
        return null;
    }

    @Override
    public Map<String, Number> doReplenishCount(String reportCode, String orgCode, String orgLevelCode) throws Exception {
        return getReplenishCount(orgCode, orgLevelCode, Html1Count2022.TABLE_ES_YEAR_B);
    }


    @Override
    public Map<String, Object> doReplenishPegging(PeggingPara peggingPara) throws Exception {
        OrgIndustryAllConditionB orgIndustryAllCondition = new OrgIndustryAllConditionB();
        Condition condition = noCondition().and(field(name("delete_time")).isNull().and(orgIndustryAllCondition.create(peggingPara.getOrgCode(), peggingPara.getOrgLevelCode()))
                .and(field(name("industry_classification")).eq("48"))).and(this.getRowCondition(peggingPara.getRowIndex()));

        return Html48CountHistory.getReportPageResult(peggingPara, orgIndustryAllCondition.getTableName(), condition, orgIndustryAllCondition.getLevelCodeField());
    }


}
