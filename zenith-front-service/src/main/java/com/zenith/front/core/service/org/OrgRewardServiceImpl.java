package com.zenith.front.core.service.org;

import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.zenith.front.api.org.IOrgRewardService;
import com.zenith.front.common.constant.CommonConstant;
import com.zenith.front.common.kit.CodeUtil;
import com.zenith.front.common.kit.StrKit;
import com.zenith.front.core.kit.DbUtil;
import com.zenith.front.core.service.sync.SyncOrgService;
import com.zenith.front.dao.mapper.org.OrgRewardMapper;
import com.zenith.front.model.dto.OrgRewardDTO;
import com.zenith.front.model.message.OutMessage;
import com.zenith.front.model.message.Status;
import com.zenith.front.model.bean.OrgReward;
import org.jooq.SelectConditionStep;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

import static com.zenith.front.model.constant.UserConstant.USER_CONTEXT;
import static org.jooq.impl.DSL.field;
import static org.jooq.impl.DSL.name;

/**
 * <p>
 * 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-07-10
 */
@Service
public class OrgRewardServiceImpl extends ServiceImpl<OrgRewardMapper, OrgReward> implements IOrgRewardService {
    @Resource
    private IOrgRewardService orgRewardDao;
    @Resource
    private OrgRewardMapper orgRewardMapper;
    @Resource
    private SyncOrgService syncOrgService;

    @Override
    public List<OrgReward> findOrgRewardByOrgCode(String orgCode) {
        return list(
                new LambdaQueryWrapper<OrgReward>()
                        .eq(OrgReward::getOrgCode, orgCode)
                        .isNull(OrgReward::getDeleteTime)
                        .orderByDesc(OrgReward::getCreateTime)
                        .orderByDesc(OrgReward::getId)
        );
    }

    @Override
    public OrgReward findByCode(String code) {
        return getOne(new LambdaQueryWrapper<OrgReward>().eq(OrgReward::getCode, code));
    }

    /**
     * 获取组织奖惩列表
     *
     * @param pageNum
     * @param pageSize
     * @param orgCode
     * @return
     */
    @Override
    public OutMessage getList(int pageNum, int pageSize, String orgCode) {
        Page<OrgReward> page = orgRewardDao.getBaseMapper().selectPage(new Page<>(pageNum, pageSize),
                new LambdaQueryWrapper<OrgReward>().eq(OrgReward::getRewardOrgCode, orgCode).isNull(OrgReward::getDeleteTime)
                        .orderByDesc(OrgReward::getCreateTime).orderByDesc(OrgReward::getId));
        return new OutMessage<>(Status.SUCCESS, page);
    }

    /**
     * 新增组织奖惩
     *
     * @param orgRewardDTO
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public OutMessage addReward(OrgRewardDTO orgRewardDTO) {
        orgRewardDTO.setEsId(CodeUtil.getEsId());
        orgRewardDTO.setCode(StrKit.getRandomUUID());
        orgRewardDTO.setIsHistory(CommonConstant.ZERO_INT);
        orgRewardDTO.setTimestamp(new Date());
        orgRewardDTO.setCreateTime(new Date());
        orgRewardDTO.setUpdateTime(new Date());
        orgRewardDTO.setUpdateAccount(USER_CONTEXT.get().getUser().getAccount());
        OrgReward orgReward = new OrgReward();
        BeanUtils.copyProperties(orgRewardDTO, orgReward);
        orgReward.setId(null);
        boolean flag = orgRewardDao.save(orgReward);
        if (flag) {
            syncOrgService.syncOrgAllD42Code(orgReward.getOrgCode());
        }
        return new OutMessage<>(flag ? Status.SUCCESS : Status.FAIL);
    }

    /**
     * 根据code查找
     *
     * @param code
     * @return
     */
    @Override
    public OutMessage findByCodeM(String code) {
        OrgReward orgReward = orgRewardDao.findByCode(code);
        if (!orgReward.getRewardOrgCode().startsWith(USER_CONTEXT.get().getUserRolePermission().getOrgCode())) {
            return new OutMessage<>(Status.PERMISSION_DENIED);
        }
        return new OutMessage<>(Status.SUCCESS, orgReward);
    }

    /**
     * 修改时间
     *
     * @param orgRewardDTO
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public OutMessage updateReward(OrgRewardDTO orgRewardDTO) {
        String code = orgRewardDTO.getCode();
        OrgReward orgReward = orgRewardDao.findByCode(code);
        if (ObjectUtil.isNull(orgReward)) {
            return new OutMessage<>(Status.PARA_ERROR);
        }
        orgRewardDTO.setUpdateAccount(USER_CONTEXT.get().getUser().getAccount());
        orgRewardDTO.setUpdateTime(new Date());
        orgRewardDTO.setTimestamp(new Date());
        BeanUtils.copyProperties(orgRewardDTO, orgReward);
        boolean flag = orgRewardDao.updateById(orgReward);
        if (flag) {
            syncOrgService.syncOrgAllD42Code(orgReward.getOrgCode());
        }
        return new OutMessage<>(flag ? Status.SUCCESS : Status.FAIL);
    }

    /**
     * 删除组织奖惩信息
     *
     * @param code
     * @param currManOrgCode
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public OutMessage delReward(String code, String currManOrgCode) {
        OrgReward orgReward = orgRewardDao.findByCode(code);
        // 获取组织层级码
        String rewardOrgCode = orgReward.getRewardOrgCode();
        if (!rewardOrgCode.startsWith(currManOrgCode)) {
            return new OutMessage<>(Status.PERMISSION_DENIED);
        }
        orgReward.setDeleteTime(new Date());
        orgReward.setTimestamp(new Date());
        orgReward.setUpdateAccount(USER_CONTEXT.get().getUser().getAccount());
        boolean update = orgRewardDao.updateById(orgReward);
        if (update) {
            syncOrgService.syncOrgAllD42Code(orgReward.getOrgCode());
        }
        return new OutMessage<>(update ? Status.SUCCESS : Status.FAIL);
    }

    @Override
    public Integer getOrgCommend(String orgCode) {
        SelectConditionStep record1s = DbUtil.DSL_CONTEXT.selectCount()
                .from(name("ccp_org_reward"))
                .where(field(name("reward_org_code"), String.class).like(orgCode + "%"))
                .and(field(name("d42_code"), String.class).eq("17"))
                .and(field(name("delete_time")).isNull());
        return orgRewardMapper.getOrgCommend(record1s.toString());
    }

    @Override
    public List<OrgReward> getRewardsByOrgCode(String orgCode, String d42Code) {
        return orgRewardDao.list(
                new LambdaQueryWrapper<OrgReward>().eq(OrgReward::getRewardOrgCode, orgCode).eq(OrgReward::getD42Code,d42Code).isNull(OrgReward::getDeleteTime)
                        .orderByDesc(OrgReward::getCreateTime).orderByDesc(OrgReward::getId));
    }


}
