package com.zenith.front.core.service.mem;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.thread.ThreadUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.zenith.front.api.mem.*;
import com.zenith.front.api.org.*;
import com.zenith.front.api.report.IStatisticsYearService;
import com.zenith.front.api.sync.ISyncMemService;
import com.zenith.front.api.unit.IUnitAllService;
import com.zenith.front.api.unit.IUnitCommitteeService;
import com.zenith.front.api.unit.IUnitCommunityService;
import com.zenith.front.api.unit.IUnitCountrusideService;
import com.zenith.front.common.constant.CommonConstant;
import com.zenith.front.common.kit.CodeUtil;
import com.zenith.front.common.kit.ModelUtils;
import com.zenith.front.common.kit.StrKit;
import com.zenith.front.core.cache.CacheUtils;
import com.zenith.front.model.bean.*;
import com.zenith.front.model.custom.Record;
import com.zenith.front.dao.mapper.mem.MemRewardMapper;
import com.zenith.front.model.dto.MemRewardDTO;
import com.zenith.front.model.message.OutMessage;
import com.zenith.front.model.message.Status;

import com.zenith.front.model.vo.BetweenReportDateVo;
import org.jooq.Record2;
import org.jooq.SelectLimitPercentStep;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.Executor;
import java.util.stream.Collectors;

import static com.zenith.front.model.constant.UserConstant.USER_CONTEXT;
import static com.zenith.front.core.kit.DbUtil.DSL_CONTEXT;
import static org.jooq.impl.DSL.*;

/**
 * <p>
 * 奖惩及出党
 * </p>
 *
 * <AUTHOR>
 * @since 2021-07-10
 */
@Service
public class MemRewardServiceImpl extends ServiceImpl<MemRewardMapper, MemReward> implements IMemRewardService {

    @Resource
    MemRewardMapper memRewardMapper;

    @Resource
    private IMemService iMemService;
    @Resource
    private IMemAllInfoService iMemAllInfoService;
    @Resource
    private ISyncMemService iSyncMemService;
    @Resource
    private Executor mySimpleAsync;
    @Resource
    private IOrgCommitteeService iOrgCommitteeService;
    @Resource
    private IUnitCommitteeService iUnitCommitteeService;
    @Resource
    private IUnitCountrusideService iUnitCountrusideService;
    @Resource
    private IOrgPartyCongressCommitteeService iOrgPartyCongressCommitteeService;
    @Resource
    private IOrgPartyCongressCommitteeAllService iOrgPartyCongressCommitteeAllService;
    @Resource
    private IMemFlowService iMemFlowService;
    @Resource
    private IMemFlowAllService iMemFlowAllService;
    @Resource
    private IOrgService iOrgService;
    @Resource
    private IOrgAllService iOrgAllService;
    @Resource
    private IUnitCommunityService iUnitCommunityService;
    @Resource
    private IUnitAllService iUnitAllService;
    @Resource
    private IMemReportService memReportService;
    @Resource
    private IStatisticsYearService iStatisticsYearService;


    @Override
    public MemReward findByCode(String code) {
        LambdaQueryWrapper<MemReward> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(MemReward::getCode, code)
                .isNull(MemReward::getDeleteTime);
        return getOne(wrapper);
    }

    /**
     * 获取人员奖惩列表
     *
     * @param pageNum
     * @param pageSize
     * @param memCode
     * @return
     */
    @Override
    public OutMessage getList(int pageNum, int pageSize, String memCode) {
        LambdaQueryWrapper<MemReward> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(MemReward::getMemCode, memCode)
                .isNull(MemReward::getDeleteTime)
                .orderByDesc(MemReward::getCreateTime).orderByDesc(MemReward::getId);
        Page<MemReward> page = memRewardMapper.selectPage(new Page<>(pageNum, pageSize), wrapper);
        return new OutMessage<>(Status.SUCCESS, page);
    }

    private static final Map<String, String> map = new HashMap<String, String>() {

        private static final long serialVersionUID = 6154006497943617631L;

        {
            put("C15", "开除党籍");
            put("C22", "取消预备党员资格");
            put("C23", "取消预备党员资格");
            put("C24", "劝退");
            put("C241", "劝退");
            put("C25", "劝退而不除名");
            put("C251", "劝退而不除名");
            put("C26", "退党除名");
            put("C27", "自行脱党除名");
            put("C28", "劝退而不除名");
            put("C29", "不予承认");
            put("C34", "劝退");
            put("C341", "劝退");
            put("C35", "劝退而不除名");
            put("C351", "劝退而不除名");
            put("C36", "退党除名");
            put("C37", "自行脱党除名");
            put("C38", "劝退而不除名");
            put("C32", "退党除名");
            put("C20", "退党除名");
        }
    };

    /**
     * 人员奖惩
     *
     * @param memRewardDTO
     * @return
     */
    @Override
    public OutMessage addMemReward(MemRewardDTO memRewardDTO) {
        OutMessage message = this.dealWithMemInfo(memRewardDTO);
        if (Objects.nonNull(message)) {
            return message;
        }
        memRewardDTO.setCode(StrKit.getRandomUUID());
        memRewardDTO.setEsId(CodeUtil.getEsId());
        memRewardDTO.setCreateTime(new Date());
        memRewardDTO.setUpdateTime(new Date());
        memRewardDTO.setUpdateAccount(USER_CONTEXT.get().getUser().getAccount());
        memRewardDTO.setTimestamp(new Date());
        memRewardDTO.setIsHistory(CommonConstant.ZERO_INT);
        MemReward memReward = memRewardDTO.toModel();
        memReward.setId(null);
        boolean flag = save(memReward);

        if (flag) {
            ThreadUtil.execAsync(() -> iSyncMemService.syncMemReward(memReward.getCode()));
        }
        return new OutMessage<>(flag ? Status.SUCCESS : Status.FAIL);
    }

    /**
     * 退出党籍关联数据处理
     */
    private OutMessage dealWithMemInfo(MemRewardDTO memRewardDTO) {
        Mem mem = iMemService.findByCode(memRewardDTO.getMemCode());
        if (map.containsKey(memRewardDTO.getD029Code())) {
            Map<String, String> d21Map = CacheUtils.getDic("dict_d12").stream().collect(Collectors.toMap(record -> record.get("name"), record1 -> record1.get("key"), (a, b) -> b, LinkedHashMap::new));
            String name = map.get(memRewardDTO.getD029Code());
            if (d21Map.containsKey(name)) {
                BetweenReportDateVo betweenReportDate = iStatisticsYearService.isBeforeReportDate(memRewardDTO.getStartDate(), "2");
                if (!betweenReportDate.getIsBetween()) {
                    return new OutMessage<String>(Status.NOT_IN_BETWEEN_DEFAULT_DATE).format("惩罚时间", betweenReportDate.getMessage());
                }
                mem.setD12Code(d21Map.get(name));
                mem.setD12Name(name);
                mem.setDeleteTime(new Date());
                mem.setLeaveOrgDate(Objects.nonNull(memRewardDTO.getStartDate()) ? memRewardDTO.getStartDate() : new Date());
                mem.setD50Code(this.getMemD50CodeByD030Name(memRewardDTO.getD030Name()));
                mem.setD029Code(memRewardDTO.getD029Code());
                boolean b = iMemService.updateById(mem);
                if (b) {
                    ThreadUtil.execAsync(() -> {
                        this.outPartyCleanData(mem.getCode(), memRewardDTO.getStartDate(), null);
                        iMemAllInfoService.update(new LambdaUpdateWrapper<MemAllInfo>().set(MemAllInfo::getD12Code, mem.getD12Code()).set(MemAllInfo::getD12Name, mem.getD12Name()).set(MemAllInfo::getD50Code, mem.getD50Code())
                                .set(MemAllInfo::getLeaveOrgDate, mem.getLeaveOrgDate()).set(MemAllInfo::getD029Code, mem.getD029Code()).set(MemAllInfo::getDeleteTime, mem.getDeleteTime())
                                .set(MemAllInfo::getD030Code, memRewardDTO.getD030Code())
                                .eq(MemAllInfo::getCode, mem.getCode()));
                    });
                }
            }
        } else {
            if (iStatisticsYearService.isBetweenReportDate(memRewardDTO.getStartDate())) {
                iMemAllInfoService.update(new LambdaUpdateWrapper<MemAllInfo>().set(MemAllInfo::getD029Code, memRewardDTO.getD029Code()).set(MemAllInfo::getD030Code, memRewardDTO.getD030Code())
                        .eq(MemAllInfo::getCode, memRewardDTO.getMemCode()));
            }
        }
        return null;
    }

    public String getMemD50CodeByD030Name(String d030Name) {
        if (StrUtil.isNotEmpty(d030Name)) {
            Map<String, String> d50Map = CacheUtils.getDic("dict_d50").stream().collect(Collectors.toMap(record -> record.get("name"), record1 -> record1.get("key"), (a, b) -> b, LinkedHashMap::new));
            List<String> d50CodeList = new ArrayList<>();
            for (String str : d030Name.split(",")) {
                String d50Code = d50Map.get(str);
                if (StrUtil.isNotEmpty(d50Code)) {
                    d50CodeList.add(d50Code);
                }
            }
            if (CollUtil.isNotEmpty(d50CodeList)) {
                return CollUtil.join(d50CodeList, ",");
            }
        }
        return null;
    }

    /**
     * 获取人员奖惩信息
     *
     * @param code
     * @return
     */
    @Override
    public OutMessage findByCodeOut(String code) {
        MemReward memReward = this.findByCode(code);
        String rewardOrgCode = memReward.getRewardOrgCode();
        if (!rewardOrgCode.startsWith(USER_CONTEXT.get().getUserRolePermission().getOrgCode())) {
            return new OutMessage<>(Status.PERMISSION_DENIED);
        }
        return new OutMessage<>(Status.SUCCESS, memReward);
    }

    /**
     * 修改人员奖惩
     *
     * @param memRewardDTO
     * @return
     */
    @Override
    public OutMessage updateMemReward(MemRewardDTO memRewardDTO) throws Exception {
        OutMessage message = this.dealWithMemInfo(memRewardDTO);
        if (Objects.nonNull(message)) {
            return message;
        }
        String code = memRewardDTO.getCode();
        MemReward memReward = this.findByCode(code);
        if (ObjectUtil.isNull(memReward)) {
            return new OutMessage<>(Status.OBJEC_NOT_EXIST);
        }
        String rewardOrgCode = memReward.getRewardOrgCode();
        if (!rewardOrgCode.startsWith(USER_CONTEXT.get().getUserRolePermission().getOrgCode())) {
            return new OutMessage<>(Status.PERMISSION_DENIED);
        }
        if (!memReward.getType().equals(memRewardDTO.getType())) {
            return new OutMessage<>(Status.PARA_ERROR);
        }
        memRewardDTO.setUpdateTime(new Date());
        memRewardDTO.setUpdateAccount(USER_CONTEXT.get().getUser().getAccount());
        memRewardDTO.setTimestamp(new Date());
        ModelUtils.copyProperties(memRewardDTO, memReward);
        boolean flag = updateById(memReward);
        if (flag) {
            ThreadUtil.execAsync(() -> iSyncMemService.syncMemReward(code));
        }
        return new OutMessage<>(flag ? Status.SUCCESS : Status.FAIL);
    }

    /**
     * 删除
     *
     * @param code
     * @return
     */
    @Override
    public OutMessage delMemReward(String code) {
        MemReward memReward = this.findByCode(code);
        if (ObjectUtil.isNull(memReward)) {
            return new OutMessage<>(Status.OBJEC_NOT_EXIST);
        }
        String rewardOrgCode = memReward.getRewardOrgCode();
        if (!rewardOrgCode.startsWith(USER_CONTEXT.get().getUserRolePermission().getOrgCode())) {
            return new OutMessage<>(Status.PERMISSION_DENIED);
        }
        memReward.setUpdateAccount(USER_CONTEXT.get().getUser().getAccount());
        memReward.setTimestamp(new Date());
        memReward.setIsHistory(CommonConstant.ONE_INT);
        memReward.setDeleteTime(new Date());
        boolean flag = updateById(memReward);
        if (flag) {
            ThreadUtil.execAsync(() -> {
                iSyncMemService.syncMemReward(code);
                MemReward one = this.getOne(new LambdaQueryWrapper<MemReward>().eq(MemReward::getMemCode, memReward.getMemCode()).isNull(MemReward::getDeleteTime).isNotNull(MemReward::getD030Code)
                        .apply(iStatisticsYearService.getReportDateSql("start_date")).orderByDesc(MemReward::getStartDate).last("limit 1"));
                one = Objects.isNull(one) ? new MemReward() : one;
                iMemAllInfoService.update(new LambdaUpdateWrapper<MemAllInfo>().set(MemAllInfo::getD029Code, one.getD029Code()).set(MemAllInfo::getD030Code, one.getD030Code())
                        .eq(MemAllInfo::getCode, memReward.getMemCode()));
            });
        }
        return new OutMessage<>(flag ? Status.SUCCESS : Status.FAIL);
    }

    @Override
    public Record getMemCommend(String orgCode) {
        SelectLimitPercentStep<Record2<BigDecimal, BigDecimal>> record2s = DSL_CONTEXT.select(
                        sum(choose().when(field(name("d029_code"), String.class).in("121", "131", "141", "151", "161", "171"), 1).otherwise(0)).as("excellentMem"),
                        sum(choose().when(field(name("d029_code"), String.class).in("112", "132", "142", "152", "162", "172"), 1).otherwise(0)).as("partyWorkers")
                )
                .from(name("ccp_mem_reward"))
                .where(field(name("reward_org_code"), String.class).like(orgCode + "%"))
                .and(field(name("delete_time")).isNull()).limit(1);
        return new Record().put(memRewardMapper.getMemCommend(record2s.toString()));
    }


    /**
     * 党员出党关联业务处理
     *
     * @param memCode   党员标识
     * @param startDate 奖惩时间
     */
    public boolean outPartyCleanData(String memCode, Date startDate, String d12Code) {
        if (Objects.isNull(startDate)) {
            return false;
        }
        //党员党内职务,党员班子职务业务处理
        boolean update1 = iOrgCommitteeService.update(new LambdaUpdateWrapper<OrgCommittee>().set(OrgCommittee::getEndDate, startDate).eq(OrgCommittee::getMemCode, memCode)
                .isNull(OrgCommittee::getDeleteTime).isNull(OrgCommittee::getEndDate));
        //单位班子业务处理：需要将党员的班子成员的任职信息，变更为届内历史人员，并且自动打上离开时间
        boolean update2 = iUnitCommitteeService.update(new LambdaUpdateWrapper<UnitCommittee>().set(UnitCommittee::getEndDate, startDate).eq(UnitCommittee::getMemCode, memCode)
                .isNull(UnitCommittee::getDeleteTime).isNull(UnitCommittee::getEndDate));

        //村(社区)工作者和后备干部业务处理：需要将党员的村社区以及后备干部，变更为离开，离开去向为其他
        boolean update3 = iUnitCountrusideService.update(new LambdaUpdateWrapper<UnitCountryside>().set(UnitCountryside::getLeaveTime, startDate)
                .set(UnitCountryside::getD117Code, "3").set(UnitCountryside::getD117Name, "其他").set(UnitCountryside::getDeleteTime, new Date()).eq(UnitCountryside::getMemCode, memCode)
                .isNull(UnitCountryside::getDeleteTime).isNull(UnitCountryside::getLeaveTime));
        //党代表信息处理：需要将党员的党代表资格进行终止，终止原因选择一致，比如被党组织劝退，除名或自行脱党停止党代表资格
        //维护死亡后，选项应该是6 因病、因故死亡终止代表资格
        Integer d105Code = StrUtil.startWith(d12Code, "1") ? 6 : 3;
        String d105Name = StrUtil.startWith(d12Code, "1") ? "因病、因故死亡终止代表资格" : "被党组织劝退、除名或者自行脱党终止代表资格";
        boolean update4 = iOrgPartyCongressCommitteeService.update(new LambdaUpdateWrapper<OrgPartyCongressCommittee>().set(OrgPartyCongressCommittee::getEndDate, startDate)
                .set(OrgPartyCongressCommittee::getd105Code, d105Code).set(OrgPartyCongressCommittee::getd105Name, d105Name).eq(OrgPartyCongressCommittee::getMemCode, memCode)
                .isNull(OrgPartyCongressCommittee::getDeleteTime).isNull(OrgPartyCongressCommittee::getEndDate));
        iOrgPartyCongressCommitteeAllService.update(new LambdaUpdateWrapper<OrgPartyCongressCommitteeAll>().set(OrgPartyCongressCommitteeAll::getEndDate, startDate)
                .set(OrgPartyCongressCommitteeAll::getd105Code, d105Code).set(OrgPartyCongressCommitteeAll::getd105Name, d105Name).eq(OrgPartyCongressCommitteeAll::getMemCode, memCode)
                .isNull(OrgPartyCongressCommitteeAll::getDeleteTime).isNull(OrgPartyCongressCommitteeAll::getEndDate));

        //流动党员中信息处理：需要将党员得流出进行停止流动，并且增加上流回日期
        boolean update5 = iMemFlowService.update(new LambdaUpdateWrapper<MemFlow>().set(MemFlow::getFlowStatus, 2).set(MemFlow::getBackflowDate, startDate).eq(MemFlow::getMemCode, memCode).eq(MemFlow::getFlowStatus, 1));
        iMemFlowAllService.update(new LambdaUpdateWrapper<MemFlowAll>().set(MemFlowAll::getFlowStatus, 2).set(MemFlowAll::getBackflowDate, startDate).eq(MemFlowAll::getMemCode, memCode).eq(MemFlowAll::getFlowStatus, 1));

        //关系转接中信息处理：需要将党员得关系转出自动打回，并且撤销关系转接

        //组织基础信息中党组织书记处理：需要将党组织中组织书记处理掉，并且抹除为空
        this.dealWithOrgSecretary(memCode, startDate);

        //单位管理中，行政村得第一书记相关数据处理，需要置换掉第一书记情况
        this.dealWithUnitFirstSecretary(memCode);
        //处理memReport
        this.deleteMemReport(memCode);

        return update1 && update2 && update3 && update4 && update5;
    }


    /**
     * 处理党组织书记
     */
    private void dealWithOrgSecretary(String memCode, Date startDate) {
        LambdaQueryWrapper<OrgCommittee> wrapper = new QueryWrapper<OrgCommittee>().lambda().eq(OrgCommittee::getMemCode, memCode).eq(OrgCommittee::getEndDate, startDate).eq(OrgCommittee::getD022Code, "1");
        List<OrgCommittee> orgCommitteeList = iOrgCommitteeService.list(wrapper);
        List<String> orgCodes = orgCommitteeList.stream().map(OrgCommittee::getOrgCode).collect(Collectors.toList());
        orgCodes.forEach(orgCode -> {
            Org org = iOrgService.findOrgByCode(orgCode);
            OrgAll orgAll = iOrgAllService.findByCode(orgCode);
            if (ObjectUtil.isNotNull(org) && ObjectUtil.isNotNull(orgAll)) {
                org.setSecretary("");
                orgAll.setSecretary("");
                iOrgService.updateById(org);
                iOrgAllService.updateById(orgAll);
            }
        });
    }

    /**
     * 处理单位扩展中第一书记
     */
    private void dealWithUnitFirstSecretary(String memCode) {
        String year = iStatisticsYearService.getStatisticalYear();
        List<UnitCommunity> list = iUnitCommunityService.list(new LambdaQueryWrapper<UnitCommunity>().eq(UnitCommunity::getFirstSecretaryCode, memCode)
                .apply("to_char(\"year\" , 'yyyy')='" + year + "'").isNull(UnitCommunity::getDeleteTime));
        list.forEach(unitCommunity -> {
            unitCommunity.setHasFirstSecretary(0);
            unitCommunity.setFirstSecretarySelect("0");
            unitCommunity.setFirstSecretaryCode("");
            unitCommunity.setFirstSecretaryName("");
        });
        if (list.size() > 0) {
            iUnitCommunityService.updateBatchById(list, list.size());
            List<String> unitCodes = list.stream().map(UnitCommunity::getUnitCode).collect(Collectors.toList());
            unitCodes.forEach(orgCode -> {
                UnitAll unitAll = iUnitAllService.findByCode(orgCode);
                if (ObjectUtil.isNotNull(unitAll)) {
                    unitAll.setFirstSecretarySelect("0");
                    unitAll.setFirstSecretaryCode("");
                    unitAll.setFirstSecretaryName("");
                    iUnitAllService.updateById(unitAll);
                }
            });
        }
    }

    public void deleteMemReport(String memCode) {
        List<MemReport> memReportList = memReportService.findByMemCode(memCode);
        if (CollUtil.isNotEmpty(memReportList)) {
            memReportList.forEach(memReport -> memReport.setDeleteTime(new Date()));
            memReportService.updateBatchById(memReportList, memReportList.size());
        }
    }

    public void clearRewardData(String memCode) {
        List<MemReward> list = list(new LambdaQueryWrapper<MemReward>().select(MemReward::getId).eq(MemReward::getMemCode, memCode)
                .isNull(MemReward::getDeleteTime).in(MemReward::getD029Code, map.keySet()));
        if (CollUtil.isNotEmpty(list)) {
            list.forEach(e -> e.setDeleteTime(new Date()));
            updateBatchById(list, list.size());
        }
    }


}
