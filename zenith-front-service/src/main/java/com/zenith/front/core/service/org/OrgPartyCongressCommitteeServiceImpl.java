package com.zenith.front.core.service.org;

import cn.afterturn.easypoi.excel.entity.ExportParams;
import cn.afterturn.easypoi.excel.entity.params.ExcelExportEntity;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.baomidou.mybatisplus.extension.toolkit.SqlHelper;
import com.zenith.front.api.mem.IMemService;
import com.zenith.front.api.org.IOrgPartyCongressCommitteeAllService;
import com.zenith.front.api.org.IOrgPartyCongressCommitteeService;
import com.zenith.front.api.org.IOrgService;
import com.zenith.front.api.report.IStatisticsYearService;
import com.zenith.front.common.constant.CommonConstant;
import com.zenith.front.common.constant.DictConstant;
import com.zenith.front.common.encrypt.properties.EncryptProperties;
import com.zenith.front.common.kit.CodeUtil;
import com.zenith.front.common.kit.EasyPoiExcelUtil;
import com.zenith.front.common.kit.ExcelExportUtil;
import com.zenith.front.common.kit.StringToBooleanConverter;
import com.zenith.front.common.untils.SM4Untils;
import com.zenith.front.core.cache.CacheUtils;
import com.zenith.front.core.kit.ChartUtil;
import com.zenith.front.dao.mapper.org.OrgPartyCongressCommitteeAllMapper;
import com.zenith.front.dao.mapper.org.OrgPartyCongressCommitteeMapper;
import com.zenith.front.dao.mapper.org.OrgPartyCongressElectMapper;
import com.zenith.front.model.bean.*;
import com.zenith.front.model.custom.Record;
import com.zenith.front.model.dto.ChartDataDTO;
import com.zenith.front.model.dto.OrgPartyCongressCommitteeDTO;
import com.zenith.front.model.dto.OrgPartyCongressCommitteeListDTO;
import com.zenith.front.model.dto.OrgPartyCongressCommitteeTerminationDTO;
import com.zenith.front.model.message.OutMessage;
import com.zenith.front.model.message.Status;
import com.zenith.front.model.vo.OptionVO;
import com.zenith.front.model.vo.OrgPartyCongressCommitteeExcel;
import com.zenith.front.model.vo.OrgPartyCongressCommitteeVO;
import org.apache.poi.ss.usermodel.Workbook;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.ResourceUtils;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.io.File;
import java.io.FileInputStream;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.Executor;
import java.util.stream.Collectors;

/**
 * <p>
 * 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-09-29
 */
@Service
public class OrgPartyCongressCommitteeServiceImpl extends ServiceImpl<OrgPartyCongressCommitteeMapper, OrgPartyCongressCommittee> implements IOrgPartyCongressCommitteeService {

    @Resource
    private OrgPartyCongressElectMapper orgPartyCongressElectMapper;
    @Resource
    private OrgPartyCongressCommitteeMapper orgPartyCongressCommitteeMapper;
    @Resource
    private IOrgPartyCongressCommitteeAllService orgPartyCongressCommitteeAllService;
    @Resource
    private OrgPartyCongressCommitteeAllMapper orgPartyCongressCommitteeAllMapper;
    @Resource
    private IStatisticsYearService iStatisticsYearService;
    @Resource
    Executor mySimpleAsync;
    @Resource
    private IOrgService orgService;
    @Resource
    private IMemService memService;
    @Resource
    HttpServletResponse response;

    @Override
    public OutMessage<Object> doSave(OrgPartyCongressCommitteeDTO dto, String account) {
        return doHandle(dto, account, true, "id");
    }

    @Override
    public OutMessage<Object> doDel(Long id, String account) {
        OrgPartyCongressCommittee dbOrgPartyCongressCommittee = orgPartyCongressCommitteeMapper.selectById(id);
        if (Objects.isNull(dbOrgPartyCongressCommittee) || Objects.nonNull(dbOrgPartyCongressCommittee.getDeleteTime())) {
            return new OutMessage<>(Status.SUCCESS);
        }
        OrgPartyCongressCommittee orgPartyCongressCommittee = new OrgPartyCongressCommittee();
        orgPartyCongressCommittee.setId(dbOrgPartyCongressCommittee.getId());
        orgPartyCongressCommittee.setUpdateAccount(account);
        orgPartyCongressCommittee.setTimestamp(new Date());
        orgPartyCongressCommittee.setDeleteTime(new Date());
        boolean flag = updateById(orgPartyCongressCommittee);
        if (flag) {
            final String code = dbOrgPartyCongressCommittee.getCode();
            CompletableFuture.runAsync(() -> syncDel(code, account), mySimpleAsync);
        }
        return new OutMessage<>(flag ? Status.SUCCESS : Status.FAIL);
    }

    private void syncDel(String code, String account) {
        List<OrgPartyCongressCommitteeAll> allList = orgPartyCongressCommitteeAllMapper.selectList(
                new LambdaQueryWrapper<OrgPartyCongressCommitteeAll>()
                        .eq(OrgPartyCongressCommitteeAll::getCode, code)
                        .isNull(OrgPartyCongressCommitteeAll::getDeleteTime)
        );
        if (CollUtil.isNotEmpty(allList)) {
            for (OrgPartyCongressCommitteeAll orgPartyCongressCommitteeAll : allList) {
                orgPartyCongressCommitteeAll.setDeleteTime(new Date());
                orgPartyCongressCommitteeAll.setUpdateAccount(account);
            }
            orgPartyCongressCommitteeAllService.updateBatchById(allList);
        }
    }

    @Override
    public OutMessage<Object> doUpdate(OrgPartyCongressCommitteeDTO dto, String account) {
        return doHandle(dto, account, false);
    }

    private OutMessage<Object> doHandle(OrgPartyCongressCommitteeDTO dto, String account, boolean hasSave, String... ignoreProperties) {
        String orgCode = dto.getOrgCode();
        Org org = orgService.findOrgByCode(orgCode);
        if (Objects.isNull(org)) {
            return new OutMessage<>(Status.ORG_NOT_EXIST);
        }
        String electCode = dto.getElectCode();
        LambdaQueryWrapper<OrgPartyCongressElect> lambdaQueryWrapper = new LambdaQueryWrapper<OrgPartyCongressElect>()
                .eq(OrgPartyCongressElect::getCode, electCode)
                .isNull(OrgPartyCongressElect::getDeleteTime);
        OrgPartyCongressElect orgPartyCongressElect = orgPartyCongressElectMapper.selectOne(lambdaQueryWrapper);
        if (Objects.isNull(orgPartyCongressElect)) {
            return new OutMessage<>(Status.ELECT_NOT_EXIST);
        }
        String memIdCard = dto.getMemIdcard();
        String memCode = dto.getMemCode();
        List<OrgPartyCongressCommittee> committeeList = orgPartyCongressCommitteeMapper.selectList(
                new LambdaQueryWrapper<OrgPartyCongressCommittee>()
                        .eq(OrgPartyCongressCommittee::getMemIdcard, memIdCard)
                        .eq(OrgPartyCongressCommittee::getOrgCode, orgCode)
                        .eq(StrUtil.equals(dto.getMemTypeCode(), "1"), OrgPartyCongressCommittee::getMemCode, memCode)
                        .isNull(OrgPartyCongressCommittee::getDeleteTime)
                        .ne(Objects.nonNull(dto.getId()), OrgPartyCongressCommittee::getId, dto.getId())
        );
        // 1 是本单位党员,判断code和身份证，0 不是本单位人员，判断身份证
        if (CollUtil.isNotEmpty(committeeList)) {
            return new OutMessage<>(Status.PERSONNEL_ALREADY_EXIST);
        }
        OrgPartyCongressCommittee orgPartyCongressCommittee = new OrgPartyCongressCommittee();
        BeanUtils.copyProperties(dto, orgPartyCongressCommittee, ignoreProperties);
        orgPartyCongressCommittee.setTimestamp(new Date());
        orgPartyCongressCommittee.setUpdateAccount(account);
        if (hasSave) {
            orgPartyCongressCommittee.setCode(IdUtil.simpleUUID());
            orgPartyCongressCommittee.setEsId(CodeUtil.getEsId());
            orgPartyCongressCommittee.setCreateTime(new Date());
        } else {
            orgPartyCongressCommittee.setUpdateTime(new Date());
            orgPartyCongressCommittee.setTimestamp(new Date());
        }
        boolean flag = hasSave ? SqlHelper.retBool(orgPartyCongressCommitteeMapper.insert(orgPartyCongressCommittee)) :
                this.update(orgPartyCongressCommittee, Wrappers.<OrgPartyCongressCommittee>lambdaUpdate()
                        .set(Objects.isNull(dto.getEndDate()), OrgPartyCongressCommittee::getEndDate, null)
                        .set(Objects.isNull(dto.getD105Code()), OrgPartyCongressCommittee::getd105Code, null)
                        .set(Objects.isNull(dto.getD105Name()), OrgPartyCongressCommittee::getd105Name, null)
                        .eq(OrgPartyCongressCommittee::getId, orgPartyCongressCommittee.getId()));
        if (flag) {
            CompletableFuture.runAsync(() -> sync(orgPartyCongressCommittee, orgPartyCongressElect.getTenureStartDate(), orgPartyCongressElect.getTenureEndDate(), org.getD01Code(), account, hasSave), mySimpleAsync);
        }
        return new OutMessage<>(flag ? Status.SUCCESS : Status.FAIL);
    }

    void sync(OrgPartyCongressCommittee orgPartyCongressCommittee, Date tenureStartDate, Date tenureEndDate, String d01Code, String account, boolean hasSave) {
        //是本单位党员
        Mem mem = null;
        if (StrUtil.equals(orgPartyCongressCommittee.getMemTypeCode(), CommonConstant.ONE)) {
            mem = memService.findByCode(orgPartyCongressCommittee.getMemCode());
        }
        if (hasSave) {
            OrgPartyCongressCommitteeAll orgPartyCongressCommitteeAll = new OrgPartyCongressCommitteeAll();
            BeanUtils.copyProperties(orgPartyCongressCommittee, orgPartyCongressCommitteeAll);
            if (StrUtil.startWith(d01Code, "12")) {
                orgPartyCongressCommitteeAll.setIsProvinceParty(CommonConstant.ONE);
            } else if (StrUtil.startWith(d01Code, "13")) {
                orgPartyCongressCommitteeAll.setIsCityParty(CommonConstant.ONE);
            } else if (StrUtil.startWith(d01Code, "14")) {
                orgPartyCongressCommitteeAll.setIsCountyParty(CommonConstant.ONE);
            } else {
                orgPartyCongressCommitteeAll.setIsTownParty(CommonConstant.ONE);
            }
            orgPartyCongressCommitteeAll.setd01Code(d01Code);
            if (Objects.nonNull(mem)) {
                orgPartyCongressCommitteeAll.setd09Code(mem.getD09Code());
                orgPartyCongressCommitteeAll.setd06Code(mem.getD06Code());
                orgPartyCongressCommitteeAll.setd124Code(mem.getD09Code());
                orgPartyCongressCommitteeAll.setd124Name(mem.getD09Name());
            }
            orgPartyCongressCommitteeAll.setAge(DateUtil.age(orgPartyCongressCommittee.getBirthday(), new Date()));
            if (tenureStartDate.before(new Date()) && tenureEndDate.after(new Date())) {
                orgPartyCongressCommitteeAll.setSlot(1);
            }
            orgPartyCongressCommitteeAllMapper.insert(orgPartyCongressCommitteeAll);
        } else {
            List<OrgPartyCongressCommitteeAll> congressCommitteeAllList = orgPartyCongressCommitteeAllMapper.selectList(
                    new LambdaQueryWrapper<OrgPartyCongressCommitteeAll>()
                            .eq(OrgPartyCongressCommitteeAll::getCode, orgPartyCongressCommittee.getCode())
                            .isNull(OrgPartyCongressCommitteeAll::getDeleteTime)
            );
            List<OrgPartyCongressCommitteeAll> updateList = new ArrayList<>();
            for (OrgPartyCongressCommitteeAll orgPartyCongressCommitteeAll : congressCommitteeAllList) {
                orgPartyCongressCommitteeAll.setMemName(orgPartyCongressCommittee.getMemName());
                orgPartyCongressCommitteeAll.setMemIdcard(orgPartyCongressCommittee.getMemIdcard());
                orgPartyCongressCommitteeAll.setSexCode(orgPartyCongressCommittee.getSexCode());
                orgPartyCongressCommitteeAll.setSexName(orgPartyCongressCommittee.getSexName());
                orgPartyCongressCommitteeAll.setBirthday(orgPartyCongressCommittee.getBirthday());
                orgPartyCongressCommitteeAll.setd07Code(orgPartyCongressCommittee.getd07Code());
                orgPartyCongressCommitteeAll.setd07Name(orgPartyCongressCommittee.getd07Name());
                orgPartyCongressCommitteeAll.setMemTypeCode(orgPartyCongressCommittee.getMemTypeCode());
                orgPartyCongressCommitteeAll.setStartDate(orgPartyCongressCommittee.getStartDate());
                orgPartyCongressCommitteeAll.setEndDate(orgPartyCongressCommittee.getEndDate());
                orgPartyCongressCommitteeAll.setd105Code(orgPartyCongressCommittee.getd105Code());
                orgPartyCongressCommitteeAll.setd105Name(orgPartyCongressCommittee.getd105Name());
                orgPartyCongressCommitteeAll.setd106Code(orgPartyCongressCommittee.getd106Code());
                orgPartyCongressCommitteeAll.setd106Name(orgPartyCongressCommittee.getd106Name());
                orgPartyCongressCommitteeAll.setPositionOrgName(orgPartyCongressCommittee.getPositionOrgName());
                orgPartyCongressCommitteeAll.setUpdateTime(new Date());
                orgPartyCongressCommitteeAll.setTimestamp(new Date());
                orgPartyCongressCommitteeAll.setUpdateAccount(account);
                if (!orgPartyCongressCommitteeAll.getd01Code().equals(d01Code)) {
                    if (StrUtil.startWith(d01Code, "12")) {
                        orgPartyCongressCommitteeAll.setIsProvinceParty(CommonConstant.ONE);
                    } else if (StrUtil.startWith(d01Code, "13")) {
                        orgPartyCongressCommitteeAll.setIsCityParty(CommonConstant.ONE);
                    } else if (StrUtil.startWith(d01Code, "14")) {
                        orgPartyCongressCommitteeAll.setIsCountyParty(CommonConstant.ONE);
                    } else {
                        orgPartyCongressCommitteeAll.setIsTownParty(CommonConstant.ONE);
                    }
                    orgPartyCongressCommitteeAll.setd01Code(d01Code);
                }
                if (Objects.nonNull(mem)) {
                    orgPartyCongressCommitteeAll.setd09Code(mem.getD09Code());
                    orgPartyCongressCommitteeAll.setd06Code(mem.getD06Code());
                    orgPartyCongressCommitteeAll.setd124Code(mem.getD09Code());
                    orgPartyCongressCommitteeAll.setd124Name(mem.getD09Name());
                } else if (StrUtil.equals(orgPartyCongressCommittee.getMemTypeCode(), CommonConstant.ZERO)) {
                    orgPartyCongressCommitteeAll.setd124Code(orgPartyCongressCommittee.getD124Code());
                    orgPartyCongressCommitteeAll.setd124Name(orgPartyCongressCommittee.getD124Name());
                }
                orgPartyCongressCommitteeAll.setAge(DateUtil.age(orgPartyCongressCommittee.getBirthday(), new Date()));
                if (tenureStartDate.before(new Date()) && tenureEndDate.after(new Date())) {
                    orgPartyCongressCommitteeAll.setSlot(1);
                }
                updateList.add(orgPartyCongressCommitteeAll);
            }
            updateList.forEach(all -> orgPartyCongressCommitteeAllService.update(all, Wrappers.<OrgPartyCongressCommitteeAll>lambdaUpdate()
                    .set(Objects.isNull(all.getEndDate()), OrgPartyCongressCommitteeAll::getEndDate, null)
                    .set(Objects.isNull(all.getd105Code()), OrgPartyCongressCommitteeAll::getd105Code, null)
                    .set(Objects.isNull(all.getd105Name()), OrgPartyCongressCommitteeAll::getd105Name, null)
                    .eq(OrgPartyCongressCommitteeAll::getId, all.getId())));
        }
    }

    @Override
    public OutMessage<Object> doList(Integer pageNum, Integer pageSize, String leave, String electCode, String memName) {
        Page<OrgPartyCongressCommittee> page = new Page<>(pageNum, pageSize);
        LambdaQueryWrapper<OrgPartyCongressCommittee> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(OrgPartyCongressCommittee::getElectCode, electCode);
        if (Boolean.TRUE.equals(StringToBooleanConverter.convert(leave))) {
            lambdaQueryWrapper.isNotNull(OrgPartyCongressCommittee::getd105Code);
        } else {
            lambdaQueryWrapper.isNull(OrgPartyCongressCommittee::getd105Code);
        }
        lambdaQueryWrapper.eq(OrgPartyCongressCommittee::getElectCode, electCode);
        lambdaQueryWrapper.isNull(OrgPartyCongressCommittee::getDeleteTime);
        lambdaQueryWrapper.orderByDesc(OrgPartyCongressCommittee::getId);
        memName = StrUtil.isNotBlank(memName) && EncryptProperties.enable ? SM4Untils.encryptContent(EncryptProperties.nginxKey, memName) : memName;
        lambdaQueryWrapper.eq(StrUtil.isNotEmpty(memName), OrgPartyCongressCommittee::getMemName, memName);
        page(page, lambdaQueryWrapper);
        List<OrgPartyCongressCommittee> records = page.getRecords();
        if (CollUtil.isNotEmpty(records)) {
            records.stream().filter(record -> StrUtil.equals(record.getMemTypeCode(), CommonConstant.ONE)
                    && StrUtil.isNotEmpty(record.getMemCode())).forEach(record -> {
                Mem mem = memService.findByCode(record.getMemCode(),"idcard");
                record.setMemIdcard(Objects.nonNull(mem) ? mem.getIdcard() : record.getMemIdcard());
            });
        }
        return new OutMessage<>(Status.SUCCESS, page);
    }

    @Override
    public OutMessage<Object> termination(OrgPartyCongressCommitteeTerminationDTO dto, String account) {
        OrgPartyCongressCommittee dbOrgPartyCongressCommittee = orgPartyCongressCommitteeMapper.selectById(dto.getId());
        if (Objects.isNull(dbOrgPartyCongressCommittee) || Objects.nonNull(dbOrgPartyCongressCommittee.getDeleteTime())) {
            return new OutMessage<>(Status.THE_PERSONNEL_OF_THE_CURRENT_SESSION_HAVE_BEEN_DELETED);
        }
        boolean betweenReportDate = iStatisticsYearService.isBetweenReportDate(dto.getEndDate());
        if (!betweenReportDate) {
            return new OutMessage<>(Status.NOT_IN_BETWEEN_REPORT_DATE).format("结束任期时间");
        }
        Map<String, String> d105Map = CacheUtils.getDic("dict_d105").stream().collect(Collectors.toMap(record -> record.get("key"), record1 -> record1.get("name"), (a, b) -> b, LinkedHashMap::new));
        final String d105Name = d105Map.get(dto.getD105Code().toString());

        OrgPartyCongressCommittee orgPartyCongressCommittee = new OrgPartyCongressCommittee();
        orgPartyCongressCommittee.setId(dbOrgPartyCongressCommittee.getId());
        orgPartyCongressCommittee.setEndDate(dto.getEndDate());
        orgPartyCongressCommittee.setd105Code(dto.getD105Code());
        orgPartyCongressCommittee.setd105Name(d105Name);
        orgPartyCongressCommittee.setUpdateTime(new Date());
        orgPartyCongressCommittee.setTimestamp(new Date());
        orgPartyCongressCommittee.setUpdateAccount(account);
        boolean flag = updateById(orgPartyCongressCommittee);
        if (flag) {
            orgPartyCongressCommitteeAllMapper.update(null, Wrappers.<OrgPartyCongressCommitteeAll>lambdaUpdate()
                    .set(OrgPartyCongressCommitteeAll::getEndDate, dto.getEndDate())
                    .set(OrgPartyCongressCommitteeAll::getd105Code, dto.getD105Code())
                    .set(OrgPartyCongressCommitteeAll::getd105Name, d105Name)
                    .set(OrgPartyCongressCommitteeAll::getUpdateTime, new Date())
                    .set(OrgPartyCongressCommitteeAll::getTimestamp, new Date())
                    .set(OrgPartyCongressCommitteeAll::getUpdateAccount, account)
                    .eq(OrgPartyCongressCommitteeAll::getCode, dbOrgPartyCongressCommittee.getCode()));
        }
        return new OutMessage<>(flag ? Status.SUCCESS : Status.FAIL);
    }

    @Override
    public OutMessage<Object> doMemList(OrgPartyCongressCommitteeListDTO dto, String account) {
        Integer pageNum = dto.getPageNum();
        Integer pageSize = dto.getPageSize();
        String subordinate = dto.getSubordinate();
        String orgCode = dto.getPositionOrgCode();
        List<String> typeList = dto.getType();
        Map<String, String> map = new HashMap<>();
        if (CommonConstant.ONE.equals(subordinate)) {
            List<Org> orgList = orgService.findAllSubOrgByOrgCode(orgCode);
            map = orgList.stream().filter(s -> StrUtil.isNotEmpty(s.getD01Code())).collect(Collectors.toMap(Org::getCode, Org::getD01Code, (v1, v2) -> v1));
        } else {
            Org byOrgCode = orgService.findByOrgCode(orgCode);
            map.put(byOrgCode.getCode(), byOrgCode.getD01Code());
        }
        Page<OrgPartyCongressCommittee> page = new Page<>(pageNum, pageSize);
        boolean isSubordinate = Boolean.TRUE.equals(StringToBooleanConverter.convert(subordinate));
        orgPartyCongressCommitteeMapper.sPage(page, typeList, isSubordinate, dto.getPositionOrgCode(), dto.getMemName(), dto.getSexCode(), dto.getD07Code(), dto.getD105Code(), dto.getD106Code(), dto.getMemTypeCode());
        if (CollectionUtil.isNotEmpty(page.getRecords())) {
            Map<String, String> mapValue = new HashMap<>();
            mapValue.put("12", "省党代表");
            mapValue.put("13", "市党代表");
            mapValue.put("14", "县党代表");
            mapValue.put("61", "乡镇党代表");
            for (OrgPartyCongressCommittee record : page.getRecords()) {
                String s = map.get(record.getOrgCode());
                if (StrUtil.isNotEmpty(s) && s.startsWith("12")) {
                    record.setParty(mapValue.get("12"));
                }
                if (StrUtil.isNotEmpty(s) && s.startsWith("13")) {
                    record.setParty(mapValue.get("13"));
                }
                if (StrUtil.isNotEmpty(s) && s.startsWith("14")) {
                    record.setParty(mapValue.get("14"));
                }
                if (StrUtil.isNotEmpty(s) && s.startsWith("61")) {
                    record.setParty(mapValue.get("61"));
                }
            }
        }
        return new OutMessage<>(Status.SUCCESS, page);
    }

    final List<String> sexList = new ArrayList<String>() {
        private static final long serialVersionUID = -8129643031492418720L;

        {
            add("男");
            add("女");
            add("未说明的性别");
        }
    };

    @Override
    public OutMessage<Object> getSexRatioTotal(ChartDataDTO dto) {
        List<Map<String, Object>> sexRatioTotal = orgPartyCongressCommitteeMapper.getSexRatioTotal(dto.getOrgCode());
        if (CollectionUtil.isEmpty(sexRatioTotal)) {
            return new OutMessage<>(Status.SUCCESS, ChartUtil.getListWithNoData("sexName", sexList));
        }
        List<Record> recordList = new ArrayList<>();
        for (Map<String, Object> stringMap : sexRatioTotal) {
            Record record = new Record();
            record.setColumns(stringMap);
            recordList.add(record);
        }
        // 获取总人数
        Long totalCount = ChartUtil.getTotalFromList(recordList, "count");
        if (totalCount == 0) {
            return new OutMessage<>(Status.SUCCESS, ChartUtil.getListWithNoData("sexName", sexList));
        }
        // 计算数据
        List<Record> recordListNew = ChartUtil.getPercentRecordList(recordList, "sexCode", "sexName", DictConstant.DICT_D63, totalCount);
        return new OutMessage<>(Status.SUCCESS, recordListNew);
    }

    List<String> educationList = new ArrayList<String>() {
        private static final long serialVersionUID = -1407594996094443623L;

        {
            add("研究生教育");
            add("本科教育");
            add("专科教育");
            add("中专");
            add("中技");
            add("高中");
            add("初中");
            add("小学");
            add("其他");
        }
    };

    @Override
    public OutMessage<Object> getEducationRatioTotal(ChartDataDTO dto) {

        // 获取比例
        String[] values = new String[]{"d07Name", "count"};
        OptionVO vo = orgPartyCongressCommitteeMapper.getEducationRatio(dto.getOrgCode());
        if (vo == null) {
            return new OutMessage<>(Status.SUCCESS, ChartUtil.getListWithNoData(values[0], sexList));
        }
        Record record = new Record();
        record.set("total", vo.getTotal());
        record.set("研究生教育", vo.getOne());
        record.set("本科教育", vo.getTwo());
        record.set("专科教育", vo.getThree());
        record.set("中专", vo.getFour());
        record.set("中技", vo.getFive());
        record.set("高中", vo.getSix());
        record.set("初中", vo.getSeven());
        record.set("小学", vo.getEight());
        record.set("其他", vo.getNine());
        Map<String, Object> map = record.getColumns();
        // 获取总人数
        Long totalCount = (Long) map.get("total");
        if (totalCount == null || totalCount == 0) {
            return new OutMessage<>(Status.SUCCESS, ChartUtil.getListWithNoData(values[0], sexList));
        }
        map.remove("total");
        List<Record> recordListData = com.zenith.front.core.kit.CollectionUtil.mapToListRecord(map, values[0], values[1]);
        if (cn.hutool.core.collection.CollectionUtil.isEmpty(recordListData)) {
            return new OutMessage<>(Status.PARA_ERROR);
        }
        // 计算百分比
        // List<Record> recordListNew = ChartUtil.getPercentRecordList(recordListData, totalCount);
        return new OutMessage<>(Status.SUCCESS, recordListData);
    }

    @Override
    public OutMessage<?> backOut(OrgPartyCongressCommitteeDTO data, String account) {
        data.setEndDate(null);
        data.setD105Code(null);
        data.setD105Name(null);
        return this.doUpdate(data, account);
    }

    @Override
    public OutMessage<Object> export(OrgPartyCongressCommitteeListDTO dto, String account) throws Exception {
        dto.setPageNum(1);
        dto.setPageSize(10000);
        Page<OrgPartyCongressCommittee> data = (Page<OrgPartyCongressCommittee>) this.doMemList(dto, account).getData();
        List<OrgPartyCongressCommitteeExcel> excelList = new ArrayList<>();
        if (CollectionUtil.isNotEmpty(data.getRecords())) {
            for (OrgPartyCongressCommittee record : data.getRecords()) {
                OrgPartyCongressCommitteeExcel excel = new OrgPartyCongressCommitteeExcel();
                BeanUtils.copyProperties(record, excel);
                excelList.add(excel);
            }
        }
        FileInputStream in = new FileInputStream(ResourceUtils.getURL(ResourceUtils.CLASSPATH_URL_PREFIX).getPath() + "public/excelftl/orgPartyCongressCommitteeExcel.xlsx");
        ExcelExportUtil<OrgPartyCongressCommitteeExcel> excelExportUtil = new ExcelExportUtil<>(OrgPartyCongressCommitteeExcel.class, 1, 0);
        File result = excelExportUtil.exportFileXlsx(in, excelList, cn.hutool.core.date.DateUtil.format(new Date(), "yyyyMMddHHmmssSSS") + ".xlsx");
        in.close();
        Map<String, String> map = new HashMap<>(5);
        map.put("url", "/public/export/" + result.getName());
        return new OutMessage<>(Status.SUCCESS, map);
    }

    @Override
    public List<OrgPartyCongressCommittee> findValidPartyRepresentative() {
        return orgPartyCongressCommitteeMapper.findValidPartyRepresentative();
    }

    @Override
    public List<OrgPartyCongressCommittee> findOrgPartyCongressCommitteeByElectCode(List<String> electCodeList) {
        return list(
                new LambdaQueryWrapper<OrgPartyCongressCommittee>()
                        .in(OrgPartyCongressCommittee::getElectCode, electCodeList)
                        .isNull(OrgPartyCongressCommittee::getDeleteTime)
        );
    }

    @Override
    public OutMessage<Object> historyList(String electCode) {
        LambdaQueryWrapper<OrgPartyCongressCommittee> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(OrgPartyCongressCommittee::getElectCode, electCode);
        lambdaQueryWrapper.isNotNull(OrgPartyCongressCommittee::getDeleteTime);
        lambdaQueryWrapper.orderByDesc(OrgPartyCongressCommittee::getDeleteTime);
        List<OrgPartyCongressCommittee> list = list(lambdaQueryWrapper);
        return new OutMessage<>(Status.SUCCESS,list);
    }

    @Override
    public OutMessage<Object> revoke(List<String> electCodes, String account) {
        LambdaQueryWrapper<OrgPartyCongressCommittee> wrapper = new LambdaQueryWrapper<OrgPartyCongressCommittee>()
                .in(OrgPartyCongressCommittee::getCode, electCodes);
        List<OrgPartyCongressCommittee> list = list(wrapper);
        if (CollUtil.isEmpty(list)) {
            return new OutMessage<>(Status.SUCCESS);
        }
        LambdaUpdateWrapper<OrgPartyCongressCommittee> updateWrapper = Wrappers.lambdaUpdate();
        updateWrapper.set(OrgPartyCongressCommittee::getDeleteTime,null)
                .set(OrgPartyCongressCommittee::getUpdateAccount,account)
                .set(OrgPartyCongressCommittee::getUpdateTime,new Date())
                .in(OrgPartyCongressCommittee::getCode,electCodes);
        boolean update = update(updateWrapper);
        if (update) {
            LambdaUpdateWrapper<OrgPartyCongressCommitteeAll> updateAllWrapper = Wrappers.lambdaUpdate();
            updateAllWrapper.set(OrgPartyCongressCommitteeAll::getDeleteTime,null)
                    .set(OrgPartyCongressCommitteeAll::getUpdateAccount,account)
                    .set(OrgPartyCongressCommitteeAll::getUpdateTime,new Date())
                    .in(OrgPartyCongressCommitteeAll::getCode,electCodes);
            orgPartyCongressCommitteeAllMapper.update(null,updateAllWrapper);
        }
        return new OutMessage<>(update ? Status.SUCCESS : Status.FAIL);
    }

    @Override
    public void exportPartyRepresentative(String orgCode) {
        List<OrgPartyCongressCommitteeVO> orgPartyCongressCommitteeVOList = this.baseMapper.exportPartyRepresentative(orgCode);

        List<Org> orgList = orgService.findAllSubOrgByOrgCode(orgCode);
        Map<String, String> map = orgList.stream().filter(s -> StrUtil.isNotEmpty(s.getD01Code())).collect(Collectors.toMap(Org::getCode, Org::getD01Code, (v1, v2) -> v1));

        List<LinkedHashMap<String, Object>> list = new ArrayList<>();
        List<ExcelExportEntity> beanList = new ArrayList<>();
        for (OrgPartyCongressCommitteeVO orgPartyCongressCommitteeVO : orgPartyCongressCommitteeVOList){
            LinkedHashMap<String, Object> linkedHashMap = new LinkedHashMap<>();
            Map<String, String> mapValue = new HashMap<>();
            mapValue.put("12", "省党代表");
            mapValue.put("13", "市党代表");
            mapValue.put("14", "县党代表");
            mapValue.put("61", "乡镇党代表");

            String s = map.get(orgPartyCongressCommitteeVO.getOrgCode());
            if (StrUtil.isNotEmpty(s) && s.startsWith("12")) {
                linkedHashMap.put("party",mapValue.get("12"));
            }
            if (StrUtil.isNotEmpty(s) && s.startsWith("13")) {
                linkedHashMap.put("party",mapValue.get("13"));
            }
            if (StrUtil.isNotEmpty(s) && s.startsWith("14")) {
                linkedHashMap.put("party",mapValue.get("14"));
            }
            if (StrUtil.isNotEmpty(s) && s.startsWith("61")) {
                linkedHashMap.put("party",mapValue.get("61"));
            }

            linkedHashMap.put("memName",orgPartyCongressCommitteeVO.getMemName());
            linkedHashMap.put("sexName",orgPartyCongressCommitteeVO.getSexName());
            linkedHashMap.put("memIdcard",orgPartyCongressCommitteeVO.getMemIdcard());
            linkedHashMap.put("d07Name",orgPartyCongressCommitteeVO.getD07Name());
            linkedHashMap.put("d106Name",orgPartyCongressCommitteeVO.getD106Name());
            linkedHashMap.put("orgName",orgPartyCongressCommitteeVO.getOrgName());

            list.add(linkedHashMap);
        }
        beanList.add(new ExcelExportEntity("人员姓名", "memName"));
        beanList.add(new ExcelExportEntity("性别", "sexName"));
        beanList.add(new ExcelExportEntity("身份证", "memIdcard"));
        beanList.add(new ExcelExportEntity("学历情况", "d07Name"));
        beanList.add(new ExcelExportEntity("人员身份", "d106Name"));
        beanList.add(new ExcelExportEntity("组织", "orgName"));
        beanList.add(new ExcelExportEntity("党代表类型", "party"));

        Workbook workbook = cn.afterturn.easypoi.excel.ExcelExportUtil.exportBigExcel(new ExportParams(null, ""), beanList, list);
        EasyPoiExcelUtil.response(response, workbook, "党代表");
    }
}
