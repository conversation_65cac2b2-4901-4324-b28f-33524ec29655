package com.zenith.front.core.analysis.count;

import cn.hutool.core.util.StrUtil;
import com.b1809.analysis.entity.PeggingPara;
import com.b1809.analysis.extend.ITableCount;
import com.b1809.analysis.plugin.elasticsearch.EsKit;
import com.zenith.front.core.analysis.ext.condition.UnitAllCondition;
import org.jooq.Condition;
import org.jooq.Record1;
import org.jooq.SelectConditionStep;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

import static com.zenith.front.common.constant.DBConstant.DSL_CONTEXT;
import static org.jooq.impl.DSL.*;

/**
 * 专题调查表三 推动发展壮大村级集体经济情况
 *
 * <AUTHOR>
 * @date 2021/11/17
 */
@Deprecated
@Component
public class Html48Count implements ITableCount {
    @Override
    public String getReportCode() {
        return "48.html";
    }

    @Override
    public Map<String, Map<String, Number>> doCount(String reportCode, String orgCode, String orgLevelCode) throws Exception {
        return getCount(orgCode, orgLevelCode, null);
    }

    public Map<String, Map<String, Number>> getCount(String orgCode, String orgLevelCode, String tableYear) throws Exception {
        String ccpOrgAll = StrUtil.isEmpty(tableYear) ? "ccp_org_all" : "ccp_org_all_" + tableYear;
        String ccpUnitAll = StrUtil.isEmpty(tableYear) ? "ccp_unit_all" : "ccp_unit_all_" + tableYear;
        Map<String, Number> result = new LinkedHashMap<>();
        Html53Count.initTableCol(1, 1, 1, 11, result);
        SelectConditionStep<Record1<Object>> records1 = DSL_CONTEXT.select(field("income_less_5w,income_50w_100w,income_above_100w,has_collective_economy,has_secretary_principal"))
                .from(table(name(ccpUnitAll)).as("ccp_unit_all")).where(new Html7Count().getUnitListCondition(orgCode, orgLevelCode)).and(field(name("d04_code"), String.class).eq("923"));
        List<com.jfinal.plugin.activerecord.Record> records = EsKit.findBySql(records1.toString()).toRecord();
        for (com.jfinal.plugin.activerecord.Record record : records) {
            //行政村总数
            Html53Count.setTableMapValue("1", "1", 1, result);
            //年经营性收入5万元以下薄弱村空壳村
            if (StrUtil.equals(record.getStr("income_less_5w"), "1")) {
                Html53Count.setTableMapValue("1", "2", 1, result);
            }
            //年经营性收入50-100万元的村
            if (StrUtil.equals(record.getStr("income_50w_100w"), "1")) {
                Html53Count.setTableMapValue("1", "3", 1, result);
            }
            //年经营性收入100万元以上的村
            if (StrUtil.equals(record.getStr("income_above_100w"), "1")) {
                Html53Count.setTableMapValue("1", "4", 1, result);
            }
            //有集体经济组织的村
            if (StrUtil.equals(record.getStr("has_collective_economy"), "1")) {
                Html53Count.setTableMapValue("1", "5", 1, result);
            }
            //村党组织书记担任村级集体经济组织负责人的村
            if (StrUtil.equals(record.getStr("has_secretary_principal"), "1")) {
                Html53Count.setTableMapValue("1", "6", 1, result);
            }
        }
        SelectConditionStep<Record1<Object>> orgSQL = DSL_CONTEXT.select(field("d01_code")).from(table(name(ccpOrgAll)).as("ccp_org_all")).where(new Html7Count().getOrgListCondition(orgCode, orgLevelCode)
                .and(field(name("d01_code")).like("12%").or(field(name("d01_code")).like("14%"))));
        List<com.jfinal.plugin.activerecord.Record> orgList = EsKit.findBySql(orgSQL.toString()).toRecord();
        //已消除集体经济薄弱村空壳村的县（市、区、旗）


        //超过 50%村没有集体经济收入的县（市、区、旗）


        //已编制消除集体经济薄弱村空壳村规划的省（区、市）


        //统一编制集体经济发展规划、逐村研究落实发展措施的县（市、区、旗）
        int count = Math.toIntExact(orgList.stream().filter(e -> StrUtil.startWith(e.getStr("d01_code"), "14")).count());
        Html53Count.setTableMapValue("1", "10", count, result);
        //将发展村级集体经济情况纳入县级领导班子和领导干部考核的县（市、区、旗）
        Html53Count.setTableMapValue("1", "11", count, result);

        Map<String, Map<String, Number>> mapMap = new HashMap<>();
        mapMap.put("table0", result);
        return mapMap;
    }

    @Override
    public Map<String, Object> doPegging(PeggingPara peggingPara) throws Exception {
        Condition condition = new Html7Count().getUnitListCondition(peggingPara.getOrgCode(), peggingPara.getOrgLevelCode())
                .and(field(name("d04_code"), String.class).eq("923")).and(this.getRowCondition(peggingPara.getRowIndex()));

        UnitAllCondition unitAllCondition = new UnitAllCondition();
        return Html53Count.getReportPageResult(peggingPara, unitAllCondition.getTableName(), condition, unitAllCondition.getLevelCodeField());
    }

    /**
     * 反查条件
     *
     * @param rowIndex 序号
     * @return 查询条件
     */
    Condition getRowCondition(String rowIndex) {
        Condition condition = noCondition();
        if ("1".equals(rowIndex)) {
        } else if ("2".equals(rowIndex)) {
            condition = condition.and(field(name("income_less_5w"), String.class).eq("1"));
        } else if ("3".equals(rowIndex)) {
            condition = condition.and(field(name("income_50w_100w"), String.class).eq("1"));
        } else if ("4".equals(rowIndex)) {
            condition = condition.and(field(name("income_above_100w"), String.class).eq("1"));
        } else if ("5".equals(rowIndex)) {
            condition = condition.and(field(name("has_collective_economy"), String.class).eq("1"));
        } else if ("6".equals(rowIndex)) {
            condition = condition.and(field(name("has_secretary_principal"), String.class).eq("1"));
        } else {
            condition = condition.and("1=0");
        }
        return condition;
    }

    @Override
    public Map<String, Number> doReplenishCount(String reportCode, String orgCode, String orgLevelCode) throws Exception {
        return null;
    }

    @Override
    public Map<String, Object> doReplenishPegging(PeggingPara peggingPara) throws Exception {
        return null;
    }
}
