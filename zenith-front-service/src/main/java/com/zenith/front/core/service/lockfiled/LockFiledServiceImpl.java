package com.zenith.front.core.service.lockfiled;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.zenith.front.api.lockfiled.ILockFiledLogService;
import com.zenith.front.api.lockfiled.ILockFiledService;
import com.zenith.front.api.mem.IMemService;
import com.zenith.front.api.org.IOrgAllService;
import com.zenith.front.api.org.IOrgService;
import com.zenith.front.api.unit.IUnitAllService;
import com.zenith.front.api.unit.IUnitService;
import com.zenith.front.common.constant.CommonConstant;
import com.zenith.front.common.constant.PermissionConstant;
import com.zenith.front.common.encrypt.properties.EncryptProperties;
import com.zenith.front.common.kit.StrKit;
import com.zenith.front.common.kit.StringToBooleanConverter;
import com.zenith.front.common.untils.SM4Untils;
import com.zenith.front.core.cache.CacheUtils;
import com.zenith.front.dao.mapper.lockfiled.LockFiledMapper;
import com.zenith.front.model.dto.BatchLockDTO;
import com.zenith.front.model.dto.LockDTO;
import com.zenith.front.model.dto.LockFiledDTO;
import com.zenith.front.model.message.OutMessage;
import com.zenith.front.model.message.Status;

import com.zenith.front.model.bean.*;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;

/**
 * <p>
 * 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-03-08
 */
@Service
public class LockFiledServiceImpl extends ServiceImpl<LockFiledMapper, LockFiled> implements ILockFiledService {

    @Resource
    private IMemService memService;
    @Resource
    private IUnitService unitService;
    @Resource
    private IUnitAllService unitAllService;
    @Resource
    private IOrgService orgService;
    @Resource
    private IOrgAllService orgAllService;
    @Resource
    private ILockFiledLogService lockFiledLogService;

    /**
     * 根据锁定对象查询锁定信息
     *
     * @param lockObject 锁定对象 1 党员 2 单位 3 组织
     * @return
     */
    private LockFiled getLockFiled(String lockObject) {
        return getOne(
                new LambdaQueryWrapper<LockFiled>()
                        .eq(LockFiled::getLockObject, lockObject)
        );
    }

    /**
     * 查询所有锁定对象信息
     *
     * @return
     */
    private List<LockFiled> getLockFiled() {
        return list(
                new LambdaQueryWrapper<LockFiled>()
                        //按照锁定对象排序
                        .orderByAsc(LockFiled::getLockObject)
                        .select(LockFiled::getLockObject, LockFiled::getFixedField, LockFiled::getField)
        );
    }

    @Override
    public OutMessage<?> saveLockFiled(LockFiledDTO lockFiledDTO, String account) {
        //锁定对象 1 党员 2 单位 3 组织
        String lockObject = lockFiledDTO.getLockObject().toString();
        if (!Arrays.asList(CommonConstant.ONE, CommonConstant.TWO, CommonConstant.THREE).contains(lockObject)) {
            throw new IllegalStateException("Unexpected value: " + lockObject);
        }
        LockFiled dbLockFiled = this.getLockFiled(lockObject);
        if (Objects.isNull(dbLockFiled)) {
            return new OutMessage<>(Status.LOCK_INFO_ITEM_NOT_CONFIGURED);
        }
        final Long lockFiledId = dbLockFiled.getId();
        Object fixedField = dbLockFiled.getFixedField();
        List<String> filedList = lockFiledDTO.getFiledList();
        LockFiled filed = new LockFiled();
        filed.setId(lockFiledId);
        filed.setLockObject(lockObject);
        filed.setField(filedList);
        filed.setUpdateAccount(account);
        filed.setUpdateTime(new Date());
        boolean flag = updateById(filed);
        if (flag) {
            if (Objects.nonNull(fixedField)) {
                filedList.addAll(Convert.toList(String.class, fixedField));
            }
            switch (lockObject) {
                case CommonConstant.ONE:
                    memService.updateLockFields(filedList);
                    break;
                case CommonConstant.TWO:
                    unitService.updateLockFields(filedList);
                    unitAllService.updateLockFields(filedList);
                    break;
                case CommonConstant.THREE:
                    orgService.updateLockFields(filedList);
                    orgAllService.updateLockFields(filedList);
                    break;
                default:

            }
        }
        return new OutMessage<>(flag ? Status.SUCCESS : Status.FAIL);
    }

    @Override
    public OutMessage<?> getLockFiledList() {
        List<LockFiled> list = this.getLockFiled();
        return new OutMessage<>(Status.SUCCESS, list);
    }

    @Override
    public OutMessage<?> lockFiled(LockDTO lockDTO, String orgId, String account, String memCode) {
        final String memName = CacheUtils.getMemName(memCode);
        //1 党员 2 单位 3 组织
        final String lockObject = lockDTO.getLockObject().toString();
        LockFiled lockFiled = this.getLockFiled(lockObject);
        if (Objects.isNull(lockFiled)) {
            return new OutMessage<>(Status.LOCK_INFO_ITEM_NOT_CONFIGURED);
        }
        final String code = lockDTO.getCode();
        Object fixedField = lockFiled.getFixedField();
        Object field = lockFiled.getField();
        List<String> fieldList = Convert.toList(String.class, field);
        if (Objects.nonNull(fixedField)) {
            fieldList.addAll(Convert.toList(String.class, fixedField));
        }
        if (CollUtil.isEmpty(fieldList)) {
            return new OutMessage<>(Status.LOCK_INFO_ITEM_NOT_CONFIGURED);
        }
        Object obj = Convert.convert(Object.class, fieldList);
        if (StrUtil.equals(lockObject, CommonConstant.ONE)) {
            Mem dbMem = memService.findByCode(code);
            if (Objects.isNull(dbMem)) {
                return new OutMessage<>(Status.MEM_IS_ERROR);
            }
            String name = dbMem.getName();
            Object lockFields = dbMem.getLockFields();
            List<String> toList = Convert.toList(String.class, lockFields);
            if (CollUtil.isNotEmpty(toList)) {
                return new OutMessage<>(Status.MEM_HAVE_BEEN_LOCKED);
            }
            Mem mem = new Mem();
            mem.setId(dbMem.getId());
            mem.setLockFields(obj);
            boolean flag = memService.updateById(mem);
            //记录日志
            if (flag) {
                lockFiledLogService.saveLog(null, CommonConstant.THREE, lockObject, code, dbMem.getMemOrgCode(), orgId, account, memName, null, name, CommonConstant.MINUS_ZERO);
            }
            return new OutMessage<>(flag ? Status.SUCCESS : Status.FAIL);
        }
        if (StrUtil.equals(lockObject, CommonConstant.TWO)) {
            Unit dbUnit = unitService.findByCode(code);
            if (Objects.isNull(dbUnit)) {
                return new OutMessage<>(Status.UNIT_IS_NULL);
            }
            Object lockFields = dbUnit.getLockFields();
            List<String> toList = Convert.toList(String.class, lockFields);
            if (CollUtil.isNotEmpty(toList)) {
                return new OutMessage<>(Status.UNIT_HAVE_BEEN_LOCKED);
            }
            Unit unit = new Unit();
            unit.setId(dbUnit.getId());
            unit.setLockFields(obj);
            boolean flag = unitService.updateById(unit);
            if (flag) {
                UnitAll dbUnitAll = unitAllService.findByCode(code);
                if (Objects.nonNull(dbUnitAll)) {
                    UnitAll unitAll = new UnitAll();
                    unitAll.setId(dbUnitAll.getId());
                    unitAll.setLockFields(obj);
                    unitAllService.updateById(unitAll);
                }
                //记录日志
                lockFiledLogService.saveLog(null, CommonConstant.THREE, lockObject, code, dbUnit.getMainUnitOrgCode(), orgId, account, memName, null, dbUnit.getName(), CommonConstant.MINUS_ZERO);
            }
            return new OutMessage<>(flag ? Status.SUCCESS : Status.FAIL);
        }
        if (StrUtil.equals(lockObject, CommonConstant.THREE)) {
            Org dbOrg = orgService.findOrgByCode(code);
            if (Objects.isNull(dbOrg)) {
                return new OutMessage<>(Status.ORG_NOT_EXIST);
            }
            Object lockFields = dbOrg.getLockFields();
            List<String> toList = Convert.toList(String.class, lockFields);
            if (CollUtil.isNotEmpty(toList)) {
                return new OutMessage<>(Status.ORG_HAVE_BEEN_LOCKED);
            }
            Org org = new Org();
            org.setId(dbOrg.getId());
            org.setLockFields(obj);
            boolean flag = orgService.updateById(org);
            if (flag) {
                OrgAll dbOrgAll = orgAllService.findByCode(code);
                if (Objects.nonNull(dbOrgAll)) {
                    OrgAll orgAll = new OrgAll();
                    orgAll.setId(dbOrgAll.getId());
                    orgAll.setLockFields(obj);
                    orgAllService.updateById(orgAll);
                }
                //记录日志
                lockFiledLogService.saveLog(null, CommonConstant.THREE, lockObject, code, dbOrg.getOrgCode(), orgId, account, memName, null, dbOrg.getName(), CommonConstant.MINUS_ZERO);
            }
            return new OutMessage<>(flag ? Status.SUCCESS : Status.FAIL);
        }
        return new OutMessage<>(Status.FAIL);
    }

    @Override
    public OutMessage<?> unlockFiled(LockDTO lockDTO, String orgId, String currPermission, String account, String memCode) {
        final String memName = CacheUtils.getMemName(memCode);
        Org org = orgService.findOrgByCode(orgId);
        if (Objects.isNull(org)) {
            return new OutMessage<>(Status.ORG_NOT_EXIST);
        }
        final String d01Code = org.getD01Code();
        final String lockObject = lockDTO.getLockObject().toString();
        final String code = lockDTO.getCode();
        final String unlockReason = lockDTO.getUnlockReason();
        String uqCode = lockDTO.getUqCode();
        //是否有权限,注解配置从1开始,String.charAt()方法 返回指定索引处的char值。索引范围 是从0到length() - 1。
        String isPermission = String.valueOf(currPermission.charAt(73 - 1));
        if (StrUtil.equals(lockObject, CommonConstant.ONE)) {
            Mem dbMem = memService.findByCode(code);
            if (Objects.isNull(dbMem)) {
                return new OutMessage<>(Status.MEM_IS_ERROR);
            }
            String name = dbMem.getName();
            if (this.message(orgId, account, memName, d01Code, lockObject, code, uqCode, isPermission, dbMem.getMemOrgCode(), unlockReason, name)) {
                return new OutMessage<>(Status.INFO_UNLOCKING_APPLICATION_HAS_BEEN_INITIATED_TO_THE_SUPERIOR);
            }
            Object lockFields = dbMem.getLockFields();
            List<String> toList = Convert.toList(String.class, lockFields);
            if (CollUtil.isEmpty(toList)) {
                //记录日志
                lockFiledLogService.saveLog(uqCode, CommonConstant.TWO, lockObject, code, dbMem.getMemOrgCode(), orgId, account, memName, unlockReason, name, CommonConstant.ONE);
                return new OutMessage<>(Status.SUCCESS);
            }
            boolean flag = memService.update(
                    Wrappers.<Mem>lambdaUpdate().eq(Mem::getId, dbMem.getId()).isNull(Mem::getDeleteTime).set(Mem::getLockFields, null).set(Mem::getUpdateAccount, account)
            );
            if (flag) {
                //记录日志
                lockFiledLogService.saveLog(uqCode, CommonConstant.TWO, lockObject, code, dbMem.getMemOrgCode(), orgId, account, memName, unlockReason, name, CommonConstant.ONE);
            }
            return new OutMessage<>(flag ? Status.SUCCESS : Status.FAIL);
        }
        if (StrUtil.equals(lockObject, CommonConstant.TWO)) {
            Unit dbUnit = unitService.findByCode(code);
            if (Objects.isNull(dbUnit)) {
                return new OutMessage<>(Status.UNIT_IS_NULL);
            }
            String unitName = dbUnit.getName();
            if (this.message(orgId, account, memName, d01Code, lockObject, code, uqCode, isPermission, dbUnit.getMainUnitOrgCode(), unlockReason, unitName)) {
                return new OutMessage<>(Status.INFO_UNLOCKING_APPLICATION_HAS_BEEN_INITIATED_TO_THE_SUPERIOR);
            }
            Object lockFields = dbUnit.getLockFields();
            List<String> toList = Convert.toList(String.class, lockFields);
            if (CollUtil.isEmpty(toList)) {
                //记录日志
                lockFiledLogService.saveLog(uqCode, CommonConstant.TWO, lockObject, code, dbUnit.getMainUnitOrgCode(), orgId, account, memName, unlockReason, unitName, CommonConstant.ONE);
                return new OutMessage<>(Status.SUCCESS);
            }
            boolean flag = unitService.update(
                    Wrappers.<Unit>lambdaUpdate().eq(Unit::getId, dbUnit.getId()).isNull(Unit::getDeleteTime).set(Unit::getLockFields, null).set(Unit::getUpdateAccount, account)
            );
            if (flag) {
                unitAllService.update(
                        Wrappers.<UnitAll>lambdaUpdate().eq(UnitAll::getCode, dbUnit.getCode()).isNull(UnitAll::getDeleteTime).set(UnitAll::getLockFields, null).set(UnitAll::getUpdateAccount, account)
                );
                //记录日志
                lockFiledLogService.saveLog(uqCode, CommonConstant.TWO, lockObject, code, dbUnit.getMainUnitOrgCode(), orgId, account, memName, unlockReason, unitName, CommonConstant.ONE);
            }
            return new OutMessage<>(flag ? Status.SUCCESS : Status.FAIL);
        }
        if (StrUtil.equals(lockObject, CommonConstant.THREE)) {
            Org dbOrg = orgService.findOrgByCode(code);
            if (Objects.isNull(dbOrg)) {
                return new OutMessage<>(Status.ORG_NOT_EXIST);
            }
            String orgName = dbOrg.getName();
            if (this.message(orgId, account, memName, d01Code, lockObject, code, uqCode, isPermission, dbOrg.getOrgCode(), unlockReason, orgName)) {
                return new OutMessage<>(Status.INFO_UNLOCKING_APPLICATION_HAS_BEEN_INITIATED_TO_THE_SUPERIOR);
            }
            Object lockFields = dbOrg.getLockFields();
            List<String> toList = Convert.toList(String.class, lockFields);
            if (CollUtil.isEmpty(toList)) {
                //记录日志
                lockFiledLogService.saveLog(uqCode, CommonConstant.TWO, lockObject, code, dbOrg.getOrgCode(), orgId, account, memName, unlockReason, orgName, CommonConstant.ONE);
                return new OutMessage<>(Status.SUCCESS);
            }
            boolean flag = orgService.update(
                    Wrappers.<Org>lambdaUpdate().eq(Org::getId, dbOrg.getId()).isNull(Org::getDeleteTime).set(Org::getLockFields, null).set(Org::getUpdateAccount, account)
            );
            if (flag) {
                orgAllService.update(
                        Wrappers.<OrgAll>lambdaUpdate().eq(OrgAll::getCode, dbOrg.getCode()).isNull(OrgAll::getDeleteTime).set(OrgAll::getLockFields, null)
                );
                //记录日志
                lockFiledLogService.saveLog(uqCode, CommonConstant.TWO, lockObject, code, dbOrg.getOrgCode(), orgId, account, memName, unlockReason, orgName, CommonConstant.ONE);
            }
            return new OutMessage<>(flag ? Status.SUCCESS : Status.FAIL);
        }
        return new OutMessage<>(Status.FAIL);
    }

    /**
     * 向上级申请和无权限处理
     *
     * @param orgId        组织code
     * @param account      操作账号
     * @param memName      操作用户
     * @param d01Code      组织类型
     * @param lockObject   解锁对象 1 党员 2 单位 3 组织
     * @param code         党员 单位 组织 code
     * @param uqCode       日志记录 code
     * @param isPermission 权限字符串
     * @param memOrgCode   操作用户所在组织code
     * @return
     */
    private boolean message(String orgId, String account, String memName, String d01Code, String lockObject, String code, String uqCode, String isPermission, String memOrgCode, String unLockReason, String name) {
        if (StrUtil.equalsAny(d01Code, "631", "632", "634", "931", "932")) {
            //记录日志
            lockFiledLogService.saveLog(uqCode, CommonConstant.ONE, lockObject, code, memOrgCode, orgId, account, memName, unLockReason, name, null);
            return true;
        }
        if (PermissionConstant.NO_ACCESS.equals(isPermission)) {
            //记录日志
            lockFiledLogService.saveLog(uqCode, CommonConstant.ONE, lockObject, code, memOrgCode, orgId, account, memName, unLockReason, name, null);
            return true;
        }
        return false;
    }

    @Override
    public OutMessage<?> batchLockFiled(BatchLockDTO batchLockDTO, String orgId, String account, String memCode) {
        LockFiled lockFiled = this.getLockFiled(CommonConstant.ONE);
        if (Objects.isNull(lockFiled)) {
            return new OutMessage<>(Status.LOCK_INFO_ITEM_NOT_CONFIGURED);
        }
        Object fixedField = lockFiled.getFixedField();
        Object field = lockFiled.getField();
        List<String> fieldList = Convert.toList(String.class, field);
        if (Objects.nonNull(fixedField)) {
            fieldList.addAll(Convert.toList(String.class, fixedField));
        }
        Object obj = Convert.convert(Object.class, fieldList);
        final List<String> memCodeList = batchLockDTO.getMemCode();
        if (CollUtil.isEmpty(memCodeList)) {
            return new OutMessage<>(Status.MEM_IS_ERROR);
        }
        List<Mem> dbMemList = memService.findMemByCodes(memCodeList);
        if (Objects.isNull(dbMemList)) {
            return new OutMessage<>(Status.MEM_IS_ERROR);
        }
        String memName = CacheUtils.getMemName(memCode);
        List<Mem> memList = new ArrayList<>();
        List<LockFiledLog> lockFiledLogList = new ArrayList<>();
        dbMemList.forEach(t -> {
            Mem mem = new Mem();
            mem.setId(t.getId());
            mem.setLockFields(obj);
            memList.add(mem);
            LockFiledLog lockFiledLog = generateLockFiledLog(orgId, account, memName, t);
            lockFiledLogList.add(lockFiledLog);
        });
        boolean flag = memService.updateBatchById(memList);
        if (flag) {
            //记录日志
            lockFiledLogService.saveBatchLog(lockFiledLogList);
        }
        return new OutMessage<>(flag ? Status.SUCCESS : Status.FAIL);
    }

    /**
     * 生成日志实体类集合
     *
     * @param orgId   操作用户所在组织code
     * @param account 操作账号名称
     * @param memName 账号相关人员
     * @param mem     党员信息
     */
    private LockFiledLog generateLockFiledLog(String orgId, String account, String memName, Mem mem) {
        LockFiledLog lockFiledLog = new LockFiledLog();
        lockFiledLog.setCode(StrKit.getRandomUUID());
        lockFiledLog.setType(CommonConstant.THREE);
        lockFiledLog.setUnlockObject(CommonConstant.ONE);
        lockFiledLog.setUnlockCode(mem.getCode());
        lockFiledLog.setUnlockName(mem.getName());
        lockFiledLog.setLevelCode(mem.getMemOrgCode());
        lockFiledLog.setMemOrgCode(orgId);
        lockFiledLog.setState(CommonConstant.MINUS_ZERO);
        lockFiledLog.setMemAccount(account);
        lockFiledLog.setAccountNumberRelatedPerson(memName);
        lockFiledLog.setCreateTime(new Date());
        return lockFiledLog;
    }

    @Override
    public OutMessage<?> unlocked(LockDTO lockDTO) {
        String orgCode = lockDTO.getOrgCode();
        //查询组织信息
        Org org = orgService.findOrgByOrgId(orgCode);
        if (Objects.isNull(org)) {
            return new OutMessage<>(Status.ORG_NOT_EXIST);
        }
        String orgOrgCode = org.getOrgCode();
        String subordinate = lockDTO.getSubordinate();
        Boolean convert = StringToBooleanConverter.convert(subordinate);
        boolean hasSubordinate = Boolean.TRUE.equals(convert);
        //查询未锁定党员
        List<Mem> memList = memService.findLockFieldsIsNull(orgOrgCode, hasSubordinate);
        List<LinkedHashMap<String, String>> result = new ArrayList<>();
        for (Mem mem : memList) {
            StringBuilder builder = new StringBuilder();
            if (StrUtil.isNotBlank(mem.getName())) {
                builder.append(mem.getName());
            }
            boolean flag = StrUtil.isNotBlank(mem.getSexName()) || Objects.nonNull(mem.getBirthday());
            if (flag) {
                builder.append("(");
            }
            if (StrUtil.isNotBlank(mem.getSexName())) {
                builder.append("性别:");
                builder.append(mem.getSexName());
                builder.append(",");
            }
            if (Objects.nonNull(mem.getBirthday())) {
                builder.append("出生日期:");
                builder.append(DateUtil.format(mem.getBirthday(), "yyyy-MM-dd"));
                builder.append(",");
            }
            if (StrUtil.endWith(builder, ",")) {
                builder.deleteCharAt(builder.length() - 1);
            }
            if (flag) {
                builder.append(")");
            }
            LinkedHashMap<String, String> map = new LinkedHashMap<>();
            map.put("code", mem.getCode());
            map.put("value", builder.toString());
            result.add(map);
        }
        return new OutMessage<>(Status.SUCCESS, result);
    }

    @Override
    public OutMessage<?> locked(LockDTO lockDTO) {
        final Integer pageNum = lockDTO.getPageNum();
        final Integer pageSize = lockDTO.getPageSize();
        final Integer locked = lockDTO.getLocked();
        final Boolean hasLocked = StringToBooleanConverter.convert(locked.toString());
        final String lockObject = lockDTO.getLockObject().toString();
        String keyword = lockDTO.getKeyword();
        final String subordinate = lockDTO.getSubordinate();
        final Boolean hasSubordinate = StringToBooleanConverter.convert(subordinate);
        final String memCode = lockDTO.getMemCode();
        if (StrUtil.isNotEmpty(keyword) && !StrUtil.equals(lockObject, CommonConstant.TWO)) {
            keyword = SM4Untils.encryptContent(EncryptProperties.nginxKey, keyword);
        }
        //1 党员
        if (StrUtil.equals(lockObject, CommonConstant.ONE)) {
            Page<Mem> page = memService.findPageByCondition(pageNum, pageSize, hasLocked, hasSubordinate, memCode, keyword);
            return new OutMessage<>(Status.SUCCESS, page);
        }
        //2 单位
        if (StrUtil.equals(lockObject, CommonConstant.TWO)) {
            Page<Unit> page = unitService.findPageByCondition(pageNum, pageSize, hasLocked, hasSubordinate, memCode, memCode, keyword);
            return new OutMessage<>(Status.SUCCESS, page);
        }
        //3 组织
        if (StrUtil.equals(lockObject, CommonConstant.THREE)) {
            Page<Org> page = orgService.findPageByCondition(pageNum, pageSize, hasLocked, hasSubordinate, memCode, keyword);
            return new OutMessage<>(Status.SUCCESS, page);
        }
        return new OutMessage<>(Status.SUCCESS, new Page<>());
    }
}
