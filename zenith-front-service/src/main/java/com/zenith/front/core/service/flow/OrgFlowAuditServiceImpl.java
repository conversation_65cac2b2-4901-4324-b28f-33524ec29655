package com.zenith.front.core.service.flow;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.thread.ThreadUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.zenith.front.api.flow.FlowExchangeService;
import com.zenith.front.api.flow.IOrgFlowAuditService;
import com.zenith.front.api.flow.IOrgFlowService;
import com.zenith.front.api.org.IOrgService;
import com.zenith.front.api.user.IUserService;
import com.zenith.front.core.cache.CacheUtils;
import com.zenith.front.dao.mapper.flow.OrgFlowAuditMapper;
import com.zenith.front.dao.mapper.flow.OrgFlowMapper;
import com.zenith.front.model.bean.Org;
import com.zenith.front.model.bean.OrgFlow;
import com.zenith.front.model.bean.OrgFlowAudit;
import com.zenith.front.model.bean.User;
import com.zenith.front.model.constant.UserConstant;
import com.zenith.front.model.custom.Record;
import com.zenith.front.model.custom.UserTicket;
import com.zenith.front.model.dto.flow.*;
import com.zenith.front.model.message.OutMessage;
import com.zenith.front.model.message.Status;
import com.zenith.front.model.vo.OrgFlowVo;
import jodd.util.StringUtil;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;

/**
 * <p>
 * 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-07-10
 */
@Service
public class OrgFlowAuditServiceImpl extends ServiceImpl<OrgFlowAuditMapper, OrgFlowAudit> implements IOrgFlowAuditService {

    @Resource
    private OrgFlowMapper orgFlowMapper;

    @Resource
    private IOrgFlowService orgFlowService;

    @Resource
    private IOrgService orgService;

    @Resource
    private FlowExchangeService flowExchangeService;

    @Resource
    private IUserService userService;

    @Override
    public OutMessage<?> getOrgFlowList(OrgFlowListAuditDto dto) {
        Page<OrgFlowVo> page = new Page<>(dto.getPageNum(), dto.getPageSize());
        UserTicket userTicket = UserConstant.USER_CONTEXT.get();
        if (ObjectUtil.isNull(userTicket)) {
            return new OutMessage<>(Status.SUCCESS, page);
        }
        String orgCode = userTicket.getUser().getOrgCode();
        dto.setOrgCode(orgCode);
        Org org = orgService.findByOrgCode(orgCode);
        if (ObjectUtil.isNull(org)) {
            return new OutMessage<>(Status.OBJEC_NOT_EXIST);
        }
        String d01Code = org.getD01Code();
        if (!StrUtil.startWith(d01Code, "1")) {
            return new OutMessage<>(Status.SUCCESS, page);
        }
        List<Record> dictD01List = CacheUtils.getDic("dict_d01");
        Map<String, String> dictD01Map = com.zenith.front.core.kit.CollectionUtil.listRecordToMap(dictD01List, "key", "name");
        List<Record> dictD200List = CacheUtils.getDic("dict_d200");
        Map<String, String> dictD200Map = com.zenith.front.core.kit.CollectionUtil.listRecordToMap(dictD200List, "key", "name");
        List<Record> dictD201List = CacheUtils.getDic("dict_d201");
        Map<String, String> dictD201Map = com.zenith.front.core.kit.CollectionUtil.listRecordToMap(dictD201List, "key", "name");
        List<Record> dictD202List = CacheUtils.getDic("dict_d202");
        Map<String, String> dictD202Map = com.zenith.front.core.kit.CollectionUtil.listRecordToMap(dictD202List, "key", "name");

        Page<OrgFlowVo> orgFlowPage = this.orgFlowMapper.selectOrgFlowPage(page, dto);
        IPage<OrgFlowVo> orgFlowVoIPage = orgFlowPage.convert(orgFlowVo -> {
            Integer isEnable = orgFlowVo.getIsEnable();
            if (ObjectUtil.isNotNull(isEnable)) {
                orgFlowVo.setIsEnableName(isEnable == 0 ? "无效" : "有效");
            }
            Integer parentFlowType = orgFlowVo.getParentFlowType();
            if (ObjectUtil.isNotNull(parentFlowType)) {
                orgFlowVo.setParentFlowTypeName(parentFlowType == 1 ? "普通党组织" : "流动党组织");
            }
            orgFlowVo.setD01Name(dictD01Map.get(orgFlowVo.getD01Code()));
            orgFlowVo.setD200Name(dictD200Map.get(orgFlowVo.getD200Code()));
            orgFlowVo.setD201Name(dictD201Map.get(orgFlowVo.getD201Code()));
            Integer isExchange = orgFlowVo.getIsExchange();
            if (ObjectUtil.isNotNull(isExchange)) {
                orgFlowVo.setIsExchangeName(isExchange == 1 ? "是" : "否");
            }
            String status = orgFlowVo.getStatus();
            if (StrUtil.isNotEmpty(status)) {
                orgFlowVo.setStatusName(dictD202Map.get(status));
            }
            if (StrUtil.isNotEmpty(orgFlowVo.getAuditUserId())) {
                User auditUser = this.userService.findById(orgFlowVo.getAuditUserId());
                if (ObjectUtil.isNotNull(auditUser)) {
                    Org auditOrg = this.orgService.findOrgByOrgCode(auditUser.getOrgCode());
                    if (ObjectUtil.isNotNull(auditOrg)) {
                        orgFlowVo.setAuditOrgCode(auditOrg.getOrgCode());
                        orgFlowVo.setAuditOrgName(auditOrg.getName());
                    }
                }
            }
            return orgFlowVo;
        });
        return new OutMessage<>(Status.SUCCESS, orgFlowVoIPage);
    }

    @Override
    @Transactional
    public OutMessage<?> saveOrgFlowAudit(OrgFlowListAuditDto dto) {
        String flowOrgCode = dto.getFlowOrgCode();
        if (StrUtil.isEmpty(flowOrgCode)) {
            return new OutMessage<>(Status.PARA_ERROR);
        }
        OrgFlowAudit orgFlowAudit = selectOrgFlowAuditByFlowOrgCode(flowOrgCode);
        if (ObjectUtil.isNull(orgFlowAudit)) {
            return new OutMessage<>(Status.OBJEC_NOT_EXIST);
        }
        OrgFlow orgFlow = this.orgFlowMapper.getOrgFlowByCode(flowOrgCode);
        if (ObjectUtil.isNull(orgFlow)) {
            return new OutMessage<>(Status.OBJEC_NOT_EXIST);
        }
        orgFlowAudit.setStatus(dto.getStatus());
        orgFlowAudit.setReason(StrUtil.equals(dto.getStatus(), "1") ? StrUtil.EMPTY : dto.getReason());
        orgFlowAudit.setAuditTime(DateUtil.date());
        UserTicket userTicket = UserConstant.USER_CONTEXT.get();
        if (ObjectUtil.isNotNull(userTicket)) {
            User user = userTicket.getUser();
            orgFlowAudit.setAuditUserId(user.getId());
            orgFlowAudit.setAuditUserName(user.getName());
        }
        if (StrUtil.equals(dto.getStatus(), "1")) {
            this.orgFlowMapper.updateIsEnableById(orgFlow.getId(), 1);
        } else if (StrUtil.equals(dto.getStatus(), "2")) {
            this.orgFlowMapper.updateIsEnableById(orgFlow.getId(), 0);
        }
        this.baseMapper.updateById(orgFlowAudit);

        Integer sourceType = orgFlow.getSourceType();
        if (ObjectUtil.isNotNull(sourceType) && sourceType == 2) {
            SyncCheckOtherDTO syncCheckOtherDTO = new SyncCheckOtherDTO();
            syncCheckOtherDTO.setCode(orgFlow.getCode());
            syncCheckOtherDTO.setStatus(StringUtil.equals(dto.getStatus(), "1") ? "4" : "3");
            syncCheckOtherDTO.setBtgyy(dto.getReason());
            if (ObjectUtil.isNotNull(userTicket) && ObjectUtil.isNotNull(userTicket.getUser())) {
                String auditOrgCode = userTicket.getUser().getOrgCode();
                Org auditOrg = this.orgService.findByOrgCode(auditOrgCode);
                if (ObjectUtil.isNull(auditOrg)) {
                    return new OutMessage<>(Status.OBJEC_NOT_EXIST);
                }
//                syncCheckOtherDTO.setSpOrgId(String.valueOf(auditOrg.getId()));
                syncCheckOtherDTO.setSpOrgId(auditOrg.getOrgCode());
                syncCheckOtherDTO.setSpOrgMc(auditOrg.getName());
            }
            ThreadUtil.execute(() -> {
                this.flowExchangeService.pushFlow4Exchange(syncCheckOtherDTO);
            });
        }

        OrgFlowSyncAll orgFlowSyncAll = new OrgFlowSyncAll();
        orgFlowSyncAll.setOrgFlowType("4");
        OrgFlowSync orgFlowSync = new OrgFlowSync();
        BeanUtil.copyProperties(orgFlow, orgFlowSync);
        if (StrUtil.equals(dto.getStatus(), "1")) {
            orgFlowSync.setIsEnable(1);
        } else if (StrUtil.equals(dto.getStatus(), "2")) {
            orgFlowSync.setIsEnable(0);
        }
        orgFlowSyncAll.setOrgFlow(orgFlowSync);

        OrgFlowAuditSync orgFlowAuditSync = new OrgFlowAuditSync();
        BeanUtil.copyProperties(orgFlowAudit, orgFlowAuditSync);
        if (ObjectUtil.isNotNull(userTicket) && ObjectUtil.isNotNull(userTicket.getUser())) {
            String auditOrgCode = userTicket.getUser().getOrgCode();
            Org auditOrg = this.orgService.findByOrgCode(auditOrgCode);
            if (ObjectUtil.isNull(auditOrg)) {
                return new OutMessage<>(Status.OBJEC_NOT_EXIST);
            }
            orgFlowAuditSync.setAuditOrgCode(auditOrg.getOrgCode());
            orgFlowAuditSync.setAuditOrgName(auditOrg.getName());
        }
        orgFlowAuditSync.setApplyOrgCode(orgFlow.getOrgCode());
        orgFlowAuditSync.setApplyOrgName(orgFlow.getName());
        orgFlowSyncAll.setOrgFlowAudit(orgFlowAuditSync);
        ThreadUtil.execute(() -> {
            this.flowExchangeService.pushFlow2Exchange(orgFlowSyncAll);
        });
        return new OutMessage<>(Status.SUCCESS);
    }

    @Override
    public OutMessage<?> cancelOrgFlowAudit(OrgFlowListAuditDto dto) {
        String flowOrgCode = dto.getFlowOrgCode();
        if (StrUtil.isEmpty(flowOrgCode)) {
            return new OutMessage<>(Status.PARA_ERROR);
        }
        OrgFlowAudit orgFlowAudit = selectOrgFlowAuditByFlowOrgCode(flowOrgCode);
        if (ObjectUtil.isNull(orgFlowAudit)) {
            return new OutMessage<>(Status.OBJEC_NOT_EXIST);
        }
        OrgFlow orgFlow = this.orgFlowMapper.getOrgFlowByCode(flowOrgCode);
        if (ObjectUtil.isNull(orgFlow)) {
            return new OutMessage<>(Status.OBJEC_NOT_EXIST);
        }
        orgFlowAudit.setStatus(dto.getStatus());
        orgFlowAudit.setReason(dto.getReason());
        orgFlowAudit.setAuditTime(DateUtil.date());
        UserTicket userTicket = UserConstant.USER_CONTEXT.get();
        if (ObjectUtil.isNotNull(userTicket)) {
            User user = userTicket.getUser();
            orgFlowAudit.setAuditUserId(user.getId());
            orgFlowAudit.setAuditUserName(user.getName());
        }
        this.orgFlowMapper.updateIsEnableById(orgFlow.getId(), 0);
        this.baseMapper.updateById(orgFlowAudit);

        //同步all_user
        ThreadUtil.execute(() -> {
            orgFlowService.pushFlowExchange(orgFlow, "7", orgFlowAudit);
        });


        return new OutMessage<>(Status.SUCCESS);
    }

    @Override
    public List<String> findExchangeFlowCodeList() {
        return this.baseMapper.findExchangeFlowCodeList();
    }

    @Override
    public OutMessage<?> updateFlowAuditByFlowCode(OrgFlowAudit audit) {
        String flowOrgCode = audit.getFlowOrgCode();
        if (StrUtil.isEmpty(flowOrgCode)) {
            return new OutMessage<>(Status.PARA_ERROR);
        }
        OrgFlowAudit orgFlowAudit = selectOrgFlowAuditByFlowOrgCode(flowOrgCode);
        if (ObjectUtil.isNull(orgFlowAudit)) {
            return new OutMessage<>(Status.OBJEC_NOT_EXIST);
        }
        orgFlowAudit.setStatus(audit.getStatus());
        orgFlowAudit.setReason(audit.getReason());
        orgFlowAudit.setAuditTime(audit.getAuditTime());
        this.baseMapper.updateById(orgFlowAudit);
        return new OutMessage<>(Status.SUCCESS);
    }

    @Override
    public OrgFlowAudit selectOrgFlowAuditByFlowOrgCode(String flowOrgCode) {
        LambdaQueryWrapper<OrgFlowAudit> auditLambdaQueryWrapper = new LambdaQueryWrapper<>();
        auditLambdaQueryWrapper.eq(OrgFlowAudit::getFlowOrgCode, flowOrgCode);
        auditLambdaQueryWrapper.isNull(OrgFlowAudit::getDeleteTime);
        auditLambdaQueryWrapper.last("limit 1");
        return this.baseMapper.selectOne(auditLambdaQueryWrapper);
    }
}
