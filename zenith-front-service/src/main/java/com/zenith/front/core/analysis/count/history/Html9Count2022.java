package com.zenith.front.core.analysis.count.history;

import com.b1809.analysis.entity.PeggingPara;
import com.b1809.analysis.extend.ITableCount;
import com.zenith.front.core.analysis.ext.condition.year2022.OrgAllConditionA;
import org.jooq.Condition;
import org.springframework.stereotype.Component;

import java.util.Map;

import static org.jooq.impl.DSL.noCondition;

/**
 * 第九表 党员培训情况
 *
 * <AUTHOR>
 * @date 2021/11/11
 */
@Component
public class Html9Count2022 extends Html9CountHistory implements ITableCount {

    @Override
    public String getReportCode() {
        return "2022_9.html";
    }

    @Override
    public Map<String, Map<String, Number>> doCount(String reportCode, String orgCode, String orgLevelCode) throws Exception {
        return null;
    }

    @Override
    public Map<String, Object> doPegging(PeggingPara peggingPara) throws Exception {
        return null;
    }

    @Override
    public Map<String, Number> doReplenishCount(String reportCode, String orgCode, String orgLevelCode) throws Exception {
        return getReplenishCount(orgCode, orgLevelCode, Html1Count2022.TABLE_ES_YEAR_A);
    }


    @Override
    public Map<String, Object> doReplenishPegging(PeggingPara peggingPara) throws Exception {
        OrgAllConditionA orgAllCondition = new OrgAllConditionA();
        Condition condition = noCondition().and(new Html6CountHistory().getOrgListCondition(peggingPara.getOrgCode(), peggingPara.getOrgLevelCode()).toString().replace("ccp_org_all", orgAllCondition.getTableName()))
                .and(this.getRowCondition(peggingPara.getRowIndex()));
        return Html48CountHistory.getReportPageResult(peggingPara, orgAllCondition.getTableName(), condition, orgAllCondition.getLevelCodeField());
    }


}
