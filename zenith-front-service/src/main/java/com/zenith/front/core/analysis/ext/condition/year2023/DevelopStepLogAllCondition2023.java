package com.zenith.front.core.analysis.ext.condition.year2023;

import com.b1809.analysis.extend.GenSqlConditionFuc;
import org.jooq.Condition;
import org.jooq.Field;

import static org.jooq.impl.DSL.field;
import static org.jooq.impl.DSL.name;

/**
 * <AUTHOR>
 * @date 2022/1/4
 */
public class DevelopStepLogAllCondition2023 implements GenSqlConditionFuc {
    @Override
    public String getTableName() {
        return "ccp_develop_step_log_all_2023";
    }

    @Override
    public Condition create(String orgCode, String orgLevelCode) {
        return field(name("log_org_code"), String.class).like(orgLevelCode + "%");
    }

    @Override
    public Field<?> getLevelCodeField() {
        return  field(name("log_org_code"), String.class);
    }
}
